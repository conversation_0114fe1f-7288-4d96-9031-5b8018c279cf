package com.xhcai.modules.system.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.system.dto.SysDictDataQueryDTO;
import com.xhcai.modules.system.entity.SysDictData;
import com.xhcai.modules.system.service.ISysDictDataService;
import com.xhcai.modules.system.vo.SysDictDataVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 字典数据管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "字典数据管理", description = "字典数据管理相关接口")
@RestController
@RequestMapping("/api/system/dict/data")
public class SysDictDataController {

    @Autowired
    private ISysDictDataService dictDataService;

    /**
     * 分页查询字典数据列表
     */
    @Operation(summary = "分页查询字典数据", description = "分页查询字典数据列表")
    @GetMapping("/page")
    @RequiresPermissions("system:dict:list")
    public Result<PageResult<SysDictDataVO>> page(@Valid SysDictDataQueryDTO queryDTO) {
        PageResult<SysDictDataVO> pageResult = dictDataService.selectDictDataPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询字典数据列表
     */
    @Operation(summary = "查询字典数据列表", description = "查询字典数据列表")
    @GetMapping("/list")
    @RequiresPermissions("system:dict:list")
    public Result<List<SysDictDataVO>> list(@Valid SysDictDataQueryDTO queryDTO) {
        List<SysDictDataVO> dictDataList = dictDataService.selectDictDataList(queryDTO);
        return Result.success(dictDataList);
    }

    /**
     * 根据字典类型查询字典数据
     */
    @Operation(summary = "根据字典类型查询字典数据", description = "根据字典类型查询字典数据")
    @GetMapping("/type/{dictType}")
    @RequiresPermissions("system:dict:list")
    public Result<List<SysDictDataVO>> getByDictType(@Parameter(description = "字典类型") @PathVariable String dictType) {
        List<SysDictDataVO> dictDataList = dictDataService.selectByDictType(dictType);
        return Result.success(dictDataList);
    }

    /**
     * 根据字典数据ID查询字典数据信息
     */
    @Operation(summary = "查询字典数据详情", description = "根据字典数据ID查询字典数据信息")
    @GetMapping("/{dictDataId}")
    @RequiresPermissions("system:dict:query")
    public Result<SysDictDataVO> getInfo(@Parameter(description = "字典数据ID") @PathVariable String dictDataId) {
        SysDictDataVO dictDataVO = dictDataService.selectDictDataById(dictDataId);
        return Result.success(dictDataVO);
    }

    /**
     * 根据字典类型和值获取字典标签
     */
    @Operation(summary = "获取字典标签", description = "根据字典类型和值获取字典标签")
    @GetMapping("/label")
    public Result<String> getDictLabel(@RequestParam("dictType") String dictType, @RequestParam("dictValue") String dictValue) {
        String label = dictDataService.getDictLabel(dictType, dictValue);
        return Result.success(label);
    }

    /**
     * 创建字典数据
     */
    @Operation(summary = "创建字典数据", description = "创建新的字典数据")
    @PostMapping
    @RequiresPermissions("system:dict:add")
    public Result<Void> add(@Valid @RequestBody SysDictData dictData) {
        boolean success = dictDataService.insertDictData(dictData);
        return success ? Result.success() : Result.fail("创建字典数据失败");
    }

    /**
     * 更新字典数据
     */
    @Operation(summary = "更新字典数据", description = "更新字典数据信息")
    @PutMapping
    @RequiresPermissions("system:dict:edit")
    public Result<Void> edit(@Valid @RequestBody SysDictData dictData) {
        boolean success = dictDataService.updateDictData(dictData);
        return success ? Result.success() : Result.fail("更新字典数据失败");
    }

    /**
     * 删除字典数据
     */
    @Operation(summary = "删除字典数据", description = "删除字典数据")
    @DeleteMapping("/{dictDataIds}")
    @RequiresPermissions("system:dict:remove")
    public Result<Void> remove(@Parameter(description = "字典数据ID列表，多个用逗号分隔") @PathVariable List<String> dictDataIds) {
        boolean success = dictDataService.deleteDictDatas(dictDataIds);
        return success ? Result.success() : Result.fail("删除字典数据失败");
    }

    /**
     * 批量更新字典数据状态
     */
    @Operation(summary = "批量更新字典数据状态", description = "批量更新字典数据状态")
    @PutMapping("/status")
    @RequiresPermissions("system:dict:edit")
    public Result<Void> updateStatus(@RequestParam List<String> dictDataIds, @RequestParam String status) {
        boolean success = dictDataService.batchUpdateStatus(dictDataIds, status);
        return success ? Result.success() : Result.fail("更新字典数据状态失败");
    }

    /**
     * 刷新字典数据缓存
     */
    @Operation(summary = "刷新字典数据缓存", description = "刷新字典数据缓存")
    @PostMapping("/refresh")
    @RequiresPermissions("system:dict:edit")
    public Result<Void> refreshCache() {
        dictDataService.refreshCache();
        return Result.success();
    }

    /**
     * 导出字典数据
     */
    @Operation(summary = "导出字典数据", description = "导出字典数据")
    @PostMapping("/export")
    @RequiresPermissions("system:dict:export")
    public Result<List<SysDictDataVO>> export(@RequestBody SysDictDataQueryDTO queryDTO) {
        List<SysDictDataVO> dictDataList = dictDataService.exportDictDatas(queryDTO);
        return Result.success(dictDataList);
    }

    /**
     * 导入字典数据
     */
    @Operation(summary = "导入字典数据", description = "导入字典数据")
    @PostMapping("/import")
    @RequiresPermissions("system:dict:import")
    public Result<String> importData(@RequestBody List<SysDictData> dictDataList) {
        String result = dictDataService.importDictDatas(dictDataList);
        return Result.success(result);
    }
}
