<template>
  <div class="category-panel" :class="{ visible }">
    <div class="panel-header">
      <h3 class="panel-title">
        <i class="fas fa-folder-tree"></i>
        文件分类
      </h3>
      <button class="close-btn" @click="$emit('close')" title="关闭">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="panel-content">
      <!-- 创建分类按钮 -->
      <div class="create-section">
        <button class="btn btn-primary btn-sm" @click="showCreateForm">
          <i class="fas fa-plus"></i>
          创建分类
        </button>
      </div>

      <!-- 分类树 -->
      <div class="category-tree">
        <div class="tree-item root-item" :class="{ active: selectedCategoryId === null }" @click="selectCategory(null)">
          <div class="item-content">
            <i class="fas fa-home item-icon"></i>
            <span class="item-name">全部文件</span>
            <span class="item-count">({{ totalFileCount }})</span>
          </div>
        </div>

        <template v-if="Array.isArray(categories)">
          <CategoryTreeNode
            v-for="category in categories"
            :key="category.id"
            :category="category"
            :level="0"
            :selected-id="selectedCategoryId"
            :all-categories="category.children"
            @select="selectCategory"
            @edit="editCategory"
            @delete="deleteCategory"
            @create-child="createChildCategory"
          />
        </template>
      </div>
    </div>

    <!-- 创建/编辑分类模态框 -->
    <div class="modal-overlay" v-if="showModal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>{{ editingCategory ? '编辑分类' : '创建分类' }}</h4>
          <button class="close-btn" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>分类名称</label>
            <input
              type="text"
              v-model="categoryForm.name"
              placeholder="请输入分类名称"
              class="form-input"
              @keyup.enter="saveCategory"
            >
          </div>
          <div class="form-group" v-if="!editingCategory">
            <label>父分类</label>
            <select v-model="categoryForm.parentId" class="form-select">
              <option value="">根分类</option>
              <option
                v-for="category in availableParents"
                :key="category.id"
                :value="category.id"
                :disabled="category.level >= 2"
              >
                {{ '　'.repeat(category.level) }}{{ category.name }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>描述</label>
            <textarea
              v-model="categoryForm.description"
              placeholder="请输入分类描述（可选）"
              class="form-textarea"
              rows="3"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-outline" @click="closeModal">取消</button>
          <button class="btn btn-primary" @click="saveCategory" :disabled="!categoryForm.name.trim()">
            {{ editingCategory ? '保存' : '创建' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import CategoryTreeNode from './CategoryTreeNode.vue'
import KnowledgeAPI from '@/api/knowledge'

// Props
interface Props {
  visible: boolean
  datasetId: string
  totalFileCount: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  categorySelected: [categoryId: string | null]
}>()

const route = useRoute()

// 响应式数据
const datasetId = ref('')
const categories = ref<any[]>([])
const selectedCategoryId = ref<string | null>(null)
const showModal = ref(false)
const editingCategory = ref<any>(null)
const categoryForm = ref({
  name: '',
  parentId: '',
  description: '',
  sortOrder: 0
})

// 计算属性
const rootCategories = computed(() => {
  return categories.value.filter(cat => !cat.parentId)
})

const availableParents = computed(() => {
  const result: any[] = []
  
  const addCategory = (cat: any, level: number) => {
    result.push({ ...cat, level })
    const children = categories.value.filter(c => c.parentId === cat.id)
    children.forEach(child => addCategory(child, level + 1))
  }
  
  rootCategories.value.forEach(cat => addCategory(cat, 0))
  return result
})

// 方法
const loadCategories = async () => {
  try {
    // 调用真实API获取分类树，传递datasetId参数
    const response = await KnowledgeAPI.getCategoryTree(props.datasetId)

    if (response.success && response.data) {
      categories.value = response.data
    } else {
      categories.value = []
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    // 使用模拟数据作为后备
    categories.value = []
  }
}

const selectCategory = (categoryId: string | null) => {
  selectedCategoryId.value = categoryId
  emit('categorySelected', categoryId)
}

const showCreateForm = () => {
  editingCategory.value = null
  categoryForm.value = {
    name: '',
    parentId: '',
    description: '',
    sortOrder: 0
  }
  showModal.value = true
}

const editCategory = (category: any) => {
  editingCategory.value = category
  categoryForm.value = {
    name: category.name,
    parentId: category.parentId || '',
    description: category.description || '',
    sortOrder: category.sortOrder || 0
  }
  showModal.value = true
}

const createChildCategory = (parentCategory: any) => {
  editingCategory.value = null
  categoryForm.value = {
    name: '',
    parentId: parentCategory.id,
    description: '',
    sortOrder: 0
  }
  showModal.value = true
}

const deleteCategory = async (category: any) => {
  if (!confirm(`确定要删除分类"${category.name}"吗？删除后该分类下的文件将移动到根目录。`)) {
    return
  }

  try {
    // 调用真实API删除分类
    const response = await KnowledgeAPI.deleteCategory(category.id)

    if (response.success) {
      // 重新加载分类列表
      await loadCategories()

      // 如果删除的是当前选中的分类，重置选择
      if (selectedCategoryId.value === category.id) {
        selectCategory(null)
      }
    } else {
      alert('删除分类失败：' + response.message)
    }
  } catch (error) {
    console.error('删除分类失败:', error)
    alert('删除分类失败，请重试')
  }
}

const saveCategory = async () => {
  if (!categoryForm.value.name.trim()) {
    return
  }

  try {
    let response

    if (editingCategory.value) {
      // 编辑分类
      response = await KnowledgeAPI.updateCategory(editingCategory.value.id, {
        name: categoryForm.value.name,
        description: categoryForm.value.description
      })
    } else {
      // 创建新分类
      response = await KnowledgeAPI.createCategory({
        datasetId: props.datasetId,
        name: categoryForm.value.name,
        description: categoryForm.value.description,
        parentId: categoryForm.value.parentId || undefined,
        sortOrder: categoryForm.value.sortOrder
      })
    }

    if (response.success) {
      // 重新加载分类列表
      await loadCategories()
      closeModal()
    } else {
      alert('保存分类失败：' + response.message)
    }
  } catch (error) {
    console.error('保存分类失败:', error)
    alert('保存分类失败，请重试')
  }
}

const closeModal = () => {
  showModal.value = false
  editingCategory.value = null
  categoryForm.value = {
    name: '',
    parentId: '',
    description: '',
    sortOrder: 0
  }
}

// 生命周期
onMounted(() => {
  // 获取路由参数 知识库ID
  datasetId.value = route.params.id as string
  loadCategories()
})
</script>

<style scoped>
.category-panel {
  position: fixed;
  left: -320px;
  top: 121px;
  width: 320px;
  height: calc(100vh - 121px);
  background: white;
  border-right: 1px solid #e2e8f0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: left 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.category-panel.visible {
  left: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.create-section {
  padding: 0 20px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-outline {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-outline:hover {
  background: #f8fafc;
  color: #334155;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 13px;
}

.category-tree {
  padding: 16px 0;
}

.tree-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.tree-item:hover {
  background: #f8fafc;
}

.tree-item.active {
  background: #eff6ff;
  border-right: 3px solid #3b82f6;
}

.root-item .item-content {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-icon {
  color: #64748b;
  font-size: 14px;
  width: 16px;
}

.item-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.item-count {
  font-size: 12px;
  color: #64748b;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 480px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}
</style>
