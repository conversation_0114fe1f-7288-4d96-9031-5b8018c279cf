package com.xhcai.plugin.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 模型选择
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelChoice {
    
    /**
     * 选择索引
     */
    private Integer index;
    
    /**
     * 消息内容
     */
    private ModelMessage message;
    
    /**
     * 完成原因
     */
    private String finishReason;
    
    /**
     * 日志概率（可选）
     */
    private Map<String, Object> logprobs;
}
