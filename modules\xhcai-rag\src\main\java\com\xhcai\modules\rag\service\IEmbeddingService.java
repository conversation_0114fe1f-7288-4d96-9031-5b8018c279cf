package com.xhcai.modules.rag.service;

import com.xhcai.modules.rag.entity.DocumentSegment;

import java.util.List;
import java.util.Map;

/**
 * 向量化服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IEmbeddingService {

    /**
     * 对文本进行向量化
     *
     * @param text 文本内容
     * @return 向量数组
     */
    float[] embedText(String text);

    /**
     * 批量向量化文本
     *
     * @param texts 文本列表
     * @return 向量列表
     */
    List<float[]> embedTexts(List<String> texts);

    /**
     * 对文档分段进行向量化
     *
     * @param segment 文档分段
     * @return 向量数组
     */
    float[] embedSegment(DocumentSegment segment);

    /**
     * 批量向量化文档分段
     *
     * @param segments 文档分段列表
     * @return 向量列表
     */
    List<float[]> embedSegments(List<DocumentSegment> segments);

    /**
     * 异步向量化文档分段
     *
     * @param segmentId 分段ID
     */
    void embedSegmentAsync(String segmentId);

    /**
     * 批量异步向量化文档分段
     *
     * @param segmentIds 分段ID列表
     */
    void embedSegmentsAsync(List<String> segmentIds);

    /**
     * 计算文本相似度
     *
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度分数
     */
    double calculateSimilarity(String text1, String text2);

    /**
     * 计算向量相似度
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度分数
     */
    double calculateSimilarity(float[] vector1, float[] vector2);

    /**
     * 搜索相似向量
     *
     * @param queryVector 查询向量
     * @param datasetId 知识库ID
     * @param topK 返回数量
     * @return 相似分段列表
     */
    List<DocumentSegment> searchSimilarVectors(float[] queryVector, String datasetId, int topK);

    /**
     * 搜索相似文本
     *
     * @param queryText 查询文本
     * @param datasetId 知识库ID
     * @param topK 返回数量
     * @return 相似分段列表
     */
    List<DocumentSegment> searchSimilarTexts(String queryText, String datasetId, int topK);

    /**
     * 获取向量维度
     *
     * @return 向量维度
     */
    int getVectorDimension();

    /**
     * 获取嵌入模型信息
     *
     * @return 模型信息
     */
    Map<String, Object> getModelInfo();

    /**
     * 检查向量化服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 获取向量化统计信息
     *
     * @param datasetId 知识库ID
     * @return 统计信息
     */
    Map<String, Object> getEmbeddingStats(String datasetId);

    /**
     * 删除文档的所有向量
     *
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteDocumentVectors(String documentId);

    /**
     * 删除知识库的所有向量
     *
     * @param datasetId 知识库ID
     * @return 删除数量
     */
    int deleteDatasetVectors(String datasetId);

    /**
     * 重建知识库向量索引
     *
     * @param datasetId 知识库ID
     * @return 是否成功
     */
    boolean rebuildVectorIndex(String datasetId);
}
