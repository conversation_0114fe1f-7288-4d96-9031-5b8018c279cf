<template>
  <!-- 只有在visible为true时才渲染整个面板 -->
  <div v-if="visible" class="module-init-panel">
    <!-- 遮罩层 -->
    <div
      class="panel-overlay"
      @click="closePanel"
    ></div>

    <!-- 侧边面板 -->
    <div
      class="panel-container"
      :class="{ 'panel-open': visible }"
    >
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon class="text-2xl text-blue-500">
              <Setting />
            </el-icon>
          </div>
          <div class="header-text">
            <h3 class="panel-title">模块数据初始化</h3>
            <p class="panel-subtitle">初始化各模块的基础数据和配置</p>
          </div>
        </div>
        <el-button 
          type="text" 
          @click="closePanel"
          class="close-btn"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>

      <!-- 面板内容 -->
      <div class="panel-content">
        <!-- 操作按钮区 -->
        <div class="action-section">
          <el-button 
            type="primary" 
            @click="initializeAllModules"
            :loading="isInitializingAll"
            :disabled="isAnyModuleInitializing"
            class="w-full mb-4"
          >
            <el-icon class="mr-2"><Refresh /></el-icon>
            {{ isInitializingAll ? '正在初始化所有模块...' : '初始化所有模块' }}
          </el-button>
          
          <div class="flex justify-between items-center mb-4">
            <span class="text-sm text-gray-600">
              共 {{ modules.length }} 个模块
            </span>
            <el-button 
              type="text" 
              @click="refreshModules"
              :loading="isRefreshing"
              size="small"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>

        <!-- 模块列表 -->
        <div class="modules-section">
          <div 
            v-for="module in sortedModules" 
            :key="module.moduleId"
            class="module-item"
          >
            <!-- 模块信息 -->
            <div class="module-info">
              <div class="module-header">
                <div class="module-icon">
                  <el-icon class="text-lg" :class="getModuleIconClass(module.moduleId)">
                    <component :is="getModuleIcon(module.moduleId)" />
                  </el-icon>
                </div>
                <div class="module-details">
                  <h4 class="module-name">{{ module.moduleName }}</h4>
                  <p class="module-description">{{ module.description }}</p>
                </div>
                <div class="module-status">
                  <el-tag 
                    :type="getStatusTagType(module.status)"
                    size="small"
                    effect="dark"
                  >
                    {{ getStatusText(module.status) }}
                  </el-tag>
                </div>
              </div>

              <!-- 进度条 -->
              <div v-if="module.progress > 0 && module.progress < 100" class="module-progress">
                <el-progress 
                  :percentage="module.progress" 
                  :status="module.status === 'FAILED' ? 'exception' : 'success'"
                  :stroke-width="6"
                />
              </div>

              <!-- 功能特性 -->
              <div class="module-features">
                <el-tag 
                  v-for="feature in module.features" 
                  :key="feature"
                  size="small"
                  class="mr-1 mb-1"
                  effect="plain"
                >
                  {{ feature }}
                </el-tag>
              </div>

              <!-- 操作按钮 -->
              <div class="module-actions">
                <el-button 
                  type="primary"
                  size="small"
                  @click="initializeModule(module)"
                  :loading="module.initializing"
                  :disabled="isAnyModuleInitializing && !module.initializing"
                >
                  <el-icon class="mr-1"><User /></el-icon>
                  {{ module.initializing ? '初始化中...' : '初始化' }}
                </el-button>
                
                <el-button 
                  type="info"
                  size="small"
                  @click="checkModuleStatus(module)"
                  :loading="module.checking"
                >
                  <el-icon class="mr-1"><View /></el-icon>
                  检查状态
                </el-button>
              </div>

              <!-- 错误信息 -->
              <div v-if="module.errorMessage" class="module-error">
                <el-alert
                  :title="module.errorMessage"
                  type="error"
                  size="small"
                  show-icon
                  :closable="false"
                />
              </div>

              <!-- 成功信息 -->
              <div v-if="module.message && module.status === 'SUCCESS'" class="module-success">
                <el-alert
                  :title="module.message"
                  type="success"
                  size="small"
                  show-icon
                  :closable="false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Close,
  Refresh,
  View,
  User,
  DataAnalysis,
  Document
} from '@element-plus/icons-vue'
import { ModuleAPI } from '@/api'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'initialized': [moduleId: string]
}>()

// 模块数据接口
interface ModuleInfo {
  moduleId: string
  moduleName: string
  description: string
  version: string
  author: string
  order: number
  apiPrefix: string
  features: string[]
  status: string
  initialized: boolean
  progress: number
  message?: string
  errorMessage?: string
  initializing?: boolean
  checking?: boolean
}

// 响应式数据
const modules = ref<ModuleInfo[]>([])
const isRefreshing = ref(false)
const isInitializingAll = ref(false)

// 计算属性
const sortedModules = computed(() => {
  return [...modules.value].sort((a, b) => (a.order || 999) - (b.order || 999))
})

const isAnyModuleInitializing = computed(() => {
  return modules.value.some(m => m.initializing) || isInitializingAll.value
})

// 方法
const closePanel = () => {
  emit('update:visible', false)
}

const refreshModules = async () => {
  try {
    isRefreshing.value = true
    const response = await ModuleAPI.getAllModules()
    if (response.code === 200 && response.data) {
      modules.value = response.data.map(module => ({
        ...module,
        initializing: false,
        checking: false,
        progress: module.initialized ? 100 : 0,
        status: module.initialized ? 'INITIALIZED' : 'NOT_INITIALIZED'
      }))
    }
  } catch (error) {
    console.error('刷新模块列表失败:', error)
    ElMessage.error('刷新模块列表失败')
  } finally {
    isRefreshing.value = false
  }
}

const checkModuleStatus = async (module: ModuleInfo) => {
  try {
    module.checking = true
    const response = await ModuleAPI.getModuleStatus(module.moduleId)
    if (response.code === 200 && response.data) {
      Object.assign(module, response.data)
    }
  } catch (error) {
    console.error('检查模块状态失败:', error)
    ElMessage.error('检查模块状态失败')
  } finally {
    module.checking = false
  }
}

const initializeModule = async (module: ModuleInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要初始化 "${module.moduleName}" 吗？`,
      '确认初始化',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    module.initializing = true
    module.progress = 0
    module.status = 'INITIALIZING'
    module.errorMessage = ''
    module.message = ''

    const response = await ModuleAPI.initializeModule(module.moduleId)
    if (response.code === 200 && response.data) {
      Object.assign(module, response.data)
      if (response.data.status === 'SUCCESS') {
        ElMessage.success(`${module.moduleName} 初始化成功`)
        emit('initialized', module.moduleId)
      } else if (response.data.status === 'FAILED') {
        ElMessage.error(`${module.moduleName} 初始化失败`)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('初始化模块失败:', error)
      ElMessage.error('初始化模块失败')
      module.status = 'FAILED'
      module.errorMessage = error instanceof Error ? error.message : '初始化失败'
    }
  } finally {
    module.initializing = false
  }
}

const initializeAllModules = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要初始化所有模块吗？这可能需要一些时间。',
      '确认初始化',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    isInitializingAll.value = true
    
    // 按顺序初始化模块
    for (const module of sortedModules.value) {
      if (!module.initialized) {
        await initializeModule(module)
      }
    }

    ElMessage.success('所有模块初始化完成')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量初始化失败:', error)
      ElMessage.error('批量初始化失败')
    }
  } finally {
    isInitializingAll.value = false
  }
}

// 辅助方法
const getModuleIcon = (moduleId: string) => {
  const iconMap: Record<string, any> = {
    system: User,
    ai: User,
    dify: User,
    agent: DataAnalysis,
    rag: Document
  }
  return iconMap[moduleId] || Setting
}

const getModuleIconClass = (moduleId: string) => {
  const classMap: Record<string, string> = {
    system: 'text-blue-500',
    ai: 'text-purple-500',
    dify: 'text-green-500',
    agent: 'text-orange-500',
    rag: 'text-red-500'
  }
  return classMap[moduleId] || 'text-gray-500'
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'INITIALIZED': 'success',
    'NOT_INITIALIZED': 'info',
    'INITIALIZING': 'warning',
    'FAILED': 'danger',
    'SUCCESS': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'INITIALIZED': '已初始化',
    'NOT_INITIALIZED': '未初始化',
    'INITIALIZING': '初始化中',
    'FAILED': '初始化失败',
    'SUCCESS': '初始化成功'
  }
  return textMap[status] || status
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 当面板显示时才加载模块数据
    refreshModules()
  }
}, { immediate: false })
</script>

<style scoped>
.module-init-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  pointer-events: auto;
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

.panel-container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 500px;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  animation: slideInRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-right: 12px;
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.panel-subtitle {
  font-size: 14px;
  margin: 4px 0 0 0;
  opacity: 0.9;
  line-height: 1.2;
}

.close-btn {
  color: white !important;
  border: none !important;
  background: transparent !important;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.action-section {
  margin-bottom: 20px;
}

.modules-section {
  padding: 0;
}

.module-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}

.module-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.module-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.module-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.module-details {
  flex: 1;
  min-width: 0;
}

.module-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.module-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.module-status {
  margin-left: 12px;
}

.module-progress {
  margin: 12px 0;
}

.module-features {
  margin: 12px 0;
}

.module-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.module-error,
.module-success {
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-container {
    width: 100%;
    right: 0;
  }
}

/* 动画效果 */
.module-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
