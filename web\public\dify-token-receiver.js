/**
 * Dify Token 接收器
 * 用于在Dify页面中接收来自父窗口的token信息并设置到localStorage
 */

(function() {
    'use strict';
    
    console.log('[Dify Token Receiver] 脚本已加载');
    
    let tokenReceived = false;
    let retryCount = 0;
    const maxRetries = 5;
    
    // 设置token到localStorage
    function setTokensToLocalStorage(tokens) {
        try {
            // 只设置基础的两个token
            localStorage.setItem('console_token', tokens.console_token);
            localStorage.setItem('refresh_token', tokens.refresh_token);

            console.log('[Dify Token Receiver] 成功设置token到localStorage');

            // 验证设置结果
            const consoleTokenSet = !!localStorage.getItem('console_token');
            const refreshTokenSet = !!localStorage.getItem('refresh_token');

            console.log('[Dify Token Receiver] 验证结果:', {
                console_token: consoleTokenSet,
                refresh_token: refreshTokenSet
            });

            return consoleTokenSet && refreshTokenSet;
            
        } catch (error) {
            console.error('[Dify Token Receiver] 设置token失败:', error);
            return false;
        }
    }
    
    // 向父窗口发送确认消息
    function sendConfirmation(success, message, data = {}) {
        try {
            const response = {
                type: success ? 'DIFY_TOKENS_SET' : 'DIFY_TOKENS_ERROR',
                source: 'dify-page',
                timestamp: Date.now(),
                message: message,
                data: data
            };
            
            // 发送给所有可能的父窗口
            if (window.parent && window.parent !== window) {
                window.parent.postMessage(response, 'http://localhost:4001');
                console.log('[Dify Token Receiver] 已发送确认消息到父窗口:', response);
            }

            if (window.top && window.top !== window) {
                window.top.postMessage(response, 'http://localhost:4001');
                console.log('[Dify Token Receiver] 已发送确认消息到顶级窗口:', response);
            }
            
        } catch (error) {
            console.error('[Dify Token Receiver] 发送确认消息失败:', error);
        }
    }
    
    // 处理接收到的消息
    function handleMessage(event) {
        console.log('[Dify Token Receiver] 收到消息:', {
            origin: event.origin,
            type: event.data?.type,
            source: event.data?.source,
            timestamp: event.data?.timestamp
        });
        
        // 检查消息类型
        if (!event.data || event.data.type !== 'SET_DIFY_TOKENS') {
            return;
        }
        
        // 检查消息来源
        if (event.data.source !== 'xhcai-choreography') {
            console.log('[Dify Token Receiver] 忽略非编排页面的消息');
            return;
        }
        
        // 防止重复处理
        if (tokenReceived) {
            console.log('[Dify Token Receiver] Token已接收，忽略重复消息');
            return;
        }
        
        console.log('[Dify Token Receiver] 开始处理token设置');
        
        const tokens = event.data.data;
        if (!tokens) {
            console.error('[Dify Token Receiver] 消息中缺少token数据');
            sendConfirmation(false, '消息中缺少token数据');
            return;
        }
        
        // 验证token数据
        if (!tokens.console_token || !tokens.refresh_token) {
            console.error('[Dify Token Receiver] Token数据不完整:', {
                hasConsoleToken: !!tokens.console_token,
                hasRefreshToken: !!tokens.refresh_token
            });
            sendConfirmation(false, 'Token数据不完整');
            return;
        }
        
        // 设置token
        const success = setTokensToLocalStorage(tokens);
        
        if (success) {
            tokenReceived = true;
            console.log('[Dify Token Receiver] Token设置成功');
            sendConfirmation(true, 'Token设置成功', {
                tokenCount: Object.keys(tokens).length - 1, // 减去origin字段
                timestamp: Date.now()
            });
            
            // 触发页面刷新或重新加载，确保Dify使用新的token
            setTimeout(() => {
                console.log('[Dify Token Receiver] 准备刷新页面以应用新token');
                // 可以选择刷新页面或触发特定事件
                // window.location.reload();
            }, 1000);
            
        } else {
            console.error('[Dify Token Receiver] Token设置失败');
            sendConfirmation(false, 'Token设置失败');
        }
    }
    
    // 发送页面准备就绪消息
    function sendPageReady() {
        try {
            const readyMessage = {
                type: 'DIFY_PAGE_READY',
                source: 'dify-page',
                timestamp: Date.now(),
                url: window.location.href
            };
            
            if (window.parent && window.parent !== window) {
                window.parent.postMessage(readyMessage, 'http://localhost:4001');
            }

            if (window.top && window.top !== window) {
                window.top.postMessage(readyMessage, 'http://localhost:4001');
            }
            
            console.log('[Dify Token Receiver] 已发送页面准备就绪消息');
            
        } catch (error) {
            console.error('[Dify Token Receiver] 发送页面准备就绪消息失败:', error);
        }
    }
    
    // 定期发送页面准备就绪消息
    function startReadyHeartbeat() {
        // 立即发送一次
        sendPageReady();
        
        // 然后每2秒发送一次，最多发送10次
        let heartbeatCount = 0;
        const heartbeatInterval = setInterval(() => {
            heartbeatCount++;
            sendPageReady();
            
            if (heartbeatCount >= 10 || tokenReceived) {
                clearInterval(heartbeatInterval);
                console.log('[Dify Token Receiver] 停止发送准备就绪消息');
            }
        }, 2000);
    }
    
    // 初始化
    function init() {
        console.log('[Dify Token Receiver] 初始化开始');
        
        // 添加消息监听器
        window.addEventListener('message', handleMessage);
        console.log('[Dify Token Receiver] 已添加消息监听器');
        
        // 开始发送准备就绪消息
        startReadyHeartbeat();
        
        // 检查当前localStorage中是否已有token
        const existingConsoleToken = localStorage.getItem('console_token');
        const existingRefreshToken = localStorage.getItem('refresh_token');

        console.log('[Dify Token Receiver] 现有token状态:', {
            console_token: !!existingConsoleToken,
            refresh_token: !!existingRefreshToken
        });
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // 也在window.onload时再次尝试
    window.addEventListener('load', () => {
        console.log('[Dify Token Receiver] 页面完全加载完成');
        if (!tokenReceived) {
            sendPageReady();
        }
    });
    
})();
