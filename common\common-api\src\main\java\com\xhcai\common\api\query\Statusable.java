package com.xhcai.common.api.query;

/**
 * 状态查询接口
 * 定义状态查询的基本方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface Statusable {

    /**
     * 获取状态
     * 
     * @return 状态值
     */
    String getStatus();

    /**
     * 是否有状态条件
     * 
     * @return true-有状态条件，false-无状态条件
     */
    default Boolean hasStatus() {
        return getStatus() != null && !getStatus().trim().isEmpty();
    }

    /**
     * 是否为启用状态
     * 通常0表示启用，1表示禁用
     * 
     * @return true-启用状态，false-非启用状态
     */
    default Boolean isEnabled() {
        return "0".equals(getStatus());
    }

    /**
     * 是否为禁用状态
     * 通常1表示禁用，0表示启用
     * 
     * @return true-禁用状态，false-非禁用状态
     */
    default Boolean isDisabled() {
        return "1".equals(getStatus());
    }

    /**
     * 检查状态是否匹配指定值
     * 
     * @param statusValue 要检查的状态值
     * @return true-状态匹配，false-状态不匹配
     */
    default Boolean isStatus(String statusValue) {
        return statusValue != null && statusValue.equals(getStatus());
    }
}
