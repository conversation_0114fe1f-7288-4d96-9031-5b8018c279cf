<template>
  <div v-if="questions && questions.length > 0" class="suggested-questions">
    <div class="questions-header">
      <div class="header-icon">
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <span class="header-text">建议问题</span>
    </div>

    <div class="questions-list">
      <button
        v-for="(question, index) in questions"
        :key="index"
        class="question-item"
        @click="handleQuestionClick(question)"
        :disabled="disabled"
      >
        <span class="question-text">{{ question }}</span>
        <svg class="question-arrow w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  questions?: string[]
  disabled?: boolean
}

interface Emits {
  (e: 'question-click', question: string): void
}

const props = withDefaults(defineProps<Props>(), {
  questions: () => [],
  disabled: false
})

const emit = defineEmits<Emits>()

const handleQuestionClick = (question: string) => {
  if (!props.disabled) {
    emit('question-click', question)
  }
}
</script>

<style scoped>
.suggested-questions {
  @apply mt-3 p-3 bg-gradient-to-br from-blue-50/40 to-indigo-50/20 rounded-lg border border-blue-100/40;
  backdrop-filter: blur(8px);
}

.questions-header {
  @apply flex items-center gap-1.5 mb-2 text-xs font-medium text-blue-600;
}

.header-icon {
  @apply flex items-center justify-center w-4 h-4 bg-blue-100/60 rounded-full;
}

.header-text {
  @apply text-blue-700;
}

.questions-list {
  @apply flex flex-wrap gap-2;
}

.question-item {
  @apply inline-flex items-center gap-1.5 px-3 py-1.5 bg-white/60 hover:bg-white/80
         rounded-full border border-blue-100/40 text-xs text-gray-600 hover:text-blue-700
         transition-all duration-200 ease-out hover:shadow-sm hover:border-blue-200/60
         disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white/60
         whitespace-nowrap;
}

.question-text {
  @apply text-xs leading-tight;
}

.question-arrow {
  @apply text-blue-400 opacity-0 transform translate-x-0.5 transition-all duration-200 ease-out flex-shrink-0;
}

.question-item:hover:not(:disabled) .question-arrow {
  @apply opacity-100 translate-x-0;
}

.question-item:hover:not(:disabled) {
  @apply text-blue-700 border-blue-300/70 bg-blue-50/60;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .suggested-questions {
    @apply mt-2 p-2.5;
  }

  .questions-header {
    @apply mb-1.5;
  }

  .questions-list {
    @apply gap-1.5;
  }

  .question-item {
    @apply px-2.5 py-1 text-xs;
  }

  .question-text {
    @apply text-xs;
  }
}

/* 动画效果 */
.suggested-questions {
  animation: fadeInUp 0.25s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.question-item {
  animation: fadeInScale 0.2s ease-out;
}

.question-item:nth-child(1) { animation-delay: 0.05s; }
.question-item:nth-child(2) { animation-delay: 0.1s; }
.question-item:nth-child(3) { animation-delay: 0.15s; }
.question-item:nth-child(4) { animation-delay: 0.2s; }
.question-item:nth-child(5) { animation-delay: 0.25s; }

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
