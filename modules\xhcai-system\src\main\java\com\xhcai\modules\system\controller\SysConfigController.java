package com.xhcai.modules.system.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.modules.system.dto.SysConfigQueryDTO;
import com.xhcai.modules.system.dto.SysConfigDTO;
import com.xhcai.modules.system.entity.SysConfig;
import com.xhcai.modules.system.service.ISysConfigService;
import com.xhcai.modules.system.vo.SysConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统配置控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "系统配置管理", description = "系统配置管理相关接口")
@RestController
@RequestMapping("/api/system/config")
@RequiresTenantAdmin(message = "系统配置管理需要租户管理员权限")
public class SysConfigController {

    @Autowired
    private ISysConfigService configService;

    /**
     * 分页查询系统配置列表
     */
    @Operation(summary = "分页查询配置列表", description = "根据条件分页查询系统配置信息")
    @GetMapping("/page")
    @RequiresPermissions("system:config:view")
    public Result<PageResult<SysConfigVO>> page(@Valid SysConfigQueryDTO queryDTO) {
        PageResult<SysConfigVO> pageResult = configService.selectConfigPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询系统配置列表
     */
    @Operation(summary = "查询配置列表", description = "根据条件查询系统配置列表")
    @GetMapping("/list")
    @RequiresPermissions("system:config:view")
    public Result<List<SysConfigVO>> list(@Valid SysConfigQueryDTO queryDTO) {
        List<SysConfigVO> configList = configService.selectConfigList(queryDTO);
        return Result.success(configList);
    }

    /**
     * 根据ID查询系统配置详情
     */
    @Operation(summary = "查询配置详情", description = "根据配置ID查询详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("system:config:view")
    public Result<SysConfigVO> getById(
            @Parameter(description = "配置ID", required = true) @PathVariable String id) {
        SysConfigVO configVO = configService.selectConfigById(id);
        return Result.success(configVO);
    }

    /**
     * 根据配置键查询配置值
     */
    @Operation(summary = "查询配置值", description = "根据配置键查询配置值")
    @GetMapping("/key/{configKey}")
    @RequiresPermissions("system:config:view")
    public Result<String> getByKey(
            @Parameter(description = "配置键", required = true) @PathVariable String configKey) {
        String configValue = configService.getConfigValue(configKey);
        return Result.success(configValue);
    }

    /**
     * 创建系统配置
     */
    @Operation(summary = "创建配置", description = "创建新的系统配置")
    @PostMapping
    @RequiresPermissions("system:config:add")
    public Result<Void> create(@Valid @RequestBody SysConfig config) {
        boolean result = configService.insertConfig(config);
        return result ? Result.success() : Result.fail("创建配置失败");
    }

    /**
     * 更新系统配置信息
     */
    @Operation(summary = "更新配置", description = "更新系统配置信息")
    @PutMapping
    @RequiresPermissions("system:config:edit")
    public Result<Void> update(@Valid @RequestBody SysConfig config) {
        boolean result = configService.updateConfig(config);
        return result ? Result.success() : Result.fail("更新配置失败");
    }

    /**
     * 删除系统配置
     */
    @Operation(summary = "删除配置", description = "批量删除系统配置")
    @DeleteMapping
    @RequiresPermissions("system:config:remove")
    public Result<Void> delete(@RequestBody List<String> configIds) {
        boolean result = configService.deleteConfigs(configIds);
        return result ? Result.success() : Result.fail("删除配置失败");
    }

    /**
     * 启用配置
     */
    @Operation(summary = "启用配置", description = "启用指定配置")
    @PutMapping("/{id}/enable")
    @RequiresPermissions("system:config:edit")
    public Result<Void> enable(
            @Parameter(description = "配置ID", required = true) @PathVariable String id) {
        boolean result = configService.batchUpdateStatus(List.of(id), "0");
        return result ? Result.success() : Result.fail("启用配置失败");
    }

    /**
     * 停用配置
     */
    @Operation(summary = "停用配置", description = "停用指定配置")
    @PutMapping("/{id}/disable")
    @RequiresPermissions("system:config:edit")
    public Result<Void> disable(
            @Parameter(description = "配置ID", required = true) @PathVariable String id) {
        boolean result = configService.batchUpdateStatus(List.of(id), "1");
        return result ? Result.success() : Result.fail("停用配置失败");
    }

    /**
     * 批量更新配置
     */
    @Operation(summary = "批量更新配置", description = "批量更新或创建系统配置")
    @PutMapping("/batch")
    @RequiresPermissions("system:config:edit")
    public Result<Void> batchUpdate(@Valid @RequestBody List<SysConfigDTO> configDTOs) {
        boolean result = configService.batchUpdateConfigs(configDTOs);
        return result ? Result.success() : Result.fail("批量更新配置失败");
    }

    /**
     * 批量更新配置状态
     */
    @Operation(summary = "批量更新状态", description = "批量更新配置状态")
    @PutMapping("/batch-status")
    @RequiresPermissions("system:config:edit")
    public Result<Void> batchUpdateStatus(
            @RequestBody List<String> configIds,
            @Parameter(description = "状态", required = true) @RequestParam String status) {
        boolean result = configService.batchUpdateStatus(configIds, status);
        return result ? Result.success() : Result.fail("批量更新状态失败");
    }

    /**
     * 获取配置分组列表
     */
    @Operation(summary = "获取配置分组", description = "获取所有配置分组列表")
    @GetMapping("/groups")
    @RequiresPermissions("system:config:view")
    public Result<List<String>> getConfigGroups() {
        List<String> groups = configService.selectConfigGroups();
        return Result.success(groups);
    }

    /**
     * 检查配置键是否存在
     */
    @Operation(summary = "检查配置键", description = "检查配置键是否已存在")
    @GetMapping("/check-key")
    @RequiresPermissions("system:config:view")
    public Result<Boolean> checkConfigKey(
            @Parameter(description = "配置键", required = true) @RequestParam String configKey,
            @Parameter(description = "排除的配置ID") @RequestParam(required = false) String excludeId) {
        boolean exists = configService.existsConfigKey(configKey, excludeId);
        return Result.success(exists);
    }

    /**
     * 刷新配置缓存
     */
    @Operation(summary = "刷新缓存", description = "刷新系统配置缓存")
    @PostMapping("/refresh-cache")
    @RequiresPermissions("system:config:edit")
    public Result<Void> refreshCache() {
        configService.refreshCache();
        return Result.success();
    }

    /**
     * 初始化系统配置
     */
    @Operation(summary = "初始化配置", description = "初始化系统默认配置")
    @PostMapping("/init")
    @RequiresPermissions("system:config:add")
    public Result<Void> initSystemConfigs() {
        configService.initSystemConfigs();
        return Result.success();
    }

    /**
     * 导出配置数据
     */
    @Operation(summary = "导出配置数据", description = "导出系统配置数据")
    @PostMapping("/export")
    @RequiresPermissions("system:config:export")
    public Result<List<SysConfigVO>> export(@RequestBody SysConfigQueryDTO queryDTO) {
        List<SysConfigVO> configList = configService.exportConfigs(queryDTO);
        return Result.success(configList);
    }

    /**
     * 导入配置数据
     */
    @Operation(summary = "导入配置数据", description = "批量导入系统配置数据")
    @PostMapping("/import")
    @RequiresPermissions("system:config:import")
    public Result<String> importConfigs(@RequestBody List<SysConfig> configList) {
        String result = configService.importConfigs(configList);
        return Result.success(result);
    }

    /**
     * 获取系统信息
     */
    @Operation(summary = "获取系统信息", description = "获取系统基本信息")
    @GetMapping("/system-info")
    @RequiresPermissions("system:config:view")
    public Result<Object> getSystemInfo() {
        // 这里可以返回系统版本、名称等基本信息
        return Result.success("系统信息获取功能待实现");
    }
}
