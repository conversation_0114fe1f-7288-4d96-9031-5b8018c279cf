<template>
  <div
    class="knowledge-graph-card bg-white border border-gray-200 rounded-lg p-6 hover:border-purple-300 hover:shadow-lg transition-all duration-300 cursor-pointer group"
    @click="handleCardClick"
  >
    <!-- 卡片头部 -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex items-center gap-3">
        <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg flex items-center justify-center text-2xl group-hover:from-purple-200 group-hover:to-pink-200 transition-colors">
          {{ graph.thumbnail }}
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
            {{ graph.name }}
          </h3>
          <div class="flex items-center gap-2 mt-1">
            <span
              class="px-2 py-1 text-xs font-medium rounded-full"
              :class="getCategoryStyle(graph.category)"
            >
              {{ getCategoryName(graph.category) }}
            </span>
            <span
              class="w-2 h-2 rounded-full"
              :class="graph.status === 'active' ? 'bg-green-500' : 'bg-gray-400'"
            ></span>
          </div>
        </div>
      </div>
      
      <!-- 操作菜单 -->
      <div class="relative">
        <button
          @click.stop="showMenu = !showMenu"
          class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
          </svg>
        </button>
        
        <!-- 下拉菜单 -->
        <div
          v-if="showMenu"
          class="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-32"
        >
          <div class="py-1">
            <button
              @click.stop="handleView"
              class="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
            >
              <span>👁️</span>
              查看
            </button>
            <button
              @click.stop="handleEdit"
              class="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
            >
              <span>✏️</span>
              编辑
            </button>
            <button
              @click.stop="handleDelete"
              class="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
            >
              <span>🗑️</span>
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 描述 -->
    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
      {{ graph.description }}
    </p>

    <!-- 统计信息 -->
    <div class="grid grid-cols-2 gap-4 mb-4">
      <div class="text-center p-3 bg-gray-50 rounded-lg">
        <div class="text-lg font-semibold text-gray-900">{{ graph.nodeCount }}</div>
        <div class="text-xs text-gray-500">节点</div>
      </div>
      <div class="text-center p-3 bg-gray-50 rounded-lg">
        <div class="text-lg font-semibold text-gray-900">{{ graph.edgeCount }}</div>
        <div class="text-xs text-gray-500">关系</div>
      </div>
    </div>

    <!-- 时间信息 -->
    <div class="flex items-center justify-between text-xs text-gray-500">
      <span>创建: {{ formatDate(graph.createdAt) }}</span>
      <span>更新: {{ formatDate(graph.updatedAt) }}</span>
    </div>

    <!-- 悬停效果 -->
    <div class="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Graph {
  id: string
  name: string
  description: string
  category: string
  nodeCount: number
  edgeCount: number
  createdAt: string
  updatedAt: string
  status: string
  thumbnail: string
}

interface Props {
  graph: Graph
}

interface Emits {
  (e: 'view', graph: Graph): void
  (e: 'edit', graph: Graph): void
  (e: 'delete', graph: Graph): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const showMenu = ref(false)

// 工具函数
const getCategoryName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    business: '业务知识',
    technical: '技术文档',
    product: '产品信息',
    customer: '客户关系'
  }
  return categoryMap[category] || category
}

const getCategoryStyle = (category: string): string => {
  const styleMap: Record<string, string> = {
    business: 'bg-blue-100 text-blue-800',
    technical: 'bg-green-100 text-green-800',
    product: 'bg-purple-100 text-purple-800',
    customer: 'bg-orange-100 text-orange-800'
  }
  return styleMap[category] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 事件处理
const handleView = () => {
  showMenu.value = false
  emit('view', props.graph)
}

const handleEdit = () => {
  showMenu.value = false
  emit('edit', props.graph)
}

const handleDelete = () => {
  showMenu.value = false
  emit('delete', props.graph)
}

// 点击卡片主体区域时查看图谱
const handleCardClick = () => {
  console.log('Card clicked:', props.graph.name)
  if (!showMenu.value) {
    console.log('Emitting view event for graph:', props.graph.id)
    emit('view', props.graph)
  } else {
    console.log('Menu is open, not emitting view event')
  }
}
</script>

<style scoped>
.knowledge-graph-card {
  position: relative;
  transform: translateY(0);
}

.knowledge-graph-card:hover {
  transform: translateY(-2px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
