package com.xhcai.modules.dify.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * Dify 远程文件上传请求 DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 远程文件上传请求")
public class DifyRemoteFileUploadRequestDTO {

    /**
     * 远程文件URL
     */
    @NotBlank(message = "文件URL不能为空")
    @Pattern(regexp = "^https?://.*", message = "文件URL格式不正确，必须以http://或https://开头")
    @Schema(description = "远程文件URL", example = "https://img.51test.net/pd/pthsp.jpg", required = true)
    private String url;

    public DifyRemoteFileUploadRequestDTO(String url) {
        this.url = url;
    }
}
