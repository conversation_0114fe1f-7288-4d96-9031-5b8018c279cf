package com.xhcai.modules.ai.dto;

import com.xhcai.modules.dify.dto.conversation.DifyMessageListResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息列表响应DTO
 * 用于返回给前端的消息列表，包含正确的时间格式转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "消息列表响应")
public class MessageListResponseDTO {
    
    @Schema(description = "消息列表")
    private List<MessageResponseDTO> data;
    
    @Schema(description = "是否有更多数据")
    private Boolean hasMore;
    
    @Schema(description = "限制数量")
    private Integer limit;
    
    @Schema(description = "总数量")
    private Integer total;
    
    @Schema(description = "第一个消息ID")
    private String firstId;
    
    @Schema(description = "最后一个消息ID")
    private String lastId;

    /**
     * 从原始响应数据转换为响应DTO
     */
    public static MessageListResponseDTO fromRawResponse(Object rawResponse) {
        MessageListResponseDTO dto = new MessageListResponseDTO();

        if (rawResponse instanceof DifyMessageListResponseDTO) {
            DifyMessageListResponseDTO difyResponse = (DifyMessageListResponseDTO) rawResponse;

            // 转换消息列表
            if (difyResponse.getData() != null) {
                List<MessageResponseDTO> messages = difyResponse.getData().stream()
                        .map(difyMessage -> MessageResponseDTO.fromDifyMessage(difyMessage))
                        .collect(Collectors.toList());
                dto.setData(messages);
            } else {
                dto.setData(new ArrayList<>());
            }

            // 设置其他字段
            dto.setHasMore(difyResponse.getHasMore());
            dto.setLimit(difyResponse.getLimit());
            dto.setTotal(difyResponse.getTotal());
            dto.setFirstId(difyResponse.getFirstId());
            dto.setLastId(difyResponse.getLastId());

        } else if (rawResponse instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = (Map<String, Object>) rawResponse;

            // 转换消息列表
            Object dataObj = responseMap.get("data");
            if (dataObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> rawMessages = (List<Object>) dataObj;
                List<MessageResponseDTO> messages = rawMessages.stream()
                        .map(MessageResponseDTO::fromRawMessage)
                        .collect(Collectors.toList());
                dto.setData(messages);
            }

            // 设置其他字段
            Object hasMoreObj = responseMap.get("has_more");
            if (hasMoreObj instanceof Boolean) {
                dto.setHasMore((Boolean) hasMoreObj);
            }

            Object limitObj = responseMap.get("limit");
            if (limitObj instanceof Number) {
                dto.setLimit(((Number) limitObj).intValue());
            }

            Object totalObj = responseMap.get("total");
            if (totalObj instanceof Number) {
                dto.setTotal(((Number) totalObj).intValue());
            }

            dto.setFirstId((String) responseMap.get("first_id"));
            dto.setLastId((String) responseMap.get("last_id"));
        }

        return dto;
    }

    // Getters and Setters
    public List<MessageResponseDTO> getData() {
        return data;
    }

    public void setData(List<MessageResponseDTO> data) {
        this.data = data;
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getFirstId() {
        return firstId;
    }

    public void setFirstId(String firstId) {
        this.firstId = firstId;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }
}
