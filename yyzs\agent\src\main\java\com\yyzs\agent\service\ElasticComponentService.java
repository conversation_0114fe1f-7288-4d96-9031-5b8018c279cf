package com.yyzs.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyzs.agent.entity.ElasticComponent;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Elastic组件服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ElasticComponentService extends IService<ElasticComponent> {

    /**
     * 上传组件安装包
     */
    String uploadPackage(MultipartFile file, String componentType, String version, String description);

    /**
     * 安装组件
     */
    boolean installComponent(String componentId, Map<String, Object> config);

    /**
     * 卸载组件
     */
    boolean uninstallComponent(String componentId, Map<String, Object> uninstallOptions);

    /**
     * 启动组件
     */
    boolean startComponent(String componentId);

    /**
     * 停止组件
     */
    boolean stopComponent(String componentId);

    /**
     * 重启组件
     */
    boolean restartComponent(String componentId);

    /**
     * 检查组件状态
     */
    ElasticComponent.ComponentStatus checkComponentStatus(String componentId);

    /**
     * 根据类型查询组件列表
     */
    List<ElasticComponent> getComponentsByType(String type);

    /**
     * 根据状态查询组件列表
     */
    List<ElasticComponent> getComponentsByStatus(ElasticComponent.ComponentStatus status);

    /**
     * 获取运行中的组件列表
     */
    List<ElasticComponent> getRunningComponents();

    /**
     * 获取已安装的组件列表
     */
    List<ElasticComponent> getInstalledComponents();

    /**
     * 根据名称查询组件
     */
    ElasticComponent getComponentByName(String name);

    /**
     * 根据端口查询组件
     */
    ElasticComponent getComponentByPort(Integer port);

    /**
     * 批量启动组件
     */
    Map<String, Boolean> batchStartComponents(List<String> componentIds);

    /**
     * 批量停止组件
     */
    Map<String, Boolean> batchStopComponents(List<String> componentIds);

    /**
     * 获取组件统计信息
     */
    Map<String, Object> getComponentStatistics();

    /**
     * 更新组件配置
     */
    boolean updateComponentConfig(String componentId, Map<String, Object> config);

    /**
     * 获取组件配置
     */
    Map<String, Object> getComponentConfig(String componentId);

    /**
     * 验证组件配置
     */
    boolean validateComponentConfig(String componentType, Map<String, Object> config);

    /**
     * 获取组件日志
     */
    List<String> getComponentLogs(String componentId, int lines);

    /**
     * 清理组件日志
     */
    boolean clearComponentLogs(String componentId);

    /**
     * 备份组件配置
     */
    String backupComponentConfig(String componentId);

    /**
     * 恢复组件配置
     */
    boolean restoreComponentConfig(String componentId, String backupPath);

    /**
     * 检查端口是否被占用
     */
    boolean isPortInUse(Integer port);

    /**
     * 获取可用端口
     */
    Integer getAvailablePort(Integer startPort, Integer endPort);

    /**
     * 同步组件状态
     */
    void syncComponentStatus();

    /**
     * 自动启动组件
     */
    void autoStartComponents();
}
