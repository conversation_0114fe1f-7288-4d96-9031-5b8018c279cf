<template>
  <div class="image-gallery">
    <!-- 单张图片 -->
    <div v-if="images.length === 1" class="single-image">
      <el-image
        :src="images[0].url"
        :alt="images[0].alt || '图片'"
        class="gallery-image single"
        fit="cover"
        :preview-src-list="previewList"
        :initial-index="0"
        :preview-teleported="true"
        :hide-on-click-modal="true"
        lazy
      >
        <template #error>
          <div class="image-error">
            <el-icon><Picture /></el-icon>
            <span>图片加载失败</span>
          </div>
        </template>
      </el-image>
      <div v-if="images[0].caption" class="image-caption">
        {{ images[0].caption }}
      </div>
    </div>

    <!-- 两张图片 -->
    <div v-else-if="images.length === 2" class="two-images">
      <div 
        v-for="(image, index) in images" 
        :key="index"
        class="image-item"
      >
        <el-image
          :src="image.url"
          :alt="image.alt || `图片${index + 1}`"
          class="gallery-image two"
          fit="cover"
          :preview-src-list="previewList"
          :initial-index="index"
          :preview-teleported="true"
          :hide-on-click-modal="true"
          lazy
        >
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>加载失败</span>
            </div>
          </template>
        </el-image>
        <div v-if="image.caption" class="image-caption">
          {{ image.caption }}
        </div>
      </div>
    </div>

    <!-- 三张图片 -->
    <div v-else-if="images.length === 3" class="three-images">
      <div class="main-image">
        <el-image
          :src="images[0].url"
          :alt="images[0].alt || '图片1'"
          class="gallery-image main"
          fit="cover"
          :preview-src-list="previewList"
          :initial-index="0"
          :preview-teleported="true"
          :hide-on-click-modal="true"
          lazy
        >
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>加载失败</span>
            </div>
          </template>
        </el-image>
        <div v-if="images[0].caption" class="image-caption">
          {{ images[0].caption }}
        </div>
      </div>
      <div class="side-images">
        <div 
          v-for="(image, index) in images.slice(1)" 
          :key="index + 1"
          class="image-item"
        >
          <el-image
            :src="image.url"
            :alt="image.alt || `图片${index + 2}`"
            class="gallery-image side"
            fit="cover"
            :preview-src-list="previewList"
            :initial-index="index + 1"
            :preview-teleported="true"
            :hide-on-click-modal="true"
            lazy
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>失败</span>
              </div>
            </template>
          </el-image>
          <div v-if="image.caption" class="image-caption">
            {{ image.caption }}
          </div>
        </div>
      </div>
    </div>

    <!-- 四张及以上图片 -->
    <div v-else class="multiple-images">
      <div class="images-grid">
        <div 
          v-for="(image, index) in displayImages" 
          :key="index"
          class="image-item"
          :class="{ 'has-more': index === 3 && images.length > 4 }"
        >
          <el-image
            :src="image.url"
            :alt="image.alt || `图片${index + 1}`"
            class="gallery-image grid"
            fit="cover"
            :preview-src-list="previewList"
            :initial-index="index"
            :preview-teleported="true"
            :hide-on-click-modal="true"
            lazy
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>失败</span>
              </div>
            </template>
          </el-image>
          
          <!-- 更多图片遮罩 -->
          <div 
            v-if="index === 3 && images.length > 4" 
            class="more-overlay"
            @click="showAllImages"
          >
            <span class="more-text">+{{ images.length - 4 }}</span>
          </div>
          
          <div v-if="image.caption" class="image-caption">
            {{ image.caption }}
          </div>
        </div>
      </div>
    </div>

    <!-- 查看全部图片按钮 -->
    <div v-if="images.length > 4" class="view-all-btn">
      <el-button @click="showAllImages" size="small" type="primary" text>
        查看全部 {{ images.length }} 张图片
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue'
import { Picture } from '@element-plus/icons-vue'

// 接口定义
interface ImageContent {
  url: string
  alt?: string
  caption?: string
}

// Props定义
const props = defineProps<{
  images: ImageContent[]
}>()

// Emits定义
const emit = defineEmits<{
  imageClick: [image: ImageContent, index: number]
  viewAll: []
}>()

// 计算属性
const previewList = computed(() => {
  return props.images.map(img => img.url)
})

const displayImages = computed(() => {
  return props.images.slice(0, 4)
})

// 事件处理
const handleImageClick = (image: ImageContent, index: number) => {
  // 移除自定义点击处理，使用Element Plus内置预览
  // emit('imageClick', image, index)
}

const showAllImages = () => {
  emit('viewAll')
}
</script>

<style scoped>
.image-gallery {
  width: 100%;
}

.gallery-image {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, opacity 0.2s ease;
  background: #f3f4f6;
}

.gallery-image:hover {
  transform: scale(1.02);
}

/* 防止图片预览闪烁 */
.gallery-image :deep(.el-image__inner) {
  transition: opacity 0.2s ease;
}

.gallery-image :deep(.el-image__preview) {
  backdrop-filter: blur(10px);
}

/* 图片加载状态 */
.gallery-image :deep(.el-image__placeholder) {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-image :deep(.el-image__placeholder)::before {
  content: '';
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #9ca3af;
  background: #f3f4f6;
}

.image-error span {
  margin-top: 8px;
  font-size: 12px;
}

.image-caption {
  margin-top: 8px;
  font-size: 14px;
  color: #6b7280;
  text-align: center;
}

/* 单张图片 */
.single-image .gallery-image {
  width: 100%;
  max-width: 400px;
  height: 300px;
}

/* 两张图片 */
.two-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.two-images .gallery-image {
  width: 100%;
  height: 200px;
}

/* 三张图片 */
.three-images {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 8px;
  height: 300px;
}

.main-image {
  height: 100%;
}

.main-image .gallery-image {
  width: 100%;
  height: 100%;
}

.side-images {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
}

.side-images .image-item {
  flex: 1;
}

.side-images .gallery-image {
  width: 100%;
  height: 100%;
}

/* 多张图片网格 */
.images-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.images-grid .image-item {
  position: relative;
}

.images-grid .gallery-image {
  width: 100%;
  height: 150px;
}

.more-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.more-overlay:hover {
  background: rgba(0, 0, 0, 0.7);
}

.more-text {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.view-all-btn {
  margin-top: 12px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .three-images {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .side-images {
    flex-direction: row;
    height: 150px;
  }
  
  .images-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .images-grid .gallery-image {
    height: 120px;
  }
}
</style>
