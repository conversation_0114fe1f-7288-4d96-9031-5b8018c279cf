// 流式内容解析器
export interface StreamContent {
  type: 'text' | 'markdown' | 'code' | 'chart' | 'image' | 'table' | 'audio' | 'video' | 'file' | 'flowchart'
  content: string
  metadata?: any
  isComplete: boolean
}

export interface ParsedStreamData {
  textContent: string
  contentType: 'text' | 'markdown' | 'html' | 'audio' | 'video' | 'file' | 'chart' | 'image' | 'flowchart'
  chartData?: any
  chartType?: string
  images?: any[]
  audioContent?: any
  videoContent?: any
  files?: any[]
  flowchartData?: any
  isComplete: boolean
}

export class StreamContentParser {
  private buffer: string = ''
  private currentBlock: StreamContent | null = null
  private blocks: StreamContent[] = []
  private options: {
    enableChartParsing?: boolean
    enableImageParsing?: boolean
    enableAudioParsing?: boolean
    enableVideoParsing?: boolean
    enableFileParsing?: boolean
    enableFlowchartParsing?: boolean
  } = {
    enableChartParsing: true,
    enableImageParsing: true,
    enableAudioParsing: true,
    enableVideoParsing: true,
    enableFileParsing: true,
    enableFlowchartParsing: true
  }

  // 设置解析选项
  setOptions(options: Partial<typeof this.options>): void {
    this.options = { ...this.options, ...options }
  }

  // 添加新的流式数据
  addChunk(chunk: string): ParsedStreamData {
    this.buffer += chunk
    this.parseBuffer()
    return this.generateOutput()
  }

  // 批量处理累积的内容 - 用于优化流式输出
  processCumulativeContent(content: string): ParsedStreamData {
    // 重置缓冲区并处理新内容
    const originalBuffer = this.buffer
    this.buffer = content
    this.parseBuffer()
    const result = this.generateOutput()

    // 如果没有识别到特殊内容，恢复原始缓冲区
    if (result.contentType === 'text' && !this.hasSpecialContent(result)) {
      this.buffer = originalBuffer
    }

    return result
  }

  // 检查是否包含特殊内容
  private hasSpecialContent(data: ParsedStreamData): boolean {
    return data.chartData !== undefined ||
           (data.images && data.images.length > 0) ||
           data.audioContent !== undefined ||
           data.videoContent !== undefined ||
           (data.files && data.files.length > 0) ||
           data.flowchartData !== undefined
  }

  // 解析缓冲区内容
  private parseBuffer(): void {
    // 检测代码块
    this.detectCodeBlocks()

    // 根据选项检测各种特殊内容
    if (this.options.enableChartParsing) {
      this.detectChartData()
    }

    if (this.options.enableImageParsing) {
      this.detectImages()
    }

    // 检测表格
    this.detectTables()

    if (this.options.enableAudioParsing) {
      this.detectAudioContent()
    }

    if (this.options.enableVideoParsing) {
      this.detectVideoContent()
    }

    if (this.options.enableFileParsing) {
      this.detectFileContent()
    }

    if (this.options.enableFlowchartParsing) {
      this.detectFlowchartContent()
    }
  }

  // 检测代码块
  private detectCodeBlocks(): void {
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g
    let match
    
    while ((match = codeBlockRegex.exec(this.buffer)) !== null) {
      const language = match[1] || 'text'
      const code = match[2]
      
      this.blocks.push({
        type: 'code',
        content: code,
        metadata: { language },
        isComplete: true
      })
    }
  }

  // 检测图表数据
  private detectChartData(): void {
    // 检测图表数据标记
    const chartRegex = /\[CHART:(\w+)\]([\s\S]*?)\[\/CHART\]/g
    let match
    
    while ((match = chartRegex.exec(this.buffer)) !== null) {
      const chartType = match[1].toLowerCase()
      const chartDataStr = match[2]
      
      try {
        const chartData = JSON.parse(chartDataStr)
        this.blocks.push({
          type: 'chart',
          content: chartDataStr,
          metadata: { chartType, chartData },
          isComplete: true
        })
      } catch (error) {
        // 只在开发环境输出警告信息
        if (import.meta.env.DEV) {
          console.warn('图表数据解析失败:', error)
        }
      }
    }
  }

  // 检测图片链接
  private detectImages(): void {
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g
    let match
    
    while ((match = imageRegex.exec(this.buffer)) !== null) {
      const alt = match[1]
      const url = match[2]
      
      this.blocks.push({
        type: 'image',
        content: match[0],
        metadata: { alt, url },
        isComplete: true
      })
    }
  }

  // 检测表格
  private detectTables(): void {
    const lines = this.buffer.split('\n')
    let tableStart = -1
    let tableEnd = -1
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      // 检测表格开始（包含|的行）
      if (line.includes('|') && tableStart === -1) {
        tableStart = i
      }
      
      // 检测表格结束（不包含|的行或空行）
      if (tableStart !== -1 && (!line.includes('|') || line === '')) {
        tableEnd = i - 1
        break
      }
    }
    
    if (tableStart !== -1) {
      const tableLines = lines.slice(tableStart, tableEnd + 1)
      const tableContent = tableLines.join('\n')
      
      this.blocks.push({
        type: 'table',
        content: tableContent,
        metadata: { lines: tableLines },
        isComplete: tableEnd !== -1
      })
    }
  }

  // 检测音频内容
  private detectAudioContent(): void {
    const audioRegex = /\[AUDIO\]([\s\S]*?)\[\/AUDIO\]/g
    let match

    while ((match = audioRegex.exec(this.buffer)) !== null) {
      try {
        const audioData = JSON.parse(match[1].trim())
        this.blocks.push({
          type: 'audio' as any,
          content: match[0],
          metadata: { audioData },
          isComplete: true
        })
      } catch (e) {
        // JSON解析失败，忽略
      }
    }
  }

  // 检测视频内容
  private detectVideoContent(): void {
    const videoRegex = /\[VIDEO\]([\s\S]*?)\[\/VIDEO\]/g
    let match

    while ((match = videoRegex.exec(this.buffer)) !== null) {
      try {
        const videoData = JSON.parse(match[1].trim())
        // 移除视频检测的控制台输出
        // if (import.meta.env.DEV) {
        //   console.log('StreamContentParser 检测到视频:', videoData)
        // }
        this.blocks.push({
          type: 'video' as any,
          content: match[0],
          metadata: { videoData },
          isComplete: true
        })
      } catch (e) {
        // 只在开发环境输出警告信息
        if (import.meta.env.DEV) {
          console.warn('视频JSON解析失败:', e, match[1])
        }
      }
    }
  }

  // 检测文件内容
  private detectFileContent(): void {
    const fileRegex = /\[FILE\]([\s\S]*?)\[\/FILE\]/g
    let match

    while ((match = fileRegex.exec(this.buffer)) !== null) {
      try {
        const fileData = JSON.parse(match[1].trim())
        this.blocks.push({
          type: 'file' as any,
          content: match[0],
          metadata: { fileData },
          isComplete: true
        })
      } catch (e) {
        // JSON解析失败，忽略
      }
    }
  }

  // 检测流程图内容
  private detectFlowchartContent(): void {
    const flowchartRegex = /\[FLOWCHART:(\w+)\]([\s\S]*?)\[\/FLOWCHART\]/g
    let match

    while ((match = flowchartRegex.exec(this.buffer)) !== null) {
      this.blocks.push({
        type: 'flowchart' as any,
        content: match[0],
        metadata: {
          flowchartType: match[1],
          flowchartData: match[2].trim()
        },
        isComplete: true
      })
    }
  }

  // 生成输出
  private generateOutput(): ParsedStreamData {
    // 移除已识别的特殊内容块，保留纯文本
    let textContent = this.buffer

    // 移除各种特殊标记
    textContent = textContent.replace(/\[CHART:\w+\][\s\S]*?\[\/CHART\]/g, '')
    textContent = textContent.replace(/\[AUDIO\][\s\S]*?\[\/AUDIO\]/g, '')
    textContent = textContent.replace(/\[VIDEO\][\s\S]*?\[\/VIDEO\]/g, '')
    textContent = textContent.replace(/\[FILE\][\s\S]*?\[\/FILE\]/g, '')
    textContent = textContent.replace(/\[FLOWCHART:\w+\][\s\S]*?\[\/FLOWCHART\]/g, '')

    // 检测内容类型
    const hasMarkdownSyntax = this.hasMarkdownSyntax(textContent)
    const hasHtmlSyntax = this.hasHtmlSyntax(textContent)

    // 提取各种数据
    const chartBlocks = this.blocks.filter(block => block.type === 'chart')
    const chartData = chartBlocks.length > 0 ? chartBlocks[0].metadata?.chartData : undefined
    const chartType = chartBlocks.length > 0 ? chartBlocks[0].metadata?.chartType : undefined

    const imageBlocks = this.blocks.filter(block => block.type === 'image')
    const images = imageBlocks.map(block => ({
      url: block.metadata?.url,
      alt: block.metadata?.alt,
      caption: block.metadata?.alt
    }))

    const audioBlocks = this.blocks.filter(block => block.type === 'audio')
    const audioContent = audioBlocks.length > 0 ? audioBlocks[0].metadata?.audioData : undefined

    const videoBlocks = this.blocks.filter(block => block.type === 'video')
    const videoContent = videoBlocks.length > 0 ? videoBlocks[0].metadata?.videoData : undefined

    const fileBlocks = this.blocks.filter(block => block.type === 'file')
    const files = fileBlocks.map(block => block.metadata?.fileData)

    const flowchartBlocks = this.blocks.filter(block => block.type === 'flowchart')
    const flowchartData = flowchartBlocks.length > 0 ? {
      type: flowchartBlocks[0].metadata?.flowchartType,
      data: flowchartBlocks[0].metadata?.flowchartData
    } : undefined

    // 确定内容类型 - 根据检测到的特殊内容类型优先设置
    let contentType: 'text' | 'markdown' | 'html' | 'audio' | 'video' | 'file' | 'chart' | 'image' | 'flowchart' = 'text'

    // 优先级：特殊内容类型 > HTML > Markdown > 文本
    if (chartData) {
      contentType = 'chart'
    } else if (audioContent) {
      contentType = 'audio'
    } else if (videoContent) {
      contentType = 'video'
    } else if (files && files.length > 0) {
      contentType = 'file'
    } else if (images.length > 0) {
      contentType = 'image'
    } else if (flowchartData) {
      contentType = 'flowchart'
    } else if (hasHtmlSyntax) {
      contentType = 'html'
    } else if (hasMarkdownSyntax) {
      contentType = 'markdown'
    }

    return {
      textContent: textContent.trim(),
      contentType,
      chartData,
      chartType,
      images: images.length > 0 ? images : undefined,
      audioContent,
      videoContent,
      files: files.length > 0 ? files : undefined,
      flowchartData,
      isComplete: this.isContentComplete()
    }
  }

  // 检测是否包含HTML语法
  private hasHtmlSyntax(content: string): boolean {
    const htmlPatterns = [
      /<[^>]+>/,              // HTML标签
      /&\w+;/,                // HTML实体
      /<\/[^>]+>/             // 闭合标签
    ]

    return htmlPatterns.some(pattern => pattern.test(content))
  }

  // 检测是否包含Markdown语法
  private hasMarkdownSyntax(content: string): boolean {
    const markdownPatterns = [
      /^#{1,6}\s/m,           // 标题
      /\*\*.*?\*\*/,          // 粗体
      /\*.*?\*/,              // 斜体
      /`.*?`/,                // 行内代码
      /```[\s\S]*?```/,       // 代码块
      /^\s*[-*+]\s/m,         // 无序列表
      /^\s*\d+\.\s/m,         // 有序列表
      /^\s*>\s/m,             // 引用
      /\|.*\|/,               // 表格
      /\[.*?\]\(.*?\)/        // 链接
    ]
    
    return markdownPatterns.some(pattern => pattern.test(content))
  }

  // 检查内容是否完整
  private isContentComplete(): boolean {
    // 检查是否有未闭合的代码块
    const codeBlockMatches = this.buffer.match(/```/g)
    if (codeBlockMatches && codeBlockMatches.length % 2 === 1) {
      return false
    }
    
    // 检查是否有未完成的图表标记
    if (this.buffer.includes('[CHART:') && !this.buffer.includes('[/CHART]')) {
      return false
    }
    
    return true
  }

  // 重置解析器
  reset(): void {
    this.buffer = ''
    this.currentBlock = null
    this.blocks = []
  }

  // 重置解析器并设置选项
  resetWithOptions(options?: Partial<typeof this.options>): void {
    this.reset()
    if (options) {
      this.setOptions(options)
    }
  }

  // 获取当前缓冲区内容
  getBuffer(): string {
    return this.buffer
  }
}

// 创建全局解析器实例
export const streamParser = new StreamContentParser()

// 模拟不同类型的流式数据
export const mockStreamData = {
  // 纯文本流
  plainText: "你好！我是AI助手。我可以帮助您解决各种问题，包括回答问题、分析数据、生成内容等。现在您可以看到我正在逐字打字，就像真人对话一样！😊",
  
  // Markdown流
  markdownText: `# 🤖 AI助手智能回复

感谢您的提问！让我为您详细解答。

## 📋 主要内容

我将从以下几个方面来回答：

1. **核心观点**：基于数据分析的结论
2. **技术实现**：具体的解决方案
3. **实际应用**：真实场景中的使用

### 💡 关键信息

这里有一个重要的\`代码示例\`：

\`\`\`javascript
// AI智能处理函数
function processUserQuery(query) {
  const result = analyzeIntent(query);
  return generateResponse(result);
}

// console.log("正在处理您的请求...");
\`\`\`

### 📊 数据分析

| 指标 | 数值 | 趋势 |
|------|------|------|
| 准确率 | 95% | ⬆️ |
| 响应速度 | 0.5s | ⬆️ |
| 用户满意度 | 4.8/5 | ⬆️ |

> **重要提示**：以上数据基于最新的测试结果，持续优化中。

希望这个详细的回答能够帮助到您！如果还有其他问题，请随时询问。😊`,

  // 包含图表的流
  chartText: `根据您的数据，我生成了以下分析图表：

[CHART:bar]
{
  "title": "月度销售数据",
  "labels": ["1月", "2月", "3月", "4月", "5月", "6月"],
  "datasets": [{
    "label": "销售额(万元)",
    "data": [65, 59, 80, 81, 56, 55],
    "backgroundColor": "rgba(59, 130, 246, 0.8)"
  }]
}
[/CHART]

从图表可以看出，3月和4月的销售表现最佳。`,

  // 包含图片的流
  imageText: `这里是一些相关的图片资料：

![示例图片1](https://picsum.photos/400/300?random=1)

![示例图片2](https://picsum.photos/400/300?random=2)

这些图片展示了相关的内容信息。`,

  // 包含音频的流
  audioText: `我为您准备了一段音频解说：

[AUDIO]
{
  "url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
  "title": "示例音频解说",
  "duration": 30
}
[/AUDIO]

这段音频包含了详细的解释说明。`,

  // 包含视频的流
  videoText: `这里有一个相关的视频教程：

[VIDEO]
{
  "url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
  "title": "AI技术演示视频",
  "duration": 60,
  "poster": "https://picsum.photos/640/360?random=3"
}
[/VIDEO]

视频中展示了具体的操作步骤和技术原理。`,

  // 包含文件的流
  fileText: `我为您准备了一些相关的文档资料：

[FILE]
{
  "name": "AI技术白皮书.pdf",
  "url": "https://example.com/whitepaper.pdf",
  "type": "application/pdf",
  "size": 2048000
}
[/FILE]

[FILE]
{
  "name": "数据分析报告.xlsx",
  "url": "https://example.com/report.xlsx",
  "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "size": 1024000
}
[/FILE]

这些文档包含了详细的技术资料和分析数据。`,

  // 包含流程图的流
  flowchartText: `让我用流程图来解释这个过程：

[FLOWCHART:mermaid]
graph TD
    A[用户输入问题] --> B{AI理解分析}
    B --> C[检索相关知识]
    B --> D[生成初步回答]
    C --> E[整合信息]
    D --> E
    E --> F[优化回答内容]
    F --> G[流式输出回答]
    G --> H[用户获得回答]
[/FLOWCHART]

这个流程图清晰地展示了AI处理问题的完整过程。`,

  // HTML内容流
  htmlText: `<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; color: white;">
<h2 style="margin: 0 0 15px 0;">🎉 特殊格式内容</h2>
<p style="margin: 0 0 10px 0;">这是一段包含HTML格式的内容，展示了丰富的样式效果。</p>
<ul style="margin: 0; padding-left: 20px;">
<li>支持各种HTML标签</li>
<li>可以自定义样式</li>
<li>提供更丰富的展示效果</li>
</ul>
</div>

<p style="margin-top: 15px;">HTML渲染器可以处理复杂的格式化内容。</p>`,

  // 复合内容流（包含多种类型）
  mixedContent: `# 🚀 综合演示内容

这是一个包含多种渲染类型的综合示例：

## 📊 数据可视化

首先，让我们看看数据图表：

[CHART:line]
{
  "title": "AI模型性能趋势",
  "labels": ["Q1", "Q2", "Q3", "Q4"],
  "datasets": [{
    "label": "准确率(%)",
    "data": [85, 88, 92, 95],
    "borderColor": "rgb(59, 130, 246)",
    "backgroundColor": "rgba(59, 130, 246, 0.1)"
  }]
}
[/CHART]

## 🖼️ 相关图片

![AI技术架构图](https://picsum.photos/500/300?random=4)

## 📁 参考资料

[FILE]
{
  "name": "技术规范文档.docx",
  "url": "https://example.com/spec.docx",
  "type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "size": 512000
}
[/FILE]

## 🎵 音频说明

[AUDIO]
{
  "url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
  "title": "技术要点解说",
  "duration": 45
}
[/AUDIO]

这样的综合内容展示了AI助手的多媒体处理能力！`
}
