<template>
  <div class="file-renderer">
    <!-- PDF预览 -->
    <div v-if="contentType === 'pdf'" class="pdf-viewer">
      <div class="file-header">
        <div class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="file-actions">
          <el-button @click="downloadFile" size="small" type="primary">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>
      <div class="pdf-container">
        <iframe 
          :src="pdfViewerUrl" 
          class="pdf-iframe"
          frameborder="0"
        ></iframe>
      </div>
    </div>

    <!-- Word文档预览 -->
    <div v-else-if="contentType === 'word'" class="word-viewer">
      <div class="file-header">
        <div class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="file-actions">
          <el-button @click="downloadFile" size="small" type="primary">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>
      <div class="word-container">
        <div class="preview-placeholder">
          <el-icon class="large-icon"><Document /></el-icon>
          <p>Word文档预览</p>
          <p class="hint">点击下载查看完整内容</p>
        </div>
      </div>
    </div>

    <!-- Excel表格预览 -->
    <div v-else-if="contentType === 'excel'" class="excel-viewer">
      <div class="file-header">
        <div class="file-info">
          <el-icon class="file-icon"><Grid /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="file-actions">
          <el-button @click="downloadFile" size="small" type="primary">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>
      <div class="excel-container">
        <div class="preview-placeholder">
          <el-icon class="large-icon"><Grid /></el-icon>
          <p>Excel表格预览</p>
          <p class="hint">点击下载查看完整内容</p>
        </div>
      </div>
    </div>

    <!-- 文本文件预览 -->
    <div v-else-if="contentType === 'text'" class="text-viewer">
      <div class="file-header">
        <div class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="file-actions">
          <el-button @click="downloadFile" size="small" type="primary">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>
      <div class="text-container">
        <div v-if="textContent" class="text-preview">
          <pre>{{ textContent }}</pre>
        </div>
        <div v-else class="loading-placeholder">
          <el-icon class="animate-spin"><Loading /></el-icon>
          <p>正在加载文件内容...</p>
        </div>
      </div>
    </div>

    <!-- 未知文件类型 -->
    <div v-else class="unknown-file">
      <div class="file-header">
        <div class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="file-actions">
          <el-button @click="downloadFile" size="small" type="primary">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>
      <div class="unknown-container">
        <div class="preview-placeholder">
          <el-icon class="large-icon"><Document /></el-icon>
          <p>无法预览此文件类型</p>
          <p class="hint">点击下载查看文件</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, defineProps, defineEmits } from 'vue'
import { Document, Download, Grid, Loading } from '@element-plus/icons-vue'

// 接口定义
interface FileContent {
  name: string
  url: string
  type: string
  size?: number
}

// Props定义
const props = defineProps<{
  file: FileContent
  contentType: 'pdf' | 'word' | 'excel' | 'text'
}>()

// Emits定义
const emit = defineEmits<{
  download: [file: FileContent]
}>()

// 响应式数据
const textContent = ref<string>('')

// 计算属性
const pdfViewerUrl = computed(() => {
  if (props.contentType === 'pdf') {
    return `${props.file.url}#toolbar=1&navpanes=1&scrollbar=1&view=FitH`
  }
  return ''
})

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 下载文件
const downloadFile = () => {
  const link = document.createElement('a')
  link.href = props.file.url
  link.download = props.file.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  emit('download', props.file)
}

// 加载文本文件内容
const loadTextContent = async () => {
  if (props.contentType === 'text') {
    try {
      const response = await fetch(props.file.url)
      const text = await response.text()
      textContent.value = text.length > 5000 ? text.substring(0, 5000) + '\n\n... (文件内容过长，已截断)' : text
    } catch (error) {
      console.error('加载文本文件失败:', error)
      textContent.value = '无法加载文件内容'
    }
  }
}

onMounted(() => {
  loadTextContent()
})
</script>

<style scoped>
.file-renderer {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #6b7280;
  font-size: 18px;
}

.file-name {
  font-weight: 500;
  color: #374151;
}

.file-size {
  color: #6b7280;
  font-size: 12px;
}

.pdf-container,
.word-container,
.excel-container,
.text-container,
.unknown-container {
  height: 400px;
  overflow: hidden;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
}

.text-preview {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.text-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.preview-placeholder,
.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
}

.large-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d1d5db;
}

.preview-placeholder p,
.loading-placeholder p {
  margin: 4px 0;
}

.hint {
  font-size: 12px;
  color: #9ca3af;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
