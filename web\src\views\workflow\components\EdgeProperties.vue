<template>
  <div class="edge-properties">
    <div class="property-header">
      <div class="edge-info">
        <div class="edge-icon">
          <i class="fas fa-arrow-right"></i>
        </div>
        <div>
          <div class="edge-title">连接线配置</div>
          <div class="edge-id">ID: {{ edge.id }}</div>
        </div>
      </div>
    </div>

    <div class="property-content">
      <!-- 基础属性 -->
      <div class="property-section">
        <h4 class="section-title">基础属性</h4>
        
        <div class="form-group">
          <label>标签</label>
          <input 
            v-model="localData.label" 
            type="text" 
            class="form-input"
            placeholder="连接线标签"
            @input="onUpdate"
          />
        </div>

        <div class="form-group">
          <label>连接类型</label>
          <select 
            v-model="localData.type" 
            class="form-select"
            @change="onUpdate"
          >
            <option value="default">默认</option>
            <option value="straight">直线</option>
            <option value="step">阶梯</option>
            <option value="smoothstep">平滑阶梯</option>
            <option value="bezier">贝塞尔曲线</option>
          </select>
        </div>

        <div class="form-group">
          <label>源节点</label>
          <div class="connection-info">
            <span class="node-name">{{ getNodeName(edge.source) }}</span>
            <span class="handle-name">{{ edge.sourceHandle || 'default' }}</span>
          </div>
        </div>

        <div class="form-group">
          <label>目标节点</label>
          <div class="connection-info">
            <span class="node-name">{{ getNodeName(edge.target) }}</span>
            <span class="handle-name">{{ edge.targetHandle || 'default' }}</span>
          </div>
        </div>
      </div>

      <!-- 样式配置 -->
      <div class="property-section">
        <h4 class="section-title">样式配置</h4>
        
        <div class="form-group">
          <label>线条颜色</label>
          <input 
            v-model="localStyle.stroke" 
            type="color" 
            class="form-input color-input"
            @input="onStyleUpdate"
          />
        </div>

        <div class="form-group">
          <label>线条宽度</label>
          <input 
            v-model.number="localStyle.strokeWidth" 
            type="number" 
            min="1" 
            max="10"
            class="form-input"
            @input="onStyleUpdate"
          />
        </div>

        <div class="form-group">
          <label>线条样式</label>
          <select 
            v-model="localStyle.strokeDasharray" 
            class="form-select"
            @change="onStyleUpdate"
          >
            <option value="">实线</option>
            <option value="5,5">虚线</option>
            <option value="10,5">长虚线</option>
            <option value="2,3">点线</option>
            <option value="5,5,2,5">点划线</option>
          </select>
        </div>

        <div class="form-group">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input 
                v-model="localData.animated" 
                type="checkbox"
                @change="onUpdate"
              />
              <span class="checkmark"></span>
              动画效果
            </label>
          </div>
        </div>
      </div>

      <!-- 标记配置 -->
      <div class="property-section">
        <h4 class="section-title">箭头标记</h4>
        
        <div class="form-group">
          <label>起始标记</label>
          <select 
            v-model="localData.markerStart" 
            class="form-select"
            @change="onUpdate"
          >
            <option value="">无</option>
            <option value="arrow">箭头</option>
            <option value="arrowclosed">实心箭头</option>
          </select>
        </div>

        <div class="form-group">
          <label>结束标记</label>
          <select 
            v-model="localData.markerEnd" 
            class="form-select"
            @change="onUpdate"
          >
            <option value="">无</option>
            <option value="arrow">箭头</option>
            <option value="arrowclosed">实心箭头</option>
          </select>
        </div>
      </div>

      <!-- 条件配置 -->
      <div class="property-section" v-if="isConditionalEdge">
        <h4 class="section-title">条件配置</h4>
        
        <div class="form-group">
          <label>条件表达式</label>
          <textarea 
            v-model="localData.condition" 
            class="form-textarea"
            rows="3"
            placeholder="输入条件表达式，如：result === 'success'"
            @input="onUpdate"
          ></textarea>
        </div>

        <div class="form-group">
          <label>条件描述</label>
          <input 
            v-model="localData.conditionDesc" 
            type="text" 
            class="form-input"
            placeholder="条件描述"
            @input="onUpdate"
          />
        </div>
      </div>

      <!-- 数据传递配置 -->
      <div class="property-section">
        <h4 class="section-title">数据传递</h4>
        
        <div class="form-group">
          <label>数据映射</label>
          <textarea 
            v-model="localData.dataMapping" 
            class="form-textarea"
            rows="4"
            placeholder="JSON格式的数据映射配置"
            @input="onUpdate"
          ></textarea>
        </div>

        <div class="form-group">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input 
                v-model="localData.passThrough" 
                type="checkbox"
                @change="onUpdate"
              />
              <span class="checkmark"></span>
              透传所有数据
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="property-actions">
      <button 
        class="btn btn-secondary"
        @click="insertNode"
      >
        <i class="fas fa-plus"></i>
        插入节点
      </button>
      
      <button 
        class="btn btn-danger"
        @click="deleteEdge"
      >
        <i class="fas fa-trash"></i>
        删除
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { Edge } from '@vue-flow/core'
import { storeToRefs } from 'pinia'

// 导入全局状态管理
import { useWorkflowStore } from '@/stores/workflowStore'

// Props
interface Props {
  edge: Edge
  nodes?: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [edgeId: string, updates: any]
  'delete': [edgeId: string]
  'insert-node': [data: { edgeId: string; position: { x: number; y: number }; sourceId: string; targetId: string }]
}>()

// 使用全局状态管理
const workflowStore = useWorkflowStore()
const { nodes: globalNodes } = storeToRefs(workflowStore)

// 响应式数据
const localData = ref({
  label: '',
  type: 'default',
  animated: false,
  markerStart: '',
  markerEnd: 'arrowclosed',
  condition: '',
  conditionDesc: '',
  dataMapping: '',
  passThrough: true
})

const localStyle = ref({
  stroke: '#b1b1b7',
  strokeWidth: 2,
  strokeDasharray: ''
})

// 计算属性
const isConditionalEdge = computed(() => {
  // 判断是否为条件连接线（从条件节点出发的连接线）
  const nodes = globalNodes.value.length > 0 ? globalNodes.value : (props.nodes || [])
  const sourceNode = nodes.find(n => n.id === props.edge.source)
  return sourceNode?.type === 'condition'
})

// 方法
const getNodeName = (nodeId: string) => {
  const nodes = globalNodes.value.length > 0 ? globalNodes.value : (props.nodes || [])
  const node = nodes.find(n => n.id === nodeId)
  return node?.data?.label || node?.type || nodeId
}

const onUpdate = () => {
  emit('update', props.edge.id, {
    ...localData.value,
    data: { ...props.edge.data, ...localData.value }
  })
}

const onStyleUpdate = () => {
  emit('update', props.edge.id, {
    style: localStyle.value
  })
}

const insertNode = () => {
  // 显示节点选择弹窗或直接插入默认节点
  const insertPosition = {
    x: (getNodePosition(props.edge.source).x + getNodePosition(props.edge.target).x) / 2,
    y: (getNodePosition(props.edge.source).y + getNodePosition(props.edge.target).y) / 2
  }

  emit('insert-node', {
    edgeId: props.edge.id,
    position: insertPosition,
    sourceId: props.edge.source,
    targetId: props.edge.target
  })
}

const getNodePosition = (nodeId: string) => {
  const nodes = globalNodes.value.length > 0 ? globalNodes.value : (props.nodes || [])
  const node = nodes.find(n => n.id === nodeId)
  return node?.position || { x: 0, y: 0 }
}

const deleteEdge = () => {
  if (confirm('确定要删除此连接线吗？')) {
    emit('delete', props.edge.id)
  }
}

// 初始化数据
const initializeData = () => {
  localData.value = {
    label: (props.edge as any).label || '',
    type: props.edge.type || 'default',
    animated: (props.edge as any).animated || false,
    markerStart: (props.edge as any).markerStart || '',
    markerEnd: (props.edge as any).markerEnd || 'arrowclosed',
    condition: props.edge.data?.condition || '',
    conditionDesc: props.edge.data?.conditionDesc || '',
    dataMapping: props.edge.data?.dataMapping || '',
    passThrough: props.edge.data?.passThrough !== false
  }
  
  localStyle.value = {
    stroke: (props.edge.style as any)?.stroke || '#b1b1b7',
    strokeWidth: (props.edge.style as any)?.strokeWidth || 2,
    strokeDasharray: (props.edge.style as any)?.strokeDasharray || ''
  }
}

// 监听边变化
watch(() => props.edge, () => {
  initializeData()
}, { immediate: true })

// 生命周期
onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.edge-properties {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.property-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.edge-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.edge-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edge-icon i {
  color: #6b7280;
  font-size: 18px;
}

.edge-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.edge-id {
  font-size: 12px;
  color: #9ca3af;
  font-family: monospace;
}

.property-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.property-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
}

.color-input {
  height: 40px;
  padding: 4px;
}

.connection-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.node-name {
  font-weight: 500;
  color: #1f2937;
}

.handle-name {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.property-actions {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  justify-content: center;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-danger:hover {
  background: #fee2e2;
}
</style>
