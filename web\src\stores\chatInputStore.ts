import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 接口定义
export interface UploadedFile {
  name: string
  file: File
}

export interface UploadedImage {
  name: string
  url: string
  file: File
}

export interface MessageData {
  content: string
  files: File[]
  images: string[]
  audio: Blob | null
}

// 工具类型定义
export type ToolType = 'police-analysis' | 'document-analysis' | 'case-analysis' | 'legal-consultation' | 'report-generation' | 'evidence-analysis'

// 事件处理器类型定义
export interface ChatInputEventHandlers {
  onSendMessage?: (messageData: MessageData) => void | Promise<void>
  onFileUpload?: (file: File) => void
  onImageUpload?: (file: File) => void
  onToolSelect?: (tool: ToolType) => void
  onVoiceRecordingStart?: () => Promise<void>
  onVoiceRecordingStop?: () => void
  onVoiceModeToggle?: () => void
  onInputMessageUpdate?: (value: string) => void
  onRemoveFile?: (index: number) => void
  onRemoveImage?: (index: number) => void
  onRemoveRecordedAudio?: () => void
  onPlayRecordedAudio?: () => void
  onRendererSelect?: (renderer: string) => void
}

export const useChatInputStore = defineStore('chatInput', () => {
  // 状态管理
  const inputMessage = ref('')
  const uploadedFiles = ref<UploadedFile[]>([])
  const uploadedImages = ref<UploadedImage[]>([])
  const recordedAudio = ref<Blob | null>(null)
  const isRecording = ref(false)
  const isVoiceMode = ref(false)
  const recordingTime = ref(0)
  const recordingDuration = ref(0)
  const isSending = ref(false)

  // 事件处理器注册
  const eventHandlers = ref<ChatInputEventHandlers>({})

  // 计算属性
  const canSend = computed(() => {
    return (
      inputMessage.value.trim() || 
      uploadedFiles.value.length > 0 || 
      uploadedImages.value.length > 0 || 
      recordedAudio.value
    ) && !isSending.value
  })

  // 注册事件处理器
  const registerEventHandlers = (handlers: ChatInputEventHandlers) => {
    eventHandlers.value = { ...eventHandlers.value, ...handlers }
  }

  // 清空事件处理器
  const clearEventHandlers = () => {
    eventHandlers.value = {}
  }

  // 统一的事件处理方法
  const handleSendMessage = async () => {
    // 在设置isSending之前检查canSend
    if (!canSend.value) return

    // 先保存消息数据（在设置isSending之前）
    const messageData: MessageData = {
      content: inputMessage.value.trim(),
      files: uploadedFiles.value.map(f => f.file),
      images: uploadedImages.value.map(img => img.url),
      audio: recordedAudio.value
    }

    // 设置发送状态，防止重复发送
    isSending.value = true

    // 立即清空输入，提供更好的用户体验
    clearInputs()

    try {
      // 调用注册的处理器
      if (eventHandlers.value.onSendMessage) {
        await eventHandlers.value.onSendMessage(messageData)
      }
    } finally {
      // 确保发送状态被重置
      isSending.value = false
    }
  }

  const handleFileUpload = (files: FileList, type: 'file' | 'image' | 'id') => {
    if (type === 'file') {
      handleSingleFileUpload(files[0])
    } else if (type === 'image') {
      handleSingleImageUpload(files[0])
    } else if (type === 'id') {
      // 处理证件上传
      console.log('证件上传:', files)
    }
  }

  const handleSingleFileUpload = (file: File) => {
    if (eventHandlers.value.onFileUpload) {
      eventHandlers.value.onFileUpload(file)
    } else {
      // 默认处理
      uploadedFiles.value.push({
        name: file.name,
        file: file
      })
    }
  }

  const handleSingleImageUpload = (file: File) => {
    if (eventHandlers.value.onImageUpload) {
      eventHandlers.value.onImageUpload(file)
    } else {
      // 默认处理
      const reader = new FileReader()
      reader.onload = (e) => {
        uploadedImages.value.push({
          name: file.name,
          url: e.target?.result as string,
          file: file
        })
      }
      reader.readAsDataURL(file)
    }
  }

  const handleToolSelect = (tool: ToolType) => {
    if (eventHandlers.value.onToolSelect) {
      eventHandlers.value.onToolSelect(tool)
    } else {
      // 默认处理：设置提示词
      const toolPrompts = {
        'police-analysis': '请帮我分析以下警情信息：',
        'document-analysis': '请帮我分析以下公文内容：',
        'case-analysis': '请帮我进行案件研判分析：',
        'legal-consultation': '请为我提供法律咨询：',
        'report-generation': '请帮我生成相关报告：',
        'evidence-analysis': '请帮我分析以下证据材料：'
      }
      const prompt = toolPrompts[tool] || '请帮我：'
      inputMessage.value = prompt
    }
  }

  const handleToggleVoiceRecording = async () => {
    if (isRecording.value) {
      if (eventHandlers.value.onVoiceRecordingStop) {
        eventHandlers.value.onVoiceRecordingStop()
      }
    } else {
      if (eventHandlers.value.onVoiceRecordingStart) {
        await eventHandlers.value.onVoiceRecordingStart()
      }
    }
  }

  const handleToggleVoiceMode = () => {
    if (eventHandlers.value.onVoiceModeToggle) {
      eventHandlers.value.onVoiceModeToggle()
    } else {
      isVoiceMode.value = !isVoiceMode.value
    }
  }

  const handleInputMessageUpdate = (value: string) => {
    inputMessage.value = value
    if (eventHandlers.value.onInputMessageUpdate) {
      eventHandlers.value.onInputMessageUpdate(value)
    }
  }

  const handleRemoveFile = (index: number) => {
    uploadedFiles.value.splice(index, 1)
    if (eventHandlers.value.onRemoveFile) {
      eventHandlers.value.onRemoveFile(index)
    }
  }

  const handleRemoveImage = (index: number) => {
    uploadedImages.value.splice(index, 1)
    if (eventHandlers.value.onRemoveImage) {
      eventHandlers.value.onRemoveImage(index)
    }
  }

  const handleRemoveRecordedAudio = () => {
    recordedAudio.value = null
    if (eventHandlers.value.onRemoveRecordedAudio) {
      eventHandlers.value.onRemoveRecordedAudio()
    }
  }

  const handlePlayRecordedAudio = () => {
    if (eventHandlers.value.onPlayRecordedAudio) {
      eventHandlers.value.onPlayRecordedAudio()
    }
  }

  const handleRendererSelect = (renderer: string) => {
    if (eventHandlers.value.onRendererSelect) {
      eventHandlers.value.onRendererSelect(renderer)
    }
  }

  // 工具方法
  const clearInputs = () => {
    inputMessage.value = ''
    uploadedFiles.value = []
    uploadedImages.value = []
    recordedAudio.value = null
  }

  const resetState = () => {
    clearInputs()
    isRecording.value = false
    isVoiceMode.value = false
    recordingTime.value = 0
    recordingDuration.value = 0
    isSending.value = false
  }

  return {
    // 状态
    inputMessage,
    uploadedFiles,
    uploadedImages,
    recordedAudio,
    isRecording,
    isVoiceMode,
    recordingTime,
    recordingDuration,
    isSending,
    canSend,

    // 事件处理器管理
    registerEventHandlers,
    clearEventHandlers,

    // 统一的事件处理方法
    handleSendMessage,
    handleFileUpload,
    handleSingleFileUpload,
    handleSingleImageUpload,
    handleToolSelect,
    handleToggleVoiceRecording,
    handleToggleVoiceMode,
    handleInputMessageUpdate,
    handleRemoveFile,
    handleRemoveImage,
    handleRemoveRecordedAudio,
    handlePlayRecordedAudio,
    handleRendererSelect,

    // 工具方法
    clearInputs,
    resetState
  }
})
