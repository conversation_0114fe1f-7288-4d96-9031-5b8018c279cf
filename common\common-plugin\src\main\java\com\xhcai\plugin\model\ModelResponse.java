package com.xhcai.plugin.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 模型响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelResponse {
    
    /**
     * 响应ID
     */
    private String id;
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 生成的选择列表
     */
    private List<ModelChoice> choices;
    
    /**
     * 使用情况统计
     */
    private ModelUsage usage;
    
    /**
     * 创建时间
     */
    private LocalDateTime created;
    
    /**
     * 响应类型
     */
    private String object;
    
    /**
     * 额外属性
     */
    private Map<String, Object> metadata;
    
    /**
     * 是否完成
     */
    private Boolean finished;
    
    /**
     * 完成原因
     */
    private String finishReason;
    
    /**
     * 错误信息（如果有）
     */
    private String error;
}
