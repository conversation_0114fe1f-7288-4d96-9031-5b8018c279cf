package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 角色信息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_role", indexes = {
    @Index(name = "uk_sys_role_code", columnList = "role_code, tenant_id", unique = true),
    @Index(name = "idx_sys_role_tenant_id", columnList = "tenant_id"),
    @Index(name = "idx_sys_role_status", columnList = "status")
})
@Schema(description = "角色信息")
@TableName("sys_role")
public class SysRole extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 角色编码
     */
    @jakarta.persistence.Column(name = "role_code", nullable = false, length = 50)
    @Schema(description = "角色编码", example = "ROLE_ADMIN")
    @NotBlank(message = "角色编码不能为空")
    @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
    @TableField("role_code")
    private String roleCode;

    /**
     * 角色名称
     */
    @jakarta.persistence.Column(name = "role_name", nullable = false, length = 50)
    @Schema(description = "角色名称", example = "超级管理员")
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
    @TableField("role_name")
    private String roleName;

    /**
     * 显示顺序
     */
    @jakarta.persistence.Column(name = "role_sort")
    @Schema(description = "显示顺序", example = "1")
    @TableField("role_sort")
    private Integer roleSort;

    /**
     * 数据范围
     */
    @jakarta.persistence.Column(name = "data_scope", length = 1)
    @Schema(description = "数据范围", example = "1", allowableValues = {"1", "2", "3", "4", "5"})
    @Pattern(regexp = "^[12345]$", message = "数据范围值必须为1-5")
    @TableField("data_scope")
    private String dataScope;

    /**
     * 菜单树选择项是否关联显示
     */
    @Schema(description = "菜单树选择项是否关联显示")
    @TableField("menu_check_strictly")
    private Boolean menuCheckStrictly;

    /**
     * 部门树选择项是否关联显示
     */
    @Schema(description = "部门树选择项是否关联显示")
    @TableField("dept_check_strictly")
    private Boolean deptCheckStrictly;

    /**
     * 状态
     */
    @jakarta.persistence.Column(name = "status", length = 1)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    // Getters and Setters
    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getRoleSort() {
        return roleSort;
    }

    public void setRoleSort(Integer roleSort) {
        this.roleSort = roleSort;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    public Boolean getMenuCheckStrictly() {
        return menuCheckStrictly;
    }

    public void setMenuCheckStrictly(Boolean menuCheckStrictly) {
        this.menuCheckStrictly = menuCheckStrictly;
    }

    public Boolean getDeptCheckStrictly() {
        return deptCheckStrictly;
    }

    public void setDeptCheckStrictly(Boolean deptCheckStrictly) {
        this.deptCheckStrictly = deptCheckStrictly;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SysRole{"
                + "roleCode='" + roleCode + '\''
                + ", roleName='" + roleName + '\''
                + ", roleSort=" + roleSort
                + ", dataScope='" + dataScope + '\''
                + ", menuCheckStrictly=" + menuCheckStrictly
                + ", deptCheckStrictly=" + deptCheckStrictly
                + ", status='" + status + '\''
                + "} " + super.toString();
    }
}
