<template>
  <div class="permission-by-role-selector" :class="{ 'is-dropdown': isDropdownMode }">
    <!-- 下拉模式 -->
    <DropdownSelector
      v-if="isDropdownMode"
      v-model="selectedPermissionIds"
      :placeholder="config.placeholder"
      :disabled="config.disabled"
      :clearable="config.clearable"
      :size="config.size"
      :show-actions="config.multiple"
      :dropdown-class="'permission-by-role-dropdown-panel'"
      :align="align"
      @change="handleDropdownChange"
      @clear="clearSelection"
    >
      <template #display>
        <span v-if="!hasSelection" class="placeholder-text">{{ config.placeholder }}</span>
        <div v-else class="selected-display">
          <span v-if="!config.multiple" class="single-selected">
            {{ displayText }}
          </span>
          <div v-else class="multiple-selected">
            <span class="selected-count">{{ displayText }}</span>
          </div>
        </div>
      </template>

      <!-- 下拉内容 -->
      <div class="dropdown-permission-by-role-selector">
        <PermissionByRoleSelectorContent
          ref="contentRef"
          v-model="selectedPermissionIds"
          :config="config"
          :only-enabled="onlyEnabled"
          :exclude-permission-ids="excludePermissionIds"
          @change="handleContentChange"
          @select="handleSelect"
          @remove="handleRemove"
          @role-change="handleRoleChange"
        />
      </div>
    </DropdownSelector>

    <!-- 嵌入模式 -->
    <div v-else class="embedded-mode">
      <PermissionByRoleSelectorContent
        v-model="selectedPermissionIds"
        :config="config"
        :only-enabled="onlyEnabled"
        :exclude-permission-ids="excludePermissionIds"
        :show-header="true"
        @change="handleContentChange"
        @select="handleSelect"
        @remove="handleRemove"
        @role-change="handleRoleChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import DropdownSelector from './DropdownSelector.vue'
import PermissionByRoleSelectorContent from './PermissionByRoleSelectorContent.vue'
import type { PermissionSelectorOption, RoleSelectorOption, SelectorConfig } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludePermissionIds?: string[]
  mode?: 'dropdown' | 'embedded'
  align?: 'left' | 'right' | 'center'
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], options: PermissionSelectorOption[]): void
  (e: 'select', value: string, option: PermissionSelectorOption): void
  (e: 'remove', value: string): void
  (e: 'roleChange', roleId: string, role: RoleSelectorOption): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludePermissionIds: () => [],
  mode: 'embedded',
  align: 'left'
})

const emit = defineEmits<Emits>()

// 响应式数据 - 确保每个组件实例都有独立的数据
const selectedPermissionIds = ref<string | string[]>(
  Array.isArray(props.modelValue) ? [...props.modelValue] : props.modelValue || (props.config.multiple ? [] : '')
)
const selectedPermissionOptions = ref<PermissionSelectorOption[]>([])
const contentRef = ref<InstanceType<typeof PermissionByRoleSelectorContent>>()

// 计算属性
const isDropdownMode = computed(() => props.mode === 'dropdown')

const mergedConfig = computed(() => ({
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择权限',
  size: 'default',
  disabled: false,
  checkStrictly: false,
  ...props.config
}))

const config = computed(() => mergedConfig.value)

const hasSelection = computed(() => {
  return selectedPermissionIds.value !== null &&
         selectedPermissionIds.value !== undefined &&
         selectedPermissionIds.value !== '' &&
         (Array.isArray(selectedPermissionIds.value) ? selectedPermissionIds.value.length > 0 : true)
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
    return selectedPermissionIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

// 计算显示文本
const displayText = computed(() => {
  if (!hasSelection.value) return ''

  if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
    return `已选择 ${selectedPermissionIds.value.length} 个权限`
  } else {
    const selectedOption = selectedPermissionOptions.value.find(option =>
      option.value === selectedPermissionIds.value
    )
    return selectedOption ? selectedOption.label : String(selectedPermissionIds.value)
  }
})

// 方法
const getSelectedPermissionLabels = () => {
  if (!hasSelection.value) return []

  if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
    return selectedPermissionOptions.value
      .filter(option => selectedPermissionIds.value.includes(option.value))
      .map(option => option.label)
  } else {
    const selectedOption = selectedPermissionOptions.value.find(option =>
      option.value === selectedPermissionIds.value
    )
    return selectedOption ? [selectedOption.label] : []
  }
}

const clearSelection = () => {
  const clearValue = config.value.multiple ? [] : ''
  selectedPermissionIds.value = clearValue
  emit('update:modelValue', clearValue)
  emit('change', clearValue, [])
}

// 事件处理
const handleDropdownChange = (value: string | string[]) => {
  selectedPermissionIds.value = value
  emit('update:modelValue', value)
}

const handleContentChange = (value: string | string[], options: PermissionSelectorOption[]) => {
  selectedPermissionIds.value = Array.isArray(value) ? [...value] : value
  selectedPermissionOptions.value = [...options]
  emit('update:modelValue', selectedPermissionIds.value)
  emit('change', selectedPermissionIds.value, options)
}

const handleSelect = (value: string, option: PermissionSelectorOption) => {
  emit('select', value, option)
}

const handleRemove = (value: string) => {
  emit('remove', value)
}

const handleRoleChange = (roleId: string, role: RoleSelectorOption) => {
  emit('roleChange', roleId, role)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedPermissionIds.value = Array.isArray(newValue) ? [...newValue] : newValue || (config.value.multiple ? [] : '')
  // 清空选项缓存，让子组件重新获取
  if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
    selectedPermissionOptions.value = []
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  clearSelection
})





</script>

<style scoped>
.permission-by-role-selector {
  width: 100%;
}

.permission-by-role-selector.is-dropdown {
  display: inline-block;
}

.placeholder-text {
  color: #c0c4cc;
}

.selected-display {
  color: #606266;
}

.single-selected {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multiple-selected {
  display: flex;
  align-items: center;
}

.selected-count {
  font-size: 14px;
  color: #606266;
}

.dropdown-permission-by-role-selector {
  min-width: 400px;
}

.embedded-mode {
  width: 100%;
}

/* 下拉面板样式 */
:deep(.permission-by-role-dropdown-panel) {
  min-width: 450px;
}

:deep(.permission-by-role-dropdown-panel .dropdown-content) {
  padding: 0;
}

:deep(.permission-by-role-dropdown-panel .permission-by-role-selector-content) {
  border: none;
  box-shadow: none;
}
</style>
