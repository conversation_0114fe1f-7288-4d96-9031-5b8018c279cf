package com.xhcai.modules.system.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典数据VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "字典数据信息")
public class SysDictDataVO {

    /**
     * 字典数据ID
     */
    @Schema(description = "字典数据ID")
    private String id;

    /**
     * 字典排序
     */
    @Schema(description = "字典排序")
    private Integer dictSort;

    /**
     * 字典标签
     */
    @Schema(description = "字典标签")
    private String dictLabel;

    /**
     * 字典键值
     */
    @Schema(description = "字典键值")
    private String dictValue;

    /**
     * 字典类型
     */
    @Schema(description = "字典类型")
    private String dictType;

    /**
     * 样式属性
     */
    @Schema(description = "样式属性")
    private String cssClass;

    /**
     * 表格回显样式
     */
    @Schema(description = "表格回显样式")
    private String listClass;

    /**
     * 是否默认
     */
    @Schema(description = "是否默认")
    private String isDefault;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 删除标记 审计字段 - 逻辑删除标记，0=正常，1=删除
     * 参考 DeletedEnum 的枚举类，0=正常，1=删除
     */
    @Schema(description = "删除标记")
    private Integer deleted = 0;

    /**
     * 是否为系统字典
     */
    @Schema(description = "是否为系统字典", example = "N", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "是否为系统字典值必须为Y或N")
    private String isSystemDict = "N";

    @Override
    public String toString() {
        return "SysDictDataVO{" +
                "id=" + id +
                ", dictSort=" + dictSort +
                ", dictLabel='" + dictLabel + '\'' +
                ", dictValue='" + dictValue + '\'' +
                ", dictType='" + dictType + '\'' +
                ", cssClass='" + cssClass + '\'' +
                ", listClass='" + listClass + '\'' +
                ", isDefault='" + isDefault + '\'' +
                ", status='" + status + '\'' +
                ", tenantId=" + tenantId +
                ", remark='" + remark + '\'' +
                ", createBy=" + createBy +
                ", createTime=" + createTime +
                ", updateBy=" + updateBy +
                ", updateTime=" + updateTime +
                '}';
    }
}
