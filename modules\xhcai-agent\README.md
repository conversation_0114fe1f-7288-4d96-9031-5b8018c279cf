# XHC AI Agent Module

## 模块简介

xhcai-agent模块是XHC AI Plus平台中的智能体管理模块，提供了智能体的创建、配置、管理、对话等完整功能。该模块基于Spring Boot 3.5.3和Spring AI框架构建，支持多种AI模型和多租户架构。

## 功能特性

### 智能体管理
- ✅ 智能体创建、更新、删除
- ✅ 智能体列表查询和详情获取
- ✅ 智能体启用/禁用管理
- ✅ 智能体复制和版本管理
- ✅ 智能体发布和公开设置
- ✅ 智能体配置导入/导出
- ✅ 智能体统计信息

### 对话管理
- ✅ 阻塞式和流式对话
- ✅ 对话历史管理
- ✅ 对话状态跟踪
- ✅ 用户反馈收集
- ✅ 对话统计分析
- ✅ 过期对话清理

### 消息管理
- ✅ 消息发送和接收
- ✅ 消息历史记录
- ✅ 消息重新生成
- ✅ 消息状态管理
- ✅ Token使用统计
- ✅ 费用计算

### 权限控制
- ✅ RBAC权限模型
- ✅ 多租户数据隔离
- ✅ 细粒度权限控制
- ✅ 角色权限分配
- ✅ 数据权限过滤

## 技术架构

### 核心技术栈
- **Spring Boot 3.5.3** - 基础框架
- **Spring AI 1.0.0-M6** - AI集成框架
- **Spring Security 6.4.1** - 安全框架
- **MyBatis Plus 3.5.12** - 数据访问层
- **PostgreSQL** - 数据库
- **FastJSON2** - JSON处理
- **Swagger/OpenAPI** - API文档

### 架构设计
```
xhcai-agent/
├── config/              # 配置类
├── controller/          # 控制器层
├── dto/                # 数据传输对象
├── entity/             # 实体类
├── mapper/             # 数据访问层
├── service/            # 服务接口
├── service/impl/       # 服务实现
├── vo/                 # 视图对象
├── init/               # 初始化类
└── resources/
    ├── mapper/         # MyBatis映射文件
    └── application-agent.yml  # 模块配置
```

## 数据库设计

### 核心表结构

#### agent - 智能体表
```sql
CREATE TABLE agent (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    avatar VARCHAR(255),
    type VARCHAR(20) NOT NULL,
    model_config TEXT,
    system_prompt TEXT,
    tools_config TEXT,
    knowledge_config TEXT,
    conversation_config TEXT,
    status VARCHAR(1) DEFAULT '1',
    is_public BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    version VARCHAR(20) DEFAULT '1.0.0',
    published_at TIMESTAMP,
    last_conversation_at TIMESTAMP,
    conversation_count BIGINT DEFAULT 0,
    -- 审计字段
    tenant_id VARCHAR(36) NOT NULL,
    remark VARCHAR(500),
    deleted BOOLEAN DEFAULT FALSE,
    create_by VARCHAR(36),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(36),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### agent_conversation - 对话表
```sql
CREATE TABLE agent_conversation (
    id VARCHAR(36) PRIMARY KEY,
    agent_id VARCHAR(36) NOT NULL,
    title VARCHAR(200),
    user_id VARCHAR(36),
    session_id VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    message_count INTEGER DEFAULT 0,
    started_at TIMESTAMP,
    ended_at TIMESTAMP,
    last_activity_at TIMESTAMP,
    total_tokens BIGINT DEFAULT 0,
    input_tokens BIGINT DEFAULT 0,
    output_tokens BIGINT DEFAULT 0,
    cost BIGINT DEFAULT 0,
    rating INTEGER,
    feedback VARCHAR(1000),
    metadata TEXT,
    -- 审计字段
    tenant_id VARCHAR(36) NOT NULL,
    remark VARCHAR(500),
    deleted BOOLEAN DEFAULT FALSE,
    create_by VARCHAR(36),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(36),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### agent_message - 消息表
```sql
CREATE TABLE agent_message (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    message_type VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    sequence_number INTEGER,
    parent_message_id VARCHAR(36),
    status VARCHAR(20) DEFAULT 'sent',
    sent_at TIMESTAMP,
    received_at TIMESTAMP,
    processing_time BIGINT,
    tokens INTEGER,
    input_tokens INTEGER,
    output_tokens INTEGER,
    cost BIGINT,
    tool_calls TEXT,
    tool_results TEXT,
    error_message VARCHAR(1000),
    metadata TEXT,
    inputs TEXT COMMENT '输入参数（JSON格式）',
    query TEXT COMMENT '查询内容',
    external_info_id VARCHAR(36) COMMENT '外部信息ID',
    -- 审计字段
    tenant_id VARCHAR(36) NOT NULL,
    remark VARCHAR(500),
    deleted BOOLEAN DEFAULT FALSE,
    create_by VARCHAR(36),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(36),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 快速开始

### 1. 添加模块依赖

在父项目的`pom.xml`中添加模块：

```xml
<modules>
    <module>modules/xhcai-agent</module>
</modules>
```

在`admin-api`的`pom.xml`中添加依赖：

```xml
<dependency>
    <groupId>com.xhcai</groupId>
    <artifactId>xhcai-agent</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置AI服务

在`application.yml`中配置AI服务：

```yaml
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
          max-tokens: 2000
```

### 3. 配置模块参数

在`application.yml`中配置模块参数：

```yaml
agent:
  limits:
    max-agents-per-tenant: 100
    max-agents-per-user: 20
    max-conversations-per-user: 50
  cache:
    agent-config:
      enabled: true
      ttl: 3600
```

### 4. 启动应用

启动应用后，模块会自动：
- 创建数据库表
- 初始化权限和菜单
- 注册模块元信息

## API接口

### 智能体管理接口

```http
# 分页查询智能体列表
POST /api/agent/page

# 查询智能体详情
GET /api/agent/{id}

# 创建智能体
POST /api/agent

# 更新智能体
PUT /api/agent

# 删除智能体
DELETE /api/agent/{id}

# 启用/禁用智能体
PUT /api/agent/{id}/enable
PUT /api/agent/{id}/disable

# 复制智能体
POST /api/agent/{id}/copy

# 发布智能体
PUT /api/agent/{id}/publish
```

### 聊天接口

```http
# 发送消息（阻塞式）
POST /api/chat/send

# 发送消息（流式）
POST /api/chat/send/stream

# 获取对话历史
GET /api/chat/conversation/{conversationId}/history

# 提交用户反馈
POST /api/chat/conversation/{conversationId}/feedback

# 获取建议问题
GET /api/chat/agent/{agentId}/suggestions
```

## 权限配置

### 权限列表

| 权限代码 | 权限名称 | 说明 |
|---------|---------|------|
| agent:list | 查看智能体列表 | 查看智能体列表页面 |
| agent:detail | 查看智能体详情 | 查看智能体详细信息 |
| agent:create | 创建智能体 | 创建新的智能体 |
| agent:update | 更新智能体 | 修改智能体信息 |
| agent:delete | 删除智能体 | 删除智能体 |
| chat:send | 发送消息 | 与智能体对话 |
| chat:history | 查看对话历史 | 查看历史对话记录 |
| chat:manage | 管理对话 | 管理对话状态 |

### 角色权限分配

- **管理员**：拥有所有权限
- **普通用户**：拥有基本的智能体使用权限
- **访客**：只能查看和使用公开的智能体

## 配置说明

### 模块配置

```yaml
agent:
  default:
    model:
      name: "gpt-3.5-turbo"
      temperature: 0.7
      max-tokens: 2000
    system-prompt: "你是一个有用的AI助手..."
    conversation:
      max-history: 20
      timeout: 30000
      auto-cleanup: true
  limits:
    max-agents-per-tenant: 100
    max-conversations-per-user: 50
    max-tokens-per-day: 100000
  cache:
    agent-config:
      enabled: true
      ttl: 3600
```

### 环境变量

| 变量名 | 说明 | 默认值 |
|-------|------|--------|
| OPENAI_API_KEY | OpenAI API密钥 | - |
| OPENAI_BASE_URL | OpenAI API地址 | https://api.openai.com |
| OLLAMA_BASE_URL | Ollama服务地址 | http://localhost:11434 |

## 开发指南

### 扩展新功能

1. **添加新的智能体类型**
   - 在`Agent`实体中扩展`type`字段的枚举值
   - 在服务层添加相应的处理逻辑
   - 更新前端界面支持新类型

2. **集成新的AI模型**
   - 在配置中添加新模型的配置
   - 在`ChatService`中添加新模型的调用逻辑
   - 更新模型选择界面

3. **添加新的工具**
   - 实现工具接口
   - 在工具配置中注册新工具
   - 更新工具选择界面

### 测试

```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn integration-test

# 运行特定测试
mvn test -Dtest=AgentServiceTest
```

## 监控和运维

### 健康检查

访问 `/actuator/health` 查看模块健康状态

### 指标监控

访问 `/actuator/metrics` 查看模块指标：
- `agent.count` - 智能体数量
- `conversation.count` - 对话数量
- `message.count` - 消息数量
- `token.usage` - Token使用量

### 日志配置

```yaml
logging:
  level:
    com.xhcai.modules.agent: DEBUG
    org.springframework.ai: INFO
```

## 注意事项

1. **API密钥安全**：请妥善保管AI服务的API密钥，不要在代码中硬编码
2. **请求限制**：注意AI服务的请求频率和Token限制
3. **数据隔离**：确保多租户数据隔离的正确性
4. **性能优化**：合理使用缓存，避免频繁的数据库查询
5. **错误处理**：完善的错误处理和用户提示

## 更新日志

### v1.0.0 (2025-01-14)
- ✅ 初始版本发布
- ✅ 智能体管理功能
- ✅ 对话聊天功能
- ✅ 权限控制系统
- ✅ 多租户支持
- ✅ API文档和配置

## 技术支持

如有问题请联系开发团队或提交Issue。
