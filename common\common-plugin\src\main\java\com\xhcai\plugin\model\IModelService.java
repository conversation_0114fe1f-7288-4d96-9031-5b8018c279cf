package com.xhcai.plugin.model;

import org.pf4j.ExtensionPoint;

import java.util.List;
import java.util.Map;

/**
 * 模型服务接口
 * 统一的AI模型服务接口，支持 OpenAI、Claude、本地模型等不同实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IModelService extends ExtensionPoint {
    
    /**
     * 获取模型服务名称
     */
    String getServiceName();
    
    /**
     * 获取模型服务类型
     */
    String getServiceType();
    
    /**
     * 获取支持的模型列表
     */
    List<ModelInfo> getSupportedModels();
    
    /**
     * 初始化模型服务
     */
    void initialize(Map<String, Object> config);
    
    /**
     * 文本生成
     * 
     * @param request 生成请求
     * @return 生成响应
     */
    ModelResponse generateText(ModelRequest request);
    
    /**
     * 流式文本生成
     * 
     * @param request 生成请求
     * @param callback 流式回调
     */
    void generateTextStream(ModelRequest request, ModelStreamCallback callback);
    
    /**
     * 文本嵌入
     * 
     * @param texts 文本列表
     * @param model 模型名称
     * @return 嵌入向量
     */
    List<List<Double>> generateEmbeddings(List<String> texts, String model);
    
    /**
     * 图像生成
     * 
     * @param prompt 提示词
     * @param model 模型名称
     * @param options 生成选项
     * @return 图像URL列表
     */
    List<String> generateImages(String prompt, String model, Map<String, Object> options);
    
    /**
     * 语音转文本
     * 
     * @param audioData 音频数据
     * @param model 模型名称
     * @param options 转换选项
     * @return 转换结果
     */
    String speechToText(byte[] audioData, String model, Map<String, Object> options);
    
    /**
     * 文本转语音
     * 
     * @param text 文本内容
     * @param model 模型名称
     * @param options 转换选项
     * @return 音频数据
     */
    byte[] textToSpeech(String text, String model, Map<String, Object> options);
    
    /**
     * 检查模型是否可用
     * 
     * @param modelName 模型名称
     * @return 是否可用
     */
    boolean isModelAvailable(String modelName);
    
    /**
     * 获取模型信息
     * 
     * @param modelName 模型名称
     * @return 模型信息
     */
    ModelInfo getModelInfo(String modelName);
    
    /**
     * 获取服务健康状态
     */
    boolean isHealthy();
    
    /**
     * 获取服务统计信息
     */
    Map<String, Object> getServiceStats();
}
