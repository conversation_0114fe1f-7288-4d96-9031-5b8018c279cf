<template>
  <div class="el-user-by-role-selector">
    <el-card class="selector-card" shadow="never">
      <!-- 头部信息 -->
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon><UserFilled /></el-icon>
            <span>按角色选择用户</span>
            <el-tag v-if="hasSelection" type="info" size="small">
              已选择 {{ selectedCount }} 个用户
            </el-tag>
          </div>
          <div class="header-actions">
            <el-button 
              v-if="config.multiple" 
              @click="selectAllCurrentRoleUsers" 
              size="small" 
              type="primary" 
              plain
              :disabled="loading || !currentRoleUsers.length"
            >
              全选当前角色
            </el-button>
            <el-button 
              @click="clearSelection" 
              size="small" 
              :disabled="!hasSelection"
            >
              清空
            </el-button>
          </div>
        </div>
      </template>

      <div class="selector-content">
        <el-row :gutter="16">
          <!-- 左侧：角色选择 -->
          <el-col :span="8">
            <div class="role-panel">
              <div class="panel-title">
                <el-icon><Key /></el-icon>
                <span>选择角色</span>
                <el-tag v-if="selectedRole" type="success" size="small">
                  {{ selectedRole.label }}
                </el-tag>
              </div>
              
              <!-- 角色搜索 -->
              <el-input
                v-model="roleFilterText"
                placeholder="搜索角色..."
                :prefix-icon="Search"
                clearable
                size="small"
                :disabled="roleLoading"
                class="role-search"
              />

              <!-- 角色列表 -->
              <div class="role-list">
                <el-scrollbar height="400px">
                  <el-skeleton v-if="roleLoading" :rows="5" animated />
                  
                  <el-empty v-else-if="!filteredRoles.length" 
                    :description="roleFilterText ? '未找到匹配的角色' : '暂无角色数据'"
                    :image-size="80"
                  />
                  
                  <div v-else class="role-items">
                    <div
                      v-for="role in filteredRoles"
                      :key="role.value"
                      class="role-item"
                      :class="{
                        'is-selected': selectedRoleId === role.value,
                        'is-disabled': role.disabled
                      }"
                      @click="handleRoleClick(role)"
                    >
                      <el-avatar :size="32" class="role-avatar">
                        <el-icon><Key /></el-icon>
                      </el-avatar>
                      
                      <div class="role-info">
                        <div class="role-name">{{ role.label }}</div>
                        <div class="role-details">
                          <span>{{ role.roleCode }}</span>
                          <span v-if="role.userCount !== undefined" class="user-count">
                            {{ role.userCount }} 个用户
                          </span>
                        </div>
                      </div>
                      
                      <el-icon v-if="selectedRoleId === role.value" class="selected-icon" color="#409eff">
                        <Check />
                      </el-icon>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </el-col>

          <!-- 右侧：用户选择 -->
          <el-col :span="16">
            <div class="user-panel">
              <div class="panel-title">
                <el-icon><User /></el-icon>
                <span>选择用户</span>
                <el-tag v-if="currentRoleUsers.length" type="info" size="small">
                  {{ currentRoleUsers.length }} 个可选用户
                </el-tag>
              </div>

              <!-- 用户搜索 -->
              <el-input
                v-model="userFilterText"
                placeholder="搜索用户姓名、用户名..."
                :prefix-icon="Search"
                clearable
                size="small"
                :disabled="userLoading || !selectedRoleId"
                class="user-search"
              />

              <!-- 用户列表 -->
              <div class="user-list">
                <el-scrollbar height="400px">
                  <el-skeleton v-if="userLoading" :rows="8" animated />
                  
                  <el-empty v-else-if="!selectedRoleId" 
                    description="请先选择角色"
                    :image-size="80"
                  />
                  
                  <el-empty v-else-if="!filteredUsers.length" 
                    :description="userFilterText ? '未找到匹配的用户' : '该角色暂无用户'"
                    :image-size="80"
                  />
                  
                  <div v-else class="user-items">
                    <div
                      v-for="user in filteredUsers"
                      :key="user.value"
                      class="user-item"
                      :class="{
                        'is-selected': isUserSelected(user.value),
                        'is-disabled': user.disabled
                      }"
                      @click="handleUserClick(user)"
                    >
                      <!-- 选择指示器 -->
                      <div class="user-selector">
                        <el-checkbox
                          v-if="config.multiple"
                          :model-value="isUserSelected(user.value)"
                          :disabled="user.disabled"
                          @click.stop
                          @change="(checked) => handleUserSelect(user, checked)"
                        />
                        <el-radio
                          v-else
                          :model-value="selectedUserIds"
                          :label="user.value"
                          :disabled="user.disabled"
                          @click.stop
                          @change="handleUserSelect(user, true)"
                        />
                      </div>

                      <!-- 用户头像 -->
                      <el-avatar :size="40" class="user-avatar">
                        <img v-if="user.avatar" :src="user.avatar" :alt="user.label" />
                        <el-icon v-else><User /></el-icon>
                      </el-avatar>
                      
                      <!-- 用户信息 -->
                      <div class="user-info">
                        <div class="user-name">{{ user.label }}</div>
                        <div class="user-details">
                          <span v-if="user.username" class="username">{{ user.username }}</span>
                          <span v-if="user.deptName" class="dept-name">{{ user.deptName }}</span>
                          <span v-if="user.email" class="email">{{ user.email }}</span>
                          <span v-if="user.phone" class="phone">{{ user.phone }}</span>
                        </div>
                        <div v-if="user.roleNames?.length" class="user-roles">
                          <el-tag
                            v-for="roleName in user.roleNames.slice(0, 2)"
                            :key="roleName"
                            type="info"
                            size="small"
                            effect="plain"
                          >
                            {{ roleName }}
                          </el-tag>
                          <el-tag
                            v-if="user.roleNames.length > 2"
                            type="info"
                            size="small"
                            effect="plain"
                          >
                            +{{ user.roleNames.length - 2 }}
                          </el-tag>
                        </div>
                      </div>

                      <!-- 用户状态 -->
                      <div class="user-status">
                        <el-tag
                          :type="getUserStatusType(user.status)"
                          size="small"
                          effect="plain"
                        >
                          {{ getUserStatusLabel(user.status) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 已选择用户标签 -->
        <div v-if="hasSelection && config.multiple" class="selected-users">
          <el-divider content-position="left">
            <el-icon><Collection /></el-icon>
            <span>已选择的用户</span>
          </el-divider>
          
          <div class="user-tags">
            <el-tag
              v-for="user in selectedUserOptions"
              :key="user.value"
              :closable="!config.disabled"
              :size="config.size"
              @close="handleUserRemove(user.value)"
            >
              <el-avatar :size="16" class="tag-avatar">
                <img v-if="user.avatar" :src="user.avatar" :alt="user.label" />
                <el-icon v-else><User /></el-icon>
              </el-avatar>
              {{ user.label }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  ElCard,
  ElRow,
  ElCol,
  ElInput,
  ElButton,
  ElTag,
  ElIcon,
  ElAvatar,
  ElScrollbar,
  ElSkeleton,
  ElEmpty,
  ElDivider,
  ElCheckbox,
  ElRadio
} from 'element-plus'
import {
  UserFilled,
  Key,
  User,
  Search,
  Check,
  Collection
} from '@element-plus/icons-vue'
import { RoleAPI, UserAPI } from '@/api/system'
import type { 
  UserSelectorOption, 
  SelectorConfig,
  SelectorEmits 
} from './types'
import type { SysRoleVO, SysUserVO } from '@/types/system'

interface RoleOption {
  value: string
  label: string
  roleCode: string
  disabled?: boolean
  userCount?: number
}

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludeUserIds?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludeUserIds: () => []
})

const emit = defineEmits<SelectorEmits>()

// 响应式数据
const roleLoading = ref(false)
const userLoading = ref(false)
const roles = ref<RoleOption[]>([])
const users = ref<UserSelectorOption[]>([])
const selectedRoleId = ref<string>('')
const selectedRole = ref<RoleOption | null>(null)
const selectedUserIds = ref<string | string[]>()
const selectedUserOptions = ref<UserSelectorOption[]>([])
const roleFilterText = ref('')
const userFilterText = ref('')

// 默认配置
const defaultConfig: SelectorConfig = {
  multiple: true,
  clearable: true,
  placeholder: '请选择用户',
  size: 'default',
  disabled: false
}

// 合并配置
const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 计算属性
const loading = computed(() => roleLoading.value || userLoading.value)

const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedUserIds.value) && selectedUserIds.value.length > 0
  }
  return selectedUserIds.value !== undefined && selectedUserIds.value !== null && selectedUserIds.value !== ''
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

const filteredRoles = computed(() => {
  if (!roleFilterText.value) return roles.value
  const filter = roleFilterText.value.toLowerCase()
  return roles.value.filter(role => 
    role.label.toLowerCase().includes(filter) ||
    role.roleCode.toLowerCase().includes(filter)
  )
})

const currentRoleUsers = computed(() => {
  if (!selectedRoleId.value) return []
  return users.value.filter(user => 
    user.roleIds?.includes(selectedRoleId.value)
  )
})

const filteredUsers = computed(() => {
  let result = currentRoleUsers.value

  if (userFilterText.value) {
    const filter = userFilterText.value.toLowerCase()
    result = result.filter(user =>
      user.label.toLowerCase().includes(filter) ||
      (user.username && user.username.toLowerCase().includes(filter)) ||
      (user.realName && user.realName.toLowerCase().includes(filter)) ||
      (user.email && user.email.toLowerCase().includes(filter)) ||
      (user.phone && user.phone.toLowerCase().includes(filter))
    )
  }

  return result
})

// 方法
const loadRoles = async () => {
  try {
    roleLoading.value = true
    const response = await RoleAPI.getRoleList({
      status: props.onlyEnabled ? '0' : undefined
    })
    const data = response.data || []
    roles.value = data.map((role: SysRoleVO) => ({
      value: role.id,
      label: role.roleName,
      roleCode: role.roleCode,
      disabled: role.status !== '0',
      userCount: role.userCount
    }))
  } catch (error) {
    console.error('加载角色列表失败:', error)
    roles.value = []
  } finally {
    roleLoading.value = false
  }
}

const loadUsersByRole = async (roleId: string) => {
  try {
    userLoading.value = true
    const response = await UserAPI.getUserListByRole({
      roleId,
      status: props.onlyEnabled ? '0' : undefined
    })
    const data = response.data || []
    const roleUsers = transformUserData(data)

    // 更新用户列表，保留其他角色的用户
    users.value = users.value.filter(user => !user.roleIds?.includes(roleId)).concat(roleUsers)
  } catch (error) {
    console.error('加载用户列表失败:', error)
  } finally {
    userLoading.value = false
  }
}

const transformUserData = (data: SysUserVO[]): UserSelectorOption[] => {
  return data
    .filter(user => !props.excludeUserIds.includes(user.id))
    .map(user => ({
      value: user.id,
      label: user.nickname || user.realName || user.username,
      username: user.username,
      nickname: user.nickname,
      realName: user.realName,
      email: user.email,
      phone: user.phone,
      avatar: user.avatar,
      deptId: user.deptId,
      deptName: user.deptName,
      roleIds: user.roleIds,
      roleNames: user.roleNames,
      status: user.status,
      disabled: user.status !== '0'
    }))
}

const getUserStatusType = (status?: string | number) => {
  switch (status) {
    case '0': return 'success'
    case '1': return 'danger'
    case '2': return 'warning'
    default: return 'info'
  }
}

const getUserStatusLabel = (status?: string | number) => {
  switch (status) {
    case '0': return '正常'
    case '1': return '禁用'
    case '2': return '锁定'
    default: return '未知'
  }
}

const isUserSelected = (userId: string): boolean => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.includes(userId)
  }
  return selectedUserIds.value === userId
}

const findUsersByIds = (ids: string | string[]): UserSelectorOption[] => {
  const targetIds = Array.isArray(ids) ? ids : (ids ? [ids] : [])
  return users.value.filter(user => targetIds.includes(user.value))
}

// 事件处理
const handleRoleClick = async (role: RoleOption) => {
  if (role.disabled) return

  selectedRoleId.value = role.value
  selectedRole.value = role

  // 清空用户搜索
  userFilterText.value = ''

  // 加载该角色的用户
  await loadUsersByRole(role.value)

  emit('role-change', role.value, role)
}

const handleUserClick = (user: UserSelectorOption) => {
  if (user.disabled) return

  if (config.value.multiple) {
    const currentIds = Array.isArray(selectedUserIds.value) ? [...selectedUserIds.value] : []
    const index = currentIds.indexOf(user.value)

    if (index > -1) {
      currentIds.splice(index, 1)
    } else {
      currentIds.push(user.value)
    }

    handleUserChange(currentIds)
  } else {
    handleUserChange(user.value)
  }
}

const handleUserSelect = (user: UserSelectorOption, checked: boolean) => {
  if (user.disabled) return

  if (config.value.multiple) {
    const currentIds = Array.isArray(selectedUserIds.value) ? [...selectedUserIds.value] : []

    if (checked && !currentIds.includes(user.value)) {
      currentIds.push(user.value)
    } else if (!checked) {
      const index = currentIds.indexOf(user.value)
      if (index > -1) {
        currentIds.splice(index, 1)
      }
    }

    handleUserChange(currentIds)
  } else if (checked) {
    handleUserChange(user.value)
  }
}

const handleUserChange = (value: string | string[]) => {
  selectedUserIds.value = value
  selectedUserOptions.value = findUsersByIds(value)

  emit('update:modelValue', value)
  emit('change', value, selectedUserOptions.value.length === 1 ? selectedUserOptions.value[0] : selectedUserOptions.value)

  if (!config.value.multiple && selectedUserOptions.value.length === 1) {
    emit('select', value as string, selectedUserOptions.value[0])
  }
}

const handleUserRemove = (userId: string) => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    const newIds = selectedUserIds.value.filter(id => id !== userId)
    handleUserChange(newIds)
    emit('remove', userId)
  }
}

const selectAllCurrentRoleUsers = () => {
  if (!config.value.multiple || !currentRoleUsers.value.length) return

  const allUserIds = currentRoleUsers.value
    .filter(user => !user.disabled)
    .map(user => user.value)

  handleUserChange(allUserIds)
}

const clearSelection = () => {
  const clearValue = config.value.multiple ? [] : ''
  selectedUserIds.value = clearValue
  selectedUserOptions.value = []

  emit('update:modelValue', clearValue)
  emit('change', clearValue, null)
  emit('clear')
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedUserIds.value = newValue || (config.value.multiple ? [] : '')
  selectedUserOptions.value = findUsersByIds(selectedUserIds.value)
}, { immediate: true })

// 组件挂载
onMounted(async () => {
  await loadRoles()
})

// 暴露方法
defineExpose({
  refresh: loadRoles,
  clearSelection,
  selectAllCurrentRoleUsers
})
</script>

<style scoped>
.el-user-by-role-selector {
  width: 100%;
}

.selector-card {
  border: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.role-search,
.user-search {
  margin-bottom: 12px;
}

.role-list,
.user-list {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.role-item,
.user-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  gap: 12px;
}

.role-item:hover,
.user-item:hover {
  background: var(--el-fill-color-light);
}

.role-item.is-selected,
.user-item.is-selected {
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
}

.role-item.is-disabled,
.user-item.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.role-info,
.user-info {
  flex: 1;
  min-width: 0;
}

.role-name,
.user-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.role-details,
.user-details {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.user-roles {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.user-status {
  flex-shrink: 0;
}

.selected-users {
  margin-top: 16px;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-avatar {
  margin-right: 4px;
}
</style>
