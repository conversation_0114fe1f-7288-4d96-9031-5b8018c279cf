package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Map;

/**
 * 搜索请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "搜索请求")
public class SearchRequest {

    @Schema(description = "知识库ID", required = true)
    @NotBlank(message = "知识库ID不能为空")
    private String datasetId;

    @Schema(description = "查询内容", required = true)
    @NotBlank(message = "查询内容不能为空")
    private String query;

    @Schema(description = "返回结果数量", example = "5")
    @Min(value = 1, message = "返回结果数量不能小于1")
    @Max(value = 100, message = "返回结果数量不能大于100")
    private Integer topK = 5;

    @Schema(description = "相似度阈值", example = "0.7")
    @Min(value = 0, message = "相似度阈值不能小于0")
    @Max(value = 1, message = "相似度阈值不能大于1")
    private Double scoreThreshold = 0.7;

    @Schema(description = "向量权重（混合搜索）", example = "0.7")
    @Min(value = 0, message = "向量权重不能小于0")
    @Max(value = 1, message = "向量权重不能大于1")
    private Double vectorWeight = 0.7;

    @Schema(description = "关键词权重（混合搜索）", example = "0.3")
    @Min(value = 0, message = "关键词权重不能小于0")
    @Max(value = 1, message = "关键词权重不能大于1")
    private Double keywordWeight = 0.3;

    @Schema(description = "搜索类型", example = "vector", allowableValues = {"vector", "keyword", "hybrid"})
    private String searchType = "vector";

    @Schema(description = "是否启用重排序")
    private Boolean enableRerank = false;

    @Schema(description = "重排序模型")
    private String rerankModel;

    @Schema(description = "是否启用查询扩展")
    private Boolean enableQueryExpansion = false;

    @Schema(description = "过滤条件")
    private Map<String, Object> filters;

    @Schema(description = "是否高亮结果")
    private Boolean enableHighlight = true;

    @Schema(description = "搜索模式", example = "normal", allowableValues = {"normal", "precise", "recall"})
    private String searchMode = "normal";

    @Schema(description = "文档类型过滤")
    private String docType;

    @Schema(description = "语言过滤")
    private String language;

    @Schema(description = "时间范围过滤 - 开始时间")
    private String startTime;

    @Schema(description = "时间范围过滤 - 结束时间")
    private String endTime;

    // 构造函数
    public SearchRequest() {}

    public SearchRequest(String datasetId, String query) {
        this.datasetId = datasetId;
        this.query = query;
    }

    public SearchRequest(String datasetId, String query, Integer topK) {
        this.datasetId = datasetId;
        this.query = query;
        this.topK = topK;
    }

    // Getters and Setters
    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public Double getScoreThreshold() {
        return scoreThreshold;
    }

    public void setScoreThreshold(Double scoreThreshold) {
        this.scoreThreshold = scoreThreshold;
    }

    public Double getVectorWeight() {
        return vectorWeight;
    }

    public void setVectorWeight(Double vectorWeight) {
        this.vectorWeight = vectorWeight;
    }

    public Double getKeywordWeight() {
        return keywordWeight;
    }

    public void setKeywordWeight(Double keywordWeight) {
        this.keywordWeight = keywordWeight;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public Boolean getEnableRerank() {
        return enableRerank;
    }

    public void setEnableRerank(Boolean enableRerank) {
        this.enableRerank = enableRerank;
    }

    public String getRerankModel() {
        return rerankModel;
    }

    public void setRerankModel(String rerankModel) {
        this.rerankModel = rerankModel;
    }

    public Boolean getEnableQueryExpansion() {
        return enableQueryExpansion;
    }

    public void setEnableQueryExpansion(Boolean enableQueryExpansion) {
        this.enableQueryExpansion = enableQueryExpansion;
    }

    public Map<String, Object> getFilters() {
        return filters;
    }

    public void setFilters(Map<String, Object> filters) {
        this.filters = filters;
    }

    public Boolean getEnableHighlight() {
        return enableHighlight;
    }

    public void setEnableHighlight(Boolean enableHighlight) {
        this.enableHighlight = enableHighlight;
    }

    public String getSearchMode() {
        return searchMode;
    }

    public void setSearchMode(String searchMode) {
        this.searchMode = searchMode;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "SearchRequest{" +
                "datasetId='" + datasetId + '\'' +
                ", query='" + query + '\'' +
                ", topK=" + topK +
                ", scoreThreshold=" + scoreThreshold +
                ", searchType='" + searchType + '\'' +
                ", searchMode='" + searchMode + '\'' +
                '}';
    }
}
