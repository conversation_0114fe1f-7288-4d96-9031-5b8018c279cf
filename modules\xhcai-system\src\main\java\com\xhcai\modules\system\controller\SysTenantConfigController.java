package com.xhcai.modules.system.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.modules.system.entity.SysTenantConfig;
import com.xhcai.modules.system.service.ISysTenantConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 租户配置管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "租户配置管理", description = "租户配置管理相关接口")
@RestController
@RequestMapping("/api/system/tenant-config")
@RequiresTenantAdmin(message = "租户配置管理需要租户管理员权限")
public class SysTenantConfigController {

    @Autowired
    private ISysTenantConfigService tenantConfigService;

    /**
     * 获取当前租户所有配置
     */
    @Operation(summary = "获取当前租户配置", description = "获取当前租户的所有配置信息")
    @GetMapping
    public Result<Map<String, String>> getCurrentTenantConfigs() {
        Map<String, String> configs = tenantConfigService.getCurrentTenantConfigs();
        return Result.success(configs);
    }

    /**
     * 根据配置键获取配置值
     */
    @Operation(summary = "获取配置值", description = "根据配置键获取配置值")
    @GetMapping("/value")
    public Result<String> getConfigValue(
            @Parameter(description = "配置键", required = true) @RequestParam String configKey) {
        String configValue = tenantConfigService.getConfigValue(configKey);
        return Result.success(configValue);
    }

    /**
     * 设置配置值
     */
    @Operation(summary = "设置配置值", description = "设置指定配置键的值")
    @PostMapping("/value")
    public Result<Void> setConfigValue(
            @Parameter(description = "配置键", required = true) @RequestParam String configKey,
            @Parameter(description = "配置值", required = true) @RequestParam String configValue) {
        boolean result = tenantConfigService.setConfigValue(configKey, configValue);
        return result ? Result.success() : Result.fail("设置配置失败");
    }

    /**
     * 批量设置配置
     */
    @Operation(summary = "批量设置配置", description = "批量设置多个配置项")
    @PostMapping("/batch")
    public Result<Void> batchSetConfigs(@RequestBody Map<String, String> configs) {
        boolean result = tenantConfigService.batchSetConfigs(configs);
        return result ? Result.success() : Result.fail("批量设置配置失败");
    }

    /**
     * 根据配置类型获取配置列表
     */
    @Operation(summary = "根据类型获取配置", description = "根据配置类型获取配置列表")
    @GetMapping("/type/{configType}")
    public Result<List<SysTenantConfig>> getConfigsByType(
            @Parameter(description = "配置类型", required = true) @PathVariable String configType) {
        List<SysTenantConfig> configs = tenantConfigService.getConfigsByType(configType, null);
        return Result.success(configs);
    }

    /**
     * 获取系统配置列表
     */
    @Operation(summary = "获取系统配置", description = "获取系统级配置列表")
    @GetMapping("/system")
    public Result<List<SysTenantConfig>> getSystemConfigs() {
        List<SysTenantConfig> configs = tenantConfigService.getSystemConfigs(null);
        return Result.success(configs);
    }

    /**
     * 获取用户配置列表
     */
    @Operation(summary = "获取用户配置", description = "获取用户级配置列表")
    @GetMapping("/user")
    public Result<List<SysTenantConfig>> getUserConfigs() {
        List<SysTenantConfig> configs = tenantConfigService.getUserConfigs(null);
        return Result.success(configs);
    }

    /**
     * 创建配置
     */
    @Operation(summary = "创建配置", description = "创建新的配置项")
    @PostMapping
    public Result<Void> createConfig(@Valid @RequestBody SysTenantConfig config) {
        boolean result = tenantConfigService.createConfig(config);
        return result ? Result.success() : Result.fail("创建配置失败");
    }

    /**
     * 更新配置
     */
    @Operation(summary = "更新配置", description = "更新配置项信息")
    @PutMapping
    public Result<Void> updateConfig(@Valid @RequestBody SysTenantConfig config) {
        boolean result = tenantConfigService.updateConfig(config);
        return result ? Result.success() : Result.fail("更新配置失败");
    }

    /**
     * 删除配置
     */
    @Operation(summary = "删除配置", description = "批量删除配置项")
    @DeleteMapping
    public Result<Void> deleteConfigs(@RequestBody List<String> configIds) {
        boolean result = tenantConfigService.deleteConfigs(configIds);
        return result ? Result.success() : Result.fail("删除配置失败");
    }

    /**
     * 检查配置键是否存在
     */
    @Operation(summary = "检查配置键", description = "检查配置键是否已存在")
    @GetMapping("/check-key")
    public Result<Boolean> checkConfigKey(
            @Parameter(description = "配置键", required = true) @RequestParam String configKey,
            @Parameter(description = "排除的配置ID") @RequestParam(required = false) String excludeId) {
        boolean exists = tenantConfigService.existsConfigKey(configKey, null, excludeId);
        return Result.success(exists);
    }

    /**
     * 重置租户配置
     */
    @Operation(summary = "重置租户配置", description = "重置当前租户配置为默认值")
    @PostMapping("/reset")
    public Result<Void> resetTenantConfigs() {
        boolean result = tenantConfigService.resetTenantConfigs(null);
        return result ? Result.success() : Result.fail("重置配置失败");
    }

    /**
     * 获取默认配置值
     */
    @Operation(summary = "获取默认配置值", description = "获取指定配置键的默认值")
    @GetMapping("/default-value")
    public Result<String> getDefaultConfigValue(
            @Parameter(description = "配置键", required = true) @RequestParam String configKey) {
        String defaultValue = tenantConfigService.getDefaultConfigValue(configKey);
        return Result.success(defaultValue);
    }

    /**
     * 刷新配置缓存
     */
    @Operation(summary = "刷新配置缓存", description = "刷新当前租户的配置缓存")
    @PostMapping("/refresh-cache")
    public Result<Void> refreshConfigCache() {
        tenantConfigService.refreshConfigCache(null);
        return Result.success();
    }

    /**
     * 清除配置缓存
     */
    @Operation(summary = "清除配置缓存", description = "清除当前租户的配置缓存")
    @PostMapping("/clear-cache")
    public Result<Void> clearConfigCache() {
        tenantConfigService.clearConfigCache(null);
        return Result.success();
    }
}
