package com.xhcai.modules.dify.controller;

import java.util.List;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.dto.thirdPlatform.PasswordVerifyDTO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountCreateDTO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountQueryDTO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountUpdateDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.dify.service.IThirdPlatformAccountService;
import com.xhcai.modules.dify.vo.ThirdPlatformAccountVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 用户第三方智能体账号控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "用户第三方智能体账号管理", description = "用户第三方智能体账号管理相关接口")
@RestController
@RequestMapping("/api/third-platform/account")
public class ThirdPlatformAccountController {

    @Autowired
    private IThirdPlatformAccountService thirdPlatformAccountService;

    @Operation(summary = "分页查询用户第三方智能体账号", description = "分页查询当前用户的第三方智能体账号列表")
    @GetMapping("/page")
    @RequiresPermissions("agent:third-platform-account:query")
    public Result<IPage<ThirdPlatformAccountVO>> page(@Valid ThirdPlatformAccountQueryDTO query) {
        IPage<ThirdPlatformAccountVO> page = thirdPlatformAccountService.selectPageVO(query);
        return Result.ok(page);
    }

    @Operation(summary = "查询用户第三方智能体账号详情", description = "根据ID查询用户第三方智能体账号详情")
    @GetMapping("/{id}")
    @RequiresPermissions("agent:third-platform-account:query")
    public Result<ThirdPlatformAccountVO> getById(
            @Parameter(description = "账号ID", required = true) @PathVariable String id) {
        ThirdPlatformAccountVO vo = thirdPlatformAccountService.selectVOById(id);
        if (vo == null) {
            return Result.error("账号不存在或无权限访问");
        }
        return Result.ok(vo);
    }

    @Operation(summary = "创建用户第三方智能体账号", description = "创建新的第三方智能体账号")
    @PostMapping
    @RequiresPermissions("agent:third-platform-account:add")
    public Result<String> create(@Valid @RequestBody ThirdPlatformAccountCreateDTO createDTO) {
        return thirdPlatformAccountService.createAccount(createDTO);
    }

    @Operation(summary = "更新用户第三方智能体账号", description = "更新指定的第三方智能体账号信息")
    @PutMapping("/{id}")
    @RequiresPermissions("agent:third-platform-account:edit")
    public Result<Void> update(
            @Parameter(description = "账号ID", required = true) @PathVariable String id,
            @Valid @RequestBody ThirdPlatformAccountUpdateDTO updateDTO) {
        return thirdPlatformAccountService.updateAccount(id, updateDTO);
    }

    @Operation(summary = "删除用户第三方智能体账号", description = "删除指定的第三方智能体账号")
    @DeleteMapping("/{id}")
    @RequiresPermissions("agent:third-platform-account:delete")
    public Result<Void> delete(
            @Parameter(description = "账号ID", required = true) @PathVariable String id) {
        return thirdPlatformAccountService.deleteAccount(id);
    }

    @Operation(summary = "测试连接", description = "测试第三方智能体账号的连接状态")
    @PostMapping("/{id}/test")
    @RequiresPermissions("agent:third-platform-account:test")
    public Result<String> testConnection(
            @Parameter(description = "账号ID", required = true) @PathVariable String id) {
        return thirdPlatformAccountService.testConnection(id);
    }

    @Operation(summary = "查询当前用户的所有第三方账号", description = "查询当前用户的所有启用状态的第三方智能体账号")
    @GetMapping("/list")
    @RequiresPermissions("agent:third-platform-account:query")
    public Result<List<ThirdPlatformAccountVO>> list() {
        List<ThirdPlatformAccountVO> list = thirdPlatformAccountService.selectByCurrentUser();
        return Result.ok(list);
    }

    @Operation(summary = "检查平台账号", description = "检查当前用户在指定平台是否已有账号")
    @GetMapping("/check/{platformId}")
    @RequiresPermissions("agent:third-platform-account:query")
    public Result<Boolean> checkPlatform(
            @Parameter(description = "平台ID", required = true) @PathVariable String platformId) {
        boolean hasAccount = thirdPlatformAccountService.hasAccountInPlatform(platformId);
        return Result.ok(hasAccount);
    }

    @Operation(summary = "验证密码", description = "验证第三方智能体账号密码")
    @PostMapping("/{id}/verify-password")
    @RequiresPermissions("agent:third-platform-account:query")
    public Result<Boolean> verifyPassword(
            @Parameter(description = "账号ID", required = true) @PathVariable String id,
            @Valid @RequestBody PasswordVerifyDTO verifyDTO) {
        boolean isValid = thirdPlatformAccountService.verifyPassword(id, verifyDTO.getPassword());
        return Result.ok(isValid);
    }
}
