package com.xhcai.modules.dify.service;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.dto.knowledge.DifyDocumentDTO;
import com.xhcai.modules.dify.dto.knowledge.DifyKnowledgeBaseDTO;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 知识库服务接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IDifyKnowledgeService {

    /**
     * 创建知识库
     *
     * @param difyKnowledgeBaseDTO 知识库信息
     * @return 创建结果
     */
    Mono<Result<DifyKnowledgeBaseDTO>> createKnowledgeBase(DifyKnowledgeBaseDTO difyKnowledgeBaseDTO);

    /**
     * 更新知识库
     *
     * @param knowledgeId 知识库ID
     * @param difyKnowledgeBaseDTO 知识库信息
     * @return 更新结果
     */
    Mono<Result<DifyKnowledgeBaseDTO>> updateKnowledgeBase(String knowledgeId, DifyKnowledgeBaseDTO difyKnowledgeBaseDTO);

    /**
     * 删除知识库
     *
     * @param knowledgeId 知识库ID
     * @return 删除结果
     */
    Mono<Result<Object>> deleteKnowledgeBase(String knowledgeId);

    /**
     * 获取知识库详情
     *
     * @param knowledgeId 知识库ID
     * @return 知识库详情
     */
    Mono<Result<DifyKnowledgeBaseDTO>> getKnowledgeBase(String knowledgeId);

    /**
     * 获取知识库列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param keyword 关键词
     * @param status 状态
     * @return 知识库列表
     */
    Mono<Result<PageResult<DifyKnowledgeBaseDTO>>> getKnowledgeBaseList(int page, int size, String keyword, String status);

    /**
     * 更新文档
     *
     * @param knowledgeId 知识库ID
     * @param documentId 文档ID
     * @param difyDocumentDTO 文档信息
     * @return 更新结果
     */
    Mono<Result<DifyDocumentDTO>> updateDocument(String knowledgeId, String documentId, DifyDocumentDTO difyDocumentDTO);

    /**
     * 删除文档
     *
     * @param knowledgeId 知识库ID
     * @param documentId 文档ID
     * @return 删除结果
     */
    Mono<Result<Object>> deleteDocument(String knowledgeId, String documentId);

    /**
     * 获取文档详情
     *
     * @param knowledgeId 知识库ID
     * @param documentId 文档ID
     * @return 文档详情
     */
    Mono<Result<DifyDocumentDTO>> getDocument(String knowledgeId, String documentId);

    /**
     * 获取文档列表
     *
     * @param knowledgeId 知识库ID
     * @param page 页码
     * @param size 每页大小
     * @param keyword 关键词
     * @param status 状态
     * @return 文档列表
     */
    Mono<Result<PageResult<DifyDocumentDTO>>> getDocumentList(String knowledgeId, int page, int size, String keyword, String status);

    /**
     * 重新处理文档
     *
     * @param knowledgeId 知识库ID
     * @param documentId 文档ID
     * @return 处理结果
     */
    Mono<Result<Object>> reprocessDocument(String knowledgeId, String documentId);

    /**
     * 获取文档处理状态
     *
     * @param knowledgeId 知识库ID
     * @param documentId 文档ID
     * @return 处理状态
     */
    Mono<Result<Object>> getDocumentProcessStatus(String knowledgeId, String documentId);

    /**
     * 在知识库中检索
     *
     * @param knowledgeId 知识库ID
     * @param query 查询内容
     * @param topK 返回数量
     * @param scoreThreshold 分数阈值
     * @return 检索结果
     */
    Mono<Result<List<Object>>> searchKnowledge(String knowledgeId, String query, Integer topK, Double scoreThreshold);

    /**
     * 批量删除文档
     *
     * @param knowledgeId 知识库ID
     * @param documentIds 文档ID列表
     * @return 删除结果
     */
    Mono<Result<Object>> batchDeleteDocuments(String knowledgeId, List<String> documentIds);

    /**
     * 获取知识库统计信息
     *
     * @param knowledgeId 知识库ID
     * @return 统计信息
     */
    Mono<Result<Object>> getKnowledgeStats(String knowledgeId);
}
