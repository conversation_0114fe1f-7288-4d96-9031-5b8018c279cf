<template>
  <div class="node-type-distribution">
    <!-- 类型列表 -->
    <div class="space-y-3">
      <div
        v-for="item in distribution"
        :key="item.type"
        class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <div class="flex items-center gap-3">
          <div
            class="w-4 h-4 rounded-full"
            :style="{ backgroundColor: item.color }"
          ></div>
          <div>
            <div class="font-medium text-gray-900">{{ item.type }}</div>
            <div class="text-xs text-gray-500">{{ getPercentage(item.count) }}%</div>
          </div>
        </div>
        <div class="text-right">
          <div class="font-semibold text-gray-900">{{ item.count }}</div>
          <div class="text-xs text-gray-500">节点</div>
        </div>
      </div>
    </div>

    <!-- 可视化图表 -->
    <div class="mt-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">分布比例</span>
        <span class="text-xs text-gray-500">总计: {{ totalCount }} 个节点</span>
      </div>
      
      <!-- 水平条形图 -->
      <div class="space-y-2">
        <div
          v-for="item in distribution"
          :key="item.type"
          class="flex items-center gap-2"
        >
          <div class="w-12 text-xs text-gray-600 text-right">{{ item.type }}</div>
          <div class="flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden">
            <div
              class="h-full rounded-full transition-all duration-500 ease-out"
              :style="{
                backgroundColor: item.color,
                width: `${getPercentage(item.count)}%`
              }"
            ></div>
          </div>
          <div class="w-8 text-xs text-gray-600">{{ item.count }}</div>
        </div>
      </div>
    </div>

    <!-- 饼图（简化版） -->
    <div class="mt-4">
      <div class="text-sm font-medium text-gray-700 mb-2">类型占比</div>
      <div class="flex items-center justify-center">
        <div class="relative w-24 h-24">
          <!-- 简化的饼图显示 -->
          <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="#f3f4f6"
              stroke-width="8"
            />
            <circle
              v-for="(item, index) in pieChartData"
              :key="item.type"
              cx="50"
              cy="50"
              r="40"
              fill="none"
              :stroke="item.color"
              stroke-width="8"
              :stroke-dasharray="`${item.circumference} ${251.2 - item.circumference}`"
              :stroke-dashoffset="item.offset"
              class="transition-all duration-500"
            />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center">
              <div class="text-lg font-bold text-gray-900">{{ totalCount }}</div>
              <div class="text-xs text-gray-500">节点</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细统计 -->
    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
      <div class="text-sm font-medium text-blue-900 mb-2">统计摘要</div>
      <div class="text-xs text-blue-700 space-y-1">
        <div>最多类型: {{ mostCommonType.type }} ({{ mostCommonType.count }} 个)</div>
        <div>类型多样性: {{ diversity.toFixed(2) }}</div>
        <div>平均每类: {{ averagePerType.toFixed(1) }} 个节点</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface DistributionItem {
  type: string
  count: number
  color: string
}

interface Props {
  distribution: DistributionItem[]
}

const props = defineProps<Props>()

// 计算属性
const totalCount = computed(() => {
  return props.distribution.reduce((sum, item) => sum + item.count, 0)
})

const mostCommonType = computed(() => {
  return props.distribution.reduce((max, item) => 
    item.count > max.count ? item : max, 
    props.distribution[0] || { type: '', count: 0 }
  )
})

const diversity = computed(() => {
  // 计算多样性指数 (Shannon diversity index)
  const total = totalCount.value
  if (total === 0) return 0
  
  return -props.distribution.reduce((sum, item) => {
    const p = item.count / total
    return sum + (p > 0 ? p * Math.log2(p) : 0)
  }, 0)
})

const averagePerType = computed(() => {
  return props.distribution.length > 0 ? totalCount.value / props.distribution.length : 0
})

const pieChartData = computed(() => {
  const total = totalCount.value
  if (total === 0) return []
  
  let currentOffset = 0
  const circumference = 2 * Math.PI * 40 // r = 40
  
  return props.distribution.map(item => {
    const percentage = item.count / total
    const itemCircumference = circumference * percentage
    const data = {
      type: item.type,
      color: item.color,
      circumference: itemCircumference,
      offset: -currentOffset
    }
    currentOffset += itemCircumference
    return data
  })
})

// 工具函数
const getPercentage = (count: number): number => {
  return totalCount.value > 0 ? Math.round((count / totalCount.value) * 100) : 0
}
</script>
