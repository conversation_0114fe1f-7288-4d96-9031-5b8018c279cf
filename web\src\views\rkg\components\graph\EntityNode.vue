<template>
  <div
    class="entity-node bg-white border-2 rounded-lg p-3 min-w-32 shadow-sm hover:shadow-md transition-all duration-200"
    :class="[
      selected ? 'border-blue-500 bg-blue-50' : 'border-blue-200',
      'hover:border-blue-400'
    ]"
  >
    <!-- 节点图标和标题 -->
    <div class="flex items-center gap-2 mb-2">
      <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
        <span class="text-white text-xs">{{ getEntityIcon(data.type) }}</span>
      </div>
      <div class="flex-1 min-w-0">
        <h4 class="font-medium text-gray-900 text-sm truncate">{{ data.label }}</h4>
        <p class="text-xs text-gray-500">{{ getEntityTypeName(data.type) }}</p>
      </div>
    </div>

    <!-- 属性预览 -->
    <div v-if="data.properties && Object.keys(data.properties).length > 0" class="text-xs text-gray-600">
      <div
        v-for="(value, key) in getPreviewProperties(data.properties)"
        :key="key"
        class="flex justify-between items-center"
      >
        <span class="text-gray-500">{{ key }}:</span>
        <span class="font-medium truncate ml-1">{{ value }}</span>
      </div>
    </div>

    <!-- 连接点 -->
    <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-top"></div>
    <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-bottom"></div>
    <div class="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-left"></div>
    <div class="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-right"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: {
    label: string
    type: string
    properties?: Record<string, any>
  }
  selected?: boolean
}

const props = defineProps<Props>()

// 工具函数
const getEntityIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    product: '📦',
    feature: '⚡',
    user: '👤',
    company: '🏢',
    document: '📄',
    category: '📁',
    tag: '🏷️',
    default: '🔵'
  }
  return iconMap[type] || iconMap.default
}

const getEntityTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    product: '产品',
    feature: '功能',
    user: '用户',
    company: '公司',
    document: '文档',
    category: '分类',
    tag: '标签'
  }
  return typeMap[type] || type
}

const getPreviewProperties = (properties: Record<string, any>): Record<string, any> => {
  // 只显示前2个属性
  const entries = Object.entries(properties).slice(0, 2)
  return Object.fromEntries(entries)
}
</script>

<style scoped>
.entity-node {
  position: relative;
  transform: translateY(0);
}

.entity-node:hover {
  transform: translateY(-1px);
}

.vue-flow__handle {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #3b82f6;
  border: 2px solid white;
}

.vue-flow__handle-top {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.vue-flow__handle-bottom {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.vue-flow__handle-left {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.vue-flow__handle-right {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}
</style>
