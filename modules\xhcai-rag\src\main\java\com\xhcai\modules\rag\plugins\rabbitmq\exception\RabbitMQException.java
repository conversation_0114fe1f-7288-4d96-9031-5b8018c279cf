package com.xhcai.modules.rag.plugins.rabbitmq.exception;

/**
 * RabbitMQ自定义异常类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class RabbitMQException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误详情
     */
    private Object errorDetails;

    public RabbitMQException() {
        super();
    }

    public RabbitMQException(String message) {
        super(message);
    }

    public RabbitMQException(String message, Throwable cause) {
        super(message, cause);
    }

    public RabbitMQException(Throwable cause) {
        super(cause);
    }

    public RabbitMQException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public RabbitMQException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public RabbitMQException(String errorCode, String message, Object errorDetails) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }

    public RabbitMQException(String errorCode, String message, Object errorDetails, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Object getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(Object errorDetails) {
        this.errorDetails = errorDetails;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        
        if (errorCode != null) {
            sb.append(" [").append(errorCode).append("]");
        }
        
        if (getMessage() != null) {
            sb.append(": ").append(getMessage());
        }
        
        if (errorDetails != null) {
            sb.append(" (详情: ").append(errorDetails).append(")");
        }
        
        return sb.toString();
    }

    /**
     * 连接异常
     */
    public static class ConnectionException extends RabbitMQException {
        public ConnectionException(String message) {
            super("CONNECTION_ERROR", message);
        }
        
        public ConnectionException(String message, Throwable cause) {
            super("CONNECTION_ERROR", message, cause);
        }
    }

    /**
     * 消息发送异常
     */
    public static class MessageSendException extends RabbitMQException {
        public MessageSendException(String message) {
            super("MESSAGE_SEND_ERROR", message);
        }
        
        public MessageSendException(String message, Throwable cause) {
            super("MESSAGE_SEND_ERROR", message, cause);
        }
    }

    /**
     * 消息消费异常
     */
    public static class MessageConsumeException extends RabbitMQException {
        public MessageConsumeException(String message) {
            super("MESSAGE_CONSUME_ERROR", message);
        }
        
        public MessageConsumeException(String message, Throwable cause) {
            super("MESSAGE_CONSUME_ERROR", message, cause);
        }
    }

    /**
     * 配置异常
     */
    public static class ConfigurationException extends RabbitMQException {
        public ConfigurationException(String message) {
            super("CONFIGURATION_ERROR", message);
        }
        
        public ConfigurationException(String message, Throwable cause) {
            super("CONFIGURATION_ERROR", message, cause);
        }
    }

    /**
     * 序列化异常
     */
    public static class SerializationException extends RabbitMQException {
        public SerializationException(String message) {
            super("SERIALIZATION_ERROR", message);
        }
        
        public SerializationException(String message, Throwable cause) {
            super("SERIALIZATION_ERROR", message, cause);
        }
    }
}
