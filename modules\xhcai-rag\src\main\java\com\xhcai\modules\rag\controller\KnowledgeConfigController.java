package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig;
import com.xhcai.modules.rag.service.KnowledgeConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 知识库配置控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/config")
@RequiredArgsConstructor
@Tag(name = "知识库配置管理", description = "知识库配置相关接口")
public class KnowledgeConfigController {

    private final KnowledgeConfigService knowledgeConfigService;

    @GetMapping("/segment")
    @Operation(summary = "获取文件分段配置")
    public Result<KnowledgeSegmentConfig> getSegmentConfig() {
        try {
            KnowledgeSegmentConfig config = knowledgeConfigService.getSegmentConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取文件分段配置失败", e);
            return Result.error("获取文件分段配置失败：" + e.getMessage());
        }
    }

    @PostMapping("/segment")
    @Operation(summary = "保存文件分段配置")
    public Result<KnowledgeSegmentConfig> saveSegmentConfig(@RequestBody KnowledgeSegmentConfig config) {
        try {
            KnowledgeSegmentConfig savedConfig = knowledgeConfigService.saveSegmentConfig(config);
            return Result.success(savedConfig);
        } catch (Exception e) {
            log.error("保存文件分段配置失败", e);
            return Result.error("保存文件分段配置失败：" + e.getMessage());
        }
    }

    @GetMapping("/vectorization")
    @Operation(summary = "获取向量化配置")
    public Result<KnowledgeVectorizationConfig> getVectorizationConfig() {
        try {
            KnowledgeVectorizationConfig config = knowledgeConfigService.getVectorizationConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取向量化配置失败", e);
            return Result.error("获取向量化配置失败：" + e.getMessage());
        }
    }

    @PostMapping("/vectorization")
    @Operation(summary = "保存向量化配置")
    public Result<KnowledgeVectorizationConfig> saveVectorizationConfig(@RequestBody KnowledgeVectorizationConfig config) {
        try {
            KnowledgeVectorizationConfig savedConfig = knowledgeConfigService.saveVectorizationConfig(config);
            return Result.success(savedConfig);
        } catch (Exception e) {
            log.error("保存向量化配置失败", e);
            return Result.error("保存向量化配置失败：" + e.getMessage());
        }
    }
}
