<template>
  <div class="modal-overlay" @click="onClose">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3>工作流设置</h3>
        <button class="close-btn" @click="onClose">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-content">
        <div class="settings-tabs">
          <button 
            v-for="tab in tabs" 
            :key="tab.key"
            class="tab-btn"
            :class="{ active: activeTab === tab.key }"
            @click="activeTab = tab.key"
          >
            <i class="fas" :class="tab.icon"></i>
            {{ tab.label }}
          </button>
        </div>

        <div class="tab-content">
          <!-- 基础设置 -->
          <div v-show="activeTab === 'basic'" class="tab-panel">
            <div class="form-group">
              <label>工作流名称</label>
              <input 
                v-model="localSettings.name" 
                type="text" 
                class="form-input"
                placeholder="输入工作流名称"
              />
            </div>

            <div class="form-group">
              <label>描述</label>
              <textarea 
                v-model="localSettings.description" 
                class="form-textarea"
                rows="3"
                placeholder="输入工作流描述"
              ></textarea>
            </div>

            <div class="form-group">
              <label>版本</label>
              <input 
                v-model.number="localSettings.version" 
                type="number" 
                class="form-input"
                min="1"
                readonly
              />
              <small class="form-help">版本号由系统自动管理</small>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input 
                    v-model="localSettings.autoSave" 
                    type="checkbox"
                  />
                  <span class="checkmark"></span>
                  启用自动保存
                </label>
              </div>
              <small class="form-help">每30秒自动保存工作流</small>
            </div>

            <div class="form-group">
              <label>自动保存间隔（秒）</label>
              <input 
                v-model.number="localSettings.autoSaveInterval" 
                type="number" 
                class="form-input"
                min="10"
                max="300"
                :disabled="!localSettings.autoSave"
              />
            </div>
          </div>

          <!-- 画布设置 -->
          <div v-show="activeTab === 'canvas'" class="tab-panel">
            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input 
                    v-model="localSettings.snapToGrid" 
                    type="checkbox"
                  />
                  <span class="checkmark"></span>
                  启用网格对齐
                </label>
              </div>
            </div>

            <div class="form-group">
              <label>网格大小</label>
              <input 
                v-model.number="localSettings.gridSize" 
                type="number" 
                class="form-input"
                min="5"
                max="50"
                :disabled="!localSettings.snapToGrid"
              />
            </div>

            <div class="form-group">
              <label>背景样式</label>
              <select v-model="localSettings.backgroundPattern" class="form-select">
                <option value="dots">点状</option>
                <option value="lines">线状</option>
                <option value="cross">十字</option>
              </select>
            </div>

            <div class="form-group">
              <label>背景颜色</label>
              <input 
                v-model="localSettings.backgroundColor" 
                type="color" 
                class="form-input color-input"
              />
            </div>

            <div class="form-group">
              <label>网格颜色</label>
              <input 
                v-model="localSettings.gridColor" 
                type="color" 
                class="form-input color-input"
              />
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input 
                    v-model="localSettings.showMiniMap" 
                    type="checkbox"
                  />
                  <span class="checkmark"></span>
                  显示小地图
                </label>
              </div>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input 
                    v-model="localSettings.showControls" 
                    type="checkbox"
                  />
                  <span class="checkmark"></span>
                  显示控制面板
                </label>
              </div>
            </div>
          </div>

          <!-- 全局变量 -->
          <div v-show="activeTab === 'variables'" class="tab-panel">
            <div class="variables-header">
              <h4>全局变量</h4>
              <button class="btn btn-primary btn-sm" @click="addVariable">
                <i class="fas fa-plus"></i>
                添加变量
              </button>
            </div>

            <div class="variables-list">
              <div 
                v-for="(variable, index) in localSettings.globalVariables" 
                :key="index"
                class="variable-item"
              >
                <div class="variable-inputs">
                  <input 
                    v-model="variable.name" 
                    type="text" 
                    placeholder="变量名"
                    class="form-input variable-name"
                  />
                  <select v-model="variable.type" class="form-select variable-type">
                    <option value="string">字符串</option>
                    <option value="number">数字</option>
                    <option value="boolean">布尔值</option>
                    <option value="object">对象</option>
                    <option value="array">数组</option>
                  </select>
                  <input 
                    v-if="variable.type !== 'object' && variable.type !== 'array'"
                    v-model="variable.value" 
                    :type="variable.type === 'number' ? 'number' : 'text'"
                    placeholder="变量值"
                    class="form-input variable-value"
                  />
                  <textarea 
                    v-else
                    v-model="variable.value" 
                    placeholder="JSON格式"
                    class="form-textarea variable-value"
                    rows="2"
                  ></textarea>
                </div>
                <button 
                  class="btn btn-danger btn-sm"
                  @click="removeVariable(index)"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>

            <div v-if="localSettings.globalVariables.length === 0" class="no-variables">
              <i class="fas fa-code"></i>
              <p>暂无全局变量</p>
            </div>
          </div>

          <!-- 执行设置 -->
          <div v-show="activeTab === 'execution'" class="tab-panel">
            <div class="form-group">
              <label>执行模式</label>
              <select v-model="localSettings.executionMode" class="form-select">
                <option value="sequential">顺序执行</option>
                <option value="parallel">并行执行</option>
                <option value="conditional">条件执行</option>
              </select>
            </div>

            <div class="form-group">
              <label>超时时间（秒）</label>
              <input 
                v-model.number="localSettings.timeout" 
                type="number" 
                class="form-input"
                min="1"
                max="3600"
              />
            </div>

            <div class="form-group">
              <label>最大重试次数</label>
              <input 
                v-model.number="localSettings.maxRetries" 
                type="number" 
                class="form-input"
                min="0"
                max="10"
              />
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input 
                    v-model="localSettings.enableLogging" 
                    type="checkbox"
                  />
                  <span class="checkmark"></span>
                  启用执行日志
                </label>
              </div>
            </div>

            <div class="form-group">
              <label>日志级别</label>
              <select 
                v-model="localSettings.logLevel" 
                class="form-select"
                :disabled="!localSettings.enableLogging"
              >
                <option value="debug">调试</option>
                <option value="info">信息</option>
                <option value="warn">警告</option>
                <option value="error">错误</option>
              </select>
            </div>

            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input 
                    v-model="localSettings.enableMetrics" 
                    type="checkbox"
                  />
                  <span class="checkmark"></span>
                  启用性能指标
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="onClose">
          取消
        </button>
        <button class="btn btn-primary" @click="onSave">
          保存设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { WorkflowConfig } from '@/api/workflow'

// Props
interface Props {
  workflowConfig: WorkflowConfig
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
  'save': [settings: any]
}>()

// 响应式数据
const activeTab = ref('basic')

const tabs = [
  { key: 'basic', label: '基础设置', icon: 'fa-cog' },
  { key: 'canvas', label: '画布设置', icon: 'fa-paint-brush' },
  { key: 'variables', label: '全局变量', icon: 'fa-code' },
  { key: 'execution', label: '执行设置', icon: 'fa-play' }
]

const localSettings = reactive({
  name: '',
  description: '',
  version: 1,
  autoSave: true,
  autoSaveInterval: 30,
  snapToGrid: true,
  gridSize: 20,
  backgroundPattern: 'dots',
  backgroundColor: '#ffffff',
  gridColor: '#e5e7eb',
  showMiniMap: true,
  showControls: true,
  globalVariables: [] as Array<{
    name: string
    type: string
    value: any
  }>,
  executionMode: 'sequential',
  timeout: 300,
  maxRetries: 3,
  enableLogging: true,
  logLevel: 'info',
  enableMetrics: true
})

// 方法
const onClose = () => {
  emit('close')
}

const onSave = () => {
  emit('save', { ...localSettings })
}

const addVariable = () => {
  localSettings.globalVariables.push({
    name: '',
    type: 'string',
    value: ''
  })
}

const removeVariable = (index: number) => {
  localSettings.globalVariables.splice(index, 1)
}

// 初始化设置
const initializeSettings = () => {
  // 从工作流配置中加载设置
  Object.assign(localSettings, {
    name: props.workflowConfig.agentId || '',
    description: '',
    version: props.workflowConfig.version || 1,
    globalVariables: Object.entries(props.workflowConfig.globalVariables || {}).map(([name, value]) => ({
      name,
      type: typeof value,
      value
    }))
  })
}

// 生命周期
onMounted(() => {
  initializeSettings()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.settings-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.tab-btn {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: #374151;
  background: #f3f4f6;
}

.tab-btn.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: white;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.color-input {
  height: 44px;
  padding: 6px;
}

.form-help {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.variables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.variables-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.variables-list {
  space-y: 12px;
}

.variable-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
}

.variable-inputs {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 120px 1fr;
  gap: 12px;
  align-items: start;
}

.variable-name,
.variable-type,
.variable-value {
  margin: 0;
}

.no-variables {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.no-variables i {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-danger:hover {
  background: #fee2e2;
}
</style>
