package com.xhcai.modules.rag.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 分段配置类型处理器
 *
 * <AUTHOR>
 */
@MappedTypes({SegmentConfig.class})
@MappedJdbcTypes({JdbcType.OTHER})
public class SegmentConfigTypeHandler extends BaseTypeHandler<SegmentConfig> {

    private static final Logger log = LoggerFactory.getLogger(SegmentConfigTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, SegmentConfig parameter, JdbcType jdbcType) throws SQLException {
        try {
            PGobject jsonObject = new PGobject();
            jsonObject.setType("jsonb");
            
            if (parameter == null) {
                jsonObject.setValue(null);
            } else {
                String jsonString = objectMapper.writeValueAsString(parameter);
                jsonObject.setValue(jsonString);
                log.debug("设置SegmentConfig JSON参数: {}", jsonString);
            }
            
            ps.setObject(i, jsonObject);
        } catch (JsonProcessingException e) {
            log.error("SegmentConfig JSON序列化失败", e);
            throw new SQLException("SegmentConfig JSON序列化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public SegmentConfig getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonString = rs.getString(columnName);
        return parseJson(jsonString);
    }

    @Override
    public SegmentConfig getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonString = rs.getString(columnIndex);
        return parseJson(jsonString);
    }

    @Override
    public SegmentConfig getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonString = cs.getString(columnIndex);
        return parseJson(jsonString);
    }

    private SegmentConfig parseJson(String jsonString) throws SQLException {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            return objectMapper.readValue(jsonString, SegmentConfig.class);
        } catch (JsonProcessingException e) {
            log.error("SegmentConfig JSON反序列化失败: {}", jsonString, e);
            throw new SQLException("SegmentConfig JSON反序列化失败: " + e.getMessage(), e);
        }
    }
}
