package com.xhcai.modules.dify.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 用户第三方智能体账号关联实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "third_platform_account", indexes = {
    @Index(name = "idx_third_platform_account_user_id", columnList = "user_id"),
    @Index(name = "idx_third_platform_account_id", columnList = "platform_id"),
    @Index(name = "idx_third_platform_account_status", columnList = "status"),
    @Index(name = "idx_third_platform_account_create_time", columnList = "create_time"),
    @Index(name = "idx_third_platform_account_tenant_id", columnList = "tenant_id"),
    @Index(name = "idx_third_platform_account_deleted", columnList = "deleted")
})
@Schema(description = "用户第三方智能体账号关联")
@TableName("third_platform_account")
public class ThirdPlatformAccount extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Column(name = "user_id", length = 36, nullable = false)
    @Schema(description = "用户ID", example = "1")
    @NotBlank(message = "用户ID不能为空")
    @TableField("user_id")
    private String userId;

    /**
     * 第三方平台ID（关联third_platform.id）
     */
    @Column(name = "platform_id", length = 36, nullable = false)
    @Schema(description = "第三方平台ID（关联third_platform.id）", example = "platform-uuid-123")
    @NotBlank(message = "第三方平台ID不能为空")
    @Size(min = 1, max = 36, message = "第三方平台ID长度必须在1-36个字符之间")
    @TableField("platform_id")
    private String platformId;

    /**
     * 账号名称
     */
    @Column(name = "account_name", length = 100, nullable = false)
    @Schema(description = "账号名称", example = "我的Dify账号")
    @NotBlank(message = "账号名称不能为空")
    @Size(min = 1, max = 100, message = "账号名称长度必须在1-100个字符之间")
    @TableField("account_name")
    private String accountName;

    /**
     * API密钥
     */
    @Column(name = "api_key", length = 500)
    @Schema(description = "API密钥", example = "app-***")
    @Size(max = 500, message = "API密钥长度不能超过500个字符")
    @TableField("api_key")
    private String apiKey;

    /**
     * 密码（加密存储）
     */
    @Column(name = "pwd", length = 100)
    @Schema(description = "密码")
    @Size(max = 100, message = "密码长度不能超过100个字符")
    @TableField("pwd")
    private String pwd;

    /**
     * 备注信息
     */
    @Column(name = "remark", length = 500)
    @Schema(description = "备注信息", example = "用于测试的账号")
    @Size(max = 500, message = "备注信息长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 状态：0-禁用，1-启用
     */
    @Column(name = "status", nullable = false)
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    @NotNull(message = "状态不能为空")
    @TableField("status")
    private Integer status;

    /**
     * 最后连接测试时间
     */
    @Column(name = "last_test_time")
    @Schema(description = "最后连接测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_test_time")
    private LocalDateTime lastTestTime;

    /**
     * 最后连接测试结果：0-失败，1-成功
     */
    @Column(name = "last_test_result")
    @Schema(description = "最后连接测试结果：0-失败，1-成功", example = "1")
    @TableField("last_test_result")
    private Integer lastTestResult;

    /**
     * 最后连接测试错误信息
     */
    @Column(name = "last_test_error", length = 1000)
    @Schema(description = "最后连接测试错误信息")
    @Size(max = 1000, message = "连接测试错误信息长度不能超过1000个字符")
    @TableField("last_test_error")
    private String lastTestError;

    /**
     * 总调用次数
     */
    @Column(name = "total_calls")
    @Schema(description = "总调用次数", example = "100")
    @TableField("total_calls")
    private Long totalCalls;

    /**
     * 成功调用次数
     */
    @Column(name = "success_calls")
    @Schema(description = "成功调用次数", example = "95")
    @TableField("success_calls")
    private Long successCalls;

    /**
     * 平均响应时间（毫秒）
     */
    @Column(name = "avg_response_time")
    @Schema(description = "平均响应时间（毫秒）", example = "1200")
    @TableField("avg_response_time")
    private Long avgResponseTime;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_time")
    @Schema(description = "最后使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_used_time")
    private LocalDateTime lastUsedTime;

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getLastTestTime() {
        return lastTestTime;
    }

    public void setLastTestTime(LocalDateTime lastTestTime) {
        this.lastTestTime = lastTestTime;
    }

    public Integer getLastTestResult() {
        return lastTestResult;
    }

    public void setLastTestResult(Integer lastTestResult) {
        this.lastTestResult = lastTestResult;
    }

    public String getLastTestError() {
        return lastTestError;
    }

    public void setLastTestError(String lastTestError) {
        this.lastTestError = lastTestError;
    }

    public Long getTotalCalls() {
        return totalCalls;
    }

    public void setTotalCalls(Long totalCalls) {
        this.totalCalls = totalCalls;
    }

    public Long getSuccessCalls() {
        return successCalls;
    }

    public void setSuccessCalls(Long successCalls) {
        this.successCalls = successCalls;
    }

    public Long getAvgResponseTime() {
        return avgResponseTime;
    }

    public void setAvgResponseTime(Long avgResponseTime) {
        this.avgResponseTime = avgResponseTime;
    }

    public LocalDateTime getLastUsedTime() {
        return lastUsedTime;
    }

    public void setLastUsedTime(LocalDateTime lastUsedTime) {
        this.lastUsedTime = lastUsedTime;
    }

    @Override
    public String toString() {
        return "ThirdPlatformAccount{" +
                "userId='" + userId + '\'' +
                ", platformId='" + platformId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", lastTestTime=" + lastTestTime +
                ", lastTestResult=" + lastTestResult +
                ", totalCalls=" + totalCalls +
                ", successCalls=" + successCalls +
                ", avgResponseTime=" + avgResponseTime +
                ", lastUsedTime=" + lastUsedTime +
                ", " + super.toString() +
                '}';
    }
}
