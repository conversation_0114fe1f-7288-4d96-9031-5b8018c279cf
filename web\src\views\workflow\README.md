# 智能体编排工作流运行功能

## 功能概述

本功能实现了智能体编排工作流的可视化运行，支持节点状态实时更新、连接线动画效果、执行日志记录等功能。参考了Vue Flow的动画示例，提供了完整的工作流执行体验。

## 主要特性

### 1. 工作流执行引擎 (`utils/workflow-engine.ts`)
- ✅ 支持多种执行模式：正常、调试、单步执行
- ✅ 完整的执行状态管理：空闲、运行中、暂停、完成、错误、取消
- ✅ 节点执行状态跟踪：等待、运行中、完成、错误、跳过
- ✅ 执行进度实时更新
- ✅ 断点调试支持
- ✅ 错误处理和恢复机制
- ✅ 事件驱动架构，支持实时状态通知

### 2. 节点执行状态可视化 (`components/nodes/BaseNode.vue`)
- ✅ 执行状态指示器（图标 + 颜色）
- ✅ 进度条显示执行进度
- ✅ 动画效果：脉冲、抖动、旋转等
- ✅ 状态文本提示
- ✅ 响应式状态更新

### 3. 流程运行控制面板 (`components/WorkflowRunner.vue`)
- ✅ 执行控制按钮：开始、暂停、恢复、停止、重置
- ✅ 执行状态显示：状态、进度、当前节点
- ✅ 执行统计：总节点数、完成数、错误数、耗时
- ✅ 支持本地执行和远程执行两种模式
- ✅ 实时状态轮询（远程模式）

### 4. 连接线动画效果 (`components/AnimatedEdge.vue`)
- ✅ 数据流动动画：流动的点、数据包
- ✅ 连接线状态变化：颜色、宽度、样式
- ✅ 执行路径高亮显示
- ✅ 可配置的动画参数：速度、颜色、数量

### 5. 执行日志面板 (`components/ExecutionLogPanel.vue`)
- ✅ 可折叠的日志面板
- ✅ 分步骤显示执行详情
- ✅ 输入输出数据展示
- ✅ 错误信息高亮显示
- ✅ 执行日志分级显示
- ✅ 执行统计汇总

### 6. API接口集成 (`api/workflow.ts`)
- ✅ 启动/暂停/恢复/停止执行
- ✅ 获取执行状态和日志
- ✅ 断点管理
- ✅ 执行历史查询
- ✅ 单步调试支持

## 画布动态展示功能

### 节点状态可视化
- ✅ **等待状态**: 黄色边框 + 脉冲动画
- ✅ **运行中状态**: 蓝色边框 + 旋转图标 + 进度条
- ✅ **完成状态**: 绿色边框 + 完成图标
- ✅ **错误状态**: 红色边框 + 错误图标 + 抖动动画
- ✅ **当前执行高亮**: 橙色高亮边框 + 特殊动画效果

### 连接线动画效果
- ✅ **数据流动动画**: 沿连接线移动的蓝色圆点
- ✅ **数据包传输**: 带标签的数据包沿路径移动
- ✅ **执行路径高亮**: 激活的连接线变蓝加粗
- ✅ **流动方向指示**: 动画显示数据流向

### 实时状态更新
- ✅ **节点进度实时更新**: 进度条显示执行百分比
- ✅ **状态文本提示**: 显示当前执行阶段信息
- ✅ **执行路径追踪**: 高亮显示当前执行路径
- ✅ **错误状态标记**: 失败节点红色高亮

## 使用方法

### 1. 运行控制操作

#### 开始执行
1. 点击工作流编辑器顶部的"显示运行器"按钮
2. 在运行控制面板中点击"开始"按钮
3. 观察画布上的节点状态变化：
   - 所有节点初始化为"等待"状态（黄色边框）
   - 开始节点变为"准备执行"状态
   - 执行开始后，当前节点高亮显示（橙色边框）

#### 暂停/恢复执行
- **暂停**: 点击"暂停"按钮，当前执行会在下一个节点处暂停
- **恢复**: 点击"恢复"按钮，继续从暂停点执行
- 暂停期间，当前节点保持高亮状态

#### 停止执行
- 点击"停止"按钮立即终止执行
- 所有节点状态重置，连接线动画停止
- 执行日志保留，可查看已完成的步骤

#### 重置状态
- 点击"重置"按钮清除所有执行状态
- 节点恢复到初始状态
- 清空执行日志和统计信息

### 2. 画布状态观察

#### 节点状态变化
```
等待执行 → 准备执行 → 执行中 → 完成/错误
  ↓         ↓        ↓       ↓
黄色脉冲   橙色高亮   蓝色旋转  绿色/红色
```

#### 连接线动画
- **数据流动**: 蓝色圆点沿连接线移动
- **数据包**: 矩形数据包显示传输内容
- **路径高亮**: 激活连接线变蓝加粗
- **动画时长**: 每个动画持续约2秒

### 3. 代码集成

```vue
<template>
  <div class="workflow-editor">
    <!-- 工作流运行器 -->
    <WorkflowRunner
      :nodes="nodes"
      :edges="edges"
      :agent-id="agentId"
      :global-variables="globalVariables"
      :use-remote-execution="false"
      @execution-start="onExecutionStart"
      @execution-complete="onExecutionComplete"
      @execution-error="onExecutionError"
      @node-start="onNodeStart"
      @node-complete="onNodeComplete"
      @progress-update="onProgressUpdate"
      @edge-traverse="onEdgeTraverse"
    />
    
    <!-- 执行日志面板 -->
    <ExecutionLogPanel
      :execution-steps="executionSteps"
      :execution-stats="executionStats"
      @clear-logs="clearExecutionLogs"
    />
  </div>
</template>
```

### 2. 节点状态更新

```typescript
// 更新节点执行状态
const updateNodeExecutionState = (nodeId: string, status: string, progress: number, info: string) => {
  const nodeIndex = nodes.value.findIndex(n => n.id === nodeId)
  if (nodeIndex !== -1) {
    nodes.value[nodeIndex] = {
      ...nodes.value[nodeIndex],
      data: {
        ...nodes.value[nodeIndex].data,
        executionStatus: status,
        executionProgress: progress,
        executionInfo: info
      }
    }
  }
}
```

### 3. 连接线动画

```typescript
// 激活连接线动画
const updateEdgeAnimationState = (edgeId: string, isFlowing: boolean, data: any) => {
  const edgeIndex = edges.value.findIndex(e => e.id === edgeId)
  if (edgeIndex !== -1) {
    edges.value[edgeIndex] = {
      ...edges.value[edgeIndex],
      animated: isFlowing,
      data: {
        ...edges.value[edgeIndex].data,
        isFlowing,
        dataPacket: data ? { label: 'Data', data } : null
      }
    }
  }
}
```

### 4. 执行引擎使用

```typescript
import { WorkflowEngine } from '@/utils/workflow-engine'

// 创建执行引擎
const engine = new WorkflowEngine(nodes, edges, 'normal')

// 设置事件监听
engine.on('execution-start', (context) => {
  console.log('执行开始:', context)
})

engine.on('node-start', (data) => {
  console.log('节点开始执行:', data.node.id)
})

engine.on('progress-update', (data) => {
  console.log('进度更新:', data.progress)
})

// 开始执行
await engine.execute()
```

## 动画效果

### 节点状态动画
- **等待状态**: 黄色脉冲动画
- **运行中**: 蓝色脉冲 + 旋转图标
- **完成**: 绿色边框
- **错误**: 红色边框 + 抖动动画
- **跳过**: 灰色半透明

### 连接线动画
- **数据流动**: 流动的蓝色圆点
- **数据包**: 带标签的矩形数据包沿路径移动
- **路径高亮**: 执行时连接线变蓝加粗
- **虚线动画**: 待执行状态的虚线流动

### 进度条动画
- **渐变填充**: 蓝色到青色的渐变
- **光泽效果**: 移动的高光条
- **平滑过渡**: 进度变化的缓动动画

## 配置选项

### WorkflowRunner 配置
```typescript
interface WorkflowRunnerProps {
  nodes: Node[]                    // 节点数据
  edges: Edge[]                    // 连接线数据
  agentId?: string                 // 智能体ID
  globalVariables?: Record<string, any>  // 全局变量
  useRemoteExecution?: boolean     // 是否使用远程执行
}
```

### 执行引擎配置
```typescript
interface ExecutionContext {
  id: string                       // 执行ID
  status: ExecutionStatus          // 执行状态
  mode: ExecutionMode              // 执行模式
  breakpoints: Set<string>         // 断点集合
  variables: Record<string, any>   // 变量存储
  metrics: ExecutionMetrics        // 执行指标
}
```

### 动画配置
```typescript
interface AnimatedEdgeProps {
  isAnimated?: boolean             // 是否启用动画
  isFlowing?: boolean              // 是否流动
  animationSpeed?: number          // 动画速度
  dotCount?: number                // 流动点数量
  dotColor?: string                // 点颜色
  packetColor?: string             // 数据包颜色
}
```

## 扩展开发

### 添加新的节点类型
1. 在 `workflow-engine.ts` 的 `executeNodeByType` 方法中添加新的 case
2. 实现对应的执行方法
3. 在节点库配置中注册新节点类型

### 自定义动画效果
1. 扩展 `AnimatedEdge.vue` 组件
2. 添加新的 CSS 动画类
3. 在执行引擎中触发相应的动画事件

### 集成外部服务
1. 在 API 层添加新的接口定义
2. 在执行引擎中调用外部服务
3. 处理异步执行和状态同步

## 注意事项

1. **性能优化**: 大量节点时建议启用虚拟滚动
2. **内存管理**: 及时清理执行状态和事件监听器
3. **错误处理**: 确保异常情况下的状态一致性
4. **用户体验**: 提供清晰的状态反馈和操作提示

## 技术栈

- **Vue 3**: 响应式框架
- **Vue Flow**: 流程图渲染
- **TypeScript**: 类型安全
- **EventEmitter**: 事件驱动
- **CSS3**: 动画效果
- **WebSocket**: 实时通信（可选）

## 参考资料

- [Vue Flow 官方文档](https://vueflow.dev/)
- [Vue Flow 动画示例](https://vueflow.dev/examples/layout/animated.html)
- [CSS 动画最佳实践](https://web.dev/animations/)
- [工作流引擎设计模式](https://martinfowler.com/articles/workflow-patterns.html)
