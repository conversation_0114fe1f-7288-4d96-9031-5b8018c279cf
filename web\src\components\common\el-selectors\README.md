# Element Plus 选择器组件

基于 Element Plus 重构的选择器组件集合，保留原有功能完整性和后端数据对接，优化样式布局适配 Element Plus。

## 组件列表

### ElDeptTreeSelector - 部门树选择器
- 支持单选/多选
- 树形结构展示部门层级
- 支持搜索过滤
- 支持禁用特定部门
- 支持只显示启用部门

### ElPermissionByRoleSelector - 角色权限选择器  
- 按角色分组显示权限
- 支持权限树形结构
- 支持批量选择/取消
- 支持权限类型过滤

### ElStatusSelector - 状态选择器
- 预设多种状态类型（基础、智能体、用户、系统）
- 支持自定义状态选项
- 状态徽章样式显示
- 支持状态描述

### ElUserByDeptSelector - 部门用户选择器
- 按部门分组显示用户
- 支持用户搜索
- 显示用户详细信息
- 支持多选用户

### ElUserByRoleSelector - 角色用户选择器
- 按角色分组显示用户
- 支持角色切换
- 用户信息展示
- 支持批量操作

## 使用方式

```vue
<template>
  <!-- 部门选择器 -->
  <ElDeptTreeSelector
    v-model="selectedDept"
    :config="{ multiple: false, clearable: true }"
    placeholder="请选择部门"
    @change="handleDeptChange"
  />

  <!-- 状态选择器 -->
  <ElStatusSelector
    v-model="selectedStatus"
    preset="user"
    :config="{ clearable: true }"
    @change="handleStatusChange"
  />
</template>

<script setup>
import { 
  ElDeptTreeSelector, 
  ElStatusSelector 
} from '@/components/common/el-selectors'

const selectedDept = ref('')
const selectedStatus = ref('')

const handleDeptChange = (value, option) => {
  console.log('部门变更:', value, option)
}

const handleStatusChange = (value, option) => {
  console.log('状态变更:', value, option)
}
</script>
```

## 特性

- 🎨 基于 Element Plus 设计规范
- 🔧 保留原有功能完整性
- 📡 完整的后端数据对接
- 🎯 统一的 API 接口
- 📱 响应式设计
- 🌐 国际化支持
- ♿ 无障碍访问
- 🎭 主题定制

## 配置选项

所有选择器都支持通过 `config` 属性传入配置：

```typescript
interface SelectorConfig {
  multiple?: boolean          // 是否多选
  clearable?: boolean         // 是否可清空
  placeholder?: string        // 占位文本
  size?: 'large' | 'default' | 'small'  // 尺寸
  disabled?: boolean          // 是否禁用
  filterable?: boolean        // 是否可搜索
  // ... 更多配置选项
}
```
