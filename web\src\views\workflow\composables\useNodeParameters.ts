/**
 * 节点参数依赖管理
 * 处理节点间的参数传递和依赖关系
 */

import { computed, ref, type Ref } from 'vue'
import type { Node, Edge } from '@vue-flow/core'

// 参数类型定义
export interface NodeParameter {
  key: string
  label: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  value: any
  description?: string
  required?: boolean
  nodeId: string
  nodeName: string
}

// 参数选择项
export interface ParameterOption {
  label: string
  value: string
  nodeId: string
  nodeName: string
  paramKey: string
  paramType: string
  description?: string
}

/**
 * 节点参数管理Hook
 */
export function useNodeParameters(nodes: Ref<Node[]>, edges: Ref<Edge[]>) {
  
  /**
   * 获取节点的所有输出参数
   */
  const getNodeOutputParameters = (nodeId: string): NodeParameter[] => {
    const node = nodes.value.find((n: Node) => n.id === nodeId)
    if (!node) return []

    const parameters: NodeParameter[] = []
    
    // 基础输出参数（所有节点都有）
    parameters.push({
      key: 'output',
      label: '节点输出',
      type: 'object',
      value: null,
      description: '节点的主要输出结果',
      nodeId: node.id,
      nodeName: node.data?.label || node.type
    })

    parameters.push({
      key: 'status',
      label: '执行状态',
      type: 'string',
      value: 'pending',
      description: '节点的执行状态',
      nodeId: node.id,
      nodeName: node.data?.label || node.type
    })

    // 根据节点类型添加特定参数
    switch (node.type) {
      case 'start':
        parameters.push({
          key: 'timestamp',
          label: '开始时间',
          type: 'string',
          value: new Date().toISOString(),
          description: '工作流开始执行的时间戳',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        break

      case 'database':
        parameters.push({
          key: 'queryResult',
          label: '查询结果',
          type: 'array',
          value: [],
          description: '数据库查询返回的结果集',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        parameters.push({
          key: 'rowCount',
          label: '影响行数',
          type: 'number',
          value: 0,
          description: '查询影响的行数',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        break

      case 'ai-chat':
        parameters.push({
          key: 'response',
          label: 'AI回复',
          type: 'string',
          value: '',
          description: 'AI模型的回复内容',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        parameters.push({
          key: 'tokens',
          label: 'Token消耗',
          type: 'number',
          value: 0,
          description: '本次请求消耗的token数量',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        break

      case 'condition':
        parameters.push({
          key: 'conditionResult',
          label: '条件结果',
          type: 'boolean',
          value: false,
          description: '条件判断的结果',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        break

      case 'file':
        parameters.push({
          key: 'filePath',
          label: '文件路径',
          type: 'string',
          value: '',
          description: '处理的文件路径',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        parameters.push({
          key: 'fileContent',
          label: '文件内容',
          type: 'string',
          value: '',
          description: '文件的内容',
          nodeId: node.id,
          nodeName: node.data?.label || node.type
        })
        break
    }

    // 添加节点配置中的自定义参数
    if (node.data?.config) {
      Object.entries(node.data.config).forEach(([key, value]) => {
        if (!parameters.some(p => p.key === key)) {
          parameters.push({
            key,
            label: key,
            type: typeof value as any,
            value,
            description: `节点配置参数: ${key}`,
            nodeId: node.id,
            nodeName: node.data?.label || node.type
          })
        }
      })
    }

    return parameters
  }

  /**
   * 获取节点的所有上游节点（递归）
   */
  const getUpstreamNodes = (nodeId: string, visited = new Set<string>()): string[] => {
    if (visited.has(nodeId)) return []
    visited.add(nodeId)

    const upstreamNodeIds: string[] = []
    
    // 找到所有指向当前节点的边
    const incomingEdges = edges.value.filter(edge => edge.target === nodeId)
    
    incomingEdges.forEach((edge: Edge) => {
      upstreamNodeIds.push(edge.source)
      // 递归获取上游节点的上游节点
      const nestedUpstream = getUpstreamNodes(edge.source, visited)
      upstreamNodeIds.push(...nestedUpstream)
    })

    return [...new Set(upstreamNodeIds)]
  }

  /**
   * 获取节点可用的参数选项
   */
  const getAvailableParameters = (nodeId: string): ParameterOption[] => {
    const upstreamNodeIds = getUpstreamNodes(nodeId)
    const options: ParameterOption[] = []

    upstreamNodeIds.forEach(upstreamNodeId => {
      const parameters = getNodeOutputParameters(upstreamNodeId)
      parameters.forEach(param => {
        options.push({
          label: `${param.nodeName}.${param.label}`,
          value: `{{${param.nodeId}.${param.key}}}`,
          nodeId: param.nodeId,
          nodeName: param.nodeName,
          paramKey: param.key,
          paramType: param.type,
          description: param.description
        })
      })
    })

    return options
  }

  /**
   * 解析参数引用
   */
  const parseParameterReference = (value: string): { nodeId: string; paramKey: string } | null => {
    const match = value.match(/^\{\{([^.]+)\.([^}]+)\}\}$/)
    if (match) {
      return {
        nodeId: match[1],
        paramKey: match[2]
      }
    }
    return null
  }

  /**
   * 检查参数引用是否有效
   */
  const isValidParameterReference = (value: string, nodeId: string): boolean => {
    const ref = parseParameterReference(value)
    if (!ref) return false

    const upstreamNodeIds = getUpstreamNodes(nodeId)
    return upstreamNodeIds.includes(ref.nodeId)
  }

  /**
   * 获取参数的实际值
   */
  const resolveParameterValue = (value: any, nodeId: string): any => {
    if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
      const ref = parseParameterReference(value)
      if (ref && isValidParameterReference(value, nodeId)) {
        // 这里应该从实际的执行结果中获取值
        // 目前返回占位符
        return `[${ref.nodeId}.${ref.paramKey}]`
      }
    }
    return value
  }

  /**
   * 验证节点的参数依赖
   */
  const validateNodeDependencies = (nodeId: string): { valid: boolean; errors: string[] } => {
    const node = nodes.value.find((n: Node) => n.id === nodeId)
    if (!node) return { valid: false, errors: ['节点不存在'] }

    const errors: string[] = []
    const upstreamNodeIds = getUpstreamNodes(nodeId)

    // 检查节点配置中的参数引用
    if (node.data?.config) {
      Object.entries(node.data.config).forEach(([key, value]) => {
        if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
          if (!isValidParameterReference(value, nodeId)) {
            errors.push(`参数 ${key} 引用了无效的上游节点参数: ${value}`)
          }
        }
      })
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 获取节点的依赖图
   */
  const getNodeDependencyGraph = (nodeId: string) => {
    const upstreamNodes = getUpstreamNodes(nodeId)
    const graph: Record<string, string[]> = {}

    upstreamNodes.forEach(upstreamNodeId => {
      graph[upstreamNodeId] = getUpstreamNodes(upstreamNodeId)
    })

    return graph
  }

  return {
    getNodeOutputParameters,
    getUpstreamNodes,
    getAvailableParameters,
    parseParameterReference,
    isValidParameterReference,
    resolveParameterValue,
    validateNodeDependencies,
    getNodeDependencyGraph
  }
}
