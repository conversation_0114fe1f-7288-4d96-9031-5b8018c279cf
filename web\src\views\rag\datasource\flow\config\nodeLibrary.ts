/**
 * 数据源流程节点库配置
 */

// Position 枚举值
const Position = {
  Left: 'left',
  Right: 'right',
  Top: 'top',
  Bottom: 'bottom'
} as const

type PositionType = typeof Position[keyof typeof Position]

export interface NodeHandle {
  id: string
  position: PositionType
  label?: string
}

export interface NodeConfig {
  [key: string]: any
}

export interface NodeLibraryItem {
  type: string
  label: string
  icon: string
  description: string
  category: string
  maxInstances?: number
  color: string
  gradient: string
  labelColor: string
  iconColor: string
  status: 'stable' | 'beta' | 'alpha' | 'deprecated'
  version: string
  tags: string[]
  handles: {
    source: NodeHandle[]
    target: NodeHandle[]
  }
  defaultData: {
    label: string
    config: NodeConfig
  }
}

export interface NodeCategory {
  name: string
  icon: string
  description: string
  nodes: NodeLibraryItem[]
}

export interface NodeLibraryConfig {
  categories: NodeCategory[]
}

/**
 * 数据源流程节点库配置
 */
export const NODE_LIBRARY_CONFIG: NodeLibraryConfig = {
  categories: [
    {
      name: "数据接入",
      icon: "fas fa-database",
      description: "各种数据源接入节点",
      nodes: [
        {
          type: "ftp",
          label: "FTP数据源",
          icon: "fas fa-server",
          description: "从FTP服务器获取文件数据",
          category: "datasource",
          color: "#3b82f6",
          gradient: "linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)",
          labelColor: "#1e40af",
          iconColor: "#3b82f6",
          status: "stable",
          version: "1.0.0",
          tags: ["文件系统", "FTP"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "FTP数据源",
            config: {
              host: "",
              port: 21,
              username: "",
              password: "",
              path: "/",
              filePattern: "*.*"
            }
          }
        },
        {
          type: "mysql",
          label: "MySQL数据库",
          icon: "fas fa-database",
          description: "从MySQL数据库获取结构化数据",
          category: "datasource",
          color: "#f59e0b",
          gradient: "linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)",
          labelColor: "#92400e",
          iconColor: "#f59e0b",
          status: "stable",
          version: "1.0.0",
          tags: ["关系数据库", "MySQL"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "MySQL数据库",
            config: {
              host: "",
              port: 3306,
              database: "",
              username: "",
              password: "",
              query: "SELECT * FROM table_name"
            }
          }
        },
        {
          type: "elasticsearch",
          label: "Elasticsearch",
          icon: "fas fa-search",
          description: "从Elasticsearch获取非结构化数据",
          category: "datasource",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%)",
          labelColor: "#6b21a8",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["搜索引擎", "Elasticsearch"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "Elasticsearch",
            config: {
              host: "",
              port: 9200,
              index: "",
              query: {},
              size: 1000
            }
          }
        },
        {
          type: "minio",
          label: "MinIO对象存储",
          icon: "fas fa-cloud",
          description: "从MinIO对象存储获取文件",
          category: "datasource",
          color: "#ef4444",
          gradient: "linear-gradient(135deg, #fef2f2 0%, #fecaca 100%)",
          labelColor: "#7f1d1d",
          iconColor: "#ef4444",
          status: "stable",
          version: "1.0.0",
          tags: ["对象存储", "MinIO"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "MinIO对象存储",
            config: {
              endpoint: "",
              accessKey: "",
              secretKey: "",
              bucket: "",
              prefix: ""
            }
          }
        },
        {
          type: "postgresql",
          label: "PostgreSQL数据库",
          icon: "fas fa-elephant",
          description: "从PostgreSQL数据库获取结构化数据",
          category: "datasource",
          color: "#336791",
          gradient: "linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",
          labelColor: "#0c4a6e",
          iconColor: "#336791",
          status: "stable",
          version: "1.0.0",
          tags: ["关系数据库", "PostgreSQL"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "PostgreSQL数据库",
            config: {
              host: "",
              port: 5432,
              database: "",
              username: "",
              password: "",
              query: "SELECT * FROM table_name",
              schema: "public"
            }
          }
        },
        {
          type: "oracle",
          label: "Oracle数据库",
          icon: "fas fa-database",
          description: "从Oracle数据库获取结构化数据",
          category: "datasource",
          color: "#f80000",
          gradient: "linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)",
          labelColor: "#7f1d1d",
          iconColor: "#f80000",
          status: "stable",
          version: "1.0.0",
          tags: ["关系数据库", "Oracle"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "Oracle数据库",
            config: {
              host: "",
              port: 1521,
              serviceName: "",
              username: "",
              password: "",
              query: "SELECT * FROM table_name"
            }
          }
        },
        {
          type: "local-file",
          label: "本地文件上传",
          icon: "fas fa-upload",
          description: "上传本地文件进行处理",
          category: "datasource",
          color: "#059669",
          gradient: "linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)",
          labelColor: "#065f46",
          iconColor: "#059669",
          status: "stable",
          version: "1.0.0",
          tags: ["文件上传", "本地文件"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "本地文件上传",
            config: {
              allowedTypes: [".pdf", ".docx", ".txt", ".md"],
              maxFileSize: "10MB",
              maxFiles: 10,
              autoProcess: true
            }
          }
        }
      ]
    },
    {
      name: "数据处理",
      icon: "fas fa-cogs",
      description: "数据处理和转换节点",
      nodes: [
        {
          type: "segment",
          label: "文档分段",
          icon: "fas fa-cut",
          description: "将文档内容分割成小段",
          category: "processing",
          color: "#10b981",
          gradient: "linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)",
          labelColor: "#065f46",
          iconColor: "#10b981",
          status: "stable",
          version: "1.0.0",
          tags: ["文档处理", "分段"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "文档分段",
            config: {
              chunkSize: 1000,
              chunkOverlap: 200,
              separator: "\n\n",
              keepSeparator: true
            }
          }
        },
        {
          type: "vectorize",
          label: "向量化",
          icon: "fas fa-vector-square",
          description: "将文本转换为向量表示",
          category: "processing",
          color: "#f59e0b",
          gradient: "linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)",
          labelColor: "#92400e",
          iconColor: "#f59e0b",
          status: "stable",
          version: "1.0.0",
          tags: ["向量化", "嵌入"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "向量化",
            config: {
              model: "text-embedding-ada-002",
              batchSize: 100,
              dimensions: 1536
            }
          }
        }
      ]
    },
    {
      name: "数据输出",
      icon: "fas fa-upload",
      description: "数据输出和存储节点",
      nodes: [
        {
          type: "weaviate",
          label: "Weaviate向量库",
          icon: "fas fa-database",
          description: "将向量数据存储到Weaviate",
          category: "output",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%)",
          labelColor: "#6b21a8",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["向量数据库", "Weaviate"],
          handles: {
            source: [],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "Weaviate向量库",
            config: {
              host: "",
              port: 8080,
              className: "",
              apiKey: ""
            }
          }
        },
        {
          type: "pinecone",
          label: "Pinecone向量库",
          icon: "fas fa-tree",
          description: "将向量数据存储到Pinecone",
          category: "output",
          color: "#22c55e",
          gradient: "linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)",
          labelColor: "#15803d",
          iconColor: "#22c55e",
          status: "stable",
          version: "1.0.0",
          tags: ["向量数据库", "Pinecone"],
          handles: {
            source: [],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "Pinecone向量库",
            config: {
              apiKey: "",
              environment: "",
              indexName: "",
              namespace: ""
            }
          }
        },
        {
          type: "milvus",
          label: "Milvus向量库",
          icon: "fas fa-cubes",
          description: "将向量数据存储到Milvus",
          category: "output",
          color: "#f59e0b",
          gradient: "linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)",
          labelColor: "#92400e",
          iconColor: "#f59e0b",
          status: "stable",
          version: "1.0.0",
          tags: ["向量数据库", "Milvus"],
          handles: {
            source: [],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "Milvus向量库",
            config: {
              host: "",
              port: 19530,
              collectionName: "",
              username: "",
              password: ""
            }
          }
        },
        {
          type: "chroma",
          label: "ChromaDB向量库",
          icon: "fas fa-palette",
          description: "将向量数据存储到ChromaDB",
          category: "output",
          color: "#ec4899",
          gradient: "linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%)",
          labelColor: "#9d174d",
          iconColor: "#ec4899",
          status: "stable",
          version: "1.0.0",
          tags: ["向量数据库", "ChromaDB"],
          handles: {
            source: [],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "ChromaDB向量库",
            config: {
              host: "",
              port: 8000,
              collectionName: "",
              apiKey: ""
            }
          }
        },
        {
          type: "qdrant",
          label: "Qdrant向量库",
          icon: "fas fa-dot-circle",
          description: "将向量数据存储到Qdrant",
          category: "output",
          color: "#6366f1",
          gradient: "linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)",
          labelColor: "#3730a3",
          iconColor: "#6366f1",
          status: "stable",
          version: "1.0.0",
          tags: ["向量数据库", "Qdrant"],
          handles: {
            source: [],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "Qdrant向量库",
            config: {
              host: "",
              port: 6333,
              collectionName: "",
              apiKey: ""
            }
          }
        }
      ]
    }
  ]
}
