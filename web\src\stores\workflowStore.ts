/**
 * Workflow 全局状态管理
 * 管理工作流编排的所有状态数据，包括节点、连接线、配置等
 */

import { ref, computed, watch } from 'vue'
import { defineStore } from 'pinia'
import type { Node, Edge } from '@vue-flow/core'
import type { WorkflowConfig, ViewportConfig } from '@/api/workflow'
import { loadAgentWorkflow, realtimeSaveWorkflow } from '@/api/workflow'
import type { Ref, ComputedRef } from 'vue'

// 执行状态类型
export interface ExecutionState {
  isRunning: boolean
  isPaused: boolean
  currentNodeId: string | null
  executionProgress: number
  executionStats: any
  executionSteps: any[]
}

// 面板状态类型
export interface PanelState {
  showNodeLibrary: boolean
  showProperties: boolean
  showRunner: boolean
  showDebugPanel: boolean
  leftPanelWidth: number
  rightPanelWidth: number
  debugPanelWidth: number
}

// 选择状态类型
export interface SelectionState {
  selectedNode: Node | null
  selectedEdge: Edge | null
}

// Workflow Store 类型定义
export interface WorkflowStore {
  // 状态
  agentId: Ref<string>
  agentInfo: Ref<any>
  workflowConfig: Ref<WorkflowConfig>
  nodes: Ref<Node[]>
  edges: Ref<Edge[]>
  viewport: Ref<ViewportConfig>
  selectionState: Ref<SelectionState>
  panelState: Ref<PanelState>
  executionState: Ref<ExecutionState>
  nodeExecutionStates: Ref<Map<string, any>>
  edgeAnimationStates: Ref<Map<string, any>>

  // 计算属性
  hasNodes: ComputedRef<boolean>
  currentWorkflowConfig: ComputedRef<any>

  // 基础方法
  initializeWorkflow: (id: string) => Promise<void>
  saveWorkflow: (operationType: string, operationDesc: string) => Promise<void>
  addNode: (node: Node) => void
  updateNode: (nodeId: string, updates: Partial<Node>) => void
  deleteNode: (nodeId: string) => void
  addEdge: (edge: Edge) => void
  updateEdge: (edgeId: string, updates: Partial<Edge>) => void
  deleteEdge: (edgeId: string) => void
  selectNode: (node: Node | null) => void
  selectEdge: (edge: Edge | null) => void
  clearSelection: () => void
  toggleNodeLibrary: () => void
  toggleProperties: () => void
  toggleRunner: () => void
  toggleDebugPanel: () => void
  setPanelWidth: (panel: 'left' | 'right' | 'debug', width: number) => void

  // 执行状态管理
  updateNodeExecutionState: (nodeId: string, status: string, progress: number, info: string) => void
  updateEdgeAnimationState: (edgeId: string, isFlowing: boolean, data: any) => void
  startExecution: () => void
  completeExecution: (stats: any) => void
  cancelExecution: () => void

  // 工具方法
  clearCanvas: () => void
  updateGlobalVariables: (variables: Record<string, any>) => void
  updateWorkflowSettings: (settings: Partial<WorkflowConfig>) => void
  getNodeExecutionStatus: (nodeId: string) => string
  getNodeExecutionProgress: (nodeId: string) => number
  getNodeExecutionInfo: (nodeId: string) => string
  getEdgeAnimated: (edgeId: string) => boolean
  getEdgeFlowing: (edgeId: string) => boolean
  getEdgeDataPacket: (edgeId: string) => any

  // 状态同步和验证
  enableAutoSave: () => void
  syncWorkflowConfig: () => void
  validateDataConsistency: () => { valid: boolean; errors: string[] }
  cleanupInvalidEdges: () => void
}

export const useWorkflowStore = defineStore('workflow', (): WorkflowStore => {
  // ===== 基础状态 =====
  const agentId = ref<string>('')
  const agentInfo = ref<any>({})
  
  // 工作流配置
  const workflowConfig = ref<WorkflowConfig>({
    agentId: '',
    version: 1,
    nodes: [],
    edges: [],
    viewport: { x: 0, y: 0, zoom: 1 },
    nodeLibrary: { categories: [] },
    globalVariables: {},
    lastModified: Date.now(),
    isPublished: false
  })

  // 核心数据
  const nodes = ref<Node[]>([])
  const edges = ref<Edge[]>([])
  const viewport = ref<ViewportConfig>({ x: 0, y: 0, zoom: 1 })

  // 选择状态
  const selectionState = ref<SelectionState>({
    selectedNode: null,
    selectedEdge: null
  })

  // 面板状态
  const panelState = ref<PanelState>({
    showNodeLibrary: true,
    showProperties: false,
    showRunner: false,
    showDebugPanel: true,
    leftPanelWidth: 300,
    rightPanelWidth: 400,
    debugPanelWidth: 450
  })

  // 执行状态
  const executionState = ref<ExecutionState>({
    isRunning: false,
    isPaused: false,
    currentNodeId: null,
    executionProgress: 0,
    executionStats: null,
    executionSteps: []
  })

  // 节点执行状态映射
  const nodeExecutionStates = ref<Map<string, any>>(new Map())
  
  // 连接线动画状态映射
  const edgeAnimationStates = ref<Map<string, any>>(new Map())

  // ===== 计算属性 =====
  const hasNodes = computed(() => nodes.value.length > 0)
  
  const currentWorkflowConfig = computed(() => ({
    ...workflowConfig.value,
    nodes: nodes.value,
    edges: edges.value,
    viewport: viewport.value
  }))

  // ===== 基础操作方法 =====
  
  /**
   * 初始化工作流数据
   */
  const initializeWorkflow = async (id: string) => {
    try {
      agentId.value = id
      
      // 加载工作流配置
      const workflowResponse = await loadAgentWorkflow(id)
      if (workflowResponse.success) {
        const config = workflowResponse.data
        workflowConfig.value = config
        nodes.value = config.nodes || []
        edges.value = config.edges || []
        viewport.value = config.viewport || { x: 0, y: 0, zoom: 1 }
      }
    } catch (error) {
      console.error('初始化工作流失败:', error)
      throw error
    }
  }

  /**
   * 保存工作流
   */
  const saveWorkflow = async (operationType: string, operationDesc: string) => {
    try {
      await realtimeSaveWorkflow({
        agentId: agentId.value,
        nodesData: JSON.stringify(nodes.value),
        edgesData: JSON.stringify(edges.value),
        viewportConfig: JSON.stringify(viewport.value),
        nodeLibrary: JSON.stringify(workflowConfig.value.nodeLibrary),
        globalVariables: JSON.stringify(workflowConfig.value.globalVariables),
        operationType,
        operationDesc
      })
      
      workflowConfig.value.lastModified = Date.now()
    } catch (error) {
      console.error('保存工作流失败:', error)
      throw error
    }
  }

  // ===== 节点操作方法 =====
  
  /**
   * 添加节点
   */
  const addNode = (node: Node) => {
    nodes.value.push(node)
    saveWorkflow('add_node', `添加节点: ${node.data?.label || node.type}`)
  }

  /**
   * 更新节点
   */
  const updateNode = (nodeId: string, updates: Partial<Node>) => {
    const nodeIndex = nodes.value.findIndex(n => n.id === nodeId)
    if (nodeIndex !== -1) {
      nodes.value[nodeIndex] = { ...nodes.value[nodeIndex], ...updates }
      saveWorkflow('node_update', `更新节点: ${nodeId}`)
    }
  }

  /**
   * 删除节点
   */
  const deleteNode = (nodeId: string) => {
    const nodeIndex = nodes.value.findIndex(n => n.id === nodeId)
    if (nodeIndex !== -1) {
      nodes.value.splice(nodeIndex, 1)
      
      // 删除与该节点相关的所有连接线
      edges.value = edges.value.filter(edge =>
        edge.source !== nodeId && edge.target !== nodeId
      )
      
      // 清除选中状态
      if (selectionState.value.selectedNode?.id === nodeId) {
        selectionState.value.selectedNode = null
      }
      
      saveWorkflow('delete_node', `删除节点: ${nodeId}`)
    }
  }

  // ===== 连接线操作方法 =====
  
  /**
   * 添加连接线
   */
  const addEdge = (edge: Edge) => {
    // 检查是否已存在相同的连接
    const existingEdge = edges.value.find(e =>
      e.source === edge.source &&
      e.target === edge.target &&
      e.sourceHandle === edge.sourceHandle &&
      e.targetHandle === edge.targetHandle
    )
    
    if (!existingEdge) {
      edges.value.push(edge)
      saveWorkflow('add_edge', `添加连接线: ${edge.source} -> ${edge.target}`)
    }
  }

  /**
   * 更新连接线
   */
  const updateEdge = (edgeId: string, updates: Partial<Edge>) => {
    const edgeIndex = edges.value.findIndex(e => e.id === edgeId)
    if (edgeIndex !== -1) {
      edges.value[edgeIndex] = { ...edges.value[edgeIndex], ...updates }
      saveWorkflow('edge_update', `更新连接线: ${edgeId}`)
    }
  }

  /**
   * 删除连接线
   */
  const deleteEdge = (edgeId: string) => {
    const edgeIndex = edges.value.findIndex(e => e.id === edgeId)
    if (edgeIndex !== -1) {
      edges.value.splice(edgeIndex, 1)
      
      // 清除选中状态
      if (selectionState.value.selectedEdge?.id === edgeId) {
        selectionState.value.selectedEdge = null
      }
      
      saveWorkflow('delete_edge', `删除连接线: ${edgeId}`)
    }
  }

  // ===== 选择状态管理 =====
  
  /**
   * 选择节点
   */
  const selectNode = (node: Node | null) => {
    selectionState.value.selectedNode = node
    selectionState.value.selectedEdge = null
    panelState.value.showProperties = !!node
  }

  /**
   * 选择连接线
   */
  const selectEdge = (edge: Edge | null) => {
    selectionState.value.selectedEdge = edge
    selectionState.value.selectedNode = null
    panelState.value.showProperties = !!edge
  }

  /**
   * 清除选择
   */
  const clearSelection = () => {
    selectionState.value.selectedNode = null
    selectionState.value.selectedEdge = null
    panelState.value.showProperties = false
  }

  // ===== 面板状态管理 =====
  
  /**
   * 切换节点库面板
   */
  const toggleNodeLibrary = () => {
    panelState.value.showNodeLibrary = !panelState.value.showNodeLibrary
  }

  /**
   * 切换属性面板
   */
  const toggleProperties = () => {
    panelState.value.showProperties = !panelState.value.showProperties
  }

  /**
   * 切换运行器面板
   */
  const toggleRunner = () => {
    panelState.value.showRunner = !panelState.value.showRunner
    if (panelState.value.showRunner) {
      panelState.value.showDebugPanel = true
    }
  }

  /**
   * 切换调试面板
   */
  const toggleDebugPanel = () => {
    panelState.value.showDebugPanel = !panelState.value.showDebugPanel
  }

  /**
   * 设置面板宽度
   */
  const setPanelWidth = (panel: 'left' | 'right' | 'debug', width: number) => {
    switch (panel) {
      case 'left':
        panelState.value.leftPanelWidth = width
        break
      case 'right':
        panelState.value.rightPanelWidth = width
        break
      case 'debug':
        panelState.value.debugPanelWidth = width
        break
    }
  }

  // ===== 执行状态管理 =====

  /**
   * 更新节点执行状态
   */
  const updateNodeExecutionState = (nodeId: string, status: string, progress: number, info: string) => {
    const nodeIndex = nodes.value.findIndex(n => n.id === nodeId)
    if (nodeIndex !== -1) {
      nodes.value[nodeIndex] = {
        ...nodes.value[nodeIndex],
        data: {
          ...nodes.value[nodeIndex].data,
          executionStatus: status,
          executionProgress: progress,
          executionInfo: info
        }
      }
    }

    // 更新执行状态映射
    nodeExecutionStates.value.set(nodeId, {
      status,
      progress,
      info
    })
  }

  /**
   * 更新连接线动画状态
   */
  const updateEdgeAnimationState = (edgeId: string, isFlowing: boolean, data: any) => {
    const edgeIndex = edges.value.findIndex(e => e.id === edgeId)
    if (edgeIndex !== -1) {
      edges.value[edgeIndex] = {
        ...edges.value[edgeIndex],
        animated: isFlowing,
        data: {
          ...edges.value[edgeIndex].data,
          isFlowing,
          dataPacket: data ? { label: 'Data', data } : null
        },
        style: {
          ...edges.value[edgeIndex].style,
          stroke: isFlowing ? '#3b82f6' : '#b1b1b7',
          strokeWidth: isFlowing ? 3 : 2
        }
      }
    }

    // 更新动画状态映射
    edgeAnimationStates.value.set(edgeId, {
      isFlowing,
      dataPacket: data ? { label: 'Data', data } : null
    })
  }

  /**
   * 开始执行
   */
  const startExecution = () => {
    executionState.value.isRunning = true
    executionState.value.isPaused = false
    executionState.value.executionProgress = 0
    executionState.value.executionSteps = []

    // 清除之前的执行状态
    nodeExecutionStates.value.clear()
    edgeAnimationStates.value.clear()

    // 重置所有节点的执行状态
    nodes.value.forEach(node => {
      updateNodeExecutionState(node.id, 'waiting', 0, '等待执行')
    })

    // 重置所有连接线的动画状态
    edges.value.forEach(edge => {
      updateEdgeAnimationState(edge.id, false, null)
    })
  }

  /**
   * 完成执行
   */
  const completeExecution = (stats: any) => {
    executionState.value.isRunning = false
    executionState.value.isPaused = false
    executionState.value.executionProgress = 100
    executionState.value.executionStats = stats
    executionState.value.currentNodeId = null
  }

  /**
   * 取消执行
   */
  const cancelExecution = () => {
    executionState.value.isRunning = false
    executionState.value.isPaused = false
    executionState.value.executionProgress = 0
    executionState.value.currentNodeId = null

    // 清除执行状态
    nodeExecutionStates.value.clear()
    edgeAnimationStates.value.clear()

    // 重置所有节点状态为idle
    nodes.value.forEach(node => {
      updateNodeExecutionState(node.id, 'idle', 0, '')
    })

    // 重置所有连接线动画
    edges.value.forEach(edge => {
      updateEdgeAnimationState(edge.id, false, null)
    })
  }

  /**
   * 清空画布
   */
  const clearCanvas = () => {
    if (confirm('确定要清空画布吗？此操作不可撤销。')) {
      nodes.value = []
      edges.value = []
      clearSelection()
      saveWorkflow('clear_canvas', '清空画布')
    }
  }

  /**
   * 更新全局变量
   */
  const updateGlobalVariables = (variables: Record<string, any>) => {
    workflowConfig.value.globalVariables = { ...variables }
    saveWorkflow('update_global_variables', '更新全局变量')
  }

  /**
   * 更新工作流设置
   */
  const updateWorkflowSettings = (settings: Partial<WorkflowConfig>) => {
    workflowConfig.value = { ...workflowConfig.value, ...settings }
    saveWorkflow('settings_update', '更新设置')
  }

  // ===== 状态同步机制 =====

  /**
   * 自动保存机制 - 监听关键状态变化
   */
  let autoSaveTimer: number | null = null
  const enableAutoSave = () => {
    // 监听nodes变化
    watch(nodes, () => {
      if (autoSaveTimer) clearTimeout(autoSaveTimer)
      autoSaveTimer = window.setTimeout(() => {
        saveWorkflow('auto_save_nodes', '自动保存节点变化')
      }, 2000) // 2秒后自动保存
    }, { deep: true })

    // 监听edges变化
    watch(edges, () => {
      if (autoSaveTimer) clearTimeout(autoSaveTimer)
      autoSaveTimer = window.setTimeout(() => {
        saveWorkflow('auto_save_edges', '自动保存连接线变化')
      }, 2000) // 2秒后自动保存
    }, { deep: true })

    // 监听viewport变化
    watch(viewport, () => {
      if (autoSaveTimer) clearTimeout(autoSaveTimer)
      autoSaveTimer = window.setTimeout(() => {
        saveWorkflow('auto_save_viewport', '自动保存视口变化')
      }, 5000) // 5秒后自动保存视口变化
    }, { deep: true })
  }

  /**
   * 同步workflowConfig中的数据
   */
  const syncWorkflowConfig = () => {
    workflowConfig.value.nodes = nodes.value as any
    workflowConfig.value.edges = edges.value as any
    workflowConfig.value.viewport = viewport.value
    workflowConfig.value.lastModified = Date.now()
  }

  // 监听核心数据变化，自动同步到workflowConfig
  watch([nodes, edges, viewport], () => {
    syncWorkflowConfig()
  }, { deep: true })

  // ===== 工具方法 =====

  /**
   * 获取节点执行状态
   */
  const getNodeExecutionStatus = (nodeId: string): string => {
    const node = nodes.value.find(n => n.id === nodeId)
    return node?.data?.executionStatus || 'idle'
  }

  /**
   * 获取节点执行进度
   */
  const getNodeExecutionProgress = (nodeId: string): number => {
    const node = nodes.value.find(n => n.id === nodeId)
    return node?.data?.executionProgress || 0
  }

  /**
   * 获取节点执行信息
   */
  const getNodeExecutionInfo = (nodeId: string): string => {
    const node = nodes.value.find(n => n.id === nodeId)
    return node?.data?.executionInfo || ''
  }

  /**
   * 获取连接线动画状态
   */
  const getEdgeAnimated = (edgeId: string): boolean => {
    const edge = edges.value.find(e => e.id === edgeId)
    return edge?.animated || false
  }

  /**
   * 获取连接线流动状态
   */
  const getEdgeFlowing = (edgeId: string): boolean => {
    const edge = edges.value.find(e => e.id === edgeId)
    return edge?.data?.isFlowing || false
  }

  /**
   * 获取连接线数据包
   */
  const getEdgeDataPacket = (edgeId: string): any => {
    const edge = edges.value.find(e => e.id === edgeId)
    return edge?.data?.dataPacket || null
  }

  // ===== 数据验证和一致性检查 =====

  /**
   * 验证数据一致性
   */
  const validateDataConsistency = (): { valid: boolean; errors: string[] } => {
    const errors: string[] = []

    // 检查连接线是否引用了不存在的节点
    edges.value.forEach(edge => {
      const sourceExists = nodes.value.some(n => n.id === edge.source)
      const targetExists = nodes.value.some(n => n.id === edge.target)

      if (!sourceExists) {
        errors.push(`连接线 ${edge.id} 引用了不存在的源节点: ${edge.source}`)
      }
      if (!targetExists) {
        errors.push(`连接线 ${edge.id} 引用了不存在的目标节点: ${edge.target}`)
      }
    })

    // 检查节点ID是否唯一
    const nodeIds = nodes.value.map(n => n.id)
    const duplicateIds = nodeIds.filter((id, index) => nodeIds.indexOf(id) !== index)
    if (duplicateIds.length > 0) {
      errors.push(`发现重复的节点ID: ${duplicateIds.join(', ')}`)
    }

    // 检查连接线ID是否唯一
    const edgeIds = edges.value.map(e => e.id)
    const duplicateEdgeIds = edgeIds.filter((id, index) => edgeIds.indexOf(id) !== index)
    if (duplicateEdgeIds.length > 0) {
      errors.push(`发现重复的连接线ID: ${duplicateEdgeIds.join(', ')}`)
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 清理无效的连接线
   */
  const cleanupInvalidEdges = () => {
    const validEdges = edges.value.filter(edge => {
      const sourceExists = nodes.value.some(n => n.id === edge.source)
      const targetExists = nodes.value.some(n => n.id === edge.target)
      return sourceExists && targetExists
    })

    if (validEdges.length !== edges.value.length) {
      edges.value = validEdges
      console.log(`清理了 ${edges.value.length - validEdges.length} 条无效连接线`)
    }
  }

  // 启用自动保存机制
  enableAutoSave()

  return {
    // 状态
    agentId,
    agentInfo,
    workflowConfig,
    nodes,
    edges,
    viewport,
    selectionState,
    panelState,
    executionState,
    nodeExecutionStates,
    edgeAnimationStates,

    // 计算属性
    hasNodes,
    currentWorkflowConfig,

    // 基础方法
    initializeWorkflow,
    saveWorkflow,
    addNode,
    updateNode,
    deleteNode,
    addEdge,
    updateEdge,
    deleteEdge,
    selectNode,
    selectEdge,
    clearSelection,
    toggleNodeLibrary,
    toggleProperties,
    toggleRunner,
    toggleDebugPanel,
    setPanelWidth,

    // 执行状态管理
    updateNodeExecutionState,
    updateEdgeAnimationState,
    startExecution,
    completeExecution,
    cancelExecution,

    // 工具方法
    clearCanvas,
    updateGlobalVariables,
    updateWorkflowSettings,
    getNodeExecutionStatus,
    getNodeExecutionProgress,
    getNodeExecutionInfo,
    getEdgeAnimated,
    getEdgeFlowing,
    getEdgeDataPacket,

    // 状态同步和验证
    enableAutoSave,
    syncWorkflowConfig,
    validateDataConsistency,
    cleanupInvalidEdges
  }
})
