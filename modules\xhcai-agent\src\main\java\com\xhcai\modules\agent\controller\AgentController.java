package com.xhcai.modules.agent.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.agent.dto.AgentCreateDTO;
import com.xhcai.modules.agent.dto.AgentQueryDTO;
import com.xhcai.modules.agent.dto.AgentUpdateDTO;
import com.xhcai.modules.agent.dto.ThirdPartyAgentCreateDTO;
import com.xhcai.modules.agent.mapper.AgentMapper.AgentStatsVO;
import com.xhcai.modules.agent.service.IAgentService;
import com.xhcai.modules.agent.vo.AgentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能体控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "智能体管理", description = "智能体的创建、配置、管理等功能")
@RestController
@RequestMapping("/api/agent")
public class AgentController {

    @Autowired
    private IAgentService agentService;

    @Operation(summary = "分页查询智能体列表", description = "根据条件分页查询智能体列表")
    @PostMapping("/page")
    @RequiresPermissions("agent:list")
    public Result<PageResult<AgentVO>> getAgentPage(@Valid @RequestBody AgentQueryDTO queryDTO) {
        PageResult<AgentVO> result = agentService.getAgentPage(queryDTO);
        return Result.success(result);
    }

    @Operation(summary = "查询智能体详情", description = "根据ID查询智能体详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("agent:detail")
    public Result<AgentVO> getAgentById(@Parameter(description = "智能体ID") @PathVariable String id) {
        AgentVO agent = agentService.getAgentById(id);
        return Result.success(agent);
    }

    @Operation(summary = "创建智能体", description = "创建新的智能体")
    @PostMapping
    @RequiresPermissions("agent:create")
    public Result<String> createAgent(@Valid @RequestBody AgentCreateDTO createDTO) {
        String agentId = agentService.createAgent(createDTO);
        return Result.success(agentId, "创建智能体成功");
    }

    @Operation(summary = "更新智能体", description = "更新智能体信息")
    @PutMapping
    @RequiresPermissions("agent:update")
    public Result<Void> updateAgent(@Valid @RequestBody AgentUpdateDTO updateDTO) {
        boolean success = agentService.updateAgent(updateDTO);
        return success ? Result.success("更新智能体成功") : Result.fail("更新智能体失败");
    }

    @Operation(summary = "删除智能体", description = "根据ID删除智能体")
    @DeleteMapping("/{id}")
    @RequiresPermissions("agent:delete")
    public Result<Void> deleteAgent(@Parameter(description = "智能体ID") @PathVariable String id) {
        boolean success = agentService.deleteAgent(id);
        return success ? Result.success("删除智能体成功") : Result.fail("删除智能体失败");
    }

    @Operation(summary = "批量删除智能体", description = "根据ID列表批量删除智能体")
    @DeleteMapping("/batch")
    @RequiresPermissions("agent:delete")
    public Result<Void> batchDeleteAgents(@RequestBody List<String> ids) {
        boolean success = agentService.batchDeleteAgents(ids);
        return success ? Result.success("批量删除智能体成功") : Result.fail("批量删除智能体失败");
    }

    @Operation(summary = "启用智能体", description = "启用指定的智能体")
    @PutMapping("/{id}/enable")
    @RequiresPermissions("agent:update")
    public Result<Void> enableAgent(@Parameter(description = "智能体ID") @PathVariable String id) {
        boolean success = agentService.enableAgent(id);
        return success ? Result.success("启用智能体成功") : Result.fail("启用智能体失败");
    }

    @Operation(summary = "禁用智能体", description = "禁用指定的智能体")
    @PutMapping("/{id}/disable")
    @RequiresPermissions("agent:update")
    public Result<Void> disableAgent(@Parameter(description = "智能体ID") @PathVariable String id) {
        boolean success = agentService.disableAgent(id);
        return success ? Result.success("禁用智能体成功") : Result.fail("禁用智能体失败");
    }

    @Operation(summary = "批量更新智能体状态", description = "批量更新智能体的启用/禁用状态")
    @PutMapping("/batch/status")
    @RequiresPermissions("agent:update")
    public Result<Void> batchUpdateStatus(@RequestBody BatchUpdateStatusRequest request) {
        boolean success = agentService.batchUpdateStatus(request.getIds(), request.getStatus());
        return success ? Result.success("批量更新状态成功") : Result.fail("批量更新状态失败");
    }

    @Operation(summary = "复制智能体", description = "复制现有智能体创建新的智能体")
    @PostMapping("/{id}/copy")
    @RequiresPermissions("agent:create")
    public Result<String> copyAgent(@Parameter(description = "智能体ID") @PathVariable String id,
            @RequestBody CopyAgentRequest request) {
        String newAgentId = agentService.copyAgent(id, request.getName());
        return Result.success(newAgentId, "复制智能体成功");
    }

    @Operation(summary = "发布智能体", description = "发布智能体并设置版本号")
    @PutMapping("/{id}/publish")
    @RequiresPermissions("agent:publish")
    public Result<Void> publishAgent(@Parameter(description = "智能体ID") @PathVariable String id,
            @RequestBody PublishAgentRequest request) {
        boolean success = agentService.publishAgent(id, request.getVersion());
        return success ? Result.success("发布智能体成功") : Result.fail("发布智能体失败");
    }

    @Operation(summary = "设置智能体公开状态", description = "设置智能体是否公开")
    @PutMapping("/{id}/public")
    @RequiresPermissions("agent:update")
    public Result<Void> setAgentPublic(@Parameter(description = "智能体ID") @PathVariable String id,
            @RequestBody SetPublicRequest request) {
        boolean success = agentService.setAgentPublic(id, request.getIsPublic());
        return success ? Result.success("设置公开状态成功") : Result.fail("设置公开状态失败");
    }

    @Operation(summary = "查询用户的智能体列表", description = "查询当前用户创建的智能体列表")
    @GetMapping("/my")
    @RequiresPermissions("agent:list")
    public Result<List<AgentVO>> getUserAgents() {
        List<AgentVO> agents = agentService.getUserAgents(null); // null表示当前用户
        return Result.success(agents);
    }

    @Operation(summary = "查询公开的智能体列表", description = "查询所有公开的智能体列表")
    @GetMapping("/public")
    public Result<List<AgentVO>> getPublicAgents() {
        List<AgentVO> agents = agentService.getPublicAgents();
        return Result.success(agents);
    }

    @Operation(summary = "检查智能体名称是否存在", description = "检查智能体名称是否已被使用")
    @GetMapping("/check-name")
    @RequiresPermissions("agent:create")
    public Result<Boolean> checkNameExists(@Parameter(description = "智能体名称") @RequestParam String name,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String excludeId) {
        boolean exists = agentService.checkNameExists(name, excludeId);
        return Result.success(exists);
    }

    @Operation(summary = "获取智能体统计信息", description = "获取智能体的各种统计数据")
    @GetMapping("/stats")
    @RequiresPermissions("agent:stats")
    public Result<AgentStatsVO> getAgentStats() {
        AgentStatsVO stats = agentService.getAgentStats();
        return Result.success(stats);
    }

    @Operation(summary = "获取已关联的智能体列表", description = "获取已关联的外部智能体列表，用于关联时过滤")
    @GetMapping("/associated")
    @RequiresPermissions("agent:list")
    public Result<List<AgentVO>> getAssociatedAgents(
            @Parameter(description = "平台ID") @RequestParam(required = false) String platformId) {
        List<AgentVO> agents = agentService.getAssociatedAgents(platformId);
        return Result.success(agents);
    }

    @Operation(summary = "获取AI探索智能体列表", description = "获取type为advanced-chat的智能体列表，用于AI探索页面")
    @GetMapping("/ai-explore")
    public Result<List<AgentVO>> getAiExploreAgents() {
        List<AgentVO> agents = agentService.getAiExploreAgents();
        return Result.success(agents);
    }

    @Operation(summary = "导出智能体配置", description = "导出智能体的配置信息")
    @GetMapping("/{id}/export")
    @RequiresPermissions("agent:export")
    public Result<String> exportAgentConfig(@Parameter(description = "智能体ID") @PathVariable String id) {
        String config = agentService.exportAgentConfig(id);
        return Result.success(config);
    }

    @Operation(summary = "导入智能体配置", description = "从配置信息导入创建智能体")
    @PostMapping("/import")
    @RequiresPermissions("agent:import")
    public Result<String> importAgentConfig(@RequestBody ImportAgentRequest request) {
        String agentId = agentService.importAgentConfig(request.getConfigJson(), request.getName());
        return Result.success(agentId, "导入智能体成功");
    }

    @Operation(summary = "批量关联智能体到项目", description = "将多个智能体关联到指定项目")
    @PostMapping("/link-to-project")
    @RequiresPermissions("agent:link")
    public Result<Boolean> linkAgentsToProject(@Valid @RequestBody LinkAgentsToProjectRequest request) {
        boolean success = agentService.linkAgentsToProject(request.getAgentIds(), request.getProjectId());
        return Result.success(success);
    }

    @Operation(summary = "获取未关联项目的智能体列表", description = "获取所有未关联到任何项目的智能体")
    @PostMapping("/unlinked")
    @RequiresPermissions("agent:list")
    public Result<PageResult<AgentVO>> getUnlinkedAgents(@Valid @RequestBody AgentQueryDTO queryDTO) {
        PageResult<AgentVO> result = agentService.getUnlinkedAgents(queryDTO);
        return Result.success(result);
    }

    @Operation(summary = "创建第三方智能体", description = "调用Dify平台创建智能体并保存到本地")
    @PostMapping("/third-party")
    @RequiresPermissions("agent:create")
    public Result<String> createThirdPartyAgent(@Valid @RequestBody ThirdPartyAgentCreateDTO createDTO) {
        String agentId = agentService.createThirdPartyAgent(createDTO);
        return Result.success(agentId, "创建第三方智能体成功");
    }

    // 内部类定义请求对象
    public static class BatchUpdateStatusRequest {

        private List<String> ids;
        private String status;

        public List<String> getIds() {
            return ids;
        }

        public void setIds(List<String> ids) {
            this.ids = ids;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }

    public static class CopyAgentRequest {

        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class PublishAgentRequest {

        private String version;

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }

    public static class SetPublicRequest {

        private Boolean isPublic;

        public Boolean getIsPublic() {
            return isPublic;
        }

        public void setIsPublic(Boolean isPublic) {
            this.isPublic = isPublic;
        }
    }

    public static class ImportAgentRequest {

        private String configJson;
        private String name;

        public String getConfigJson() {
            return configJson;
        }

        public void setConfigJson(String configJson) {
            this.configJson = configJson;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class LinkAgentsToProjectRequest {

        private List<String> agentIds;
        private String projectId;

        public List<String> getAgentIds() {
            return agentIds;
        }

        public void setAgentIds(List<String> agentIds) {
            this.agentIds = agentIds;
        }

        public String getProjectId() {
            return projectId;
        }

        public void setProjectId(String projectId) {
            this.projectId = projectId;
        }
    }
}
