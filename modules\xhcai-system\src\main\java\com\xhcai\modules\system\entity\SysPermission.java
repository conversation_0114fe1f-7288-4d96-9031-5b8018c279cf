package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.annotation.NoTenant;
import com.xhcai.common.datasource.entity.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 权限信息实体类
 *
 * <p>
 * 权限信息是系统级别的配置，全局共享，不需要租户隔离</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_permission")
@Schema(description = "权限信息")
@TableName("sys_permission")
@NoTenant(reason = "权限信息是系统级别的配置，全局共享")
public class SysPermission extends BaseEntity {

    // id字段由BaseEntityWithoutTenant提供
    /**
     * 权限编码
     */
    @Schema(description = "权限编码", example = "system:user:list")
    @NotBlank(message = "权限编码不能为空")
    @TableField("permission_code")
    private String permissionCode;

    /**
     * 权限名称
     */
    @Schema(description = "权限名称", example = "用户查询")
    @NotBlank(message = "权限名称不能为空")
    @TableField("permission_name")
    private String permissionName;

    /**
     * 权限类型
     */
    @Schema(description = "权限类型", example = "1", allowableValues = {"1", "2", "3"})
    @Pattern(regexp = "^[123]$", message = "权限类型值必须为1-3")
    @TableField("permission_type")
    private String permissionType;

    /**
     * 父权限ID
     */
    @Schema(description = "父权限ID", example = "0")
    @TableField("parent_id")
    private String parentId;

    /**
     * 权限路径
     */
    @Schema(description = "权限路径", example = "/system/user")
    @TableField("permission_path")
    private String permissionPath;

    /**
     * 权限图标
     */
    @Schema(description = "权限图标", example = "user")
    @TableField("icon")
    private String icon;

    /**
     * 组件路径
     */
    @Schema(description = "组件路径", example = "system/user/index")
    @TableField("component")
    private String component;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序", example = "1")
    @TableField("order_num")
    private Integer orderNum;

    /**
     * 是否外链
     */
    @Schema(description = "是否外链")
    @TableField("is_frame")
    private Boolean isFrame;

    /**
     * 是否缓存
     */
    @Schema(description = "是否缓存")
    @TableField("is_cache")
    private Boolean isCache;

    /**
     * 是否可见
     */
    @Schema(description = "是否可见")
    @TableField("visible")
    private Boolean visible;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    // 审计字段由BaseEntityWithoutTenant提供
    // Getters and Setters - 只保留业务字段的getter/setter
    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getPermissionPath() {
        return permissionPath;
    }

    public void setPermissionPath(String permissionPath) {
        this.permissionPath = permissionPath;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Boolean getIsFrame() {
        return isFrame;
    }

    public void setIsFrame(Boolean isFrame) {
        this.isFrame = isFrame;
    }

    public Boolean getIsCache() {
        return isCache;
    }

    public void setIsCache(Boolean isCache) {
        this.isCache = isCache;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SysPermission{"
                + "permissionCode='" + permissionCode + '\''
                + ", permissionName='" + permissionName + '\''
                + ", permissionType='" + permissionType + '\''
                + ", parentId=" + parentId
                + ", permissionPath='" + permissionPath + '\''
                + ", icon='" + icon + '\''
                + ", component='" + component + '\''
                + ", orderNum=" + orderNum
                + ", isFrame=" + isFrame
                + ", isCache=" + isCache
                + ", visible=" + visible
                + ", status='" + status + '\''
                + ", " + super.toString()
                + '}';
    }
}
