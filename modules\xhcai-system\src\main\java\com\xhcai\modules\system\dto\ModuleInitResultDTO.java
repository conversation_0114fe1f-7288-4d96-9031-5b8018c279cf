package com.xhcai.modules.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 模块初始化结果DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "模块初始化结果")
public class ModuleInitResultDTO {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "总体状态：RUNNING-运行中，SUCCESS-成功，FAILED-失败，PARTIAL-部分成功")
    private String status;

    @Schema(description = "总进度百分比(0-100)")
    private Integer totalProgress;

    @Schema(description = "成功初始化的模块数量")
    private Integer successCount;

    @Schema(description = "失败的模块数量")
    private Integer failedCount;

    @Schema(description = "总模块数量")
    private Integer totalCount;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "耗时(毫秒)")
    private Long duration;

    @Schema(description = "各模块初始化结果")
    private List<ModuleInitItemResult> moduleResults;

    @Schema(description = "总体消息")
    private String message;

    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 模块初始化项结果
     */
    @Schema(description = "模块初始化项结果")
    public static class ModuleInitItemResult {
        @Schema(description = "模块ID")
        private String moduleId;

        @Schema(description = "模块名称")
        private String moduleName;

        @Schema(description = "状态：SUCCESS-成功，FAILED-失败，SKIPPED-跳过")
        private String status;

        @Schema(description = "进度百分比(0-100)")
        private Integer progress;

        @Schema(description = "消息")
        private String message;

        @Schema(description = "错误信息")
        private String errorMessage;

        @Schema(description = "开始时间")
        private LocalDateTime startTime;

        @Schema(description = "结束时间")
        private LocalDateTime endTime;

        @Schema(description = "耗时(毫秒)")
        private Long duration;

        @Schema(description = "初始化详情")
        private Map<String, Object> details;

        public String getModuleId() {
            return moduleId;
        }

        public void setModuleId(String moduleId) {
            this.moduleId = moduleId;
        }

        public String getModuleName() {
            return moduleName;
        }

        public void setModuleName(String moduleName) {
            this.moduleName = moduleName;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Integer getProgress() {
            return progress;
        }

        public void setProgress(Integer progress) {
            this.progress = progress;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public void setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
        }

        public Long getDuration() {
            return duration;
        }

        public void setDuration(Long duration) {
            this.duration = duration;
        }

        public Map<String, Object> getDetails() {
            return details;
        }

        public void setDetails(Map<String, Object> details) {
            this.details = details;
        }
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotalProgress() {
        return totalProgress;
    }

    public void setTotalProgress(Integer totalProgress) {
        this.totalProgress = totalProgress;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(Integer failedCount) {
        this.failedCount = failedCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public List<ModuleInitItemResult> getModuleResults() {
        return moduleResults;
    }

    public void setModuleResults(List<ModuleInitItemResult> moduleResults) {
        this.moduleResults = moduleResults;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
