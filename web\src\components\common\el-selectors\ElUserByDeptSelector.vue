<template>
  <div class="el-user-by-dept-selector">
    <el-card class="selector-card" shadow="never">
      <!-- 头部信息 -->
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon><UserFilled /></el-icon>
            <span>按部门选择用户</span>
            <el-tag type="info" size="small">
              已选择 {{ selectedCount }} 个用户
            </el-tag>
          </div>
          <div class="header-actions">
            <el-button 
              v-if="config.multiple" 
              @click="selectAllCurrentDeptUsers" 
              size="small" 
              type="primary" 
              plain
              :disabled="loading || !currentDeptUsers.length"
            >
              全选当前部门
            </el-button>
            <el-button 
              @click="clearSelection" 
              size="small" 
              :disabled="!hasSelection"
            >
              清空
            </el-button>
          </div>
        </div>
      </template>
      <!-- 已选择用户标签 -->
      <div v-if="hasSelection" class="selected-users">
        <div class="user-tags">
          <el-tag
            v-for="user in selectedUserOptions"
            :key="user.value"
            :closable="!config.disabled"
            :size="config.size"
            @close="handleUserRemove(user.value)"
          >
            <el-avatar :size="16" class="tag-avatar">
              <img v-if="user.avatar" :src="user.avatar" :alt="user.label" />
              <el-icon v-else><User /></el-icon>
            </el-avatar>
            {{ user.label }}
          </el-tag>
        </div>
      </div>
      <div class="selector-content">
        <el-row :gutter="16">
          <!-- 左侧：部门选择 -->
          <el-col :span="8">
            <div class="dept-panel">
              <div class="panel-title">
                <el-icon><OfficeBuilding /></el-icon>
                <span>选择部门</span>
                <el-tag v-if="selectedDept" type="success" size="small">
                  {{ selectedDept.label }}
                </el-tag>
              </div>
              
              <!-- 部门搜索 -->
              <el-input
                v-model="deptFilterText"
                placeholder="搜索部门..."
                :prefix-icon="Search"
                clearable
                size="small"
                :disabled="deptLoading"
                class="dept-search"
              />

              <!-- 部门树 -->
              <div class="dept-tree">
                <el-scrollbar height="400px">
                  <el-skeleton v-if="deptLoading" :rows="5" animated />
                  
                  <el-empty v-else-if="!filteredDepts.length" 
                    :description="deptFilterText ? '未找到匹配的部门' : '暂无部门数据'"
                    :image-size="80"
                  />
                  
                  <el-tree
                    v-else
                    ref="deptTreeRef"
                    :data="filteredDepts"
                    :props="deptTreeProps"
                    :filter-node-method="filterDeptNode"
                    :highlight-current="true"
                    :expand-on-click-node="false"
                    :default-expand-all="false"
                    node-key="value"
                    @node-click="handleDeptClick"
                  >
                    <template #default="{ node, data }">
                      <div class="dept-node">
                        <el-icon class="dept-icon" :color="getDeptIconColor(data)">
                          <component :is="getDeptIcon(data)" />
                        </el-icon>
                        <span class="dept-label">{{ data.label }}</span>
                        <span v-if="data.userCount !== undefined" class="user-count">
                          {{ data.userCount }}人
                        </span>
                      </div>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </div>
            </div>
          </el-col>

          <!-- 右侧：用户选择 -->
          <el-col :span="16">
            <div class="user-panel">
              <div class="panel-title">
                <el-icon><User /></el-icon>
                <span>选择用户</span>
                <el-tag v-if="currentDeptUsers.length" type="info" size="small">
                  {{ currentDeptUsers.length }} 个可选用户
                </el-tag>
              </div>

              <!-- 用户搜索 -->
              <el-input
                v-model="userFilterText"
                placeholder="搜索用户姓名、用户名..."
                :prefix-icon="Search"
                clearable
                size="small"
                :disabled="userLoading || !selectedDeptId"
                class="user-search"
              />

              <!-- 用户列表 -->
              <div class="user-list">
                <el-scrollbar height="400px">
                  <el-skeleton v-if="userLoading" :rows="8" animated />
                  
                  <el-empty v-else-if="!selectedDeptId" 
                    description="请先选择部门"
                    :image-size="80"
                  />
                  
                  <el-empty v-else-if="!filteredUsers.length" 
                    :description="userFilterText ? '未找到匹配的用户' : '该部门暂无用户'"
                    :image-size="80"
                  />
                  
                  <div v-else class="user-items">
                    <div
                      v-for="user in filteredUsers"
                      :key="user.value"
                      class="user-item"
                      :class="{
                        'is-selected': isUserSelected(user.value),
                        'is-disabled': user.disabled
                      }"
                    >
                      <!-- 选择指示器 -->
                      <div class="user-selector">
                        <!-- 自定义选择指示器 -->
                        <div
                          class="custom-user-selector"
                          :class="{
                            'selected': isUserSelected(user.value),
                            'disabled': user.disabled,
                            'multiple': config.multiple,
                            'single': !config.multiple
                          }"
                          @click="!user.disabled && handleUserClick(user)"
                        >
                          <!-- 选择指示器 -->
                          <div class="selector-indicator">
                            <!-- 多选模式：复选框样式 -->
                            <div v-if="config.multiple" class="custom-checkbox">
                              <div class="checkbox-inner">
                                <i v-if="isUserSelected(user.value)" class="fas fa-check"></i>
                              </div>
                            </div>
                            <!-- 单选模式：单选框样式 -->
                            <div v-else class="custom-radio">
                              <div class="radio-inner">
                                <div v-if="isUserSelected(user.value)" class="radio-dot"></div>
                              </div>
                            </div>
                          </div>

                          <!-- 用户信息内容 -->
                          <div class="flex items-center flex-1">
                            <!-- 用户头像 -->
                            <el-avatar :size="40" class="user-avatar">
                              <img v-if="user.avatar" :src="user.avatar" :alt="user.label" />
                              <el-icon v-else><User /></el-icon>
                            </el-avatar>

                            <!-- 用户信息 -->
                            <div class="user-info">
                              <div class="user-name">{{ user.label }}</div>
                              <div class="user-details">
                                <span v-if="user.username" class="username">{{ user.username }}</span>
                                <span v-if="user.email" class="email">{{ user.email }}</span>
                                <span v-if="user.phone" class="phone">{{ user.phone }}</span>
                              </div>
                              <div v-if="user.roleNames?.length" class="user-roles">
                                <el-tag
                                  v-for="roleName in user.roleNames.slice(0, 2)"
                                  :key="roleName"
                                  type="info"
                                  size="small"
                                  effect="plain"
                                >
                                  {{ roleName }}
                                </el-tag>
                                <el-tag
                                  v-if="user.roleNames.length > 2"
                                  type="info"
                                  size="small"
                                  effect="plain"
                                >
                                  +{{ user.roleNames.length - 2 }}
                                </el-tag>
                              </div>
                            </div>

                            <!-- 用户状态 -->
                            <div class="user-status">
                              <el-tag
                                :type="getUserStatusType(user.status)"
                                size="small"
                                effect="plain"
                              >
                                {{ getUserStatusLabel(user.status) }}
                              </el-tag>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  ElCard,
  ElRow,
  ElCol,
  ElInput,
  ElButton,
  ElTag,
  ElIcon,
  ElAvatar,
  ElTree,
  ElScrollbar,
  ElSkeleton,
  ElEmpty,
  ElDivider
} from 'element-plus'
import {
  UserFilled,
  OfficeBuilding,
  User,
  Search,
  Collection,
  Folder
} from '@element-plus/icons-vue'
import { DeptAPI, UserAPI } from '@/api/system'
import type { 
  UserSelectorOption, 
  DeptSelectorOption,
  SelectorConfig,
  SelectorEmits 
} from './types'
import type { SysDeptVO, SysUserVO } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludeUserIds?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludeUserIds: () => []
})

const emit = defineEmits<SelectorEmits>()

// 响应式数据
const deptLoading = ref(false)
const userLoading = ref(false)
const depts = ref<DeptSelectorOption[]>([])
const users = ref<UserSelectorOption[]>([])
const allUsers = ref<UserSelectorOption[]>([]) // 全局用户缓存
const selectedDeptId = ref<string>('')
const selectedDept = ref<DeptSelectorOption | null>(null)
const selectedUserIds = ref<string | string[]>()
const selectedUserOptions = ref<UserSelectorOption[]>([])
const deptFilterText = ref('')
const userFilterText = ref('')
const deptTreeRef = ref()

// 默认配置
const defaultConfig: SelectorConfig = {
  multiple: true,
  clearable: true,
  placeholder: '请选择用户',
  size: 'default',
  disabled: false
}

// 合并配置
const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 部门树属性
const deptTreeProps = {
  children: 'children',
  label: 'label',
  disabled: 'disabled'
}

// 计算属性
const loading = computed(() => deptLoading.value || userLoading.value)

const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedUserIds.value) && selectedUserIds.value.length > 0
  }
  return selectedUserIds.value !== undefined && selectedUserIds.value !== null && selectedUserIds.value !== ''
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

const filteredDepts = computed(() => {
  if (!deptFilterText.value) return depts.value
  const filter = deptFilterText.value.toLowerCase()
  
  const filterTree = (nodes: DeptSelectorOption[]): DeptSelectorOption[] => {
    return nodes.filter(node => {
      const matches = node.label.toLowerCase().includes(filter) ||
                     (node.deptCode && node.deptCode.toLowerCase().includes(filter))
      
      if (matches) return true
      
      if (node.children) {
        const filteredChildren = filterTree(node.children)
        if (filteredChildren.length > 0) {
          node.children = filteredChildren
          return true
        }
      }
      
      return false
    })
  }
  
  return filterTree(JSON.parse(JSON.stringify(depts.value)))
})

const currentDeptUsers = computed(() => {
  if (!selectedDeptId.value) return []
  return users.value.filter(user => user.deptId === selectedDeptId.value)
})

const filteredUsers = computed(() => {
  let result = currentDeptUsers.value

  if (userFilterText.value) {
    const filter = userFilterText.value.toLowerCase()
    result = result.filter(user =>
      user.label.toLowerCase().includes(filter) ||
      (user.username && user.username.toLowerCase().includes(filter)) ||
      (user.realName && user.realName.toLowerCase().includes(filter)) ||
      (user.email && user.email.toLowerCase().includes(filter)) ||
      (user.phone && user.phone.toLowerCase().includes(filter))
    )
  }

  return result
})

// 方法
const loadDepts = async () => {
  try {
    deptLoading.value = true
    const response = await DeptAPI.getDeptTree({
      status: props.onlyEnabled ? '0' : undefined
    })
    const data = response.data || []
    depts.value = transformDeptData(data)
  } catch (error) {
    console.error('加载部门列表失败:', error)
    depts.value = []
  } finally {
    deptLoading.value = false
  }
}

const loadUsersByDept = async (deptId: string) => {
  try {
    userLoading.value = true
    const response = await UserAPI.getUserPage({
      deptId,
      status: props.onlyEnabled ? '0' : undefined,
      current: 1,
      size: 100
    })
    const data = response.data?.records || []
    const deptUsers = transformUserData(data)

    // 更新用户列表，保留其他部门的用户
    users.value = users.value.filter(user => user.deptId !== deptId).concat(deptUsers)

    // 更新全局用户缓存
    deptUsers.forEach(user => {
      const existingIndex = allUsers.value.findIndex(u => u.value === user.value)
      if (existingIndex >= 0) {
        allUsers.value[existingIndex] = user
      } else {
        allUsers.value.push(user)
      }
    })
  } catch (error) {
    console.error('加载用户列表失败:', error)
  } finally {
    userLoading.value = false
  }
}

// 加载缺失的用户
const loadMissingUsers = async (userIds: string[]) => {
  try {
    // 这里需要一个根据用户ID批量查询用户的API
    // 暂时使用单个查询的方式
    const userPromises = userIds.map(async (userId) => {
      try {
        const response = await UserAPI.getUserById(userId)
        if (response.success && response.data) {
          return response.data
        }
        return null
      } catch (error) {
        console.error(`加载用户 ${userId} 失败:`, error)
        return null
      }
    })

    const users = await Promise.all(userPromises)
    const validUsers = users.filter(user => user !== null) as SysUserVO[]

    if (validUsers.length > 0) {
      const missingUsers = transformUserData(validUsers)

      // 添加到全局缓存
      missingUsers.forEach(user => {
        const existingIndex = allUsers.value.findIndex(u => u.value === user.value)
        if (existingIndex >= 0) {
          allUsers.value[existingIndex] = user
        } else {
          allUsers.value.push(user)
        }
      })

      // 更新选中用户选项
      if (selectedUserIds.value) {
        selectedUserOptions.value = findUsersByIds(selectedUserIds.value)
      }
    }
  } catch (error) {
    console.error('加载缺失用户失败:', error)
  }
}

const transformDeptData = (data: SysDeptVO[]): DeptSelectorOption[] => {
  const transform = (depts: SysDeptVO[]): DeptSelectorOption[] => {
    return depts.map(dept => ({
      value: dept.id,
      label: dept.deptName,
      deptCode: dept.deptCode,
      disabled: dept.status !== '0',
      parentId: dept.parentId,
      userCount: dept.userCount,
      children: dept.children && dept.children.length > 0 ? transform(dept.children) : undefined
    }))
  }
  return transform(data)
}

const transformUserData = (data: SysUserVO[]): UserSelectorOption[] => {
  return data
    .filter(user => !props.excludeUserIds.includes(user.id))
    .map(user => ({
      value: user.id,
      label: user.nickname ||  user.username,
      username: user.username,
      nickname: user.nickname,
      realName: user.nickname,
      email: user.email,
      phone: user.phone,
      avatar: user.avatar,
      deptId: user.deptId,
      deptName: user.deptName,
      roleIds: user.roleIds,
      roleNames: user.roleNames,
      status: user.status,
      disabled: user.status !== '0'
    }))
}

const getDeptIcon = (data: DeptSelectorOption) => {
  return data.children && data.children.length > 0 ? Folder : OfficeBuilding
}

const getDeptIconColor = (data: DeptSelectorOption) => {
  if (data.disabled) return '#c0c4cc'
  return data.children && data.children.length > 0 ? '#f39c12' : '#409eff'
}

const getUserStatusType = (status?: string | number) => {
  switch (status) {
    case '0': return 'success'
    case '1': return 'danger'
    case '2': return 'warning'
    default: return 'info'
  }
}

const getUserStatusLabel = (status?: string | number) => {
  switch (status) {
    case '0': return '正常'
    case '1': return '禁用'
    case '2': return '锁定'
    default: return '未知'
  }
}

const filterDeptNode = (value: string, data: DeptSelectorOption) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase()) ||
         (data.deptCode && data.deptCode.toLowerCase().includes(value.toLowerCase()))
}

const isUserSelected = (userId: string): boolean => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.includes(userId)
  }
  return selectedUserIds.value === userId
}

const findUsersByIds = (ids: string | string[]): UserSelectorOption[] => {
  const targetIds = Array.isArray(ids) ? ids : (ids ? [ids] : [])
  // 首先从当前部门用户中查找
  let foundUsers = users.value.filter(user => targetIds.includes(user.value))

  // 如果没有找到全部用户，从全局缓存中查找
  const foundIds = foundUsers.map(user => user.value)
  const missingIds = targetIds.filter(id => !foundIds.includes(id))

  if (missingIds.length > 0) {
    const missingUsers = allUsers.value.filter(user => missingIds.includes(user.value))
    foundUsers = [...foundUsers, ...missingUsers]
  }

  // 如果还有缺失的用户，需要异步加载
  const stillMissingIds = targetIds.filter(id => !foundUsers.some(user => user.value === id))
  if (stillMissingIds.length > 0) {
    loadMissingUsers(stillMissingIds)
  }

  return foundUsers
}

// 事件处理
const handleDeptClick = async (data: DeptSelectorOption) => {
  if (data.disabled) return

  selectedDeptId.value = data.value
  selectedDept.value = data

  // 清空用户搜索
  userFilterText.value = ''

  // 加载该部门的用户
  await loadUsersByDept(data.value)
}

const handleUserClick = (user: UserSelectorOption) => {
  if (user.disabled) return

  if (config.value.multiple) {
    const currentIds = Array.isArray(selectedUserIds.value) ? [...selectedUserIds.value] : []
    const index = currentIds.indexOf(user.value)

    if (index > -1) {
      currentIds.splice(index, 1)
    } else {
      currentIds.push(user.value)
    }

    handleUserChange(currentIds)
  } else {
    handleUserChange(user.value)
  }
}



const handleUserChange = (value: string | string[]) => {
  selectedUserIds.value = value
  selectedUserOptions.value = findUsersByIds(value)

  emit('update:modelValue', value)

  // 修复多选模式下的change事件参数
  if (config.value.multiple) {
    emit('change', value, selectedUserOptions.value)
  } else {
    emit('change', value, selectedUserOptions.value.length === 1 ? selectedUserOptions.value[0] : null)

    // 单选模式下才触发select事件
    if (selectedUserOptions.value.length === 1) {
      emit('select', value as string, selectedUserOptions.value[0])
    }
  }
}

const handleUserRemove = (userId: string) => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    const newIds = selectedUserIds.value.filter(id => id !== userId)
    handleUserChange(newIds)
    emit('remove', userId)
  }
}

const selectAllCurrentDeptUsers = () => {
  if (!config.value.multiple || !currentDeptUsers.value.length) return

  const allUserIds = currentDeptUsers.value
    .filter(user => !user.disabled)
    .map(user => user.value)

  handleUserChange(allUserIds)
}

const clearSelection = () => {
  const clearValue = config.value.multiple ? [] : ''
  selectedUserIds.value = clearValue
  selectedUserOptions.value = []

  emit('update:modelValue', clearValue)
  emit('change', clearValue, null)
  emit('clear')
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  // 确保多选模式下初始化为数组，单选模式下初始化为字符串
  if (config.value.multiple) {
    selectedUserIds.value = Array.isArray(newValue) ? newValue : (newValue ? [newValue] : [])
  } else {
    selectedUserIds.value = Array.isArray(newValue) ? (newValue[0] || '') : (newValue || '')
  }
  selectedUserOptions.value = findUsersByIds(selectedUserIds.value)
}, { immediate: true })

// 监听部门搜索
watch(deptFilterText, (value) => {
  if (deptTreeRef.value) {
    deptTreeRef.value.filter(value)
  }
})

// 组件挂载
onMounted(async () => {
  await loadDepts()
})

// 暴露方法
defineExpose({
  refresh: loadDepts,
  clearSelection,
  selectAllCurrentDeptUsers
})
</script>

<style scoped>
.el-user-by-dept-selector {
  width: 100%;
}

.selector-card {
  border: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.dept-search,
.user-search {
  margin-bottom: 12px;
}

.dept-tree,
.user-list {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.dept-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.dept-icon {
  flex-shrink: 0;
}

.dept-label {
  flex: 1;
  min-width: 0;
}

.user-count {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  flex-shrink: 0;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  gap: 12px;
}

.user-item:hover {
  background: var(--el-fill-color-light);
}

.user-item.is-selected {
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
}

.user-item.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.user-details {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.user-roles {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.user-status {
  flex-shrink: 0;
}

.selected-users {
  margin-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;
  padding: 10px;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-avatar {
  margin-right: 4px;
}

/* 自定义选择器样式 */
.custom-user-selector {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  margin-bottom: 8px;
}

.custom-user-selector:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color-hover);
}

.custom-user-selector.selected {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.custom-user-selector.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.custom-user-selector.disabled:hover {
  background-color: transparent;
  border-color: transparent;
}

.selector-indicator {
  margin-right: 12px;
  flex-shrink: 0;
}

/* 自定义复选框样式 */
.custom-checkbox {
  width: 16px;
  height: 16px;
  position: relative;
}

.checkbox-inner {
  width: 100%;
  height: 100%;
  border: 2px solid var(--el-border-color);
  border-radius: 3px;
  background-color: var(--el-fill-color-blank);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-user-selector.selected .checkbox-inner {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.checkbox-inner i {
  color: white;
  font-size: 10px;
  font-weight: bold;
}

/* 自定义单选框样式 */
.custom-radio {
  width: 16px;
  height: 16px;
  position: relative;
}

.radio-inner {
  width: 100%;
  height: 100%;
  border: 2px solid var(--el-border-color);
  border-radius: 50%;
  background-color: var(--el-fill-color-blank);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-user-selector.selected .radio-inner {
  border-color: var(--el-color-primary);
}

.radio-dot {
  width: 8px;
  height: 8px;
  background-color: var(--el-color-primary);
  border-radius: 50%;
}

/* 悬停效果 */
.custom-user-selector:hover .checkbox-inner,
.custom-user-selector:hover .radio-inner {
  border-color: var(--el-color-primary);
}

.custom-user-selector.disabled:hover .checkbox-inner,
.custom-user-selector.disabled:hover .radio-inner {
  border-color: var(--el-border-color);
}
</style>
