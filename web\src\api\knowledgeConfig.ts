/**
 * 知识库配置管理API接口
 * 基于后端xhcai-rag模块的配置管理接口
 */

import { apiClient } from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'
import type { FileCleanSegmentConfig, VectorizationConfig } from '@/types/rag'

/**
 * 知识库配置管理API类
 */
export class KnowledgeConfigAPI {

  /**
   * 获取文件分段配置
   */
  static async getSegmentConfig(): Promise<ApiResponse<FileCleanSegmentConfig>> {
    return apiClient.get<FileCleanSegmentConfig>('/api/rag/config/segment')
  }

  /**
   * 保存文件分段配置
   */
  static async saveSegmentConfig(config: FileCleanSegmentConfig): Promise<ApiResponse<FileCleanSegmentConfig>> {
    return apiClient.post<FileCleanSegmentConfig>('/api/rag/config/segment', config)
  }

  /**
   * 获取向量化配置
   */
  static async getVectorizationConfig(): Promise<ApiResponse<VectorizationConfig>> {
    return apiClient.get<VectorizationConfig>('/api/rag/config/vectorization')
  }

  /**
   * 保存向量化配置
   */
  static async saveVectorizationConfig(config: VectorizationConfig): Promise<ApiResponse<VectorizationConfig>> {
    return apiClient.post<VectorizationConfig>('/api/rag/config/vectorization', config)
  }
}

export default KnowledgeConfigAPI
