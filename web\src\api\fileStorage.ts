import { apiClient } from '@/utils/apiClient'
import type { ApiResponse, PageResult } from '@/types/api'
/**
 * 文件存储配置创建DTO
 */
export interface FileStorageConfigCreateDTO {
  name: string
  storageType: string
  host?: string
  port?: number
  accessKey?: string
  secretKey?: string
  bucketName?: string
  region?: string
  sslEnabled?: string
  customDomain?: string
  connectionConfig?: string
  isDefault?: string
  status?: string
  description?: string
}

/**
 * 文件存储配置更新DTO
 */
export interface FileStorageConfigUpdateDTO extends FileStorageConfigCreateDTO {}

/**
 * 文件存储配置查询DTO
 */
export interface FileStorageConfigQueryDTO {
  pageNum?: number
  pageSize?: number
  name?: string
  storageType?: string
  status?: string
  isDefault?: string
  startTime?: string
  endTime?: string
}

/**
 * 文件存储配置VO
 */
export interface FileStorageConfigVO {
  id: string
  name: string
  storageType: string
  storageTypeName: string
  host?: string
  port?: number
  accessKey?: string
  bucketName?: string
  region?: string
  sslEnabled?: string
  customDomain?: string
  connectionConfig?: string
  isDefault: string
  status: string
  description?: string
  tenantId: string
  createTime: string
  updateTime: string
  createBy: string
  updateBy: string
  icon?: string
  iconColor?: string
}

/**
 * 文件存储配置API
 */
export const FileStorageAPI = {
  /**
   * 分页查询文件存储配置列表
   */
  getFileStorageConfigPage(params: FileStorageConfigQueryDTO): Promise<ApiResponse<PageResult<FileStorageConfigVO>>> {
    return apiClient.get('/api/rag/file-storage/page', { params })
  },

  /**
   * 查询文件存储配置列表
   */
  getFileStorageConfigList(params: FileStorageConfigQueryDTO): Promise<ApiResponse<FileStorageConfigVO[]>> {
    return apiClient.get('/api/rag/file-storage/list', { params })
  },

  /**
   * 根据ID查询文件存储配置详情
   */
  getFileStorageConfigById(id: string): Promise<ApiResponse<FileStorageConfigVO>> {
    return apiClient.get(`/api/rag/file-storage/${id}`)
  },

  /**
   * 创建文件存储配置
   */
  createFileStorageConfig(data: FileStorageConfigCreateDTO): Promise<ApiResponse<void>> {
    return apiClient.post('/api/rag/file-storage', data)
  },

  /**
   * 更新文件存储配置
   */
  updateFileStorageConfig(id: string, data: FileStorageConfigUpdateDTO): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/rag/file-storage/${id}`, data)
  },

  /**
   * 删除文件存储配置
   */
  deleteFileStorageConfigs(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete('/api/rag/file-storage', { data: ids })
  },

  /**
   * 批量更新状态
   */
  batchUpdateStatus(ids: string[], status: string): Promise<ApiResponse<void>> {
    return apiClient.put('/api/rag/file-storage/status', { ids, status })
  },

  /**
   * 设置默认存储配置
   */
  setDefaultConfig(id: string): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/rag/file-storage/${id}/default`)
  },

  /**
   * 获取默认存储配置
   */
  getDefaultConfig(): Promise<ApiResponse<FileStorageConfigVO>> {
    return apiClient.get('/api/rag/file-storage/default')
  },

  /**
   * 根据存储类型获取配置列表
   */
  getConfigsByType(storageType: string): Promise<ApiResponse<FileStorageConfigVO[]>> {
    return apiClient.get(`/api/rag/file-storage/type/${storageType}`)
  },

  /**
   * 测试存储连接
   */
  testConnectionById(id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return apiClient.post(`/api/rag/file-storage/${id}/test`)
  },

  /**
   * 测试存储连接（根据配置）
   */
  testConnectionByConfig(config: FileStorageConfigCreateDTO): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return apiClient.post('/api/rag/file-storage/test', config)
  }
}
