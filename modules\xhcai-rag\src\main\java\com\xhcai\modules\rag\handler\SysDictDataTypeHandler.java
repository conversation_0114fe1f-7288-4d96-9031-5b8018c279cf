package com.xhcai.modules.rag.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.xhcai.modules.system.vo.SysDictDataVO;

/**
 * 字典数据类型处理器
 *
 * <AUTHOR>
 */
@MappedTypes({SysDictDataVO.class})
@MappedJdbcTypes({JdbcType.OTHER})
public class SysDictDataTypeHandler extends BaseTypeHandler<SysDictDataVO> {

    private static final Logger log = LoggerFactory.getLogger(SysDictDataTypeHandler.class);

    // 简化的 ObjectMapper，只处理基本字段
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    // 需要保留的字段列表（排除时间字段）
    private static final Set<String> KEEP_FIELDS = new HashSet<>(Arrays.asList(
            "id", "dict_sort", "dict_label", "dict_value", "dict_type",
            "css_class", "list_class", "is_default", "status", "tenant_id", "remark"
    ));

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, SysDictDataVO parameter, JdbcType jdbcType) throws SQLException {
        try {
            PGobject jsonObject = new PGobject();
            jsonObject.setType("jsonb");

            if (parameter == null) {
                jsonObject.setValue(null);
            } else {
                String jsonString = objectMapper.writeValueAsString(parameter);
                jsonObject.setValue(jsonString);
                log.debug("设置SysDictDataVO JSON参数: {}", jsonString);
            }

            ps.setObject(i, jsonObject);
        } catch (JsonProcessingException e) {
            log.error("SysDictDataVO JSON序列化失败", e);
            throw new SQLException("SysDictDataVO JSON序列化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public SysDictDataVO getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonString = rs.getString(columnName);
        return parseJson(jsonString);
    }

    @Override
    public SysDictDataVO getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonString = rs.getString(columnIndex);
        return parseJson(jsonString);
    }

    @Override
    public SysDictDataVO getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonString = cs.getString(columnIndex);
        return parseJson(jsonString);
    }

    private SysDictDataVO parseJson(String jsonString) throws SQLException {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            // 先解析为 Map，过滤掉时间字段
            Map<String, Object> rawMap = objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {
            });

            // 创建过滤后的 Map，只保留需要的字段
            Map<String, Object> filteredMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : rawMap.entrySet()) {
                if (KEEP_FIELDS.contains(entry.getKey())) {
                    filteredMap.put(entry.getKey(), entry.getValue());
                }
            }

            // 将过滤后的 Map 转换为 SysDictDataVO
            String filteredJson = objectMapper.writeValueAsString(filteredMap);
            return objectMapper.readValue(filteredJson, SysDictDataVO.class);

        } catch (JsonProcessingException e) {
            log.error("SysDictDataVO JSON反序列化失败: {}", jsonString, e);
            throw new SQLException("SysDictDataVO JSON反序列化失败: " + e.getMessage(), e);
        }
    }
}
