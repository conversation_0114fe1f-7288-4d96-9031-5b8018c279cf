# YYZS Agent Platform Frontend

## 项目简介

YYZS Agent Platform Frontend 是基于 React + Next.js + TailwindCSS 的现代化前端应用，为 YYZS Agent Platform 后端服务提供用户界面。该前端应用专门用于管理 Elastic Stack 组件的安装、配置、监控和运维。

## 技术栈

- **框架**: Next.js 14 (React 18)
- **样式**: TailwindCSS 3.3
- **语言**: TypeScript 5.2
- **状态管理**: React Query (TanStack Query)
- **HTTP客户端**: Axios
- **图标**: Lucide React
- **图表**: Recharts
- **表单**: React Hook Form
- **通知**: React Hot Toast
- **动画**: Framer Motion
- **构建工具**: Next.js内置构建系统

## 项目结构

```
yyzs/web/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── layout.tsx          # 根布局
│   │   ├── page.tsx           # 首页
│   │   └── globals.css        # 全局样式
│   ├── api/                   # API客户端
│   │   ├── client.ts          # HTTP客户端封装
│   │   ├── components.ts      # 组件管理API
│   │   └── monitor.ts         # 监控API
│   ├── components/            # React组件
│   │   ├── ui/               # 基础UI组件
│   │   ├── forms/            # 表单组件
│   │   ├── charts/           # 图表组件
│   │   └── layout/           # 布局组件
│   ├── hooks/                # 自定义Hooks
│   ├── types/                # TypeScript类型定义
│   │   ├── component.ts      # 组件相关类型
│   │   └── monitor.ts        # 监控相关类型
│   ├── utils/                # 工具函数
│   └── styles/               # 样式文件
├── public/                   # 静态资源
├── package.json             # 项目配置
├── next.config.js           # Next.js配置
├── tailwind.config.js       # TailwindCSS配置
├── tsconfig.json           # TypeScript配置
└── README.md               # 项目文档
```

## 主要功能

### 1. 仪表板
- 系统概览和统计信息
- 组件状态实时监控
- 系统资源使用情况
- 快速操作入口

### 2. 组件管理
- 组件列表查看和筛选
- 组件安装包上传
- 组件安装、卸载、启停
- 组件配置管理
- 批量操作支持

### 3. 监控中心
- 实时性能监控
- 历史数据查看
- 性能趋势分析
- 告警管理
- 监控配置

### 4. 系统管理
- 系统设置
- 用户管理
- 日志查看
- 数据导出

## API规范

### 请求格式
所有API请求都通过统一的HTTP客户端发送，支持：
- 自动添加认证头
- 请求/响应拦截
- 错误处理
- 超时控制

### 响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  errors?: Record<string, string>;
}
```

### 错误处理
- 网络错误自动重试
- 401错误自动跳转登录
- 统一错误提示
- 详细错误日志

## 开发指南

### 环境要求
- Node.js 18+
- npm 8+ 或 yarn 1.22+

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发模式
```bash
npm run dev
# 或
yarn dev
```

应用将在 http://localhost:3001 启动

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 启动生产服务器
```bash
npm run start
# 或
yarn start
```

### 代码检查
```bash
npm run lint
# 或
yarn lint
```

### 类型检查
```bash
npm run type-check
# 或
yarn type-check
```

## 环境配置

### 环境变量
创建 `.env.local` 文件：
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_APP_NAME=YYZS Agent Platform
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### API代理
开发环境下，Next.js会自动将 `/api/*` 请求代理到后端服务器。

## 样式规范

### TailwindCSS类名规范
- 使用语义化的类名组合
- 优先使用Tailwind内置类
- 自定义组件样式放在 `@layer components`
- 工具类放在 `@layer utilities`

### 组件样式
```typescript
// 推荐的组件样式写法
const Button = ({ variant = 'primary', size = 'md', children, ...props }) => {
  const baseClasses = 'btn';
  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    outline: 'btn-outline',
  };
  const sizeClasses = {
    sm: 'btn-sm',
    md: '',
    lg: 'btn-lg',
  };
  
  return (
    <button 
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}
      {...props}
    >
      {children}
    </button>
  );
};
```

## 组件开发规范

### 文件命名
- 组件文件使用 PascalCase: `ComponentName.tsx`
- 工具函数使用 camelCase: `utilityFunction.ts`
- 类型定义使用 camelCase: `componentTypes.ts`

### 组件结构
```typescript
'use client'; // 如果需要客户端渲染

import { useState, useEffect } from 'react';
import { SomeIcon } from 'lucide-react';

interface ComponentProps {
  // 属性定义
}

export default function Component({ prop1, prop2 }: ComponentProps) {
  // 状态和逻辑
  
  return (
    <div className="component-wrapper">
      {/* JSX内容 */}
    </div>
  );
}
```

### Hooks使用
- 优先使用React内置Hooks
- 自定义Hooks放在 `src/hooks/` 目录
- 使用React Query管理服务器状态
- 使用useState管理本地状态

## 部署说明

### Docker部署
```dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules

EXPOSE 3001
CMD ["npm", "start"]
```

### 静态部署
```bash
npm run build
npm run export
```

生成的静态文件在 `out/` 目录中。

## 性能优化

### 代码分割
- 使用动态导入 `import()` 进行代码分割
- 路由级别的代码分割
- 组件级别的懒加载

### 图片优化
- 使用 Next.js Image 组件
- 支持WebP格式
- 自动响应式图片

### 缓存策略
- API响应缓存
- 静态资源缓存
- 浏览器缓存优化

## 浏览器支持

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 许可证

Apache License 2.0

## 联系方式

- 邮箱: <EMAIL>
- 网站: https://www.yyzs.com
