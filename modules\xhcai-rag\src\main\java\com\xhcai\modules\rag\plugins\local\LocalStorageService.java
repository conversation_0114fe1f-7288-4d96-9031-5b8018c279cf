package com.xhcai.modules.rag.plugins.local;


import com.xhcai.plugin.storage.IStorageService;
import com.xhcai.plugin.storage.StorageFileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 本地存储服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class LocalStorageService implements IStorageService {

    private String basePath;
    private boolean initialized = false;

    @Override
    public String getServiceName() {
        return "本地存储服务";
    }

    @Override
    public String getServiceType() {
        return "local";
    }

    @Override
    public void initialize(Map<String, Object> config) {
        try {
            this.basePath = (String) config.get("basePath");
            
            if (basePath == null) {
                this.basePath = System.getProperty("user.home") + "/xhcai-storage";
            }

            // 创建基础目录
            Path baseDir = Paths.get(basePath);
            if (!Files.exists(baseDir)) {
                Files.createDirectories(baseDir);
            }

            this.initialized = true;
            log.info("本地存储服务初始化成功: {}", basePath);

        } catch (Exception e) {
            log.error("本地存储服务初始化失败", e);
            throw new RuntimeException("本地存储服务初始化失败", e);
        }
    }

    @Override
    public String uploadFile(String bucketName, String objectName, InputStream inputStream, String contentType) {
        return uploadFile(bucketName, objectName, inputStream, contentType, null);
    }

    @Override
    public String uploadFile(String bucketName, String objectName, InputStream inputStream, 
                           String contentType, Map<String, String> metadata) {
        checkInitialized();

        try {
            Path filePath = buildFilePath(bucketName, objectName);
            
            // 创建父目录
            Files.createDirectories(filePath.getParent());
            
            // 写入文件
            Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
            
            log.info("本地文件上传成功: {}", filePath);
            return "file://" + filePath.toAbsolutePath();

        } catch (Exception e) {
            log.error("本地文件上传失败: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("本地文件上传失败", e);
        } finally {
            closeInputStream(inputStream);
        }
    }

    @Override
    public InputStream downloadFile(String bucketName, String objectName) {
        checkInitialized();

        try {
            Path filePath = buildFilePath(bucketName, objectName);
            
            if (!Files.exists(filePath)) {
                throw new RuntimeException("文件不存在: " + filePath);
            }
            
            return Files.newInputStream(filePath);

        } catch (Exception e) {
            log.error("本地文件下载失败: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("本地文件下载失败", e);
        }
    }

    @Override
    public boolean deleteFile(String bucketName, String objectName) {
        checkInitialized();

        try {
            Path filePath = buildFilePath(bucketName, objectName);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("本地文件删除成功: {}", filePath);
                return true;
            } else {
                log.warn("本地文件不存在: {}", filePath);
                return false;
            }

        } catch (Exception e) {
            log.error("本地文件删除失败: {}/{}", bucketName, objectName, e);
            return false;
        }
    }

    @Override
    public Map<String, Boolean> deleteFiles(String bucketName, List<String> objectNames) {
        Map<String, Boolean> results = new HashMap<>();
        
        for (String objectName : objectNames) {
            boolean success = deleteFile(bucketName, objectName);
            results.put(objectName, success);
        }
        return results;
    }

    @Override
    public List<StorageFileInfo> listFiles(String bucketName, String prefix, int maxKeys) {
        checkInitialized();


        List<StorageFileInfo> fileInfos = new ArrayList<>();
        
        try {
            Path bucketPath = Paths.get(basePath, bucketName);
            
            if (!Files.exists(bucketPath)) {
                return fileInfos;
            }

            Path searchPath = prefix != null ? bucketPath.resolve(prefix) : bucketPath;
            
            if (Files.exists(searchPath)) {
                try (Stream<Path> paths = Files.walk(searchPath)) {
                    paths.filter(Files::isRegularFile)
                         .forEach(path -> {
                             try {
                                 String relativePath = bucketPath.relativize(path).toString().replace("\\", "/");
                                 StorageFileInfo fileInfo = createFileInfo(bucketName, relativePath, path);
                                 fileInfos.add(fileInfo);
                             } catch (Exception e) {
                                 log.warn("获取文件信息失败: {}", path, e);
                             }
                         });
                }
            }
            
            return fileInfos;

        } catch (Exception e) {
            log.error("本地文件列表获取失败: {}/{}", bucketName, prefix, e);
            throw new RuntimeException("本地文件列表获取失败", e);
        }
    }

    @Override
    public StorageFileInfo getFileInfo(String bucketName, String objectName) {
        checkInitialized();

        try {
            Path filePath = buildFilePath(bucketName, objectName);
            
            if (!Files.exists(filePath)) {
                return null;
            }
            
            return createFileInfo(bucketName, objectName, filePath);

        } catch (Exception e) {
            log.error("本地文件信息获取失败: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("本地文件信息获取失败", e);
        }
    }

    @Override
    public boolean bucketExists(String bucketName) {
        checkInitialized();

        try {
            Path bucketPath = Paths.get(basePath, bucketName);
            return Files.exists(bucketPath) && Files.isDirectory(bucketPath);
        } catch (Exception e) {
            log.error("检查本地目录存在性失败: {}", bucketName, e);
            return false;
        }
    }

    @Override
    public boolean createBucket(String bucketName) {
        checkInitialized();

        try {
            Path bucketPath = Paths.get(basePath, bucketName);
            
            if (!Files.exists(bucketPath)) {
                Files.createDirectories(bucketPath);
                log.info("本地目录创建成功: {}", bucketPath);
            }
            
            return true;
        } catch (Exception e) {
            log.error("本地目录创建失败: {}", bucketName, e);
            return false;
        }
    }

    @Override
    public boolean deleteBucket(String bucketName) {
        checkInitialized();

        try {
            Path bucketPath = Paths.get(basePath, bucketName);
            
            if (Files.exists(bucketPath)) {
                // 递归删除目录及其内容
                deleteDirectoryRecursively(bucketPath);
                log.info("本地目录删除成功: {}", bucketPath);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("本地目录删除失败: {}", bucketName, e);
            return false;
        }
    }

    @Override
    public boolean isHealthy() {
        if (!initialized) {
            return false;
        }

        try {
            Path baseDir = Paths.get(basePath);
            return Files.exists(baseDir) && Files.isDirectory(baseDir) && Files.isWritable(baseDir);
        } catch (Exception e) {
            log.error("本地存储健康检查失败", e);
            return false;
        }
    }

    @Override
    public boolean fileExists(String bucketName, String objectName) {
        return false;
    }

    @Override
    public String generatePresignedUrl(String bucketName, String objectName, int expireSeconds) {
        return "";
    }

    /**
     * 构建文件路径
     */
    private Path buildFilePath(String bucketName, String objectName) {
        return Paths.get(basePath, bucketName, objectName);
    }

    /**
     * 创建文件信息对象
     */
    private StorageFileInfo createFileInfo(String bucketName, String objectName, Path filePath) throws IOException {
        return StorageFileInfo.builder()
                .bucketName(bucketName)
                .objectName(objectName)
                .size(Files.size(filePath))
                .lastModified(LocalDateTime.ofInstant(
                        Files.getLastModifiedTime(filePath).toInstant(),
                        ZoneId.systemDefault()))
                .isDirectory(Files.isDirectory(filePath))
                .url("file://" + filePath.toAbsolutePath())
                .build();
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectoryRecursively(Path directory) throws IOException {
        try (Stream<Path> paths = Files.walk(directory)) {
            paths.sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                 .forEach(path -> {
                     try {
                         Files.delete(path);
                     } catch (IOException e) {
                         log.warn("删除文件/目录失败: {}", path, e);
                     }
                 });
        }
    }

    /**
     * 关闭输入流
     */
    private void closeInputStream(InputStream inputStream) {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.warn("关闭输入流失败", e);
            }
        }
    }

    /**
     * 检查是否已初始化
     */
    private void checkInitialized() {
        if (!initialized) {
            throw new IllegalStateException("本地存储服务未初始化");
        }
    }
}
