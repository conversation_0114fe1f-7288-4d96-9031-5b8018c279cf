@echo off
echo Starting YYZS Agent Platform Frontend...
echo.

REM 检查 Node.js 是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM 检查 npm 是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not available
    pause
    exit /b 1
)

REM 检查是否存在 node_modules
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM 清理构建缓存
if exist ".next" (
    echo Cleaning build cache...
    rmdir /s /q ".next"
)

echo.
echo Starting development server on http://localhost:3001
echo Press Ctrl+C to stop the server
echo.

REM 启动开发服务器
npm run dev

pause
