package com.xhcai.modules.dify.controller;

import java.util.List;
import java.util.Map;

import com.xhcai.modules.dify.service.IThirdPlatformService;
import com.xhcai.modules.dify.vo.ThirdPlatformVO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformQueryDTO;
import com.xhcai.modules.dify.entity.ThirdPlatform;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 第三方智能体管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "第三方智能体管理", description = "第三方智能体管理相关接口")
@RestController
@RequestMapping("/api/third-platform")
@Validated
public class ThirdPlatformController {

    @Autowired
    private IThirdPlatformService thirdPlatformService;

    /**
     * 分页查询第三方智能体列表
     */
    @Operation(summary = "分页查询第三方智能体平台列表", description = "根据查询条件分页查询第三方智能体平台列表")
    @GetMapping("/page")
    @RequiresPermissions("agent:third-platform-agent:list")
    public Result<PageResult<ThirdPlatformVO>> getAgentPage(@Valid ThirdPlatformQueryDTO queryDTO) {
        PageResult<ThirdPlatformVO> result = thirdPlatformService.getPlatformPage(queryDTO);
        return Result.success(result);
    }

    /**
     * 根据用户权限查询第三方智能体列表
     */
    @Operation(summary = "根据用户账号配置查询第三方智能体列表", description = "根据查询条件分页查询第三方智能体平台列表")
    @GetMapping("/list")
    @RequiresPermissions("agent:third-platform-agent:list")
    public Result<List<ThirdPlatformVO>> getAgentList(@Valid ThirdPlatformQueryDTO queryDTO) {
        List<ThirdPlatformVO> result = thirdPlatformService.getPlatformList(queryDTO);
        return Result.success(result);
    }


    /**
     * 根据ID查询第三方智能体详情
     */
    @Operation(summary = "查询第三方智能体详情", description = "根据ID查询第三方智能体详情")
    @GetMapping("/{id}")
    @RequiresPermissions("agent:third-platform-agent:query")
    public Result<ThirdPlatformVO> getAgentById(
            @Parameter(description = "智能体ID", required = true) @PathVariable String id) {
        ThirdPlatformVO result = thirdPlatformService.getPlatformById(id);
        return Result.success(result);
    }

    /**
     * 新增第三方智能体
     */
    @Operation(summary = "新增第三方智能体", description = "新增第三方智能体")
    @PostMapping
    @RequiresPermissions("agent:third-platform-agent:add")
    public Result<Void> addAgent(@Valid @RequestBody ThirdPlatform agent) {
        boolean success = thirdPlatformService.addPlatform(agent);
        return success ? Result.success() : Result.error("新增失败");
    }

    /**
     * 修改第三方智能体
     */
    @Operation(summary = "修改第三方智能体", description = "修改第三方智能体")
    @PutMapping
    @RequiresPermissions("agent:third-platform-agent:edit")
    public Result<Void> updateAgent(@Valid @RequestBody ThirdPlatform agent) {
        boolean success = thirdPlatformService.updatePlatform(agent);
        return success ? Result.success() : Result.error("修改失败");
    }

    /**
     * 删除第三方智能体
     */
    @Operation(summary = "删除第三方智能体", description = "根据ID删除第三方智能体")
    @DeleteMapping("/{id}")
    @RequiresPermissions("agent:third-platform-agent:remove")
    public Result<Void> deleteAgent(
            @Parameter(description = "智能体ID", required = true) @PathVariable String id) {
        boolean success = thirdPlatformService.deletePlatform(id);
        return success ? Result.success() : Result.error("删除失败");
    }

    /**
     * 批量删除第三方智能体
     */
    @Operation(summary = "批量删除第三方智能体", description = "根据ID列表批量删除第三方智能体")
    @DeleteMapping("/batch")
    @RequiresPermissions("agent:third-platform-agent:remove")
    public Result<Void> batchDeletePlatform(@RequestBody List<String> ids) {
        boolean success = thirdPlatformService.batchDeletePlatforms(ids);
        return success ? Result.success() : Result.error("批量删除失败");
    }

    /**
     * 启用/禁用第三方智能体
     */
    @Operation(summary = "启用/禁用第三方智能体", description = "启用或禁用第三方智能体")
    @PutMapping("/{id}/status")
    @RequiresPermissions("agent:third-platform-agent:edit")
    public Result<Void> toggleStatus(
            @Parameter(description = "智能体ID", required = true) @PathVariable String id,
            @Parameter(description = "状态：0-禁用，1-启用", required = true) @RequestParam Integer status) {
        boolean success = thirdPlatformService.toggleStatus(id, status);
        return success ? Result.success() : Result.error("状态切换失败");
    }

    /**
     * 批量启用/禁用第三方智能体
     */
    @Operation(summary = "批量启用/禁用第三方智能体", description = "批量启用或禁用第三方智能体")
    @PutMapping("/batch/status")
    @RequiresPermissions("agent:third-platform-agent:edit")
    public Result<Void> batchToggleStatus(
            @RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> ids = (List<String>) params.get("ids");
        Integer status = (Integer) params.get("status");
        boolean success = thirdPlatformService.batchToggleStatus(ids, status);
        return success ? Result.success() : Result.error("批量状态切换失败");
    }

    /**
     * 测试第三方智能体连接
     */
    @Operation(summary = "测试智能体连接", description = "测试第三方智能体连接是否正常")
    @PostMapping("/{id}/test")
    @RequiresPermissions("agent:third-platform-agent:test")
    public Result<String> testConnection(
            @Parameter(description = "智能体ID", required = true) @PathVariable String id) {
        String result = thirdPlatformService.testConnection(id);
        return Result.success(result);
    }

    /**
     * 查询用户可访问的第三方智能体列表
     */
    @Operation(summary = "查询可访问的智能体列表", description = "查询当前用户可访问的第三方智能体列表")
    @GetMapping("/accessible")
    public Result<List<ThirdPlatformVO>> getAccessibleAgents(
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId) {
        List<ThirdPlatformVO> result = thirdPlatformService.getAccessiblePlatforms(userId);
        return Result.success(result);
    }

    /**
     * 检查用户访问权限
     */
    @Operation(summary = "检查用户访问权限", description = "检查用户是否有权限访问指定智能体")
    @GetMapping("/{agentId}/access/{userId}")
    public Result<Boolean> checkUserAccess(
            @Parameter(description = "智能体ID", required = true) @PathVariable String agentId,
            @Parameter(description = "用户ID", required = true) @PathVariable String userId) {
        boolean hasAccess = thirdPlatformService.checkUserAccess(agentId, userId);
        return Result.success(hasAccess);
    }

    /**
     * 获取智能体统计信息
     */
    @Operation(summary = "获取智能体统计信息", description = "获取第三方智能体的统计信息")
    @GetMapping("/statistics")
    @RequiresPermissions("agent:third-platform-agent:list")
    public Result<Map<String, Object>> getAgentStatistics() {
        Map<String, Object> statistics = thirdPlatformService.getPlatformStatistics();
        return Result.success(statistics);
    }

    /**
     * 复制智能体配置
     */
    @Operation(summary = "复制智能体配置", description = "复制现有智能体配置创建新智能体")
    @PostMapping("/{sourceId}/copy")
    @RequiresPermissions("agent:third-platform-agent:add")
    public Result<Void> copyAgent(
            @Parameter(description = "源智能体ID", required = true) @PathVariable String sourceId,
            @Parameter(description = "新智能体名称", required = true) @RequestParam String newName) {
        boolean success = thirdPlatformService.copyPlatform(sourceId, newName);
        return success ? Result.success() : Result.error("复制失败");
    }

    /**
     * 导出智能体配置
     */
    @Operation(summary = "导出智能体配置", description = "导出指定智能体的配置信息")
    @PostMapping("/export")
    @RequiresPermissions("agent:third-platform-agent:export")
    public Result<List<ThirdPlatformVO>> exportAgents(@RequestBody List<String> ids) {
        List<ThirdPlatformVO> result = thirdPlatformService.exportPlatforms(ids);
        return Result.success(result);
    }

    /**
     * 导入智能体配置
     */
    @Operation(summary = "导入智能体配置", description = "导入智能体配置信息")
    @PostMapping("/import")
    @RequiresPermissions("agent:third-platform-agent:import")
    public Result<String> importPlatforms(@RequestBody List<ThirdPlatform> agents) {
        String result = thirdPlatformService.importPlatforms(agents);
        return Result.success(result);
    }

    /**
     * 验证智能体配置
     */
    @Operation(summary = "验证智能体配置", description = "验证智能体配置是否正确")
    @PostMapping("/validate")
    public Result<String> validateAgentConfig(@RequestBody ThirdPlatform agent) {
        String result = thirdPlatformService.validatePlatformConfig(agent);
        if (result == null) {
            return Result.success("配置验证通过");
        } else {
            return Result.error(result);
        }
    }

}
