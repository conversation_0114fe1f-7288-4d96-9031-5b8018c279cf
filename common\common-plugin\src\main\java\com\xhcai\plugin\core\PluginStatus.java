package com.xhcai.plugin.core;

/**
 * 插件状态枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PluginStatus {
    
    /**
     * 已创建，但未启动
     */
    CREATED("created", "已创建"),
    
    /**
     * 正在启动中
     */
    STARTING("starting", "启动中"),
    
    /**
     * 已启动，正常运行
     */
    STARTED("started", "已启动"),
    
    /**
     * 正在停止中
     */
    STOPPING("stopping", "停止中"),
    
    /**
     * 已停止
     */
    STOPPED("stopped", "已停止"),
    
    /**
     * 启动失败
     */
    FAILED("failed", "启动失败"),
    
    /**
     * 已禁用
     */
    DISABLED("disabled", "已禁用");
    
    private final String code;
    private final String name;
    
    PluginStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取插件状态
     */
    public static PluginStatus fromCode(String code) {
        for (PluginStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown plugin status code: " + code);
    }
}
