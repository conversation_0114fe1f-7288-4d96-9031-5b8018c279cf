package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 角色权限关联实体类
 *
 * <p>
 * 角色权限关联需要租户隔离，不同租户的角色权限关联是独立的</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_role_permission")
@Schema(description = "角色权限关联")
@TableName("sys_role_permission")
public class SysRolePermission extends BaseWithTenantIDEntity {

    // id和审计字段由BaseEntity提供
    /**
     * 角色ID
     */
    @Schema(description = "角色ID", example = "1")
    @TableField("role_id")
    private String roleId;

    /**
     * 权限ID
     */
    @Schema(description = "权限ID", example = "1")
    @TableField("permission_id")
    private String permissionId;

    // 租户ID和其他审计字段由BaseEntity提供
    // Getters and Setters - 只保留业务字段
    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(String permissionId) {
        this.permissionId = permissionId;
    }

    @Override
    public String toString() {
        return "SysRolePermission{"
                + "roleId=" + roleId
                + ", permissionId=" + permissionId
                + ", " + super.toString()
                + '}';
    }
}
