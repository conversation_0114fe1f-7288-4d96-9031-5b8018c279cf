'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { 
  ArrowLeft, 
  Trash2, 
  <PERSON><PERSON><PERSON>cle, 
  AlertTriangle,
  Loader2,
  Download,
  Server,
  Database,
  FileText,
  Settings as SettingsIcon,
  HardDrive,
  Cloud,
  Wifi
} from 'lucide-react';
import { ElasticComponent } from '@/types/component';
import ComponentsAPI from '@/api/components';
import toast from 'react-hot-toast';

// 卸载步骤
enum UninstallStep {
  CONFIGURE = 'configure',
  BACKUP = 'backup', 
  UNINSTALL = 'uninstall',
  COMPLETE = 'complete'
}

const STEP_LABELS = {
  [UninstallStep.CONFIGURE]: '卸载配置',
  [UninstallStep.BACKUP]: '数据备份',
  [UninstallStep.UNINSTALL]: '卸载组件',
  [UninstallStep.COMPLETE]: '卸载完成'
};

// 卸载选项
interface UninstallOptions {
  removeConfig: boolean;
  removeData: boolean;
  removeLogs: boolean;
  backup: boolean;
  backupConfig: boolean;
  backupData: boolean;
  backupLogs: boolean;
  backupType: 'local' | 'minio' | 'ftp';
  backupLocation?: string;
}

// 备份类型配置
interface BackupConfig {
  minio?: {
    endpoint: string;
    accessKey: string;
    secretKey: string;
    bucket: string;
  };
  ftp?: {
    host: string;
    port: number;
    username: string;
    password: string;
    path: string;
  };
}

export default function ComponentUninstallPage() {
  const router = useRouter();
  const params = useParams();
  const componentId = params.componentId as string;

  const [currentStep, setCurrentStep] = useState<UninstallStep>(UninstallStep.CONFIGURE);
  const [loading, setLoading] = useState(false);
  const [component, setComponent] = useState<ElasticComponent | null>(null);
  const [uninstallOptions, setUninstallOptions] = useState<UninstallOptions>({
    removeConfig: false,
    removeData: false,
    removeLogs: false,
    backup: false,
    backupConfig: false,
    backupData: false,
    backupLogs: false,
    backupType: 'local'
  });
  const [backupConfig, setBackupConfig] = useState<BackupConfig>({});
  const [uninstallProgress, setUninstallProgress] = useState(0);

  // 加载组件信息
  useEffect(() => {
    if (componentId) {
      loadComponentInfo();
    }
  }, [componentId]);

  const loadComponentInfo = async () => {
    try {
      const response = await ComponentsAPI.getComponent(componentId);
      if (response.success && response.data) {
        setComponent(response.data);
      } else {
        toast.error('组件不存在');
        router.push('/');
      }
    } catch (error) {
      console.error('加载组件信息失败:', error);
      toast.error('加载组件信息失败');
      router.push('/');
    }
  };

  // 处理卸载选项变化
  const handleOptionChange = (option: keyof UninstallOptions, value: boolean | string) => {
    setUninstallOptions(prev => ({
      ...prev,
      [option]: value
    }));
  };

  // 处理备份配置变化
  const handleBackupConfigChange = (type: 'minio' | 'ftp', field: string, value: string | number) => {
    setBackupConfig(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value
      }
    }));
  };

  // 下一步
  const handleNext = () => {
    if (currentStep === UninstallStep.CONFIGURE) {
      if (uninstallOptions.backup) {
        setCurrentStep(UninstallStep.BACKUP);
      } else {
        handleUninstall();
      }
    } else if (currentStep === UninstallStep.BACKUP) {
      handleUninstall();
    }
  };

  // 执行卸载
  const handleUninstall = async () => {
    setLoading(true);
    setCurrentStep(UninstallStep.UNINSTALL);
    
    try {
      // 模拟卸载进度
      const progressInterval = setInterval(() => {
        setUninstallProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 500);

      // 执行备份（如果需要）
      if (uninstallOptions.backup) {
        // TODO: 实现备份逻辑
      }

      // 执行卸载
      const response = await ComponentsAPI.uninstallComponent(componentId);
      
      clearInterval(progressInterval);
      setUninstallProgress(100);
      
      if (response.success) {
        setTimeout(() => {
          setCurrentStep(UninstallStep.COMPLETE);
          toast.success('组件卸载成功');
        }, 1000);
      } else {
        toast.error(response.message || '卸载失败');
        setCurrentStep(UninstallStep.CONFIGURE);
        setUninstallProgress(0);
      }
    } catch (error) {
      console.error('卸载失败:', error);
      toast.error('卸载失败，请重试');
      setCurrentStep(UninstallStep.CONFIGURE);
      setUninstallProgress(0);
    } finally {
      setLoading(false);
    }
  };

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    const steps = Object.values(UninstallStep);
    const currentIndex = steps.indexOf(currentStep);

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => (
          <div key={step} className="flex items-center">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
              index <= currentIndex 
                ? 'bg-error-600 border-error-600 text-white' 
                : 'border-gray-300 text-gray-400'
            }`}>
              {index < currentIndex ? (
                <CheckCircle className="h-5 w-5" />
              ) : (
                <span className="text-sm font-medium">{index + 1}</span>
              )}
            </div>
            <span className={`ml-2 text-sm font-medium ${
              index <= currentIndex ? 'text-gray-900' : 'text-gray-400'
            }`}>
              {STEP_LABELS[step]}
            </span>
            {index < steps.length - 1 && (
              <div className={`w-12 h-0.5 mx-4 ${
                index < currentIndex ? 'bg-error-600' : 'bg-gray-300'
              }`} />
            )}
          </div>
        ))}
      </div>
    );
  };

  if (!component) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="btn-outline mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </button>
              <div className="flex items-center">
                <Server className="h-8 w-8 text-error-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    卸载 {component.name}
                  </h1>
                  <p className="text-sm text-gray-500">按照步骤完成组件卸载</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderStepIndicator()}

        <div className="card">
          <div className="card-body">
            {/* 配置步骤 */}
            {currentStep === UninstallStep.CONFIGURE && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  卸载配置
                </h3>
                
                <div className="space-y-6">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                      <p className="text-sm text-yellow-800">
                        <strong>警告：</strong>卸载操作将永久删除组件，请谨慎操作。建议在卸载前进行数据备份。
                      </p>
                    </div>
                  </div>

                  {/* 组件信息 */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">组件信息</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">组件名称：</span>
                        <span className="font-medium">{component.name}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">组件类型：</span>
                        <span className="font-medium">{component.type}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">版本：</span>
                        <span className="font-medium">{component.version}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">状态：</span>
                        <span className="font-medium">{component.status}</span>
                      </div>
                    </div>
                  </div>

                  {/* 删除选项 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">删除选项</h4>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={uninstallOptions.removeConfig}
                          onChange={(e) => handleOptionChange('removeConfig', e.target.checked)}
                          className="form-checkbox"
                        />
                        <SettingsIcon className="h-4 w-4 ml-2 mr-2 text-gray-500" />
                        <span className="text-sm">删除配置文件</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={uninstallOptions.removeData}
                          onChange={(e) => handleOptionChange('removeData', e.target.checked)}
                          className="form-checkbox"
                        />
                        <Database className="h-4 w-4 ml-2 mr-2 text-gray-500" />
                        <span className="text-sm">删除数据文件</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={uninstallOptions.removeLogs}
                          onChange={(e) => handleOptionChange('removeLogs', e.target.checked)}
                          className="form-checkbox"
                        />
                        <FileText className="h-4 w-4 ml-2 mr-2 text-gray-500" />
                        <span className="text-sm">删除日志文件</span>
                      </label>
                    </div>
                  </div>

                  {/* 备份选项 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">备份选项</h4>
                    <label className="flex items-center mb-3">
                      <input
                        type="checkbox"
                        checked={uninstallOptions.backup}
                        onChange={(e) => handleOptionChange('backup', e.target.checked)}
                        className="form-checkbox"
                      />
                      <span className="ml-2 text-sm">在卸载前进行数据备份</span>
                    </label>

                    {uninstallOptions.backup && (
                      <div className="ml-6 space-y-3 border-l-2 border-gray-200 pl-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700 mb-2">备份内容：</p>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={uninstallOptions.backupConfig}
                                onChange={(e) => handleOptionChange('backupConfig', e.target.checked)}
                                className="form-checkbox"
                              />
                              <span className="ml-2 text-sm">配置文件</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={uninstallOptions.backupData}
                                onChange={(e) => handleOptionChange('backupData', e.target.checked)}
                                className="form-checkbox"
                              />
                              <span className="ml-2 text-sm">数据文件</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={uninstallOptions.backupLogs}
                                onChange={(e) => handleOptionChange('backupLogs', e.target.checked)}
                                className="form-checkbox"
                              />
                              <span className="ml-2 text-sm">日志文件</span>
                            </label>
                          </div>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-700 mb-2">备份方式：</p>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="backupType"
                                value="local"
                                checked={uninstallOptions.backupType === 'local'}
                                onChange={(e) => handleOptionChange('backupType', e.target.value)}
                                className="form-radio"
                              />
                              <HardDrive className="h-4 w-4 ml-2 mr-2 text-gray-500" />
                              <span className="text-sm">本地下载</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="backupType"
                                value="minio"
                                checked={uninstallOptions.backupType === 'minio'}
                                onChange={(e) => handleOptionChange('backupType', e.target.value)}
                                className="form-radio"
                              />
                              <Cloud className="h-4 w-4 ml-2 mr-2 text-gray-500" />
                              <span className="text-sm">MinIO 对象存储</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="backupType"
                                value="ftp"
                                checked={uninstallOptions.backupType === 'ftp'}
                                onChange={(e) => handleOptionChange('backupType', e.target.value)}
                                className="form-radio"
                              />
                              <Wifi className="h-4 w-4 ml-2 mr-2 text-gray-500" />
                              <span className="text-sm">FTP 服务器</span>
                            </label>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => router.back()}
                    className="btn-outline"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleNext}
                    className="btn-error"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {uninstallOptions.backup ? '下一步' : '开始卸载'}
                  </button>
                </div>
              </div>
            )}

            {/* 备份步骤 */}
            {currentStep === UninstallStep.BACKUP && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  备份配置
                </h3>

                <div className="space-y-6">
                  {uninstallOptions.backupType === 'minio' && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">MinIO 配置</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">服务端点</label>
                          <input
                            type="text"
                            placeholder="http://localhost:9000"
                            value={backupConfig.minio?.endpoint || ''}
                            onChange={(e) => handleBackupConfigChange('minio', 'endpoint', e.target.value)}
                            className="form-input"
                          />
                        </div>
                        <div>
                          <label className="form-label">存储桶</label>
                          <input
                            type="text"
                            placeholder="backup-bucket"
                            value={backupConfig.minio?.bucket || ''}
                            onChange={(e) => handleBackupConfigChange('minio', 'bucket', e.target.value)}
                            className="form-input"
                          />
                        </div>
                        <div>
                          <label className="form-label">访问密钥</label>
                          <input
                            type="text"
                            placeholder="Access Key"
                            value={backupConfig.minio?.accessKey || ''}
                            onChange={(e) => handleBackupConfigChange('minio', 'accessKey', e.target.value)}
                            className="form-input"
                          />
                        </div>
                        <div>
                          <label className="form-label">密钥</label>
                          <input
                            type="password"
                            placeholder="Secret Key"
                            value={backupConfig.minio?.secretKey || ''}
                            onChange={(e) => handleBackupConfigChange('minio', 'secretKey', e.target.value)}
                            className="form-input"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {uninstallOptions.backupType === 'ftp' && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">FTP 配置</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">主机地址</label>
                          <input
                            type="text"
                            placeholder="ftp.example.com"
                            value={backupConfig.ftp?.host || ''}
                            onChange={(e) => handleBackupConfigChange('ftp', 'host', e.target.value)}
                            className="form-input"
                          />
                        </div>
                        <div>
                          <label className="form-label">端口</label>
                          <input
                            type="number"
                            placeholder="21"
                            value={backupConfig.ftp?.port || ''}
                            onChange={(e) => handleBackupConfigChange('ftp', 'port', parseInt(e.target.value))}
                            className="form-input"
                          />
                        </div>
                        <div>
                          <label className="form-label">用户名</label>
                          <input
                            type="text"
                            placeholder="username"
                            value={backupConfig.ftp?.username || ''}
                            onChange={(e) => handleBackupConfigChange('ftp', 'username', e.target.value)}
                            className="form-input"
                          />
                        </div>
                        <div>
                          <label className="form-label">密码</label>
                          <input
                            type="password"
                            placeholder="password"
                            value={backupConfig.ftp?.password || ''}
                            onChange={(e) => handleBackupConfigChange('ftp', 'password', e.target.value)}
                            className="form-input"
                          />
                        </div>
                        <div className="col-span-2">
                          <label className="form-label">远程路径</label>
                          <input
                            type="text"
                            placeholder="/backup/"
                            value={backupConfig.ftp?.path || ''}
                            onChange={(e) => handleBackupConfigChange('ftp', 'path', e.target.value)}
                            className="form-input"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {uninstallOptions.backupType === 'local' && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <Download className="h-5 w-5 text-blue-600 mr-2" />
                        <p className="text-sm text-blue-800">
                          备份文件将打包下载到本地，请确保有足够的存储空间。
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => setCurrentStep(UninstallStep.CONFIGURE)}
                    className="btn-outline"
                  >
                    上一步
                  </button>
                  <button
                    onClick={handleUninstall}
                    disabled={loading}
                    className="btn-error"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    开始卸载
                  </button>
                </div>
              </div>
            )}

            {/* 卸载步骤 */}
            {currentStep === UninstallStep.UNINSTALL && (
              <div>
                <div className="text-center">
                  <Loader2 className="h-16 w-16 text-error-600 mx-auto mb-4 animate-spin" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    正在卸载组件...
                  </h3>
                  <p className="text-gray-600 mb-6">
                    请稍候，正在执行卸载操作
                  </p>

                  <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div
                      className="bg-error-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uninstallProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-500">{uninstallProgress.toFixed(0)}% 完成</p>
                </div>
              </div>
            )}

            {/* 完成步骤 */}
            {currentStep === UninstallStep.COMPLETE && (
              <div>
                <div className="text-center">
                  <CheckCircle className="h-16 w-16 text-success-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    组件卸载成功！
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {component.name} 已成功卸载
                  </p>
                </div>

                <div className="flex justify-center space-x-4">
                  <button
                    onClick={() => router.push('/')}
                    className="btn-primary"
                  >
                    返回首页
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
