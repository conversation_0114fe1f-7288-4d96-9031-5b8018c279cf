package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 租户配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_tenant_config")
@Schema(description = "租户配置")
@TableName("sys_tenant_config")
public class SysTenantConfig extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 配置键
     */
    @Column(name = "config_key", length = 100)
    @Schema(description = "配置键", example = "system.theme")
    @NotBlank(message = "配置键不能为空")
    @Size(min = 1, max = 100, message = "配置键长度必须在1-100个字符之间")
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @Column(name = "config_value", length = 2000)
    @Schema(description = "配置值")
    @Size(max = 2000, message = "配置值长度不能超过2000个字符")
    @TableField("config_value")
    private String configValue;

    /**
     * 配置名称
     */
    @Column(name = "config_name", length = 100)
    @Schema(description = "配置名称", example = "系统主题")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    @TableField("config_name")
    private String configName;

    /**
     * 配置描述
     */
    @Column(name = "config_desc", length = 500)
    @Schema(description = "配置描述")
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    @TableField("config_desc")
    private String configDesc;

    /**
     * 配置类型
     */
    @Column(name = "config_type", length = 20)
    @Schema(description = "配置类型", example = "string", allowableValues = {"string", "number", "boolean", "json"})
    @Size(max = 20, message = "配置类型长度不能超过20个字符")
    @TableField("config_type")
    private String configType;

    /**
     * 是否系统配置
     */
    @Column(name = "is_system")
    @Schema(description = "是否系统配置")
    @TableField("is_system")
    private Boolean isSystem;

    /**
     * 是否加密
     */
    @Column(name = "is_encrypted")
    @Schema(description = "是否加密")
    @TableField("is_encrypted")
    private Boolean isEncrypted;

    /**
     * 排序号
     */
    @Column(name = "sort_order")
    @Schema(description = "排序号", example = "1")
    @TableField("sort_order")
    private Integer sortOrder;

    // Getters and Setters
    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigDesc() {
        return configDesc;
    }

    public void setConfigDesc(String configDesc) {
        this.configDesc = configDesc;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public Boolean getIsEncrypted() {
        return isEncrypted;
    }

    public void setIsEncrypted(Boolean isEncrypted) {
        this.isEncrypted = isEncrypted;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "SysTenantConfig{" +
                "configKey='" + configKey + '\'' +
                ", configValue='" + configValue + '\'' +
                ", configName='" + configName + '\'' +
                ", configDesc='" + configDesc + '\'' +
                ", configType='" + configType + '\'' +
                ", isSystem=" + isSystem +
                ", isEncrypted=" + isEncrypted +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
