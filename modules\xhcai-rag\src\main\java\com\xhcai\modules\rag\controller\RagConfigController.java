package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.config.RagProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * RAG配置管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "RAG配置管理", description = "RAG模块配置信息的查询和管理")
@RestController
@RequestMapping("/api/rag")
@Validated
public class RagConfigController {

    private static final Logger log = LoggerFactory.getLogger(RagConfigController.class);

    @Autowired
    private RagProperties ragProperties;

    @Operation(summary = "获取RAG配置信息", description = "获取RAG模块的配置信息")
    @GetMapping("/config")
    @RequiresPermissions("rag:config:view")
    public Result<Map<String, Object>> getRagConfig() {
        log.info("获取RAG配置信息");

        Map<String, Object> config = new HashMap<>();
        
        // 文档配置
        Map<String, Object> documentConfig = new HashMap<>();
        documentConfig.put("maxSize", ragProperties.getDocument().getMaxSize());
        documentConfig.put("allowedTypes", ragProperties.getDocument().getAllowedTypes());
        documentConfig.put("chunkSize", ragProperties.getDocument().getChunkSize());
        documentConfig.put("chunkOverlap", ragProperties.getDocument().getChunkOverlap());
        documentConfig.put("maxChunks", ragProperties.getDocument().getMaxChunks());
        documentConfig.put("processingTimeout", ragProperties.getDocument().getProcessingTimeout());
        
        // 限制配置
        Map<String, Object> limitsConfig = new HashMap<>();
        limitsConfig.put("maxDatasetsPerTenant", ragProperties.getLimits().getMaxDatasetsPerTenant());
        limitsConfig.put("maxDocumentsPerDataset", ragProperties.getLimits().getMaxDocumentsPerDataset());
        limitsConfig.put("maxTokensPerDay", ragProperties.getLimits().getMaxTokensPerDay());
        limitsConfig.put("maxRetrievalsPerDay", ragProperties.getLimits().getMaxRetrievalsPerDay());
        limitsConfig.put("maxDocumentSize", ragProperties.getLimits().getMaxDocumentSize());
        limitsConfig.put("maxUploadFiles", ragProperties.getLimits().getMaxUploadFiles());
        
        config.put("document", documentConfig);
        config.put("limits", limitsConfig);

        return Result.success(config);
    }
}
