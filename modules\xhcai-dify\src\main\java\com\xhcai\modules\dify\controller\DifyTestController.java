package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.config.DifyWebClientConfig;
import com.xhcai.modules.dify.controller.DifyChatController.TestChatRequest;
import com.xhcai.modules.dify.dto.chat.DifyChatRequestDTO;
import com.xhcai.modules.dify.service.DifyRawStreamService;
import com.xhcai.modules.dify.service.IDifyChatService;
import com.xhcai.modules.dify.service.IDifyAuthService;
import com.xhcai.modules.dify.service.IDifyMultiPlatformAuthService;
import com.xhcai.common.security.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Dify 测试控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Tag(name = "Dify测试", description = "Dify连接测试相关接口")
@RestController
@RequestMapping("/api/dify/test")
public class DifyTestController {

    private static final Logger log = LoggerFactory.getLogger(DifyTestController.class);

    @Autowired
    private DifyConfig difyConfig;


    @Autowired
    private DifyRawStreamService difyRawStreamService;

    @Autowired
    private IDifyAuthService difyAuthService;

    @Autowired
    private IDifyMultiPlatformAuthService difyMultiPlatformAuthService;

    /**
     * 测试 Dify 认证登录
     */
    @Operation(summary = "测试Dify认证登录")
    @GetMapping("/auth/login")
    public Result<Map<String, Object>> testAuthLogin() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始测试Dify认证登录...");

            // 直接调用登录方法
            var loginResponse = difyAuthService.login().block();

            if (loginResponse != null && loginResponse.getAccessToken() != null) {
                String accessToken = loginResponse.getAccessToken();
                log.info("Dify认证登录测试成功，获取到访问令牌: {}...",
                        accessToken.substring(0, Math.min(20, accessToken.length())));
                result.put("success", true);
                result.put("message", "认证登录成功");
                result.put("accessToken", accessToken.substring(0, Math.min(20, accessToken.length())) + "...");
            } else {
                log.error("Dify认证登录测试失败，未获取到访问令牌");
                result.put("success", false);
                result.put("message", "认证登录失败，未获取到访问令牌");
            }

            return Result.success(result);
        } catch (Exception e) {
            log.error("Dify认证登录测试异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "认证登录异常: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            return Result.success(result);
        }
    }

    /**
     * 测试 Dify 获取有效令牌
     */
    @Operation(summary = "测试Dify获取有效令牌")
    @GetMapping("/auth/token")
    public Result<Map<String, Object>> testGetValidToken() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始测试Dify获取有效令牌...");

            // 调用获取有效令牌方法
            String accessToken = difyAuthService.getValidAccessToken().block();

            if (accessToken != null) {
                log.info("Dify获取有效令牌测试成功，获取到访问令牌: {}...",
                        accessToken.substring(0, Math.min(20, accessToken.length())));
                result.put("success", true);
                result.put("message", "获取有效令牌成功");
                result.put("accessToken", accessToken.substring(0, Math.min(20, accessToken.length())) + "...");
            } else {
                log.error("Dify获取有效令牌测试失败，未获取到访问令牌");
                result.put("success", false);
                result.put("message", "获取有效令牌失败，未获取到访问令牌");
            }

            return Result.success(result);
        } catch (Exception e) {
            log.error("Dify获取有效令牌测试异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取有效令牌异常: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            return Result.success(result);
        }
    }

    /**
     * 清除 Redis 中的 Dify 令牌
     */
    @Operation(summary = "清除Redis中的Dify令牌")
    @DeleteMapping("/auth/clear")
    public Result<Map<String, Object>> clearTokens() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始清除Redis中的Dify令牌...");

            // 调用清除令牌方法
            difyAuthService.clearTokens().block();

            log.info("Redis中的Dify令牌清除成功");
            result.put("success", true);
            result.put("message", "令牌清除成功");

            return Result.success(result);
        } catch (Exception e) {
            log.error("清除Redis中的Dify令牌异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "令牌清除异常: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            return Result.success(result);
        }
    }

    /**
     * 测试 Dify 连接
     */
    @Operation(summary = "测试Dify连接")
    @GetMapping("/connection")
    public Result<Map<String, Object>> testConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试Dify连接 - baseUrl: {}",
                    difyConfig.getBaseUrl());
            
            // 创建简单的WebClient进行测试
            WebClient testClient = WebClient.builder()
                    .baseUrl(difyConfig.getBaseUrl())
                    .build();
            
            // 构建测试请求（与 curl 示例保持一致）
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("inputs", new HashMap<>());
            requestBody.put("query", "怎么上传代码");
            requestBody.put("response_mode", "blocking");
            requestBody.put("conversation_id", "");
            requestBody.put("user", "abc-123");
            requestBody.put("files", new java.util.ArrayList<>());
            
            log.info("发送测试请求: {}", requestBody);

            // 使用硬编码的 API Key 进行测试
            String testApiKey = "app-HZWa3wyLD5KZVwbksobWe9nk";
            log.info("使用硬编码API Key测试: {}", testApiKey.substring(0, Math.min(10, testApiKey.length())) + "...");

            String response = testClient.post()
                    .uri("/v1/chat-messages")
                    .header("Authorization", "Bearer " + testApiKey)
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .onStatus(status -> status.is4xxClientError(), clientResponse -> {
                        log.error("4xx错误: {}", clientResponse.statusCode());
                        return clientResponse.bodyToMono(String.class)
                                .map(body -> {
                                    log.error("错误响应: {}", body);
                                    return new RuntimeException("4xx错误: " + clientResponse.statusCode() + ", body: " + body);
                                });
                    })
                    .onStatus(status -> status.is5xxServerError(), serverResponse -> {
                        log.error("5xx错误: {}", serverResponse.statusCode());
                        return serverResponse.bodyToMono(String.class)
                                .map(body -> {
                                    log.error("错误响应: {}", body);
                                    return new RuntimeException("5xx错误: " + serverResponse.statusCode() + ", body: " + body);
                                });
                    })
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            
            log.info("Dify响应: {}", response);
            
            result.put("success", true);
            result.put("message", "连接成功");
            result.put("response", response);
            result.put("config", Map.of(
                    "baseUrl", difyConfig.getBaseUrl(),
                    "sslEnabled", difyConfig.isSslEnabled()
            ));
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("Dify连接测试失败", e);
            
            result.put("success", false);
            result.put("message", "连接失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            result.put("config", Map.of(
                    "baseUrl", difyConfig.getBaseUrl(),
                    "sslEnabled", difyConfig.isSslEnabled()
            ));
            
            return Result.success(result);
        }
    }

    /**
     * 测试原始 curl 请求
     */


    /**
     * 获取配置信息


    /**
     * 测试原始 SSE 流 - 直接调用 Dify API
     */
    @Operation(summary = "测试原始SSE流")
    @PostMapping(value = "/stream/raw", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testRawStream(@RequestBody TestChatRequest request) {
        log.info("测试原始SSE流: {}", request);

        // 使用硬编码的 API Key（测试用）
        String testApiKey = "app-HZWa3wyLD5KZVwbksobWe9nk";

        // 使用 DifyRawStreamService 创建原始 SSE 流
        return difyRawStreamService.createRawSseStream(
                request.getQuery(),
                request.getUser() != null ? request.getUser() : "test-user",
                request.getConversationId(),
                testApiKey
        );
    }

    /**
     * 测试原始 SSE 流 - 使用写死的配置
     */
    @Operation(summary = "测试原始SSE流-写死配置")
    @PostMapping(value = "/stream/raw/default", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testRawStreamDefault(@RequestBody TestChatRequest request) {
        log.info("测试原始SSE流(写死配置): {}", request);

        // 使用写死的 API Key
        String testApiKey = "app-HZWa3wyLD5KZVwbksobWe9nk";
        return difyRawStreamService.createRawSseStream(
                request.getQuery(),
                request.getUser() != null ? request.getUser() : "test-user",
                request.getConversationId(),
                testApiKey
        );
    }

    /**
     * 获取原始数据流 - 不包装为 SSE
     */
    @Operation(summary = "获取原始数据流")
    @PostMapping(value = "/stream/raw/data", produces = MediaType.TEXT_PLAIN_VALUE)
    public Mono<String> getRawDataStream(@RequestBody TestChatRequest request) {
        log.info("获取原始数据流: {}", request);

        // 使用硬编码的 API Key（测试用）
        String testApiKey = "app-HZWa3wyLD5KZVwbksobWe9nk";

        return difyRawStreamService.getRawDataStream(
                request.getQuery(),
                request.getUser() != null ? request.getUser() : "test-user",
                request.getConversationId(),
                testApiKey
        ).collectList()
         .map(lines -> String.join("\n", lines));
    }


    /**
     * 简单的 SSE 测试接口
     */
    @Operation(summary = "简单SSE测试")
    @PostMapping(value = "/stream/simple", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testSimpleStream(@RequestBody TestChatRequest request) {
        log.info("简单SSE测试: {}", request);

        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        CompletableFuture.runAsync(() -> {
            try {
                // 发送开始事件
                emitter.send(SseEmitter.event()
                        .data("开始简单SSE测试...")
                        .name("start"));

                Thread.sleep(1000);

                // 发送一些测试数据
                String[] messages = {
                    "Hello",
                    " there",
                    "! How",
                    " can",
                    " I",
                    " help",
                    " you",
                    " today",
                    "?"
                };

                for (String message : messages) {
                    emitter.send(SseEmitter.event()
                            .data(message)
                            .name("message"));
                    Thread.sleep(500); // 模拟流式响应
                }

                // 发送结束事件
                emitter.send(SseEmitter.event()
                        .data("简单SSE测试完成")
                        .name("complete"));

                emitter.complete();

            } catch (Exception e) {
                log.error("简单SSE测试失败", e);
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 测试获取当前用户信息
     */
    @Operation(summary = "测试获取当前用户信息")
    @GetMapping("/current-user")
    public Result<Map<String, Object>> getCurrentUser() {
        Map<String, Object> result = new HashMap<>();

        try {
            var currentUser = SecurityUtils.getCurrentUserSafely();
            if (currentUser != null) {
                result.put("success", true);
                result.put("userId", currentUser.getUserId());
                result.put("username", currentUser.getUsername());
                result.put("message", "获取用户信息成功");
            } else {
                result.put("success", false);
                result.put("message", "未获取到用户信息，可能未登录");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            result.put("success", false);
            result.put("message", "获取用户信息失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            return Result.success(result);
        }
    }

    /**
     * 测试获取平台访问令牌
     */
    @Operation(summary = "测试获取平台访问令牌")
    @GetMapping("/platform/token/{platformId}")
    public Mono<Result<Map<String, Object>>> getAccessToken(@PathVariable String platformId) {
        log.info("测试获取平台{}的访问令牌", platformId);

        return difyMultiPlatformAuthService.getCurrentUserValidAccessToken(platformId)
                .map(token -> {
                    Map<String, Object> result = new HashMap<>();
                    if (token != null && !token.trim().isEmpty()) {
                        result.put("success", true);
                        result.put("message", "令牌获取成功");
                        result.put("tokenLength", token.length());
                        result.put("tokenPreview", token.substring(0, Math.min(20, token.length())) + "...");
                    } else {
                        result.put("success", false);
                        result.put("message", "令牌为空");
                    }
                    return Result.success(result);
                })
                .onErrorResume(e -> {
                    log.error("获取平台{}访问令牌失败", platformId, e);
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", false);
                    result.put("message", "获取令牌失败: " + e.getMessage());
                    result.put("error", e.getClass().getSimpleName());
                    return Mono.just(Result.success(result));
                });
    }

    /**
     * 测试令牌刷新
     */
    @Operation(summary = "测试令牌刷新")
    @PostMapping("/platform/refresh/{platformId}")
    public Mono<Result<Map<String, Object>>> refreshToken(@PathVariable String platformId) {
        log.info("测试刷新平台{}的令牌", platformId);

        return difyMultiPlatformAuthService.handleCurrentUserUnauthorized(platformId)
                .map(newToken -> {
                    Map<String, Object> result = new HashMap<>();
                    if (newToken != null && !newToken.trim().isEmpty()) {
                        result.put("success", true);
                        result.put("message", "令牌刷新成功");
                        result.put("tokenLength", newToken.length());
                        result.put("tokenPreview", newToken.substring(0, Math.min(20, newToken.length())) + "...");
                    } else {
                        result.put("success", false);
                        result.put("message", "刷新后令牌为空");
                    }
                    return Result.success(result);
                })
                .onErrorResume(e -> {
                    log.error("刷新平台{}令牌失败", platformId, e);
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", false);
                    result.put("message", "令牌刷新失败: " + e.getMessage());
                    result.put("error", e.getClass().getSimpleName());
                    return Mono.just(Result.success(result));
                });
    }

    /**
     * 清除平台令牌
     */
    @Operation(summary = "清除平台令牌")
    @DeleteMapping("/platform/clear/{platformId}")
    public Result<Map<String, Object>> clearPlatformTokens(@PathVariable String platformId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("清除平台{}的令牌", platformId);
            difyMultiPlatformAuthService.clearCurrentUserTokenCache(platformId);

            result.put("success", true);
            result.put("message", "平台令牌清除成功");
            result.put("platformId", platformId);
            log.info("平台{}的令牌清除成功", platformId);

            return Result.success(result);
        } catch (Exception e) {
            log.error("清除平台{}令牌失败", platformId, e);
            result.put("success", false);
            result.put("message", "清除令牌失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            result.put("platformId", platformId);
            return Result.success(result);
        }
    }

    /**
     * 清除所有平台令牌
     */
    @Operation(summary = "清除所有平台令牌")
    @DeleteMapping("/platform/clear-all")
    public Result<Map<String, Object>> clearAllPlatformTokens() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("清除所有平台的令牌");

            // 这里可以添加清除所有平台令牌的逻辑
            // 由于没有直接的方法，我们可以通过Redis操作来实现
            var currentUser = SecurityUtils.getCurrentUserSafely();
            if (currentUser != null && currentUser.getUserId() != null) {
                String userId = currentUser.getUserId();
                log.info("为用户{}清除所有平台令牌", userId);

                // 注意：这里需要实际的清除逻辑，可能需要调用Redis服务
                // 暂时返回成功状态
                result.put("success", true);
                result.put("message", "所有平台令牌清除成功");
                result.put("userId", userId);
            } else {
                result.put("success", false);
                result.put("message", "无法获取当前用户信息");
            }

            return Result.success(result);
        } catch (Exception e) {
            log.error("清除所有平台令牌失败", e);
            result.put("success", false);
            result.put("message", "清除所有令牌失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            return Result.success(result);
        }
    }

    /**
     * 强制刷新平台令牌（先清除再获取新令牌）
     */
    @Operation(summary = "强制刷新平台令牌")
    @PostMapping("/platform/force-refresh/{platformId}")
    public Mono<Result<Map<String, Object>>> forceRefreshToken(@PathVariable String platformId) {
        log.info("强制刷新平台{}的令牌", platformId);

        return Mono.fromRunnable(() -> {
                    // 先清除令牌缓存
                    difyMultiPlatformAuthService.clearCurrentUserTokenCache(platformId);
                    log.info("已清除平台{}的令牌缓存", platformId);
                })
                .then(difyMultiPlatformAuthService.getCurrentUserValidAccessToken(platformId))
                .map(newToken -> {
                    Map<String, Object> result = new HashMap<>();
                    if (newToken != null && !newToken.trim().isEmpty()) {
                        result.put("success", true);
                        result.put("message", "强制刷新令牌成功");
                        result.put("tokenLength", newToken.length());
                        result.put("tokenPreview", newToken.substring(0, Math.min(20, newToken.length())) + "...");
                    } else {
                        result.put("success", false);
                        result.put("message", "强制刷新后令牌为空");
                    }
                    result.put("platformId", platformId);
                    return Result.success(result);
                })
                .onErrorResume(e -> {
                    log.error("强制刷新平台{}令牌失败", platformId, e);
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", false);
                    result.put("message", "强制刷新令牌失败: " + e.getMessage());
                    result.put("error", e.getClass().getSimpleName());
                    result.put("platformId", platformId);
                    return Mono.just(Result.success(result));
                });
    }
}
