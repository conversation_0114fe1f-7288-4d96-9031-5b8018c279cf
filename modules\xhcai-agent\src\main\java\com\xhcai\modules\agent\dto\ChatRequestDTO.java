package com.xhcai.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 聊天请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "聊天请求信息")
public class ChatRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 对话ID（可选，用于继续已有对话）
     */
    @Schema(description = "对话ID", example = "conv_001")
    @Size(max = 36, message = "对话ID长度不能超过36个字符")
    private String conversationId;

    /**
     * 用户消息内容
     */
    @Schema(description = "用户消息内容", example = "你好，我想了解一下产品信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "消息内容不能为空")
    private String message;

    /**
     * 是否流式响应
     */
    @Schema(description = "是否流式响应", example = "false")
    private Boolean stream = false;

    /**
     * 会话ID（外部系统）
     */
    @Schema(description = "会话ID", example = "session_12345")
    @Size(max = 100, message = "会话ID长度不能超过100个字符")
    private String sessionId;

    /**
     * 上下文变量
     */
    @Schema(description = "上下文变量", example = "{\"user_name\":\"张三\",\"user_level\":\"VIP\"}")
    private Map<String, Object> variables;

    /**
     * 文件列表（用于文档问答）
     */
    @Schema(description = "文件列表")
    private List<FileInfo> files;

    /**
     * 扩展信息
     */
    @Schema(description = "扩展信息", example = "{\"source\":\"web\",\"device\":\"mobile\"}")
    private Map<String, Object> metadata;

    /**
     * 文件信息
     */
    @Schema(description = "文件信息")
    public static class FileInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 文件ID
         */
        @Schema(description = "文件ID", example = "file_001")
        private String fileId;

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "document.pdf")
        private String fileName;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "pdf")
        private String fileType;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小", example = "1024000")
        private Long fileSize;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://example.com/files/document.pdf")
        private String fileUrl;

        // Getters and Setters
        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }
    }

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public List<FileInfo> getFiles() {
        return files;
    }

    public void setFiles(List<FileInfo> files) {
        this.files = files;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
}
