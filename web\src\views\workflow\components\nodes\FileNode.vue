<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="file-node-content">
        <div class="file-info" v-if="data.config?.filePath || data.config?.outputPath">
          <div class="info-item" v-if="data.config?.filePath">
            <i class="fas fa-folder-open"></i>
            <span>{{ truncatePath(data.config.filePath!) }}</span>
          </div>
          <div class="info-item" v-if="data.config?.outputPath">
            <i class="fas fa-save"></i>
            <span>{{ truncatePath(data.config.outputPath!) }}</span>
          </div>
        </div>

        <p class="node-description">{{ getNodeDescription() }}</p>

        <div class="file-config" v-if="data.config">
          <div class="config-row" v-if="data.config.extractType">
            <span class="config-label">提取类型:</span>
            <span class="config-value">{{ data.config.extractType }}</span>
          </div>
          <div class="config-row" v-if="data.config.outputFormat">
            <span class="config-label">输出格式:</span>
            <span class="config-value">{{ data.config.outputFormat }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { getNodeByType } from '../../config/nodeLibrary'

// Props
interface FileNodeData {
  label?: string
  description?: string
  nodeType?: string  // 添加节点类型字段
  config?: {
    fileType?: string
    filePath?: string
    outputPath?: string
    format?: string
    extractType?: string
    outputFormat?: string
  }
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: FileNodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 方法
const getNodeDescription = () => {
  // 优先使用 nodeType，如果没有则使用 fileType，最后默认为 generate-ppt
  const nodeType = props.data.nodeType || props.data.config?.fileType || 'generate-ppt'
  const nodeConfig = getNodeByType(nodeType)
  return nodeConfig?.description || '文件处理节点'
}

const truncatePath = (path: string) => {
  if (path.length > 30) {
    return '...' + path.substring(path.length - 27)
  }
  return path
}
</script>

<style scoped>
.file-node-content {
  text-align: left;
}

.file-info {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #374151;
  margin-bottom: 6px;
  padding: 2px 0;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item i {
  width: 14px;
  text-align: center;
  color: #f59e0b;
  font-size: 11px;
}

.info-item span {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #fffbeb;
  color: #78350f;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #fed7aa;
  font-weight: 500;
}

.node-description {
  font-size: 13px;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.5;
  font-weight: 400;
}

.file-config {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.config-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.config-row:last-child {
  border-bottom: none;
}

.config-label {
  font-weight: 600;
  color: #1f2937;
  flex-shrink: 0;
}

.config-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #fffbeb;
  color: #92400e;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #fed7aa;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}
</style>
