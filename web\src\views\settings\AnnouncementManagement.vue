<template>
  <div class="announcements-management space-y-6">
    <!-- 发布新公告 -->
    <div class="announcement-form bg-white rounded-lg shadow-sm p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">发布新公告</h3>
      <div class="form-space space-y-4">
        <div class="form-group">
          <label class="form-label">公告标题</label>
          <input v-model="newAnnouncement.title" type="text" class="form-input" placeholder="输入公告标题" />
        </div>
        <div class="form-group">
          <label class="form-label">公告内容</label>
          <RichTextEditor
            v-model="newAnnouncement.content"
            placeholder="输入公告内容..."
          />
        </div>
        <div class="grid grid-cols-3 gap-4">
          <div class="form-group">
            <label class="form-label">公告类型</label>
            <select v-model="newAnnouncement.type" class="form-input">
              <option value="info">信息</option>
              <option value="warning">警告</option>
              <option value="success">成功</option>
              <option value="error">错误</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">优先级</label>
            <select v-model="newAnnouncement.priority" class="form-input">
              <option value="low">低</option>
              <option value="normal">普通</option>
              <option value="high">高</option>
              <option value="urgent">紧急</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">发布范围</label>
            <select v-model="newAnnouncement.scope" class="form-input">
              <option value="all">全部用户</option>
              <option value="unit">指定单位</option>
              <option value="role">指定角色</option>
            </select>
          </div>
        </div>
        <div v-if="newAnnouncement.scope === 'unit'" class="form-group">
          <label class="form-label">目标单位</label>
          <select v-model="newAnnouncement.targetUnits" multiple class="form-input">
            <option v-for="unit in units" :key="unit.id" :value="unit.id">{{ unit.name }}</option>
          </select>
        </div>
        <div v-if="newAnnouncement.scope === 'role'" class="form-group">
          <label class="form-label">目标角色</label>
          <div class="flex gap-4">
            <label class="flex items-center">
              <input v-model="newAnnouncement.targetRoles" value="admin" type="checkbox" class="form-checkbox mr-2" />
              <span>管理员</span>
            </label>
            <label class="flex items-center">
              <input v-model="newAnnouncement.targetRoles" value="designer" type="checkbox" class="form-checkbox mr-2" />
              <span>设计者</span>
            </label>
            <label class="flex items-center">
              <input v-model="newAnnouncement.targetRoles" value="user" type="checkbox" class="form-checkbox mr-2" />
              <span>使用者</span>
            </label>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div class="form-group">
            <label class="form-label">生效时间</label>
            <input v-model="newAnnouncement.startTime" type="datetime-local" class="form-input" />
          </div>
          <div class="form-group">
            <label class="form-label">失效时间</label>
            <input v-model="newAnnouncement.endTime" type="datetime-local" class="form-input" />
          </div>
        </div>
        <div class="form-actions">
          <button @click="publishAnnouncement" class="btn-primary">发布公告</button>
          <button @click="saveDraft" class="btn-secondary ml-3">保存草稿</button>
        </div>
      </div>
    </div>
    
    <!-- 公告列表 -->
    <div class="announcements-list bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">已发布公告</h3>
        <div class="flex items-center gap-4">
          <!-- 筛选器 -->
          <select v-model="filterType" class="form-input w-32">
            <option value="">全部类型</option>
            <option value="info">信息</option>
            <option value="warning">警告</option>
            <option value="success">成功</option>
            <option value="error">错误</option>
          </select>
          <select v-model="filterStatus" class="form-input w-32">
            <option value="">全部状态</option>
            <option value="active">生效中</option>
            <option value="expired">已过期</option>
            <option value="draft">草稿</option>
          </select>
        </div>
      </div>
      
      <div class="announcements space-y-3">
        <div
          v-for="announcement in filteredAnnouncements"
          :key="announcement.id"
          class="announcement-item p-4 border border-gray-200 rounded-lg"
        >
          <div class="announcement-header flex items-center justify-between mb-2">
            <div class="flex items-center gap-3">
              <h4 class="font-medium text-gray-800">{{ announcement.title }}</h4>
              <span
                class="priority-badge px-2 py-1 text-xs rounded-full"
                :class="{
                  'bg-gray-100 text-gray-600': announcement.priority === 'low',
                  'bg-blue-100 text-blue-600': announcement.priority === 'normal',
                  'bg-orange-100 text-orange-600': announcement.priority === 'high',
                  'bg-red-100 text-red-600': announcement.priority === 'urgent'
                }"
              >
                {{ getPriorityText(announcement.priority) }}
              </span>
            </div>
            <div class="announcement-meta flex items-center gap-3">
              <span
                class="type-badge px-2 py-1 text-xs rounded-full"
                :class="{
                  'bg-blue-100 text-blue-600': announcement.type === 'info',
                  'bg-yellow-100 text-yellow-600': announcement.type === 'warning',
                  'bg-green-100 text-green-600': announcement.type === 'success',
                  'bg-red-100 text-red-600': announcement.type === 'error'
                }"
              >
                {{ getTypeText(announcement.type) }}
              </span>
              <span
                class="status-badge px-2 py-1 text-xs rounded-full"
                :class="{
                  'bg-green-100 text-green-600': announcement.status === 'active',
                  'bg-gray-100 text-gray-600': announcement.status === 'expired',
                  'bg-yellow-100 text-yellow-600': announcement.status === 'draft'
                }"
              >
                {{ getStatusText(announcement.status) }}
              </span>
              <span class="text-xs text-gray-500">{{ announcement.publishTime }}</span>
            </div>
          </div>
          <div class="announcement-content text-sm text-gray-600 mb-3" v-html="announcement.content"></div>
          <div class="announcement-info text-xs text-gray-500 mb-3">
            <span>发布范围：{{ getScopeText(announcement.scope, announcement.targetUnits, announcement.targetRoles) }}</span>
            <span class="ml-4">生效时间：{{ announcement.startTime || '立即' }}</span>
            <span class="ml-4">失效时间：{{ announcement.endTime || '永久' }}</span>
          </div>
          <div class="announcement-actions flex gap-2">
            <button @click="editAnnouncement(announcement)" class="btn-secondary text-xs px-3 py-1">编辑</button>
            <button v-if="announcement.status === 'active'" @click="expireAnnouncement(announcement.id)" class="btn-secondary text-xs px-3 py-1">下线</button>
            <button v-if="announcement.status === 'draft'" @click="publishDraft(announcement.id)" class="btn-primary text-xs px-3 py-1">发布</button>
            <button @click="deleteAnnouncement(announcement.id)" class="text-red-500 text-xs px-3 py-1 hover:bg-red-50 rounded">删除</button>
          </div>
        </div>
      </div>

      <!-- 公告管理分页 -->
      <Pagination
        v-model:currentPage="announcementsPagination.currentPage"
        v-model:pageSize="announcementsPagination.pageSize"
        :total="filteredAnnouncements.length"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Pagination from '@/components/common/Pagination.vue'
import RichTextEditor from '@/components/RichTextEditor.vue'

// 单位列表
const units = ref([
  { id: 1, name: '技术部' },
  { id: 2, name: '产品部' },
  { id: 3, name: '运营部' },
  { id: 4, name: '客服部' }
])

// 筛选器
const filterType = ref('')
const filterStatus = ref('')

// 新公告数据
const newAnnouncement = ref({
  title: '',
  content: '',
  type: 'info',
  priority: 'normal',
  scope: 'all',
  targetUnits: [],
  targetRoles: [],
  startTime: '',
  endTime: ''
})

// 公告列表
const announcements = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于今晚22:00-24:00进行维护，期间可能影响服务使用。',
    type: 'warning',
    priority: 'high',
    scope: 'all',
    targetUnits: [],
    targetRoles: [],
    status: 'active',
    startTime: '2024-01-15 10:00',
    endTime: '2024-01-16 10:00',
    publishTime: '2024-01-15 10:30'
  },
  {
    id: 2,
    title: '新功能上线',
    content: '智能体管理功能已上线，欢迎体验使用。',
    type: 'success',
    priority: 'normal',
    scope: 'role',
    targetUnits: [],
    targetRoles: ['designer'],
    status: 'active',
    startTime: '',
    endTime: '',
    publishTime: '2024-01-14 14:20'
  }
])

// 分页数据
const announcementsPagination = ref({ currentPage: 1, pageSize: 10 })

// 过滤后的公告列表
const filteredAnnouncements = computed(() => {
  let filtered = announcements.value
  
  if (filterType.value) {
    filtered = filtered.filter(announcement => announcement.type === filterType.value)
  }
  
  if (filterStatus.value) {
    filtered = filtered.filter(announcement => announcement.status === filterStatus.value)
  }
  
  const start = (announcementsPagination.value.currentPage - 1) * announcementsPagination.value.pageSize
  const end = start + announcementsPagination.value.pageSize
  return filtered.slice(start, end)
})

// 辅助方法
const getPriorityText = (priority: string) => {
  const map: Record<string, string> = { low: '低', normal: '普通', high: '高', urgent: '紧急' }
  return map[priority] || priority
}

const getTypeText = (type: string) => {
  const map: Record<string, string> = { info: '信息', warning: '警告', success: '成功', error: '错误' }
  return map[type] || type
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = { active: '生效中', expired: '已过期', draft: '草稿' }
  return map[status] || status
}

const getScopeText = (scope: string, targetUnits: number[], targetRoles: string[]) => {
  if (scope === 'all') return '全部用户'
  if (scope === 'unit') {
    const unitNames = targetUnits.map(id => units.value.find(u => u.id === id)?.name).filter(Boolean)
    return `指定单位：${unitNames.join(', ')}`
  }
  if (scope === 'role') {
    const roleMap: Record<string, string> = { admin: '管理员', designer: '设计者', user: '使用者' }
    const roleNames = targetRoles.map(role => roleMap[role]).filter(Boolean)
    return `指定角色：${roleNames.join(', ')}`
  }
  return scope
}

// 公告管理方法
const publishAnnouncement = () => {
  if (!newAnnouncement.value.title || !newAnnouncement.value.content) {
    alert('请填写完整的公告信息')
    return
  }

  const announcement = {
    id: Date.now(),
    ...newAnnouncement.value,
    status: 'active',
    publishTime: new Date().toLocaleString('zh-CN')
  }

  announcements.value.unshift(announcement)
  resetForm()
  alert('公告发布成功')
}

const saveDraft = () => {
  if (!newAnnouncement.value.title) {
    alert('请至少填写公告标题')
    return
  }

  const announcement = {
    id: Date.now(),
    ...newAnnouncement.value,
    status: 'draft',
    publishTime: new Date().toLocaleString('zh-CN')
  }

  announcements.value.unshift(announcement)
  resetForm()
  alert('草稿保存成功')
}

const resetForm = () => {
  newAnnouncement.value = {
    title: '',
    content: '',
    type: 'info',
    priority: 'normal',
    scope: 'all',
    targetUnits: [],
    targetRoles: [],
    startTime: '',
    endTime: ''
  }
}

const editAnnouncement = (announcement: any) => {
  newAnnouncement.value = { ...announcement }
}

const expireAnnouncement = (id: number) => {
  if (confirm('确定要下线该公告吗？')) {
    const index = announcements.value.findIndex(a => a.id === id)
    if (index !== -1) {
      announcements.value[index].status = 'expired'
    }
  }
}

const publishDraft = (id: number) => {
  if (confirm('确定要发布该草稿吗？')) {
    const index = announcements.value.findIndex(a => a.id === id)
    if (index !== -1) {
      announcements.value[index].status = 'active'
      announcements.value[index].publishTime = new Date().toLocaleString('zh-CN')
    }
  }
}

const deleteAnnouncement = (id: number) => {
  if (confirm('确定要删除该公告吗？')) {
    announcements.value = announcements.value.filter(a => a.id !== id)
  }
}
</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');

.announcement-item {
  transition: all 0.3s ease;
}

.announcement-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
