<template>
  <div class="el-role-selector">
    <el-select
      v-model="selectedValue"
      :multiple="config.multiple"
      :clearable="config.clearable"
      :filterable="config.filterable"
      :placeholder="config.placeholder"
      :size="config.size"
      :disabled="config.disabled"
      :loading="loading"
      :remote="config.remote"
      :remote-method="config.remote ? handleRemoteSearch : undefined"
      :reserve-keyword="config.reserveKeyword"
      :no-data-text="config.noDataText"
      :no-match-text="config.noMatchText"
      :loading-text="config.loadingText"
      :teleported="config.teleported"
      :popper-class="config.popperClass"
      :fit-input-width="config.fitInputWidth"
      :suffix-icon="config.suffixIcon"
      :tag-type="config.tagType"
      :max-collapse-tags="config.maxCollapseTags"
      :collapse-tags-tooltip="config.collapseTagsTooltip"
      :effect="config.effect"
      value-key="value"
      @change="handleChange"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
      @clear="handleClear"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <el-option
        v-for="role in filteredRoles"
        :key="role.value"
        :label="role.label"
        :value="role.value"
        :disabled="role.disabled"
      >
        <div class="role-option">
          <div class="role-icon" :style="{ background: getRoleIconBg(role.roleKey) }">
            <i :class="getRoleIcon(role.roleKey)"></i>
          </div>
          <div class="role-content">
            <div class="role-name">{{ role.label }}</div>
            <div v-if="role.description" class="role-description">{{ role.description }}</div>
            <div v-if="showRoleExtra" class="role-extra">
              <span v-if="role.roleKey" class="role-key">{{ role.roleKey }}</span>
              <span v-if="role.userCount !== undefined" class="user-count">
                {{ role.userCount }}个用户
              </span>
            </div>
          </div>
          <div v-if="role.status !== undefined" class="role-status">
            <el-tag
              :type="getRoleStatusType(role.status)"
              size="small"
              effect="plain"
            >
              {{ getRoleStatusLabel(role.status) }}
            </el-tag>
          </div>
        </div>
      </el-option>
      
      <template #empty>
        <div class="empty-content">
          <el-icon size="48" color="#c0c4cc">
            <UserFilled />
          </el-icon>
          <p>{{ config.noDataText || '暂无角色数据' }}</p>
        </div>
      </template>
    </el-select>

    <!-- 已选择标签显示（多选模式下） -->
    <div v-if="config.multiple && showSelectedTags && hasSelection" class="selected-tags">
      <div class="tags-header">已选择的角色:</div>
      <div class="tags-content">
        <el-tag
          v-for="option in selectedOptions"
          :key="option.value"
          :type="config.tagType"
          :closable="!config.disabled"
          :size="config.size"
          @close="handleTagClose(option.value)"
        >
          <div class="tag-content">
            <div class="tag-icon" :style="{ background: getRoleIconBg(option.roleKey) }">
              <i :class="getRoleIcon(option.roleKey)"></i>
            </div>
            <span>{{ option.label }}</span>
          </div>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElSelect, ElOption, ElIcon, ElTag } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import { RoleAPI } from '@/api/system'
import type { 
  RoleSelectorOption, 
  SelectorConfig, 
  SelectorEmits 
} from './types'
import type { SysRoleVO } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludeRoleIds?: string[]
  showRoleExtra?: boolean
  showSelectedTags?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludeRoleIds: () => [],
  showRoleExtra: true,
  showSelectedTags: true
})

const emit = defineEmits<SelectorEmits>()

// 响应式数据
const loading = ref(false)
const roles = ref<RoleSelectorOption[]>([])
const selectedValue = ref<string | string[]>()
const selectedOptions = ref<RoleSelectorOption[]>([])
const searchKeyword = ref('')

// 默认配置
const defaultConfig: SelectorConfig = {
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择角色',
  size: 'default',
  disabled: false,
  remote: false,
  reserveKeyword: false,
  teleported: true,
  fitInputWidth: false,
  tagType: 'info',
  maxCollapseTags: 3,
  collapseTagsTooltip: true,
  effect: 'light',
  noDataText: '暂无角色数据',
  noMatchText: '无匹配数据',
  loadingText: '加载中...'
}

// 合并配置
const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 计算属性
const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedValue.value) && selectedValue.value.length > 0
  }
  return selectedValue.value !== undefined && selectedValue.value !== null && selectedValue.value !== ''
})

const filteredRoles = computed(() => {
  let result = roles.value

  // 状态过滤
  if (props.onlyEnabled) {
    result = result.filter(role => role.status === '0')
  }

  // 排除指定角色
  if (props.excludeRoleIds.length > 0) {
    result = result.filter(role => !props.excludeRoleIds.includes(role.value))
  }

  // 搜索过滤（非远程搜索模式）
  if (!config.value.remote && searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(role =>
      role.label.toLowerCase().includes(keyword) ||
      (role.roleKey && role.roleKey.toLowerCase().includes(keyword)) ||
      (role.description && role.description.toLowerCase().includes(keyword))
    )
  }

  return result
})

// 方法
const loadRoles = async (keyword?: string) => {
  try {
    loading.value = true
    const response = await RoleAPI.getRolePage({
      current: 1,
      size: 100,
      roleName: keyword,
      status: props.onlyEnabled ? '0' : undefined
    })
    const data = response.data?.records || []
    roles.value = transformRoleData(data)
  } catch (error) {
    console.error('加载角色列表失败:', error)
    roles.value = []
  } finally {
    loading.value = false
  }
}

const transformRoleData = (data: SysRoleVO[]): RoleSelectorOption[] => {
  return data.map(role => ({
    value: role.id,
    label: role.roleName,
    roleKey: role.roleKey,
    description: role.remark,
    status: role.status,
    userCount: role.userCount,
    disabled: role.status !== '0'
  }))
}

const getRoleIcon = (roleKey?: string) => {
  const iconMap: Record<string, string> = {
    'admin': 'fas fa-crown',
    'manager': 'fas fa-user-tie',
    'editor': 'fas fa-edit',
    'viewer': 'fas fa-eye',
    'analyst': 'fas fa-chart-line',
    'operator': 'fas fa-cogs'
  }
  return iconMap[roleKey || ''] || 'fas fa-user'
}

const getRoleIconBg = (roleKey?: string) => {
  const colorMap: Record<string, string> = {
    'admin': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
    'manager': 'linear-gradient(135deg, #4834d4 0%, #686de0 100%)',
    'editor': 'linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%)',
    'viewer': 'linear-gradient(135deg, #5f27cd 0%, #a55eea 100%)',
    'analyst': 'linear-gradient(135deg, #00d2d3 0%, #01a3a4 100%)',
    'operator': 'linear-gradient(135deg, #feca57 0%, #ff9ff3 100%)'
  }
  return colorMap[roleKey || ''] || 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)'
}

const getRoleStatusType = (status?: string | number) => {
  switch (status) {
    case '0': return 'success'
    case '1': return 'danger'
    default: return 'info'
  }
}

const getRoleStatusLabel = (status?: string | number) => {
  switch (status) {
    case '0': return '正常'
    case '1': return '禁用'
    default: return '未知'
  }
}

const findOptionsByValues = (values: string | string[]): RoleSelectorOption[] => {
  const targetValues = Array.isArray(values) ? values : (values ? [values] : [])
  return roles.value.filter(role => targetValues.includes(role.value))
}

// 事件处理
const handleChange = (value: string | string[]) => {
  selectedValue.value = value
  selectedOptions.value = findOptionsByValues(value)
  
  emit('update:modelValue', value)
  emit('change', value, selectedOptions.value.length === 1 ? selectedOptions.value[0] : selectedOptions.value)
  
  if (!config.value.multiple && selectedOptions.value.length === 1) {
    emit('select', value as string, selectedOptions.value[0])
  }
}

const handleVisibleChange = (visible: boolean) => {
  emit('visible-change', visible)
}

const handleRemoteSearch = (query: string) => {
  searchKeyword.value = query
  if (config.value.remote) {
    loadRoles(query)
  }
}

const handleRemoveTag = (value: string) => {
  emit('remove-tag', value)
  emit('remove', value)
}

const handleTagClose = (value: string) => {
  if (config.value.multiple && Array.isArray(selectedValue.value)) {
    const newValue = selectedValue.value.filter(v => v !== value)
    handleChange(newValue)
  }
}

const handleClear = () => {
  const clearValue = config.value.multiple ? [] : ''
  selectedValue.value = clearValue
  selectedOptions.value = []
  emit('update:modelValue', clearValue)
  emit('change', clearValue, null)
  emit('clear')
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue || (config.value.multiple ? [] : '')
  selectedOptions.value = findOptionsByValues(selectedValue.value)
}, { immediate: true })

// 组件挂载
onMounted(() => {
  loadRoles()
})

// 暴露方法
defineExpose({
  refresh: loadRoles,
  clearSelection: handleClear
})
</script>

<style scoped>
.el-role-selector {
  width: 100%;
}

.role-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  width: 100%;
}

.role-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.role-content {
  flex: 1;
  min-width: 0;
}

.role-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.role-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
  line-height: 1.4;
}

.role-extra {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: var(--el-text-color-placeholder);
}

.role-key {
  background: var(--el-fill-color-light);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

.user-count {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  padding: 2px 6px;
  border-radius: 4px;
}

.role-status {
  flex-shrink: 0;
}

.selected-tags {
  margin-top: 8px;
}

.tags-header {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag-icon {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
}

.empty-content p {
  margin: 8px 0 0 0;
  font-size: 14px;
}
</style>
