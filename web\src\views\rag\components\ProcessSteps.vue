<template>
  <div class="process-steps">
    <div class="steps-container">
      <div 
        v-for="(step, index) in steps" 
        :key="step.id"
        class="step-item"
        :class="{ 
          active: currentStep === step.id, 
          completed: step.id < currentStep,
          disabled: step.id > currentStep && !step.completed
        }"
      >
        <!-- 步骤圆圈 -->
        <div class="step-circle">
          <div class="step-number" v-if="!step.completed && step.id !== currentStep">
            {{ step.id }}
          </div>
          <div class="step-icon" v-else-if="step.completed">
            <i class="fas fa-check"></i>
          </div>
          <div class="step-loading" v-else-if="step.id === currentStep && step.loading">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <div class="step-number" v-else>
            {{ step.id }}
          </div>
        </div>

        <!-- 连接线 -->
        <div 
          v-if="index < steps.length - 1" 
          class="step-connector"
          :class="{ active: step.id < currentStep || step.completed }"
        ></div>

        <!-- 步骤内容 -->
        <div class="step-content">
          <div class="step-title">{{ step.title }}</div>
          <div class="step-description">{{ step.description }}</div>
          
          <!-- 步骤状态信息 -->
          <div class="step-status" v-if="step.status">
            <span class="status-text" :class="step.statusType">
              {{ step.status }}
            </span>
          </div>

          <!-- 进度条 -->
          <div class="step-progress" v-if="step.id === currentStep && step.progress !== undefined">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: step.progress + '%' }"></div>
            </div>
            <div class="progress-text">{{ step.progress }}%</div>
          </div>

          <!-- 错误信息 -->
          <div class="step-error" v-if="step.error">
            <i class="fas fa-exclamation-triangle"></i>
            {{ step.error }}
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="step-actions" v-if="showActions">
      <button 
        class="btn btn-secondary" 
        @click="$emit('prev')"
        :disabled="currentStep <= 1"
      >
        <i class="fas fa-arrow-left"></i>
        上一步
      </button>
      
      <button 
        class="btn btn-primary" 
        @click="$emit('next')"
        :disabled="!canProceed"
      >
        <span v-if="currentStep < steps.length">
          下一步
          <i class="fas fa-arrow-right"></i>
        </span>
        <span v-else>
          完成
          <i class="fas fa-check"></i>
        </span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 步骤数据接口
export interface ProcessStep {
  id: number
  title: string
  description: string
  completed?: boolean
  loading?: boolean
  progress?: number
  status?: string
  statusType?: 'info' | 'success' | 'warning' | 'error'
  error?: string
}

// Props
interface Props {
  steps: ProcessStep[]
  currentStep: number
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showActions: false
})

// Emits
defineEmits<{
  next: []
  prev: []
}>()

// 计算属性
const canProceed = computed(() => {
  const current = props.steps.find(step => step.id === props.currentStep)
  return current?.completed || current?.progress === 100
})
</script>

<style scoped>
.process-steps {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.steps-container {
  display: flex;
  align-items: flex-start;
  gap: 0;
  margin-bottom: 24px;
}

.step-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-height: 120px;
}

.step-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.step-item .step-circle {
  background: #e8f4fd;
  color: #7f8c8d;
  border: 2px solid #e8f4fd;
}

.step-item.active .step-circle {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: white;
  border: 2px solid #4a90e2;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.step-item.completed .step-circle {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: 2px solid #27ae60;
}

.step-item.disabled .step-circle {
  background: #f8f9fa;
  color: #bdc3c7;
  border: 2px solid #ecf0f1;
}

.step-number,
.step-icon,
.step-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-connector {
  position: absolute;
  top: 24px;
  left: calc(50% + 24px);
  right: calc(-50% + 24px);
  height: 2px;
  background: #e8f4fd;
  transition: all 0.3s ease;
  z-index: 1;
}

.step-connector.active {
  background: linear-gradient(90deg, #4a90e2, #357abd);
}

.step-content {
  text-align: center;
  max-width: 200px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.step-item.active .step-title {
  color: #4a90e2;
}

.step-item.completed .step-title {
  color: #27ae60;
}

.step-item.disabled .step-title {
  color: #bdc3c7;
}

.step-description {
  font-size: 14px;
  color: #7f8c8d;
  line-height: 1.4;
  margin-bottom: 8px;
}

.step-status {
  margin-top: 8px;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
}

.status-text.info {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
}

.status-text.success {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.status-text.warning {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.status-text.error {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.step-progress {
  margin-top: 12px;
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e8f4fd;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a90e2, #357abd);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #4a90e2;
  font-weight: 500;
}

.step-error {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-radius: 8px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.step-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e8f4fd;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: white;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #4a90e2;
  border: 1px solid #e1ecf4;
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .steps-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .step-item {
    flex-direction: row;
    align-items: center;
    text-align: left;
    min-height: auto;
    padding: 16px 0;
  }
  
  .step-circle {
    margin-bottom: 0;
    margin-right: 16px;
    flex-shrink: 0;
  }
  
  .step-connector {
    display: none;
  }
  
  .step-content {
    text-align: left;
    max-width: none;
    flex: 1;
  }
  
  .step-actions {
    flex-direction: column;
  }
}
</style>
