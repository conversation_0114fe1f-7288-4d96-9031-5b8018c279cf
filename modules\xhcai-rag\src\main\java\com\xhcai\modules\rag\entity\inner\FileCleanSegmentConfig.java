package com.xhcai.modules.rag.entity.inner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件完整配置DTO（分段+清洗）
 */
@Schema(description = "文件完整配置")
@Data
public class FileCleanSegmentConfig {

    /**
     * 分段配置
     */
    @Schema(description = "分段配置")
    private SegmentConfig segmentConfig;

    /**
     * 清洗配置
     */
    @Schema(description = "清洗配置")
    private CleaningConfig cleaningConfig;
}
