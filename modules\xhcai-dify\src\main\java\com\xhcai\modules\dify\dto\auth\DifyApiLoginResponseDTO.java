package com.xhcai.modules.dify.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Dify API 登录响应 DTO（用于解析Dify API的实际响应格式）
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Schema(description = "Dify API 登录响应")
public class DifyApiLoginResponseDTO {

    /**
     * 响应结果
     */
    @Schema(description = "响应结果")
    private String result;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private Data data;

    /**
     * 响应数据内部类
     */
    @Schema(description = "响应数据")
    public static class Data {
        /**
         * 访问令牌
         */
        @JsonProperty("access_token")
        @Schema(description = "访问令牌")
        private String accessToken;

        /**
         * 刷新令牌
         */
        @JsonProperty("refresh_token")
        @Schema(description = "刷新令牌")
        private String refreshToken;

        // Getters and Setters
        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "accessToken='" + (accessToken != null ? accessToken.substring(0, Math.min(20, accessToken.length())) + "..." : "null") + '\'' +
                    ", refreshToken='" + (refreshToken != null ? refreshToken.substring(0, Math.min(20, refreshToken.length())) + "..." : "null") + '\'' +
                    '}';
        }
    }

    // Getters and Setters
    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    /**
     * 转换为标准的 DifyLoginResponseDTO
     */
    public DifyLoginResponseDTO toStandardResponse() {
        DifyLoginResponseDTO response = new DifyLoginResponseDTO();
        if (data != null) {
            response.setAccessToken(data.getAccessToken());
            response.setRefreshToken(data.getRefreshToken());
        }
        return response;
    }

    @Override
    public String toString() {
        return "DifyApiLoginResponseDTO{" +
                "result='" + result + '\'' +
                ", data=" + data +
                '}';
    }
}
