package com.yyzs.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyzs.agent.entity.ElasticComponent;
import com.yyzs.agent.mapper.ElasticComponentMapper;
import com.yyzs.agent.service.ElasticComponentService;
import com.yyzs.agent.service.ComponentInstallService;
import com.yyzs.agent.service.ComponentMonitorService;
import com.yyzs.agent.service.FileStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.ServerSocket;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Elastic组件服务实现类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ElasticComponentServiceImpl extends ServiceImpl<ElasticComponentMapper, ElasticComponent>
        implements ElasticComponentService {

    private final ComponentInstallService componentInstallService;
    private final FileStorageService fileStorageService;

    @Value("${yyzs.agent.install-path:/opt/elastic}")
    private String defaultInstallPath;

    @Value("${yyzs.agent.package-storage-path:/data/packages}")
    private String packageStoragePath;

    @Override
    @Transactional
    public String uploadPackage(MultipartFile file, String componentType, String version, String description) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                throw new IllegalArgumentException("上传文件不能为空");
            }

            // 验证组件类型
            if (!isValidComponentType(componentType)) {
                throw new IllegalArgumentException("不支持的组件类型: " + componentType);
            }

            // 存储文件
            String filePath = fileStorageService.storeFile(file, packageStoragePath);

            // 创建组件记录
            ElasticComponent component = new ElasticComponent();
            component.setName(generateComponentName(componentType, version));
            component.setType(componentType);
            component.setVersion(version);
            component.setDescription(description);
            component.setStatus(ElasticComponent.ComponentStatus.UNINSTALLED);
            component.setPackageFileName(file.getOriginalFilename());
            component.setPackageFilePath(filePath);
            component.setPackageFileSize(file.getSize());
            component.setCreateTime(LocalDateTime.now());
            component.setUpdateTime(LocalDateTime.now());

            save(component);
            log.info("组件包上传成功: {}", component.getName());
            return component.getId();

        } catch (IOException e) {
            log.error("上传组件包失败", e);
            throw new RuntimeException("上传组件包失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean installComponent(String componentId, Map<String, Object> config) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        if (component.getStatus() != ElasticComponent.ComponentStatus.UNINSTALLED) {
            throw new IllegalStateException("组件已安装或正在安装中");
        }

        try {
            // 更新状态为安装中
            component.setStatus(ElasticComponent.ComponentStatus.INSTALLING);
            updateById(component);

            // 执行安装
            boolean success = componentInstallService.installComponent(component, config);

            if (success) {
                component.setStatus(ElasticComponent.ComponentStatus.INSTALLED);
                component.setInstallTime(LocalDateTime.now());
                component.setInstallPath(defaultInstallPath + "/" + component.getName());
                updateById(component);
                log.info("组件安装成功: {}", component.getName());
                return true;
            } else {
                component.setStatus(ElasticComponent.ComponentStatus.ERROR);
                updateById(component);
                log.error("组件安装失败: {}", component.getName());
                return false;
            }

        } catch (Exception e) {
            component.setStatus(ElasticComponent.ComponentStatus.ERROR);
            updateById(component);
            log.error("组件安装异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean uninstallComponent(String componentId, Map<String, Object> uninstallOptions) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        try {
            // 先停止组件
            if (component.getStatus() == ElasticComponent.ComponentStatus.RUNNING) {
                stopComponent(componentId);
            }

            // 处理卸载选项
            if (uninstallOptions != null) {
                // 处理备份选项
                Boolean backup = (Boolean) uninstallOptions.get("backup");
                if (backup != null && backup) {
                    log.info("执行备份操作: {}", componentId);
                    // TODO: 实现备份逻辑
                }

                // 处理删除选项
                Boolean removeConfig = (Boolean) uninstallOptions.get("removeConfig");
                Boolean removeData = (Boolean) uninstallOptions.get("removeData");
                Boolean removeLogs = (Boolean) uninstallOptions.get("removeLogs");

                log.info("卸载选项 - 删除配置: {}, 删除数据: {}, 删除日志: {}",
                        removeConfig, removeData, removeLogs);
            }

            // 执行卸载
            boolean success = componentInstallService.uninstallComponent(component);

            if (success) {
                component.setStatus(ElasticComponent.ComponentStatus.UNINSTALLED);
                component.setProcessId(null);
                component.setInstallPath(null);
                component.setConfigPath(null);
                component.setLogPath(null);
                updateById(component);
                log.info("组件卸载成功: {}", component.getName());
                return true;
            } else {
                log.error("组件卸载失败: {}", component.getName());
                return false;
            }

        } catch (Exception e) {
            log.error("组件卸载异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean startComponent(String componentId) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        if (component.getStatus() != ElasticComponent.ComponentStatus.INSTALLED
                && component.getStatus() != ElasticComponent.ComponentStatus.STOPPED) {
            throw new IllegalStateException("组件状态不允许启动: " + component.getStatus());
        }

        try {
            boolean success = componentInstallService.startComponent(component);

            if (success) {
                component.setStatus(ElasticComponent.ComponentStatus.RUNNING);
                component.setLastStartTime(LocalDateTime.now());
                updateById(component);
                log.info("组件启动成功: {}", component.getName());
                return true;
            } else {
                component.setStatus(ElasticComponent.ComponentStatus.ERROR);
                updateById(component);
                log.error("组件启动失败: {}", component.getName());
                return false;
            }

        } catch (Exception e) {
            component.setStatus(ElasticComponent.ComponentStatus.ERROR);
            updateById(component);
            log.error("组件启动异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean stopComponent(String componentId) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        if (component.getStatus() != ElasticComponent.ComponentStatus.RUNNING) {
            throw new IllegalStateException("组件未运行，无法停止");
        }

        try {
            boolean success = componentInstallService.stopComponent(component);

            if (success) {
                component.setStatus(ElasticComponent.ComponentStatus.STOPPED);
                component.setLastStopTime(LocalDateTime.now());
                component.setProcessId(null);
                updateById(component);
                log.info("组件停止成功: {}", component.getName());
                return true;
            } else {
                log.error("组件停止失败: {}", component.getName());
                return false;
            }

        } catch (Exception e) {
            log.error("组件停止异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean restartComponent(String componentId) {
        try {
            stopComponent(componentId);
            Thread.sleep(2000); // 等待2秒
            return startComponent(componentId);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("组件重启被中断: " + componentId, e);
            return false;
        }
    }

    @Override
    public ElasticComponent.ComponentStatus checkComponentStatus(String componentId) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        ElasticComponent.ComponentStatus actualStatus = componentInstallService.checkComponentStatus(component);

        // 如果状态发生变化，更新数据库
        if (actualStatus != component.getStatus()) {
            component.setStatus(actualStatus);
            updateById(component);
        }

        return actualStatus;
    }

    @Override
    public List<ElasticComponent> getComponentsByType(String type) {
        return baseMapper.findByType(type);
    }

    @Override
    public List<ElasticComponent> getComponentsByStatus(ElasticComponent.ComponentStatus status) {
        return baseMapper.findByStatus(status.name());
    }

    @Override
    public List<ElasticComponent> getRunningComponents() {
        return baseMapper.findRunningComponents();
    }

    @Override
    public List<ElasticComponent> getInstalledComponents() {
        return baseMapper.findInstalledComponents();
    }

    @Override
    public ElasticComponent getComponentByName(String name) {
        return baseMapper.findByName(name);
    }

    @Override
    public ElasticComponent getComponentByPort(Integer port) {
        return baseMapper.findByPort(port);
    }

    @Override
    public Map<String, Boolean> batchStartComponents(List<String> componentIds) {
        Map<String, Boolean> results = new HashMap<>();
        for (String componentId : componentIds) {
            try {
                boolean success = startComponent(componentId);
                results.put(componentId, success);
            } catch (Exception e) {
                log.error("批量启动组件失败: " + componentId, e);
                results.put(componentId, false);
            }
        }
        return results;
    }

    @Override
    public Map<String, Boolean> batchStopComponents(List<String> componentIds) {
        Map<String, Boolean> results = new HashMap<>();
        for (String componentId : componentIds) {
            try {
                boolean success = stopComponent(componentId);
                results.put(componentId, success);
            } catch (Exception e) {
                log.error("批量停止组件失败: " + componentId, e);
                results.put(componentId, false);
            }
        }
        return results;
    }

    @Override
    public Map<String, Object> getComponentStatistics() {
        List<ElasticComponentMapper.ComponentStatusCount> statusCounts = baseMapper.countByStatus();
        List<ElasticComponentMapper.ComponentTypeCount> typeCounts = baseMapper.countByType();

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("statusCounts", statusCounts);
        statistics.put("typeCounts", typeCounts);
        statistics.put("totalCount", count());
        statistics.put("runningCount", getRunningComponents().size());
        statistics.put("installedCount", getInstalledComponents().size());

        return statistics;
    }

    @Override
    public boolean updateComponentConfig(String componentId, Map<String, Object> config) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        return componentInstallService.updateComponentConfig(component, config);
    }

    @Override
    public Map<String, Object> getComponentConfig(String componentId) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        return componentInstallService.getComponentConfig(component);
    }

    @Override
    public boolean validateComponentConfig(String componentType, Map<String, Object> config) {
        return componentInstallService.validateComponentConfig(componentType, config);
    }

    @Override
    public List<String> getComponentLogs(String componentId, int lines) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        return componentInstallService.getComponentLogs(component, lines);
    }

    @Override
    public boolean clearComponentLogs(String componentId) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        return componentInstallService.clearComponentLogs(component);
    }

    @Override
    public String backupComponentConfig(String componentId) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        return componentInstallService.backupComponentConfig(component);
    }

    @Override
    public boolean restoreComponentConfig(String componentId, String backupPath) {
        ElasticComponent component = getById(componentId);
        if (component == null) {
            throw new IllegalArgumentException("组件不存在: " + componentId);
        }

        return componentInstallService.restoreComponentConfig(component, backupPath);
    }

    @Override
    public boolean isPortInUse(Integer port) {
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            return false;
        } catch (IOException e) {
            return true;
        }
    }

    @Override
    public Integer getAvailablePort(Integer startPort, Integer endPort) {
        for (int port = startPort; port <= endPort; port++) {
            if (!isPortInUse(port)) {
                return port;
            }
        }
        return null;
    }

    @Override
    public void syncComponentStatus() {
        List<ElasticComponent> components = list();
        for (ElasticComponent component : components) {
            try {
                checkComponentStatus(component.getId());
            } catch (Exception e) {
                log.error("同步组件状态失败: " + component.getName(), e);
            }
        }
    }

    @Override
    public void autoStartComponents() {
        QueryWrapper<ElasticComponent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("auto_start", true)
                .in("status", Arrays.asList(
                        ElasticComponent.ComponentStatus.INSTALLED.name(),
                        ElasticComponent.ComponentStatus.STOPPED.name()
                ));

        List<ElasticComponent> components = list(queryWrapper);
        for (ElasticComponent component : components) {
            try {
                startComponent(component.getId());
            } catch (Exception e) {
                log.error("自动启动组件失败: " + component.getName(), e);
            }
        }
    }

    /**
     * 验证组件类型是否有效
     */
    private boolean isValidComponentType(String componentType) {
        List<String> supportedTypes = Arrays.asList(
                "filebeat", "heartbeat", "metricbeat", "packetbeat",
                "winlogbeat", "auditbeat", "logstash", "elasticsearch", "kafka"
        );
        return supportedTypes.contains(componentType.toLowerCase());
    }

    /**
     * 生成组件名称
     */
    private String generateComponentName(String componentType, String version) {
        return componentType + "-" + version + "-" + System.currentTimeMillis();
    }
}
