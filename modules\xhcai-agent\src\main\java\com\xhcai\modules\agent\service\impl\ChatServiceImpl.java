package com.xhcai.modules.agent.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.utils.JsonUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.agent.dto.ChatRequestDTO;
import com.xhcai.modules.agent.entity.Agent;
import com.xhcai.modules.agent.entity.AgentConversation;
import com.xhcai.modules.agent.entity.AgentMessage;
import com.xhcai.modules.agent.mapper.AgentConversationMapper;
import com.xhcai.modules.agent.mapper.AgentMapper;
import com.xhcai.modules.agent.mapper.AgentMessageMapper;
import com.xhcai.modules.agent.service.IChatService;
import com.xhcai.modules.agent.vo.ChatResponseVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 聊天服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class ChatServiceImpl implements IChatService {

    private static final Logger log = LoggerFactory.getLogger(ChatServiceImpl.class);

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private AgentConversationMapper conversationMapper;

    @Autowired
    private AgentMessageMapper messageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChatResponseVO sendMessage(ChatRequestDTO requestDTO) {
        // 验证请求
        if (!validateChatRequest(requestDTO)) {
            throw new BusinessException("聊天请求验证失败");
        }

        // 检查智能体是否可用
        if (!isAgentAvailable(requestDTO.getAgentId())) {
            throw new BusinessException("智能体不可用");
        }

        try {
            // 获取或创建对话
            AgentConversation conversation = getOrCreateConversation(requestDTO);
            
            // 保存用户消息
            AgentMessage userMessage = saveUserMessage(conversation.getId(), requestDTO);
            
            // 调用AI服务生成回复
            ChatResponseVO response = generateAIResponse(conversation, userMessage, requestDTO);
            
            // 保存AI回复消息
            saveAssistantMessage(conversation.getId(), response);
            
            // 更新对话统计
            updateConversationStats(conversation.getId());
            
            return response;
            
        } catch (Exception e) {
            log.error("发送消息失败", e);
            throw new BusinessException("发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public SseEmitter sendMessageStream(ChatRequestDTO requestDTO) {
        // 验证请求
        if (!validateChatRequest(requestDTO)) {
            throw new BusinessException("聊天请求验证失败");
        }

        // 检查智能体是否可用
        if (!isAgentAvailable(requestDTO.getAgentId())) {
            throw new BusinessException("智能体不可用");
        }

        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        CompletableFuture.runAsync(() -> {
            try {
                // 获取或创建对话
                AgentConversation conversation = getOrCreateConversation(requestDTO);
                
                // 保存用户消息
                AgentMessage userMessage = saveUserMessage(conversation.getId(), requestDTO);
                
                // 流式生成AI回复
                generateAIResponseStream(conversation, userMessage, requestDTO, emitter);
                
            } catch (Exception e) {
                log.error("流式发送消息失败", e);
                try {
                    emitter.completeWithError(e);
                } catch (Exception ex) {
                    log.error("发送错误事件失败", ex);
                }
            }
        });

        return emitter;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean endConversation(String conversationId) {
        if (!StringUtils.hasText(conversationId)) {
            throw new BusinessException("对话ID不能为空");
        }

        int result = conversationMapper.endConversation(conversationId, LocalDateTime.now());
        
        if (result > 0) {
            log.info("结束对话成功，ID: {}", conversationId);
        }
        
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredConversations() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        LocalDateTime expiredTime = LocalDateTime.now().minusHours(24); // 24小时未活动的对话视为过期
        
        int result = conversationMapper.cleanExpiredConversations(expiredTime, tenantId);
        
        if (result > 0) {
            log.info("清理过期对话成功，数量: {}", result);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChatResponseVO regenerateResponse(String messageId) {
        if (!StringUtils.hasText(messageId)) {
            throw new BusinessException("消息ID不能为空");
        }

        // 获取原消息
        AgentMessage originalMessage = messageMapper.selectById(messageId);
        if (originalMessage == null) {
            throw new BusinessException("消息不存在");
        }

        // 获取对话信息
        AgentConversation conversation = conversationMapper.selectById(originalMessage.getConversationId());
        if (conversation == null) {
            throw new BusinessException("对话不存在");
        }

        try {
            // 重新生成回复
            ChatRequestDTO requestDTO = new ChatRequestDTO();
            requestDTO.setAgentId(conversation.getAgentId());
            requestDTO.setConversationId(conversation.getId());
            requestDTO.setMessage(originalMessage.getContent());
            
            ChatResponseVO response = generateAIResponse(conversation, originalMessage, requestDTO);
            
            // 更新原消息
            originalMessage.setContent(response.getContent());
            originalMessage.setStatus("sent");
            originalMessage.setProcessingTime(response.getProcessingTime());
            originalMessage.setTokens(response.getTokens());
            originalMessage.setInputTokens(response.getInputTokens());
            originalMessage.setOutputTokens(response.getOutputTokens());
            originalMessage.setCost(response.getCost());
            messageMapper.updateById(originalMessage);
            
            return response;
            
        } catch (Exception e) {
            log.error("重新生成回复失败", e);
            throw new BusinessException("重新生成回复失败: " + e.getMessage());
        }
    }

    @Override
    public List<ChatResponseVO> getConversationHistory(String conversationId, Integer limit) {
        if (!StringUtils.hasText(conversationId)) {
            throw new BusinessException("对话ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        List<AgentMessage> messages = messageMapper.selectLatestMessages(conversationId, tenantId, limit);
        
        List<ChatResponseVO> responses = new ArrayList<>();
        for (AgentMessage message : messages) {
            ChatResponseVO response = convertToResponseVO(message);
            responses.add(response);
        }
        
        return responses;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitFeedback(String conversationId, Integer rating, String feedback) {
        if (!StringUtils.hasText(conversationId)) {
            throw new BusinessException("对话ID不能为空");
        }

        int result = conversationMapper.updateFeedback(conversationId, rating, feedback);
        
        if (result > 0) {
            log.info("提交用户反馈成功，对话ID: {}, 评分: {}", conversationId, rating);
        }
        
        return result > 0;
    }

    @Override
    public List<String> getSuggestedQuestions(String agentId, String conversationId) {
        // 这里可以实现根据智能体和对话历史生成建议问题的逻辑
        List<String> suggestions = new ArrayList<>();
        suggestions.add("你能帮我做什么？");
        suggestions.add("请介绍一下你的功能");
        suggestions.add("有什么使用技巧吗？");
        return suggestions;
    }

    @Override
    public boolean validateChatRequest(ChatRequestDTO requestDTO) {
        if (requestDTO == null) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getAgentId())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getMessage())) {
            return false;
        }
        
        return true;
    }

    @Override
    public boolean isAgentAvailable(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            return false;
        }

        Agent agent = agentMapper.selectById(agentId);
        return agent != null && "1".equals(agent.getStatus());
    }

    @Override
    public ConversationStatsVO getConversationStats(String conversationId) {
        if (!StringUtils.hasText(conversationId)) {
            throw new BusinessException("对话ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        
        // 获取对话信息
        AgentConversation conversation = conversationMapper.selectById(conversationId);
        if (conversation == null) {
            throw new BusinessException("对话不存在");
        }

        // 获取消息统计
        Integer messageCount = messageMapper.countByConversationId(conversationId, tenantId);
        Integer userMessageCount = messageMapper.countUserMessages(conversationId, tenantId);
        Integer assistantMessageCount = messageMapper.countAssistantMessages(conversationId, tenantId);
        
        // 获取token统计
        AgentMessageMapper.TokenStatsVO tokenStats = messageMapper.getTokenStatsByConversation(conversationId, tenantId);

        ConversationStatsVO stats = new ConversationStatsVO();
        stats.setMessageCount(messageCount);
        stats.setUserMessageCount(userMessageCount);
        stats.setAssistantMessageCount(assistantMessageCount);
        stats.setStartedAt(conversation.getStartedAt());
        stats.setLastActivityAt(conversation.getLastActivityAt());
        stats.setStatus(conversation.getStatus());
        
        if (tokenStats != null) {
            stats.setTotalTokens(tokenStats.getTotalTokens());
            stats.setInputTokens(tokenStats.getInputTokens());
            stats.setOutputTokens(tokenStats.getOutputTokens());
            stats.setTotalCost(tokenStats.getTotalCost());
        }

        return stats;
    }

    /**
     * 获取或创建对话
     */
    private AgentConversation getOrCreateConversation(ChatRequestDTO requestDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String userId = SecurityUtils.getCurrentUserId();

        AgentConversation conversation;
        
        if (StringUtils.hasText(requestDTO.getConversationId())) {
            // 使用现有对话
            conversation = conversationMapper.selectById(requestDTO.getConversationId());
            if (conversation == null) {
                throw new BusinessException("对话不存在");
            }
        } else if (StringUtils.hasText(requestDTO.getSessionId())) {
            // 根据会话ID查找对话
            // 注意：不需要传递tenantId，多租户插件会自动处理
            conversation = conversationMapper.selectBySessionId(requestDTO.getSessionId());
            if (conversation == null) {
                // 创建新对话
                conversation = createNewConversation(requestDTO, userId, tenantId);
            }
        } else {
            // 创建新对话
            conversation = createNewConversation(requestDTO, userId, tenantId);
        }

        return conversation;
    }

    /**
     * 创建新对话
     */
    private AgentConversation createNewConversation(ChatRequestDTO requestDTO, String userId, String tenantId) {
        AgentConversation conversation = new AgentConversation();
        conversation.setAgentId(requestDTO.getAgentId());
        conversation.setUserId(userId);
        conversation.setSessionId(StringUtils.hasText(requestDTO.getSessionId()) ? 
                                 requestDTO.getSessionId() : UUID.randomUUID().toString());
        conversation.setStatus("active");
        conversation.setMessageCount(0);
        conversation.setStartedAt(LocalDateTime.now());
        conversation.setLastActivityAt(LocalDateTime.now());
        conversation.setTotalTokens(0L);
        conversation.setInputTokens(0L);
        conversation.setOutputTokens(0L);
        conversation.setCost(0L);
        
        if (requestDTO.getMetadata() != null) {
            conversation.setMetadata(JsonUtils.toJsonString(requestDTO.getMetadata()));
        }

        conversationMapper.insert(conversation);
        return conversation;
    }

    /**
     * 保存用户消息
     */
    private AgentMessage saveUserMessage(String conversationId, ChatRequestDTO requestDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        
        AgentMessage message = new AgentMessage();
        message.setConversationId(conversationId);
        message.setMessageType("user");
        message.setContent(requestDTO.getMessage());
        message.setSequenceNumber(messageMapper.getNextSequenceNumber(conversationId, tenantId));
        message.setStatus("sent");
        message.setSentAt(LocalDateTime.now());
        message.setReceivedAt(LocalDateTime.now());

        messageMapper.insert(message);
        return message;
    }

    /**
     * 生成AI回复
     */
    private ChatResponseVO generateAIResponse(AgentConversation conversation, AgentMessage userMessage, ChatRequestDTO requestDTO) {
        // 这里应该调用实际的AI服务（如Spring AI）
        // 目前返回模拟响应
        ChatResponseVO response = new ChatResponseVO();
        response.setMessageId(UUID.randomUUID().toString());
        response.setConversationId(conversation.getId());
        response.setAgentId(conversation.getAgentId());
        response.setContent("这是一个模拟的AI回复。实际实现中应该调用AI服务。");
        response.setMessageType("assistant");
        response.setStatus("sent");
        response.setFinished(true);
        response.setProcessingTime(1000L);
        response.setTokens(50);
        response.setInputTokens(30);
        response.setOutputTokens(20);
        response.setCost(5L);
        response.setCreateTime(LocalDateTime.now());

        return response;
    }

    /**
     * 流式生成AI回复
     */
    private void generateAIResponseStream(AgentConversation conversation, AgentMessage userMessage, 
                                        ChatRequestDTO requestDTO, SseEmitter emitter) {
        try {
            // 模拟流式响应
            String fullResponse = "这是一个模拟的流式AI回复。";
            String[] words = fullResponse.split("");
            
            for (int i = 0; i < words.length; i++) {
                ChatResponseVO response = new ChatResponseVO();
                response.setMessageId(UUID.randomUUID().toString());
                response.setConversationId(conversation.getId());
                response.setAgentId(conversation.getAgentId());
                response.setContent(String.join("", java.util.Arrays.copyOfRange(words, 0, i + 1)));
                response.setFinished(i == words.length - 1);
                
                emitter.send(SseEmitter.event().data(response));
                Thread.sleep(100); // 模拟延迟
            }
            
            emitter.complete();
            
        } catch (Exception e) {
            log.error("流式生成回复失败", e);
            emitter.completeWithError(e);
        }
    }

    /**
     * 保存AI回复消息
     */
    private void saveAssistantMessage(String conversationId, ChatResponseVO response) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        
        AgentMessage message = new AgentMessage();
        message.setConversationId(conversationId);
        message.setMessageType("assistant");
        message.setContent(response.getContent());
        message.setSequenceNumber(messageMapper.getNextSequenceNumber(conversationId, tenantId));
        message.setStatus("sent");
        message.setSentAt(LocalDateTime.now());
        message.setReceivedAt(LocalDateTime.now());
        message.setProcessingTime(response.getProcessingTime());
        message.setTokens(response.getTokens());
        message.setInputTokens(response.getInputTokens());
        message.setOutputTokens(response.getOutputTokens());
        message.setCost(response.getCost());

        messageMapper.insert(message);
    }

    /**
     * 更新对话统计
     */
    private void updateConversationStats(String conversationId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        Integer messageCount = messageMapper.countByConversationId(conversationId, tenantId);
        conversationMapper.updateMessageStats(conversationId, messageCount, LocalDateTime.now());
    }

    /**
     * 转换为响应VO
     */
    private ChatResponseVO convertToResponseVO(AgentMessage message) {
        ChatResponseVO response = new ChatResponseVO();
        response.setMessageId(message.getId());
        response.setConversationId(message.getConversationId());
        response.setContent(message.getContent());
        response.setMessageType(message.getMessageType());
        response.setStatus(message.getStatus());
        response.setFinished(true);
        response.setProcessingTime(message.getProcessingTime());
        response.setTokens(message.getTokens());
        response.setInputTokens(message.getInputTokens());
        response.setOutputTokens(message.getOutputTokens());
        response.setCost(message.getCost());
        response.setErrorMessage(message.getErrorMessage());
        response.setCreateTime(message.getCreateTime());
        
        return response;
    }
}
