/**
 * API拦截器配置
 * 统一处理请求认证、响应处理、错误处理等
 */

import { useAuthStore } from '@/stores/authStore'
import { envConfig, logger } from '@/config/env'
import type { RequestConfig, ApiResponse, ApiError } from '@/types/api'

/**
 * 请求拦截器 - 添加认证信息
 * 为除/api/auth/login登录接口外的所有请求添加token、access_token、refresh_token
 */
export const authRequestInterceptor = (config: RequestConfig): RequestConfig => {
  // 如果配置了跳过认证，则不添加认证头
  if (config.skipAuth) {
    return config
  }

  // 检查是否为登录接口，登录接口不需要认证头
  if (config.url === '/api/auth/login') {
    return config
  }

  const authStore = useAuthStore()

  // 为所有其他接口添加认证头
  if (authStore.isLoggedIn && authStore.tokens) {
    const authHeaders = authStore.getAuthHeaders()
    config.headers = {
      ...config.headers,
      ...authHeaders
    }

    logger.debug('Added auth headers to request:', {
      url: config.url,
      headers: Object.keys(authHeaders)
    })
  }

  return config
}

/**
 * 请求拦截器 - 添加通用头信息
 */
export const commonRequestInterceptor = (config: RequestConfig): RequestConfig => {
  // 添加通用头信息
  config.headers = {
    'X-Requested-With': 'XMLHttpRequest',
    'X-Client-Version': envConfig.appVersion,
    ...config.headers
  }
  
  return config
}

/**
 * 响应拦截器 - 处理成功响应
 */
export const successResponseInterceptor = <T>(response: ApiResponse<T>): ApiResponse<T> => {
  // 记录成功响应
  logger.debug('API Success Response:', response)
  
  return response
}

/**
 * 错误拦截器 - 处理认证错误
 */
export const authErrorInterceptor = async (error: ApiError): Promise<never> => {
  const authStore = useAuthStore()
  
  // 处理认证相关错误
  if (error.code === 401 || error.code === 403) {
    logger.warn('Authentication error, clearing auth and redirecting to login')
    
    // 清除认证信息
    authStore.clearAuth()
    
    // 跳转到登录页面
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname
      if (currentPath !== envConfig.loginRedirectPath) {
        window.location.href = envConfig.loginRedirectPath
      }
    }
  }
  
  throw error
}

/**
 * 错误拦截器 - 处理网络错误
 */
export const networkErrorInterceptor = async (error: ApiError): Promise<never> => {
  // 处理网络相关错误
  if (error.code === 0 || error.msg.includes('网络')) {
    logger.error('Network error:', error)

    // 可以在这里添加网络错误的统一处理逻辑
    // 比如显示网络错误提示、重试机制等
  }
  
  throw error
}

/**
 * 错误拦截器 - 处理服务器错误
 */
export const serverErrorInterceptor = async (error: ApiError): Promise<never> => {
  // 处理服务器错误
  if (error.code >= 500) {
    logger.error('Server error:', error)
    
    // 可以在这里添加服务器错误的统一处理逻辑
    // 比如显示服务器错误提示、错误上报等
  }
  
  throw error
}

/**
 * 错误拦截器 - 通用错误处理
 */
export const commonErrorInterceptor = async (error: ApiError): Promise<never> => {
  // 记录所有错误
  logger.error('API Error:', error)
  
  // 可以在这里添加通用错误处理逻辑
  // 比如错误上报、用户提示等
  
  throw error
}

/**
 * Token刷新拦截器
 */
export const tokenRefreshInterceptor = async (error: ApiError): Promise<never> => {
  const authStore = useAuthStore()
  
  // 如果是token过期错误，尝试刷新token
  if (error.code === 401 && authStore.tokens?.refresh_token) {
    try {
      logger.info('Attempting to refresh token')
      
      // 这里应该调用刷新token的API
      // const refreshResponse = await refreshTokenAPI(authStore.tokens.refresh_token)
      // authStore.updateTokens(refreshResponse.data)
      
      // 暂时抛出错误，等待实际的刷新token API实现
      throw error
      
    } catch (refreshError) {
      logger.error('Token refresh failed:', refreshError)
      // 刷新失败，清除认证信息并跳转登录
      authStore.clearAuth()
      if (typeof window !== 'undefined') {
        window.location.href = envConfig.loginRedirectPath
      }
      throw error
    }
  }
  
  throw error
}

/**
 * 配置所有拦截器
 */
export const setupInterceptors = (apiClient: any) => {
  // 请求拦截器
  apiClient.addRequestInterceptor(commonRequestInterceptor)
  apiClient.addRequestInterceptor(authRequestInterceptor)
  
  // 响应拦截器
  apiClient.addResponseInterceptor(successResponseInterceptor)
  
  // 错误拦截器（按优先级顺序）
  apiClient.addErrorInterceptor(tokenRefreshInterceptor)
  apiClient.addErrorInterceptor(authErrorInterceptor)
  apiClient.addErrorInterceptor(networkErrorInterceptor)
  apiClient.addErrorInterceptor(serverErrorInterceptor)
  apiClient.addErrorInterceptor(commonErrorInterceptor)
}

export default setupInterceptors
