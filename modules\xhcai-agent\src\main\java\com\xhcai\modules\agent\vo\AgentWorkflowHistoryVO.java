package com.xhcai.modules.agent.vo;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体工作流历史记录VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体工作流历史记录VO")
public class AgentWorkflowHistoryVO {

    /**
     * 历史记录ID
     */
    @Schema(description = "历史记录ID", example = "history_001")
    private String id;

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "workflow_001")
    private String workflowId;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    private String agentId;

    /**
     * 配置哈希值
     */
    @Schema(description = "配置哈希值", example = "a1b2c3d4e5f6...")
    private String configHash;

    /**
     * 工作流名称（快照）
     */
    @Schema(description = "工作流名称", example = "客服工作流")
    private String name;

    /**
     * 工作流描述（快照）
     */
    @Schema(description = "工作流描述", example = "智能客服的工作流程配置")
    private String description;

    /**
     * 版本号（快照）
     */
    @Schema(description = "版本号", example = "1")
    private Integer version;

    /**
     * Vue Flow节点数据快照（JSON格式）
     */
    @Schema(description = "Vue Flow节点数据快照", example = "[{\"id\":\"node1\",\"type\":\"start\",\"position\":{\"x\":100,\"y\":100},\"data\":{\"label\":\"开始\"}}]")
    private String nodesData;

    /**
     * Vue Flow边数据快照（JSON格式）
     */
    @Schema(description = "Vue Flow边数据快照", example = "[{\"id\":\"edge1\",\"source\":\"node1\",\"target\":\"node2\",\"type\":\"default\"}]")
    private String edgesData;

    /**
     * 视口配置快照（JSON格式）
     */
    @Schema(description = "视口配置快照", example = "{\"x\":0,\"y\":0,\"zoom\":1}")
    private String viewportConfig;

    /**
     * 全局变量配置（JSON格式快照）
     */
    @Schema(description = "全局变量配置", example = "{\"var1\":\"value1\",\"var2\":\"value2\"}")
    private String globalVariables;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "node_add")
    private String operationType;

    /**
     * 操作描述
     */
    @Schema(description = "操作描述", example = "添加了开始节点")
    private String operationDesc;

    /**
     * 变更摘要
     */
    @Schema(description = "变更摘要", example = "新增2个节点，修改1个连接")
    private String changeSummary;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作用户ID
     */
    @Schema(description = "操作用户ID", example = "user_001")
    private String operationUserId;

    /**
     * 操作用户名称
     */
    @Schema(description = "操作用户名称", example = "张三")
    private String operationUserName;

    /**
     * 是否为重要变更
     */
    @Schema(description = "是否为重要变更", example = "true")
    private Boolean isMajorChange;

    /**
     * 配置大小（字节数）
     */
    @Schema(description = "配置大小", example = "1024")
    private Long configSize;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID", example = "tenant_001")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "user_001")
    private String createBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人", example = "user_001")
    private String updateBy;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getConfigHash() {
        return configHash;
    }

    public void setConfigHash(String configHash) {
        this.configHash = configHash;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getNodesData() {
        return nodesData;
    }

    public void setNodesData(String nodesData) {
        this.nodesData = nodesData;
    }

    public String getEdgesData() {
        return edgesData;
    }

    public void setEdgesData(String edgesData) {
        this.edgesData = edgesData;
    }

    public String getViewportConfig() {
        return viewportConfig;
    }

    public void setViewportConfig(String viewportConfig) {
        this.viewportConfig = viewportConfig;
    }

    public String getGlobalVariables() {
        return globalVariables;
    }

    public void setGlobalVariables(String globalVariables) {
        this.globalVariables = globalVariables;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public String getChangeSummary() {
        return changeSummary;
    }

    public void setChangeSummary(String changeSummary) {
        this.changeSummary = changeSummary;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationUserId() {
        return operationUserId;
    }

    public void setOperationUserId(String operationUserId) {
        this.operationUserId = operationUserId;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public Boolean getIsMajorChange() {
        return isMajorChange;
    }

    public void setIsMajorChange(Boolean isMajorChange) {
        this.isMajorChange = isMajorChange;
    }

    public Long getConfigSize() {
        return configSize;
    }

    public void setConfigSize(Long configSize) {
        this.configSize = configSize;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
}
