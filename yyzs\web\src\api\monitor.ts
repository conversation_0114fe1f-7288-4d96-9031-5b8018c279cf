import apiClient, { ApiResponse } from './client';
import {
  ComponentMonitor,
  SystemResourceUsage,
  ComponentPerformanceStats,
  MonitoringStatus,
  AlertRecord,
  MonitorTimeRange,
} from '@/types/monitor';

/**
 * 监控管理API
 */
export class MonitorAPI {
  /**
   * 获取组件最新监控数据
   */
  static async getLatestMonitorData(componentId: string): Promise<ApiResponse<ComponentMonitor>> {
    return apiClient.get(`/api/monitor/components/${componentId}/latest`);
  }

  /**
   * 获取组件历史监控数据
   */
  static async getHistoryMonitorData(
    componentId: string,
    timeRange: MonitorTimeRange
  ): Promise<ApiResponse<ComponentMonitor[]>> {
    const params = new URLSearchParams({
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
    });
    return apiClient.get(`/api/monitor/components/${componentId}/history?${params.toString()}`);
  }

  /**
   * 获取所有组件最新监控数据
   */
  static async getAllLatestMonitorData(): Promise<ApiResponse<ComponentMonitor[]>> {
    return apiClient.get('/api/monitor/components/latest');
  }

  /**
   * 获取不健康的组件
   */
  static async getUnhealthyComponents(): Promise<ApiResponse<ComponentMonitor[]>> {
    return apiClient.get('/api/monitor/components/unhealthy');
  }

  /**
   * 检查组件健康状态
   */
  static async checkComponentHealth(componentId: string): Promise<ApiResponse<{
    componentId: string;
    healthy: boolean;
    status: string;
  }>> {
    return apiClient.get(`/api/monitor/components/${componentId}/health`);
  }

  /**
   * 获取组件性能统计
   */
  static async getComponentPerformanceStats(
    componentId: string,
    timeRange: MonitorTimeRange
  ): Promise<ApiResponse<ComponentPerformanceStats>> {
    const params = new URLSearchParams({
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
    });
    return apiClient.get(`/api/monitor/components/${componentId}/stats?${params.toString()}`);
  }

  /**
   * 获取系统资源使用情况
   */
  static async getSystemResourceUsage(): Promise<ApiResponse<SystemResourceUsage>> {
    return apiClient.get('/api/monitor/system/resources');
  }

  /**
   * 手动收集监控数据
   */
  static async collectMonitorData(componentId: string): Promise<ApiResponse<ComponentMonitor>> {
    return apiClient.post(`/api/monitor/components/${componentId}/collect`);
  }

  /**
   * 批量收集所有组件监控数据
   */
  static async collectAllMonitorData(): Promise<ApiResponse<{
    collectedCount: number;
    data: ComponentMonitor[];
  }>> {
    return apiClient.post('/api/monitor/components/collect-all');
  }

  /**
   * 启动监控
   */
  static async startMonitoring(): Promise<ApiResponse<{ active: boolean }>> {
    return apiClient.post('/api/monitor/start');
  }

  /**
   * 停止监控
   */
  static async stopMonitoring(): Promise<ApiResponse<{ active: boolean }>> {
    return apiClient.post('/api/monitor/stop');
  }

  /**
   * 获取监控状态
   */
  static async getMonitoringStatus(): Promise<ApiResponse<MonitoringStatus>> {
    return apiClient.get('/api/monitor/status');
  }

  /**
   * 设置监控间隔
   */
  static async setMonitorInterval(intervalSeconds: number): Promise<ApiResponse<{
    intervalSeconds: number;
  }>> {
    return apiClient.put(`/api/monitor/interval?intervalSeconds=${intervalSeconds}`);
  }

  /**
   * 清理历史监控数据
   */
  static async cleanupHistoryData(beforeTime: string): Promise<ApiResponse<{
    deletedCount: number;
  }>> {
    return apiClient.delete(`/api/monitor/cleanup?beforeTime=${beforeTime}`);
  }

  /**
   * 获取告警历史
   */
  static async getAlertHistory(componentId: string): Promise<ApiResponse<AlertRecord[]>> {
    return apiClient.get(`/api/monitor/components/${componentId}/alerts`);
  }

  /**
   * 发送测试告警
   */
  static async sendTestAlert(componentId: string, message: string): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/monitor/components/${componentId}/test-alert`, { message });
  }

  /**
   * 获取监控概览数据
   */
  static async getMonitorOverview(): Promise<ApiResponse<{
    totalComponents: number;
    runningComponents: number;
    healthyComponents: number;
    unhealthyComponents: number;
    systemResourceUsage: SystemResourceUsage;
    recentAlerts: AlertRecord[];
  }>> {
    return apiClient.get('/api/monitor/overview');
  }

  /**
   * 获取监控趋势数据
   */
  static async getMonitorTrends(
    componentId: string,
    timeRange: MonitorTimeRange,
    interval: '1m' | '5m' | '15m' | '1h' | '1d' = '5m'
  ): Promise<ApiResponse<{
    timestamps: string[];
    cpuUsage: number[];
    memoryUsage: number[];
    diskUsage: number[];
    networkIn: number[];
    networkOut: number[];
    responseTime: number[];
  }>> {
    const params = new URLSearchParams({
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      interval,
    });
    return apiClient.get(`/api/monitor/components/${componentId}/trends?${params.toString()}`);
  }

  /**
   * 导出监控数据
   */
  static async exportMonitorData(
    componentId: string,
    timeRange: MonitorTimeRange,
    format: 'csv' | 'json' = 'csv'
  ): Promise<ApiResponse<{ downloadUrl: string }>> {
    const params = new URLSearchParams({
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
      format,
    });
    return apiClient.get(`/api/monitor/components/${componentId}/export?${params.toString()}`);
  }
}

export default MonitorAPI;
