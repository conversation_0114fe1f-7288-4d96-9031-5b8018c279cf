package com.xhcai.modules.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysTenantQueryDTO;
import com.xhcai.modules.system.entity.SysTenant;
import com.xhcai.modules.system.vo.SysTenantVO;

/**
 * 租户信息服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysTenantService extends IService<SysTenant> {

    /**
     * 分页查询租户列表
     *
     * @param queryDTO 查询条件
     * @return 租户分页列表
     */
    PageResult<SysTenantVO> selectTenantPage(SysTenantQueryDTO queryDTO);

    /**
     * 根据租户编码查询租户信息
     *
     * @param tenantCode 租户编码
     * @return 租户信息
     */
    SysTenant selectByTenantCode(String tenantCode);

    /**
     * 根据租户域名查询租户信息
     *
     * @param domain 租户域名
     * @return 租户信息
     */
    SysTenant selectByDomain(String domain);

    /**
     * 创建租户
     *
     * @param tenant 租户信息
     * @return 是否成功
     */
    boolean createTenant(SysTenant tenant);

    /**
     * 更新租户信息
     *
     * @param tenant 租户信息
     * @return 是否成功
     */
    boolean updateTenant(SysTenant tenant);

    /**
     * 删除租户
     *
     * @param tenantIds 租户ID列表
     * @return 是否成功
     */
    boolean deleteTenants(List<String> tenantIds);

    /**
     * 启用租户
     *
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean enableTenant(String tenantId);

    /**
     * 停用租户
     *
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean disableTenant(String tenantId);

    /**
     * 续期租户
     *
     * @param tenantId 租户ID
     * @param months 续期月数
     * @return 是否成功
     */
    boolean renewTenant(String tenantId, Integer months);

    /**
     * 检查租户编码是否存在
     *
     * @param tenantCode 租户编码
     * @param excludeId 排除的租户ID
     * @return 是否存在
     */
    boolean existsTenantCode(String tenantCode, String excludeId);

    /**
     * 检查租户域名是否存在
     *
     * @param domain 租户域名
     * @param excludeId 排除的租户ID
     * @return 是否存在
     */
    boolean existsDomain(String domain, String excludeId);

    /**
     * 获取租户统计信息
     *
     * @param tenantId 租户ID
     * @return 租户VO（包含统计信息）
     */
    SysTenantVO getTenantStatistics(String tenantId);

    /**
     * 查询即将过期的租户列表
     *
     * @param days 提前天数
     * @return 即将过期的租户列表
     */
    List<SysTenantVO> getExpiringTenants(Integer days);

    /**
     * 查询已过期的租户列表
     *
     * @return 已过期的租户列表
     */
    List<SysTenantVO> getExpiredTenants();

    /**
     * 处理过期租户 将过期租户状态设置为过期
     *
     * @return 处理的租户数量
     */
    int processExpiredTenants();

    /**
     * 初始化租户数据 为新租户创建默认的角色、权限、部门、管理员用户等
     *
     * @param tenantId 租户ID
     * @param adminUsername 管理员用户名
     * @param adminPassword 管理员密码
     * @param adminEmail 管理员邮箱
     * @return 是否成功
     */
    boolean initializeTenantData(String tenantId, String adminUsername, String adminPassword, String adminEmail);

    /**
     * 复制租户配置
     *
     * @param sourceTenantId 源租户ID
     * @param targetTenantId 目标租户ID
     * @return 是否成功
     */
    boolean copyTenantConfigs(String sourceTenantId, String targetTenantId);

    /**
     * 验证租户状态 检查租户是否可用（未过期、未停用）
     *
     * @param tenantId 租户ID
     * @return 是否可用
     */
    boolean validateTenantStatus(String tenantId);

    /**
     * 获取租户的用户数量
     *
     * @param tenantId 租户ID
     * @return 用户数量
     */
    Integer getTenantUserCount(String tenantId);

    /**
     * 检查租户用户数量限制
     *
     * @param tenantId 租户ID
     * @return 是否超出限制
     */
    boolean checkUserLimit(String tenantId);
}
