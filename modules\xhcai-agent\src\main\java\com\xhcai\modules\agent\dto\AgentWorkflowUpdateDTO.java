package com.xhcai.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 智能体工作流更新DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体工作流更新信息")
public class AgentWorkflowUpdateDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "workflow_001", required = true)
    @NotBlank(message = "工作流ID不能为空")
    @Size(max = 36, message = "工作流ID长度不能超过36个字符")
    private String id;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001", required = true)
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 工作流名称
     */
    @Schema(description = "工作流名称", example = "客服工作流")
    @Size(min = 1, max = 100, message = "工作流名称长度必须在1-100个字符之间")
    private String name;

    /**
     * 工作流描述
     */
    @Schema(description = "工作流描述", example = "智能客服的工作流程配置")
    @Size(max = 500, message = "工作流描述长度不能超过500个字符")
    private String description;

    /**
     * Vue Flow节点数据（JSON格式）
     */
    @Schema(description = "Vue Flow节点数据", example = "[{\"id\":\"node1\",\"type\":\"start\",\"position\":{\"x\":100,\"y\":100},\"data\":{\"label\":\"开始\"}}]")
    private String nodesData;

    /**
     * Vue Flow边数据（JSON格式）
     */
    @Schema(description = "Vue Flow边数据", example = "[{\"id\":\"edge1\",\"source\":\"node1\",\"target\":\"node2\",\"type\":\"default\"}]")
    private String edgesData;

    /**
     * 视口配置（JSON格式）
     */
    @Schema(description = "视口配置", example = "{\"x\":0,\"y\":0,\"zoom\":1}")
    private String viewportConfig;

    /**
     * 节点库配置（JSON格式）
     */
    @Schema(description = "节点库配置", example = "{\"categories\":[{\"name\":\"基础节点\",\"nodes\":[{\"type\":\"start\",\"label\":\"开始节点\"}]}]}")
    private String nodeLibrary;

    /**
     * 全局变量配置（JSON格式）
     */
    @Schema(description = "全局变量配置", example = "{\"var1\":\"value1\",\"var2\":\"value2\"}")
    private String globalVariables;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "node_add")
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    private String operationType;

    /**
     * 操作描述
     */
    @Schema(description = "操作描述", example = "添加了开始节点")
    @Size(max = 200, message = "操作描述长度不能超过200个字符")
    private String operationDesc;

    /**
     * 是否创建新版本
     */
    @Schema(description = "是否创建新版本", example = "true")
    private Boolean createNewVersion;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNodesData() {
        return nodesData;
    }

    public void setNodesData(String nodesData) {
        this.nodesData = nodesData;
    }

    public String getEdgesData() {
        return edgesData;
    }

    public void setEdgesData(String edgesData) {
        this.edgesData = edgesData;
    }

    public String getViewportConfig() {
        return viewportConfig;
    }

    public void setViewportConfig(String viewportConfig) {
        this.viewportConfig = viewportConfig;
    }

    public String getNodeLibrary() {
        return nodeLibrary;
    }

    public void setNodeLibrary(String nodeLibrary) {
        this.nodeLibrary = nodeLibrary;
    }

    public String getGlobalVariables() {
        return globalVariables;
    }

    public void setGlobalVariables(String globalVariables) {
        this.globalVariables = globalVariables;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public Boolean getCreateNewVersion() {
        return createNewVersion;
    }

    public void setCreateNewVersion(Boolean createNewVersion) {
        this.createNewVersion = createNewVersion;
    }
}
