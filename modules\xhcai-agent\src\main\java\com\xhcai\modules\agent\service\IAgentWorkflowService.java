package com.xhcai.modules.agent.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.agent.dto.AgentWorkflowCreateDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowQueryDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowUpdateDTO;
import com.xhcai.modules.agent.dto.RealtimeSaveDTO;
import com.xhcai.modules.agent.entity.AgentWorkflow;
import com.xhcai.modules.agent.vo.AgentWorkflowVO;
import com.xhcai.modules.agent.vo.AgentWorkflowVersionVO;

/**
 * 智能体工作流服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IAgentWorkflowService extends IService<AgentWorkflow> {

    /**
     * 分页查询工作流列表
     *
     * @param queryDTO 查询条件
     * @return 工作流列表
     */
    IPage<AgentWorkflowVO> getWorkflowPage(AgentWorkflowQueryDTO queryDTO);

    /**
     * 根据ID查询工作流详情
     *
     * @param id 工作流ID
     * @return 工作流详情
     */
    AgentWorkflowVO getWorkflowById(String id);

    /**
     * 根据智能体ID查询最新版本工作流
     *
     * @param agentId 智能体ID
     * @return 最新版本工作流
     */
    AgentWorkflowVO getLatestWorkflowByAgentId(String agentId);

    /**
     * 创建工作流
     *
     * @param createDTO 创建信息
     * @return 工作流ID
     */
    String createWorkflow(AgentWorkflowCreateDTO createDTO);

    /**
     * 更新工作流（实时保存）
     *
     * @param updateDTO 更新信息
     * @return 是否成功
     */
    boolean updateWorkflow(AgentWorkflowUpdateDTO updateDTO);

    /**
     * 删除工作流
     *
     * @param id 工作流ID
     * @return 是否成功
     */
    boolean deleteWorkflow(String id);

    /**
     * 批量删除工作流
     *
     * @param ids 工作流ID列表
     * @return 是否成功
     */
    boolean deleteWorkflowBatch(List<String> ids);

    /**
     * 发布工作流
     *
     * @param id 工作流ID
     * @return 是否成功
     */
    boolean publishWorkflow(String id);

    /**
     * 取消发布工作流
     *
     * @param id 工作流ID
     * @return 是否成功
     */
    boolean unpublishWorkflow(String id);

    /**
     * 查询智能体工作流版本历史
     *
     * @param agentId 智能体ID
     * @return 版本历史列表
     */
    List<AgentWorkflowVersionVO> getVersionHistory(String agentId);

    /**
     * 回滚到指定版本
     *
     * @param agentId 智能体ID
     * @param version 目标版本号
     * @return 是否成功
     */
    boolean rollbackToVersion(String agentId, Integer version);

    /**
     * 复制工作流到新版本
     *
     * @param sourceId 源工作流ID
     * @param operationDesc 操作描述
     * @return 新版本工作流ID
     */
    String copyToNewVersion(String sourceId, String operationDesc);

    /**
     * 检查工作流名称是否存在
     *
     * @param agentId 智能体ID
     * @param name 工作流名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkNameExists(String agentId, String name, String excludeId);

    /**
     * 获取智能体的最大版本号
     *
     * @param agentId 智能体ID
     * @return 最大版本号
     */
    Integer getMaxVersionByAgentId(String agentId);

    /**
     * 统计智能体工作流数量
     *
     * @param agentId 智能体ID
     * @return 工作流数量
     */
    Long countByAgentId(String agentId);

    /**
     * 统计工作流版本数量
     *
     * @param agentId 智能体ID
     * @return 版本数量
     */
    Long countVersionsByAgentId(String agentId);

    /**
     * 验证工作流配置
     *
     * @param workflowConfig 工作流配置JSON
     * @return 验证结果
     */
    boolean validateWorkflowConfig(String workflowConfig);

    /**
     * 导出工作流配置
     *
     * @param id 工作流ID
     * @return 配置JSON
     */
    String exportWorkflowConfig(String id);

    /**
     * 导入工作流配置
     *
     * @param agentId 智能体ID
     * @param configJson 配置JSON
     * @return 工作流ID
     */
    String importWorkflowConfig(String agentId, String configJson);

    /**
     * 清理过期的工作流版本
     *
     * @param agentId 智能体ID
     * @param keepVersions 保留版本数
     * @return 清理数量
     */
    int cleanupOldVersions(String agentId, int keepVersions);

    /**
     * Vue Flow实时保存工作流
     *
     * @param saveDTO 保存数据
     * @return 操作结果
     */
    String realtimeSave(RealtimeSaveDTO saveDTO);

    /**
     * 获取默认节点库配置
     *
     * @return 节点库配置JSON
     */
    String getDefaultNodeLibrary();

}
