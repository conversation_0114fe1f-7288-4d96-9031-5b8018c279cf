const express = require('express');
const cors = require('cors');
const app = express();
const port = 8000;

// 启用CORS
app.use(cors());
app.use(express.json());

// 模拟智能体列表
const mockAgents = [
  {
    id: 'agent-1',
    appId: 'app-123',
    name: '智能助手',
    description: '通用智能助手',
    avatar: '',
    status: 'active'
  },
  {
    id: 'agent-2', 
    appId: 'app-456',
    name: '代码助手',
    description: '专业代码助手',
    avatar: '',
    status: 'active'
  }
];

// 模拟参数配置数据
const mockParameterConfigs = {
  'app-123': {
    code: 200,
    message: '操作成功',
    data: {
      opening_statement: '你好！我是智能助手，有什么可以帮助你的吗？',
      suggested_questions: ['你能做什么？', '如何使用你？'],
      suggested_questions_after_answer: {
        enabled: false
      },
      speech_to_text: {
        enabled: false
      },
      text_to_speech: {
        enabled: false,
        voice: '',
        language: ''
      },
      retriever_resource: {
        enabled: true
      },
      annotation_reply: {
        enabled: false
      },
      more_like_this: {
        enabled: false
      },
      user_input_form: [
        {
          select: {
            variable: 'model',
            label: '模型',
            type: 'select',
            max_length: 48,
            required: true,
            options: ['deepseek-r1', 'qwen3', 'gpt-4']
          }
        },
        {
          variable: 'topic',
          label: '主题',
          type: 'text-input',
          max_length: 48,
          required: true,
          options: []
        }
      ],
      sensitive_word_avoidance: {
        enabled: false
      },
      file_upload: {
        enabled: false,
        allowed_file_types: ['image'],
        allowed_file_extensions: ['.JPG', '.JPEG', '.PNG', '.GIF', '.WEBP', '.SVG'],
        allowed_file_upload_methods: ['local_file', 'remote_url'],
        number_limits: 3,
        fileUploadConfig: {
          file_size_limit: 15,
          batch_count_limit: 5,
          image_file_size_limit: 10,
          video_file_size_limit: 100,
          audio_file_size_limit: 50,
          workflow_file_upload_limit: 10
        }
      },
      system_parameters: {
        image_file_size_limit: 10,
        video_file_size_limit: 100,
        audio_file_size_limit: 50,
        file_size_limit: 15,
        workflow_file_upload_limit: 10
      }
    },
    timestamp: new Date().toISOString(),
    path: null,
    success: true,
    fail: false
  },
  'app-456': {
    code: 200,
    message: '操作成功',
    data: {
      opening_statement: '我是代码助手，专门帮助你解决编程问题。',
      suggested_questions: ['如何写一个函数？', '代码优化建议'],
      user_input_form: [
        {
          select: {
            variable: 'language',
            label: '编程语言',
            type: 'select',
            max_length: 20,
            required: true,
            options: ['JavaScript', 'Python', 'Java', 'TypeScript']
          }
        },
        {
          variable: 'difficulty',
          label: '难度级别',
          type: 'text-input',
          max_length: 20,
          required: false,
          options: []
        }
      ]
    },
    timestamp: new Date().toISOString(),
    success: true,
    fail: false
  }
};

// 获取智能体列表
app.get('/api/ai/agents', (req, res) => {
  console.log('📡 收到获取智能体列表请求');
  res.json({
    code: 200,
    message: '操作成功',
    data: mockAgents,
    success: true,
    fail: false
  });
});

// 获取聊天参数配置
app.get('/api/ai/chat/parameters/:appId', (req, res) => {
  const { appId } = req.params;
  console.log('📡 收到获取参数配置请求，appId:', appId);
  
  const config = mockParameterConfigs[appId];
  if (config) {
    console.log('✅ 返回参数配置:', config);
    res.json(config);
  } else {
    console.log('⚠️ 未找到对应的参数配置');
    res.json({
      code: 404,
      message: '未找到配置',
      data: null,
      success: false,
      fail: true
    });
  }
});

// 模拟流式聊天接口
app.post('/api/ai/chat/streaming', (req, res) => {
  console.log('📡 收到流式聊天请求:', req.body);
  
  // 设置SSE响应头
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': '*'
  });

  // 模拟流式响应
  const messages = [
    '你好！',
    '我收到了你的参数配置：',
    JSON.stringify(req.body.inputs, null, 2),
    '这是一个模拟的回复。'
  ];

  let index = 0;
  const interval = setInterval(() => {
    if (index < messages.length) {
      const data = {
        event: 'message',
        data: {
          answer: messages[index],
          conversation_id: 'mock-conversation-id',
          message_id: 'mock-message-id'
        }
      };
      res.write(`data: ${JSON.stringify(data)}\n\n`);
      index++;
    } else {
      res.write(`data: [DONE]\n\n`);
      res.end();
      clearInterval(interval);
    }
  }, 500);

  // 处理客户端断开连接
  req.on('close', () => {
    clearInterval(interval);
    res.end();
  });
});

app.listen(port, () => {
  console.log(`🚀 模拟服务器运行在 http://localhost:${port}`);
  console.log('📋 可用的接口:');
  console.log('  GET  /api/ai/agents - 获取智能体列表');
  console.log('  GET  /api/ai/chat/parameters/:appId - 获取参数配置');
  console.log('  POST /api/ai/chat/streaming - 流式聊天');
});
