<template>
  <div class="graph-statistics-panel">
    <!-- 节点类型统计 -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">节点类型</h4>
      <div class="space-y-2">
        <div
          v-for="nodeType in nodeTypeStats"
          :key="nodeType.type"
          class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          :class="{ 'bg-blue-50 border border-blue-200': selectedNodeTypes.includes(nodeType.type) }"
          @click="toggleNodeType(nodeType.type)"
        >
          <div class="flex items-center gap-2">
            <div
              class="w-3 h-3 rounded-full"
              :style="{ backgroundColor: nodeType.color }"
            ></div>
            <span class="text-sm text-gray-700">{{ nodeType.label }}</span>
          </div>
          <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ nodeType.count }}</span>
        </div>
      </div>
    </div>

    <!-- 关系类型统计 -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">关系类型</h4>
      <div class="space-y-2">
        <div
          v-for="edgeType in edgeTypeStats"
          :key="edgeType.type"
          class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          :class="{ 'bg-green-50 border border-green-200': selectedEdgeTypes.includes(edgeType.type) }"
          @click="toggleEdgeType(edgeType.type)"
        >
          <div class="flex items-center gap-2">
            <div class="w-3 h-1 bg-gray-400 rounded"></div>
            <span class="text-sm text-gray-700">{{ edgeType.label }}</span>
          </div>
          <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ edgeType.count }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <button
        @click="applyFilter"
        :disabled="selectedNodeTypes.length === 0 && selectedEdgeTypes.length === 0"
        class="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
      >
        应用筛选
      </button>
      <button
        @click="resetFilter"
        class="px-3 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
      >
        重置
      </button>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { Node, Edge } from '@vue-flow/core'

interface Props {
  nodes: Node[]
  edges: Edge[]
}

interface Emits {
  (e: 'node-type-select', nodeTypes: string[]): void
  (e: 'edge-type-select', edgeTypes: string[]): void
  (e: 'reset-filter'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const selectedNodeTypes = ref<string[]>([])
const selectedEdgeTypes = ref<string[]>([])

// 节点类型颜色映射
const nodeTypeColors: Record<string, string> = {
  'case': '#ef4444',      // 案件 - 红色
  'person': '#3b82f6',    // 人员 - 蓝色
  'evidence': '#f59e0b',  // 证据 - 橙色
  'location': '#10b981',  // 地点 - 绿色
  'time': '#8b5cf6',      // 时间 - 紫色
  'law': '#6b7280',       // 法律 - 灰色
  'info': '#ec4899',      // 信息 - 粉色
  'company': '#059669',   // 公司 - 深绿色
  'phone': '#0891b2',     // 电话 - 青色
  'address': '#dc2626',   // 地址 - 深红色
  'vehicle': '#7c3aed',   // 车辆 - 深紫色
  'account': '#ea580c',   // 账户 - 深橙色
  'contact': '#4338ca',   // 联系人 - 靛蓝色
  'insurance': '#16a34a', // 保险 - 中绿色
  'violation': '#dc2626', // 违章 - 深红色
  'maintenance': '#0d9488', // 保养 - 青绿色
  'route': '#7c2d12',     // 路线 - 棕色
  'traffic': '#1e40af',   // 交通 - 深蓝色
  'transaction': '#be123c', // 交易 - 深粉色
  'risk': '#dc2626',      // 风险 - 深红色
  'merchant': '#059669',  // 商户 - 深绿色
  'record': '#4338ca',    // 记录 - 靛蓝色
  'executive': '#7c3aed', // 高管 - 深紫色
  'investment': '#ea580c', // 投资 - 深橙色
  'business': '#16a34a',  // 业务 - 中绿色
  'malware': '#dc2626',   // 恶意软件 - 深红色
  'attacker': '#991b1b',  // 攻击者 - 暗红色
  'vulnerability': '#f59e0b', // 漏洞 - 橙色
  'target': '#3b82f6',    // 目标 - 蓝色
  'technique': '#8b5cf6', // 技术 - 紫色
  'threat': '#ec4899',    // 威胁 - 粉色
  'entity': '#6b7280',    // 实体 - 灰色
  'concept': '#10b981'    // 概念 - 绿色
}

// 节点类型标签映射
const nodeTypeLabels: Record<string, string> = {
  'case': '案件',
  'person': '人员',
  'evidence': '证据',
  'location': '地点',
  'time': '时间',
  'law': '法律条文',
  'info': '相关信息',
  'company': '公司',
  'phone': '电话',
  'address': '地址',
  'vehicle': '车辆',
  'account': '账户',
  'contact': '联系人',
  'insurance': '保险',
  'violation': '违章',
  'maintenance': '保养',
  'route': '路线',
  'traffic': '交通记录',
  'transaction': '交易',
  'risk': '风险',
  'merchant': '商户',
  'record': '记录',
  'executive': '高管',
  'investment': '投资',
  'business': '业务',
  'malware': '恶意软件',
  'attacker': '攻击者',
  'vulnerability': '漏洞',
  'target': '目标',
  'technique': '攻击技术',
  'threat': '威胁',
  'entity': '实体',
  'concept': '概念'
}

// 计算属性
const nodeTypeStats = computed(() => {
  const typeCount: Record<string, number> = {}
  
  props.nodes.forEach(node => {
    const type = node.data?.type || 'unknown'
    typeCount[type] = (typeCount[type] || 0) + 1
  })
  
  return Object.entries(typeCount).map(([type, count]) => ({
    type,
    label: nodeTypeLabels[type] || type,
    count,
    color: nodeTypeColors[type] || '#6b7280'
  })).sort((a, b) => b.count - a.count)
})

const edgeTypeStats = computed(() => {
  const typeCount: Record<string, number> = {}
  
  props.edges.forEach(edge => {
    const type = edge.data?.relationship || edge.label || 'unknown'
    typeCount[type] = (typeCount[type] || 0) + 1
  })
  
  return Object.entries(typeCount).map(([type, count]) => ({
    type,
    label: type,
    count
  })).sort((a, b) => b.count - a.count)
})

// 方法
const toggleNodeType = (type: string) => {
  const index = selectedNodeTypes.value.indexOf(type)
  if (index > -1) {
    selectedNodeTypes.value.splice(index, 1)
  } else {
    selectedNodeTypes.value.push(type)
  }
}

const toggleEdgeType = (type: string) => {
  const index = selectedEdgeTypes.value.indexOf(type)
  if (index > -1) {
    selectedEdgeTypes.value.splice(index, 1)
  } else {
    selectedEdgeTypes.value.push(type)
  }
}

const applyFilter = () => {
  emit('node-type-select', [...selectedNodeTypes.value])
  emit('edge-type-select', [...selectedEdgeTypes.value])
}

const resetFilter = () => {
  selectedNodeTypes.value = []
  selectedEdgeTypes.value = []
  emit('reset-filter')
}

// 监听器
watch([selectedNodeTypes, selectedEdgeTypes], () => {
  // 自动应用筛选
  applyFilter()
}, { deep: true })
</script>

<style scoped>
.graph-statistics-panel {
  height: calc(100vh - 270px);
  overflow-y: auto;
}

.graph-statistics-panel::-webkit-scrollbar {
  width: 4px;
}

.graph-statistics-panel::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.graph-statistics-panel::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.graph-statistics-panel::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
