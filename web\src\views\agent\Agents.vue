<template>
  <div class="container overflow-auto bg-gradient-secondary">
    <!-- 搜索和过滤区域 -->
    <div class="filter-section">
      <div class="filter-header">
        <h1 class="page-title">
          <i class="fas fa-robot"></i>
          智能体管理平台<span v-if="projectName" class="text-yellow-500 text-lg mt-15" style="padding-top: 15px">（{{projectName}}）</span>
        </h1>
        <div class="header-actions">
          <!-- 最小化任务栏 -->
          <MinimizedTaskbar
            :running-agents="minimizedAgents"
            @restore="restoreRunner"
            @close="closeSpecificAgent"
          />

          <button class="btn btn-primary" @click="showCreateAgentModal">
            <i class="fas fa-plus"></i>
            创建智能体
          </button>
          <button class="btn btn-secondary" @click="showAssociateModal">
            <i class="fas fa-link"></i>
            关联第三方智能体
          </button>
        </div>
      </div>

      <div class="filter-controls">
        <div class="type-filter-row">
          <div class="type-filters">
            <button
              v-for="typeFilter in typeFilters"
              :key="typeFilter.type"
              :class="['type-filter-btn', { active: selectedType === typeFilter.type }]"
              @click="handleTypeFilter(typeFilter.type)"
            >
              {{ typeFilter.label }}
              <span class="type-count">{{ typeFilter.count }}</span>
            </button>
          </div>

          <div class="main-filter-row">
            
            <div class="search-box">
              <i class="fas fa-search"></i>
              <input
                type="text"
                v-model="searchQuery"
                placeholder="搜索智能体名称或描述..."
                @input="handleSearch"
              >
            </div>

            <div class="filter-group">
              <select v-model="selectedUnit" @change="applyFilters">
                <option value="">全部单位</option>
                <option value="技术部">技术部</option>
                <option value="产品部">产品部</option>
                <option value="运营部">运营部</option>
                <option value="研发中心">研发中心</option>
                <option value="客服部">客服部</option>
                <option value="市场部">市场部</option>
                <option value="电商部">电商部</option>
                <option value="数据部">数据部</option>
                <option value="办公室">办公室</option>
                <option value="知识部">知识部</option>
                <option value="项目部">项目部</option>
                <option value="财务部">财务部</option>
                <option value="人事部">人事部</option>
                <option value="仓储部">仓储部</option>
                <option value="测试部">测试部</option>
              </select>
            </div>

            <div class="filter-group">
              <select v-model="selectedTag" @change="applyFilters">
                <option value="">全部标签</option>
                <option value="对话">对话</option>
                <option value="分析">分析</option>
                <option value="创作">创作</option>
                <option value="工具">工具</option>
                <option value="助手">助手</option>
                <option value="自动化">自动化</option>
                <option value="流程">流程</option>
                <option value="客服">客服</option>
                <option value="营销">营销</option>
                <option value="管理">管理</option>
                <option value="电商">电商</option>
                <option value="订单">订单</option>
                <option value="报告">报告</option>
                <option value="数据">数据</option>
                <option value="会议">会议</option>
                <option value="记录">记录</option>
                <option value="聊天">聊天</option>
                <option value="机器人">机器人</option>
                <option value="知识">知识</option>
                <option value="项目">项目</option>
                <option value="财务">财务</option>
                <option value="审批">审批</option>
                <option value="招聘">招聘</option>
                <option value="人事">人事</option>
                <option value="库存">库存</option>
                <option value="仓储">仓储</option>
                <option value="开发中">开发中</option>
                <option value="测试">测试</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体卡片网格 -->
    <div class="agents-grid">
      <!-- 加载异常提示 -->
      <div v-if="loadError" class="error-message" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
        <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 20px; color: #dc3545; opacity: 0.8;"></i>
        <h3 style="color: #dc3545; margin-bottom: 10px;">加载异常</h3>
        <p style="color: #6c757d; margin-bottom: 20px;">{{ errorMessage }}</p>
        <button class="btn btn-primary" @click="retryLoad">
          <i class="fas fa-redo"></i>
          重新加载
        </button>
      </div>

      <!-- 无数据提示 -->
      <div v-else-if="!loading && filteredAgents.length === 0" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #6c757d;">
        <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
        <h3>未找到匹配的智能体</h3>
        <p>请尝试调整搜索条件或创建新的智能体</p>
      </div>

      <div
        v-for="agent in filteredAgents"
        :key="agent.id"
        class="agent-card"
        @click="openRunnerModal(agent)"
        @contextmenu.prevent="showContextMenu($event, agent)"
      >
        <button class="card-menu-btn" title="更多操作" @click.stop="showContextMenu($event, agent)">
          <i class="fas fa-ellipsis-v"></i>
        </button>
        <div class="agent-header">
          <div class="agent-icon" :style="{ background: agent.iconBackground || `linear-gradient(135deg, ${getRandomColor()} 0%, ${getRandomColor()}aa 100%)` }">
            <i :class="agent.icon"></i>
          </div>
          <div class="agent-info">
            <div class="agent-title-row">
              <h3>{{ agent.name }}</h3>
              <HoverTooltip
                v-if="agent.sourceType === 'external'"
                placement="top"
                :delay="300"
              >
                <template #trigger>
                  <span class="external-badge" title="外部智能体">
                    <i class="fas fa-external-link"></i>
                    外部
                  </span>
                </template>
                <template #content>
                  <ExternalAgentTooltip
                    v-if="getExternalAgentDetails(agent)"
                    :details="getExternalAgentDetails(agent)!"
                  />
                  <div v-else class="loading-tooltip">
                    <i class="fas fa-spinner fa-spin"></i>
                    加载详细信息...
                  </div>
                </template>
              </HoverTooltip>
            </div>
            <span class="agent-type">{{ agent.type }}</span>
          </div>
        </div>
        <div class="agent-description">{{ agent.description || '' }}</div>
        <div class="agent-tags">
          <span v-for="tag in agent.tags" :key="tag" class="agent-tag">{{ tag }}</span>
          <button class="add-tag-btn" title="添加标签" @click.stop="openTagModal(agent)">
            <i class="fas fa-plus"></i>
            添加
          </button>
        </div>
        <div class="agent-meta">
          <div class="agent-creator">
            <div><strong>{{ agent.unit }}</strong></div>
            <div>设计者：{{ agent.creator }}</div>
          </div>
          <div class="agent-date">{{ agent.createTime }}</div>
        </div>
      </div>
    </div>
    
    <!-- 分页控件 -->
    <div class="pagination-container">
      <Pagination
        v-if="filteredAgents.length > 0"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="filteredAgents.length"
        :show-page-size-selector="true"
        :show-page-numbers="true"
        :show-jumper="true"
        @change="handlePaginationChange"
      />
    </div>

    <!-- 右键菜单 -->
    <div class="context-menu" ref="contextMenu" v-show="contextMenuVisible" :style="contextMenuStyle">
      <div
        class="menu-item"
        @click="handleContextMenuClick('choreographed')"
      >
        <i class="fas fa-play"></i>
        编排
      </div>
      <div class="menu-item" @click="handleContextMenuClick('edit')">
        <i class="fas fa-edit"></i>
        编辑
      </div>
      <div class="menu-item" @click="handleContextMenuClick('clone')">
        <i class="fas fa-copy"></i>
        克隆
      </div>
      <div class="menu-item" @click="handleContextMenuClick('share-clone')">
        <i class="fas fa-share-alt"></i>
        共享克隆
      </div>
      <div class="menu-item" @click="handleContextMenuClick('publish')">
        <i class="fas fa-share"></i>
        发布
      </div>
      <div class="menu-item" @click="handleContextMenuClick('api-key')">
        <i class="fas fa-key"></i>
        申请API密钥
      </div>
      <div class="menu-item menu-item-danger" @click="handleContextMenuClick('delete')">
        <i class="fas fa-trash"></i>
        删除
      </div>
    </div>

    <!-- 标签编辑组件 -->
    <TagEditor
      v-model:visible="tagModalVisible"
      :tags="currentEditingAgent?.tags || []"
      @save="handleTagSave"
    />

    <!-- 发布模态框 -->
    <PublishModal
      v-model:visible="publishModalVisible"
      :agent="currentPublishAgent"
      @publish="handlePublish"
    />

    <!-- 编辑智能体模态框 -->
    <EditAgentModal
      v-model:visible="editModalVisible"
      :agent="currentEditingAgent"
      @save="handleAgentEditSave"
    />

    <!-- 共享克隆模态框 -->
    <ShareCloneModal
      v-model:visible="shareModalVisible"
      :agent="currentShareAgent"
      @share="handleShare"
    />

    <!-- 创建智能体模态框 -->
    <CreateAgentModal
      v-model:visible="createAgentModalVisible"
      @create="handleCreateAgent"
      @refresh="handleRefreshAgents"
    />

    <!-- 智能体运行弹出层 -->
    <AgentRunnerWindow
      :visible="runnerModalVisible"
      :agent-info="currentRunningAgent"
      :is-minimized="isMinimized"
      :is-maximized="isMaximized"
      :position="windowPosition"
      :size="windowSize"
      @minimize="minimizeRunner"
      @close="closeRunnerModal"
      @update:position="updatePosition"
      @update:size="updateSize"
      @update:is-maximized="updateMaximized"
    />



    <!-- 删除确认模态框 -->
    <div class="delete-confirm-modal" v-show="deleteConfirmVisible" @click="closeDeleteConfirm">
      <div class="delete-confirm-content" @click.stop>
        <div class="delete-confirm-header">
          <div class="warning-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h3>确认删除</h3>
        </div>

        <div class="delete-confirm-body">
          <p class="delete-message">
            您确定要删除智能体 <strong>"{{ currentDeleteAgent?.name }}"</strong> 吗？
          </p>
          <div class="delete-warning">
            <i class="fas fa-info-circle"></i>
            <span>此操作不可撤销，删除后将无法恢复该智能体的所有数据。</span>
          </div>
        </div>

        <div class="delete-confirm-footer">
          <button class="btn-modern btn-secondary" @click="closeDeleteConfirm">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button class="btn-modern btn-danger" @click="confirmDelete">
            <i class="fas fa-trash"></i>
            确认删除
          </button>
        </div>
      </div>
    </div>

    <!-- API密钥申请模态框 -->
    <ApiKeyModal
      v-model:visible="apiKeyModalVisible"
      :agent="currentApiKeyAgent"
      :api-key-list="apiKeyList"
      @submit="handleApiKeySubmit"
    />

    <!-- 关联第三方智能体模态框 -->
    <AssociateThirdPlatform
      v-model:visible="associateModalVisible"
      @associate="handleAssociateAgents"
    />

    <!-- 遮罩层 -->
    <div class="overlay" v-show="contextMenuVisible || tagModalVisible || deleteConfirmVisible" @click="hideContextMenu"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { AgentsAPI, type Agent, type AgentVO, type CreateAgentRequest, type ExternalAgentDetails } from '@/api/agents'

// 扩展AgentVO，添加前端显示需要的字段
interface ExtendedAgentVO extends Omit<AgentVO, 'description'> {
  description: string // 确保description不为undefined
  icon: string        // 从avatar字段映射而来
  unit: string        // 从createByDeptName字段映射而来
  creator: string     // 从createByName/createByUsername字段映射而来
  createTime: string  // 格式化后的创建时间
  tags: string[]      // 前端维护的标签数组
  platformId?: string // 第三方平台ID
  appId?: string      // 应用ID
  agentType?: string  // 智能体类型
  sourceType?: string // 智能体来源类型
}
import { useAgentRunner } from '@/composables/useAgentRunner'
import MinimizedTaskbar from '@/views/agent/MinimizedTaskbar.vue'
import AgentRunnerWindow from '@/views/agent/AgentRunnerWindow.vue'
import TagEditor from '@/views/agent/TagEditor.vue'
import PublishModal from '@/views/agent/PublishModal.vue'
import HoverTooltip from '@/components/common/HoverTooltip.vue'
import ExternalAgentTooltip from '@/views/agent/ExternalAgentTooltip.vue'
import EditAgentModal from '@/views/agent/EditAgentModal.vue'
import ShareCloneModal from '@/views/agent/ShareCloneModal.vue'
import CreateAgentModal from '@/views/agent/CreateAgentModal.vue'
import ApiKeyModal from '@/views/agent/ApiKeyModal.vue'
import AssociateThirdPlatform from '@/components/agent/AssociateThirdPlatform.vue'
import Pagination from '@/components/common/Pagination.vue'

// 使用路由
const router = useRouter()
const route = useRoute()

// 使用智能体运行管理器
const {
  runningAgents,
  runnerModalVisible,
  isMinimized,
  isMaximized,
  windowPosition,
  windowSize,
  currentRunningAgent,
  minimizedAgents,
  openRunnerModal,
  closeRunnerModal,
  minimizeRunner,
  restoreRunner,
  closeSpecificAgent,
  updatePosition,
  updateSize,
  updateMaximized
} = useAgentRunner({
  onAgentOpened: (agent) => {
    console.log('智能体已打开:', agent.name)
  },
  onAgentClosed: (agentId) => {
    console.log('智能体已关闭:', agentId)
  },
  onAgentMinimized: (agentId) => {
    console.log('智能体已最小化:', agentId)
  },
  onAgentRestored: (agentId) => {
    console.log('智能体已恢复:', agentId)
  }
})

// 定义用户类型
interface User {
  id: number
  name: string
  unit: string
  role: string
  email: string
}

// 定义上下文菜单样式类型
interface ContextMenuStyle {
  left?: string
  top?: string
}

// 定义API密钥类型
interface ApiKey {
  id: string
  agentId: string | number
  agentName: string
  applicantUnit: string
  applicantName: string
  applicantPhone: string
  responsibleUnit: string
  responsibleName: string
  responsiblePhone: string
  accessFrequency: string
  accessCount: string
  status: 'approved' | 'pending' | 'rejected'
  statusText: string
  createTime: string
  approveTime?: string
  rejectTime?: string
  rejectReason?: string
}

// 响应式数据
const searchQuery = ref('')
const selectedUnit = ref('')
const selectedTag = ref('')
const selectedType = ref('')
const currentPage = ref(1)
const pageSize = ref(40)
const contextMenuVisible = ref(false)
const tagModalVisible = ref(false)
const publishModalVisible = ref(false)
const editModalVisible = ref(false)
const shareModalVisible = ref(false)
const createAgentModalVisible = ref(false)
const apiKeyModalVisible = ref(false)
const associateModalVisible = ref(false)
const deleteConfirmVisible = ref(false)
const currentDeleteAgent = ref<ExtendedAgentVO | null>(null)
const contextMenuStyle = ref<ContextMenuStyle>({})
const currentContextAgent = ref<ExtendedAgentVO | null>(null)
const currentEditingAgent = ref<ExtendedAgentVO | null>(null)
const currentPublishAgent = ref<ExtendedAgentVO | null>(null)
const currentShareAgent = ref<ExtendedAgentVO | null>(null)


const contextMenu = ref<HTMLElement | null>(null)
const projectId = ref('')
const projectName = ref('')

// API密钥申请相关
const currentApiKeyAgent = ref<ExtendedAgentVO | null>(null)
const apiKeyList = ref<ApiKey[]>([
  {
    id: 'API_AGENT_001',
    agentId: 1,
    agentName: 'GPT-4智能助手',
    applicantUnit: '北京科技有限公司',
    applicantName: '张三',
    applicantPhone: '13800138001',
    responsibleUnit: '技术部',
    responsibleName: '李四',
    responsiblePhone: '13800138002',
    accessFrequency: 'medium',
    accessCount: '1000',
    status: 'approved',
    statusText: '已通过',
    createTime: '2024-01-15 10:30:00',
    approveTime: '2024-01-16 14:20:00'
  },
  {
    id: 'API_AGENT_002',
    agentId: 2,
    agentName: '代码生成助手',
    applicantUnit: '上海软件开发公司',
    applicantName: '王五',
    applicantPhone: '13800138003',
    responsibleUnit: '研发中心',
    responsibleName: '赵六',
    responsiblePhone: '13800138004',
    accessFrequency: 'high',
    accessCount: '5000',
    status: 'pending',
    statusText: '审核中',
    createTime: '2024-01-20 09:15:00'
  },
  {
    id: 'API_AGENT_003',
    agentId: 3,
    agentName: '文档分析专家',
    applicantUnit: '深圳数据分析公司',
    applicantName: '陈七',
    applicantPhone: '13800138005',
    responsibleUnit: '数据部',
    responsibleName: '刘八',
    responsiblePhone: '13800138006',
    accessFrequency: 'low',
    accessCount: '500',
    status: 'rejected',
    statusText: '已拒绝',
    createTime: '2024-01-18 16:45:00',
    rejectTime: '2024-01-19 11:30:00',
    rejectReason: '申请频率过低，不符合使用要求'
  },
  {
    id: 'API_AGENT_004',
    agentId: 1,
    agentName: 'GPT-4智能助手',
    applicantUnit: '广州创新科技',
    applicantName: '周九',
    applicantPhone: '13800138007',
    responsibleUnit: '产品部',
    responsibleName: '吴十',
    responsiblePhone: '13800138008',
    accessFrequency: 'medium',
    accessCount: '2000',
    status: 'pending',
    statusText: '审核中',
    createTime: '2024-01-22 14:20:00'
  },
  {
    id: 'API_AGENT_005',
    agentId: 4,
    agentName: '智能客服机器人',
    applicantUnit: '杭州电商平台',
    applicantName: '郑十一',
    applicantPhone: '13800138009',
    responsibleUnit: '客服部',
    responsibleName: '孙十二',
    responsiblePhone: '13800138010',
    accessFrequency: 'high',
    accessCount: '10000',
    status: 'approved',
    statusText: '已通过',
    createTime: '2024-01-10 08:30:00',
    approveTime: '2024-01-11 16:45:00'
  }
])

// 创建智能体相关数据 - 已迁移到 CreateAgentModal 组件

// 智能体数据 - 使用扩展的AgentVO类型
const agentsData = ref<ExtendedAgentVO[]>([])
const loading = ref(false)
const loadError = ref(false)
const errorMessage = ref('')
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 搜索参数
const searchParams = ref({
  name: '',
  mode: '',
  orgId: '',
  tagIds: [],
  is_created_by_me: false
})



// 加载智能体数据
const loadAgents = async () => {
  loading.value = true
  loadError.value = false
  errorMessage.value = ''
  try {
    const response = await AgentsAPI.getAgents({
      current: pagination.value.currentPage,
      size: pagination.value.pageSize,
      projectId: projectId.value,
      ...searchParams.value
    })

    if (response.success && response.data) {
      // 后端返回的数据结构中，数据列表字段是 records，不是 list
      const records = response.data.records || []

      if (records.length > 0) {
        // 添加调试日志
        console.log('原始数据示例:', records[0])

        agentsData.value = records.map((item: AgentVO): ExtendedAgentVO => {
          const mappedType = getTypeLabel(item.type || 'chat')

          // 调试每个智能体的类型映射
          if (item.type && item.type !== 'chat') {
            console.log(`智能体 "${item.name}": 原始类型=${item.type}, 映射类型=${mappedType}, sourceType=${item.sourceType}`)
          }

          return {
            // 直接展开AgentVO的所有字段
            ...item,
            // 只映射需要转换的字段
            description: item.description || '',
            icon: item.avatar || 'fas fa-robot',
            unit: item.createByDeptName || '未知部门',
            creator: item.createByName || item.createByUsername || '系统',
            createTime: item.createTime ? new Date(item.createTime).toISOString().split('T')[0] : '',
            // 强制使用前端的类型映射，确保显示一致性
            type: mappedType,
            tags: [], // 前端维护的标签数组
            // 添加编排功能需要的字段
            platformId: item.platformId,
            appId: item.appId || item.externalAgentId, // 如果没有appId，使用externalAgentId
            agentType: item.type, // 使用原始的type字段作为agentType
            sourceType: item.sourceType
          }
        })

        // 调试处理后的数据
        console.log('处理后数据示例:', agentsData.value[0])
        console.log('类型统计:', getTypeStats())

        pagination.value.total = response.data.total || 0
      } else {
        // 如果后端返回空数据，清空列表
        agentsData.value = []
        pagination.value.total = 0
      }
    } else {
      // 如果接口调用失败，清空数据并设置错误状态
      console.error('接口调用失败:', response.message || '未知错误')
      agentsData.value = []
      pagination.value.total = 0
      loadError.value = true
      errorMessage.value = response.message || '加载智能体数据失败，请稍后重试'
    }
  } catch (error) {
    console.error('加载智能体失败:', error)
    // 如果接口调用失败，清空数据并设置错误状态
    agentsData.value = []
    pagination.value.total = 0
    loadError.value = true
    errorMessage.value = '网络连接异常，无法加载智能体数据，请检查网络连接后重试'
  } finally {
    loading.value = false
  }
}

// 重新加载数据
const retryLoad = () => {
  loadAgents()
}



// 获取外部智能体详细信息
const getExternalAgentDetails = (agent: ExtendedAgentVO): ExternalAgentDetails | null => {
  if (agent.sourceType !== 'external') {
    return null
  }

  // 优先使用新的 thirdPlatform 字段
  if (agent.thirdPlatform) {
    const tpa = agent.thirdPlatform
    return {
      platform: tpa.name || '未知平台',
      status: tpa.status === 1 ? 'connected' : 'disconnected',
      lastSync: tpa.updateTime || tpa.createTime || '',
      connectionUrl: tpa.connectionUrl,
      unitName: tpa.unitName,
      accessScope: tpa.accessScopeText,
      lastTestTime: tpa.lastTestTime,
      lastTestResult: tpa.lastTestResultText,
      lastTestError: tpa.lastTestError,
      createByName: tpa.createByName,
      updateByName: tpa.updateByName,
      config: {
        timeout: tpa.timeout
      }
    }
  }

  // 降级到旧的 externalAgentDetails 字段
  if (agent.externalAgentDetails) {
    try {
      return JSON.parse(agent.externalAgentDetails) as ExternalAgentDetails
    } catch (error) {
      console.error('解析外部智能体详细信息失败:', error)
    }
  }

  // 返回默认的详细信息
  return {
    platform: agent.externalAgentName || '未知平台',
    status: 'disconnected',
    lastSync: agent.updateTime || '',
    error: '无法获取详细信息'
  }
}

// 初始化加载
onMounted(() => {
  projectId.value = route.query.projectId as string
  projectName.value = route.query.projectName as string
  loadAgents()
})


// 获取智能体类型统计
const getTypeStats = (): Record<string, number> => {
  const typeStats: Record<string, number> = {}
  agentsData.value.forEach(agent => {
    typeStats[agent.type] = (typeStats[agent.type] || 0) + 1
  })
  return typeStats
}

// 类型过滤器
const typeFilters = computed(() => {
  const typeStats = getTypeStats()
  const allowedTypes = ['Chatflow', '工作流', '聊天助手', 'Agent', '文本生成']

  // 调试类型统计
  console.log('类型统计详情:', typeStats)
  console.log('允许的类型:', allowedTypes)

  const filters = [
    { type: '', label: '全部类型', count: agentsData.value.length }
  ]

  allowedTypes.forEach(type => {
    const count = typeStats[type] || 0
    console.log(`类型 "${type}" 的数量: ${count}`)
    if (count > 0) {
      filters.push({ type, label: type, count })
    }
  })

  console.log('最终过滤器:', filters)
  return filters
})

// 过滤后的智能体
const filteredAgents = computed(() => {
  const searchTerm = searchQuery.value.toLowerCase().trim()

  return agentsData.value.filter(agent => {
    const matchesSearch = !searchTerm ||
      agent.name.toLowerCase().includes(searchTerm) ||
      (agent.description || '').toLowerCase().includes(searchTerm)

    const matchesUnit = !selectedUnit.value || agent.unit === selectedUnit.value
    const matchesTag = !selectedTag.value || agent.tags.includes(selectedTag.value)
    const matchesType = !selectedType.value || agent.type === selectedType.value

    return matchesSearch && matchesUnit && matchesTag && matchesType
  })
})


// 分页处理方法
const handlePaginationChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 方法
const handleSearch = () => {
  applyFilters()
}

const handleTypeFilter = (type: string) => {
  selectedType.value = type
  applyFilters()
}

const applyFilters = () => {
  currentPage.value = 1
}

// 显示创建智能体模态框
const showCreateAgentModal = () => {
  createAgentModalVisible.value = true
}

// 显示关联第三方智能体模态框
const showAssociateModal = () => {
  associateModalVisible.value = true
}

// 处理创建智能体事件（从 CreateAgentModal 组件传来）
const handleCreateAgent = async (createRequest: CreateAgentRequest | null) => {
  // 如果createRequest为null，说明是第三方智能体创建，已经在组件内部处理了
  if (!createRequest) {
    return
  }

  try {
    // 显示加载状态
    loading.value = true

    // 调用API创建智能体
    const response = await AgentsAPI.createAgent(createRequest)

    if (response.success) {
      // 创建成功，获取返回的智能体ID
      const agentId = response.data

      // 创建本地显示的智能体对象
      const newAgent: Agent = {
        id: agentId,
        name: createRequest.name,
        description: createRequest.description || '',
        icon: createRequest.avatar || 'fas fa-robot',
        iconBackground: createRequest.iconBackground,
        unit: '技术部', // 默认单位，实际应该从用户信息获取
        creator: '当前用户', // 默认创建者，实际应该从用户信息获取
        createTime: new Date().toISOString().split('T')[0],
        type: getTypeLabel(createRequest.type),
        tags: []
      }

      // 添加到智能体列表
      agentsData.value.unshift(newAgent)

      // 显示成功消息
      alert(`智能体"${newAgent.name}"创建成功！`)
    } else {
      // 创建失败
      alert(`创建智能体失败：${response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('创建智能体失败:', error)
    alert('创建智能体失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理刷新智能体列表事件
const handleRefreshAgents = async () => {
  await loadAgents()
}

// 处理关联第三方智能体事件
const handleAssociateAgents = async (associatedAgents: any[]) => {
  try {
    loading.value = true
    let successCount = 0
    let failCount = 0

    // 逐个创建智能体
    for (const agent of associatedAgents) {
      try {
        // 直接使用智能体mode字段作为type字段，不进行映射转换
        const getDirectType = (mode: string): 'advanced-chat' | 'workflow' | 'chat' | 'agent-chat' | 'completion' => {
          // 验证mode值是否为有效的类型
          const validTypes: ('advanced-chat' | 'workflow' | 'chat' | 'agent-chat' | 'completion')[] = [
            'advanced-chat', 'workflow', 'chat', 'agent-chat', 'completion'
          ]

          if (validTypes.includes(mode as any)) {
            return mode as 'advanced-chat' | 'workflow' | 'chat' | 'agent-chat' | 'completion'
          }

          // 如果mode值不在有效类型中，使用默认值
          return 'workflow'
        }

        const createRequest: CreateAgentRequest = {
          name: agent.name,
          description: agent.description || '',
          avatar: 'fas fa-sitemap',
          iconBackground: 'linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)',
          type: getDirectType(agent.mode || ''),
          isPublic: false,
          version: '1.0.0',
          sourceType: 'external',
          externalAgentId: agent.id,
          platformId: agent.platformId || agent.thirdPlatform?.id || ''
        }

        // 调用API创建智能体
        const response = await AgentsAPI.createAgent(createRequest)

        if (response.success) {
          // 创建成功，获取返回的智能体ID
          const agentId = response.data

          // 创建本地显示的智能体对象
          const newAgent: Agent = {
            id: agentId,
            name: createRequest.name,
            description: createRequest.description || '',
            icon: createRequest.avatar || 'fas fa-robot',
            iconBackground: createRequest.iconBackground,
            unit: agent.thirdPlatform?.unitName || '外部平台',
            creator: agent.thirdPlatform?.createByName || '外部系统',
            createTime: new Date().toISOString().split('T')[0],
            type: getTypeLabel(createRequest.type),
            tags: [],
            sourceType: 'external',
            sourceTypeName: '外部智能体'
          }

          // 添加到智能体列表
          agentsData.value.unshift(newAgent)
          successCount++
        } else {
          console.error(`创建智能体"${agent.name}"失败:`, response.message)
          failCount++
        }
      } catch (error) {
        console.error(`创建智能体"${agent.name}"失败:`, error)
        failCount++
      }
    }

    // 显示结果消息
    if (successCount > 0 && failCount === 0) {
      alert(`成功关联 ${successCount} 个第三方智能体！`)
    } else if (successCount > 0 && failCount > 0) {
      alert(`成功关联 ${successCount} 个智能体，${failCount} 个失败`)
    } else {
      alert(`关联失败，请稍后重试`)
    }
  } catch (error) {
    console.error('关联第三方智能体失败:', error)
    alert('关联第三方智能体失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 辅助方法：将后端类型转换为前端显示标签
const getTypeLabel = (backendType: string): string => {
  const typeLabels: Record<string, string> = {
    'advanced-chat': 'Chatflow',
    'workflow': '工作流',
    'chat': '聊天助手',
    'agent-chat': 'Agent',
    'completion': '文本生成'
  }
  return typeLabels[backendType] || backendType
}

const getRandomColor = () => {
  const colors = ['#667eea', '#f093fb', '#4facfe', '#43e97b', '#fa709a', '#ffecd2']
  return colors[Math.floor(Math.random() * colors.length)]
}

const showContextMenu = (event: MouseEvent, agent: ExtendedAgentVO) => {
  currentContextAgent.value = agent
  contextMenuVisible.value = true

  contextMenuStyle.value = {
    left: event.pageX + 'px',
    top: event.pageY + 'px'
  }

  nextTick(() => {
    if (contextMenu.value) {
      const rect = contextMenu.value.getBoundingClientRect()
      if (rect.right > window.innerWidth) {
        contextMenuStyle.value.left = (event.pageX - rect.width) + 'px'
      }
      if (rect.bottom > window.innerHeight) {
        contextMenuStyle.value.top = (event.pageY - rect.height) + 'px'
      }
    }
  })
}

const hideContextMenu = () => {
  contextMenuVisible.value = false
  currentContextAgent.value = null
}

const handleContextMenuClick = (action: string) => {
  if (!currentContextAgent.value) return

  const agent = currentContextAgent.value

  if (action === 'choreographed') {
    // 处理智能体编排
    handleAgentChoreography(agent)
  } else if (action === 'publish') {
    openPublishModal(agent)
  } else if (action === 'edit') {
    openEditModal(agent)
  } else if (action === 'share-clone') {
    openShareModal(agent)
  } else if (action === 'delete') {
    openDeleteConfirm(agent)
  } else if (action === 'clone') {
    alert(`正在克隆智能体"${agent.name}"...\n\n功能开发中，敬请期待！`)
  } else if (action === 'api-key') {
    openApiKeyModal(agent)
  }

  hideContextMenu()
}

// 处理智能体编排
const handleAgentChoreography = async (agent: ExtendedAgentVO) => {
  try {
    // 添加调试日志
    console.log('智能体编排 - 智能体信息:', {
      id: agent.id,
      name: agent.name,
      type: agent.type,
      sourceType: agent.sourceType,
      platformId: agent.platformId,
      appId: agent.appId,
      agentType: agent.agentType
    })

    // 区分外部智能体和系统原生智能体
    if (agent.sourceType === 'external') {
      // 外部智能体：检查是否有platformId和appId
      if (!agent.platformId || !agent.appId) {
        ElMessage.error('智能体缺少必要的平台信息，无法进行编排')
        return
      }
    } else {
      // 系统原生智能体：跳转到系统原生编排页面
      console.log('智能体编排 - 系统原生智能体:', `sourceType=${agent.sourceType}`)
      handleNativeAgentChoreography(agent)
      return
    }

    // 根据智能体类型确定跳转URL
    let targetUrl = ''
    const baseUrl = 'http://localhost:3001'

    // 获取实际的智能体类型，优先使用agentType，如果没有则从后端type字段推断
    let actualType = agent.agentType
    if (!actualType) {
      // 从后端返回的type字段推断原始类型
      // 后端可能返回的是原始类型，需要进行反向映射
      const typeMapping: Record<string, string> = {
        'Chatflow': 'advanced-chat',
        '工作流': 'workflow',
        '聊天助手': 'chat',
        'Agent': 'agent-chat',
        '文本生成': 'completion'
      }
      actualType = typeMapping[agent.type] || agent.type
    }

    console.log('智能体编排 - 类型判断:', {
      originalType: agent.type,
      agentType: agent.agentType,
      actualType: actualType
    })

    if (actualType === 'workflow' || actualType === 'advanced-chat') {
      targetUrl = `${baseUrl}/app/${agent.appId}/workflow`
    } else if (actualType === 'chat' || actualType === 'agent-chat' || actualType === 'completion') {
      targetUrl = `${baseUrl}/app/${agent.appId}/configuration`
    } else {
      ElMessage.warning(`不支持的智能体类型：${actualType}（原始类型：${agent.type}）`)
      return
    }

    // 在新tab页面中打开编排界面
    const choreographyUrl = `/agent-choreography?targetUrl=${encodeURIComponent(targetUrl)}&platformId=${encodeURIComponent(agent.platformId)}&agentName=${encodeURIComponent(agent.name)}&agentId=${encodeURIComponent(agent.id)}&agentType=${encodeURIComponent(actualType)}`

    console.log('智能体编排 - 打开新tab页面:', choreographyUrl)

    // 在新tab中打开编排页面（不指定窗口特性）
    window.open(choreographyUrl, '_blank')

    ElMessage.success('编排页面已在新标签页中打开')

  } catch (error: any) {
    console.error('智能体编排失败:', error)
    ElMessage.error('智能体编排失败：' + (error.message || '未知错误'))
  }
}

// 处理系统原生智能体编排
const handleNativeAgentChoreography = async (agent: ExtendedAgentVO) => {
  try {
    console.log('系统原生智能体编排 - 智能体信息:', {
      id: agent.id,
      name: agent.name,
      type: agent.type,
      sourceType: agent.sourceType
    })

    // 跳转到系统原生编排页面（Workflow页面）
    console.log('系统原生智能体编排 - 跳转到Workflow页面, agentId:', agent.id)

    // 使用正确的路由跳转到Workflow页面
    router.push({ name: 'Workflow', params: { id: agent.id } })

    ElMessage.success('正在跳转到工作流编排页面')

  } catch (error: any) {
    console.error('系统原生智能体编排失败:', error)
    ElMessage.error('系统原生智能体编排失败：' + (error.message || '未知错误'))
  }
}



const openTagModal = (agent: ExtendedAgentVO) => {
  currentEditingAgent.value = { ...agent }
  tagModalVisible.value = true
}

const handleTagSave = (tags: string[]) => {
  if (!currentEditingAgent.value) return

  const originalAgent = agentsData.value.find(agent => agent.id === currentEditingAgent.value?.id)
  if (originalAgent) {
    originalAgent.tags = [...tags]
  }
}

// 发布相关方法
const openPublishModal = (agent: ExtendedAgentVO) => {
  currentPublishAgent.value = agent
  publishModalVisible.value = true
}

const handlePublish = (publishData: any) => {
  const { agent, targetLabel, visibilityLabel, selectedUsers, selectedUnits } = publishData

  let message = `智能体"${agent.name}"已成功发布至${targetLabel}！`

  if (visibilityLabel) {
    message += `\n可见权限：${visibilityLabel}`

    if (selectedUsers && selectedUsers.length > 0) {
      const userNames = selectedUsers.map((u: any) => u.name).join('、')
      message += `\n可见用户：${userNames}`
    }

    if (selectedUnits && selectedUnits.length > 0) {
      const unitNames = selectedUnits.join('、')
      message += `\n可见单位：${unitNames}`
    }
  }

  alert(message)
}

// 共享相关方法
const handleShare = (data: { agent: Agent, users: User[] }) => {
  const { agent, users } = data
  const userNames = users.map(u => u.name).join('、')
  alert(`智能体"${agent.name}"已成功共享给：${userNames}`)
}

// 编辑相关方法
const openEditModal = (agent: ExtendedAgentVO) => {
  currentEditingAgent.value = agent
  editModalVisible.value = true
}

const handleAgentEditSave = async (data: { name: string; description: string; icon: string; iconBackground: string }) => {
  if (!currentEditingAgent.value) return

  try {
    // 重新加载智能体列表以获取最新数据
    await loadAgents()

    // 更新本地数据（可选，因为已经重新加载了）
    const agent = currentEditingAgent.value
    const originalAgent = agentsData.value.find(a => a.id === agent.id)

    if (originalAgent) {
      originalAgent.name = data.name
      originalAgent.description = data.description
      if (data.icon) {
        originalAgent.icon = data.icon
      }
      if (data.iconBackground) {
        originalAgent.iconBackground = data.iconBackground
      }
    }

    console.log(`智能体"${data.name}"信息已更新！`)
  } catch (error) {
    console.error('刷新智能体列表失败:', error)
  }
}

// 共享相关方法
const openShareModal = (agent: ExtendedAgentVO) => {
  currentShareAgent.value = agent
  shareModalVisible.value = true
}

// API密钥申请相关方法
const openApiKeyModal = (agent: ExtendedAgentVO) => {
  currentApiKeyAgent.value = agent
  apiKeyModalVisible.value = true
}

const handleApiKeySubmit = (newApiKey: ApiKey) => {
  apiKeyList.value.push(newApiKey)
}

// 删除确认相关方法
const openDeleteConfirm = (agent: ExtendedAgentVO) => {
  currentDeleteAgent.value = agent
  deleteConfirmVisible.value = true
}

const closeDeleteConfirm = () => {
  deleteConfirmVisible.value = false
  currentDeleteAgent.value = null
}

const confirmDelete = async () => {
  if (!currentDeleteAgent.value) return

  const agentName = currentDeleteAgent.value.name
  const agentId = currentDeleteAgent.value.id

  try {
    // 显示加载状态
    loading.value = true

    // 调用后端API删除智能体
    const response = await AgentsAPI.deleteAgent(agentId)

    if (response.success) {
      // 删除成功，从前端列表中移除
      agentsData.value = agentsData.value.filter(agent => agent.id !== agentId)

      // 关闭确认框
      closeDeleteConfirm()

      // 显示成功消息
      alert(`智能体"${agentName}"删除成功`)
    } else {
      // 删除失败
      alert(`删除智能体失败：${response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('删除智能体失败:', error)
    alert('删除智能体失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

</script>

<style scoped>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  width: 100%;
  max-width: none;
  margin: 0;
  min-height: 100vh;
  color: #333;
}

/* 过滤区域头部 */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  min-height: 60px;
  gap: 16px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
}

.page-title i {
  color: #3498db;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-create-agent {
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  color: #2c5282;
  border: 1px solid rgba(127, 179, 211, 0.3);
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn-create-agent:hover {
  background: linear-gradient(135deg, #90c9e8 0%, #6ba3c7 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(127, 179, 211, 0.4);
  color: #1a365d;
}

.btn-associate-agent {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.3);
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn-associate-agent:hover {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  color: #1d4ed8;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

/* 搜索和过滤区域 */
.filter-section {
  background: rgba(255, 255, 255, 0.9);
  padding: 20px 25px;
  margin-bottom: 25px;
  backdrop-filter: blur(10px);
}

.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 主要过滤行 */
.main-filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: space-between;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 280px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 11px 12px 11px 38px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fff;
  box-sizing: border-box;
  height: 40px;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 类型过滤行 */
.type-filter-row {
  display: flex;
  width: 100%;
}

.filter-group {
  display: flex;
  flex: 0 0 auto;
}

.filter-group select {
  padding: 8px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  height: 40px;
  color: #495057;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

.filter-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  z-index: 20;
}

.filter-group select:hover {
  border-color: #ced4da;
}

/* 确保下拉选项不被遮盖 */
.filter-group select option {
  padding: 8px 12px;
  color: #495057;
  background: #fff;
  border: none;
}

.filter-group select option:first-child {
  color: #6c757d;
  font-style: italic;
}

.filter-group select option:hover {
  background: #f8f9fa;
}

/* 智能体类型过滤器 */
.type-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.type-filter-btn {
  padding: 5px 10px;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  background: #fff;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  outline: none;
  white-space: nowrap;
}

.type-filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.type-filter-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
}

.type-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 1px 5px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  min-width: 16px;
  text-align: center;
}

.type-filter-btn.active .type-count {
  background: rgba(255, 255, 255, 0.3);
}

.type-filter-btn:not(.active) .type-count {
  background: #f8f9fa;
  color: #6c757d;
}

/* 智能体卡片网格 */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  align-items: stretch;
  padding: 0 20px;
}

/* 智能体卡片 */
.agent-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.agent-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.agent-card:hover .card-menu-btn {
  opacity: 1;
  visibility: visible;
}

/* 卡片菜单按钮 */
.card-menu-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #6c757d;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.card-menu-btn:hover {
  background: #667eea;
  color: white;
  transform: scale(1.1);
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.agent-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.agent-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.agent-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  flex: 1;
}

.external-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
  border: 1px solid rgba(245, 158, 11, 0.2);
  flex-shrink: 0;
}

.external-badge i {
  font-size: 8px;
}

.agent-type {
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.agent-description {
  color: #6c757d;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 60px;
}

.agent-description:empty::before {
  content: "智能体功能描述信息，点右键或点右上角菜单中的编辑按钮，添加描述信息";
  color: #adb5bd;
  font-style: italic;
  font-size: 12px;
  line-height: 1.4;
}

.agent-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
  border-top: 1px solid #f1f3f4;
  padding-top: 12px;
  margin-top: auto;
}

.agent-creator {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.agent-date {
  font-size: 12px;
  color: #adb5bd;
}

/* 智能体标签 */
.agent-tags {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
  min-height: 28px;
}

.agent-tag {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  color: #667eea;
  padding: 3px 7px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  border: 1px solid #667eea30;
}

.add-tag-btn {
  background: #f8f9fa;
  color: #6c757d;
  padding: 3px 7px;
  border-radius: 10px;
  font-size: 10px;
  border: 1px dashed #dee2e6;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 3px;
}

.add-tag-btn:hover {
  background: #e9ecef;
  border-color: #667eea;
  color: #667eea;
}



/* 右键菜单 */
.context-menu {
  position: fixed;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 160px;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.menu-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #495057;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item-danger {
  color: #dc3545;
}

.menu-item-danger:hover {
  background: #fff5f5;
}



/* 模态框底部 - 浅蓝色系 */
.publish-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #dbeafe;
  background: #f8fafc;
}

.footer-left {
  flex: 1;
}

.publish-summary {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-text {
  font-size: 13px;
  color: #4b5563;
  line-height: 1.4;
}

.summary-text strong {
  color: #111827;
  font-weight: 600;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 浅蓝色系按钮样式 */
.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern.btn-secondary {
  background: #f0f9ff;
  color: #374151;
  border: 1px solid #bae6fd;
}

.btn-modern.btn-secondary:hover {
  background: #dbeafe;
  color: #111827;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.15);
}

.btn-modern.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.25);
}

.btn-modern.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.35);
}

.btn-modern.btn-primary:disabled {
  background: #bae6fd;
  color: #60a5fa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-modern.btn-primary:disabled::before {
  display: none;
}

.btn-modern i {
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .publish-modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }

  .publish-modal-body {
    flex-direction: column;
  }

  .publish-left-panel {
    width: 100%;
    max-height: 300px;
  }

  .publish-right-panel {
    border-top: 1px solid #e2e8f0;
    border-right: none;
  }
}

@media (max-width: 768px) {
  .publish-modal {
    padding: 10px;
  }

  .publish-modal-header {
    padding: 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-right h2 {
    font-size: 20px;
  }

  .publish-modal-footer {
    padding: 20px;
    flex-direction: column;
    gap: 16px;
  }

  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .btn-modern {
    flex: 1;
    justify-content: center;
  }
}





/* 创建智能体模态框样式已迁移到 CreateAgentModal.vue 组件 */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 删除确认模态框样式 */
.delete-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  backdrop-filter: blur(4px);
}

.delete-confirm-content {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  width: 90%;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.delete-confirm-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f1f5f9;
}

.warning-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: #d97706;
  font-size: 24px;
}

.delete-confirm-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.delete-confirm-body {
  padding: 20px 24px;
}

.delete-message {
  font-size: 16px;
  color: #374151;
  margin: 0 0 16px;
  text-align: center;
  line-height: 1.5;
}

.delete-message strong {
  color: #1f2937;
  font-weight: 600;
}

.delete-warning {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 16px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  font-size: 14px;
  color: #92400e;
}

.delete-warning i {
  color: #d97706;
  margin-top: 2px;
  flex-shrink: 0;
}

.delete-confirm-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 100px;
  justify-content: center;
}

.btn-modern.btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-modern.btn-secondary:hover {
  background: #f1f5f9;
  color: #475569;
  border-color: #cbd5e1;
}

.btn-modern.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.btn-modern.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.btn-modern.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.btn-modern.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.pagination-container {
  position: fixed;
  z-index: 999;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding: 0 20px;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .delete-confirm-content {
    max-width: 95%;
    margin: 20px;
  }

  .delete-confirm-header {
    padding: 20px 20px 12px;
  }

  .warning-icon {
    width: 56px;
    height: 56px;
    font-size: 20px;
    margin-bottom: 12px;
  }

  .delete-confirm-header h3 {
    font-size: 18px;
  }

  .delete-confirm-body {
    padding: 16px 20px;
  }

  .delete-message {
    font-size: 15px;
  }

  .delete-confirm-footer {
    padding: 12px 20px 20px;
    flex-direction: column;
  }

  .btn-modern {
    width: 100%;
  }
}



/* 头部操作区域 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-end;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}

/* API密钥申请模态框样式已迁移到 ApiKeyModal.vue 组件 */

/* 外部智能体悬停提示样式 */
.loading-tooltip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}

.loading-tooltip i {
  color: #6366f1;
}
</style>
