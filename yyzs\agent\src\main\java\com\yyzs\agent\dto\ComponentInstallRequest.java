package com.yyzs.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 组件安装请求DTO
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "组件安装请求")
public class ComponentInstallRequest {

    @Schema(description = "组件ID", required = true)
    @NotBlank(message = "组件ID不能为空")
    private String componentId;

    @Schema(description = "安装配置参数")
    private Map<String, Object> config;

    @Schema(description = "是否自动启动", defaultValue = "false")
    private Boolean autoStart = false;

    @Schema(description = "自定义安装路径")
    private String customInstallPath;

    @Schema(description = "监听端口")
    private Integer port;

    @Schema(description = "主机地址", defaultValue = "localhost")
    private String host = "localhost";
}
