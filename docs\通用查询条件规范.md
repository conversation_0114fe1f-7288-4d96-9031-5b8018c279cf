# 通用查询条件规范

## 概述

为了统一整个项目中各模块的查询条件规范，提高代码的一致性和可维护性，设计了一套组合式的查询条件体系。用户可以根据实际需求，按需选择和组合不同的查询条件。

## 设计原则

1. **组合式设计**：将不同类型的查询条件拆分为独立的接口和基类
2. **按需继承**：查询DTO可以根据需要继承或实现不同的查询条件
3. **类型安全**：使用泛型和接口确保类型安全
4. **扩展性强**：新增查询条件类型时不影响现有代码

## 查询条件分类

### 1. 核心接口

| 接口名 | 描述 | 主要方法 |
|--------|------|----------|
| `Pageable` | 分页查询接口 | `getCurrent()`, `getSize()`, `getOrderBy()` |
| `TimeRangeable` | 时间范围查询接口 | `getBeginTime()`, `getEndTime()` |
| `Statusable` | 状态查询接口 | `getStatus()`, `isEnabled()`, `isDisabled()` |
| `Keywordable` | 关键字搜索接口 | `getKeyword()`, `getLikeKeyword()` |
| `DataScopeable` | 数据权限查询接口 | `getTenantId()`, `getDataScope()` |

### 2. 基础DTO类

| 类名 | 继承关系 | 实现接口 | 适用场景 |
|------|----------|----------|----------|
| `PageQueryDTO` | - | `Pageable` | 只需要分页功能 |
| `TimeRangeQueryDTO` | - | `TimeRangeable` | 只需要时间范围查询 |
| `StatusQueryDTO` | - | `Statusable` | 只需要状态查询 |
| `KeywordQueryDTO` | - | `Keywordable` | 只需要关键字搜索 |
| `DataScopeQueryDTO` | - | `DataScopeable` | 只需要数据权限查询 |

### 3. 组合DTO类

| 类名 | 继承关系 | 实现接口 | 适用场景 |
|------|----------|----------|----------|
| `PageTimeRangeQueryDTO` | `PageQueryDTO` | `TimeRangeable` | 分页+时间范围 |
| `PageStatusQueryDTO` | `PageQueryDTO` | `Statusable` | 分页+状态 |
| `PageKeywordQueryDTO` | `PageQueryDTO` | `Keywordable` | 分页+关键字搜索 |
| `FullQueryDTO` | `PageQueryDTO` | 所有接口 | 需要所有查询条件 |

## 使用指南

### 1. 选择合适的基类

根据业务需求选择合适的基类：

```java
// 场景1：只需要分页
public class SimpleQueryDTO extends PageQueryDTO {
    // 业务字段
}

// 场景2：需要分页+时间范围
public class LogQueryDTO extends PageTimeRangeQueryDTO {
    // 业务字段
}

// 场景3：需要分页+状态
public class ConfigQueryDTO extends PageStatusQueryDTO {
    // 业务字段
}

// 场景4：需要分页+关键字搜索
public class ArticleQueryDTO extends PageKeywordQueryDTO {
    // 业务字段
}

// 场景5：需要所有查询条件
public class UserQueryDTO extends FullQueryDTO {
    // 业务字段
}
```

### 2. 自定义组合

如果预定义的组合不满足需求，可以自定义组合：

```java
// 需要分页+状态+关键字搜索
public class CustomQueryDTO extends PageQueryDTO implements Statusable, Keywordable {
    
    @Schema(description = "状态")
    private String status;
    
    @Schema(description = "搜索关键字")
    private String keyword;
    
    // 实现接口方法
    @Override
    public String getStatus() {
        return status;
    }
    
    @Override
    public String getKeyword() {
        return keyword;
    }
    
    // getter/setter方法
}
```

### 3. 使用查询条件构建器

在Service或Mapper中使用查询条件构建器：

```java
@Service
public class UserService {
    
    public PageResult<User> queryUsers(UserQueryDTO query) {
        // 构建查询条件
        QueryConditionBuilder builder = QueryConditionUtils.buildBasicConditions(
            query, 
            "create_time",  // 时间字段
            "status",       // 状态字段
            "username", "nickname", "email"  // 搜索字段
        );
        
        // 添加自定义条件
        if (query.getDeptId() != null) {
            builder.addEqualCondition("dept_id", "deptId", query.getDeptId());
        }
        
        // 获取WHERE子句和参数
        String whereClause = builder.buildWhereClause();
        Map<String, Object> params = builder.getParameters();
        
        // 执行查询
        return userMapper.selectPage(query, whereClause, params);
    }
}
```

### 4. 在Mapper中使用

```java
@Mapper
public interface UserMapper {
    
    @Select({
        "<script>",
        "SELECT * FROM sys_user",
        "<if test='whereClause != null and whereClause != \"\"'>",
        "WHERE ${whereClause}",
        "</if>",
        "<if test='orderBy != null and orderBy != \"\"'>",
        "ORDER BY ${orderBy} ${orderDirection}",
        "</if>",
        "</script>"
    })
    List<User> selectList(@Param("query") UserQueryDTO query, 
                         @Param("whereClause") String whereClause,
                         @Param("params") Map<String, Object> params);
}
```

## 接口方法说明

### Pageable接口

```java
// 基础方法
Long getCurrent();          // 当前页码
Long getSize();            // 每页大小
String getOrderBy();       // 排序字段
String getOrderDirection(); // 排序方向

// 便捷方法
Long getOffset();          // 偏移量
Long getLimit();           // 限制数量
Boolean isAsc();           // 是否升序
Boolean isDesc();          // 是否降序
```

### TimeRangeable接口

```java
// 基础方法
String getBeginTime();     // 开始时间字符串
String getEndTime();       // 结束时间字符串

// 便捷方法
LocalDateTime getBeginDateTime();  // 开始时间对象
LocalDateTime getEndDateTime();    // 结束时间对象
Boolean hasTimeRange();            // 是否有时间范围
Boolean hasCompleteTimeRange();    // 是否有完整时间范围
```

### Statusable接口

```java
// 基础方法
String getStatus();        // 状态值

// 便捷方法
Boolean hasStatus();       // 是否有状态条件
Boolean isEnabled();       // 是否启用（状态为0）
Boolean isDisabled();      // 是否禁用（状态为1）
Boolean isStatus(String statusValue); // 检查状态是否匹配
```

### Keywordable接口

```java
// 基础方法
String getKeyword();       // 搜索关键字

// 便捷方法
Boolean hasKeyword();      // 是否有关键字
String getTrimmedKeyword(); // 去除空格的关键字
String getLikeKeyword();   // 用于LIKE查询的关键字（%keyword%）
String getPrefixKeyword(); // 前缀匹配关键字（keyword%）
String getSuffixKeyword(); // 后缀匹配关键字（%keyword）
```

### DataScopeable接口

```java
// 基础方法
Long getTenantId();        // 租户ID
String getDataScope();     // 数据权限SQL
void setDataScope(String dataScope); // 设置数据权限SQL

// 便捷方法
Boolean hasTenant();       // 是否有租户条件
Boolean hasDataScope();    // 是否有数据权限条件
Boolean isPlatformAdmin(); // 是否平台管理员
Boolean isTenantUser();    // 是否租户用户
```

## 最佳实践

### 1. 字段命名规范

- 时间字段：`beginTime`、`endTime`
- 状态字段：`status`
- 关键字字段：`keyword`
- 租户字段：`tenantId`
- 数据权限字段：`dataScope`

### 2. 验证注解使用

```java
// 时间格式验证
@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
         message = "时间格式必须为：yyyy-MM-dd HH:mm:ss")
private String beginTime;

// 状态值验证
@Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
private String status;

// 关键字长度验证
@Size(max = 100, message = "搜索关键字长度不能超过100个字符")
private String keyword;
```

### 3. toString方法规范

```java
@Override
public String toString() {
    return "UserQueryDTO{" +
            "current=" + getCurrent() +
            ", size=" + getSize() +
            ", orderBy='" + getOrderBy() + '\'' +
            ", orderDirection='" + getOrderDirection() + '\'' +
            ", beginTime='" + beginTime + '\'' +
            ", endTime='" + endTime + '\'' +
            ", status='" + status + '\'' +
            ", keyword='" + keyword + '\'' +
            // 业务字段...
            '}';
}
```

## 扩展指南

### 1. 新增查询条件接口

```java
// 1. 定义接口
public interface Sortable {
    String getSortField();
    String getSortOrder();
    
    default Boolean hasSort() {
        return getSortField() != null && !getSortField().trim().isEmpty();
    }
}

// 2. 创建基础DTO
public class SortQueryDTO implements Sortable {
    private String sortField;
    private String sortOrder;
    
    // getter/setter方法
}

// 3. 创建组合DTO
public class PageSortQueryDTO extends PageQueryDTO implements Sortable {
    // 实现接口
}
```

### 2. 扩展查询条件构建器

```java
// 在QueryConditionBuilder中添加新方法
public QueryConditionBuilder addSortCondition(Sortable sortable) {
    if (sortable != null && sortable.hasSort()) {
        parameters.put("sortField", sortable.getSortField());
        parameters.put("sortOrder", sortable.getSortOrder());
    }
    return this;
}
```

## 注意事项

1. **数据权限字段**：`dataScope`字段通常由系统自动设置，不应该在前端传递
2. **时间格式**：统一使用`yyyy-MM-dd HH:mm:ss`格式
3. **状态值**：建议使用字符串类型，便于扩展
4. **关键字搜索**：支持多字段搜索，使用OR连接
5. **分页参数**：页码从1开始，每页大小限制在1-100之间

## 迁移指南

### 从现有DTO迁移

1. **分析现有DTO**：确定需要哪些查询条件
2. **选择合适基类**：根据需求选择预定义的基类或自定义组合
3. **重构字段名**：统一字段命名规范
4. **更新验证注解**：使用统一的验证规则
5. **修改Service层**：使用查询条件构建器
6. **更新Mapper**：使用动态SQL

### 示例迁移

```java
// 迁移前
public class OldUserQueryDTO {
    private Integer pageNum;
    private Integer pageSize;
    private String userName;
    private String startTime;
    private String endTime;
    private Integer status;
    // ...
}

// 迁移后
public class NewUserQueryDTO extends PageTimeRangeQueryDTO implements Statusable, Keywordable {
    // 业务字段
    private String username;
    
    // 通用查询条件（继承和接口提供）
    // beginTime, endTime - 来自TimeRangeable
    // status - 来自Statusable
    // keyword - 来自Keywordable
    // current, size - 来自PageQueryDTO
}
```

## 完整示例

### Service层示例

```java
@Service
public class UserService {

    @Autowired
    private UserMapper userMapper;

    /**
     * 分页查询用户
     */
    public PageResult<User> queryUsers(UserQueryDTO query) {
        // 设置数据权限
        DataScopeUtils.setDataScope(query);

        // 构建基础查询条件
        QueryConditionBuilder builder = QueryConditionUtils.buildBasicConditions(
            query,
            "create_time",  // 时间字段
            "status",       // 状态字段
            "username", "nickname", "email", "phone"  // 搜索字段
        );

        // 添加业务特有条件
        if (query.getDeptId() != null) {
            builder.addEqualCondition("dept_id", "deptId", query.getDeptId());
        }

        if (query.getGender() != null && !query.getGender().trim().isEmpty()) {
            builder.addEqualCondition("gender", "gender", query.getGender());
        }

        // 执行查询
        String whereClause = builder.buildWhereClause();
        Map<String, Object> params = builder.getParameters();

        // 查询总数
        Long total = query.getSearchCount() ?
            userMapper.countByCondition(whereClause, params) : 0L;

        // 查询数据
        List<User> records = userMapper.selectByCondition(query, whereClause, params);

        return new PageResult<>(records, total, query.getCurrent(), query.getSize());
    }

    /**
     * 导出用户（不分页）
     */
    public List<User> exportUsers(UserQueryDTO query) {
        // 设置数据权限
        DataScopeUtils.setDataScope(query);

        // 构建查询条件（不包含分页）
        QueryConditionBuilder builder = new QueryConditionBuilder()
            .addTimeRangeCondition(query, "create_time")
            .addStatusCondition(query, "status")
            .addKeywordCondition(query, "username", "nickname", "email", "phone")
            .addDataScopeCondition(query);

        // 添加业务条件
        if (query.getDeptId() != null) {
            builder.addEqualCondition("dept_id", "deptId", query.getDeptId());
        }

        String whereClause = builder.buildWhereClause();
        Map<String, Object> params = builder.getParameters();

        return userMapper.selectForExport(whereClause, params);
    }
}
```

### Mapper层示例

```java
@Mapper
public interface UserMapper {

    /**
     * 条件查询用户列表
     */
    @Select({
        "<script>",
        "SELECT u.*, d.dept_name",
        "FROM sys_user u",
        "LEFT JOIN sys_dept d ON u.dept_id = d.dept_id",
        "<if test='whereClause != null and whereClause != \"\"'>",
        "WHERE ${whereClause}",
        "</if>",
        "<if test='query.orderBy != null and query.orderBy != \"\"'>",
        "ORDER BY ${query.orderBy} ${query.orderDirection}",
        "</if>",
        "LIMIT #{query.offset}, #{query.limit}",
        "</script>"
    })
    List<User> selectByCondition(@Param("query") UserQueryDTO query,
                                @Param("whereClause") String whereClause,
                                @Param("params") Map<String, Object> params);

    /**
     * 统计符合条件的记录数
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM sys_user u",
        "<if test='whereClause != null and whereClause != \"\"'>",
        "WHERE ${whereClause}",
        "</if>",
        "</script>"
    })
    Long countByCondition(@Param("whereClause") String whereClause,
                         @Param("params") Map<String, Object> params);

    /**
     * 导出查询（不分页）
     */
    @Select({
        "<script>",
        "SELECT u.*, d.dept_name",
        "FROM sys_user u",
        "LEFT JOIN sys_dept d ON u.dept_id = d.dept_id",
        "<if test='whereClause != null and whereClause != \"\"'>",
        "WHERE ${whereClause}",
        "</if>",
        "ORDER BY u.create_time DESC",
        "</script>"
    })
    List<User> selectForExport(@Param("whereClause") String whereClause,
                              @Param("params") Map<String, Object> params);
}
```

### Controller层示例

```java
@RestController
@RequestMapping("/system/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 分页查询用户
     */
    @GetMapping("/list")
    public Result<PageResult<User>> list(@Valid UserQueryDTO query) {
        PageResult<User> result = userService.queryUsers(query);
        return Result.success(result);
    }

    /**
     * 导出用户
     */
    @PostMapping("/export")
    public void export(@RequestBody @Valid UserQueryDTO query, HttpServletResponse response) {
        List<User> users = userService.exportUsers(query);
        // 导出逻辑...
    }
}
```

通过这套规范，可以大大提高查询DTO的一致性和可维护性，同时为开发者提供了灵活的组合选择。
