<template>
  <div class="vector-db-management">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">向量数据库管理</h2>
          <p class="text-gray-600 mt-1">管理和配置向量数据库连接</p>
        </div>
        <button
          @click="showAddForm = true"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center"
        >
          <span class="mr-2">+</span>添加向量数据库
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters mb-6 bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            @input="handleSearch"
            type="text"
            placeholder="搜索向量数据库名称..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div class="w-48">
          <select
            v-model="statusFilter"
            @change="handleSearch"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部状态</option>
            <option value="0">启用</option>
            <option value="1">停用</option>
          </select>
        </div>
        <div class="w-48">
          <select
            v-model="typeFilter"
            @change="handleSearch"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部类型</option>
            <option v-for="type in vectorDbTypes" :key="type.dictValue" :value="type.dictValue">
              {{ type.dictLabel }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- 向量数据库列表 -->
    <div class="vector-db-list bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">向量数据库列表</h3>
      </div>

      <div class="p-4">
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="text-gray-500">加载中...</div>
        </div>

        <div v-else-if="vectorDatabases.length === 0" class="flex items-center justify-center py-12">
          <div class="text-center text-gray-500">
            <div class="text-4xl mb-4">🗄️</div>
            <div>暂无向量数据库配置</div>
          </div>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="db in vectorDatabases"
            :key="db.id"
            class="vector-db-card p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all"
          >
            <!-- 数据库类型图标和名称 -->
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <div
                  class="w-10 h-10 rounded-lg flex items-center justify-center text-xl"
                  :style="{ backgroundColor: db.iconColor || '#f3f4f6' }"
                >
                  {{ db.icon || '🗄️' }}
                </div>
                <div>
                  <div class="font-semibold text-gray-900 flex items-center">
                    {{ db.name }}
                    <span v-if="db.isDefault === 'Y'" class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      默认
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">{{ db.typeName }}</div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  class="px-2 py-1 text-xs rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': db.status === '0',
                    'bg-red-100 text-red-800': db.status === '1'
                  }"
                >
                  {{ db.status === '0' ? '启用' : '停用' }}
                </span>
              </div>
            </div>

            <!-- 连接信息 -->
            <div class="mb-3 space-y-1">
              <div class="text-sm text-gray-600">
                <span class="font-medium">主机:</span> {{ db.host }}:{{ db.port }}
              </div>
              <div v-if="db.databaseName" class="text-sm text-gray-600">
                <span class="font-medium">数据库:</span> {{ db.databaseName }}
              </div>
              <div v-if="db.description" class="text-sm text-gray-500">{{ db.description }}</div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center justify-between pt-3 border-t border-gray-100">
              <div class="flex space-x-2">
                <button
                  @click="testConnection(db)"
                  class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                >
                  测试连接
                </button>
                <button
                  v-if="db.isDefault !== 'Y'"
                  @click="setDefault(db)"
                  class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                >
                  设为默认
                </button>
              </div>
              <div class="flex space-x-1">
                <button
                  @click="editVectorDb(db)"
                  class="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                  title="编辑"
                >
                  ✏️
                </button>
                <button
                  @click="toggleStatus(db)"
                  class="p-1 text-gray-400 hover:text-yellow-500 transition-colors"
                  :title="db.status === '0' ? '停用' : '启用'"
                >
                  {{ db.status === '0' ? '⏸️' : '▶️' }}
                </button>
                <button
                  @click="deleteVectorDb(db)"
                  class="p-1 text-gray-400 hover:text-red-500 transition-colors"
                  title="删除"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑向量数据库表单弹窗 -->
    <div v-if="showAddForm || showEditForm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ showAddForm ? '添加向量数据库' : '编辑向量数据库' }}
          </h3>
        </div>

        <div class="p-6">
          <form @submit.prevent="submitForm" class="space-y-4">
            <!-- 基本信息 -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">数据库名称 *</label>
                <input
                  v-model="formData.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入数据库名称"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">数据库类型 *</label>
                <select
                  v-model="formData.type"
                  required
                  @change="handleTypeChange"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择类型</option>
                  <option v-for="type in vectorDbTypes" :key="type.dictValue" :value="type.dictValue">
                    {{ type.dictLabel }}
                  </option>
                </select>
              </div>
            </div>

            <!-- 连接信息 -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">主机地址 *</label>
                <input
                  v-model="formData.host"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="localhost"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">端口 *</label>
                <input
                  v-model="formData.port"
                  type="number"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="9200"
                />
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div v-if="needsDatabase">
                <label class="block text-sm font-medium text-gray-700 mb-1">数据库名</label>
                <input
                  v-model="formData.databaseName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="vector_db"
                />
              </div>
              <div v-if="needsDatabase || formData.type === 'elasticsearch'">
                <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                <input
                  v-model="formData.username"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="用户名"
                />
              </div>
              <div v-if="formData.type === 'elasticsearch'">
                <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                <input
                  v-model="formData.password"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="密码"
                />
              </div>
            </div>

            <div v-if="needsDatabase">
              <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
              <input
                v-model="formData.password"
                type="password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="密码"
              />
            </div>

            <!-- SSL/TLS配置 -->
            <div v-if="showSslConfig" class="space-y-4">
              <div class="flex items-center">
                <input
                  v-model="sslEnabled"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label class="ml-2 text-sm text-gray-700">启用SSL/TLS</label>
              </div>

              <div v-if="sslEnabled" class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">CA证书文件</label>
                  <div class="flex items-center space-x-2">
                    <input
                      type="file"
                      ref="caCertFileInput"
                      @change="handleCaCertUpload"
                      accept=".crt,.pem,.cer"
                      class="hidden"
                    />
                    <button
                      type="button"
                      @click="() => caCertFileInput?.click()"
                      class="px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                    >
                      选择文件
                    </button>
                    <span v-if="formData.caCertPath" class="text-sm text-gray-600">{{ formData.caCertPath }}</span>
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">客户端证书</label>
                    <input
                      v-model="formData.clientCert"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="客户端证书路径"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">客户端密钥</label>
                    <input
                      v-model="formData.clientKey"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="客户端密钥路径"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 其他连接属性 -->
            <div v-if="showAdvancedConfig" class="space-y-4">
              <h4 class="text-sm font-medium text-gray-900">高级配置</h4>

              <!-- Weaviate API Key -->
              <div v-if="formData.type === 'weaviate'">
                <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                <input
                  v-model="formData.apiKey"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Weaviate API Key"
                />
              </div>

              <!-- Qdrant API Key -->
              <div v-if="formData.type === 'qdrant'">
                <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                <input
                  v-model="formData.apiKey"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Qdrant API Key"
                />
              </div>

              <!-- Milvus Token -->
              <div v-if="formData.type === 'milvus'">
                <label class="block text-sm font-medium text-gray-700 mb-1">Token</label>
                <input
                  v-model="formData.token"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Milvus Token"
                />
              </div>

              <!-- 连接超时 -->
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">连接超时(秒)</label>
                  <input
                    v-model="formData.connectionTimeout"
                    type="number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="30"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">读取超时(秒)</label>
                  <input
                    v-model="formData.readTimeout"
                    type="number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="60"
                  />
                </div>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
              <textarea
                v-model="formData.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入描述信息"
              ></textarea>
            </div>

            <div class="flex items-center">
              <input
                v-model="isDefaultChecked"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label class="ml-2 text-sm text-gray-700">设为默认向量数据库</label>
            </div>
          </form>
        </div>

        <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="closeForm"
            type="button"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            @click="submitForm"
            type="button"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            :disabled="submitting"
          >
            {{ submitting ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DictAPI } from '@/api/dict'
import { VectorDatabaseAPI } from '@/api/vectorDatabase'
import type { SysDictDataVO } from '@/types/system'
import type { VectorDatabaseVO, VectorDatabaseCreateDTO, VectorDatabaseQueryDTO } from '@/api/vectorDatabase'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddForm = ref(false)
const showEditForm = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')

// 文件输入引用
const caCertFileInput = ref<HTMLInputElement>()

// 向量数据库类型字典
const vectorDbTypes = ref<SysDictDataVO[]>([])

// 向量数据库列表
const vectorDatabases = ref<VectorDatabaseVO[]>([])

// 表单数据
const formData = reactive<VectorDatabaseCreateDTO>({
  name: '',
  type: '',
  host: '',
  port: 9200,
  databaseName: '',
  username: '',
  password: '',
  description: '',
  isDefault: 'N',
  status: '0',
  icon: '',
  iconColor: '',
  connectionConfig: '',
  indexConfig: '',
  poolConfig: '',
  remark: '',
  // SSL配置
  sslEnabled: 'N',
  caCertPath: '',
  clientCert: '',
  clientKey: '',
  // API配置
  apiKey: '',
  token: '',
  // 超时配置
  connectionTimeout: 30,
  readTimeout: 60
})

// 计算属性
const needsDatabase = computed(() => {
  return ['postgresql_pgvector', 'milvus'].includes(formData.type)
})

const showSslConfig = computed(() => {
  return ['elasticsearch', 'milvus', 'qdrant'].includes(formData.type)
})

const showAdvancedConfig = computed(() => {
  return ['weaviate', 'qdrant', 'milvus'].includes(formData.type)
})

const sslEnabled = computed({
  get: () => formData.sslEnabled === 'Y',
  set: (value: boolean) => {
    formData.sslEnabled = value ? 'Y' : 'N'
  }
})

const isDefaultChecked = computed({
  get: () => formData.isDefault === 'Y',
  set: (value: boolean) => {
    formData.isDefault = value ? 'Y' : 'N'
  }
})

// 方法
const loadVectorDbTypes = async () => {
  try {
    const response = await DictAPI.getDictDataByType('vector_database_type')
    if (response.success && response.data) {
      vectorDbTypes.value = response.data
    }
  } catch (error) {
    console.error('加载向量数据库类型失败:', error)
    ElMessage.error('加载向量数据库类型失败')
  }
}

const loadVectorDatabases = async () => {
  loading.value = true
  try {
    const queryParams: VectorDatabaseQueryDTO = {
      name: searchQuery.value,
      status: statusFilter.value,
      type: typeFilter.value
    }

    const response = await VectorDatabaseAPI.getVectorDatabaseList(queryParams)
    if (response.success && response.data) {
      vectorDatabases.value = response.data
    } else {
      ElMessage.error('加载向量数据库列表失败')
    }
  } catch (error) {
    console.error('加载向量数据库列表失败:', error)
    ElMessage.error('加载向量数据库列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  loadVectorDatabases()
}

const handleTypeChange = () => {
  // 根据类型设置默认端口
  const typePortMap: Record<string, number> = {
    'elasticsearch': 9200,
    'weaviate': 8080,
    'postgresql_pgvector': 5432,
    'milvus': 19530,
    'qdrant': 6333
  }

  if (formData.type && typePortMap[formData.type]) {
    formData.port = typePortMap[formData.type]
  }
}

const handleCaCertUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    try {
      // TODO: 上传证书文件到文件存储
      // 这里应该调用文件上传API，将证书文件上传到配置的文件存储中
      // const uploadResponse = await FileStorageAPI.uploadFile(file)
      // formData.caCertPath = uploadResponse.data.filePath

      // 临时设置文件名
      formData.caCertPath = file.name
      ElMessage.success('证书文件选择成功')
    } catch (error) {
      console.error('证书文件上传失败:', error)
      ElMessage.error('证书文件上传失败')
    }
  }
}

const editVectorDb = (db: VectorDatabaseVO) => {
  currentEditingId = db.id
  Object.assign(formData, {
    name: db.name,
    type: db.type,
    host: db.host,
    port: db.port,
    databaseName: db.databaseName,
    username: db.username,
    password: db.password,
    description: db.description,
    isDefault: db.isDefault,
    status: db.status,
    icon: db.icon,
    iconColor: db.iconColor,
    connectionConfig: db.connectionConfig,
    indexConfig: db.indexConfig,
    poolConfig: db.poolConfig,
    remark: db.remark,
    // 从connectionConfig中解析SSL和其他配置
    sslEnabled: 'N',
    caCertPath: '',
    clientCert: '',
    clientKey: '',
    apiKey: '',
    token: '',
    connectionTimeout: 30,
    readTimeout: 60
  })

  // 如果有connectionConfig，尝试解析其中的配置
  if (db.connectionConfig) {
    try {
      const config = JSON.parse(db.connectionConfig)
      if (config.sslEnabled) formData.sslEnabled = config.sslEnabled
      if (config.caCertPath) formData.caCertPath = config.caCertPath
      if (config.clientCert) formData.clientCert = config.clientCert
      if (config.clientKey) formData.clientKey = config.clientKey
      if (config.apiKey) formData.apiKey = config.apiKey
      if (config.token) formData.token = config.token
      if (config.connectionTimeout) formData.connectionTimeout = config.connectionTimeout
      if (config.readTimeout) formData.readTimeout = config.readTimeout
    } catch (e) {
      console.warn('解析连接配置失败:', e)
    }
  }

  showEditForm.value = true
}

const deleteVectorDb = async (db: VectorDatabaseVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除向量数据库 "${db.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await VectorDatabaseAPI.deleteVectorDatabases([db.id])
    if (response.success) {
      ElMessage.success('删除成功')
      loadVectorDatabases()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error) {
    // 用户取消删除或API调用失败
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const toggleStatus = async (db: VectorDatabaseVO) => {
  try {
    const action = db.status === '0' ? '停用' : '启用'
    const newStatus = db.status === '0' ? '1' : '0'

    await ElMessageBox.confirm(
      `确定要${action}向量数据库 "${db.name}" 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await VectorDatabaseAPI.batchUpdateStatus([db.id], newStatus)
    if (response.success) {
      db.status = newStatus
      ElMessage.success(`${action}成功`)
    } else {
      ElMessage.error(`${action}失败`)
    }
  } catch (error) {
    // 用户取消操作或API调用失败
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const setDefault = async (db: VectorDatabaseVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 "${db.name}" 设为默认向量数据库吗？`,
      '确认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await VectorDatabaseAPI.setDefaultVectorDatabase(db.id)
    if (response.success) {
      ElMessage.success('设置默认向量数据库成功')
      loadVectorDatabases()
    } else {
      ElMessage.error('设置默认向量数据库失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('设置默认向量数据库失败')
    }
  }
}

const testConnection = async (db: VectorDatabaseVO) => {
  try {
    ElMessage.info('正在测试连接...')
    const response = await VectorDatabaseAPI.testConnectionById(db.id)
    if (response.success && response.data.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(response.data.message || '连接测试失败')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error('连接测试失败')
  }
}

const submitForm = async () => {
  submitting.value = true
  try {
    // 构建连接配置JSON
    const connectionConfig = {
      sslEnabled: formData.sslEnabled,
      caCertPath: formData.caCertPath,
      clientCert: formData.clientCert,
      clientKey: formData.clientKey,
      apiKey: formData.apiKey,
      token: formData.token,
      connectionTimeout: formData.connectionTimeout,
      readTimeout: formData.readTimeout
    }

    // 创建提交数据，将扩展配置序列化到connectionConfig中
    const submitData = {
      ...formData,
      connectionConfig: JSON.stringify(connectionConfig)
    }

    let response
    if (showAddForm.value) {
      response = await VectorDatabaseAPI.createVectorDatabase(submitData)
    } else {
      // 编辑模式需要获取当前编辑的ID
      const editingId = getCurrentEditingId()
      if (!editingId) {
        ElMessage.error('无法获取编辑的向量数据库ID')
        return
      }
      response = await VectorDatabaseAPI.updateVectorDatabase(editingId, submitData)
    }

    if (response.success) {
      ElMessage.success(showAddForm.value ? '添加成功' : '更新成功')
      closeForm()
      loadVectorDatabases()
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    submitting.value = false
  }
}

// 获取当前编辑的ID（需要在编辑时保存）
let currentEditingId = ''
const getCurrentEditingId = () => currentEditingId

const closeForm = () => {
  showAddForm.value = false
  showEditForm.value = false
  currentEditingId = ''
  Object.assign(formData, {
    name: '',
    type: '',
    host: '',
    port: 9200,
    databaseName: '',
    username: '',
    password: '',
    description: '',
    isDefault: 'N',
    status: '0',
    icon: '',
    iconColor: '',
    connectionConfig: '',
    indexConfig: '',
    poolConfig: '',
    remark: '',
    // 重置SSL配置
    sslEnabled: 'N',
    caCertPath: '',
    clientCert: '',
    clientKey: '',
    // 重置API配置
    apiKey: '',
    token: '',
    // 重置超时配置
    connectionTimeout: 30,
    readTimeout: 60
  })
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadVectorDbTypes()
  await loadVectorDatabases()
})
</script>

<style scoped>
.vector-db-management {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.vector-db-card {
  transition: all 0.2s ease-in-out;
}

.vector-db-card:hover {
  transform: translateY(-2px);
}
</style>
