<template>
  <div class="video-player">
    <div class="player-container">
      <div class="player-header">
        <div class="video-info">
          <el-icon class="video-icon"><VideoCamera /></el-icon>
          <div class="video-details">
            <div class="video-title">{{ video.title || '视频文件' }}</div>
            <div class="video-duration" v-if="duration">{{ formatTime(duration) }}</div>
          </div>
        </div>
        <div class="player-actions">
          <el-button @click="toggleFullscreen" size="small" type="primary" text>
            <el-icon><FullScreen /></el-icon>
            全屏
          </el-button>
          <el-button @click="downloadVideo" size="small" type="primary" text>
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>

      <div class="video-container" ref="videoContainer">
        <video
          ref="videoElement"
          :src="video.url"
          :poster="video.poster"
          @loadedmetadata="onLoadedMetadata"
          @timeupdate="onTimeUpdate"
          @ended="onEnded"
          @error="onError"
          @click="togglePlay"
          class="video-element"
          preload="metadata"
        ></video>

        <!-- 自定义控制栏 -->
        <div class="video-controls" :class="{ 'show': showControls }">
          <!-- 播放/暂停按钮 -->
          <el-button
            @click="togglePlay"
            :type="isPlaying ? 'warning' : 'primary'"
            circle
            size="large"
            class="play-btn"
          >
            <el-icon size="20">
              <VideoPlay v-if="!isPlaying" />
              <VideoPause v-else />
            </el-icon>
          </el-button>

          <!-- 进度条 -->
          <div class="progress-area">
            <div class="time-display">
              <span class="current-time">{{ formatTime(currentTime) }}</span>
              <span class="total-time">{{ formatTime(duration) }}</span>
            </div>
            
            <div class="progress-container" @click="seekTo">
              <div class="progress-track" ref="progressTrack">
                <div 
                  class="progress-fill" 
                  :style="{ width: progressPercentage + '%' }"
                ></div>
                <div 
                  class="progress-thumb" 
                  :style="{ left: progressPercentage + '%' }"
                  @mousedown="startDrag"
                ></div>
              </div>
            </div>
          </div>

          <!-- 音量和全屏控制 -->
          <div class="control-actions">
            <div class="volume-control">
              <el-button @click="toggleMute" size="small" text>
                <el-icon>
                  <Mute v-if="isMuted || volume === 0" />
                  <Microphone v-else />
                </el-icon>
              </el-button>
              <div class="volume-slider">
                <el-slider
                  v-model="volume"
                  :min="0"
                  :max="100"
                  :show-tooltip="false"
                  size="small"
                  @input="updateVolume"
                />
              </div>
            </div>
            
            <el-button @click="toggleFullscreen" size="small" text>
              <el-icon><FullScreen /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 加载指示器 -->
        <div v-if="isLoading" class="loading-overlay">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载中...</span>
        </div>

        <!-- 播放按钮覆盖层 -->
        <div v-if="!isPlaying && !isLoading" class="play-overlay" @click="togglePlay">
          <el-button type="primary" circle size="large" class="overlay-play-btn">
            <el-icon size="32"><VideoPlay /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, defineProps, defineEmits } from 'vue'
import { 
  VideoCamera,
  FullScreen,
  Download,
  VideoPlay, 
  VideoPause, 
  Microphone, 
  Mute,
  Loading
} from '@element-plus/icons-vue'

// 接口定义
interface VideoContent {
  url: string
  title?: string
  duration?: number
  poster?: string
}

// Props定义
const props = defineProps<{
  video: VideoContent
}>()

// Emits定义
const emit = defineEmits<{
  download: [video: VideoContent]
  play: []
  pause: []
  ended: []
  fullscreen: [isFullscreen: boolean]
}>()

// 响应式数据
const videoElement = ref<HTMLVideoElement>()
const videoContainer = ref<HTMLElement>()
const progressTrack = ref<HTMLElement>()

const isPlaying = ref(false)
const isLoading = ref(false)
const showControls = ref(true)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(80)
const isMuted = ref(false)
const isDragging = ref(false)
const isFullscreen = ref(false)

let controlsTimer: number | null = null

// 计算属性
const progressPercentage = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 格式化时间
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const hours = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 播放/暂停切换
const togglePlay = () => {
  if (!videoElement.value) return
  
  if (isPlaying.value) {
    videoElement.value.pause()
    isPlaying.value = false
    emit('pause')
  } else {
    isLoading.value = true
    videoElement.value.play().then(() => {
      isLoading.value = false
      isPlaying.value = true
      emit('play')
    }).catch(() => {
      isLoading.value = false
    })
  }
  
  showControlsTemporarily()
}

// 静音切换
const toggleMute = () => {
  if (!videoElement.value) return
  
  isMuted.value = !isMuted.value
  videoElement.value.muted = isMuted.value
}

// 更新音量
const updateVolume = (value: number) => {
  if (!videoElement.value) return
  
  volume.value = value
  videoElement.value.volume = value / 100
  
  if (value === 0) {
    isMuted.value = true
    videoElement.value.muted = true
  } else if (isMuted.value) {
    isMuted.value = false
    videoElement.value.muted = false
  }
}

// 跳转到指定位置
const seekTo = (event: MouseEvent) => {
  if (!videoElement.value || !progressTrack.value) return
  
  const rect = progressTrack.value.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = clickX / rect.width
  const newTime = percentage * duration.value
  
  videoElement.value.currentTime = newTime
  currentTime.value = newTime
}

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  event.preventDefault()
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value || !progressTrack.value || !videoElement.value) return
    
    const rect = progressTrack.value.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    const newTime = percentage * duration.value
    
    videoElement.value.currentTime = newTime
    currentTime.value = newTime
  }
  
  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 全屏切换
const toggleFullscreen = () => {
  if (!videoContainer.value) return
  
  if (!isFullscreen.value) {
    if (videoContainer.value.requestFullscreen) {
      videoContainer.value.requestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 临时显示控制栏
const showControlsTemporarily = () => {
  showControls.value = true
  
  if (controlsTimer) {
    clearTimeout(controlsTimer)
  }
  
  controlsTimer = window.setTimeout(() => {
    if (isPlaying.value) {
      showControls.value = false
    }
  }, 3000)
}

// 下载视频
const downloadVideo = () => {
  const link = document.createElement('a')
  link.href = props.video.url
  link.download = props.video.title || 'video'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  emit('download', props.video)
}

// 视频事件处理
const onLoadedMetadata = () => {
  if (videoElement.value) {
    duration.value = videoElement.value.duration
    videoElement.value.volume = volume.value / 100
  }
}

const onTimeUpdate = () => {
  if (videoElement.value && !isDragging.value) {
    currentTime.value = videoElement.value.currentTime
  }
}

const onEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
  showControls.value = true
  emit('ended')
}

const onError = (error: Event) => {
  console.error('视频加载错误:', error)
  isPlaying.value = false
  isLoading.value = false
}

// 全屏状态监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  emit('fullscreen', isFullscreen.value)
}

onMounted(() => {
  if (videoElement.value) {
    videoElement.value.volume = volume.value / 100
  }
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  
  // 鼠标移动时显示控制栏
  if (videoContainer.value) {
    videoContainer.value.addEventListener('mousemove', showControlsTemporarily)
    videoContainer.value.addEventListener('mouseleave', () => {
      if (isPlaying.value) {
        showControls.value = false
      }
    })
  }
})

onUnmounted(() => {
  if (videoElement.value) {
    videoElement.value.pause()
  }
  
  if (controlsTimer) {
    clearTimeout(controlsTimer)
  }
  
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.video-player {
  width: 100%;
  max-width: 800px;
}

.player-container {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: black;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.video-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.video-icon {
  color: #6b7280;
  font-size: 24px;
}

.video-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.video-title {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.video-duration {
  color: #6b7280;
  font-size: 12px;
}

.player-actions {
  display: flex;
  gap: 8px;
}

.video-container {
  position: relative;
  background: black;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px 16px 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-controls.show {
  opacity: 1;
}

.play-btn {
  flex-shrink: 0;
}

.progress-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: white;
}

.progress-container {
  cursor: pointer;
}

.progress-track {
  position: relative;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 14px;
  height: 14px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: left 0.1s ease;
}

.progress-thumb:hover {
  transform: translate(-50%, -50%) scale(1.2);
}

.control-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-slider {
  width: 80px;
}

.volume-slider :deep(.el-slider__runway) {
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
}

.volume-slider :deep(.el-slider__bar) {
  background: #3b82f6;
}

.volume-slider :deep(.el-slider__button) {
  width: 12px;
  height: 12px;
  border: 2px solid #3b82f6;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  gap: 12px;
}

.loading-icon {
  font-size: 32px;
  animation: spin 1s linear infinite;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: background 0.3s ease;
}

.play-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.overlay-play-btn {
  background: rgba(59, 130, 246, 0.9) !important;
  border: none !important;
  backdrop-filter: blur(10px);
}

.overlay-play-btn:hover {
  background: rgba(59, 130, 246, 1) !important;
  transform: scale(1.1);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 全屏样式 */
.video-container:fullscreen {
  background: black;
}

.video-container:fullscreen .video-element {
  width: 100vw;
  height: 100vh;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .video-controls {
    flex-direction: column;
    gap: 12px;
    padding: 16px 12px 12px;
  }

  .progress-area {
    width: 100%;
  }

  .control-actions {
    width: 100%;
    justify-content: space-between;
  }

  .volume-control {
    flex: 1;
    justify-content: center;
  }

  .volume-slider {
    width: 120px;
  }
}
</style>
