package com.xhcai.common.security.config;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import jakarta.annotation.PostConstruct;

/**
 * CORS跨域配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class CorsConfig {

    private static final Logger log = LoggerFactory.getLogger(CorsConfig.class);

    @Value("${cors.allowed-origins:*}")
    private String allowedOriginsStr;

    @Value("${cors.allowed-methods:*}")
    private String allowedMethodsStr;

    @Value("${cors.allowed-headers:*}")
    private String allowedHeadersStr;

    @Value("${cors.allow-credentials:false}")
    private Boolean allowCredentials;

    @Value("${cors.max-age:3600}")
    private Long maxAge;

    @PostConstruct
    public void logCorsConfiguration() {
        log.info("🌐 CORS Configuration Loaded:");
        log.info("  ✅ Allowed Origins: {}", allowedOriginsStr);
        log.info("  ✅ Allowed Methods: {}", allowedMethodsStr);
        log.info("  ✅ Allowed Headers: {}", allowedHeadersStr);
        log.info("  ✅ Allow Credentials: {}", allowCredentials);
        log.info("  ✅ Max Age: {} seconds", maxAge);
    }

    /**
     * CORS配置源
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // 设置允许的源
        if ("*".equals(allowedOriginsStr)) {
            if (allowCredentials) {
                // 当允许凭证时，不能使用通配符，使用模式匹配
                configuration.addAllowedOriginPattern("*");
                log.warn("⚠️ CORS配置警告: 允许凭证时使用通配符源可能导致安全问题");
            } else {
                configuration.addAllowedOrigin("*");
            }
        } else {
            List<String> origins = Arrays.asList(allowedOriginsStr.split(","));
            for (String origin : origins) {
                configuration.addAllowedOrigin(origin.trim());
            }
        }

        // 设置允许的方法
        if ("*".equals(allowedMethodsStr)) {
            configuration.addAllowedMethod("*");
        } else {
            List<String> methods = Arrays.asList(allowedMethodsStr.split(","));
            configuration.setAllowedMethods(methods);
        }

        // 设置允许的头
        if ("*".equals(allowedHeadersStr)) {
            configuration.addAllowedHeader("*");
        } else {
            List<String> headers = Arrays.asList(allowedHeadersStr.split(","));
            configuration.setAllowedHeaders(headers);
        }

        // 设置是否允许凭证
        configuration.setAllowCredentials(allowCredentials);

        // 设置预检请求的缓存时间
        configuration.setMaxAge(maxAge);

        // 暴露的响应头
        configuration.setExposedHeaders(Arrays.asList(
                "Authorization",
                "Content-Type",
                "X-Requested-With",
                "accept",
                "Origin",
                "Access-Control-Request-Method",
                "Access-Control-Request-Headers"
        ));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        return source;
    }
}
