package com.xhcai.modules.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 文件上传响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文件上传响应")
public class FileUploadResponseDTO {

    @Schema(description = "文件记录ID")
    private String id;

    @Schema(description = "原始文件名")
    @JsonProperty("original_filename")
    private String originalFilename;

    @Schema(description = "文件大小（字节）")
    @JsonProperty("file_size")
    private Long fileSize;

    @Schema(description = "文件扩展名")
    @JsonProperty("file_extension")
    private String fileExtension;

    @Schema(description = "MIME类型")
    @JsonProperty("mime_type")
    private String mimeType;

    @Schema(description = "Dify文件ID")
    @JsonProperty("dify_file_id")
    private String difyFileId;

    @Schema(description = "MinIO存储桶名称")
    @JsonProperty("minio_bucket")
    private String minioBucket;

    @Schema(description = "MinIO对象名称")
    @JsonProperty("minio_object_name")
    private String minioObjectName;

    @Schema(description = "MinIO访问URL")
    @JsonProperty("minio_url")
    private String minioUrl;

    @Schema(description = "文件状态")
    private String status;

    @Schema(description = "上传时间")
    @JsonProperty("upload_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    @Schema(description = "Dify预览URL")
    @JsonProperty("dify_preview_url")
    private String difyPreviewUrl;

    @Schema(description = "错误信息")
    @JsonProperty("error_message")
    private String errorMessage;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getDifyFileId() {
        return difyFileId;
    }

    public void setDifyFileId(String difyFileId) {
        this.difyFileId = difyFileId;
    }

    public String getMinioBucket() {
        return minioBucket;
    }

    public void setMinioBucket(String minioBucket) {
        this.minioBucket = minioBucket;
    }

    public String getMinioObjectName() {
        return minioObjectName;
    }

    public void setMinioObjectName(String minioObjectName) {
        this.minioObjectName = minioObjectName;
    }

    public String getMinioUrl() {
        return minioUrl;
    }

    public void setMinioUrl(String minioUrl) {
        this.minioUrl = minioUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getDifyPreviewUrl() {
        return difyPreviewUrl;
    }

    public void setDifyPreviewUrl(String difyPreviewUrl) {
        this.difyPreviewUrl = difyPreviewUrl;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
