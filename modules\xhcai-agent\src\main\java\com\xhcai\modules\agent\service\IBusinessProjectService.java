package com.xhcai.modules.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.agent.dto.BusinessProjectCreateDTO;
import com.xhcai.modules.agent.dto.BusinessProjectQueryDTO;
import com.xhcai.modules.agent.dto.BusinessProjectUpdateDTO;
import com.xhcai.modules.agent.entity.BusinessProject;
import com.xhcai.modules.agent.vo.BusinessProjectVO;

import java.util.List;

/**
 * 业务项目服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IBusinessProjectService extends IService<BusinessProject> {

    /**
     * 分页查询业务项目列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<BusinessProjectVO> getProjectPage(BusinessProjectQueryDTO queryDTO);

    /**
     * 根据ID查询业务项目详情
     *
     * @param id 项目ID
     * @return 项目详情
     */
    BusinessProjectVO getProjectById(String id);

    /**
     * 创建业务项目
     *
     * @param createDTO 创建DTO
     * @return 创建的项目
     */
    BusinessProjectVO createProject(BusinessProjectCreateDTO createDTO);

    /**
     * 更新业务项目
     *
     * @param id 项目ID
     * @param updateDTO 更新DTO
     * @return 更新后的项目
     */
    BusinessProjectVO updateProject(String id, BusinessProjectUpdateDTO updateDTO);

    /**
     * 删除业务项目
     *
     * @param id 项目ID
     * @return 是否删除成功
     */
    boolean deleteProject(String id);

    /**
     * 批量删除业务项目
     *
     * @param ids 项目ID列表
     * @return 是否删除成功
     */
    boolean deleteProjects(List<String> ids);

    /**
     * 更新项目团队
     *
     * @param projectId 项目ID
     * @param teamMembers 团队成员列表
     * @return 更新后的项目
     */
    BusinessProjectVO updateProjectTeam(String projectId, List<BusinessProjectCreateDTO.TeamMemberDTO> teamMembers);

    /**
     * 添加团队成员
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 角色
     * @return 是否添加成功
     */
    boolean addTeamMember(String projectId, String userId, String role);

    /**
     * 移除团队成员
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否移除成功
     */
    boolean removeTeamMember(String projectId, String userId);

    /**
     * 更新团队成员角色
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param role 新角色
     * @return 是否更新成功
     */
    boolean updateTeamMemberRole(String projectId, String userId, String role);

    /**
     * 切换项目环境
     *
     * @param projectId 项目ID
     * @param environment 新环境
     * @return 更新后的项目
     */
    BusinessProjectVO switchEnvironment(String projectId, String environment);

    /**
     * 获取项目统计信息
     *
     * @param projectId 项目ID
     * @return 统计信息
     */
    BusinessProject getProjectStats(String projectId);
}
