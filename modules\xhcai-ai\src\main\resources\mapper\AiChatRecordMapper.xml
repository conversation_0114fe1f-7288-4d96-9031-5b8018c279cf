<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhcai.modules.ai.mapper.AiChatRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xhcai.modules.ai.entity.AiChatRecord">
        <id column="id" property="id" />
        <result column="session_id" property="sessionId" />
        <result column="user_id" property="userId" />
        <result column="model_name" property="modelName" />
        <result column="user_message" property="userMessage" />
        <result column="ai_response" property="aiResponse" />
        <result column="message_type" property="messageType" />
        <result column="tokens_used" property="tokensUsed" />
        <result column="cost_time" property="costTime" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="app_id" property="appId" />
        <result column="input" property="input" />
    </resultMap>

    <!-- VO查询映射结果 -->
    <resultMap id="VoResultMap" type="com.xhcai.modules.ai.vo.AiChatRecordVO">
        <id column="id" property="recordId" />
        <result column="session_id" property="sessionId" />
        <result column="user_id" property="userId" />
        <result column="model_name" property="modelName" />
        <result column="user_message" property="userMessage" />
        <result column="ai_response" property="aiResponse" />
        <result column="message_type" property="messageType" />
        <result column="tokens_used" property="tokensUsed" />
        <result column="cost_time" property="costTime" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="create_time" property="createTime" />
        <result column="app_id" property="appId" />
        <result column="input" property="input" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, session_id, user_id, model_name, user_message, ai_response,
        message_type, tokens_used, cost_time, status, error_msg,
        create_time, update_time, create_by, update_by, deleted, tenant_id, app_id, input
    </sql>

    <!-- 分页查询聊天记录 -->
    <select id="selectChatRecordPage" resultMap="VoResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM ai_chat_record
        <where>
            deleted = 0
            <if test="query.sessionId != null and query.sessionId != ''">
                AND session_id = #{query.sessionId}
            </if>
            <if test="query.userId != null">
                AND user_id = #{query.userId}
            </if>
            <if test="query.modelName != null and query.modelName != ''">
                AND model_name = #{query.modelName}
            </if>
            <if test="query.messageType != null and query.messageType != ''">
                AND message_type = #{query.messageType}
            </if>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.beginTime != null and query.beginTime != ''">
                AND create_time &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND create_time &lt;= #{query.endTime}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (user_message LIKE CONCAT('%', #{query.keyword}, '%') 
                     OR ai_response LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据会话ID和用户ID获取聊天历史记录 -->
    <select id="selectChatHistory" resultMap="VoResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM ai_chat_record
        WHERE deleted = 0
          AND session_id = #{sessionId}
        <if test="userId != null">
          AND user_id = #{userId}
        </if>
        ORDER BY create_time ASC
    </select>

</mapper>
