package com.xhcai.modules.system.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.modules.system.dto.SysLoginLogQueryDTO;
import com.xhcai.modules.system.service.ISysLoginLogService;
import com.xhcai.modules.system.vo.SysLoginLogVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 登录日志管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "登录日志管理", description = "登录日志管理相关接口")
@RestController
@RequestMapping("/api/system/login-log")
@RequiresTenantAdmin(message = "登录日志管理需要租户管理员权限")
public class SysLoginLogController {

    @Autowired
    private ISysLoginLogService loginLogService;

    /**
     * 分页查询登录日志列表
     */
    @Operation(summary = "分页查询登录日志列表", description = "根据条件分页查询登录日志信息")
    @GetMapping("/page")
    @RequiresPermissions("system:loginlog:view")
    public Result<PageResult<SysLoginLogVO>> page(@Valid SysLoginLogQueryDTO queryDTO) {
        PageResult<SysLoginLogVO> pageResult = loginLogService.selectLoginLogPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询登录日志详情
     */
    @Operation(summary = "查询登录日志详情", description = "根据ID查询登录日志详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("system:loginlog:view")
    public Result<SysLoginLogVO> getById(@PathVariable String id) {
        SysLoginLogVO loginLogVO = loginLogService.selectLoginLogById(id);
        return Result.success(loginLogVO);
    }

    /**
     * 删除登录日志
     */
    @Operation(summary = "删除登录日志", description = "根据ID删除登录日志")
    @DeleteMapping("/{id}")
    @RequiresPermissions("system:loginlog:delete")
    public Result<Void> delete(@PathVariable String id) {
        loginLogService.deleteLoginLogById(id);
        return Result.success();
    }

    /**
     * 批量删除登录日志
     */
    @Operation(summary = "批量删除登录日志", description = "根据ID列表批量删除登录日志")
    @DeleteMapping("/batch")
    @RequiresPermissions("system:loginlog:delete")
    public Result<Void> deleteBatch(@RequestBody String[] ids) {
        loginLogService.deleteLoginLogByIds(ids);
        return Result.success();
    }

    /**
     * 清理过期登录日志
     */
    @Operation(summary = "清理过期登录日志", description = "清理指定天数之前的登录日志")
    @DeleteMapping("/clean/{days}")
    @RequiresPermissions("system:loginlog:delete")
    public Result<Void> cleanExpiredLogs(@PathVariable int days) {
        loginLogService.cleanExpiredLogs(days);
        return Result.success();
    }

    /**
     * 获取登录统计信息
     */
    @Operation(summary = "获取登录统计信息", description = "获取登录相关的统计数据")
    @GetMapping("/stats")
    @RequiresPermissions("system:loginlog:view")
    public Result<Object> getLoginStats() {
        Object stats = loginLogService.getLoginStats();
        return Result.success(stats);
    }
}
