package com.xhcai.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.modules.system.annotation.DataScope;
import com.xhcai.modules.system.dto.SysDeptQueryDTO;
import com.xhcai.modules.system.entity.SysDept;
import com.xhcai.modules.system.service.ISysDeptService;
import com.xhcai.modules.system.vo.SysDeptVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 部门管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "部门管理", description = "部门管理相关接口")
@RestController
@RequestMapping("/api/system/dept")
@RequiresTenantAdmin(message = "部门管理需要租户管理员权限")
public class SysDeptController {

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询部门列表
     */
    @Operation(summary = "查询部门列表", description = "根据条件查询部门列表")
    @GetMapping("/list")
    @DataScope(deptAlias = "d")
    public Result<List<SysDeptVO>> list(@Valid SysDeptQueryDTO queryDTO) {
        List<SysDeptVO> deptList = deptService.selectDeptList(queryDTO);
        return Result.success(deptList);
    }

    /**
     * 查询部门树
     */
    @Operation(summary = "查询部门树", description = "查询部门树形结构")
    @GetMapping("/tree")
    @DataScope(deptAlias = "d")
    public Result<List<SysDeptVO>> tree(@Valid SysDeptQueryDTO queryDTO) {
        List<SysDeptVO> deptTree = deptService.selectDeptTree(queryDTO);
        return Result.success(deptTree);
    }

    /**
     * 查询部门选择树（排除指定部门）
     */
    @Operation(summary = "查询部门选择树", description = "查询部门选择树，排除指定部门及其子部门")
    @GetMapping("/select-tree")
    public Result<List<SysDeptVO>> selectTree(
            @Parameter(description = "排除的部门ID") @RequestParam(value = "excludeDeptId", required = false) String excludeDeptId) {
        List<SysDeptVO> deptTree = deptService.buildDeptSelectTree(excludeDeptId);
        return Result.success(deptTree);
    }

    /**
     * 根据ID查询部门详情
     */
    @Operation(summary = "查询部门详情", description = "根据部门ID查询详细信息")
    @GetMapping("/{id}")
    public Result<SysDeptVO> getById(
            @Parameter(description = "部门ID", required = true) @PathVariable String id) {
        SysDeptVO deptVO = deptService.selectDeptById(id);
        return Result.success(deptVO);
    }

    /**
     * 创建部门
     */
    @Operation(summary = "创建部门", description = "创建新的部门")
    @PostMapping
    public Result<Void> create(@Valid @RequestBody SysDept dept) {
        boolean result = deptService.insertDept(dept);
        return result ? Result.success() : Result.fail("创建部门失败");
    }

    /**
     * 更新部门信息
     */
    @Operation(summary = "更新部门信息", description = "更新部门基本信息")
    @PutMapping
    public Result<Void> update(@Valid @RequestBody SysDept dept) {
        boolean result = deptService.updateDept(dept);
        return result ? Result.success() : Result.fail("更新部门失败");
    }

    /**
     * 删除部门
     */
    @Operation(summary = "删除部门", description = "批量删除部门")
    @DeleteMapping
    public Result<Void> delete(@RequestBody List<String> deptIds) {
        boolean result = deptService.deleteDepts(deptIds);
        return result ? Result.success() : Result.fail("删除部门失败");
    }

    /**
     * 启用部门
     */
    @Operation(summary = "启用部门", description = "启用指定部门")
    @PutMapping("/{id}/enable")
    public Result<Void> enable(
            @Parameter(description = "部门ID", required = true) @PathVariable String id) {
        boolean result = deptService.enableDept(id);
        return result ? Result.success() : Result.fail("启用部门失败");
    }

    /**
     * 停用部门
     */
    @Operation(summary = "停用部门", description = "停用指定部门")
    @PutMapping("/{id}/disable")
    public Result<Void> disable(
            @Parameter(description = "部门ID", required = true) @PathVariable String id) {
        boolean result = deptService.disableDept(id);
        return result ? Result.success() : Result.fail("停用部门失败");
    }

    /**
     * 移动部门
     */
    @Operation(summary = "移动部门", description = "将部门移动到新的父部门下")
    @PutMapping("/{id}/move")
    public Result<Void> move(
            @Parameter(description = "部门ID", required = true) @PathVariable String id,
            @Parameter(description = "新父部门ID", required = true) @RequestParam String newParentId) {
        boolean result = deptService.moveDept(id, newParentId);
        return result ? Result.success() : Result.fail("移动部门失败");
    }

    /**
     * 查询子部门
     */
    @Operation(summary = "查询子部门", description = "查询指定部门的直接子部门")
    @GetMapping("/{id}/children")
    public Result<List<SysDeptVO>> getChildren(
            @Parameter(description = "父部门ID", required = true) @PathVariable String id) {
        List<SysDeptVO> children = deptService.selectChildrenByParentId(id);
        return Result.success(children);
    }

    /**
     * 查询部门及其所有子部门
     */
    @Operation(summary = "查询部门及子部门", description = "查询部门及其所有子部门")
    @GetMapping("/{id}/descendants")
    public Result<List<SysDeptVO>> getDescendants(
            @Parameter(description = "部门ID", required = true) @PathVariable String id) {
        List<SysDeptVO> descendants = deptService.selectDeptAndChildren(id);
        return Result.success(descendants);
    }

    /**
     * 获取部门路径
     */
    @Operation(summary = "获取部门路径", description = "获取部门的完整路径")
    @GetMapping("/{id}/path")
    public Result<String> getDeptPath(
            @Parameter(description = "部门ID", required = true) @PathVariable String id) {
        String path = deptService.getDeptPath(id);
        return Result.success(path);
    }

    /**
     * 获取部门用户数量
     */
    @Operation(summary = "获取部门用户数量", description = "获取指定部门的用户数量")
    @GetMapping("/{id}/user-count")
    public Result<Integer> getUserCount(
            @Parameter(description = "部门ID", required = true) @PathVariable String id) {
        Integer userCount = deptService.getDeptUserCount(id);
        return Result.success(userCount);
    }

    /**
     * 检查部门编码是否存在
     */
    @Operation(summary = "检查部门编码", description = "检查部门编码是否已存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkDeptCode(
            @Parameter(description = "部门编码", required = true) @RequestParam("deptCode") String deptCode,
            @Parameter(description = "排除的部门ID") @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = deptService.existsDeptCode(deptCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查部门名称是否存在
     */
    @Operation(summary = "检查部门名称", description = "检查同级部门名称是否已存在")
    @GetMapping("/check-name")
    public Result<Boolean> checkDeptName(
            @Parameter(description = "部门名称", required = true) @RequestParam("deptName") String deptName,
            @Parameter(description = "父部门ID", required = true) @RequestParam("parentId") String parentId,
            @Parameter(description = "排除的部门ID") @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = deptService.existsDeptName(deptName, parentId, excludeId);
        return Result.success(exists);
    }

    /**
     * 批量更新部门状态
     */
    @Operation(summary = "批量更新状态", description = "批量更新部门状态")
    @PutMapping("/batch-status")
    public Result<Void> batchUpdateStatus(
            @RequestBody List<String> deptIds,
            @Parameter(description = "状态", required = true) @RequestParam String status) {
        boolean result = deptService.batchUpdateStatus(deptIds, status);
        return result ? Result.success() : Result.fail("批量更新状态失败");
    }

    /**
     * 同步部门排序
     */
    @Operation(summary = "同步部门排序", description = "同步部门排序号")
    @PutMapping("/sync-order")
    public Result<Void> syncOrder(@RequestBody List<SysDept> depts) {
        boolean result = deptService.syncDeptOrder(depts);
        return result ? Result.success() : Result.fail("同步排序失败");
    }
}
