@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './compatibility.css';

/* 全局样式变量 */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea, #764ba2);
  --secondary-gradient: linear-gradient(135deg, #f5f7fa, #e4eaf3);
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --text-primary: #2c3e50;
  --text-secondary: #5a6c7d;
  --text-muted: #7f8c8d;
  --background-white: rgba(255, 255, 255, 0.95);
  --border-light: rgba(0, 0, 0, 0.1);
  --shadow-light: 0 2px 20px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.1);
  --border-radius: 6px;
  --border-radius-large: 16px;
  --transition: all 0.3s ease;
}

/* CSS变量不支持时的fallback */
html {
  /* Fallback colors for browsers that don't support CSS variables */
  color: #2c3e50;
  background-color: #f5f7fa;
}

/* 检测CSS变量支持 */
@supports not (color: var(--primary-color)) {
  /* 为不支持CSS变量的浏览器提供固定样式 */
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  .card {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  }
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Vue Flow 执行状态指示器层级修复 */
.vue-flow .vue-flow__node .execution-indicator {
  position: absolute !important;
  z-index: 999999 !important;
  pointer-events: none !important;
}

/* 确保Vue Flow节点容器不会遮盖执行状态指示器 */
.vue-flow .vue-flow__node {
  overflow: visible !important;
}

/* 为执行状态的节点提供更高的层级 */
.vue-flow .vue-flow__node.execution-running,
.vue-flow .vue-flow__node.execution-waiting,
.vue-flow .vue-flow__node.execution-error,
.vue-flow .vue-flow__node.execution-completed,
.vue-flow .vue-flow__node.execution-skipped {
  z-index: 1001 !important;
  position: relative !important;
}

/* 当前执行节点的最高层级 */
.vue-flow .vue-flow__node.node-currently-executing {
  z-index: 1002 !important;
  position: relative !important;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--secondary-gradient);
}

/* 滚动条样式 - 兼容性处理 */
/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #667eea rgba(0, 0, 0, 0.1);
}

/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  -webkit-border-radius: 4px;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #667eea;
  background: var(--primary-gradient, #667eea);
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: background 0.3s ease;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4c93);
}

/* 高对比度模式下的滚动条样式 */
@media (forced-colors: active) {
  body {
    scrollbar-width: auto;
  }
}

/* 保持对旧浏览器的兼容性 */
@supports not (forced-colors: active) {
  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    body {
      -ms-overflow-style: -ms-autohiding-scrollbar;
    }
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具类 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 自定义组件样式 - 兼容性处理 */
.btn-primary {
  /* Fallback styles */
  background: #667eea;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  /* Modern styles with Tailwind */
  @apply bg-gradient-primary text-white px-6 py-3 rounded-xl font-medium transition-all duration-300;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  /* Fallback styles */
  background: rgba(255, 255, 255, 0.95);
  color: #667eea;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  /* Modern styles with Tailwind */
  @apply bg-white/95 text-primary-500 px-6 py-3 rounded-xl font-medium border border-black/10 transition-all duration-300;
}

.btn-secondary:hover {
  background-color: #f0f4ff;
  transform: translateY(-2px);
}

.card {
  /* Fallback styles */
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  /* Modern styles with backdrop-filter support */
  @apply bg-white/95 rounded-2xl shadow-lg border border-white/20 transition-all duration-300;
}

/* 支持backdrop-filter的浏览器 */
@supports (backdrop-filter: blur(12px)) or (-webkit-backdrop-filter: blur(12px)) {
  .card {
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
  }
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.form-input {
  /* Fallback styles */
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e0e9ff;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  outline: none;
  /* Modern styles with Tailwind */
  @apply w-full px-4 py-3 border-2 border-primary-100 rounded-xl bg-white/80 transition-all duration-300 outline-none;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.form-label {
  /* Fallback styles */
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  /* Modern styles with Tailwind */
  @apply block text-sm font-semibold text-gray-700 mb-2;
}

/* 知识图谱和数据源页面专用样式 */
.data-source-card {
  @apply bg-white border border-gray-200 rounded-xl p-6 hover:border-blue-300 hover:shadow-lg transition-all duration-300 cursor-pointer;
  transform: translateY(0);
}

.data-source-card:hover {
  transform: translateY(-2px);
}

.knowledge-graph-card {
  @apply bg-white border border-gray-200 rounded-lg p-6 hover:border-purple-300 hover:shadow-lg transition-all duration-300 cursor-pointer;
  transform: translateY(0);
}

.knowledge-graph-card:hover {
  transform: translateY(-2px);
}

/* VueFlow 自定义样式 */
.vue-flow__node {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.vue-flow__edge-path {
  stroke: #6b7280;
  stroke-width: 2;
}

.vue-flow__edge.animated .vue-flow__edge-path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* 现代化表单样式 */
.modern-select {
  @apply px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white;
}

.modern-checkbox {
  @apply rounded border-gray-300 text-blue-600 focus:ring-blue-500;
}

.modern-radio {
  @apply text-blue-600 focus:ring-blue-500;
}

/* 渐变背景 */
.gradient-header {
  background: linear-gradient(135deg, #f0f4ff 0%, #f3f0ff 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #faf5ff 0%, #fdf2f8 100%);
}

.gradient-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

/* 加载状态 */
.loading-overlay {
  @apply fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50;
}

.loading-spinner-large {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin;
}

/* 面包屑样式 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #6b7280;
}

.breadcrumb-item.current {
  color: #1f2937;
  font-weight: 600;
}

.breadcrumb i {
  color: #d1d5db;
  font-size: 12px;
}
