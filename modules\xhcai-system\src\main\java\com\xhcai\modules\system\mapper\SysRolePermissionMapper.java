package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.system.entity.SysRolePermission;

/**
 * 角色权限关联Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysRolePermissionMapper extends BaseMapper<SysRolePermission> {

    /**
     * 根据角色ID删除角色权限关联
     *
     * @param roleId 角色ID
     * @return 删除行数
     */
    @Update("UPDATE sys_role_permission SET deleted = 1 WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") String roleId);

    /**
     * 根据角色ID列表删除角色权限关联
     *
     * @param roleIds 角色ID列表
     * @return 删除行数
     */
    @Update("<script>"
            + "UPDATE sys_role_permission SET deleted = 1 "
            + "WHERE role_id IN "
            + "<foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>"
            + "#{roleId}"
            + "</foreach>"
            + "</script>")
    int deleteByRoleIds(@Param("roleIds") List<String> roleIds);

    /**
     * 根据权限ID删除角色权限关联
     *
     * @param permissionId 权限ID
     * @return 删除行数
     */
    @Update("UPDATE sys_role_permission SET deleted = 1 WHERE permission_id = #{permissionId}")
    int deleteByPermissionId(@Param("permissionId") String permissionId);

    /**
     * 根据权限ID列表删除角色权限关联
     *
     * @param permissionIds 权限ID列表
     * @return 删除行数
     */
    @Update("<script>"
            + "UPDATE sys_role_permission SET deleted = 1 "
            + "WHERE permission_id IN "
            + "<foreach collection='permissionIds' item='permissionId' open='(' separator=',' close=')'>"
            + "#{permissionId}"
            + "</foreach>"
            + "</script>")
    int deleteByPermissionIds(@Param("permissionIds") List<String> permissionIds);

    /**
     * 查询角色的权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @Select("SELECT permission_id FROM sys_role_permission "
            + "WHERE role_id = #{roleId} AND deleted = 0")
    List<String> selectPermissionIdsByRoleId(@Param("roleId") String roleId);

    /**
     * 查询权限的角色ID列表
     *
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_role_permission "
            + "WHERE permission_id = #{permissionId} AND deleted = 0")
    List<String> selectRoleIdsByPermissionId(@Param("permissionId") String permissionId);

    /**
     * 检查角色权限关联是否存在
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_role_permission "
            + "WHERE role_id = #{roleId} AND permission_id = #{permissionId} AND deleted = 0")
    Integer existsRolePermission(@Param("roleId") String roleId, @Param("permissionId") String permissionId);

    /**
     * 根据用户ID查询权限ID列表
     *
     * @param userId 用户ID
     * @return 权限ID列表
     */
    @Select("SELECT DISTINCT rp.permission_id FROM sys_role_permission rp "
            + "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id "
            + "INNER JOIN sys_role r ON rp.role_id = r.id "
            + "WHERE ur.user_id = #{userId} AND rp.deleted = 0 AND ur.deleted = 0 "
            + "AND r.status = '0' AND r.deleted = 0")
    List<String> selectPermissionIdsByUserId(@Param("userId") String userId);

    /**
     * 复制角色权限
     *
     * @param sourceRoleId 源角色ID
     * @param targetRoleId 目标角色ID
     * @param tenantId 租户ID
     * @param createBy 创建者
     * @return 复制行数
     */
    @Insert("INSERT INTO sys_role_permission (id, role_id, permission_id, tenant_id, create_by, create_time, update_by, update_time, deleted) "
            + "SELECT REPLACE(gen_random_uuid()::text, '-', ''), #{targetRoleId}, permission_id, #{tenantId}, #{createBy}, NOW(), #{createBy}, NOW(), 0 "
            + "FROM sys_role_permission "
            + "WHERE role_id = #{sourceRoleId} AND deleted = 0")
    int copyRolePermissions(@Param("sourceRoleId") String sourceRoleId,
            @Param("targetRoleId") String targetRoleId,
            @Param("tenantId") String tenantId,
            @Param("createBy") String createBy);
}
