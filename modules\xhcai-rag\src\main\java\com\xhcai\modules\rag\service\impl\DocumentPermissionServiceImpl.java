package com.xhcai.modules.rag.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.DocumentPermissionDTO;
import com.xhcai.modules.rag.entity.DocumentPermission;
import com.xhcai.modules.rag.mapper.DocumentPermissionMapper;
import com.xhcai.modules.rag.service.IDocumentPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文档权限服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor
public class DocumentPermissionServiceImpl extends ServiceImpl<DocumentPermissionMapper, DocumentPermission> implements IDocumentPermissionService {

    private final DocumentPermissionMapper documentPermissionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDocumentPermission(DocumentPermissionDTO permissionDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        if (CollectionUtils.isEmpty(permissionDTO.getDocumentIds())) {
            throw new BusinessException("文档ID列表不能为空");
        }

        // 删除现有权限
        documentPermissionMapper.deleteByDocumentIds(tenantId, permissionDTO.getDocumentIds());

        // 创建新权限
        List<DocumentPermission> permissions = new ArrayList<>();

        for (String documentId : permissionDTO.getDocumentIds()) {
            if ("public".equals(permissionDTO.getPermissionType())) {
                // 公开权限
                DocumentPermission permission = createPermission(documentId, "public", null, null,
                        permissionDTO.getPermissionLevel(), tenantId, currentUserId);
                permissions.add(permission);
            } else if ("private".equals(permissionDTO.getPermissionType())) {
                // 私有权限
                DocumentPermission permission = createPermission(documentId, "private", currentUserId, "user",
                        permissionDTO.getPermissionLevel(), tenantId, currentUserId);
                permissions.add(permission);
            } else if ("role".equals(permissionDTO.getPermissionType()) && !CollectionUtils.isEmpty(permissionDTO.getRoleIds())) {
                // 角色权限
                for (String roleId : permissionDTO.getRoleIds()) {
                    DocumentPermission permission = createPermission(documentId, "role", roleId, "role",
                            permissionDTO.getPermissionLevel(), tenantId, currentUserId);
                    permissions.add(permission);
                }
            } else if ("department".equals(permissionDTO.getPermissionType()) && !CollectionUtils.isEmpty(permissionDTO.getDepartmentIds())) {
                // 部门权限
                for (String deptId : permissionDTO.getDepartmentIds()) {
                    DocumentPermission permission = createPermission(documentId, "department", deptId, "department",
                            permissionDTO.getPermissionLevel(), tenantId, currentUserId);
                    permissions.add(permission);
                }
            } else if ("users".equals(permissionDTO.getPermissionType()) && !CollectionUtils.isEmpty(permissionDTO.getUserIds())) {
                // 指定用户权限
                for (String userId : permissionDTO.getUserIds()) {
                    DocumentPermission permission = createPermission(documentId, "users", userId, "user",
                            permissionDTO.getPermissionLevel(), tenantId, currentUserId);
                    permissions.add(permission);
                }
            }
        }

        if (!permissions.isEmpty()) {
            saveBatch(permissions);
        }
    }

    @Override
    public List<DocumentPermission> getDocumentPermissions(String documentId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        return documentPermissionMapper.selectByDocumentId(tenantId, documentId);
    }

    @Override
    public List<DocumentPermission> getDocumentPermissions(List<String> documentIds) {
        if (CollectionUtils.isEmpty(documentIds)) {
            return new ArrayList<>();
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return documentPermissionMapper.selectByDocumentIds(tenantId, documentIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDocumentPermissions(String documentId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        documentPermissionMapper.deleteByDocumentId(tenantId, documentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDocumentPermissions(List<String> documentIds) {
        if (CollectionUtils.isEmpty(documentIds)) {
            return;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        documentPermissionMapper.deleteByDocumentIds(tenantId, documentIds);
    }

    @Override
    public boolean hasDocumentAccess(String documentId, String userId) {
        // 获取当前用户的角色和部门信息
        Set<String> userRoleIds = SecurityUtils.getCurrentUserRoleIds();
        String userDeptId = SecurityUtils.getCurrentUserDeptId();

        return hasDocumentAccess(documentId, userId, userRoleIds, userDeptId);
    }

    @Override
    public boolean hasDocumentAccess(String documentId, String userId, Set<String> roleIds, String departmentId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 获取文档的权限设置
        List<DocumentPermission> permissions = documentPermissionMapper.selectByDocumentId(tenantId, documentId);

        if (permissions.isEmpty()) {
            // 如果没有权限设置，默认为私有（只有创建者可以访问）
            return false;
        }

        // 检查权限
        for (DocumentPermission permission : permissions) {
            if ("public".equals(permission.getPermissionType())) {
                // 公开权限，所有人都可以访问
                return true;
            } else if ("private".equals(permission.getPermissionType()) && userId.equals(permission.getTargetId())) {
                // 私有权限，只有指定用户可以访问
                return true;
            } else if ("users".equals(permission.getPermissionType()) && userId.equals(permission.getTargetId())) {
                // 指定用户权限
                return true;
            } else if ("role".equals(permission.getPermissionType())) {
                // 角色权限，检查用户是否拥有该角色
                if (roleIds != null && roleIds.contains(permission.getTargetId())) {
                    return true;
                }
            } else if ("department".equals(permission.getPermissionType())) {
                // 部门权限，检查用户是否属于该部门
                if (departmentId != null && departmentId.equals(permission.getTargetId())) {
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    public List<String> getAccessibleDocumentIds(List<String> documentIds, String userId) {
        if (CollectionUtils.isEmpty(documentIds)) {
            return new ArrayList<>();
        }

        // 获取当前用户的角色和部门信息
        Set<String> userRoleIds = SecurityUtils.getCurrentUserRoleIds();
        String userDeptId = SecurityUtils.getCurrentUserDeptId();

        return documentIds.stream()
                .filter(documentId -> hasDocumentAccess(documentId, userId, userRoleIds, userDeptId))
                .collect(Collectors.toList());
    }

    /**
     * 创建权限对象
     */
    private DocumentPermission createPermission(String documentId, String permissionType, String targetId,
            String targetType, String permissionLevel, String tenantId, String currentUserId) {
        DocumentPermission permission = new DocumentPermission();
        permission.setDocumentId(documentId);
        permission.setPermissionType(permissionType);
        permission.setTargetId(targetId);
        permission.setTargetType(targetType);
        permission.setPermissionLevel(permissionLevel);
        permission.setEnabled(true);
        permission.setTenantId(tenantId);
        permission.setCreateBy(currentUserId);
        return permission;
    }
}
