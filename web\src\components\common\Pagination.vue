<template>
  <div class="pagination-wrapper" v-if="totalPages > 1 || showPageSizeSelector">
    <div class="pagination-info">
      <span class="info-text">
        显示 {{ startIndex }}-{{ endIndex }} 条，共 {{ total }} 条记录
      </span>
      
      <!-- 每页显示条数选择器 -->
      <div v-if="showPageSizeSelector" class="page-size-selector">
        <span class="page-size-label">每页显示</span>
        <select 
          :value="pageSize" 
          @change="handlePageSizeChange"
          class="page-size-select"
        >
          <option 
            v-for="size in pageSizeOptions" 
            :key="size" 
            :value="size"
          >
            {{ size }}
          </option>
        </select>
        <span class="page-size-label">条</span>
      </div>
    </div>
    
    <div class="pagination-controls" v-if="totalPages > 1">
      <!-- 首页 -->
      <button
        class="page-btn"
        @click="changePage(1)"
        :disabled="currentPage === 1"
        :title="firstPageText"
      >
        <i class="fas fa-angle-double-left"></i>
      </button>
      
      <!-- 上一页 -->
      <button
        class="page-btn"
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
        :title="prevPageText"
      >
        <i class="fas fa-angle-left"></i>
      </button>
      
      <!-- 页码显示 -->
      <div class="page-numbers" v-if="showPageNumbers">
        <button
          v-for="page in visiblePages"
          :key="page"
          class="page-btn page-number"
          :class="{ active: page === currentPage }"
          @click="changePage(page)"
          :disabled="page === '...'"
        >
          {{ page }}
        </button>
      </div>
      
      <!-- 页面信息 -->
      <span v-else class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </span>
      
      <!-- 下一页 -->
      <button
        class="page-btn"
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
        :title="nextPageText"
      >
        <i class="fas fa-angle-right"></i>
      </button>
      
      <!-- 末页 -->
      <button
        class="page-btn"
        @click="changePage(totalPages)"
        :disabled="currentPage === totalPages"
        :title="lastPageText"
      >
        <i class="fas fa-angle-double-right"></i>
      </button>
      
      <!-- 跳转到指定页 -->
      <div v-if="showJumper" class="page-jumper">
        <span class="jumper-label">跳至</span>
        <input
          type="number"
          :min="1"
          :max="totalPages"
          v-model.number="jumpPage"
          @keyup.enter="handleJump"
          class="jumper-input"
        />
        <span class="jumper-label">页</span>
        <button 
          class="jumper-btn"
          @click="handleJump"
          :disabled="!isValidJumpPage"
        >
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  currentPage: number
  pageSize: number
  total: number
  pageSizeOptions?: number[]
  showPageSizeSelector?: boolean
  showPageNumbers?: boolean
  showJumper?: boolean
  maxVisiblePages?: number
  firstPageText?: string
  prevPageText?: string
  nextPageText?: string
  lastPageText?: string
}

interface Emits {
  (e: 'update:currentPage', page: number): void
  (e: 'update:pageSize', size: number): void
  (e: 'change', page: number, size: number): void
  (e: 'pageSizeChange', size: number): void
}

const props = withDefaults(defineProps<Props>(), {
  pageSizeOptions: () => [10, 20, 50, 100],
  showPageSizeSelector: true,
  showPageNumbers: false,
  showJumper: false,
  maxVisiblePages: 7,
  firstPageText: '首页',
  prevPageText: '上一页',
  nextPageText: '下一页',
  lastPageText: '末页'
})

const emit = defineEmits<Emits>()

// 响应式数据
const jumpPage = ref<number>(props.currentPage)

// 计算属性
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

const startIndex = computed(() => {
  if (props.total === 0) return 0
  return (props.currentPage - 1) * props.pageSize + 1
})

const endIndex = computed(() => {
  return Math.min(props.currentPage * props.pageSize, props.total)
})

const isValidJumpPage = computed(() => {
  return jumpPage.value >= 1 && jumpPage.value <= totalPages.value && jumpPage.value !== props.currentPage
})

const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const maxVisible = props.maxVisiblePages
  const current = props.currentPage
  const total = totalPages.value

  if (total <= maxVisible) {
    // 总页数小于等于最大显示页数，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总页数大于最大显示页数，需要省略
    const halfVisible = Math.floor(maxVisible / 2)
    
    if (current <= halfVisible + 1) {
      // 当前页在前半部分
      for (let i = 1; i <= maxVisible - 2; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - halfVisible) {
      // 当前页在后半部分
      pages.push(1)
      pages.push('...')
      for (let i = total - maxVisible + 3; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间部分
      pages.push(1)
      pages.push('...')
      for (let i = current - halfVisible + 1; i <= current + halfVisible - 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }
  
  return pages
})

// 方法
const changePage = (page: number) => {
  if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
    emit('update:currentPage', page)
    emit('change', page, props.pageSize)
  }
}

const handlePageSizeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  const newSize = parseInt(target.value)
  
  // 计算新的页码，保持当前显示的第一条记录位置不变
  const currentFirstIndex = (props.currentPage - 1) * props.pageSize + 1
  const newPage = Math.ceil(currentFirstIndex / newSize)
  
  emit('update:pageSize', newSize)
  emit('update:currentPage', newPage)
  emit('pageSizeChange', newSize)
  emit('change', newPage, newSize)
}

const handleJump = () => {
  if (isValidJumpPage.value) {
    changePage(jumpPage.value)
  }
}

// 监听当前页变化，同步跳转输入框
watch(() => props.currentPage, (newPage) => {
  jumpPage.value = newPage
})
</script>

<style scoped>
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.info-text {
  white-space: nowrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}

.page-size-select {
  padding: 4px 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.page-size-select:focus {
  outline: none;
  border-color: #3b82f6;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-number {
  min-width: 32px;
  font-weight: 500;
}

.page-number.active {
  background: #3b82f6;
  border-color: #3b82f6;
}

.page-number.active:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.page-info {
  font-size: 14px;
  color: #6b7280;
  margin: 0 8px;
  white-space: nowrap;
}

.page-jumper {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
  padding-left: 16px;
  border-left: 1px solid #e5e7eb;
}

.jumper-label {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}

.jumper-input {
  width: 60px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.jumper-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.jumper-btn {
  height: 32px;
  padding: 0 12px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
}

.jumper-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
}

.jumper-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-wrapper {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .pagination-info {
    justify-content: center;
  }
  
  .pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .page-jumper {
    margin-left: 0;
    padding-left: 0;
    border-left: none;
    border-top: 1px solid #e5e7eb;
    padding-top: 16px;
    justify-content: center;
  }
}
</style>
