# Dify平台对接配置
dify:
  # Dify API基础URL - 使用本地Dify实例
  base-url: ${DIFY_BASE_URL:http://192.168.50.142}
  # API密钥（需要在环境变量或配置中心配置）
  api-key: ${DIFY_API_KEY:app-HZWa3wyLD5KZVwbksobWe9nk}
  # 应用ID（用于Console API调用）
  app-id: ${DIFY_APP_ID:}
  # 登录邮箱
  email: ${DIFY_EMAIL:<EMAIL>}
  # 登录密码
  password: ${DIFY_PASSWORD:XH12345@}
  # 语言设置
  language: ${DIFY_LANGUAGE:zh-Hans}
  # 连接超时时间（毫秒）- 减少超时时间以快速失败
  connect-timeout: ${DIFY_CONNECT_TIMEOUT:10000}
  # 读取超时时间（毫秒）- 减少超时时间以快速失败
  read-timeout: ${DIFY_READ_TIMEOUT:30000}
  # 是否启用SSL验证
  ssl-enabled: ${DIFY_SSL_ENABLED:false}
  # 重试次数
  retry-count: ${DIFY_RETRY_COUNT:3}
  # 重试间隔（毫秒）
  retry-interval: ${DIFY_RETRY_INTERVAL:1000}
  # 是否启用测试模式（使用Mock响应）
  test-mode: ${DIFY_TEST_MODE:false}

# 日志配置
logging:
  level:
    com.xhcai.modules.dify: ${DIFY_LOG_LEVEL:DEBUG}
    org.springframework.web.reactive.function.client: ${DIFY_WEBCLIENT_LOG_LEVEL:DEBUG}

# Spring配置
spring:
  # WebFlux配置
  webflux:
    # 最大内存大小
    max-in-memory-size: ${DIFY_WEBFLUX_MAX_MEMORY:10MB}

  # 文件上传配置
  servlet:
    multipart:
      # 最大文件大小
      max-file-size: ${DIFY_MAX_FILE_SIZE:100MB}
      # 最大请求大小
      max-request-size: ${DIFY_MAX_REQUEST_SIZE:100MB}

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: ${DIFY_MANAGEMENT_ENDPOINTS:health,info,metrics,dify}
  endpoint:
    health:
      show-details: ${DIFY_HEALTH_SHOW_DETAILS:when-authorized}
    dify:
      enabled: ${DIFY_ENDPOINT_ENABLED:true}
  metrics:
    tags:
      module: dify
