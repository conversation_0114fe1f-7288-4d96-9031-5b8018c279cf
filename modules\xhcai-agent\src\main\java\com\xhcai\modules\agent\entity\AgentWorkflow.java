package com.xhcai.modules.agent.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 智能体工作流实体 基于Vue Flow数据结构重新设计，支持完整的流程编排功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent_workflow")
@Schema(description = "智能体工作流")
@TableName("agent_workflow")
public class AgentWorkflow extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @Column(name = "agent_id", length = 36)
    @Schema(description = "智能体ID", example = "agent_001")
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    @TableField("agent_id")
    private String agentId;

    /**
     * 工作流名称
     */
    @Column(name = "name", length = 100)
    @Schema(description = "工作流名称", example = "客服工作流")
    @NotBlank(message = "工作流名称不能为空")
    @Size(min = 1, max = 100, message = "工作流名称长度必须在1-100个字符之间")
    @TableField("name")
    private String name;

    /**
     * 工作流描述
     */
    @Column(name = "description", length = 500)
    @Schema(description = "工作流描述", example = "智能客服的工作流程配置")
    @Size(max = 500, message = "工作流描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 版本号
     */
    @Column(name = "version")
    @Schema(description = "版本号", example = "1")
    @NotNull(message = "版本号不能为空")
    @TableField("version")
    private Integer version;

    /**
     * Vue Flow节点数据（JSON格式） 存储Vue Flow的nodes数组，包含节点的完整信息
     */
    @Column(name = "nodes_data", columnDefinition = "TEXT")
    @Schema(description = "Vue Flow节点数据", example = "[{\"id\":\"node1\",\"type\":\"start\",\"position\":{\"x\":100,\"y\":100},\"data\":{\"label\":\"开始\"}}]")
    @TableField("nodes_data")
    private String nodesData;

    /**
     * Vue Flow边数据（JSON格式） 存储Vue Flow的edges数组，包含连接线的完整信息
     */
    @Column(name = "edges_data", columnDefinition = "TEXT")
    @Schema(description = "Vue Flow边数据", example = "[{\"id\":\"edge1\",\"source\":\"node1\",\"target\":\"node2\",\"type\":\"default\"}]")
    @TableField("edges_data")
    private String edgesData;

    /**
     * 视口配置（JSON格式） 存储画布的缩放、平移等视口信息
     */
    @Column(name = "viewport_config", columnDefinition = "TEXT")
    @Schema(description = "视口配置", example = "{\"x\":0,\"y\":0,\"zoom\":1}")
    @TableField("viewport_config")
    private String viewportConfig;

    /**
     * 全局变量配置（JSON格式） 存储工作流的全局变量和环境配置
     */
    @Column(name = "global_variables", columnDefinition = "TEXT")
    @Schema(description = "全局变量配置", example = "{\"var1\":\"value1\",\"var2\":\"value2\"}")
    @TableField("global_variables")
    private String globalVariables;

    /**
     * 节点库配置（JSON格式） 存储可用的节点类型和配置模板
     */
    @Column(name = "node_library", columnDefinition = "TEXT")
    @Schema(description = "节点库配置", example = "{\"categories\":[{\"name\":\"基础节点\",\"nodes\":[{\"type\":\"start\",\"label\":\"开始节点\"}]}]}")
    @TableField("node_library")
    private String nodeLibrary;

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @TableField("status")
    private String status;

    /**
     * 是否已发布
     */
    @Column(name = "is_published")
    @Schema(description = "是否已发布", example = "false")
    @TableField("is_published")
    private Boolean isPublished;

    /**
     * 发布时间
     */
    @Column(name = "published_at")
    @Schema(description = "发布时间")
    @TableField("published_at")
    private LocalDateTime publishedAt;

    /**
     * 最后修改时间
     */
    @Column(name = "last_modified")
    @Schema(description = "最后修改时间")
    @TableField("last_modified")
    private LocalDateTime lastModified;

    /**
     * 操作类型
     */
    @Column(name = "operation_type", length = 50)
    @Schema(description = "操作类型", example = "node_add")
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @Column(name = "operation_desc", length = 200)
    @Schema(description = "操作描述", example = "添加了开始节点")
    @Size(max = 200, message = "操作描述长度不能超过200个字符")
    @TableField("operation_desc")
    private String operationDesc;

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getNodesData() {
        return nodesData;
    }

    public void setNodesData(String nodesData) {
        this.nodesData = nodesData;
    }

    public String getEdgesData() {
        return edgesData;
    }

    public void setEdgesData(String edgesData) {
        this.edgesData = edgesData;
    }

    public String getViewportConfig() {
        return viewportConfig;
    }

    public void setViewportConfig(String viewportConfig) {
        this.viewportConfig = viewportConfig;
    }

    public String getNodeLibrary() {
        return nodeLibrary;
    }

    public void setNodeLibrary(String nodeLibrary) {
        this.nodeLibrary = nodeLibrary;
    }

    public String getGlobalVariables() {
        return globalVariables;
    }

    public void setGlobalVariables(String globalVariables) {
        this.globalVariables = globalVariables;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public LocalDateTime getLastModified() {
        return lastModified;
    }

    public void setLastModified(LocalDateTime lastModified) {
        this.lastModified = lastModified;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }
}
