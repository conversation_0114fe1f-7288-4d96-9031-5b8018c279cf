<template>
  <!-- 最小化任务栏 -->
  <div class="minimized-taskbar" v-show="minimizedAgents.length > 0">
    <div
      v-for="agent in minimizedAgents"
      :key="agent.id"
      class="minimized-item"
      @click="restoreAgent(agent.id)"
    >
      <div class="minimized-icon">
        <i :class="agent.icon"></i>
      </div>
      <span class="minimized-title">{{ agent.name }}</span>
      <button class="minimized-close" @click.stop="closeAgent(agent.id)">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义智能体类型
interface Agent {
  id: string
  name: string
  description: string
  icon: string
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
}

// 定义运行中智能体的类型
interface RunningAgent extends Agent {
  isMinimized: boolean
  isMaximized: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
}

// Props
interface Props {
  runningAgents: RunningAgent[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  restore: [agentId: string]
  close: [agentId: string]
}>()

// 计算属性
const minimizedAgents = computed(() => {
  return props.runningAgents.filter(agent => agent.isMinimized)
})

// 方法
const restoreAgent = (agentId: string) => {
  emit('restore', agentId)
}

const closeAgent = (agentId: string) => {
  emit('close', agentId)
}
</script>

<style scoped>
/* 最小化任务栏 */
.minimized-taskbar {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 500px;
  overflow-x: auto;
  padding: 4px 0;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.minimized-taskbar::-webkit-scrollbar {
  height: 4px;
}

.minimized-taskbar::-webkit-scrollbar-track {
  background: transparent;
}

.minimized-taskbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.minimized-taskbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.minimized-item {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-width: 120px;
  max-width: 160px;
  flex-shrink: 0;
}

.minimized-item:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.minimized-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1a365d;
  font-size: 10px;
  flex-shrink: 0;
}

.minimized-title {
  font-size: 11px;
  font-weight: 500;
  color: #1a365d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  max-width: 80px;
}

.minimized-close {
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.8);
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  flex-shrink: 0;
}

.minimized-close:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .minimized-taskbar {
    max-width: 100%;
    gap: 6px;
  }

  .minimized-item {
    padding: 4px 8px;
    min-width: 100px;
    max-width: 140px;
  }

  .minimized-icon {
    width: 18px;
    height: 18px;
    font-size: 9px;
  }

  .minimized-title {
    font-size: 10px;
    max-width: 70px;
  }

  .minimized-close {
    width: 14px;
    height: 14px;
    font-size: 7px;
  }
}
</style>
