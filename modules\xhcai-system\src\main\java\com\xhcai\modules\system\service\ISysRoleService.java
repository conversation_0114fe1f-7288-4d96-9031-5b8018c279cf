package com.xhcai.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysRoleQueryDTO;
import com.xhcai.modules.system.entity.SysRole;
import com.xhcai.modules.system.vo.SysRoleVO;

import java.util.List;
import java.util.Set;

/**
 * 角色信息服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysRoleService extends IService<SysRole> {

    /**
     * 分页查询角色列表
     *
     * @param queryDTO 查询条件
     * @return 角色分页列表
     */
    PageResult<SysRoleVO> selectRolePage(SysRoleQueryDTO queryDTO);

    /**
     * 查询角色列表
     *
     * @param queryDTO 查询条件
     * @return 角色列表
     */
    List<SysRoleVO> selectRoleList(SysRoleQueryDTO queryDTO);

    /**
     * 根据角色ID查询角色信息
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    SysRoleVO selectRoleById(String roleId);

    /**
     * 根据角色编码查询角色信息
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    SysRole selectByRoleCode(String roleCode);

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean insertRole(SysRole role);

    /**
     * 更新角色信息
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean updateRole(SysRole role);

    /**
     * 删除角色
     *
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean deleteRoles(List<String> roleIds);

    /**
     * 启用角色
     *
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean enableRole(String roleId);

    /**
     * 停用角色
     *
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean disableRole(String roleId);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean existsRoleCode(String roleCode, String excludeId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean existsRoleName(String roleName, String excludeId);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRoleVO> selectRolesByUserId(String userId);

    /**
     * 根据用户ID查询角色实体列表（用于数据权限）
     *
     * @param userId 用户ID
     * @return 角色实体列表
     */
    List<SysRole> selectRoleEntitiesByUserId(String userId);

    /**
     * 根据用户ID查询角色编码集合
     *
     * @param userId 用户ID
     * @return 角色编码集合
     */
    Set<String> selectRoleCodesByUserId(String userId);

    /**
     * 为角色分配权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean assignPermissions(String roleId, List<String> permissionIds);

    /**
     * 查询角色已分配的权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<String> selectRolePermissionIds(String roleId);

    /**
     * 批量更新角色状态
     *
     * @param roleIds 角色ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> roleIds, String status);

    /**
     * 检查角色是否被用户使用
     *
     * @param roleId 角色ID
     * @return 是否被使用
     */
    boolean isRoleUsedByUsers(String roleId);

    /**
     * 获取所有可用角色（用于下拉选择）
     *
     * @return 角色列表
     */
    List<SysRoleVO> selectAllAvailableRoles();

    /**
     * 根据数据范围查询角色列表
     *
     * @param dataScope 数据范围
     * @return 角色列表
     */
    List<SysRoleVO> selectRolesByDataScope(String dataScope);

    /**
     * 复制角色权限
     *
     * @param sourceRoleId 源角色ID
     * @param targetRoleId 目标角色ID
     * @return 是否成功
     */
    boolean copyRolePermissions(String sourceRoleId, String targetRoleId);
}
