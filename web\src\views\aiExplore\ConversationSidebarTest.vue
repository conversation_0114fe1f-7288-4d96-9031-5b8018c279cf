<template>
  <div class="conversation-sidebar-test min-h-screen bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">对话侧边栏测试</h1>
      
      <!-- 测试说明 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-700 mb-4">测试说明</h2>
        <p class="text-gray-600 mb-4">
          这个页面用于测试对话侧边栏的"新对话"功能。当智能体没有查询到对话记录时，
          左侧对话记录会增加一个"新对话"的对话记录，记录ID为空，并设置为选中效果。
        </p>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="font-medium text-blue-800 mb-2">测试场景：</h3>
          <ul class="text-blue-700 space-y-1">
            <li>• 场景1：没有appId时，显示本地对话记录</li>
            <li>• 场景2：有appId但没有对话记录时，显示"新对话"</li>
            <li>• 场景3：有appId且有对话记录时，显示正常对话列表</li>
            <li>• <strong>新功能1</strong>：点击"新建对话"按钮，在对话记录中增加"新对话"项</li>
            <li>• <strong>新功能2</strong>：选中新对话时，不调用获取消息接口</li>
          </ul>
        </div>
      </div>

      <!-- 测试控制面板 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-700 mb-4">测试控制</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            @click="setTestScenario('no-app-id')"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-colors',
              currentScenario === 'no-app-id' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            场景1：无appId
          </button>
          <button
            @click="setTestScenario('empty-conversations')"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-colors',
              currentScenario === 'empty-conversations' 
                ? 'bg-green-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            场景2：空对话记录
          </button>
          <button
            @click="setTestScenario('with-conversations')"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-colors',
              currentScenario === 'with-conversations' 
                ? 'bg-purple-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            场景3：有对话记录
          </button>
        </div>
        <div class="mt-4 text-sm text-gray-600">
          当前场景：<span class="font-medium">{{ scenarioDescription }}</span>
        </div>
      </div>

      <!-- 测试区域 -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="flex h-96">
          <!-- 对话侧边栏 -->
          <ConversationSidebar
            :app-id="currentAppId"
            @hide-sidebar="handleHideSidebar"
            @conversation-selected="handleConversationSelected"
          />
          
          <!-- 右侧内容区域 -->
          <div class="flex-1 bg-gray-50 p-6 flex flex-col">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">选中的对话信息</h3>
            <div class="bg-white rounded-lg p-4 border border-gray-200 flex-1">
              <div v-if="selectedConversationId === null" class="text-gray-500 text-center py-8">
                尚未选择任何对话
              </div>
              <div v-else-if="selectedConversationId === ''" class="text-green-600 text-center py-8">
                <div class="text-2xl mb-2">✨</div>
                <div class="font-medium">默认新对话已选中</div>
                <div class="text-sm text-gray-500 mt-2">ID为空字符串，表示这是默认的新对话</div>
              </div>
              <div v-else-if="selectedConversationId.startsWith('new-')" class="text-green-600 text-center py-8">
                <div class="text-2xl mb-2">🆕</div>
                <div class="font-medium">手动创建的新对话已选中</div>
                <div class="text-sm text-gray-500 mt-2">ID: {{ selectedConversationId }}</div>
                <div class="text-xs text-gray-400 mt-1">这是通过点击"新建对话"按钮创建的</div>
              </div>
              <div v-else class="text-blue-600 text-center py-8">
                <div class="text-2xl mb-2">💬</div>
                <div class="font-medium">对话ID: {{ selectedConversationId }}</div>
                <div class="text-sm text-gray-500 mt-2">这是一个已存在的对话记录</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 事件日志 -->
      <div class="bg-white rounded-lg shadow-md p-6 mt-8">
        <h2 class="text-xl font-semibold text-gray-700 mb-4">事件日志</h2>
        <div class="bg-gray-50 rounded-lg p-4 h-32 overflow-y-auto">
          <div v-if="eventLogs.length === 0" class="text-gray-500 text-center py-4">
            暂无事件日志
          </div>
          <div v-for="(log, index) in eventLogs" :key="index" class="text-sm mb-2">
            <span class="text-gray-500">{{ log.timestamp }}</span>
            <span class="ml-2" :class="log.type === 'info' ? 'text-blue-600' : 'text-green-600'">
              {{ log.message }}
            </span>
          </div>
        </div>
        <button
          @click="clearLogs"
          class="mt-2 px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 transition-colors"
        >
          清空日志
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ConversationSidebar from '@/views/aiExplore/ConversationSidebar.vue'

// 测试状态
const currentScenario = ref<'no-app-id' | 'empty-conversations' | 'with-conversations'>('no-app-id')
const selectedConversationId = ref<string | null>(null)
const eventLogs = ref<Array<{ timestamp: string, type: string, message: string }>>([])

// 计算属性
const currentAppId = computed(() => {
  switch (currentScenario.value) {
    case 'no-app-id':
      return undefined
    case 'empty-conversations':
      return 'test-empty-app-id' // 这个appId不存在，会返回空对话记录
    case 'with-conversations':
      return 'test-app-id' // 这个appId可能有对话记录
    default:
      return undefined
  }
})

const scenarioDescription = computed(() => {
  switch (currentScenario.value) {
    case 'no-app-id':
      return '无appId，显示本地对话记录'
    case 'empty-conversations':
      return '有appId但无对话记录，应显示"新对话"'
    case 'with-conversations':
      return '有appId且可能有对话记录'
    default:
      return '未知场景'
  }
})

// 方法
const setTestScenario = (scenario: typeof currentScenario.value) => {
  currentScenario.value = scenario
  selectedConversationId.value = null
  addLog('info', `切换到测试场景：${scenarioDescription.value}`)
}

const handleHideSidebar = () => {
  addLog('info', '侧边栏隐藏事件触发')
}

const handleConversationSelected = (conversationId: string) => {
  selectedConversationId.value = conversationId
  if (conversationId === '') {
    addLog('success', '选中了默认"新对话"（ID为空字符串）')
  } else if (conversationId.startsWith('new-')) {
    addLog('success', `选中了手动创建的新对话：${conversationId}`)
  } else if (conversationId) {
    addLog('success', `选中了已存在的对话：${conversationId}`)
  } else {
    addLog('info', '清空了对话选择')
  }
}

const addLog = (type: string, message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  eventLogs.value.unshift({ timestamp, type, message })
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

const clearLogs = () => {
  eventLogs.value = []
}

// 初始化
addLog('info', '对话侧边栏测试页面已加载')
</script>

<style scoped>
/* 自定义样式 */
.conversation-sidebar-test {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>
