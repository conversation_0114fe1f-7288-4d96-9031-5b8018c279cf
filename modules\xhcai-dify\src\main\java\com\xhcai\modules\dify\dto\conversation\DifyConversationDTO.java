package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * Dify 会话信息 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify 会话信息")
public class DifyConversationDTO {

    @Schema(description = "会话ID")
    private String id;

    @Schema(description = "会话名称")
    private String name;

    @Schema(description = "输入参数")
    private Map<String, Object> inputs;

    @Schema(description = "会话状态")
    private String status;

    @Schema(description = "会话介绍")
    private String introduction;

    @Schema(description = "创建时间戳")
    @JsonProperty("created_at")
    private Long createdAt;

    @Schema(description = "更新时间戳")
    @JsonProperty("updated_at")
    private Long updatedAt;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DifyConversationDTO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", inputs=" + inputs +
                ", status='" + status + '\'' +
                ", introduction='" + introduction + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
