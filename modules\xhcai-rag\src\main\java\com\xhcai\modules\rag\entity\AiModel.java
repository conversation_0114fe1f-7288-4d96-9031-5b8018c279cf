package com.xhcai.modules.rag.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * AI模型配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "models")
@Schema(description = "AI模型配置")
@TableName("models")
public class AiModel extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    // ========== 基本信息 ==========
    /**
     * 模型名称
     */
    @Column(name = "name", length = 100, nullable = false)
    @Schema(description = "模型名称", example = "GPT-4")
    @NotBlank(message = "模型名称不能为空")
    @Size(min = 1, max = 100, message = "模型名称长度必须在1-100个字符之间")
    @TableField("name")
    private String name;

    /**
     * 模型标识
     */
    @Column(name = "model_id", length = 100, nullable = false)
    @Schema(description = "模型标识", example = "gpt-4-0613")
    @NotBlank(message = "模型标识不能为空")
    @Size(min = 1, max = 100, message = "模型标识长度必须在1-100个字符之间")
    @TableField("model_id")
    private String modelId;

    /**
     * 模型提供商
     */
    @Column(name = "provider", length = 50, nullable = false)
    @Schema(description = "模型提供商", example = "OpenAI")
    @NotBlank(message = "模型提供商不能为空")
    @Size(min = 1, max = 50, message = "模型提供商长度必须在1-50个字符之间")
    @TableField("provider")
    private String provider;

    /**
     * 模型类型
     */
    @Column(name = "type", length = 50, nullable = false)
    @Schema(description = "模型类型", example = "对话")
    @NotBlank(message = "模型类型不能为空")
    @Size(min = 1, max = 50, message = "模型类型长度必须在1-50个字符之间")
    @TableField("type")
    private String type;

    /**
     * 推理平台
     */
    @Column(name = "platform", length = 50)
    @Schema(description = "推理平台", example = "OpenAI")
    @Size(max = 50, message = "推理平台长度不能超过50个字符")
    @TableField("platform")
    private String platform;

    /**
     * 模型版本
     */
    @Column(name = "version", length = 50)
    @Schema(description = "模型版本", example = "v1.0")
    @Size(max = 50, message = "模型版本长度不能超过50个字符")
    @TableField("version")
    private String version;

    /**
     * 模型描述
     */
    @Column(name = "description", length = 500)
    @Schema(description = "模型描述")
    @Size(max = 500, message = "模型描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 状态：0-停用，1-启用
     */
    @Column(name = "status", length = 1, nullable = false)
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status = "1";

    // ========== 接口配置 ==========
    /**
     * API端点
     */
    @Column(name = "api_endpoint", length = 200)
    @Schema(description = "API端点", example = "https://api.openai.com/v1")
    @Size(max = 200, message = "API端点长度不能超过200个字符")
    @TableField("api_endpoint")
    private String apiEndpoint;

    /**
     * API密钥
     */
    @Column(name = "api_key", length = 500)
    @Schema(description = "API密钥")
    @Size(max = 500, message = "API密钥长度不能超过500个字符")
    @TableField("api_key")
    private String apiKey;

    /**
     * 组织ID
     */
    @Column(name = "organization_id", length = 100)
    @Schema(description = "组织ID", example = "org-...")
    @Size(max = 100, message = "组织ID长度不能超过100个字符")
    @TableField("organization_id")
    private String organizationId;

    /**
     * 请求超时时间（秒）
     */
    @Column(name = "timeout")
    @Schema(description = "请求超时时间（秒）", example = "30")
    @Min(value = 1, message = "超时时间必须大于0")
    @Max(value = 300, message = "超时时间不能超过300秒")
    @TableField("timeout")
    private Integer timeout = 30;

    // ========== 模型参数 ==========
    /**
     * 最大Token数
     */
    @Column(name = "max_tokens")
    @Schema(description = "最大Token数", example = "4096")
    @Min(value = 1, message = "最大Token数必须大于0")
    @TableField("max_tokens")
    private Integer maxTokens = 4096;

    /**
     * 温度值
     */
    @Column(name = "temperature", precision = 3, scale = 2)
    @Schema(description = "温度值", example = "0.7")
    @DecimalMin(value = "0.0", message = "温度值不能小于0")
    @DecimalMax(value = "2.0", message = "温度值不能大于2")
    @TableField("temperature")
    private BigDecimal temperature = new BigDecimal("0.7");

    /**
     * Top P值
     */
    @Column(name = "top_p", precision = 3, scale = 2)
    @Schema(description = "Top P值", example = "1.0")
    @DecimalMin(value = "0.0", message = "Top P值不能小于0")
    @DecimalMax(value = "1.0", message = "Top P值不能大于1")
    @TableField("top_p")
    private BigDecimal topP = new BigDecimal("1.0");

    /**
     * 频率惩罚
     */
    @Column(name = "frequency_penalty", precision = 3, scale = 2)
    @Schema(description = "频率惩罚", example = "0.0")
    @DecimalMin(value = "-2.0", message = "频率惩罚不能小于-2")
    @DecimalMax(value = "2.0", message = "频率惩罚不能大于2")
    @TableField("frequency_penalty")
    private BigDecimal frequencyPenalty = new BigDecimal("0.0");

    /**
     * 存在惩罚
     */
    @Column(name = "presence_penalty", precision = 3, scale = 2)
    @Schema(description = "存在惩罚", example = "0.0")
    @DecimalMin(value = "-2.0", message = "存在惩罚不能小于-2")
    @DecimalMax(value = "2.0", message = "存在惩罚不能大于2")
    @TableField("presence_penalty")
    private BigDecimal presencePenalty = new BigDecimal("0.0");

    /**
     * 停止序列
     */
    @Column(name = "stop_sequences", length = 200)
    @Schema(description = "停止序列", example = "\\n,\\n\\n")
    @Size(max = 200, message = "停止序列长度不能超过200个字符")
    @TableField("stop_sequences")
    private String stopSequences;

    // ========== 费用配置 ==========
    /**
     * 输入价格（$/1K tokens）
     */
    @Column(name = "input_price", precision = 10, scale = 6)
    @Schema(description = "输入价格（$/1K tokens）", example = "0.03")
    @DecimalMin(value = "0.0", message = "输入价格不能小于0")
    @TableField("input_price")
    private BigDecimal inputPrice = new BigDecimal("0.0");

    /**
     * 输出价格（$/1K tokens）
     */
    @Column(name = "output_price", precision = 10, scale = 6)
    @Schema(description = "输出价格（$/1K tokens）", example = "0.06")
    @DecimalMin(value = "0.0", message = "输出价格不能小于0")
    @TableField("output_price")
    private BigDecimal outputPrice = new BigDecimal("0.0");

    /**
     * 每分钟请求限制
     */
    @Column(name = "rpm_limit")
    @Schema(description = "每分钟请求限制", example = "3500")
    @Min(value = 1, message = "每分钟请求限制必须大于0")
    @TableField("rpm_limit")
    private Integer rpmLimit = 3500;

    /**
     * 每分钟Token限制
     */
    @Column(name = "tpm_limit")
    @Schema(description = "每分钟Token限制", example = "90000")
    @Min(value = 1, message = "每分钟Token限制必须大于0")
    @TableField("tpm_limit")
    private Integer tpmLimit = 90000;

    // ========== Getter and Setter ==========
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApiEndpoint() {
        return apiEndpoint;
    }

    public void setApiEndpoint(String apiEndpoint) {
        this.apiEndpoint = apiEndpoint;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public BigDecimal getTopP() {
        return topP;
    }

    public void setTopP(BigDecimal topP) {
        this.topP = topP;
    }

    public BigDecimal getFrequencyPenalty() {
        return frequencyPenalty;
    }

    public void setFrequencyPenalty(BigDecimal frequencyPenalty) {
        this.frequencyPenalty = frequencyPenalty;
    }

    public BigDecimal getPresencePenalty() {
        return presencePenalty;
    }

    public void setPresencePenalty(BigDecimal presencePenalty) {
        this.presencePenalty = presencePenalty;
    }

    public String getStopSequences() {
        return stopSequences;
    }

    public void setStopSequences(String stopSequences) {
        this.stopSequences = stopSequences;
    }

    public BigDecimal getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(BigDecimal inputPrice) {
        this.inputPrice = inputPrice;
    }

    public BigDecimal getOutputPrice() {
        return outputPrice;
    }

    public void setOutputPrice(BigDecimal outputPrice) {
        this.outputPrice = outputPrice;
    }

    public Integer getRpmLimit() {
        return rpmLimit;
    }

    public void setRpmLimit(Integer rpmLimit) {
        this.rpmLimit = rpmLimit;
    }

    public Integer getTpmLimit() {
        return tpmLimit;
    }

    public void setTpmLimit(Integer tpmLimit) {
        this.tpmLimit = tpmLimit;
    }

    @Override
    public String toString() {
        return "AiModel{"
                + "id=" + getId()
                + ", name='" + name + '\''
                + ", modelId='" + modelId + '\''
                + ", provider='" + provider + '\''
                + ", type='" + type + '\''
                + ", version='" + version + '\''
                + ", description='" + description + '\''
                + ", status='" + status + '\''
                + ", apiEndpoint='" + apiEndpoint + '\''
                + ", organizationId='" + organizationId + '\''
                + ", timeout=" + timeout
                + ", maxTokens=" + maxTokens
                + ", temperature=" + temperature
                + ", topP=" + topP
                + ", frequencyPenalty=" + frequencyPenalty
                + ", presencePenalty=" + presencePenalty
                + ", stopSequences='" + stopSequences + '\''
                + ", inputPrice=" + inputPrice
                + ", outputPrice=" + outputPrice
                + ", rpmLimit=" + rpmLimit
                + ", tpmLimit=" + tpmLimit
                + ", tenantId=" + getTenantId()
                + ", remark='" + getRemark() + '\''
                + ", createBy=" + getCreateBy()
                + ", createTime=" + getCreateTime()
                + ", updateBy=" + getUpdateBy()
                + ", updateTime=" + getUpdateTime()
                + '}';
    }
}
