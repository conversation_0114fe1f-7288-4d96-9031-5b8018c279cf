package com.xhcai.common.api.dto;

import com.xhcai.common.api.query.TimeRangeable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

/**
 * 分页+时间范围查询DTO基类
 * 组合分页查询和时间范围查询功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "分页+时间范围查询DTO基类")
public class PageTimeRangeQueryDTO extends PageQueryDTO implements TimeRangeable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "开始时间格式必须为：yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "结束时间格式必须为：yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @Override
    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    @Override
    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "PageTimeRangeQueryDTO{" +
                "current=" + getCurrent() +
                ", size=" + getSize() +
                ", orderBy='" + getOrderBy() + '\'' +
                ", orderDirection='" + getOrderDirection() + '\'' +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                '}';
    }
}
