<template>
  <div class="tenant-management bg-white rounded-lg shadow-sm p-6">
    <!-- 页面头部 -->
    <div class="tenants-header flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900">租户列表</h3>
      <button @click="showAddModal = true" class="btn-primary">
        <span class="mr-2">➕</span>
        添加租户
      </button>
    </div>

    <!-- 筛选组件 -->
    <TenantFilters />

    <!-- 表格组件 -->
    <TenantTable
      @view="handleViewTenant"
      @edit="handleEditTenant"
      @init="handleInitTenant"
    />

    <!-- 添加/编辑租户表单 -->
    <TenantForm
      v-model:visible="showAddModal"
      :tenant="null"
      @success="handleFormSuccess"
    />

    <TenantForm
      v-model:visible="showEditModal"
      :tenant="currentTenant"
      @success="handleFormSuccess"
    />

    <!-- 租户详情 -->
    <TenantDetail
      v-model:visible="showDetailModal"
      :tenant="currentTenant"
      @edit="handleEditFromDetail"
    />

    <!-- 模块初始化面板 -->
    <ModuleInitPanel 
      v-model:visible="showInitPanel"
      @initialized="handleModuleInitialized"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { SysTenantVO } from '@/types/system'

// 导入子组件
import TenantFilters from './TenantFilters.vue'
import TenantTable from './TenantTable.vue'
import TenantForm from './TenantForm.vue'
import TenantDetail from './TenantDetail.vue'
import ModuleInitPanel from './ModuleInitPanel.vue'

// 导入 composable
import { useTenantData } from './composables/useTenantData'

// 使用租户数据管理 composable
const { initData } = useTenantData()

// 响应式数据
const showAddModal = ref(false)
const showEditModal = ref(false)
const showDetailModal = ref(false)
const showInitPanel = ref(false)
const currentTenant = ref<SysTenantVO | null>(null)

// 方法

// 表格操作方法
const handleViewTenant = (tenant: SysTenantVO) => {
  currentTenant.value = tenant
  showDetailModal.value = true
}

const handleEditTenant = (tenant: SysTenantVO) => {
  currentTenant.value = tenant
  showEditModal.value = true
}

const handleEditFromDetail = (tenant: SysTenantVO) => {
  showDetailModal.value = false
  setTimeout(() => {
    currentTenant.value = tenant
    showEditModal.value = true
  }, 100)
}

const handleInitTenant = (_tenant: SysTenantVO) => {
  showInitPanel.value = true
}

// 表单成功处理
const handleFormSuccess = (message: string) => {
  ElMessage.success(message)
}

// 模块初始化相关方法
const handleModuleInitialized = (moduleId: string) => {
  console.log(`模块 ${moduleId} 初始化完成`)
  ElMessage.success(`模块 ${moduleId} 初始化完成`)
}

// 生命周期
onMounted(() => {
  initData()
})
</script>

<style scoped>
.tenant-management {
  min-height: 600px;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}
</style>
