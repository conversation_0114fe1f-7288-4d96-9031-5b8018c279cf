#!/bin/bash

# Admin API 构建脚本
# 用于构建包含所有依赖的可执行 JAR 文件

echo "🚀 开始构建 Admin API..."

# 清理之前的构建
echo "🧹 清理之前的构建..."
mvn clean

# 编译和打包
echo "📦 编译和打包..."
mvn package -DskipTests

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    
    # 显示生成的 JAR 文件信息
    echo ""
    echo "📋 构建结果："
    ls -lh target/*.jar
    
    echo ""
    echo "🔍 JAR 文件详细信息："
    for jar in target/*.jar; do
        if [ -f "$jar" ]; then
            echo "文件: $jar"
            echo "大小: $(du -h "$jar" | cut -f1)"
            echo "类型: $(file "$jar")"
            echo "主类: $(unzip -p "$jar" META-INF/MANIFEST.MF | grep Main-Class || echo '未找到主类')"
            echo "---"
        fi
    done
    
    # 检查是否包含依赖
    echo ""
    echo "🔍 检查是否包含依赖库..."
    main_jar=$(ls target/*-SNAPSHOT.jar 2>/dev/null | head -1)
    if [ -f "$main_jar" ]; then
        echo "检查 JAR 文件: $main_jar"
        jar_size=$(stat -f%z "$main_jar" 2>/dev/null || stat -c%s "$main_jar" 2>/dev/null)
        if [ "$jar_size" -gt 50000000 ]; then  # 50MB
            echo "✅ JAR 文件大小 $(du -h "$main_jar" | cut -f1)，看起来包含了依赖"
        else
            echo "⚠️  JAR 文件大小 $(du -h "$main_jar" | cut -f1)，可能没有包含所有依赖"
        fi
        
        # 检查是否包含 Spring Boot 类
        if unzip -l "$main_jar" | grep -q "org/springframework/boot"; then
            echo "✅ 包含 Spring Boot 依赖"
        else
            echo "❌ 未包含 Spring Boot 依赖"
        fi
        
        # 检查是否包含项目模块
        if unzip -l "$main_jar" | grep -q "com/xhcai"; then
            echo "✅ 包含项目代码"
        else
            echo "❌ 未包含项目代码"
        fi
    fi
    
    echo ""
    echo "🎉 构建完成！"
    echo "💡 运行命令: java -jar target/admin-api-1.0.0.jar"
    
else
    echo "❌ 构建失败！"
    exit 1
fi
