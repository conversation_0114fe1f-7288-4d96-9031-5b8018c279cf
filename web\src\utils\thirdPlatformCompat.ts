/**
 * 第三方库兼容性检查和处理
 * 检查TipTap、Pinia、Vue Router等第三方库的浏览器支持情况
 */

/**
 * 第三方库兼容性信息
 */
export interface ThirdPlatformCompatibility {
  tiptap: {
    supported: boolean
    version: string
    issues: string[]
  }
  pinia: {
    supported: boolean
    version: string
    issues: string[]
  }
  vueRouter: {
    supported: boolean
    version: string
    issues: string[]
  }
  prosemirror: {
    supported: boolean
    issues: string[]
  }
}

/**
 * 检查TipTap编辑器兼容性
 */
export function checkTipTapCompatibility(): {
  supported: boolean
  version: string
  issues: string[]
} {
  const issues: string[] = []
  let supported = true

  try {
    // 检查必要的DOM API
    if (!document.createElement) {
      issues.push('document.createElement not supported')
      supported = false
    }

    if (!document.createRange) {
      issues.push('document.createRange not supported (required for text selection)')
      supported = false
    }

    if (!window.getSelection) {
      issues.push('window.getSelection not supported (required for text selection)')
      supported = false
    }

    // 检查MutationObserver支持
    if (!window.MutationObserver) {
      issues.push('MutationObserver not supported (required for content change detection)')
      supported = false
    }

    // 检查contentEditable支持
    const testDiv = document.createElement('div')
    if (typeof testDiv.contentEditable === 'undefined') {
      issues.push('contentEditable not supported')
      supported = false
    }

    // 检查execCommand支持（虽然已废弃，但TipTap可能需要）
    if (!document.execCommand) {
      issues.push('document.execCommand not supported (may affect some formatting features)')
    }

    // 检查ClipboardAPI支持
    if (!navigator.clipboard) {
      issues.push('Clipboard API not supported (copy/paste functionality may be limited)')
    }

    // 检查DragEvent支持
    if (!window.DragEvent) {
      issues.push('DragEvent not supported (drag and drop functionality may not work)')
    }

  } catch (error) {
    issues.push(`TipTap compatibility check failed: ${error}`)
    supported = false
  }

  return {
    supported,
    version: '2.23.0', // 当前使用的版本
    issues
  }
}

/**
 * 检查Pinia状态管理兼容性
 */
export function checkPiniaCompatibility(): {
  supported: boolean
  version: string
  issues: string[]
} {
  const issues: string[] = []
  let supported = true

  try {
    // 检查Proxy支持（Pinia依赖Vue3的响应式系统）
    if (!window.Proxy) {
      issues.push('Proxy not supported (required for reactive state)')
      supported = false
    }

    // 检查WeakMap支持
    if (!window.WeakMap) {
      issues.push('WeakMap not supported (may affect memory management)')
      supported = false
    }

    // 检查Symbol支持
    if (!window.Symbol) {
      issues.push('Symbol not supported (may affect internal implementation)')
      supported = false
    }

    // 检查Map支持
    if (!window.Map) {
      issues.push('Map not supported (may affect store management)')
    }

    // 检查Set支持
    if (!window.Set) {
      issues.push('Set not supported (may affect store management)')
    }

    // 检查JSON支持
    if (!window.JSON || !JSON.stringify || !JSON.parse) {
      issues.push('JSON not fully supported (required for state serialization)')
      supported = false
    }

  } catch (error) {
    issues.push(`Pinia compatibility check failed: ${error}`)
    supported = false
  }

  return {
    supported,
    version: '2.1.7', // 当前使用的版本
    issues
  }
}

/**
 * 检查Vue Router兼容性
 */
export function checkVueRouterCompatibility(): {
  supported: boolean
  version: string
  issues: string[]
} {
  const issues: string[] = []
  let supported = true

  try {
    // 检查History API支持
    if (!window.history || !window.history.pushState) {
      issues.push('History API not supported (will fallback to hash mode)')
    }

    // 检查URL API支持
    if (!window.URL) {
      issues.push('URL API not supported (may affect route parsing)')
    }

    // 检查Location API支持
    if (!window.location) {
      issues.push('Location API not supported')
      supported = false
    }

    // 检查addEventListener支持
    if (!window.addEventListener) {
      issues.push('addEventListener not supported (required for navigation events)')
      supported = false
    }

    // 检查popstate事件支持
    try {
      const testEvent = new PopStateEvent('popstate')
      if (!testEvent) {
        issues.push('PopStateEvent not supported (may affect browser navigation)')
      }
    } catch {
      issues.push('PopStateEvent constructor not supported')
    }

    // 检查Promise支持（路由守卫需要）
    if (!window.Promise) {
      issues.push('Promise not supported (required for navigation guards)')
      supported = false
    }

  } catch (error) {
    issues.push(`Vue Router compatibility check failed: ${error}`)
    supported = false
  }

  return {
    supported,
    version: '4.2.5', // 当前使用的版本
    issues
  }
}

/**
 * 检查ProseMirror兼容性（TipTap的底层依赖）
 */
export function checkProseMirrorCompatibility(): {
  supported: boolean
  issues: string[]
} {
  const issues: string[] = []
  let supported = true

  try {
    // 检查DOM Range API
    if (!document.createRange) {
      issues.push('Range API not supported')
      supported = false
    }

    // 检查Selection API
    if (!window.getSelection) {
      issues.push('Selection API not supported')
      supported = false
    }

    // 检查Node API
    if (!window.Node || !Node.ELEMENT_NODE) {
      issues.push('Node API not fully supported')
      supported = false
    }

    // 检查DocumentFragment支持
    if (!document.createDocumentFragment) {
      issues.push('DocumentFragment not supported')
      supported = false
    }

    // 检查TreeWalker支持
    if (!document.createTreeWalker) {
      issues.push('TreeWalker not supported (may affect node traversal)')
    }

    // 检查KeyboardEvent支持
    if (!window.KeyboardEvent) {
      issues.push('KeyboardEvent not supported (keyboard shortcuts may not work)')
    }

    // 检查InputEvent支持
    if (!window.InputEvent) {
      issues.push('InputEvent not supported (input handling may be limited)')
    }

  } catch (error) {
    issues.push(`ProseMirror compatibility check failed: ${error}`)
    supported = false
  }

  return {
    supported,
    issues
  }
}

/**
 * 获取完整的第三方库兼容性报告
 */
export function getThirdPlatformCompatibility(): ThirdPlatformCompatibility {
  return {
    tiptap: checkTipTapCompatibility(),
    pinia: checkPiniaCompatibility(),
    vueRouter: checkVueRouterCompatibility(),
    prosemirror: checkProseMirrorCompatibility()
  }
}

/**
 * 为不兼容的第三方库提供降级方案
 */
export function setupThirdPlatformFallbacks(compatibility: ThirdPlatformCompatibility): void {
  // TipTap降级方案
  if (!compatibility.tiptap.supported) {
    console.warn('TipTap not fully supported, setting up fallbacks...')
    
    // 如果不支持MutationObserver，使用轮询检测内容变化
    if (!window.MutationObserver) {
      (window as any).MutationObserver = class MutationObserverFallback {
        private callback: MutationCallback
        private target: Node | null = null
        private interval: number | null = null
        private lastContent: string = ''

        constructor(callback: MutationCallback) {
          this.callback = callback
        }

        observe(target: Node): void {
          this.target = target
          this.lastContent = (target as Element).innerHTML || ''

          this.interval = window.setInterval(() => {
            if (this.target) {
              const currentContent = (this.target as Element).innerHTML || ''
              if (currentContent !== this.lastContent) {
                this.callback([], this as any)
                this.lastContent = currentContent
              }
            }
          }, 100)
        }

        disconnect(): void {
          if (this.interval) {
            clearInterval(this.interval)
            this.interval = null
          }
        }

        takeRecords(): MutationRecord[] {
          // 降级实现：返回空数组，因为我们使用轮询而不是真正的变化记录
          return []
        }
      }
    }
  }

  // Pinia降级方案
  if (!compatibility.pinia.supported) {
    console.warn('Pinia not fully supported, consider using Vuex as fallback')
  }

  // Vue Router降级方案
  if (!compatibility.vueRouter.supported) {
    console.warn('Vue Router not fully supported, some navigation features may not work')
  }

  // ProseMirror降级方案
  if (!compatibility.prosemirror.supported) {
    console.warn('ProseMirror not fully supported, rich text editing may be limited')
  }
}

/**
 * 初始化第三方库兼容性检查
 */
export function initThirdPlatformCompatibility(): void {
  const compatibility = getThirdPlatformCompatibility()
  
  // 设置降级方案
  setupThirdPlatformFallbacks(compatibility)
  
  // 输出兼容性报告
  console.log('third-platform library compatibility:', compatibility)
  
  // 检查是否有严重的兼容性问题
  const criticalIssues = [
    ...compatibility.tiptap.issues,
    ...compatibility.pinia.issues,
    ...compatibility.vueRouter.issues,
    ...compatibility.prosemirror.issues
  ].filter(issue => issue.includes('not supported'))
  
  if (criticalIssues.length > 0) {
    console.warn('Critical compatibility issues detected:', criticalIssues)
  }
}
