package com.xhcai.common.security.annotation;

import java.lang.annotation.*;

/**
 * 角色校验注解
 * 用于方法级别的角色控制
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresRoles {

    /**
     * 需要的角色标识
     * 支持多个角色，默认为AND关系
     */
    String[] value() default {};

    /**
     * 角色关系类型
     * AND: 需要拥有所有角色
     * OR: 需要拥有任意一个角色
     */
    Logical logical() default Logical.AND;

    /**
     * 角色校验失败时的提示信息
     */
    String message() default "角色权限不足";

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 且关系
         */
        AND,
        /**
         * 或关系
         */
        OR
    }
}
