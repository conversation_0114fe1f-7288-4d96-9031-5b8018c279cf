package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify 会话名称响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 会话名称响应")
public class DifyConversationNameResponseDTO {

    /**
     * 会话ID
     */
    @JsonProperty("id")
    @Schema(description = "会话ID", example = "conv_123456")
    private String id;

    /**
     * 会话名称
     */
    @JsonProperty("name")
    @Schema(description = "会话名称", example = "关于AI的讨论")
    private String name;

    /**
     * 会话状态
     */
    @JsonProperty("status")
    @Schema(description = "会话状态", example = "normal")
    private String status;

    /**
     * 会话介绍
     */
    @JsonProperty("introduction")
    @Schema(description = "会话介绍")
    private String introduction;

    /**
     * 创建时间戳
     */
    @JsonProperty("created_at")
    @Schema(description = "创建时间戳")
    private Long createdAt;

    /**
     * 更新时间戳
     */
    @JsonProperty("updated_at")
    @Schema(description = "更新时间戳")
    private Long updatedAt;
}
