<template>
  <div class="rich-text-editor h-full flex flex-col">
    <!-- 工具栏 -->
    <div class="editor-toolbar border border-gray-300 rounded-t-lg bg-gray-50 p-2 flex flex-wrap gap-1 flex-shrink-0">
      <!-- 文本格式 -->
      <div class="toolbar-group flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          @click="editor?.chain().focus().toggleBold().run()"
          :class="['toolbar-btn', { active: editor?.isActive('bold') }]"
          title="粗体"
        >
          <strong>B</strong>
        </button>
        <button
          @click="editor?.chain().focus().toggleItalic().run()"
          :class="['toolbar-btn', { active: editor?.isActive('italic') }]"
          title="斜体"
        >
          <em>I</em>
        </button>
        <button
          @click="editor?.chain().focus().toggleStrike().run()"
          :class="['toolbar-btn', { active: editor?.isActive('strike') }]"
          title="删除线"
        >
          <s>S</s>
        </button>
      </div>

      <!-- 标题 -->
      <div class="toolbar-group flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
          :class="['toolbar-btn', { active: editor?.isActive('heading', { level: 1 }) }]"
          title="标题1"
        >
          H1
        </button>
        <button
          @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
          :class="['toolbar-btn', { active: editor?.isActive('heading', { level: 2 }) }]"
          title="标题2"
        >
          H2
        </button>
        <button
          @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()"
          :class="['toolbar-btn', { active: editor?.isActive('heading', { level: 3 }) }]"
          title="标题3"
        >
          H3
        </button>
      </div>

      <!-- 对齐方式 -->
      <div class="toolbar-group flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          @click="editor?.chain().focus().setTextAlign('left').run()"
          :class="['toolbar-btn', { active: editor?.isActive({ textAlign: 'left' }) }]"
          title="左对齐"
        >
          ≡
        </button>
        <button
          @click="editor?.chain().focus().setTextAlign('center').run()"
          :class="['toolbar-btn', { active: editor?.isActive({ textAlign: 'center' }) }]"
          title="居中对齐"
        >
          ≣
        </button>
        <button
          @click="editor?.chain().focus().setTextAlign('right').run()"
          :class="['toolbar-btn', { active: editor?.isActive({ textAlign: 'right' }) }]"
          title="右对齐"
        >
          ≡
        </button>
      </div>

      <!-- 列表 -->
      <div class="toolbar-group flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          @click="editor?.chain().focus().toggleBulletList().run()"
          :class="['toolbar-btn', { active: editor?.isActive('bulletList') }]"
          title="无序列表"
        >
          •
        </button>
        <button
          @click="editor?.chain().focus().toggleOrderedList().run()"
          :class="['toolbar-btn', { active: editor?.isActive('orderedList') }]"
          title="有序列表"
        >
          1.
        </button>
      </div>

      <!-- 代码和特殊格式 -->
      <div class="toolbar-group flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          @click="editor?.chain().focus().toggleCode().run()"
          :class="['toolbar-btn', { active: editor?.isActive('code') }]"
          title="行内代码"
        >
          <i class="fas fa-code"></i>
        </button>
        <button
          @click="editor?.chain().focus().toggleCodeBlock().run()"
          :class="['toolbar-btn', { active: editor?.isActive('codeBlock') }]"
          title="代码块"
        >
          <i class="fas fa-file-code"></i>
        </button>
        <button
          @click="insertApiTemplate"
          class="toolbar-btn"
          title="插入API参数模板"
        >
          <i class="fas fa-cogs"></i>
        </button>
        <button
          @click="editor?.chain().focus().setHorizontalRule().run()"
          class="toolbar-btn"
          title="分隔线"
        >
          <i class="fas fa-minus"></i>
        </button>
      </div>

      <!-- 文档导入 -->
      <div class="toolbar-group flex gap-1 border-r border-gray-300 pr-2 mr-2">
        <button
          @click="showImportDialog = true"
          class="toolbar-btn import-btn"
          title="导入文档 (支持 Markdown, Word, PDF, 文本)"
        >
          <i class="fas fa-file-import"></i>
        </button>
      </div>

      <!-- 其他功能 -->
      <div class="toolbar-group flex gap-1">
        <button
          @click="editor?.chain().focus().clearNodes().unsetAllMarks().run()"
          class="toolbar-btn"
          title="清除格式"
        >
          清除
        </button>
        <button
          @click="editor?.chain().focus().undo().run()"
          :disabled="!editor?.can().undo()"
          class="toolbar-btn"
          title="撤销"
        >
          ↶
        </button>
        <button
          @click="editor?.chain().focus().redo().run()"
          :disabled="!editor?.can().redo()"
          class="toolbar-btn"
          title="重做"
        >
          ↷
        </button>
      </div>
    </div>

    <!-- 编辑器内容区域 -->
    <EditorContent
      :editor="editor as any"
      class="editor-content border border-t-0 border-gray-300 rounded-b-lg flex-1 overflow-y-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
    />

    <!-- 文档导入对话框 -->
    <DocumentImportDialog
      :visible="showImportDialog"
      @close="showImportDialog = false"
      @imported="handleDocumentImported"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import { createLowlight } from 'lowlight'
import DocumentImportDialog from './DocumentImportDialog.vue'

// 创建lowlight实例
const lowlight = createLowlight()

// 注册常用语言（可选，如果需要语法高亮）
// import javascript from 'highlight.js/lib/languages/javascript'
// import typescript from 'highlight.js/lib/languages/typescript'
// import css from 'highlight.js/lib/languages/css'
// import html from 'highlight.js/lib/languages/xml'
// lowlight.register('javascript', javascript)
// lowlight.register('typescript', typescript)
// lowlight.register('css', css)
// lowlight.register('html', html)

interface Props {
  modelValue: string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'file-imported', data: { content: string, files: string[] }): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入内容...'
})

const emit = defineEmits<Emits>()

// 导入对话框状态
const showImportDialog = ref(false)

// 创建编辑器实例
const editor = ref<Editor | null>(null)

// 初始化编辑器
editor.value = new Editor({
  content: props.modelValue,
  extensions: [
    StarterKit.configure({
      codeBlock: false, // 禁用默认的代码块，使用自定义的
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    TextStyle,
    Color.configure({
      types: ['textStyle'],
    }),
    CodeBlockLowlight.configure({
      lowlight,
      defaultLanguage: 'javascript',
    }),
  ],
  editorProps: {
    attributes: {
      class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none h-full p-4',
      placeholder: props.placeholder,
    },
  },
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    emit('update:modelValue', html)
  },
})

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHTML()) {
    editor.value.commands.setContent(newValue || '', false)
  }
})

// 文档导入处理方法
const handleDocumentImported = (data: { content: string, files: string[] }) => {
  if (!editor.value || !data.content) return

  // 获取当前内容
  const currentContent = editor.value.getHTML()

  // 决定如何插入新内容
  let newContent = ''
  if (currentContent && currentContent !== '<p></p>' && currentContent.trim() !== '') {
    // 如果已有内容，在后面追加
    newContent = currentContent + '<br><br>' + data.content
  } else {
    // 如果没有内容，直接设置
    newContent = data.content
  }

  // 更新编辑器内容
  editor.value.commands.setContent(newContent)
  emit('update:modelValue', newContent)

  // 触发导入事件
  emit('file-imported', data)
}

// 插入API参数模板
const insertApiTemplate = () => {
  if (!editor.value) return

  const apiTemplate = `
<div class="api-template" style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin: 16px 0; background: #f9fafb;">
  <h4 style="margin: 0 0 12px 0; color: #374151; font-weight: 600;">API接口文档</h4>

  <div style="margin-bottom: 12px;">
    <strong style="color: #1f2937;">接口地址：</strong>
    <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 4px; font-family: 'Courier New', monospace;">POST /api/example</code>
  </div>

  <div style="margin-bottom: 12px;">
    <strong style="color: #1f2937;">请求参数：</strong>
  </div>

  <table style="width: 100%; border-collapse: collapse; margin-bottom: 12px;">
    <thead>
      <tr style="background: #f3f4f6;">
        <th style="border: 1px solid #d1d5db; padding: 8px; text-align: left;">参数名</th>
        <th style="border: 1px solid #d1d5db; padding: 8px; text-align: left;">类型</th>
        <th style="border: 1px solid #d1d5db; padding: 8px; text-align: left;">必填</th>
        <th style="border: 1px solid #d1d5db; padding: 8px; text-align: left;">说明</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td style="border: 1px solid #d1d5db; padding: 8px;"><code>id</code></td>
        <td style="border: 1px solid #d1d5db; padding: 8px;">string</td>
        <td style="border: 1px solid #d1d5db; padding: 8px;">是</td>
        <td style="border: 1px solid #d1d5db; padding: 8px;">用户ID</td>
      </tr>
      <tr>
        <td style="border: 1px solid #d1d5db; padding: 8px;"><code>name</code></td>
        <td style="border: 1px solid #d1d5db; padding: 8px;">string</td>
        <td style="border: 1px solid #d1d5db; padding: 8px;">否</td>
        <td style="border: 1px solid #d1d5db; padding: 8px;">用户名称</td>
      </tr>
    </tbody>
  </table>

  <div style="margin-bottom: 12px;">
    <strong style="color: #1f2937;">请求示例：</strong>
  </div>

  <pre style="background: #1f2937; color: #f9fafb; padding: 12px; border-radius: 6px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 14px;"><code>{
  "id": "12345",
  "name": "张三"
}</code></pre>

  <div style="margin-bottom: 12px;">
    <strong style="color: #1f2937;">响应示例：</strong>
  </div>

  <pre style="background: #1f2937; color: #f9fafb; padding: 12px; border-radius: 6px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 14px;"><code>{
  "code": 200,
  "message": "success",
  "data": {
    "id": "12345",
    "name": "张三",
    "created_at": "2024-01-01T00:00:00Z"
  }
}</code></pre>
</div>
`

  editor.value.commands.insertContent(apiTemplate)
}

// 组件卸载前销毁编辑器
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})
</script>

<style scoped>
.rich-text-editor {
  border-radius: 8px;
  overflow: hidden;
}

.toolbar-btn {
  @apply px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-100 hover:text-gray-800 transition-all duration-200;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn.active {
  @apply bg-primary-500 text-white border-primary-500;
}

.toolbar-btn:hover {
  @apply bg-gray-100;
}

.toolbar-btn.active:hover {
  @apply bg-primary-600;
}

.toolbar-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.toolbar-btn.import-btn {
  @apply bg-blue-50 text-blue-600 border-blue-200;
  position: relative;
}

.toolbar-btn.import-btn:hover {
  @apply bg-blue-100 text-blue-700 border-blue-300;
}

.toolbar-btn.import-btn::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 6px;
  height: 6px;
  background: #10b981;
  border-radius: 50%;
  border: 1px solid white;
}

.editor-content {
  line-height: 1.6;
}

/* Tiptap编辑器内容样式 */
.editor-content :deep(.ProseMirror) {
  outline: none;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.editor-content :deep(.ProseMirror p) {
  margin-bottom: 8px;
}

.editor-content :deep(.ProseMirror h1) {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 12px;
  margin-top: 16px;
}

.editor-content :deep(.ProseMirror h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  margin-top: 14px;
}

.editor-content :deep(.ProseMirror h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 8px;
  margin-top: 12px;
}

.editor-content :deep(.ProseMirror ul),
.editor-content :deep(.ProseMirror ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.editor-content :deep(.ProseMirror li) {
  margin-bottom: 4px;
}

.editor-content :deep(.ProseMirror strong) {
  font-weight: 600;
}

.editor-content :deep(.ProseMirror em) {
  font-style: italic;
}

.editor-content :deep(.ProseMirror s) {
  text-decoration: line-through;
}

.editor-content :deep(.ProseMirror[data-placeholder]:before) {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
  height: 0;
  float: left;
}

.editor-content :deep(.ProseMirror.ProseMirror-focused) {
  outline: none;
}

/* 文本对齐样式 */
.editor-content :deep(.ProseMirror [data-text-align="left"]) {
  text-align: left;
}

.editor-content :deep(.ProseMirror [data-text-align="center"]) {
  text-align: center;
}

.editor-content :deep(.ProseMirror [data-text-align="right"]) {
  text-align: right;
}

.editor-content :deep(.ProseMirror [data-text-align="justify"]) {
  text-align: justify;
}

/* 代码样式 */
.editor-content :deep(.ProseMirror code) {
  background: #f3f4f6;
  color: #1f2937;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.9em;
}

.editor-content :deep(.ProseMirror pre) {
  background: #1f2937;
  color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 16px 0;
}

.editor-content :deep(.ProseMirror pre code) {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

/* 代码块语言标签 */
.editor-content :deep(.ProseMirror .hljs) {
  background: #1f2937 !important;
  color: #f9fafb !important;
}

/* API模板样式 */
.editor-content :deep(.api-template) {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  background: #f9fafb;
}

.editor-content :deep(.api-template h4) {
  margin: 0 0 12px 0;
  color: #374151;
  font-weight: 600;
}

.editor-content :deep(.api-template table) {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
}

.editor-content :deep(.api-template th),
.editor-content :deep(.api-template td) {
  border: 1px solid #d1d5db;
  padding: 8px;
  text-align: left;
}

.editor-content :deep(.api-template th) {
  background: #f3f4f6;
  font-weight: 600;
}

/* 分隔线样式 */
.editor-content :deep(.ProseMirror hr) {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 24px 0;
}
</style>
