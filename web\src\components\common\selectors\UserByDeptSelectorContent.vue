<template>
  <div class="user-by-dept-selector-content">
    <!-- 选择器头部 -->
    <div v-if="showHeader" class="selector-header flex items-center justify-between mb-3">
      <div class="flex items-center gap-2">
        <span class="text-sm font-medium text-gray-700">按部门选择用户</span>
        <span v-if="hasSelection" class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
          已选择 {{ selectedCount }} 个用户
        </span>
      </div>
      <div class="flex items-center gap-2">
        <el-button 
          v-if="config.multiple" 
          @click="selectAllUsers" 
          size="small" 
          type="primary" 
          plain
          :disabled="userLoading || !currentDeptUsers.length"
        >
          全选当前部门
        </el-button>
        <el-button 
          @click="clearSelection" 
          size="small" 
          :disabled="!hasSelection"
        >
          清空
        </el-button>
      </div>
    </div>

    <div class="selector-content grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- 左侧：部门树 -->
      <div class="dept-panel">
        <div class="panel-header flex items-center justify-between mb-2">
          <h4 class="text-sm font-medium text-gray-700">选择部门</h4>
          <span v-if="selectedDept" class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
            {{ selectedDept.label }}
          </span>
        </div>
        
        <DeptTreeSelectorContent
          v-model="selectedDeptId"
          :config="deptSelectorConfig"
          :show-header="false"
          :show-selected-tags="false"
          @change="handleDeptChange"
        />
      </div>

      <!-- 右侧：用户列表 -->
      <div class="user-panel">
        <div class="panel-header flex items-center justify-between mb-2">
          <h4 class="text-sm font-medium text-gray-700">
            选择用户
            <span v-if="selectedDept" class="text-xs text-gray-500">
              ({{ selectedDept.label }})
            </span>
          </h4>
          <span v-if="currentDeptUsers.length" class="text-xs text-gray-500">
            共 {{ currentDeptUsers.length }} 人
          </span>
        </div>

        <!-- 用户搜索 -->
        <div class="search-box mb-3">
          <el-input
            v-model="userFilterText"
            placeholder="搜索用户姓名、用户名..."
            :prefix-icon="Search"
            clearable
            size="small"
            :disabled="!selectedDeptId || userLoading"
          />
        </div>

        <!-- 用户列表 -->
        <div class="user-list border border-gray-200 rounded-lg bg-white min-h-[300px] max-h-[400px] overflow-auto">
          <div v-if="!selectedDeptId" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-user text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">请先选择部门</p>
          </div>

          <div v-else-if="userLoading" class="loading-state text-center py-8">
            <div class="loading-spinner">
              <i class="el-icon-loading animate-spin"></i>
            </div>
            <p class="text-gray-500 text-sm mt-2">加载用户中...</p>
          </div>

          <div v-else-if="!filteredUsers.length" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-user text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">
              {{ userFilterText ? '未找到匹配的用户' : '该部门暂无用户' }}
            </p>
          </div>

          <div v-else class="user-items p-2">
            <div
              v-for="user in filteredUsers"
              :key="user.value"
              class="user-item flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
              :class="{
                'bg-blue-50 border-blue-200': isUserSelected(user.value),
                'opacity-50 cursor-not-allowed': user.disabled
              }"
              @click="handleUserClick(user)"
            >
              <el-checkbox
                v-if="config.multiple"
                :model-value="isUserSelected(user.value)"
                :disabled="user.disabled"
                @change="(checked) => handleUserCheck(user, !!checked)"
                class="mr-3"
              />
              
              <div class="user-avatar mr-3">
                <el-avatar :size="32" :src="user.avatar">
                  <span class="text-sm">{{ user.nickname?.[0] || user.username[0] }}</span>
                </el-avatar>
              </div>
              
              <div class="user-info flex-1 min-w-0">
                <div class="user-name text-sm font-medium text-gray-900 truncate">
                  {{ user.nickname || user.username }}
                </div>
                <div class="user-details text-xs text-gray-500 truncate">
                  {{ user.username }}
                  <span v-if="user.email" class="ml-2">{{ user.email }}</span>
                </div>
              </div>
              
              <div class="user-status">
                <el-tag
                  :type="user.status === '0' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ user.status === '0' ? '正常' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 已选择的用户标签 -->
    <div v-if="hasSelection && config.multiple && showSelectedTags" class="selected-tags mt-4">
      <div class="text-xs text-gray-600 mb-2">已选择的用户:</div>
      <div class="flex flex-wrap gap-1">
        <el-tag
          v-for="user in getSelectedUserOptions()"
          :key="user.value"
          :closable="!config.disabled"
          size="small"
          @close="removeUserSelection(user.value)"
        >
          <div class="flex items-center">
            <el-avatar :size="16" :src="user.avatar" class="mr-1">
              <span class="text-xs">{{ user.nickname?.[0] || user.username[0] }}</span>
            </el-avatar>
            {{ user.nickname || user.username }}
          </div>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElInput, ElButton, ElTag, ElCheckbox, ElAvatar } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { UserAPI } from '@/api/system'
import DeptTreeSelectorContent from './DeptTreeSelectorContent.vue'
import type { UserSelectorOption, DeptSelectorOption, SelectorConfig } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludeUserIds?: string[]
  showHeader?: boolean
  showSelectedTags?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], options: UserSelectorOption[]): void
  (e: 'select', value: string, option: UserSelectorOption): void
  (e: 'remove', value: string): void
  (e: 'deptChange', deptId: string, dept: DeptSelectorOption): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludeUserIds: () => [],
  showHeader: false,
  showSelectedTags: true
})

const emit = defineEmits<Emits>()

// 响应式数据 - 确保每个组件实例都有独立的数据
const selectedDeptId = ref<string>('')
const selectedDept = ref<DeptSelectorOption | null>(null)
const selectedUserIds = ref<string | string[]>(
  Array.isArray(props.modelValue) ? [...props.modelValue] : props.modelValue || (props.config.multiple ? [] : '')
)
const currentDeptUsers = ref<UserSelectorOption[]>([])
const userFilterText = ref('')
const userLoading = ref(false)
const allLoadedUsers = ref<UserSelectorOption[]>([]) // 缓存所有加载过的用户

// 配置
const defaultConfig: SelectorConfig = {
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择用户',
  size: 'default',
  disabled: false
}

const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

const deptSelectorConfig = computed(() => ({
  multiple: false,
  checkStrictly: true,
  showCheckbox: false,
  expandOnClickNode: true,
  defaultExpandAll: false
}))

// 计算属性
const filteredUsers = computed(() => {
  if (!userFilterText.value) return currentDeptUsers.value
  
  const filterText = userFilterText.value.toLowerCase()
  return currentDeptUsers.value.filter(user => 
    user.label.toLowerCase().includes(filterText) ||
    user.username.toLowerCase().includes(filterText) ||
    (user.email && user.email.toLowerCase().includes(filterText))
  )
})

const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedUserIds.value) && selectedUserIds.value.length > 0
  }
  return selectedUserIds.value !== '' && selectedUserIds.value !== null && selectedUserIds.value !== undefined
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

// 方法
const loadDeptUsers = async (deptId: string) => {
  if (!deptId) {
    currentDeptUsers.value = []
    return
  }

  try {
    userLoading.value = true
    const response = await UserAPI.getUserPage({
      deptId,
      status: props.onlyEnabled ? '0' : undefined,
      current: 1,
      size: 100
    })

    const users = response.data?.records || []
    const userOptions = users
      .filter(user => !props.excludeUserIds.includes(user.id))
      .map(user => ({
        value: user.id,
        label: user.nickname || user.username,
        disabled: user.status !== '0',
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        deptId: user.deptId,
        deptName: user.deptName,
        status: user.status,
        roles: user.roles
      }))

    currentDeptUsers.value = userOptions

    // 更新缓存，避免重复的用户
    userOptions.forEach(user => {
      const existingIndex = allLoadedUsers.value.findIndex(u => u.value === user.value)
      if (existingIndex >= 0) {
        allLoadedUsers.value[existingIndex] = user
      } else {
        allLoadedUsers.value.push(user)
      }
    })
  } catch (error) {
    console.error('加载部门用户失败:', error)
    currentDeptUsers.value = []
  } finally {
    userLoading.value = false
  }
}

const isUserSelected = (userId: string): boolean => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.includes(userId)
  }
  return selectedUserIds.value === userId
}

const handleDeptChange = (value: string | string[], depts: DeptSelectorOption[]) => {
  const deptId = Array.isArray(value) ? value[0] : value
  selectedDept.value = depts[0] || null
  loadDeptUsers(deptId)
  emit('deptChange', deptId, depts[0])
}

const handleUserClick = (user: UserSelectorOption) => {
  if (user.disabled) return

  if (config.value.multiple) {
    handleUserCheck(user, !isUserSelected(user.value))
  } else {
    selectedUserIds.value = user.value
    emit('update:modelValue', user.value)
    emit('change', user.value, [user])
    emit('select', user.value, user)
  }
}

const handleUserCheck = (user: UserSelectorOption, checked: boolean) => {
  if (!config.value.multiple || user.disabled) return
  
  let newValues: string[]
  if (Array.isArray(selectedUserIds.value)) {
    newValues = [...selectedUserIds.value]
  } else {
    newValues = []
  }
  
  if (checked) {
    if (!newValues.includes(user.value)) {
      newValues.push(user.value)
    }
  } else {
    const index = newValues.indexOf(user.value)
    if (index > -1) {
      newValues.splice(index, 1)
    }
  }
  
  selectedUserIds.value = newValues
  emit('update:modelValue', newValues)
  
  const selectedOptions = getSelectedUserOptions()
  emit('change', newValues, selectedOptions)
  
  if (checked) {
    emit('select', user.value, user)
  } else {
    emit('remove', user.value)
  }
}

const selectAllUsers = () => {
  if (!config.value.multiple) return
  
  const enabledUsers = filteredUsers.value.filter(user => !user.disabled)
  const allUserIds = enabledUsers.map(user => user.value)
  
  selectedUserIds.value = allUserIds
  emit('update:modelValue', allUserIds)
  emit('change', allUserIds, enabledUsers)
}

const clearSelection = () => {
  selectedUserIds.value = config.value.multiple ? [] : ''
  emit('update:modelValue', selectedUserIds.value)
  emit('change', selectedUserIds.value, [])
}

const removeUserSelection = (userId: string) => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    const newValues = selectedUserIds.value.filter(id => id !== userId)
    selectedUserIds.value = newValues
    emit('update:modelValue', newValues)
    
    const selectedOptions = getSelectedUserOptions()
    emit('change', newValues, selectedOptions)
    emit('remove', userId)
  }
}

const getSelectedUserOptions = (): UserSelectorOption[] => {
  const selectedIds = config.value.multiple && Array.isArray(selectedUserIds.value)
    ? selectedUserIds.value
    : selectedUserIds.value ? [selectedUserIds.value as string] : []

  // 从所有加载过的用户中查找，不仅仅是当前部门的用户
  return allLoadedUsers.value.filter(user => selectedIds.includes(user.value))
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedUserIds.value = Array.isArray(newValue) ? [...newValue] : newValue || (config.value.multiple ? [] : '')
}, { immediate: true })

// 组件挂载
onMounted(() => {
  selectedUserIds.value = Array.isArray(props.modelValue) ? [...props.modelValue] : props.modelValue || (config.value.multiple ? [] : '')
})

// 暴露方法
defineExpose({
  clearSelection,
  selectAllUsers,
  getSelectedUserOptions,
  refresh: () => loadDeptUsers(selectedDeptId.value)
})
</script>

<style scoped>
.user-by-dept-selector-content {
  width: 100%;
}

.panel-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.user-item {
  border: 1px solid transparent;
}

.user-item:hover {
  border-color: #e5e7eb;
}

.user-item.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.selected-tags {
  max-height: 120px;
  overflow-y: auto;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  font-size: 24px;
  color: #409eff;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
