package com.xhcai.common.security.annotation;

import java.lang.annotation.*;

/**
 * 租户管理员权限校验注解
 * 用于标识需要租户管理员权限的方法或类
 * 租户管理员只能操作自己租户内的数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresTenantAdmin {

    /**
     * 权限校验失败时的提示信息
     */
    String message() default "需要租户管理员权限";

    /**
     * 是否允许平台管理员访问
     * 默认为true，平台管理员可以访问所有租户管理员功能
     */
    boolean allowPlatformAdmin() default true;
}
