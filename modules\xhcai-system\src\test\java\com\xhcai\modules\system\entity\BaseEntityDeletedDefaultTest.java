package com.xhcai.modules.system.entity;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试BaseEntity中deleted字段的默认值
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class BaseEntityDeletedDefaultTest {

    @Test
    public void testSysDictDataDeletedDefault() {
        // 创建字典数据实体
        SysDictData dictData = new SysDictData();
        
        // 验证deleted字段默认值为0
        assertNotNull(dictData.getDeleted(), "deleted字段不应该为null");
        assertEquals(Integer.valueOf(0), dictData.getDeleted(), "deleted字段默认值应该为0");
    }

    @Test
    public void testSysUserDeletedDefault() {
        // 创建用户实体
        SysUser user = new SysUser();
        
        // 验证deleted字段默认值为0
        assertNotNull(user.getDeleted(), "deleted字段不应该为null");
        assertEquals(Integer.valueOf(0), user.getDeleted(), "deleted字段默认值应该为0");
    }

    @Test
    public void testSysDictDeletedDefault() {
        // 创建字典类型实体
        SysDict dict = new SysDict();
        
        // 验证deleted字段默认值为0
        assertNotNull(dict.getDeleted(), "deleted字段不应该为null");
        assertEquals(Integer.valueOf(0), dict.getDeleted(), "deleted字段默认值应该为0");
    }

    @Test
    public void testSysRoleDeletedDefault() {
        // 创建角色实体
        SysRole role = new SysRole();
        
        // 验证deleted字段默认值为0
        assertNotNull(role.getDeleted(), "deleted字段不应该为null");
        assertEquals(Integer.valueOf(0), role.getDeleted(), "deleted字段默认值应该为0");
    }

    @Test
    public void testSysPermissionDeletedDefault() {
        // 创建权限实体
        SysPermission permission = new SysPermission();
        
        // 验证deleted字段默认值为0
        assertNotNull(permission.getDeleted(), "deleted字段不应该为null");
        assertEquals(Integer.valueOf(0), permission.getDeleted(), "deleted字段默认值应该为0");
    }

    @Test
    public void testSysDeptDeletedDefault() {
        // 创建部门实体
        SysDept dept = new SysDept();
        
        // 验证deleted字段默认值为0
        assertNotNull(dept.getDeleted(), "deleted字段不应该为null");
        assertEquals(Integer.valueOf(0), dept.getDeleted(), "deleted字段默认值应该为0");
    }
}
