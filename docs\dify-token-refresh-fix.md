# Dify令牌刷新机制修复文档

## 问题描述

在使用Dify令牌调用Dify接口时，出现401错误：

```
2025-08-21 09:31:40.457 -DEBUG 9820 --- [ctor-http-nio-2] c.x.m.dify.config.DifyWebClientConfig    : 401 错误响应体: {"code": "unauthorized", "message": "Token has expired.", "status": 401}

2025-08-21 09:31:40.465 -ERROR 9820 --- [ctor-http-nio-2] c.x.m.dify.config.DifyWebClientConfig    : 获取平台5668647c8ac87db478939ddd8810c098的智能体列表失败

org.springframework.web.reactive.function.client.WebClientResponseException: 401 Token Expired
```

## 问题原因

1. **令牌过期检测不完整**：系统能够检测到令牌过期，但在某些情况下没有正确触发刷新机制
2. **错误处理链不完整**：在响应式编程中，401错误没有被正确传播到错误处理链
3. **重试机制缺失**：令牌刷新后没有自动重试原始请求
4. **Fallback机制不完善**：当refresh_token也过期时，没有自动重新登录
5. **响应式上下文问题**：在响应式编程中，`SecurityContextHolder`无法正确获取用户信息，导致"您当前没有登录"错误

## 修复内容

### 1. 改进DifyWebClientConfig中的错误处理

**文件**: `modules/xhcai-dify/src/main/java/com/xhcai/modules/dify/config/DifyWebClientConfig.java`

**主要改进**:
- 增强了`makeAuthenticatedGetRequestReactiveForPlatform`方法的错误处理
- 添加了完整的401和600错误处理链
- 改进了令牌过期检测逻辑
- 添加了自动重试机制

**关键代码**:
```java
.onErrorResume(WebClientResponseException.class, e -> {
    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
        log.warn("处理平台{}的401错误：尝试刷新令牌 (重试次数: {})", platformId, retryCount);
        return difyMultiPlatformAuthService.handleCurrentUserUnauthorized(platformId)
                .doOnNext(newToken -> log.info("平台{}令牌刷新成功，准备重试", platformId))
                .flatMap(newAccessToken -> {
                    log.info("平台{}使用新令牌重试API调用", platformId);
                    return makeAuthenticatedGetRequestReactiveForPlatform(platformId, uri, retryCount + 1);
                });
    }
    // ... 其他错误处理
})
```

### 2. 增强DifyMultiPlatformAuthServiceImpl的令牌刷新逻辑

**文件**: `modules/xhcai-dify/src/main/java/com/xhcai/modules/dify/service/impl/DifyMultiPlatformAuthServiceImpl.java`

**主要改进**:
- 改进了`handleUnauthorized`方法，增加了自动fallback到重新登录的机制
- 增强了同步版本的错误处理
- 添加了更详细的日志记录
- **解决响应式上下文问题**：修改了`handleCurrentUserUnauthorized`和`handleCurrentUserRelogin`方法，支持从Reactor Context和SecurityContext两种方式获取用户信息

**关键代码**:
```java
@Override
public Mono<String> handleUnauthorized(String userId, String platformId) {
    log.info("处理用户{}在平台{}的401未授权错误", userId, platformId);
    return refreshToken(userId, platformId)
            .flatMap(response -> {
                try {
                    String accessToken = response.getAccessTokenFromData();
                    if (StringUtils.hasText(accessToken)) {
                        log.info("用户{}在平台{}的令牌刷新成功", userId, platformId);
                        return Mono.just(accessToken);
                    } else {
                        log.warn("用户{}在平台{}的令牌刷新失败：访问令牌为空，尝试重新登录", userId, platformId);
                        return handleRelogin(userId, platformId);
                    }
                } catch (Exception e) {
                    log.warn("用户{}在平台{}解析访问令牌失败，尝试重新登录", userId, platformId, e);
                    return handleRelogin(userId, platformId);
                }
            })
            .onErrorResume(e -> {
                log.warn("用户{}在平台{}的令牌刷新完全失败，尝试重新登录", userId, platformId, e);
                return handleRelogin(userId, platformId);
            });
}
```

**响应式上下文解决方案**:
```java
@Override
public Mono<String> handleCurrentUserUnauthorized(String platformId) {
    return Mono.deferContextual(contextView -> {
        // 首先尝试从Reactor Context获取用户ID
        String userId = contextView.getOrDefault("userId", null);

        if (!StringUtils.hasText(userId)) {
            // 如果Context中没有，尝试从SecurityContext获取
            try {
                LoginUser currentUser = SecurityUtils.getCurrentUserSafely();
                if (currentUser != null && StringUtils.hasText(currentUser.getUserId())) {
                    userId = currentUser.getUserId();
                }
            } catch (Exception e) {
                log.debug("从SecurityContext获取用户ID失败: {}", e.getMessage());
            }
        }

        if (!StringUtils.hasText(userId)) {
            return Mono.error(new RuntimeException("用户未登录或无法获取用户信息"));
        }

        return handleUnauthorized(userId, platformId);
    });
}
```

### 3. 令牌过期检测机制

**已存在的方法**: `isDifyTokenExpiredError`

该方法能够准确识别Dify特定的令牌过期错误格式：
```java
private boolean isDifyTokenExpiredError(String responseBody) {
    try {
        return responseBody != null &&
               (responseBody.contains("\"code\": \"unauthorized\"") ||
                responseBody.contains("\"code\":\"unauthorized\"")) &&
               (responseBody.contains("Token has expired") ||
                responseBody.contains("token has expired") ||
                responseBody.contains("令牌已过期"));
    } catch (Exception e) {
        log.debug("解析错误响应体失败: {}", e.getMessage());
        return false;
    }
}
```

## 修复后的工作流程

1. **API调用** → 收到401错误
2. **错误检测** → 识别为令牌过期错误
3. **尝试刷新** → 使用refresh_token刷新access_token
4. **刷新成功** → 使用新令牌重试原始请求
5. **刷新失败** → 自动重新登录获取新的令牌对
6. **重新登录成功** → 使用新令牌重试原始请求
7. **重试次数限制** → 最多重试2次，避免无限循环

## 测试验证

### 单元测试
- `DifyTokenRefreshTest.java`: 测试令牌刷新的核心逻辑
- 验证刷新成功和失败的场景
- 测试fallback到重新登录的机制

### 集成测试
- `DifyTokenRefreshIntegrationTest.java`: 使用WireMock模拟完整的HTTP交互
- 测试端到端的令牌刷新流程
- 验证并发场景和重试限制

## 使用说明

修复后，系统会自动处理令牌过期的情况，开发者无需手动处理401错误。系统会：

1. 自动检测令牌过期
2. 尝试使用refresh_token刷新
3. 如果刷新失败，自动重新登录
4. 使用新令牌重试原始请求
5. 记录详细的日志便于调试

## 注意事项

1. **重试次数限制**: 系统最多重试2次，避免无限循环
2. **并发安全**: 令牌刷新操作是线程安全的
3. **日志记录**: 所有令牌操作都有详细的日志记录
4. **错误传播**: 如果所有重试都失败，会抛出明确的异常信息

## 监控建议

建议监控以下指标：
- 令牌刷新频率
- 刷新失败率
- 重新登录频率
- API调用重试次数

这些指标可以帮助识别潜在的配置问题或服务异常。
