/**
 * 通用选择器组件导出
 */

export { default as DropdownSelector } from './DropdownSelector.vue'
export { default as DeptTreeSelector } from './DeptTreeSelector.vue'
export { default as DeptTreeSelectorContent } from './DeptTreeSelectorContent.vue'
export { default as UserByDeptSelector } from './UserByDeptSelector.vue'
export { default as UserByDeptSelectorContent } from './UserByDeptSelectorContent.vue'
export { default as UserByRoleSelector } from './UserByRoleSelector.vue'
export { default as UserByRoleSelectorContent } from './UserByRoleSelectorContent.vue'
export { default as PermissionByRoleSelector } from './PermissionByRoleSelector.vue'
export { default as PermissionByRoleSelectorContent } from './PermissionByRoleSelectorContent.vue'

// 导出类型
export type {
  SelectorOption,
  SelectorConfig,
  DeptSelectorOption,
  UserSelectorOption,
  RoleSelectorOption,
  PermissionSelectorOption
} from '@/types/system'
