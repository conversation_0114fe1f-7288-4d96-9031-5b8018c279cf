package com.xhcai.modules.agent.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 智能体实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent")
@Schema(description = "智能体")
@TableName("agent")
public class Agent extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体名称
     */
    @Column(name = "name", length = 100)
    @Schema(description = "智能体名称", example = "客服助手")
    @NotBlank(message = "智能体名称不能为空")
    @Size(min = 1, max = 100, message = "智能体名称长度必须在1-100个字符之间")
    @TableField("name")
    private String name;

    /**
     * 智能体描述
     */
    @Column(name = "description", length = 500)
    @Schema(description = "智能体描述", example = "专业的客服助手，能够回答用户问题")
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 智能体头像URL
     */
    @Column(name = "avatar", length = 255)
    @Schema(description = "智能体头像URL", example = "https://example.com/avatar.jpg")
    @Size(max = 255, message = "头像URL长度不能超过255个字符")
    @TableField("avatar")
    private String avatar;

    /**
     * 智能体图标背景颜色
     */
    @Column(name = "icon_background", length = 100)
    @Schema(description = "智能体图标背景颜色", example = "linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)")
    @Size(max = 100, message = "图标背景颜色长度不能超过100个字符")
    @TableField("icon_background")
    private String iconBackground;

    /**
     * 智能体类型
     */
    @Column(name = "type", length = 20)
    @Schema(description = "智能体类型", example = "chat", allowableValues = {"chat", "workflow", "completion", "advanced-chat", "agent-chat"})
    @NotBlank(message = "智能体类型不能为空")
    @Pattern(regexp = "^(chat|workflow|completion|advanced-chat|agent-chat)$", message = "智能体类型必须为chat、workflow、completion、advanced-chat或agent-chat")
    @TableField("type")
    private String type;

    /**
     * 模型配置（JSON格式）
     */
    @Column(name = "model_config", columnDefinition = "TEXT")
    @Schema(description = "模型配置", example = "{\"model\":\"gpt-3.5-turbo\",\"temperature\":0.7}")
    @TableField("model_config")
    private String modelConfig;

    /**
     * 系统提示词
     */
    @Column(name = "system_prompt", columnDefinition = "TEXT")
    @Schema(description = "系统提示词", example = "你是一个专业的客服助手...")
    @TableField("system_prompt")
    private String systemPrompt;

    /**
     * 工具配置（JSON格式）
     */
    @Column(name = "tools_config", columnDefinition = "TEXT")
    @Schema(description = "工具配置", example = "[{\"name\":\"search\",\"enabled\":true}]")
    @TableField("tools_config")
    private String toolsConfig;

    /**
     * 知识库配置（JSON格式）
     */
    @Column(name = "knowledge_config", columnDefinition = "TEXT")
    @Schema(description = "知识库配置", example = "{\"datasets\":[\"kb_001\",\"kb_002\"]}")
    @TableField("knowledge_config")
    private String knowledgeConfig;

    /**
     * 对话配置（JSON格式）
     */
    @Column(name = "conversation_config", columnDefinition = "TEXT")
    @Schema(description = "对话配置", example = "{\"maxTokens\":2000,\"temperature\":0.7}")
    @TableField("conversation_config")
    private String conversationConfig;

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    /**
     * 是否公开
     */
    @Column(name = "is_public")
    @Schema(description = "是否公开", example = "false")
    @TableField("is_public")
    private Boolean isPublic;

    /**
     * 排序号
     */
    @Column(name = "sort_order")
    @Schema(description = "排序号", example = "1")
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 版本号
     */
    @Column(name = "version", length = 20)
    @Schema(description = "版本号", example = "1.0.0")
    @Size(max = 20, message = "版本号长度不能超过20个字符")
    @TableField("version")
    private String version;

    /**
     * 发布时间
     */
    @Column(name = "published_at")
    @Schema(description = "发布时间")
    @TableField("published_at")
    private LocalDateTime publishedAt;

    /**
     * 最后对话时间
     */
    @Column(name = "last_conversation_at")
    @Schema(description = "最后对话时间")
    @TableField("last_conversation_at")
    private LocalDateTime lastConversationAt;

    /**
     * 对话次数
     */
    @Column(name = "conversation_count")
    @Schema(description = "对话次数", example = "100")
    @TableField("conversation_count")
    private Long conversationCount;

    /**
     * 智能体来源类型：platform-本平台，external-外部智能体
     */
    @Column(name = "source_type", length = 20)
    @Schema(description = "智能体来源类型", example = "platform", allowableValues = {"platform", "external"})
    @Pattern(regexp = "^(platform|external)$", message = "智能体来源类型必须为platform或external")
    @TableField("source_type")
    private String sourceType;

    /**
     * 外部智能体ID（当sourceType为external时使用）
     */
    @Column(name = "external_agent_id", length = 36)
    @Schema(description = "外部智能体ID", example = "ext_agent_001")
    @Size(max = 36, message = "外部智能体ID长度不能超过36个字符")
    @TableField("external_agent_id")
    private String externalAgentId;

    /**
     * 第三方智能体平台ID
     */
    @Column(name = "platform_id", length = 36)
    @Schema(description = "第三方智能体平台ID", example = "platform_001")
    @Size(max = 36, message = "平台ID长度不能超过36个字符")
    @TableField("platform_id")
    private String platformId;

    /**
     * 智能体属于对应的AI场景（即项目）
     */
    @Column(name = "project_id", length = 36)
    @Schema(description = "智能体属于对应的AI场景（即项目）")
    @Size(max = 36, message = "项目ID长度不能超过36个字符")
    @TableField("project_id")
    private String project_id;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getIconBackground() {
        return iconBackground;
    }

    public void setIconBackground(String iconBackground) {
        this.iconBackground = iconBackground;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getModelConfig() {
        return modelConfig;
    }

    public void setModelConfig(String modelConfig) {
        this.modelConfig = modelConfig;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public String getToolsConfig() {
        return toolsConfig;
    }

    public void setToolsConfig(String toolsConfig) {
        this.toolsConfig = toolsConfig;
    }

    public String getKnowledgeConfig() {
        return knowledgeConfig;
    }

    public void setKnowledgeConfig(String knowledgeConfig) {
        this.knowledgeConfig = knowledgeConfig;
    }

    public String getConversationConfig() {
        return conversationConfig;
    }

    public void setConversationConfig(String conversationConfig) {
        this.conversationConfig = conversationConfig;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public LocalDateTime getLastConversationAt() {
        return lastConversationAt;
    }

    public void setLastConversationAt(LocalDateTime lastConversationAt) {
        this.lastConversationAt = lastConversationAt;
    }

    public Long getConversationCount() {
        return conversationCount;
    }

    public void setConversationCount(Long conversationCount) {
        this.conversationCount = conversationCount;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getExternalAgentId() {
        return externalAgentId;
    }

    public void setExternalAgentId(String externalAgentId) {
        this.externalAgentId = externalAgentId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getProject_id() {
        return project_id;
    }

    public void setProject_id(String project_id) {
        this.project_id = project_id;
    }
}
