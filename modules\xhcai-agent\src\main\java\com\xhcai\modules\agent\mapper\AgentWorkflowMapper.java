package com.xhcai.modules.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.agent.dto.AgentWorkflowQueryDTO;
import com.xhcai.modules.agent.entity.AgentWorkflow;
import com.xhcai.modules.agent.vo.AgentWorkflowVO;
import com.xhcai.modules.agent.vo.AgentWorkflowVersionVO;

/**
 * 智能体工作流Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AgentWorkflowMapper extends BaseMapper<AgentWorkflow> {

    /**
     * 分页查询工作流列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 工作流列表
     */
    @Select("<script>"
            + "SELECT "
            + "    aw.id, aw.agent_id as agentId, a.name as agentName, aw.name, aw.description, "
            + "    aw.version, aw.workflow_config as workflowConfig, aw.nodes_config as nodesConfig, "
            + "    aw.connections_config as connectionsConfig, aw.global_variables as globalVariables, "
            + "    aw.status, "
            + "    CASE aw.status "
            + "        WHEN '1' THEN '启用' "
            + "        WHEN '0' THEN '禁用' "
            + "        ELSE '未知' "
            + "    END as statusName, "
            + "    aw.is_published as isPublished, aw.published_at as publishedAt, "
            + "    aw.last_modified as lastModified, aw.operation_type as operationType, "
            + "    aw.operation_desc as operationDesc, aw.create_time as createTime, "
            + "    aw.update_time as updateTime, aw.create_by as createBy, "
            + "    aw.update_by as updateBy, aw.remark "
            + "FROM agent_workflow aw "
            + "LEFT JOIN agent a ON aw.agent_id = a.id AND a.deleted = 0 "
            + "WHERE aw.deleted = 0 AND aw.tenant_id = #{queryDTO.tenantId} "
            + "<if test='queryDTO.agentId != null and queryDTO.agentId != \"\"'>"
            + "    AND aw.agent_id = #{queryDTO.agentId} "
            + "</if>"
            + "<if test='queryDTO.name != null and queryDTO.name != \"\"'>"
            + "    AND aw.name LIKE CONCAT('%', #{queryDTO.name}, '%') "
            + "</if>"
            + "<if test='queryDTO.version != null'>"
            + "    AND aw.version = #{queryDTO.version} "
            + "</if>"
            + "<if test='queryDTO.status != null and queryDTO.status != \"\"'>"
            + "    AND aw.status = #{queryDTO.status} "
            + "</if>"
            + "<if test='queryDTO.isPublished != null'>"
            + "    AND aw.is_published = #{queryDTO.isPublished} "
            + "</if>"
            + "<if test='queryDTO.operationType != null and queryDTO.operationType != \"\"'>"
            + "    AND aw.operation_type = #{queryDTO.operationType} "
            + "</if>"
            + "<if test='queryDTO.startTime != null'>"
            + "    AND aw.create_time >= #{queryDTO.startTime} "
            + "</if>"
            + "<if test='queryDTO.endTime != null'>"
            + "    AND aw.create_time &lt;= #{queryDTO.endTime} "
            + "</if>"
            + "<if test='queryDTO.latestVersion != null and queryDTO.latestVersion == true'>"
            + "    AND aw.version = (SELECT MAX(version) FROM agent_workflow WHERE agent_id = aw.agent_id AND deleted = 0) "
            + "</if>"
            + "ORDER BY aw.agent_id, aw.version DESC, aw.create_time DESC"
            + "</script>")
    IPage<AgentWorkflowVO> selectWorkflowPage(Page<AgentWorkflowVO> page, @Param("queryDTO") AgentWorkflowQueryDTO queryDTO);

    /**
     * 根据ID查询工作流详情
     *
     * @param id 工作流ID
     * @param tenantId 租户ID
     * @return 工作流详情
     */
    @Select("SELECT "
            + "    aw.id, aw.agent_id as agentId, a.name as agentName, aw.name, aw.description, "
            + "    aw.version, aw.nodes_data as nodesData, aw.edges_data as edgesData, "
            + "    aw.viewport_config as viewportConfig, aw.node_library as nodeLibrary, "
            + "    aw.global_variables as globalVariables, aw.status, "
            + "    CASE aw.status "
            + "        WHEN '1' THEN '启用' "
            + "        WHEN '0' THEN '禁用' "
            + "        ELSE '未知' "
            + "    END as statusName, "
            + "    aw.is_published as isPublished, aw.published_at as publishedAt, "
            + "    aw.last_modified as lastModified, aw.operation_type as operationType, "
            + "    aw.operation_desc as operationDesc, aw.create_time as createTime, "
            + "    aw.update_time as updateTime, aw.create_by as createBy, "
            + "    aw.update_by as updateBy, aw.remark "
            + "FROM agent_workflow aw "
            + "LEFT JOIN agent a ON aw.agent_id = a.id AND a.deleted = 0 "
            + "WHERE aw.id = #{id} AND aw.deleted = 0 AND aw.tenant_id = #{tenantId}")
    AgentWorkflowVO selectWorkflowById(@Param("id") String id, @Param("tenantId") String tenantId);

    /**
     * 根据智能体ID查询最新版本工作流
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 最新版本工作流
     */
    @Select("SELECT "
            + "    aw.id, aw.agent_id as agentId, a.name as agentName, aw.name, aw.description, "
            + "    aw.version, aw.nodes_data as nodesData, aw.edges_data as edgesData, "
            + "    aw.viewport_config as viewportConfig, aw.node_library as nodeLibrary, "
            + "    aw.global_variables as globalVariables, aw.status, "
            + "    CASE aw.status "
            + "        WHEN '1' THEN '启用' "
            + "        WHEN '0' THEN '禁用' "
            + "        ELSE '未知' "
            + "    END as statusName, "
            + "    aw.is_published as isPublished, aw.published_at as publishedAt, "
            + "    aw.last_modified as lastModified, aw.operation_type as operationType, "
            + "    aw.operation_desc as operationDesc, aw.create_time as createTime, "
            + "    aw.update_time as updateTime, aw.create_by as createBy, "
            + "    aw.update_by as updateBy, aw.remark "
            + "FROM agent_workflow aw "
            + "LEFT JOIN agent a ON aw.agent_id = a.id AND a.deleted = 0 "
            + "WHERE aw.agent_id = #{agentId} AND aw.deleted = 0 AND aw.tenant_id = #{tenantId} "
            + "AND aw.version = (SELECT MAX(version) FROM agent_workflow WHERE agent_id = #{agentId} AND deleted = 0 AND tenant_id = #{tenantId}) "
            + "ORDER BY aw.create_time DESC LIMIT 1")
    AgentWorkflowVO selectLatestWorkflowByAgentId(@Param("agentId") String agentId, @Param("tenantId") String tenantId);

    /**
     * 查询智能体工作流版本历史
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 版本历史列表
     */
    @Select("SELECT "
            + "    aw.id, aw.agent_id as agentId, aw.version, aw.name, aw.description, "
            + "    aw.is_published as isPublished, aw.published_at as publishedAt, "
            + "    aw.last_modified as lastModified, aw.operation_type as operationType, "
            + "    aw.operation_desc as operationDesc, aw.create_time as createTime, "
            + "    aw.create_by as createBy, "
            + "    CASE WHEN aw.version = (SELECT MAX(version) FROM agent_workflow WHERE agent_id = #{agentId} AND deleted = 0 AND tenant_id = #{tenantId}) "
            + "         THEN true ELSE false END as isCurrent, "
            + "    (SELECT COUNT(*) FROM JSON_ARRAY_ELEMENTS(aw.nodes_config::json)) as nodeCount, "
            + "    (SELECT COUNT(*) FROM JSON_ARRAY_ELEMENTS(aw.connections_config::json)) as connectionCount "
            + "FROM agent_workflow aw "
            + "WHERE aw.agent_id = #{agentId} AND aw.deleted = 0 AND aw.tenant_id = #{tenantId} "
            + "ORDER BY aw.version DESC, aw.create_time DESC")
    List<AgentWorkflowVersionVO> selectVersionHistory(@Param("agentId") String agentId, @Param("tenantId") String tenantId);

    /**
     * 获取智能体的最大版本号
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 最大版本号
     */
    @Select("SELECT COALESCE(MAX(version), 0) FROM agent_workflow "
            + "WHERE agent_id = #{agentId} AND deleted = 0 AND tenant_id = #{tenantId}")
    Integer selectMaxVersionByAgentId(@Param("agentId") String agentId, @Param("tenantId") String tenantId);

    /**
     * 更新工作流发布状态
     *
     * @param id 工作流ID
     * @param isPublished 是否已发布
     * @param tenantId 租户ID
     * @return 更新行数
     */
    @Update("UPDATE agent_workflow SET is_published = #{isPublished}, "
            + "published_at = CASE WHEN #{isPublished} = true THEN NOW() ELSE NULL END, "
            + "update_time = NOW() "
            + "WHERE id = #{id} AND deleted = 0 AND tenant_id = #{tenantId}")
    int updatePublishStatus(@Param("id") String id, @Param("isPublished") Boolean isPublished, @Param("tenantId") String tenantId);

    /**
     * 统计智能体工作流数量
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 工作流数量
     */
    @Select("SELECT COUNT(*) FROM agent_workflow "
            + "WHERE agent_id = #{agentId} AND deleted = 0 AND tenant_id = #{tenantId}")
    Long countByAgentId(@Param("agentId") String agentId, @Param("tenantId") String tenantId);

    /**
     * 统计工作流版本数量
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 版本数量
     */
    @Select("SELECT COUNT(DISTINCT version) FROM agent_workflow "
            + "WHERE agent_id = #{agentId} AND deleted = 0 AND tenant_id = #{tenantId}")
    Long countVersionsByAgentId(@Param("agentId") String agentId, @Param("tenantId") String tenantId);
}
