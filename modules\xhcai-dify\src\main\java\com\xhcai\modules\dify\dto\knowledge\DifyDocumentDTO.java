package com.xhcai.modules.dify.dto.knowledge;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文档DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyDocumentDTO {

    /**
     * 文档ID
     */
    private String id;

    /**
     * 知识库ID
     */
    @NotBlank(message = "知识库ID不能为空")
    @JsonProperty("knowledge_id")
    private String knowledgeId;

    /**
     * 文档名称
     */
    @NotBlank(message = "文档名称不能为空")
    @Size(max = 200, message = "文档名称长度不能超过200个字符")
    private String name;

    /**
     * 文档类型：text-文本, file-文件, url-网页
     */
    private String type;

    /**
     * 文档内容（文本类型）
     */
    private String content;

    /**
     * 文件信息（文件类型）
     */
    @JsonProperty("file_info")
    private FileInfo fileInfo;

    /**
     * URL信息（网页类型）
     */
    @JsonProperty("url_info")
    private UrlInfo urlInfo;

    /**
     * 文档状态：processing-处理中, completed-已完成, failed-失败
     */
    private String status;

    /**
     * 处理进度（0-100）
     */
    private Integer progress;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 分段配置
     */
    @JsonProperty("indexing_config")
    private IndexingConfig indexingConfig;

    /**
     * 分段数量
     */
    @JsonProperty("segment_count")
    private Integer segmentCount;

    /**
     * 字符数量
     */
    @JsonProperty("character_count")
    private Long characterCount;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    @JsonProperty("created_by")
    private String createdBy;

    /**
     * 扩展属性
     */
    private Map<String, Object> metadata;

    /**
     * 文件信息
     */
    public static class FileInfo {
        @JsonProperty("file_id")
        private String fileId;
        @JsonProperty("file_name")
        private String fileName;
        @JsonProperty("file_size")
        private Long fileSize;
        @JsonProperty("file_type")
        private String fileType;
        @JsonProperty("upload_url")
        private String uploadUrl;

        // Getters and Setters
        public String getFileId() { return fileId; }
        public void setFileId(String fileId) { this.fileId = fileId; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }
        public String getUploadUrl() { return uploadUrl; }
        public void setUploadUrl(String uploadUrl) { this.uploadUrl = uploadUrl; }
    }

    /**
     * URL信息
     */
    public static class UrlInfo {
        private String url;
        private String title;
        private String description;
        @JsonProperty("crawl_config")
        private CrawlConfig crawlConfig;

        public static class CrawlConfig {
            @JsonProperty("max_depth")
            private Integer maxDepth;
            @JsonProperty("max_pages")
            private Integer maxPages;
            @JsonProperty("include_patterns")
            private List<String> includePatterns;
            @JsonProperty("exclude_patterns")
            private List<String> excludePatterns;

            // Getters and Setters
            public Integer getMaxDepth() { return maxDepth; }
            public void setMaxDepth(Integer maxDepth) { this.maxDepth = maxDepth; }
            public Integer getMaxPages() { return maxPages; }
            public void setMaxPages(Integer maxPages) { this.maxPages = maxPages; }
            public List<String> getIncludePatterns() { return includePatterns; }
            public void setIncludePatterns(List<String> includePatterns) { this.includePatterns = includePatterns; }
            public List<String> getExcludePatterns() { return excludePatterns; }
            public void setExcludePatterns(List<String> excludePatterns) { this.excludePatterns = excludePatterns; }
        }

        // Getters and Setters
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public CrawlConfig getCrawlConfig() { return crawlConfig; }
        public void setCrawlConfig(CrawlConfig crawlConfig) { this.crawlConfig = crawlConfig; }
    }

    /**
     * 分段配置
     */
    public static class IndexingConfig {
        @JsonProperty("chunk_strategy")
        private String chunkStrategy; // automatic, custom

        @JsonProperty("chunk_size")
        private Integer chunkSize;

        @JsonProperty("chunk_overlap")
        private Integer chunkOverlap;

        @JsonProperty("separator")
        private String separator;

        @JsonProperty("preprocessing_rules")
        private List<PreprocessingRule> preprocessingRules;

        public static class PreprocessingRule {
            private String type;
            private boolean enabled;
            private Map<String, Object> config;

            // Getters and Setters
            public String getType() { return type; }
            public void setType(String type) { this.type = type; }
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            public Map<String, Object> getConfig() { return config; }
            public void setConfig(Map<String, Object> config) { this.config = config; }
        }

        // Getters and Setters
        public String getChunkStrategy() { return chunkStrategy; }
        public void setChunkStrategy(String chunkStrategy) { this.chunkStrategy = chunkStrategy; }
        public Integer getChunkSize() { return chunkSize; }
        public void setChunkSize(Integer chunkSize) { this.chunkSize = chunkSize; }
        public Integer getChunkOverlap() { return chunkOverlap; }
        public void setChunkOverlap(Integer chunkOverlap) { this.chunkOverlap = chunkOverlap; }
        public String getSeparator() { return separator; }
        public void setSeparator(String separator) { this.separator = separator; }
        public List<PreprocessingRule> getPreprocessingRules() { return preprocessingRules; }
        public void setPreprocessingRules(List<PreprocessingRule> preprocessingRules) { this.preprocessingRules = preprocessingRules; }
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getKnowledgeId() { return knowledgeId; }
    public void setKnowledgeId(String knowledgeId) { this.knowledgeId = knowledgeId; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    public FileInfo getFileInfo() { return fileInfo; }
    public void setFileInfo(FileInfo fileInfo) { this.fileInfo = fileInfo; }
    public UrlInfo getUrlInfo() { return urlInfo; }
    public void setUrlInfo(UrlInfo urlInfo) { this.urlInfo = urlInfo; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public Integer getProgress() { return progress; }
    public void setProgress(Integer progress) { this.progress = progress; }
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    public IndexingConfig getIndexingConfig() { return indexingConfig; }
    public void setIndexingConfig(IndexingConfig indexingConfig) { this.indexingConfig = indexingConfig; }
    public Integer getSegmentCount() { return segmentCount; }
    public void setSegmentCount(Integer segmentCount) { this.segmentCount = segmentCount; }
    public Long getCharacterCount() { return characterCount; }
    public void setCharacterCount(Long characterCount) { this.characterCount = characterCount; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
}
