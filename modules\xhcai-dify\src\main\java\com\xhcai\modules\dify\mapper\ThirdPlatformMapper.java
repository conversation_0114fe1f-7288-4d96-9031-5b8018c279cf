package com.xhcai.modules.dify.mapper;

import java.util.List;

import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformQueryDTO;
import com.xhcai.modules.dify.entity.ThirdPlatform;
import com.xhcai.modules.dify.vo.ThirdPlatformVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 第三方智能体Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface ThirdPlatformMapper extends BaseMapper<ThirdPlatform> {

    /**
     * 根据ID查询第三方智能体（用于关联查询）
     */
    @Select("SELECT * FROM third_platform WHERE id = #{id} AND deleted = 0")
    ThirdPlatform selectByIdForAssociation(@Param("id") String id);

    /**
     * 根据ID和来源类型查询第三方智能体（用于关联查询，带条件判断）
     */
    @Select("<script>"
            + "SELECT * FROM third_platform "
            + "WHERE deleted = 0 "
            + "<if test='sourceType != null and sourceType == \"external\" and id != null and id != \"\"'>"
            + "  AND id = #{id}"
            + "</if>"
            + "<if test='sourceType == null or sourceType != \"external\" or id == null or id == \"\"'>"
            + "  AND 1 = 0"
            + "</if>"
            + "</script>")
    ThirdPlatform selectByIdAndSourceType(@Param("id") String id, @Param("sourceType") String sourceType);

    /**
     * 批量查询第三方智能体（用于性能优化）
     */
    @Select("<script>"
            + "SELECT * FROM third_platform "
            + "WHERE deleted = 0 "
            + "<if test='ids != null and ids.size() > 0'>"
            + "  AND id IN "
            + "  <foreach collection='ids' item='id' open='(' separator=',' close=')'>"
            + "    #{id}"
            + "  </foreach>"
            + "</if>"
            + "</script>")
    List<ThirdPlatform> selectByIds(@Param("ids") List<String> ids);

    /**
     * 分页查询第三方智能体列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 第三方智能体VO分页结果
     */
    @Select("<script>"
            + "SELECT "
            + "a.id, a.platform_id, a.name, a.description, a.unit_id, d.dept_name as unit_name, "
            + "a.connection_url, a.api_key, a.timeout, a.access_scope, "
            + "a.authorized_users, a.authorized_units, a.status, a.icon, a.icon_bg, "
            + "a.last_test_time, a.last_test_result, a.last_test_error, "
            + "a.create_by, cu.nickname as create_by_name, a.create_time, "
            + "a.update_by, uu.nickname as update_by_name, a.update_time, a.remark "
            + "FROM third_platform a "
            + "LEFT JOIN sys_dept d ON a.unit_id = d.id "
            + "LEFT JOIN sys_user cu ON a.create_by = cu.id "
            + "LEFT JOIN sys_user uu ON a.update_by = uu.id "
            + "WHERE a.deleted = 0 "
            + "<if test='query.name != null and query.name != \"\"'>"
            + "AND a.name LIKE CONCAT('%', #{query.name}, '%') "
            + "</if>"
            + "<if test='query.description != null and query.description != \"\"'>"
            + "AND a.description LIKE CONCAT('%', #{query.description}, '%') "
            + "</if>"
            + "<if test='query.unitId != null and query.unitId != \"\"'>"
            + "AND a.unit_id = #{query.unitId} "
            + "</if>"
            + "<if test='query.accessScope != null and query.accessScope != \"\"'>"
            + "AND a.access_scope = #{query.accessScope} "
            + "</if>"
            + "<if test='query.status != null'>"
            + "AND a.status = #{query.status} "
            + "</if>"
            + "<if test='query.connectionUrl != null and query.connectionUrl != \"\"'>"
            + "AND a.connection_url LIKE CONCAT('%', #{query.connectionUrl}, '%') "
            + "</if>"
            + "<if test='query.keyword != null and query.keyword != \"\"'>"
            + "AND (a.name LIKE CONCAT('%', #{query.keyword}, '%') OR a.description LIKE CONCAT('%', #{query.keyword}, '%')) "
            + "</if>"
            + "<if test='query.beginTime != null and query.beginTime != \"\"'>"
            + "AND a.create_time >= #{query.beginTime} "
            + "</if>"
            + "<if test='query.endTime != null and query.endTime != \"\"'>"
            + "AND a.create_time &lt;= #{query.endTime} "
            + "</if>"
            + "ORDER BY a.create_time DESC"
            + "</script>")
    @Results(id = "ThirdPlatformVOResult", value = {
        @Result(property = "id", column = "id"),
        @Result(property = "platformId", column = "platform_id"),
        @Result(property = "name", column = "name"),
        @Result(property = "description", column = "description"),
        @Result(property = "unitId", column = "unit_id"),
        @Result(property = "unitName", column = "unit_name"),
        @Result(property = "connectionUrl", column = "connection_url"),
        @Result(property = "apiKey", column = "api_key"),
        @Result(property = "timeout", column = "timeout"),
        @Result(property = "accessScope", column = "access_scope"),
        @Result(property = "authorizedUsersJson", column = "authorized_users"),
        @Result(property = "authorizedUnitsJson", column = "authorized_units"),
        @Result(property = "status", column = "status"),
        @Result(property = "icon", column = "icon"),
        @Result(property = "iconBg", column = "icon_bg"),
        @Result(property = "lastTestTime", column = "last_test_time"),
        @Result(property = "lastTestResult", column = "last_test_result"),
        @Result(property = "lastTestError", column = "last_test_error"),
        @Result(property = "createBy", column = "create_by"),
        @Result(property = "createByName", column = "create_by_name"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateBy", column = "update_by"),
        @Result(property = "updateByName", column = "update_by_name"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "remark", column = "remark")
    })
    IPage<ThirdPlatformVO> selectAgentPage(Page<ThirdPlatformVO> page, @Param("query") ThirdPlatformQueryDTO queryDTO);

    /**
     * 根据用户账号查询第三方智能体列表
     * @param queryDTO 查询条件
     * @return 第三方智能体VO分页结果
     */
    @Select("<script>"
            + "SELECT "
            + "a.id, a.platform_id, a.name, a.description, a.unit_id, d.dept_name as unit_name, "
            + "a.connection_url, a.api_key, a.timeout, a.access_scope, "
            + "a.authorized_users, a.authorized_units, a.status, a.icon, a.icon_bg, "
            + "a.last_test_time, a.last_test_result, a.last_test_error, "
            + "a.create_by, cu.nickname as create_by_name, a.create_time, "
            + "a.update_by, uu.nickname as update_by_name, a.update_time, a.remark "
            + "FROM third_platform a "
            + "LEFT JOIN sys_dept d ON a.unit_id = d.id "
            + "LEFT JOIN sys_user cu ON a.create_by = cu.id "
            + "LEFT JOIN sys_user uu ON a.update_by = uu.id "
            + "WHERE a.deleted = 0 "
            + "<if test='query.name != null and query.name != \"\"'>"
            + "AND a.name LIKE CONCAT('%', #{query.name}, '%') "
            + "</if>"
            + "<if test='query.description != null and query.description != \"\"'>"
            + "AND a.description LIKE CONCAT('%', #{query.description}, '%') "
            + "</if>"
            + "<if test='query.unitId != null and query.unitId != \"\"'>"
            + "AND a.unit_id = #{query.unitId} "
            + "</if>"
            + "<if test='query.accessScope != null and query.accessScope != \"\"'>"
            + "AND a.access_scope = #{query.accessScope} "
            + "</if>"
            + "<if test='query.status != null'>"
            + "AND a.status = #{query.status} "
            + "</if>"
            + "<if test='query.connectionUrl != null and query.connectionUrl != \"\"'>"
            + "AND a.connection_url LIKE CONCAT('%', #{query.connectionUrl}, '%') "
            + "</if>"
            + "<if test='query.keyword != null and query.keyword != \"\"'>"
            + "AND (a.name LIKE CONCAT('%', #{query.keyword}, '%') OR a.description LIKE CONCAT('%', #{query.keyword}, '%')) "
            + "</if>"
            + "<if test='query.beginTime != null and query.beginTime != \"\"'>"
            + "AND a.create_time >= #{query.beginTime} "
            + "</if>"
            + "<if test='query.endTime != null and query.endTime != \"\"'>"
            + "AND a.create_time &lt;= #{query.endTime} "
            + "</if>"
            + "ORDER BY a.create_time DESC"
            + "</script>")
    @ResultMap("ThirdPlatformVOResult")
    List<ThirdPlatformVO> selectAgentList(@Param("query") ThirdPlatformQueryDTO queryDTO,
                                          @Param("userId") String userId,
                                          @Param("tenantId") String tenantId);

    /**
     * 根据ID查询第三方智能体详情
     *
     * @param id 主键ID
     * @return 第三方智能体VO
     */
    @Select("SELECT "
            + "a.id, a.platform_id, a.name, a.description, a.unit_id, d.dept_name as unit_name, "
            + "a.connection_url, a.api_key, a.timeout, a.access_scope, "
            + "a.authorized_users, a.authorized_units, a.status, a.icon, a.icon_bg, "
            + "a.last_test_time, a.last_test_result, a.last_test_error, "
            + "a.create_by, cu.nickname as create_by_name, a.create_time, "
            + "a.update_by, uu.nickname as update_by_name, a.update_time, a.remark "
            + "FROM third_platform a "
            + "LEFT JOIN sys_dept d ON a.unit_id = d.id "
            + "LEFT JOIN sys_user cu ON a.create_by = cu.id "
            + "LEFT JOIN sys_user uu ON a.update_by = uu.id "
            + "WHERE a.deleted = 0 AND a.id = #{id}")
    @ResultMap("ThirdPlatformVOResult")
    ThirdPlatformVO selectAgentById(@Param("id") String id);

    /**
     * 查询用户可访问的第三方智能体列表
     *
     * @param userId 用户ID
     * @param unitId 用户所属单位ID
     * @return 第三方智能体列表
     */
    @Select("SELECT "
            + "a.id, a.platform_id, a.name, a.description, a.unit_id, d.dept_name as unit_name, "
            + "a.connection_url, a.api_key, a.timeout, a.access_scope, "
            + "a.authorized_users, a.authorized_units, a.status, a.icon, a.icon_bg, "
            + "a.last_test_time, a.last_test_result, a.last_test_error, "
            + "a.create_by, cu.nickname as create_by_name, a.create_time, "
            + "a.update_by, uu.nickname as update_by_name, a.update_time, a.remark "
            + "FROM third_platform a "
            + "LEFT JOIN sys_dept d ON a.unit_id = d.id "
            + "LEFT JOIN sys_user cu ON a.create_by = cu.id "
            + "LEFT JOIN sys_user uu ON a.update_by = uu.id "
            + "WHERE a.deleted = 0 AND a.status = 1 "
            + "AND ("
            + "a.access_scope = 'public' "
            + "OR (a.access_scope = 'personal' AND a.create_by = #{userId}) "
            + "OR (a.access_scope = 'partial_users' AND JSON_CONTAINS(a.authorized_users, JSON_QUOTE(#{userId}))) "
            + "OR (a.access_scope = 'partial_units' AND JSON_CONTAINS(a.authorized_units, JSON_QUOTE(#{unitId}))) "
            + ") "
            + "ORDER BY a.create_time DESC")
    @ResultMap("ThirdPlatformVOResult")
    List<ThirdPlatformVO> selectAccessibleAgents(@Param("userId") String userId, @Param("unitId") String unitId);

    /**
     * 根据访问范围和授权信息查询智能体数量
     *
     * @param accessScope 访问范围
     * @param userId 用户ID
     * @param unitId 单位ID
     * @return 智能体数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) "
            + "FROM third_platform a "
            + "WHERE a.deleted = 0 AND a.status = 1 "
            + "<if test='accessScope != null and accessScope != \"\"'>"
            + "AND a.access_scope = #{accessScope} "
            + "</if>"
            + "<if test='userId != null and userId != \"\"'>"
            + "AND ("
            + "a.access_scope = 'public' "
            + "OR (a.access_scope = 'personal' AND a.create_by = #{userId}) "
            + "OR (a.access_scope = 'partial_users' AND JSON_CONTAINS(a.authorized_users, JSON_QUOTE(#{userId}))) "
            + ") "
            + "</if>"
            + "<if test='unitId != null and unitId != \"\"'>"
            + "AND ("
            + "a.access_scope = 'public' "
            + "OR (a.access_scope = 'partial_units' AND JSON_CONTAINS(a.authorized_units, JSON_QUOTE(#{unitId}))) "
            + ") "
            + "</if>"
            + "</script>")
    Long countByAccessScope(@Param("accessScope") String accessScope, @Param("userId") String userId, @Param("unitId") String unitId);

    /**
     * 批量更新智能体状态
     *
     * @param ids 智能体ID列表
     * @param status 状态
     * @param updateBy 更新者
     * @return 更新数量
     */
    @Update("<script>"
            + "UPDATE third_platform "
            + "SET status = #{status}, update_by = #{updateBy}, update_time = NOW() "
            + "WHERE id IN "
            + "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"
            + "#{id}"
            + "</foreach>"
            + "AND deleted = 0"
            + "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 更新连接测试结果
     *
     * @param id 智能体ID
     * @param testResult 测试结果
     * @param testError 测试错误信息
     * @param updateBy 更新者
     * @return 更新数量
     */
    @Update("UPDATE third_platform "
            + "SET last_test_time = NOW(), last_test_result = #{testResult}, "
            + "last_test_error = #{testError}, update_by = #{updateBy}, update_time = NOW() "
            + "WHERE id = #{id} AND deleted = 0")
    int updateTestResult(@Param("id") String id, @Param("testResult") Integer testResult,
            @Param("testError") String testError, @Param("updateBy") String updateBy);

    /**
     * 根据单位ID查询智能体数量
     *
     * @param unitId 单位ID
     * @return 智能体数量
     */
    @Select("SELECT COUNT(*) FROM third_platform WHERE unit_id = #{unitId} AND deleted = 0")
    Long countByUnitId(@Param("unitId") String unitId);

    /**
     * 根据用户ID查询相关的智能体数量（创建的或被授权的）
     *
     * @param userId 用户ID
     * @return 智能体数量
     */
    @Select("SELECT COUNT(*) FROM third_platform a "
            + "WHERE a.deleted = 0 "
            + "AND ("
            + "a.create_by = #{userId} "
            + "OR (a.access_scope = 'partial_users' AND JSON_CONTAINS(a.authorized_users, JSON_QUOTE(#{userId}))) "
            + ")")
    Long countByUserId(@Param("userId") String userId);
}
