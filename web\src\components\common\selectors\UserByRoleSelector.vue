<template>
  <div class="user-by-role-selector" :class="{ 'is-dropdown': isDropdownMode }">
    <!-- 下拉模式 -->
    <DropdownSelector
      v-if="isDropdownMode"
      v-model="selectedUserIds"
      :placeholder="config.placeholder"
      :disabled="config.disabled"
      :clearable="config.clearable"
      :size="config.size"
      :show-actions="config.multiple"
      :dropdown-class="'user-by-role-dropdown-panel'"
      :align="align"
      @change="handleDropdownChange"
      @clear="clearSelection"
    >
      <template #display>
        <span v-if="!hasSelection" class="placeholder-text">{{ config.placeholder }}</span>
        <div v-else class="selected-display">
          <span v-if="!config.multiple" class="single-selected">
            {{ displayText }}
          </span>
          <div v-else class="multiple-selected">
            <span class="selected-count">{{ displayText }}</span>
          </div>
        </div>
      </template>

      <!-- 下拉内容 -->
      <div class="dropdown-user-by-role-selector">
        <UserByRoleSelectorContent
          ref="contentRef"
          v-model="selectedUserIds"
          :config="config"
          :only-enabled="onlyEnabled"
          :exclude-user-ids="excludeUserIds"
          @change="handleContentChange"
          @select="handleSelect"
          @remove="handleRemove"
          @role-change="handleRoleChange"
        />
      </div>
    </DropdownSelector>

    <!-- 嵌入模式 -->
    <div v-else class="embedded-mode">
      <UserByRoleSelectorContent
        v-model="selectedUserIds"
        :config="config"
        :only-enabled="onlyEnabled"
        :exclude-user-ids="excludeUserIds"
        :show-header="true"
        @change="handleContentChange"
        @select="handleSelect"
        @remove="handleRemove"
        @role-change="handleRoleChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import DropdownSelector from './DropdownSelector.vue'
import UserByRoleSelectorContent from './UserByRoleSelectorContent.vue'
import type { UserSelectorOption, RoleSelectorOption, SelectorConfig } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludeUserIds?: string[]
  mode?: 'dropdown' | 'embedded'
  align?: 'left' | 'right' | 'center'
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], options: UserSelectorOption[]): void
  (e: 'select', value: string, option: UserSelectorOption): void
  (e: 'remove', value: string): void
  (e: 'roleChange', roleId: string, role: RoleSelectorOption): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludeUserIds: () => [],
  mode: 'embedded',
  align: 'left'
})

const emit = defineEmits<Emits>()

// 响应式数据 - 确保每个组件实例都有独立的数据
const selectedUserIds = ref<string | string[]>(
  Array.isArray(props.modelValue) ? [...props.modelValue] : props.modelValue || (props.config.multiple ? [] : '')
)
const selectedUserOptions = ref<UserSelectorOption[]>([])
const contentRef = ref<InstanceType<typeof UserByRoleSelectorContent>>()

// 计算属性
const isDropdownMode = computed(() => props.mode === 'dropdown')

const mergedConfig = computed(() => ({
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择用户',
  size: 'default',
  disabled: false,
  ...props.config
}))

const config = computed(() => mergedConfig.value)

const hasSelection = computed(() => {
  return selectedUserIds.value !== null &&
         selectedUserIds.value !== undefined &&
         selectedUserIds.value !== '' &&
         (Array.isArray(selectedUserIds.value) ? selectedUserIds.value.length > 0 : true)
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

// 计算显示文本
const displayText = computed(() => {
  if (!hasSelection.value) return ''

  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return `已选择 ${selectedUserIds.value.length} 个用户`
  } else {
    const selectedOption = selectedUserOptions.value.find(option =>
      option.value === selectedUserIds.value
    )
    return selectedOption ? selectedOption.label : String(selectedUserIds.value)
  }
})

// 方法
const getSelectedUserLabels = () => {
  if (!hasSelection.value) return []

  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserOptions.value
      .filter(option => selectedUserIds.value.includes(option.value))
      .map(option => option.label)
  } else {
    const selectedOption = selectedUserOptions.value.find(option =>
      option.value === selectedUserIds.value
    )
    return selectedOption ? [selectedOption.label] : []
  }
}

const clearSelection = () => {
  const clearValue = config.value.multiple ? [] : ''
  selectedUserIds.value = clearValue
  emit('update:modelValue', clearValue)
  emit('change', clearValue, [])
}

// 事件处理
const handleDropdownChange = (value: string | string[]) => {
  selectedUserIds.value = value
  emit('update:modelValue', value)
}

const handleContentChange = (value: string | string[], options: UserSelectorOption[]) => {
  selectedUserIds.value = Array.isArray(value) ? [...value] : value
  selectedUserOptions.value = [...options]
  emit('update:modelValue', selectedUserIds.value)
  emit('change', selectedUserIds.value, options)
}

const handleSelect = (value: string, option: UserSelectorOption) => {
  emit('select', value, option)
}

const handleRemove = (value: string) => {
  emit('remove', value)
}

const handleRoleChange = (roleId: string, role: RoleSelectorOption) => {
  emit('roleChange', roleId, role)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedUserIds.value = Array.isArray(newValue) ? [...newValue] : newValue || (config.value.multiple ? [] : '')
  // 清空选项缓存，让子组件重新获取
  if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
    selectedUserOptions.value = []
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  clearSelection
})
</script>

<style scoped>
.user-by-role-selector {
  width: 100%;
}

.user-by-role-selector.is-dropdown {
  display: inline-block;
}

.placeholder-text {
  color: #c0c4cc;
}

.selected-display {
  color: #606266;
}

.single-selected {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multiple-selected {
  display: flex;
  align-items: center;
}

.selected-count {
  font-size: 14px;
  color: #606266;
}

.dropdown-user-by-role-selector {
  min-width: 400px;
}

.embedded-mode {
  width: 100%;
}

/* 下拉面板样式 */
:deep(.user-by-role-dropdown-panel) {
  min-width: 450px;
}

:deep(.user-by-role-dropdown-panel .dropdown-content) {
  padding: 0;
}

:deep(.user-by-role-dropdown-panel .user-by-role-selector-content) {
  border: none;
  box-shadow: none;
}
</style>
