/**
 * 节点管理器
 * 提供统一的节点管理接口，整合注册表和工厂功能
 */

import { NodeRegistry, type NodeRegistration, type NodeCategoryRegistration } from '../registry/NodeRegistry'
import { NodeFactory, type NodeCreateOptions, type NodeCreateResult } from '../factory/NodeFactory'
import { autoRegisterNodes, registerSingleNode } from '../registry/AutoRegister'
import type { NodeLibraryItem } from '../../../config/nodeLibrary'

/**
 * 节点搜索选项
 */
export interface NodeSearchOptions {
  category?: string
  tags?: string[]
  status?: string
  keyword?: string
}

/**
 * 节点管理器类
 */
class NodeManagerClass {
  private initialized = false

  /**
   * 初始化节点管理器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      await autoRegisterNodes()
      this.initialized = true
      console.log('NodeManager initialized successfully')
    } catch (error) {
      console.error('Failed to initialize NodeManager:', error)
      throw error
    }
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized
  }

  // ==================== 节点创建相关 ====================

  /**
   * 创建节点
   */
  createNode(options: NodeCreateOptions): NodeCreateResult | null {
    this.ensureInitialized()
    return NodeFactory.createNode(options)
  }

  /**
   * 批量创建节点
   */
  createNodes(optionsList: NodeCreateOptions[]): NodeCreateResult[] {
    this.ensureInitialized()
    return NodeFactory.createNodes(optionsList)
  }

  /**
   * 创建默认节点
   */
  createDefaultNode(type: string, position?: { x: number; y: number }): NodeCreateResult | null {
    this.ensureInitialized()
    return NodeFactory.createDefaultNode(type, position)
  }

  /**
   * 克隆节点
   */
  cloneNode(sourceNode: NodeCreateResult, position?: { x: number; y: number }): NodeCreateResult | null {
    this.ensureInitialized()
    return NodeFactory.cloneNode(sourceNode, position)
  }

  // ==================== 节点查询相关 ====================

  /**
   * 获取节点配置
   */
  getNodeConfig(type: string): NodeLibraryItem | undefined {
    return NodeRegistry.getNodeConfig(type)
  }

  /**
   * 获取节点组件
   */
  getNodeComponent(type: string): any {
    return NodeRegistry.getNodeComponent(type)
  }

  /**
   * 获取节点注册信息
   */
  getNodeRegistration(type: string): NodeRegistration | undefined {
    return NodeRegistry.getNode(type)
  }

  /**
   * 检查节点是否存在
   */
  hasNode(type: string): boolean {
    return NodeRegistry.hasNode(type)
  }

  /**
   * 获取所有节点类型
   */
  getAllNodeTypes(): string[] {
    return NodeRegistry.getAllNodeTypes()
  }

  /**
   * 根据类别获取节点类型
   */
  getNodesByCategory(category: string): string[] {
    return NodeRegistry.getNodesByCategory(category)
  }

  /**
   * 搜索节点
   */
  searchNodes(options: NodeSearchOptions): string[] {
    let results = this.getAllNodeTypes()

    // 按类别过滤
    if (options.category) {
      results = results.filter(type => {
        const registration = NodeRegistry.getNode(type)
        return registration?.category === options.category
      })
    }

    // 按标签过滤
    if (options.tags && options.tags.length > 0) {
      results = results.filter(type => {
        const config = this.getNodeConfig(type)
        if (!config?.tags) return false
        return options.tags!.some(tag => config.tags.includes(tag))
      })
    }

    // 按状态过滤
    if (options.status) {
      results = results.filter(type => {
        const config = this.getNodeConfig(type)
        return config?.status === options.status
      })
    }

    // 按关键词过滤
    if (options.keyword) {
      const keyword = options.keyword.toLowerCase()
      results = results.filter(type => {
        const config = this.getNodeConfig(type)
        if (!config) return false
        
        return (
          config.label.toLowerCase().includes(keyword) ||
          config.description.toLowerCase().includes(keyword) ||
          config.tags.some(tag => tag.toLowerCase().includes(keyword))
        )
      })
    }

    return results
  }

  // ==================== 类别管理相关 ====================

  /**
   * 获取所有类别
   */
  getAllCategories(): NodeCategoryRegistration[] {
    return NodeRegistry.getAllCategories()
  }

  /**
   * 获取类别信息
   */
  getCategory(name: string): NodeCategoryRegistration | undefined {
    return NodeRegistry.getCategory(name)
  }

  /**
   * 检查类别是否存在
   */
  hasCategory(name: string): boolean {
    return NodeRegistry.hasCategory(name)
  }

  // ==================== 节点注册相关 ====================

  /**
   * 注册单个节点
   */
  registerNode(type: string, category: string, component: any, config: NodeLibraryItem): void {
    registerSingleNode(type, category, component, config)
  }

  /**
   * 注册类别
   */
  registerCategory(category: NodeCategoryRegistration): void {
    NodeRegistry.registerCategory(category)
  }

  /**
   * 取消注册节点
   */
  unregisterNode(type: string): boolean {
    return NodeRegistry.unregisterNode(type)
  }

  /**
   * 取消注册类别
   */
  unregisterCategory(name: string): boolean {
    return NodeRegistry.unregisterCategory(name)
  }

  // ==================== 统计和调试相关 ====================

  /**
   * 获取统计信息
   */
  getStats(): {
    registry: ReturnType<typeof NodeRegistry.getStats>
    factory: ReturnType<typeof NodeFactory.getStats>
    initialized: boolean
  } {
    return {
      registry: NodeRegistry.getStats(),
      factory: NodeFactory.getStats(),
      initialized: this.initialized
    }
  }

  /**
   * 重置工厂计数器
   */
  resetFactoryCounter(): void {
    NodeFactory.resetIdCounter()
  }

  /**
   * 清空所有注册信息
   */
  clear(): void {
    NodeRegistry.clear()
    this.initialized = false
  }

  // ==================== 私有方法 ====================

  /**
   * 确保已初始化
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error('NodeManager not initialized. Call initialize() first.')
    }
  }
}

// 导出单例实例
export const NodeManager = new NodeManagerClass()

// 导出类型
export type { NodeManagerClass }
