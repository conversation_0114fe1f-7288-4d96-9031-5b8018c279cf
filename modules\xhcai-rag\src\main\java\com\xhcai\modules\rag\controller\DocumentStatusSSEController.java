package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.service.DocumentStatusSSEService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * 文档状态SSE推送控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Tag(name = "文档状态SSE推送", description = "文档状态实时推送相关接口")
@RestController
@RequestMapping("/api/rag/document-status-sse")
public class DocumentStatusSSEController {

    @Autowired
    private DocumentStatusSSEService documentStatusSSEService;

    @Operation(summary = "建立SSE连接", description = "建立文档状态推送的SSE连接")
    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @RequiresPermissions("rag:document:list")
    public SseEmitter connect() {
        log.info("建立文档状态SSE连接");
        return documentStatusSSEService.createConnection();
    }

    @Operation(summary = "获取连接统计", description = "获取当前SSE连接的统计信息")
    @GetMapping("/stats")
    @RequiresPermissions("rag:document:list")
    public Result<Map<String, Integer>> getConnectionStats() {
        log.info("获取SSE连接统计信息");
        Map<String, Integer> stats = documentStatusSSEService.getConnectionStats();
        return Result.success(stats);
    }

    @Operation(summary = "关闭连接", description = "关闭当前用户的所有SSE连接")
    @PostMapping("/disconnect")
    @RequiresPermissions("rag:document:list")
    public Result<Void> disconnect() {
        log.info("关闭当前用户的SSE连接");
        // 这里可以通过SecurityUtils获取当前用户信息来关闭连接
        // documentStatusSSEService.closeUserConnections(tenantId, userId);
        return Result.success();
    }
}
