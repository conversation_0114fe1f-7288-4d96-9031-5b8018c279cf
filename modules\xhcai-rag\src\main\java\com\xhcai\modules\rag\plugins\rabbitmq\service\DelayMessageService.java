package com.xhcai.modules.rag.plugins.rabbitmq.service;

import com.xhcai.modules.rag.plugins.rabbitmq.config.RabbitMQProperties;
import com.xhcai.modules.rag.plugins.rabbitmq.exception.RabbitMQException;
import com.xhcai.modules.rag.plugins.rabbitmq.model.RabbitMQMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 延迟消息服务
 * 提供多种延迟消息实现方式
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "xhcai.plugin.types.queue.config", name = "type", havingValue = "rabbitmq")
public class DelayMessageService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitMQProperties rabbitMQProperties;

    /**
     * 发送延迟消息 - 使用消息头方式
     * 适用于有延迟消息插件的RabbitMQ
     *
     * @param message   消息对象
     * @param delayTime 延迟时间（毫秒）
     */
    public void sendDelayMessageWithHeader(RabbitMQMessage message, long delayTime) {
        try {
            message.setStatus(RabbitMQMessage.MessageStatus.SENT);
            
            CorrelationData correlationData = new CorrelationData(message.getMessageId());
            
            rabbitTemplate.convertAndSend(
                    rabbitMQProperties.getExchange().getMain(),
                    message.getMessageType().getRoutingKey(),
                    message,
                    messagePostProcessor -> {
                        MessageProperties properties = messagePostProcessor.getMessageProperties();
                        // 使用x-delay头（需要rabbitmq-delayed-message-exchange插件）
                        properties.setHeader("x-delay", delayTime);
                        properties.setHeader("x-delay-timestamp", System.currentTimeMillis());
                        return messagePostProcessor;
                    },
                    correlationData
            );
            
            log.info("延迟消息发送成功(Header方式): messageId={}, delayTime={}ms", message.getMessageId(), delayTime);
            
        } catch (Exception e) {
            log.error("延迟消息发送失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            throw new RabbitMQException("延迟消息发送失败", e);
        }
    }

    /**
     * 发送延迟消息 - 使用TTL + 死信队列方式
     * 适用于标准RabbitMQ
     *
     * @param message   消息对象
     * @param delayTime 延迟时间（毫秒）
     */
    public void sendDelayMessageWithTTL(RabbitMQMessage message, long delayTime) {
        try {
            message.setStatus(RabbitMQMessage.MessageStatus.SENT);
            
            CorrelationData correlationData = new CorrelationData(message.getMessageId());
            
            // 发送到延迟队列，设置TTL
            rabbitTemplate.convertAndSend(
                    "delay.exchange", // 延迟交换机
                    "delay.queue",    // 延迟队列
                    message,
                    messagePostProcessor -> {
                        MessageProperties properties = messagePostProcessor.getMessageProperties();
                        // 设置消息TTL
                        properties.setExpiration(String.valueOf(delayTime));
                        // 设置原始路由信息
                        properties.setHeader("x-original-exchange", rabbitMQProperties.getExchange().getMain());
                        properties.setHeader("x-original-routing-key", message.getMessageType().getRoutingKey());
                        return messagePostProcessor;
                    },
                    correlationData
            );
            
            log.info("延迟消息发送成功(TTL方式): messageId={}, delayTime={}ms", message.getMessageId(), delayTime);
            
        } catch (Exception e) {
            log.error("延迟消息发送失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            throw new RabbitMQException("延迟消息发送失败", e);
        }
    }

    /**
     * 发送延迟消息 - 使用应用层延迟方式
     * 在应用层实现延迟，适用于任何RabbitMQ版本
     *
     * @param message   消息对象
     * @param delayTime 延迟时间（毫秒）
     */
    @Async
    public CompletableFuture<Void> sendDelayMessageWithAsync(RabbitMQMessage message, long delayTime) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始延迟发送消息: messageId={}, delayTime={}ms", message.getMessageId(), delayTime);
                
                // 等待指定时间
                Thread.sleep(delayTime);
                
                // 发送消息
                message.setStatus(RabbitMQMessage.MessageStatus.SENT);
                CorrelationData correlationData = new CorrelationData(message.getMessageId());
                
                rabbitTemplate.convertAndSend(
                        rabbitMQProperties.getExchange().getMain(),
                        message.getMessageType().getRoutingKey(),
                        message,
                        correlationData
                );
                
                log.info("延迟消息发送成功(异步方式): messageId={}, delayTime={}ms", message.getMessageId(), delayTime);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("延迟消息发送被中断: messageId={}", message.getMessageId(), e);
                throw new RabbitMQException("延迟消息发送被中断", e);
            } catch (Exception e) {
                log.error("延迟消息发送失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
                throw new RabbitMQException("延迟消息发送失败", e);
            }
        });
    }

    /**
     * 发送延迟消息 - 智能选择方式
     * 根据配置和环境自动选择最佳的延迟消息实现方式
     *
     * @param message   消息对象
     * @param delayTime 延迟时间（毫秒）
     */
    public void sendDelayMessageSmart(RabbitMQMessage message, long delayTime) {
        // 对于短延迟（小于1分钟），使用异步方式
        if (delayTime < TimeUnit.MINUTES.toMillis(1)) {
            sendDelayMessageWithAsync(message, delayTime);
            return;
        }
        
        // 对于长延迟，优先使用Header方式（需要插件支持）
        try {
            sendDelayMessageWithHeader(message, delayTime);
        } catch (Exception e) {
            log.warn("Header方式发送失败，尝试TTL方式: {}", e.getMessage());
            try {
                sendDelayMessageWithTTL(message, delayTime);
            } catch (Exception e2) {
                log.warn("TTL方式发送失败，使用异步方式: {}", e2.getMessage());
                sendDelayMessageWithAsync(message, delayTime);
            }
        }
    }

    /**
     * 检查延迟消息插件是否可用
     *
     * @return 是否支持延迟消息插件
     */
    public boolean isDelayPluginAvailable() {
        try {
            // 尝试发送一个测试消息来检查插件是否可用
            // 这里可以实现具体的检查逻辑
            return false; // 默认假设不可用
        } catch (Exception e) {
            log.debug("延迟消息插件检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取推荐的延迟消息实现方式
     *
     * @param delayTime 延迟时间
     * @return 推荐的实现方式描述
     */
    public String getRecommendedDelayMethod(long delayTime) {
        if (delayTime < TimeUnit.SECONDS.toMillis(30)) {
            return "ASYNC - 应用层异步延迟（适合短延迟）";
        } else if (isDelayPluginAvailable()) {
            return "HEADER - 延迟消息插件（推荐）";
        } else {
            return "TTL - TTL + 死信队列（标准方式）";
        }
    }
}
