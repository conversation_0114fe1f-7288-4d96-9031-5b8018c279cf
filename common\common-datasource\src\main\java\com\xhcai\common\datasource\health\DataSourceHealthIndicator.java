package com.xhcai.common.datasource.health;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;

/**
 * 多数据源健康检查指示器 检查所有配置的数据源连接状态
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("genericDataSourceHealthIndicator")
@ConditionalOnClass(HealthIndicator.class)
public class DataSourceHealthIndicator implements HealthIndicator {

    private static final Logger log = LoggerFactory.getLogger(DataSourceHealthIndicator.class);

    @Autowired(required = false)
    private DataSource dataSource;

    @Override
    public Health health() {
        try {
            return checkDataSourceHealth();
        } catch (Exception e) {
            log.error("数据源健康检查失败", e);
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }

    private Health checkDataSourceHealth() {
        Health.Builder builder = Health.up();

        if (dataSource == null) {
            return builder
                    .withDetail("status", "数据源未配置或使用动态数据源")
                    .withDetail("message", "请检查动态数据源配置是否正确")
                    .build();
        }

        // 检查动态数据源
        if (dataSource instanceof DynamicRoutingDataSource) {
            DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) dataSource;
            Map<String, DataSource> dataSources = dynamicDataSource.getDataSources();

            builder.withDetail("totalDataSources", dataSources.size());

            int healthyCount = 0;
            for (Map.Entry<String, DataSource> entry : dataSources.entrySet()) {
                String dsName = entry.getKey();
                DataSource ds = entry.getValue();

                try {
                    checkSingleDataSource(ds);
                    builder.withDetail(dsName, "UP");
                    healthyCount++;
                } catch (Exception e) {
                    builder.withDetail(dsName, "DOWN - " + e.getMessage());
                    log.warn("数据源 {} 连接异常: {}", dsName, e.getMessage());
                }
            }

            builder.withDetail("healthyDataSources", healthyCount);

            // 如果所有数据源都不健康，则整体状态为DOWN
            if (healthyCount == 0) {
                builder.down();
            }
        } else {
            // 单数据源检查
            try {
                checkSingleDataSource(dataSource);
                builder.withDetail("singleDataSource", "UP");
            } catch (Exception e) {
                builder.down().withDetail("singleDataSource", "DOWN - " + e.getMessage());
            }
        }

        return builder.build();
    }

    private void checkSingleDataSource(DataSource dataSource) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (!connection.isValid(5)) {
                throw new SQLException("数据源连接无效");
            }
        }
    }
}
