package com.xhcai.modules.system.service;

import com.xhcai.modules.system.dto.SysPermissionQueryDTO;
import com.xhcai.modules.system.entity.SysPermission;
import com.xhcai.modules.system.vo.SysPermissionVO;
import com.xhcai.modules.system.vo.SysUserVO;

import java.util.List;

/**
 * 菜单管理服务接口
 * 
 * 注意：菜单是权限的一种特殊类型，这里提供菜单相关的便捷方法
 * 实际实现委托给权限服务处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysMenuService {

    /**
     * 查询菜单树
     *
     * @param queryDTO 查询条件
     * @return 菜单树
     */
    List<SysPermissionVO> selectMenuTree(SysPermissionQueryDTO queryDTO);

    /**
     * 根据用户ID查询菜单树
     *
     * @param sysUserVO 用户
     * @return 菜单树
     */
    List<SysPermissionVO> selectMenuTreeByUserId(SysUserVO sysUserVO);

    /**
     * 构建菜单选择树
     *
     * @param excludeMenuId 排除的菜单ID
     * @return 菜单选择树
     */
    List<SysPermissionVO> buildMenuSelectTree(String excludeMenuId);

    /**
     * 创建菜单
     *
     * @param menu 菜单信息
     * @return 是否成功
     */
    boolean insertMenu(SysPermission menu);

    /**
     * 更新菜单
     *
     * @param menu 菜单信息
     * @return 是否成功
     */
    boolean updateMenu(SysPermission menu);

    /**
     * 删除菜单
     *
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    boolean deleteMenus(List<String> menuIds);

    /**
     * 根据角色ID查询菜单权限
     *
     * @param roleId 角色ID
     * @return 菜单权限列表
     */
    List<SysPermissionVO> selectMenuPermissionsByRoleId(String roleId);

    /**
     * 获取所有菜单（用于角色分配）
     *
     * @return 菜单列表
     */
    List<SysPermissionVO> selectAllMenus();
}
