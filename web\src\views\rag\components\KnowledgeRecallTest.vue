<template>
  <div class="knowledge-recall-test">
    <div class="test-modal-content" @click.stop>
      <!-- 主体内容 -->
      <div class="test-body">
        <!-- 左侧配置面板 -->
        <div class="config-panel">
          <div class="config-section">
            <h3 class="section-title">
              <i class="fas fa-vector-square"></i>
              向量模型配置
            </h3>
            <div class="config-group">
              <label class="config-label">向量模型</label>
              <select v-model="testConfig.vectorModel" class="config-select">
                <option value="text-embedding-ada-002">text-embedding-ada-002</option>
                <option value="text-embedding-3-small">text-embedding-3-small</option>
                <option value="text-embedding-3-large">text-embedding-3-large</option>
                <option value="bge-large-zh-v1.5">bge-large-zh-v1.5</option>
                <option value="bge-m3">bge-m3</option>
              </select>
            </div>
            <div class="config-group">
              <label class="config-label">向量维度</label>
              <input 
                type="number" 
                v-model.number="testConfig.vectorDimension" 
                class="config-input"
                min="128"
                max="3072"
                step="1"
              >
            </div>
          </div>

          <div class="config-section">
            <h3 class="section-title">
              <i class="fas fa-sort-amount-down"></i>
              重排序配置
            </h3>
            <div class="config-group">
              <label class="config-label">
                <input 
                  type="checkbox" 
                  v-model="testConfig.enableRerank" 
                  class="config-checkbox"
                >
                启用重排序
              </label>
            </div>
            <div class="config-group" v-show="testConfig.enableRerank">
              <label class="config-label">重排序模型</label>
              <select v-model="testConfig.rerankModel" class="config-select">
                <option value="bge-reranker-large">bge-reranker-large</option>
                <option value="bge-reranker-base">bge-reranker-base</option>
                <option value="cohere-rerank-multilingual-v3.0">cohere-rerank-multilingual-v3.0</option>
              </select>
            </div>
            <div class="config-group" v-show="testConfig.enableRerank">
              <label class="config-label">重排序Top-K</label>
              <input 
                type="number" 
                v-model.number="testConfig.rerankTopK" 
                class="config-input"
                min="1"
                max="100"
                step="1"
              >
            </div>
          </div>

          <div class="config-section">
            <h3 class="section-title">
              <i class="fas fa-search"></i>
              检索配置
            </h3>
            <div class="config-group">
              <label class="config-label">检索数量 (Top-K)</label>
              <input 
                type="number" 
                v-model.number="testConfig.topK" 
                class="config-input"
                min="1"
                max="50"
                step="1"
              >
            </div>
            <div class="config-group">
              <label class="config-label">相似度阈值</label>
              <input 
                type="number" 
                v-model.number="testConfig.similarityThreshold" 
                class="config-input"
                min="0"
                max="1"
                step="0.01"
              >
            </div>
            <div class="config-group">
              <label class="config-label">检索策略</label>
              <select v-model="testConfig.retrievalStrategy" class="config-select">
                <option value="semantic">语义检索</option>
                <option value="keyword">关键词检索</option>
                <option value="hybrid">混合检索</option>
              </select>
            </div>
            <div class="config-group" v-show="testConfig.retrievalStrategy === 'hybrid'">
              <label class="config-label">语义权重</label>
              <input 
                type="number" 
                v-model.number="testConfig.semanticWeight" 
                class="config-input"
                min="0"
                max="1"
                step="0.1"
              >
            </div>
          </div>

          <div class="config-section">
            <h3 class="section-title">
              <i class="fas fa-filter"></i>
              过滤配置
            </h3>
            <div class="config-group">
              <label class="config-label">
                <input 
                  type="checkbox" 
                  v-model="testConfig.enableMetadataFilter" 
                  class="config-checkbox"
                >
                启用元数据过滤
              </label>
            </div>
            <div class="config-group" v-show="testConfig.enableMetadataFilter">
              <label class="config-label">文件类型过滤</label>
              <div class="checkbox-group">
                <label v-for="fileType in fileTypes" :key="fileType.value" class="checkbox-item">
                  <input 
                    type="checkbox" 
                    :value="fileType.value"
                    v-model="testConfig.fileTypeFilter"
                    class="config-checkbox"
                  >
                  {{ fileType.label }}
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧测试面板 -->
        <div class="test-panel">
          <!-- 查询输入 -->
          <div class="query-section">
            <div class="query-header">
              <h3 class="section-title">
                <i class="fas fa-question-circle"></i>
                测试查询
              </h3>
              <div class="query-actions">
                <button class="btn btn-outline" @click="resetToDefault">
                  <i class="fas fa-undo"></i>
                  重置默认
                </button>
                <button class="btn btn-primary" @click="applyCurrentConfig" :disabled="!hasConfigChanges">
                  <i class="fas fa-check"></i>
                  采用当前配置
                </button>
                <button class="btn btn-sm btn-outline" @click="clearQuery">
                  <i class="fas fa-eraser"></i>
                  清空
                </button>
                <button class="btn btn-sm btn-primary" @click="runTest" :disabled="!testQuery.trim() || testing">
                  <i class="fas fa-play" v-if="!testing"></i>
                  <i class="fas fa-spinner fa-spin" v-else></i>
                  {{ testing ? '测试中...' : '开始测试' }}
                </button>
              </div>
            </div>
            <textarea 
              v-model="testQuery"
              class="query-input"
              placeholder="请输入测试查询内容，例如：如何使用向量数据库进行语义检索？"
              rows="4"
            ></textarea>
          </div>

          <!-- 测试结果 -->
          <div class="results-section">
            <div class="results-header">
              <h3 class="section-title">
                <i class="fas fa-list-ol"></i>
                召回结果
                <span class="result-count" v-if="testResults.length > 0">
                  ({{ testResults.length }} 条)
                </span>
              </h3>
              <div class="results-actions" v-if="testResults.length > 0">
                <button class="btn btn-sm btn-outline" @click="exportResults">
                  <i class="fas fa-download"></i>
                  导出结果
                </button>
              </div>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" v-if="!testing && testResults.length === 0">
              <div class="empty-icon">🔍</div>
              <p class="empty-text">输入查询内容并点击"开始测试"来查看召回结果</p>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state" v-if="testing">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <p class="loading-text">正在执行召回测试...</p>
            </div>

            <!-- 结果列表 -->
            <div class="results-list" v-if="!testing && testResults.length > 0">
              <div 
                v-for="(result, index) in testResults" 
                :key="index"
                class="result-item"
                :class="{ 'high-score': result.score >= 0.8, 'medium-score': result.score >= 0.6 && result.score < 0.8 }"
              >
                <div class="result-header">
                  <div class="result-rank">{{ index + 1 }}</div>
                  <div class="result-score">
                    <span class="score-value">{{ (result.score * 100).toFixed(1) }}%</span>
                    <div class="score-bar">
                      <div class="score-fill" :style="{ width: (result.score * 100) + '%' }"></div>
                    </div>
                  </div>
                  <div class="result-actions">
                    <button class="btn-icon" @click="viewFullContent(result)" title="查看完整内容">
                      <i class="fas fa-expand"></i>
                    </button>
                  </div>
                </div>
                <div class="result-content">
                  <div class="result-meta">
                    <span class="file-name">{{ result.fileName }}</span>
                    <span class="chunk-info">第 {{ result.chunkIndex }} 段</span>
                    <span class="file-type">{{ result.fileType.toUpperCase() }}</span>
                  </div>
                  <div class="result-text">{{ result.content }}</div>
                  <div class="result-metadata" v-if="result.metadata">
                    <span class="metadata-item" v-for="(value, key) in result.metadata" :key="key">
                      {{ key }}: {{ value }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 全文查看模态框 -->
    <div class="full-content-modal" v-show="fullContentVisible" @click="closeFullContent">
      <div class="full-content-modal-content" @click.stop>
        <div class="full-content-header">
          <h3>完整内容</h3>
          <button class="close-btn" @click="closeFullContent">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="full-content-body">
          <div class="content-meta">
            <span class="file-name">{{ selectedResult?.fileName }}</span>
            <span class="chunk-info">第 {{ selectedResult?.chunkIndex }} 段</span>
            <span class="score">相似度: {{ selectedResult ? (selectedResult.score * 100).toFixed(1) : 0 }}%</span>
          </div>
          <div class="content-text">{{ selectedResult?.fullContent || selectedResult?.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  visible?: boolean
  datasetId?: string
  knowledgeBase?: any
  onClose?: () => void
  onConfigApplied?: (config: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  datasetId: '',
  knowledgeBase: null
})

// Emits
const emit = defineEmits<{
  close: []
  configApplied: [config: any]
}>()

// 响应式数据
const testQuery = ref('')
const testing = ref(false)
const testResults = ref<any[]>([])
const fullContentVisible = ref(false)
const selectedResult = ref<any>(null)

// 测试配置
const testConfig = ref({
  // 向量模型配置
  vectorModel: 'text-embedding-ada-002',
  vectorDimension: 1536,

  // 重排序配置
  enableRerank: true,
  rerankModel: 'bge-reranker-large',
  rerankTopK: 20,

  // 检索配置
  topK: 10,
  similarityThreshold: 0.6,
  retrievalStrategy: 'semantic',
  semanticWeight: 0.7,

  // 过滤配置
  enableMetadataFilter: false,
  fileTypeFilter: [] as string[]
})

// 原始配置（用于重置和比较）
const originalConfig = ref<any>(null)

// 文件类型选项
const fileTypes = [
  { value: 'pdf', label: 'PDF' },
  { value: 'doc', label: 'Word' },
  { value: 'txt', label: '文本' },
  { value: 'md', label: 'Markdown' },
  { value: 'xlsx', label: 'Excel' }
]

// 计算属性
const hasConfigChanges = computed(() => {
  if (!originalConfig.value) return false
  return JSON.stringify(testConfig.value) !== JSON.stringify(originalConfig.value)
})

const resetToDefault = () => {
  if (originalConfig.value) {
    testConfig.value = JSON.parse(JSON.stringify(originalConfig.value))
  }
}

const applyCurrentConfig = () => {
  if (hasConfigChanges.value) {
    const config = JSON.parse(JSON.stringify(testConfig.value))
    emit('configApplied', config)
    props.onConfigApplied?.(config)
    originalConfig.value = JSON.parse(JSON.stringify(testConfig.value))
  }
}

const clearQuery = () => {
  testQuery.value = ''
  testResults.value = []
}

const runTest = async () => {
  if (!testQuery.value.trim() || testing.value) return

  testing.value = true
  testResults.value = []

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟测试结果
    testResults.value = generateMockResults()
  } catch (error) {
    console.error('召回测试失败:', error)
  } finally {
    testing.value = false
  }
}

const generateMockResults = () => {
  const mockResults = [
    {
      fileName: '向量数据库技术指南.pdf',
      chunkIndex: 3,
      fileType: 'pdf',
      content: '向量数据库是一种专门用于存储和检索高维向量数据的数据库系统。它通过计算向量之间的相似度来实现语义检索，广泛应用于推荐系统、搜索引擎和人工智能领域。',
      fullContent: '向量数据库是一种专门用于存储和检索高维向量数据的数据库系统。它通过计算向量之间的相似度来实现语义检索，广泛应用于推荐系统、搜索引擎和人工智能领域。向量数据库的核心优势在于能够处理非结构化数据，如文本、图像和音频，将其转换为数值向量进行存储和检索。',
      score: 0.92,
      metadata: {
        author: '张三',
        category: '技术文档',
        tags: '向量数据库,AI,检索'
      }
    },
    {
      fileName: '机器学习实战.docx',
      chunkIndex: 15,
      fileType: 'doc',
      content: '语义检索是基于内容语义理解的信息检索技术，它不仅仅匹配关键词，而是理解查询意图和文档内容的语义关系。',
      score: 0.85,
      metadata: {
        author: '李四',
        category: '学习资料'
      }
    },
    {
      fileName: 'AI技术发展趋势.txt',
      chunkIndex: 8,
      fileType: 'txt',
      content: '现代搜索引擎正在从传统的关键词匹配向语义理解转变，利用深度学习模型来理解用户查询的真实意图。',
      score: 0.78,
      metadata: {
        category: '行业报告'
      }
    }
  ]

  return mockResults.slice(0, testConfig.value.topK)
}

const viewFullContent = (result: any) => {
  selectedResult.value = result
  fullContentVisible.value = true
}

const closeFullContent = () => {
  fullContentVisible.value = false
  selectedResult.value = null
}

const exportResults = () => {
  const data = {
    query: testQuery.value,
    config: testConfig.value,
    results: testResults.value,
    timestamp: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `recall-test-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 生命周期
onMounted(() => {
  // 初始化原始配置
  originalConfig.value = JSON.parse(JSON.stringify(testConfig.value))
})

// 监听配置变化
watch(() => testConfig.value.vectorModel, (newModel) => {
  // 根据模型自动调整向量维度
  const dimensionMap: Record<string, number> = {
    'text-embedding-ada-002': 1536,
    'text-embedding-3-small': 1536,
    'text-embedding-3-large': 3072,
    'bge-large-zh-v1.5': 1024,
    'bge-m3': 1024
  }

  if (dimensionMap[newModel]) {
    testConfig.value.vectorDimension = dimensionMap[newModel]
  }
})
</script>

<style scoped>
.test-modal-content {
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 头部样式 */
.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.kb-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.test-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.test-subtitle {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline {
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #94a3b8;
  cursor: not-allowed;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

/* 主体内容样式 */
.test-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.config-panel {
  width: 350px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  overflow-y: auto;
  padding: 20px;
}

.config-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.config-group {
  margin-bottom: 16px;
}

.config-label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.config-select,
.config-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.config-select:focus,
.config-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.config-checkbox {
  margin-right: 8px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #374151;
}

/* 测试面板样式 */
.test-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.query-section {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.query-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.query-actions {
  display: flex;
  gap: 8px;
}

.query-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
}

.query-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 12px 20px;
}

.result-count {
  font-size: 14px;
  color: #64748b;
  font-weight: normal;
}

.results-actions {
  display: flex;
  gap: 8px;
}

/* 空状态和加载状态 */
.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text,
.loading-text {
  font-size: 16px;
  text-align: center;
}

.loading-spinner {
  font-size: 24px;
  margin-bottom: 16px;
  color: #3b82f6;
}

/* 结果列表样式 */
.results-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 20px 20px;
}

.result-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.result-item.high-score {
  border-left: 4px solid #10b981;
}

.result-item.medium-score {
  border-left: 4px solid #f59e0b;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.result-rank {
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.result-score {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  min-width: 50px;
}

.score-bar {
  flex: 1;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
  max-width: 100px;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #3b82f6 50%, #8b5cf6 100%);
  transition: width 0.3s ease;
}

.result-actions {
  display: flex;
  gap: 4px;
}

.btn-icon {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: #e2e8f0;
  color: #374151;
}

.result-content {
  padding: 16px;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 12px;
}

.file-name {
  font-weight: 600;
  color: #1e293b;
}

.chunk-info,
.file-type {
  color: #64748b;
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
}

.result-text {
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  margin-bottom: 8px;
}

.result-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.metadata-item {
  font-size: 11px;
  color: #64748b;
  background: #f8fafc;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #e2e8f0;
}

/* 全文查看模态框 */
.full-content-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.full-content-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
}

.full-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.full-content-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.full-content-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.content-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
}

.content-meta .file-name {
  font-weight: 600;
  color: #1e293b;
}

.content-meta .chunk-info,
.content-meta .score {
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
}

.content-text {
  font-size: 15px;
  line-height: 1.7;
  color: #374151;
  white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .test-modal-content {
    width: 98%;
    height: 95%;
  }

  .config-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .test-body {
    flex-direction: column;
  }

  .config-panel {
    width: 100%;
    max-height: 300px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .query-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
