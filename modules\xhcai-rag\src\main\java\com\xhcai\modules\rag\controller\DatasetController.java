package com.xhcai.modules.rag.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.DatasetCreateDTO;
import com.xhcai.modules.rag.dto.DatasetQueryDTO;
import com.xhcai.modules.rag.entity.Dataset;
import com.xhcai.modules.rag.service.IDatasetService;
import com.xhcai.modules.rag.vo.DatasetVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 知识库管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "知识库管理", description = "知识库的创建、查询、更新、删除等操作")
@RestController
@RequestMapping("/api/rag/datasets")
@Validated
public class DatasetController {

    private static final Logger log = LoggerFactory.getLogger(DatasetController.class);

    @Autowired
    private IDatasetService datasetService;

    @Operation(summary = "分页查询知识库列表", description = "根据条件分页查询知识库列表")
    @GetMapping("/page")
    @RequiresPermissions("rag:dataset:list")
    public Result<PageResult<DatasetVO>> page(@Parameter(description = "查询条件") DatasetQueryDTO queryDTO) {
        log.info("分页查询知识库列表: {}", queryDTO);

        IPage<DatasetVO> page = datasetService.pageByTenantWithUserInfo(
                queryDTO.getCurrent(),
                queryDTO.getSize(),
                queryDTO.getTenantId(),
                queryDTO.getName(),
                queryDTO.getDataSourceType()
        );

        PageResult<DatasetVO> pageResult = new PageResult<>(
                page.getRecords(),
                page.getTotal(),
                page.getCurrent(),
                page.getSize()
        );

        return Result.success(pageResult);
    }

    @Operation(summary = "查询知识库列表", description = "查询指定租户的所有知识库")
    @GetMapping("/list")
    @RequiresPermissions("rag:dataset:list")
    public Result<List<DatasetVO>> list(@Parameter(description = "租户ID") @RequestParam String tenantId) {
        log.info("查询知识库列表: tenantId={}", tenantId);

        List<DatasetVO> datasets = datasetService.listByTenantId(tenantId);

        return Result.success(datasets);
    }

    @Operation(summary = "查询知识库详情", description = "根据ID查询知识库详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("rag:dataset:detail")
    public Result<DatasetVO> getById(@Parameter(description = "知识库ID") @PathVariable String id) {
        log.info("查询知识库详情: id={}", id);

        Dataset dataset = datasetService.getDatasetById(id);
        if (dataset == null) {
            return Result.fail("知识库不存在");
        }

        DatasetVO vo = new DatasetVO();
        BeanUtils.copyProperties(dataset, vo);
        return Result.success(vo);
    }

    @Operation(summary = "创建知识库", description = "创建新的知识库")
    @PostMapping
    @RequiresPermissions("rag:dataset:create")
    public Result<DatasetVO> create(@Parameter(description = "知识库信息") @RequestBody @Validated DatasetCreateDTO createDTO) {
        log.info("创建知识库: {}", createDTO);

        Dataset dataset = new Dataset();
        BeanUtils.copyProperties(createDTO, dataset);

        // 从当前用户上下文获取租户ID
        String tenantId = SecurityUtils.getCurrentTenantId();
        if (tenantId == null) {
            return Result.fail("获取租户信息失败，请重新登录");
        }
        dataset.setTenantId(tenantId);

        Dataset createdDataset = datasetService.createDataset(dataset);
        DatasetVO vo = new DatasetVO();
        BeanUtils.copyProperties(createdDataset, vo);

        return Result.success(vo);
    }

    @Operation(summary = "更新知识库", description = "更新知识库信息")
    @PutMapping("/{id}")
    @RequiresPermissions("rag:dataset:update")
    public Result<DatasetVO> update(
            @Parameter(description = "知识库ID") @PathVariable String id,
            @Parameter(description = "知识库信息") @RequestBody @Validated DatasetCreateDTO updateDTO) {
        log.info("更新知识库: id={}, data={}", id, updateDTO);

        Dataset dataset = new Dataset();
        BeanUtils.copyProperties(updateDTO, dataset);
        dataset.setId(id);

        Dataset updatedDataset = datasetService.updateDataset(dataset);

        DatasetVO vo = new DatasetVO();
        BeanUtils.copyProperties(updatedDataset, vo);
        return Result.success(vo);
    }

    @Operation(summary = "删除知识库", description = "删除指定的知识库")
    @DeleteMapping("/{id}")
    @RequiresPermissions("rag:dataset:delete")
    public Result<Void> delete(@Parameter(description = "知识库ID") @PathVariable String id) {
        log.info("删除知识库: id={}", id);

        boolean success = datasetService.deleteDataset(id);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("删除失败");
        }
    }

    @Operation(summary = "检查知识库名称", description = "检查知识库名称是否已存在")
    @GetMapping("/check-name")
    @RequiresPermissions("rag:dataset:create")
    public Result<Boolean> checkName(
            @Parameter(description = "知识库名称") @RequestParam String name,
            @Parameter(description = "租户ID") @RequestParam String tenantId,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String excludeId) {
        log.info("检查知识库名称: name={}, tenantId={}, excludeId={}", name, tenantId, excludeId);

        boolean exists = datasetService.existsByName(name, tenantId, excludeId);
        return Result.success(!exists);
    }

    @Operation(summary = "统计知识库数量", description = "统计指定租户的知识库数量")
    @GetMapping("/count")
    @RequiresPermissions("rag:dataset:list")
    public Result<Long> count(@Parameter(description = "租户ID") @RequestParam String tenantId) {
        log.info("统计知识库数量: tenantId={}", tenantId);

        Long count = datasetService.countByTenantId(tenantId);
        return Result.success(count);
    }

    @Operation(summary = "获取统计信息", description = "获取知识库的统计信息")
    @GetMapping("/{id}/stats")
    @RequiresPermissions("rag:dataset:detail")
    public Result<Object> getStats(@Parameter(description = "知识库ID") @PathVariable String id) {
        log.info("获取知识库统计信息: id={}", id);

        Object stats = datasetService.getDatasetStats(id);
        return Result.success(stats);
    }

    @Operation(summary = "复制知识库", description = "复制现有知识库")
    @PostMapping("/{id}/copy")
    @RequiresPermissions("rag:dataset:create")
    public Result<DatasetVO> copy(
            @Parameter(description = "源知识库ID") @PathVariable String id,
            @Parameter(description = "新知识库名称") @RequestParam String newName,
            @Parameter(description = "租户ID") @RequestParam String tenantId) {
        log.info("复制知识库: sourceId={}, newName={}, tenantId={}", id, newName, tenantId);

        // 从当前用户上下文获取用户ID
        String userId = SecurityUtils.getCurrentUserId();

        Dataset copiedDataset = datasetService.copyDataset(id, newName, tenantId, userId);
        DatasetVO vo = new DatasetVO();
        BeanUtils.copyProperties(copiedDataset, vo);

        return Result.success(vo);
    }

    @Operation(summary = "导出配置", description = "导出知识库配置")
    @GetMapping("/{id}/export")
    @RequiresPermissions("rag:dataset:detail")
    public Result<Object> exportConfig(@Parameter(description = "知识库ID") @PathVariable String id) {
        log.info("导出知识库配置: id={}", id);

        Object config = datasetService.exportDatasetConfig(id);
        return Result.success(config);
    }

    @Operation(summary = "导入配置", description = "导入知识库配置")
    @PostMapping("/import")
    @RequiresPermissions("rag:dataset:create")
    public Result<DatasetVO> importConfig(
            @Parameter(description = "配置信息") @RequestBody Object config,
            @Parameter(description = "租户ID") @RequestParam String tenantId) {
        log.info("导入知识库配置: tenantId={}", tenantId);

        // 从当前用户上下文获取用户ID
        String userId = SecurityUtils.getCurrentUserId();

        Dataset importedDataset = datasetService.importDatasetConfig(config, tenantId, userId);
        DatasetVO vo = new DatasetVO();
        BeanUtils.copyProperties(importedDataset, vo);

        return Result.success(vo);
    }
}
