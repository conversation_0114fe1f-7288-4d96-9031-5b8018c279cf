/**
 * 工作流管理相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

// Vue Flow节点接口
export interface VueFlowNode {
  id: string
  type: string
  position: { x: number; y: number }
  data: Record<string, any>
  [key: string]: any
}

// Vue Flow边接口
export interface VueFlowEdge {
  id: string
  source: string
  target: string
  type?: string
  sourceHandle?: string
  targetHandle?: string
  [key: string]: any
}

// 视口配置接口
export interface ViewportConfig {
  x: number
  y: number
  zoom: number
}

// 节点库配置接口
export interface NodeLibraryConfig {
  categories: Array<{
    name: string
    nodes: Array<{
      type: string
      label: string
      icon?: string
      description?: string
      defaultData?: Record<string, any>
    }>
  }>
}

// 工作流配置接口
export interface WorkflowConfig {
  agentId: string
  version: number
  nodes: VueFlowNode[]
  edges: VueFlowEdge[]
  viewport: ViewportConfig
  nodeLibrary: NodeLibraryConfig
  globalVariables: Record<string, any>
  lastModified: number
  isPublished: boolean
}

// 工作流信息接口
export interface WorkflowInfo {
  id: string
  agentId: string
  agentName?: string
  name: string
  description?: string
  version: number
  nodesData?: string
  edgesData?: string
  viewportConfig?: string
  nodeLibrary?: string
  globalVariables?: string
  status: string
  statusName?: string
  isPublished: boolean
  publishedAt?: string
  lastModified?: string
  operationType?: string
  operationDesc?: string
  createTime?: string
  updateTime?: string
  createBy?: string
  updateBy?: string
  remark?: string
}

// 版本历史接口
export interface WorkflowVersion {
  id: string
  agentId: string
  version: number
  name: string
  description?: string
  isPublished: boolean
  publishedAt?: string
  lastModified?: string
  operationType?: string
  operationDesc?: string
  createTime?: string
  createBy?: string
  isCurrent: boolean
  nodeCount?: number
  connectionCount?: number
}

// 查询条件接口
export interface WorkflowQueryParams {
  pageNum?: number
  pageSize?: number
  agentId?: string
  name?: string
  version?: number
  status?: string
  isPublished?: boolean
  operationType?: string
  startTime?: string
  endTime?: string
  latestVersion?: boolean
}

// 创建工作流接口
export interface CreateWorkflowParams {
  agentId: string
  name: string
  description?: string
  nodesData?: string
  edgesData?: string
  viewportConfig?: string
  nodeLibrary?: string
  globalVariables?: string
  operationType?: string
  operationDesc?: string
}

// 更新工作流接口
export interface UpdateWorkflowParams {
  id: string
  agentId: string
  name?: string
  description?: string
  nodesData?: string
  edgesData?: string
  viewportConfig?: string
  nodeLibrary?: string
  globalVariables?: string
  operationType?: string
  operationDesc?: string
  createNewVersion?: boolean
}



// 统计信息接口
export interface WorkflowStats {
  totalCount: number
  versionCount: number
  maxVersion: number
}

// 工作流历史记录接口
export interface WorkflowHistory {
  id: string
  workflowId: string
  agentId: string
  configHash: string
  name: string
  description?: string
  version: number
  workflowConfig?: string
  nodesConfig?: string
  connectionsConfig?: string
  globalVariables?: string
  operationType: string
  operationDesc?: string
  changeSummary?: string
  operationTime: string
  operationUserId?: string
  operationUserName?: string
  isMajorChange: boolean
  configSize?: number
  tenantId: string
  createTime: string
  createBy?: string
  updateTime?: string
  updateBy?: string
}

// 历史记录查询参数接口
export interface WorkflowHistoryQueryParams {
  pageNum?: number
  pageSize?: number
  workflowId?: string
  agentId?: string
  configHash?: string
  operationType?: string
  operationUserId?: string
  operationUserName?: string
  isMajorChange?: boolean
  version?: number
  keyword?: string
  operationStartTime?: string
  operationEndTime?: string
  startTime?: string
  endTime?: string
  sortField?: string
  sortOrder?: string
}

// 历史记录统计信息接口
export interface WorkflowHistoryStats {
  totalCount: number
  majorChangeCount: number
  firstOperationTime?: string
  lastOperationTime?: string
  totalConfigSize: number
  uniqueOperatorCount: number
}

// 历史记录对比结果接口
export interface WorkflowHistoryCompare {
  fromHistory: WorkflowHistory
  toHistory: WorkflowHistory
  differences: string[]
  summary: string
}

/**
 * 分页查询工作流列表
 */
export function getWorkflowPage(params: WorkflowQueryParams): Promise<ApiResponse<PaginatedResponse<WorkflowInfo>>> {
  return apiClient.post('/api/agent/workflow/page', params)
}

/**
 * 根据ID查询工作流详情
 */
export function getWorkflowById(id: string): Promise<ApiResponse<WorkflowInfo>> {
  return apiClient.get(`/api/agent/workflow/${id}`)
}

/**
 * 根据智能体ID查询最新版本工作流
 */
export function getLatestWorkflowByAgentId(agentId: string): Promise<ApiResponse<WorkflowInfo>> {
  return apiClient.get(`/api/agent/workflow/latest/${agentId}`)
}

/**
 * 创建工作流
 */
export function createWorkflow(params: CreateWorkflowParams): Promise<ApiResponse<string>> {
  return apiClient.post('/api/agent/workflow', params)
}

/**
 * 更新工作流
 */
export function updateWorkflow(params: UpdateWorkflowParams): Promise<ApiResponse<boolean>> {
  return apiClient.put('/api/agent/workflow', params)
}



/**
 * 删除工作流
 */
export function deleteWorkflow(id: string): Promise<ApiResponse<boolean>> {
  return apiClient.delete(`/api/agent/workflow/${id}`)
}

/**
 * 批量删除工作流
 */
export function deleteWorkflowBatch(ids: string[]): Promise<ApiResponse<boolean>> {
  return apiClient.delete('/api/agent/workflow/batch', { data: ids })
}

/**
 * 发布工作流
 */
export function publishWorkflow(id: string): Promise<ApiResponse<boolean>> {
  return apiClient.put(`/api/agent/workflow/${id}/publish`)
}

/**
 * 取消发布工作流
 */
export function unpublishWorkflow(id: string): Promise<ApiResponse<boolean>> {
  return apiClient.put(`/api/agent/workflow/${id}/unpublish`)
}

/**
 * 查询工作流版本历史
 */
export function getVersionHistory(agentId: string): Promise<ApiResponse<WorkflowVersion[]>> {
  return apiClient.get(`/api/agent/workflow/versions/${agentId}`)
}

/**
 * 回滚到指定版本
 */
export function rollbackToVersion(agentId: string, version: number): Promise<ApiResponse<boolean>> {
  return apiClient.post('/api/agent/workflow/rollback', { agentId, version })
}

/**
 * 复制工作流到新版本
 */
export function copyToNewVersion(sourceId: string, operationDesc?: string): Promise<ApiResponse<string>> {
  const params = operationDesc ? `?operationDesc=${encodeURIComponent(operationDesc)}` : ''
  return apiClient.post(`/api/agent/workflow/copy/${sourceId}${params}`)
}

/**
 * 导出工作流配置
 */
export function exportWorkflowConfig(id: string): Promise<ApiResponse<string>> {
  return apiClient.get(`/api/agent/workflow/${id}/export`)
}

/**
 * 导入工作流配置
 */
export function importWorkflowConfig(agentId: string, configJson: string): Promise<ApiResponse<string>> {
  return apiClient.post('/api/agent/workflow/import', { agentId, configJson })
}

/**
 * 获取工作流统计信息
 */
export function getWorkflowStats(agentId: string): Promise<ApiResponse<WorkflowStats>> {
  return apiClient.get(`/api/agent/workflow/stats/${agentId}`)
}

/**
 * 获取节点库配置
 */
export function getNodeLibrary(): Promise<ApiResponse<string>> {
  return apiClient.get('/api/agent/workflow/node-library')
}

/**
 * 实时保存工作流配置
 */
export function realtimeSaveWorkflow(params: {
  agentId: string
  nodesData?: string
  edgesData?: string
  viewportConfig?: string
  nodeLibrary?: string
  globalVariables?: string
  operationType?: string
  operationDesc?: string
}): Promise<ApiResponse<string>> {
  // 直接使用新的API接口
  return apiClient.post('/api/agent/workflow/save-realtime', {
    agentId: params.agentId,
    nodesData: params.nodesData,
    edgesData: params.edgesData,
    viewportConfig: params.viewportConfig,
    nodeLibrary: params.nodeLibrary,
    globalVariables: params.globalVariables,
    operationType: params.operationType,
    operationDesc: params.operationDesc
  })
}

/**
 * 分页查询工作流历史记录
 */
export function getWorkflowHistoryPage(params: WorkflowHistoryQueryParams): Promise<ApiResponse<PaginatedResponse<WorkflowHistory>>> {
  return apiClient.post('/api/agent/workflow/history/page', params)
}

/**
 * 根据工作流ID查询历史记录
 */
export function getWorkflowHistoryByWorkflowId(workflowId: string): Promise<ApiResponse<WorkflowHistory[]>> {
  return apiClient.get(`/api/agent/workflow/history/workflow/${workflowId}`)
}

/**
 * 根据智能体ID查询历史记录
 */
export function getWorkflowHistoryByAgentId(agentId: string): Promise<ApiResponse<WorkflowHistory[]>> {
  return apiClient.get(`/api/agent/workflow/history/agent/${agentId}`)
}

/**
 * 根据配置哈希值查询历史记录
 */
export function getWorkflowHistoryByConfigHash(configHash: string): Promise<ApiResponse<WorkflowHistory>> {
  return apiClient.get(`/api/agent/workflow/history/config/${configHash}`)
}

/**
 * 查询最近的历史记录
 */
export function getRecentWorkflowHistory(workflowId: string, limit: number = 10): Promise<ApiResponse<WorkflowHistory[]>> {
  return apiClient.get(`/api/agent/workflow/history/recent/${workflowId}?limit=${limit}`)
}

/**
 * 查询重要变更历史
 */
export function getMajorWorkflowChanges(workflowId: string): Promise<ApiResponse<WorkflowHistory[]>> {
  return apiClient.get(`/api/agent/workflow/history/major-changes/${workflowId}`)
}

/**
 * 查询用户操作历史
 */
export function getWorkflowHistoryByUser(workflowId: string, userId: string): Promise<ApiResponse<WorkflowHistory[]>> {
  return apiClient.get(`/api/agent/workflow/history/user/${workflowId}/${userId}`)
}

/**
 * 查询操作类型历史
 */
export function getWorkflowHistoryByOperationType(workflowId: string, operationType: string): Promise<ApiResponse<WorkflowHistory[]>> {
  return apiClient.get(`/api/agent/workflow/history/operation/${workflowId}?operationType=${operationType}`)
}

/**
 * 对比历史记录
 */
export function compareWorkflowHistory(fromHistoryId: string, toHistoryId: string): Promise<ApiResponse<WorkflowHistoryCompare>> {
  return apiClient.get(`/api/agent/workflow/history/compare?fromHistoryId=${fromHistoryId}&toHistoryId=${toHistoryId}`)
}

// ==================== 工作流执行相关接口 ====================

// 执行请求参数
export interface WorkflowExecutionRequest {
  agentId: string
  workflowId?: string
  mode?: 'normal' | 'debug' | 'step'
  globalVariables?: Record<string, any>
  breakpoints?: string[]
}

// 执行响应
export interface WorkflowExecutionResponse {
  executionId: string
  status: 'started' | 'running' | 'paused' | 'completed' | 'error' | 'cancelled'
  startTime: number
  message?: string
}

// 执行状态
export interface WorkflowExecutionStatus {
  executionId: string
  status: 'idle' | 'running' | 'paused' | 'completed' | 'error' | 'cancelled'
  progress: number
  currentNodeId?: string
  currentNodeName?: string
  startTime: number
  endTime?: number
  duration?: number
  metrics: {
    totalNodes: number
    completedNodes: number
    errorNodes: number
    skippedNodes: number
  }
  steps: ExecutionStep[]
}

// 执行步骤
export interface ExecutionStep {
  nodeId: string
  nodeName: string
  nodeType: string
  status: 'pending' | 'waiting' | 'running' | 'completed' | 'error' | 'skipped'
  startTime?: number
  endTime?: number
  duration?: number
  progress?: number
  input?: any
  output?: any
  error?: string
  logs: ExecutionLog[]
}

// 执行日志
export interface ExecutionLog {
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  timestamp: number
}

/**
 * 启动工作流执行
 */
export function startWorkflowExecution(request: WorkflowExecutionRequest): Promise<ApiResponse<WorkflowExecutionResponse>> {
  return apiClient.post('/api/agent/workflow/execution/start', request)
}

/**
 * 暂停工作流执行
 */
export function pauseWorkflowExecution(executionId: string): Promise<ApiResponse<boolean>> {
  return apiClient.post(`/api/agent/workflow/execution/${executionId}/pause`)
}

/**
 * 恢复工作流执行
 */
export function resumeWorkflowExecution(executionId: string): Promise<ApiResponse<boolean>> {
  return apiClient.post(`/api/agent/workflow/execution/${executionId}/resume`)
}

/**
 * 停止工作流执行
 */
export function stopWorkflowExecution(executionId: string): Promise<ApiResponse<boolean>> {
  return apiClient.post(`/api/agent/workflow/execution/${executionId}/stop`)
}

/**
 * 获取工作流执行状态
 */
export function getWorkflowExecutionStatus(executionId: string): Promise<ApiResponse<WorkflowExecutionStatus>> {
  return apiClient.get(`/api/agent/workflow/execution/${executionId}/status`)
}

/**
 * 获取工作流执行日志
 */
export function getWorkflowExecutionLogs(executionId: string): Promise<ApiResponse<ExecutionStep[]>> {
  return apiClient.get(`/api/agent/workflow/execution/${executionId}/logs`)
}

/**
 * 单步执行（调试模式）
 */
export function stepWorkflowExecution(executionId: string): Promise<ApiResponse<boolean>> {
  return apiClient.post(`/api/agent/workflow/execution/${executionId}/step`)
}

/**
 * 设置断点
 */
export function setWorkflowBreakpoint(executionId: string, nodeId: string): Promise<ApiResponse<boolean>> {
  return apiClient.post(`/api/agent/workflow/execution/${executionId}/breakpoint`, { nodeId })
}

/**
 * 移除断点
 */
export function removeWorkflowBreakpoint(executionId: string, nodeId: string): Promise<ApiResponse<boolean>> {
  return apiClient.delete(`/api/agent/workflow/execution/${executionId}/breakpoint/${nodeId}`)
}

/**
 * 清除所有断点
 */
export function clearWorkflowBreakpoints(executionId: string): Promise<ApiResponse<boolean>> {
  return apiClient.delete(`/api/agent/workflow/execution/${executionId}/breakpoints`)
}

/**
 * 获取执行历史
 */
export function getWorkflowExecutionHistory(agentId: string, limit: number = 10): Promise<ApiResponse<WorkflowExecutionStatus[]>> {
  return apiClient.get(`/api/agent/workflow/execution/history/${agentId}?limit=${limit}`)
}

/**
 * 查询历史记录统计
 */
export function getWorkflowHistoryStats(agentId: string): Promise<ApiResponse<WorkflowHistoryStats>> {
  return apiClient.get(`/api/agent/workflow/history/stats/${agentId}`)
}

/**
 * 统计工作流历史记录数量
 */
export function countWorkflowHistoryByWorkflowId(workflowId: string): Promise<ApiResponse<number>> {
  return apiClient.get(`/api/agent/workflow/history/count/workflow/${workflowId}`)
}

/**
 * 统计智能体历史记录数量
 */
export function countWorkflowHistoryByAgentId(agentId: string): Promise<ApiResponse<number>> {
  return apiClient.get(`/api/agent/workflow/history/count/agent/${agentId}`)
}

/**
 * 统计时间范围内历史记录数量
 */
export function countWorkflowHistoryByTimeRange(workflowId: string, startTime: string, endTime: string): Promise<ApiResponse<number>> {
  return apiClient.get(`/api/agent/workflow/history/count/time-range/${workflowId}?startTime=${startTime}&endTime=${endTime}`)
}

/**
 * 检查配置是否存在
 */
export function isWorkflowConfigExists(configHash: string): Promise<ApiResponse<boolean>> {
  return apiClient.get(`/api/agent/workflow/history/exists/${configHash}`)
}

/**
 * 清理历史记录
 */
export function cleanupWorkflowHistory(beforeTime: string): Promise<ApiResponse<number>> {
  return apiClient.delete(`/api/agent/workflow/history/cleanup?beforeTime=${beforeTime}`)
}

/**
 * 回滚到历史版本
 */
export async function rollbackToHistoryVersion(historyVersion: WorkflowHistory) {
  try {
    // 构建安全的配置数据
    let nodes = []
    let edges = []
    let globalVariables = {}

    try {
      nodes = historyVersion.nodesConfig ? JSON.parse(historyVersion.nodesConfig) : []
    } catch (e) {
      console.warn('解析节点配置失败:', e)
      nodes = []
    }

    try {
      edges = historyVersion.connectionsConfig ? JSON.parse(historyVersion.connectionsConfig) : []
    } catch (e) {
      console.warn('解析连接配置失败:', e)
      edges = []
    }

    try {
      globalVariables = historyVersion.globalVariables ? JSON.parse(historyVersion.globalVariables) : {}
    } catch (e) {
      console.warn('解析全局变量失败:', e)
      globalVariables = {}
    }

    const response = await realtimeSaveWorkflow({
      agentId: historyVersion.agentId,
      nodesData: JSON.stringify(nodes),
      edgesData: JSON.stringify(edges),
      viewportConfig: JSON.stringify({ x: 0, y: 0, zoom: 1 }),
      nodeLibrary: JSON.stringify({ categories: [] }),
      globalVariables: JSON.stringify(globalVariables),
      operationType: 'rollback',
      operationDesc: `回滚到版本 ${historyVersion.version}`
    })

    console.log('回滚工作流配置完成:', response)
    return { success: response.success, message: response.message, data: { nodes, edges, globalVariables } }
  } catch (error: any) {
    console.error('回滚工作流配置失败:', error)
    return { success: false, message: error.message || '回滚失败' }
  }
}

/**
 * 加载智能体工作流配置
 */
export async function loadAgentWorkflow(agentId: string) {
  try {
    const response = await getLatestWorkflowByAgentId(agentId)

    if (response.success && response.data) {
      const workflow = response.data

      // 转换为Vue Flow格式
      const config: WorkflowConfig = {
        agentId: workflow.agentId,
        version: workflow.version,
        nodes: workflow.nodesData ? JSON.parse(workflow.nodesData) : [],
        edges: workflow.edgesData ? JSON.parse(workflow.edgesData) : [],
        viewport: workflow.viewportConfig ? JSON.parse(workflow.viewportConfig) : { x: 0, y: 0, zoom: 1 },
        nodeLibrary: workflow.nodeLibrary ? JSON.parse(workflow.nodeLibrary) : { categories: [] },
        globalVariables: workflow.globalVariables ? JSON.parse(workflow.globalVariables) : {},
        lastModified: workflow.lastModified ? new Date(workflow.lastModified).getTime() : Date.now(),
        isPublished: workflow.isPublished
      }

      return { success: true, data: config }
    } else {
      // 如果没有找到工作流，返回默认配置
      return {
        success: true,
        data: {
          agentId,
          version: 1,
          nodes: [],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 },
          nodeLibrary: { categories: [] },
          globalVariables: {},
          lastModified: Date.now(),
          isPublished: false
        }
      }
    }
  } catch (error: any) {
    console.error('加载工作流配置失败:', error)

    // 出错时返回默认配置
    return {
      success: true,
      data: {
        agentId,
        version: 1,
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
        nodeLibrary: { categories: [] },
        globalVariables: {},
        lastModified: Date.now(),
        isPublished: false
      }
    }
  }
}
