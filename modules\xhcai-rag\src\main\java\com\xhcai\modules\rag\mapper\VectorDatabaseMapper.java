package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.rag.dto.VectorDatabaseQueryDTO;
import com.xhcai.modules.rag.entity.VectorDatabase;
import com.xhcai.modules.rag.vo.VectorDatabaseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 向量数据库Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface VectorDatabaseMapper extends BaseMapper<VectorDatabase> {

    /**
     * 分页查询向量数据库列表
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @return 向量数据库分页列表
     */
    @Select("<script>" +
            "SELECT vd.*, " +
            "       sdd.dict_label as type_name " +
            "FROM vector_databases vd " +
            "LEFT JOIN sys_dict_data sdd ON vd.type = sdd.dict_value AND sdd.dict_type = 'vector_database_type' " +
            "WHERE vd.deleted = 0 " +
            "<if test='queryDTO.name != null and queryDTO.name != \"\"'>" +
            "  AND vd.name LIKE CONCAT('%', #{queryDTO.name}, '%') " +
            "</if>" +
            "<if test='queryDTO.type != null and queryDTO.type != \"\"'>" +
            "  AND vd.type = #{queryDTO.type} " +
            "</if>" +
            "<if test='queryDTO.host != null and queryDTO.host != \"\"'>" +
            "  AND vd.host LIKE CONCAT('%', #{queryDTO.host}, '%') " +
            "</if>" +
            "<if test='queryDTO.status != null and queryDTO.status != \"\"'>" +
            "  AND vd.status = #{queryDTO.status} " +
            "</if>" +
            "<if test='queryDTO.isDefault != null and queryDTO.isDefault != \"\"'>" +
            "  AND vd.is_default = #{queryDTO.isDefault} " +
            "</if>" +
            "<if test='queryDTO.keyword != null and queryDTO.keyword != \"\"'>" +
            "  AND (vd.name LIKE CONCAT('%', #{queryDTO.keyword}, '%') " +
            "       OR vd.description LIKE CONCAT('%', #{queryDTO.keyword}, '%')) " +
            "</if>" +
            "<if test='queryDTO.beginTime != null'>" +
            "  AND vd.create_time >= #{queryDTO.beginTime} " +
            "</if>" +
            "<if test='queryDTO.endTime != null'>" +
            "  AND vd.create_time &lt;= #{queryDTO.endTime} " +
            "</if>" +
            "ORDER BY vd.is_default DESC, vd.create_time DESC" +
            "</script>")
    IPage<VectorDatabaseVO> selectVectorDatabasePage(Page<VectorDatabaseVO> page, @Param("queryDTO") VectorDatabaseQueryDTO queryDTO);

    /**
     * 查询向量数据库列表
     *
     * @param queryDTO 查询条件
     * @return 向量数据库列表
     */
    @Select("<script>" +
            "SELECT vd.*, " +
            "       sdd.dict_label as type_name " +
            "FROM vector_databases vd " +
            "LEFT JOIN sys_dict_data sdd ON vd.type = sdd.dict_value AND sdd.dict_type = 'vector_database_type' " +
            "WHERE vd.deleted = 0 " +
            "<if test='queryDTO.name != null and queryDTO.name != \"\"'>" +
            "  AND vd.name LIKE CONCAT('%', #{queryDTO.name}, '%') " +
            "</if>" +
            "<if test='queryDTO.type != null and queryDTO.type != \"\"'>" +
            "  AND vd.type = #{queryDTO.type} " +
            "</if>" +
            "<if test='queryDTO.host != null and queryDTO.host != \"\"'>" +
            "  AND vd.host LIKE CONCAT('%', #{queryDTO.host}, '%') " +
            "</if>" +
            "<if test='queryDTO.status != null and queryDTO.status != \"\"'>" +
            "  AND vd.status = #{queryDTO.status} " +
            "</if>" +
            "<if test='queryDTO.isDefault != null and queryDTO.isDefault != \"\"'>" +
            "  AND vd.is_default = #{queryDTO.isDefault} " +
            "</if>" +
            "<if test='queryDTO.keyword != null and queryDTO.keyword != \"\"'>" +
            "  AND (vd.name LIKE CONCAT('%', #{queryDTO.keyword}, '%') " +
            "       OR vd.description LIKE CONCAT('%', #{queryDTO.keyword}, '%')) " +
            "</if>" +
            "<if test='queryDTO.beginTime != null'>" +
            "  AND vd.create_time >= #{queryDTO.beginTime} " +
            "</if>" +
            "<if test='queryDTO.endTime != null'>" +
            "  AND vd.create_time &lt;= #{queryDTO.endTime} " +
            "</if>" +
            "ORDER BY vd.is_default DESC, vd.create_time DESC" +
            "</script>")
    List<VectorDatabaseVO> selectVectorDatabaseList(@Param("queryDTO") VectorDatabaseQueryDTO queryDTO);

    /**
     * 根据ID查询向量数据库详情
     *
     * @param id 向量数据库ID
     * @return 向量数据库详情
     */
    @Select("SELECT vd.*, " +
            "       sdd.dict_label as type_name " +
            "FROM vector_databases vd " +
            "LEFT JOIN sys_dict_data sdd ON vd.type = sdd.dict_value AND sdd.dict_type = 'vector_database_type' " +
            "WHERE vd.id = #{id} AND vd.deleted = 0")
    VectorDatabaseVO selectVectorDatabaseById(@Param("id") String id);

    /**
     * 查询默认向量数据库
     *
     * @return 默认向量数据库
     */
    @Select("SELECT vd.*, " +
            "       sdd.dict_label as type_name " +
            "FROM vector_databases vd " +
            "LEFT JOIN sys_dict_data sdd ON vd.type = sdd.dict_value AND sdd.dict_type = 'vector_database_type' " +
            "WHERE vd.is_default = 'Y' AND vd.status = '0' AND vd.deleted = 0 " +
            "LIMIT 1")
    VectorDatabaseVO selectDefaultVectorDatabase();

    /**
     * 查询启用的向量数据库列表
     *
     * @return 启用的向量数据库列表
     */
    @Select("SELECT vd.*, " +
            "       sdd.dict_label as type_name " +
            "FROM vector_databases vd " +
            "LEFT JOIN sys_dict_data sdd ON vd.type = sdd.dict_value AND sdd.dict_type = 'vector_database_type' " +
            "WHERE vd.status = '0' AND vd.deleted = 0 " +
            "ORDER BY vd.is_default DESC, vd.create_time DESC")
    List<VectorDatabaseVO> selectEnabledVectorDatabases();

    /**
     * 更新默认向量数据库
     *
     * @param id 向量数据库ID
     * @return 更新行数
     */
    @Update("UPDATE vector_databases SET is_default = 'N' WHERE is_default = 'Y' AND deleted = 0")
    int clearDefaultVectorDatabase();

    /**
     * 设置默认向量数据库
     *
     * @param id 向量数据库ID
     * @return 更新行数
     */
    @Update("UPDATE vector_databases SET is_default = 'Y' WHERE id = #{id} AND deleted = 0")
    int setDefaultVectorDatabase(@Param("id") String id);

    /**
     * 更新连接测试结果
     *
     * @param id              向量数据库ID
     * @param testResult      测试结果
     * @param errorMessage    错误信息
     * @return 更新行数
     */
    @Update("UPDATE vector_databases SET " +
            "last_test_time = NOW(), " +
            "last_test_result = #{testResult}, " +
            "test_error_message = #{errorMessage} " +
            "WHERE id = #{id} AND deleted = 0")
    int updateTestResult(@Param("id") String id, 
                        @Param("testResult") String testResult, 
                        @Param("errorMessage") String errorMessage);

    /**
     * 批量更新状态
     *
     * @param ids    向量数据库ID列表
     * @param status 状态
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE vector_databases SET status = #{status} " +
            "WHERE deleted = 0 AND id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "  #{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status);
}
