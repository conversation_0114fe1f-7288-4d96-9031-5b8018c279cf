<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify Token 代理页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
        }
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 30px;
        }
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .status-item:last-child {
            margin-bottom: 0;
        }
        .status-value {
            font-family: monospace;
            background: rgba(0, 0, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        .success {
            color: #10b981;
        }
        .error {
            color: #ef4444;
        }
        .loading {
            color: #f59e0b;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 5px;
            transition: all 0.2s ease;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .logs {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-item {
            margin-bottom: 5px;
        }
        .log-time {
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <div class="title">Dify Token 代理页面</div>
        <div class="subtitle">正在设置登录凭证...</div>
        
        <div class="status">
            <div class="status-item">
                <span>页面状态:</span>
                <span id="pageStatus" class="status-value loading">初始化中</span>
            </div>
            <div class="status-item">
                <span>Access Token:</span>
                <span id="accessTokenStatus" class="status-value">未设置</span>
            </div>
            <div class="status-item">
                <span>Refresh Token:</span>
                <span id="refreshTokenStatus" class="status-value">未设置</span>
            </div>
            <div class="status-item">
                <span>本地存储:</span>
                <span id="localStorageStatus" class="status-value">检查中</span>
            </div>
        </div>
        
        <div>
            <button class="btn" onclick="checkTokens()">检查Token</button>
            <button class="btn" onclick="clearTokens()">清除Token</button>
            <button class="btn" onclick="redirectToDify()">跳转到Dify</button>
        </div>
        
        <div class="logs" id="logs"></div>
    </div>

    <script>
        let accessToken = '';
        let refreshToken = '';
        let targetUrl = '';

        // 添加日志
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `<span class="log-time">[${time}]</span> ${message}`;
            logs.insertBefore(logItem, logs.firstChild);
            
            // 只保留最近20条日志
            while (logs.children.length > 20) {
                logs.removeChild(logs.lastChild);
            }
            
            console.log(`[${time}] ${message}`);
        }

        // 更新状态显示
        function updateStatus() {
            document.getElementById('pageStatus').textContent = '运行中';
            document.getElementById('pageStatus').className = 'status-value success';
            
            document.getElementById('accessTokenStatus').textContent = 
                accessToken ? accessToken.substring(0, 20) + '...' : '未设置';
            document.getElementById('accessTokenStatus').className = 
                'status-value ' + (accessToken ? 'success' : 'error');
                
            document.getElementById('refreshTokenStatus').textContent = 
                refreshToken ? refreshToken.substring(0, 20) + '...' : '未设置';
            document.getElementById('refreshTokenStatus').className = 
                'status-value ' + (refreshToken ? 'success' : 'error');
                
            // 检查本地存储
            const storedConsoleToken = localStorage.getItem('console_token');
            const storedRefreshToken = localStorage.getItem('refresh_token');
            const hasTokens = storedConsoleToken && storedRefreshToken;
            
            document.getElementById('localStorageStatus').textContent = 
                hasTokens ? '已设置' : '未设置';
            document.getElementById('localStorageStatus').className = 
                'status-value ' + (hasTokens ? 'success' : 'error');
        }

        // 设置Token到本地存储
        function setTokensToLocalStorage() {
            if (!accessToken || !refreshToken) {
                addLog('缺少必要的token，无法设置本地存储', 'error');
                return false;
            }

            try {
                // 设置多种可能的键名格式
                const tokenKeys = [
                    'console_token',
                    'refresh_token',
                    'dify_console_token',
                    'dify_refresh_token',
                    'access_token',
                    'accessToken',
                    'refreshToken'
                ];

                // 设置访问令牌
                tokenKeys.forEach(key => {
                    if (key.includes('console') || key.includes('access')) {
                        localStorage.setItem(key, accessToken);
                    }
                    if (key.includes('refresh')) {
                        localStorage.setItem(key, refreshToken);
                    }
                });

                addLog(`成功设置 ${tokenKeys.length} 个本地存储键`, 'success');
                
                // 验证设置
                const verification = tokenKeys.map(key => ({
                    key,
                    exists: !!localStorage.getItem(key)
                }));
                
                const successCount = verification.filter(v => v.exists).length;
                addLog(`验证结果: ${successCount}/${tokenKeys.length} 个键设置成功`, 'success');
                
                return true;
            } catch (error) {
                addLog(`设置本地存储失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 检查Token
        function checkTokens() {
            addLog('开始检查token状态');
            
            const keys = [
                'console_token', 'refresh_token', 'dify_console_token', 
                'dify_refresh_token', 'access_token', 'accessToken', 'refreshToken'
            ];
            
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    addLog(`${key}: ${value.substring(0, 20)}...`, 'success');
                } else {
                    addLog(`${key}: 未设置`, 'error');
                }
            });
            
            updateStatus();
        }

        // 清除Token
        function clearTokens() {
            const keys = [
                'console_token', 'refresh_token', 'dify_console_token', 
                'dify_refresh_token', 'access_token', 'accessToken', 'refreshToken'
            ];
            
            keys.forEach(key => {
                localStorage.removeItem(key);
            });
            
            addLog('已清除所有token', 'info');
            updateStatus();
        }

        // 跳转到Dify
        function redirectToDify() {
            if (targetUrl) {
                addLog(`跳转到: ${targetUrl}`, 'info');
                // 延迟跳转，确保token设置完成
                setTimeout(() => {
                    window.location.href = targetUrl;
                }, 500);
            } else {
                addLog('未设置目标URL', 'error');
            }
        }

        // 通过iframe方式设置token到目标域名
        function setTokenViaIframe() {
            if (!accessToken || !refreshToken) {
                addLog('缺少token，无法通过iframe设置', 'error');
                return;
            }

            // 创建隐藏的iframe指向目标域名
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'http://192.168.50.142/token-setter.html';

            // 监听iframe加载完成
            iframe.onload = function() {
                addLog('iframe加载完成，发送token', 'info');

                const tokenData = {
                    type: 'SET_TOKENS',
                    data: {
                        console_token: accessToken,
                        refresh_token: refreshToken,
                        access_token: accessToken,
                        dify_console_token: accessToken,
                        dify_refresh_token: refreshToken
                    }
                };

                // 发送token到iframe
                iframe.contentWindow.postMessage(tokenData, 'http://192.168.50.142');
                addLog('已发送token到iframe', 'success');

                // 3秒后移除iframe并跳转
                setTimeout(() => {
                    document.body.removeChild(iframe);
                    redirectToDify();
                }, 3000);
            };

            iframe.onerror = function() {
                addLog('iframe加载失败', 'error');
                // 直接跳转
                redirectToDify();
            };

            document.body.appendChild(iframe);
            addLog('已创建iframe用于设置token', 'info');
        }

        // 解析URL参数
        function parseUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            accessToken = urlParams.get('accessToken') || '';
            refreshToken = urlParams.get('refreshToken') || '';
            targetUrl = urlParams.get('targetUrl') || '';
            
            addLog('解析URL参数完成');
            addLog(`AccessToken: ${accessToken ? accessToken.substring(0, 20) + '...' : '未提供'}`);
            addLog(`RefreshToken: ${refreshToken ? refreshToken.substring(0, 20) + '...' : '未提供'}`);
            addLog(`TargetUrl: ${targetUrl || '未提供'}`);
        }

        // 页面初始化
        function init() {
            addLog('页面初始化开始');
            
            // 解析URL参数
            parseUrlParams();
            
            // 设置token到本地存储
            if (accessToken && refreshToken) {
                const success = setTokensToLocalStorage();
                if (success) {
                    addLog('Token设置成功，3秒后自动跳转', 'success');
                    setTimeout(() => {
                        if (targetUrl) {
                            redirectToDify();
                        }
                    }, 3000);
                }
            } else {
                addLog('缺少必要的token参数', 'error');
            }
            
            updateStatus();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
