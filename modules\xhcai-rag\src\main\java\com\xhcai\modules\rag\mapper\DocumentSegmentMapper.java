package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.rag.entity.DocumentSegment;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 文档分段Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface DocumentSegmentMapper extends BaseMapper<DocumentSegment> {

    /**
     * 根据文档ID分页查询文档分段列表
     *
     * @param page 分页参数
     * @param documentId 文档ID
     * @param status 状态
     * @param enabled 是否启用
     * @return 文档分段分页列表
     */
    @Select("<script>" +
            "SELECT * FROM document_segments " +
            "WHERE deleted = 0 " +
            "<if test='documentId != null and documentId != \"\"'>" +
            "  AND document_id = #{documentId} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "  AND status = #{status} " +
            "</if>" +
            "<if test='enabled != null'>" +
            "  AND enabled = #{enabled} " +
            "</if>" +
            "ORDER BY position ASC" +
            "</script>")
    IPage<DocumentSegment> selectPageByDocument(Page<DocumentSegment> page,
                                               @Param("documentId") String documentId,
                                               @Param("status") String status,
                                               @Param("enabled") Boolean enabled);

    /**
     * 根据文档ID查询文档分段列表
     *
     * @param documentId 文档ID
     * @return 文档分段列表
     */
    @Select("SELECT * FROM document_segments WHERE document_id = #{documentId} AND deleted = 0 ORDER BY position ASC")
    List<DocumentSegment> selectByDocumentId(@Param("documentId") String documentId);

    /**
     * 根据知识库ID查询文档分段列表
     *
     * @param datasetId 知识库ID
     * @return 文档分段列表
     */
    @Select("SELECT * FROM document_segments WHERE dataset_id = #{datasetId} AND deleted = 0 ORDER BY position ASC")
    List<DocumentSegment> selectByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 根据文档ID统计分段数量
     *
     * @param documentId 文档ID
     * @return 分段数量
     */
    @Select("SELECT COUNT(*) FROM document_segments WHERE document_id = #{documentId} AND deleted = 0")
    Long countByDocumentId(@Param("documentId") String documentId);

    /**
     * 根据知识库ID统计分段数量
     *
     * @param datasetId 知识库ID
     * @return 分段数量
     */
    @Select("SELECT COUNT(*) FROM document_segments WHERE dataset_id = #{datasetId} AND deleted = 0")
    Long countByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 根据文档ID和状态统计分段数量
     *
     * @param documentId 文档ID
     * @param status 状态
     * @return 分段数量
     */
    @Select("SELECT COUNT(*) FROM document_segments WHERE document_id = #{documentId} AND status = #{status} AND deleted = 0")
    Long countByDocumentIdAndStatus(@Param("documentId") String documentId, @Param("status") String status);

    /**
     * 查询等待向量化的分段列表
     *
     * @param limit 限制数量
     * @return 分段列表
     */
    @Select("SELECT * FROM document_segments WHERE status = 'waiting' AND enabled = true AND deleted = 0 ORDER BY create_time ASC LIMIT #{limit}")
    List<DocumentSegment> selectWaitingSegments(@Param("limit") Integer limit);

    /**
     * 查询向量化中的分段列表
     *
     * @return 分段列表
     */
    @Select("SELECT * FROM document_segments WHERE status = 'indexing' AND enabled = true AND deleted = 0")
    List<DocumentSegment> selectIndexingSegments();

    /**
     * 根据索引节点ID查询分段
     *
     * @param indexNodeId 索引节点ID
     * @return 文档分段
     */
    @Select("SELECT * FROM document_segments WHERE index_node_id = #{indexNodeId} AND deleted = 0")
    DocumentSegment selectByIndexNodeId(@Param("indexNodeId") String indexNodeId);

    /**
     * 更新分段状态
     *
     * @param id 分段ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE document_segments SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") String status);

    /**
     * 批量插入分段
     *
     * @param segments 分段列表
     * @return 插入行数
     */
    @InsertProvider(type = DocumentSegmentSqlProvider.class, method = "batchInsert")
    int batchInsert(@Param("segments") List<DocumentSegment> segments);


    /**
     * 根据文档ID删除所有分段
     *
     * @param documentId 文档ID
     * @return 删除行数
     */
    @Update("UPDATE document_segments SET deleted = 1, update_time = NOW() WHERE document_id = #{documentId}")
    int deleteByDocumentId(@Param("documentId") String documentId);



    /**
     * 统计文档分段的总字符数和token数
     *
     * @param documentId 文档ID
     * @return 统计结果 [wordCount, tokens]
     */
    @Select("SELECT COALESCE(SUM(word_count), 0) as word_count, COALESCE(SUM(tokens), 0) as tokens FROM document_segments WHERE document_id = #{documentId} AND deleted = 0")
    List<Long> selectStatsByDocumentId(@Param("documentId") String documentId);

    /**
     * 统计知识库分段的总字符数和token数
     *
     * @param datasetId 知识库ID
     * @return 统计结果 [wordCount, tokens]
     */
    @Select("SELECT COALESCE(SUM(word_count), 0) as word_count, COALESCE(SUM(tokens), 0) as tokens FROM document_segments WHERE dataset_id = #{datasetId} AND deleted = 0")
    List<Long> selectStatsByDatasetId(@Param("datasetId") String datasetId);
}
