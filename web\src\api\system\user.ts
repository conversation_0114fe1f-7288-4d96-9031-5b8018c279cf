/**
 * 用户个人信息管理相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'

// 用户个人信息VO
export interface UserProfileVO {
  id: string
  username: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  gender?: string
  genderText?: string
  birthday?: string
  deptId?: string
  deptName?: string
  status: string
  statusText: string
  userType: string
  userTypeText: string
  loginIp?: string
  loginDate?: string
  createTime: string
  updateTime: string
  roles: string[]
  permissions: string[]
  tenantId?: string
  tenantName?: string
  bio?: string
}

// 用户个人信息更新参数
export interface UserProfileUpdateDTO {
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  gender?: string
  birthday?: string
  bio?: string
}

// 修改密码参数
export interface ChangePasswordDTO {
  oldPassword: string
  newPassword: string
}

/**
 * 用户个人信息API类
 */
export class UserProfileAPI {
  /**
   * 获取当前用户个人信息
   */
  static async getCurrentUserProfile(): Promise<ApiResponse<UserProfileVO>> {
    return apiClient.get('/api/system/user/profile')
  }

  /**
   * 更新当前用户个人信息
   */
  static async updateCurrentUserProfile(data: UserProfileUpdateDTO): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/user/profile', data)
  }

  /**
   * 修改当前用户密码
   */
  static async changeCurrentUserPassword(data: ChangePasswordDTO): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/user/profile/password', data)
  }
}

// 导出便捷函数
export const getCurrentUserProfile = UserProfileAPI.getCurrentUserProfile
export const updateCurrentUserProfile = UserProfileAPI.updateCurrentUserProfile
export const changeCurrentUserPassword = UserProfileAPI.changeCurrentUserPassword

export default UserProfileAPI
