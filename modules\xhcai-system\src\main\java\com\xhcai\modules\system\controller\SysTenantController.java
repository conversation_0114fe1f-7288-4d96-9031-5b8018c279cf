package com.xhcai.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPlatformAdmin;
import com.xhcai.modules.system.annotation.DataScope;
import com.xhcai.modules.system.dto.SysTenantQueryDTO;
import com.xhcai.modules.system.entity.SysTenant;
import com.xhcai.modules.system.service.ISysTenantService;
import com.xhcai.modules.system.vo.SysTenantVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 租户管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "租户管理", description = "租户管理相关接口")
@RestController
@RequestMapping("/api/system/tenant")
@RequiresPlatformAdmin(message = "租户管理需要平台管理员权限")
public class SysTenantController {

    @Autowired
    private ISysTenantService tenantService;

    /**
     * 分页查询租户列表
     */
    @Operation(summary = "分页查询租户列表", description = "根据条件分页查询租户信息")
    @GetMapping("/page")
    @DataScope(deptAlias = "d", userAlias = "u")
    public Result<PageResult<SysTenantVO>> page(@Valid SysTenantQueryDTO queryDTO) {
        PageResult<SysTenantVO> pageResult = tenantService.selectTenantPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询租户详情
     */
    @Operation(summary = "查询租户详情", description = "根据租户ID查询详细信息")
    @GetMapping("/{id}")
    public Result<SysTenantVO> getById(
            @Parameter(description = "租户ID", required = true) @PathVariable String id) {
        SysTenantVO tenantVO = tenantService.getTenantStatistics(id);
        return Result.success(tenantVO);
    }

    /**
     * 创建租户
     */
    @Operation(summary = "创建租户", description = "创建新的租户")
    @PostMapping
    public Result<Void> create(@Valid @RequestBody SysTenant tenant) {
        boolean result = tenantService.createTenant(tenant);
        return result ? Result.success() : Result.fail("创建租户失败");
    }

    /**
     * 更新租户信息
     */
    @Operation(summary = "更新租户信息", description = "更新租户基本信息")
    @PutMapping
    public Result<Void> update(@Valid @RequestBody SysTenant tenant) {
        boolean result = tenantService.updateTenant(tenant);
        return result ? Result.success() : Result.fail("更新租户失败");
    }

    /**
     * 删除租户
     */
    @Operation(summary = "删除租户", description = "批量删除租户（逻辑删除）")
    @DeleteMapping
    public Result<Void> delete(@RequestBody List<String> tenantIds) {
        boolean result = tenantService.deleteTenants(tenantIds);
        return result ? Result.success() : Result.fail("删除租户失败");
    }

    /**
     * 启用租户
     */
    @Operation(summary = "启用租户", description = "启用指定租户")
    @PutMapping("/{id}/enable")
    public Result<Void> enable(
            @Parameter(description = "租户ID", required = true) @PathVariable String id) {
        boolean result = tenantService.enableTenant(id);
        return result ? Result.success() : Result.fail("启用租户失败");
    }

    /**
     * 停用租户
     */
    @Operation(summary = "停用租户", description = "停用指定租户")
    @PutMapping("/{id}/disable")
    public Result<Void> disable(
            @Parameter(description = "租户ID", required = true) @PathVariable String id) {
        boolean result = tenantService.disableTenant(id);
        return result ? Result.success() : Result.fail("停用租户失败");
    }

    /**
     * 续期租户
     */
    @Operation(summary = "续期租户", description = "为租户续期指定月数")
    @PutMapping("/{id}/renew")
    public Result<Void> renew(
            @Parameter(description = "租户ID", required = true) @PathVariable String id,
            @Parameter(description = "续期月数", required = true) @RequestParam Integer months) {
        boolean result = tenantService.renewTenant(id, months);
        return result ? Result.success() : Result.fail("续期租户失败");
    }

    /**
     * 检查租户编码是否存在
     */
    @Operation(summary = "检查租户编码", description = "检查租户编码是否已存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkTenantCode(
            @Parameter(description = "租户编码", required = true) @RequestParam String tenantCode,
            @Parameter(description = "排除的租户ID") @RequestParam(required = false) String excludeId) {
        boolean exists = tenantService.existsTenantCode(tenantCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查租户域名是否存在
     */
    @Operation(summary = "检查租户域名", description = "检查租户域名是否已存在")
    @GetMapping("/check-domain")
    public Result<Boolean> checkDomain(
            @Parameter(description = "租户域名", required = true) @RequestParam("domain") String domain,
            @Parameter(description = "排除的租户ID") @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = tenantService.existsDomain(domain, excludeId);
        return Result.success(exists);
    }

    /**
     * 查询即将过期的租户
     */
    @Operation(summary = "查询即将过期的租户", description = "查询指定天数内即将过期的租户")
    @GetMapping("/expiring")
    public Result<List<SysTenantVO>> getExpiringTenants(
            @Parameter(description = "提前天数", example = "7") @RequestParam(value = "days", defaultValue = "7") Integer days) {
        List<SysTenantVO> tenants = tenantService.getExpiringTenants(days);
        return Result.success(tenants);
    }

    /**
     * 查询已过期的租户
     */
    @Operation(summary = "查询已过期的租户", description = "查询所有已过期的租户")
    @GetMapping("/expired")
    public Result<List<SysTenantVO>> getExpiredTenants() {
        List<SysTenantVO> tenants = tenantService.getExpiredTenants();
        return Result.success(tenants);
    }

    /**
     * 处理过期租户
     */
    @Operation(summary = "处理过期租户", description = "将过期租户状态设置为过期")
    @PostMapping("/process-expired")
    public Result<Integer> processExpiredTenants() {
        int processedCount = tenantService.processExpiredTenants();
        return Result.success(processedCount);
    }

    /**
     * 初始化租户数据
     */
    @Operation(summary = "初始化租户数据", description = "为租户初始化默认数据")
    @PostMapping("/{id}/initialize")
    public Result<Void> initializeTenantData(
            @Parameter(description = "租户ID", required = true) @PathVariable String id,
            @Parameter(description = "管理员用户名") @RequestParam(defaultValue = "admin") String adminUsername,
            @Parameter(description = "管理员密码") @RequestParam(defaultValue = "123456") String adminPassword,
            @Parameter(description = "管理员邮箱") @RequestParam(required = false) String adminEmail) {
        boolean result = tenantService.initializeTenantData(id, adminUsername, adminPassword, adminEmail);
        return result ? Result.success() : Result.fail("初始化租户数据失败");
    }

    /**
     * 复制租户配置
     */
    @Operation(summary = "复制租户配置", description = "从源租户复制配置到目标租户")
    @PostMapping("/copy-configs")
    public Result<Void> copyTenantConfigs(
            @Parameter(description = "源租户ID", required = true) @RequestParam String sourceTenantId,
            @Parameter(description = "目标租户ID", required = true) @RequestParam String targetTenantId) {
        boolean result = tenantService.copyTenantConfigs(sourceTenantId, targetTenantId);
        return result ? Result.success() : Result.fail("复制租户配置失败");
    }

    /**
     * 验证租户状态
     */
    @Operation(summary = "验证租户状态", description = "检查租户是否可用")
    @GetMapping("/{id}/validate")
    public Result<Boolean> validateTenantStatus(
            @Parameter(description = "租户ID", required = true) @PathVariable String id) {
        boolean isValid = tenantService.validateTenantStatus(id);
        return Result.success(isValid);
    }

    /**
     * 获取租户用户数量
     */
    @Operation(summary = "获取租户用户数量", description = "获取指定租户的用户数量")
    @GetMapping("/{id}/user-count")
    public Result<Integer> getTenantUserCount(
            @Parameter(description = "租户ID", required = true) @PathVariable String id) {
        Integer userCount = tenantService.getTenantUserCount(id);
        return Result.success(userCount);
    }

    /**
     * 检查租户用户数量限制
     */
    @Operation(summary = "检查用户数量限制", description = "检查租户是否超出用户数量限制")
    @GetMapping("/{id}/check-user-limit")
    public Result<Boolean> checkUserLimit(
            @Parameter(description = "租户ID", required = true) @PathVariable String id) {
        boolean exceeded = tenantService.checkUserLimit(id);
        return Result.success(exceeded);
    }
}
