/**
 * 统一的日志管理工具
 * 在生产环境中自动禁用调试信息，避免控制台输出无关信息
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

class Logger {
  private level: LogLevel
  private isDevelopment: boolean

  constructor() {
    this.isDevelopment = import.meta.env.DEV
    // 生产环境只显示错误信息，开发环境显示所有信息
    this.level = this.isDevelopment ? LogLevel.DEBUG : LogLevel.ERROR
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel) {
    this.level = level
  }

  /**
   * 调试信息 - 只在开发环境显示
   */
  debug(...args: any[]) {
    if (this.level <= LogLevel.DEBUG && this.isDevelopment) {
      console.debug('[DEBUG]', ...args)
    }
  }

  /**
   * 一般信息 - 只在开发环境显示
   */
  info(...args: any[]) {
    if (this.level <= LogLevel.INFO && this.isDevelopment) {
      console.info('[INFO]', ...args)
    }
  }

  /**
   * 警告信息 - 开发环境和生产环境都显示
   */
  warn(...args: any[]) {
    if (this.level <= LogLevel.WARN) {
      console.warn('[WARN]', ...args)
    }
  }

  /**
   * 错误信息 - 始终显示
   */
  error(...args: any[]) {
    if (this.level <= LogLevel.ERROR) {
      console.error('[ERROR]', ...args)
    }
  }

  /**
   * 流式数据调试 - 只在开发环境显示，且频率限制
   */
  streamDebug(message: string, data?: any) {
    if (this.isDevelopment && this.level <= LogLevel.DEBUG) {
      // 使用时间戳限制输出频率，避免控制台被刷屏
      const now = Date.now()
      const key = `stream_${message}`
      const lastLog = (this as any)[key] || 0
      
      // 每500ms最多输出一次相同的流式调试信息
      if (now - lastLog > 500) {
        console.debug('[STREAM]', message, data ? data : '')
        ;(this as any)[key] = now
      }
    }
  }

  /**
   * AI消息调试 - 只在开发环境显示
   */
  aiDebug(message: string, ...args: any[]) {
    if (this.isDevelopment && this.level <= LogLevel.DEBUG) {
      console.debug('[AI]', message, ...args)
    }
  }

  /**
   * 渲染器调试 - 只在开发环境显示
   */
  rendererDebug(renderer: string, message: string, ...args: any[]) {
    if (this.isDevelopment && this.level <= LogLevel.DEBUG) {
      console.debug(`[${renderer.toUpperCase()}]`, message, ...args)
    }
  }

  /**
   * API调试 - 只在开发环境显示
   */
  apiDebug(endpoint: string, message: string, ...args: any[]) {
    if (this.isDevelopment && this.level <= LogLevel.DEBUG) {
      console.debug(`[API:${endpoint}]`, message, ...args)
    }
  }

  /**
   * 性能调试 - 只在开发环境显示
   */
  perfDebug(operation: string, duration: number) {
    if (this.isDevelopment && this.level <= LogLevel.DEBUG) {
      console.debug(`[PERF] ${operation}: ${duration}ms`)
    }
  }

  /**
   * 组件调试 - 只在开发环境显示
   */
  componentDebug(component: string, message: string, ...args: any[]) {
    if (this.isDevelopment && this.level <= LogLevel.DEBUG) {
      console.debug(`[${component}]`, message, ...args)
    }
  }
}

// 创建全局日志实例
export const logger = new Logger()

// 导出日志级别枚举
export { LogLevel as LogLevels }

// 默认导出
export default logger
