package com.xhcai.modules.agent.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.agent.dto.BusinessProjectQueryDTO;
import com.xhcai.modules.agent.entity.BusinessProject;

/**
 * 业务项目Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface BusinessProjectMapper extends BaseMapper<BusinessProject> {

    /**
     * 分页查询业务项目列表（包含关联信息）
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Select({
        "<script>",
        "SELECT bp.*, ",
        "       su.id as owner_id, su.username as owner_username, su.nickname as owner_nickname, ",
        "       su.email as owner_email, su.phone as owner_phone, su.avatar as owner_avatar, ",
        "       su.dept_id as owner_dept_id, su.status as owner_status, ",
        "       su.create_time as owner_create_time, su.update_time as owner_update_time ",
        "FROM business_project bp ",
        "LEFT JOIN sys_user su ON bp.owner_id = su.id ",
        "WHERE bp.deleted = 0 ",
        "<if test='queryDTO.name != null and queryDTO.name != \"\"'>",
        "  AND bp.name LIKE CONCAT('%', #{queryDTO.name}, '%') ",
        "</if>",
        "<if test='queryDTO.description != null and queryDTO.description != \"\"'>",
        "  AND bp.description LIKE CONCAT('%', #{queryDTO.description}, '%') ",
        "</if>",
        "<if test='queryDTO.environment != null and queryDTO.environment != \"\"'>",
        "  AND bp.environment = #{queryDTO.environment} ",
        "</if>",
        "<if test='queryDTO.status != null and queryDTO.status != \"\"'>",
        "  AND bp.status = #{queryDTO.status} ",
        "</if>",
        "<if test='queryDTO.ownerId != null and queryDTO.ownerId != \"\"'>",
        "  AND bp.owner_id = #{queryDTO.ownerId} ",
        "</if>",
        "<if test='queryDTO.beginTime != null and queryDTO.beginTime != \"\"'>",
        "  AND bp.create_time >= #{queryDTO.beginTime} ",
        "</if>",
        "<if test='queryDTO.endTime != null and queryDTO.endTime != \"\"'>",
        "  AND bp.create_time &lt;= #{queryDTO.endTime} ",
        "</if>",
        "ORDER BY bp.create_time DESC",
        "</script>"
    })
    IPage<BusinessProject> selectProjectPageWithOwner(Page<BusinessProject> page, @Param("queryDTO") BusinessProjectQueryDTO queryDTO);

    /**
     * 根据ID查询业务项目（包含关联信息）
     *
     * @param id 项目ID
     * @return 业务项目
     */
    @Select({
        "SELECT bp.*, ",
        "       su.id as owner_id, su.username as owner_username, su.nickname as owner_nickname, ",
        "       su.email as owner_email, su.phone as owner_phone, su.avatar as owner_avatar, ",
        "       su.dept_id as owner_dept_id, su.status as owner_status, ",
        "       su.create_time as owner_create_time, su.update_time as owner_update_time ",
        "FROM business_project bp ",
        "LEFT JOIN sys_user su ON bp.owner_id = su.id ",
        "WHERE bp.id = #{id} AND bp.deleted = 0"
    })
    BusinessProject selectProjectWithOwnerById(@Param("id") String id);

    /**
     * 查询项目统计信息
     *
     * @param projectId 项目ID
     * @return 统计信息
     */
    @Select({
        "<script>",
        "SELECT ",
        "  COALESCE(agent_stats.agent_count, 0) as agent_count, ",
        "  COALESCE(agent_stats.agent_running_count, 0) as agent_running_count, ",
        "  0 as knowledge_count, ",
        "  0 as knowledge_built_count, ",
        "  0 as graph_count, ",
        "  0 as graph_relation_count ",
        "FROM (",
        "  SELECT ",
        "    COUNT(*) as agent_count, ",
        "    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as agent_running_count ",
        "  FROM agent ",
        "  WHERE project_id = #{projectId} AND deleted = 0",
        ") agent_stats",
        "</script>"
    })
    BusinessProject selectProjectStats(@Param("projectId") String projectId);
}
