package com.xhcai.common.api.query;

import java.time.LocalDateTime;

/**
 * 时间范围查询接口
 * 定义时间范围查询的基本方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TimeRangeable {

    /**
     * 获取开始时间（字符串格式）
     * 
     * @return 开始时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    String getBeginTime();

    /**
     * 获取结束时间（字符串格式）
     * 
     * @return 结束时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    String getEndTime();

    /**
     * 获取开始时间（LocalDateTime格式）
     * 
     * @return 开始时间LocalDateTime对象
     */
    default LocalDateTime getBeginDateTime() {
        String beginTime = getBeginTime();
        if (beginTime == null || beginTime.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(beginTime.replace(" ", "T"));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取结束时间（LocalDateTime格式）
     * 
     * @return 结束时间LocalDateTime对象
     */
    default LocalDateTime getEndDateTime() {
        String endTime = getEndTime();
        if (endTime == null || endTime.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(endTime.replace(" ", "T"));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 是否有时间范围条件
     * 
     * @return true-有时间范围条件，false-无时间范围条件
     */
    default Boolean hasTimeRange() {
        return getBeginTime() != null || getEndTime() != null;
    }

    /**
     * 是否有完整的时间范围（开始时间和结束时间都不为空）
     * 
     * @return true-有完整时间范围，false-时间范围不完整
     */
    default Boolean hasCompleteTimeRange() {
        return getBeginTime() != null && getEndTime() != null;
    }
}
