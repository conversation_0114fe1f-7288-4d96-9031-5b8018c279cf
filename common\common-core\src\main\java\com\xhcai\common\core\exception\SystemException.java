package com.xhcai.common.core.exception;

/**
 * 系统异常类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SystemException extends BaseException {

    private static final long serialVersionUID = 1L;

    public SystemException() {
        super();
    }

    public SystemException(String message) {
        super(message);
    }

    public SystemException(Integer code, String message) {
        super(code, message);
    }

    public SystemException(String message, Throwable cause) {
        super(message, cause);
    }

    public SystemException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
