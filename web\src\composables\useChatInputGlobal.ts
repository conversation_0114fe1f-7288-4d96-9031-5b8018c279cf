import { computed } from 'vue'
import { useChatInputStore, type ChatInputEventHandlers, type MessageData, type ToolType } from '@/stores/chatInputStore'

/**
 * 全局聊天输入组合式函数
 * 提供统一的事件处理和状态管理
 */
export function useChatInputGlobal(handlers?: ChatInputEventHandlers) {
  const chatInputStore = useChatInputStore()

  // 注册事件处理器
  if (handlers) {
    chatInputStore.registerEventHandlers(handlers)
  }

  // 返回所有需要的状态和方法
  return {
    // 状态
    inputMessage: computed(() => chatInputStore.inputMessage),
    uploadedFiles: computed(() => chatInputStore.uploadedFiles),
    uploadedImages: computed(() => chatInputStore.uploadedImages),
    recordedAudio: computed(() => chatInputStore.recordedAudio),
    isRecording: computed(() => chatInputStore.isRecording),
    isVoiceMode: computed(() => chatInputStore.isVoiceMode),
    recordingTime: computed(() => chatInputStore.recordingTime),
    recordingDuration: computed(() => chatInputStore.recordingDuration),
    isSending: computed(() => chatInputStore.isSending),
    canSend: computed(() => chatInputStore.canSend),

    // 事件处理方法
    handleSendMessage: chatInputStore.handleSendMessage,
    handleFileUpload: chatInputStore.handleFileUpload,
    handleSingleFileUpload: chatInputStore.handleSingleFileUpload,
    handleSingleImageUpload: chatInputStore.handleSingleImageUpload,
    handleToolSelect: chatInputStore.handleToolSelect,
    handleToggleVoiceRecording: chatInputStore.handleToggleVoiceRecording,
    handleToggleVoiceMode: chatInputStore.handleToggleVoiceMode,
    handleInputMessageUpdate: chatInputStore.handleInputMessageUpdate,
    handleRemoveFile: chatInputStore.handleRemoveFile,
    handleRemoveImage: chatInputStore.handleRemoveImage,
    handleRemoveRecordedAudio: chatInputStore.handleRemoveRecordedAudio,
    handlePlayRecordedAudio: chatInputStore.handlePlayRecordedAudio,
    handleRendererSelect: chatInputStore.handleRendererSelect,

    // 工具方法
    clearInputs: chatInputStore.clearInputs,
    resetState: chatInputStore.resetState,
    registerEventHandlers: chatInputStore.registerEventHandlers,
    clearEventHandlers: chatInputStore.clearEventHandlers,

    // 便捷的 props 对象（用于直接传递给 ChatInputArea）
    chatInputProps: computed(() => ({
      'is-sending': chatInputStore.isSending,
      'is-recording': chatInputStore.isRecording,
      'is-voice-mode': chatInputStore.isVoiceMode,
      'recording-time': chatInputStore.recordingTime,
      'recording-duration': chatInputStore.recordingDuration,
      'onSend-message': chatInputStore.handleSendMessage,
      'onFile-upload': chatInputStore.handleFileUpload,
      'onTool-select': chatInputStore.handleToolSelect,
      'onRenderer-select': chatInputStore.handleRendererSelect,
      'onToggle-voice-recording': chatInputStore.handleToggleVoiceRecording,
      'onToggle-voice-mode': chatInputStore.handleToggleVoiceMode,
      'onRemove-file': chatInputStore.handleRemoveFile,
      'onRemove-image': chatInputStore.handleRemoveImage,
      'onRemove-recorded-audio': chatInputStore.handleRemoveRecordedAudio,
      'onPlay-recorded-audio': chatInputStore.handlePlayRecordedAudio,
      'onUpdate:input-message': chatInputStore.handleInputMessageUpdate
    }))
  }
}

/**
 * 专门用于页面级组件的聊天输入处理
 * 提供完整的事件处理逻辑
 */
export function useChatInputForPage(config: {
  onSendMessage: (messageData: MessageData) => void | Promise<void>
  onFileUpload?: (file: File) => void
  onImageUpload?: (file: File) => void
  onToolSelect?: (tool: ToolType) => void
  onVoiceRecordingStart?: () => Promise<void>
  onVoiceRecordingStop?: () => void
  onVoiceModeToggle?: () => void
}) {
  return useChatInputGlobal({
    onSendMessage: config.onSendMessage,
    onFileUpload: config.onFileUpload,
    onImageUpload: config.onImageUpload,
    onToolSelect: config.onToolSelect,
    onVoiceRecordingStart: config.onVoiceRecordingStart,
    onVoiceRecordingStop: config.onVoiceRecordingStop,
    onVoiceModeToggle: config.onVoiceModeToggle,
    onInputMessageUpdate: (value: string) => {
      // 默认的输入更新处理
      console.log('输入内容更新:', value)
    },
    onRemoveFile: (index: number) => {
      console.log('移除文件:', index)
    },
    onRemoveImage: (index: number) => {
      console.log('移除图片:', index)
    },
    onRemoveRecordedAudio: () => {
      console.log('移除录音')
    },
    onPlayRecordedAudio: () => {
      console.log('播放录音')
    },
    onRendererSelect: (renderer: string) => {
      console.log('选择渲染器:', renderer)
    }
  })
}

/**
 * 简化版的聊天输入处理
 * 只处理基本的发送消息功能
 */
export function useChatInputSimple(onSendMessage: (messageData: MessageData) => void | Promise<void>) {
  return useChatInputGlobal({
    onSendMessage
  })
}
