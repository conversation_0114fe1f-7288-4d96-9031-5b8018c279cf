<template>
  <div class="task-management-overlay" v-show="visible" @click="handleOverlayClick">
    <div class="task-management-modal" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <div class="header-left">
          <h2 class="modal-title">
            <i class="fas fa-tasks"></i>
            任务管理
          </h2>
          <span class="knowledge-base-name">{{ props.knowledgeBaseName || '知识库' }}</span>
        </div>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 标签页 -->
      <div class="tab-container">
        <div class="tab-nav">
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'segment' }"
            @click="activeTab = 'segment'"
          >
            <i class="fas fa-cut"></i>
            文件分段任务
            <span class="task-count">{{ segmentTasks.length }}</span>
          </button>
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'vectorize' }"
            @click="activeTab = 'vectorize'"
          >
            <i class="fas fa-vector-square"></i>
            向量化任务
            <span class="task-count">{{ vectorizeTasks.length }}</span>
          </button>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input 
              type="text" 
              placeholder="搜索任务..." 
              v-model="searchQuery"
              @input="handleSearch"
            >
          </div>
          <select v-model="statusFilter" @change="handleFilter" class="status-filter">
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="processing">处理中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
          </select>
          <button class="btn btn-primary" @click="refreshTasks">
            <i class="fas fa-sync-alt"></i>
            刷新
          </button>
        </div>

        <!-- 任务列表 -->
        <div class="task-content">
          <!-- 文件分段任务 -->
          <div v-if="activeTab === 'segment'" class="task-list">
            <div class="task-stats">
              <div class="stat-item">
                <span class="stat-label">总任务数</span>
                <span class="stat-value">{{ filteredSegmentTasks.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">处理中</span>
                <span class="stat-value processing">{{ getTaskCountByStatus(filteredSegmentTasks, 'processing') }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">已完成</span>
                <span class="stat-value completed">{{ getTaskCountByStatus(filteredSegmentTasks, 'completed') }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">失败</span>
                <span class="stat-value failed">{{ getTaskCountByStatus(filteredSegmentTasks, 'failed') }}</span>
              </div>
            </div>

            <div class="task-items">
              <div 
                v-for="task in filteredSegmentTasks" 
                :key="task.id" 
                class="task-item"
                :class="task.status"
              >
                <div class="task-info">
                  <div class="task-header">
                    <div class="file-info">
                      <i class="fas fa-file-alt file-icon"></i>
                      <span class="file-name">{{ task.fileName }}</span>
                      <span class="task-status" :class="task.status">{{ getStatusText(task.status) }}</span>
                    </div>
                    <div class="task-actions">
                      <button 
                        v-if="task.status === 'failed'" 
                        class="btn btn-sm btn-outline"
                        @click="retryTask(task)"
                      >
                        <i class="fas fa-redo"></i>
                        重试
                      </button>
                      <button 
                        v-if="task.status === 'processing'" 
                        class="btn btn-sm btn-danger"
                        @click="cancelTask(task)"
                      >
                        <i class="fas fa-stop"></i>
                        取消
                      </button>
                      <button class="btn btn-sm btn-outline" @click="viewTaskDetail(task)">
                        <i class="fas fa-eye"></i>
                        详情
                      </button>
                    </div>
                  </div>
                  <div class="task-details">
                    <div class="detail-item">
                      <span class="label">创建时间：</span>
                      <span class="value">{{ formatDate(task.createdAt) }}</span>
                    </div>
                    <div class="detail-item" v-if="task.completedAt">
                      <span class="label">完成时间：</span>
                      <span class="value">{{ formatDate(task.completedAt) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">分段数量：</span>
                      <span class="value">{{ task.segmentCount || 0 }}</span>
                    </div>
                    <div class="detail-item" v-if="task.errorMessage">
                      <span class="label">错误信息：</span>
                      <span class="value error">{{ task.errorMessage }}</span>
                    </div>
                  </div>
                  <div class="progress-bar" v-if="task.status === 'processing'">
                    <div class="progress-fill" :style="{ width: task.progress + '%' }"></div>
                    <span class="progress-text">{{ task.progress }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 向量化任务 -->
          <div v-if="activeTab === 'vectorize'" class="task-list">
            <div class="task-stats">
              <div class="stat-item">
                <span class="stat-label">总任务数</span>
                <span class="stat-value">{{ filteredVectorizeTasks.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">处理中</span>
                <span class="stat-value processing">{{ getTaskCountByStatus(filteredVectorizeTasks, 'processing') }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">已完成</span>
                <span class="stat-value completed">{{ getTaskCountByStatus(filteredVectorizeTasks, 'completed') }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">失败</span>
                <span class="stat-value failed">{{ getTaskCountByStatus(filteredVectorizeTasks, 'failed') }}</span>
              </div>
            </div>

            <div class="task-items">
              <div 
                v-for="task in filteredVectorizeTasks" 
                :key="task.id" 
                class="task-item"
                :class="task.status"
              >
                <div class="task-info">
                  <div class="task-header">
                    <div class="file-info">
                      <i class="fas fa-vector-square file-icon"></i>
                      <span class="file-name">{{ task.fileName }}</span>
                      <span class="task-status" :class="task.status">{{ getStatusText(task.status) }}</span>
                    </div>
                    <div class="task-actions">
                      <button 
                        v-if="task.status === 'failed'" 
                        class="btn btn-sm btn-outline"
                        @click="retryTask(task)"
                      >
                        <i class="fas fa-redo"></i>
                        重试
                      </button>
                      <button 
                        v-if="task.status === 'processing'" 
                        class="btn btn-sm btn-danger"
                        @click="cancelTask(task)"
                      >
                        <i class="fas fa-stop"></i>
                        取消
                      </button>
                      <button class="btn btn-sm btn-outline" @click="viewTaskDetail(task)">
                        <i class="fas fa-eye"></i>
                        详情
                      </button>
                    </div>
                  </div>
                  <div class="task-details">
                    <div class="detail-item">
                      <span class="label">创建时间：</span>
                      <span class="value">{{ formatDate(task.createdAt) }}</span>
                    </div>
                    <div class="detail-item" v-if="task.completedAt">
                      <span class="label">完成时间：</span>
                      <span class="value">{{ formatDate(task.completedAt) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">向量数量：</span>
                      <span class="value">{{ task.vectorCount || 0 }}</span>
                    </div>
                    <div class="detail-item" v-if="task.errorMessage">
                      <span class="label">错误信息：</span>
                      <span class="value error">{{ task.errorMessage }}</span>
                    </div>
                  </div>
                  <div class="progress-bar" v-if="task.status === 'processing'">
                    <div class="progress-fill" :style="{ width: task.progress + '%' }"></div>
                    <span class="progress-text">{{ task.progress }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { formatDate } from '@/utils/fileUtils'
import { RagAPI } from '@/api/rag'

// Props
interface Props {
  visible: boolean
  datasetId: string
  knowledgeBaseName?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const activeTab = ref<'segment' | 'vectorize'>('segment')
const searchQuery = ref('')
const statusFilter = ref('')
const segmentTasks = ref<any[]>([])
const vectorizeTasks = ref<any[]>([])

// 计算属性
const filteredSegmentTasks = computed(() => {
  return filterTasks(segmentTasks.value)
})

const filteredVectorizeTasks = computed(() => {
  return filterTasks(vectorizeTasks.value)
})

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const filterTasks = (tasks: any[]) => {
  let filtered = tasks

  if (searchQuery.value) {
    filtered = filtered.filter(task => 
      task.fileName.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }

  return filtered
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 过滤逻辑已在计算属性中处理
}

const getTaskCountByStatus = (tasks: any[], status: string) => {
  return tasks.filter(task => task.status === status).length
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

const retryTask = async (task: any) => {
  try {
    if (activeTab.value === 'segment') {
      await RagAPI.retrySegmentationTask(task.id)
    } else {
      await RagAPI.retryVectorizationTask(task.id)
    }

    // 重新加载任务列表
    await loadTasks()
  } catch (error) {
    console.error('重试任务失败:', error)
    alert('重试任务失败，请稍后再试')
  }
}

const cancelTask = async (task: any) => {
  try {
    if (activeTab.value === 'segment') {
      await RagAPI.cancelSegmentationTask(task.id)
    } else {
      await RagAPI.cancelVectorizationTask(task.id)
    }

    // 重新加载任务列表
    await loadTasks()
  } catch (error) {
    console.error('取消任务失败:', error)
    alert('取消任务失败，请稍后再试')
  }
}

const viewTaskDetail = (task: any) => {
  console.log('查看任务详情:', task)
  // 这里实现查看详情逻辑
}

const refreshTasks = async () => {
  await loadTasks()
}

const loadTasks = async () => {
  try {
    // 加载分段任务
    const segmentResponse = await RagAPI.getSegmentationTasks({
      datasetId: props.datasetId,
      current: 1,
      size: 100
    })

    if (segmentResponse.success) {
      segmentTasks.value = segmentResponse.data.records || []
    }

    // 加载向量化任务
    const vectorResponse = await RagAPI.getVectorizationTasks({
      datasetId: props.datasetId,
      current: 1,
      size: 100
    })

    if (vectorResponse.success) {
      vectorizeTasks.value = vectorResponse.data.records || []
    }
  } catch (error) {
    console.error('加载任务失败:', error)

    // 如果API调用失败，使用模拟数据
    segmentTasks.value = [
      {
        id: '1',
        fileName: '技术文档.pdf',
        status: 'completed',
        createdAt: new Date('2024-01-15T10:00:00'),
        completedAt: new Date('2024-01-15T10:05:00'),
        segmentCount: 25,
        progress: 100
      },
      {
        id: '2',
        fileName: '用户手册.docx',
        status: 'processing',
        createdAt: new Date('2024-01-15T11:00:00'),
        segmentCount: 0,
        progress: 65
      },
      {
        id: '3',
        fileName: '产品介绍.txt',
        status: 'failed',
        createdAt: new Date('2024-01-15T12:00:00'),
        errorMessage: '文件格式不支持',
        segmentCount: 0,
        progress: 0
      }
    ]

    vectorizeTasks.value = [
      {
        id: '4',
        fileName: '技术文档.pdf',
        status: 'completed',
        createdAt: new Date('2024-01-15T10:10:00'),
        completedAt: new Date('2024-01-15T10:15:00'),
        vectorCount: 25,
        progress: 100
      },
      {
        id: '5',
        fileName: 'API文档.md',
        status: 'processing',
        createdAt: new Date('2024-01-15T11:30:00'),
        vectorCount: 0,
        progress: 30
      }
    ]
  }
}

// 生命周期
onMounted(() => {
  loadTasks()
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadTasks()
  }
})
</script>

<style scoped>
.task-management-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.task-management-modal {
  background: white;
  border-radius: 12px;
  width: 95%;
  height: 90%;
  max-width: 1400px;
  max-height: 900px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.knowledge-base-name {
  font-size: 14px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 12px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.tab-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-nav {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  background: white;
  padding: 0 24px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  color: #374151;
  background: #f8fafc;
}

.tab-btn.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

.task-count {
  background: #e2e8f0;
  color: #64748b;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.tab-btn.active .task-count {
  background: #dbeafe;
  color: #3b82f6;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: white;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
}

.search-box input:focus {
  outline: none;
  border-color: #3b82f6;
}

.status-filter {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.status-filter:focus {
  outline: none;
  border-color: #3b82f6;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-outline {
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.btn-danger {
  background: #dc2626;
  color: white;
}

.btn-danger:hover {
  background: #b91c1c;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.task-content {
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
}

.task-list {
  padding: 24px;
}

.task-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
  min-width: 120px;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.stat-value.processing {
  color: #f59e0b;
}

.stat-value.completed {
  color: #10b981;
}

.stat-value.failed {
  color: #dc2626;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.2s ease;
}

.task-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-item.processing {
  border-left: 4px solid #f59e0b;
}

.task-item.completed {
  border-left: 4px solid #10b981;
}

.task-item.failed {
  border-left: 4px solid #dc2626;
}

.task-item.pending {
  border-left: 4px solid #64748b;
}

.task-info {
  padding: 16px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #64748b;
  font-size: 16px;
}

.file-name {
  font-weight: 500;
  color: #1e293b;
}

.task-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.task-status.pending {
  background: #f1f5f9;
  color: #64748b;
}

.task-status.processing {
  background: #fef3c7;
  color: #f59e0b;
}

.task-status.completed {
  background: #d1fae5;
  color: #10b981;
}

.task-status.failed {
  background: #fee2e2;
  color: #dc2626;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.task-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

.detail-item {
  font-size: 12px;
}

.label {
  color: #64748b;
  font-weight: 500;
}

.value {
  color: #1e293b;
}

.value.error {
  color: #dc2626;
}

.progress-bar {
  position: relative;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 11px;
  color: #64748b;
}
</style>
