# Agent表添加platform_id字段修改文档

## 修改概述

为agent表增加了`platform_id`字段，用于关联第三方智能体平台ID，建立智能体与第三方平台的关联关系。

## 修改内容

### 1. 数据库层修改

#### 1.1 数据库迁移脚本
**文件**: `modules/xhcai-agent/src/main/resources/db/migration/add_platform_id_to_agent.sql`

- 添加`platform_id`字段：`VARCHAR(36)`，可空
- 添加字段注释：`第三方智能体平台ID，关联third_platform表`
- 创建索引：`idx_agent_platform_id`
- 创建复合索引：`idx_agent_platform_source`（platform_id + source_type）
- 预留外键约束代码（已注释）

#### 1.2 字段规格
```sql
ALTER TABLE agent ADD COLUMN platform_id VARCHAR(36) COMMENT "第三方智能体平台ID，关联third_platform表";
CREATE INDEX idx_agent_platform_id ON agent(platform_id);
CREATE INDEX idx_agent_platform_source ON agent(platform_id, source_type);
```

### 2. 实体层修改

#### 2.1 Agent实体类
**文件**: `modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/entity/Agent.java`

**新增字段**:
```java
/**
 * 第三方智能体平台ID
 */
@Column(name = "platform_id", length = 36)
@Schema(description = "第三方智能体平台ID", example = "platform_001")
@Size(max = 36, message = "平台ID长度不能超过36个字符")
@TableField("platform_id")
private String platformId;
```

**新增方法**:
- `getPlatformId()`
- `setPlatformId(String platformId)`

### 3. DTO层修改

#### 3.1 AgentCreateDTO
**文件**: `modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/dto/AgentCreateDTO.java`

**新增字段**:
```java
/**
 * 第三方智能体平台ID
 */
@Schema(description = "第三方智能体平台ID", example = "platform_001")
@Size(max = 36, message = "平台ID长度不能超过36个字符")
private String platformId;
```

#### 3.2 AgentUpdateDTO
**文件**: `modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/dto/AgentUpdateDTO.java`

**新增字段**:
```java
/**
 * 第三方智能体平台ID
 */
@Schema(description = "第三方智能体平台ID", example = "platform_001")
@Size(max = 36, message = "平台ID长度不能超过36个字符")
private String platformId;
```

同时补充了缺失的`sourceType`和`externalAgentId`字段。

### 4. VO层修改

#### 4.1 AgentVO
**文件**: `modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/vo/AgentVO.java`

**新增字段**:
```java
/**
 * 第三方智能体平台ID
 */
@Schema(description = "第三方智能体平台ID", example = "platform_001")
private String platformId;
```

### 5. 数据访问层修改

#### 5.1 AgentMapper
**文件**: `modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/mapper/AgentMapper.java`

**修改的查询方法**:
- `selectAgentPage()` - 分页查询
- `selectAgentPageWithTenant()` - 带租户信息的分页查询  
- `selectAgentById()` - 根据ID查询详情

**SQL修改**:
```sql
-- 在SELECT子句中添加
a.platform_id as platformId,
```

## 字段用途说明

### platform_id字段的作用

1. **关联第三方平台**: 当智能体来源为外部平台时，通过platform_id关联到third_platform表
2. **平台管理**: 便于按平台分组管理智能体
3. **权限控制**: 可以基于平台ID进行权限控制
4. **数据统计**: 支持按平台统计智能体数量和使用情况

### 使用场景

1. **外部智能体导入**: 从Dify等第三方平台导入智能体时设置platform_id
2. **平台切换**: 支持智能体在不同平台间迁移
3. **混合部署**: 同时支持本平台和第三方平台的智能体
4. **统一管理**: 在统一界面管理来自不同平台的智能体

## 数据关系

```
agent表 -----> third_platform表
platform_id    id (主键)

关联关系：
- agent.platform_id = third_platform.id
- 一个平台可以有多个智能体
- 一个智能体只能属于一个平台（可为空）
```

## 兼容性说明

### 向后兼容
- 新增字段为可空字段，不影响现有数据
- 现有API接口保持兼容
- 现有业务逻辑不受影响

### 数据迁移
- 现有智能体的platform_id默认为NULL
- 可根据需要批量更新现有数据的platform_id

## 部署说明

### 1. 数据库更新
```bash
# 执行数据库迁移脚本
mysql -u username -p database_name < modules/xhcai-agent/src/main/resources/db/migration/add_platform_id_to_agent.sql
```

### 2. 应用部署
```bash
# 重新编译agent模块
mvn clean compile -pl modules/xhcai-agent

# 重启应用
```

### 3. 验证部署
- 检查数据库表结构是否正确添加字段
- 验证API接口是否正常工作
- 测试新字段的读写功能

## 注意事项

1. **索引优化**: 已添加相关索引，提高查询性能
2. **外键约束**: 暂未启用外键约束，可根据需要后续添加
3. **数据一致性**: 需要确保platform_id的值在third_platform表中存在
4. **前端适配**: 前端界面需要相应调整以支持platform_id字段

## 后续扩展

1. **外键约束**: 可以启用外键约束确保数据一致性
2. **级联操作**: 可以设置级联删除或更新规则
3. **平台特定配置**: 可以基于platform_id实现平台特定的配置和功能
4. **多平台同步**: 支持智能体在多个平台间同步
