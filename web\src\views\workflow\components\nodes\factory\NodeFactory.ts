/**
 * 节点工厂
 * 根据节点类型动态创建对应的节点组件
 */

import type { Component } from 'vue'
import { NodeRegistry, type NodeComponent } from '../registry/NodeRegistry'
import type { NodeLibraryItem } from '../../../config/nodeLibrary'

/**
 * 节点创建选项
 */
export interface NodeCreateOptions {
  type: string
  data?: Record<string, any>
  position?: { x: number; y: number }
  id?: string
}

/**
 * 节点创建结果
 */
export interface NodeCreateResult {
  id: string
  type: string
  component: NodeComponent
  config: NodeLibraryItem
  data: Record<string, any>
  position: { x: number; y: number }
}

/**
 * 节点工厂类
 */
class NodeFactoryClass {
  private nodeIdCounter = 0

  /**
   * 生成唯一的节点ID
   */
  private generateNodeId(type: string): string {
    return `${type}_${Date.now()}_${++this.nodeIdCounter}`
  }

  /**
   * 创建节点
   */
  createNode(options: NodeCreateOptions): NodeCreateResult | null {
    const { type, data = {}, position = { x: 0, y: 0 }, id } = options

    // 从注册表获取节点信息
    const registration = NodeRegistry.getNode(type)
    if (!registration) {
      console.error(`Node type "${type}" not found in registry`)
      return null
    }

    // 生成节点ID
    const nodeId = id || this.generateNodeId(type)

    // 合并默认数据和传入数据
    const nodeData = {
      ...registration.config.defaultData,
      ...data
    }

    return {
      id: nodeId,
      type,
      component: registration.component,
      config: registration.config,
      data: nodeData,
      position
    }
  }

  /**
   * 批量创建节点
   */
  createNodes(optionsList: NodeCreateOptions[]): NodeCreateResult[] {
    const results: NodeCreateResult[] = []
    
    for (const options of optionsList) {
      const result = this.createNode(options)
      if (result) {
        results.push(result)
      }
    }

    return results
  }

  /**
   * 创建默认节点（使用节点类型的默认配置）
   */
  createDefaultNode(type: string, position?: { x: number; y: number }): NodeCreateResult | null {
    return this.createNode({
      type,
      position
    })
  }

  /**
   * 克隆节点
   */
  cloneNode(sourceNode: NodeCreateResult, position?: { x: number; y: number }): NodeCreateResult | null {
    return this.createNode({
      type: sourceNode.type,
      data: { ...sourceNode.data },
      position: position || { 
        x: sourceNode.position.x + 50, 
        y: sourceNode.position.y + 50 
      }
    })
  }

  /**
   * 验证节点类型是否可创建
   */
  canCreateNode(type: string): boolean {
    return NodeRegistry.hasNode(type)
  }

  /**
   * 获取可创建的节点类型列表
   */
  getAvailableNodeTypes(): string[] {
    return NodeRegistry.getAllNodeTypes()
  }

  /**
   * 根据类别获取可创建的节点类型
   */
  getAvailableNodeTypesByCategory(category: string): string[] {
    return NodeRegistry.getNodesByCategory(category)
  }

  /**
   * 获取节点类型的配置信息
   */
  getNodeTypeConfig(type: string): NodeLibraryItem | undefined {
    return NodeRegistry.getNodeConfig(type)
  }

  /**
   * 获取节点类型的组件
   */
  getNodeTypeComponent(type: string): NodeComponent | undefined {
    return NodeRegistry.getNodeComponent(type)
  }

  /**
   * 重置节点ID计数器
   */
  resetIdCounter(): void {
    this.nodeIdCounter = 0
  }

  /**
   * 获取工厂统计信息
   */
  getStats(): {
    availableTypes: number
    createdNodes: number
  } {
    return {
      availableTypes: NodeRegistry.getAllNodeTypes().length,
      createdNodes: this.nodeIdCounter
    }
  }
}

// 导出单例实例
export const NodeFactory = new NodeFactoryClass()

// 导出类型
export type { NodeFactoryClass }
