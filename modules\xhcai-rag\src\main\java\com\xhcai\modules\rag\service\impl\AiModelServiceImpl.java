package com.xhcai.modules.rag.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.rag.dto.AiModelCreateDTO;
import com.xhcai.modules.rag.dto.AiModelQueryDTO;
import com.xhcai.modules.rag.dto.AiModelUpdateDTO;
import com.xhcai.modules.rag.entity.AiModel;
import com.xhcai.modules.rag.mapper.AiModelMapper;
import com.xhcai.modules.rag.service.IAiModelService;
import com.xhcai.modules.rag.vo.AiModelVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI模型配置服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class AiModelServiceImpl extends ServiceImpl<AiModelMapper, AiModel> implements IAiModelService {

    private static final Logger log = LoggerFactory.getLogger(AiModelServiceImpl.class);

    @Override
    public PageResult<AiModelVO> selectAiModelPage(AiModelQueryDTO queryDTO) {
        // 创建分页对象
        Page<AiModelVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        
        // 执行分页查询
        IPage<AiModelVO> pageResult = baseMapper.selectAiModelPage(page, queryDTO);
        
        // 脱敏API密钥
        if (pageResult.getRecords() != null) {
            pageResult.getRecords().forEach(this::maskApiKey);
        }
        
        // 转换为PageResult
        return PageResult.of(
            pageResult.getRecords(),
            pageResult.getTotal(),
            pageResult.getCurrent(),
            pageResult.getSize()
        );
    }

    @Override
    public List<AiModelVO> selectAiModelList(AiModelQueryDTO queryDTO) {
        List<AiModelVO> list = baseMapper.selectAiModelList(queryDTO);
        
        // 脱敏API密钥
        if (list != null) {
            list.forEach(this::maskApiKey);
        }
        
        return list;
    }

    @Override
    public AiModelVO selectAiModelById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("模型ID不能为空");
        }
        
        AiModelVO modelVO = baseMapper.selectAiModelById(id);
        if (modelVO == null) {
            throw new BusinessException("AI模型不存在");
        }
        
        // 脱敏API密钥
        maskApiKey(modelVO);
        
        return modelVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAiModel(AiModelCreateDTO createDTO) {
        // 参数校验
        validateCreateDTO(createDTO);
        
        // 检查模型标识是否已存在
        if (existsModelId(createDTO.getModelId(), null)) {
            throw new BusinessException("模型标识已存在");
        }
        
        // 检查模型名称是否已存在
        if (existsModelName(createDTO.getName(), null)) {
            throw new BusinessException("模型名称已存在");
        }
        
        // 创建实体对象
        AiModel aiModel = new AiModel();
        BeanUtils.copyProperties(createDTO, aiModel);
        
        // 设置默认值
        if (!StringUtils.hasText(aiModel.getStatus())) {
            aiModel.setStatus("1");
        }
        
        boolean result = save(aiModel);
        if (result) {
            log.info("创建AI模型成功: {}", aiModel.getName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAiModel(AiModelUpdateDTO updateDTO) {
        // 参数校验
        validateUpdateDTO(updateDTO);
        
        // 检查模型是否存在
        AiModel existingModel = getById(updateDTO.getId());
        if (existingModel == null) {
            throw new BusinessException("AI模型不存在");
        }
        
        // 检查模型标识是否已存在（排除自己）
        if (existsModelId(updateDTO.getModelId(), updateDTO.getId())) {
            throw new BusinessException("模型标识已存在");
        }
        
        // 检查模型名称是否已存在（排除自己）
        if (existsModelName(updateDTO.getName(), updateDTO.getId())) {
            throw new BusinessException("模型名称已存在");
        }
        
        // 更新实体对象
        AiModel aiModel = new AiModel();
        BeanUtils.copyProperties(updateDTO, aiModel);
        
        boolean result = updateById(aiModel);
        if (result) {
            log.info("更新AI模型成功: {}", aiModel.getName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAiModel(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("模型ID不能为空");
        }
        
        AiModel aiModel = getById(id);
        if (aiModel == null) {
            throw new BusinessException("AI模型不存在");
        }
        
        boolean result = removeById(id);
        if (result) {
            log.info("删除AI模型成功: {}", aiModel.getName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteAiModels(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("删除的模型ID不能为空");
        }
        
        boolean result = removeByIds(ids);
        if (result) {
            log.info("批量删除AI模型成功，数量: {}", ids.size());
        }
        return result;
    }

    @Override
    public boolean existsModelId(String modelId, String excludeId) {
        if (!StringUtils.hasText(modelId)) {
            return false;
        }
        return baseMapper.checkModelIdExists(modelId, excludeId) > 0;
    }

    @Override
    public boolean existsModelName(String name, String excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        return baseMapper.checkModelNameExists(name, excludeId) > 0;
    }

    @Override
    public List<AiModelVO> selectByProvider(String provider) {
        if (!StringUtils.hasText(provider)) {
            throw new BusinessException("提供商不能为空");
        }
        
        List<AiModelVO> list = baseMapper.selectByProvider(provider);
        
        // 脱敏API密钥
        if (list != null) {
            list.forEach(this::maskApiKey);
        }
        
        return list;
    }

    @Override
    public List<AiModelVO> selectByType(String type) {
        if (!StringUtils.hasText(type)) {
            throw new BusinessException("模型类型不能为空");
        }
        
        List<AiModelVO> list = baseMapper.selectByType(type);
        
        // 脱敏API密钥
        if (list != null) {
            list.forEach(this::maskApiKey);
        }
        
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, String status) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("模型ID不能为空");
        }
        
        if (!StringUtils.hasText(status) || (!status.equals("0") && !status.equals("1"))) {
            throw new BusinessException("状态值必须为0或1");
        }
        
        int count = baseMapper.batchUpdateStatus(ids, status);
        boolean result = count > 0;
        
        if (result) {
            log.info("批量更新AI模型状态成功，数量: {}, 状态: {}", count, status);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableAiModel(String id) {
        return batchUpdateStatus(List.of(id), "1");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableAiModel(String id) {
        return batchUpdateStatus(List.of(id), "0");
    }

    @Override
    public List<Map<String, Object>> countByProvider() {
        return baseMapper.countByProvider();
    }

    @Override
    public List<Map<String, Object>> countByType() {
        return baseMapper.countByType();
    }

    @Override
    public List<AiModelVO> exportAiModels(AiModelQueryDTO queryDTO) {
        return selectAiModelList(queryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importAiModels(List<AiModel> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            throw new BusinessException("导入的模型列表不能为空");
        }
        
        int successCount = 0;
        int failureCount = 0;
        StringBuilder errorMsg = new StringBuilder();
        
        for (AiModel model : modelList) {
            try {
                // 检查必填字段
                if (!StringUtils.hasText(model.getName()) ||
                    !StringUtils.hasText(model.getModelId()) ||
                    !StringUtils.hasText(model.getProvider()) ||
                    !StringUtils.hasText(model.getType())) {
                    failureCount++;
                    errorMsg.append("模型 ").append(model.getName()).append(" 缺少必填字段；");
                    continue;
                }
                
                // 检查是否已存在
                if (existsModelId(model.getModelId(), null)) {
                    failureCount++;
                    errorMsg.append("模型标识 ").append(model.getModelId()).append(" 已存在；");
                    continue;
                }
                
                // 设置默认值
                if (!StringUtils.hasText(model.getStatus())) {
                    model.setStatus("1");
                }
                
                save(model);
                successCount++;
                
            } catch (Exception e) {
                failureCount++;
                errorMsg.append("模型 ").append(model.getName()).append(" 导入失败：").append(e.getMessage()).append("；");
                log.error("导入AI模型失败: {}", model.getName(), e);
            }
        }
        
        String result = String.format("导入完成，成功：%d，失败：%d", successCount, failureCount);
        if (errorMsg.length() > 0) {
            result += "，错误信息：" + errorMsg.toString();
        }
        
        log.info("AI模型导入结果: {}", result);
        return result;
    }

    @Override
    public Map<String, Object> testModelConnection(String id) {
        AiModel model = getById(id);
        if (model == null) {
            throw new BusinessException("AI模型不存在");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // TODO: 实现具体的模型连接测试逻辑
            // 这里可以根据不同的提供商实现不同的测试逻辑
            
            result.put("success", true);
            result.put("message", "连接测试成功");
            result.put("responseTime", 100); // 模拟响应时间
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "连接测试失败：" + e.getMessage());
            log.error("AI模型连接测试失败: {}", model.getName(), e);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyAiModel(String id, String newName, String newModelId) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("源模型ID不能为空");
        }
        
        if (!StringUtils.hasText(newName)) {
            throw new BusinessException("新模型名称不能为空");
        }
        
        if (!StringUtils.hasText(newModelId)) {
            throw new BusinessException("新模型标识不能为空");
        }
        
        // 检查源模型是否存在
        AiModel sourceModel = getById(id);
        if (sourceModel == null) {
            throw new BusinessException("源AI模型不存在");
        }
        
        // 检查新模型标识是否已存在
        if (existsModelId(newModelId, null)) {
            throw new BusinessException("新模型标识已存在");
        }
        
        // 检查新模型名称是否已存在
        if (existsModelName(newName, null)) {
            throw new BusinessException("新模型名称已存在");
        }
        
        // 复制模型
        AiModel newModel = new AiModel();
        BeanUtils.copyProperties(sourceModel, newModel);
        newModel.setId(null); // 清空ID，让数据库自动生成
        newModel.setName(newName);
        newModel.setModelId(newModelId);
        
        boolean result = save(newModel);
        if (result) {
            log.info("复制AI模型成功: {} -> {}", sourceModel.getName(), newName);
        }
        return result;
    }

    /**
     * 脱敏API密钥
     *
     * @param modelVO 模型VO
     */
    private void maskApiKey(AiModelVO modelVO) {
        if (modelVO != null && StringUtils.hasText(modelVO.getApiKey())) {
            String apiKey = modelVO.getApiKey();
            if (apiKey.length() > 8) {
                modelVO.setApiKey(apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4));
            } else {
                modelVO.setApiKey("****");
            }
        }
    }

    /**
     * 校验创建DTO
     *
     * @param createDTO 创建DTO
     */
    private void validateCreateDTO(AiModelCreateDTO createDTO) {
        if (createDTO == null) {
            throw new BusinessException("创建参数不能为空");
        }
        
        if (!StringUtils.hasText(createDTO.getName())) {
            throw new BusinessException("模型名称不能为空");
        }
        
        if (!StringUtils.hasText(createDTO.getModelId())) {
            throw new BusinessException("模型标识不能为空");
        }
        
        if (!StringUtils.hasText(createDTO.getProvider())) {
            throw new BusinessException("模型提供商不能为空");
        }
        
        if (!StringUtils.hasText(createDTO.getType())) {
            throw new BusinessException("模型类型不能为空");
        }
    }

    /**
     * 校验更新DTO
     *
     * @param updateDTO 更新DTO
     */
    private void validateUpdateDTO(AiModelUpdateDTO updateDTO) {
        if (updateDTO == null) {
            throw new BusinessException("更新参数不能为空");
        }
        
        if (!StringUtils.hasText(updateDTO.getId())) {
            throw new BusinessException("模型ID不能为空");
        }
        
        if (!StringUtils.hasText(updateDTO.getName())) {
            throw new BusinessException("模型名称不能为空");
        }
        
        if (!StringUtils.hasText(updateDTO.getModelId())) {
            throw new BusinessException("模型标识不能为空");
        }
        
        if (!StringUtils.hasText(updateDTO.getProvider())) {
            throw new BusinessException("模型提供商不能为空");
        }
        
        if (!StringUtils.hasText(updateDTO.getType())) {
            throw new BusinessException("模型类型不能为空");
        }
    }
}
