<template>
  <!-- 图标选择器模态框 -->
  <div class="icon-selector-modal" v-show="visible" @click="closeIconSelector">
    <div class="icon-selector-content" @click.stop>
      <div class="icon-selector-header">
        <h3>选择图标</h3>
        <button class="close-btn" @click="closeIconSelector">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="icon-selector-body">
        <div class="icon-tabs">
          <button
            :class="['icon-tab', { active: iconTab === 'system' }]"
            @click="iconTab = 'system'"
          >
            系统图标
          </button>
          <button
            :class="['icon-tab', { active: iconTab === 'upload' }]"
            @click="iconTab = 'upload'"
          >
            上传图片
          </button>
        </div>

        <!-- 系统图标 -->
        <div class="system-icons" v-if="iconTab === 'system'">
          <!-- 背景颜色选择 -->
          <div class="color-selector-section">
            <h4>选择背景颜色</h4>
            <div class="color-options">
              <div
                v-for="color in backgroundColors"
                :key="color.name"
                :class="['color-option', { selected: currentSelectedBackground === color.gradient }]"
                :style="{ background: color.gradient }"
                @click="selectBackground(color.gradient)"
                :title="color.name"
              >
                <i class="fas fa-check" v-if="currentSelectedBackground === color.gradient"></i>
              </div>
            </div>
          </div>

          <!-- 图标预览 -->
          <div class="icon-preview-section" v-if="currentSelectedIcon">
            <h4>预览效果</h4>
            <div class="preview-icon" :style="{ background: currentSelectedBackground }">
              <i :class="currentSelectedIcon"></i>
            </div>
          </div>

          <!-- 图标选择 -->
          <div class="icons-grid">
            <div
              v-for="icon in systemIcons"
              :key="icon"
              :class="['icon-option', { selected: currentSelectedIcon === icon }]"
              @click="selectIcon(icon)"
              :style="{ background: currentSelectedIcon === icon ? currentSelectedBackground : '#ffffff' }"
            >
              <i :class="icon"></i>
            </div>
          </div>
        </div>

        <!-- 上传图片 -->
        <div class="upload-section" v-if="iconTab === 'upload'">
          <div class="upload-area" @click="triggerFileUpload">
            <input
              type="file"
              ref="fileInput"
              @change="handleFileUpload"
              accept="image/*"
              style="display: none;"
            >
            <div class="upload-content">
              <i class="fas fa-cloud-upload-alt"></i>
              <p>点击上传图片</p>
              <span>支持 JPG、PNG、GIF 格式，大小不超过 2MB</span>
            </div>
          </div>
        </div>
      </div>
      <div class="icon-selector-footer">
        <button class="btn-modern btn-secondary" @click="closeIconSelector">取消</button>
        <button class="btn-modern btn-primary" @click="confirmIconSelection">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
interface Props {
  visible: boolean
  selectedIcon?: string
  selectedBackground?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedIcon: '',
  selectedBackground: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'select': [data: { icon: string; background: string }]
}>()

// 响应式数据
const iconTab = ref('system')
const currentSelectedIcon = ref('')
const currentSelectedBackground = ref('')
const fileInput = ref<HTMLInputElement | null>(null)

// 系统图标列表
const systemIcons = ref([
  'fas fa-robot', 'fas fa-headset', 'fas fa-chart-line', 'fas fa-code',
  'fas fa-pen-fancy', 'fas fa-image', 'fas fa-language', 'fas fa-edit',
  'fas fa-microphone', 'fas fa-thumbs-up', 'fas fa-file-alt', 'fas fa-heart',
  'fas fa-question-circle', 'fas fa-sitemap', 'fas fa-users', 'fas fa-bullhorn',
  'fas fa-shopping-cart', 'fas fa-file-pdf', 'fas fa-clipboard-list', 'fas fa-comments',
  'fas fa-book', 'fas fa-tasks', 'fas fa-calculator', 'fas fa-user-tie',
  'fas fa-warehouse', 'fas fa-tools', 'fas fa-cog', 'fas fa-flask',
  'fas fa-brain', 'fas fa-lightbulb', 'fas fa-magic', 'fas fa-rocket',
  'fas fa-star', 'fas fa-gem', 'fas fa-crown', 'fas fa-shield-alt'
])

// 背景颜色选项
const backgroundColors = ref([
  { name: '蓝紫渐变', gradient: 'linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)' },
  { name: '粉紫渐变', gradient: 'linear-gradient(135deg, #f093fb 0%, #f093fbaa 100%)' },
  { name: '蓝青渐变', gradient: 'linear-gradient(135deg, #4facfe 0%, #4facfeaa 100%)' },
  { name: '绿色渐变', gradient: 'linear-gradient(135deg, #43e97b 0%, #43e97baa 100%)' },
  { name: '粉红渐变', gradient: 'linear-gradient(135deg, #fa709a 0%, #fa709aaa 100%)' },
  { name: '黄色渐变', gradient: 'linear-gradient(135deg, #ffecd2 0%, #ffecd2aa 100%)' },
  { name: '橙色渐变', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #ff9a9eaa 100%)' },
  { name: '紫色渐变', gradient: 'linear-gradient(135deg, #a8edea 0%, #a8edeaaa 100%)' },
  { name: '红色渐变', gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ff6b6baa 100%)' },
  { name: '青色渐变', gradient: 'linear-gradient(135deg, #74b9ff 0%, #74b9ffaa 100%)' },
  { name: '薄荷渐变', gradient: 'linear-gradient(135deg, #00b894 0%, #00b894aa 100%)' },
  { name: '金色渐变', gradient: 'linear-gradient(135deg, #fdcb6e 0%, #fdcb6eaa 100%)' }
])

// 监听 selectedIcon 变化
watch(() => props.selectedIcon, (newIcon) => {
  currentSelectedIcon.value = newIcon || ''
}, { immediate: true })

// 监听 selectedBackground 变化
watch(() => props.selectedBackground, (newBackground) => {
  currentSelectedBackground.value = newBackground || backgroundColors.value[0].gradient
}, { immediate: true })

// 监听 visible 变化，重置状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    currentSelectedIcon.value = props.selectedIcon || ''
    currentSelectedBackground.value = props.selectedBackground || backgroundColors.value[0].gradient
    iconTab.value = 'system'
  }
})

// 方法
const closeIconSelector = () => {
  emit('update:visible', false)
}

const selectIcon = (icon: string) => {
  currentSelectedIcon.value = icon
}

const selectBackground = (background: string) => {
  currentSelectedBackground.value = background
}

const confirmIconSelection = () => {
  if (currentSelectedIcon.value && currentSelectedBackground.value) {
    emit('select', {
      icon: currentSelectedIcon.value,
      background: currentSelectedBackground.value
    })
  }
  closeIconSelector()
}

const triggerFileUpload = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    // 检查文件大小（2MB限制）
    if (file.size > 2 * 1024 * 1024) {
      alert('文件大小不能超过2MB')
      return
    }

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件')
      return
    }

    // 这里可以添加文件上传逻辑
    alert('文件上传功能开发中...')
  }
}
</script>

<style scoped>
/* 图标选择器模态框 */
.icon-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10001;
  backdrop-filter: blur(4px);
}

.icon-selector-content {
  background: #ffffff;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  border: 1px solid #f1f5f9;
}

.icon-selector-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
}

.icon-selector-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.icon-selector-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.icon-tabs {
  display: flex;
  border-bottom: 1px solid #f1f5f9;
}

.icon-tab {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: #f8fafc;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.icon-tab.active {
  background: #ffffff;
  color: #1a202c;
  border-bottom: 2px solid #3b82f6;
}

.icon-tab:hover:not(.active) {
  background: #f1f5f9;
  color: #4b5563;
}

.system-icons {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* 背景颜色选择器 */
.color-selector-section {
  margin-bottom: 20px;
}

.color-selector-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
  margin-bottom: 20px;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  position: relative;
}

.color-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.color-option.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.color-option i {
  color: #ffffff;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 图标预览 */
.icon-preview-section {
  margin-bottom: 20px;
  text-align: center;
}

.icon-preview-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
}

.preview-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.preview-icon i {
  font-size: 32px;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.icons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 12px;
}

.icon-option {
  width: 60px;
  height: 60px;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  font-size: 20px;
  color: #6b7280;
}

.icon-option:hover {
  border-color: #3b82f6;
  background: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  color: #3b82f6;
}

.icon-option.selected {
  border-color: #3b82f6;
  background: #eff6ff;
  color: #2563eb;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.upload-section {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area {
  width: 100%;
  height: 200px;
  border: 2px dashed #cbd5e0;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f8fafc;
}

.upload-area:hover {
  border-color: #3b82f6;
  background: #f0f7ff;
}

.upload-content {
  text-align: center;
  color: #4b5563;
}

.upload-content i {
  font-size: 48px;
  margin-bottom: 12px;
  color: #3b82f6;
}

.upload-content p {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #1a202c;
}

.upload-content span {
  font-size: 12px;
  color: #6b7280;
}

.icon-selector-footer {
  padding: 16px 24px;
  border-top: 1px solid #f1f5f9;
  background: #ffffff;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e7eb;
}

.close-btn:hover {
  background: #ffffff;
  color: #374151;
  border-color: #d1d5db;
}

/* 按钮样式 */
.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn-modern.btn-secondary {
  background: #ffffff;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.btn-modern.btn-secondary:hover {
  background: #f9fafb;
  color: #1f2937;
  border-color: #9ca3af;
}

.btn-modern.btn-primary {
  background: #3b82f6;
  color: #ffffff;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-modern.btn-primary:hover {
  background: #2563eb;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
}

.btn-modern.btn-primary:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .icon-selector-content {
    width: 95%;
    max-height: 90vh;
  }

  .icons-grid {
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    gap: 8px;
  }

  .icon-option {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
}
</style>
