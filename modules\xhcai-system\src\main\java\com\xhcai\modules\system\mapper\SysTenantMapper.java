package com.xhcai.modules.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysTenant;

/**
 * 租户信息Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysTenantMapper extends BaseMapper<SysTenant> {

    /**
     * 分页查询租户列表
     *
     * @param page 分页参数
     * @param tenantCode 租户编码
     * @param tenantName 租户名称
     * @param contactPerson 联系人
     * @param contactPhone 联系电话
     * @param contactEmail 联系邮箱
     * @param status 租户状态
     * @param includeExpired 是否包含过期租户
     * @return 租户分页列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_tenant "
            + "WHERE deleted = 0 "
            + "<if test='tenantCode != null and tenantCode != \"\"'>"
            + "  AND tenant_code LIKE CONCAT('%', #{tenantCode}, '%') "
            + "</if>"
            + "<if test='tenantName != null and tenantName != \"\"'>"
            + "  AND tenant_name LIKE CONCAT('%', #{tenantName}, '%') "
            + "</if>"
            + "<if test='contactPerson != null and contactPerson != \"\"'>"
            + "  AND contact_person LIKE CONCAT('%', #{contactPerson}, '%') "
            + "</if>"
            + "<if test='contactPhone != null and contactPhone != \"\"'>"
            + "  AND contact_phone LIKE CONCAT('%', #{contactPhone}, '%') "
            + "</if>"
            + "<if test='contactEmail != null and contactEmail != \"\"'>"
            + "  AND contact_email LIKE CONCAT('%', #{contactEmail}, '%') "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "  AND status = #{status} "
            + "</if>"
            + "<if test='includeExpired != null and !includeExpired'>"
            + "  AND (expire_time IS NULL OR expire_time > NOW()) "
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    IPage<SysTenant> selectTenantPage(
            Page<SysTenant> page,
            @Param("tenantCode") String tenantCode,
            @Param("tenantName") String tenantName,
            @Param("contactPerson") String contactPerson,
            @Param("contactPhone") String contactPhone,
            @Param("contactEmail") String contactEmail,
            @Param("status") String status,
            @Param("includeExpired") Boolean includeExpired
    );

    /**
     * 根据租户编码查询租户信息
     *
     * @param tenantCode 租户编码
     * @return 租户信息
     */
    @Select("SELECT * FROM sys_tenant WHERE tenant_code = #{tenantCode} AND deleted = 0")
    SysTenant selectByTenantCode(@Param("tenantCode") String tenantCode);

    /**
     * 根据租户域名查询租户信息
     *
     * @param domain 租户域名
     * @return 租户信息
     */
    @Select("SELECT * FROM sys_tenant WHERE domain = #{domain} AND deleted = 0")
    SysTenant selectByDomain(@Param("domain") String domain);

    /**
     * 统计租户下的用户数量
     *
     * @param tenantId 租户ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE tenant_id = #{tenantId} AND deleted = 0")
    Integer countUsersByTenantId(@Param("tenantId") String tenantId);

    /**
     * 检查租户编码是否存在
     *
     * @param tenantCode 租户编码
     * @param excludeId 排除的租户ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_tenant "
            + "WHERE tenant_code = #{tenantCode} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "  AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsTenantCode(@Param("tenantCode") String tenantCode, @Param("excludeId") String excludeId);

    /**
     * 检查租户域名是否存在
     *
     * @param domain 租户域名
     * @param excludeId 排除的租户ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_tenant "
            + "WHERE domain = #{domain} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "  AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsDomain(@Param("domain") String domain, @Param("excludeId") String excludeId);

    /**
     * 查询即将过期的租户列表
     *
     * @param days 提前天数
     * @return 即将过期的租户列表
     */
    @Select("SELECT * FROM sys_tenant "
            + "WHERE deleted = 0 AND status = 0 "
            + "AND expire_time IS NOT NULL "
            + "AND expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) "
            + "ORDER BY expire_time ASC")
    java.util.List<SysTenant> selectExpiringTenants(@Param("days") Integer days);

    /**
     * 查询已过期的租户列表
     *
     * @return 已过期的租户列表
     */
    @Select("SELECT * FROM sys_tenant "
            + "WHERE deleted = 0 AND status != '2' "
            + "AND expire_time IS NOT NULL AND expire_time < NOW() "
            + "ORDER BY expire_time ASC")
    java.util.List<SysTenant> selectExpiredTenants();
}
