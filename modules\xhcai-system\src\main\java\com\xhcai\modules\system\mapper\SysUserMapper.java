package com.xhcai.modules.system.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysUser;

/**
 * 用户信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户名查询用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND deleted = 0")
    SysUser selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户信息
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email} AND deleted = 0")
    SysUser selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone} AND deleted = 0")
    SysUser selectByPhone(@Param("phone") String phone);

    /**
     * 根据用户ID查询用户信息（忽略租户隔离）
     * 此方法专门用于JWT认证过滤器，绕过租户隔离限制
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE id = #{userId} AND deleted = 0")
    SysUser selectByIdIgnoreTenant(@Param("userId") String userId);

    /**
     * 分页查询用户列表
     *
     * @param page 分页参数
     * @param username 用户名（模糊查询）
     * @param nickname 昵称（模糊查询）
     * @param email 邮箱（模糊查询）
     * @param phone 手机号（模糊查询）
     * @param status 状态
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @param dataScope 数据权限SQL
     * @return 用户分页列表
     */
    @Select("<script>"
            + "SELECT u.* FROM sys_user u "
            + "LEFT JOIN sys_dept d ON u.dept_id = d.id "
            + "<where>"
            + "u.deleted = 0 "
            + "<if test='tenantId != null and tenantId != \"\"'>AND u.tenant_id = #{tenantId}</if> "
            + "<if test='username != null and username != \"\"'>AND u.username LIKE CONCAT('%', #{username}, '%')</if> "
            + "<if test='nickname != null and nickname != \"\"'>AND u.nickname LIKE CONCAT('%', #{nickname}, '%')</if> "
            + "<if test='email != null and email != \"\"'>AND u.email LIKE CONCAT('%', #{email}, '%')</if> "
            + "<if test='phone != null and phone != \"\"'>AND u.phone LIKE CONCAT('%', #{phone}, '%')</if> "
            + "<if test='status != null and status != \"\"'>AND u.status = #{status}</if> "
            + "<if test='deptId != null and deptId != \"\"'>AND u.dept_id = #{deptId}</if> "
            + "<if test='dataScope != null and dataScope != \"\"'>${dataScope}</if> "
            + "</where>"
            + "ORDER BY u.create_time DESC"
            + "</script>")
    IPage<SysUser> selectUserPage(Page<SysUser> page,
            @Param("username") String username,
            @Param("nickname") String nickname,
            @Param("email") String email,
            @Param("phone") String phone,
            @Param("status") String status,
            @Param("deptId") String deptId,
            @Param("tenantId") String tenantId,
            @Param("dataScope") String dataScope);

    /**
     * 根据用户ID查询用户权限
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 权限集合
     */
    @Select("SELECT DISTINCT sp.permission_code "
            + "FROM sys_user_role sur "
            + "LEFT JOIN sys_role_permission srp ON sur.role_id = srp.role_id "
            + "LEFT JOIN sys_permission sp ON srp.permission_id = sp.id "
            + "WHERE sur.user_id = #{userId} AND sur.tenant_id = #{tenantId} "
            + "AND sur.deleted = 0 AND srp.deleted = 0 AND sp.deleted = 0 AND sp.status = '0'")
    Set<String> selectUserPermissions(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 根据用户ID查询用户角色
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 角色集合
     */
    @Select("SELECT DISTINCT sr.role_code "
            + "FROM sys_user_role sur "
            + "LEFT JOIN sys_role sr ON sur.role_id = sr.id "
            + "WHERE sur.user_id = #{userId} AND sur.tenant_id = #{tenantId} "
            + "AND sur.deleted = 0 AND sr.deleted = 0 AND sr.status = '0'")
    Set<String> selectUserRoles(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 根据角色ID查询用户列表
     *
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户列表
     */
    @Select("SELECT su.* FROM sys_user su "
            + "INNER JOIN sys_user_role sur ON su.id = sur.user_id "
            + "WHERE sur.role_id = #{roleId} AND sur.tenant_id = #{tenantId} "
            + "AND su.deleted = 0 AND sur.deleted = 0 "
            + "ORDER BY su.create_time DESC")
    List<SysUser> selectUsersByRoleId(@Param("roleId") String roleId, @Param("tenantId") String tenantId);

    /**
     * 根据部门ID查询用户列表
     *
     * @param deptId 部门ID
     * @param tenantId 租户ID
     * @return 用户列表
     */
    @Select("SELECT * FROM sys_user "
            + "WHERE dept_id = #{deptId} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "ORDER BY create_time DESC")
    List<SysUser> selectUsersByDeptId(@Param("deptId") String deptId, @Param("tenantId") String tenantId);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param userId 用户ID（排除自己）
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(1) > 0 FROM sys_user "
            + "WHERE username = #{username} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "<if test='userId != null'>AND id != #{userId}</if>"
            + "</script>")
    boolean existsUsername(@Param("username") String username, @Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param userId 用户ID（排除自己）
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(1) > 0 FROM sys_user "
            + "WHERE email = #{email} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "<if test='userId != null'>AND id != #{userId}</if>"
            + "</script>")
    boolean existsEmail(@Param("email") String email, @Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param userId 用户ID（排除自己）
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(1) > 0 FROM sys_user "
            + "WHERE phone = #{phone} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "<if test='userId != null'>AND id != #{userId}</if>"
            + "</script>")
    boolean existsPhone(@Param("phone") String phone, @Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 更新用户登录信息
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @param loginTime 登录时间
     */
    @Update("UPDATE sys_user SET login_ip = #{loginIp}, login_time = #{loginTime}::timestamp, "
            + "update_time = CURRENT_TIMESTAMP WHERE id = #{userId}")
    void updateLoginInfo(@Param("userId") String userId, @Param("loginIp") String loginIp, @Param("loginTime") String loginTime);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param password 新密码
     */
    @Update("UPDATE sys_user SET password = #{password}, update_time = CURRENT_TIMESTAMP WHERE id = #{userId}")
    void resetPassword(@Param("userId") String userId, @Param("password") String password);
}
