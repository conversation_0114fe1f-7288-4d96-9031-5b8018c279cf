package com.xhcai.modules.system.service;

/**
 * 系统查询服务接口
 * 用于处理跨模块的关联查询，避免循环依赖
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISystemQueryService {

    /**
     * 根据部门ID查询部门名称
     *
     * @param deptId 部门ID
     * @return 部门名称
     */
    String getDeptNameById(String deptId);

    /**
     * 根据用户ID查询用户姓名
     *
     * @param userId 用户ID
     * @return 用户姓名
     */
    String getUserNameById(String userId);

    /**
     * 根据部门ID统计用户数量
     *
     * @param deptId 部门ID
     * @return 用户数量
     */
    Integer countUsersByDeptId(String deptId);
}
