spring:
  # JPA配置 - 生产环境验证模式
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: com.xhcai.common.datasource.config.CustomPhysicalNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    
  # 动态数据源配置（生产环境）
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组，默认值即为 master
      primary: master
      # 严格模式，默认false. 设置为true后在未匹配到指定数据源时候会抛出异常
      strict: true
      # 数据源配置
      datasource:
        # 主数据源（系统核心数据）
        master:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:xhcai_plus}?currentSchema=xhcai_plus&useSSL=true&serverTimezone=GMT%2B8
          username: ${DB_USERNAME:postgres}
          password: ${DB_PASSWORD:123456}
          # 生产环境连接池配置
          druid:
            initial-size: 20
            min-idle: 20
            max-active: 300
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20

        # AI数据源（AI相关数据）
        ai:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${AI_DB_HOST:localhost}:${AI_DB_PORT:5432}/${AI_DB_NAME:xhcai_plus}?currentSchema=xhcai_ai&useSSL=true&serverTimezone=GMT%2B8
          username: ${AI_DB_USERNAME:postgres}
          password: ${AI_DB_PASSWORD:123456}
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 100
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20

        # dify数据源（dify相关数据）
        dify:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${DIFY_DB_HOST:localhost}:${DIFY_DB_PORT:5432}/${DIFY_DB_NAME:dify}?currentSchema=${DIFY_DB_SCHEMA:public}&useSSL=true&serverTimezone=GMT%2B8
          username: ${DIFY_DB_USERNAME:postgres}
          password: ${DIFY_DB_PASSWORD:123456}
          druid:
            initial-size: ${DIFY_DB_DRUID_INITIAL_SIZE:3}
            min-idle: ${DIFY_DB_DRUID_MIN_IDLE:3}
            max-active: ${DIFY_DB_DRUID_MAX_ACTIVE:15}
            max-wait: ${DIFY_DB_DRUID_MAX_WAIT:60000}
            time-between-eviction-runs-millis: ${DIFY_DB_DRUID_TIME_BETWEEN_EVICTION_RUNS_MILLIS:60000}
            min-evictable-idle-time-millis: ${DIFY_DB_DRUID_MIN_EVICTABLE_IDLE_TIME_MILLIS:300000}
            validation-query: ${DIFY_DB_DRUID_VALIDATION_QUERY:SELECT 1}
            test-while-idle: ${DIFY_DB_DRUID_TEST_WHILE_IDLE:true}
            test-on-borrow: ${DIFY_DB_DRUID_TEST_ON_BORROW:false}
            test-on-return: ${DIFY_DB_DRUID_TEST_ON_RETURN:false}
            pool-prepared-statements: ${DIFY_DB_DRUID_POOL_PREPARED_STATEMENTS:true}
            max-pool-prepared-statement-per-connection-size: ${DIFY_DB_DRUID_MAX_POOL_PREPARED_STATEMENT_PER_CONNECTION_SIZE:20}

        # 日志数据源（日志数据）
        log:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${LOG_DB_HOST:localhost}:${LOG_DB_PORT:5432}/${LOG_DB_NAME:xhcai_plus}?currentSchema=xhcai_log&useSSL=true&serverTimezone=GMT%2B8
          username: ${LOG_DB_USERNAME:postgres}
          password: ${LOG_DB_PASSWORD:123456}
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 50
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 10s
      lettuce:
        pool:
          min-idle: 10
          max-idle: 20
          max-active: 50
          max-wait: -1ms
      
  # AI配置（生产环境）
  ai:
    # 嵌入模型提供商选择：openai 或 ollama
    embedding:
      provider: ${AI_EMBEDDING_PROVIDER:openai}

    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: ${OPENAI_CHAT_MODEL:gpt-3.5-turbo}
          temperature: ${OPENAI_TEMPERATURE:0.7}
          max-tokens: ${OPENAI_MAX_TOKENS:4096}
      embedding:
        options:
          model: ${OPENAI_EMBEDDING_MODEL:text-embedding-ada-002}

    ollama:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
          model: ${OLLAMA_CHAT_MODEL:llama2}
          temperature: ${OLLAMA_TEMPERATURE:0.7}
      embedding:
        options:
          model: ${OLLAMA_EMBEDDING_MODEL:nomic-embed-text}

    # PgVector配置
    vectorstore:
      pgvector:
        index-type: ${PGVECTOR_INDEX_TYPE:HNSW}
        distance-type: ${PGVECTOR_DISTANCE_TYPE:COSINE_DISTANCE}
        dimensions: ${PGVECTOR_DIMENSIONS:1536}  # 根据使用的embedding模型调整

# 日志配置
logging:
  level:
    root: warn
    com.xhcai: info
    com.xhcai.plugin: info
    org.pf4j: warn
    org.springframework.security: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:/var/log/xhcai-plus/application.log}
  logback:
    rollingpolicy:
      max-file-size: ${LOG_MAX_FILE_SIZE:500MB}
      max-history: ${LOG_MAX_HISTORY:90}
      total-size-cap: ${LOG_TOTAL_SIZE_CAP:10GB}

# Actuator监控配置（生产环境限制访问）
management:
  endpoints:
    web:
      exposure:
        include: ${MANAGEMENT_ENDPOINTS_INCLUDE:health,info,metrics,prometheus,plugin-context}
      base-path: ${MANAGEMENT_ENDPOINTS_BASE_PATH:/actuator}
  endpoint:
    health:
      show-details: ${MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS:when-authorized}
      show-components: ${MANAGEMENT_ENDPOINT_HEALTH_SHOW_COMPONENTS:when-authorized}
    plugin-context:
      enabled: ${MANAGEMENT_ENDPOINT_PLUGIN_CONTEXT_ENABLED:true}
  health:
    # 启用数据源健康检查
    datasource:
      enabled: ${MANAGEMENT_HEALTH_DATASOURCE_ENABLED:true}
  security:
    enabled: ${MANAGEMENT_SECURITY_ENABLED:true}
  metrics:
    export:
      prometheus:
        enabled: ${MANAGEMENT_METRICS_PROMETHEUS_ENABLED:true}
    tags:
      application: xhcai-plus
      environment: production

# Swagger配置（生产环境禁用）
springdoc:
  api-docs:
    enabled: ${SPRINGDOC_API_DOCS_ENABLED:false}
  swagger-ui:
    enabled: ${SPRINGDOC_SWAGGER_UI_ENABLED:false}

# 跨域配置（生产环境严格限制）
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:"*"}
  allowed-methods: ${CORS_ALLOWED_METHODS:"*"}
  allowed-headers: ${CORS_ALLOWED_HEADERS:"*"}
  allow-credentials: ${CORS_ALLOW_CREDENTIALS:false}
  max-age: ${CORS_MAX_AGE:3600}

# MyBatis Plus 配置
mybatis-plus:
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.xhcai.modules.*.entity
  # mapper xml 文件扫描
  mapper-locations: classpath*:mapper/**/*.xml
  # 类型处理器扫描
  type-handlers-package: com.xhcai.common.datasource.handler
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: false
    # 调用 setters 时，是否触发延迟加载对象的加载
    call-setters-on-nulls: true
    # JDBC类型为null时的处理
    jdbc-type-for-null: 'null'
    # 解决Spring Boot 3.x兼容性问题
    lazy-loading-enabled: false
    aggressive-lazy-loading: false

xhcai:
  plugin:
    # 插件根目录
    root-path: ./plugins
    # 是否开发模式
    dev-mode: true
    # 是否启用热插拔
    hot-swap-enabled: true
    # 插件扫描间隔（秒）
    scan-interval: 10
    # 插件启动超时时间（秒）
    start-timeout: 30
    # 插件停止超时时间（秒）
    stop-timeout: 10

    # 各插件类型的特定配置
    types:
      storage:
        enabled: true
        directory: storage
        max-plugins: 5
        config:
          # MinIO 配置示例
          minio:
            endpoint: http://localhost:9000
            access-key: minioadmin
            secret-key: minioadmin

      model:
        enabled: true
        directory: model
        max-plugins: 10
        config:
          # OpenAI 配置示例
          openai:
            api-key: ${OPENAI_API_KEY:your-api-key-here}
            base-url: https://api.openai.com/v1

      notification:
        enabled: true
        directory: notification
        max-plugins: 5
        config:
          # 邮件配置示例
          email:
            smtp-host: smtp.gmail.com
            smtp-port: 587
            username: ${EMAIL_USERNAME:}
            password: ${EMAIL_PASSWORD:}

      datasource:
        enabled: true
        directory: datasource
        max-plugins: 5
        config:
          # 数据源配置示例
          mysql:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: ********************************
            username: ${DB_USERNAME:root}
            password: ${DB_PASSWORD:password}

