package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 字典数据查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "字典数据查询条件")
public class SysDictDataQueryDTO extends PageQueryDTO {

    /**
     * 字典类型
     */
    @Schema(description = "字典类型")
    private String dictType;

    /**
     * 字典标签
     */
    @Schema(description = "字典标签")
    private String dictLabel;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    // Getters and Setters
    public String getDictType() {
        return dictType;
    }

    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    public String getDictLabel() {
        return dictLabel;
    }

    public void setDictLabel(String dictLabel) {
        this.dictLabel = dictLabel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SysDictDataQueryDTO{"
                + "dictType='" + dictType + '\''
                + ", dictLabel='" + dictLabel + '\''
                + ", status='" + status + '\''
                + ", current=" + getCurrent()
                + ", size=" + getSize()
                + ", orderBy='" + getOrderBy() + '\''
                + ", orderDirection='" + getOrderDirection() + '\''
                + '}';
    }
}
