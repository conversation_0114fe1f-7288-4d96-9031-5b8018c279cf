package com.xhcai.modules.rag.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 向量数据库查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "向量数据库查询DTO")
public class VectorDatabaseQueryDTO extends PageTimeRangeQueryDTO {

    @Schema(description = "数据库名称", example = "Elasticsearch集群")
    private String name;

    @Schema(description = "数据库类型", example = "elasticsearch")
    private String type;

    @Schema(description = "主机地址", example = "localhost")
    private String host;

    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    private String status;

    @Schema(description = "是否为默认数据库", example = "N", allowableValues = {"Y", "N"})
    private String isDefault;

    @Schema(description = "关键词搜索（名称、描述）", example = "Elasticsearch")
    private String keyword;
}
