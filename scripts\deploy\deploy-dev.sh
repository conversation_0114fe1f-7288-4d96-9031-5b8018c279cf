#!/bin/bash

# XHC AI Plus 开发环境部署脚本
# 用于快速部署开发环境

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
ENV_FILE="$PROJECT_ROOT/.env"
ENV_EXAMPLE_FILE="$PROJECT_ROOT/admin-api/src/main/resources/.env.example"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装，请先安装 Java 17 或更高版本"
        exit 1
    fi
    
    # 检查 Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装，请先安装 Maven"
        exit 1
    fi
    
    # 检查 PostgreSQL 客户端
    if ! command -v psql &> /dev/null; then
        log_warning "PostgreSQL 客户端未安装，无法进行数据库连接测试"
    fi
    
    # 检查 Redis 客户端
    if ! command -v redis-cli &> /dev/null; then
        log_warning "Redis 客户端未安装，无法进行 Redis 连接测试"
    fi
    
    log_success "依赖检查完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        if [ -f "$ENV_EXAMPLE_FILE" ]; then
            cp "$ENV_EXAMPLE_FILE" "$ENV_FILE"
            log_success "已从模板创建 .env 文件"
            log_warning "请编辑 $ENV_FILE 文件，配置正确的环境变量值"
        else
            log_error "找不到 .env.example 模板文件"
            exit 1
        fi
    else
        log_info ".env 文件已存在，跳过创建"
    fi
}

# 加载环境变量
load_env_variables() {
    log_info "加载环境变量..."
    
    if [ -f "$ENV_FILE" ]; then
        # 导出环境变量（忽略注释和空行）
        export $(grep -v '^#' "$ENV_FILE" | grep -v '^$' | xargs)
        log_success "环境变量加载完成"
    else
        log_error "找不到 .env 文件"
        exit 1
    fi
}

# 验证环境变量
validate_env_variables() {
    log_info "验证环境变量..."
    
    required_vars=(
        "DB_HOST"
        "DB_PORT"
        "DB_NAME"
        "DB_USERNAME"
        "DB_PASSWORD"
        "REDIS_HOST"
        "REDIS_PORT"
        "JWT_SECRET"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        log_error "缺少必需的环境变量: ${missing_vars[*]}"
        log_error "请检查 $ENV_FILE 文件"
        exit 1
    fi
    
    log_success "环境变量验证通过"
}

# 测试数据库连接
test_database_connection() {
    log_info "测试数据库连接..."
    
    if command -v psql &> /dev/null; then
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USERNAME" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
            log_success "数据库连接测试成功"
        else
            log_error "数据库连接测试失败"
            log_error "请检查数据库配置: $DB_HOST:$DB_PORT/$DB_NAME"
            exit 1
        fi
    else
        log_warning "跳过数据库连接测试（psql 未安装）"
    fi
}

# 测试 Redis 连接
test_redis_connection() {
    log_info "测试 Redis 连接..."
    
    if command -v redis-cli &> /dev/null; then
        if [ -n "$REDIS_PASSWORD" ]; then
            redis_cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD"
        else
            redis_cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
        fi
        
        if $redis_cmd ping &> /dev/null; then
            log_success "Redis 连接测试成功"
        else
            log_error "Redis 连接测试失败"
            log_error "请检查 Redis 配置: $REDIS_HOST:$REDIS_PORT"
            exit 1
        fi
    else
        log_warning "跳过 Redis 连接测试（redis-cli 未安装）"
    fi
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    cd "$PROJECT_ROOT"
    
    # 清理并编译
    mvn clean compile -DskipTests
    
    if [ $? -eq 0 ]; then
        log_success "项目构建成功"
    else
        log_error "项目构建失败"
        exit 1
    fi
}

# 启动应用
start_application() {
    log_info "启动应用..."
    
    cd "$PROJECT_ROOT"
    
    # 设置 Spring Profile
    export SPRING_PROFILES_ACTIVE=dev
    
    # 启动应用
    mvn spring-boot:run -pl admin-api -Dspring-boot.run.profiles=dev
}

# 主函数
main() {
    log_info "开始部署 XHC AI Plus 开发环境..."
    
    check_dependencies
    create_env_file
    load_env_variables
    validate_env_variables
    test_database_connection
    test_redis_connection
    build_project
    start_application
    
    log_success "开发环境部署完成！"
    log_info "应用访问地址: http://localhost:${SERVER_PORT:-8000}"
    log_info "Swagger 文档: http://localhost:${SERVER_PORT:-8000}/swagger-ui.html"
    log_info "Actuator 监控: http://localhost:${SERVER_PORT:-8000}/actuator"
}

# 处理中断信号
trap 'log_warning "部署被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
