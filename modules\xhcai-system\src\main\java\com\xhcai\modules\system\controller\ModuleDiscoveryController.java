package com.xhcai.modules.system.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.system.dto.ModuleInitDTO;
import com.xhcai.modules.system.service.IModuleDiscoveryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模块发现控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "模块发现", description = "模块发现和管理相关接口")
@RestController
@RequestMapping("/api/system/modules")
public class ModuleDiscoveryController {

    private static final Logger log = LoggerFactory.getLogger(ModuleDiscoveryController.class);

    @Autowired
    private IModuleDiscoveryService moduleDiscoveryService;

    @Operation(summary = "获取所有模块列表", description = "获取系统中所有可用的模块列表")
    @GetMapping
    @RequiresPermissions("system:module:list")
    public Result<List<ModuleInitDTO>> getAllModules() {
        log.info("获取所有模块列表");
        List<ModuleInitDTO> modules = moduleDiscoveryService.getAllModules();
        return Result.success(modules);
    }

    @Operation(summary = "获取模块信息", description = "获取指定模块的详细信息")
    @GetMapping("/{moduleId}")
    @RequiresPermissions("system:module:view")
    public Result<ModuleInitDTO> getModuleInfo(
            @Parameter(description = "模块ID") @PathVariable String moduleId) {
        log.info("获取模块信息: moduleId={}", moduleId);
        ModuleInitDTO module = moduleDiscoveryService.getModuleInfo(moduleId);
        if (module == null) {
            return Result.fail("模块不存在: " + moduleId);
        }
        return Result.success(module);
    }

    @Operation(summary = "刷新模块列表", description = "重新扫描和刷新模块列表")
    @PostMapping("/refresh")
    @RequiresPermissions("system:module:refresh")
    public Result<Void> refreshModules() {
        log.info("刷新模块列表");
        moduleDiscoveryService.refreshModules();
        return Result.success();
    }
}
