package com.xhcai.modules.system.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.SysPermissionQueryDTO;
import com.xhcai.modules.system.entity.SysPermission;
import com.xhcai.modules.system.service.ISysMenuService;
import com.xhcai.modules.system.vo.SysPermissionVO;
import com.xhcai.modules.system.vo.SysUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "菜单管理", description = "菜单管理相关接口")
@RestController
@RequestMapping("/api/system/menu")
@RequiresTenantAdmin(message = "菜单管理需要租户管理员权限")
public class SysMenuController {

    @Autowired
    private ISysMenuService menuService;

    /**
     * 查询菜单树
     */
    @Operation(summary = "查询菜单树", description = "查询菜单树形结构")
    @GetMapping("/tree")
    @RequiresPermissions("system:menu:view")
    public Result<List<SysPermissionVO>> tree(@Valid SysPermissionQueryDTO queryDTO) {
        List<SysPermissionVO> menuTree = menuService.selectMenuTree(queryDTO);
        return Result.success(menuTree);
    }

    /**
     * 根据用户ID查询菜单树
     */
    @Operation(summary = "查询用户菜单树", description = "根据用户ID查询菜单树")
    @GetMapping("/user/{userId}")
    @RequiresPermissions("system:menu:view")
    public Result<List<SysPermissionVO>> getUserMenuTree(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId) {
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setId(userId);
        sysUserVO.setTenantId(SecurityUtils.getCurrentTenantId());
        List<SysPermissionVO> menuTree = menuService.selectMenuTreeByUserId(sysUserVO);
        return Result.success(menuTree);
    }

    /**
     * 查询菜单选择树
     */
    @Operation(summary = "查询菜单选择树", description = "查询菜单选择树，排除指定菜单及其子菜单")
    @GetMapping("/select-tree")
    @RequiresPermissions("system:menu:view")
    public Result<List<SysPermissionVO>> selectTree(
            @Parameter(description = "排除的菜单ID") @RequestParam(required = false) String excludeMenuId) {
        List<SysPermissionVO> menuTree = menuService.buildMenuSelectTree(excludeMenuId);
        return Result.success(menuTree);
    }

    /**
     * 创建菜单
     */
    @Operation(summary = "创建菜单", description = "创建新的菜单")
    @PostMapping
    @RequiresPermissions("system:menu:add")
    public Result<Void> create(@Valid @RequestBody SysPermission menu) {
        boolean result = menuService.insertMenu(menu);
        return result ? Result.success() : Result.fail("创建菜单失败");
    }

    /**
     * 更新菜单信息
     */
    @Operation(summary = "更新菜单", description = "更新菜单信息")
    @PutMapping
    @RequiresPermissions("system:menu:edit")
    public Result<Void> update(@Valid @RequestBody SysPermission menu) {
        boolean result = menuService.updateMenu(menu);
        return result ? Result.success() : Result.fail("更新菜单失败");
    }

    /**
     * 删除菜单
     */
    @Operation(summary = "删除菜单", description = "批量删除菜单")
    @DeleteMapping
    @RequiresPermissions("system:menu:remove")
    public Result<Void> delete(@RequestBody List<String> menuIds) {
        boolean result = menuService.deleteMenus(menuIds);
        return result ? Result.success() : Result.fail("删除菜单失败");
    }

    /**
     * 根据角色ID查询菜单权限
     */
    @Operation(summary = "查询角色菜单", description = "根据角色ID查询菜单权限")
    @GetMapping("/role/{roleId}")
    @RequiresPermissions("system:menu:view")
    public Result<List<SysPermissionVO>> getMenusByRoleId(
            @Parameter(description = "角色ID", required = true) @PathVariable String roleId) {
        List<SysPermissionVO> menus = menuService.selectMenuPermissionsByRoleId(roleId);
        return Result.success(menus);
    }

    /**
     * 获取所有菜单
     */
    @Operation(summary = "获取所有菜单", description = "获取所有菜单列表，用于角色分配")
    @GetMapping("/all")
    @RequiresPermissions("system:menu:view")
    public Result<List<SysPermissionVO>> getAllMenus() {
        List<SysPermissionVO> menus = menuService.selectAllMenus();
        return Result.success(menus);
    }

    /**
     * 获取当前用户菜单
     */
    @Operation(summary = "获取当前用户菜单", description = "获取当前登录用户的菜单树")
    @GetMapping("/current")
    public Result<List<SysPermissionVO>> getCurrentUserMenus() {
        // 这里需要从SecurityContext中获取当前用户ID
        // 暂时返回空列表，后续在认证控制器中实现
        return Result.success(List.of());
    }

    /**
     * 获取菜单面包屑导航
     */
    @Operation(summary = "获取菜单面包屑", description = "根据菜单ID获取面包屑导航")
    @GetMapping("/{id}/breadcrumb")
    @RequiresPermissions("system:menu:view")
    public Result<List<SysPermissionVO>> getBreadcrumb(
            @Parameter(description = "菜单ID", required = true) @PathVariable String id) {
        // 这里需要实现面包屑逻辑，暂时返回空列表
        return Result.success(List.of());
    }

    /**
     * 检查菜单路径是否存在
     */
    @Operation(summary = "检查菜单路径", description = "检查菜单路径是否已存在")
    @GetMapping("/check-path")
    @RequiresPermissions("system:menu:view")
    public Result<Boolean> checkMenuPath(
            @Parameter(description = "菜单路径", required = true) @RequestParam String menuPath,
            @Parameter(description = "排除的菜单ID") @RequestParam(required = false) String excludeId) {
        // 这里需要调用权限服务的相关方法来检查路径
        // 暂时返回false
        return Result.success(false);
    }

    /**
     * 获取菜单图标列表
     */
    @Operation(summary = "获取菜单图标", description = "获取系统支持的菜单图标列表")
    @GetMapping("/icons")
    @RequiresPermissions("system:menu:view")
    public Result<List<String>> getMenuIcons() {
        // 返回常用的菜单图标列表
        List<String> icons = List.of(
            "dashboard", "user", "users", "setting", "settings", "menu",
            "home", "file", "folder", "edit", "delete", "search",
            "plus", "minus", "check", "close", "arrow-left", "arrow-right",
            "arrow-up", "arrow-down", "star", "heart", "bell", "message"
        );
        return Result.success(icons);
    }
}
