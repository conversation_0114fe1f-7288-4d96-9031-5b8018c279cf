package com.xhcai.modules.system.entity;

import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 部门信息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_dept")
@Schema(description = "部门信息")
@TableName("sys_dept")
public class SysDept extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父部门ID
     */
    @Column(name = "parent_id", length = 36)
    @Schema(description = "父部门ID", example = "0")
    @TableField("parent_id")
    private String parentId;

    /**
     * 祖级列表
     */
    @Column(name = "ancestors", length = 500)
    @Schema(description = "祖级列表", example = "0,1,2")
    @Size(max = 500, message = "祖级列表长度不能超过500个字符")
    @TableField("ancestors")
    private String ancestors;

    /**
     * 部门名称
     */
    @Column(name = "dept_name", length = 30)
    @Schema(description = "部门名称", example = "技术部")
    @NotBlank(message = "部门名称不能为空")
    @Size(min = 1, max = 30, message = "部门名称长度必须在1-30个字符之间")
    @TableField("dept_name")
    private String deptName;

    /**
     * 部门编码
     */
    @Column(name = "dept_code", length = 30)
    @Schema(description = "部门编码", example = "TECH")
    @Size(max = 30, message = "部门编码长度不能超过30个字符")
    @Pattern(regexp = "^[A-Za-z0-9_]*$", message = "部门编码只能包含字母、数字和下划线")
    @TableField("dept_code")
    private String deptCode;

    /**
     * 显示顺序
     */
    @Column(name = "order_num")
    @Schema(description = "显示顺序", example = "1")
    @TableField("order_num")
    private Integer orderNum;

    /**
     * 负责人ID
     */
    @Column(name = "leader_id", length = 36)
    @Schema(description = "负责人ID")
    @TableField("leader_id")
    private String leaderId;

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    /**
     * 子部门列表（非数据库字段）
     */
    @Schema(description = "子部门列表")
    @TableField(exist = false)
    private List<SysDept> children = new ArrayList<>();

    /**
     * 父部门名称（非数据库字段）
     */
    @Schema(description = "父部门名称")
    @TableField(exist = false)
    private String parentName;

    /**
     * 负责人姓名（非数据库字段）
     */
    @Schema(description = "负责人姓名")
    @TableField(exist = false)
    private String leaderName;

    // Getters and Setters
    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(String leaderId) {
        this.leaderId = leaderId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<SysDept> getChildren() {
        return children;
    }

    public void setChildren(List<SysDept> children) {
        this.children = children;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    @Override
    public String toString() {
        return "SysDept{"
                + "parentId=" + parentId
                + ", ancestors='" + ancestors + '\''
                + ", deptName='" + deptName + '\''
                + ", deptCode='" + deptCode + '\''
                + ", orderNum=" + orderNum
                + ", leaderId='" + leaderId + '\''
                + ", status='" + status + '\''
                + ", children=" + children
                + ", parentName='" + parentName + '\''
                + '}';
    }
}
