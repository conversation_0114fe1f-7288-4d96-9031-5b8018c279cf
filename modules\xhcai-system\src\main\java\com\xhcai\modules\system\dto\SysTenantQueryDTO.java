package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 租户查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "租户查询DTO")
public class SysTenantQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 租户编码
     */
    @Schema(description = "租户编码", example = "TENANT001")
    @Size(max = 50, message = "租户编码长度不能超过50个字符")
    private String tenantCode;

    /**
     * 租户名称
     */
    @Schema(description = "租户名称", example = "示例企业")
    @Size(max = 100, message = "租户名称长度不能超过100个字符")
    private String tenantName;

    /**
     * 联系人
     */
    @Schema(description = "联系人", example = "张三")
    @Size(max = 50, message = "联系人长度不能超过50个字符")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "13800138000")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    private String contactEmail;

    /**
     * 租户状态
     */
    @Schema(description = "租户状态", example = "0", allowableValues = {"0", "1", "2"})
    private String status;

    /**
     * 是否包含过期租户
     */
    @Schema(description = "是否包含过期租户", example = "false")
    private Boolean includeExpired;

    /**
     * 数据权限SQL
     */
    @Schema(hidden = true)
    private String dataScope;

    // Getters and Setters
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIncludeExpired() {
        return includeExpired;
    }

    public void setIncludeExpired(Boolean includeExpired) {
        this.includeExpired = includeExpired;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    @Override
    public String toString() {
        return "SysTenantQueryDTO{"
                + "tenantCode='" + tenantCode + '\''
                + ", tenantName='" + tenantName + '\''
                + ", contactPerson='" + contactPerson + '\''
                + ", contactPhone='" + contactPhone + '\''
                + ", contactEmail='" + contactEmail + '\''
                + ", status='" + status + '\''
                + ", includeExpired=" + includeExpired
                + "} " + super.toString();
    }
}
