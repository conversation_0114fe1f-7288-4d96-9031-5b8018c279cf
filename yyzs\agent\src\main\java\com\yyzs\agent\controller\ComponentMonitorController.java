package com.yyzs.agent.controller;

import com.yyzs.agent.entity.ComponentMonitor;
import com.yyzs.agent.service.ComponentMonitorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组件监控控制器
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor")
@RequiredArgsConstructor
@Tag(name = "组件监控", description = "组件运行状态监控和性能数据管理")
public class ComponentMonitorController {

    private final ComponentMonitorService componentMonitorService;

    @Operation(summary = "获取组件最新监控数据", description = "获取指定组件的最新监控数据")
    @GetMapping("/components/{componentId}/latest")
    public ResponseEntity<ComponentMonitor> getLatestMonitorData(
            @Parameter(description = "组件ID") @PathVariable String componentId) {
        
        try {
            ComponentMonitor monitor = componentMonitorService.getLatestMonitorData(componentId);
            if (monitor != null) {
                return ResponseEntity.ok(monitor);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取组件最新监控数据失败: " + componentId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "获取组件历史监控数据", description = "获取指定组件在指定时间范围内的历史监控数据")
    @GetMapping("/components/{componentId}/history")
    public ResponseEntity<List<ComponentMonitor>> getHistoryMonitorData(
            @Parameter(description = "组件ID") @PathVariable String componentId,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            List<ComponentMonitor> monitors = componentMonitorService.getHistoryMonitorData(componentId, startTime, endTime);
            return ResponseEntity.ok(monitors);
        } catch (Exception e) {
            log.error("获取组件历史监控数据失败: " + componentId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "获取所有组件最新监控数据", description = "获取所有组件的最新监控数据")
    @GetMapping("/components/latest")
    public ResponseEntity<List<ComponentMonitor>> getAllLatestMonitorData() {
        try {
            List<ComponentMonitor> monitors = componentMonitorService.getAllLatestMonitorData();
            return ResponseEntity.ok(monitors);
        } catch (Exception e) {
            log.error("获取所有组件最新监控数据失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "获取不健康的组件", description = "获取当前不健康状态的组件列表")
    @GetMapping("/components/unhealthy")
    public ResponseEntity<List<ComponentMonitor>> getUnhealthyComponents() {
        try {
            List<ComponentMonitor> monitors = componentMonitorService.getUnhealthyComponents();
            return ResponseEntity.ok(monitors);
        } catch (Exception e) {
            log.error("获取不健康组件失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "检查组件健康状态", description = "检查指定组件的健康状态")
    @GetMapping("/components/{componentId}/health")
    public ResponseEntity<Map<String, Object>> checkComponentHealth(
            @Parameter(description = "组件ID") @PathVariable String componentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean healthy = componentMonitorService.checkComponentHealth(componentId);
            result.put("componentId", componentId);
            result.put("healthy", healthy);
            result.put("status", healthy ? "健康" : "不健康");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查组件健康状态失败: " + componentId, e);
            result.put("error", "检查健康状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "获取组件性能统计", description = "获取指定组件在指定时间范围内的性能统计数据")
    @GetMapping("/components/{componentId}/stats")
    public ResponseEntity<Map<String, Object>> getComponentPerformanceStats(
            @Parameter(description = "组件ID") @PathVariable String componentId,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            Map<String, Object> stats = componentMonitorService.getComponentPerformanceStats(componentId, startTime, endTime);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取组件性能统计失败: " + componentId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "获取系统资源使用情况", description = "获取当前系统的资源使用情况")
    @GetMapping("/system/resources")
    public ResponseEntity<Map<String, Object>> getSystemResourceUsage() {
        try {
            Map<String, Object> usage = componentMonitorService.getSystemResourceUsage();
            return ResponseEntity.ok(usage);
        } catch (Exception e) {
            log.error("获取系统资源使用情况失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "手动收集监控数据", description = "手动触发指定组件的监控数据收集")
    @PostMapping("/components/{componentId}/collect")
    public ResponseEntity<Map<String, Object>> collectMonitorData(
            @Parameter(description = "组件ID") @PathVariable String componentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            ComponentMonitor monitor = componentMonitorService.collectMonitorData(componentId);
            if (monitor != null) {
                result.put("success", true);
                result.put("message", "监控数据收集成功");
                result.put("data", monitor);
            } else {
                result.put("success", false);
                result.put("message", "监控数据收集失败");
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("手动收集监控数据失败: " + componentId, e);
            result.put("success", false);
            result.put("message", "收集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "批量收集所有组件监控数据", description = "手动触发所有组件的监控数据收集")
    @PostMapping("/components/collect-all")
    public ResponseEntity<Map<String, Object>> collectAllMonitorData() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ComponentMonitor> monitors = componentMonitorService.collectAllMonitorData();
            result.put("success", true);
            result.put("message", "批量收集监控数据成功");
            result.put("collectedCount", monitors.size());
            result.put("data", monitors);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量收集监控数据失败", e);
            result.put("success", false);
            result.put("message", "批量收集失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "启动监控", description = "启动自动监控任务")
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startMonitoring() {
        Map<String, Object> result = new HashMap<>();
        try {
            componentMonitorService.startMonitoring();
            result.put("success", true);
            result.put("message", "监控已启动");
            result.put("active", componentMonitorService.isMonitoringActive());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("启动监控失败", e);
            result.put("success", false);
            result.put("message", "启动监控失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "停止监控", description = "停止自动监控任务")
    @PostMapping("/stop")
    public ResponseEntity<Map<String, Object>> stopMonitoring() {
        Map<String, Object> result = new HashMap<>();
        try {
            componentMonitorService.stopMonitoring();
            result.put("success", true);
            result.put("message", "监控已停止");
            result.put("active", componentMonitorService.isMonitoringActive());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("停止监控失败", e);
            result.put("success", false);
            result.put("message", "停止监控失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "获取监控状态", description = "获取当前监控任务的状态")
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getMonitoringStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean active = componentMonitorService.isMonitoringActive();
            result.put("active", active);
            result.put("status", active ? "运行中" : "已停止");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取监控状态失败", e);
            result.put("error", "获取监控状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "设置监控间隔", description = "设置自动监控的时间间隔")
    @PutMapping("/interval")
    public ResponseEntity<Map<String, Object>> setMonitorInterval(
            @Parameter(description = "监控间隔（秒）") @RequestParam int intervalSeconds) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            if (intervalSeconds < 10) {
                result.put("success", false);
                result.put("message", "监控间隔不能小于10秒");
                return ResponseEntity.badRequest().body(result);
            }
            
            componentMonitorService.setMonitorInterval(intervalSeconds);
            result.put("success", true);
            result.put("message", "监控间隔设置成功");
            result.put("intervalSeconds", intervalSeconds);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("设置监控间隔失败", e);
            result.put("success", false);
            result.put("message", "设置监控间隔失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "清理历史监控数据", description = "清理指定时间之前的历史监控数据")
    @DeleteMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupHistoryData(
            @Parameter(description = "清理此时间之前的数据") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beforeTime) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            int deletedCount = componentMonitorService.cleanupHistoryData(beforeTime);
            result.put("success", true);
            result.put("message", "历史数据清理完成");
            result.put("deletedCount", deletedCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理历史监控数据失败", e);
            result.put("success", false);
            result.put("message", "清理失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "获取告警历史", description = "获取指定组件的告警历史记录")
    @GetMapping("/components/{componentId}/alerts")
    public ResponseEntity<List<Map<String, Object>>> getAlertHistory(
            @Parameter(description = "组件ID") @PathVariable String componentId) {
        
        try {
            List<Map<String, Object>> alerts = componentMonitorService.getAlertHistory(componentId);
            return ResponseEntity.ok(alerts);
        } catch (Exception e) {
            log.error("获取告警历史失败: " + componentId, e);
            return ResponseEntity.badRequest().build();
        }
    }
}
