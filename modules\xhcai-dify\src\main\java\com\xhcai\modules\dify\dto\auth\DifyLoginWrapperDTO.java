package com.xhcai.modules.dify.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Dify 登录响应包装 DTO
 * 用于处理 Dify 返回的嵌套结构：
 * {
 *   "result": "success",
 *   "data": {
 *     "access_token": "...",
 *     "refresh_token": "..."
 *   }
 * }
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Schema(description = "Dify 登录响应包装")
public class DifyLoginWrapperDTO {

    /**
     * 响应结果
     */
    @Schema(description = "响应结果", example = "success")
    private String result;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private DifyLoginDataDTO data;

    /**
     * 内部数据类
     */
    @Schema(description = "登录数据")
    public static class DifyLoginDataDTO {
        
        /**
         * 访问令牌
         */
        @JsonProperty("access_token")
        @Schema(description = "访问令牌")
        private String accessToken;

        /**
         * 刷新令牌
         */
        @JsonProperty("refresh_token")
        @Schema(description = "刷新令牌")
        private String refreshToken;

        // Getters and Setters
        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        @Override
        public String toString() {
            return "DifyLoginDataDTO{" +
                    "accessToken='" + (accessToken != null ? accessToken.substring(0, Math.min(20, accessToken.length())) + "..." : "null") + '\'' +
                    ", refreshToken='" + (refreshToken != null ? refreshToken.substring(0, Math.min(20, refreshToken.length())) + "..." : "null") + '\'' +
                    '}';
        }
    }

    // Getters and Setters
    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public DifyLoginDataDTO getData() {
        return data;
    }

    public void setData(DifyLoginDataDTO data) {
        this.data = data;
    }

    /**
     * 转换为标准的登录响应 DTO
     */
    public DifyLoginResponseDTO toLoginResponse() {
        DifyLoginResponseDTO response = new DifyLoginResponseDTO();
        if (data != null) {
            response.setAccessToken(data.getAccessToken());
            response.setRefreshToken(data.getRefreshToken());
        }
        return response;
    }

    /**
     * 检查登录是否成功
     */
    public boolean isSuccess() {
        return "success".equals(result) && data != null && 
               data.getAccessToken() != null && !data.getAccessToken().trim().isEmpty();
    }

    @Override
    public String toString() {
        return "DifyLoginWrapperDTO{" +
                "result='" + result + '\'' +
                ", data=" + data +
                '}';
    }
}
