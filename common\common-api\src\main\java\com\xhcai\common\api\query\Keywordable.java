package com.xhcai.common.api.query;

/**
 * 关键字搜索接口
 * 定义关键字搜索的基本方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface Keywordable {

    /**
     * 获取搜索关键字
     * 
     * @return 搜索关键字
     */
    String getKeyword();

    /**
     * 是否有关键字条件
     * 
     * @return true-有关键字条件，false-无关键字条件
     */
    default Boolean hasKeyword() {
        return getKeyword() != null && !getKeyword().trim().isEmpty();
    }

    /**
     * 获取处理后的关键字（去除前后空格）
     * 
     * @return 处理后的关键字
     */
    default String getTrimmedKeyword() {
        return hasKeyword() ? getKeyword().trim() : null;
    }

    /**
     * 获取用于SQL LIKE查询的关键字（添加%通配符）
     * 
     * @return 用于LIKE查询的关键字
     */
    default String getLikeKeyword() {
        return hasKeyword() ? "%" + getTrimmedKeyword() + "%" : null;
    }

    /**
     * 获取用于SQL前缀匹配的关键字（右侧添加%通配符）
     * 
     * @return 用于前缀匹配的关键字
     */
    default String getPrefixKeyword() {
        return hasKeyword() ? getTrimmedKeyword() + "%" : null;
    }

    /**
     * 获取用于SQL后缀匹配的关键字（左侧添加%通配符）
     * 
     * @return 用于后缀匹配的关键字
     */
    default String getSuffixKeyword() {
        return hasKeyword() ? "%" + getTrimmedKeyword() : null;
    }
}
