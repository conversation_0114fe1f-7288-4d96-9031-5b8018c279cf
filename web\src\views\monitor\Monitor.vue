<template>
  <div class="monitor-page min-h-screen flex">
    <!-- 左侧固定区域 -->
    <div class="monitor-sidebar w-80 bg-white shadow-lg border-r border-gray-200 flex flex-col">
      <!-- 页面头部 -->
      <div class="monitor-header p-6 border-b border-gray-200">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">系统监控</h1>
          <p class="text-sm text-gray-600 mt-2">实时监控智能体、知识库、插件和模型运行状态</p>
        </div>
        <div class="flex items-center gap-4 mt-4">
          <div class="flex items-center gap-2 text-sm text-gray-600">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>实时更新</span>
          </div>
          <button @click="refreshAllData" class="btn-primary px-3 py-2 text-xs">
            <i class="fas fa-sync-alt mr-1"></i>刷新数据
          </button>
        </div>
      </div>

      <!-- 监控导航 -->
      <div class="monitor-nav flex-1 p-4">
        <div class="nav-tabs space-y-2">
          <button
            v-for="tab in monitorTabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            class="nav-tab w-full px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center text-left"
            :class="{
              'bg-blue-50 text-blue-600 border border-blue-200': activeTab === tab.key,
              'text-gray-700 hover:text-blue-600 hover:bg-blue-50': activeTab !== tab.key
            }"
          >
            <i :class="tab.icon + ' mr-3 text-lg'"></i>
            <span>{{ tab.name }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="monitor-content flex-1 min-h-0 overflow-auto">
      <div class="p-6">
        <!-- 智能体监控 -->
        <AgentMonitor v-if="activeTab === 'agent'" :refresh-trigger="refreshTrigger" />

        <!-- 知识库监控 -->
        <KnowledgeMonitor v-if="activeTab === 'knowledge'" :refresh-trigger="refreshTrigger" />

        <!-- 插件监控 -->
        <PluginMonitor v-if="activeTab === 'plugin'" :refresh-trigger="refreshTrigger" />

        <!-- 模型监控 -->
        <ModelMonitor v-if="activeTab === 'model'" :refresh-trigger="refreshTrigger" />

        <!-- 服务器监控 -->
        <ServerMonitor v-if="activeTab === 'server'" :refresh-trigger="refreshTrigger" />

        <!-- 监控告警 -->
        <AlertMonitor v-if="activeTab === 'alert'" :refresh-trigger="refreshTrigger" />

        <!-- 日志审计 -->
        <AuditMonitor
          v-if="activeTab === 'audit'"
          :refresh-trigger="refreshTrigger"
          @open-session-panel="handleOpenSessionPanel"
        />

        <!-- 登录日志 -->
        <LoginLogMonitor v-if="activeTab === 'login'" :refresh-trigger="refreshTrigger" />
      </div>
    </div>

    <!-- 会话详情面板 -->
    <div
      v-if="showSessionPanel"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click.self="closeSessionPanel"
    >
      <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        <!-- 面板头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 class="text-xl font-semibold text-gray-900">{{ selectedAgentName }} - 会话详情</h2>
            <p class="text-sm text-gray-500 mt-1">查看智能体的所有会话记录</p>
          </div>
          <button
            @click="closeSessionPanel"
            class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 面板内容 -->
        <div class="flex-1 flex overflow-hidden">
          <!-- 会话列表 -->
          <div class="w-1/3 border-r border-gray-200 flex flex-col">
            <!-- 搜索栏 -->
            <div class="p-4 border-b border-gray-200">
              <input
                v-model="sessionSearchQuery"
                type="text"
                placeholder="搜索会话..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- 会话列表 -->
            <div class="flex-1 overflow-y-auto audit-scroll">
              <div class="p-4 space-y-3">
                <div
                  v-for="session in filteredSessions"
                  :key="session.id"
                  @click="viewSessionMessages(session)"
                  :class="[
                    'session-card p-4 rounded-lg border cursor-pointer transition-all duration-200',
                    selectedSession?.id === session.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  ]"
                >
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="text-sm font-medium text-gray-900 mb-1">
                        会话 #{{ session.sessionId }}
                      </div>
                      <div class="text-xs text-gray-500 mb-2">
                        {{ formatDateTime(session.startTime) }}
                      </div>
                      <div class="text-xs text-gray-600">
                        消息数: {{ session.messageCount }} | 时长: {{ session.duration }}
                      </div>
                    </div>
                    <div class="flex flex-col items-end gap-1">
                      <span :class="getSessionStatusClass(session.status)" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium">
                        {{ getSessionStatusText(session.status) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 消息详情 -->
          <div class="flex-1 flex flex-col">
            <div v-if="!selectedSession" class="flex-1 flex items-center justify-center text-gray-500">
              <div class="text-center">
                <i class="fas fa-comments text-4xl mb-4"></i>
                <p>请选择一个会话查看详细消息</p>
              </div>
            </div>

            <div v-else class="flex-1 flex flex-col">
              <!-- 消息头部 -->
              <div class="p-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-medium text-gray-900">会话 #{{ selectedSession.sessionId }}</h3>
                    <p class="text-sm text-gray-500">
                      {{ formatDateTime(selectedSession.startTime) }} - {{ formatDateTime(selectedSession.endTime) }}
                    </p>
                  </div>
                  <div class="text-sm text-gray-600">
                    共 {{ selectedSessionMessages.length }} 条消息
                  </div>
                </div>
              </div>

              <!-- 消息列表 -->
              <div class="flex-1 overflow-y-auto audit-scroll p-4">
                <div class="space-y-4">
                  <div
                    v-for="message in selectedSessionMessages"
                    :key="message.id"
                    :class="[
                      'flex',
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    ]"
                  >
                    <div
                      :class="[
                        'max-w-3xl rounded-lg p-4 message-bubble',
                        message.role === 'user'
                          ? 'user-message bg-blue-100 text-blue-900'
                          : 'assistant-message bg-gray-100 text-gray-900'
                      ]"
                    >
                      <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                          <div
                            :class="[
                              'w-8 h-8 rounded-full flex items-center justify-center text-white text-sm',
                              message.role === 'user' ? 'bg-blue-500' : 'bg-gray-500'
                            ]"
                          >
                            <i :class="message.role === 'user' ? 'fas fa-user' : 'fas fa-robot'"></i>
                          </div>
                        </div>
                        <div class="flex-1">
                          <div class="text-sm font-medium mb-1">
                            {{ message.role === 'user' ? '用户' : '智能体' }}
                          </div>
                          <div class="text-sm whitespace-pre-wrap">{{ message.content }}</div>
                          <div class="text-xs opacity-75 mt-2">
                            {{ formatDateTime(message.timestamp) }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import AgentMonitor from '@/views/monitor/AgentMonitor.vue'
import KnowledgeMonitor from '@/views/monitor/KnowledgeMonitor.vue'
import PluginMonitor from '@/views/monitor/PluginMonitor.vue'
import ModelMonitor from '@/views/monitor/ModelMonitor.vue'
import ServerMonitor from '@/views/monitor/ServerMonitor.vue'
import AlertMonitor from '@/views/monitor/AlertMonitor.vue'
import AuditMonitor from '@/views/monitor/AuditMonitor.vue'
import LoginLogMonitor from '@/views/monitor/LoginLogMonitor.vue'

// 响应式数据
const activeTab = ref('agent')
const refreshTrigger = ref(0)

// 监控标签页配置
const monitorTabs = [
  { key: 'agent', name: '智能体监控', icon: 'fas fa-robot' },
  { key: 'knowledge', name: '知识库监控', icon: 'fas fa-database' },
  { key: 'plugin', name: '插件监控', icon: 'fas fa-puzzle-piece' },
  { key: 'model', name: '模型监控', icon: 'fas fa-brain' },
  { key: 'server', name: '服务器监控', icon: 'fas fa-server' },
  { key: 'alert', name: '监控告警', icon: 'fas fa-exclamation-triangle' },
  { key: 'audit', name: '日志审计', icon: 'fas fa-file-alt' },
  { key: 'login', name: '登录日志', icon: 'fas fa-sign-in-alt' }
]

// 会话面板相关
const showSessionPanel = ref(false)
const selectedAgentId = ref<number | null>(null)
const selectedAgentName = ref('')
const sessionSearchQuery = ref('')
const selectedSession = ref<any>(null)
const selectedSessionMessages = ref<any[]>([])

// 模拟会话数据
const sessions = ref([
  {
    id: 1,
    sessionId: 'S001',
    agentId: 1,
    startTime: '2024-06-30 09:15:30',
    endTime: '2024-06-30 09:25:45',
    messageCount: 8,
    duration: '10分15秒',
    status: 'completed'
  },
  {
    id: 2,
    sessionId: 'S002',
    agentId: 1,
    startTime: '2024-06-30 10:30:20',
    endTime: '2024-06-30 10:45:10',
    messageCount: 12,
    duration: '14分50秒',
    status: 'completed'
  },
  {
    id: 3,
    sessionId: 'S003',
    agentId: 1,
    startTime: '2024-06-30 14:20:15',
    endTime: null,
    messageCount: 5,
    duration: '进行中',
    status: 'active'
  }
])

// 模拟消息数据
const messages = ref([
  {
    id: 1,
    sessionId: 1,
    role: 'user',
    content: '你好，我想了解一下产品的功能特性。',
    timestamp: '2024-06-30 09:15:30'
  },
  {
    id: 2,
    sessionId: 1,
    role: 'assistant',
    content: '您好！我很乐意为您介绍我们产品的功能特性。我们的产品主要包含以下几个核心功能：\n\n1. 智能对话：支持自然语言交互\n2. 知识管理：强大的知识库系统\n3. 插件扩展：丰富的插件生态\n4. 模型集成：支持多种AI模型\n\n您对哪个方面比较感兴趣呢？',
    timestamp: '2024-06-30 09:15:45'
  },
  {
    id: 3,
    sessionId: 1,
    role: 'user',
    content: '我想了解知识库系统的具体功能。',
    timestamp: '2024-06-30 09:16:20'
  },
  {
    id: 4,
    sessionId: 1,
    role: 'assistant',
    content: '知识库系统是我们产品的核心功能之一，主要包括：\n\n📚 **文档管理**\n- 支持多种格式文档上传（PDF、Word、TXT等）\n- 自动文档解析和结构化处理\n- 智能标签和分类管理\n\n🔍 **智能检索**\n- 语义搜索，理解用户意图\n- 相关性排序和推荐\n- 多维度筛选和过滤\n\n🧠 **知识图谱**\n- 自动构建实体关系\n- 知识关联和推理\n- 可视化知识网络\n\n您希望深入了解哪个具体功能呢？',
    timestamp: '2024-06-30 09:17:10'
  }
])

// 计算属性
const filteredSessions = computed(() => {
  let filtered = sessions.value.filter(session => session.agentId === selectedAgentId.value)

  if (sessionSearchQuery.value) {
    const query = sessionSearchQuery.value.toLowerCase()
    filtered = filtered.filter(session =>
      session.sessionId.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 方法
const refreshAllData = () => {
  refreshTrigger.value++
}

const handleOpenSessionPanel = (agentId: number, agentName: string) => {
  selectedAgentId.value = agentId
  selectedAgentName.value = agentName
  showSessionPanel.value = true
  sessionSearchQuery.value = ''
  selectedSession.value = null
  selectedSessionMessages.value = []
}

const closeSessionPanel = () => {
  showSessionPanel.value = false
  selectedAgentId.value = null
  selectedAgentName.value = ''
  sessionSearchQuery.value = ''
  selectedSession.value = null
  selectedSessionMessages.value = []
}

const viewSessionMessages = (session: any) => {
  selectedSession.value = session
  selectedSessionMessages.value = messages.value.filter(msg => msg.sessionId === session.id)
}

const getSessionStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'active': 'bg-green-100 text-green-800',
    'completed': 'bg-blue-100 text-blue-800',
    'error': 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getSessionStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': '进行中',
    'completed': '已完成',
    'error': '异常'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  // 初始化时刷新一次数据
  refreshAllData()
})
</script>
<style scoped>
.monitor-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.monitor-sidebar {
  position: fixed;
  left: 0;
  top: 70px; /* 顶部导航栏高度 */
  height: calc(100vh - 70px); /* 减去顶部导航栏高度 */
  z-index: 40; /* 低于顶部导航栏的z-index: 50 */
}

.monitor-content {
  margin-left: 320px; /* 左侧边栏宽度 + 间距 */
  height: calc(100vh - 70px); /* 减去顶部导航栏高度 */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
}

.monitor-header {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.nav-tab {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-tab:hover::before {
  left: 100%;
}

.nav-tab:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.nav-tab.bg-blue-50 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
}

/* 会话面板样式 */
.session-card {
  transition: all 0.2s ease;
}

.session-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* 消息气泡样式优化 */
.user-message .message-bubble {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.assistant-message .message-bubble {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

/* 滚动条样式 */
.audit-scroll::-webkit-scrollbar {
  width: 6px;
}

.audit-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.audit-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.audit-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.monitor-content > div > div {
  animation: slideInRight 0.4s ease-out;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .monitor-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    top: 0;
  }

  .monitor-content {
    margin-left: 0;
    margin-top: 0;
    height: auto;
    border-radius: 0;
  }

  .monitor-page {
    flex-direction: column;
  }

  .nav-tabs {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .nav-tabs {
    grid-template-columns: 1fr;
  }

  .monitor-header h1 {
    font-size: 1.5rem;
  }

  .nav-tab {
    padding: 0.75rem 1rem;
  }

  .monitor-header .flex {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
}
</style>