<template>
  <div class="datasource-index">
    <!-- 头部导航 -->
    <div class="header">
      <div class="header-left">
        <button
          @click="goBack"
          class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <div class="page-info">
          <div class="page-icon">
            <i class="fas fa-database"></i>
          </div>
          <div class="page-details">
            <h1 class="page-title">数据源工作流
              <span v-if="datasetName" class="text-blue-400 text-lg mt-15" style="padding-top: 15px">({{datasetName}})</span>
            </h1>
            <p class="page-description">管理和配置知识库的数据源处理流程</p>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-primary" @click="createNewWorkflow">
          <i class="fas fa-plus"></i>
          创建新工作流
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 默认数据源和自定义工作流统一布局 -->
      <div class="content-grid">
        <!-- 默认数据源：本地文件上传卡片 -->
        <div class="datasource-card default-card" @click="openFileUploadPage">
          <div class="card-header">
            <div class="datasource-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
              <i class="fas fa-upload"></i>
            </div>
            <div class="card-badge">
              <span class="badge-text">默认</span>
            </div>
          </div>

          <div class="card-content">
            <h3 class="datasource-name">本地文件上传</h3>
            <p class="datasource-description">支持上传本地文件并进行向量化处理，支持多种文件格式</p>

            <div class="datasource-features">
              <div class="feature-item">
                <i class="fas fa-file-alt"></i>
                <span>支持多种格式</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-bolt"></i>
                <span>快速处理</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <span>安全可靠</span>
              </div>
            </div>

            <div class="datasource-status">
              <span class="status-badge active">可用</span>
            </div>
          </div>
        </div>

        <!-- 自定义工作流卡片 -->
        <div
          v-for="workflow in customWorkflows"
          :key="workflow.id"
          class="workflow-card"
          @click="openWorkflow(workflow)"
        >
          <div class="card-header">
            <div class="workflow-icon" :style="{ background: workflow.iconBg }">
              <i :class="workflow.icon"></i>
            </div>
            <div class="card-actions">
              <button class="action-btn" @click.stop="editWorkflow(workflow)" title="编辑">
                <i class="fas fa-edit"></i>
              </button>
              <button
                class="action-btn"
                @click.stop="deleteWorkflow(workflow)"
                title="删除"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <div class="card-content">
            <h3 class="workflow-name">{{ workflow.name }}</h3>
            <p class="workflow-description">{{ workflow.description }}</p>

            <div class="workflow-stats">
              <div class="stat-item">
                <i class="fas fa-plug"></i>
                <span>{{ workflow.nodeCount }} 个节点</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-clock"></i>
                <span>{{ formatDate(workflow.lastModified) }}</span>
              </div>
            </div>

            <div class="workflow-status">
              <span class="status-badge" :class="workflow.status">
                {{ getStatusText(workflow.status) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 创建新工作流卡片 -->
        <div class="workflow-card create-card" @click="createNewWorkflow">
          <div class="create-content">
            <div class="create-icon">
              <i class="fas fa-plus"></i>
            </div>
            <h3 class="create-title">创建新工作流</h3>
            <p class="create-description">设计自定义的数据处理流程</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 通用运行窗口 -->
    <RunnerWindow
      v-model:visible="runnerWindow.windowState.visible"
      v-model:isMinimized="runnerWindow.windowState.isMinimized"
      v-model:isMaximized="runnerWindow.windowState.isMaximized"
      v-model:position="runnerWindow.windowState.position"
      v-model:size="runnerWindow.windowState.size"
      :title="runnerWindow.windowConfig.title"
      :component="runnerWindow.windowConfig.component"
      :componentProps="runnerWindow.windowConfig.componentProps"
      :headerInfo="runnerWindow.windowConfig.headerInfo"
      :resizable="runnerWindow.windowConfig.resizable"
      :draggable="runnerWindow.windowConfig.draggable"
      :showControls="runnerWindow.windowConfig.showControls"
      :enableTaskbar="runnerWindow.windowConfig.enableTaskbar"
      @minimize="runnerWindow.minimizeWindow"
      @restore="runnerWindow.restoreWindow"
      @close="runnerWindow.closeWindow"
      @update:position="runnerWindow.updatePosition"
      @update:size="runnerWindow.updateSize"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, shallowRef, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { RunnerWindow, useRunnerWindow } from '@/components/common/runner-window'
import DataHandleWorkFlow from './DataHandleWorkFlow.vue'
import {v4 as uuidv4 } from 'uuid'

const datasetName = ref('')

// 路由和导航
const route = useRoute()
const router = useRouter()

// 通用运行窗口
const runnerWindow = useRunnerWindow()

// 响应式数据
const customWorkflows = ref([
  {
    id: 'ftp-datasource',
    name: 'FTP数据源',
    description: '从FTP服务器获取文件并处理',
    icon: 'fas fa-server',
    iconBg: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    nodeCount: 4,
    lastModified: new Date('2024-01-10'),
    status: 'active'
  }
])

// 计算属性
const datasetId = computed(() => route.params.id as string)

// 方法
const goBack = () => {
  router.push({path: `/knowledge/${datasetId.value}`, query: {name: datasetName.value}})
}

const createNewWorkflow = () => {
  openWorkflowDesigner()
}

const openWorkflow = (workflow: any) => {
  // 打开工作流运行状态页面
  openWorkflowStatus(workflow)
}

const openWorkflowDesigner = (workflow?: any) => {
  runnerWindow.openWindow({
    title: workflow ? `编辑工作流 - ${workflow.name}` : '创建新工作流',
    component: shallowRef(DataHandleWorkFlow),
    componentProps: {
      datasetId: datasetId.value,
      workflowId: workflow?.id,
      onClose: () => runnerWindow.closeWindow(),
      onSave: handleWorkflowSave
    },
    headerInfo: {
      icon: workflow?.icon || 'fas fa-stream',
      iconBg: workflow?.iconBg || '#3b82f6',
      title: workflow ? `编辑工作流 - ${workflow.name}` : '创建新工作流',
      subtitle: '设计数据处理流程',
      meta: [
        { label: '类型', value: '数据源工作流', type: 'badge', color: 'blue' },
        { label: '知识库', value: datasetId.value, type: 'text' }
      ]
    },
    defaultFullscreen: true,
    enableTaskbar: false,
    resizable: true,
    draggable: true,
    showControls: true
  })
}

const editWorkflow = (workflow: any) => {
  openWorkflowDesigner(workflow)
}

const deleteWorkflow = (workflow: any) => {
  if (confirm(`确定要删除工作流"${workflow.name}"吗？`)) {
    const index = customWorkflows.value.findIndex(w => w.id === workflow.id)
    if (index > -1) {
      customWorkflows.value.splice(index, 1)
    }
  }
}

const openFileUploadPage = () => {
  // 使用路由跳转到文档处理流程页面
  router.push({
    name: 'KnowledgeCreateFlow',
    query: {
      datasetId: datasetId.value,
      name: datasetName.value,
      batchId: uuidv4()
    }
  })
}

const openWorkflowStatus = (workflow: any) => {
  runnerWindow.openWindow({
    title: `${workflow.name} - 运行状态`,
    component: shallowRef(defineAsyncComponent(() => import('./components/WorkflowStatusPage.vue'))),
    componentProps: {
      workflow: workflow,
      datasetId: datasetId.value,
      onClose: () => runnerWindow.closeWindow()
    },
    headerInfo: {
      icon: workflow.icon,
      iconBg: workflow.iconBg,
      title: `${workflow.name} - 运行状态`,
      subtitle: '查看工作流执行情况',
      meta: [
        { label: '类型', value: '运行监控', type: 'badge', color: 'green' },
        { label: '状态', value: getStatusText(workflow.status), type: 'badge', color: workflow.status === 'active' ? 'green' : 'gray' }
      ]
    },
    defaultFullscreen: true,
    enableTaskbar: true,
    resizable: true,
    draggable: true,
    showControls: true
  })
}

const handleWorkflowSave = (workflowData: any) => {
  console.log('保存工作流:', workflowData)
  // 这里可以调用API保存工作流
  runnerWindow.closeWindow()
}



const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '运行中'
    case 'inactive':
      return '已停止'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
}

// 生命周期
onMounted(() => {
  datasetName.value = route.query.name as string
  // 可以在这里加载工作流数据
})
</script>

<style scoped>
.datasource-index {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.page-description {
  font-size: 14px;
  color: #64748b;
  margin: 4px 0 0 0;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.main-content {
  flex: 1;
  padding: 40px;
  overflow-y: auto;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.datasource-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.datasource-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.default-card {
  border: 2px solid #3b82f6;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.default-card:hover {
  border-color: #2563eb;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.datasource-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-badge {
  position: absolute;
  top: 12px;
  right: 12px;
}

.badge-text {
  background: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.datasource-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.datasource-description {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.datasource-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
}

.feature-item i {
  font-size: 12px;
  color: #3b82f6;
}

.datasource-status {
  display: flex;
  justify-content: flex-end;
}



.workflow-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.workflow-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
}

.workflow-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.workflow-card:hover .card-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.card-content {
  padding: 20px;
}

.workflow-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.workflow-description {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.workflow-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
}

.stat-item i {
  font-size: 12px;
}

.workflow-status {
  display: flex;
  justify-content: flex-end;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #f1f5f9;
  color: #64748b;
}

.status-badge.error {
  background: #fef2f2;
  color: #dc2626;
}

.create-card {
  border: 2px dashed #cbd5e1;
  background: #f8fafc;
}

.create-card:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.create-content {
  padding: 40px 20px;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.create-icon {
  width: 64px;
  height: 64px;
  background: #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 24px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.create-card:hover .create-icon {
  background: #3b82f6;
  color: white;
}

.create-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.create-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}
</style>
