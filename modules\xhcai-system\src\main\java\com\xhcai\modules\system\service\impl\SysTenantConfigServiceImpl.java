package com.xhcai.modules.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.system.entity.SysTenantConfig;
import com.xhcai.modules.system.mapper.SysTenantConfigMapper;
import com.xhcai.modules.system.service.ISysTenantConfigService;
import com.xhcai.common.security.utils.SecurityUtils;

/**
 * 租户配置服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysTenantConfigServiceImpl extends ServiceImpl<SysTenantConfigMapper, SysTenantConfig> implements ISysTenantConfigService {

    private static final Logger log = LoggerFactory.getLogger(SysTenantConfigServiceImpl.class);

    @Autowired
    private SysTenantConfigMapper tenantConfigMapper;

    /**
     * 配置缓存 - 租户ID -> 配置Map
     */
    private final Map<String, Map<String, String>> configCache = new ConcurrentHashMap<>();

    @Override
    public String getConfigValue(String configKey, String tenantId) {
        if (!StringUtils.hasText(configKey) || tenantId == null) {
            return null;
        }

        // 先从缓存获取
        Map<String, String> tenantConfigs = configCache.get(tenantId);
        if (tenantConfigs != null && tenantConfigs.containsKey(configKey)) {
            return tenantConfigs.get(configKey);
        }

        // 从数据库获取
        SysTenantConfig config = tenantConfigMapper.selectByKeyAndTenant(configKey, tenantId);
        String configValue = config != null ? config.getConfigValue() : null;

        // 更新缓存
        if (tenantConfigs == null) {
            tenantConfigs = new ConcurrentHashMap<>();
            configCache.put(tenantId, tenantConfigs);
        }
        tenantConfigs.put(configKey, configValue);

        return configValue;
    }

    @Override
    public String getConfigValue(String configKey) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        if (tenantId == null) {
            // 未登录状态下返回系统默认值
            return getSystemDefaultValue(configKey);
        }
        return getConfigValue(configKey, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setConfigValue(String configKey, String configValue, String tenantId) {
        if (!StringUtils.hasText(configKey) || tenantId == null) {
            throw new BusinessException("配置键和租户ID不能为空");
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(tenantId)) {
            throw new BusinessException("权限不足，无法设置该租户的配置");
        }

        SysTenantConfig config = tenantConfigMapper.selectByKeyAndTenant(configKey, tenantId);
        boolean result;

        if (config != null) {
            // 更新现有配置
            config.setConfigValue(configValue);
            result = updateById(config);
        } else {
            // 创建新配置
            config = new SysTenantConfig();
            config.setConfigKey(configKey);
            config.setConfigValue(configValue);
            config.setTenantId(tenantId);
            config.setIsSystem(false);
            config.setIsEncrypted(false);
            result = save(config);
        }

        if (result) {
            // 更新缓存
            refreshConfigCache(tenantId);
        }

        return result;
    }

    @Override
    public boolean setConfigValue(String configKey, String configValue) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        if (tenantId == null) {
            throw new BusinessException("未登录或无法获取租户信息，无法设置配置");
        }
        return setConfigValue(configKey, configValue, tenantId);
    }

    @Override
    public Map<String, String> getTenantConfigs(String tenantId) {
        if (tenantId == null) {
            return new HashMap<>();
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(tenantId)) {
            throw new BusinessException("权限不足，无法获取该租户的配置");
        }

        // 先从缓存获取
        Map<String, String> tenantConfigs = configCache.get(tenantId);
        if (tenantConfigs != null) {
            return new HashMap<>(tenantConfigs);
        }

        // 从数据库获取
        List<SysTenantConfig> configs = tenantConfigMapper.selectByTenantId(tenantId);
        tenantConfigs = configs.stream()
                .collect(Collectors.toMap(
                        SysTenantConfig::getConfigKey,
                        config -> config.getConfigValue() != null ? config.getConfigValue() : "",
                        (existing, replacement) -> replacement
                ));

        // 更新缓存
        configCache.put(tenantId, new ConcurrentHashMap<>(tenantConfigs));

        return tenantConfigs;
    }

    @Override
    public Map<String, String> getCurrentTenantConfigs() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        if (tenantId == null) {
            // 未登录状态下返回空配置
            return new HashMap<>();
        }
        return getTenantConfigs(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSetConfigs(Map<String, String> configs, String tenantId) {
        if (CollectionUtils.isEmpty(configs) || tenantId == null) {
            return false;
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(tenantId)) {
            throw new BusinessException("权限不足，无法设置该租户的配置");
        }

        try {
            for (Map.Entry<String, String> entry : configs.entrySet()) {
                setConfigValue(entry.getKey(), entry.getValue(), tenantId);
            }
            return true;
        } catch (Exception e) {
            log.error("批量设置租户配置失败: {}", e.getMessage(), e);
            throw new BusinessException("批量设置配置失败: " + e.getMessage());
        }
    }

    @Override
    public boolean batchSetConfigs(Map<String, String> configs) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        if (tenantId == null) {
            throw new BusinessException("未登录或无法获取租户信息，无法批量设置配置");
        }
        return batchSetConfigs(configs, tenantId);
    }

    @Override
    public List<SysTenantConfig> getConfigsByType(String configType, String tenantId) {
        if (!StringUtils.hasText(configType) || tenantId == null) {
            return List.of();
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(tenantId)) {
            throw new BusinessException("权限不足，无法获取该租户的配置");
        }

        return tenantConfigMapper.selectByTypeAndTenant(configType, tenantId);
    }

    @Override
    public List<SysTenantConfig> getSystemConfigs(String tenantId) {
        if (tenantId == null) {
            return List.of();
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(tenantId)) {
            throw new BusinessException("权限不足，无法获取该租户的配置");
        }

        return tenantConfigMapper.selectSystemConfigs(tenantId);
    }

    @Override
    public List<SysTenantConfig> getUserConfigs(String tenantId) {
        if (tenantId == null) {
            return List.of();
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(tenantId)) {
            throw new BusinessException("权限不足，无法获取该租户的配置");
        }

        return tenantConfigMapper.selectUserConfigs(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createConfig(SysTenantConfig config) {
        if (config == null) {
            throw new BusinessException("配置信息不能为空");
        }

        // 参数校验
        validateConfig(config, true);

        // 检查权限
        if (!SecurityUtils.canAccessTenant(config.getTenantId())) {
            throw new BusinessException("权限不足，无法创建该租户的配置");
        }

        // 检查配置键是否已存在
        if (existsConfigKey(config.getConfigKey(), config.getTenantId(), null)) {
            throw new BusinessException("配置键已存在");
        }

        boolean result = save(config);
        if (result) {
            refreshConfigCache(config.getTenantId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfig(SysTenantConfig config) {
        if (config == null || config.getId() == null) {
            throw new BusinessException("配置信息不能为空");
        }

        // 参数校验
        validateConfig(config, false);

        // 检查配置是否存在
        SysTenantConfig existConfig = getById(config.getId());
        if (existConfig == null) {
            throw new BusinessException("配置不存在");
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(existConfig.getTenantId())) {
            throw new BusinessException("权限不足，无法更新该租户的配置");
        }

        // 检查配置键是否已存在（排除自己）
        if (existsConfigKey(config.getConfigKey(), existConfig.getTenantId(), config.getId())) {
            throw new BusinessException("配置键已存在");
        }

        boolean result = updateById(config);
        if (result) {
            refreshConfigCache(existConfig.getTenantId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfigs(List<String> configIds) {
        if (CollectionUtils.isEmpty(configIds)) {
            return false;
        }

        // 获取要删除的配置信息
        List<SysTenantConfig> configs = listByIds(configIds);
        if (CollectionUtils.isEmpty(configs)) {
            return false;
        }

        // 检查权限
        for (SysTenantConfig config : configs) {
            if (!SecurityUtils.canAccessTenant(config.getTenantId())) {
                throw new BusinessException("权限不足，无法删除该租户的配置");
            }
        }

        boolean result = removeByIds(configIds);
        if (result) {
            // 刷新相关租户的缓存
            configs.stream()
                    .map(SysTenantConfig::getTenantId)
                    .distinct()
                    .forEach(this::refreshConfigCache);
        }

        return result;
    }

    /**
     * 参数校验
     */
    private void validateConfig(SysTenantConfig config, boolean isCreate) {
        if (!StringUtils.hasText(config.getConfigKey())) {
            throw new BusinessException("配置键不能为空");
        }

        if (config.getTenantId() == null) {
            throw new BusinessException("租户ID不能为空");
        }

        if (!StringUtils.hasText(config.getConfigName())) {
            throw new BusinessException("配置名称不能为空");
        }

        // 配置键格式验证
        if (!config.getConfigKey().matches("^[a-zA-Z][a-zA-Z0-9._-]*$")) {
            throw new BusinessException("配置键格式不正确，只能包含字母、数字、点、下划线和连字符，且必须以字母开头");
        }

        // 配置键长度验证
        if (config.getConfigKey().length() > 100) {
            throw new BusinessException("配置键长度不能超过100个字符");
        }

        // 配置名称长度验证
        if (config.getConfigName().length() > 50) {
            throw new BusinessException("配置名称长度不能超过50个字符");
        }

        // 配置值长度验证
        if (config.getConfigValue() != null && config.getConfigValue().length() > 2000) {
            throw new BusinessException("配置值长度不能超过2000个字符");
        }

        // 配置描述长度验证
        if (config.getConfigDesc() != null && config.getConfigDesc().length() > 200) {
            throw new BusinessException("配置描述长度不能超过200个字符");
        }

        // 特定配置值的格式验证
        validateConfigValue(config.getConfigKey(), config.getConfigValue());
    }

    /**
     * 验证特定配置的值格式
     */
    private void validateConfigValue(String configKey, String configValue) {
        if (configValue == null || configValue.trim().isEmpty()) {
            return;
        }

        switch (configKey) {
            case "system.maxFileSize":
                try {
                    long size = Long.parseLong(configValue);
                    if (size <= 0 || size > 1073741824L) { // 最大1GB
                        throw new BusinessException("文件大小限制必须在1字节到1GB之间");
                    }
                } catch (NumberFormatException e) {
                    throw new BusinessException("文件大小限制必须是有效的数字");
                }
                break;
            case "system.pageSize":
            case "system.maxPageSize":
                try {
                    int size = Integer.parseInt(configValue);
                    if (size <= 0 || size > 1000) {
                        throw new BusinessException("分页大小必须在1到1000之间");
                    }
                } catch (NumberFormatException e) {
                    throw new BusinessException("分页大小必须是有效的数字");
                }
                break;
            case "security.sessionTimeout":
                try {
                    int timeout = Integer.parseInt(configValue);
                    if (timeout < 300 || timeout > 86400) { // 5分钟到24小时
                        throw new BusinessException("会话超时时间必须在300秒到86400秒之间");
                    }
                } catch (NumberFormatException e) {
                    throw new BusinessException("会话超时时间必须是有效的数字");
                }
                break;
            case "ai.temperature":
                try {
                    double temp = Double.parseDouble(configValue);
                    if (temp < 0.0 || temp > 2.0) {
                        throw new BusinessException("AI温度参数必须在0.0到2.0之间");
                    }
                } catch (NumberFormatException e) {
                    throw new BusinessException("AI温度参数必须是有效的数字");
                }
                break;
        }
    }

    @Override
    public boolean existsConfigKey(String configKey, String tenantId, String excludeId) {
        if (!StringUtils.hasText(configKey) || tenantId == null) {
            return false;
        }

        return tenantConfigMapper.existsConfigKey(configKey, tenantId, excludeId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyConfigsToTenant(String sourceTenantId, String targetTenantId) {
        if (sourceTenantId == null || targetTenantId == null) {
            throw new BusinessException("源租户ID和目标租户ID不能为空");
        }

        // 只有平台管理员可以复制配置
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法复制租户配置");
        }

        try {
            String createBy = SecurityUtils.getCurrentUserId();
            int copiedCount = tenantConfigMapper.copyConfigsToTenant(sourceTenantId, targetTenantId, createBy);

            if (copiedCount > 0) {
                refreshConfigCache(targetTenantId);
                log.info("成功复制 {} 个配置从租户 {} 到租户 {}", copiedCount, sourceTenantId, targetTenantId);
            }

            return copiedCount > 0;
        } catch (Exception e) {
            log.error("复制租户配置失败: {}", e.getMessage(), e);
            throw new BusinessException("复制租户配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetTenantConfigs(String tenantId) {
        if (tenantId == null) {
            throw new BusinessException("租户ID不能为空");
        }

        // 检查权限
        if (!SecurityUtils.canAccessTenant(tenantId)) {
            throw new BusinessException("权限不足，无法重置该租户的配置");
        }

        try {
            // 删除现有配置
            List<SysTenantConfig> existingConfigs = tenantConfigMapper.selectByTenantId(tenantId);
            if (!CollectionUtils.isEmpty(existingConfigs)) {
                List<String> configIds = existingConfigs.stream()
                        .map(SysTenantConfig::getId)
                        .collect(Collectors.toList());
                removeByIds(configIds);
            }

            // 为租户创建默认配置（不再从其他租户复制）
            createDefaultTenantConfigs(tenantId);
            boolean result = true;

            if (result) {
                refreshConfigCache(tenantId);
                log.info("成功重置租户 {} 的配置", tenantId);
            }

            return result;
        } catch (Exception e) {
            log.error("重置租户配置失败: {}", e.getMessage(), e);
            throw new BusinessException("重置租户配置失败: " + e.getMessage());
        }
    }

    @Override
    public String getDefaultConfigValue(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }

        // 返回系统默认值，不再依赖特定租户
        return getSystemDefaultValue(configKey);
    }

    /**
     * 获取系统默认配置值
     */
    private String getSystemDefaultValue(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }

        // 根据配置键返回系统默认值
        switch (configKey) {
            case "system.theme":
                return "default";
            case "system.title":
                return "XHC AI Plus 平台";
            case "system.copyright":
                return "Copyright © 2024 XHC科技";
            case "system.logo":
                return "/static/images/logo.png";
            case "system.favicon":
                return "/static/images/favicon.ico";
            case "system.language":
                return "zh-CN";
            case "system.timezone":
                return "Asia/Shanghai";
            case "system.dateFormat":
                return "YYYY-MM-DD";
            case "system.timeFormat":
                return "HH:mm:ss";
            case "system.pageSize":
                return "20";
            case "system.maxFileSize":
                return "10485760"; // 10MB
            case "system.allowedFileTypes":
                return "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar";
            default:
                return null;
        }
    }

    /**
     * 创建默认租户配置
     */
    private void createDefaultTenantConfigs(String tenantId) {
        try {
            List<SysTenantConfig> defaultConfigs = new ArrayList<>();

            // 基础系统配置
            addDefaultConfig(defaultConfigs, tenantId, "system.theme", "default", "系统主题", "系统界面主题设置", 1);
            addDefaultConfig(defaultConfigs, tenantId, "system.title", "XHC AI Plus 平台", "系统标题", "系统页面标题", 2);
            addDefaultConfig(defaultConfigs, tenantId, "system.copyright", "Copyright © 2024 XHC科技", "版权信息", "系统版权信息", 3);
            addDefaultConfig(defaultConfigs, tenantId, "system.logo", "/static/images/logo.png", "系统Logo", "系统Logo图片路径", 4);
            addDefaultConfig(defaultConfigs, tenantId, "system.favicon", "/static/images/favicon.ico", "网站图标", "浏览器标签页图标", 5);

            // 国际化配置
            addDefaultConfig(defaultConfigs, tenantId, "system.language", "zh-CN", "系统语言", "系统默认语言", 10);
            addDefaultConfig(defaultConfigs, tenantId, "system.timezone", "Asia/Shanghai", "时区设置", "系统默认时区", 11);
            addDefaultConfig(defaultConfigs, tenantId, "system.dateFormat", "YYYY-MM-DD", "日期格式", "系统日期显示格式", 12);
            addDefaultConfig(defaultConfigs, tenantId, "system.timeFormat", "HH:mm:ss", "时间格式", "系统时间显示格式", 13);

            // 分页配置
            addDefaultConfig(defaultConfigs, tenantId, "system.pageSize", "20", "分页大小", "列表页面默认分页大小", 20);
            addDefaultConfig(defaultConfigs, tenantId, "system.maxPageSize", "100", "最大分页", "列表页面最大分页大小", 21);

            // 文件上传配置
            addDefaultConfig(defaultConfigs, tenantId, "system.maxFileSize", "10485760", "文件大小限制", "单个文件最大上传大小(字节)", 30);
            addDefaultConfig(defaultConfigs, tenantId, "system.allowedFileTypes", "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar", "允许文件类型", "允许上传的文件类型", 31);
            addDefaultConfig(defaultConfigs, tenantId, "system.uploadPath", "/uploads", "上传路径", "文件上传存储路径", 32);

            // 安全配置
            addDefaultConfig(defaultConfigs, tenantId, "security.sessionTimeout", "1800", "会话超时", "用户会话超时时间(秒)", 40);
            addDefaultConfig(defaultConfigs, tenantId, "security.passwordMinLength", "6", "密码最小长度", "用户密码最小长度", 41);
            addDefaultConfig(defaultConfigs, tenantId, "security.passwordMaxLength", "20", "密码最大长度", "用户密码最大长度", 42);
            addDefaultConfig(defaultConfigs, tenantId, "security.loginRetryLimit", "5", "登录重试限制", "登录失败重试次数限制", 43);

            // AI配置
            addDefaultConfig(defaultConfigs, tenantId, "ai.enabled", "true", "AI功能启用", "是否启用AI功能", 50);
            addDefaultConfig(defaultConfigs, tenantId, "ai.maxTokens", "2000", "AI最大令牌", "AI对话最大令牌数", 51);
            addDefaultConfig(defaultConfigs, tenantId, "ai.temperature", "0.7", "AI温度参数", "AI回答的创造性程度", 52);

            // 批量保存配置
            if (!defaultConfigs.isEmpty()) {
                saveBatch(defaultConfigs);
                log.info("为租户 {} 创建了 {} 个默认配置", tenantId, defaultConfigs.size());
            }

        } catch (Exception e) {
            log.error("创建默认租户配置失败: {}", e.getMessage(), e);
            throw new BusinessException("创建默认租户配置失败: " + e.getMessage());
        }
    }

    /**
     * 添加默认配置项
     */
    private void addDefaultConfig(List<SysTenantConfig> configs, String tenantId, String key, String value,
                                 String name, String desc, int sortOrder) {
        SysTenantConfig config = new SysTenantConfig();
        config.setConfigKey(key);
        config.setConfigValue(value);
        config.setConfigName(name);
        config.setConfigDesc(desc);
        config.setConfigType("string");
        config.setIsSystem(true);
        config.setIsEncrypted(false);
        config.setSortOrder(sortOrder);
        config.setTenantId(tenantId);
        config.setRemark("系统默认配置");
        configs.add(config);
    }

    @Override
    public void refreshConfigCache(String tenantId) {
        if (tenantId == null) {
            return;
        }

        try {
            // 从数据库重新加载配置
            List<SysTenantConfig> configs = tenantConfigMapper.selectByTenantId(tenantId);
            Map<String, String> configMap = configs.stream()
                    .collect(Collectors.toMap(
                            SysTenantConfig::getConfigKey,
                            config -> config.getConfigValue() != null ? config.getConfigValue() : "",
                            (existing, replacement) -> replacement
                    ));

            // 更新缓存
            configCache.put(tenantId, new ConcurrentHashMap<>(configMap));

            log.debug("刷新租户 {} 的配置缓存，共 {} 个配置", tenantId, configMap.size());
        } catch (Exception e) {
            log.error("刷新租户 {} 配置缓存失败: {}", tenantId, e.getMessage(), e);
        }
    }

    @Override
    public void clearConfigCache(String tenantId) {
        if (tenantId == null) {
            return;
        }

        configCache.remove(tenantId);
        log.debug("清除租户 {} 的配置缓存", tenantId);
    }

    /**
     * 清除所有配置缓存
     */
    public void clearAllConfigCache() {
        configCache.clear();
        log.info("清除所有租户配置缓存");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cachedTenantCount", configCache.size());
        stats.put("totalConfigCount", configCache.values().stream()
                .mapToInt(Map::size)
                .sum());
        return stats;
    }
}
