package com.yyzs.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yyzs.agent.entity.ElasticComponent;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Elastic组件Mapper接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface ElasticComponentMapper extends BaseMapper<ElasticComponent> {

    /**
     * 根据组件类型查询组件列表
     */
    @Select("SELECT * FROM elastic_component WHERE type = #{type} AND deleted = 0 ORDER BY create_time DESC")
    List<ElasticComponent> findByType(@Param("type") String type);

    /**
     * 根据状态查询组件列表
     */
    @Select("SELECT * FROM elastic_component WHERE status = #{status} AND deleted = 0 ORDER BY create_time DESC")
    List<ElasticComponent> findByStatus(@Param("status") String status);

    /**
     * 查询运行中的组件列表
     */
    @Select("SELECT * FROM elastic_component WHERE status = 'RUNNING' AND deleted = 0 ORDER BY create_time DESC")
    List<ElasticComponent> findRunningComponents();

    /**
     * 查询已安装的组件列表
     */
    @Select("SELECT * FROM elastic_component WHERE status IN ('INSTALLED', 'RUNNING', 'STOPPED') AND deleted = 0 ORDER BY create_time DESC")
    List<ElasticComponent> findInstalledComponents();

    /**
     * 根据名称查询组件
     */
    @Select("SELECT * FROM elastic_component WHERE name = #{name} AND deleted = 0 LIMIT 1")
    ElasticComponent findByName(@Param("name") String name);

    /**
     * 根据端口查询组件
     */
    @Select("SELECT * FROM elastic_component WHERE port = #{port} AND deleted = 0 LIMIT 1")
    ElasticComponent findByPort(@Param("port") Integer port);

    /**
     * 根据进程ID查询组件
     */
    @Select("SELECT * FROM elastic_component WHERE process_id = #{processId} AND deleted = 0 LIMIT 1")
    ElasticComponent findByProcessId(@Param("processId") Long processId);

    /**
     * 更新组件状态
     */
    @Update("UPDATE elastic_component SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") String status);

    /**
     * 更新组件进程ID
     */
    @Update("UPDATE elastic_component SET process_id = #{processId}, update_time = NOW() WHERE id = #{id}")
    int updateProcessId(@Param("id") String id, @Param("processId") Long processId);

    /**
     * 批量更新组件状态
     */
    @Update("<script>" +
            "UPDATE elastic_component SET status = #{status}, update_time = NOW() WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status);

    /**
     * 统计各状态组件数量
     */
    @Select("SELECT status, COUNT(*) as count FROM elastic_component WHERE deleted = 0 GROUP BY status")
    @Results({
        @Result(column = "status", property = "status"),
        @Result(column = "count", property = "count")
    })
    List<ComponentStatusCount> countByStatus();

    /**
     * 统计各类型组件数量
     */
    @Select("SELECT type, COUNT(*) as count FROM elastic_component WHERE deleted = 0 GROUP BY type")
    @Results({
        @Result(column = "type", property = "type"),
        @Result(column = "count", property = "count")
    })
    List<ComponentTypeCount> countByType();

    /**
     * 组件状态统计结果类
     */
    class ComponentStatusCount {
        private String status;
        private Long count;

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
    }

    /**
     * 组件类型统计结果类
     */
    class ComponentTypeCount {
        private String type;
        private Long count;

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
    }
}
