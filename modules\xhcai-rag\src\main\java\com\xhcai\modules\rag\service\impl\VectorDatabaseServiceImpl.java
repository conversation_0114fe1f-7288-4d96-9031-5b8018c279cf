package com.xhcai.modules.rag.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.utils.XhcaiUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.VectorDatabaseCreateDTO;
import com.xhcai.modules.rag.dto.VectorDatabaseQueryDTO;
import com.xhcai.modules.rag.dto.VectorDatabaseUpdateDTO;
import com.xhcai.modules.rag.entity.VectorDatabase;
import com.xhcai.modules.rag.mapper.VectorDatabaseMapper;
import com.xhcai.modules.rag.service.IVectorDatabaseService;
import com.xhcai.modules.rag.vo.VectorDatabaseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 向量数据库服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
public class VectorDatabaseServiceImpl extends ServiceImpl<VectorDatabaseMapper, VectorDatabase> implements IVectorDatabaseService {

    @Autowired
    private VectorDatabaseMapper vectorDatabaseMapper;

    @Override
    public PageResult<VectorDatabaseVO> selectVectorDatabasePage(VectorDatabaseQueryDTO queryDTO) {
        Page<VectorDatabaseVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<VectorDatabaseVO> result = vectorDatabaseMapper.selectVectorDatabasePage(page, queryDTO);
        return PageResult.of(result.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
    }

    @Override
    public List<VectorDatabaseVO> selectVectorDatabaseList(VectorDatabaseQueryDTO queryDTO) {
        return vectorDatabaseMapper.selectVectorDatabaseList(queryDTO);
    }

    @Override
    public VectorDatabaseVO selectVectorDatabaseById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }
        return vectorDatabaseMapper.selectVectorDatabaseById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertVectorDatabase(VectorDatabaseCreateDTO createDTO) {
        // 参数校验
        validateVectorDatabase(createDTO, true);

        // 检查名称是否已存在
        if (existsVectorDatabaseName(createDTO.getName(), null)) {
            throw new BusinessException("向量数据库名称已存在");
        }

        // 如果设置为默认，先清除其他默认设置
        if ("Y".equals(createDTO.getIsDefault())) {
            vectorDatabaseMapper.clearDefaultVectorDatabase();
        }

        // 创建向量数据库实体
        VectorDatabase vectorDatabase = new VectorDatabase();
        BeanUtils.copyProperties(createDTO, vectorDatabase);
        
        // 设置ID
        vectorDatabase.setId(XhcaiUtils.generateSimpleUuid());
        
        // 设置创建信息
        vectorDatabase.setCreateBy(SecurityUtils.getCurrentUserId());
        vectorDatabase.setCreateTime(LocalDateTime.now());
        
        // 设置租户ID
        vectorDatabase.setTenantId(SecurityUtils.getCurrentTenantId());

        boolean result = save(vectorDatabase);
        if (result) {
            log.info("创建向量数据库成功: {}", createDTO.getName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVectorDatabase(String id, VectorDatabaseUpdateDTO updateDTO) {
        // 参数校验
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }

        // 检查向量数据库是否存在
        VectorDatabase existingVectorDatabase = getById(id);
        if (existingVectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }

        // 检查名称是否已存在（排除当前记录）
        if (StringUtils.hasText(updateDTO.getName()) && existsVectorDatabaseName(updateDTO.getName(), id)) {
            throw new BusinessException("向量数据库名称已存在");
        }

        // 如果设置为默认，先清除其他默认设置
        if ("Y".equals(updateDTO.getIsDefault())) {
            vectorDatabaseMapper.clearDefaultVectorDatabase();
        }

        // 更新向量数据库信息
        BeanUtils.copyProperties(updateDTO, existingVectorDatabase);
        
        // 设置更新信息
        existingVectorDatabase.setUpdateBy(SecurityUtils.getCurrentUserId());
        existingVectorDatabase.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(existingVectorDatabase);
        if (result) {
            log.info("更新向量数据库成功: {}", existingVectorDatabase.getName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVectorDatabases(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("向量数据库ID列表不能为空");
        }

        // 检查是否包含默认向量数据库
        for (String id : ids) {
            VectorDatabase vectorDatabase = getById(id);
            if (vectorDatabase != null && "Y".equals(vectorDatabase.getIsDefault())) {
                throw new BusinessException("不能删除默认向量数据库");
            }
        }

        boolean result = removeByIds(ids);
        if (result) {
            log.info("删除向量数据库成功，数量: {}", ids.size());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, String status) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("向量数据库ID列表不能为空");
        }
        if (!StringUtils.hasText(status)) {
            throw new BusinessException("状态不能为空");
        }

        int count = vectorDatabaseMapper.batchUpdateStatus(ids, status);
        boolean result = count > 0;
        if (result) {
            log.info("批量更新向量数据库状态成功，数量: {}", count);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultVectorDatabase(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }

        // 检查向量数据库是否存在且启用
        VectorDatabase vectorDatabase = getById(id);
        if (vectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }
        if (!"0".equals(vectorDatabase.getStatus())) {
            throw new BusinessException("只能设置启用状态的向量数据库为默认");
        }

        // 先清除其他默认设置
        vectorDatabaseMapper.clearDefaultVectorDatabase();
        
        // 设置新的默认向量数据库
        int count = vectorDatabaseMapper.setDefaultVectorDatabase(id);
        boolean result = count > 0;
        if (result) {
            log.info("设置默认向量数据库成功: {}", vectorDatabase.getName());
        }
        return result;
    }

    @Override
    public VectorDatabaseVO getDefaultVectorDatabase() {
        return vectorDatabaseMapper.selectDefaultVectorDatabase();
    }

    @Override
    public List<VectorDatabaseVO> getEnabledVectorDatabases() {
        return vectorDatabaseMapper.selectEnabledVectorDatabases();
    }

    @Override
    public Map<String, Object> testConnection(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }

        VectorDatabase vectorDatabase = getById(id);
        if (vectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }

        return performConnectionTest(vectorDatabase);
    }

    @Override
    public Map<String, Object> testConnection(VectorDatabaseCreateDTO createDTO) {
        // 参数校验
        validateVectorDatabase(createDTO, false);

        // 创建临时向量数据库对象进行测试
        VectorDatabase vectorDatabase = new VectorDatabase();
        BeanUtils.copyProperties(createDTO, vectorDatabase);

        return performConnectionTest(vectorDatabase);
    }

    @Override
    public Map<String, Object> getStatistics(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }

        VectorDatabase vectorDatabase = getById(id);
        if (vectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("id", id);
        statistics.put("name", vectorDatabase.getName());
        statistics.put("type", vectorDatabase.getType());
        statistics.put("status", vectorDatabase.getStatus());
        statistics.put("lastTestTime", vectorDatabase.getLastTestTime());
        statistics.put("lastTestResult", vectorDatabase.getLastTestResult());
        
        // TODO: 实现具体的统计逻辑，如索引数量、文档数量等
        statistics.put("indexCount", 0);
        statistics.put("documentCount", 0);
        statistics.put("storageSize", "0 MB");

        return statistics;
    }

    @Override
    public boolean initializeVectorDatabase(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }

        VectorDatabase vectorDatabase = getById(id);
        if (vectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }

        // TODO: 实现向量数据库初始化逻辑
        log.info("初始化向量数据库: {}", vectorDatabase.getName());
        return true;
    }

    @Override
    public boolean cleanupVectorDatabase(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }

        VectorDatabase vectorDatabase = getById(id);
        if (vectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }

        // TODO: 实现向量数据库清理逻辑
        log.info("清理向量数据库: {}", vectorDatabase.getName());
        return true;
    }

    @Override
    public Map<String, Object> backupVectorDatabase(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }

        VectorDatabase vectorDatabase = getById(id);
        if (vectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "备份任务已启动");
        result.put("backupPath", "/backup/" + vectorDatabase.getName() + "_" + System.currentTimeMillis());
        
        // TODO: 实现向量数据库备份逻辑
        log.info("备份向量数据库: {}", vectorDatabase.getName());
        return result;
    }

    @Override
    public Map<String, Object> restoreVectorDatabase(String id, String backupPath) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("向量数据库ID不能为空");
        }
        if (!StringUtils.hasText(backupPath)) {
            throw new BusinessException("备份路径不能为空");
        }

        VectorDatabase vectorDatabase = getById(id);
        if (vectorDatabase == null) {
            throw new BusinessException("向量数据库不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "恢复任务已启动");
        
        // TODO: 实现向量数据库恢复逻辑
        log.info("恢复向量数据库: {}, 备份路径: {}", vectorDatabase.getName(), backupPath);
        return result;
    }

    @Override
    public boolean existsVectorDatabaseName(String name, String excludeId) {
        if (!StringUtils.hasText(name)) {
            return false;
        }

        LambdaQueryWrapper<VectorDatabase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VectorDatabase::getName, name);
        if (StringUtils.hasText(excludeId)) {
            queryWrapper.ne(VectorDatabase::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public List<VectorDatabaseVO> exportVectorDatabases(VectorDatabaseQueryDTO queryDTO) {
        return selectVectorDatabaseList(queryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importVectorDatabases(List<VectorDatabase> vectorDatabases) {
        if (vectorDatabases == null || vectorDatabases.isEmpty()) {
            throw new BusinessException("导入数据不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (VectorDatabase vectorDatabase : vectorDatabases) {
            try {
                // 检查名称是否已存在
                if (existsVectorDatabaseName(vectorDatabase.getName(), null)) {
                    vectorDatabase.setName(vectorDatabase.getName() + "_" + System.currentTimeMillis());
                }

                // 重新生成ID
                vectorDatabase.setId(XhcaiUtils.generateSimpleUuid());
                
                // 设置创建信息
                vectorDatabase.setCreateBy(SecurityUtils.getCurrentUserId());
                vectorDatabase.setCreateTime(LocalDateTime.now());
                vectorDatabase.setTenantId(SecurityUtils.getCurrentTenantId());

                save(vectorDatabase);
                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMessages.append("导入向量数据库失败: ").append(vectorDatabase.getName())
                           .append(", 错误: ").append(e.getMessage()).append("; ");
                log.error("导入向量数据库失败: {}", vectorDatabase.getName(), e);
            }
        }

        String result = String.format("导入完成，成功: %d, 失败: %d", successCount, failCount);
        if (failCount > 0) {
            result += ", 错误信息: " + errorMessages.toString();
        }

        log.info("导入向量数据库完成: {}", result);
        return result;
    }

    /**
     * 执行连接测试
     */
    private Map<String, Object> performConnectionTest(VectorDatabase vectorDatabase) {
        Map<String, Object> result = new HashMap<>();
        String testResult = "Y";
        String errorMessage = null;

        try {
            // TODO: 根据不同的向量数据库类型实现具体的连接测试逻辑
            switch (vectorDatabase.getType()) {
                case "elasticsearch":
                    testElasticsearchConnection(vectorDatabase);
                    break;
                case "weaviate":
                    testWeaviateConnection(vectorDatabase);
                    break;
                case "postgresql_pgvector":
                    testPostgreSQLConnection(vectorDatabase);
                    break;
                case "milvus":
                    testMilvusConnection(vectorDatabase);
                    break;
                case "qdrant":
                    testQdrantConnection(vectorDatabase);
                    break;
                default:
                    throw new BusinessException("不支持的向量数据库类型: " + vectorDatabase.getType());
            }

            result.put("success", true);
            result.put("message", "连接测试成功");
        } catch (Exception e) {
            testResult = "N";
            errorMessage = e.getMessage();
            result.put("success", false);
            result.put("message", "连接测试失败: " + e.getMessage());
            log.error("向量数据库连接测试失败: {}", vectorDatabase.getName(), e);
        }

        // 更新测试结果（如果有ID）
        if (StringUtils.hasText(vectorDatabase.getId())) {
            vectorDatabaseMapper.updateTestResult(vectorDatabase.getId(), testResult, errorMessage);
        }

        result.put("testTime", LocalDateTime.now());
        result.put("testResult", testResult);
        return result;
    }

    /**
     * 测试Elasticsearch连接
     */
    private void testElasticsearchConnection(VectorDatabase vectorDatabase) {
        // TODO: 实现Elasticsearch连接测试
        log.info("测试Elasticsearch连接: {}:{}", vectorDatabase.getHost(), vectorDatabase.getPort());
    }

    /**
     * 测试Weaviate连接
     */
    private void testWeaviateConnection(VectorDatabase vectorDatabase) {
        // TODO: 实现Weaviate连接测试
        log.info("测试Weaviate连接: {}:{}", vectorDatabase.getHost(), vectorDatabase.getPort());
    }

    /**
     * 测试PostgreSQL连接
     */
    private void testPostgreSQLConnection(VectorDatabase vectorDatabase) {
        // TODO: 实现PostgreSQL连接测试
        log.info("测试PostgreSQL连接: {}:{}", vectorDatabase.getHost(), vectorDatabase.getPort());
    }

    /**
     * 测试Milvus连接
     */
    private void testMilvusConnection(VectorDatabase vectorDatabase) {
        // TODO: 实现Milvus连接测试
        log.info("测试Milvus连接: {}:{}", vectorDatabase.getHost(), vectorDatabase.getPort());
    }

    /**
     * 测试Qdrant连接
     */
    private void testQdrantConnection(VectorDatabase vectorDatabase) {
        // TODO: 实现Qdrant连接测试
        log.info("测试Qdrant连接: {}:{}", vectorDatabase.getHost(), vectorDatabase.getPort());
    }

    /**
     * 参数校验
     */
    private void validateVectorDatabase(VectorDatabaseCreateDTO createDTO, boolean isInsert) {
        if (createDTO == null) {
            throw new BusinessException("向量数据库信息不能为空");
        }
        if (!StringUtils.hasText(createDTO.getName())) {
            throw new BusinessException("向量数据库名称不能为空");
        }
        if (!StringUtils.hasText(createDTO.getType())) {
            throw new BusinessException("向量数据库类型不能为空");
        }
        if (!StringUtils.hasText(createDTO.getHost())) {
            throw new BusinessException("主机地址不能为空");
        }
        if (createDTO.getPort() == null || createDTO.getPort() <= 0 || createDTO.getPort() > 65535) {
            throw new BusinessException("端口必须在1-65535之间");
        }
    }
}
