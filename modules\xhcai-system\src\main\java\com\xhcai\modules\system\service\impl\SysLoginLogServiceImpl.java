package com.xhcai.modules.system.service.impl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.SysLoginLogQueryDTO;
import com.xhcai.modules.system.entity.SysLoginLog;
import com.xhcai.modules.system.mapper.SysLoginLogMapper;
import com.xhcai.modules.system.service.ISysLoginLogService;
import com.xhcai.modules.system.vo.SysLoginLogVO;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 用户登录日志服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class SysLoginLogServiceImpl extends ServiceImpl<SysLoginLogMapper, SysLoginLog> implements ISysLoginLogService {

    private static final Logger log = LoggerFactory.getLogger(SysLoginLogServiceImpl.class);

    @Override
    public void recordLoginLog(String username, String status, String message, HttpServletRequest request) {
        try {
            SysLoginLog loginLog = new SysLoginLog();
            loginLog.setUsername(username);
            loginLog.setStatus(status);
            loginLog.setMsg(message);
            loginLog.setLoginTime(LocalDateTime.now());
            loginLog.setSessionId(request.getSession().getId());

            // 获取IP地址
            String ip = getClientIpAddress(request);
            loginLog.setLoginIp(ip);

            // 设置登陆用户ID
            try {
                String userId = SecurityUtils.getCurrentUserId();
                loginLog.setUserId(userId);
                if (StringUtils.hasText(userId)) {
                    loginLog.setTenantId(userId);
                }
            } catch (Exception e) {
                // 登录时可能还没有设置用户信息，忽略异常
                log.debug("获取用户ID失败，可能是登录过程中: {}", e.getMessage());
            }

            // 获取浏览器信息
            String userAgent = request.getHeader("User-Agent");
            if (StringUtils.hasText(userAgent)) {
                loginLog.setBrowser(getBrowserInfo(userAgent));
                loginLog.setOs(getOsInfo(userAgent));
            }

            // 获取登录地点（这里可以集成IP地址库）
            loginLog.setLoginLocation(getLocationByIp(ip));

            // 设置租户ID
            try {
                String tenantId = SecurityUtils.getCurrentTenantId();
                if (StringUtils.hasText(tenantId)) {
                    loginLog.setTenantId(tenantId);
                }
            } catch (Exception e) {
                // 登录时可能还没有设置租户信息，忽略异常
                log.debug("获取租户ID失败，可能是登录过程中: {}", e.getMessage());
            }

            save(loginLog);
            log.info("记录登录日志成功: username={}, status={}, ip={}", username, status, ip);
        } catch (Exception e) {
            log.error("记录登录日志失败: username={}, status={}", username, status, e);
        }
    }

    @Override
    public void recordLoginSuccess(String username, HttpServletRequest request) {
        recordLoginLog(username, "0", "登录成功", request);
    }

    @Override
    public void recordLoginFailure(String username, String message, HttpServletRequest request) {
        recordLoginLog(username, "1", message, request);
    }

    @Override
    public void recordLogout(String username, String sessionId) {
        try {
            // 更新登出时间
            LambdaQueryWrapper<SysLoginLog> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysLoginLog::getUsername, username)
                    .eq(SysLoginLog::getSessionId, sessionId)
                    .eq(SysLoginLog::getStatus, "0")
                    .isNull(SysLoginLog::getLogoutTime)
                    .orderByDesc(SysLoginLog::getLoginTime)
                    .last("LIMIT 1");

            SysLoginLog loginLog = getOne(wrapper);
            if (loginLog != null) {
                loginLog.setLogoutTime(LocalDateTime.now());
                updateById(loginLog);
                log.info("记录登出日志成功: username={}, sessionId={}", username, sessionId);
            }
        } catch (Exception e) {
            log.error("记录登出日志失败: username={}, sessionId={}", username, sessionId, e);
        }
    }

    @Override
    public void cleanExpiredLogs(int days) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
            LambdaQueryWrapper<SysLoginLog> wrapper = new LambdaQueryWrapper<>();
            wrapper.lt(SysLoginLog::getLoginTime, expireTime);

            int count = Math.toIntExact(count(wrapper));
            if (count > 0) {
                remove(wrapper);
                log.info("清理过期登录日志成功，清理数量: {}", count);
            }
        } catch (Exception e) {
            log.error("清理过期登录日志失败", e);
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = null;

        // 尝试从各种代理头中获取真实IP
        String[] headers = {
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR",
            "X-Real-IP"
        };

        for (String header : headers) {
            ip = request.getHeader(header);
            if (isValidIp(ip)) {
                break;
            }
        }

        // 如果代理头中没有获取到有效IP，则从RemoteAddr获取
        if (!isValidIp(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个有效IP
        if (ip != null && ip.contains(",")) {
            String[] ips = ip.split(",");
            for (String singleIp : ips) {
                singleIp = singleIp.trim();
                if (isValidIp(singleIp)) {
                    ip = singleIp;
                    break;
                }
            }
        }

        // 处理IPv6本地回环地址
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            ip = "127.0.0.1";
        }

        return ip != null ? ip : "127.0.0.1";
    }

    /**
     * 验证IP地址是否有效
     */
    private boolean isValidIp(String ip) {
        return ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip);
    }

    /**
     * 获取浏览器信息
     */
    private String getBrowserInfo(String userAgent) {
        if (userAgent.contains("Chrome")) {
            return "Chrome";
        } else if (userAgent.contains("Firefox")) {
            return "Firefox";
        } else if (userAgent.contains("Safari") && !userAgent.contains("Chrome")) {
            return "Safari";
        } else if (userAgent.contains("Edge")) {
            return "Edge";
        } else if (userAgent.contains("Opera")) {
            return "Opera";
        } else {
            return "Unknown";
        }
    }

    /**
     * 获取操作系统信息
     */
    private String getOsInfo(String userAgent) {
        if (userAgent.contains("Windows")) {
            return "Windows";
        } else if (userAgent.contains("Mac")) {
            return "Mac OS";
        } else if (userAgent.contains("Linux")) {
            return "Linux";
        } else if (userAgent.contains("Android")) {
            return "Android";
        } else if (userAgent.contains("iPhone") || userAgent.contains("iPad")) {
            return "iOS";
        } else {
            return "Unknown";
        }
    }

    /**
     * 根据IP获取地理位置 这里可以集成第三方IP地址库，如高德、百度等
     */
    private String getLocationByIp(String ip) {
        // 简单判断内网IP
        if (isInternalIp(ip)) {
            return "内网IP";
        }

        // 这里可以调用第三方API获取真实地理位置
        // 暂时返回未知
        return "未知";
    }

    /**
     * 判断是否为内网IP
     */
    private boolean isInternalIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return true;
        }

        // 本地回环地址
        if ("127.0.0.1".equals(ip) || "localhost".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
            return true;
        }

        // 内网IP段
        Pattern pattern = Pattern.compile("^(10\\.|172\\.(1[6-9]|2\\d|3[01])\\.|192\\.168\\.).*");
        Matcher matcher = pattern.matcher(ip);
        return matcher.matches();
    }

    @Override
    public PageResult<SysLoginLogVO> selectLoginLogPage(SysLoginLogQueryDTO queryDTO) {
        // 创建分页对象
        Page<SysLoginLog> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

        // 构建查询条件
        LambdaQueryWrapper<SysLoginLog> wrapper = new LambdaQueryWrapper<>();

        // 用户名模糊查询
        if (StringUtils.hasText(queryDTO.getUsername())) {
            wrapper.like(SysLoginLog::getUsername, queryDTO.getUsername());
        }

        // 登录IP模糊查询
        if (StringUtils.hasText(queryDTO.getLoginIp())) {
            wrapper.like(SysLoginLog::getLoginIp, queryDTO.getLoginIp());
        }

        // 登录地点模糊查询
        if (StringUtils.hasText(queryDTO.getLoginLocation())) {
            wrapper.like(SysLoginLog::getLoginLocation, queryDTO.getLoginLocation());
        }

        // 登录状态精确查询
        if (StringUtils.hasText(queryDTO.getStatus())) {
            wrapper.eq(SysLoginLog::getStatus, queryDTO.getStatus());
        }

        // 浏览器模糊查询
        if (StringUtils.hasText(queryDTO.getBrowser())) {
            wrapper.like(SysLoginLog::getBrowser, queryDTO.getBrowser());
        }

        // 操作系统模糊查询
        if (StringUtils.hasText(queryDTO.getOs())) {
            wrapper.like(SysLoginLog::getOs, queryDTO.getOs());
        }

        // 会话ID精确查询
        if (StringUtils.hasText(queryDTO.getSessionId())) {
            wrapper.eq(SysLoginLog::getSessionId, queryDTO.getSessionId());
        }

        // 时间范围查询
        if (queryDTO.getBeginTime() != null) {
            wrapper.ge(SysLoginLog::getLoginTime, queryDTO.getBeginTime());
        }
        if (queryDTO.getEndTime() != null) {
            wrapper.le(SysLoginLog::getLoginTime, queryDTO.getEndTime());
        }

        // 排序
        if (StringUtils.hasText(queryDTO.getOrderBy())) {
            if ("asc".equalsIgnoreCase(queryDTO.getOrderDirection())) {
                wrapper.orderByAsc(SysLoginLog::getLoginTime);
            } else {
                wrapper.orderByDesc(SysLoginLog::getLoginTime);
            }
        } else {
            wrapper.orderByDesc(SysLoginLog::getLoginTime);
        }

        // 执行分页查询
        IPage<SysLoginLog> pageResult = page(page, wrapper);

        // 转换为VO
        List<SysLoginLogVO> voList = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .toList();

        return PageResult.of(voList, pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize());
    }

    @Override
    public SysLoginLogVO selectLoginLogById(String id) {
        SysLoginLog loginLog = getById(id);
        return loginLog != null ? convertToVO(loginLog) : null;
    }

    @Override
    public void deleteLoginLogById(String id) {
        removeById(id);
        log.info("删除登录日志成功: id={}", id);
    }

    @Override
    public void deleteLoginLogByIds(String[] ids) {
        removeBatchByIds(Arrays.asList(ids));
        log.info("批量删除登录日志成功: ids={}", Arrays.toString(ids));
    }

    @Override
    public Object getLoginStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总登录次数
        long totalLogins = count();
        stats.put("totalLogins", totalLogins);

        // 成功登录次数
        LambdaQueryWrapper<SysLoginLog> successWrapper = new LambdaQueryWrapper<>();
        successWrapper.eq(SysLoginLog::getStatus, "0");
        long successLogins = count(successWrapper);
        stats.put("successLogins", successLogins);

        // 失败登录次数
        long failedLogins = totalLogins - successLogins;
        stats.put("failedLogins", failedLogins);

        // 活跃用户数（最近30天有登录记录的用户）
        LambdaQueryWrapper<SysLoginLog> activeWrapper = new LambdaQueryWrapper<>();
        activeWrapper.eq(SysLoginLog::getStatus, "0")
                .ge(SysLoginLog::getLoginTime, LocalDateTime.now().minusDays(30))
                .select(SysLoginLog::getUsername)
                .groupBy(SysLoginLog::getUsername);
        List<SysLoginLog> activeUsers = list(activeWrapper);
        stats.put("activeUsers", activeUsers.size());

        return stats;
    }

    /**
     * 转换为VO对象
     */
    private SysLoginLogVO convertToVO(SysLoginLog loginLog) {
        SysLoginLogVO vo = new SysLoginLogVO();
        BeanUtils.copyProperties(loginLog, vo);
        return vo;
    }
}
