package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.system.entity.SysTenantConfig;

/**
 * 租户配置Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysTenantConfigMapper extends BaseMapper<SysTenantConfig> {

    /**
     * 根据配置键和租户ID查询配置
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @return 配置信息
     */
    @Select("SELECT * FROM sys_tenant_config "
            + "WHERE config_key = #{configKey} AND tenant_id = #{tenantId} AND deleted = 0")
    SysTenantConfig selectByKeyAndTenant(@Param("configKey") String configKey, @Param("tenantId") String tenantId);

    /**
     * 根据租户ID查询所有配置
     *
     * @param tenantId 租户ID
     * @return 配置列表
     */
    @Select("SELECT * FROM sys_tenant_config "
            + "WHERE tenant_id = #{tenantId} AND deleted = 0 "
            + "ORDER BY sort_order ASC, config_key ASC")
    List<SysTenantConfig> selectByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据配置类型和租户ID查询配置
     *
     * @param configType 配置类型
     * @param tenantId 租户ID
     * @return 配置列表
     */
    @Select("SELECT * FROM sys_tenant_config "
            + "WHERE config_type = #{configType} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "ORDER BY sort_order ASC, config_key ASC")
    List<SysTenantConfig> selectByTypeAndTenant(@Param("configType") String configType, @Param("tenantId") String tenantId);

    /**
     * 查询系统配置
     *
     * @param tenantId 租户ID
     * @return 系统配置列表
     */
    @Select("SELECT * FROM sys_tenant_config "
            + "WHERE is_system = true AND tenant_id = #{tenantId} AND deleted = 0 "
            + "ORDER BY sort_order ASC, config_key ASC")
    List<SysTenantConfig> selectSystemConfigs(@Param("tenantId") String tenantId);

    /**
     * 查询用户配置
     *
     * @param tenantId 租户ID
     * @return 用户配置列表
     */
    @Select("SELECT * FROM sys_tenant_config "
            + "WHERE is_system = false AND tenant_id = #{tenantId} AND deleted = 0 "
            + "ORDER BY sort_order ASC, config_key ASC")
    List<SysTenantConfig> selectUserConfigs(@Param("tenantId") String tenantId);

    /**
     * 批量更新配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @param tenantId 租户ID
     * @return 更新行数
     */
    @Update("UPDATE sys_tenant_config SET config_value = #{configValue}, update_time = NOW() "
            + "WHERE config_key = #{configKey} AND tenant_id = #{tenantId} AND deleted = 0")
    int updateConfigValue(@Param("configKey") String configKey,
            @Param("configValue") String configValue,
            @Param("tenantId") String tenantId);

    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @param excludeId 排除的配置ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_tenant_config "
            + "WHERE config_key = #{configKey} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "  AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsConfigKey(@Param("configKey") String configKey,
            @Param("tenantId") String tenantId,
            @Param("excludeId") String excludeId);

    /**
     * 复制默认配置到新租户
     *
     * @param sourceTenantId 源租户ID
     * @param targetTenantId 目标租户ID
     * @param createBy 创建人
     * @return 复制的配置数量
     */
    @Insert("INSERT INTO sys_tenant_config "
            + "(id, config_key, config_value, config_name, config_desc, config_type, "
            + "is_system, is_encrypted, sort_order, tenant_id, remark, deleted, create_by, create_time, update_by, update_time) "
            + "SELECT REPLACE(gen_random_uuid()::text, '-', ''), config_key, config_value, config_name, config_desc, config_type, "
            + "is_system, is_encrypted, sort_order, #{targetTenantId}, remark, '0', #{createBy}, NOW(), #{createBy}, NOW() "
            + "FROM sys_tenant_config "
            + "WHERE tenant_id = #{sourceTenantId} AND deleted = 0")
    int copyConfigsToTenant(@Param("sourceTenantId") String sourceTenantId,
            @Param("targetTenantId") String targetTenantId,
            @Param("createBy") String createBy);
}
