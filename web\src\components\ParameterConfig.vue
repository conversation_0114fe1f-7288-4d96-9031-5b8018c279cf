<template>
  <div v-if="parameters && parameters.length > 0" class="parameter-config">
    <div class="config-header">
      <div class="header-left">
        <div class="header-icon">
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <span class="header-text">参数配置</span>
      </div>
      <button
        @click="handleCollapse"
        class="collapse-button"
        title="收起参数配置"
      >
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="config-form">
      <div
        v-for="(param, index) in parameters"
        :key="index"
        class="form-item"
      >
        <!-- 下拉选择框 -->
        <div v-if="isSelectParameter(param)" class="form-field">
          <label class="field-label">
            {{ param.select.label }}
            <span v-if="param.select.required" class="required-mark">*</span>
          </label>
          <el-select
            :model-value="values[param.select.variable] || ''"
            @update:model-value="updateValue(param.select.variable, $event)"
            :placeholder="`请选择${param.select.label}`"
            :class="[
              'field-input',
              { 'field-error': param.select.required && (!values[param.select.variable] || values[param.select.variable] === '') }
            ]"
            size="small"
          >
            <el-option
              v-for="option in param.select.options"
              :key="option"
              :label="option"
              :value="option"
            />
          </el-select>
          <div v-if="param.select.required && (!values[param.select.variable] || values[param.select.variable] === '')" class="error-message">
            {{ param.select.label }}是必填项
          </div>
        </div>

        <!-- 文本输入框 -->
        <div v-else-if="isTextInputParameter(param)" class="form-field">
          <label class="field-label">
            {{ getTextInputLabel(param) }}
            <span v-if="getTextInputRequired(param)" class="required-mark">*</span>
          </label>
          <el-input
            :model-value="values[getTextInputVariable(param)] || ''"
            @update:model-value="updateValue(getTextInputVariable(param), $event)"
            :placeholder="`请输入${getTextInputLabel(param)}`"
            :maxlength="getTextInputMaxLength(param)"
            :class="[
              'field-input',
              { 'field-error': getTextInputRequired(param) && (!values[getTextInputVariable(param)] || values[getTextInputVariable(param)].toString().trim() === '') }
            ]"
            size="small"
            show-word-limit
          />
          <div v-if="getTextInputRequired(param) && (!values[getTextInputVariable(param)] || values[getTextInputVariable(param)].toString().trim() === '')" class="error-message">
            {{ getTextInputLabel(param) }}是必填项
          </div>
        </div>

        <!-- 段落文本框 -->
        <div v-else-if="isParagraphParameter(param)" class="form-field">
          <label class="field-label">
            {{ getParagraphLabel(param) }}
            <span v-if="getParagraphRequired(param)" class="required-mark">*</span>
          </label>
          <el-input
            :model-value="values[getParagraphVariable(param)] || ''"
            @update:model-value="updateValue(getParagraphVariable(param), $event)"
            :placeholder="`请输入${getParagraphLabel(param)}`"
            :maxlength="getParagraphMaxLength(param)"
            type="textarea"
            :rows="3"
            class="field-input"
            size="small"
            show-word-limit
            resize="vertical"
          />
        </div>

        <!-- 数字输入框 -->
        <div v-else-if="isNumberParameter(param)" class="form-field">
          <label class="field-label">
            {{ getNumberLabel(param) }}
            <span v-if="getNumberRequired(param)" class="required-mark">*</span>
          </label>
          <el-input-number
            :model-value="values[getNumberVariable(param)] || 0"
            @update:model-value="updateValue(getNumberVariable(param), $event)"
            :placeholder="`请输入${getNumberLabel(param)}`"
            :max="getNumberMaxLength(param)"
            :min="0"
            class="field-input"
            size="small"
            controls-position="right"
          />
        </div>

        <!-- 单个文件上传 -->
        <div v-else-if="isFileParameter(param)" class="form-field">
          <label class="field-label">
            {{ getFileLabel(param) }}
            <span v-if="getFileRequired(param)" class="required-mark">*</span>
          </label>
          <div class="file-upload-container">
            <el-upload
              :file-list="getFileList(getFileVariable(param))"
              :limit="1"
              :on-change="(file, fileList) => handleFileChange(getFileVariable(param), file, fileList, false, param)"
              :on-remove="(file) => handleFileRemove(getFileVariable(param), file, false)"
              :before-upload="() => false"
              :show-file-list="true"
              :accept="getFileAccept(param)"
              class="file-uploader"
              drag
            >
              <div class="upload-content">
                <el-icon class="upload-icon"><Upload /></el-icon>
                <div class="upload-text">点击或拖拽文件到此处上传</div>
                <div class="upload-hint">{{ getFileHint(param) }}</div>
              </div>
            </el-upload>
          </div>
        </div>

        <!-- 批量文件上传 -->
        <div v-else-if="isFileListParameter(param)" class="form-field">
          <label class="field-label">
            {{ getFileListLabel(param) }}
            <span v-if="getFileListRequired(param)" class="required-mark">*</span>
          </label>
          <div class="file-upload-container">
            <el-upload
              :file-list="getFileList(getFileListVariable(param))"
              :limit="getFileListMaxLength(param)"
              :on-change="(file, fileList) => handleFileChange(getFileListVariable(param), file, fileList, true, param)"
              :on-remove="(file) => handleFileRemove(getFileListVariable(param), file, true)"
              :before-upload="() => false"
              :show-file-list="true"
              :accept="getFileListAccept(param)"
              class="file-uploader"
              drag
              multiple
            >
              <div class="upload-content">
                <el-icon class="upload-icon"><Upload /></el-icon>
                <div class="upload-text">点击或拖拽文件到此处上传</div>
                <div class="upload-hint">{{ getFileListHint(param) }}</div>
              </div>
            </el-upload>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElSelect, ElOption, ElInput, ElUpload, ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import type { UploadFile, UploadFiles } from 'element-plus'
import { exploreApi } from '@/api/explore'

// 参数类型定义
interface SelectParameter {
  select: {
    variable: string
    label: string
    type: 'select'
    max_length: number
    required: boolean
    options: string[]
  }
}

interface TextInputParameter {
  variable: string
  label: string
  type: 'text-input'
  max_length: number
  required: boolean
  options: string[]
}

interface TextInputParameterWrapper {
  'text-input': {
    variable: string
    label: string
    type: 'text-input'
    max_length: number
    required: boolean
    options: string[]
  }
}

interface ParagraphParameterWrapper {
  'paragraph': {
    variable: string
    label: string
    type: 'paragraph'
    max_length: number
    required: boolean
    options: string[]
  }
}

interface NumberParameterWrapper {
  'number': {
    variable: string
    label: string
    type: 'number'
    max_length: number
    required: boolean
    options: string[]
  }
}

interface FileParameterWrapper {
  'file': {
    variable: string
    label: string
    type: 'file'
    max_length: number
    required: boolean
    options: string[]
    allowed_file_upload_methods: string[]
    allowed_file_types: string[]
    allowed_file_extensions: string[]
  }
}

interface FileListParameterWrapper {
  'file-list': {
    variable: string
    label: string
    type: 'file-list'
    max_length: number
    required: boolean
    options: string[]
    allowed_file_upload_methods: string[]
    allowed_file_types: string[]
    allowed_file_extensions: string[]
  }
}

type Parameter = SelectParameter | TextInputParameter | TextInputParameterWrapper | ParagraphParameterWrapper | NumberParameterWrapper | FileParameterWrapper | FileListParameterWrapper

// 类型守卫函数
const isSelectParameter = (param: Parameter): param is SelectParameter => {
  return 'select' in param
}

const isTextInputParameter = (param: Parameter): param is TextInputParameter | TextInputParameterWrapper => {
  return ('variable' in param && 'type' in param && param.type === 'text-input') || 'text-input' in param
}

const isParagraphParameter = (param: Parameter): param is ParagraphParameterWrapper => {
  return 'paragraph' in param
}

const isNumberParameter = (param: Parameter): param is NumberParameterWrapper => {
  return 'number' in param
}

const isFileParameter = (param: Parameter): param is FileParameterWrapper => {
  return 'file' in param
}

const isFileListParameter = (param: Parameter): param is FileListParameterWrapper => {
  return 'file-list' in param
}

interface Props {
  parameters?: Parameter[]
  values?: Record<string, any>
}

interface Emits {
  (e: 'update:values', values: Record<string, any>): void
  (e: 'collapse'): void
  (e: 'validation-change', isValid: boolean, errors: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  parameters: () => [],
  values: () => ({})
})

const emit = defineEmits<Emits>()

// 辅助函数：获取文本输入框的属性
const getTextInputVariable = (param: Parameter): string => {
  if ('text-input' in param) {
    return param['text-input'].variable
  }
  return (param as TextInputParameter).variable
}

const getTextInputLabel = (param: Parameter): string => {
  if ('text-input' in param) {
    return param['text-input'].label
  }
  return (param as TextInputParameter).label
}

const getTextInputRequired = (param: Parameter): boolean => {
  if ('text-input' in param) {
    return param['text-input'].required
  }
  return (param as TextInputParameter).required
}

const getTextInputMaxLength = (param: Parameter): number => {
  if ('text-input' in param) {
    return param['text-input'].max_length
  }
  return (param as TextInputParameter).max_length
}

// 辅助函数：获取段落文本框的属性
const getParagraphVariable = (param: Parameter): string => {
  return (param as ParagraphParameterWrapper).paragraph.variable
}

const getParagraphLabel = (param: Parameter): string => {
  return (param as ParagraphParameterWrapper).paragraph.label
}

const getParagraphRequired = (param: Parameter): boolean => {
  return (param as ParagraphParameterWrapper).paragraph.required
}

const getParagraphMaxLength = (param: Parameter): number => {
  return (param as ParagraphParameterWrapper).paragraph.max_length
}

// 辅助函数：获取数字输入框的属性
const getNumberVariable = (param: Parameter): string => {
  return (param as NumberParameterWrapper).number.variable
}

const getNumberLabel = (param: Parameter): string => {
  return (param as NumberParameterWrapper).number.label
}

const getNumberRequired = (param: Parameter): boolean => {
  return (param as NumberParameterWrapper).number.required
}

const getNumberMaxLength = (param: Parameter): number => {
  return (param as NumberParameterWrapper).number.max_length
}

// 辅助函数：获取单个文件上传的属性
const getFileVariable = (param: Parameter): string => {
  return (param as FileParameterWrapper).file.variable
}

const getFileLabel = (param: Parameter): string => {
  return (param as FileParameterWrapper).file.label
}

const getFileRequired = (param: Parameter): boolean => {
  return (param as FileParameterWrapper).file.required
}

const getFileAccept = (param: Parameter): string => {
  const fileParam = (param as FileParameterWrapper).file
  if (fileParam.allowed_file_extensions && fileParam.allowed_file_extensions.length > 0) {
    return fileParam.allowed_file_extensions.join(',')
  }
  return '*'
}

const getFileHint = (param: Parameter): string => {
  const fileParam = (param as FileParameterWrapper).file
  const types = fileParam.allowed_file_types?.join('、') || '所有类型'
  return `支持${types}文件`
}

// 辅助函数：获取批量文件上传的属性
const getFileListVariable = (param: Parameter): string => {
  return (param as FileListParameterWrapper)['file-list'].variable
}

const getFileListLabel = (param: Parameter): string => {
  return (param as FileListParameterWrapper)['file-list'].label
}

const getFileListRequired = (param: Parameter): boolean => {
  return (param as FileListParameterWrapper)['file-list'].required
}

const getFileListMaxLength = (param: Parameter): number => {
  return (param as FileListParameterWrapper)['file-list'].max_length
}

const getFileListAccept = (param: Parameter): string => {
  const fileListParam = (param as FileListParameterWrapper)['file-list']
  if (fileListParam.allowed_file_extensions && fileListParam.allowed_file_extensions.length > 0) {
    return fileListParam.allowed_file_extensions.join(',')
  }
  return '*'
}

const getFileListHint = (param: Parameter): string => {
  const fileListParam = (param as FileListParameterWrapper)['file-list']
  const types = fileListParam.allowed_file_types?.join('、') || '所有类型'
  const maxCount = fileListParam.max_length
  return `支持${types}文件，最多${maxCount}个`
}

// 更新参数值
const updateValue = (variable: string, value: any) => {
  console.log('🔧 参数值更新:', variable, '=', value)
  const newValues = { ...props.values, [variable]: value }
  console.log('🔧 新的参数值集合:', newValues)
  emit('update:values', newValues)
}

// 处理收起按钮点击
const handleCollapse = () => {
  console.log('🔧 参数配置收起')
  emit('collapse')
}

// 获取文件列表用于显示
const getFileList = (variable: string): UploadFile[] => {
  const fileData = props.values[variable]
  if (!fileData) return []

  if (Array.isArray(fileData)) {
    // 批量文件
    return fileData.map((file, index) => ({
      name: file.name || `文件${index + 1}`,
      uid: file.upload_file_id || `${index}`,
      status: 'success' as const,
      url: file.url
    }))
  } else {
    // 单个文件
    return [{
      name: fileData.name || '文件',
      uid: fileData.upload_file_id || '1',
      status: 'success' as const,
      url: fileData.url
    }]
  }
}

// 处理文件变化
const handleFileChange = async (variable: string, file: UploadFile, fileList: UploadFiles, isMultiple: boolean, param: Parameter) => {
  console.log('🔧 文件变化:', variable, file, fileList, 'isMultiple:', isMultiple)

  // 只处理新添加的文件（状态为ready的文件）
  const newFiles = fileList.filter(f => f.status === 'ready' && f.raw)
  console.log('🔧 新文件数量:', newFiles.length)

  if (newFiles.length === 0) {
    return
  }

  try {
    if (isMultiple) {
      // 批量文件上传
      const currentFiles = props.values[variable] || []
      console.log('🔧 当前已有文件数量:', currentFiles.length)

      const uploadPromises = newFiles.map(f => uploadSingleFile(f.raw!, param))

      // 显示上传中状态
      newFiles.forEach(f => {
        f.status = 'uploading'
      })

      const uploadResults = await Promise.all(uploadPromises)
      console.log('🔧 上传结果:', uploadResults)

      // 更新文件状态为成功
      newFiles.forEach((f, index) => {
        f.status = 'success'
        f.uid = uploadResults[index].id
      })

      const newFileData = uploadResults.map(result => ({
        name: result.original_filename,
        type: getFileTypeFromMimeType(result.mime_type),
        transfer_method: 'local_file',
        url: result.minio_url || '',
        upload_file_id: result.id,
        dify_file_id: result.dify_file_id
      }))

      const allFiles = [...currentFiles, ...newFileData]
      console.log('🔧 更新后的所有文件:', allFiles)
      updateValue(variable, allFiles)

    } else {
      // 单个文件上传
      const newFile = newFiles[0]
      newFile.status = 'uploading'

      const uploadResult = await uploadSingleFile(newFile.raw!, param)

      newFile.status = 'success'
      newFile.uid = uploadResult.id

      const fileData = {
        name: uploadResult.original_filename,
        type: getFileTypeFromMimeType(uploadResult.mime_type),
        transfer_method: 'local_file',
        url: uploadResult.minio_url || '',
        upload_file_id: uploadResult.id,
        dify_file_id: uploadResult.dify_file_id
      }

      updateValue(variable, fileData)
    }

  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败，请重试')

    // 更新文件状态为失败
    newFiles.forEach(f => {
      f.status = 'fail'
    })
  }
}

// 处理文件移除
const handleFileRemove = (variable: string, file: UploadFile, isMultiple: boolean) => {
  console.log('🔧 文件移除:', variable, file)

  if (isMultiple) {
    // 批量文件
    const currentFiles = props.values[variable] || []
    const updatedFiles = currentFiles.filter((f: any) => f.upload_file_id !== file.uid)
    updateValue(variable, updatedFiles)
  } else {
    // 单个文件
    updateValue(variable, null)
  }
}

// 上传单个文件到服务器
const uploadSingleFile = async (file: File, param: Parameter): Promise<any> => {
  console.log('🔧 开始上传文件:', file.name)

  const response = await exploreApi.uploadChatFile(file)

  if (response.success && response.data) {
    console.log('✅ 文件上传成功:', response.data)
    return response.data
  } else {
    throw new Error(response.message || '文件上传失败')
  }
}

// 根据MIME类型获取文件类型
const getFileTypeFromMimeType = (mimeType: string): string => {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('audio/')) return 'audio'
  if (mimeType.startsWith('video/')) return 'video'
  return 'document'
}

// 根据文件获取文件类型（备用）
const getFileTypeFromFile = (file: File | undefined): string => {
  if (!file) return 'document'
  return getFileTypeFromMimeType(file.type)
}

// 验证参数是否有效
const validateParameters = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  props.parameters.forEach(param => {
    if (isSelectParameter(param)) {
      const config = param.select
      if (config.required) {
        const value = props.values[config.variable]
        if (!value || value === '') {
          errors.push(`${config.label}是必填项`)
        }
      }
    } else if (isTextInputParameter(param)) {
      const variable = getTextInputVariable(param)
      const label = getTextInputLabel(param)
      const required = getTextInputRequired(param)
      if (required) {
        const value = props.values[variable]
        if (!value || value.toString().trim() === '') {
          errors.push(`${label}是必填项`)
        }
      }
    } else if (isParagraphParameter(param)) {
      const variable = getParagraphVariable(param)
      const label = getParagraphLabel(param)
      const required = getParagraphRequired(param)
      if (required) {
        const value = props.values[variable]
        if (!value || value.toString().trim() === '') {
          errors.push(`${label}是必填项`)
        }
      }
    } else if (isNumberParameter(param)) {
      const variable = getNumberVariable(param)
      const label = getNumberLabel(param)
      const required = getNumberRequired(param)
      if (required) {
        const value = props.values[variable]
        if (value === null || value === undefined || value === '') {
          errors.push(`${label}是必填项`)
        }
      }
    } else if (isFileParameter(param)) {
      const variable = getFileVariable(param)
      const label = getFileLabel(param)
      const required = getFileRequired(param)
      if (required) {
        const value = props.values[variable]
        if (!value || !value.upload_file_id) {
          errors.push(`${label}是必填项`)
        }
      }
    } else if (isFileListParameter(param)) {
      const variable = getFileListVariable(param)
      const label = getFileListLabel(param)
      const required = getFileListRequired(param)
      if (required) {
        const value = props.values[variable]
        if (!value || !Array.isArray(value) || value.length === 0) {
          errors.push(`${label}是必填项`)
        }
      }
    }
  })

  const isValid = errors.length === 0
  return { isValid, errors }
}

// 监听参数值变化，自动验证
import { watch } from 'vue'
watch(() => props.values, () => {
  const { isValid, errors } = validateParameters()
  emit('validation-change', isValid, errors)
}, { deep: true, immediate: true })
</script>

<style scoped>
.parameter-config {
  @apply mt-3 p-3 bg-gradient-to-br from-green-50/40 to-emerald-50/20 rounded-lg border border-green-100/40;
  backdrop-filter: blur(8px);
}

.config-header {
  @apply flex items-center justify-between mb-3 text-xs font-medium text-green-600;
}

.header-left {
  @apply flex items-center gap-1.5;
}

.header-icon {
  @apply flex items-center justify-center w-4 h-4 bg-green-100/60 rounded-full;
}

.header-text {
  @apply text-green-700;
}

.collapse-button {
  @apply flex items-center justify-center w-5 h-5 text-green-500 hover:text-green-700 hover:bg-green-100/60 rounded-full transition-all duration-200 cursor-pointer;
}

.collapse-button:hover {
  @apply scale-110;
}

.config-form {
  @apply space-y-3;
}

.form-item {
  @apply w-full;
}

.form-field {
  @apply w-full;
}

.field-label {
  @apply block text-xs font-medium text-gray-700 mb-1;
}

.required-mark {
  @apply text-red-500 ml-0.5;
}

.field-input {
  @apply w-full;
}

.field-error :deep(.el-input__wrapper) {
  @apply border-red-300 shadow-red-100;
}

.field-error :deep(.el-select .el-input__wrapper) {
  @apply border-red-300 shadow-red-100;
}

.error-message {
  @apply text-red-500 text-xs mt-1;
}

/* 数字输入框样式 */
.field-input :deep(.el-input-number) {
  @apply w-full;
}

.field-input :deep(.el-input-number .el-input__inner) {
  text-align: left;
}

/* 文本域样式 */
.field-input :deep(.el-textarea__inner) {
  @apply resize-y min-h-[60px];
}

/* 文件上传容器样式 */
.file-upload-container {
  @apply w-full;
}

.file-uploader :deep(.el-upload) {
  @apply w-full;
}

.file-uploader :deep(.el-upload-dragger) {
  @apply w-full h-24 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 hover:border-green-400 hover:bg-green-50 transition-colors duration-200;
}

.upload-content {
  @apply flex flex-col items-center justify-center h-full text-gray-500;
}

.upload-icon {
  @apply text-2xl mb-1 text-gray-400;
}

.upload-text {
  @apply text-sm font-medium mb-1;
}

.upload-hint {
  @apply text-xs text-gray-400;
}

/* 文件列表样式 */
.file-uploader :deep(.el-upload-list) {
  @apply mt-2;
}

.file-uploader :deep(.el-upload-list__item) {
  @apply bg-white border border-gray-200 rounded-md;
}

.file-uploader :deep(.el-upload-list__item.is-uploading) {
  @apply border-blue-300 bg-blue-50;
}

.file-uploader :deep(.el-upload-list__item.is-success) {
  @apply border-green-300 bg-green-50;
}

.file-uploader :deep(.el-upload-list__item.is-fail) {
  @apply border-red-300 bg-red-50;
}

/* 上传进度条样式 */
.file-uploader :deep(.el-progress) {
  @apply mt-1;
}

.file-uploader :deep(.el-progress-bar__outer) {
  @apply bg-gray-200;
}

.file-uploader :deep(.el-progress-bar__inner) {
  @apply bg-blue-500;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-select) {
  @apply w-full;
}

:deep(.el-select .el-input__wrapper) {
  @apply bg-white/80 border-green-200/60 hover:border-green-300/80 focus:border-green-400;
  backdrop-filter: blur(4px);
}

:deep(.el-input__wrapper) {
  @apply bg-white/80 border-green-200/60 hover:border-green-300/80 focus:border-green-400;
  backdrop-filter: blur(4px);
}

:deep(.el-input__inner) {
  @apply text-sm;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .parameter-config {
    @apply mt-2 p-2.5;
  }

  .config-header {
    @apply mb-2;
  }

  .config-form {
    @apply space-y-2;
  }

  .field-label {
    @apply text-xs;
  }
}

/* 动画效果 */
.parameter-config {
  animation: fadeInUp 0.25s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-item {
  animation: fadeInScale 0.2s ease-out;
}

.form-item:nth-child(1) { animation-delay: 0.05s; }
.form-item:nth-child(2) { animation-delay: 0.1s; }
.form-item:nth-child(3) { animation-delay: 0.15s; }
.form-item:nth-child(4) { animation-delay: 0.2s; }
.form-item:nth-child(5) { animation-delay: 0.25s; }

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
