package com.xhcai.plugin.annotation;

import com.xhcai.plugin.core.PluginType;

import java.lang.annotation.*;

/**
 * 插件服务注解
 * 用于标记插件服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PluginService {
    
    /**
     * 插件类型
     */
    PluginType type();
    
    /**
     * 服务名称
     */
    String name();
    
    /**
     * 服务描述
     */
    String description() default "";
    
    /**
     * 服务版本
     */
    String version() default "1.0.0";
    
    /**
     * 服务优先级（数值越小优先级越高）
     */
    int priority() default 100;
    
    /**
     * 是否默认启用
     */
    boolean enabled() default true;
}
