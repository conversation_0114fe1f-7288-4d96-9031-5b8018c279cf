<template>
  <div class="execution-log-panel" :class="{ collapsed: !isExpanded }">
    <!-- 面板头部 -->
    <div class="panel-header" @click="toggleExpanded">
      <div class="header-left">
        <i class="fas fa-list-alt"></i>
        <span class="panel-title">执行日志</span>
        <span v-if="executionSteps.length > 0" class="step-count">
          ({{ executionSteps.length }} 步)
        </span>
      </div>
      <div class="header-right">
        <button
          v-if="isExpanded"
          class="btn btn-sm btn-secondary"
          @click.stop="clearLogs"
          :disabled="executionSteps.length === 0"
        >
          <i class="fas fa-trash"></i>
          清除
        </button>
        <button class="btn btn-icon" @click.stop="toggleExpanded">
          <i class="fas" :class="isExpanded ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
        </button>
      </div>
    </div>

    <!-- 面板内容 -->
    <div v-if="isExpanded" class="panel-content">


      <!-- 步骤列表 -->
      <div class="steps-container">
        <div
          v-for="(step, index) in executionSteps"
          :key="step.nodeId"
          class="step-item"
          :class="`step-${step.status}`"
        >
          <!-- 步骤头部 -->
          <div class="step-header" @click="toggleStepExpanded(index)">
            <div class="step-info">
              <div class="step-icon">
                <i v-if="step.status === 'pending'" class="fas fa-clock"></i>
                <i v-else-if="step.status === 'waiting'" class="fas fa-hourglass-start"></i>
                <i v-else-if="step.status === 'running'" class="fas fa-spinner fa-spin"></i>
                <i v-else-if="step.status === 'completed'" class="fas fa-check-circle"></i>
                <i v-else-if="step.status === 'error'" class="fas fa-exclamation-circle"></i>
                <i v-else-if="step.status === 'skipped'" class="fas fa-forward"></i>
              </div>
              <div class="step-details">
                <div class="step-name">{{ step.nodeName }}</div>
                <div class="step-meta">
                  <span class="step-type">{{ step.nodeType }}</span>
                  <span v-if="step.duration" class="step-duration">
                    {{ formatDuration(step.duration) }}
                  </span>
                  <span v-if="step.progress !== undefined" class="step-progress">
                    {{ step.progress }}%
                  </span>
                </div>
              </div>
            </div>
            <div class="step-actions">
              <button class="btn btn-icon btn-sm">
                <i class="fas" :class="step.expanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
              </button>
            </div>
          </div>

          <!-- 步骤详情 -->
          <div v-if="step.expanded" class="step-content">
            <!-- 输入数据 -->
            <div v-if="step.input" class="data-section">
              <div class="section-title">
                <i class="fas fa-sign-in-alt"></i>
                输入数据
              </div>
              <div class="data-content">
                <pre>{{ formatData(step.input) }}</pre>
              </div>
            </div>

            <!-- 输出数据 -->
            <div v-if="step.output" class="data-section">
              <div class="section-title">
                <i class="fas fa-sign-out-alt"></i>
                输出数据
              </div>
              <div class="data-content">
                <pre>{{ formatData(step.output) }}</pre>
              </div>
            </div>

            <!-- 错误信息 -->
            <div v-if="step.error" class="data-section error-section">
              <div class="section-title">
                <i class="fas fa-exclamation-triangle"></i>
                错误信息
              </div>
              <div class="data-content error-content">
                <pre>{{ step.error }}</pre>
              </div>
            </div>

            <!-- 日志记录 -->
            <div v-if="step.logs && step.logs.length > 0" class="data-section">
              <div class="section-title">
                <i class="fas fa-file-alt"></i>
                执行日志
              </div>
              <div class="logs-content">
                <div
                  v-for="(log, logIndex) in step.logs"
                  :key="logIndex"
                  class="log-item"
                  :class="`log-${log.level}`"
                >
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                  <span class="log-level">{{ log.level.toUpperCase() }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="executionSteps.length === 0" class="empty-state">
        <i class="fas fa-inbox"></i>
        <p>暂无执行日志</p>
        <p class="empty-hint">点击"开始"按钮运行工作流</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props
interface ExecutionLogPanelProps {
  executionSteps?: any[]
  executionStats?: any
  autoExpand?: boolean
}

const props = withDefaults(defineProps<ExecutionLogPanelProps>(), {
  executionSteps: () => [],
  autoExpand: true
})

// Emits
const emit = defineEmits<{
  'clear-logs': []
}>()

// 响应式数据
const isExpanded = ref(props.autoExpand)
const stepExpandedStates = ref<Map<string, boolean>>(new Map())

// 计算属性
const executionSteps = computed(() => {
  return props.executionSteps.map((step, index) => ({
    ...step,
    expanded: stepExpandedStates.value.get(step.nodeId) || step.expanded || false
  }))
})

// 监听执行步骤变化，自动展开面板
watch(() => props.executionSteps.length, (newLength, oldLength) => {
  if (newLength > oldLength && props.autoExpand) {
    isExpanded.value = true
  }
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const toggleStepExpanded = (index: number) => {
  const step = props.executionSteps[index]
  if (step) {
    const currentState = stepExpandedStates.value.get(step.nodeId) || step.expanded || false
    stepExpandedStates.value.set(step.nodeId, !currentState)
  }
}

const clearLogs = () => {
  emit('clear-logs')
}



const formatDuration = (duration: number): string => {
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

const formatData = (data: any): string => {
  if (typeof data === 'string') {
    return data
  }
  return JSON.stringify(data, null, 2)
}
</script>

<style scoped>
.execution-log-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.execution-log-panel.collapsed {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
}

.panel-header:hover {
  background: #f9fafb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-title {
  font-weight: 600;
  color: #1f2937;
}

.step-count {
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-content {
  max-height: calc(100vh - 400px);
  overflow-y: auto;
}



.steps-container {
  padding: 8px;
}

.step-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.2s;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-pending { border-color: #d1d5db; }
.step-waiting { border-color: #f59e0b; }
.step-running { border-color: #3b82f6; }
.step-completed { border-color: #10b981; }
.step-error { border-color: #ef4444; }
.step-skipped { border-color: #6b7280; }

.step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.step-header:hover {
  background: #f9fafb;
}

.step-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-icon {
  width: 20px;
  text-align: center;
  color: #6b7280;
}

.step-pending .step-icon { color: #d1d5db; }
.step-waiting .step-icon { color: #f59e0b; }
.step-running .step-icon { color: #3b82f6; }
.step-completed .step-icon { color: #10b981; }
.step-error .step-icon { color: #ef4444; }
.step-skipped .step-icon { color: #6b7280; }

.step-name {
  font-weight: 600;
  color: #1f2937;
}

.step-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.step-content {
  padding: 0 12px 12px;
  border-top: 1px solid #f3f4f6;
}

.data-section {
  margin-bottom: 16px;
}

.data-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.data-content {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.error-section .section-title {
  color: #ef4444;
}

.error-content {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.logs-content {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  font-size: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #9ca3af;
  min-width: 80px;
}

.log-level {
  font-weight: 600;
  min-width: 50px;
}

.log-debug .log-level { color: #6b7280; }
.log-info .log-level { color: #3b82f6; }
.log-warn .log-level { color: #f59e0b; }
.log-error .log-level { color: #ef4444; }

.log-message {
  flex: 1;
  color: #374151;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-hint {
  font-size: 14px;
  margin-top: 8px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-icon {
  padding: 4px;
  width: 24px;
  height: 24px;
  justify-content: center;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
