<template>
  <div class="dropdown-selector" :class="{ 'is-disabled': disabled }">
    <!-- 触发器 -->
    <div
      ref="triggerRef"
      class="dropdown-trigger"
      :class="[
        `dropdown-trigger--${size}`,
        {
          'is-active': visible,
          'is-disabled': disabled,
          'has-value': hasValue
        }
      ]"
      @click="handleTriggerClick"
    >
      <!-- 显示内容 -->
      <div class="trigger-content">
        <div v-if="!hasValue" class="placeholder">
          {{ placeholder }}
        </div>
        <div v-else class="selected-display">
          <slot name="display" :selected="selectedDisplay">
            <span class="selected-text">{{ selectedDisplay }}</span>
          </slot>
        </div>
      </div>
      
      <!-- 清空按钮 -->
      <div v-if="clearable && hasValue && !disabled" class="clear-btn" @click.stop="handleClear">
        <svg class="clear-icon" viewBox="0 0 1024 1024" width="14" height="14">
          <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1 1.9-11.2c1.5-1.2 3.3-1.9 5.2-1.9l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130.1 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z" fill="currentColor"/>
        </svg>
      </div>

      <!-- 下拉箭头 -->
      <div class="dropdown-arrow" :class="{ 'is-reverse': visible }">
        <svg class="arrow-icon" viewBox="0 0 1024 1024" width="14" height="14">
          <path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z" fill="currentColor"/>
        </svg>
      </div>
    </div>

    <!-- 下拉面板 -->
    <teleport to="body">
      <transition
        name="dropdown-fade"
        @enter="onEnter"
        @leave="onLeave"
      >
        <div
          v-if="visible"
          ref="dropdownRef"
          class="dropdown-panel"
          :class="[
            `dropdown-panel--${size}`,
            dropdownClass,
            { 'has-actions': showActions }
          ]"
          :style="dropdownStyle"
          @click.stop
        >
          <!-- 加载状态 -->
          <div v-if="loading" class="dropdown-loading">
            <div class="loading-spinner">
              <i class="el-icon-loading"></i>
            </div>
            <span class="loading-text">加载中...</span>
          </div>

          <!-- 内容区域 -->
          <div v-else class="dropdown-content">
            <slot
              :close="handleClose"
              :confirm="handleConfirm"
              :loading="loading"
              :setLoading="setLoading"
            >
              <!-- 默认内容 -->
              <div class="default-content">
                <div class="default-icon">
                  <i class="el-icon-box"></i>
                </div>
                <p class="default-text">请在 slot 中放置选择器组件</p>
              </div>
            </slot>
          </div>

          <!-- 操作按钮 -->
          <div v-if="showActions" class="dropdown-actions">
            <div class="actions-wrapper">
              <button
                class="action-btn cancel-btn"
                @click="handleCancel"
                :disabled="loading"
              >
                <i class="el-icon-close"></i>
                <span>取消</span>
              </button>
              <button
                class="action-btn confirm-btn"
                @click="handleConfirm"
                :disabled="loading"
              >
                <i class="el-icon-check"></i>
                <span>确定</span>
              </button>
            </div>
          </div>
        </div>
      </transition>
    </teleport>

    <!-- 遮罩层 -->
    <div
      v-if="visible"
      class="dropdown-overlay"
      @click="handleOverlayClick"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  modelValue?: any
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  size?: 'large' | 'default' | 'small'
  dropdownClass?: string
  showActions?: boolean
  closeOnSelect?: boolean
  width?: string | number
  maxHeight?: string | number
  placement?: 'bottom' | 'top' | 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end'
  displayText?: string  // 新增：自定义显示文本
  align?: 'left' | 'right' | 'center'  // 新增：对齐方式控制
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
  (e: 'clear'): void
  (e: 'visible-change', visible: boolean): void
  (e: 'confirm', value: any): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择',
  disabled: false,
  clearable: true,
  size: 'default',
  showActions: false,
  closeOnSelect: true,
  placement: 'bottom-start',
  align: 'left'
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const triggerRef = ref<HTMLElement>()
const dropdownRef = ref<HTMLElement>()
const tempValue = ref<any>(null)
const loading = ref(false)

// 计算属性
const hasValue = computed(() => {
  return props.modelValue !== null &&
         props.modelValue !== undefined &&
         props.modelValue !== '' &&
         (Array.isArray(props.modelValue) ? props.modelValue.length > 0 : true)
})

const selectedDisplay = computed(() => {
  if (!hasValue.value) return ''

  // 优先使用自定义显示文本
  if (props.displayText) {
    return props.displayText
  }

  if (Array.isArray(props.modelValue)) {
    return `已选择 ${props.modelValue.length} 项`
  }

  return String(props.modelValue)
})

const dropdownStyle = computed(() => {
  const style: any = {}
  
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  
  if (props.maxHeight) {
    style.maxHeight = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight
  }
  
  return style
})

// 方法
const updatePosition = async () => {
  if (!visible.value || !triggerRef.value || !dropdownRef.value) return

  await nextTick()

  const trigger = triggerRef.value
  const dropdown = dropdownRef.value
  const triggerRect = trigger.getBoundingClientRect()
  const viewportHeight = window.innerHeight
  const viewportWidth = window.innerWidth
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

  // 重置样式以获取真实尺寸
  dropdown.style.visibility = 'hidden'
  dropdown.style.display = 'block'
  dropdown.style.maxHeight = ''
  dropdown.style.width = ''
  dropdown.style.minWidth = ''

  await nextTick()

  // 获取下拉面板的自然尺寸
  const dropdownRect = dropdown.getBoundingClientRect()
  const margin = 12 // 增加边距，确保不会贴边

  let finalTop = 0
  let finalLeft = 0
  let actualPlacement = props.placement

  // === 水平位置计算 ===
  let preferredLeft = 0
  let alignMode = props.align

  // 根据 placement 确定初始对齐方式（如果没有明确指定 align）
  if (!props.align || props.align === 'left') {
    switch (props.placement) {
      case 'bottom-start':
      case 'top-start':
        alignMode = 'left'
        break
      case 'bottom-end':
      case 'top-end':
        alignMode = 'right'
        break
      default:
        alignMode = 'center'
    }
  }

  // 计算首选位置
  switch (alignMode) {
    case 'left':
      preferredLeft = triggerRect.left
      break
    case 'right':
      preferredLeft = triggerRect.right - dropdownRect.width
      break
    case 'center':
    default:
      preferredLeft = triggerRect.left + (triggerRect.width - dropdownRect.width) / 2
  }

  // 智能边界检查和调整
  const leftSpace = triggerRect.left - margin
  const rightSpace = viewportWidth - triggerRect.right - margin
  const centerSpace = Math.min(leftSpace, rightSpace)

  if (preferredLeft < margin) {
    // 左边超出边界
    if (alignMode === 'right' && rightSpace >= dropdownRect.width) {
      // 如果是右对齐且右侧有足够空间，保持右对齐
      finalLeft = triggerRect.right - dropdownRect.width
    } else if (alignMode === 'center' && centerSpace >= dropdownRect.width / 2) {
      // 如果是居中对齐，尝试调整到可居中的位置
      finalLeft = Math.max(margin, triggerRect.left + (triggerRect.width - dropdownRect.width) / 2)
    } else {
      // 否则左对齐到边界
      finalLeft = margin
    }
  } else if (preferredLeft + dropdownRect.width > viewportWidth - margin) {
    // 右边超出边界
    if (alignMode === 'left' && leftSpace >= dropdownRect.width) {
      // 如果是左对齐且左侧有足够空间，保持左对齐
      finalLeft = triggerRect.left
    } else if (alignMode === 'center' && centerSpace >= dropdownRect.width / 2) {
      // 如果是居中对齐，尝试调整到可居中的位置
      finalLeft = Math.min(viewportWidth - dropdownRect.width - margin,
                          triggerRect.left + (triggerRect.width - dropdownRect.width) / 2)
    } else {
      // 否则右对齐到边界
      finalLeft = viewportWidth - dropdownRect.width - margin
    }

    // 如果面板太宽，压缩宽度
    if (finalLeft < margin) {
      finalLeft = margin
      const maxWidth = viewportWidth - margin * 2
      dropdown.style.width = `${maxWidth}px`
      dropdown.style.maxWidth = `${maxWidth}px`
    }
  } else {
    // 没有超出边界，使用首选位置
    finalLeft = preferredLeft
  }

  // 特殊处理：当组件靠近边缘时的对齐优化
  const triggerCenterX = triggerRect.left + triggerRect.width / 2

  if (alignMode === 'center') {
    // 居中对齐时，确保下拉面板尽可能居中
    if (triggerCenterX < viewportWidth * 0.25) {
      // 触发器在左侧1/4区域，左对齐
      finalLeft = Math.max(margin, triggerRect.left)
    } else if (triggerCenterX > viewportWidth * 0.75) {
      // 触发器在右侧1/4区域，右对齐
      finalLeft = Math.min(viewportWidth - dropdownRect.width - margin, triggerRect.right - dropdownRect.width)
    }
  }

  // === 垂直位置计算 ===
  const spaceBelow = viewportHeight - triggerRect.bottom
  const spaceAbove = triggerRect.top
  const dropdownHeight = dropdownRect.height
  const gap = 4 // 触发器和面板之间的间距

  if (props.placement.startsWith('top')) {
    // 优先向上显示
    if (spaceAbove >= dropdownHeight + gap + margin) {
      // 向上有足够空间
      finalTop = triggerRect.top - dropdownHeight - gap
      actualPlacement = props.placement
    } else if (spaceBelow >= dropdownHeight + gap + margin) {
      // 向上空间不够，改为向下
      finalTop = triggerRect.bottom + gap
      actualPlacement = props.placement.replace('top', 'bottom') as any
    } else {
      // 两边都不够，选择空间更大的一边并限制高度
      if (spaceAbove > spaceBelow) {
        finalTop = margin
        const maxHeight = spaceAbove - gap - margin
        dropdown.style.maxHeight = `${maxHeight}px`
        actualPlacement = props.placement
      } else {
        finalTop = triggerRect.bottom + gap
        const maxHeight = spaceBelow - gap - margin
        dropdown.style.maxHeight = `${maxHeight}px`
        actualPlacement = props.placement.replace('top', 'bottom') as any
      }
    }
  } else {
    // 优先向下显示
    if (spaceBelow >= dropdownHeight + gap + margin) {
      // 向下有足够空间
      finalTop = triggerRect.bottom + gap
      actualPlacement = props.placement
    } else if (spaceAbove >= dropdownHeight + gap + margin) {
      // 向下空间不够，改为向上
      finalTop = triggerRect.top - dropdownHeight - gap
      actualPlacement = props.placement.replace('bottom', 'top') as any
    } else {
      // 两边都不够，选择空间更大的一边并限制高度
      if (spaceBelow > spaceAbove) {
        finalTop = triggerRect.bottom + gap
        const maxHeight = spaceBelow - gap - margin
        dropdown.style.maxHeight = `${maxHeight}px`
        actualPlacement = props.placement
      } else {
        finalTop = margin
        const maxHeight = spaceAbove - gap - margin
        dropdown.style.maxHeight = `${maxHeight}px`
        actualPlacement = props.placement.replace('bottom', 'top') as any
      }
    }
  }

  // 最终边界检查
  finalTop = Math.max(margin, Math.min(finalTop, viewportHeight - margin - 100)) // 至少保留100px高度
  finalLeft = Math.max(margin, Math.min(finalLeft, viewportWidth - margin - 100)) // 至少保留100px宽度

  // 应用最终位置（加上滚动偏移）
  dropdown.style.top = `${finalTop + scrollTop}px`
  dropdown.style.left = `${finalLeft + scrollLeft}px`
  dropdown.style.visibility = 'visible'

  // 设置最小宽度
  if (!props.width) {
    const minWidth = Math.max(triggerRect.width, 200)
    dropdown.style.minWidth = `${minWidth}px`
  }

  // 添加位置类名
  dropdown.className = dropdown.className.replace(/placement-\w+/g, '')
  dropdown.classList.add(`placement-${actualPlacement}`)
}

const handleTriggerClick = () => {
  if (props.disabled) return
  
  visible.value = !visible.value
  
  if (visible.value) {
    tempValue.value = props.modelValue
    nextTick(() => {
      updatePosition()
    })
  }
  
  emit('visible-change', visible.value)
}

const handleClose = () => {
  visible.value = false
  emit('visible-change', false)
}

const handleConfirm = () => {
  emit('update:modelValue', tempValue.value)
  emit('change', tempValue.value)
  emit('confirm', tempValue.value)
  handleClose()
}

const handleCancel = () => {
  tempValue.value = props.modelValue
  emit('cancel')
  handleClose()
}

const handleClear = () => {
  const clearValue = Array.isArray(props.modelValue) ? [] : null
  emit('update:modelValue', clearValue)
  emit('change', clearValue)
  emit('clear')
}

const handleOverlayClick = () => {
  if (props.showActions) {
    handleCancel()
  } else {
    handleClose()
  }
}

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 防抖的位置更新
const debouncedUpdatePosition = debounce(updatePosition, 16) // 约60fps

const handleResize = () => {
  if (visible.value) {
    debouncedUpdatePosition()
  }
}

const handleScroll = () => {
  if (visible.value) {
    debouncedUpdatePosition()
  }
}

const setLoading = (value: boolean) => {
  loading.value = value
}

// 动画钩子
const onEnter = (el: Element) => {
  const element = el as HTMLElement
  element.style.opacity = '0'
  element.style.transform = 'translateY(-8px) scale(0.95)'

  requestAnimationFrame(() => {
    element.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
    element.style.opacity = '1'
    element.style.transform = 'translateY(0) scale(1)'
  })
}

const onLeave = (el: Element) => {
  const element = el as HTMLElement
  element.style.transition = 'all 0.15s cubic-bezier(0.4, 0, 1, 1)'
  element.style.opacity = '0'
  element.style.transform = 'translateY(-4px) scale(0.98)'
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (!props.showActions) {
    tempValue.value = newValue
  }
})

// 生命周期
onMounted(() => {
  tempValue.value = props.modelValue
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleScroll, true)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleScroll, true)
})

// 暴露方法
defineExpose({
  close: handleClose,
  updatePosition
})
</script>

<style scoped>
.dropdown-selector {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}

.dropdown-trigger {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s;
  box-sizing: border-box;
}

.dropdown-trigger--large {
  min-height: 40px;
  height: auto;
  font-size: 14px;
  padding: 8px 48px 8px 12px; /* 增加右边距为清空按钮和箭头留空间 */
}

.dropdown-trigger--default {
  min-height: 32px;
  height: auto;
  font-size: 14px;
  padding: 6px 48px 6px 12px; /* 增加右边距为清空按钮和箭头留空间 */
}

.dropdown-trigger--small {
  min-height: 24px;
  height: auto;
  font-size: 12px;
  padding: 4px 40px 4px 8px; /* 增加右边距为清空按钮和箭头留空间 */
}

.dropdown-trigger:hover:not(.is-disabled) {
  border-color: #c0c4cc;
}

.dropdown-trigger.is-active {
  border-color: #409eff;
}

.dropdown-trigger.is-disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.trigger-content {
  flex: 1;
  overflow: hidden;
}

.placeholder {
  color: #c0c4cc;
  user-select: none;
}

.selected-display {
  color: #606266;
}

.selected-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clear-btn {
  position: absolute;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: #c0c4cc;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 50%;
  z-index: 1;
}

.clear-btn:hover {
  color: #909399;
}

.clear-icon {
  width: 14px;
  height: 14px;
  display: block;
}

.dropdown-arrow {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: #c0c4cc;
  transition: all 0.2s;
  pointer-events: none;
}

.dropdown-arrow.is-reverse {
  transform: translateY(-50%) rotate(180deg);
}

.arrow-icon {
  width: 14px;
  height: 14px;
  display: block;
}

/* 触发器状态下的图标颜色 */
.dropdown-trigger:hover:not(.is-disabled) .dropdown-arrow {
  color: #909399;
}

.dropdown-trigger.is-active .dropdown-arrow {
  color: #409eff;
}

.dropdown-trigger.is-disabled .dropdown-arrow,
.dropdown-trigger.is-disabled .clear-btn {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 确保内容区域不会与按钮重叠 */
.trigger-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.placeholder,
.selected-display {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.placeholder {
  color: #c0c4cc;
}

.selected-text {
  color: #606266;
}

/* 调试样式 - 确保按钮可见 */
.dropdown-trigger {
  position: relative;
}

.dropdown-trigger .clear-btn,
.dropdown-trigger .dropdown-arrow {
  opacity: 1;
  visibility: visible;
}

/* 小尺寸下的按钮调整 */
.dropdown-trigger--small .clear-btn {
  right: 22px;
  width: 14px;
  height: 14px;
}

.dropdown-trigger--small .clear-icon,
.dropdown-trigger--small .arrow-icon {
  width: 12px;
  height: 12px;
}

.dropdown-trigger--small .dropdown-arrow {
  right: 6px;
  width: 14px;
  height: 14px;
}

/* 大尺寸下的按钮调整 */
.dropdown-trigger--large .clear-btn {
  right: 30px;
  width: 18px;
  height: 18px;
}

.dropdown-trigger--large .clear-icon,
.dropdown-trigger--large .arrow-icon {
  width: 16px;
  height: 16px;
}

.dropdown-trigger--large .dropdown-arrow {
  right: 10px;
  width: 18px;
  height: 18px;
}

/* 确保SVG图标正确渲染 */
.clear-icon,
.arrow-icon {
  flex-shrink: 0;
  pointer-events: none;
}

/* 清空按钮的交互效果 */
.clear-btn:active {
  transform: translateY(-50%) scale(0.95);
}

/* 箭头旋转动画优化 */
.dropdown-arrow {
  transform-origin: center;
}

.dropdown-arrow.is-reverse .arrow-icon {
  transform: rotate(180deg);
}

/* 确保在不同主题下的可见性 */
@media (prefers-color-scheme: dark) {
  .clear-btn {
    color: #909399;
  }

  .clear-btn:hover {
    color: #c0c4cc;
  }

  .dropdown-arrow {
    color: #909399;
  }
}



.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1999;
  background: transparent;
}

.dropdown-panel {
  position: fixed;
  z-index: 99999;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.04);
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(8px);
  transform-origin: top center;
  /* 确保面板不会超出视窗 */
  max-width: calc(100vw - 24px);
  box-sizing: border-box;
}

.dropdown-content {
  flex: 1;
  overflow: auto;
  max-height: 360px;
  min-height: 0;
  padding: 4px;
}

.dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 3px;
  transition: background 0.2s;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* 加载状态 */
.dropdown-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.loading-spinner {
  font-size: 24px;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 默认内容样式 */
.default-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  text-align: center;
}

.default-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.default-text {
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

/* 操作按钮区域 */
.dropdown-actions {
  border-top: 1px solid #e4e7ed;
  background: linear-gradient(to bottom, #fafbfc, #f5f6f7);
  padding: 8px 12px;
}

.actions-wrapper {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #fff;
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 72px;
  justify-content: center;
}

.action-btn:hover:not(:disabled) {
  border-color: #c0c4cc;
  background: #f5f7fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-btn {
  color: #606266;
}

.cancel-btn:hover:not(:disabled) {
  color: #409eff;
  border-color: #409eff;
}

.confirm-btn {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.confirm-btn:hover:not(:disabled) {
  background: #66b1ff;
  border-color: #66b1ff;
}

/* 动画效果 */
.dropdown-fade-enter-active,
.dropdown-fade-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-fade-enter-from {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.dropdown-fade-leave-to {
  opacity: 0;
  transform: translateY(-4px) scale(0.98);
}

/* 位置相关样式 */
.placement-bottom,
.placement-bottom-start,
.placement-bottom-end {
  transform-origin: top center;
}

.placement-top,
.placement-top-start,
.placement-top-end {
  transform-origin: bottom center;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .dropdown-panel {
    max-width: calc(100vw - 24px);
    max-height: calc(100vh - 120px);
    min-width: 280px;
  }

  .dropdown-content {
    max-height: calc(100vh - 220px);
    min-height: 200px;
  }

  .action-btn {
    padding: 10px 16px;
    font-size: 14px;
  }
}

/* 确保面板内容不会溢出 */
.dropdown-panel .dropdown-content {
  overflow-x: hidden;
  overflow-y: auto;
}

/* 处理极小屏幕 */
@media (max-width: 480px) {
  .dropdown-panel {
    max-width: calc(100vw - 16px);
    min-width: 260px;
  }
}

/* 确保面板在所有情况下都可见 */
.dropdown-panel {
  min-height: 100px;
  max-height: min(400px, calc(100vh - 120px));
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .dropdown-panel {
    border-width: 2px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .action-btn {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .dropdown-fade-enter-active,
  .dropdown-fade-leave-active,
  .action-btn,
  .loading-spinner {
    transition: none !important;
    animation: none !important;
  }
}

.action-btn:hover {
  border-color: #c0c4cc;
}

.confirm-btn {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.confirm-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.default-content {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 响应式 */
@media (max-width: 768px) {
  .dropdown-panel {
    max-height: 60vh;
  }
  
  .dropdown-content {
    max-height: calc(60vh - 60px);
  }
}
</style>
