package com.xhcai.modules.rag.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.rag.dto.FileStorageConfigCreateDTO;
import com.xhcai.modules.rag.dto.FileStorageConfigQueryDTO;
import com.xhcai.modules.rag.dto.FileStorageConfigUpdateDTO;
import com.xhcai.modules.rag.entity.FileStorageConfig;
import com.xhcai.modules.rag.mapper.FileStorageConfigMapper;
import com.xhcai.modules.rag.service.IFileStorageConfigService;
import com.xhcai.modules.rag.vo.FileStorageConfigVO;
import com.xhcai.modules.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件存储配置服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
public class FileStorageConfigServiceImpl extends ServiceImpl<FileStorageConfigMapper, FileStorageConfig> 
        implements IFileStorageConfigService {

    @Autowired
    private FileStorageConfigMapper fileStorageConfigMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Override
    public PageResult<FileStorageConfigVO> selectFileStorageConfigPage(FileStorageConfigQueryDTO queryDTO) {
        Page<FileStorageConfig> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        LambdaQueryWrapper<FileStorageConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(queryDTO.getName()), FileStorageConfig::getName, queryDTO.getName())
               .eq(StringUtils.hasText(queryDTO.getStorageType()), FileStorageConfig::getStorageType, queryDTO.getStorageType())
               .eq(StringUtils.hasText(queryDTO.getStatus()), FileStorageConfig::getStatus, queryDTO.getStatus())
               .eq(StringUtils.hasText(queryDTO.getIsDefault()), FileStorageConfig::getIsDefault, queryDTO.getIsDefault())
               .orderByDesc(FileStorageConfig::getIsDefault)
               .orderByDesc(FileStorageConfig::getCreateTime);

        Page<FileStorageConfig> result = page(page, wrapper);
        
        List<FileStorageConfigVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.of(voList, result.getTotal(), queryDTO.getPageNum(), queryDTO.getPageSize());
    }

    @Override
    public List<FileStorageConfigVO> selectFileStorageConfigList(FileStorageConfigQueryDTO queryDTO) {
        List<FileStorageConfig> configs = fileStorageConfigMapper.selectFileStorageConfigList(
                queryDTO.getName(),
                queryDTO.getStorageType(),
                queryDTO.getStatus(),
                queryDTO.getIsDefault()
        );

        return configs.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public FileStorageConfigVO selectFileStorageConfigById(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        FileStorageConfig config = getById(id);
        return config != null ? convertToVO(config) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createFileStorageConfig(FileStorageConfigCreateDTO createDTO) {
        // 参数校验
        validateFileStorageConfig(createDTO, true);

        // 检查名称是否已存在
        if (existsConfigName(createDTO.getName(), null)) {
            throw new BusinessException("存储配置名称已存在");
        }

        FileStorageConfig config = new FileStorageConfig();
        BeanUtils.copyProperties(createDTO, config);

        // 如果设置为默认，先取消其他默认配置
        if ("Y".equals(createDTO.getIsDefault())) {
            fileStorageConfigMapper.clearAllDefaultConfig();
        }

        boolean result = save(config);
        if (result) {
            log.info("创建文件存储配置成功: {}", createDTO.getName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFileStorageConfig(String id, FileStorageConfigUpdateDTO updateDTO) {
        // 参数校验
        validateFileStorageConfig(updateDTO, false);

        FileStorageConfig existingConfig = getById(id);
        if (existingConfig == null) {
            throw new BusinessException("文件存储配置不存在");
        }

        // 检查名称是否已存在（排除自己）
        if (existsConfigName(updateDTO.getName(), id)) {
            throw new BusinessException("存储配置名称已存在");
        }

        BeanUtils.copyProperties(updateDTO, existingConfig);

        // 如果设置为默认，先取消其他默认配置
        if ("Y".equals(updateDTO.getIsDefault())) {
            fileStorageConfigMapper.clearAllDefaultConfig();
        }

        boolean result = updateById(existingConfig);
        if (result) {
            log.info("更新文件存储配置成功: {}", updateDTO.getName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFileStorageConfigs(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("删除的配置ID不能为空");
        }

        // 检查是否有默认配置
        for (String id : ids) {
            FileStorageConfig config = getById(id);
            if (config != null && "Y".equals(config.getIsDefault())) {
                throw new BusinessException("不能删除默认存储配置，请先设置其他配置为默认");
            }
        }

        boolean result = removeByIds(ids);
        if (result) {
            log.info("删除文件存储配置成功，数量: {}", ids.size());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, String status) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("更新的配置ID不能为空");
        }

        int result = fileStorageConfigMapper.batchUpdateStatus(ids, status);
        if (result > 0) {
            log.info("批量更新文件存储配置状态成功，数量: {}", result);
        }
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultConfig(String id) {
        FileStorageConfig config = getById(id);
        if (config == null) {
            throw new BusinessException("文件存储配置不存在");
        }

        // 先取消所有默认配置
        fileStorageConfigMapper.clearAllDefaultConfig();
        
        // 设置新的默认配置
        int result = fileStorageConfigMapper.setDefaultConfig(id);
        if (result > 0) {
            log.info("设置默认文件存储配置成功: {}", config.getName());
        }
        return result > 0;
    }

    @Override
    public FileStorageConfigVO getDefaultConfig() {
        FileStorageConfig config = fileStorageConfigMapper.selectDefaultConfig();
        return config != null ? convertToVO(config) : null;
    }

    @Override
    public List<FileStorageConfigVO> getConfigsByType(String storageType) {
        List<FileStorageConfig> configs = fileStorageConfigMapper.selectByStorageType(storageType);
        return configs.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean testConnection(String id) {
        FileStorageConfig config = getById(id);
        if (config == null) {
            throw new BusinessException("文件存储配置不存在");
        }

        FileStorageConfigCreateDTO testConfig = new FileStorageConfigCreateDTO();
        BeanUtils.copyProperties(config, testConfig);
        return testConnection(testConfig);
    }

    @Override
    public boolean testConnection(FileStorageConfigCreateDTO config) {
        try {
            // 根据存储类型进行连接测试
            switch (config.getStorageType()) {
                case "minio":
                    return testMinioConnection(config);
                case "ftp":
                    return testFtpConnection(config);
                case "obs":
                    return testObsConnection(config);
                case "local":
                    return testLocalConnection(config);
                default:
                    throw new BusinessException("不支持的存储类型: " + config.getStorageType());
            }
        } catch (Exception e) {
            log.error("测试存储连接失败", e);
            return false;
        }
    }

    /**
     * 转换为VO
     */
    private FileStorageConfigVO convertToVO(FileStorageConfig config) {
        FileStorageConfigVO vo = new FileStorageConfigVO();
        BeanUtils.copyProperties(config, vo);
        
        // 获取存储类型名称
        String typeName = dictDataService.getDictLabel("file_storage_type", config.getStorageType());
        vo.setStorageTypeName(typeName);
        
        // 获取图标和颜色
        // 这里可以根据字典数据获取图标和颜色信息
        
        return vo;
    }

    /**
     * 校验文件存储配置
     */
    private void validateFileStorageConfig(Object configDTO, boolean isCreate) {
        // 基本校验已通过注解完成，这里可以添加业务校验
    }

    /**
     * 检查配置名称是否存在
     */
    private boolean existsConfigName(String name, String excludeId) {
        LambdaQueryWrapper<FileStorageConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileStorageConfig::getName, name);
        if (StringUtils.hasText(excludeId)) {
            wrapper.ne(FileStorageConfig::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    /**
     * 测试MinIO连接
     */
    private boolean testMinioConnection(FileStorageConfigCreateDTO config) {
        // TODO: 实现MinIO连接测试
        log.info("测试MinIO连接: {}:{}", config.getHost(), config.getPort());
        return true;
    }

    /**
     * 测试FTP连接
     */
    private boolean testFtpConnection(FileStorageConfigCreateDTO config) {
        // TODO: 实现FTP连接测试
        log.info("测试FTP连接: {}:{}", config.getHost(), config.getPort());
        return true;
    }

    /**
     * 测试OBS连接
     */
    private boolean testObsConnection(FileStorageConfigCreateDTO config) {
        // TODO: 实现OBS连接测试
        log.info("测试OBS连接: {}", config.getHost());
        return true;
    }

    /**
     * 测试本地存储连接
     */
    private boolean testLocalConnection(FileStorageConfigCreateDTO config) {
        // TODO: 实现本地存储连接测试
        log.info("测试本地存储连接: {}", config.getBucketName());
        return true;
    }
}
