package com.xhcai.modules.dify.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Dify 登录请求 DTO
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Schema(description = "Dify 登录请求")
public class DifyLoginRequestDTO {

    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>", required = true)
    private String email;

    /**
     * 密码
     */
    @Schema(description = "密码", example = "XH12345@", required = true)
    private String password;

    /**
     * 语言
     */
    @Schema(description = "语言", example = "zh-Hans")
    private String language = "zh-Hans";

    /**
     * 记住我
     */
    @Schema(description = "记住我", example = "true")
    private Boolean rememberMe = true;

    // Getters and Setters
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    @Override
    public String toString() {
        return "DifyLoginRequestDTO{" +
                "email='" + email + '\'' +
                ", password='***'" +
                ", language='" + language + '\'' +
                ", rememberMe=" + rememberMe +
                '}';
    }
}
