package com.xhcai.common.api.exception;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.enums.ResultCode;
import com.xhcai.common.core.exception.BaseException;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.exception.SystemException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        Result<Void> result = Result.fail(e.getCode(), e.getMessage());
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleSystemException(SystemException e, HttpServletRequest request) {
        log.error("系统异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI(), e);
        Result<Void> result = Result.fail(e.getCode(), e.getMessage());
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理基础异常
     */
    @ExceptionHandler(BaseException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleBaseException(BaseException e, HttpServletRequest request) {
        log.warn("基础异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        Result<Void> result = Result.fail(e.getCode(), e.getMessage());
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数校验异常: {}, 请求路径: {}", message, request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.PARAM_ERROR.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBindException(BindException e, HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("绑定异常: {}, 请求路径: {}", message, request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.PARAM_ERROR.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.warn("约束违反异常: {}, 请求路径: {}", message, request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.PARAM_ERROR.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        String message = "缺少必需的请求参数: " + e.getParameterName();
        log.warn("缺少请求参数异常: {}, 请求路径: {}", message, request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.PARAM_ERROR.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理方法参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String message = "参数类型不匹配: " + e.getName();
        log.warn("参数类型不匹配异常: {}, 请求路径: {}", message, request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.PARAM_ERROR.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        String message = "请求体格式错误";
        log.warn("HTTP消息不可读异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.PARAM_ERROR.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Result<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        String message = "不支持的请求方法: " + e.getMethod();
        log.warn("请求方法不支持异常: {}, 请求路径: {}", message, request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.METHOD_NOT_ALLOWED.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理媒体类型不支持异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public Result<Void> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        String message = "不支持的媒体类型: " + e.getContentType();
        log.warn("媒体类型不支持异常: {}, 请求路径: {}", message, request.getRequestURI());
        Result<Void> result = Result.fail(415, message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
    public Result<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        String message = "上传文件大小超出限制";
        log.warn("文件上传大小超限异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.PAYLOAD_TOO_LARGE.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理资源未找到异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<Void> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        String message = "请求的资源不存在";
        log.warn("资源未找到异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.NOT_FOUND.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<Void> handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
        String message = "认证失败";
        if (e instanceof BadCredentialsException) {
            message = "用户名或密码错误";
        }
        log.warn("认证异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.UNAUTHORIZED.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理访问拒绝异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<Void> handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        String message = "访问被拒绝，权限不足";
        log.warn("访问拒绝异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        Result<Void> result = Result.fail(ResultCode.FORBIDDEN.getCode(), message);
        result.setPath(request.getRequestURI());
        return result;
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI(), e);
        Result<Void> result = Result.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "系统内部错误:"+e.getMessage());
        result.setPath(request.getRequestURI());
        return result;
    }
}
