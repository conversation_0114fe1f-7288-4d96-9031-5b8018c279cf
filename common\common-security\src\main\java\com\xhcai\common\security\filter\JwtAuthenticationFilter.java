package com.xhcai.common.security.filter;

import java.io.IOException;
import java.util.Objects;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.xhcai.common.security.service.LoginUser;
import com.xhcai.common.security.service.UserCacheService;
import com.xhcai.common.security.utils.JwtUtils;

import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * JWT认证过滤器
 *
 * 从请求头中提取JWT token，验证并设置用户认证信息到SecurityContext
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String TOKEN_PREFIX = "Bearer ";

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserCacheService userCacheService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        String requestUri = request.getRequestURI();
        log.debug("JWT认证过滤器处理请求: {}", requestUri);

        try {
            // 从请求中获取JWT token
            String token = getTokenFromRequest(request);
            log.debug("从请求中提取的token: {}", token != null ? "存在" : "不存在");

            if (StringUtils.hasText(token)) {
                log.debug("开始验证JWT token");

                if (jwtUtils.validateToken(token)) {
                    log.debug("JWT token验证成功");

                    // 从token中获取用户信息
                    LoginUser loginUser = getUserFromToken(token);

                    if (loginUser != null) {
                        log.debug("成功构建LoginUser: userId={}, username={}",
                                loginUser.getUserId(), loginUser.getUsername());

                        // 创建认证对象
                        UsernamePasswordAuthenticationToken authentication
                                = new UsernamePasswordAuthenticationToken(
                                        loginUser,
                                        null,
                                        loginUser.getAuthorities()
                                );

                        // 设置请求详情
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                        // 设置到SecurityContext
                        SecurityContextHolder.getContext().setAuthentication(authentication);

                        log.debug("JWT认证成功，用户: {}, 租户: {}, 权限数量: {}",
                                loginUser.getUsername(), loginUser.getTenantId(),
                                loginUser.getPermissions().size());
                    } else {
                        log.warn("无法从JWT token构建LoginUser对象");
                    }
                } else {
                    log.warn("JWT token验证失败");
                }
            } else {
                log.debug("请求中未找到JWT token");
            }
        } catch (Exception e) {
            log.error("JWT认证过程中发生错误: {}", e.getMessage(), e);
            // 清除SecurityContext，确保认证失败时不会有残留的认证信息
            SecurityContextHolder.clearContext();
        }

        // 继续过滤链
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中提取JWT token
     *
     * @param request HTTP请求
     * @return JWT token，如果不存在返回null
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 从Authorization头获取token
        String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(TOKEN_PREFIX)) {
            return bearerToken.substring(TOKEN_PREFIX.length());
        }

        // 从请求参数获取token（用于某些特殊场景，如WebSocket连接和SSE）
        String paramToken = request.getParameter("token");
        if (StringUtils.hasText(paramToken)) {
            return paramToken;
        }

        return null;
    }

    /**
     * 从JWT token中构建LoginUser对象
     *
     * @param token JWT token
     * @return LoginUser对象
     */
    private LoginUser getUserFromToken(String token) {
        try {
            Claims claims = jwtUtils.getClaimsFromToken(token);
            if (claims == null) {
                log.warn("无法从JWT token获取claims");
                return null;
            }

            // 从claims中提取用户信息
            String userId = claims.getSubject();
            String username = claims.get("username", String.class);

            log.debug("从JWT token提取用户信息: userId={}, username={}",
                    userId, username);

            if (!StringUtils.hasText(userId) || !StringUtils.hasText(username)) {
                log.warn("JWT token中缺少必要的用户信息: userId={}, username={}", userId, username);
                return null;
            }

            // 首先尝试从缓存获取用户信息
            LoginUser cachedUser = userCacheService.getCachedUser(userId);
            if (cachedUser != null) {
                log.debug("从缓存获取用户信息成功: userId={}", userId);

                // 验证缓存中的用户信息是否与数据库一致（特别是tenantId）
                if (isUserInfoConsistent(cachedUser)) {
                    // 刷新缓存过期时间
                    userCacheService.refreshUserCache(userId);
                    return cachedUser;
                } else {
                    log.debug("缓存中的用户信息与数据库不一致，清除缓存并重新构建: userId={}", userId);
                    userCacheService.clearUserCache(userId);
                }
            }

            log.debug("缓存中未找到用户信息或信息不一致，从数据库构建新的LoginUser对象: userId={}", userId);

            // 从数据库获取最新的用户信息
            Object userEntity = getUserFromDatabase(userId);
            if (userEntity == null) {
                log.warn("数据库中未找到用户信息: userId={}", userId);
                return null;
            }

            // 创建LoginUser对象，使用数据库中的最新信息
            LoginUser loginUser = buildLoginUserFromEntity(userEntity);
            if (loginUser == null) {
                log.warn("构建LoginUser对象失败: userId={}", userId);
                return null;
            }

            // 从缓存或数据库获取用户的权限和角色信息
            loginUser.setPermissions(getUserPermissions(userId));
            loginUser.setRoles(getUserRoles(userId));

            // 缓存用户信息
            userCacheService.cacheUser(loginUser);

            log.debug("从数据库构建LoginUser成功: userId={}, username={}, tenantId={}",
                    loginUser.getUserId(), loginUser.getUsername(), loginUser.getTenantId());

            return loginUser;

        } catch (Exception e) {
            log.error("从JWT token构建LoginUser失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断是否跳过JWT认证 对于某些公开接口，可以跳过JWT认证
     *
     * @param request HTTP请求
     * @return 是否跳过认证
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();

        return path.startsWith("/api/auth/")
                || path.startsWith("/api/public/")
                || path.startsWith("/api/platform/init/")
                || path.startsWith("/actuator/")
                || path.startsWith("/v3/api-docs/")
                || path.startsWith("/swagger-ui/")
                || path.startsWith("/doc.html")
                || path.startsWith("/webjars/")
                || path.equals("/favicon.ico");
    }

    /**
     * 获取用户权限信息 优先从缓存获取，缓存不存在时从数据库获取
     *
     * @param userId 用户ID
     * @return 用户权限集合
     */
    private Set<String> getUserPermissions(String userId) {
        try {
            // 首先尝试从缓存获取
            Set<String> cachedPermissions = userCacheService.getCachedUserPermissions(userId);
            if (!cachedPermissions.isEmpty()) {
                log.debug("从缓存获取用户权限成功: userId={}, 权限数量={}", userId, cachedPermissions.size());
                return cachedPermissions;
            }

            // 从数据库获取权限信息
            try {
                Class<?> permissionServiceClass = Class.forName("com.xhcai.modules.system.service.ISysPermissionService");
                Object permissionService = getBean(permissionServiceClass);
                @SuppressWarnings("unchecked")
                Set<String> permissions = (Set<String>) permissionServiceClass
                        .getMethod("selectPermissionCodesByUserId", String.class)
                        .invoke(permissionService, userId);

                log.debug("从数据库获取用户权限成功: userId={}, 权限数量={}", userId, permissions.size());
                return permissions != null ? permissions : Set.of();

            } catch (Exception e) {
                log.debug("从数据库获取用户权限失败: userId={}, error={}", userId, e.getMessage());
                return Set.of();
            }

        } catch (Exception e) {
            log.error("获取用户权限失败: userId={}, error={}", userId, e.getMessage(), e);
            return Set.of();
        }
    }

    /**
     * 获取用户角色信息 优先从缓存获取，缓存不存在时从数据库获取
     *
     * @param userId 用户ID
     * @return 用户角色集合
     */
    private Set<String> getUserRoles(String userId) {
        try {
            // 首先尝试从缓存获取
            Set<String> cachedRoles = userCacheService.getCachedUserRoles(userId);
            if (!cachedRoles.isEmpty()) {
                log.debug("从缓存获取用户角色成功: userId={}, 角色数量={}", userId, cachedRoles.size());
                return cachedRoles;
            }

            // 从数据库获取角色信息
            try {
                Class<?> roleServiceClass = Class.forName("com.xhcai.modules.system.service.ISysRoleService");
                Object roleService = getBean(roleServiceClass);
                @SuppressWarnings("unchecked")
                Set<String> roles = (Set<String>) roleServiceClass
                        .getMethod("selectRoleCodesByUserId", String.class)
                        .invoke(roleService, userId);

                log.debug("从数据库获取用户角色成功: userId={}, 角色数量={}", userId, roles.size());
                return roles != null ? roles : Set.of();

            } catch (Exception e) {
                log.debug("从数据库获取用户角色失败: userId={}, error={}", userId, e.getMessage());
                return Set.of();
            }

        } catch (Exception e) {
            log.error("获取用户角色失败: userId={}, error={}", userId, e.getMessage(), e);
            return Set.of();
        }
    }

    /**
     * 验证缓存中的用户信息是否与数据库一致
     *
     * @param cachedUser 缓存中的用户信息
     * @return 是否一致
     */
    private boolean isUserInfoConsistent(LoginUser cachedUser) {
        try {
            Object userEntity = getUserFromDatabase(cachedUser.getUserId());
            if (userEntity == null) {
                log.debug("数据库中未找到用户，缓存信息不一致: userId={}", cachedUser.getUserId());
                return false;
            }

            // 获取数据库中的最新tenantId
            String dbTenantId = (String) userEntity.getClass().getMethod("getTenantId").invoke(userEntity);
            String cachedTenantId = cachedUser.getTenantId();

            // 比较tenantId是否一致
            boolean tenantIdConsistent = Objects.equals(dbTenantId, cachedTenantId);
            if (!tenantIdConsistent) {
                log.debug("用户tenantId不一致 - 缓存: {}, 数据库: {}, userId={}",
                        cachedTenantId, dbTenantId, cachedUser.getUserId());
            }

            return tenantIdConsistent;

        } catch (Exception e) {
            log.debug("验证用户信息一致性失败: userId={}, error={}", cachedUser.getUserId(), e.getMessage());
            return false;
        }
    }

    /**
     * 从数据库获取用户信息
     *
     * @param userId 用户ID
     * @return 用户实体对象
     */
    private Object getUserFromDatabase(String userId) {
        try {
            // 由于用户表启用了多租户隔离，在JWT认证阶段我们还没有租户上下文
            // 直接使用Mapper的特殊方法查询，绕过租户隔离
            return getUserFromMapper(userId);

        } catch (Exception e) {
            log.debug("从数据库获取用户信息失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * 通过Mapper直接查询用户信息（绕过租户隔离）
     *
     * @param userId 用户ID
     * @return 用户实体对象
     */
    private Object getUserFromMapper(String userId) {
        try {
            // 添加一个专门的方法来查询用户信息，绕过租户隔离
            Class<?> userMapperClass = Class.forName("com.xhcai.modules.system.mapper.SysUserMapper");
            Object userMapper = getBean(userMapperClass);

            // 尝试使用自定义的查询方法
            try {
                return userMapperClass
                        .getMethod("selectByIdIgnoreTenant", String.class)
                        .invoke(userMapper, userId);
            } catch (NoSuchMethodException e) {
                // 如果没有自定义方法，使用selectById
                log.debug("未找到selectByIdIgnoreTenant方法，使用selectById: userId={}", userId);
                return userMapperClass
                        .getMethod("selectById", java.io.Serializable.class)
                        .invoke(userMapper, userId);
            }

        } catch (Exception e) {
            log.debug("通过Mapper查询用户信息失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * 从用户实体构建LoginUser对象
     *
     * @param userEntity 用户实体对象
     * @return LoginUser对象
     */
    private LoginUser buildLoginUserFromEntity(Object userEntity) {
        try {
            LoginUser loginUser = new LoginUser();

            // 使用反射获取用户实体的各个字段
            String userId = (String) userEntity.getClass().getMethod("getId").invoke(userEntity);
            String username = (String) userEntity.getClass().getMethod("getUsername").invoke(userEntity);
            String tenantId = (String) userEntity.getClass().getMethod("getTenantId").invoke(userEntity);
            String deptId = (String) userEntity.getClass().getMethod("getDeptId").invoke(userEntity);
            String nickname = (String) userEntity.getClass().getMethod("getNickname").invoke(userEntity);
            String email = (String) userEntity.getClass().getMethod("getEmail").invoke(userEntity);
            String phone = (String) userEntity.getClass().getMethod("getPhone").invoke(userEntity);
            String avatar = (String) userEntity.getClass().getMethod("getAvatar").invoke(userEntity);
            String status = (String) userEntity.getClass().getMethod("getStatus").invoke(userEntity);
            String userType = (String) userEntity.getClass().getMethod("getUserType").invoke(userEntity);

            // 设置LoginUser的各个字段
            loginUser.setUserId(userId);
            loginUser.setUsername(username);
            loginUser.setTenantId(tenantId);
            loginUser.setDeptId(deptId);
            loginUser.setNickname(nickname);
            loginUser.setEmail(email);
            loginUser.setPhone(phone);
            loginUser.setAvatar(avatar);
            loginUser.setStatus(status != null ? status : "0");
            loginUser.setUserType(userType != null ? userType : "normal");

            log.debug("从用户实体构建LoginUser成功: userId={}, username={}, tenantId={}",
                    userId, username, tenantId);

            return loginUser;

        } catch (Exception e) {
            log.error("从用户实体构建LoginUser失败: error={}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取用户类型信息
     *
     * @param userId 用户ID
     * @return 用户类型
     */
    private String getUserType(String userId) {
        try {
            Object userEntity = getUserFromDatabase(userId);
            if (userEntity != null) {
                String userType = (String) userEntity.getClass().getMethod("getUserType").invoke(userEntity);
                log.debug("从数据库获取用户类型成功: userId={}, userType={}", userId, userType);
                return userType != null ? userType : "normal"; // 默认为普通用户
            }

            return "normal"; // 默认为普通用户

        } catch (Exception e) {
            log.error("获取用户类型失败: userId={}, error={}", userId, e.getMessage(), e);
            return "normal"; // 默认为普通用户
        }
    }

    /**
     * 获取Spring Bean
     */
    private Object getBean(Class<?> clazz) {
        try {
            Class<?> applicationContextHolderClass = Class.forName("com.xhcai.common.core.utils.ApplicationContextHolder");
            return applicationContextHolderClass.getMethod("getBean", Class.class).invoke(null, clazz);
        } catch (Exception e) {
            throw new RuntimeException("获取Bean失败: " + clazz.getName(), e);
        }
    }
}
