<template>
  <div class="config-modal-overlay" v-show="visible" @click="handleOverlayClick">
    <div class="config-modal" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <div class="header-left">
          <h2 class="modal-title">
            <i class="fas fa-cog"></i>
            知识库配置
          </h2>
          <span class="knowledge-base-name">{{ knowledgeBase?.name || '知识库' }}</span>
        </div>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 标签页 -->
      <div class="tab-container">
        <div class="tab-nav">
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'basic' }"
            @click="activeTab = 'basic'"
          >
            <i class="fas fa-info-circle"></i>
            基本配置
          </button>
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'vector' }"
            @click="activeTab = 'vector'"
          >
            <i class="fas fa-vector-square"></i>
            向量模型配置
          </button>
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'task' }"
            @click="activeTab = 'task'"
          >
            <i class="fas fa-tasks"></i>
            分段配置
          </button>
        </div>

        <!-- 配置内容 -->
        <div class="config-content">
          <!-- 基本配置 -->
          <div v-if="activeTab === 'basic'" class="config-panel">
            <div class="config-section">
              <h3 class="section-title">知识库信息</h3>
              <div class="form-group">
                <label class="form-label">知识库名称</label>
                <input 
                  type="text" 
                  class="form-input" 
                  v-model="basicConfig.name"
                  placeholder="请输入知识库名称"
                >
              </div>
              <div class="form-group">
                <label class="form-label">描述</label>
                <textarea 
                  class="form-textarea" 
                  v-model="basicConfig.description"
                  placeholder="请输入知识库描述"
                  rows="3"
                ></textarea>
              </div>
              <div class="form-group">
                <label class="form-label">标签</label>
                <div class="tag-input-container">
                  <div class="tags">
                    <span 
                      v-for="(tag, index) in basicConfig.tags" 
                      :key="index" 
                      class="tag"
                    >
                      {{ tag }}
                      <button class="tag-remove" @click="removeTag(index)">
                        <i class="fas fa-times"></i>
                      </button>
                    </span>
                  </div>
                  <input 
                    type="text" 
                    class="tag-input" 
                    v-model="newTag"
                    @keyup.enter="addTag"
                    placeholder="输入标签后按回车添加"
                  >
                </div>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">访问控制</h3>
              <div class="form-group">
                <label class="form-label">访问权限</label>
                <select class="form-select" v-model="basicConfig.accessLevel">
                  <option value="private">私有</option>
                  <option value="team">团队</option>
                  <option value="public">公开</option>
                </select>
              </div>
              <div class="form-group" v-if="basicConfig.accessLevel === 'team'">
                <label class="form-label">授权团队</label>
                <div class="team-selector">
                  <div class="selected-teams">
                    <span 
                      v-for="team in basicConfig.authorizedTeams" 
                      :key="team.id" 
                      class="team-tag"
                    >
                      {{ team.name }}
                      <button class="team-remove" @click="removeTeam(team.id)">
                        <i class="fas fa-times"></i>
                      </button>
                    </span>
                  </div>
                  <button class="btn btn-outline btn-sm" @click="showTeamSelector = true">
                    <i class="fas fa-plus"></i>
                    添加团队
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 向量模型配置 -->
          <div v-if="activeTab === 'vector'" class="config-panel">
            <div class="config-section">
              <h3 class="section-title">嵌入模型</h3>
              <div class="form-group">
                <label class="form-label">模型提供商</label>
                <select class="form-select" v-model="vectorConfig.provider">
                  <option value="openai">OpenAI</option>
                  <option value="azure">Azure OpenAI</option>
                  <option value="huggingface">Hugging Face</option>
                  <option value="local">本地模型</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">模型名称</label>
                <select class="form-select" v-model="vectorConfig.modelName">
                  <option v-for="model in availableModels" :key="model.value" :value="model.value">
                    {{ model.label }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">向量维度</label>
                <input 
                  type="number" 
                  class="form-input" 
                  v-model="vectorConfig.dimensions"
                  readonly
                >
                <small class="form-hint">根据选择的模型自动设置</small>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">检索配置</h3>
              <div class="form-group">
                <label class="form-label">相似度阈值</label>
                <div class="slider-container">
                  <input 
                    type="range" 
                    class="form-slider" 
                    v-model="vectorConfig.similarityThreshold"
                    min="0" 
                    max="1" 
                    step="0.01"
                  >
                  <span class="slider-value">{{ vectorConfig.similarityThreshold }}</span>
                </div>
                <small class="form-hint">设置检索时的最小相似度要求</small>
              </div>
              <div class="form-group">
                <label class="form-label">最大检索数量</label>
                <input 
                  type="number" 
                  class="form-input" 
                  v-model="vectorConfig.maxResults"
                  min="1" 
                  max="100"
                >
                <small class="form-hint">单次检索返回的最大结果数量</small>
              </div>
            </div>
          </div>

          <!-- 任务配置 -->
          <div v-if="activeTab === 'task'" class="config-panel">
            <div class="config-section">
              <h3 class="section-title">文件处理</h3>
              <div class="form-group">
                <label class="form-label">分段策略</label>
                <select class="form-select" v-model="taskConfig.segmentStrategy">
                  <option value="fixed">固定长度</option>
                  <option value="semantic">语义分段</option>
                  <option value="paragraph">段落分段</option>
                </select>
              </div>
              <div class="form-group" v-if="taskConfig.segmentStrategy === 'fixed'">
                <label class="form-label">分段长度</label>
                <input 
                  type="number" 
                  class="form-input" 
                  v-model="taskConfig.segmentLength"
                  min="100" 
                  max="2000"
                >
                <small class="form-hint">每个分段的字符数量</small>
              </div>
              <div class="form-group" v-if="taskConfig.segmentStrategy === 'fixed'">
                <label class="form-label">重叠长度</label>
                <input 
                  type="number" 
                  class="form-input" 
                  v-model="taskConfig.overlapLength"
                  min="0" 
                  max="500"
                >
                <small class="form-hint">相邻分段之间的重叠字符数</small>
              </div>
            </div>

            <div class="config-section">
              <h3 class="section-title">任务调度</h3>
              <div class="form-group">
                <label class="form-label">并发处理数</label>
                <input 
                  type="number" 
                  class="form-input" 
                  v-model="taskConfig.concurrency"
                  min="1" 
                  max="10"
                >
                <small class="form-hint">同时处理的文件数量</small>
              </div>
              <div class="form-group">
                <label class="form-label">自动重试次数</label>
                <input 
                  type="number" 
                  class="form-input" 
                  v-model="taskConfig.retryCount"
                  min="0" 
                  max="5"
                >
                <small class="form-hint">任务失败时的自动重试次数</small>
              </div>
              <div class="form-group">
                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input 
                      type="checkbox" 
                      v-model="taskConfig.autoVectorize"
                    >
                    <span class="checkbox-text">分段完成后自动向量化</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button class="btn btn-outline" @click="resetConfig">
            重置
          </button>
          <button class="btn btn-primary" @click="saveConfig" :disabled="saving">
            <i class="fas fa-spinner fa-spin" v-if="saving"></i>
            <i class="fas fa-save" v-else></i>
            {{ saving ? '保存中...' : '保存配置' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { RagAPI } from '@/api/rag'

// Props
interface Props {
  visible: boolean
  datasetId: string
  knowledgeBase?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const activeTab = ref<'basic' | 'vector' | 'task'>('basic')
const saving = ref(false)
const showTeamSelector = ref(false)
const newTag = ref('')

// 配置数据
const basicConfig = ref({
  name: '',
  description: '',
  tags: [] as string[],
  accessLevel: 'private',
  authorizedTeams: [] as any[]
})

const vectorConfig = ref({
  provider: 'openai',
  modelName: 'text-embedding-ada-002',
  dimensions: 1536,
  similarityThreshold: 0.7,
  maxResults: 10
})

const taskConfig = ref({
  segmentStrategy: 'fixed',
  segmentLength: 500,
  overlapLength: 50,
  concurrency: 3,
  retryCount: 2,
  autoVectorize: true
})

// 计算属性
const availableModels = computed(() => {
  const modelMap: Record<string, any[]> = {
    openai: [
      { label: 'text-embedding-ada-002', value: 'text-embedding-ada-002' },
      { label: 'text-embedding-3-small', value: 'text-embedding-3-small' },
      { label: 'text-embedding-3-large', value: 'text-embedding-3-large' }
    ],
    azure: [
      { label: 'text-embedding-ada-002', value: 'text-embedding-ada-002' }
    ],
    huggingface: [
      { label: 'sentence-transformers/all-MiniLM-L6-v2', value: 'sentence-transformers/all-MiniLM-L6-v2' },
      { label: 'sentence-transformers/all-mpnet-base-v2', value: 'sentence-transformers/all-mpnet-base-v2' }
    ],
    local: [
      { label: '本地嵌入模型', value: 'local-embedding' }
    ]
  }
  return modelMap[vectorConfig.value.provider] || []
})

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const addTag = () => {
  if (newTag.value.trim() && !basicConfig.value.tags.includes(newTag.value.trim())) {
    basicConfig.value.tags.push(newTag.value.trim())
    newTag.value = ''
  }
}

const removeTag = (index: number) => {
  basicConfig.value.tags.splice(index, 1)
}

const removeTeam = (teamId: string) => {
  basicConfig.value.authorizedTeams = basicConfig.value.authorizedTeams.filter(team => team.id !== teamId)
}

const resetConfig = () => {
  loadConfig()
}

const saveConfig = async () => {
  saving.value = true
  try {
    const configData = {
      basic: basicConfig.value,
      vector: vectorConfig.value,
      task: taskConfig.value
    }

    const response = await RagAPI.updateDatasetConfig(props.datasetId, configData)

    if (response.success) {
      alert('配置保存成功！')
      emit('close')
    } else {
      alert('配置保存失败：' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    alert('配置保存失败，请稍后再试')
  } finally {
    saving.value = false
  }
}

const loadConfig = async () => {
  try {
    // 从props中加载基本信息
    if (props.knowledgeBase) {
      basicConfig.value.name = props.knowledgeBase.name || ''
      basicConfig.value.description = props.knowledgeBase.description || ''
      basicConfig.value.tags = props.knowledgeBase.tags || []
    }

    // 从API加载配置
    const response = await RagAPI.getDatasetConfig(props.datasetId)

    if (response.success && response.data) {
      const config = response.data

      // 更新向量配置
      if (config.vector) {
        vectorConfig.value = {
          ...vectorConfig.value,
          ...config.vector
        }
      }

      // 更新任务配置
      if (config.task) {
        taskConfig.value = {
          ...taskConfig.value,
          ...config.task
        }
      }

      // 更新基本配置
      if (config.basic) {
        basicConfig.value = {
          ...basicConfig.value,
          ...config.basic
        }
      }
    }
  } catch (error) {
    console.error('加载配置失败:', error)

    // 如果API调用失败，使用默认值
    vectorConfig.value = {
      provider: 'openai',
      modelName: 'text-embedding-ada-002',
      dimensions: 1536,
      similarityThreshold: 0.7,
      maxResults: 10
    }

    taskConfig.value = {
      segmentStrategy: 'fixed',
      segmentLength: 500,
      overlapLength: 50,
      concurrency: 3,
      retryCount: 2,
      autoVectorize: true
    }
  }
}

// 监听模型变化，自动更新维度
watch(() => vectorConfig.value.modelName, (newModel) => {
  const dimensionMap: Record<string, number> = {
    'text-embedding-ada-002': 1536,
    'text-embedding-3-small': 1536,
    'text-embedding-3-large': 3072,
    'sentence-transformers/all-MiniLM-L6-v2': 384,
    'sentence-transformers/all-mpnet-base-v2': 768,
    'local-embedding': 768
  }
  vectorConfig.value.dimensions = dimensionMap[newModel] || 1536
})

// 生命周期
onMounted(async () => {
  await loadConfig()
})

// 监听visible变化
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    await loadConfig()
  }
})
</script>

<style scoped>
.config-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.config-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  height: 85%;
  max-width: 1000px;
  max-height: 700px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.knowledge-base-name {
  font-size: 14px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 12px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.tab-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-nav {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  background: white;
  padding: 0 24px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  color: #374151;
  background: #f8fafc;
}

.tab-btn.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

.config-content {
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
}

.config-panel {
  padding: 24px;
}

.config-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-hint {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.tag-input-container {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px;
  min-height: 40px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #eff6ff;
  color: #3b82f6;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  border: 1px solid #dbeafe;
}

.tag-remove {
  border: none;
  background: none;
  color: #3b82f6;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.tag-remove:hover {
  background: #3b82f6;
  color: white;
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  font-size: 14px;
}

.team-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-teams {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.team-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f0fdf4;
  color: #16a34a;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  border: 1px solid #bbf7d0;
}

.team-remove {
  border: none;
  background: none;
  color: #16a34a;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.team-remove:hover {
  background: #16a34a;
  color: white;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e2e8f0;
  outline: none;
  -webkit-appearance: none;
}

.form-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
}

.form-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
}

.slider-value {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  min-width: 40px;
  text-align: center;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.checkbox-text {
  color: #374151;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: white;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-outline {
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.btn-secondary {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #f1f5f9;
  color: #334155;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}
</style>
