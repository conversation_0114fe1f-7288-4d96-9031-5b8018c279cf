<template>
  <div class="node-library">
    <div class="search-box">
      <i class="fas fa-search"></i>
      <input 
        v-model="searchQuery" 
        type="text" 
        placeholder="搜索节点..."
        class="search-input"
      />
    </div>

    <div class="categories">
      <div 
        v-for="category in filteredCategories" 
        :key="category.name"
        class="category"
      >
        <div 
          class="category-header"
          @click="toggleCategory(category.name)"
        >
          <div class="category-info">
            <i :class="category.icon || 'fa-solid fa-folder'"></i>
            <span class="category-name">{{ category.name }}</span>
            <span class="node-count">({{ category.nodes.length }})</span>
          </div>
          <i 
            class="fas fa-chevron-down toggle-icon"
            :class="{ expanded: expandedCategories.includes(category.name) }"
          ></i>
        </div>

        <div 
          v-show="expandedCategories.includes(category.name)"
          class="category-content"
        >
          <div class="category-description" v-if="category.description">
            {{ category.description }}
          </div>

          <div class="nodes">
            <div
              v-for="node in category.nodes"
              :key="node.type"
              class="node-item"
              :draggable="true"
              @dragstart="onDragStart($event, node)"
              @click="addNodeToCanvas(node)"
              :title="node.description"
            >
              <div
                class="node-icon"
                :style="{
                  background: node.gradient || '#f3f4f6',
                  color: node.iconColor || '#6b7280'
                }"
              >
                <i :class="node.icon || 'fa-solid fa-cube'"></i>
              </div>
              <div class="node-info">
                <div class="node-header">
                  <div
                    class="node-label"
                    :style="{ color: node.labelColor || '#1f2937' }"
                  >
                    {{ node.label }}
                  </div>
                  <div class="node-badges">
                    <span
                      v-if="node.status"
                      class="status-badge"
                      :class="`status-${node.status}`"
                    >
                      {{ getStatusText(node.status) }}
                    </span>
                    <span v-if="node.version" class="version-badge">
                      v{{ node.version }}
                    </span>
                  </div>
                </div>
                <div class="node-description">{{ node.description }}</div>
                <div v-if="node.tags && node.tags.length > 0" class="node-tags">
                  <span
                    v-for="tag in node.tags"
                    :key="tag"
                    class="node-tag"
                    :style="{
                      backgroundColor: node.gradient ? 'rgba(255, 255, 255, 0.9)' : '#f3f4f6',
                      color: node.iconColor || '#6b7280',
                      borderColor: node.iconColor ? `${node.iconColor}20` : '#e5e7eb'
                    }"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="filteredCategories.length === 0" class="no-results">
      <i class="fas fa-search"></i>
      <p>未找到匹配的节点</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { NODE_LIBRARY_CONFIG, type NodeCategory } from '../config/nodeLibrary'
import { getAllNodeConfigs, watchNodeConfigChanges } from '../composables/useNodeConfig'

// Props
interface Props {
  categories?: NodeCategory[]
}

const props = withDefaults(defineProps<Props>(), {
  categories: () => NODE_LIBRARY_CONFIG.categories
})

// Emits
const emit = defineEmits<{
  'node-drag-start': [nodeType: string]
  'add-node': [node: any]
}>()

// 响应式数据
const searchQuery = ref('')
const expandedCategories = ref<string[]>([])

// 获取响应式节点配置
const allNodeConfigs = getAllNodeConfigs()

// 计算属性 - 构建响应式的分类结构
const reactiveCategories = computed(() => {
  // 如果有传入的categories prop，优先使用
  if (props.categories && props.categories.length > 0) {
    return props.categories
  }

  // 否则从响应式配置中构建分类
  const categoriesMap = new Map<string, NodeCategory>()

  // 初始化原始分类结构
  NODE_LIBRARY_CONFIG.categories.forEach(category => {
    categoriesMap.set(category.name, {
      name: category.name,
      icon: category.icon,
      description: category.description,
      nodes: []
    })
  })

  // 填充响应式节点配置
  allNodeConfigs.value.forEach(nodeConfig => {
    // 找到节点所属的分类
    for (const originalCategory of NODE_LIBRARY_CONFIG.categories) {
      if (originalCategory.nodes.some(n => n.type === nodeConfig.type)) {
        const category = categoriesMap.get(originalCategory.name)
        if (category) {
          category.nodes.push(nodeConfig)
        }
        break
      }
    }
  })

  return Array.from(categoriesMap.values()).filter(category => category.nodes.length > 0)
})

const filteredCategories = computed(() => {
  const categories = reactiveCategories.value

  if (!searchQuery.value) {
    return categories
  }

  const query = searchQuery.value.toLowerCase()
  return categories
    .map((category: NodeCategory) => ({
      ...category,
      nodes: category.nodes.filter(node =>
        node.label.toLowerCase().includes(query) ||
        node.description.toLowerCase().includes(query) ||
        node.type.toLowerCase().includes(query)
      )
    }))
    .filter((category: NodeCategory) => category.nodes.length > 0)
})

// 方法
const toggleCategory = (categoryName: string) => {
  const index = expandedCategories.value.indexOf(categoryName)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(categoryName)
  }
}

const onDragStart = (event: DragEvent, node: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', JSON.stringify(node))
    event.dataTransfer.effectAllowed = 'move'
  }
  emit('node-drag-start', node.type)
}

const addNodeToCanvas = (node: any) => {
  emit('add-node', node)
}

// 辅助方法
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'stable': '稳定',
    'beta': '测试',
    'alpha': '预览',
    'deprecated': '已弃用'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  // 默认展开第一个分类
  const categories = reactiveCategories.value
  if (categories.length > 0) {
    expandedCategories.value.push(categories[0].name)
  }
})

// 监听配置变更，确保界面实时更新
watchNodeConfigChanges(() => {
  // 配置变更时的处理逻辑（如果需要的话）
  console.log('节点配置已更新，界面将自动刷新')
})
</script>

<style scoped>
.node-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-box {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.search-box i {
  position: absolute;
  left: 28px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #3b82f6;
}

.categories {
  flex: 1;
  overflow-y: auto;
}

.category {
  border-bottom: 1px solid #f3f4f6;
}

.category-header {
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s;
}

.category-header:hover {
  background: #f9fafb;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-info i {
  color: #6b7280;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.category-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.node-count {
  color: #9ca3af;
  font-size: 12px;
}

.toggle-icon {
  color: #9ca3af;
  font-size: 12px;
  transition: transform 0.2s;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.category-content {
  background: #fafbfc;
}

.category-description {
  padding: 8px 16px;
  font-size: 12px;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.nodes {
  padding: 8px 0;
}

.node-item {
  padding: 14px 16px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  gap: 14px;
  transition: all 0.2s ease;
  user-select: none;
  border-radius: 12px;
  margin: 6px 8px;
  border: 1px solid #f3f4f6;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.node-item:hover {
  background: #fafbfc;
  border-color: #d1d5db;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.node-item:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.node-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.node-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 1;
}

.node-icon i {
  font-size: 18px;
  position: relative;
  z-index: 1;
  font-weight: 500;
}

.node-info {
  flex: 1;
  min-width: 0;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
  gap: 8px;
}

.node-label {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  flex: 1;
  color: #1f2937;
}

.node-badges {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.status-badge {
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
}

.status-stable {
  background: #dcfce7;
  color: #166534;
  border-color: #bbf7d0;
}

.status-beta {
  background: #fef3c7;
  color: #92400e;
  border-color: #fde68a;
}

.status-alpha {
  background: #f3e8ff;
  color: #7c3aed;
  border-color: #e9d5ff;
}

.status-deprecated {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.version-badge {
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 12px;
  background: #f3f4f6;
  color: #6b7280;
  font-weight: 500;
  border: 1px solid #e5e7eb;
}

.node-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 400;
}

.node-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.node-tag {
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: inherit;
}

.no-results {
  padding: 40px 20px;
  text-align: center;
  color: #9ca3af;
}

.no-results i {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.no-results p {
  margin: 0;
  font-size: 14px;
}

/* 拖拽样式 */
.node-item[draggable="true"] {
  cursor: grab;
}

.node-item[draggable="true"]:active {
  cursor: grabbing;
}
</style>
