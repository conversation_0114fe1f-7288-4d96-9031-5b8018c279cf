# 移除第三方智能体账号卡片中的登录密码显示

## 修改概述

从前端页面个人信息-第三方智能体账号的账号卡片中移除了登录密码信息的显示，提升了安全性和界面简洁性。

## 修改内容

### 1. 移除密码显示区域

**文件**: `web/src/views/profile/ThirdPlatformAccounts.vue`

**修改前**:
```vue
<div class="flex items-center justify-between">
  <span class="text-sm text-gray-600">登陆账号</span>
  <span class="text-sm font-medium text-gray-800">{{ account.accountName }}</span>
</div>
<div class="flex items-center justify-between">
  <span class="text-sm text-gray-600">登陆密码</span>
  <div class="flex items-center gap-2">
    <span class="text-sm font-mono text-gray-800">{{ maskApiKey(account.apiKey, account.id) }}</span>
    <button @click="toggleApiKeyVisibility(account.id)" class="text-gray-500 hover:text-gray-700">
      <el-icon><View v-if="!visibleApiKeys.has(account.id)" /><Hide v-else /></el-icon>
    </button>
  </div>
</div>
```

**修改后**:
```vue
<div class="flex items-center justify-between">
  <span class="text-sm text-gray-600">登陆账号</span>
  <span class="text-sm font-medium text-gray-800">{{ account.accountName }}</span>
</div>
```

### 2. 清理相关函数和变量

移除了以下不再使用的代码：

#### 变量
```typescript
// 移除：显示API密钥的账号ID集合
const visibleApiKeys = ref<Set<string>>(new Set())
```

#### 函数
```typescript
// 移除：密码脱敏显示函数
const maskApiKey = (apiKey: string, accountId: string) => {
  if (!apiKey) return ''
  if (visibleApiKeys.value.has(accountId)) return apiKey
  return apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 8)
}

// 移除：切换密码可见性函数
const toggleApiKeyVisibility = (accountId: string) => {
  if (visibleApiKeys.value.has(accountId)) {
    visibleApiKeys.value.delete(accountId)
  } else {
    visibleApiKeys.value.add(accountId)
  }
}
```

### 3. 清理图标导入

**修改前**:
```typescript
import { Plus, MoreFilled, View, Hide, Connection, Platform } from '@element-plus/icons-vue'
```

**修改后**:
```typescript
import { Plus, MoreFilled, Connection, Platform } from '@element-plus/icons-vue'
```

移除了不再使用的 `View` 和 `Hide` 图标。

## 修改效果

### 界面变化
- **移除前**: 账号卡片显示登录密码（脱敏），包含显示/隐藏切换按钮
- **移除后**: 账号卡片只显示登录账号名，不再显示密码信息

### 显示内容
账号卡片现在只显示以下信息：
- 平台信息（图标、名称、状态）
- 登录账号名
- 添加时间
- 最后使用时间
- 使用统计（总调用次数、成功率、平均响应时间）

## 安全性提升

1. **减少敏感信息暴露**: 即使是脱敏的密码信息也不再显示
2. **降低社会工程学风险**: 避免用户在公共场所查看时密码信息被窥视
3. **简化界面**: 移除不必要的密码显示功能，界面更加简洁

## 功能保留

- 密码编辑功能仍然保留在编辑弹窗中
- 密码验证功能正常工作
- 账号的其他管理功能不受影响

## 注意事项

- 用户仍可以通过编辑功能修改密码
- 密码信息仍然安全存储在数据库中
- 此修改只影响显示层，不影响后端逻辑

## 兼容性

- 此修改为纯前端修改，不影响后端API
- 不影响现有的账号数据
- 向后兼容，不会破坏现有功能
