package com.xhcai.modules.dify.service;

import com.xhcai.modules.dify.dto.auth.DifyLoginResponseDTO;
import com.xhcai.modules.dify.dto.auth.DifyRefreshTokenResponseDTO;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Dify 多平台认证服务接口
 * 支持用户在多个Dify平台配置多个账号，每个平台每个用户只能有一个账号
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface IDifyMultiPlatformAuthService {

    /**
     * 获取用户在指定平台的有效访问令牌
     * 如果Redis中没有令牌或令牌无效，会自动登录获取新令牌
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 访问令牌的Mono
     */
    Mono<String> getValidAccessToken(String userId, String platformId);

    /**
     * 同步获取用户在指定平台的有效访问令牌
     * 如果Redis中没有令牌或令牌无效，会自动登录获取新令牌
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 访问令牌
     */
    String getValidAccessTokenSync(String userId, String platformId);

    /**
     * 获取当前用户在指定平台的有效访问令牌
     * 如果Redis中没有令牌或令牌无效，会自动登录获取新令牌
     *
     * @param platformId 平台ID
     * @return 访问令牌的Mono
     */
    Mono<String> getCurrentUserValidAccessToken(String platformId);

    /**
     * 同步获取当前用户在指定平台的有效访问令牌
     * 如果Redis中没有令牌或令牌无效，会自动登录获取新令牌
     *
     * @param platformId 平台ID
     * @return 访问令牌
     */
    String getCurrentUserValidAccessTokenSync(String platformId);

    /**
     * 用户在指定平台登录
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 登录响应的Mono
     */
    Mono<DifyLoginResponseDTO> login(String userId, String platformId);

    /**
     * 当前用户在指定平台登录
     *
     * @param platformId 平台ID
     * @return 登录响应的Mono
     */
    Mono<DifyLoginResponseDTO> currentUserLogin(String platformId);

    /**
     * 直接登录Dify平台（不存储token到Redis）
     * 仅调用Dify登录接口获取access_token和refresh_token
     *
     * @param platformId 平台ID
     * @return 登录响应的Mono
     */
    Mono<DifyLoginResponseDTO> directLogin(String platformId);

    /**
     * 刷新用户在指定平台的访问令牌
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 刷新令牌响应的Mono
     */
    Mono<DifyRefreshTokenResponseDTO> refreshToken(String userId, String platformId);

    /**
     * 处理401未授权错误（令牌过期）
     * 自动刷新令牌并返回新的访问令牌
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 新的访问令牌的Mono
     */
    Mono<String> handleUnauthorized(String userId, String platformId);

    /**
     * 处理当前用户的401未授权错误（令牌过期）
     * 自动刷新令牌并返回新的访问令牌
     *
     * @param platformId 平台ID
     * @return 新的访问令牌的Mono
     */
    Mono<String> handleCurrentUserUnauthorized(String platformId);

    /**
     * 处理600需要重新登录错误
     * 重新登录并返回新的访问令牌
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 新的访问令牌的Mono
     */
    Mono<String> handleRelogin(String userId, String platformId);

    /**
     * 处理当前用户的600需要重新登录错误
     * 重新登录并返回新的访问令牌
     *
     * @param platformId 平台ID
     * @return 新的访问令牌的Mono
     */
    Mono<String> handleCurrentUserRelogin(String platformId);

    /**
     * 同步处理401未授权错误（令牌过期）
     * 自动刷新令牌并返回新的访问令牌
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 新的访问令牌
     */
    String handleUnauthorizedSync(String userId, String platformId);

    /**
     * 同步处理600需要重新登录错误
     * 重新登录并返回新的访问令牌
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 新的访问令牌
     */
    String handleReloginSync(String userId, String platformId);

    /**
     * 清除用户在指定平台的令牌缓存
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     */
    void clearTokenCache(String userId, String platformId);

    /**
     * 清除当前用户在指定平台的令牌缓存
     *
     * @param platformId 平台ID
     */
    void clearCurrentUserTokenCache(String platformId);

    /**
     * 检查用户在指定平台是否有有效的账号配置
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 是否有有效账号配置
     */
    boolean hasValidAccount(String userId, String platformId);

    /**
     * 检查平台状态和配置信息
     *
     * @param platformId 平台ID
     * @return 平台状态信息
     */
    Map<String, Object> checkPlatformStatus(String platformId);

    /**
     * 检查当前用户在指定平台是否有有效的账号配置
     *
     * @param platformId 平台ID
     * @return 是否有有效账号配置
     */
    boolean currentUserHasValidAccount(String platformId);

    /**
     * 测试用户在指定平台的连接
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @return 测试结果
     */
    Mono<String> testConnection(String userId, String platformId);

    /**
     * 测试当前用户在指定平台的连接
     *
     * @param platformId 平台ID
     * @return 测试结果
     */
    Mono<String> testCurrentUserConnection(String platformId);
}
