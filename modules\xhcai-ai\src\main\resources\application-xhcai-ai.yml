# AI模块配置文件
# 包含AI功能相关的配置

# Spring AI配置
spring:
  ai:
    # OpenAI配置
    openai:
      # API密钥
      api-key: ${OPENAI_API_KEY:}
      # API基础URL
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      # 聊天配置
      chat:
        options:
          # 默认模型
          model: ${OPENAI_CHAT_MODEL:gpt-3.5-turbo}
          # 温度参数
          temperature: ${OPENAI_CHAT_TEMPERATURE:0.7}
          # 最大令牌数
          max-tokens: ${OPENAI_CHAT_MAX_TOKENS:2000}
          # Top P参数
          top-p: ${OPENAI_CHAT_TOP_P:1.0}
          # 频率惩罚
          frequency-penalty: ${OPENAI_CHAT_FREQUENCY_PENALTY:0.0}
          # 存在惩罚
          presence-penalty: ${OPENAI_CHAT_PRESENCE_PENALTY:0.0}
      # 嵌入配置
      embedding:
        options:
          # 嵌入模型
          model: ${OPENAI_EMBEDDING_MODEL:text-embedding-ada-002}
          # 编码格式
          encoding-format: ${OPENAI_EMBEDDING_ENCODING:float}
      # 图像配置
      image:
        options:
          # 图像模型
          model: ${OPENAI_IMAGE_MODEL:dall-e-3}
          # 图像质量
          quality: ${OPENAI_IMAGE_QUALITY:standard}
          # 图像大小
          size: ${OPENAI_IMAGE_SIZE:1024x1024}
          # 图像风格
          style: ${OPENAI_IMAGE_STYLE:vivid}

# AI模块自定义配置
ai:
  # 聊天配置
  chat:
    # 默认配置
    default:
      # 系统提示词
      system-prompt: ${AI_CHAT_SYSTEM_PROMPT:你是一个有用的AI助手，请尽力帮助用户解决问题。}
      # 最大历史消息数
      max-history: ${AI_CHAT_MAX_HISTORY:20}
      # 会话超时时间（分钟）
      session-timeout: ${AI_CHAT_SESSION_TIMEOUT:30}
    # 限制配置
    limits:
      # 每用户每天最大请求数
      max-requests-per-user-per-day: ${AI_CHAT_MAX_REQUESTS_PER_USER_PER_DAY:100}
      # 每用户每小时最大请求数
      max-requests-per-user-per-hour: ${AI_CHAT_MAX_REQUESTS_PER_USER_PER_HOUR:20}
      # 最大消息长度
      max-message-length: ${AI_CHAT_MAX_MESSAGE_LENGTH:4000}

  # 文档处理配置
  document:
    # 支持的文件类型
    supported-types: ${AI_DOCUMENT_SUPPORTED_TYPES:pdf,doc,docx,txt,md}
    # 最大文件大小（MB）
    max-file-size: ${AI_DOCUMENT_MAX_FILE_SIZE:10}
    # 文档分块配置
    chunking:
      # 分块大小
      chunk-size: ${AI_DOCUMENT_CHUNK_SIZE:1000}
      # 分块重叠
      chunk-overlap: ${AI_DOCUMENT_CHUNK_OVERLAP:200}
    # 向量化配置
    embedding:
      # 向量维度
      dimension: ${AI_DOCUMENT_EMBEDDING_DIMENSION:1536}
      # 批处理大小
      batch-size: ${AI_DOCUMENT_EMBEDDING_BATCH_SIZE:100}

  # 模型管理配置
  model:
    # 模型缓存配置
    cache:
      # 是否启用缓存
      enabled: ${AI_MODEL_CACHE_ENABLED:true}
      # 缓存过期时间（小时）
      ttl: ${AI_MODEL_CACHE_TTL:24}
      # 最大缓存数量
      max-size: ${AI_MODEL_CACHE_MAX_SIZE:100}
    # 模型监控配置
    monitoring:
      # 是否启用监控
      enabled: ${AI_MODEL_MONITORING_ENABLED:true}
      # 监控间隔（秒）
      interval: ${AI_MODEL_MONITORING_INTERVAL:60}
      # 性能阈值
      performance-threshold: ${AI_MODEL_PERFORMANCE_THRESHOLD:5000}

  # 安全配置
  security:
    # 内容过滤
    content-filter:
      # 是否启用内容过滤
      enabled: ${AI_CONTENT_FILTER_ENABLED:true}
      # 过滤级别（low, medium, high）
      level: ${AI_CONTENT_FILTER_LEVEL:medium}
      # 自定义过滤词
      custom-words: ${AI_CONTENT_FILTER_CUSTOM_WORDS:}
    # 访问控制
    access-control:
      # 是否启用IP限制
      ip-restriction-enabled: ${AI_IP_RESTRICTION_ENABLED:false}
      # 允许的IP列表
      allowed-ips: ${AI_ALLOWED_IPS:}
      # 是否启用用户权限检查
      user-permission-enabled: ${AI_USER_PERMISSION_ENABLED:true}

  # 缓存配置
  cache:
    # 聊天记录缓存
    chat-history:
      # 是否启用缓存
      enabled: ${AI_CACHE_CHAT_HISTORY_ENABLED:true}
      # 缓存过期时间（分钟）
      ttl: ${AI_CACHE_CHAT_HISTORY_TTL:60}
      # 最大缓存数量
      max-size: ${AI_CACHE_CHAT_HISTORY_MAX_SIZE:1000}
    # 模型响应缓存
    model-response:
      # 是否启用缓存
      enabled: ${AI_CACHE_MODEL_RESPONSE_ENABLED:false}
      # 缓存过期时间（分钟）
      ttl: ${AI_CACHE_MODEL_RESPONSE_TTL:30}
      # 最大缓存数量
      max-size: ${AI_CACHE_MODEL_RESPONSE_MAX_SIZE:500}

  # 监控配置
  monitoring:
    # 指标收集
    metrics:
      # 是否启用指标收集
      enabled: ${AI_METRICS_ENABLED:true}
      # 指标收集间隔（秒）
      interval: ${AI_METRICS_INTERVAL:30}
      # 指标保留时间（天）
      retention-days: ${AI_METRICS_RETENTION_DAYS:7}
    # 健康检查
    health:
      # 是否启用健康检查
      enabled: ${AI_HEALTH_CHECK_ENABLED:true}
      # 检查间隔（秒）
      interval: ${AI_HEALTH_CHECK_INTERVAL:60}
      # 检查超时时间（秒）
      timeout: ${AI_HEALTH_CHECK_TIMEOUT:10}

# 日志配置
logging:
  level:
    com.xhcai.modules.ai: ${AI_LOG_LEVEL:INFO}
    # AI服务日志
    com.xhcai.modules.ai.service: ${AI_SERVICE_LOG_LEVEL:DEBUG}
    # AI控制器日志
    com.xhcai.modules.ai.controller: ${AI_CONTROLLER_LOG_LEVEL:INFO}
    # Spring AI日志
    org.springframework.ai: ${SPRING_AI_LOG_LEVEL:INFO}

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: ${AI_MANAGEMENT_ENDPOINTS:health,info,metrics,ai}
  endpoint:
    ai:
      enabled: ${AI_ENDPOINT_ENABLED:true}
  metrics:
    tags:
      module: ai
