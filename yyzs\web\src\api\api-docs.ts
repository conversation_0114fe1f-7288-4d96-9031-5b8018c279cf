import apiClient, { ApiResponse } from './client';

/**
 * API文档管理API
 */
export class ApiDocsAPI {
  /**
   * 获取API接口列表
   */
  static async getApiEndpoints(category?: string): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    if (category) queryParams.append('category', category);
    
    const url = `/api/docs/endpoints${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get(url);
  }

  /**
   * 获取API接口详情
   */
  static async getApiEndpoint(endpointId: string): Promise<ApiResponse<any>> {
    return apiClient.get(`/api/docs/endpoints/${endpointId}`);
  }

  /**
   * 获取API分类列表
   */
  static async getApiCategories(): Promise<ApiResponse<any[]>> {
    return apiClient.get('/api/docs/categories');
  }
}

export default ApiDocsAPI;
