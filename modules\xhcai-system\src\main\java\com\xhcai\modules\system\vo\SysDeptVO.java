package com.xhcai.modules.system.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 部门信息VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "部门信息VO")
public class SysDeptVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID", example = "1")
    private String id;

    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID", example = "0")
    private String parentId;

    /**
     * 祖级列表
     */
    @Schema(description = "祖级列表", example = "0,1,2")
    private String ancestors;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称", example = "技术部")
    private String deptName;

    /**
     * 部门编码
     */
    @Schema(description = "部门编码", example = "TECH")
    private String deptCode;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序", example = "1")
    private Integer orderNum;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String leaderId;

    /**
     * 负责人姓名
     */
    @Schema(description = "负责人姓名", example = "张三")
    private String leaderName;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称", example = "正常")
    private String statusName;

    /**
     * 子部门列表
     */
    @Schema(description = "子部门列表")
    private List<SysDeptVO> children = new ArrayList<>();

    /**
     * 父部门名称
     */
    @Schema(description = "父部门名称")
    private String parentName;

    /**
     * 部门层级
     */
    @Schema(description = "部门层级", example = "1")
    private Integer level;

    /**
     * 是否有子部门
     */
    @Schema(description = "是否有子部门")
    private Boolean hasChildren;

    /**
     * 部门用户数量
     */
    @Schema(description = "部门用户数量", example = "10")
    private Integer userCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(String leaderId) {
        this.leaderId = leaderId;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public List<SysDeptVO> getChildren() {
        return children;
    }

    public void setChildren(List<SysDeptVO> children) {
        this.children = children;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getHasChildren() {
        return hasChildren;
    }

    public void setHasChildren(Boolean hasChildren) {
        this.hasChildren = hasChildren;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "SysDeptVO{"
                + "id=" + id
                + ", parentId=" + parentId
                + ", ancestors='" + ancestors + '\''
                + ", deptName='" + deptName + '\''
                + ", deptCode='" + deptCode + '\''
                + ", orderNum=" + orderNum
                + ", leaderId='" + leaderId + '\''
                + ", leaderName='" + leaderName + '\''
                + ", status='" + status + '\''
                + ", statusName='" + statusName + '\''
                + ", children=" + children
                + ", parentName='" + parentName + '\''
                + ", level=" + level
                + ", hasChildren=" + hasChildren
                + ", userCount=" + userCount
                + ", createTime=" + createTime
                + ", updateTime=" + updateTime
                + '}';
    }
}
