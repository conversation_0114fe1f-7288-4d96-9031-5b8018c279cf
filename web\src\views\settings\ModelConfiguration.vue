<template>
  <div class="model-configuration">
    <!-- 模型信息管理 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="models-header flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">模型信息管理</h3>
          <button @click="showAddModelModal = true" class="btn-primary">
            <span class="mr-2">➕</span>
            添加模型
          </button>
        </div>

        <!-- 模型筛选 -->
        <div class="models-filter mb-4 flex gap-4">
          <select v-model="filterProvider" @change="loadModels" class="form-input w-40">
            <option value="">全部提供商</option>
            <option v-for="option in providerOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
          <select v-model="filterType" @change="loadModels" class="form-input w-40">
            <option value="">全部类型</option>
            <option v-for="option in typeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>

        <!-- 模型列表 -->
        <div class="models-table">
          <div class="table-header grid grid-cols-7 gap-4 p-4 bg-gray-50 rounded-t-lg font-medium text-gray-700">
            <div>模型名称</div>
            <div>提供商</div>
            <div>推理平台</div>
            <div>类型</div>
            <div>版本</div>
            <div>状态</div>
            <div>操作</div>
          </div>
          <div class="table-body">
            <div
              v-for="model in filteredModels"
              :key="model.id"
              class="table-row grid grid-cols-7 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-300"
            >
              <div class="font-medium text-gray-900">{{ model.name }}</div>
              <div class="text-gray-700">{{ model.providerDict ? model.providerDict.dictLabel : model.provider }}</div>
              <div class="text-gray-700">{{ model.platformDict ? model.platformDict.dictLabel : model.platform }}</div>
              <div class="text-gray-700">{{ model.typeDict ? model.typeDict.dictLabel :model.type }}</div>
              <div class="text-gray-700">{{ model.version || '默认' }}</div>
              <div>
                <span
                  class="px-2 py-1 text-xs rounded-full"
                  :class="{
                    'bg-green-100 text-green-600': model.status === 'active',
                    'bg-gray-100 text-gray-600': model.status === 'inactive',
                    'bg-yellow-100 text-yellow-600': model.status === 'deprecated'
                  }"
                >
                  {{ getStatusText(model.status) }}
                </span>
              </div>
              <div class="flex gap-2">
                <button @click="editModel(model)" class="btn-secondary text-xs px-2 py-1">编辑</button>
                <button @click="deleteModel(model.id)" class="text-red-500 text-xs px-2 py-1 hover:bg-red-50 rounded">删除</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 模型管理分页 -->
        <Pagination
          v-model:currentPage="modelsPagination.currentPage"
          v-model:pageSize="modelsPagination.pageSize"
          :total="total"
          @change="loadModels"
        />
    </div>

    <!-- 添加/编辑模型模态框 -->
    <el-dialog
      v-model="showModelModal"
      :title="showAddModelModal ? '添加模型' : '编辑模型'"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      class="model-dialog"
    >
      <div class="model-form-container">
        <el-form
          ref="modelFormRef"
          :model="currentModel"
          label-width="120px"
          @submit.prevent="saveModel"
        >
          <!-- 基本信息 -->
          <el-divider content-position="left">
            <span class="text-lg font-medium">基本信息</span>
          </el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模型名称" required>
                <el-input
                  v-model="currentModel.name"
                  placeholder="例如：GPT-4"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="模型标识" required>
                <el-input
                  v-model="currentModel.modelId"
                  placeholder="例如：gpt-4-0613"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模型提供商" required>
                <el-select
                  v-model="currentModel.provider"
                  placeholder="请选择提供商"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in providerOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="模型类型" required>
                <el-select
                  v-model="currentModel.type"
                  placeholder="请选择类型"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in typeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="推理平台">
                <el-select
                  v-model="currentModel.platform"
                  placeholder="请选择推理平台"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in platformOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="模型版本">
                <el-input
                  v-model="currentModel.version"
                  placeholder="例如：v1.0"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态">
                <el-select
                  v-model="currentModel.status"
                  placeholder="请选择状态"
                  style="width: 100%"
                >
                  <el-option label="启用" value="1" />
                  <el-option label="停用" value="0" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="模型描述">
            <el-input
              v-model="currentModel.description"
              type="textarea"
              :rows="3"
              placeholder="描述模型的功能特点和适用场景"
            />
          </el-form-item>

          <!-- 接口配置 -->
          <el-divider content-position="left">
            <span class="text-lg font-medium">接口配置</span>
          </el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="API端点">
                <el-input
                  v-model="currentModel.apiEndpoint"
                  placeholder="https://api.openai.com/v1"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="API密钥">
                <el-input
                  v-model="currentModel.apiKey"
                  type="password"
                  placeholder="sk-..."
                  show-password
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="组织ID">
                <el-input
                  v-model="currentModel.organizationId"
                  placeholder="org-..."
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="请求超时（秒）">
                <el-input-number
                  v-model="currentModel.timeout"
                  :min="1"
                  :max="300"
                  placeholder="30"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 模型参数 -->
          <el-divider content-position="left">
            <span class="text-lg font-medium">模型参数</span>
          </el-divider>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="最大Token数">
                <el-input-number
                  v-model="currentModel.maxTokens"
                  :min="1"
                  placeholder="4096"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="温度值 (0-2)">
                <el-input-number
                  v-model="currentModel.temperature"
                  :min="0"
                  :max="2"
                  :step="0.1"
                  :precision="1"
                  placeholder="0.7"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="Top P (0-1)">
                <el-input-number
                  v-model="currentModel.topP"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  :precision="1"
                  placeholder="1"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="频率惩罚 (-2 to 2)">
                <el-input-number
                  v-model="currentModel.frequencyPenalty"
                  :min="-2"
                  :max="2"
                  :step="0.1"
                  :precision="1"
                  placeholder="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="存在惩罚 (-2 to 2)">
                <el-input-number
                  v-model="currentModel.presencePenalty"
                  :min="-2"
                  :max="2"
                  :step="0.1"
                  :precision="1"
                  placeholder="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="停止序列">
                <el-input
                  v-model="currentModel.stopSequences"
                  placeholder="用逗号分隔多个序列"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 费用配置 -->
          <el-divider content-position="left">
            <span class="text-lg font-medium">费用配置</span>
          </el-divider>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="输入价格 ($/1K tokens)">
                <el-input-number
                  v-model="currentModel.inputPrice"
                  :min="0"
                  :step="0.0001"
                  :precision="4"
                  placeholder="0.03"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="输出价格 ($/1K tokens)">
                <el-input-number
                  v-model="currentModel.outputPrice"
                  :min="0"
                  :step="0.0001"
                  :precision="4"
                  placeholder="0.06"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="每分钟请求限制">
                <el-input-number
                  v-model="currentModel.rpmLimit"
                  :min="1"
                  placeholder="3500"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="每分钟Token限制">
                <el-input-number
                  v-model="currentModel.tpmLimit"
                  :min="1"
                  placeholder="90000"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="flex gap-3">
          <el-button type="primary" @click="saveModel" style="flex: 1">
            <span class="mr-2">💾</span>
            保存模型
          </el-button>
          <el-button @click="closeModelModal" style="flex: 1">
            <span class="mr-2">❌</span>
            取消
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElInputNumber,
  ElRow,
  ElCol,
  ElDivider,
  ElNotification,
  ElMessageBox,
  ElMessage
} from 'element-plus'
import Pagination from '@/components/common/Pagination.vue'
import { AiModelAPI, type AiModel, type AiModelQueryParams } from '@/api/model'
import { DictAPI } from '@/api/dict'

// 字典数据接口
interface DictOption {
  label: string
  value: string
}

// 筛选条件
const filterProvider = ref('')
const filterType = ref('')

// 字典数据
const providerOptions = ref<DictOption[]>([])
const typeOptions = ref<DictOption[]>([])
const platformOptions = ref<DictOption[]>([])

// 模型数据
const models = ref<AiModel[]>([])
const loading = ref(false)
const total = ref(0)



// 分页数据
const modelsPagination = ref({ currentPage: 1, pageSize: 10 })

// 过滤后的模型列表（用于显示）
const filteredModels = computed(() => {
  return models.value
})

// 表单引用
const modelFormRef = ref()

// 模态框状态
const showAddModelModal = ref(false)
const showEditModelModal = ref(false)
const showModelModal = computed({
  get: () => showAddModelModal.value || showEditModelModal.value,
  set: (value: boolean) => {
    if (!value) {
      closeModelModal()
    }
  }
})

// 当前编辑的模型
const currentModel = ref<AiModel>({
  name: '',
  modelId: '',
  provider: '',
  type: '',
  platform: '',
  version: '',
  description: '',
  status: '1',
  // 接口配置
  apiEndpoint: '',
  apiKey: '',
  organizationId: '',
  timeout: 30,
  // 模型参数
  maxTokens: 4096,
  temperature: 0.7,
  topP: 1,
  frequencyPenalty: 0,
  presencePenalty: 0,
  stopSequences: '',
  // 费用配置
  inputPrice: 0,
  outputPrice: 0,
  rpmLimit: 3500,
  tpmLimit: 90000
})

// 辅助方法
const getStatusText = (status: string) => {
  const map: Record<string, string> = { '1': '活跃', '0': '停用' }
  return map[status] || status
}

// 加载字典数据
const loadDictionaries = async () => {
  try {
    // 加载AI提供商字典
    const providerResponse = await DictAPI.getDictDataByType('ai_provider')
    if (providerResponse.success && providerResponse.data) {
      providerOptions.value = providerResponse.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue
      }))
    }

    // 加载AI模型类型字典
    const typeResponse = await DictAPI.getDictDataByType('ai_model_type')
    if (typeResponse.success && typeResponse.data) {
      typeOptions.value = typeResponse.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue
      }))
    }

    // 加载推理平台字典
    const platformResponse = await DictAPI.getDictDataByType('model_inference_platform')
    if (platformResponse.success && platformResponse.data) {
      platformOptions.value = platformResponse.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue
      }))
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 加载模型列表
const loadModels = async () => {
  loading.value = true
  try {
    const params: AiModelQueryParams = {
      current: modelsPagination.value.currentPage,
      size: modelsPagination.value.pageSize,
      provider: filterProvider.value || undefined,
      type: filterType.value || undefined
    }

    const response = await AiModelAPI.getAiModelPage(params)
    if (response.success && response.data) {
      models.value = response.data.records
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载模型列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 模型管理方法
const editModel = (model: AiModel) => {
  currentModel.value = { ...model }
  showEditModelModal.value = true
}

const deleteModel = async (modelId: string | undefined) => {
  if (!modelId) return

  ElMessageBox.confirm(
    '确定要删除该模型吗？',
    '请选择是否删除',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const response = await AiModelAPI.deleteAiModel(modelId)
      if (response.success) {
        await loadModels() // 重新加载列表
        ElNotification({
          type: 'success',
          message: '删除成功！',
          title: "删除成功",
        })
      } else {
        ElNotification({
          title: '删除失败',
          message: "删除失败" + response.message,
          type: 'error'
        })
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: "取消删除！",
      })
    })
}

const closeModelModal = () => {
  showAddModelModal.value = false
  showEditModelModal.value = false
  currentModel.value = {
    name: '',
    modelId: '',
    provider: '',
    type: '',
    platform: '',
    version: '',
    description: '',
    status: '1',
    // 接口配置
    apiEndpoint: '',
    apiKey: '',
    organizationId: '',
    timeout: 30,
    // 模型参数
    maxTokens: 4096,
    temperature: 0.7,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
    stopSequences: '',
    // 费用配置
    inputPrice: 0,
    outputPrice: 0,
    rpmLimit: 3500,
    tpmLimit: 90000
  }
}

const saveModel = async () => {
  try {
    if (showEditModelModal.value && currentModel.value.id) {
      // 编辑模型
      const updateData = { ...currentModel.value, id: currentModel.value.id! }
      const response = await AiModelAPI.updateAiModel(updateData)
      if (response.success) {
        await loadModels() // 重新加载列表
        closeModelModal()
        ElNotification({
          title: '模型更新成功',
          message: "模型更新成功",
          type: 'info'
        })
      } else {
        ElNotification({
          title: '更新失败',
          message: "更新失败" + response.message,
          type: 'error'
        })
      }
    } else {
      // 添加模型
      const response = await AiModelAPI.createAiModel(currentModel.value)
      if (response.success) {
        await loadModels() // 重新加载列表
        closeModelModal()
        ElNotification({
          title: '模型创建成功',
          message: "模型创建成功",
          type: 'info'
        })
      } else {
        ElNotification({
          title: '创建失败',
          message: "创建失败" + response.message,
          type: 'error'
        })
      }
    }
  } catch (error) {
    ElNotification({
      title: '保存失败',
      message: "保存失败",
      type: 'error'
    })
  }
}

// 生命周期
onMounted(async () => {
  await loadDictionaries()
  await loadModels()
})


</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');

/* Element Plus 模态框样式优化 */
:deep(.model-dialog) {
  .el-dialog__body {
    padding: 0 20px 20px 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}

/* 表单样式优化 */
.model-form-container {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .el-divider {
    margin: 30px 0 20px 0;
  }

  .el-divider__text {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
