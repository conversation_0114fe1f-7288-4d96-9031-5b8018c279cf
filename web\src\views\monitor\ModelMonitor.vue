<template>
  <div class="model-monitor space-y-6">
    <!-- 模型概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-brain text-indigo-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总模型数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ modelStats.total }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-play text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日调用次数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ modelStats.todayCalls.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-tachometer-alt text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均响应时间</p>
            <p class="text-2xl font-semibold text-gray-900">{{ modelStats.avgResponseTime }}ms</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-bolt text-yellow-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均Token速率</p>
            <p class="text-2xl font-semibold text-gray-900">{{ modelStats.avgTokenSpeed }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 模型监控表格 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">模型监控详情</h3>
          <div class="flex items-center gap-4">
            <input
              v-model="modelSearchQuery"
              type="text"
              placeholder="搜索模型..."
              class="form-input text-sm w-64"
            />
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模型信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">调用次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">响应时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Token速率</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="model in paginatedModels" :key="model.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-indigo-400 to-indigo-600 flex items-center justify-center">
                      <i class="fas fa-brain text-white"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ model.name }}</div>
                    <div class="text-sm text-gray-500">{{ model.provider }}</div>
                    <div class="text-xs text-gray-400">{{ model.version }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">总量: {{ model.totalCalls.toLocaleString() }}</div>
                <div class="text-sm text-gray-500">今日: {{ model.todayCalls.toLocaleString() }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">平均: {{ model.avgResponseTime }}ms</div>
                <div class="text-sm text-gray-500">
                  最长: {{ model.maxResponseTime }}ms | 最短: {{ model.minResponseTime }}ms
                </div>
                <div class="text-sm text-gray-400">
                  今日最长: {{ model.todayMaxResponseTime }}ms | 今日最短: {{ model.todayMinResponseTime }}ms
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ model.avgTokenSpeed }} token/s</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(model.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusText(model.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 模型分页 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredModels.length) }} 条，
            共 {{ filteredModels.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="text-sm text-gray-700">
              第 {{ currentPage }} / {{ totalPages }} 页
            </span>
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  refreshTrigger: 0
})

// 搜索和过滤
const modelSearchQuery = ref('')

// 分页配置
const pageSize = ref(10)
const currentPage = ref(1)

// 模型统计数据
const modelStats = ref({
  total: 45,
  todayCalls: 15670,
  avgResponseTime: 245,
  avgTokenSpeed: '125 token/s'
})

// 模型监控数据
const models = ref([
  {
    id: 1,
    name: 'GPT-4',
    provider: 'OpenAI',
    version: 'gpt-4-0613',
    totalCalls: 8920,
    todayCalls: 156,
    avgResponseTime: 245,
    maxResponseTime: 1200,
    minResponseTime: 89,
    todayMaxResponseTime: 890,
    todayMinResponseTime: 95,
    avgTokenSpeed: 125,
    status: 'healthy'
  },
  {
    id: 2,
    name: 'Claude-3',
    provider: 'Anthropic',
    version: 'claude-3-sonnet',
    totalCalls: 5670,
    todayCalls: 89,
    avgResponseTime: 180,
    maxResponseTime: 980,
    minResponseTime: 67,
    todayMaxResponseTime: 650,
    todayMinResponseTime: 78,
    avgTokenSpeed: 145,
    status: 'healthy'
  },
  {
    id: 3,
    name: 'Gemini Pro',
    provider: 'Google',
    version: 'gemini-pro-1.0',
    totalCalls: 3450,
    todayCalls: 67,
    avgResponseTime: 320,
    maxResponseTime: 1500,
    minResponseTime: 120,
    todayMaxResponseTime: 1200,
    todayMinResponseTime: 145,
    avgTokenSpeed: 98,
    status: 'warning'
  }
])

// 计算属性：过滤后的数据
const filteredModels = computed(() => {
  let filtered = models.value

  if (modelSearchQuery.value) {
    const query = modelSearchQuery.value.toLowerCase()
    filtered = filtered.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.provider.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 分页后的数据
const paginatedModels = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredModels.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredModels.value.length / pageSize.value)
})

// 方法
const updateModelStats = () => {
  models.value.forEach(model => {
    model.todayCalls = Math.floor(Math.random() * 200) + 30
    model.avgResponseTime = Math.floor(Math.random() * 300) + 100
    model.todayMaxResponseTime = Math.floor(Math.random() * 800) + 400
    model.todayMinResponseTime = Math.floor(Math.random() * 100) + 50
    model.avgTokenSpeed = Math.floor(Math.random() * 80) + 80

    // 更新状态
    if (model.avgResponseTime > 400) {
      model.status = 'error'
    } else if (model.avgResponseTime > 300) {
      model.status = 'warning'
    } else {
      model.status = 'healthy'
    }
  })

  // 更新统计数据
  modelStats.value.todayCalls = models.value.reduce((sum, model) => sum + model.todayCalls, 0)
  modelStats.value.avgResponseTime = Math.round(
    models.value.reduce((sum, model) => sum + model.avgResponseTime, 0) / models.value.length
  )
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return statusMap[status] || status
}

// 分页方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const resetPagination = () => {
  currentPage.value = 1
}

// 监听搜索条件变化，重置分页
watch([modelSearchQuery], () => {
  resetPagination()
})

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  updateModelStats()
})

onMounted(() => {
  updateModelStats()
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-input {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  background-color: #f8fafc;
  transform: scale(1.01);
}

/* 渐变背景 */
.from-indigo-400 {
  --tw-gradient-from: #818cf8;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(129, 140, 248, 0));
}

.to-indigo-600 {
  --tw-gradient-to: #4f46e5;
}
</style>
