<template>
  <div class="server-monitor space-y-6">
    <!-- 服务器概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-server text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总服务器数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ serverStats.total }}</p>
            <div class="text-xs text-gray-500 mt-1">
              在线: {{ serverStats.online }} | 警告: {{ serverStats.warning }} | 离线: {{ serverStats.offline }}
            </div>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-microchip text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均CPU使用率</p>
            <p class="text-2xl font-semibold text-gray-900">{{ serverStats.avgCpuUsage }}%</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-memory text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均内存使用率</p>
            <p class="text-2xl font-semibold text-gray-900">{{ serverStats.avgMemoryUsage }}%</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-cogs text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总服务数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ serverStats.totalServices }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务器监控列表 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">服务器监控详情</h3>
          <div class="flex items-center gap-4">
            <select v-model="serverStatusFilter" class="form-select text-sm">
              <option value="">全部状态</option>
              <option value="healthy">正常</option>
              <option value="warning">警告</option>
              <option value="error">异常</option>
            </select>
            <input
              v-model="serverSearchQuery"
              type="text"
              placeholder="搜索服务器..."
              class="form-input text-sm w-64"
            />
          </div>
        </div>
      </div>

      <!-- 服务器卡片列表 -->
      <div class="p-6 space-y-6">
        <div
          v-for="server in filteredServers"
          :key="server.id"
          class="server-card bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors duration-300"
        >
          <!-- 服务器基本信息 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-12 w-12">
                <div class="h-12 w-12 rounded-lg bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center">
                  <i class="fas fa-server text-white text-lg"></i>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-lg font-medium text-gray-900">{{ server.name }}</div>
                <div class="text-sm text-gray-500">{{ server.ip }} · {{ server.location }}</div>
                <div class="text-xs text-gray-400">{{ server.os }} · 运行时间: {{ server.uptime }}</div>
              </div>
            </div>
            <div class="flex items-center gap-4">
              <span :class="getStatusClass(server.status)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                {{ getStatusText(server.status) }}
              </span>
              <div class="text-xs text-gray-500">
                更新: {{ server.lastUpdate }}
              </div>
            </div>
          </div>

          <!-- 服务器性能指标 -->
          <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div class="metric-item">
              <div class="text-xs text-gray-500 mb-1">CPU使用率</div>
              <div class="flex items-center">
                <div class="text-sm font-semibold text-gray-900">{{ server.cpuUsage }}%</div>
                <div class="ml-2 flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300"
                    :class="server.cpuUsage > 70 ? 'bg-red-500' : server.cpuUsage > 50 ? 'bg-yellow-500' : 'bg-green-500'"
                    :style="{ width: server.cpuUsage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            <div class="metric-item">
              <div class="text-xs text-gray-500 mb-1">内存使用率</div>
              <div class="flex items-center">
                <div class="text-sm font-semibold text-gray-900">{{ server.memoryUsage }}%</div>
                <div class="ml-2 flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300"
                    :class="server.memoryUsage > 80 ? 'bg-red-500' : server.memoryUsage > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                    :style="{ width: server.memoryUsage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            <div class="metric-item">
              <div class="text-xs text-gray-500 mb-1">磁盘使用率</div>
              <div class="flex items-center">
                <div class="text-sm font-semibold text-gray-900">{{ server.diskUsage }}%</div>
                <div class="ml-2 flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300"
                    :class="server.diskUsage > 80 ? 'bg-red-500' : server.diskUsage > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                    :style="{ width: server.diskUsage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
            <div class="metric-item">
              <div class="text-xs text-gray-500 mb-1">网络入流量</div>
              <div class="text-sm font-semibold text-gray-900">{{ server.networkIn }} MB/s</div>
            </div>
            <div class="metric-item">
              <div class="text-xs text-gray-500 mb-1">网络出流量</div>
              <div class="text-sm font-semibold text-gray-900">{{ server.networkOut }} MB/s</div>
            </div>
          </div>

          <!-- 平台服务列表 -->
          <div class="services-section">
            <h4 class="text-sm font-medium text-gray-900 mb-3">平台服务 ({{ server.services.length }})</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="service in server.services"
                :key="service.name"
                class="service-card bg-white rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors duration-200"
              >
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <div class="service-icon mr-3">
                      <i
                        :class="getServiceIcon(service.type)"
                        class="text-lg"
                        :style="{ color: getServiceColor(service.type) }"
                      ></i>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ service.name }}</div>
                      <div class="text-xs text-gray-500">{{ service.type }}</div>
                    </div>
                  </div>
                  <span :class="getStatusClass(service.status)" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium">
                    {{ getStatusText(service.status) }}
                  </span>
                </div>
                <div class="service-details text-xs text-gray-600 space-y-1">
                  <div class="flex justify-between">
                    <span>端口:</span>
                    <span>{{ service.port }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>版本:</span>
                    <span>{{ service.version }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>CPU:</span>
                    <span>{{ service.cpu }}%</span>
                  </div>
                  <div class="flex justify-between">
                    <span>内存:</span>
                    <span>{{ service.memory }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  refreshTrigger: 0
})

// 搜索和过滤
const serverSearchQuery = ref('')
const serverStatusFilter = ref('')

// 服务器统计数据
const serverStats = ref({
  total: 12,
  online: 11,
  warning: 1,
  offline: 0,
  avgCpuUsage: 45.2,
  avgMemoryUsage: 68.5,
  avgDiskUsage: 32.8,
  totalServices: 60
})

// 服务器监控数据
const servers = ref([
  {
    id: 1,
    name: 'AI-Server-01',
    ip: '*************',
    location: '北京机房A',
    os: 'Ubuntu 22.04 LTS',
    cpuUsage: 35.2,
    memoryUsage: 68.5,
    diskUsage: 45.8,
    networkIn: 125.6,
    networkOut: 89.3,
    uptime: '15天 8小时',
    status: 'healthy',
    lastUpdate: '2024-06-30 14:30:25',
    services: [
      { name: 'PostgreSQL', type: 'postgresql', status: 'healthy', port: 5432, version: '14.9', cpu: 8.5, memory: 12.3 },
      { name: 'Redis', type: 'redis', status: 'healthy', port: 6379, version: '7.0.12', cpu: 2.1, memory: 5.8 },
      { name: 'Frontend', type: '前后端服务', status: 'healthy', port: 3000, version: '1.0.0', cpu: 15.2, memory: 25.4 }
    ]
  },
  {
    id: 2,
    name: 'AI-Server-02',
    ip: '*************',
    location: '北京机房A',
    os: 'Ubuntu 22.04 LTS',
    cpuUsage: 52.8,
    memoryUsage: 75.2,
    diskUsage: 28.9,
    networkIn: 89.2,
    networkOut: 156.7,
    uptime: '12天 15小时',
    status: 'warning',
    lastUpdate: '2024-06-30 14:29:58',
    services: [
      { name: 'Weaviate', type: 'weaviate知识库服务', status: 'healthy', port: 8080, version: '1.21.2', cpu: 25.8, memory: 45.6 },
      { name: 'Backend API', type: '前后端服务', status: 'warning', port: 8000, version: '1.0.0', cpu: 18.9, memory: 28.7 },
      { name: 'Plugin Service', type: '插件服务', status: 'healthy', port: 9000, version: '1.2.1', cpu: 8.1, memory: 15.3 }
    ]
  }
])

// 计算属性：过滤后的数据
const filteredServers = computed(() => {
  let filtered = servers.value

  if (serverStatusFilter.value) {
    filtered = filtered.filter(server => server.status === serverStatusFilter.value)
  }

  if (serverSearchQuery.value) {
    const query = serverSearchQuery.value.toLowerCase()
    filtered = filtered.filter(server =>
      server.name.toLowerCase().includes(query) ||
      server.ip.toLowerCase().includes(query) ||
      server.location.toLowerCase().includes(query) ||
      server.os.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 方法
const updateServerStats = () => {
  servers.value.forEach(server => {
    server.cpuUsage = Math.round(Math.random() * 80 + 10)
    server.memoryUsage = Math.round(Math.random() * 70 + 20)
    server.diskUsage = Math.round(Math.random() * 60 + 15)
    server.networkIn = Math.round(Math.random() * 200 + 50)
    server.networkOut = Math.round(Math.random() * 150 + 30)
    server.lastUpdate = new Date().toLocaleString('zh-CN')

    // 更新服务状态
    server.services.forEach(service => {
      service.cpu = Math.round(Math.random() * 50 * 10) / 10
      service.memory = Math.round(Math.random() * 60 * 10) / 10

      // 根据资源使用情况更新服务状态
      if (service.cpu > 40 || service.memory > 50) {
        service.status = 'warning'
      } else if (service.cpu > 60 || service.memory > 70) {
        service.status = 'error'
      } else {
        service.status = 'healthy'
      }
    })

    // 更新服务器状态
    const avgServiceCpu = server.services.reduce((sum, service) => sum + service.cpu, 0) / server.services.length
    if (server.cpuUsage > 70 || server.memoryUsage > 80 || avgServiceCpu > 50) {
      server.status = 'warning'
    } else if (server.cpuUsage > 85 || server.memoryUsage > 90) {
      server.status = 'error'
    } else {
      server.status = 'healthy'
    }
  })

  // 更新统计数据
  serverStats.value.online = servers.value.filter(server => server.status === 'healthy').length
  serverStats.value.warning = servers.value.filter(server => server.status === 'warning').length
  serverStats.value.offline = servers.value.filter(server => server.status === 'error').length
  serverStats.value.avgCpuUsage = Math.round(
    servers.value.reduce((sum, server) => sum + server.cpuUsage, 0) / servers.value.length * 10
  ) / 10
  serverStats.value.avgMemoryUsage = Math.round(
    servers.value.reduce((sum, server) => sum + server.memoryUsage, 0) / servers.value.length * 10
  ) / 10
  serverStats.value.avgDiskUsage = Math.round(
    servers.value.reduce((sum, server) => sum + server.diskUsage, 0) / servers.value.length * 10
  ) / 10
  serverStats.value.totalServices = servers.value.reduce((sum, server) => sum + server.services.length, 0)
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return statusMap[status] || status
}

const getServiceIcon = (serviceType: string) => {
  const iconMap: Record<string, string> = {
    'postgresql': 'fas fa-database',
    'redis': 'fas fa-cube',
    'weaviate知识库服务': 'fas fa-brain',
    '前后端服务': 'fas fa-globe',
    '插件服务': 'fas fa-puzzle-piece'
  }
  return iconMap[serviceType] || 'fas fa-cog'
}

const getServiceColor = (serviceType: string) => {
  const colorMap: Record<string, string> = {
    'postgresql': '#336791',
    'redis': '#DC382D',
    'weaviate知识库服务': '#00D4AA',
    '前后端服务': '#42B883',
    '插件服务': '#FF6B6B'
  }
  return colorMap[serviceType] || '#6B7280'
}

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  updateServerStats()
})

onMounted(() => {
  updateServerStats()
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-input, .form-select {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 服务器监控特定样式 */
.server-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.server-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(156, 163, 175, 0.5);
}

.metric-item {
  min-width: 120px;
}

.service-card {
  transition: all 0.2s ease;
}

.service-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 进度条动画 */
.metric-item .h-2 {
  transition: width 0.5s ease-in-out;
}

/* 服务状态指示器 */
.service-card .status-badge {
  font-size: 10px;
  padding: 2px 6px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .server-card {
    padding: 1rem;
  }

  .metric-item {
    min-width: auto;
  }

  .services-section .grid {
    grid-template-columns: 1fr;
  }
}

/* 渐变背景 */
.from-blue-400 {
  --tw-gradient-from: #60a5fa;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0));
}

.to-blue-600 {
  --tw-gradient-to: #2563eb;
}
</style>
