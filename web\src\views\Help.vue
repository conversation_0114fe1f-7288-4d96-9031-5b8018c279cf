<template>
  <div class="help-page min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- 页面头部 -->
    <div class="help-header bg-white shadow-sm border-b border-gray-200 px-4 py-4">
      <div class="mx-auto" style="max-width: calc(100vw - 2rem);">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">帮助中心</h1>
            <p class="text-sm text-gray-600 mt-1">查找您需要的帮助文档和常见问题解答</p>
          </div>
          <div class="flex items-center gap-4">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索帮助文档..."
                class="form-input pl-10 pr-4 py-2 w-full sm:w-80"
              />
              <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mx-auto px-4 py-8" style="max-width: calc(100vw - 2rem);">
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- 左侧分类导航 -->
        <div class="w-full lg:w-64 flex-shrink-0">
          <div class="bg-white rounded-lg shadow-sm p-6 lg:sticky lg:top-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">文档分类</h3>
            <nav class="space-y-2">
              <button
                v-for="category in categories"
                :key="category.key"
                @click="selectedCategory = category.key"
                class="w-full text-left px-3 py-2 rounded-lg transition-colors"
                :class="{
                  'bg-blue-100 text-blue-700 font-medium': selectedCategory === category.key,
                  'text-gray-700 hover:bg-gray-100': selectedCategory !== category.key
                }"
              >
                <i :class="category.icon" class="mr-3"></i>
                {{ category.name }}
                <span class="float-right text-sm text-gray-500">{{ getCategoryCount(category.key) }}</span>
              </button>
            </nav>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="flex-1">
          <!-- 文档列表 -->
          <div v-if="!selectedDoc" class="space-y-6">
            <!-- 分类标题 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-2">
                {{ getCurrentCategoryName() }}
              </h2>
              <p class="text-gray-600">
                {{ getCurrentCategoryDescription() }}
              </p>
            </div>

            <!-- 文档卡片列表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div
                v-for="doc in filteredDocs"
                :key="doc.id"
                class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer border border-gray-200 hover:border-blue-300"
                @click="selectDoc(doc)"
              >
                <div class="flex items-start justify-between mb-3">
                  <h3 class="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors">
                    {{ doc.title }}
                  </h3>
                  <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {{ getCategoryName(doc.category) }}
                  </span>
                </div>
                <div class="text-sm text-gray-600 mb-4" v-html="getDocPreview(doc.content)"></div>
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>最后更新: {{ formatDate(doc.updatedAt) }}</span>
                  <span class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-right mr-1"></i>阅读更多
                  </span>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredDocs.length === 0" class="bg-white rounded-lg shadow-sm p-12 text-center">
              <div class="text-gray-400 text-6xl mb-4">
                <i class="fas fa-search"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">未找到相关文档</h3>
              <p class="text-gray-500">
                {{ searchQuery ? '请尝试其他关键词搜索' : '该分类下暂无文档' }}
              </p>
            </div>
          </div>

          <!-- 文档详情 -->
          <div v-else class="bg-white rounded-lg shadow-sm">
            <!-- 文档头部 -->
            <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
              <div class="flex items-center justify-between">
                <div>
                  <button
                    @click="selectedDoc = null"
                    class="text-blue-600 hover:text-blue-800 mb-2 text-sm"
                  >
                    <i class="fas fa-arrow-left mr-2"></i>返回文档列表
                  </button>
                  <h1 class="text-2xl font-bold text-gray-900">{{ selectedDoc.title }}</h1>
                  <p class="text-sm text-gray-600 mt-1">
                    分类: {{ getCategoryName(selectedDoc.category) }} • 
                    最后更新: {{ formatDateTime(selectedDoc.updatedAt) }}
                  </p>
                </div>
                <div class="flex items-center gap-3">
                  <button
                    @click="printDoc"
                    class="btn-secondary text-sm"
                  >
                    <i class="fas fa-print mr-2"></i>打印
                  </button>
                  <button
                    @click="shareDoc"
                    class="btn-secondary text-sm"
                  >
                    <i class="fas fa-share mr-2"></i>分享
                  </button>
                </div>
              </div>
            </div>

            <!-- 文档内容 -->
            <div class="p-6">
              <div class="prose max-w-none" v-html="selectedDoc.content"></div>
            </div>

            <!-- 文档底部 -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                  这篇文档对您有帮助吗？
                </div>
                <div class="flex items-center gap-2">
                  <button
                    @click="rateDoc(true)"
                    class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                  >
                    <i class="fas fa-thumbs-up mr-1"></i>有帮助
                  </button>
                  <button
                    @click="rateDoc(false)"
                    class="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                  >
                    <i class="fas fa-thumbs-down mr-1"></i>没帮助
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 定义文档类型
interface HelpDoc {
  id: number
  title: string
  content: string
  category: string
  updatedAt: string
  createdAt: string
}

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedDoc = ref<HelpDoc | null>(null)

// 分类数据
const categories = [
  { key: 'all', name: '全部文档', icon: 'fas fa-list', description: '查看所有帮助文档' },
  { key: 'getting-started', name: '快速入门', icon: 'fas fa-rocket', description: '新手入门指南和基础教程' },
  { key: 'user-guide', name: '用户指南', icon: 'fas fa-user-guide', description: '详细的功能使用说明' },
  { key: 'api-docs', name: 'API文档', icon: 'fas fa-code', description: '开发者API接口文档' },
  { key: 'troubleshooting', name: '故障排除', icon: 'fas fa-tools', description: '常见问题解决方案' },
  { key: 'faq', name: '常见问题', icon: 'fas fa-question-circle', description: '用户常见问题解答' },
  { key: 'other', name: '其他', icon: 'fas fa-ellipsis-h', description: '其他相关文档' }
]

// 帮助文档数据（从设置页面获取，这里模拟数据）
const helpDocs = ref([
  {
    id: 1,
    title: '快速入门指南',
    content: '<h1>快速入门指南</h1><p>欢迎使用AI智能体平台！本指南将帮助您快速上手。</p><h2>第一步：登录系统</h2><p>使用您的账号和密码登录系统...</p><h2>第二步：创建智能体</h2><p>点击智能体页面的创建按钮...</p>',
    category: 'getting-started',
    status: 'active',
    createdAt: '2024-01-15 10:00:00',
    updatedAt: '2024-01-20 14:30:00'
  },
  {
    id: 2,
    title: '智能体创建教程',
    content: '<h1>智能体创建教程</h1><p>本教程将详细介绍如何创建和配置智能体。</p><h2>创建步骤</h2><ol><li>进入智能体页面</li><li>点击创建按钮</li><li>填写基本信息</li><li>配置模型参数</li><li>测试和发布</li></ol>',
    category: 'user-guide',
    status: 'active',
    createdAt: '2024-01-16 09:00:00',
    updatedAt: '2024-01-22 16:45:00'
  },
  {
    id: 3,
    title: 'API接口文档',
    content: '<h1>API接口文档</h1><p>本文档介绍平台提供的API接口。</p><h2>认证方式</h2><p>使用API Key进行认证...</p><h2>接口列表</h2><ul><li>获取智能体列表</li><li>创建智能体</li><li>调用智能体</li></ul>',
    category: 'api-docs',
    status: 'active',
    createdAt: '2024-01-18 11:00:00',
    updatedAt: '2024-01-25 10:15:00'
  }
])

// 计算属性
const filteredDocs = computed(() => {
  let filtered = helpDocs.value.filter(doc => doc.status === 'active')
  
  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(doc => doc.category === selectedCategory.value)
  }
  
  // 按搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(doc =>
      doc.title.toLowerCase().includes(query) ||
      doc.content.toLowerCase().includes(query)
    )
  }
  
  return filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
})

// 方法
const selectDoc = (doc: any) => {
  selectedDoc.value = doc
}

const getCategoryCount = (categoryKey: string) => {
  if (categoryKey === 'all') {
    return helpDocs.value.filter(doc => doc.status === 'active').length
  }
  return helpDocs.value.filter(doc => doc.category === categoryKey && doc.status === 'active').length
}

const getCurrentCategoryName = () => {
  const category = categories.find(cat => cat.key === selectedCategory.value)
  return category ? category.name : '全部文档'
}

const getCurrentCategoryDescription = () => {
  const category = categories.find(cat => cat.key === selectedCategory.value)
  return category ? category.description : '查看所有帮助文档'
}

const getCategoryName = (categoryKey: string) => {
  const category = categories.find(cat => cat.key === categoryKey)
  return category ? category.name : categoryKey
}

const getDocPreview = (content: string) => {
  // 移除HTML标签并截取前100个字符
  const text = content.replace(/<[^>]*>/g, '')
  return text.length > 100 ? text.substring(0, 100) + '...' : text
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const printDoc = () => {
  window.print()
}

const shareDoc = () => {
  if (navigator.share) {
    navigator.share({
      title: selectedDoc.value?.title,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    alert('链接已复制到剪贴板')
  }
}

const rateDoc = (helpful: boolean) => {
  const message = helpful ? '感谢您的反馈！' : '感谢您的反馈，我们会持续改进文档质量。'
  alert(message)
}

// 生命周期
onMounted(() => {
  // 这里可以从API获取帮助文档数据
  console.log('帮助页面已加载')
})
</script>

<style scoped>
.help-page {
  width: 100%;
  overflow-x: hidden;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-secondary {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  color: #1e40af;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #a8d8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 13px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #bae6fd 0%, #93c5fd 100%);
  color: #1e3a8a;
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(168, 216, 240, 0.3);
}

.prose {
  line-height: 1.7;
}

.prose h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #374151;
}

.prose p {
  margin-bottom: 1rem;
  color: #4b5563;
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  color: #4b5563;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .help-page {
    padding: 0;
  }

  .help-header {
    padding: 1rem;
  }

  .mx-auto {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
    max-width: calc(100vw - 1rem) !important;
  }
}

@media (max-width: 640px) {
  .form-input {
    width: 100% !important;
  }
}
</style>
