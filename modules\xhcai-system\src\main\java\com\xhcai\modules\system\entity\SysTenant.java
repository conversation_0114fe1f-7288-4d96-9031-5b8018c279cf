package com.xhcai.modules.system.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.annotation.NoTenant;
import com.xhcai.common.datasource.entity.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 租户信息实体
 *
 * <p>
 * 租户表本身不需要租户隔离，因为它是管理租户的基础表</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_tenant")
@Schema(description = "租户信息")
@TableName("sys_tenant")
@NoTenant(reason = "租户表本身不需要租户隔离")
public class SysTenant extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 租户编码
     */
    @Column(name = "tenant_code", length = 50)
    @Schema(description = "租户编码", example = "TENANT001")
    @NotBlank(message = "租户编码不能为空")
    @Size(min = 2, max = 50, message = "租户编码长度必须在2-50个字符之间")
    @Pattern(regexp = "^[A-Z0-9_]+$", message = "租户编码只能包含大写字母、数字和下划线")
    @TableField("tenant_code")
    private String tenantCode;

    /**
     * 租户名称
     */
    @Column(name = "tenant_name", length = 100)
    @Schema(description = "租户名称", example = "示例企业")
    @NotBlank(message = "租户名称不能为空")
    @Size(min = 2, max = 100, message = "租户名称长度必须在2-100个字符之间")
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 租户简称
     */
    @Column(name = "tenant_short_name", length = 50)
    @Schema(description = "租户简称", example = "示例")
    @Size(max = 50, message = "租户简称长度不能超过50个字符")
    @TableField("tenant_short_name")
    private String tenantShortName;

    /**
     * 联系人
     */
    @Column(name = "contact_person", length = 50)
    @Schema(description = "联系人", example = "张三")
    @Size(max = 50, message = "联系人长度不能超过50个字符")
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Column(name = "contact_phone", length = 20)
    @Schema(description = "联系电话", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @Column(name = "contact_email", length = 100)
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    @Email(message = "联系邮箱格式不正确")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 租户地址
     */
    @Column(name = "address", length = 200)
    @Schema(description = "租户地址")
    @Size(max = 200, message = "租户地址长度不能超过200个字符")
    @TableField("address")
    private String address;

    /**
     * 租户LOGO
     */
    @Column(name = "logo", length = 500)
    @Schema(description = "租户LOGO")
    @Size(max = 500, message = "租户LOGO长度不能超过500个字符")
    @TableField("logo")
    private String logo;

    /**
     * 租户域名
     */
    @Column(name = "domain", length = 100)
    @Schema(description = "租户域名", example = "tenant.example.com")
    @Size(max = 100, message = "租户域名长度不能超过100个字符")
    @TableField("domain")
    private String domain;

    /**
     * 租户状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "租户状态", example = "0", allowableValues = {"0", "1", "2"})
    @Pattern(regexp = "^[012]$", message = "租户状态值必须为0、1或2")
    @TableField("status")
    private String status;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /**
     * 用户数量限制
     */
    @Column(name = "user_limit")
    @Schema(description = "用户数量限制", example = "100")
    @TableField("user_limit")
    private Integer userLimit;

    /**
     * 存储空间限制（MB）
     */
    @Column(name = "storage_limit")
    @Schema(description = "存储空间限制（MB）", example = "1024")
    @TableField("storage_limit")
    private Long storageLimit;

    /**
     * 租户描述
     */
    @Column(name = "description", length = 500)
    @Schema(description = "租户描述")
    @Size(max = 500, message = "租户描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    // Getters and Setters
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getTenantShortName() {
        return tenantShortName;
    }

    public void setTenantShortName(String tenantShortName) {
        this.tenantShortName = tenantShortName;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getUserLimit() {
        return userLimit;
    }

    public void setUserLimit(Integer userLimit) {
        this.userLimit = userLimit;
    }

    public Long getStorageLimit() {
        return storageLimit;
    }

    public void setStorageLimit(Long storageLimit) {
        this.storageLimit = storageLimit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "SysTenant{"
                + "tenantCode='" + tenantCode + '\''
                + ", tenantName='" + tenantName + '\''
                + ", tenantShortName='" + tenantShortName + '\''
                + ", contactPerson='" + contactPerson + '\''
                + ", contactPhone='" + contactPhone + '\''
                + ", contactEmail='" + contactEmail + '\''
                + ", address='" + address + '\''
                + ", logo='" + logo + '\''
                + ", domain='" + domain + '\''
                + ", status='" + status + '\''
                + ", expireTime=" + expireTime
                + ", userLimit=" + userLimit
                + ", storageLimit=" + storageLimit
                + ", description='" + description + '\''
                + ", " + super.toString()
                + '}';
    }
}
