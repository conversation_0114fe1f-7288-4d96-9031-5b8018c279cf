'use client';

import { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Play, 
  Square, 
  RotateCcw, 
  Settings, 
  FileText, 
  Activity,
  Server,
  Clock,
  HardDrive,
  Cpu,
  MemoryStick,
  Network,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { ElasticComponent, ComponentStatus, ComponentStatusLabels, ComponentStatusColors } from '@/types/component';
import { ComponentMonitor } from '@/types/monitor';
import ComponentsAPI from '@/api/components';
import MonitorAPI from '@/api/monitor';
import toast from 'react-hot-toast';

interface ComponentDetailProps {
  component: ElasticComponent;
  onBack: () => void;
  onComponentUpdate?: (component: ElasticComponent) => void;
}

export default function ComponentDetail({ component, onBack, onComponentUpdate }: ComponentDetailProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'config' | 'logs' | 'monitor'>('overview');
  const [componentData, setComponentData] = useState<ElasticComponent>(component);
  const [monitorData, setMonitorData] = useState<ComponentMonitor | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [config, setConfig] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    loadComponentData();
    if (activeTab === 'monitor') {
      loadMonitorData();
    } else if (activeTab === 'logs') {
      loadLogs();
    } else if (activeTab === 'config') {
      loadConfig();
    }
  }, [activeTab]);

  const loadComponentData = async () => {
    try {
      const response = await ComponentsAPI.getComponent(component.id);
      if (response.success && response.data) {
        setComponentData(response.data);
        onComponentUpdate?.(response.data);
      }
    } catch (error: any) {
      console.error('获取组件详情失败:', error);
    }
  };

  const loadMonitorData = async () => {
    try {
      setLoading(true);
      const response = await MonitorAPI.getLatestMonitorData(component.id);
      if (response.success && response.data) {
        setMonitorData(response.data);
      }
    } catch (error: any) {
      console.error('获取监控数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadLogs = async () => {
    try {
      setLoading(true);
      const response = await ComponentsAPI.getComponentLogs(component.id, 100);
      if (response.success && response.data) {
        setLogs(response.data.logs);
      }
    } catch (error: any) {
      console.error('获取日志失败:', error);
      toast.error('获取日志失败');
    } finally {
      setLoading(false);
    }
  };

  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await ComponentsAPI.getComponentConfig(component.id);
      if (response.success && response.data) {
        setConfig(response.data);
      }
    } catch (error: any) {
      console.error('获取配置失败:', error);
      toast.error('获取配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (action: 'start' | 'stop' | 'restart') => {
    setActionLoading(true);
    
    try {
      let response;
      switch (action) {
        case 'start':
          response = await ComponentsAPI.startComponent(component.id);
          break;
        case 'stop':
          response = await ComponentsAPI.stopComponent(component.id);
          break;
        case 'restart':
          response = await ComponentsAPI.restartComponent(component.id);
          break;
      }

      if (response.success) {
        toast.success(`组件${getActionLabel(action)}成功`);
        await loadComponentData();
      } else {
        throw new Error(response.message || `${getActionLabel(action)}失败`);
      }
    } catch (error: any) {
      console.error(`组件${getActionLabel(action)}失败:`, error);
      toast.error(error.message || `${getActionLabel(action)}失败`);
    } finally {
      setActionLoading(false);
    }
  };

  const getActionLabel = (action: string) => {
    const labels: Record<string, string> = {
      start: '启动',
      stop: '停止',
      restart: '重启'
    };
    return labels[action] || action;
  };

  const getStatusIcon = (status: ComponentStatus) => {
    switch (status) {
      case ComponentStatus.RUNNING:
        return <CheckCircle className="h-5 w-5 text-success-600" />;
      case ComponentStatus.STOPPED:
        return <XCircle className="h-5 w-5 text-secondary-600" />;
      case ComponentStatus.ERROR:
        return <AlertTriangle className="h-5 w-5 text-error-600" />;
      case ComponentStatus.INSTALLING:
        return <Clock className="h-5 w-5 text-warning-600" />;
      default:
        return <XCircle className="h-5 w-5 text-secondary-400" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}天 ${hours}小时`;
    if (hours > 0) return `${hours}小时 ${minutes}分钟`;
    return `${minutes}分钟`;
  };

  const tabs = [
    { id: 'overview', label: '概览', icon: Server },
    { id: 'monitor', label: '监控', icon: Activity },
    { id: 'config', label: '配置', icon: Settings },
    { id: 'logs', label: '日志', icon: FileText },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{componentData.name}</h1>
            <p className="text-gray-600">{componentData.type} v{componentData.version}</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {componentData.status === ComponentStatus.STOPPED && (
            <button
              onClick={() => handleAction('start')}
              disabled={actionLoading}
              className="btn-success"
            >
              <Play className="h-4 w-4 mr-2" />
              启动
            </button>
          )}
          
          {componentData.status === ComponentStatus.RUNNING && (
            <button
              onClick={() => handleAction('stop')}
              disabled={actionLoading}
              className="btn-warning"
            >
              <Square className="h-4 w-4 mr-2" />
              停止
            </button>
          )}
          
          <button
            onClick={() => handleAction('restart')}
            disabled={actionLoading || componentData.status === ComponentStatus.INSTALLING}
            className="btn-primary"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            重启
          </button>
        </div>
      </div>

      {/* Status Card */}
      <div className="bg-white rounded-lg shadow-soft p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(componentData.status)}
            <div>
              <h3 className="text-lg font-medium text-gray-900">组件状态</h3>
              <p className={`text-sm ${ComponentStatusColors[componentData.status]}`}>
                {ComponentStatusLabels[componentData.status]}
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-gray-500">端口</p>
            <p className="text-lg font-medium text-gray-900">
              {componentData.port || '未配置'}
            </p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-soft">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <OverviewTab component={componentData} />
          )}
          
          {activeTab === 'monitor' && (
            <MonitorTab 
              component={componentData} 
              monitorData={monitorData} 
              loading={loading}
              onRefresh={loadMonitorData}
            />
          )}
          
          {activeTab === 'config' && (
            <ConfigTab 
              config={config} 
              loading={loading}
              onRefresh={loadConfig}
            />
          )}
          
          {activeTab === 'logs' && (
            <LogsTab 
              logs={logs} 
              loading={loading}
              onRefresh={loadLogs}
            />
          )}
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ component }: { component: ElasticComponent }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">基本信息</h4>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-500">组件名称</span>
            <span className="font-medium">{component.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">组件类型</span>
            <span className="font-medium">{component.type}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">版本</span>
            <span className="font-medium">{component.version}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">主机</span>
            <span className="font-medium">{component.host}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">端口</span>
            <span className="font-medium">{component.port || '未配置'}</span>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">路径信息</h4>
        <div className="space-y-3">
          <div>
            <span className="text-gray-500 block">安装路径</span>
            <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
              {component.installPath || '未设置'}
            </span>
          </div>
          <div>
            <span className="text-gray-500 block">配置路径</span>
            <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
              {component.configPath || '未设置'}
            </span>
          </div>
          <div>
            <span className="text-gray-500 block">日志路径</span>
            <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
              {component.logPath || '未设置'}
            </span>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">时间信息</h4>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-500">创建时间</span>
            <span className="font-medium">
              {new Date(component.createTime).toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">更新时间</span>
            <span className="font-medium">
              {new Date(component.updateTime).toLocaleString()}
            </span>
          </div>
          {component.lastStartTime && (
            <div className="flex justify-between">
              <span className="text-gray-500">最后启动</span>
              <span className="font-medium">
                {new Date(component.lastStartTime).toLocaleString()}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">其他信息</h4>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-500">自动启动</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              component.autoStart 
                ? 'bg-success-100 text-success-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {component.autoStart ? '启用' : '禁用'}
            </span>
          </div>
          {component.processId && (
            <div className="flex justify-between">
              <span className="text-gray-500">进程ID</span>
              <span className="font-medium">{component.processId}</span>
            </div>
          )}
          {component.description && (
            <div>
              <span className="text-gray-500 block">描述</span>
              <span className="text-sm">{component.description}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Monitor Tab Component
interface MonitorTabProps {
  component: ElasticComponent;
  monitorData: ComponentMonitor | null;
  loading: boolean;
  onRefresh: () => void;
}

function MonitorTab({ component, monitorData, loading, onRefresh }: MonitorTabProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">加载监控数据...</p>
        </div>
      </div>
    );
  }

  if (!monitorData) {
    return (
      <div className="text-center py-12">
        <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无监控数据</h3>
        <p className="text-gray-600 mb-4">组件可能尚未启动或监控服务未运行</p>
        <button onClick={onRefresh} className="btn-primary">
          刷新数据
        </button>
      </div>
    );
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}天 ${hours}小时`;
    if (hours > 0) return `${hours}小时 ${minutes}分钟`;
    return `${minutes}分钟`;
  };

  return (
    <div className="space-y-6">
      {/* Health Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {monitorData.isHealthy ? (
            <CheckCircle className="h-6 w-6 text-success-600" />
          ) : (
            <AlertTriangle className="h-6 w-6 text-error-600" />
          )}
          <div>
            <h4 className="font-medium text-gray-900">健康状态</h4>
            <p className={`text-sm ${monitorData.isHealthy ? 'text-success-600' : 'text-error-600'}`}>
              {monitorData.isHealthy ? '健康' : '不健康'}
            </p>
            {monitorData.healthMessage && (
              <p className="text-xs text-gray-500">{monitorData.healthMessage}</p>
            )}
          </div>
        </div>
        <button onClick={onRefresh} className="btn-outline btn-sm">
          刷新
        </button>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* CPU Usage */}
        {monitorData.cpuUsage !== undefined && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Cpu className="h-4 w-4 text-primary-600" />
              <span className="text-sm font-medium text-gray-700">CPU使用率</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {monitorData.cpuUsage.toFixed(1)}%
            </div>
          </div>
        )}

        {/* Memory Usage */}
        {monitorData.memoryUsage !== undefined && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <MemoryStick className="h-4 w-4 text-success-600" />
              <span className="text-sm font-medium text-gray-700">内存使用</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatBytes(monitorData.memoryUsage * 1024 * 1024)}
            </div>
            {monitorData.memoryUsagePercent && (
              <div className="text-sm text-gray-500">
                {monitorData.memoryUsagePercent.toFixed(1)}%
              </div>
            )}
          </div>
        )}

        {/* Disk Usage */}
        {monitorData.diskUsage !== undefined && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <HardDrive className="h-4 w-4 text-warning-600" />
              <span className="text-sm font-medium text-gray-700">磁盘使用</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatBytes(monitorData.diskUsage * 1024 * 1024)}
            </div>
            {monitorData.diskUsagePercent && (
              <div className="text-sm text-gray-500">
                {monitorData.diskUsagePercent.toFixed(1)}%
              </div>
            )}
          </div>
        )}

        {/* Uptime */}
        {monitorData.uptime !== undefined && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-4 w-4 text-secondary-600" />
              <span className="text-sm font-medium text-gray-700">运行时长</span>
            </div>
            <div className="text-lg font-bold text-gray-900">
              {formatUptime(monitorData.uptime)}
            </div>
          </div>
        )}
      </div>

      {/* Process Info */}
      {(monitorData.processId || monitorData.threadCount) && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="font-medium text-gray-900 mb-3">进程信息</h5>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {monitorData.processId && (
              <div>
                <span className="text-sm text-gray-500">进程ID</span>
                <div className="font-medium">{monitorData.processId}</div>
              </div>
            )}
            {monitorData.threadCount && (
              <div>
                <span className="text-sm text-gray-500">线程数</span>
                <div className="font-medium">{monitorData.threadCount}</div>
              </div>
            )}
            {monitorData.fileDescriptorCount && (
              <div>
                <span className="text-sm text-gray-500">文件描述符</span>
                <div className="font-medium">{monitorData.fileDescriptorCount}</div>
              </div>
            )}
            {monitorData.responseTime && (
              <div>
                <span className="text-sm text-gray-500">响应时间</span>
                <div className="font-medium">{monitorData.responseTime}ms</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Last Update */}
      <div className="text-sm text-gray-500 text-center">
        最后更新: {new Date(monitorData.updateTime).toLocaleString()}
      </div>
    </div>
  );
}

// Config Tab Component
interface ConfigTabProps {
  config: Record<string, any>;
  loading: boolean;
  onRefresh: () => void;
}

function ConfigTab({ config, loading, onRefresh }: ConfigTabProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">加载配置信息...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-gray-900">配置参数</h4>
        <button onClick={onRefresh} className="btn-outline btn-sm">
          刷新
        </button>
      </div>

      {Object.keys(config).length === 0 ? (
        <div className="text-center py-8">
          <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">暂无配置信息</p>
        </div>
      ) : (
        <div className="bg-gray-50 rounded-lg p-4">
          <pre className="text-sm text-gray-800 whitespace-pre-wrap">
            {JSON.stringify(config, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

// Logs Tab Component
interface LogsTabProps {
  logs: string[];
  loading: boolean;
  onRefresh: () => void;
}

function LogsTab({ logs, loading, onRefresh }: LogsTabProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">加载日志信息...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-gray-900">组件日志</h4>
        <button onClick={onRefresh} className="btn-outline btn-sm">
          刷新
        </button>
      </div>

      {logs.length === 0 ? (
        <div className="text-center py-8">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">暂无日志信息</p>
        </div>
      ) : (
        <div className="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
          <div className="font-mono text-sm text-green-400 space-y-1">
            {logs.map((log, index) => (
              <div key={index} className="whitespace-pre-wrap">
                {log}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
