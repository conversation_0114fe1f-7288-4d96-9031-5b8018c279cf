package com.xhcai.modules.rag.entity;

import java.util.HashMap;
import java.util.Map;

import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库分段配置实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "datasets_segment_config")
@TableName(value = "datasets_segment_config", autoResultMap = true)
@Schema(description = "知识库分段配置")
public class KnowledgeSegmentConfig extends BaseWithTenantIDEntity {

    /**
     * 分段配置
     */
    @Schema(description = "分段配置")
    @Column(name = "segment_config", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    @TableField(value = "segment_config", typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private SegmentConfig segmentConfig;

    /**
     * 清洗配置
     */
    @Schema(description = "清洗配置")
    @Column(name = "cleaning_config", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    @TableField(value = "cleaning_config", typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private CleaningConfig cleaningConfig;

}
