package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Dify 会话列表响应 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify 会话列表响应")
public class DifyConversationListResponseDTO {

    @Schema(description = "会话列表")
    @JsonProperty("data")
    private List<DifyConversationDTO> data;

    @Schema(description = "是否有更多数据")
    @JsonProperty("has_more")
    private Boolean hasMore;

    @Schema(description = "总数量")
    @JsonProperty("total")
    private Integer total;

    public List<DifyConversationDTO> getData() {
        return data;
    }

    public void setData(List<DifyConversationDTO> data) {
        this.data = data;
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "DifyConversationListResponseDTO{" +
                "data=" + data +
                ", hasMore=" + hasMore +
                ", total=" + total +
                '}';
    }
}
