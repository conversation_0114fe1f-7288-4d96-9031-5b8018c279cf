package com.xhcai.plugin.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 模型信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelInfo {
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 模型显示名称
     */
    private String displayName;
    
    /**
     * 模型描述
     */
    private String description;
    
    /**
     * 模型版本
     */
    private String version;
    
    /**
     * 模型类型
     */
    private ModelType modelType;
    
    /**
     * 支持的功能
     */
    private List<ModelCapability> capabilities;
    
    /**
     * 最大上下文长度
     */
    private Integer maxContextLength;
    
    /**
     * 最大输出长度
     */
    private Integer maxOutputLength;
    
    /**
     * 输入价格（每1000 tokens）
     */
    private Double inputPrice;
    
    /**
     * 输出价格（每1000 tokens）
     */
    private Double outputPrice;
    
    /**
     * 模型参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 是否可用
     */
    private Boolean available;
    
    /**
     * 模型提供商
     */
    private String provider;
    
    /**
     * 模型类型枚举
     */
    public enum ModelType {
        TEXT_GENERATION,    // 文本生成
        TEXT_EMBEDDING,     // 文本嵌入
        IMAGE_GENERATION,   // 图像生成
        SPEECH_TO_TEXT,     // 语音转文本
        TEXT_TO_SPEECH,     // 文本转语音
        MULTIMODAL          // 多模态
    }
    
    /**
     * 模型能力枚举
     */
    public enum ModelCapability {
        CHAT,               // 对话
        COMPLETION,         // 补全
        EMBEDDING,          // 嵌入
        IMAGE_GENERATION,   // 图像生成
        IMAGE_UNDERSTANDING,// 图像理解
        SPEECH_TO_TEXT,     // 语音转文本
        TEXT_TO_SPEECH,     // 文本转语音
        FUNCTION_CALLING,   // 函数调用
        CODE_GENERATION,    // 代码生成
        REASONING           // 推理
    }
}
