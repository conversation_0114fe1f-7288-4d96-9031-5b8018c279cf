package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 文件存储配置创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "文件存储配置创建DTO")
public class FileStorageConfigCreateDTO {

    /**
     * 存储配置名称
     */
    @Schema(description = "存储配置名称", example = "默认MinIO存储")
    @NotBlank(message = "存储配置名称不能为空")
    @Size(max = 100, message = "存储配置名称长度不能超过100个字符")
    private String name;

    /**
     * 存储类型
     */
    @Schema(description = "存储类型", example = "minio")
    @NotBlank(message = "存储类型不能为空")
    @Size(max = 50, message = "存储类型长度不能超过50个字符")
    private String storageType;

    /**
     * 主机地址
     */
    @Schema(description = "主机地址", example = "*************")
    @Size(max = 255, message = "主机地址长度不能超过255个字符")
    private String host;

    /**
     * 端口号
     */
    @Schema(description = "端口号", example = "9000")
    private Integer port;

    /**
     * 访问密钥ID
     */
    @Schema(description = "访问密钥ID")
    @Size(max = 255, message = "访问密钥ID长度不能超过255个字符")
    private String accessKey;

    /**
     * 访问密钥Secret
     */
    @Schema(description = "访问密钥Secret")
    @Size(max = 255, message = "访问密钥Secret长度不能超过255个字符")
    private String secretKey;

    /**
     * 存储桶名称/根目录
     */
    @Schema(description = "存储桶名称/根目录", example = "xhcai-files")
    @Size(max = 100, message = "存储桶名称长度不能超过100个字符")
    private String bucketName;

    /**
     * 区域
     */
    @Schema(description = "区域", example = "us-east-1")
    @Size(max = 100, message = "区域长度不能超过100个字符")
    private String region;

    /**
     * 是否启用SSL
     */
    @Schema(description = "是否启用SSL", example = "Y")
    private String sslEnabled = "N";

    /**
     * 自定义域名
     */
    @Schema(description = "自定义域名", example = "files.example.com")
    @Size(max = 255, message = "自定义域名长度不能超过255个字符")
    private String customDomain;

    /**
     * 连接配置JSON
     */
    @Schema(description = "连接配置JSON")
    private String connectionConfig;

    /**
     * 是否为默认存储
     */
    @Schema(description = "是否为默认存储", example = "Y")
    private String isDefault = "N";

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0")
    private String status = "0";

    /**
     * 描述
     */
    @Schema(description = "描述")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
}
