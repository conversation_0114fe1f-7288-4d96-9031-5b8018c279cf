import { ref, computed } from 'vue'

// 定义智能体类型
export interface Agent {
  id: string
  name: string
  description: string
  icon: string
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
}

// 定义运行中智能体的类型
export interface RunningAgent extends Agent {
  isMinimized: boolean
  isMaximized: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
}

// 全局状态
const runningAgents = ref<RunningAgent[]>([])
const activeAgentId = ref<string | null>(null)
const runnerModalVisible = ref(false)
const isMinimized = ref(false)
const isMaximized = ref(false)
const windowPosition = ref({ x: 100, y: 80 })
const windowSize = ref({ width: 1200, height: 800 })

// 事件回调类型
export interface AgentRunnerEvents {
  onAgentOpened?: (agent: Agent) => void
  onAgentClosed?: (agentId: string) => void
  onAgentMinimized?: (agentId: string) => void
  onAgentRestored?: (agentId: string) => void
}

// 全局事件回调
let globalEvents: AgentRunnerEvents = {}

export function useAgentRunner(events?: AgentRunnerEvents) {
  // 合并事件回调
  if (events) {
    globalEvents = { ...globalEvents, ...events }
  }

  // 计算属性
  const currentRunningAgent = computed(() => {
    if (!activeAgentId.value) return null
    return runningAgents.value.find(agent => agent.id === activeAgentId.value) || null
  })

  const minimizedAgents = computed(() => {
    return runningAgents.value.filter(agent => agent.isMinimized)
  })

  const hasMinimizedAgents = computed(() => {
    return minimizedAgents.value.length > 0
  })

  // 方法
  const openRunnerModal = (agent: Agent) => {
    // 检查是否已经在运行
    const existingAgent = runningAgents.value.find(a => a.id === agent.id)
    if (existingAgent) {
      // 如果已经在运行，切换到该智能体
      activeAgentId.value = agent.id
      isMinimized.value = false
      runnerModalVisible.value = true
      globalEvents.onAgentRestored?.(agent.id)
      return
    }

    // 添加到运行列表
    runningAgents.value.push({
      ...agent,
      isMinimized: false,
      isMaximized: true,
      position: { x: 100, y: 80 },
      size: { width: 1200, height: 800 }
    })

    activeAgentId.value = agent.id

    // 重置窗口状态，默认为最大化（全屏）
    isMinimized.value = false
    isMaximized.value = true

    // 设置初始位置和大小（用于还原时使用）
    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight
    windowSize.value = {
      width: Math.min(1200, screenWidth - 200),
      height: Math.min(800, screenHeight - 160)
    }
    windowPosition.value = {
      x: Math.max(50, (screenWidth - windowSize.value.width) / 2),
      y: Math.max(80, (screenHeight - windowSize.value.height) / 2)
    }

    runnerModalVisible.value = true
    globalEvents.onAgentOpened?.(agent)
  }

  const closeRunnerModal = () => {
    if (activeAgentId.value) {
      // 从运行列表中移除
      const agentId = activeAgentId.value
      runningAgents.value = runningAgents.value.filter(agent => agent.id !== agentId)
      globalEvents.onAgentClosed?.(agentId)
      activeAgentId.value = null
    }

    runnerModalVisible.value = false
    isMinimized.value = false
    isMaximized.value = false
  }

  const minimizeRunner = () => {
    if (activeAgentId.value) {
      // 更新运行列表中的状态
      const agent = runningAgents.value.find(a => a.id === activeAgentId.value)
      if (agent) {
        agent.isMinimized = true
        agent.position = { ...windowPosition.value }
        agent.size = { ...windowSize.value }
        agent.isMaximized = isMaximized.value
      }
      globalEvents.onAgentMinimized?.(activeAgentId.value)
    }
    isMinimized.value = true
  }

  const restoreRunner = (agentId: string) => {
    const agent = runningAgents.value.find(a => a.id === agentId)
    if (!agent) return

    // 切换到目标智能体
    activeAgentId.value = agentId

    // 恢复窗口状态
    isMinimized.value = false
    isMaximized.value = agent.isMaximized
    windowPosition.value = { ...agent.position }
    windowSize.value = { ...agent.size }

    // 更新运行列表状态
    agent.isMinimized = false

    runnerModalVisible.value = true
    globalEvents.onAgentRestored?.(agentId)
  }

  const closeSpecificAgent = (agentId: string) => {
    runningAgents.value = runningAgents.value.filter(agent => agent.id !== agentId)

    // 如果关闭的是当前活跃的智能体
    if (activeAgentId.value === agentId) {
      runnerModalVisible.value = false
      activeAgentId.value = null
      isMinimized.value = false
      isMaximized.value = false
    }

    globalEvents.onAgentClosed?.(agentId)
  }

  const updatePosition = (position: { x: number; y: number }) => {
    windowPosition.value = position
  }

  const updateSize = (size: { width: number; height: number }) => {
    windowSize.value = size
  }

  const updateMaximized = (maximized: boolean) => {
    isMaximized.value = maximized
  }

  // 获取指定智能体的运行状态
  const getAgentRunningState = (agentId: string) => {
    return runningAgents.value.find(agent => agent.id === agentId)
  }

  // 检查智能体是否正在运行
  const isAgentRunning = (agentId: string) => {
    return runningAgents.value.some(agent => agent.id === agentId)
  }

  // 获取所有运行中的智能体ID
  const getRunningAgentIds = () => {
    return runningAgents.value.map(agent => agent.id)
  }

  return {
    // 状态
    runningAgents: runningAgents.value,
    activeAgentId,
    runnerModalVisible,
    isMinimized,
    isMaximized,
    windowPosition,
    windowSize,
    
    // 计算属性
    currentRunningAgent,
    minimizedAgents,
    hasMinimizedAgents,
    
    // 方法
    openRunnerModal,
    closeRunnerModal,
    minimizeRunner,
    restoreRunner,
    closeSpecificAgent,
    updatePosition,
    updateSize,
    updateMaximized,
    getAgentRunningState,
    isAgentRunning,
    getRunningAgentIds
  }
}
