package com.xhcai.modules.rag.enums;

/**
 * 权限类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PermissionType {
    
    /**
     * 只读权限
     */
    READ("1", "只读"),
    
    /**
     * 读写权限
     */
    READ_WRITE("2", "读写"),
    
    /**
     * 管理权限
     */
    MANAGE("3", "管理");

    private final String code;
    private final String description;

    PermissionType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 权限类型代码
     * @return 权限类型枚举
     */
    public static PermissionType fromCode(String code) {
        for (PermissionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的权限类型代码: " + code);
    }

    /**
     * 判断是否包含读权限
     *
     * @return 是否包含读权限
     */
    public boolean canRead() {
        return this == READ || this == READ_WRITE || this == MANAGE;
    }

    /**
     * 判断是否包含写权限
     *
     * @return 是否包含写权限
     */
    public boolean canWrite() {
        return this == READ_WRITE || this == MANAGE;
    }

    /**
     * 判断是否包含管理权限
     *
     * @return 是否包含管理权限
     */
    public boolean canManage() {
        return this == MANAGE;
    }
}
