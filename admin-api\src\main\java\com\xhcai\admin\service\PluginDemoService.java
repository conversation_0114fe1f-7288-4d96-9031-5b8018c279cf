package com.xhcai.admin.service;

import com.xhcai.plugin.model.IModelService;
import com.xhcai.plugin.model.ModelRequest;
import com.xhcai.plugin.model.ModelResponse;
import com.xhcai.plugin.service.PluginServiceManager;
import com.xhcai.plugin.storage.IStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 插件使用示例服务 演示如何在业务代码中使用插件服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PluginDemoService {

    private final PluginServiceManager pluginServiceManager;

    /**
     * 文件上传示例
     */
    public String uploadFile(MultipartFile file, String bucketName) {
        try {
            // 获取默认存储服务
            IStorageService storageService = pluginServiceManager.getDefaultStorageService();

            // 生成对象名称
            String objectName = System.currentTimeMillis() + "_" + file.getOriginalFilename();

            // 上传文件
            String url = storageService.uploadFile(
                    bucketName,
                    objectName,
                    file.getInputStream(),
                    file.getContentType()
            );

            log.info("File uploaded successfully: {}", url);
            return url;

        } catch (Exception e) {
            log.error("Failed to upload file", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 使用指定存储服务上传文件
     */
    public String uploadFileWithService(MultipartFile file, String bucketName, String serviceType) {
        try {
            // 获取指定类型的存储服务
            IStorageService storageService = pluginServiceManager.getStorageServiceByType(serviceType);
            if (storageService == null) {
                throw new RuntimeException("存储服务不可用: " + serviceType);
            }

            // 检查服务健康状态
            if (!storageService.isHealthy()) {
                throw new RuntimeException("存储服务不健康: " + serviceType);
            }

            String objectName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
            String url = storageService.uploadFile(
                    bucketName,
                    objectName,
                    file.getInputStream(),
                    file.getContentType()
            );

            log.info("File uploaded with service {}: {}", serviceType, url);
            return url;

        } catch (Exception e) {
            log.error("Failed to upload file with service: {}", serviceType, e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * AI 文本生成示例
     */
    public String generateText(String prompt, String model) {
        try {
            // 获取默认模型服务
            IModelService modelService = pluginServiceManager.getDefaultModelService();

            // 检查模型是否可用
            if (!modelService.isModelAvailable(model)) {
                throw new RuntimeException("模型不可用: " + model);
            }

            // 构建请求
            ModelRequest request = ModelRequest.builder()
                    .model(model)
                    .prompt(prompt)
                    .maxTokens(1000)
                    .temperature(0.7)
                    .build();

            // 生成文本
            ModelResponse response = modelService.generateText(request);

            if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                String content = response.getChoices().get(0).getMessage().getContent();
                log.info("Text generated successfully for model: {}", model);
                return content;
            } else {
                throw new RuntimeException("生成结果为空");
            }

        } catch (Exception e) {
            log.error("Failed to generate text with model: {}", model, e);
            throw new RuntimeException("文本生成失败: " + e.getMessage());
        }
    }

    /**
     * 使用指定模型服务生成文本
     */
    public String generateTextWithService(String prompt, String model, String serviceType) {
        try {
            // 获取指定类型的模型服务
            IModelService modelService = pluginServiceManager.getModelServiceByType(serviceType);
            if (modelService == null) {
                throw new RuntimeException("模型服务不可用: " + serviceType);
            }

            // 检查服务健康状态
            if (!modelService.isHealthy()) {
                throw new RuntimeException("模型服务不健康: " + serviceType);
            }

            // 检查模型是否可用
            if (!modelService.isModelAvailable(model)) {
                throw new RuntimeException("模型不可用: " + model);
            }

            ModelRequest request = ModelRequest.builder()
                    .model(model)
                    .prompt(prompt)
                    .maxTokens(1000)
                    .temperature(0.7)
                    .build();

            ModelResponse response = modelService.generateText(request);

            if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                String content = response.getChoices().get(0).getMessage().getContent();
                log.info("Text generated with service {}: {}", serviceType, model);
                return content;
            } else {
                throw new RuntimeException("生成结果为空");
            }

        } catch (Exception e) {
            log.error("Failed to generate text with service: {} - {}", serviceType, model, e);
            throw new RuntimeException("文本生成失败: " + e.getMessage());
        }
    }

    /**
     * 文本嵌入示例
     */
    public List<List<Double>> generateEmbeddings(List<String> texts, String model) {
        try {
            // 获取支持指定模型的服务
            IModelService modelService = pluginServiceManager.getModelServiceForModel(model);
            if (modelService == null) {
                throw new RuntimeException("没有找到支持模型的服务: " + model);
            }

            // 生成嵌入
            List<List<Double>> embeddings = modelService.generateEmbeddings(texts, model);
            log.info("Embeddings generated for {} texts with model: {}", texts.size(), model);
            return embeddings;

        } catch (Exception e) {
            log.error("Failed to generate embeddings with model: {}", model, e);
            throw new RuntimeException("嵌入生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有可用的存储服务信息
     */
    public List<Map<String, Object>> getAvailableStorageServices() {
        try {
            var services = pluginServiceManager.getStorageServices();
            return services.stream()
                    .map(service -> Map.<String, Object>of(
                    "name", service.getServiceName(),
                    "type", service.getServiceType(),
                    "healthy", service.isHealthy()
            ))
                    .toList();
        } catch (Exception e) {
            log.error("Failed to get available storage services", e);
            throw new RuntimeException("获取存储服务失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有可用的模型服务信息
     */
    public List<Map<String, Object>> getAvailableModelServices() {
        try {
            var services = pluginServiceManager.getModelServices();
            return services.stream()
                    .map(service -> {
                        Map<String, Object> info = Map.<String, Object>of(
                                "name", service.getServiceName(),
                                "type", service.getServiceType(),
                                "healthy", service.isHealthy(),
                                "supportedModels", service.getSupportedModels().stream()
                                        .map(model -> Map.<String, Object>of(
                                        "name", model.getModelName(),
                                        "displayName", model.getDisplayName(),
                                        "type", model.getModelType(),
                                        "available", model.getAvailable()
                                ))
                                        .toList()
                        );
                        return info;
                    })
                    .toList();
        } catch (Exception e) {
            log.error("Failed to get available model services", e);
            throw new RuntimeException("获取模型服务失败: " + e.getMessage());
        }
    }
}
