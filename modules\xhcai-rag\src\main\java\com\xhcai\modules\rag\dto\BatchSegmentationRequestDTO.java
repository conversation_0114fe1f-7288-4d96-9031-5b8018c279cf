package com.xhcai.modules.rag.dto;

import com.xhcai.modules.rag.entity.inner.FileCleanSegmentConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 批量分段处理请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "批量分段处理请求DTO")
@Data
public class BatchSegmentationRequestDTO {

    /**
     * 文档ID列表
     */
    @Schema(description = "文档ID列表", example = "[\"doc1\", \"doc2\"]")
    @NotEmpty(message = "文档ID列表不能为空")
    private List<String> documentIds;

    /**
     * 文档元数据映射 (documentId -> docMetadata)
     */
    @Schema(description = "文档元数据映射")
    @NotNull(message = "文档元数据不能为空")
    private Map<String, FileCleanSegmentConfig> docConfigs;

    /**
     * 分段配置
     */
    @Schema(description = "分段配置")
    private SegmentConfig segmentationConfig;

}
