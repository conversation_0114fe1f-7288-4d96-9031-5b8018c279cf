package com.xhcai.modules.rag.plugins.ftp;

import com.xhcai.plugin.storage.IStorageService;
import com.xhcai.plugin.storage.StorageFileInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * FTP存储服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class FtpStorageService implements IStorageService {

    private String host;
    private int port = 21;
    private String username;
    private String password;
    private String basePath = "/";
    private boolean initialized = false;

    @Override
    public String getServiceName() {
        return "FTP存储服务";
    }

    @Override
    public String getServiceType() {
        return "ftp";
    }

    @Override
    public void initialize(Map<String, Object> config) {
        try {
            this.host = (String) config.get("host");
            this.port = config.get("port") != null ? (Integer) config.get("port") : 21;
            this.username = (String) config.get("username");
            this.password = (String) config.get("password");
            this.basePath = config.get("basePath") != null ? (String) config.get("basePath") : "/";

            if (host == null || username == null || password == null) {
                throw new IllegalArgumentException("FTP配置不完整");
            }

            // 测试连接
            testConnection();
            this.initialized = true;
            log.info("FTP存储服务初始化成功: {}:{}", host, port);

        } catch (Exception e) {
            log.error("FTP存储服务初始化失败", e);
            throw new RuntimeException("FTP存储服务初始化失败", e);
        }
    }

    @Override
    public String uploadFile(String bucketName, String objectName, InputStream inputStream, String contentType) {
        return uploadFile(bucketName, objectName, inputStream, contentType, null);
    }

    @Override
    public String uploadFile(String bucketName, String objectName, InputStream inputStream, 
                           String contentType, Map<String, String> metadata) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        try {
            // 构建完整路径
            String fullPath = buildPath(bucketName, objectName);
            String directory = getDirectoryPath(fullPath);
            String fileName = getFileName(fullPath);

            // 创建目录
            createDirectories(ftpClient, directory);

            // 切换到目标目录
            ftpClient.changeWorkingDirectory(directory);

            // 设置文件传输模式
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);

            // 上传文件
            boolean success = ftpClient.storeFile(fileName, inputStream);
            if (!success) {
                throw new RuntimeException("FTP文件上传失败");
            }

            log.info("FTP文件上传成功: {}", fullPath);
            return "ftp://" + host + ":" + port + fullPath;

        } catch (Exception e) {
            log.error("FTP文件上传失败: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("FTP文件上传失败", e);
        } finally {
            closeFtpClient(ftpClient);
            closeInputStream(inputStream);
        }
    }

    @Override
    public InputStream downloadFile(String bucketName, String objectName) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        try {
            String fullPath = buildPath(bucketName, objectName);
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            
            InputStream inputStream = ftpClient.retrieveFileStream(fullPath);
            if (inputStream == null) {
                throw new RuntimeException("文件不存在或下载失败: " + fullPath);
            }
            
            return inputStream;

        } catch (Exception e) {
            log.error("FTP文件下载失败: {}/{}", bucketName, objectName, e);
            closeFtpClient(ftpClient);
            throw new RuntimeException("FTP文件下载失败", e);
        }
    }

    @Override
    public boolean deleteFile(String bucketName, String objectName) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        try {
            String fullPath = buildPath(bucketName, objectName);
            boolean success = ftpClient.deleteFile(fullPath);
            
            if (success) {
                log.info("FTP文件删除成功: {}", fullPath);
            } else {
                log.warn("FTP文件删除失败: {}", fullPath);
            }
            
            return success;

        } catch (Exception e) {
            log.error("FTP文件删除失败: {}/{}", bucketName, objectName, e);
            return false;
        } finally {
            closeFtpClient(ftpClient);
        }
    }

    @Override
    public Map<String, Boolean> deleteFiles(String bucketName, List<String> objectNames) {
        Map<String, Boolean> results = new HashMap<>();
        
        for (String objectName : objectNames) {
            boolean success = deleteFile(bucketName, objectName);
            results.put(objectName, success);
        }
        
        return results;
    }

    @Override
    public List<StorageFileInfo> listFiles(String bucketName, String prefix, int maxKeys) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        List<StorageFileInfo> fileInfos = new ArrayList<>();
        
        try {
            String directory = buildPath(bucketName, prefix != null ? prefix : "");
            FTPFile[] files = ftpClient.listFiles(directory);
            
            for (FTPFile file : files) {
                if (file.isFile()) {
                    StorageFileInfo fileInfo = StorageFileInfo.builder()
                            .bucketName(bucketName)
                            .objectName(prefix != null ? prefix + "/" + file.getName() : file.getName())
                            .size(file.getSize())
                            .lastModified(LocalDateTime.ofInstant(
                                    file.getTimestamp().toInstant(), 
                                    ZoneId.systemDefault()))
                            .isDirectory(false)
                            .build();
                    fileInfos.add(fileInfo);
                }
            }
            
            return fileInfos;

        } catch (Exception e) {
            log.error("FTP文件列表获取失败: {}/{}", bucketName, prefix, e);
            throw new RuntimeException("FTP文件列表获取失败", e);
        } finally {
            closeFtpClient(ftpClient);
        }
    }

    @Override
    public StorageFileInfo getFileInfo(String bucketName, String objectName) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        try {
            String fullPath = buildPath(bucketName, objectName);
            FTPFile[] files = ftpClient.listFiles(fullPath);
            
            if (files.length > 0) {
                FTPFile file = files[0];
                return StorageFileInfo.builder()
                        .bucketName(bucketName)
                        .objectName(objectName)
                        .size(file.getSize())
                        .lastModified(LocalDateTime.ofInstant(
                                file.getTimestamp().toInstant(), 
                                ZoneId.systemDefault()))
                        .isDirectory(file.isDirectory())
                        .build();
            }
            
            return null;

        } catch (Exception e) {
            log.error("FTP文件信息获取失败: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("FTP文件信息获取失败", e);
        } finally {
            closeFtpClient(ftpClient);
        }
    }

    @Override
    public boolean bucketExists(String bucketName) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        try {
            String bucketPath = basePath + "/" + bucketName;
            return ftpClient.changeWorkingDirectory(bucketPath);
        } catch (Exception e) {
            log.error("检查FTP目录存在性失败: {}", bucketName, e);
            return false;
        } finally {
            closeFtpClient(ftpClient);
        }
    }

    @Override
    public boolean createBucket(String bucketName) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        try {
            String bucketPath = basePath + "/" + bucketName;
            boolean success = ftpClient.makeDirectory(bucketPath);
            
            if (success) {
                log.info("FTP目录创建成功: {}", bucketPath);
            }
            
            return success;
        } catch (Exception e) {
            log.error("FTP目录创建失败: {}", bucketName, e);
            return false;
        } finally {
            closeFtpClient(ftpClient);
        }
    }

    @Override
    public boolean deleteBucket(String bucketName) {
        checkInitialized();

        FTPClient ftpClient = createFtpClient();
        try {
            String bucketPath = basePath + "/" + bucketName;
            boolean success = ftpClient.removeDirectory(bucketPath);
            
            if (success) {
                log.info("FTP目录删除成功: {}", bucketPath);
            }
            
            return success;
        } catch (Exception e) {
            log.error("FTP目录删除失败: {}", bucketName, e);
            return false;
        } finally {
            closeFtpClient(ftpClient);
        }
    }

    @Override
    public boolean isHealthy() {
        if (!initialized) {
            return false;
        }

        try {
            testConnection();
            return true;
        } catch (Exception e) {
            log.error("FTP健康检查失败", e);
            return false;
        }
    }

    @Override
    public boolean fileExists(String bucketName, String objectName) {
        return false;
    }

    @Override
    public String generatePresignedUrl(String bucketName, String objectName, int expireSeconds) {
        return "";
    }

    /**
     * 测试连接
     */
    private void testConnection() throws IOException {
        FTPClient ftpClient = createFtpClient();
        try {
            // 连接测试成功
            log.debug("FTP连接测试成功");
        } finally {
            closeFtpClient(ftpClient);
        }
    }

    /**
     * 创建FTP客户端并连接
     */
    private FTPClient createFtpClient() {
        FTPClient ftpClient = new FTPClient();
        try {
            ftpClient.connect(host, port);
            ftpClient.login(username, password);
            ftpClient.enterLocalPassiveMode();
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            return ftpClient;
        } catch (Exception e) {
            try {
                ftpClient.disconnect();
            } catch (IOException ex) {
                // 忽略断开连接异常
            }
            throw new RuntimeException("FTP连接失败", e);
        }
    }

    /**
     * 关闭FTP客户端
     */
    private void closeFtpClient(FTPClient ftpClient) {
        if (ftpClient != null && ftpClient.isConnected()) {
            try {
                ftpClient.logout();
                ftpClient.disconnect();
            } catch (IOException e) {
                log.warn("关闭FTP连接失败", e);
            }
        }
    }

    /**
     * 关闭输入流
     */
    private void closeInputStream(InputStream inputStream) {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.warn("关闭输入流失败", e);
            }
        }
    }

    /**
     * 构建完整路径
     */
    private String buildPath(String bucketName, String objectName) {
        return basePath + "/" + bucketName + "/" + objectName;
    }

    /**
     * 获取目录路径
     */
    private String getDirectoryPath(String fullPath) {
        int lastSlash = fullPath.lastIndexOf('/');
        return lastSlash > 0 ? fullPath.substring(0, lastSlash) : "/";
    }

    /**
     * 获取文件名
     */
    private String getFileName(String fullPath) {
        int lastSlash = fullPath.lastIndexOf('/');
        return lastSlash >= 0 ? fullPath.substring(lastSlash + 1) : fullPath;
    }

    /**
     * 创建目录（递归）
     */
    private void createDirectories(FTPClient ftpClient, String directory) throws IOException {
        String[] dirs = directory.split("/");
        String currentDir = "";
        
        for (String dir : dirs) {
            if (!dir.isEmpty()) {
                currentDir += "/" + dir;
                if (!ftpClient.changeWorkingDirectory(currentDir)) {
                    ftpClient.makeDirectory(currentDir);
                }
            }
        }
    }

    /**
     * 检查是否已初始化
     */
    private void checkInitialized() {
        if (!initialized) {
            throw new IllegalStateException("FTP存储服务未初始化");
        }
    }
}
