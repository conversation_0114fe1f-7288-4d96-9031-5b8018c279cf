package com.xhcai.common.api.query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询条件构建器
 * 用于动态构建SQL查询条件
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class QueryConditionBuilder {

    private final List<String> conditions = new ArrayList<>();
    private final Map<String, Object> parameters = new HashMap<>();

    /**
     * 添加分页条件
     * 
     * @param pageable 分页查询对象
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addPageCondition(Pageable pageable) {
        if (pageable != null) {
            // 分页条件通常在Service层处理，这里主要记录排序条件
            if (pageable.getOrderBy() != null && !pageable.getOrderBy().trim().isEmpty()) {
                // 排序条件需要在具体的Mapper中处理，这里只做记录
                parameters.put("orderBy", pageable.getOrderBy());
                parameters.put("orderDirection", pageable.getOrderDirection());
            }
        }
        return this;
    }

    /**
     * 添加时间范围条件
     * 
     * @param timeRangeable 时间范围查询对象
     * @param timeField 时间字段名
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addTimeRangeCondition(TimeRangeable timeRangeable, String timeField) {
        if (timeRangeable != null && timeField != null) {
            if (timeRangeable.getBeginTime() != null && !timeRangeable.getBeginTime().trim().isEmpty()) {
                conditions.add(timeField + " >= #{beginTime}");
                parameters.put("beginTime", timeRangeable.getBeginTime());
            }
            if (timeRangeable.getEndTime() != null && !timeRangeable.getEndTime().trim().isEmpty()) {
                conditions.add(timeField + " <= #{endTime}");
                parameters.put("endTime", timeRangeable.getEndTime());
            }
        }
        return this;
    }

    /**
     * 添加状态条件
     * 
     * @param statusable 状态查询对象
     * @param statusField 状态字段名
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addStatusCondition(Statusable statusable, String statusField) {
        if (statusable != null && statusable.hasStatus() && statusField != null) {
            conditions.add(statusField + " = #{status}");
            parameters.put("status", statusable.getStatus());
        }
        return this;
    }

    /**
     * 添加关键字搜索条件
     * 
     * @param keywordable 关键字查询对象
     * @param searchFields 搜索字段列表
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addKeywordCondition(Keywordable keywordable, String... searchFields) {
        if (keywordable != null && keywordable.hasKeyword() && searchFields != null && searchFields.length > 0) {
            List<String> keywordConditions = new ArrayList<>();
            for (String field : searchFields) {
                keywordConditions.add(field + " LIKE #{keyword}");
            }
            if (!keywordConditions.isEmpty()) {
                conditions.add("(" + String.join(" OR ", keywordConditions) + ")");
                parameters.put("keyword", keywordable.getLikeKeyword());
            }
        }
        return this;
    }

    /**
     * 添加数据权限条件
     * 
     * @param dataScopeable 数据权限查询对象
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addDataScopeCondition(DataScopeable dataScopeable) {
        if (dataScopeable != null) {
            if (dataScopeable.hasTenant()) {
                conditions.add("tenant_id = #{tenantId}");
                parameters.put("tenantId", dataScopeable.getTenantId());
            }
            if (dataScopeable.hasDataScope()) {
                conditions.add(dataScopeable.getDataScope());
            }
        }
        return this;
    }

    /**
     * 添加自定义条件
     * 
     * @param condition SQL条件
     * @param paramName 参数名
     * @param paramValue 参数值
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addCustomCondition(String condition, String paramName, Object paramValue) {
        if (condition != null && !condition.trim().isEmpty()) {
            conditions.add(condition);
            if (paramName != null && paramValue != null) {
                parameters.put(paramName, paramValue);
            }
        }
        return this;
    }

    /**
     * 添加IN条件
     * 
     * @param field 字段名
     * @param paramName 参数名
     * @param values 值列表
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addInCondition(String field, String paramName, List<?> values) {
        if (field != null && paramName != null && values != null && !values.isEmpty()) {
            conditions.add(field + " IN (#{" + paramName + "})");
            parameters.put(paramName, values);
        }
        return this;
    }

    /**
     * 添加等值条件
     * 
     * @param field 字段名
     * @param paramName 参数名
     * @param value 值
     * @return 当前构建器实例
     */
    public QueryConditionBuilder addEqualCondition(String field, String paramName, Object value) {
        if (field != null && paramName != null && value != null) {
            conditions.add(field + " = #{" + paramName + "}");
            parameters.put(paramName, value);
        }
        return this;
    }

    /**
     * 构建WHERE子句
     * 
     * @return WHERE子句（不包含WHERE关键字）
     */
    public String buildWhereClause() {
        if (conditions.isEmpty()) {
            return "";
        }
        return String.join(" AND ", conditions);
    }

    /**
     * 构建完整的WHERE子句
     * 
     * @return 完整的WHERE子句（包含WHERE关键字）
     */
    public String buildFullWhereClause() {
        String whereClause = buildWhereClause();
        return whereClause.isEmpty() ? "" : "WHERE " + whereClause;
    }

    /**
     * 获取参数Map
     * 
     * @return 参数Map
     */
    public Map<String, Object> getParameters() {
        return new HashMap<>(parameters);
    }

    /**
     * 获取条件列表
     * 
     * @return 条件列表
     */
    public List<String> getConditions() {
        return new ArrayList<>(conditions);
    }

    /**
     * 清空所有条件和参数
     * 
     * @return 当前构建器实例
     */
    public QueryConditionBuilder clear() {
        conditions.clear();
        parameters.clear();
        return this;
    }

    /**
     * 判断是否有条件
     * 
     * @return true-有条件，false-无条件
     */
    public boolean hasConditions() {
        return !conditions.isEmpty();
    }

    /**
     * 获取条件数量
     * 
     * @return 条件数量
     */
    public int getConditionCount() {
        return conditions.size();
    }
}
