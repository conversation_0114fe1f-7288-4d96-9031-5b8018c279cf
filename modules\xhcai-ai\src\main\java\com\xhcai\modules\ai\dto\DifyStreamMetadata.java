package com.xhcai.modules.ai.dto;

/**
 * Dify流式响应元数据
 * 用于收集流式响应过程中的关键信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DifyStreamMetadata {

    /**
     * Dify消息ID
     */
    private String messageId;

    /**
     * Dify对话ID
     */
    private String conversationId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 输入token数量
     */
    private Integer promptTokens;

    /**
     * 输出token数量
     */
    private Integer completionTokens;

    /**
     * 总token数量
     */
    private Integer totalTokens;

    /**
     * 创建时间
     */
    private Long createdAt;

    /**
     * 事件类型
     */
    private String event;

    /**
     * 答案内容
     */
    private String answer;

    /**
     * 提问
     */
    private String query;

    /**
     * 上一个聊天记录ID
     */
    private String parentMessageId;

    private String agentId;


    /**
     * 来源变量选择器
     */
    private String fromVariableSelector;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getPromptTokens() {
        return promptTokens;
    }

    public void setPromptTokens(Integer promptTokens) {
        this.promptTokens = promptTokens;
    }

    public Integer getCompletionTokens() {
        return completionTokens;
    }

    public void setCompletionTokens(Integer completionTokens) {
        this.completionTokens = completionTokens;
    }

    public Integer getTotalTokens() {
        return totalTokens;
    }

    public void setTotalTokens(Integer totalTokens) {
        this.totalTokens = totalTokens;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getQuery() {return query;}

    public void setQuery(String query) {this.query = query;}

    public String getParentMessageId() {return parentMessageId;}

    public void setParentMessageId(String parentMessageId) {this.parentMessageId = parentMessageId;}

    public String getAgentId() {return agentId;}

    public void setAgentId(String agentId) {this.agentId = agentId;}

    public String getFromVariableSelector() {
        return fromVariableSelector;
    }

    public void setFromVariableSelector(String fromVariableSelector) {
        this.fromVariableSelector = fromVariableSelector;
    }

    @Override
    public String toString() {
        return "DifyStreamMetadata{" +
                "messageId='" + messageId + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", taskId='" + taskId + '\'' +
                ", promptTokens=" + promptTokens +
                ", completionTokens=" + completionTokens +
                ", totalTokens=" + totalTokens +
                ", createdAt=" + createdAt +
                ", event='" + event + '\'' +
                ", answer='" + answer + '\'' +
                ", fromVariableSelector='" + fromVariableSelector + '\'' +
                '}';
    }
}
