<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="condition-node-content">
        <div class="condition-display" v-if="data.config?.condition">
          <code>{{ data.config.condition }}</code>
        </div>
        <p class="node-description">{{ nodeConfig.description }}</p>
        <div class="condition-config" v-if="data.config">
          <div class="config-row" v-if="data.config.operator">
            <span class="config-label">操作符:</span>
            <span class="config-value">{{ data.config.operator }}</span>
          </div>
          <div class="config-row" v-if="data.config.value !== undefined">
            <span class="config-label">比较值:</span>
            <span class="config-value">{{ data.config.value }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { getNodeByType } from '../../config/nodeLibrary'

// Props
interface ConditionNodeData {
  label?: string
  description?: string
  config?: {
    condition?: string
    operator?: string
    value?: any
  }
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: ConditionNodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 获取节点配置
const nodeConfig = computed(() => {
  return getNodeByType('condition') || {
    description: '根据条件进行分支判断'
  }
})
</script>

<style scoped>
.condition-node-content {
  text-align: center;
}

.condition-display {
  background: #fef3c7;
  border: 1px solid #fde68a;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 12px;
}

.condition-display code {
  color: #92400e;
  font-size: 12px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  word-break: break-all;
  font-weight: 500;
}

.node-description {
  font-size: 13px;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.5;
  font-weight: 400;
}

.condition-config {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: left;
  margin-top: 8px;
}

.config-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.config-row:last-child {
  border-bottom: none;
}

.config-label {
  font-weight: 600;
  color: #1f2937;
  flex-shrink: 0;
}

.config-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #fef3c7;
  color: #78350f;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #fde68a;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}
</style>
