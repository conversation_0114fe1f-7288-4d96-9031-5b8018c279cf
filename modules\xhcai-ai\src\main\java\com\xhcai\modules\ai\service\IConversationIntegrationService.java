package com.xhcai.modules.ai.service;

import com.xhcai.modules.ai.dto.AiChatRequest;
import com.xhcai.modules.ai.dto.ConversationInfo;
import com.xhcai.modules.ai.dto.DifyStreamMetadata;
import com.xhcai.modules.ai.entity.AiChatRecord;

/**
 * 会话集成服务接口
 * 用于整合AI聊天记录和Agent会话系统
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IConversationIntegrationService {

    /**
     * 创建或获取Agent会话记录
     * 基于AI聊天请求创建对应的Agent会话和消息记录
     *
     * @return Agent会话ID
     */
    String createOrGetAgentConversation(ConversationInfo conversation);

    String updateAgentConversation(ConversationInfo conversation);

    /**
     * 保存用户消息到Agent消息表
     *
     * @param conversationId Agent会话ID
     * @param request AI聊天请求
     * @return Agent消息ID
     */
//    String saveUserMessage(String conversationId, AiChatRequest request);

    /**
     * 保存AI回复消息到Agent消息表
     *
     * @param conversationId Agent会话ID
     * @param aiResponse AI回复内容
     * @param chatRecord AI聊天记录
     * @return Agent消息ID
     */
    String saveAssistantMessage(String conversationId, String aiResponse, AiChatRecord chatRecord);

    /**
     * 更新Agent会话统计信息
     *
     * @param conversationId Agent会话ID
     * @param chatRecord AI聊天记录
     */
    void updateConversationStats(String conversationId, AiChatRecord chatRecord);

    /**
     * 根据sessionId和appId查找Agent会话
     *
     * @param sessionId 会话ID
     * @return Agent会话ID，如果不存在返回null
     */
    String findAgentConversationId(String sessionId);

    /**
     * 同步AI聊天记录到Agent系统
     * 将现有的AI聊天记录同步到Agent消息表
     *
     * @param chatRecord AI聊天记录
     */
    void syncChatRecordToAgent(AiChatRecord chatRecord);

    /**
     * 获取对话中的下一个序号
     *
     * @param conversationId 对话ID
     * @return 下一个序号
     */
    Integer getNextSequenceNumber(String conversationId);

    /**
     * 保存用户消息到Agent消息表
     *
     * @param conversationId Agent会话ID
     * @param message 用户消息内容
     * @param sequenceNumber 消息序号
     * @param messageId 消息ID
     * @return Agent消息ID
     */
    String saveUserMessage(String conversationId, String message, Integer sequenceNumber, String messageId);

    /**
     * 保存错误消息到Agent消息表
     *
     * @param conversationId Agent会话ID
     * @param errorMessage 错误消息内容
     * @param sequenceNumber 消息序号
     * @param messageId 消息ID
     * @param parentMessageId 父消息ID
     * @param costTime 处理时间
     * @return Agent消息ID
     */
    String saveErrorMessage(String conversationId, String errorMessage, Integer sequenceNumber,
                           String messageId, String parentMessageId, long costTime);

    /**
     * 创建聊天消息记录（问答合并模式）
     * 用户提问存到query字段，等待AI回复后更新content字段
     *
     * @param conversationId Agent会话ID
     * @param userMessage 用户消息内容
     * @param requestDTO 请求DTO（用于获取inputs等信息）
     * @param sequenceNumber 消息序号
     * @param messageId 消息ID
     * @return Agent消息ID
     */
    String createChatMessageRecord(String conversationId, String userMessage, Object requestDTO,
                                  Integer sequenceNumber, String messageId);

    /**
     * 更新聊天消息记录的AI回复内容
     *
     * @param messageId 消息ID
     * @param aiResponse AI回复内容
     * @param costTime 处理时间
     * @param errorMessage 错误消息（可选）
     */
    void updateChatMessageWithResponse(String messageId, String aiResponse, long costTime, String errorMessage);

    /**
     * 更新聊天消息记录的AI回复内容（包含Dify元数据）
     *
     * @param aiResponse AI回复内容
     * @param costTime 处理时间
     * @param errorMessage 错误消息（可选）
     * @param metadata Dify流式响应元数据
     */
    void updateChatMessageWithDifyResponse( String aiResponse, long costTime,
                                          String errorMessage, DifyStreamMetadata metadata);
}
