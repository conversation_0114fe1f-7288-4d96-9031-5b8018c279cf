import apiClient, { ApiResponse } from './client';

/**
 * 设置管理API
 */
export class SettingsAPI {
  /**
   * 获取Agent节点信息
   */
  static async getAgentInfo(): Promise<ApiResponse<any>> {
    return apiClient.get('/api/settings/agent-info');
  }

  /**
   * 更新Agent节点信息
   */
  static async updateAgentInfo(agentInfo: any): Promise<ApiResponse<void>> {
    return apiClient.put('/api/settings/agent-info', agentInfo);
  }

  /**
   * 获取监听任务列表
   */
  static async getMonitorTasks(): Promise<ApiResponse<any[]>> {
    return apiClient.get('/api/settings/monitor-tasks');
  }

  /**
   * 创建监听任务
   */
  static async createMonitorTask(taskInfo: any): Promise<ApiResponse<void>> {
    return apiClient.post('/api/settings/monitor-tasks', taskInfo);
  }

  /**
   * 更新监听任务
   */
  static async updateMonitorTask(taskId: string, taskInfo: any): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/settings/monitor-tasks/${taskId}`, taskInfo);
  }

  /**
   * 删除监听任务
   */
  static async deleteMonitorTask(taskId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/settings/monitor-tasks/${taskId}`);
  }

  /**
   * 切换监听任务状态
   */
  static async toggleMonitorTask(taskId: string, enabled: boolean): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/settings/monitor-tasks/${taskId}/toggle?enabled=${enabled}`);
  }

  /**
   * 获取访问密钥列表
   */
  static async getAccessKeys(): Promise<ApiResponse<any[]>> {
    return apiClient.get('/api/settings/access-keys');
  }

  /**
   * 生成访问密钥
   */
  static async generateAccessKey(keyInfo: any): Promise<ApiResponse<any>> {
    return apiClient.post('/api/settings/access-keys', keyInfo);
  }

  /**
   * 更新访问密钥
   */
  static async updateAccessKey(keyId: string, keyInfo: any): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/settings/access-keys/${keyId}`, keyInfo);
  }

  /**
   * 删除访问密钥
   */
  static async deleteAccessKey(keyId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/settings/access-keys/${keyId}`);
  }
}

export default SettingsAPI;
