package com.xhcai.modules.rag.service.impl;

import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.config.OnlyOfficeProperties;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.service.IDocumentService;
import com.xhcai.modules.rag.service.IOnlyOfficeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * OnlyOffice服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class OnlyOfficeServiceImpl implements IOnlyOfficeService {

    @Autowired
    private OnlyOfficeProperties onlyOfficeProperties;

    @Autowired
    private IDocumentService documentService;

    @Override
    public Map<String, Object> generateDocumentConfig(String documentId, String fileName, String fileUrl, String mode) {
        log.info("生成OnlyOffice文档配置: documentId={}, fileName={}, mode={}", documentId, fileName, mode);

        try {
            // 获取文档信息
            Document document = documentService.getDocumentById(documentId);
            if (document == null) {
                throw new RuntimeException("文档不存在: " + documentId);
            }

            String fileExtension = document.getDocType();
            if (!isSupportedFormat(fileExtension)) {
                throw new RuntimeException("不支持的文档格式: " + fileExtension);
            }

            // 构建文档配置
            Map<String, Object> config = new HashMap<>();
            
            // 文档信息
            Map<String, Object> documentConfig = new HashMap<>();
            documentConfig.put("fileType", fileExtension);
            documentConfig.put("key", generateDocumentKey(documentId));
            documentConfig.put("title", fileName);
            documentConfig.put("url", fileUrl);
            config.put("document", documentConfig);

            // 文档类型
            config.put("documentType", onlyOfficeProperties.getDocumentType(fileExtension));

            // 编辑器配置
            Map<String, Object> editorConfig = new HashMap<>();
            editorConfig.put("mode", mode);
            editorConfig.put("lang", "zh-CN");
            editorConfig.put("callbackUrl", onlyOfficeProperties.getCallbackUrl() + "?documentId=" + documentId);

            // 用户信息
            Map<String, Object> user = new HashMap<>();
            user.put("id", SecurityUtils.getCurrentUserId());
            user.put("name", SecurityUtils.getCurrentUsername());
            editorConfig.put("user", user);

            // 自定义配置
            Map<String, Object> customization = new HashMap<>();
            customization.put("autosave", true);
            customization.put("forcesave", false);
            customization.put("commentAuthorOnly", false);
            customization.put("comments", true);
            customization.put("compactHeader", false);
            customization.put("compactToolbar", false);
            customization.put("compatibleFeatures", false);
            customization.put("customer", Map.of(
                "address", "xhcai-plus",
                "info", "Document Management System",
                "logo", "",
                "mail", "<EMAIL>",
                "name", "XHCAI Plus",
                "www", "https://xhcai.com"
            ));
            customization.put("feedback", Map.of("visible", false));
            customization.put("goback", Map.of("visible", false));
            customization.put("help", true);
            customization.put("hideRightMenu", false);
            customization.put("logo", Map.of(
                "image", "",
                "imageEmbedded", "",
                "url", "https://xhcai.com"
            ));
            customization.put("macros", true);
            customization.put("macrosMode", "warn");
            customization.put("mentionShare", true);
            customization.put("plugins", true);
            customization.put("review", Map.of("hideReviewDisplay", false));
            customization.put("showReviewChanges", false);
            customization.put("spellcheck", true);
            customization.put("toolbarNoTabs", false);
            customization.put("trackChanges", false);
            customization.put("unit", "cm");
            customization.put("zoom", 100);

            editorConfig.put("customization", customization);
            config.put("editorConfig", editorConfig);

            // 高度和宽度
            config.put("height", "100%");
            config.put("width", "100%");

            // 事件配置
            Map<String, Object> events = new HashMap<>();
            events.put("onAppReady", "onAppReady");
            events.put("onDocumentStateChange", "onDocumentStateChange");
            events.put("onRequestEditRights", "onRequestEditRights");
            events.put("onRequestHistory", "onRequestHistory");
            events.put("onRequestHistoryClose", "onRequestHistoryClose");
            events.put("onRequestHistoryData", "onRequestHistoryData");
            events.put("onRequestRestore", "onRequestRestore");
            config.put("events", events);

            // 如果启用了JWT，添加token
            if (onlyOfficeProperties.isJwtEnabled()) {
                String token = generateJwtToken(config);
                config.put("token", token);
            }

            log.info("OnlyOffice文档配置生成成功: documentId={}", documentId);
            return config;

        } catch (Exception e) {
            log.error("生成OnlyOffice文档配置失败: documentId={}, error={}", documentId, e.getMessage(), e);
            throw new RuntimeException("生成文档配置失败: " + e.getMessage());
        }
    }

    @Override
    public String generatePreviewUrl(String documentId) {
        // 这里返回一个包含文档配置的预览页面URL
        return "/onlyoffice/preview?documentId=" + documentId;
    }

    @Override
    public Map<String, Object> handleCallback(Map<String, Object> callbackData) {
        log.info("处理OnlyOffice回调: {}", callbackData);

        Map<String, Object> response = new HashMap<>();
        
        try {
            Integer status = (Integer) callbackData.get("status");
            String documentId = (String) callbackData.get("key");

            switch (status) {
                case 1: // 文档正在编辑
                    log.info("文档正在编辑: documentId={}", documentId);
                    response.put("error", 0);
                    break;
                case 2: // 文档准备保存
                case 3: // 文档保存出错
                    log.info("文档保存状态: documentId={}, status={}", documentId, status);
                    String url = (String) callbackData.get("url");
                    if (url != null) {
                        // TODO: 下载并保存文档
                        log.info("文档下载URL: {}", url);
                    }
                    response.put("error", 0);
                    break;
                case 4: // 文档关闭，无更改
                    log.info("文档关闭，无更改: documentId={}", documentId);
                    response.put("error", 0);
                    break;
                case 6: // 文档正在编辑，但当前文档状态已保存
                case 7: // 强制保存时出错
                    log.info("文档状态: documentId={}, status={}", documentId, status);
                    response.put("error", 0);
                    break;
                default:
                    log.warn("未知的文档状态: documentId={}, status={}", documentId, status);
                    response.put("error", 1);
                    break;
            }

        } catch (Exception e) {
            log.error("处理OnlyOffice回调失败: {}", e.getMessage(), e);
            response.put("error", 1);
        }

        return response;
    }

    @Override
    public boolean isSupportedFormat(String format) {
        return onlyOfficeProperties.isSupportedFormat(format);
    }

    @Override
    public boolean isEditableFormat(String format) {
        return onlyOfficeProperties.isEditableFormat(format);
    }

    @Override
    public String generateJwtToken(Map<String, Object> payload) {
        if (!onlyOfficeProperties.isJwtEnabled()) {
            return null;
        }

        try {
            // 简化的JWT实现，实际项目中应该使用专业的JWT库
            String header = Base64.getEncoder().encodeToString("{\"alg\":\"HS256\",\"typ\":\"JWT\"}".getBytes());
            String payloadJson = convertMapToJson(payload);
            String payloadBase64 = Base64.getEncoder().encodeToString(payloadJson.getBytes());
            
            String data = header + "." + payloadBase64;
            String signature = generateHmacSha256(data, onlyOfficeProperties.getJwtSecret());
            
            return data + "." + signature;
        } catch (Exception e) {
            log.error("生成JWT令牌失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean validateJwtToken(String token) {
        if (!onlyOfficeProperties.isJwtEnabled() || token == null) {
            return !onlyOfficeProperties.isJwtEnabled();
        }

        try {
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return false;
            }

            String data = parts[0] + "." + parts[1];
            String signature = generateHmacSha256(data, onlyOfficeProperties.getJwtSecret());
            
            return signature.equals(parts[2]);
        } catch (Exception e) {
            log.error("验证JWT令牌失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成文档密钥
     */
    private String generateDocumentKey(String documentId) {
        // 使用文档ID和时间戳生成唯一密钥
        return documentId + "_" + System.currentTimeMillis();
    }

    /**
     * 生成HMAC-SHA256签名
     */
    private String generateHmacSha256(String data, String secret) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hash);
    }

    /**
     * 简单的Map转JSON实现
     */
    private String convertMapToJson(Map<String, Object> map) {
        // 这里应该使用专业的JSON库，如Jackson或Gson
        // 为了简化，这里只是一个基本实现
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":");
            if (entry.getValue() instanceof String) {
                json.append("\"").append(entry.getValue()).append("\"");
            } else {
                json.append(entry.getValue());
            }
            first = false;
        }
        json.append("}");
        return json.toString();
    }
}
