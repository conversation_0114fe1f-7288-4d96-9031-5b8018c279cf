package com.xhcai.modules.dify.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify 停止工作流响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 停止工作流响应")
public class DifyStopWorkflowResponseDTO {

    /**
     * 操作结果
     */
    @Schema(description = "操作结果", example = "success")
    private String result;
}
