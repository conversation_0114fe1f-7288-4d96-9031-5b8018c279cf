package com.xhcai.modules.system.service.impl;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.xhcai.modules.system.vo.SysUserVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.utils.ApplicationContextHolder;
import com.xhcai.common.security.service.LoginUser;
import com.xhcai.common.security.utils.JwtUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.LoginDTO;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.service.IAuthService;
import com.xhcai.modules.system.service.ISysMenuService;
import com.xhcai.modules.system.service.ISysRoleService;
import com.xhcai.modules.system.service.ISysTenantService;
import com.xhcai.modules.system.service.ISysUserService;
import com.xhcai.modules.system.vo.LoginVO;
import com.xhcai.modules.system.vo.SysPermissionVO;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class AuthServiceImpl implements IAuthService {

    private static final Logger log = LoggerFactory.getLogger(AuthServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysTenantService tenantService;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public LoginVO login(LoginDTO loginDTO) {
        log.info("用户登录: {}", loginDTO.getUsername());

        // 1. 参数校验
        if (!StringUtils.hasText(loginDTO.getUsername()) || !StringUtils.hasText(loginDTO.getPassword())) {
            throw new BusinessException("用户名或密码不能为空");
        }

        // 2. 验证码校验（如果需要）
        if (StringUtils.hasText(loginDTO.getCaptcha()) && StringUtils.hasText(loginDTO.getUuid())) {
            if (!validateCaptcha(loginDTO.getCaptcha(), loginDTO.getUuid())) {
                throw new BusinessException("验证码错误");
            }
        }

        // 3. 查询用户信息
        SysUser user = userService.selectByUsername(loginDTO.getUsername());
        if (user == null) {
            throw new BadCredentialsException("用户名或密码错误");
        }

        // 4. 验证密码
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            throw new BadCredentialsException("用户名或密码错误");
        }

        // 5. 检查用户状态
        if (!CommonConstants.STATUS_NORMAL.equals(user.getStatus())) {
            if (CommonConstants.STATUS_DISABLE.equals(user.getStatus())) {
                throw new BusinessException("用户已被停用");
            } else if ("9".equals(user.getStatus())) {
                throw new BusinessException("用户已被锁定");
            } else {
                throw new BusinessException("用户状态异常");
            }
        }

        // 6. 获取用户权限和角色
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setId(user.getId());
        sysUserVO.setTenantId(user.getTenantId());
        Set<String> permissions = userService.selectUserPermissions(sysUserVO);
        Set<String> roles = userService.selectUserRoles(sysUserVO);

        // 7. 生成JWT Token
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("username", user.getUsername());
        claims.put("tenantId", user.getTenantId());
        claims.put("deptId", user.getDeptId());

        String accessToken = jwtUtils.generateToken(user.getId().toString(), claims);
        String refreshToken = jwtUtils.generateRefreshToken(user.getId().toString());

        // 8. 获取用户菜单
        List<SysPermissionVO> menus = menuService.selectMenuTreeByUserId(sysUserVO);

        // 9. 更新用户登录信息
        updateUserLoginInfo(user);

        // 10. 构建LoginUser对象并缓存到Redis
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(user.getId());
        loginUser.setUsername(user.getUsername());
        loginUser.setTenantId(user.getTenantId());
        loginUser.setDeptId(user.getDeptId());
        loginUser.setNickname(user.getNickname());
        loginUser.setEmail(user.getEmail());
        loginUser.setPhone(user.getPhone());
        loginUser.setAvatar(user.getAvatar());
        loginUser.setStatus(user.getStatus());
        loginUser.setUserType(user.getUserType()); // 设置用户类型
        loginUser.setPermissions(permissions);
        loginUser.setRoles(roles);
        loginUser.setLoginTime(System.currentTimeMillis());

        // 缓存用户信息到Redis
        try {
            Class<?> userCacheServiceClass = Class.forName("com.xhcai.common.security.service.UserCacheService");
            Object userCacheService = ApplicationContextHolder.getBean(userCacheServiceClass);
            userCacheServiceClass.getMethod("cacheUser", LoginUser.class).invoke(userCacheService, loginUser);
            log.debug("用户信息已缓存到Redis: userId={}", user.getId());
        } catch (Exception e) {
            log.warn("缓存用户信息失败: {}", e.getMessage());
        }

        // 设置当前请求的SecurityContext
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                loginUser,
                null,
                loginUser.getAuthorities()
        );
        SecurityContextHolder.getContext().setAuthentication(authentication);
        log.debug("已设置SecurityContext: userId={}, username={}", user.getId(), user.getUsername());

        // 11. 构建返回结果
        LoginVO loginVO = new LoginVO();
        loginVO.setAccessToken(accessToken);
        loginVO.setRefreshToken(refreshToken);
        loginVO.setExpiresIn(86400L); // 24小时
        loginVO.setPermissions(permissions);
        loginVO.setRoles(roles);
        loginVO.setMenus(menus);

        // 构建用户信息
        LoginVO.UserInfo userInfo = new LoginVO.UserInfo();
        BeanUtils.copyProperties(user, userInfo);
        userInfo.setUserId(user.getId());
        loginVO.setUserInfo(userInfo);

        log.info("用户登录成功: {}", loginDTO.getUsername());
        return loginVO;
    }

    @Override
    public boolean logout(String token) {
        try {
            // 清除Security上下文
            SecurityContextHolder.clearContext();

            // 这里可以将token加入黑名单，防止被重复使用
            // 暂时简单处理，实际项目中可以使用Redis存储黑名单
            log.info("用户登出成功");
            return true;
        } catch (Exception e) {
            log.error("用户登出失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public LoginVO refreshToken(String refreshToken) {
        try {
            // 验证刷新令牌
            if (!jwtUtils.validateToken(refreshToken)) {
                throw new BusinessException("刷新令牌无效");
            }

            // 获取用户ID
            String userId = jwtUtils.getSubjectFromToken(refreshToken);
            if (!StringUtils.hasText(userId)) {
                throw new BusinessException("刷新令牌无效");
            }

            // 查询用户信息
            SysUser user = userService.getById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            // 生成新的访问令牌
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", user.getId());
            claims.put("username", user.getUsername());
            claims.put("tenantId", user.getTenantId());
            claims.put("deptId", user.getDeptId());

            String newAccessToken = jwtUtils.generateToken(user.getId().toString(), claims);

            LoginVO loginVO = new LoginVO();
            loginVO.setAccessToken(newAccessToken);
            loginVO.setRefreshToken(refreshToken); // 刷新令牌保持不变
            loginVO.setExpiresIn(86400L);

            return loginVO;
        } catch (Exception e) {
            log.error("刷新令牌失败: {}", e.getMessage(), e);
            throw new BusinessException("刷新令牌失败");
        }
    }

    @Override
    public LoginVO.UserInfo getCurrentUserInfo() {
        LoginUser currentUser = SecurityUtils.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }

        LoginVO.UserInfo userInfo = new LoginVO.UserInfo();
        BeanUtils.copyProperties(currentUser, userInfo);
        return userInfo;
    }

    @Override
    public boolean validateToken(String token) {
        return jwtUtils.validateToken(token);
    }

    @Override
    public Object generateCaptcha() {
        // 这里应该生成验证码图片
        // 暂时返回简单的验证码信息
        Map<String, Object> result = new HashMap<>();
        result.put("uuid", java.util.UUID.randomUUID().toString());
        result.put("img", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
        return result;
    }

    @Override
    public boolean validateCaptcha(String captcha, String uuid) {
        // 这里应该验证验证码
        // 暂时简单处理，返回true
        return true;
    }

    /**
     * 更新用户登录信息
     */
    private void updateUserLoginInfo(SysUser user) {
        try {
            user.setLoginTime(LocalDateTime.now());
            // 这里可以设置登录IP等信息
            userService.updateById(user);
        } catch (Exception e) {
            log.error("更新用户登录信息失败: {}", e.getMessage(), e);
        }
    }
}
