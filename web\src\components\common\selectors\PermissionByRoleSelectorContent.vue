<template>
  <div class="permission-by-role-selector-content">
    <!-- 选择器头部 -->
    <div v-if="showHeader" class="selector-header flex items-center justify-between mb-3">
      <div class="flex items-center gap-2">
        <span class="text-sm font-medium text-gray-700">按角色选择权限</span>
        <span v-if="hasSelection" class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
          已选择 {{ selectedCount }} 个权限
        </span>
      </div>
      <div class="flex items-center gap-2">
        <el-button 
          v-if="config.multiple" 
          @click="selectAllPermissions" 
          size="small" 
          type="primary" 
          plain
          :disabled="loading || !currentRolePermissions.length"
        >
          全选当前角色权限
        </el-button>
        <el-button 
          @click="clearSelection" 
          size="small" 
          :disabled="!hasSelection"
        >
          清空
        </el-button>
      </div>
    </div>

    <div class="selector-content grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- 左侧：角色列表 -->
      <div class="role-panel">
        <div class="panel-header flex items-center justify-between mb-2">
          <h4 class="text-sm font-medium text-gray-700">选择角色</h4>
          <span v-if="selectedRole" class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
            {{ selectedRole.label }}
          </span>
        </div>
        
        <!-- 角色搜索 -->
        <div class="search-box mb-3">
          <el-input
            v-model="roleFilterText"
            placeholder="搜索角色名称、编码..."
            :prefix-icon="Search"
            clearable
            size="small"
            :disabled="roleLoading"
          />
        </div>

        <!-- 角色列表 -->
        <div class="role-list border border-gray-200 rounded-lg bg-white min-h-[300px] max-h-[400px] overflow-auto">
          <div v-if="roleLoading" class="loading-state text-center py-8">
            <div class="loading-spinner">
              <i class="el-icon-loading animate-spin"></i>
            </div>
            <p class="text-gray-500 text-sm mt-2">加载角色中...</p>
          </div>

          <div v-else-if="!filteredRoles.length" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-user-solid text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">
              {{ roleFilterText ? '未找到匹配的角色' : '暂无角色数据' }}
            </p>
          </div>

          <div v-else class="role-items p-2">
            <div
              v-for="role in filteredRoles"
              :key="role.value"
              class="role-item flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
              :class="{
                'bg-blue-50 border-blue-200': selectedRoleId === role.value,
                'opacity-50 cursor-not-allowed': role.disabled
              }"
              @click="handleRoleClick(role)"
            >
              <div class="role-icon mr-3">
                <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                  <i class="el-icon-key text-white text-sm"></i>
                </div>
              </div>
              
              <div class="role-info flex-1 min-w-0">
                <div class="role-name text-sm font-medium text-gray-900 truncate">
                  {{ role.label }}
                </div>
                <div class="role-details text-xs text-gray-500 truncate">
                  {{ role.roleCode }}
                  <span v-if="role.permissionIds && role.permissionIds.length" class="ml-2">
                    {{ role.permissionIds.length }} 个权限
                  </span>
                </div>
              </div>
              
              <div class="role-status">
                <el-tag
                  :type="role.status === '0' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ role.status === '0' ? '正常' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：权限树 -->
      <div class="permission-panel">
        <div class="panel-header flex items-center justify-between mb-2">
          <h4 class="text-sm font-medium text-gray-700">
            选择权限
            <span v-if="selectedRole" class="text-xs text-gray-500">
              ({{ selectedRole.label }})
            </span>
          </h4>
          <div class="flex items-center gap-2">
            <el-button 
              @click="expandAllPermissions" 
              size="small" 
              plain
              :disabled="!selectedRoleId || permissionLoading"
            >
              展开
            </el-button>
            <el-button 
              @click="collapseAllPermissions" 
              size="small" 
              plain
              :disabled="!selectedRoleId || permissionLoading"
            >
              折叠
            </el-button>
          </div>
        </div>

        <!-- 权限搜索 -->
        <div class="search-box mb-3">
          <el-input
            v-model="permissionFilterText"
            placeholder="搜索权限名称、编码..."
            :prefix-icon="Search"
            clearable
            size="small"
            :disabled="!selectedRoleId || permissionLoading"
          />
        </div>

        <!-- 权限树 -->
        <div class="permission-tree border border-gray-200 rounded-lg bg-white min-h-[300px] max-h-[400px] overflow-auto">
          <div v-if="!selectedRoleId" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-key text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">请先选择角色</p>
          </div>

          <div v-else-if="permissionLoading" class="loading-state text-center py-8">
            <div class="loading-spinner">
              <i class="el-icon-loading animate-spin"></i>
            </div>
            <p class="text-gray-500 text-sm mt-2">加载权限中...</p>
          </div>

          <div v-else-if="!currentRolePermissions.length" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-key text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">该角色暂无权限</p>
          </div>

          <el-tree
            v-else
            ref="permissionTreeRef"
            :data="currentRolePermissions"
            :props="treeProps"
            :show-checkbox="config.multiple"
            :check-strictly="config.checkStrictly"
            :expand-on-click-node="false"
            :filter-node-method="filterPermissionNode"
            :highlight-current="!config.multiple"
            :current-node-key="!config.multiple ? selectedPermissionIds : undefined"
            :checked-keys="config.multiple ? selectedPermissionIds : []"
            :expanded-keys="expandedPermissionKeys"
            node-key="value"
            class="permission-tree-inner p-2"
            @check="handlePermissionCheck"
            @node-click="handlePermissionNodeClick"
          >
            <template #default="{ node, data }">
              <div class="tree-node flex items-center justify-between w-full">
                <div class="node-content flex items-center">
                  <span class="node-icon mr-2">
                    <i v-if="data.permissionType === '1'" class="el-icon-menu text-blue-500"></i>
                    <i v-else-if="data.permissionType === '2'" class="el-icon-document text-green-500"></i>
                    <i v-else class="el-icon-setting text-orange-500"></i>
                  </span>
                  <span class="node-label text-sm" :class="{ 'text-gray-400': data.disabled }">
                    {{ data.label }}
                  </span>
                  <span v-if="data.permissionCode" class="permission-code text-xs text-gray-500 ml-2">
                    ({{ data.permissionCode }})
                  </span>
                </div>
                <div class="node-extra flex items-center text-xs text-gray-500">
                  <el-tag
                    :type="getPermissionTypeTag(data.permissionType).type"
                    size="small"
                  >
                    {{ getPermissionTypeTag(data.permissionType).label }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
    </div>

    <!-- 已选择的权限标签 -->
    <div v-if="hasSelection && config.multiple && showSelectedTags" class="selected-tags mt-4">
      <div class="text-xs text-gray-600 mb-2">已选择的权限:</div>
      <div class="flex flex-wrap gap-1">
        <el-tag
          v-for="permission in getSelectedPermissionOptions()"
          :key="permission.value"
          :closable="!config.disabled"
          size="small"
          @close="removePermissionSelection(permission.value)"
        >
          <div class="flex items-center">
            <i v-if="permission.permissionType === '1'" class="el-icon-menu mr-1"></i>
            <i v-else-if="permission.permissionType === '2'" class="el-icon-document mr-1"></i>
            <i v-else class="el-icon-setting mr-1"></i>
            {{ permission.label }}
          </div>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElInput, ElButton, ElTag, ElTree } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { RoleAPI, PermissionAPI } from '@/api/system'
import type { PermissionSelectorOption, RoleSelectorOption, SelectorConfig } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludePermissionIds?: string[]
  showHeader?: boolean
  showSelectedTags?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], options: PermissionSelectorOption[]): void
  (e: 'select', value: string, option: PermissionSelectorOption): void
  (e: 'remove', value: string): void
  (e: 'roleChange', roleId: string, role: RoleSelectorOption): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludePermissionIds: () => [],
  showHeader: false,
  showSelectedTags: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const selectedRoleId = ref<string>('')
const selectedRole = ref<RoleSelectorOption | null>(null)
const selectedPermissionIds = ref<string | string[]>(props.modelValue || (props.config.multiple ? [] : ''))
const allRoles = ref<RoleSelectorOption[]>([])
const currentRolePermissions = ref<PermissionSelectorOption[]>([])
const roleFilterText = ref('')
const permissionFilterText = ref('')
const expandedPermissionKeys = ref<string[]>([])
const roleLoading = ref(false)
const permissionLoading = ref(false)

// 树组件引用
const permissionTreeRef = ref<InstanceType<typeof ElTree>>()

// 树属性配置
const treeProps = {
  children: 'children',
  label: 'label',
  disabled: 'disabled'
}

// 配置
const defaultConfig: SelectorConfig = {
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择权限',
  size: 'default',
  disabled: false,
  checkStrictly: false
}

const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 计算属性
const filteredRoles = computed(() => {
  if (!roleFilterText.value) return allRoles.value
  
  const filterText = roleFilterText.value.toLowerCase()
  return allRoles.value.filter(role => 
    role.label.toLowerCase().includes(filterText) ||
    role.roleCode.toLowerCase().includes(filterText)
  )
})

const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedPermissionIds.value) && selectedPermissionIds.value.length > 0
  }
  return selectedPermissionIds.value !== '' && selectedPermissionIds.value !== null && selectedPermissionIds.value !== undefined
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
    return selectedPermissionIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

const loading = computed(() => roleLoading.value || permissionLoading.value)

// 方法
const loadRoles = async () => {
  try {
    roleLoading.value = true
    const response = await RoleAPI.getRoleList({
      status: props.onlyEnabled ? '0' : undefined
    })
    
    const roles = response.data || []
    allRoles.value = await Promise.all(roles.map(async role => {
      // 获取角色的权限ID列表
      try {
        const permissionResponse = await RoleAPI.getRolePermissionIds(role.id)
        return {
          value: role.id,
          label: role.roleName,
          roleCode: role.roleCode,
          status: role.status,
          permissionIds: permissionResponse.data || [],
          disabled: role.status !== '0'
        }
      } catch (error) {
        console.error(`获取角色 ${role.id} 权限失败:`, error)
        return {
          value: role.id,
          label: role.roleName,
          roleCode: role.roleCode,
          status: role.status,
          permissionIds: [],
          disabled: role.status !== '0'
        }
      }
    }))
  } catch (error) {
    console.error('加载角色失败:', error)
    allRoles.value = []
  } finally {
    roleLoading.value = false
  }
}

const loadRolePermissions = async (roleId: string) => {
  if (!roleId) {
    currentRolePermissions.value = []
    return
  }

  try {
    permissionLoading.value = true
    
    // 获取角色的权限列表
    const response = await PermissionAPI.getPermissionsByRoleId(roleId)
    const permissions = response.data || []
    
    // 转换为选择器选项格式
    const transformPermissions = (perms: any[]): PermissionSelectorOption[] => {
      return perms.map(permission => ({
        value: permission.id,
        label: permission.permissionName,
        permissionCode: permission.permissionCode,
        permissionType: permission.permissionType,
        parentId: permission.parentId,
        status: permission.status,
        level: permission.level,
        disabled: permission.status !== '0',
        children: permission.children && permission.children.length > 0 
          ? transformPermissions(permission.children) 
          : undefined
      }))
    }

    currentRolePermissions.value = transformPermissions(permissions)
  } catch (error) {
    console.error('加载角色权限失败:', error)
    currentRolePermissions.value = []
  } finally {
    permissionLoading.value = false
  }
}

const getPermissionTypeTag = (type: string) => {
  switch (type) {
    case '1':
      return { label: '目录', type: 'primary' }
    case '2':
      return { label: '菜单', type: 'success' }
    case '3':
      return { label: '按钮', type: 'warning' }
    default:
      return { label: '未知', type: 'info' }
  }
}

const filterPermissionNode = (value: string, data: PermissionSelectorOption) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase()) ||
         (data.permissionCode && data.permissionCode.toLowerCase().includes(value.toLowerCase()))
}

const handleRoleClick = (role: RoleSelectorOption) => {
  if (role.disabled) return
  
  selectedRoleId.value = role.value
  selectedRole.value = role
  loadRolePermissions(role.value)
  emit('roleChange', role.value, role)
}

const handlePermissionNodeClick = (data: PermissionSelectorOption) => {
  if (config.value.multiple || data.disabled) return
  
  selectedPermissionIds.value = data.value
  emit('update:modelValue', data.value)
  emit('change', data.value, [data])
  emit('select', data.value, data)
}

const handlePermissionCheck = (data: PermissionSelectorOption, checkedInfo: any) => {
  if (!config.value.multiple) return
  
  const checkedKeys = checkedInfo.checkedKeys
  selectedPermissionIds.value = checkedKeys
  emit('update:modelValue', checkedKeys)
  
  const selectedOptions = getSelectedPermissionOptions()
  emit('change', checkedKeys, selectedOptions)
}

const selectAllPermissions = () => {
  if (!config.value.multiple) return
  
  const getAllPermissionIds = (permissions: PermissionSelectorOption[]): string[] => {
    let ids: string[] = []
    permissions.forEach(permission => {
      if (!permission.disabled) {
        ids.push(permission.value)
      }
      if (permission.children && permission.children.length > 0) {
        ids = ids.concat(getAllPermissionIds(permission.children))
      }
    })
    return ids
  }
  
  const allIds = getAllPermissionIds(currentRolePermissions.value)
  selectedPermissionIds.value = allIds
  emit('update:modelValue', allIds)
  
  const selectedOptions = getSelectedPermissionOptions()
  emit('change', allIds, selectedOptions)
  
  // 更新树的选中状态
  nextTick(() => {
    permissionTreeRef.value?.setCheckedKeys(allIds)
  })
}

const clearSelection = () => {
  selectedPermissionIds.value = config.value.multiple ? [] : ''
  emit('update:modelValue', selectedPermissionIds.value)
  emit('change', selectedPermissionIds.value, [])
  
  // 更新树的选中状态
  nextTick(() => {
    if (config.value.multiple) {
      permissionTreeRef.value?.setCheckedKeys([])
    } else {
      permissionTreeRef.value?.setCurrentKey(null)
    }
  })
}

const removePermissionSelection = (permissionId: string) => {
  if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
    const newValues = selectedPermissionIds.value.filter(id => id !== permissionId)
    selectedPermissionIds.value = newValues
    emit('update:modelValue', newValues)
    
    const selectedOptions = getSelectedPermissionOptions()
    emit('change', newValues, selectedOptions)
    emit('remove', permissionId)
    
    // 更新树的选中状态
    nextTick(() => {
      permissionTreeRef.value?.setCheckedKeys(newValues)
    })
  }
}

const expandAllPermissions = () => {
  const getAllKeys = (permissions: PermissionSelectorOption[]): string[] => {
    let keys: string[] = []
    permissions.forEach(permission => {
      if (permission.children && permission.children.length > 0) {
        keys.push(permission.value)
        keys = keys.concat(getAllKeys(permission.children))
      }
    })
    return keys
  }
  
  expandedPermissionKeys.value = getAllKeys(currentRolePermissions.value)
}

const collapseAllPermissions = () => {
  expandedPermissionKeys.value = []
}

const getSelectedPermissionOptions = (): PermissionSelectorOption[] => {
  const selectedIds = config.value.multiple && Array.isArray(selectedPermissionIds.value) 
    ? selectedPermissionIds.value 
    : selectedPermissionIds.value ? [selectedPermissionIds.value as string] : []
  
  const findPermissions = (permissions: PermissionSelectorOption[], targetIds: string[]): PermissionSelectorOption[] => {
    let result: PermissionSelectorOption[] = []
    for (const permission of permissions) {
      if (targetIds.includes(permission.value)) {
        result.push(permission)
      }
      if (permission.children && permission.children.length > 0) {
        result = result.concat(findPermissions(permission.children, targetIds))
      }
    }
    return result
  }
  
  return findPermissions(currentRolePermissions.value, selectedIds)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedPermissionIds.value = newValue || (config.value.multiple ? [] : '')
  
  // 同步树的选中状态
  nextTick(() => {
    if (config.value.multiple && Array.isArray(newValue)) {
      permissionTreeRef.value?.setCheckedKeys(newValue)
    } else if (newValue) {
      permissionTreeRef.value?.setCurrentKey(newValue as string)
    }
  })
}, { immediate: true })

// 监听过滤文本变化
watch(permissionFilterText, (value) => {
  permissionTreeRef.value?.filter(value)
})

// 组件挂载
onMounted(async () => {
  selectedPermissionIds.value = props.modelValue || (config.value.multiple ? [] : '')
  await loadRoles()
})

// 暴露方法
defineExpose({
  clearSelection,
  selectAllPermissions,
  expandAllPermissions,
  collapseAllPermissions,
  getSelectedPermissionOptions,
  refresh: () => {
    loadRoles()
    if (selectedRoleId.value) {
      loadRolePermissions(selectedRoleId.value)
    }
  }
})
</script>

<style scoped>
.permission-by-role-selector-content {
  width: 100%;
}

.panel-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.role-item {
  border: 1px solid transparent;
}

.role-item:hover {
  border-color: #e5e7eb;
}

.role-item.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.permission-tree-inner :deep(.el-tree-node__content) {
  height: auto;
  padding: 8px 0;
}

.tree-node {
  flex: 1;
  padding-right: 8px;
}

.node-content {
  flex: 1;
  min-width: 0;
}

.node-label {
  word-break: break-all;
}

.node-extra {
  flex-shrink: 0;
}

.selected-tags {
  max-height: 120px;
  overflow-y: auto;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  font-size: 24px;
  color: #409eff;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
