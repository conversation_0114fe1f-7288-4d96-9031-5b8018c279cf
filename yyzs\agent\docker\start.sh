#!/bin/bash

# YYZS Agent 启动脚本
# 用于Docker容器中启动应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "${LOG_LEVEL}" == "DEBUG" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 检查必要的环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    local required_vars=(
        "DB_HOST"
        "DB_PORT" 
        "DB_NAME"
        "DB_USERNAME"
        "DB_PASSWORD"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            log_error "必需的环境变量 $var 未设置"
            exit 1
        fi
    done
    
    log_info "环境变量检查完成"
}

# 等待数据库连接
wait_for_database() {
    log_info "等待数据库连接: ${DB_HOST}:${DB_PORT}"
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if nc -z "${DB_HOST}" "${DB_PORT}" 2>/dev/null; then
            log_info "数据库连接成功"
            return 0
        fi
        
        log_warn "数据库连接失败，重试 $attempt/$max_attempts"
        sleep 2
        ((attempt++))
    done
    
    log_error "数据库连接超时"
    exit 1
}

# Redis功能已移除，使用内存缓存

# 初始化应用目录
init_directories() {
    log_info "初始化应用目录..."
    
    # 确保目录存在
    mkdir -p /app/logs
    mkdir -p /app/data
    mkdir -p /app/temp
    mkdir -p /opt/elastic
    mkdir -p /var/log/yyzs
    
    # 设置权限
    chmod 755 /app/logs
    chmod 755 /app/data
    chmod 755 /app/temp
    chmod 755 /opt/elastic
    chmod 755 /var/log/yyzs
    
    log_info "目录初始化完成"
}

# 生成应用配置
generate_config() {
    log_info "生成应用配置..."
    
    local config_file="/app/config/application-runtime.yml"
    
    cat > "${config_file}" << EOF
# 运行时动态配置
server:
  port: ${SERVER_PORT:-8080}

management:
  server:
    port: ${MANAGEMENT_PORT:-8081}
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

spring:
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: ${DDL_AUTO:-validate}
    show-sql: ${SHOW_SQL:-false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  # Redis配置已移除，使用内存缓存

logging:
  level:
    root: ${LOG_LEVEL:-INFO}
    com.yyzs: ${APP_LOG_LEVEL:-INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:-WARN}
  file:
    name: /var/log/yyzs/yyzs-agent.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

yyzs:
  agent:
    install-path: ${ELASTIC_INSTALL_PATH:-/opt/elastic}
    temp-path: /app/temp
    max-upload-size: ${MAX_UPLOAD_SIZE:-500MB}
    allowed-file-types: tar.gz,tgz,zip
    monitor:
      enabled: ${MONITOR_ENABLED:-true}
      interval: ${MONITOR_INTERVAL:-30s}
    security:
      jwt:
        secret: ${JWT_SECRET:-yyzs-agent-jwt-secret-key-2024}
        expiration: ${JWT_EXPIRATION:-86400}
EOF

    log_info "配置文件生成完成: ${config_file}"
}

# 启动应用
start_application() {
    log_info "启动YYZS Agent应用..."
    
    # 构建Java启动参数
    local java_opts="${JAVA_OPTS}"
    
    # 添加JVM参数
    java_opts="${java_opts} -Djava.security.egd=file:/dev/./urandom"
    java_opts="${java_opts} -Djava.awt.headless=true"
    java_opts="${java_opts} -Dfile.encoding=UTF-8"
    java_opts="${java_opts} -Duser.timezone=${TZ:-Asia/Shanghai}"
    
    # 添加Spring配置
    java_opts="${java_opts} -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE:-docker}"
    java_opts="${java_opts} -Dspring.config.additional-location=file:/app/config/application-runtime.yml"
    
    # 添加应用参数
    java_opts="${java_opts} -Dyyzs.agent.install-path=${ELASTIC_INSTALL_PATH:-/opt/elastic}"
    java_opts="${java_opts} -Dyyzs.agent.temp-path=/app/temp"
    
    log_info "Java启动参数: ${java_opts}"
    log_info "应用启动中..."
    
    # 启动应用
    exec java ${java_opts} -jar /app/yyzs-agent.jar "$@"
}

# 显示帮助信息
show_help() {
    cat << EOF
YYZS Agent Docker启动脚本

用法: $0 [命令] [选项]

命令:
  server          启动应用服务器（默认）
  help           显示此帮助信息
  version        显示版本信息
  check          检查环境和配置

环境变量:
  DB_HOST         数据库主机地址
  DB_PORT         数据库端口（默认: 5432）
  DB_NAME         数据库名称
  DB_USERNAME     数据库用户名
  DB_PASSWORD     数据库密码
  
  # Redis配置已移除
  
  SERVER_PORT     应用服务端口（默认: 8080）
  MANAGEMENT_PORT 管理端口（默认: 8081）
  LOG_LEVEL       日志级别（默认: INFO）
  
  JAVA_OPTS       JVM参数
  SPRING_PROFILES_ACTIVE  Spring配置文件（默认: docker）

示例:
  docker run -e DB_HOST=postgres -e DB_PASSWORD=secret yyzs-agent:latest
  docker run yyzs-agent:latest help
  docker run yyzs-agent:latest check

EOF
}

# 显示版本信息
show_version() {
    cat << EOF
YYZS Agent v1.0.0
Build: $(date '+%Y-%m-%d %H:%M:%S')
Java: $(java -version 2>&1 | head -n 1)
Platform: $(uname -a)
EOF
}

# 检查环境
check_environment() {
    log_info "=== 环境检查 ==="
    
    # 检查Java版本
    log_info "Java版本:"
    java -version
    
    # 检查系统信息
    log_info "系统信息:"
    uname -a
    
    # 检查磁盘空间
    log_info "磁盘空间:"
    df -h /app /opt/elastic /var/log/yyzs
    
    # 检查内存
    log_info "内存信息:"
    free -h
    
    # 检查网络连接
    log_info "网络连接检查:"
    if [[ -n "${DB_HOST}" ]]; then
        if nc -z "${DB_HOST}" "${DB_PORT:-5432}" 2>/dev/null; then
            log_info "✓ 数据库连接正常: ${DB_HOST}:${DB_PORT:-5432}"
        else
            log_error "✗ 数据库连接失败: ${DB_HOST}:${DB_PORT:-5432}"
        fi
    fi
    
    # Redis检查已移除
    
    log_info "=== 检查完成 ==="
}

# 主函数
main() {
    local command="${1:-server}"
    
    case "${command}" in
        "server")
            log_info "启动YYZS Agent服务器..."
            check_env_vars
            wait_for_database
            # wait_for_redis 已移除
            init_directories
            generate_config
            start_application "${@:2}"
            ;;
        "help")
            show_help
            ;;
        "version")
            show_version
            ;;
        "check")
            check_environment
            ;;
        *)
            log_error "未知命令: ${command}"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "收到终止信号，正在关闭..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
