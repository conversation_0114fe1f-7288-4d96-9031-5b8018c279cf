<template>
  <div class="permission-panel" :class="{ visible }" @click.self="close">
    <div class="panel-content" :class="{ visible }">
      <!-- 头部 -->
      <div class="panel-header">
        <div class="header-left">
          <div class="file-info" v-if="!isBatch">
            <div class="file-icon" :class="getFileTypeClass(file?.type)">
              {{ getFileIcon(file?.type) }}
            </div>
            <div class="file-details">
              <h3 class="file-name">{{ file?.name }}</h3>
              <div class="file-meta">
                <span>{{ formatFileSize(file?.size) }}</span>
                <span>•</span>
                <span>当前权限: {{ getPermissionText(file?.permission) }}</span>
              </div>
            </div>
          </div>
          <div class="batch-info" v-else>
            <div class="batch-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="batch-details">
              <h3 class="batch-title">批量权限设置</h3>
              <div class="batch-meta">
                <span>已选择 {{ files?.length || 0 }} 个文件</span>
              </div>
            </div>
          </div>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="close" title="关闭">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- 权限设置内容 -->
      <div class="permission-content">
        <div class="permission-section">
          <h4 class="section-title">{{ isBatch ? '批量权限设置' : '文件权限设置' }}</h4>
          <p class="section-description">{{ isBatch ? '为选中的文件设置统一权限' : '设置谁可以访问此文件' }}</p>

          <!-- 批量文件列表 -->
          <div class="batch-files-list" v-if="isBatch && files?.length">
            <div class="files-header">
              <span class="files-title">选中的文件 ({{ files.length }})</span>
              <button class="toggle-files-btn" @click="showAllFiles = !showAllFiles">
                {{ showAllFiles ? '收起' : '展开' }}
                <i class="fas" :class="showAllFiles ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
              </button>
            </div>
            <div class="files-container" v-if="showAllFiles">
              <div class="file-item" v-for="file in files.slice(0, 10)" :key="file.id">
                <div class="file-icon" :class="getFileTypeClass(file.type)">
                  {{ getFileIcon(file.type) }}
                </div>
                <div class="file-info">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-meta">{{ formatFileSize(file.size) }} • {{ getPermissionText(file.permission) }}</div>
                </div>
              </div>
              <div class="more-files" v-if="files.length > 10">
                还有 {{ files.length - 10 }} 个文件...
              </div>
            </div>
          </div>

          <div class="permission-options">
            <div 
              class="permission-option"
              :class="{ active: selectedPermission === 'public' }"
              @click="selectPermission('public')"
            >
              <div class="option-icon">🌐</div>
              <div class="option-content">
                <div class="option-title">公开共享</div>
                <div class="option-description">所有人都可以访问此文件</div>
              </div>
              <div class="option-radio">
                <i class="fas fa-check" v-if="selectedPermission === 'public'"></i>
              </div>
            </div>

            <div 
              class="permission-option"
              :class="{ active: selectedPermission === 'private' }"
              @click="selectPermission('private')"
            >
              <div class="option-icon">🔒</div>
              <div class="option-content">
                <div class="option-title">个人私有</div>
                <div class="option-description">只有文件所有者可以访问</div>
              </div>
              <div class="option-radio">
                <i class="fas fa-check" v-if="selectedPermission === 'private'"></i>
              </div>
            </div>

            <div 
              class="permission-option"
              :class="{ active: selectedPermission === 'role' }"
              @click="selectPermission('role')"
            >
              <div class="option-icon">👥</div>
              <div class="option-content">
                <div class="option-title">角色权限</div>
                <div class="option-description">指定角色的用户可以访问</div>
              </div>
              <div class="option-radio">
                <i class="fas fa-check" v-if="selectedPermission === 'role'"></i>
              </div>
            </div>

            <div 
              class="permission-option"
              :class="{ active: selectedPermission === 'department' }"
              @click="selectPermission('department')"
            >
              <div class="option-icon">🏢</div>
              <div class="option-content">
                <div class="option-title">单位权限</div>
                <div class="option-description">指定单位的用户可以访问</div>
              </div>
              <div class="option-radio">
                <i class="fas fa-check" v-if="selectedPermission === 'department'"></i>
              </div>
            </div>

            <div 
              class="permission-option"
              :class="{ active: selectedPermission === 'users' }"
              @click="selectPermission('users')"
            >
              <div class="option-icon">👤</div>
              <div class="option-content">
                <div class="option-title">指定用户</div>
                <div class="option-description">只有指定的用户可以访问</div>
              </div>
              <div class="option-radio">
                <i class="fas fa-check" v-if="selectedPermission === 'users'"></i>
              </div>
            </div>
          </div>

          <!-- 详细设置区域 -->
          <div class="permission-details" v-if="selectedPermission !== 'public' && selectedPermission !== 'private'">
            <div v-if="selectedPermission === 'role'" class="detail-section">
              <div class="section-header">
                <h5>选择角色</h5>
                <span class="selected-count" v-if="selectedRoles.length > 0">
                  已选择 {{ selectedRoles.length }} 个角色
                </span>
              </div>
              <div class="role-selector-wrapper">
                <ElRoleSelector
                  v-model="selectedRoles"
                  :config="{
                    multiple: true,
                    placeholder: '请选择角色',
                    clearable: true,
                    filterable: true
                  }"
                  :show-role-extra="true"
                  :show-selected-tags="true"
                />
              </div>
            </div>

            <div v-if="selectedPermission === 'department'" class="detail-section">
              <div class="section-header">
                <h5>选择单位</h5>
                <span class="selected-count" v-if="selectedDepartments.length > 0">
                  已选择 {{ selectedDepartments.length }} 个单位
                </span>
              </div>
              <div class="dept-selector-wrapper">
                <ElDeptTreeSelector
                  v-model="selectedDepartments"
                  :config="{
                    multiple: true,
                    placeholder: '请选择单位',
                    clearable: true,
                    filterable: true
                  }"
                  :show-node-extra="true"
                  :show-selected-tags="true"
                />
              </div>
            </div>

            <div v-if="selectedPermission === 'users'" class="detail-section">
              <div class="section-header">
                <h5>选择用户</h5>
                <span class="selected-count" v-if="selectedUsers.length > 0">
                  已选择 {{ selectedUsers.length }} 个用户
                </span>
              </div>
              <div class="user-selector-wrapper">
                <ElUserByDeptSelector
                  v-model="selectedUsers"
                  :config="{
                    multiple: true,
                    placeholder: '请选择用户',
                    clearable: true
                  }"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="panel-footer">
        <button class="btn btn-secondary" @click="close">取消</button>
        <button class="btn btn-primary" @click="savePermission" :disabled="!canSave">
          保存权限设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { getFileIcon, getFileTypeClass, formatFileSize, getPermissionText } from '@/utils/fileUtils'
import { ElRoleSelector, ElDeptTreeSelector, ElUserByDeptSelector } from '@/components/common/el-selectors'
import KnowledgeAPI from '@/api/knowledge'

// Props
interface Props {
  file?: any
  files?: any[]
  visible: boolean
  isBatch?: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  'permission-updated': []
}>()

// 响应式数据
const selectedPermission = ref('public')
const selectedRoles = ref<string[]>([])
const selectedDepartments = ref<string[]>([])
const selectedUsers = ref<string[]>([])
const showAllFiles = ref(false)

// 计算属性

const canSave = computed(() => {
  if (selectedPermission.value === 'public' || selectedPermission.value === 'private') {
    return true
  }
  if (selectedPermission.value === 'role') {
    return selectedRoles.value.length > 0
  }
  if (selectedPermission.value === 'department') {
    return selectedDepartments.value.length > 0
  }
  if (selectedPermission.value === 'users') {
    return selectedUsers.value.length > 0
  }
  return false
})

// 方法
const close = () => {
  emit('close')
}

const selectPermission = (permission: string) => {
  selectedPermission.value = permission
  // 清空之前的选择
  selectedRoles.value = []
  selectedDepartments.value = []
  selectedUsers.value = []
}

const savePermission = async () => {
  try {
    let documentIds: string[] = []

    if (props.isBatch && props.files?.length) {
      // 批量权限设置
      documentIds = props.files.map(f => f.id)
    } else if (props.file) {
      // 单个文件权限设置
      documentIds = [props.file.id]
    }

    if (documentIds.length === 0) {
      alert('没有选择文件')
      return
    }

    const permissionData = {
      documentIds,
      permissionType: selectedPermission.value as 'public' | 'private' | 'role' | 'department' | 'users',
      roleIds: selectedRoles.value,
      departmentIds: selectedDepartments.value,
      userIds: selectedUsers.value,
      permissionLevel: 'read'
    }

    const response = await KnowledgeAPI.setDocumentPermission(permissionData)

    if (response.success) {
      if (props.isBatch && props.files?.length) {
        alert(`已为 ${props.files.length} 个文件设置权限！`)
      } else {
        alert('权限设置已保存！')
      }

      emit('permission-updated')
      close()
    } else {
      alert('权限设置失败：' + response.message)
    }
  } catch (error) {
    console.error('保存权限设置失败:', error)
    alert('权限设置失败，请重试')
  }
}

// 其他方法

// 监听文件变化，初始化权限设置
watch(() => props.file, (newFile) => {
  if (newFile) {
    selectedPermission.value = newFile.permission || 'public'
  }
}, { immediate: true })

// 监听面板可见性
watch(() => props.visible, (visible) => {
  if (!visible) {
    selectedPermission.value = 'public'
    selectedRoles.value = []
    selectedDepartments.value = []
    selectedUsers.value = []
  }
})
</script>

<style scoped>
.permission-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.permission-panel.visible {
  opacity: 1;
  visibility: visible;
}

.panel-content {
  position: absolute;
  top: 0;
  right: 0;
  width: clamp(400px, 50vw, 800px);
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
}

.panel-content.visible {
  transform: translateX(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.file-type-pdf { background: #fef3c7; }
.file-type-doc { background: #dbeafe; }
.file-type-txt { background: #f3f4f6; }
.file-type-md { background: #ecfdf5; }
.file-type-xlsx { background: #fef2f2; }

.file-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.file-meta {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
}

.file-meta span {
  margin: 0 4px;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background: #fee2e2;
  color: #dc2626;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid #fecaca;
}

.close-btn:hover {
  background: #fecaca;
}

.permission-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.permission-section {
  width: 100%;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.section-description {
  margin: 0 0 24px 0;
  color: #64748b;
  font-size: 14px;
}

.permission-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

@media (max-width: 600px) {
  .permission-options {
    grid-template-columns: 1fr;
  }
}

.permission-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 80px;
}

.permission-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.permission-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.option-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.option-description {
  font-size: 14px;
  color: #64748b;
}

.option-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.permission-option.active .option-radio {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.permission-details {
  border-top: 1px solid #e2e8f0;
  padding-top: 24px;
}

.detail-section h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.selected-count {
  font-size: 12px;
  color: #3b82f6;
  background: #eff6ff;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.role-selector-wrapper,
.dept-selector-wrapper,
.user-selector-wrapper {
  margin-top: 12px;
}

.role-selector-wrapper :deep(.el-select) {
  width: 100%;
}

.dept-selector-wrapper :deep(.el-tree-select) {
  width: 100%;
}

.user-selector-wrapper :deep(.selector-card) {
  border: none;
  box-shadow: none;
}



.panel-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #f1f5f9;
  color: #334155;
}

/* 批量模式样式 */
.batch-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.batch-icon {
  width: 40px;
  height: 40px;
  background: #eff6ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 18px;
}

.batch-details {
  flex: 1;
}

.batch-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.batch-meta {
  font-size: 14px;
  color: #64748b;
  margin-top: 2px;
}

.batch-files-list {
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.files-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.toggle-files-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.toggle-files-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.files-container {
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item .file-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.file-item .file-info {
  flex: 1;
  min-width: 0;
}

.file-item .file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-item .file-meta {
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
}

.more-files {
  padding: 12px 16px;
  text-align: center;
  font-size: 12px;
  color: #64748b;
  background: #f8fafc;
  border-top: 1px solid #f1f5f9;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #94a3b8;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-content {
    width: 100vw;
  }

  .panel-header {
    padding: 16px 20px;
  }

  .file-name, .batch-title {
    font-size: 16px;
  }

  .permission-content {
    padding: 20px;
  }

  .permission-options {
    gap: 8px;
  }

  .permission-option {
    padding: 12px;
    min-height: 70px;
  }

  .option-title {
    font-size: 14px;
  }

  .option-description {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-right {
    align-self: flex-end;
  }

  .permission-option {
    gap: 8px;
    padding: 10px;
  }

  .option-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }

  .panel-footer {
    padding: 12px 20px;
    flex-direction: column;
    gap: 8px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
