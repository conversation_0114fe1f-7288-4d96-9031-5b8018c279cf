<template>
  <div class="node-details">
    <!-- 节点基本信息 -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
      <div class="flex items-center gap-3 mb-3">
        <div
          class="w-8 h-8 rounded-full flex items-center justify-center text-white"
          :class="getNodeColorClass(node.type)"
        >
          {{ getNodeIcon(node.data.type) }}
        </div>
        <div>
          <h4 class="font-medium text-gray-900">{{ node.data.label }}</h4>
          <p class="text-sm text-gray-500">{{ getNodeTypeName(node.data.type) }}</p>
        </div>
      </div>
      
      <!-- 节点ID -->
      <div class="text-xs text-gray-500">
        <span class="font-medium">ID:</span> {{ node.id }}
      </div>
    </div>

    <!-- 节点属性 -->
    <div v-if="node.data.properties" class="mb-4">
      <h5 class="font-medium text-gray-900 mb-3">属性</h5>
      <div class="space-y-2">
        <div
          v-for="(value, key) in node.data.properties"
          :key="key"
          class="flex justify-between items-start p-2 bg-gray-50 rounded"
        >
          <span class="text-sm font-medium text-gray-600 capitalize">{{ key }}:</span>
          <span class="text-sm text-gray-900 text-right flex-1 ml-2">{{ formatValue(value) }}</span>
        </div>
      </div>
    </div>

    <!-- 连接信息 -->
    <div class="mb-4">
      <h5 class="font-medium text-gray-900 mb-3">连接信息</h5>
      <div class="grid grid-cols-2 gap-3">
        <div class="text-center p-3 bg-blue-50 rounded-lg">
          <div class="text-lg font-semibold text-blue-600">{{ incomingCount }}</div>
          <div class="text-xs text-blue-500">入度</div>
        </div>
        <div class="text-center p-3 bg-green-50 rounded-lg">
          <div class="text-lg font-semibold text-green-600">{{ outgoingCount }}</div>
          <div class="text-xs text-green-500">出度</div>
        </div>
      </div>
    </div>

    <!-- 相关节点 -->
    <div v-if="relatedNodes.length > 0" class="mb-4">
      <h5 class="font-medium text-gray-900 mb-3">相关节点</h5>
      <div class="space-y-2 max-h-40 overflow-y-auto">
        <div
          v-for="related in relatedNodes"
          :key="related.id"
          class="flex items-center gap-2 p-2 bg-gray-50 rounded hover:bg-gray-100 cursor-pointer transition-colors"
          @click="$emit('selectNode', related)"
        >
          <div
            class="w-4 h-4 rounded-full flex items-center justify-center text-xs text-white"
            :class="getNodeColorClass(related.type)"
          >
            {{ getNodeIcon(related.data.type) }}
          </div>
          <span class="text-sm text-gray-900 flex-1 truncate">{{ related.data.label }}</span>
          <span class="text-xs text-gray-500">{{ related.relationship }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <button
        @click="$emit('editNode', node)"
        class="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        编辑
      </button>
      <button
        @click="$emit('deleteNode', node)"
        class="px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
      >
        删除
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Node } from '@vue-flow/core'

interface Props {
  node: Node
}

interface Emits {
  (e: 'selectNode', node: any): void
  (e: 'editNode', node: Node): void
  (e: 'deleteNode', node: Node): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const incomingCount = computed(() => {
  // 这里应该从图谱数据中计算入度
  return Math.floor(Math.random() * 10) + 1
})

const outgoingCount = computed(() => {
  // 这里应该从图谱数据中计算出度
  return Math.floor(Math.random() * 10) + 1
})

const relatedNodes = computed(() => {
  // 这里应该从图谱数据中获取相关节点
  return [
    {
      id: 'related-1',
      type: 'entity',
      data: { label: '相关实体1', type: 'product' },
      relationship: '关联'
    },
    {
      id: 'related-2',
      type: 'concept',
      data: { label: '相关概念1', type: 'concept' },
      relationship: '包含'
    }
  ]
})

// 工具函数
const getNodeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    product: '📦',
    feature: '⚡',
    user: '👤',
    concept: '💡',
    category: '📂',
    default: '🔵'
  }
  return iconMap[type] || iconMap.default
}

const getNodeColorClass = (nodeType: string): string => {
  const colorMap: Record<string, string> = {
    entity: 'bg-blue-500',
    concept: 'bg-green-500',
    default: 'bg-gray-500'
  }
  return colorMap[nodeType] || colorMap.default
}

const getNodeTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    product: '产品',
    feature: '功能',
    user: '用户',
    concept: '概念',
    category: '类别'
  }
  return typeMap[type] || type
}

const formatValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}
</script>
