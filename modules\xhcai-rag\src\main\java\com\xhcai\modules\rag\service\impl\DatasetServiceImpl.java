package com.xhcai.modules.rag.service.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.modules.rag.entity.Dataset;
import com.xhcai.modules.rag.mapper.DatasetMapper;
import com.xhcai.modules.rag.service.IDatasetService;
import com.xhcai.modules.rag.vo.DatasetVO;
import com.xhcai.modules.system.entity.SysDept;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.service.ISysDeptService;
import com.xhcai.modules.system.service.ISysUserService;
import com.xhcai.modules.system.vo.SysUserVO;

/**
 * 知识库服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class DatasetServiceImpl extends ServiceImpl<DatasetMapper, Dataset> implements IDatasetService {

    private static final Logger log = LoggerFactory.getLogger(DatasetServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    @Override
    public IPage<DatasetVO> pageByTenant(Long current, Long size, String tenantId, String name, String dataSourceType) {
        Page<DatasetVO> page = new Page<>(current, size);
        return baseMapper.selectPageByTenant(page, tenantId, name, dataSourceType);
    }

    @Override
    public IPage<DatasetVO> pageByTenantWithUserInfo(Long current, Long size, String tenantId, String name, String dataSourceType) {
        // 1. 先查询基础的Dataset分页数据
        IPage<DatasetVO> page = pageByTenant(current, size, tenantId, name, dataSourceType);

        if (page.getRecords().isEmpty()) {
            return page;
        }

        // 2. 收集所有的创建人和更新人ID
        Set<String> userIds = new HashSet<>();
        for (DatasetVO dataset : page.getRecords()) {
            if (dataset.getCreateBy() != null) {
                userIds.add(dataset.getCreateBy());
            }
            if (dataset.getUpdateBy() != null) {
                userIds.add(dataset.getUpdateBy());
            }
        }

        if (userIds.isEmpty()) {
            return page;
        }

        // 3. 批量查询用户信息
        Map<String, SysUserVO> userInfoMap = batchGetUserInfo(userIds);

        // 4. 将用户信息设置到Dataset中
        for (DatasetVO dataset : page.getRecords()) {
            if (dataset.getCreateBy() != null) {
                SysUserVO createUserInfo = userInfoMap.get(dataset.getCreateBy());
                if (createUserInfo != null) {
                    dataset.setCreateUser(createUserInfo);
                }
            }

            if (dataset.getUpdateBy() != null) {
                SysUserVO updateUserInfo = userInfoMap.get(dataset.getUpdateBy());
                if (updateUserInfo != null) {
                    dataset.setUpdateUser(updateUserInfo);
                }
            }
        }

        return page;
    }

    /**
     * 批量获取用户信息
     *
     * @param userIds 用户ID集合
     * @return 用户信息Map，key为用户ID，value为用户信息
     */
    private Map<String, SysUserVO> batchGetUserInfo(Set<String> userIds) {
        Map<String, SysUserVO> userInfoMap = new HashMap<>();

        if (userIds.isEmpty()) {
            return userInfoMap;
        }

        List<SysUser> pageResult = userService.listByIds(userIds);

        // 收集用户部门信息
        Set<String> deptIds = new HashSet<>();
        for (SysUser user : pageResult) {
            SysUserVO sysUserVO = new SysUserVO();
            BeanUtils.copyProperties(user, sysUserVO);
            userInfoMap.put(user.getId(), sysUserVO);
            if (StringUtils.hasText(user.getDeptId())) {
                deptIds.add(user.getDeptId());
            }
        }

        if (!deptIds.isEmpty()) {
            // 将Set转换为List，避免MyBatis参数绑定问题
            List<SysDept> sysDepts = deptService.listByIds(deptIds);
            userInfoMap.forEach((key, value) -> {
                if (StringUtils.hasText(value.getDeptId())) {
                    Optional<SysDept> match = sysDepts.stream()
                            .filter(sysDept -> sysDept.getId().equals(value.getDeptId()))
                            .findFirst();
                    match.ifPresent(sysDept -> value.setDeptName(sysDept.getDeptName()));
                }
            });
        }

        return userInfoMap;
    }

    @Override
    public List<DatasetVO> listByTenantId(String tenantId) {
        return baseMapper.selectByTenantId(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Dataset createDataset(Dataset dataset) {
//        // 检查名称是否重复
//        if (existsByName(dataset.getName(), dataset.getTenantId(), null)) {
//            throw new RuntimeException("知识库名称已存在");
//        }

        if (save(dataset)) {
            log.info("知识库创建成功: {}, 知识库名称: {}", dataset.getId(), dataset.getName());
            return dataset;
        } else {
            throw new RuntimeException("知识库创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Dataset updateDataset(Dataset dataset) {
        log.info("更新知识库: {}", dataset.getId());

        // 检查知识库是否存在
        Dataset existingDataset = getById(dataset.getId());
        if (existingDataset == null) {
            throw new RuntimeException("知识库不存在");
        }

        // 检查名称是否重复
        if (StringUtils.hasText(dataset.getName())
                && existsByName(dataset.getName(), dataset.getTenantId(), dataset.getId())) {
            throw new RuntimeException("知识库名称已存在");
        }

        if (updateById(dataset)) {
            log.info("知识库更新成功: {}", dataset.getId());
            return getById(dataset.getId());
        } else {
            throw new RuntimeException("知识库更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDataset(String id) {
        log.info("删除知识库: {}", id);

        // 检查知识库是否存在
        Dataset dataset = getById(id);
        if (dataset == null) {
            throw new RuntimeException("知识库不存在");
        }

        // TODO: 检查是否有关联的文档，如果有则不允许删除
        if (removeById(id)) {
            log.info("知识库删除成功: {}", id);
            return true;
        } else {
            throw new RuntimeException("知识库删除失败");
        }
    }

    @Override
    public Dataset getDatasetById(String id) {
        return getById(id);
    }

    @Override
    public boolean existsByName(String name, String tenantId, String excludeId) {
        LambdaQueryWrapper<Dataset> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Dataset::getName, name)
                .eq(Dataset::getTenantId, tenantId);

        if (StringUtils.hasText(excludeId)) {
            wrapper.ne(Dataset::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public Long countByTenantId(String tenantId) {
        return baseMapper.countByTenantId(tenantId);
    }

    @Override
    public Long countByModelId(String modelId) {
        return baseMapper.countByModelId(modelId);
    }

    @Override
    public IPage<Dataset> pageByUserPermission(Long current, Long size, String tenantId, String userId,
            String deptId, List<String> roleIds, String name) {
        Page<Dataset> page = new Page<>(current, size);
        return baseMapper.selectPageByUserPermission(page, tenantId, userId, deptId, roleIds, name);
    }

    @Override
    public boolean hasPermission(String datasetId, String userId, String deptId, List<String> roleIds) {
        return baseMapper.hasPermission(datasetId, userId, deptId, roleIds);
    }

    @Override
    public boolean hasManagePermission(String datasetId, String userId) {
        // 检查是否是创建者
        Dataset dataset = getById(datasetId);
        if (dataset != null && userId.equals(dataset.getCreateBy())) {
            return true;
        }

        // TODO: 检查是否有管理权限
        return false;
    }

    @Override
    public Object getDatasetStats(String datasetId) {
        log.info("获取知识库统计信息: {}", datasetId);

        Dataset dataset = getById(datasetId);
        if (dataset == null) {
            throw new RuntimeException("知识库不存在");
        }

        // TODO: 实现统计信息获取逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("datasetId", datasetId);
        stats.put("name", dataset.getName());
        stats.put("documentCount", 0);
        stats.put("segmentCount", 0);
        stats.put("totalTokens", 0);
        stats.put("createTime", dataset.getCreateTime());

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Dataset copyDataset(String sourceId, String newName, String tenantId, String userId) {
        log.info("复制知识库: {} -> {}", sourceId, newName);

        Dataset sourceDataset = getById(sourceId);
        if (sourceDataset == null) {
            throw new RuntimeException("源知识库不存在");
        }

        // 检查新名称是否重复
        if (existsByName(newName, tenantId, null)) {
            throw new RuntimeException("知识库名称已存在");
        }

        // 创建新知识库
        Dataset newDataset = new Dataset();
        newDataset.setName(newName);
        newDataset.setDescription(sourceDataset.getDescription());
        newDataset.setDataSourceType(sourceDataset.getDataSourceType());
        newDataset.setModelId(sourceDataset.getModelId());
        newDataset.setVectorizationConfig(sourceDataset.getVectorizationConfig());
        newDataset.setTenantId(tenantId);

        return createDataset(newDataset);
    }

    @Override
    public Object exportDatasetConfig(String datasetId) {
        log.info("导出知识库配置: {}", datasetId);

        Dataset dataset = getById(datasetId);
        if (dataset == null) {
            throw new RuntimeException("知识库不存在");
        }

        // TODO: 实现配置导出逻辑
        Map<String, Object> config = new HashMap<>();
        config.put("name", dataset.getName());
        config.put("description", dataset.getDescription());
        config.put("dataSourceType", dataset.getDataSourceType());
        config.put("vectorizationConfig", dataset.getVectorizationConfig());

        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Dataset importDatasetConfig(Object config, String tenantId, String userId) {
        log.info("导入知识库配置");

        if (!(config instanceof Map)) {
            throw new RuntimeException("配置格式错误");
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> configMap = (Map<String, Object>) config;

        // 创建新知识库
        Dataset dataset = new Dataset();
        dataset.setName((String) configMap.get("name"));
        dataset.setDescription((String) configMap.get("description"));
        dataset.setDataSourceType((String) configMap.get("dataSourceType"));
        dataset.setVectorizationConfig((Map<String, Object>) configMap.get("vectorizationConfig"));
        dataset.setTenantId(tenantId);

        return createDataset(dataset);
    }
}
