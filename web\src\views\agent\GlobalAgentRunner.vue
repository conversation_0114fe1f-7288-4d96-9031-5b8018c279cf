<template>
  <!-- 全局智能体运行管理器 -->
  <AgentRunnerManager :events="events" ref="runnerManager" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AgentRunnerManager from './AgentRunnerManager.vue'
import type { Agent, AgentRunnerEvents } from '@/composables/useAgentRunner'

// 组件引用
const runnerManager = ref<InstanceType<typeof AgentRunnerManager> | null>(null)

// 事件处理
const events: AgentRunnerEvents = {
  onAgentOpened: (agent: Agent) => {
    console.log('智能体已打开:', agent.name)
  },
  onAgentClosed: (agentId: string) => {
    console.log('智能体已关闭:', agentId)
  },
  onAgentMinimized: (agentId: string) => {
    console.log('智能体已最小化:', agentId)
  },
  onAgentRestored: (agentId: string) => {
    console.log('智能体已恢复:', agentId)
  }
}

// 暴露全局方法
const openAgent = (agent: Agent) => {
  runnerManager.value?.openRunnerModal(agent)
}

// 暴露给全局使用
defineExpose({
  openAgent
})
</script>

<style scoped>
/* 全局样式可以在这里定义 */
</style>
