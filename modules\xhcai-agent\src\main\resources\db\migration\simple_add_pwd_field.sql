-- 简化版本：为第三方智能体账号表添加密码字段

-- 添加pwd字段
ALTER TABLE third_platform_account ADD COLUMN IF NOT EXISTS pwd VARCHAR(100) COMMENT '密码（BCrypt加密存储）';

-- 修改api_key字段为可空
ALTER TABLE third_platform_account ALTER COLUMN api_key DROP NOT NULL;

-- 为pwd字段添加索引
CREATE INDEX IF NOT EXISTS idx_third_platform_account_pwd ON third_platform_account(pwd);

-- 查看表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'third_platform_account' 
  AND table_schema = 'xhcai_plus'
  AND column_name IN ('api_key', 'pwd')
ORDER BY ordinal_position;
