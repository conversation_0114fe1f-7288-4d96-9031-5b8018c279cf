package com.xhcai.modules.dify.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * Dify聊天响应DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Schema(description = "Dify聊天响应")
public class DifyChatResponseDTO {

    /**
     * 事件类型
     */
    @Schema(description = "事件类型")
    private String event;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    @JsonProperty("task_id")
    private String taskId;

    /**
     * 工作流执行ID
     */
    @Schema(description = "工作流执行ID")
    @JsonProperty("workflow_run_id")
    private String workflowRunId;

    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    @JsonProperty("message_id")
    private String messageId;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 答案内容
     */
    @Schema(description = "答案内容")
    private String answer;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * 元数据
     */
    @Schema(description = "元数据")
    private Map<String, Object> metadata;

    /**
     * 工作流数据
     */
    @Schema(description = "工作流数据")
    private Object data;

    /**
     * 使用情况
     */
    @Schema(description = "使用情况")
    private UsageInfo usage;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String error;

    /**
     * 状态码
     */
    @Schema(description = "状态码")
    private Integer code;

    public DifyChatResponseDTO() {
    }

    // Getters and Setters
    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getWorkflowRunId() {
        return workflowRunId;
    }

    public void setWorkflowRunId(String workflowRunId) {
        this.workflowRunId = workflowRunId;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public UsageInfo getUsage() {
        return usage;
    }

    public void setUsage(UsageInfo usage) {
        this.usage = usage;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 使用情况信息
     */
    public static class UsageInfo {
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;

        @JsonProperty("prompt_unit_price")
        private String promptUnitPrice;

        @JsonProperty("prompt_price_unit")
        private String promptPriceUnit;

        @JsonProperty("prompt_price")
        private String promptPrice;

        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        @JsonProperty("completion_unit_price")
        private String completionUnitPrice;

        @JsonProperty("completion_price_unit")
        private String completionPriceUnit;

        @JsonProperty("completion_price")
        private String completionPrice;

        @JsonProperty("total_tokens")
        private Integer totalTokens;

        @JsonProperty("total_price")
        private String totalPrice;

        private String currency;

        @JsonProperty("latency")
        private Double latency;

        // Getters and Setters
        public Integer getPromptTokens() {
            return promptTokens;
        }

        public void setPromptTokens(Integer promptTokens) {
            this.promptTokens = promptTokens;
        }

        public String getPromptUnitPrice() {
            return promptUnitPrice;
        }

        public void setPromptUnitPrice(String promptUnitPrice) {
            this.promptUnitPrice = promptUnitPrice;
        }

        public String getPromptPriceUnit() {
            return promptPriceUnit;
        }

        public void setPromptPriceUnit(String promptPriceUnit) {
            this.promptPriceUnit = promptPriceUnit;
        }

        public String getPromptPrice() {
            return promptPrice;
        }

        public void setPromptPrice(String promptPrice) {
            this.promptPrice = promptPrice;
        }

        public Integer getCompletionTokens() {
            return completionTokens;
        }

        public void setCompletionTokens(Integer completionTokens) {
            this.completionTokens = completionTokens;
        }

        public String getCompletionUnitPrice() {
            return completionUnitPrice;
        }

        public void setCompletionUnitPrice(String completionUnitPrice) {
            this.completionUnitPrice = completionUnitPrice;
        }

        public String getCompletionPriceUnit() {
            return completionPriceUnit;
        }

        public void setCompletionPriceUnit(String completionPriceUnit) {
            this.completionPriceUnit = completionPriceUnit;
        }

        public String getCompletionPrice() {
            return completionPrice;
        }

        public void setCompletionPrice(String completionPrice) {
            this.completionPrice = completionPrice;
        }

        public Integer getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(Integer totalTokens) {
            this.totalTokens = totalTokens;
        }

        public String getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(String totalPrice) {
            this.totalPrice = totalPrice;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public Double getLatency() {
            return latency;
        }

        public void setLatency(Double latency) {
            this.latency = latency;
        }
    }

    @Override
    public String toString() {
        return "DifyChatResponseDTO{" +
                "event='" + event + '\'' +
                ", taskId='" + taskId + '\'' +
                ", workflowRunId='" + workflowRunId + '\'' +
                ", messageId='" + messageId + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", answer='" + answer + '\'' +
                ", createdAt=" + createdAt +
                ", metadata=" + metadata +
                ", data=" + data +
                ", usage=" + usage +
                ", error='" + error + '\'' +
                ", code=" + code +
                '}';
    }
}
