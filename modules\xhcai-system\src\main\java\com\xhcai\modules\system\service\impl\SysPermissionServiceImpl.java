package com.xhcai.modules.system.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.system.dto.SysPermissionQueryDTO;
import com.xhcai.modules.system.entity.SysPermission;
import com.xhcai.modules.system.mapper.SysPermissionMapper;
import com.xhcai.modules.system.mapper.SysRolePermissionMapper;
import com.xhcai.modules.system.service.ISysPermissionService;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.vo.SysPermissionVO;
import com.xhcai.modules.system.vo.SysUserVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限信息服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements ISysPermissionService {

    private static final Logger log = LoggerFactory.getLogger(SysPermissionServiceImpl.class);

    @Autowired
    private SysPermissionMapper permissionMapper;

    @Autowired
    private SysRolePermissionMapper rolePermissionMapper;

    @Override
    public PageResult<SysPermissionVO> selectPermissionPage(SysPermissionQueryDTO queryDTO) {
        Page<SysPermission> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        Page<SysPermission> permissionPage = permissionMapper.selectPermissionPage(page,
                queryDTO.getPermissionCode(),
                queryDTO.getPermissionName(),
                queryDTO.getPermissionType(),
                queryDTO.getStatus());

        List<SysPermissionVO> permissionVOs = permissionPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<>(permissionVOs, permissionPage.getTotal(), permissionPage.getCurrent(), permissionPage.getSize());
    }

    @Override
    public List<SysPermissionVO> selectPermissionList(SysPermissionQueryDTO queryDTO) {
        List<SysPermission> permissions = permissionMapper.selectPermissionList(
                queryDTO.getPermissionCode(),
                queryDTO.getPermissionName(),
                queryDTO.getPermissionType(),
                queryDTO.getStatus());

        return permissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysPermissionVO> selectPermissionTree(SysPermissionQueryDTO queryDTO) {
        List<SysPermission> permissions = permissionMapper.selectPermissionTree(queryDTO.getStatus());
        return buildPermissionTree(permissions);
    }

    @Override
    public SysPermissionVO selectPermissionById(String permissionId) {
        if (permissionId == null) {
            return null;
        }

        SysPermission permission = getById(permissionId);
        if (permission == null) {
            return null;
        }

        SysPermissionVO permissionVO = convertToVO(permission);

        // 设置父权限名称
        if (permission.getParentId() != null && !CommonConstants.PERMISSION_TREE_ROOT_ID.equals(permission.getParentId())) {
            SysPermission parentPermission = getById(permission.getParentId());
            if (parentPermission != null) {
                permissionVO.setParentName(parentPermission.getPermissionName());
            }
        }

        // 设置是否有子权限
        Integer childrenCount = permissionMapper.hasChildren(permissionId);
        permissionVO.setHasChildren(childrenCount > 0);

        return permissionVO;
    }

    @Override
    public SysPermission selectByPermissionCode(String permissionCode) {
        if (!StringUtils.hasText(permissionCode)) {
            return null;
        }
        return permissionMapper.selectByPermissionCode(permissionCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertPermission(SysPermission permission) {
        // 参数校验
        validatePermission(permission, true);

        // 检查权限编码是否已存在
        if (StringUtils.hasText(permission.getPermissionCode()) && existsPermissionCode(permission.getPermissionCode(), null)) {
            throw new BusinessException("权限编码已存在");
        }

        // 检查同级权限名称是否已存在
        if (existsPermissionName(permission.getPermissionName(), permission.getParentId(), null)) {
            throw new BusinessException("同级权限名称已存在");
        }

        // 设置排序号
        if (permission.getOrderNum() == null) {
            Integer maxOrderNum = permissionMapper.selectMaxOrderNum(permission.getParentId());
            permission.setOrderNum(maxOrderNum + 1);
        }

        // 设置默认状态
        if (!StringUtils.hasText(permission.getStatus())) {
            permission.setStatus(CommonConstants.STATUS_NORMAL);
        }

        // 设置默认父权限ID
        if (permission.getParentId() == null) {
            permission.setParentId(CommonConstants.PERMISSION_TREE_ROOT_ID);
        }

        return save(permission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePermission(SysPermission permission) {
        // 参数校验
        validatePermission(permission, false);

        // 检查权限是否存在
        SysPermission existPermission = getById(permission.getId());
        if (existPermission == null) {
            throw new BusinessException("权限不存在");
        }

        // 检查权限编码是否已存在（排除自己）
        if (StringUtils.hasText(permission.getPermissionCode()) && existsPermissionCode(permission.getPermissionCode(), permission.getId())) {
            throw new BusinessException("权限编码已存在");
        }

        // 检查同级权限名称是否已存在（排除自己）
        if (existsPermissionName(permission.getPermissionName(), permission.getParentId(), permission.getId())) {
            throw new BusinessException("同级权限名称已存在");
        }

        // 检查是否将权限移动到自己的子权限下
        if (permission.getParentId() != null && isChildPermission(permission.getId(), permission.getParentId())) {
            throw new BusinessException("不能将权限移动到自己的子权限下");
        }

        return updateById(permission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePermissions(List<String> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return false;
        }

        for (String permissionId : permissionIds) {
            // 检查是否存在子权限
            if (hasChildren(permissionId)) {
                SysPermission permission = getById(permissionId);
                throw new BusinessException("权限【" + permission.getPermissionName() + "】存在子权限，不能删除");
            }
        }

        // 删除权限角色关联
        rolePermissionMapper.deleteByPermissionIds(permissionIds);

        return removeByIds(permissionIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enablePermission(String permissionId) {
        return updatePermissionStatus(permissionId, CommonConstants.STATUS_NORMAL);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disablePermission(String permissionId) {
        return updatePermissionStatus(permissionId, CommonConstants.STATUS_DISABLE);
    }

    @Override
    public boolean existsPermissionCode(String permissionCode, String excludeId) {
        if (!StringUtils.hasText(permissionCode)) {
            return false;
        }
        return permissionMapper.existsPermissionCode(permissionCode, excludeId) > 0;
    }

    @Override
    public boolean existsPermissionName(String permissionName, String parentId, String excludeId) {
        if (!StringUtils.hasText(permissionName)) {
            return false;
        }
        return permissionMapper.existsPermissionName(permissionName, parentId, excludeId) > 0;
    }

    @Override
    public boolean hasChildren(String permissionId) {
        if (permissionId == null) {
            return false;
        }
        return permissionMapper.hasChildren(permissionId) > 0;
    }

    @Override
    public List<SysPermissionVO> selectPermissionsByUserId(SysUserVO sysUserVO) {
        if (sysUserVO == null) {
            return new ArrayList<>();
        }

        List<SysPermission> permissions = permissionMapper.selectPermissionsByUserId(sysUserVO.getId(), sysUserVO.getTenantId());
        return permissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public Set<String> selectPermissionCodesByUserId(String userId) {
        if (userId == null) {
            return Set.of();
        }
        String tenantId = SecurityUtils.getCurrentTenantId();
        return permissionMapper.selectPermissionCodesByUserId(userId, tenantId);
    }

    @Override
    public List<SysPermissionVO> selectPermissionsByRoleId(String roleId) {
        if (roleId == null) {
            return new ArrayList<>();
        }

        List<SysPermission> permissions = permissionMapper.selectPermissionsByRoleId(roleId);
        return permissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysPermissionVO> buildPermissionTree(List<SysPermission> permissions) {
        if (CollectionUtils.isEmpty(permissions)) {
            return new ArrayList<>();
        }

        List<SysPermissionVO> permissionVOs = permissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return buildTree(permissionVOs, CommonConstants.PERMISSION_TREE_ROOT_ID);
    }

    @Override
    public List<SysPermissionVO> buildPermissionSelectTree(String excludePermissionId) {
        List<SysPermission> allPermissions = permissionMapper.selectPermissionTree(CommonConstants.STATUS_NORMAL);

        if (excludePermissionId != null) {
            // 排除指定权限及其子权限
            allPermissions = allPermissions.stream()
                    .filter(permission -> !permission.getId().equals(excludePermissionId)
                            && !isChildPermission(excludePermissionId, permission.getId()))
                    .collect(Collectors.toList());
        }

        return buildPermissionTree(allPermissions);
    }

    @Override
    public String getPermissionPath(String permissionId) {
        if (permissionId == null) {
            return "";
        }

        SysPermission permission = getById(permissionId);
        if (permission == null) {
            return "";
        }

        StringBuilder path = new StringBuilder();
        buildPermissionPath(permission, path);
        return path.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> permissionIds, String status) {
        if (CollectionUtils.isEmpty(permissionIds) || !StringUtils.hasText(status)) {
            return false;
        }

        return lambdaUpdate()
                .in(SysPermission::getId, permissionIds)
                .set(SysPermission::getStatus, status)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncPermissionOrder(List<SysPermission> permissions) {
        if (CollectionUtils.isEmpty(permissions)) {
            return false;
        }

        for (SysPermission permission : permissions) {
            if (permission.getId() != null && permission.getOrderNum() != null) {
                lambdaUpdate()
                        .eq(SysPermission::getId, permission.getId())
                        .set(SysPermission::getOrderNum, permission.getOrderNum())
                        .update();
            }
        }

        return true;
    }

    @Override
    public List<SysPermissionVO> selectAllAvailablePermissions() {
        List<SysPermission> permissions = permissionMapper.selectAllAvailablePermissions();
        return permissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysPermissionVO> selectPermissionsByType(String permissionType) {
        if (!StringUtils.hasText(permissionType)) {
            return new ArrayList<>();
        }

        List<SysPermission> permissions = permissionMapper.selectPermissionsByType(permissionType);
        return permissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean movePermission(String permissionId, String newParentId) {
        if (permissionId == null || newParentId == null) {
            throw new BusinessException("权限ID和新父权限ID不能为空");
        }

        SysPermission permission = getById(permissionId);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }

        // 检查是否将权限移动到自己的子权限下
        if (isChildPermission(permissionId, newParentId)) {
            throw new BusinessException("不能将权限移动到自己的子权限下");
        }

        // 更新父权限
        permission.setParentId(newParentId);
        return updateById(permission);
    }

    /**
     * 参数校验
     */
    private void validatePermission(SysPermission permission, boolean isCreate) {
        if (permission == null) {
            throw new BusinessException("权限信息不能为空");
        }

        if (!StringUtils.hasText(permission.getPermissionName())) {
            throw new BusinessException("权限名称不能为空");
        }

        if (isCreate && !StringUtils.hasText(permission.getPermissionCode())) {
            throw new BusinessException("权限编码不能为空");
        }

        if (permission.getParentId() == null) {
            permission.setParentId(CommonConstants.PERMISSION_TREE_ROOT_ID);
        }
    }

    /**
     * 更新权限状态
     */
    private boolean updatePermissionStatus(String permissionId, String status) {
        if (permissionId == null || !StringUtils.hasText(status)) {
            throw new BusinessException("权限ID和状态不能为空");
        }

        SysPermission permission = getById(permissionId);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }

        permission.setStatus(status);
        return updateById(permission);
    }

    /**
     * 检查是否是子权限
     */
    private boolean isChildPermission(String permissionId, String parentId) {
        if (permissionId == null || parentId == null) {
            return false;
        }

        if (permissionId.equals(parentId)) {
            return true;
        }

        SysPermission parentPermission = getById(parentId);
        if (parentPermission == null || parentPermission.getParentId() == null) {
            return false;
        }

        return isChildPermission(permissionId, parentPermission.getParentId());
    }

    /**
     * 构建权限路径
     */
    private void buildPermissionPath(SysPermission permission, StringBuilder path) {
        if (permission.getParentId() != null && !CommonConstants.PERMISSION_TREE_ROOT_ID.equals(permission.getParentId())) {
            SysPermission parentPermission = getById(permission.getParentId());
            if (parentPermission != null) {
                buildPermissionPath(parentPermission, path);
                path.append("/");
            }
        }
        path.append(permission.getPermissionName());
    }

    /**
     * 转换为VO
     */
    private SysPermissionVO convertToVO(SysPermission permission) {
        if (permission == null) {
            return null;
        }

        SysPermissionVO vo = new SysPermissionVO();
        BeanUtils.copyProperties(permission, vo);

        // 设置状态名称
        if (CommonConstants.STATUS_NORMAL.equals(permission.getStatus())) {
            vo.setStatusName("正常");
        } else if (CommonConstants.STATUS_DISABLE.equals(permission.getStatus())) {
            vo.setStatusName("停用");
        } else {
            vo.setStatusName("未知");
        }

        // 设置权限类型名称
        switch (permission.getPermissionType()) {
            case "1" -> vo.setPermissionTypeName("菜单");
            case "2" -> vo.setPermissionTypeName("按钮");
            case "3" -> vo.setPermissionTypeName("接口");
            default -> vo.setPermissionTypeName("未知");
        }

        // 判断是否为系统内置权限
        vo.setIsSystem(permission.getPermissionCode() != null && 
                       permission.getPermissionCode().startsWith("system:"));

        return vo;
    }

    /**
     * 构建树形结构
     */
    private List<SysPermissionVO> buildTree(List<SysPermissionVO> permissions, String parentId) {
        List<SysPermissionVO> tree = new ArrayList<>();

        for (SysPermissionVO permission : permissions) {
            if (parentId.equals(permission.getParentId())) {
                List<SysPermissionVO> children = buildTree(permissions, permission.getId());
                permission.setChildren(children);
                permission.setHasChildren(!children.isEmpty());
                tree.add(permission);
            }
        }

        return tree;
    }
}
