package com.yyzs.agent.config;

import com.yyzs.agent.service.ComponentMonitorService;
import com.yyzs.agent.service.ElasticComponentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 应用启动初始化组件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationStartupRunner implements ApplicationRunner {

    private final ElasticComponentService elasticComponentService;
    private final ComponentMonitorService componentMonitorService;

    @Value("${yyzs.agent.install-path:/opt/elastic}")
    private String installBasePath;

    @Value("${yyzs.agent.package-storage-path:/data/packages}")
    private String packageStoragePath;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("YYZS Agent Platform 正在启动...");

        // 1. 创建必要的目录
        createRequiredDirectories();

        // 2. 同步组件状态
        syncComponentStatus();

        // 3. 自动启动组件
        autoStartComponents();

        // 4. 启动监控服务
        startMonitoringService();

        log.info("YYZS Agent Platform 启动完成！");
        log.info("Swagger UI: http://localhost:8080/swagger-ui.html");
        log.info("组件安装路径: {}", installBasePath);
        log.info("安装包存储路径: {}", packageStoragePath);
    }

    /**
     * 创建必要的目录
     */
    private void createRequiredDirectories() {
        try {
            // 创建安装基础目录
            if (!Files.exists(Paths.get(installBasePath))) {
                Files.createDirectories(Paths.get(installBasePath));
                log.info("创建安装目录: {}", installBasePath);
            }

            // 创建安装包存储目录
            if (!Files.exists(Paths.get(packageStoragePath))) {
                Files.createDirectories(Paths.get(packageStoragePath));
                log.info("创建安装包存储目录: {}", packageStoragePath);
            }

            // 创建日志目录
            String logPath = "./logs";
            if (!Files.exists(Paths.get(logPath))) {
                Files.createDirectories(Paths.get(logPath));
                log.info("创建日志目录: {}", logPath);
            }

        } catch (Exception e) {
            log.error("创建必要目录失败", e);
        }
    }

    /**
     * 同步组件状态
     */
    private void syncComponentStatus() {
        try {
            log.info("开始同步组件状态...");
            elasticComponentService.syncComponentStatus();
            log.info("组件状态同步完成");
        } catch (Exception e) {
            log.error("同步组件状态失败", e);
        }
    }

    /**
     * 自动启动组件
     */
    private void autoStartComponents() {
        try {
            log.info("开始自动启动组件...");
            elasticComponentService.autoStartComponents();
            log.info("自动启动组件完成");
        } catch (Exception e) {
            log.error("自动启动组件失败", e);
        }
    }

    /**
     * 启动监控服务
     */
    private void startMonitoringService() {
        try {
            log.info("启动组件监控服务...");
            componentMonitorService.startMonitoring();
            log.info("组件监控服务已启动");
        } catch (Exception e) {
            log.error("启动监控服务失败", e);
        }
    }
}
