<template>
  <div
    class="project-card"
    @contextmenu.prevent="showContextMenuHandler"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="project-icon" :style="{ background: project.iconColor }">
        <i :class="project.icon || 'fas fa-project-diagram'"></i>
      </div>
      <div class="project-info">
        <h3 class="project-name">{{ project.name }}</h3>
        <p class="project-description">{{ project.description }}</p>
      </div>
      <div class="card-menu">
        <button class="menu-trigger" @click.stop="toggleMenu">
          <i class="fas fa-ellipsis-v"></i>
        </button>
        <ProjectCardMenu
          v-if="showMenu"
          class="menu-dropdown"
          @click.stop
          @edit="handleEdit"
          @delete="handleDelete"
          @create-agent="handleCreateAgent"
          @create-knowledge="handleCreateKnowledge"
          @create-graph="handleCreateGraph"
          @link-agent="handleLinkAgent"
          @link-knowledge="handleLinkKnowledge"
          @link-graph="handleLinkGraph"
          @manage-team="handleManageTeam"
          @switch-environment="handleSwitchEnvironment"
        />
      </div>
    </div>

    <!-- 项目状态 -->
    <div class="project-status">
      <div class="status-item">
        <span class="status-label">环境:</span>
        <span :class="['status-badge', `env-${project.environment}`]">
          {{ getEnvironmentLabel(project.environment) }}
        </span>
      </div>
      <div class="status-item">
        <span class="status-label">状态:</span>
        <span :class="['status-badge', `status-${project.status}`]">
          {{ getStatusLabel(project.status) }}
        </span>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-grid">
      <div class="stat-item" @click="navigateToAgents">
        <div class="stat-icon agent">
          <i class="fas fa-robot"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ project.agentCount }}</div>
          <div class="stat-label">智能体</div>
          <div class="stat-detail">{{ project.agentRunningCount }} 运行中</div>
        </div>
      </div>

      <div class="stat-item" @click="navigateToKnowledge">
        <div class="stat-icon knowledge">
          <i class="fas fa-book"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ project.knowledgeCount }}</div>
          <div class="stat-label">知识库</div>
          <div class="stat-detail">{{ project.knowledgeBuiltCount }} 已构建</div>
        </div>
      </div>

      <div class="stat-item" @click="navigateToGraph">
        <div class="stat-icon graph">
          <i class="fas fa-project-diagram"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ project.graphCount }}</div>
          <div class="stat-label">知识图谱</div>
          <div class="stat-detail">{{ project.graphRelationCount }} 关系</div>
        </div>
      </div>
    </div>

    <!-- 团队信息 -->
    <div class="team-info">
      <div class="team-label">
        <i class="fas fa-user"></i>
        负责人: {{ project.owner?.nickname || project.owner?.username || '未设置' }}
      </div>
      <div class="team-members">
        <div class="member-avatars">
          <div
            v-for="member in (project.teamMembers || []).slice(0, 3)"
            :key="member.id"
            class="member-avatar"
            :title="member.user?.nickname || member.user?.username || '未知用户'"
          >
            {{ (member.user?.nickname || member.user?.username || '?').charAt(0) }}
          </div>
          <div v-if="(project.teamMembers || []).length > 3" class="member-more">
            +{{ (project.teamMembers || []).length - 3 }}
          </div>
        </div>
      </div>
    </div>

    <!-- 时间信息 -->
    <div class="time-info">
      <span class="time-item">
        <i class="fas fa-calendar-plus"></i>
        创建: {{ formatDate(project.createTime) }}
      </span>
      <span class="time-item">
        <i class="fas fa-calendar-edit"></i>
        更新: {{ formatDate(project.updateTime) }}
      </span>
    </div>

    <!-- 右键菜单 -->
    <ProjectCardMenu
      v-if="showContextMenu"
      class="context-menu"
      :style="contextMenuStyle"
      @click.stop
      @edit="handleEdit"
      @delete="handleDelete"
      @create-agent="handleCreateAgent"
      @create-knowledge="handleCreateKnowledge"
      @create-graph="handleCreateGraph"
      @link-agent="handleLinkAgent"
      @link-knowledge="handleLinkKnowledge"
      @link-graph="handleLinkGraph"
      @manage-team="handleManageTeam"
      @switch-environment="handleSwitchEnvironment"
    />

    <!-- 关联智能体弹出层 -->
    <LinkAgentModal
      :visible="showLinkAgentModal"
      :project="project"
      @close="showLinkAgentModal = false"
      @confirm="handleLinkAgentConfirm"
    />

    <!-- 新建智能体弹出层 -->
    <CreateAgentModal
      :visible="showCreateAgentModal"
      @close="showCreateAgentModal = false"
      @success="handleCreateAgentSuccess"
    />

    <!-- 新建知识图谱弹出层 -->
    <CreateGraphModal
      v-if="showCreateGraphModal"
      @close="showCreateGraphModal = false"
      @success="handleCreateGraphSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick  } from 'vue'

// 导入API类型
import type { BusinessProject } from '@/api/business'
import ProjectCardMenu from './ProjectCardMenu.vue'
import LinkAgentModal from './LinkAgentModal.vue'
import CreateAgentModal from '@/views/agent/CreateAgentModal.vue'
import CreateGraphModal from '@/views/rkg/components/CreateGraphModal.vue'
import { AgentsAPI } from '@/api/agents'
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

const props = defineProps<{
  project: BusinessProject
}>()

// Emits
const emit = defineEmits<{
  edit: [project: BusinessProject]
  delete: [project: BusinessProject]
  'create-agent': [project: BusinessProject]
  'create-knowledge': [project: BusinessProject]
  'create-graph': [project: BusinessProject]
  'link-agent': [project: BusinessProject]
  'link-knowledge': [project: BusinessProject]
  'link-graph': [project: BusinessProject]
  'manage-team': [project: BusinessProject]
  'switch-environment': [project: BusinessProject]
  'navigate-knowledge': [project: BusinessProject]
  'navigate-graph': [project: BusinessProject]
}>()

// 响应式数据
const showMenu = ref(false)
const showContextMenu = ref(false)
const contextMenuPosition = ref({ x: 0, y: 0 })
const showLinkAgentModal = ref(false)
const showCreateAgentModal = ref(false)
const showCreateGraphModal = ref(false)

// 计算属性
const contextMenuStyle = computed(() => ({
  left: `${contextMenuPosition.value.x}px`,
  top: `${contextMenuPosition.value.y}px`
}))

// 方法
const toggleMenu = () => {
  showMenu.value = !showMenu.value
}

const showContextMenuHandler = async (event: MouseEvent) => {
  // 计算菜单位置，确保不超出视窗边界
  const menuWidth = 160
  const menuHeight = 300
  let x = event.clientX
  let y = event.clientY

  // 检查右边界
  if (x + menuWidth > window.innerWidth) {
    x = window.innerWidth - menuWidth - 10
  }

  // 检查下边界
  if (y + menuHeight > window.innerHeight) {
    y = window.innerHeight - menuHeight - 10
  }

  contextMenuPosition.value = { x, y }
  await nextTick();
  showContextMenu.value = true
}

const hideContextMenu = () => {
  showContextMenu.value = false
}

const hideMenu = () => {
  showMenu.value = false
}

// 事件处理
const handleEdit = () => {
  emit('edit', props.project)
  hideMenu()
  hideContextMenu()
}

const handleDelete = () => {
  emit('delete', props.project)
  hideMenu()
  hideContextMenu()
}

const handleCreateAgent = () => {
  showCreateAgentModal.value = true
  hideMenu()
  hideContextMenu()
}

const handleCreateAgentSuccess = (agentId: string) => {
  showCreateAgentModal.value = false
  // 触发父组件刷新项目数据
  emit('create-agent', props.project)
}

const handleCreateKnowledge = () => {
  // 路由到创建知识库页面
  router.push('/knowledge/create')
  hideMenu()
  hideContextMenu()
}

const handleCreateGraph = () => {
  showCreateGraphModal.value = true
  hideMenu()
  hideContextMenu()
}

const handleCreateGraphSuccess = (graphId: string) => {
  showCreateGraphModal.value = false
  // 触发父组件刷新项目数据
  emit('create-graph', props.project)
}

const handleManageTeam = () => {
  emit('manage-team', props.project)
  hideMenu()
  hideContextMenu()
}

const handleSwitchEnvironment = () => {
  emit('switch-environment', props.project)
  hideMenu()
  hideContextMenu()
}

const handleLinkAgent = () => {
  showLinkAgentModal.value = true
  hideMenu()
  hideContextMenu()
}

const handleLinkAgentConfirm = async (agentIds: string[]) => {
  try {
    await AgentsAPI.linkAgentsToProject(agentIds, props.project.id)
    showLinkAgentModal.value = false
    // 触发父组件刷新项目数据
    emit('link-agent', props.project)
  } catch (error) {
    console.error('关联智能体失败:', error)
  }
}

const handleLinkKnowledge = () => {
  emit('link-knowledge', props.project)
  hideMenu()
  hideContextMenu()
}

const handleLinkGraph = () => {
  emit('link-graph', props.project)
  hideMenu()
  hideContextMenu()
}

const navigateToAgents = () => {
  router.push({
    name: 'Agents',
    query: { projectId: props.project.id, projectName: props.project.name }
  })
}

const navigateToKnowledge = () => {
  emit('navigate-knowledge', props.project)
}

const navigateToGraph = () => {
  emit('navigate-graph', props.project)
}

// 工具方法
const getEnvironmentLabel = (env: string) => {
  const labels = {
    production: '生产',
    test: '测试',
    development: '开发'
  }
  return labels[env as keyof typeof labels] || env
}

const getStatusLabel = (status: string) => {
  const labels = {
    active: '运行中',
    inactive: '已停止',
    maintenance: '维护中'
  }
  return labels[status as keyof typeof labels] || status
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', hideMenu)
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', hideMenu)
  document.removeEventListener('click', hideContextMenu)
})
</script>

<style scoped>
.project-card {
  background: var(--background-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-light);
  padding: 24px;
  transition: var(--transition);
  position: relative;
  cursor: pointer;
}

.project-card:hover {
  box-shadow: var(--shadow-medium);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
}

.project-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.project-icon i {
  font-size: 24px;
  color: white;
}

.project-info {
  flex: 1;
  min-width: 0;
}

.project-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.project-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-menu {
  position: relative;
}

.menu-trigger {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
}

.menu-trigger:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
}

/* 项目状态 */
.project-status {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 500;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.env-production {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.status-badge.env-test {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.status-badge.env-development {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.status-badge.status-active {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.status-badge.status-inactive {
  background: rgba(149, 165, 166, 0.1);
  color: #95a5a6;
}

.status-badge.status-maintenance {
  background: rgba(230, 126, 34, 0.1);
  color: #e67e22;
}

/* 统计信息 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.stat-item:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.agent {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.knowledge {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.graph {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon i {
  font-size: 18px;
  color: white;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  margin: 2px 0;
}

.stat-detail {
  font-size: 11px;
  color: var(--text-muted);
  line-height: 1;
}

/* 团队信息 */
.team-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
}

.team-label {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 6px;
}

.team-label i {
  color: var(--text-muted);
}

.member-avatars {
  display: flex;
  align-items: center;
  gap: -8px;
}

.member-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  border: 2px solid white;
  margin-left: -8px;
  position: relative;
  z-index: 1;
}

.member-avatar:first-child {
  margin-left: 0;
}

.member-more {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: var(--text-muted);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 500;
  border: 2px solid white;
  margin-left: -8px;
}

/* 时间信息 */
.time-info {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: var(--text-muted);
  flex-wrap: wrap;
  gap: 8px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.time-item i {
  font-size: 10px;
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-card {
    padding: 20px;
  }

  .card-header {
    gap: 12px;
  }

  .project-icon {
    width: 40px;
    height: 40px;
  }

  .project-icon i {
    font-size: 20px;
  }

  .project-name {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-icon {
    width: 36px;
    height: 36px;
  }

  .stat-icon i {
    font-size: 16px;
  }

  .stat-number {
    font-size: 18px;
  }

  .team-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .time-info {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .project-card {
    padding: 16px;
  }

  .project-status {
    flex-direction: column;
    gap: 8px;
  }

  .status-item {
    justify-content: space-between;
  }

  .member-avatar,
  .member-more {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
}
</style>
