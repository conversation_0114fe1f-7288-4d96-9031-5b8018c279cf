package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.AiModelCreateDTO;
import com.xhcai.modules.rag.dto.AiModelQueryDTO;
import com.xhcai.modules.rag.dto.AiModelUpdateDTO;
import com.xhcai.modules.rag.entity.AiModel;
import com.xhcai.modules.rag.service.IAiModelService;
import com.xhcai.modules.rag.vo.AiModelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * AI模型配置管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "AI模型配置管理", description = "AI模型配置管理相关接口")
@RestController
@RequestMapping("/api/rag/model")
public class AiModelController {

    @Autowired
    private IAiModelService aiModelService;

    /**
     * 分页查询AI模型列表
     */
    @Operation(summary = "分页查询AI模型", description = "分页查询AI模型列表")
    @GetMapping("/page")
    @RequiresPermissions("ai:model:list")
    public Result<PageResult<AiModelVO>> page(@Valid AiModelQueryDTO queryDTO) {
        PageResult<AiModelVO> pageResult = aiModelService.selectAiModelPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询AI模型列表
     */
    @Operation(summary = "查询AI模型列表", description = "查询AI模型列表")
    @GetMapping("/list")
    @RequiresPermissions("ai:model:list")
    public Result<List<AiModelVO>> list(@Valid AiModelQueryDTO queryDTO) {
        List<AiModelVO> modelList = aiModelService.selectAiModelList(queryDTO);
        return Result.success(modelList);
    }

    /**
     * 根据ID查询AI模型详情
     */
    @Operation(summary = "查询AI模型详情", description = "根据ID查询AI模型详情")
    @GetMapping("/{id}")
    @RequiresPermissions("ai:model:query")
    public Result<AiModelVO> getInfo(@Parameter(description = "模型ID") @PathVariable String id) {
        AiModelVO modelVO = aiModelService.selectAiModelById(id);
        return Result.success(modelVO);
    }

    /**
     * 创建AI模型
     */
    @Operation(summary = "创建AI模型", description = "创建AI模型")
    @PostMapping
    @RequiresPermissions("ai:model:create")
    public Result<Boolean> create(@Valid @RequestBody AiModelCreateDTO createDTO) {
        boolean result = aiModelService.createAiModel(createDTO);
        return Result.success(result);
    }

    /**
     * 更新AI模型
     */
    @Operation(summary = "更新AI模型", description = "更新AI模型")
    @PutMapping
    @RequiresPermissions("ai:model:update")
    public Result<Boolean> update(@Valid @RequestBody AiModelUpdateDTO updateDTO) {
        boolean result = aiModelService.updateAiModel(updateDTO);
        return Result.success(result);
    }

    /**
     * 删除AI模型
     */
    @Operation(summary = "删除AI模型", description = "删除AI模型")
    @DeleteMapping("/{id}")
    @RequiresPermissions("ai:model:delete")
    public Result<Boolean> delete(@Parameter(description = "模型ID") @PathVariable String id) {
        boolean result = aiModelService.deleteAiModel(id);
        return Result.success(result);
    }

    /**
     * 批量删除AI模型
     */
    @Operation(summary = "批量删除AI模型", description = "批量删除AI模型")
    @DeleteMapping("/batch")
    @RequiresPermissions("ai:model:delete")
    public Result<Boolean> batchDelete(@RequestBody List<String> ids) {
        boolean result = aiModelService.batchDeleteAiModels(ids);
        return Result.success(result);
    }

    /**
     * 启用AI模型
     */
    @Operation(summary = "启用AI模型", description = "启用AI模型")
    @PutMapping("/{id}/enable")
    @RequiresPermissions("ai:model:update")
    public Result<Boolean> enable(@Parameter(description = "模型ID") @PathVariable String id) {
        boolean result = aiModelService.enableAiModel(id);
        return Result.success(result);
    }

    /**
     * 停用AI模型
     */
    @Operation(summary = "停用AI模型", description = "停用AI模型")
    @PutMapping("/{id}/disable")
    @RequiresPermissions("ai:model:update")
    public Result<Boolean> disable(@Parameter(description = "模型ID") @PathVariable String id) {
        boolean result = aiModelService.disableAiModel(id);
        return Result.success(result);
    }

    /**
     * 批量更新AI模型状态
     */
    @Operation(summary = "批量更新AI模型状态", description = "批量更新AI模型状态")
    @PutMapping("/batch/status")
    @RequiresPermissions("ai:model:update")
    public Result<Boolean> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> ids = (List<String>) params.get("ids");
        String status = (String) params.get("status");
        boolean result = aiModelService.batchUpdateStatus(ids, status);
        return Result.success(result);
    }

    /**
     * 根据提供商查询模型列表
     */
    @Operation(summary = "根据提供商查询模型", description = "根据提供商查询模型列表")
    @GetMapping("/provider/{provider}")
    @RequiresPermissions("ai:model:list")
    public Result<List<AiModelVO>> getByProvider(@Parameter(description = "提供商") @PathVariable String provider) {
        List<AiModelVO> modelList = aiModelService.selectByProvider(provider);
        return Result.success(modelList);
    }

    /**
     * 根据类型查询模型列表
     */
    @Operation(summary = "根据类型查询模型", description = "根据类型查询模型列表")
    @GetMapping("/type/{type}")
    @RequiresPermissions("ai:model:list")
    public Result<List<AiModelVO>> getByType(@Parameter(description = "模型类型") @PathVariable String type) {
        List<AiModelVO> modelList = aiModelService.selectByType(type);
        return Result.success(modelList);
    }

    /**
     * 统计各提供商的模型数量
     */
    @Operation(summary = "统计各提供商模型数量", description = "统计各提供商的模型数量")
    @GetMapping("/stats/provider")
    @RequiresPermissions("ai:model:list")
    public Result<List<Map<String, Object>>> countByProvider() {
        List<Map<String, Object>> stats = aiModelService.countByProvider();
        return Result.success(stats);
    }

    /**
     * 统计各类型的模型数量
     */
    @Operation(summary = "统计各类型模型数量", description = "统计各类型的模型数量")
    @GetMapping("/stats/type")
    @RequiresPermissions("ai:model:list")
    public Result<List<Map<String, Object>>> countByType() {
        List<Map<String, Object>> stats = aiModelService.countByType();
        return Result.success(stats);
    }

    /**
     * 导出AI模型数据
     */
    @Operation(summary = "导出AI模型数据", description = "导出AI模型数据")
    @PostMapping("/export")
    @RequiresPermissions("ai:model:export")
    public Result<List<AiModelVO>> export(@RequestBody AiModelQueryDTO queryDTO) {
        List<AiModelVO> modelList = aiModelService.exportAiModels(queryDTO);
        return Result.success(modelList);
    }

    /**
     * 导入AI模型数据
     */
    @Operation(summary = "导入AI模型数据", description = "导入AI模型数据")
    @PostMapping("/import")
    @RequiresPermissions("ai:model:import")
    public Result<String> importData(@RequestBody List<AiModel> modelList) {
        String result = aiModelService.importAiModels(modelList);
        return Result.success(result);
    }

    /**
     * 测试模型连接
     */
    @Operation(summary = "测试模型连接", description = "测试模型连接")
    @PostMapping("/{id}/test")
    @RequiresPermissions("ai:model:test")
    public Result<Map<String, Object>> testConnection(@Parameter(description = "模型ID") @PathVariable String id) {
        Map<String, Object> result = aiModelService.testModelConnection(id);
        return Result.success(result);
    }

    /**
     * 复制模型配置
     */
    @Operation(summary = "复制模型配置", description = "复制模型配置")
    @PostMapping("/{id}/copy")
    @RequiresPermissions("ai:model:create")
    public Result<Boolean> copy(
            @Parameter(description = "源模型ID") @PathVariable String id,
            @RequestBody Map<String, String> params) {
        String newName = params.get("newName");
        String newModelId = params.get("newModelId");
        boolean result = aiModelService.copyAiModel(id, newName, newModelId);
        return Result.success(result);
    }
}
