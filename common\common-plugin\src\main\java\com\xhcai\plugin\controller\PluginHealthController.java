package com.xhcai.plugin.controller;

import com.xhcai.plugin.config.PluginProperties;
import com.xhcai.plugin.core.PluginType;
import com.xhcai.plugin.service.PluginServiceManager;
import com.xhcai.plugin.storage.IStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 插件系统健康检查控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/plugin/health")
public class PluginHealthController {
    
    @Autowired
    private PluginServiceManager pluginServiceManager;

    @Autowired(required = false)
    private PluginProperties pluginProperties;

    @Value("${xhcai.plugin.root-path:plugins}")
    private String pluginRootPath;
    
    /**
     * 检查插件系统整体健康状态
     */
    @GetMapping("/status")
    public Map<String, Object> getPluginSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 检查插件服务管理器
            if (pluginServiceManager == null) {
                status.put("status", "ERROR");
                status.put("message", "PluginServiceManager is null");
                return status;
            }
            
            // 检查存储服务
            Map<String, Object> storageStatus = checkStorageServices();
            status.put("storage", storageStatus);
            
            // 检查模型服务
            Map<String, Object> modelStatus = checkModelServices();
            status.put("model", modelStatus);
            
            // 整体状态
            boolean allHealthy = (Boolean) storageStatus.get("healthy") && 
                               (Boolean) modelStatus.get("healthy");
            status.put("status", allHealthy ? "HEALTHY" : "DEGRADED");
            status.put("message", allHealthy ? "All plugin services are healthy" : "Some plugin services have issues");
            
        } catch (Exception e) {
            log.error("Failed to check plugin system status", e);
            status.put("status", "ERROR");
            status.put("message", "Failed to check plugin system: " + e.getMessage());
        }
        
        return status;
    }
    
    /**
     * 检查存储服务状态
     */
    @GetMapping("/storage")
    public Map<String, Object> checkStorageServices() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<IStorageService> services = pluginServiceManager.getStorageServices();
            result.put("count", services.size());
            result.put("healthy", !services.isEmpty());

            if (!services.isEmpty()) {
                IStorageService defaultService = pluginServiceManager.getDefaultStorageService();
                result.put("defaultService", Map.of(
                    "name", defaultService.getServiceName(),
                    "type", defaultService.getServiceType(),
                    "healthy", defaultService.isHealthy()
                ));

                // 添加所有服务的详细信息
                result.put("services", services.stream().map(service -> Map.of(
                    "name", service.getServiceName(),
                    "type", service.getServiceType(),
                    "healthy", service.isHealthy()
                )).toList());
            }

            result.put("message", services.isEmpty() ? "No storage services available" : "Storage services are available");

        } catch (Exception e) {
            log.error("Failed to check storage services", e);
            result.put("healthy", false);
            result.put("count", 0);
            result.put("message", "Failed to check storage services: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }

        return result;
    }
    
    /**
     * 检查模型服务状态
     */
    @GetMapping("/model")
    public Map<String, Object> checkModelServices() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            var services = pluginServiceManager.getModelServices();
            result.put("count", services.size());
            result.put("healthy", !services.isEmpty());
            result.put("message", services.isEmpty() ? "No model services available" : "Model services are available");
            
        } catch (Exception e) {
            log.error("Failed to check model services", e);
            result.put("healthy", false);
            result.put("count", 0);
            result.put("message", "Failed to check model services: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 重启插件上下文
     */
    @GetMapping("/restart/{pluginType}")
    public Map<String, Object> restartPluginContext(@PathVariable String pluginType) {
        Map<String, Object> result = new HashMap<>();

        try {
            PluginType type = PluginType.valueOf(pluginType.toUpperCase());
            pluginServiceManager.restartPluginContext(type);

            result.put("status", "SUCCESS");
            result.put("message", "Plugin context restarted successfully: " + pluginType);

        } catch (Exception e) {
            log.error("Failed to restart plugin context: {}", pluginType, e);
            result.put("status", "ERROR");
            result.put("message", "Failed to restart plugin context: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取详细的插件诊断信息
     */
    @GetMapping("/diagnostic")
    public Map<String, Object> getPluginDiagnostic() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查插件上下文管理器
            var contextManager = pluginServiceManager.getContextManager();
            result.put("contextManagerAvailable", contextManager != null);

            if (contextManager != null) {
                var allContexts = contextManager.getAllContexts();
                result.put("totalContexts", allContexts.size());

                Map<String, Object> contextDetails = new HashMap<>();
                for (var entry : allContexts.entrySet()) {
                    PluginType type = entry.getKey();
                    var context = entry.getValue();

                    Map<String, Object> contextInfo = new HashMap<>();
                    contextInfo.put("status", context.getStatus());
                    contextInfo.put("pluginManagerAvailable", context.getPluginManager() != null);

                    if (context.getPluginManager() != null) {
                        var pluginManager = context.getPluginManager();
                        contextInfo.put("loadedPlugins", pluginManager.getPlugins().size());
                        contextInfo.put("startedPlugins", pluginManager.getStartedPlugins().size());
                        contextInfo.put("pluginDetails", pluginManager.getPlugins().stream()
                            .map(plugin -> Map.of(
                                "id", plugin.getPluginId(),
                                "version", plugin.getDescriptor().getVersion(),
                                "state", plugin.getPluginState(),
                                "path", plugin.getPluginPath().toString()
                            )).toList());
                    }

                    contextDetails.put(type.getCode(), contextInfo);
                }
                result.put("contexts", contextDetails);
            }

            result.put("status", "SUCCESS");

        } catch (Exception e) {
            log.error("Failed to get plugin diagnostic", e);
            result.put("status", "ERROR");
            result.put("message", "Failed to get plugin diagnostic: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取插件配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getPluginConfig() {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("pluginRootPath", pluginRootPath);
            result.put("pluginRootPathExists", new File(pluginRootPath).exists());

            if (pluginProperties != null) {
                result.put("devMode", pluginProperties.isDevMode());
                result.put("hotSwapEnabled", pluginProperties.isHotSwapEnabled());
                result.put("scanInterval", pluginProperties.getScanInterval());
                result.put("startTimeout", pluginProperties.getStartTimeout());
                result.put("stopTimeout", pluginProperties.getStopTimeout());

                if (pluginProperties.getTypes() != null) {
                    Map<String, Object> typeConfigs = new HashMap<>();
                    for (var entry : pluginProperties.getTypes().entrySet()) {
                        String typeName = entry.getKey();
                        var typeConfig = entry.getValue();

                        Map<String, Object> typeInfo = new HashMap<>();
                        typeInfo.put("enabled", typeConfig.isEnabled());
                        typeInfo.put("directory", typeConfig.getDirectory());
                        typeInfo.put("maxPlugins", typeConfig.getMaxPlugins());
                        typeInfo.put("config", typeConfig.getConfig());

                        // 检查插件目录是否存在
                        String pluginDir = pluginRootPath + "/" + typeName;
                        typeInfo.put("directoryExists", new File(pluginDir).exists());
                        typeInfo.put("directoryPath", pluginDir);

                        // 列出目录中的文件
                        File dir = new File(pluginDir);
                        if (dir.exists() && dir.isDirectory()) {
                            File[] files = dir.listFiles();
                            if (files != null) {
                                typeInfo.put("filesInDirectory", List.of(files).stream()
                                    .map(f -> Map.of(
                                        "name", f.getName(),
                                        "isDirectory", f.isDirectory(),
                                        "size", f.length(),
                                        "lastModified", f.lastModified()
                                    )).toList());
                            }
                        }

                        typeConfigs.put(typeName, typeInfo);
                    }
                    result.put("typeConfigs", typeConfigs);
                }
            } else {
                result.put("pluginPropertiesAvailable", false);
            }

            result.put("status", "SUCCESS");

        } catch (Exception e) {
            log.error("Failed to get plugin config", e);
            result.put("status", "ERROR");
            result.put("message", "Failed to get plugin config: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取详细的插件扩展信息
     */
    @GetMapping("/extensions")
    public Map<String, Object> getPluginExtensions() {
        Map<String, Object> result = new HashMap<>();

        try {
            var contextManager = pluginServiceManager.getContextManager();
            if (contextManager == null) {
                result.put("status", "ERROR");
                result.put("message", "PluginContextManager is null");
                return result;
            }

            Map<String, Object> extensionDetails = new HashMap<>();

            for (PluginType pluginType : PluginType.values()) {
                try {
                    var context = contextManager.getPluginContext(pluginType);
                    var pluginManager = context.getPluginManager();

                    Map<String, Object> typeInfo = new HashMap<>();
                    typeInfo.put("contextStatus", context.getStatus());
                    typeInfo.put("loadedPlugins", pluginManager.getPlugins().size());
                    typeInfo.put("startedPlugins", pluginManager.getStartedPlugins().size());

                    // 获取扩展点信息
                    if (pluginType == PluginType.STORAGE) {
                        var storageExtensions = pluginManager.getExtensions(IStorageService.class);
                        typeInfo.put("extensionCount", storageExtensions.size());
                        typeInfo.put("extensions", storageExtensions.stream().map(ext -> Map.of(
                            "className", ext.getClass().getName(),
                            "serviceName", ext.getServiceName(),
                            "serviceType", ext.getServiceType(),
                            "initialized", checkIfInitialized(ext)
                        )).toList());
                    } else if (pluginType == PluginType.MODEL) {
                        var modelExtensions = pluginManager.getExtensions(com.xhcai.plugin.model.IModelService.class);
                        typeInfo.put("extensionCount", modelExtensions.size());
                        typeInfo.put("extensions", modelExtensions.stream().map(ext -> Map.of(
                            "className", ext.getClass().getName(),
                            "serviceName", ext.getServiceName(),
                            "serviceType", ext.getServiceType()
                        )).toList());
                    } else {
                        // 对于其他类型，尝试获取通用扩展

//                        var allExtensions = pluginManager.getExtensions();
//                        typeInfo.put("extensionCount", allExtensions.size());
//                        typeInfo.put("extensions", allExtensions.stream().map(ext -> Map.of(
//                            "className", ext.getClass().getName()
//                        )).toList());
                    }

                    extensionDetails.put(pluginType.getCode(), typeInfo);

                } catch (Exception e) {
                    log.error("Failed to get extensions for plugin type: {}", pluginType, e);
                    extensionDetails.put(pluginType.getCode(), Map.of(
                        "error", e.getMessage(),
                        "errorType", e.getClass().getSimpleName()
                    ));
                }
            }

            result.put("extensions", extensionDetails);
            result.put("status", "SUCCESS");

        } catch (Exception e) {
            log.error("Failed to get plugin extensions", e);
            result.put("status", "ERROR");
            result.put("message", "Failed to get plugin extensions: " + e.getMessage());
        }

        return result;
    }

    private boolean checkIfInitialized(IStorageService service) {
        try {
            // 尝试调用一个简单的方法来检查是否已初始化
            service.getServiceName();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 手动初始化存储服务
     */
    @GetMapping("/init-storage")
    public Map<String, Object> initializeStorageServices() {
        Map<String, Object> result = new HashMap<>();

        try {
            var contextManager = pluginServiceManager.getContextManager();
            if (contextManager == null) {
                result.put("status", "ERROR");
                result.put("message", "PluginContextManager is null");
                return result;
            }

            var storageContext = contextManager.getPluginContext(PluginType.STORAGE);
            var pluginManager = storageContext.getPluginManager();
            var storageExtensions = pluginManager.getExtensions(IStorageService.class);

            result.put("foundExtensions", storageExtensions.size());

            if (storageExtensions.isEmpty()) {
                result.put("status", "WARNING");
                result.put("message", "No storage extensions found");
                return result;
            }

            // 尝试初始化每个存储服务
            List<Map<String, Object>> initResults = new ArrayList<>();

            for (IStorageService service : storageExtensions) {
                Map<String, Object> serviceResult = new HashMap<>();
                serviceResult.put("serviceName", service.getServiceName());
                serviceResult.put("serviceType", service.getServiceType());

                try {
                    // 获取配置并初始化
                    if (pluginProperties != null && pluginProperties.getTypes() != null) {
                        var storageConfig = pluginProperties.getTypes().get("storage");
                        if (storageConfig != null && storageConfig.getConfig() != null) {
                            service.initialize(storageConfig.getConfig());
                            serviceResult.put("initialized", true);
                            serviceResult.put("healthy", service.isHealthy());
                        } else {
                            serviceResult.put("initialized", false);
                            serviceResult.put("error", "No storage configuration found");
                        }
                    } else {
                        serviceResult.put("initialized", false);
                        serviceResult.put("error", "Plugin properties not available");
                    }
                } catch (Exception e) {
                    serviceResult.put("initialized", false);
                    serviceResult.put("error", e.getMessage());
                }

                initResults.add(serviceResult);
            }

            result.put("initResults", initResults);
            result.put("status", "SUCCESS");

        } catch (Exception e) {
            log.error("Failed to initialize storage services", e);
            result.put("status", "ERROR");
            result.put("message", "Failed to initialize storage services: " + e.getMessage());
        }

        return result;
    }
}
