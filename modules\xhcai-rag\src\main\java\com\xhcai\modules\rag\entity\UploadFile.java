package com.xhcai.modules.rag.entity;

import java.time.LocalDateTime;

import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 文件上传记录实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文件上传记录实体")
@Entity
@Data
@Table(name = "upload_files")
@TableName(value = "upload_files")
public class UploadFile extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", example = "用户手册.pdf")
    @Column(name = "original_filename", nullable = false, length = 255)
    @TableField("original_filename")
    private String originalFilename;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "1024000")
    @Column(name = "file_size")
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名", example = "pdf")
    @Column(name = "file_extension", length = 10)
    @TableField("file_extension")
    private String fileExtension;

    /**
     * MIME类型
     */
    @Schema(description = "MIME类型", example = "application/pdf")
    @Column(name = "mime_type", length = 100)
    @TableField("mime_type")
    private String mimeType;

    /**
     * 文件内容hash值
     */
    @Schema(description = "文件内容hash值", example = "a1b2c3d4e5f6...")
    @Column(name = "file_hash", length = 64)
    @TableField("file_hash")
    private String fileHash;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID", example = "dataset123")
    @Column(name = "dataset_id", nullable = false, length = 36)
    @TableField("dataset_id")
    private String datasetId;

    /**
     * 上传批次ID
     */
    @Schema(description = "上传批次ID", example = "batch123")
    @Column(name = "batch_id", length = 36)
    @TableField("batch_id")
    private String batchId;

    /**
     * 关联的文档ID（documents表）
     */
    @Schema(description = "关联的文档ID", example = "doc123")
    @Column(name = "document_id", length = 36)
    @TableField("document_id")
    private String documentId;

    /**
     * MinIO存储桶名称
     */
    @Schema(description = "MinIO存储桶名称", example = "xhcai-plus")
    @Column(name = "minio_bucket", length = 100)
    @TableField("minio_bucket")
    private String minioBucket;

    /**
     * MinIO对象名称（文件路径）
     */
    @Schema(description = "MinIO对象名称", example = "2024/01/15/1642234567890_document.pdf")
    @Column(name = "minio_object_name", length = 500)
    @TableField("minio_object_name")
    private String minioObjectName;

    /**
     * MinIO访问URL
     */
    @Schema(description = "MinIO访问URL")
    @Column(name = "minio_url", length = 1000)
    @TableField("minio_url")
    private String minioUrl;

    /**
     * 上传状态
     */
    @Schema(description = "上传状态", example = "uploaded", allowableValues = {"uploading", "uploaded", "failed", "deleted"})
    @Column(name = "upload_status", length = 20)
    @TableField("upload_status")
    private String uploadStatus;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    @Column(name = "upload_time")
    @TableField("upload_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    /**
     * 上传用户ID
     */
    @Schema(description = "上传用户ID", example = "user_001")
    @Column(name = "upload_user_id", length = 36)
    @TableField("upload_user_id")
    private String uploadUserId;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "upload", allowableValues = {"upload", "delete"})
    @Column(name = "operation_type", length = 20)
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @Column(name = "operation_time")
    @TableField("operation_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    @Column(name = "error_message", length = 1000)
    @TableField("error_message")
    private String errorMessage;
}
