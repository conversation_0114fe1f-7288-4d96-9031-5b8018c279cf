<template>
  <div v-if="hasFilters" class="graph-filter-tips">
    <div class="filter-tips-container">
      <div class="tips-header">
        <el-icon class="header-icon"><Filter /></el-icon>
        <span class="header-text">当前筛选条件</span>
        <el-button 
          type="text" 
          size="small" 
          @click="clearAllFilters"
          class="clear-all-btn"
        >
          清除全部
        </el-button>
      </div>
      
      <div class="filter-content">
        <!-- 节点类型筛选 -->
        <div v-if="selectedNodeTypes.length > 0" class="filter-group">
          <div class="group-label">
            <el-icon><CircleCheckFilled /></el-icon>
            <span>节点类型:</span>
          </div>
          <div class="filter-tags">
            <el-tag
              v-for="nodeType in selectedNodeTypes"
              :key="nodeType"
              type="primary"
              size="small"
              closable
              @close="removeNodeType(nodeType)"
              class="filter-tag"
            >
              <div class="tag-content">
                <div 
                  class="node-color-indicator"
                  :style="{ backgroundColor: getNodeTypeColor(nodeType) }"
                ></div>
                <span>{{ getNodeTypeLabel(nodeType) }}</span>
              </div>
            </el-tag>
          </div>
        </div>

        <!-- 关系类型筛选 -->
        <div v-if="selectedEdgeTypes.length > 0" class="filter-group">
          <div class="group-label">
            <el-icon><Connection /></el-icon>
            <span>关系类型:</span>
          </div>
          <div class="filter-tags">
            <el-tag
              v-for="edgeType in selectedEdgeTypes"
              :key="edgeType"
              type="success"
              size="small"
              closable
              @close="removeEdgeType(edgeType)"
              class="filter-tag"
            >
              <div class="tag-content">
                <div class="edge-indicator"></div>
                <span>{{ edgeType }}</span>
              </div>
            </el-tag>
          </div>
        </div>

        <!-- 关联深度显示 -->
        <div v-if="relationDepth && relationDepth !== 'all'" class="filter-group">
          <div class="group-label">
            <el-icon><Share /></el-icon>
            <span>关联深度:</span>
          </div>
          <div class="filter-tags">
            <el-tag
              type="warning"
              size="small"
              class="filter-tag depth-tag"
            >
              {{ relationDepth }}层关联
            </el-tag>
          </div>
        </div>

        <!-- 布局信息显示 -->
        <div v-if="selectedLayout && selectedLayout !== 'force'" class="filter-group">
          <div class="group-label">
            <el-icon><Grid /></el-icon>
            <span>布局:</span>
          </div>
          <div class="filter-tags">
            <el-tag
              type="info"
              size="small"
              class="filter-tag layout-tag"
            >
              {{ getLayoutLabel(selectedLayout) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElIcon, ElButton, ElTag } from 'element-plus'
import { 
  Filter, 
  CircleCheckFilled,
  Connection, 
  Share, 
  Grid 
} from '@element-plus/icons-vue'

interface Props {
  selectedNodeTypes: string[]
  selectedEdgeTypes: string[]
  relationDepth?: string
  selectedLayout?: string
}

interface Emits {
  (e: 'remove-node-type', nodeType: string): void
  (e: 'remove-edge-type', edgeType: string): void
  (e: 'clear-all'): void
}

const props = withDefaults(defineProps<Props>(), {
  selectedNodeTypes: () => [],
  selectedEdgeTypes: () => [],
  relationDepth: 'all',
  selectedLayout: 'force'
})

const emit = defineEmits<Emits>()

// 节点类型颜色映射（与GraphStatisticsPanel.vue保持一致）
const nodeTypeColors: Record<string, string> = {
  'case': '#ef4444',      // 案件 - 红色
  'person': '#3b82f6',    // 人员 - 蓝色
  'evidence': '#f59e0b',  // 证据 - 橙色
  'location': '#10b981',  // 地点 - 绿色
  'time': '#8b5cf6',      // 时间 - 紫色
  'law': '#6b7280',       // 法律 - 灰色
  'info': '#ec4899',      // 信息 - 粉色
  'company': '#059669',   // 公司 - 深绿色
  'phone': '#0891b2',     // 电话 - 青色
  'address': '#dc2626',   // 地址 - 深红色
  'vehicle': '#7c3aed',   // 车辆 - 深紫色
  'account': '#ea580c',   // 账户 - 深橙色
  'contact': '#4338ca',   // 联系人 - 靛蓝色
  'insurance': '#16a34a', // 保险 - 中绿色
  'violation': '#dc2626', // 违章 - 深红色
  'maintenance': '#0d9488', // 保养 - 青绿色
  'route': '#7c2d12',     // 路线 - 棕色
  'traffic': '#1e40af',   // 交通 - 深蓝色
  'transaction': '#be123c', // 交易 - 深粉色
  'risk': '#dc2626',      // 风险 - 深红色
  'merchant': '#059669',  // 商户 - 深绿色
  'record': '#4338ca',    // 记录 - 靛蓝色
  'executive': '#7c3aed', // 高管 - 深紫色
  'investment': '#ea580c', // 投资 - 深橙色
  'business': '#16a34a',  // 业务 - 中绿色
  'malware': '#dc2626',   // 恶意软件 - 深红色
  'attacker': '#991b1b',  // 攻击者 - 暗红色
  'vulnerability': '#f59e0b', // 漏洞 - 橙色
  'target': '#3b82f6',    // 目标 - 蓝色
  'technique': '#8b5cf6', // 技术 - 紫色
  'threat': '#ec4899',    // 威胁 - 粉色
  'entity': '#6b7280',    // 实体 - 灰色
  'concept': '#10b981'    // 概念 - 绿色
}

// 节点类型标签映射（与GraphStatisticsPanel.vue保持一致）
const nodeTypeLabels: Record<string, string> = {
  'case': '案件',
  'person': '人员',
  'evidence': '证据',
  'location': '地点',
  'time': '时间',
  'law': '法律条文',
  'info': '相关信息',
  'company': '公司',
  'phone': '电话',
  'address': '地址',
  'vehicle': '车辆',
  'account': '账户',
  'contact': '联系人',
  'insurance': '保险',
  'violation': '违章',
  'maintenance': '保养',
  'route': '路线',
  'traffic': '交通记录',
  'transaction': '交易',
  'risk': '风险',
  'merchant': '商户',
  'record': '记录',
  'executive': '高管',
  'investment': '投资',
  'business': '业务',
  'malware': '恶意软件',
  'attacker': '攻击者',
  'vulnerability': '漏洞',
  'target': '目标',
  'technique': '攻击技术',
  'threat': '威胁',
  'entity': '实体',
  'concept': '概念'
}

// 布局标签映射
const layoutLabels: Record<string, string> = {
  'force': '力导向布局',
  'circular': '环形布局',
  'hierarchical': '层次布局',
  'grid': '网格布局'
}

// 计算属性
const hasFilters = computed(() => {
  return props.selectedNodeTypes.length > 0 || 
         props.selectedEdgeTypes.length > 0
})

// 方法
const getNodeTypeColor = (nodeType: string): string => {
  return nodeTypeColors[nodeType] || '#6b7280'
}

const getNodeTypeLabel = (nodeType: string): string => {
  return nodeTypeLabels[nodeType] || nodeType
}

const getLayoutLabel = (layout: string): string => {
  return layoutLabels[layout] || layout
}

const removeNodeType = (nodeType: string) => {
  emit('remove-node-type', nodeType)
}

const removeEdgeType = (edgeType: string) => {
  emit('remove-edge-type', edgeType)
}

const clearAllFilters = () => {
  emit('clear-all')
}
</script>

<style scoped>
.graph-filter-tips {
  width: 100%;
  margin-bottom: 16px;
}

.filter-tips-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.header-icon {
  color: #3b82f6;
  font-size: 16px;
}

.header-text {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  flex: 1;
}

.clear-all-btn {
  color: #64748b;
  font-size: 12px;
  padding: 4px 8px;
}

.clear-all-btn:hover {
  color: #ef4444;
  background-color: #fef2f2;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-group {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.group-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
  margin-top: 2px;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  flex: 1;
}

.filter-tag {
  transition: all 0.2s ease;
}

.filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.tag-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.node-color-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.edge-indicator {
  width: 12px;
  height: 2px;
  background-color: #6b7280;
  border-radius: 1px;
  flex-shrink: 0;
}

.depth-tag,
.layout-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-group {
    flex-direction: column;
    gap: 6px;
  }
  
  .group-label {
    min-width: auto;
  }
}
</style>
