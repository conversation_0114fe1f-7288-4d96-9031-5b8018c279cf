<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- 模态框头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">创建知识图谱</h2>
        <button
          @click="$emit('close')"
          class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="p-6">
        <form @submit.prevent="handleSubmit">
          <!-- 基本信息 -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">图谱名称 *</label>
                <input
                  v-model="formData.name"
                  type="text"
                  required
                  placeholder="输入图谱名称"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">类别</label>
                <select
                  v-model="formData.category"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="business">业务知识</option>
                  <option value="technical">技术文档</option>
                  <option value="product">产品信息</option>
                  <option value="customer">客户关系</option>
                </select>
              </div>
            </div>
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
              <textarea
                v-model="formData.description"
                rows="3"
                placeholder="描述这个知识图谱的用途和内容"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              ></textarea>
            </div>
          </div>

          <!-- 数据源选择 -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">数据源</h3>
            <div class="space-y-3">
              <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-purple-300 transition-colors">
                <input
                  v-model="formData.dataSource"
                  type="radio"
                  value="knowledge-base"
                  class="text-purple-600 focus:ring-purple-500"
                >
                <div class="ml-3">
                  <div class="flex items-center gap-2">
                    <span class="text-lg">📚</span>
                    <span class="font-medium text-gray-900">现有知识库</span>
                  </div>
                  <p class="text-sm text-gray-500">从现有知识库中提取实体和关系</p>
                </div>
              </label>
              
              <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-purple-300 transition-colors">
                <input
                  v-model="formData.dataSource"
                  type="radio"
                  value="database"
                  class="text-purple-600 focus:ring-purple-500"
                >
                <div class="ml-3">
                  <div class="flex items-center gap-2">
                    <span class="text-lg">🗄️</span>
                    <span class="font-medium text-gray-900">数据库</span>
                  </div>
                  <p class="text-sm text-gray-500">从关系数据库中构建知识图谱</p>
                </div>
              </label>
              
              <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-purple-300 transition-colors">
                <input
                  v-model="formData.dataSource"
                  type="radio"
                  value="manual"
                  class="text-purple-600 focus:ring-purple-500"
                >
                <div class="ml-3">
                  <div class="flex items-center gap-2">
                    <span class="text-lg">✏️</span>
                    <span class="font-medium text-gray-900">手动创建</span>
                  </div>
                  <p class="text-sm text-gray-500">手动添加节点和关系</p>
                </div>
              </label>
            </div>
          </div>

          <!-- 知识库选择 -->
          <div v-if="formData.dataSource === 'knowledge-base'" class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">选择知识库</label>
            <div class="space-y-2 max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3">
              <label
                v-for="kb in availableKnowledgeBases"
                :key="kb.id"
                class="flex items-center"
              >
                <input
                  v-model="formData.selectedKnowledgeBases"
                  type="checkbox"
                  :value="kb.id"
                  class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                >
                <span class="ml-2 text-sm text-gray-700">{{ kb.name }}</span>
              </label>
            </div>
          </div>

          <!-- 数据库配置 -->
          <div v-if="formData.dataSource === 'database'" class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">数据库连接</label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                v-model="formData.dbHost"
                type="text"
                placeholder="主机地址"
                class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
              <input
                v-model="formData.dbName"
                type="text"
                placeholder="数据库名"
                class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
            </div>
          </div>

          <!-- 生成选项 -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">生成选项</h3>
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="formData.autoExtractEntities"
                  type="checkbox"
                  class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                >
                <span class="ml-2 text-sm text-gray-700">自动提取实体</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="formData.autoExtractRelations"
                  type="checkbox"
                  class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                >
                <span class="ml-2 text-sm text-gray-700">自动识别关系</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="formData.enableClustering"
                  type="checkbox"
                  class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                >
                <span class="ml-2 text-sm text-gray-700">启用聚类分析</span>
              </label>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="$emit('close')"
              class="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="!formData.name"
              class="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              创建图谱
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'

interface Emits {
  (e: 'close'): void
  (e: 'create', data: any): void
}

const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  name: '',
  category: 'business',
  description: '',
  dataSource: 'knowledge-base',
  selectedKnowledgeBases: [] as string[],
  dbHost: '',
  dbName: '',
  autoExtractEntities: true,
  autoExtractRelations: true,
  enableClustering: false
})

// 可用知识库
const availableKnowledgeBases = ref([
  { id: 'kb-1', name: '产品文档库' },
  { id: 'kb-2', name: '技术文档库' },
  { id: 'kb-3', name: '用户手册库' }
])

// 事件处理
const handleSubmit = () => {
  emit('create', { ...formData })
}
</script>
