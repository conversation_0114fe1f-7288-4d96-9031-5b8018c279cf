<template>
  <div class="model-agent-selector h-full flex flex-col">
    <!-- 欢迎标题区域 -->
    <div class="welcome-header text-center mb-6">
      <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
        <span class="text-2xl">🤖</span>
      </div>
      <h3 class="text-xl font-semibold text-gray-800 mb-2">开始AI对话</h3>
      <p class="text-gray-500 text-sm">选择您需要的AI模型或智能体，开始您的AI探索之旅</p>
    </div>

    <!-- 标签页切换 -->
    <div class="mb-6">
      <!-- 主标签页 -->
      <div class="flex flex-wrap items-center gap-2">
        <!-- AI模型标签页 -->
        <button
          @click="activeTab = 'models'"
          class="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200"
          :class="activeTab === 'models'
            ? 'bg-blue-100 text-blue-700 shadow-sm'
            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'"
        >
          <div class="flex items-center gap-2">
            <span>🧠</span>
            <span>AI模型</span>
            <span class="px-1.5 py-0.5 bg-gray-200 text-gray-600 text-xs rounded-full">{{ models.length }}</span>
          </div>
        </button>

        <!-- AI探索智能体标签页 -->
        <button
          @click="handleTabClick('agents')"
          class="px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 border"
          :class="activeTab === 'agents'
            ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white border-transparent shadow-sm'
            : 'bg-white text-gray-600 border-gray-200 hover:border-gray-300 hover:bg-gray-50'"
        >
          <div class="flex items-center gap-2">
            <span class="text-base">🚀</span>
            <span>AI探索智能体</span>
            <span
              class="px-1.5 py-0.5 text-xs rounded-full"
              :class="activeTab === 'agents'
                ? 'bg-black bg-opacity-20 text-white'
                : 'bg-gray-200 text-gray-600'"
            >
              {{ dynamicAgents.length }}
            </span>
            <!-- 加载状态指示器 -->
            <span v-if="agentsLoading" class="animate-spin">⏳</span>
          </div>
        </button>
        <div v-if="agentsLoading" class="text-center py-12">
          <p class="text-base text-gray-500 mb-2">正在加载智能体...</p>
          <p class="text-sm text-gray-400">请稍候</p>
        </div>
      </div>
    </div>



    <!-- 内容区域 -->
    <div class="flex-1 overflow-y-auto">
      <!-- 模型列表 -->
      <div v-if="activeTab === 'models'" class="space-y-3">
        <div
          v-for="model in filteredModels"
          :key="model.id"
          @click="selectModel(model)"
          class="model-card group p-4 rounded-xl border border-gray-100 hover:border-blue-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 cursor-pointer transition-all duration-300 hover:shadow-md hover:-translate-y-1"
          :class="{ 'border-blue-200 bg-blue-50 shadow-sm': selectedItem?.id === model.id }"
        >
          <div class="flex items-start gap-3">
            <!-- 模型图标 -->
            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-sm flex-shrink-0">
              <span class="text-white text-lg">{{ model.icon }}</span>
            </div>

            <!-- 模型信息 -->
            <div class="flex-1 min-w-0">
              <!-- 标题行 -->
              <div class="flex items-center gap-2 mb-2">
                <h5 class="font-semibold text-gray-800 text-base group-hover:text-blue-700 transition-colors">{{ model.name }}</h5>
                <span class="px-2 py-1 text-xs font-medium rounded-full" :class="model.badgeColor">{{ model.badge }}</span>
                <span v-if="model.version" class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full font-medium">{{ model.version }}</span>
              </div>

              <!-- 描述 -->
              <p class="text-sm text-gray-600 mb-3 leading-relaxed">{{ model.description }}</p>

              <!-- 性能指标 -->
              <div class="flex flex-wrap gap-2">
                <div class="flex items-center gap-1.5 px-2 py-1 bg-gray-50 rounded-full">
                  <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span class="text-xs text-gray-600 font-medium">{{ model.contextLength }}</span>
                </div>
                <div class="flex items-center gap-1.5 px-2 py-1 bg-gray-50 rounded-full">
                  <span class="w-2 h-2 bg-blue-400 rounded-full"></span>
                  <span class="text-xs text-gray-600 font-medium">速度{{ model.speed }}</span>
                </div>
                <div class="flex items-center gap-1.5 px-2 py-1 bg-gray-50 rounded-full">
                  <span class="w-2 h-2 bg-orange-400 rounded-full"></span>
                  <span class="text-xs text-gray-600 font-medium">成本{{ model.cost }}</span>
                </div>
              </div>
            </div>

            <!-- 选中状态 -->
            <div v-if="selectedItem?.id === model.id" class="text-blue-500 flex-shrink-0">
              <el-icon size="18"><Check /></el-icon>
            </div>
          </div>
        </div>

        <!-- 模型列表为空 -->
        <div v-if="filteredModels.length === 0" class="text-center py-12">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <el-icon class="text-gray-400" size="24"><Monitor /></el-icon>
          </div>
          <p class="text-base text-gray-500 mb-2">未找到匹配的模型</p>
          <p class="text-sm text-gray-400">尝试使用其他关键词搜索</p>
        </div>
      </div>

      <!-- AI探索智能体列表 -->
      <div v-else-if="activeTab === 'agents'" class="space-y-3">
        <!-- 加载状态 -->
        <div v-if="agentsLoading" class="text-center py-12">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <span class="animate-spin text-2xl">⏳</span>
          </div>
          <p class="text-base text-gray-500 mb-2">正在加载智能体...</p>
          <p class="text-sm text-gray-400">请稍候</p>
        </div>

        <!-- AI探索智能体列表 -->
        <div
          v-else
          v-for="agent in dynamicAgents"
          :key="agent.id"
          @click="selectAgent(agent)"
          class="agent-card group p-4 rounded-xl border border-gray-100 hover:border-green-200 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 cursor-pointer transition-all duration-300 hover:shadow-md hover:-translate-y-1"
          :class="{ 'border-green-200 bg-green-50 shadow-sm': selectedItem?.id === agent.id }"
        >
          <div class="flex items-start gap-3">
            <!-- 智能体图标 -->
            <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-sm flex-shrink-0">
              <span class="text-white text-lg">🚀</span>
            </div>

            <!-- 智能体信息 -->
            <div class="flex-1 min-w-0">
              <!-- 标题行 -->
              <div class="flex items-center gap-2 mb-2">
                <h5 class="font-semibold text-gray-800 text-base group-hover:text-green-700 transition-colors">{{ agent.name }}</h5>
                <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full font-medium">{{ agent.unit }}</span>
                <span v-if="agent.designer" class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full font-medium">{{ agent.designer }}</span>
              </div>

              <!-- 描述 -->
              <p class="text-sm text-gray-600 mb-3 leading-relaxed">{{ agent.description }}</p>

              <!-- 标签 -->
              <div class="flex flex-wrap gap-1.5">
                <span
                  v-for="tag in agent.tags.slice(0, 4)"
                  :key="tag"
                  class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium hover:bg-green-200 transition-colors"
                >
                  {{ tag }}
                </span>
                <span
                  v-if="agent.tags.length > 4"
                  class="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full font-medium"
                >
                  +{{ agent.tags.length - 4 }}
                </span>
              </div>
            </div>

            <!-- 选中状态 -->
            <div v-if="selectedItem?.id === agent.id" class="text-green-500 flex-shrink-0">
              <el-icon size="18"><Check /></el-icon>
            </div>
          </div>
        </div>

        <!-- 智能体列表为空（非加载状态） -->
        <div v-if="!agentsLoading && dynamicAgents.length === 0" class="text-center py-12">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <el-icon class="text-gray-400" size="24"><UserFilled /></el-icon>
          </div>
          <p class="text-base text-gray-500 mb-2">该分类暂无智能体</p>
          <p class="text-sm text-gray-400">请选择其他分类查看</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
console.log('ModelAgentSelector setup 执行')
import { ref, computed, watch, onMounted } from 'vue'
import { Check, Monitor, UserFilled } from '@element-plus/icons-vue'
import { useModelStore, type ModelItem, type AgentItem } from '@/stores/modelStore'

// 使用共享数据存储
const {
  models,
  dynamicAgents,
  agentsLoading,
  selectedModelInfo,
  setSelectedModel,
  loadAiExploreAgents
} = useModelStore()

// Emits
const emit = defineEmits<{
  'model-select': [model: ModelItem]
  'agent-select': [agent: AgentItem]
}>()

// 响应式数据
const activeTab = ref<string>('agents') // 默认选择AI探索智能体标签页
const selectedItem = computed(() => selectedModelInfo.value)

// ModelAgentSelector现在不需要处理消息发送
// 消息发送完全由AIExplore页面的全局处理器处理

// 加载AI探索智能体数据的函数
const loadAiExploreAgentsIfNeeded = () => {
  console.log('loadAiExploreAgentsIfNeeded called', {
    dynamicAgentsLength: dynamicAgents.value.length,
    agentsLoading: agentsLoading.value
  })
  if (dynamicAgents.value.length === 0 && !agentsLoading.value) {
    console.log('调用 loadAiExploreAgents')
    console.log('[handleTabClick] 即将调用 loadAiExploreAgents 方法')
    loadAiExploreAgents()
  }
}

// 监听activeTab变化，当切换到AI探索智能体时才加载数据
watch(activeTab, (newTab) => {
  console.log('activeTab changed to:', newTab)
  // 每次切换到AI探索智能体标签页都强制请求接口
  if (newTab === 'agents') {
    console.log('[watch activeTab] 切换到AI探索智能体标签页，准备调用loadAiExploreAgents')
    console.log('[watch activeTab] 即将调用 loadAiExploreAgents 方法')
    loadAiExploreAgents()
  }
}, { immediate: true }) // 立即执行一次

// 删除不需要的分类监听

// 组件挂载时检查是否需要加载AI探索智能体数据
onMounted(() => {
  console.log('=== ModelAgentSelector mounted ===')
  console.log('activeTab初始值:', activeTab.value)
  console.log('dynamicAgents数量:', dynamicAgents.value.length)
  console.log('agentsLoading状态:', agentsLoading.value)

  // 如果当前激活的是AI探索智能体标签页，则加载数据
  if (activeTab.value === 'agents') {
    console.log('[onMounted] 初始状态就是agents，准备调用loadAiExploreAgents')
    loadAiExploreAgentsIfNeeded()
  } else {
    console.log('初始状态不是agents，当前是:', activeTab.value)
  }
})



// 计算属性
const filteredModels = computed(() => {
  return models.value
})



// 删除复杂的计算属性，简化逻辑

// 方法
const handleTabClick = (tabId: string) => {
  console.log('=== 标签页点击 ===')
  console.log('点击的标签页ID:', tabId)
  console.log('当前activeTab:', activeTab.value)

  activeTab.value = tabId

  console.log('设置后的activeTab:', activeTab.value)

  // 如果点击的是AI探索智能体标签页，强制加载数据
  if (tabId === 'agents') {
    console.log('[handleTabClick] 点击了AI探索智能体标签页，准备调用loadAiExploreAgents')
    loadAiExploreAgents()
  }
}

const selectModel = (model: ModelItem) => {
  setSelectedModel(model.id, model)
  emit('model-select', model)
}

const selectAgent = (agent: AgentItem) => {
  setSelectedModel(agent.id, agent)
  emit('agent-select', agent)
}

// 所有 ChatInputArea 相关的事件处理现在通过 chatInputHandlers 统一管理
</script>

<style scoped>
.model-agent-selector {
  padding: 2rem 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-header {
  margin-bottom: 2rem;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.search-input :deep(.el-input__wrapper):hover {
  border-color: #d1d5db;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.model-card,
.agent-card {
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.model-card:hover,
.agent-card:hover {
  transform: translateY(-1px);
}

.category-section:not(:last-child) {
  margin-bottom: 1.5rem;
}

/* 滚动条样式 */
.model-agent-selector ::-webkit-scrollbar {
  width: 6px;
}

.model-agent-selector ::-webkit-scrollbar-track {
  background: transparent;
}

.model-agent-selector ::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.model-agent-selector ::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}
</style>
