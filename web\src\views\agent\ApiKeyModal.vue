<template>
  <!-- API密钥申请模态框 -->
  <div class="api-key-modal" v-show="visible" @click="closeModal">
    <div class="api-key-modal-content" @click.stop>
      <div class="api-key-modal-header">
        <div class="header-left">
          <div class="agent-info" v-if="agent">
            <div class="agent-avatar">
              <i :class="agent.icon"></i>
            </div>
            <div class="agent-details">
              <h3>申请API密钥</h3>
              <span class="agent-name">{{ agent.name }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="api-key-modal-body">
        <div class="api-key-tabs">
          <button
            class="api-key-tab"
            :class="{ active: activeTab === 'form' }"
            @click="activeTab = 'form'"
          >
            申请表单
          </button>
          <button
            class="api-key-tab"
            :class="{ active: activeTab === 'list' }"
            @click="activeTab = 'list'"
          >
            已申请密钥
          </button>
        </div>

        <!-- 申请表单 -->
        <div class="api-key-form-section" v-show="activeTab === 'form'">
          <div class="form-grid">
            <div class="form-group">
              <label>申请单位</label>
              <input type="text" v-model="form.applicantUnit" placeholder="请输入申请单位" class="form-input">
            </div>
            <div class="form-group">
              <label>申请人姓名</label>
              <input type="text" v-model="form.applicantName" placeholder="请输入申请人姓名" class="form-input">
            </div>
            <div class="form-group">
              <label>申请人手机号</label>
              <input type="tel" v-model="form.applicantPhone" placeholder="请输入申请人手机号" class="form-input">
            </div>
            <div class="form-group">
              <label>责任单位</label>
              <input type="text" v-model="form.responsibleUnit" placeholder="请输入责任单位" class="form-input">
            </div>
            <div class="form-group">
              <label>责任人姓名</label>
              <input type="text" v-model="form.responsibleName" placeholder="请输入责任人姓名" class="form-input">
            </div>
            <div class="form-group">
              <label>责任人手机号</label>
              <input type="tel" v-model="form.responsiblePhone" placeholder="请输入责任人手机号" class="form-input">
            </div>
            <div class="form-group">
              <label>访问频率</label>
              <select v-model="form.accessFrequency" class="form-input">
                <option value="">请选择访问频率</option>
                <option value="low">低频（每分钟10次以下）</option>
                <option value="medium">中频（每分钟10-50次）</option>
                <option value="high">高频（每分钟50次以上）</option>
              </select>
            </div>
            <div class="form-group">
              <label>访问次数</label>
              <input type="number" v-model="form.accessCount" placeholder="请输入预计访问次数" class="form-input">
            </div>
          </div>
        </div>

        <!-- 已申请密钥列表 -->
        <div class="api-key-list-section" v-show="activeTab === 'list'">
          <div class="api-key-list">
            <div v-if="currentAgentApiKeys.length === 0" class="empty-state">
              <i class="fas fa-key"></i>
              <p>暂无已申请的API密钥</p>
            </div>
            <div v-else class="key-items">
              <div v-for="(key, index) in currentAgentApiKeys" :key="index" class="key-item">
                <div class="key-info">
                  <div class="key-header">
                    <span class="key-id">密钥ID: {{ key.id }}</span>
                    <span class="key-status" :class="key.status">{{ key.statusText }}</span>
                  </div>
                  <div class="key-details">
                    <p><strong>申请单位:</strong> {{ key.applicantUnit }}</p>
                    <p><strong>申请人:</strong> {{ key.applicantName }} ({{ key.applicantPhone }})</p>
                    <p><strong>责任单位:</strong> {{ key.responsibleUnit }}</p>
                    <p><strong>责任人:</strong> {{ key.responsibleName }} ({{ key.responsiblePhone }})</p>
                    <p><strong>访问频率:</strong> {{ key.accessFrequency }}</p>
                    <p><strong>访问次数:</strong> {{ key.accessCount }}</p>
                    <p><strong>申请时间:</strong> {{ key.createTime }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="api-key-modal-footer">
        <div class="footer-left">
          <span class="api-key-tip">提交后将进入审核流程，请耐心等待</span>
        </div>
        <div class="footer-right">
          <button class="btn-modern btn-secondary" @click="closeModal">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button class="btn-modern btn-primary" @click="submitApplication" :disabled="!isFormValid">
            <i class="fas fa-paper-plane"></i>
            提交申请
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 定义Agent类型
interface Agent {
  id: string | number
  name: string
  icon: string
  description?: string
  unit?: string
  creator?: string
  createTime?: string
  type?: string
  tags?: string[]
}

// 定义API密钥类型
interface ApiKey {
  id: string
  agentId: string | number
  agentName: string
  applicantUnit: string
  applicantName: string
  applicantPhone: string
  responsibleUnit: string
  responsibleName: string
  responsiblePhone: string
  accessFrequency: string
  accessCount: string
  status: 'approved' | 'pending' | 'rejected'
  statusText: string
  createTime: string
  approveTime?: string
  rejectTime?: string
  rejectReason?: string
}

// Props
interface Props {
  visible: boolean
  agent: Agent | null
  apiKeyList?: ApiKey[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  agent: null,
  apiKeyList: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [data: ApiKey]
}>()

// 响应式数据
const activeTab = ref('form')
const form = ref({
  applicantUnit: '',
  applicantName: '',
  applicantPhone: '',
  responsibleUnit: '',
  responsibleName: '',
  responsiblePhone: '',
  accessFrequency: '',
  accessCount: ''
})

// 计算属性
const isFormValid = computed(() => {
  return form.value.applicantUnit && form.value.applicantName && form.value.applicantPhone &&
         form.value.responsibleUnit && form.value.responsibleName && form.value.responsiblePhone &&
         form.value.accessFrequency && form.value.accessCount
})

// 当前智能体的API密钥列表
const currentAgentApiKeys = computed(() => {
  if (!props.agent) return []
  return props.apiKeyList.filter(key => key.agentId === props.agent!.id)
})

// 方法
const closeModal = () => {
  emit('update:visible', false)
}

const resetForm = () => {
  form.value = {
    applicantUnit: '',
    applicantName: '',
    applicantPhone: '',
    responsibleUnit: '',
    responsibleName: '',
    responsiblePhone: '',
    accessFrequency: '',
    accessCount: ''
  }
}

const submitApplication = () => {
  if (!isFormValid.value || !props.agent) return

  const newApiKey: ApiKey = {
    id: `API_${Date.now()}`,
    agentId: props.agent.id,
    agentName: props.agent.name,
    ...form.value,
    status: 'pending' as const,
    statusText: '审核中',
    createTime: new Date().toLocaleString()
  }

  emit('submit', newApiKey)
  alert(`API密钥申请已提交！\n申请ID: ${newApiKey.id}\n请耐心等待审核结果。`)
  closeModal()
}

// 监听visible变化，重置表单和标签页
watch(() => props.visible, (newVal) => {
  if (newVal) {
    activeTab.value = 'form'
    resetForm()
  }
})
</script>

<style scoped>
/* API密钥申请模态框样式 */
.api-key-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
  z-index: 10000;
}

.api-key-modal-content {
  background: #fefefe;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(147, 197, 253, 0.3);
  animation: slideUp 0.4s ease;
  display: flex;
  flex-direction: column;
}

.api-key-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.header-left {
  flex: 1;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1a365d;
  font-size: 18px;
}

.agent-details h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.agent-name {
  font-size: 12px;
  color: #4b5563;
  font-weight: normal;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.api-key-modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.api-key-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.api-key-tab {
  padding: 8px 16px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  font-weight: 500;
}

.api-key-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fefefe;
  color: #111827;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.api-key-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.api-key-tip {
  font-size: 12px;
  color: #4b5563;
  font-weight: normal;
}

/* 按钮样式 */
.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern.btn-secondary {
  background: #f0f9ff;
  color: #374151;
  border: 1px solid #bae6fd;
}

.btn-modern.btn-secondary:hover {
  background: #dbeafe;
  color: #111827;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.15);
}

.btn-modern.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.25);
}

.btn-modern.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.35);
}

.btn-modern.btn-primary:disabled {
  background: #bae6fd;
  color: #60a5fa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-modern.btn-primary:disabled::before {
  display: none;
}

.btn-modern i {
  font-size: 11px;
}

/* API密钥列表样式 */
.api-key-list {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.3;
}

.key-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.key-id {
  font-weight: 600;
  color: #1e40af;
}

.key-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.key-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.key-status.approved {
  background: #d1fae5;
  color: #065f46;
}

.key-status.rejected {
  background: #fee2e2;
  color: #991b1b;
}

.key-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #4b5563;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .api-key-modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .api-key-modal-header {
    padding: 12px 16px;
  }

  .api-key-modal-body {
    padding: 16px;
  }

  .api-key-modal-footer {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
  }

  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .btn-modern {
    flex: 1;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}
</style>
