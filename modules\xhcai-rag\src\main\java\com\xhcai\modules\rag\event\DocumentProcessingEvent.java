package com.xhcai.modules.rag.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 文档处理事件
 * 用于在事务提交后发送MQ消息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public class DocumentProcessingEvent extends ApplicationEvent {

    private final String documentId;
    private final String userId;
    private final String tenantId;

    public DocumentProcessingEvent(Object source, String documentId, String userId, String tenantId) {
        super(source);
        this.documentId = documentId;
        this.userId = userId;
        this.tenantId = tenantId;
    }
}
