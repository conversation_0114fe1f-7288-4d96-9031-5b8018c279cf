package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 登录日志查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "登录日志查询DTO")
public class SysLoginLogQueryDTO extends PageTimeRangeQueryDTO {

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    private String username;

    /**
     * 登录IP
     */
    @Schema(description = "登录IP")
    @Size(max = 50, message = "登录IP长度不能超过50个字符")
    private String loginIp;

    /**
     * 登录地点
     */
    @Schema(description = "登录地点")
    @Size(max = 255, message = "登录地点长度不能超过255个字符")
    private String loginLocation;

    /**
     * 登录状态：0-成功，1-失败
     */
    @Schema(description = "登录状态：0-成功，1-失败")
    private String status;

    /**
     * 浏览器类型
     */
    @Schema(description = "浏览器类型")
    @Size(max = 50, message = "浏览器类型长度不能超过50个字符")
    private String browser;

    /**
     * 操作系统
     */
    @Schema(description = "操作系统")
    @Size(max = 50, message = "操作系统长度不能超过50个字符")
    private String os;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    @Size(max = 128, message = "会话ID长度不能超过128个字符")
    private String sessionId;

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    @Override
    public String toString() {
        return "SysLoginLogQueryDTO{" +
                "username='" + username + '\'' +
                ", loginIp='" + loginIp + '\'' +
                ", loginLocation='" + loginLocation + '\'' +
                ", status='" + status + '\'' +
                ", browser='" + browser + '\'' +
                ", os='" + os + '\'' +
                ", sessionId='" + sessionId + '\'' +
                "} " + super.toString();
    }
}
