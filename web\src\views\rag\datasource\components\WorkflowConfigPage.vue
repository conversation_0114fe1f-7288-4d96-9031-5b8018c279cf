<template>
  <div class="workflow-config-page">
    <!-- 头部 -->
    <div class="config-header">
      <div class="header-info">
        <h2>工作流配置</h2>
        <p>配置工作流的基本信息和定时任务</p>
      </div>
      <button class="close-btn" @click="$emit('close')">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- 配置内容 -->
    <div class="config-content">
      <!-- 基本信息配置 -->
      <div class="config-section">
        <div class="section-header">
          <div class="section-icon">
            <i class="fas fa-info-circle"></i>
          </div>
          <div class="section-title">
            <h3>基本信息</h3>
            <p>配置工作流的名称、描述等基本信息</p>
          </div>
        </div>
        
        <div class="section-content">
          <div class="form-group">
            <label class="form-label">工作流名称</label>
            <input 
              v-model="workflowConfig.name" 
              type="text" 
              class="form-input"
              placeholder="请输入工作流名称"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">工作流描述</label>
            <textarea 
              v-model="workflowConfig.description" 
              class="form-textarea"
              rows="3"
              placeholder="请输入工作流描述"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">工作流版本</label>
            <input 
              v-model="workflowConfig.version" 
              type="text" 
              class="form-input"
              placeholder="例如：v1.0.0"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">工作流标签</label>
            <div class="tags-input">
              <div class="tags-list">
                <span 
                  v-for="(tag, index) in workflowConfig.tags" 
                  :key="index"
                  class="tag-item"
                >
                  {{ tag }}
                  <button class="tag-remove" @click="removeTag(index)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
              <input 
                v-model="newTag"
                type="text" 
                class="tag-input"
                placeholder="添加标签"
                @keyup.enter="addTag"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 定时任务配置 -->
      <div class="config-section">
        <div class="section-header">
          <div class="section-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="section-title">
            <h3>定时任务</h3>
            <p>配置工作流的自动执行计划</p>
          </div>
          <div class="section-toggle">
            <label class="toggle-switch">
              <input 
                v-model="scheduleConfig.enabled" 
                type="checkbox"
                class="toggle-input"
              />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
        
        <div class="section-content" v-if="scheduleConfig.enabled">
          <div class="form-group">
            <label class="form-label">执行频率</label>
            <select v-model="scheduleConfig.frequency" class="form-select">
              <option value="once">仅执行一次</option>
              <option value="hourly">每小时</option>
              <option value="daily">每天</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          
          <div class="form-group" v-if="scheduleConfig.frequency === 'custom'">
            <label class="form-label">Cron 表达式</label>
            <input 
              v-model="scheduleConfig.cronExpression" 
              type="text" 
              class="form-input"
              placeholder="例如：0 0 12 * * ?"
            />
            <div class="form-help">
              <i class="fas fa-info-circle"></i>
              <span>使用标准的 Cron 表达式格式</span>
            </div>
          </div>
          
          <div class="form-group" v-if="scheduleConfig.frequency !== 'custom'">
            <label class="form-label">执行时间</label>
            <input 
              v-model="scheduleConfig.executeTime" 
              type="time" 
              class="form-input"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">开始日期</label>
            <input 
              v-model="scheduleConfig.startDate" 
              type="date" 
              class="form-input"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">结束日期</label>
            <input 
              v-model="scheduleConfig.endDate" 
              type="date" 
              class="form-input"
            />
            <div class="form-help">
              <i class="fas fa-info-circle"></i>
              <span>留空表示无结束日期</span>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">最大重试次数</label>
            <input 
              v-model.number="scheduleConfig.maxRetries" 
              type="number" 
              min="0" 
              max="10"
              class="form-input"
            />
          </div>
          
          <div class="form-group">
            <label class="toggle-option">
              <input 
                v-model="scheduleConfig.skipOnFailure" 
                type="checkbox"
                class="toggle-checkbox"
              />
              <span class="toggle-label">失败时跳过后续任务</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 通知配置 -->
      <div class="config-section">
        <div class="section-header">
          <div class="section-icon">
            <i class="fas fa-bell"></i>
          </div>
          <div class="section-title">
            <h3>通知设置</h3>
            <p>配置工作流执行结果的通知方式</p>
          </div>
        </div>
        
        <div class="section-content">
          <div class="notification-options">
            <label class="notification-option">
              <input 
                v-model="notificationConfig.onSuccess" 
                type="checkbox"
                class="option-checkbox"
              />
              <div class="option-content">
                <div class="option-icon success">
                  <i class="fas fa-check-circle"></i>
                </div>
                <div class="option-info">
                  <div class="option-title">执行成功</div>
                  <div class="option-desc">工作流成功完成时发送通知</div>
                </div>
              </div>
            </label>
            
            <label class="notification-option">
              <input 
                v-model="notificationConfig.onFailure" 
                type="checkbox"
                class="option-checkbox"
              />
              <div class="option-content">
                <div class="option-icon error">
                  <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="option-info">
                  <div class="option-title">执行失败</div>
                  <div class="option-desc">工作流执行失败时发送通知</div>
                </div>
              </div>
            </label>
            
            <label class="notification-option">
              <input 
                v-model="notificationConfig.onStart" 
                type="checkbox"
                class="option-checkbox"
              />
              <div class="option-content">
                <div class="option-icon info">
                  <i class="fas fa-play-circle"></i>
                </div>
                <div class="option-info">
                  <div class="option-title">开始执行</div>
                  <div class="option-desc">工作流开始执行时发送通知</div>
                </div>
              </div>
            </label>
          </div>
          
          <div class="form-group" v-if="hasNotificationEnabled">
            <label class="form-label">通知邮箱</label>
            <input 
              v-model="notificationConfig.email" 
              type="email" 
              class="form-input"
              placeholder="请输入邮箱地址"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="config-footer">
      <div class="footer-actions">
        <button class="btn btn-outline" @click="resetConfig">
          <i class="fas fa-undo"></i>
          重置
        </button>
        <button class="btn btn-primary" @click="saveConfig">
          <i class="fas fa-save"></i>
          保存配置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

// Props
interface Props {
  datasetId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
}>()

// 响应式数据
const newTag = ref('')

// 工作流配置
const workflowConfig = reactive({
  name: '数据源工作流',
  description: '自动处理数据源文件的工作流',
  version: 'v1.0.0',
  tags: ['数据处理', '自动化']
})

// 定时任务配置
const scheduleConfig = reactive({
  enabled: false,
  frequency: 'daily',
  cronExpression: '0 0 12 * * ?',
  executeTime: '12:00',
  startDate: '',
  endDate: '',
  maxRetries: 3,
  skipOnFailure: false
})

// 通知配置
const notificationConfig = reactive({
  onSuccess: true,
  onFailure: true,
  onStart: false,
  email: ''
})

// 计算属性
const hasNotificationEnabled = computed(() => {
  return notificationConfig.onSuccess || notificationConfig.onFailure || notificationConfig.onStart
})

// 方法
const addTag = () => {
  if (newTag.value.trim() && !workflowConfig.tags.includes(newTag.value.trim())) {
    workflowConfig.tags.push(newTag.value.trim())
    newTag.value = ''
  }
}

const removeTag = (index: number) => {
  workflowConfig.tags.splice(index, 1)
}

const resetConfig = () => {
  // 重置为默认配置
  Object.assign(workflowConfig, {
    name: '数据源工作流',
    description: '自动处理数据源文件的工作流',
    version: 'v1.0.0',
    tags: ['数据处理', '自动化']
  })
  
  Object.assign(scheduleConfig, {
    enabled: false,
    frequency: 'daily',
    cronExpression: '0 0 12 * * ?',
    executeTime: '12:00',
    startDate: '',
    endDate: '',
    maxRetries: 3,
    skipOnFailure: false
  })
  
  Object.assign(notificationConfig, {
    onSuccess: true,
    onFailure: true,
    onStart: false,
    email: ''
  })
}

const saveConfig = () => {
  // 保存配置
  console.log('保存工作流配置:', {
    workflow: workflowConfig,
    schedule: scheduleConfig,
    notification: notificationConfig
  })
  
  // 这里应该调用API保存配置
  alert('配置保存成功！')
  emit('close')
}
</script>

<style scoped>
.workflow-config-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.header-info h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.header-info p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.config-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.config-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 24px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.section-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.section-title {
  flex: 1;
}

.section-title h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.section-title p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.section-toggle {
  margin-left: auto;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 28px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e1;
  transition: 0.3s;
  border-radius: 14px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-input:checked + .toggle-slider {
  background-color: #3b82f6;
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.section-content {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 6px;
  font-size: 12px;
  color: #64748b;
}

.tags-input {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px;
  background: white;
  min-height: 44px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #eff6ff;
  color: #1d4ed8;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.tag-remove {
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.tag-remove:hover {
  background: #dc2626;
  color: white;
}

.tag-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  min-width: 120px;
}

.toggle-option {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.toggle-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.toggle-label {
  font-size: 14px;
  color: #374151;
}

.notification-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.notification-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-option:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.option-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #3b82f6;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.option-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.option-icon.success {
  background: #dcfce7;
  color: #16a34a;
}

.option-icon.error {
  background: #fef2f2;
  color: #dc2626;
}

.option-icon.info {
  background: #dbeafe;
  color: #2563eb;
}

.option-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.option-desc {
  font-size: 12px;
  color: #64748b;
}

.config-footer {
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #e2e8f0;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline {
  background: white;
  color: #64748b;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
