package com.xhcai.modules.dify.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Dify 刷新令牌请求 DTO
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Schema(description = "Dify 刷新令牌请求")
public class DifyRefreshTokenRequestDTO {

    /**
     * 刷新令牌
     */
    @JsonProperty("refresh_token")
    @Schema(description = "刷新令牌", required = true)
    private String refreshToken;

    public DifyRefreshTokenRequestDTO() {
    }

    public DifyRefreshTokenRequestDTO(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    // Getters and Setters
    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    @Override
    public String toString() {
        return "DifyRefreshTokenRequestDTO{" +
                "refreshToken='" + (refreshToken != null ? refreshToken.substring(0, Math.min(20, refreshToken.length())) + "..." : "null") + '\'' +
                '}';
    }
}
