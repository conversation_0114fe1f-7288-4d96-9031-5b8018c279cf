<template>
  <div class="data-node" :class="{ selected: selected }">
    <div class="node-header">
      <div class="node-icon">
        <i class="fas" :class="getDataIcon(data.config?.dataType || 'transform')"></i>
      </div>
      <div class="node-title">{{ data.label || getDataName(data.config?.dataType || 'transform') }}</div>
    </div>
    
    <div class="node-content">
      <p class="node-description" v-if="data.description">{{ data.description }}</p>
    </div>

    <Handle type="target" :position="Position.Left" id="input" class="node-handle node-handle-input" />
    <Handle type="source" :position="Position.Right" id="output" class="node-handle node-handle-output" />
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'

interface DataNodeData {
  label?: string
  description?: string
  config?: {
    dataType?: string
  }
}

interface Props extends NodeProps {
  data: DataNodeData
}

defineProps<Props>()

const getDataIcon = (dataType: string) => {
  const iconMap: Record<string, string> = {
    'data-filter': 'fa-filter',
    'data-transform': 'fa-exchange-alt',
    'data-merge': 'fa-code-merge',
    'data-sort': 'fa-sort',
    'data-aggregate': 'fa-layer-group'
  }
  return iconMap[dataType] || 'fa-database'
}

const getDataName = (dataType: string) => {
  const nameMap: Record<string, string> = {
    'data-filter': '数据过滤',
    'data-transform': '数据转换',
    'data-merge': '数据合并',
    'data-sort': '数据排序',
    'data-aggregate': '数据聚合'
  }
  return nameMap[dataType] || '数据工具'
}
</script>

<style scoped>
.data-node {
  background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
  border: 2px solid #84cc16;
  border-radius: 12px;
  min-width: 160px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  position: relative;
}

.data-node:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.data-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.node-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.node-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.node-title {
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex: 1;
}

.node-content {
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 0 0 12px 12px;
}

.node-description {
  color: #374151;
  font-size: 13px;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

.node-handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  background: #84cc16;
  border-radius: 50%;
}

.node-handle-input {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-handle-output {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-handle:hover {
  width: 16px;
  height: 16px;
  border-width: 3px;
}

.node-handle-input:hover {
  left: -8px;
}

.node-handle-output:hover {
  right: -8px;
}
</style>
