package com.xhcai.modules.rag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.LocalDateTime;

/**
 * 嵌入向量实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "embeddings")
@TableName("embeddings")
public class Embedding extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 内容哈希值
     */
    @Column(name = "hash", nullable = false, length = 64)
    @TableField("hash")
    private String hash;

    /**
     * 嵌入向量内容
     */
    @Column(name = "embedding", nullable = false, columnDefinition = "TEXT")
    @TableField("embedding")
    private String embedding;

    /**
     * 模型名称
     */
    @Column(name = "model_name", nullable = false, length = 255)
    @TableField("model_name")
    private String modelName = "text-embedding-ada-002";

    /**
     * 模型提供商
     */
    @Column(name = "provider_name", nullable = false, length = 255)
    @TableField("provider_name")
    private String providerName = "";


    // ==================== Getters and Setters ====================

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getEmbedding() {
        return embedding;
    }

    public void setEmbedding(String embedding) {
        this.embedding = embedding;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    @Override
    public String toString() {
        return "Embedding{" +
                "id='" + getId() + '\'' +
                ", hash='" + hash + '\'' +
                ", modelName='" + modelName + '\'' +
                ", providerName='" + providerName + '\'' +
                ", createTime=" + getCreateTime() +
                '}';
    }
}
