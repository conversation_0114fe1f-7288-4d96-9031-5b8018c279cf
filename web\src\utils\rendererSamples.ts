// 渲染器样例数据生成器
import type { RendererType, RendererSample } from '@/types/renderer'

// 智能内容匹配器
export class ContentMatcher {
  // 关键词匹配规则
  private static readonly KEYWORD_RULES: Record<RendererType, string[]> = {
    image: ['图片', '图像', '照片', '截图', '画面', '图表', '示意图', '配图'],
    audio: ['语音', '音频', '声音', '录音', '音乐', '播放', '听'],
    video: ['视频', '录像', '播放', '观看', '影片', '短片', '演示'],
    chart: ['图表', '数据', '统计', '分析', '柱状图', '折线图', '饼图', '报表'],
    flowchart: ['流程图', '流程', '步骤', '过程', '工作流', '流程图', '示意图'],
    file: ['文件', '文档', '下载', '附件', '资料', 'PDF', 'Word', 'Excel'],
    html: ['网页', 'HTML', '页面', '样式', '布局'],
    markdown: ['格式', '文档', '说明', '教程', '指南'],
    mixed: ['综合', '多种', '复合', '混合'],
    text: [] // 默认类型，无特定关键词
  }

  // 根据用户输入内容智能匹配渲染器类型
  static matchRenderer(userInput: string): RendererType {
    if (!userInput || typeof userInput !== 'string') {
      return 'text'
    }

    const input = userInput.toLowerCase()

    // 按优先级顺序检查关键词匹配
    const priorityOrder: RendererType[] = [
      'image', 'audio', 'video', 'chart', 'flowchart', 'file', 'html', 'markdown', 'mixed'
    ]

    for (const rendererType of priorityOrder) {
      const keywords = this.KEYWORD_RULES[rendererType]
      if (keywords.some(keyword => input.includes(keyword))) {
        // 移除智能匹配渲染器的控制台输出
        // console.log(`智能匹配到渲染器: ${rendererType}, 匹配关键词: ${keywords.filter(k => input.includes(k)).join(', ')}`)
        return rendererType
      }
    }

    // 如果没有匹配到特定类型，返回默认的text类型
    return 'text'
  }

  // 获取匹配到的关键词（用于调试）
  static getMatchedKeywords(userInput: string, rendererType: RendererType): string[] {
    if (!userInput || typeof userInput !== 'string') {
      return []
    }

    const input = userInput.toLowerCase()
    const keywords = this.KEYWORD_RULES[rendererType] || []
    return keywords.filter(keyword => input.includes(keyword))
  }
}

// 样例数据生成器
export class RendererSampleGenerator {
  // 获取指定渲染器的样例数据
  static getSample(rendererType: RendererType): RendererSample {
    const samples = {
      text: {
        type: 'text' as RendererType,
        content: `这是一个纯文本渲染器的示例回复。

它可以显示多行文本内容，支持自动换行和链接识别。

例如，这里有一个链接：https://www.example.com

纯文本渲染器会自动将URL转换为可点击的链接，同时保持文本的原始格式。

这种渲染方式适合简单的文本回复和基础信息展示。`,
        description: '展示纯文本内容的渲染效果'
      },

      markdown: {
        type: 'markdown' as RendererType,
        content: `# Markdown渲染器示例

这是一个**Markdown渲染器**的示例回复，支持丰富的格式化内容。

## 功能特性

- **粗体文本**和*斜体文本*
- ~~删除线文本~~
- \`行内代码\`

### 代码块示例

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
  return "Markdown渲染器支持代码高亮";
}
\`\`\`

### 表格示例

| 功能 | 支持 | 说明 |
|------|------|------|
| 标题 | ✅ | 支持多级标题 |
| 列表 | ✅ | 有序和无序列表 |
| 链接 | ✅ | [点击访问](https://www.example.com) |

> 这是一个引用块，用于突出显示重要信息。

Markdown渲染器提供了丰富的文本格式化功能，适合技术文档和结构化内容展示。`,
        description: '展示Markdown格式内容的渲染效果'
      },

      image: {
        type: 'image' as RendererType,
        content: `这里为您展示一些相关的图片资料：

![示例图片1](https://picsum.photos/400/300?random=1)

![示例图片2](https://picsum.photos/400/300?random=2)

![示例图片3](https://picsum.photos/400/300?random=3)

图片渲染器支持多种展示模式：
- 单张图片大图展示
- 多张图片网格布局
- 图片预览和缩放功能
- 图片标题和描述显示

这些图片展示了图片渲染器的各种功能特性。`,
        description: '展示图片画廊的渲染效果'
      },

      audio: {
        type: 'audio' as RendererType,
        content: `我为您准备了一段音频解说：

[AUDIO]
{
  "url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
  "title": "示例音频解说",
  "duration": 30
}
[/AUDIO]

音频渲染器的主要功能：
- 支持多种音频格式播放
- 提供播放进度控制
- 音量调节功能
- 播放状态显示
- 音频文件下载

这段音频包含了详细的功能演示和使用说明。`,
        description: '展示音频播放器的渲染效果'
      },

      video: {
        type: 'video' as RendererType,
        content: `这里有一个相关的视频教程：

[VIDEO]
{
  "url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
  "title": "AI技术演示视频",
  "duration": 60,
  "poster": "https://picsum.photos/640/360?random=3"
}
[/VIDEO]

视频渲染器的功能特点：
- 支持多种视频格式
- 自定义封面图片
- 全屏播放支持
- 播放控制面板
- 视频信息显示

视频中展示了具体的操作步骤和技术原理，帮助您更好地理解相关概念。`,
        description: '展示视频播放器的渲染效果'
      },

      chart: {
        type: 'chart' as RendererType,
        content: `让我用图表来展示相关数据：

[CHART:bar]
{
  "title": "月度销售数据统计",
  "labels": ["1月", "2月", "3月", "4月", "5月", "6月"],
  "datasets": [{
    "label": "销售额(万元)",
    "data": [65, 59, 80, 81, 56, 55],
    "backgroundColor": "rgba(59, 130, 246, 0.8)"
  }]
}
[/CHART]

图表渲染器支持多种图表类型：
- 柱状图（Bar Chart）
- 折线图（Line Chart）
- 饼图（Pie Chart）
- 散点图（Scatter Chart）

从图表可以看出，3月和4月的销售表现最佳，这为我们的业务决策提供了重要参考。`,
        description: '展示数据图表的渲染效果'
      },

      flowchart: {
        type: 'flowchart' as RendererType,
        content: `让我用流程图来解释这个过程：

[FLOWCHART:mermaid]
graph TD
    A[用户输入问题] --> B{AI理解分析}
    B --> C[检索相关知识]
    B --> D[生成初步回答]
    C --> E[整合信息]
    D --> E
    E --> F[优化回答内容]
    F --> G[流式输出回答]
    G --> H[用户获得回答]
[/FLOWCHART]

流程图渲染器的特点：
- 支持Mermaid语法
- 多种图表类型（流程图、时序图、甘特图等）
- 交互式图表展示
- 导出和分享功能

这个流程图清晰地展示了AI处理问题的完整过程，帮助理解系统的工作原理。`,
        description: '展示流程图的渲染效果'
      },

      file: {
        type: 'file' as RendererType,
        content: `我为您准备了一些相关的文档资料：

[FILE]
{
  "name": "AI技术白皮书.pdf",
  "url": "https://example.com/whitepaper.pdf",
  "type": "application/pdf",
  "size": 2048000
}
[/FILE]

[FILE]
{
  "name": "数据分析报告.xlsx",
  "url": "https://example.com/report.xlsx",
  "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "size": 1024000
}
[/FILE]

文件渲染器支持的功能：
- PDF文件在线预览
- Office文档预览
- 文件信息显示
- 文件下载功能
- 多种文件格式支持

这些文档包含了详细的技术资料和分析数据，您可以直接在线预览或下载查看。`,
        description: '展示文件预览的渲染效果'
      },

      html: {
        type: 'html' as RendererType,
        content: `<div class="html-content">
<h2>HTML渲染器示例</h2>
<p>这是一个<strong>HTML渲染器</strong>的示例，支持直接渲染HTML内容。</p>
<div style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 20px; border-radius: 10px; color: white; margin: 10px 0;">
  <h3>特色功能</h3>
  <ul>
    <li>支持完整的HTML标签</li>
    <li>支持CSS样式</li>
    <li>支持交互元素</li>
  </ul>
</div>
<p>HTML渲染器适合展示富文本内容和自定义样式。</p>
</div>`,
        description: '展示HTML内容的渲染效果'
      },

      mixed: {
        type: 'mixed' as RendererType,
        content: `这是一个混合内容渲染器的示例，可以同时展示多种类型的内容：

## 文本内容
这里是一些**Markdown格式**的文本内容。

## 图片展示
![示例图片](https://picsum.photos/300/200?random=10)

## 数据图表
[CHART:line]
{
  "title": "混合内容示例图表",
  "data": {
    "labels": ["一月", "二月", "三月"],
    "datasets": [{
      "label": "数据系列",
      "data": [10, 20, 15]
    }]
  }
}

混合渲染器可以在一个回复中展示多种不同类型的内容。`,
        description: '展示混合内容的渲染效果'
      }
    }

    return samples[rendererType] || samples.text
  }

  // 获取所有样例数据
  static getAllSamples(): RendererSample[] {
    const types: RendererType[] = ['text', 'markdown', 'image', 'audio', 'video', 'chart', 'flowchart', 'file', 'html', 'mixed']
    return types.map(type => this.getSample(type))
  }

  // 随机获取一个样例
  static getRandomSample(): RendererSample {
    const types: RendererType[] = ['text', 'markdown', 'image', 'audio', 'video', 'chart', 'flowchart', 'file', 'html', 'mixed']
    const randomType = types[Math.floor(Math.random() * types.length)]
    return this.getSample(randomType)
  }
}
