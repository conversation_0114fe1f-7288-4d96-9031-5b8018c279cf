#!/bin/bash

# 生产环境插件构建脚本
# 用于构建、签名和部署插件到生产环境

set -e

echo "=== 生产环境插件构建开始 ==="

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build/plugins"
PLUGIN_DIR="$PROJECT_ROOT/plugins"

# 清理构建目录
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"/{storage,model,notify}

cd "$PROJECT_ROOT"

# 构建函数
build_plugin() {
    local plugin_path=$1
    local plugin_name=$2
    local target_dir=$3
    
    echo "构建插件: $plugin_name"
    cd "$plugin_path"
    
    # 清理并构建
    mvn clean package -DskipTests -Pprod
    
    # 检查构建结果
    if [ -f "target/${plugin_name}-1.0.0.jar" ]; then
        # 复制到构建目录
        cp "target/${plugin_name}-1.0.0.jar" "$BUILD_DIR/$target_dir/"
        
        # 生成校验和
        cd "$BUILD_DIR/$target_dir"
        sha256sum "${plugin_name}-1.0.0.jar" > "${plugin_name}-1.0.0.jar.sha256"
        
        echo "✓ $plugin_name 构建成功"
    else
        echo "✗ $plugin_name 构建失败"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 构建所有插件
build_plugin "plugins/storage/minio-storage-plugin" "minio-storage-plugin" "storage"
build_plugin "plugins/model/openai-model-plugin" "openai-model-plugin" "model"
build_plugin "plugins/notify/email-notification-plugin" "email-notification-plugin" "notify"

# 生成插件清单
echo "生成插件清单..."
cat > "$BUILD_DIR/plugin-manifest.json" << EOF
{
  "version": "1.0.0",
  "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "plugins": {
    "storage": [
      {
        "id": "minio-storage-plugin",
        "version": "1.0.0",
        "file": "minio-storage-plugin-1.0.0.jar",
        "checksum": "$(cat $BUILD_DIR/storage/minio-storage-plugin-1.0.0.jar.sha256 | cut -d' ' -f1)"
      }
    ],
    "model": [
      {
        "id": "openai-model-plugin",
        "version": "1.0.0",
        "file": "openai-model-plugin-1.0.0.jar",
        "checksum": "$(cat $BUILD_DIR/model/openai-model-plugin-1.0.0.jar.sha256 | cut -d' ' -f1)"
      }
    ],
    "notify": [
      {
        "id": "email-notification-plugin",
        "version": "1.0.0",
        "file": "email-notification-plugin-1.0.0.jar",
        "checksum": "$(cat $BUILD_DIR/notify/email-notification-plugin-1.0.0.jar.sha256 | cut -d' ' -f1)"
      }
    ]
  }
}
EOF

# 创建部署包
echo "创建部署包..."
cd "$BUILD_DIR"
tar -czf "../plugins-$(date +%Y%m%d-%H%M%S).tar.gz" .

echo "=== 生产环境插件构建完成 ==="
echo "构建目录: $BUILD_DIR"
echo "部署包: $PROJECT_ROOT/build/plugins-*.tar.gz"
