package com.xhcai.modules.rag.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 向量数据库VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "向量数据库信息")
public class VectorDatabaseVO {

    @Schema(description = "向量数据库ID")
    private String id;

    @Schema(description = "数据库名称")
    private String name;

    @Schema(description = "数据库类型")
    private String type;

    @Schema(description = "数据库类型名称")
    private String typeName;

    @Schema(description = "主机地址")
    private String host;

    @Schema(description = "端口")
    private Integer port;

    @Schema(description = "数据库名")
    private String databaseName;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "连接配置")
    private String connectionConfig;

    @Schema(description = "索引配置")
    private String indexConfig;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "是否为默认数据库")
    private String isDefault;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "图标颜色")
    private String iconColor;

    @Schema(description = "连接池配置")
    private String poolConfig;

    @Schema(description = "最后连接测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastTestTime;

    @Schema(description = "最后连接测试结果")
    private String lastTestResult;

    @Schema(description = "连接测试错误信息")
    private String testErrorMessage;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "删除标记")
    private Integer deleted = 0;
}
