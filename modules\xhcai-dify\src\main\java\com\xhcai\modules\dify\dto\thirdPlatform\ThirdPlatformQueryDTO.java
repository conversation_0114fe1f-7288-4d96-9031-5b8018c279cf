package com.xhcai.modules.dify.dto.thirdPlatform;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 第三方智能体查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "第三方智能体查询DTO")
public class ThirdPlatformQueryDTO extends PageTimeRangeQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体名称（模糊查询）
     */
    @Schema(description = "智能体名称（模糊查询）", example = "ChatGPT")
    @Size(max = 100, message = "智能体名称长度不能超过100个字符")
    private String name;

    /**
     * 智能体描述（模糊查询）
     */
    @Schema(description = "智能体描述（模糊查询）", example = "OpenAI")
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    private String description;

    /**
     * 所属单位ID
     */
    @Schema(description = "所属单位ID", example = "1")
    @Size(max = 36, message = "所属单位ID长度不能超过36个字符")
    private String unitId;

    /**
     * 访问范围
     */
    @Schema(description = "访问范围", example = "public", allowableValues = {"personal", "public", "partial_users", "partial_units"})
    @Size(max = 20, message = "访问范围长度不能超过20个字符")
    private String accessScope;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 连接地址（模糊查询）
     */
    @Schema(description = "连接地址（模糊查询）", example = "openai.com")
    @Size(max = 500, message = "连接地址长度不能超过500个字符")
    private String connectionUrl;

    /**
     * 关键词搜索（同时搜索名称和描述）
     */
    @Schema(description = "关键词搜索（同时搜索名称和描述）", example = "智能助手")
    @Size(max = 100, message = "关键词长度不能超过100个字符")
    private String keyword;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getAccessScope() {
        return accessScope;
    }

    public void setAccessScope(String accessScope) {
        this.accessScope = accessScope;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getConnectionUrl() {
        return connectionUrl;
    }

    public void setConnectionUrl(String connectionUrl) {
        this.connectionUrl = connectionUrl;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return "ThirdPlatformQueryDTO{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", unitId='" + unitId + '\'' +
                ", accessScope='" + accessScope + '\'' +
                ", status=" + status +
                ", connectionUrl='" + connectionUrl + '\'' +
                ", keyword='" + keyword + '\'' +
                ", " + super.toString() +
                '}';
    }
}
