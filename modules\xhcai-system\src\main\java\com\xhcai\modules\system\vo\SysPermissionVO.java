package com.xhcai.modules.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限信息VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "权限信息VO")
public class SysPermissionVO {

    /**
     * 权限ID
     */
    @Schema(description = "权限ID", example = "1")
    private String id;

    /**
     * 权限编码
     */
    @Schema(description = "权限编码", example = "system:user:list")
    private String permissionCode;

    /**
     * 权限名称
     */
    @Schema(description = "权限名称", example = "用户查询")
    private String permissionName;

    /**
     * 权限类型
     */
    @Schema(description = "权限类型", example = "1")
    private String permissionType;

    /**
     * 权限类型名称
     */
    @Schema(description = "权限类型名称", example = "菜单")
    private String permissionTypeName;

    /**
     * 父权限ID
     */
    @Schema(description = "父权限ID", example = "0")
    private String parentId;

    /**
     * 父权限名称
     */
    @Schema(description = "父权限名称", example = "系统管理")
    private String parentName;

    /**
     * 权限路径
     */
    @Schema(description = "权限路径", example = "/system/user")
    private String permissionPath;

    /**
     * 权限图标
     */
    @Schema(description = "权限图标", example = "user")
    private String icon;

    /**
     * 组件路径
     */
    @Schema(description = "组件路径", example = "system/user/index")
    private String component;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序", example = "1")
    private Integer orderNum;

    /**
     * 是否外链
     */
    @Schema(description = "是否外链")
    private Boolean isFrame;

    /**
     * 是否缓存
     */
    @Schema(description = "是否缓存")
    private Boolean isCache;

    /**
     * 是否可见
     */
    @Schema(description = "是否可见")
    private Boolean visible;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称", example = "正常")
    private String statusName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID", example = "1")
    private String tenantId;

    /**
     * 创建者
     */
    @Schema(description = "创建者", example = "admin")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者", example = "admin")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 子权限列表
     */
    @Schema(description = "子权限列表")
    private List<SysPermissionVO> children;

    /**
     * 是否有子权限
     */
    @Schema(description = "是否有子权限")
    private Boolean hasChildren;

    /**
     * 层级
     */
    @Schema(description = "层级", example = "1")
    private Integer level;

    /**
     * 是否为系统内置权限
     */
    @Schema(description = "是否为系统内置权限")
    private Boolean isSystem;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }

    public String getPermissionTypeName() {
        return permissionTypeName;
    }

    public void setPermissionTypeName(String permissionTypeName) {
        this.permissionTypeName = permissionTypeName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getPermissionPath() {
        return permissionPath;
    }

    public void setPermissionPath(String permissionPath) {
        this.permissionPath = permissionPath;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Boolean getIsFrame() {
        return isFrame;
    }

    public void setIsFrame(Boolean isFrame) {
        this.isFrame = isFrame;
    }

    public Boolean getIsCache() {
        return isCache;
    }

    public void setIsCache(Boolean isCache) {
        this.isCache = isCache;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public List<SysPermissionVO> getChildren() {
        return children;
    }

    public void setChildren(List<SysPermissionVO> children) {
        this.children = children;
    }

    public Boolean getHasChildren() {
        return hasChildren;
    }

    public void setHasChildren(Boolean hasChildren) {
        this.hasChildren = hasChildren;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }
}
