package com.xhcai.modules.rag.service;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.plugins.rabbitmq.model.RabbitMQMessage;
import com.xhcai.modules.rag.plugins.rabbitmq.producer.RabbitMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.DocumentStatusUpdateDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 文档状态SSE推送服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class DocumentStatusSSEService {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired(required = false)
    private RabbitMQProducer rabbitMQProducer;

    /**
     * 存储用户的SSE连接 (tenantId_userId -> List<SseEmitter>)
     */
    private final Map<String, CopyOnWriteArrayList<SseEmitter>> userConnections = new ConcurrentHashMap<>();

    /**
     * SSE连接超时时间 (30分钟)
     */
    private static final long SSE_TIMEOUT = 30 * 60 * 1000L;

    /**
     * 创建SSE连接
     *
     * @return SseEmitter
     */
    public SseEmitter createConnection() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String userId = SecurityUtils.getCurrentUserId();
        String userKey = tenantId + "_" + userId;

        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

        // 获取或创建用户连接列表
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.computeIfAbsent(
                userKey, k -> new CopyOnWriteArrayList<>());
        emitters.add(emitter);

        log.info("创建SSE连接: tenantId={}, userId={}, 当前连接数={}",
                tenantId, userId, emitters.size());

        // 设置连接完成和超时的回调
        emitter.onCompletion(() -> {
            emitters.remove(emitter);
            log.info("SSE连接完成: tenantId={}, userId={}, 剩余连接数={}",
                    tenantId, userId, emitters.size());
        });

        emitter.onTimeout(() -> {
            emitters.remove(emitter);
            log.info("SSE连接超时: tenantId={}, userId={}, 剩余连接数={}",
                    tenantId, userId, emitters.size());
        });

        emitter.onError((ex) -> {
            emitters.remove(emitter);
            log.error("SSE连接错误: tenantId={}, userId={}, error={}",
                    tenantId, userId, ex.getMessage(), ex);
        });

        // 发送连接成功消息
        JSONObject data = new JSONObject();
        data.put("message", "SSE连接已建立");
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data(data.toJSONString()));
        } catch (IOException e) {
            log.error("发送SSE连接成功消息失败: tenantId={}, userId={}", tenantId, userId, e);
            emitters.remove(emitter);
        }

        return emitter;
    }

    /**
     * 推送文档状态更新（带用户信息）
     *
     * @param document 文档对象
     * @param status 文档状态
     * @param progress 处理进度
     * @param currentStage 当前阶段描述
     * @param tenantId 租户ID（可选，为null时尝试从SecurityUtils获取）
     * @param userId 用户ID（可选，为null时尝试从SecurityUtils获取）
     */
    public void pushDocumentStatusUpdate(Document document, DocumentStatus status,
                                          Integer progress, String currentStage, String tenantId, String userId) {
        if (document == null) {
            return;
        }

        try {
            // 如果没有传入用户信息，尝试从SecurityUtils获取
            if (tenantId == null || userId == null) {
                try {
                    tenantId = tenantId != null ? tenantId : SecurityUtils.getCurrentTenantId();
                    userId = userId != null ? userId : SecurityUtils.getCurrentUserId();
                } catch (Exception e) {
                    // 如果无法获取用户信息，使用文档的创建者信息
                    log.warn("无法获取当前用户信息，使用文档创建者信息: {}", e.getMessage());
                    tenantId = document.getTenantId();
                    userId = document.getCreateBy();
                }
            }

            // 创建状态更新DTO
            DocumentStatusUpdateDTO statusUpdate = DocumentStatusUpdateDTO.createProcessingUpdate(
                    document.getId(), document.getName(), currentStage, progress, tenantId, userId);
            statusUpdate.setStatus(status);

            // 通过SSE推送状态更新
            log.info("准备推送文档状态更新: documentId={}, status={}, tenantId={}, userId={}",
                    document.getId(), status, tenantId, userId);

            // 检查是否有活跃的SSE连接
            boolean hasConnection = hasActiveConnection(tenantId, userId);
            if (hasConnection) {
                pushDocumentStatusUpdate(statusUpdate);
                log.info("文档状态更新推送完成: documentId={}", document.getId());
            } else {
                log.warn("没有找到活跃的SSE连接，跳过推送: documentId={}, tenantId={}, userId={}",
                        document.getId(), tenantId, userId);
            }

            // 通过RabbitMQ推送状态更新
            if (rabbitMQProducer != null) {
                RabbitMQMessage message = RabbitMQMessage.createDocumentStatusPushMessage(
                        statusUpdate, tenantId, userId);
                rabbitMQProducer.sendMessage(message);
            }

            log.debug("推送文档状态更新: documentId={}, status={}, progress={}",
                    document.getId(), status, progress);

        } catch (Exception e) {
            log.error("推送文档状态更新失败: documentId={}, error={}",
                    document.getId(), e.getMessage(), e);
        }
    }

    /**
     * 推送文档状态更新
     *
     * @param statusUpdate 状态更新信息
     */
    public void pushDocumentStatusUpdate(DocumentStatusUpdateDTO statusUpdate) {
        String userKey = statusUpdate.getTenantId() + "_" + statusUpdate.getUserId();
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.get(userKey);

        if (emitters == null || emitters.isEmpty()) {
            log.warn("没有找到用户的SSE连接: tenantId={}, userId={}, 当前所有连接: {}",
                    statusUpdate.getTenantId(), statusUpdate.getUserId(), userConnections.keySet());
            return;
        }

        log.info("推送文档状态更新: documentId={}, status={}, tenantId={}, userId={}, 连接数={}",
                statusUpdate.getDocumentId(), statusUpdate.getStatus(),
                statusUpdate.getTenantId(), statusUpdate.getUserId(), emitters.size());

        // 向所有连接推送状态更新
        emitters.removeIf(emitter -> {
            try {
                String jsonData = objectMapper.writeValueAsString(statusUpdate);
                emitter.send(SseEmitter.event()
                        .name("document-status-update")
                        .data(jsonData));
                return false; // 发送成功，保留连接
            } catch (IOException e) {
                log.warn("推送文档状态更新失败，移除连接: tenantId={}, userId={}, error={}",
                        statusUpdate.getTenantId(), statusUpdate.getUserId(), e.getMessage());
                return true; // 发送失败，移除连接
            }
        });
    }

    /**
     * 关闭用户的所有SSE连接
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     */
    public void closeUserConnections(String tenantId, String userId) {
        String userKey = tenantId + "_" + userId;
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.remove(userKey);

        if (emitters != null) {
            log.info("关闭用户SSE连接: tenantId={}, userId={}, 连接数={}",
                    tenantId, userId, emitters.size());

            emitters.forEach(emitter -> {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("关闭SSE连接失败: tenantId={}, userId={}", tenantId, userId, e);
                }
            });
        }
    }

    /**
     * 检查用户是否有活跃的SSE连接
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否有活跃连接
     */
    public boolean hasActiveConnection(String tenantId, String userId) {
        String userKey = tenantId + "_" + userId;
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.get(userKey);
        boolean hasConnection = emitters != null && !emitters.isEmpty();

        log.info("检查SSE连接状态: tenantId={}, userId={}, userKey={}, hasConnection={}, 连接数={}",
                tenantId, userId, userKey, hasConnection,
                emitters != null ? emitters.size() : 0);

        return hasConnection;
    }

    /**
     * 获取当前连接统计信息
     *
     * @return 连接统计信息
     */
    public Map<String, Integer> getConnectionStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        userConnections.forEach((userKey, emitters) -> {
            stats.put(userKey, emitters.size());
        });
        return stats;
    }

    /**
     * 清理所有连接
     */
    public void clearAllConnections() {
        log.info("清理所有SSE连接，当前用户数: {}", userConnections.size());

        userConnections.forEach((userKey, emitters) -> {
            emitters.forEach(emitter -> {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("关闭SSE连接失败: userKey={}", userKey, e);
                }
            });
        });

        userConnections.clear();
        log.info("所有SSE连接已清理完成");
    }
}
