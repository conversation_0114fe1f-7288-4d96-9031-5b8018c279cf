<template>
  <!-- 通用运行弹出层 -->
  <div
    class="runner-window"
    v-show="visible && !isMinimized"
    :style="runnerWindowStyle"
    ref="runnerWindow"
  >
    <!-- 顶部信息栏（可拖动） -->
    <div
      class="runner-header"
      @mousedown="draggable ? startDrag($event) : null"
      @dblclick="toggleMaximize"
      :class="{ 'cursor-move': draggable }"
    >
      <!-- 左侧信息区域 -->
      <div class="header-info-bar">
        <!-- 默认头像/图标 -->
        <div v-if="!headerInfo" class="default-icon">
          <i class="fas fa-window-maximize"></i>
        </div>
        
        <!-- 自定义头部信息 -->
        <div v-else class="custom-header-info">
          <div class="header-icon" v-if="headerInfo.icon" :style="{ backgroundColor: headerInfo.iconBg || '#3b82f6' }">
            <i :class="headerInfo.icon" v-if="headerInfo.icon.startsWith('fa')"></i>
            <span v-else>{{ headerInfo.icon }}</span>
          </div>
          <div class="header-details">
            <h3>{{ headerInfo.title || title || '运行窗口' }}</h3>
            <div class="header-meta" v-if="headerInfo.subtitle || headerInfo.meta">
              <span v-if="headerInfo.subtitle" class="subtitle">{{ headerInfo.subtitle }}</span>
              <div v-if="headerInfo.meta" class="meta-items">
                <span 
                  v-for="meta in headerInfo.meta" 
                  :key="meta.label"
                  :class="getMetaClass(meta.type, meta.color)"
                >
                  {{ meta.label }}：{{ meta.value }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧控制按钮 -->
      <div class="runner-controls" v-if="showControls">
        <button
          v-if="showMinimizeButton"
          class="runner-control-btn minimize-btn"
          title="最小化"
          @click="minimizeRunner"
        >
          <i class="fas fa-minus"></i>
        </button>
        <button
          v-if="!defaultFullscreen"
          class="runner-control-btn maximize-btn"
          title="最大化/还原"
          @click="toggleMaximize"
        >
          <i :class="isMaximized ? 'fas fa-window-restore' : 'fas fa-window-maximize'"></i>
        </button>
        <button class="runner-control-btn close-btn" title="关闭" @click="closeRunner">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- 动态组件容器 -->
    <div class="runner-content-container">
      <div class="runner-content-wrapper">
        <component
          :is="component"
          v-if="component"
          v-bind="componentProps"
          :is-in-modal="true"
          class="runner-dynamic-component"
        />
        <div v-else class="empty-content">
          <el-icon class="text-4xl text-gray-400 mb-4"><Document /></el-icon>
          <p class="text-gray-500">暂无内容</p>
        </div>
      </div>
    </div>

    <!-- 调整大小的拖拽点 -->
    <div class="resize-handles" v-if="resizable && !isMaximized">
      <div class="resize-handle resize-n" @mousedown="startResize('n')"></div>
      <div class="resize-handle resize-s" @mousedown="startResize('s')"></div>
      <div class="resize-handle resize-w" @mousedown="startResize('w')"></div>
      <div class="resize-handle resize-e" @mousedown="startResize('e')"></div>
      <div class="resize-handle resize-nw" @mousedown="startResize('nw')"></div>
      <div class="resize-handle resize-ne" @mousedown="startResize('ne')"></div>
      <div class="resize-handle resize-sw" @mousedown="startResize('sw')"></div>
      <div class="resize-handle resize-se" @mousedown="startResize('se')"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineExpose } from 'vue'
import { Document } from '@element-plus/icons-vue'
import type { RunnerWindowProps, RunnerWindowEmits, RunnerWindowInstance } from './types'

// Props
const props = withDefaults(defineProps<RunnerWindowProps>(), {
  visible: false,
  title: '运行窗口',
  isMinimized: false,
  isMaximized: false,
  position: () => ({ x: 100, y: 80 }),
  size: () => ({ width: 1000, height: 700 }),
  resizable: true,
  draggable: true,
  showControls: true,
  defaultFullscreen: false,
  enableTaskbar: true
})

// Emits
const emit = defineEmits<RunnerWindowEmits>()

// 响应式数据
const runnerWindow = ref<HTMLElement | null>(null)
const isDragging = ref(false)
const isResizing = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const resizeDirection = ref('')
const originalWindowState = ref({ x: 0, y: 0, width: 0, height: 0 })

// 计算属性
const runnerWindowStyle = computed(() => {
  if (props.isMaximized || props.defaultFullscreen) {
    return {
      position: 'fixed' as const,
      top: '0px',
      left: '0px',
      width: '100vw',
      height: '100vh',
      zIndex: 10000
    }
  }

  return {
    position: 'fixed' as const,
    top: props.position.y + 'px',
    left: props.position.x + 'px',
    width: props.size.width + 'px',
    height: props.size.height + 'px',
    zIndex: 10000
  }
})

// 计算是否显示最小化按钮
const showMinimizeButton = computed(() => {
  return props.enableTaskbar && props.showControls
})

// 方法
const minimizeRunner = () => {
  emit('minimize')
  emit('update:isMinimized', true)
}

const closeRunner = () => {
  emit('close')
  emit('update:visible', false)
}

const toggleMaximize = () => {
  if (props.isMaximized) {
    // 还原窗口
    emit('update:isMaximized', false)
    emit('update:position', { ...originalWindowState.value })
    emit('update:size', {
      width: originalWindowState.value.width,
      height: originalWindowState.value.height
    })
  } else {
    // 最大化窗口
    originalWindowState.value = {
      x: props.position.x,
      y: props.position.y,
      width: props.size.width,
      height: props.size.height
    }
    emit('update:isMaximized', true)
  }
  emit('maximize', !props.isMaximized)
}

// 拖动相关方法
const startDrag = (event: MouseEvent) => {
  if (props.isMaximized || !props.draggable) return

  isDragging.value = true
  dragStart.value = {
    x: event.clientX - props.position.x,
    y: event.clientY - props.position.y
  }

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
  event.preventDefault()
}

const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  const newPosition = {
    x: Math.max(0, Math.min(window.innerWidth - 200, event.clientX - dragStart.value.x)),
    y: Math.max(60, Math.min(window.innerHeight - 100, event.clientY - dragStart.value.y))
  }
  emit('update:position', newPosition)
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 调整大小相关方法
const startResize = (direction: string) => {
  if (props.isMaximized || !props.resizable) return

  isResizing.value = true
  resizeDirection.value = direction

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
}

const handleResize = (event: MouseEvent) => {
  if (!isResizing.value) return

  const minWidth = 400
  const minHeight = 300
  const direction = resizeDirection.value

  let newWidth = props.size.width
  let newHeight = props.size.height
  let newX = props.position.x
  let newY = props.position.y

  // 计算新的尺寸和位置
  if (direction.includes('e')) {
    newWidth = Math.max(minWidth, event.clientX - props.position.x)
  }
  if (direction.includes('w')) {
    const newLeft = Math.min(event.clientX, props.position.x + props.size.width - minWidth)
    newWidth = props.position.x + props.size.width - newLeft
    newX = newLeft
  }
  if (direction.includes('s')) {
    newHeight = Math.max(minHeight, event.clientY - props.position.y)
  }
  if (direction.includes('n')) {
    let newTop = Math.min(event.clientY, props.position.y + props.size.height - minHeight)
    newTop = Math.max(60, newTop) // 不能超过顶部菜单
    newHeight = props.position.y + props.size.height - newTop
    newY = newTop
  }

  // 确保窗口不超出屏幕边界
  newX = Math.max(0, Math.min(newX, window.innerWidth - newWidth))
  newY = Math.max(60, Math.min(newY, window.innerHeight - newHeight))

  // 应用新的尺寸和位置
  emit('update:size', { width: newWidth, height: newHeight })
  emit('update:position', { x: newX, y: newY })
}

const stopResize = () => {
  isResizing.value = false
  resizeDirection.value = ''
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

const getMetaClass = (type?: string, color?: string) => {
  const baseClass = 'meta-item'
  if (type === 'badge') {
    const colorClass = color ? `badge-${color}` : 'badge-default'
    return `${baseClass} badge ${colorClass}`
  }
  return `${baseClass} text`
}

// 暴露实例方法
const instance: RunnerWindowInstance = {
  minimize: minimizeRunner,
  maximize: () => emit('update:isMaximized', true),
  close: closeRunner,
  toggleMaximize,
  updatePosition: (position) => emit('update:position', position),
  updateSize: (size) => emit('update:size', size)
}

defineExpose(instance)
</script>

<style scoped>
/* 通用运行窗口样式 */
.runner-window {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  min-width: 400px;
  min-height: 300px;
  user-select: none;
}

.runner-header {
  height: 50px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom: 1px solid #e2e8f0;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  flex-shrink: 0;
  position: relative;
}

.header-info-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.default-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1a365d;
  font-size: 14px;
}

.custom-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.header-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.header-details {
  flex: 1;
  min-width: 0;
}

.header-details h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a365d;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
  flex-wrap: wrap;
}

.subtitle {
  font-size: 11px;
  color: #64748b;
}

.meta-items {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 10px;
}

.meta-item.text {
  color: #64748b;
}

.meta-item.badge {
  padding: 1px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.badge-default {
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  color: #1a365d;
}

.badge-blue {
  background: #dbeafe;
  color: #1e40af;
}

.badge-green {
  background: #dcfce7;
  color: #166534;
}

.badge-purple {
  background: #f3e8ff;
  color: #7c3aed;
}

.runner-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.runner-control-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.minimize-btn {
  background: #ffc107;
  color: white;
}

.maximize-btn {
  background: #28a745;
  color: white;
}

.close-btn {
  background: #dc3545;
  color: white;
}

.minimize-btn:hover,
.maximize-btn:hover,
.close-btn:hover {
  transform: scale(1.1);
}

.runner-content-container {
  flex: 1;
  position: relative;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素能正确收缩 */
}

.runner-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素能正确收缩 */
}

.runner-dynamic-component {
  flex: 1;
  width: 100%;
  border: none;
  background: #ffffff;
  min-height: 0; /* 确保组件内容能正确滚动 */
  overflow: hidden; /* 让组件内部处理滚动 */
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: #6b7280;
  min-height: 200px; /* 确保空状态有最小高度 */
}

/* 调整大小拖拽点样式 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  pointer-events: all;
  z-index: 10;
}

.resize-n {
  top: -3px;
  left: 10px;
  right: 10px;
  height: 6px;
  cursor: n-resize;
}

.resize-s {
  bottom: -3px;
  left: 10px;
  right: 10px;
  height: 6px;
  cursor: s-resize;
}

.resize-w {
  left: -3px;
  top: 10px;
  bottom: 10px;
  width: 6px;
  cursor: w-resize;
}

.resize-e {
  right: -3px;
  top: 10px;
  bottom: 10px;
  width: 6px;
  cursor: e-resize;
}

.resize-nw {
  top: -3px;
  left: -3px;
  width: 10px;
  height: 10px;
  cursor: nw-resize;
}

.resize-ne {
  top: -3px;
  right: -3px;
  width: 10px;
  height: 10px;
  cursor: ne-resize;
}

.resize-sw {
  bottom: -3px;
  left: -3px;
  width: 10px;
  height: 10px;
  cursor: sw-resize;
}

.resize-se {
  bottom: -3px;
  right: -3px;
  width: 10px;
  height: 10px;
  cursor: se-resize;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .runner-window {
    border-radius: 8px;
    min-width: 300px;
    min-height: 250px;
  }

  .runner-header {
    height: 44px;
    padding: 0 12px;
    border-radius: 8px 8px 0 0;
  }

  .header-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .header-details h3 {
    font-size: 13px;
  }

  .header-meta {
    gap: 6px;
  }

  .meta-item {
    font-size: 9px;
  }

  .runner-control-btn {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
}
</style>
