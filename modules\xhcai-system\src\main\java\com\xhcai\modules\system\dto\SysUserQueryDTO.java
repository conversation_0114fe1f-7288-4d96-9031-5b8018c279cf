package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "用户查询条件")
public class SysUserQueryDTO extends PageQueryDTO {

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 状态
     */
    @Schema(description = "状态", allowableValues = {"0", "1", "9"})
    private String status;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 性别
     */
    @Schema(description = "性别", allowableValues = {"0", "1", "2"})
    private String gender;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private String endTime;

    /**
     * 数据权限SQL
     */
    @Schema(hidden = true)
    private String dataScope;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    @Override
    public String toString() {
        return "SysUserQueryDTO{"
                + "current=" + getCurrent()
                + ", size=" + getSize()
                + ", username='" + username + '\''
                + ", nickname='" + nickname + '\''
                + ", email='" + email + '\''
                + ", phone='" + phone + '\''
                + ", status='" + status + '\''
                + ", deptId=" + deptId
                + ", gender='" + gender + '\''
                + ", beginTime='" + beginTime + '\''
                + ", endTime='" + endTime + '\''
                + ", orderBy='" + getOrderBy() + '\''
                + ", orderDirection='" + getOrderDirection() + '\''
                + '}';
    }
}
