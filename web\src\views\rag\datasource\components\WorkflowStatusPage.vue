<template>
  <div class="workflow-status-page">
    <!-- 工作流概览 -->
    <div class="workflow-overview">
      <div class="overview-header">
        <div class="workflow-info">
          <div class="workflow-icon" :style="{ background: workflow.iconBg }">
            <i :class="workflow.icon"></i>
          </div>
          <div class="workflow-details">
            <h2>{{ workflow.name }}</h2>
            <p>{{ workflow.description }}</p>
          </div>
        </div>
        
        <div class="workflow-actions">
          <button class="btn btn-outline" @click="pauseWorkflow" v-if="isRunning">
            <i class="fas fa-pause"></i>
            暂停
          </button>
          <button class="btn btn-outline" @click="resumeWorkflow" v-else-if="isPaused">
            <i class="fas fa-play"></i>
            恢复
          </button>
          <button class="btn btn-primary" @click="startWorkflow" v-else>
            <i class="fas fa-play"></i>
            启动
          </button>
          <button class="btn btn-outline" @click="stopWorkflow" v-if="isRunning || isPaused">
            <i class="fas fa-stop"></i>
            停止
          </button>
        </div>
      </div>
      
      <!-- 状态指标 -->
      <div class="status-metrics">
        <div class="metric-card">
          <div class="metric-icon success">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ metrics.successCount }}</div>
            <div class="metric-label">成功处理</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon error">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ metrics.errorCount }}</div>
            <div class="metric-label">处理失败</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon processing">
            <i class="fas fa-spinner fa-spin" v-if="isRunning"></i>
            <i class="fas fa-clock" v-else></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ metrics.processingCount }}</div>
            <div class="metric-label">处理中</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon total">
            <i class="fas fa-list"></i>
          </div>
          <div class="metric-info">
            <div class="metric-value">{{ metrics.totalCount }}</div>
            <div class="metric-label">总计</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：执行日志 -->
      <div class="logs-section">
        <div class="section-header">
          <h3>执行日志</h3>
          <div class="log-controls">
            <button class="btn btn-sm btn-outline" @click="clearLogs">
              <i class="fas fa-trash"></i>
              清空
            </button>
            <button class="btn btn-sm btn-outline" @click="refreshLogs">
              <i class="fas fa-sync-alt"></i>
              刷新
            </button>
          </div>
        </div>
        
        <div class="logs-container" ref="logsContainer">
          <div 
            v-for="log in logs" 
            :key="log.id"
            class="log-entry"
            :class="log.level"
          >
            <div class="log-time">{{ formatTime(log.timestamp) }}</div>
            <div class="log-level">
              <span class="level-badge" :class="log.level">{{ log.level.toUpperCase() }}</span>
            </div>
            <div class="log-message">{{ log.message }}</div>
          </div>
          
          <div v-if="logs.length === 0" class="no-logs">
            <i class="fas fa-file-alt"></i>
            <p>暂无日志记录</p>
          </div>
        </div>
      </div>

      <!-- 右侧：任务队列 -->
      <div class="tasks-section">
        <div class="section-header">
          <h3>任务队列</h3>
          <div class="task-filter">
            <select v-model="taskFilter" class="form-select">
              <option value="all">全部</option>
              <option value="pending">待处理</option>
              <option value="processing">处理中</option>
              <option value="completed">已完成</option>
              <option value="failed">失败</option>
            </select>
          </div>
        </div>
        
        <div class="tasks-container">
          <div 
            v-for="task in filteredTasks" 
            :key="task.id"
            class="task-item"
            :class="task.status"
          >
            <div class="task-icon">
              <i class="fas fa-spinner fa-spin" v-if="task.status === 'processing'"></i>
              <i class="fas fa-check-circle" v-else-if="task.status === 'completed'"></i>
              <i class="fas fa-exclamation-circle" v-else-if="task.status === 'failed'"></i>
              <i class="fas fa-clock" v-else></i>
            </div>
            
            <div class="task-info">
              <div class="task-name">{{ task.name }}</div>
              <div class="task-details">
                <span class="task-type">{{ task.type }}</span>
                <span class="task-time">{{ formatTime(task.createdAt) }}</span>
              </div>
              <div class="task-progress" v-if="task.status === 'processing'">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: `${task.progress}%` }"></div>
                </div>
                <span class="progress-text">{{ task.progress }}%</span>
              </div>
            </div>
            
            <div class="task-actions">
              <button 
                class="action-btn" 
                @click="retryTask(task)" 
                v-if="task.status === 'failed'"
                title="重试"
              >
                <i class="fas fa-redo"></i>
              </button>
              <button 
                class="action-btn" 
                @click="cancelTask(task)" 
                v-if="task.status === 'processing' || task.status === 'pending'"
                title="取消"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          
          <div v-if="filteredTasks.length === 0" class="no-tasks">
            <i class="fas fa-tasks"></i>
            <p>暂无任务</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// Props
interface Props {
  workflow: any
  datasetId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'close': []
}>()

// 响应式数据
const logsContainer = ref<HTMLElement | null>(null)
const taskFilter = ref('all')

// 工作流状态
const workflowStatus = ref('stopped') // stopped, running, paused
const isRunning = computed(() => workflowStatus.value === 'running')
const isPaused = computed(() => workflowStatus.value === 'paused')

// 指标数据
const metrics = ref({
  successCount: 156,
  errorCount: 3,
  processingCount: 2,
  totalCount: 161
})

// 日志数据
const logs = ref([
  {
    id: 1,
    timestamp: new Date(),
    level: 'info',
    message: '工作流启动成功'
  },
  {
    id: 2,
    timestamp: new Date(Date.now() - 60000),
    level: 'success',
    message: '文件 document1.pdf 处理完成'
  },
  {
    id: 3,
    timestamp: new Date(Date.now() - 120000),
    level: 'error',
    message: '文件 document2.docx 处理失败：格式不支持'
  },
  {
    id: 4,
    timestamp: new Date(Date.now() - 180000),
    level: 'warning',
    message: '检测到重复文件，已跳过处理'
  }
])

// 任务数据
const tasks = ref([
  {
    id: 1,
    name: 'document1.pdf',
    type: '文档处理',
    status: 'completed',
    progress: 100,
    createdAt: new Date(Date.now() - 300000)
  },
  {
    id: 2,
    name: 'document2.docx',
    type: '文档处理',
    status: 'processing',
    progress: 65,
    createdAt: new Date(Date.now() - 180000)
  },
  {
    id: 3,
    name: 'document3.txt',
    type: '文档处理',
    status: 'pending',
    progress: 0,
    createdAt: new Date(Date.now() - 60000)
  },
  {
    id: 4,
    name: 'document4.pdf',
    type: '文档处理',
    status: 'failed',
    progress: 0,
    createdAt: new Date(Date.now() - 240000)
  }
])

// 计算属性
const filteredTasks = computed(() => {
  if (taskFilter.value === 'all') {
    return tasks.value
  }
  return tasks.value.filter(task => task.status === taskFilter.value)
})

// 方法
const startWorkflow = () => {
  workflowStatus.value = 'running'
  addLog('info', '工作流已启动')
}

const pauseWorkflow = () => {
  workflowStatus.value = 'paused'
  addLog('warning', '工作流已暂停')
}

const resumeWorkflow = () => {
  workflowStatus.value = 'running'
  addLog('info', '工作流已恢复')
}

const stopWorkflow = () => {
  workflowStatus.value = 'stopped'
  addLog('info', '工作流已停止')
}

const addLog = (level: string, message: string) => {
  logs.value.unshift({
    id: Date.now(),
    timestamp: new Date(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
  
  // 自动滚动到顶部
  nextTick(() => {
    if (logsContainer.value) {
      logsContainer.value.scrollTop = 0
    }
  })
}

const clearLogs = () => {
  logs.value = []
}

const refreshLogs = () => {
  // 模拟刷新日志
  addLog('info', '日志已刷新')
}

const retryTask = (task: any) => {
  task.status = 'pending'
  task.progress = 0
  addLog('info', `任务 ${task.name} 已重新加入队列`)
}

const cancelTask = (task: any) => {
  task.status = 'failed'
  task.progress = 0
  addLog('warning', `任务 ${task.name} 已取消`)
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 模拟实时更新
let updateInterval: number | null = null

onMounted(() => {
  // 每5秒更新一次数据
  updateInterval = setInterval(() => {
    if (isRunning.value) {
      // 模拟任务进度更新
      tasks.value.forEach(task => {
        if (task.status === 'processing' && task.progress < 100) {
          task.progress = Math.min(100, task.progress + Math.random() * 10)
          if (task.progress >= 100) {
            task.status = 'completed'
            metrics.value.successCount++
            metrics.value.processingCount--
            addLog('success', `任务 ${task.name} 处理完成`)
          }
        }
      })
    }
  }, 5000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.workflow-status-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8fafc;
}

.workflow-overview {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 24px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.workflow-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.workflow-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
}

.workflow-details h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.workflow-details p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.workflow-actions {
  display: flex;
  gap: 12px;
}

.status-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.metric-icon.success {
  background: #dcfce7;
  color: #16a34a;
}

.metric-icon.error {
  background: #fef2f2;
  color: #dc2626;
}

.metric-icon.processing {
  background: #dbeafe;
  color: #2563eb;
}

.metric-icon.total {
  background: #f3f4f6;
  color: #6b7280;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.metric-label {
  font-size: 14px;
  color: #64748b;
}

.main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
  padding: 24px;
  overflow: hidden;
}

.logs-section,
.tasks-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.log-controls,
.task-filter {
  display: flex;
  gap: 8px;
}

.logs-container,
.tasks-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.log-entry {
  display: grid;
  grid-template-columns: 80px 60px 1fr;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.log-entry.info {
  background: #f0f9ff;
  border-left: 4px solid #3b82f6;
}

.log-entry.success {
  background: #f0fdf4;
  border-left: 4px solid #22c55e;
}

.log-entry.warning {
  background: #fffbeb;
  border-left: 4px solid #f59e0b;
}

.log-entry.error {
  background: #fef2f2;
  border-left: 4px solid #ef4444;
}

.log-time {
  color: #64748b;
  font-family: monospace;
}

.level-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.level-badge.info {
  background: #dbeafe;
  color: #1d4ed8;
}

.level-badge.success {
  background: #dcfce7;
  color: #166534;
}

.level-badge.warning {
  background: #fef3c7;
  color: #92400e;
}

.level-badge.error {
  background: #fecaca;
  color: #991b1b;
}

.log-message {
  color: #1e293b;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.task-item:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.task-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.task-item.pending .task-icon {
  background: #f3f4f6;
  color: #6b7280;
}

.task-item.processing .task-icon {
  background: #dbeafe;
  color: #2563eb;
}

.task-item.completed .task-icon {
  background: #dcfce7;
  color: #16a34a;
}

.task-item.failed .task-icon {
  background: #fef2f2;
  color: #dc2626;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 4px;
}

.task-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #64748b;
  white-space: nowrap;
}

.task-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  color: #64748b;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.no-logs,
.no-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #64748b;
  text-align: center;
}

.no-logs i,
.no-tasks i {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.no-logs p,
.no-tasks p {
  font-size: 14px;
  margin: 0;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-outline {
  background: white;
  color: #64748b;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.form-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  background: white;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
}
</style>
