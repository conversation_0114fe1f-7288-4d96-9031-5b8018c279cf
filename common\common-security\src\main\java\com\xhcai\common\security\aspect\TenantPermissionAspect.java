package com.xhcai.common.security.aspect;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.xhcai.common.core.exception.SystemException;
import com.xhcai.common.security.annotation.CrossTenantAccess;
import com.xhcai.common.security.annotation.RequiresPlatformAdmin;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.common.security.service.LoginUser;
import com.xhcai.common.security.utils.SecurityUtils;

/**
 * 租户权限切面 处理平台管理员、租户管理员、跨租户访问等权限校验
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Aspect
@Component
@Order(100) // 在其他权限切面之前执行
public class TenantPermissionAspect {

    private static final Logger log = LoggerFactory.getLogger(TenantPermissionAspect.class);

    /**
     * 平台管理员权限校验
     */
    @Before("@annotation(requiresPlatformAdmin)")
    public void checkPlatformAdminPermission(JoinPoint joinPoint, RequiresPlatformAdmin requiresPlatformAdmin) {
        LoginUser currentUser = SecurityUtils.getCurrentUser();
        if (currentUser == null) {
            throw new SystemException("用户未登录");
        }

        boolean isPlatformAdmin = SecurityUtils.isPlatformAdmin();
        boolean isTenantAdmin = SecurityUtils.isTenantAdmin();

        // 如果不是平台管理员
        if (!isPlatformAdmin) {
            // 检查是否允许租户管理员访问
            if (!requiresPlatformAdmin.allowTenantAdmin() || !isTenantAdmin) {
                log.warn("用户 {} 尝试访问平台管理员功能: {}", currentUser.getUsername(), joinPoint.getSignature().getName());
                throw new SystemException(requiresPlatformAdmin.message());
            }
        }

        log.debug("平台管理员权限校验通过: 用户={}, 方法={}", currentUser.getUsername(), joinPoint.getSignature().getName());
    }

    /**
     * 租户管理员权限校验
     */
    @Before("@annotation(requiresTenantAdmin)")
    public void checkTenantAdminPermission(JoinPoint joinPoint, RequiresTenantAdmin requiresTenantAdmin) {
        LoginUser currentUser = SecurityUtils.getCurrentUser();
        if (currentUser == null) {
            throw new SystemException("用户未登录");
        }

        boolean isPlatformAdmin = SecurityUtils.isPlatformAdmin();
        boolean isTenantAdmin = SecurityUtils.isTenantAdmin();

        // 如果是平台管理员且允许平台管理员访问，直接通过
        if (isPlatformAdmin && requiresTenantAdmin.allowPlatformAdmin()) {
            log.debug("平台管理员访问租户管理功能: 用户={}, 方法={}", currentUser.getUsername(), joinPoint.getSignature().getName());
            return;
        }

        // 检查是否是租户管理员
        if (!isTenantAdmin) {
            log.warn("用户 {} 尝试访问租户管理员功能: {}", currentUser.getUsername(), joinPoint.getSignature().getName());
            throw new SystemException(requiresTenantAdmin.message());
        }

        log.debug("租户管理员权限校验通过: 用户={}, 租户={}, 方法={}",
                currentUser.getUsername(), currentUser.getTenantId(), joinPoint.getSignature().getName());
    }

    /**
     * 跨租户访问权限校验
     */
    @Before("@annotation(crossTenantAccess)")
    public void checkCrossTenantAccess(JoinPoint joinPoint, CrossTenantAccess crossTenantAccess) {
        LoginUser currentUser = SecurityUtils.getCurrentUser();
        if (currentUser == null) {
            throw new SystemException("用户未登录");
        }

        // 只有平台管理员可以进行跨租户访问
        if (!SecurityUtils.isPlatformAdmin()) {
            log.warn("用户 {} 尝试进行跨租户访问: {}", currentUser.getUsername(), joinPoint.getSignature().getName());
            throw new SystemException(crossTenantAccess.message());
        }

        // 记录跨租户访问日志
        if (crossTenantAccess.logAccess()) {
            log.info("跨租户访问: 用户={}, 方法={}, 参数={}",
                    currentUser.getUsername(), joinPoint.getSignature().getName(), joinPoint.getArgs());
        }
    }

}
