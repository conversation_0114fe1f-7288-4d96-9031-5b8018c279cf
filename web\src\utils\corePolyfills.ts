/**
 * 核心Polyfills集成
 * 为旧版浏览器提供必要的API支持
 */

/**
 * Intersection Observer Polyfill
 */
if (!('IntersectionObserver' in window)) {
  class IntersectionObserverPolyfill {
    private callback: IntersectionObserverCallback
    private options: IntersectionObserverInit
    private targets: Element[] = []
    private interval: number | null = null

    constructor(callback: IntersectionObserverCallback, options: IntersectionObserverInit = {}) {
      this.callback = callback
      this.options = {
        root: options.root || null,
        rootMargin: options.rootMargin || '0px',
        threshold: options.threshold || 0
      }
    }

    observe(target: Element): void {
      if (this.targets.indexOf(target) === -1) {
        this.targets.push(target)
        this.startPolling()
      }
    }

    unobserve(target: Element): void {
      const index = this.targets.indexOf(target)
      if (index !== -1) {
        this.targets.splice(index, 1)
        if (this.targets.length === 0) {
          this.stopPolling()
        }
      }
    }

    disconnect(): void {
      this.targets = []
      this.stopPolling()
    }

    private startPolling(): void {
      if (!this.interval) {
        this.interval = window.setInterval(() => {
          this.checkIntersections()
        }, 100)
      }
    }

    private stopPolling(): void {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    }

    private checkIntersections(): void {
      const entries: IntersectionObserverEntry[] = []
      
      this.targets.forEach(target => {
        const rect = target.getBoundingClientRect()
        const isIntersecting = rect.top < window.innerHeight && rect.bottom > 0
        
        entries.push({
          target,
          isIntersecting,
          intersectionRatio: isIntersecting ? 1 : 0,
          boundingClientRect: rect,
          intersectionRect: isIntersecting ? rect : new DOMRect(),
          rootBounds: new DOMRect(0, 0, window.innerWidth, window.innerHeight),
          time: Date.now()
        } as IntersectionObserverEntry)
      })

      if (entries.length > 0) {
        this.callback(entries, this as any)
      }
    }
  }

  (window as any).IntersectionObserver = IntersectionObserverPolyfill
}

/**
 * Resize Observer Polyfill
 */
if (!('ResizeObserver' in window)) {
  class ResizeObserverPolyfill {
    private callback: ResizeObserverCallback
    private targets: Map<Element, { width: number; height: number }> = new Map()
    private interval: number | null = null

    constructor(callback: ResizeObserverCallback) {
      this.callback = callback
    }

    observe(target: Element): void {
      const rect = target.getBoundingClientRect()
      this.targets.set(target, { width: rect.width, height: rect.height })
      this.startPolling()
    }

    unobserve(target: Element): void {
      this.targets.delete(target)
      if (this.targets.size === 0) {
        this.stopPolling()
      }
    }

    disconnect(): void {
      this.targets.clear()
      this.stopPolling()
    }

    private startPolling(): void {
      if (!this.interval) {
        this.interval = window.setInterval(() => {
          this.checkResize()
        }, 100)
      }
    }

    private stopPolling(): void {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    }

    private checkResize(): void {
      const entries: ResizeObserverEntry[] = []

      this.targets.forEach((lastSize, target) => {
        const rect = target.getBoundingClientRect()
        if (rect.width !== lastSize.width || rect.height !== lastSize.height) {
          this.targets.set(target, { width: rect.width, height: rect.height })
          
          entries.push({
            target,
            contentRect: new DOMRect(0, 0, rect.width, rect.height),
            borderBoxSize: [{ blockSize: rect.height, inlineSize: rect.width }],
            contentBoxSize: [{ blockSize: rect.height, inlineSize: rect.width }],
            devicePixelContentBoxSize: [{ blockSize: rect.height, inlineSize: rect.width }]
          } as ResizeObserverEntry)
        }
      })

      if (entries.length > 0) {
        this.callback(entries, this as any)
      }
    }
  }

  (window as any).ResizeObserver = ResizeObserverPolyfill
}

/**
 * Fetch API Polyfill (简化版)
 */
if (!('fetch' in window)) {
  (window as any).fetch = function(url: string, options: RequestInit = {}): Promise<Response> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      const method = options.method || 'GET'
      
      xhr.open(method, url)
      
      // 设置请求头
      if (options.headers) {
        Object.entries(options.headers).forEach(([key, value]) => {
          xhr.setRequestHeader(key, value as string)
        })
      }
      
      xhr.onload = () => {
        const response = {
          ok: xhr.status >= 200 && xhr.status < 300,
          status: xhr.status,
          statusText: xhr.statusText,
          headers: new Map(),
          text: () => Promise.resolve(xhr.responseText),
          json: () => Promise.resolve(JSON.parse(xhr.responseText)),
          blob: () => Promise.resolve(new Blob([xhr.response])),
          arrayBuffer: () => Promise.resolve(xhr.response),
          // 添加缺失的 Response 属性
          redirected: false,
          type: 'basic' as ResponseType,
          url: url,
          clone: () => response as unknown as Response,
          bytes: () => Promise.resolve(new Uint8Array()),
          formData: () => Promise.resolve(new FormData()),
          body: null,
          bodyUsed: false
        }
        resolve(response as unknown as Response)
      }
      
      xhr.onerror = () => reject(new Error('Network error'))
      xhr.ontimeout = () => reject(new Error('Request timeout'))
      
      // 发送请求
      xhr.send(options.body as any)
    })
  }
}

/**
 * URL Polyfill (简化版)
 */
if (!('URL' in window)) {
  (window as any).URL = class URLPolyfill {
    href: string
    protocol: string
    host: string
    hostname: string
    port: string
    pathname: string
    search: string
    hash: string
    origin: string

    constructor(url: string, base?: string) {
      const a = document.createElement('a')
      a.href = base ? new URLPolyfill(base).href + '/' + url : url
      
      this.href = a.href
      this.protocol = a.protocol
      this.host = a.host
      this.hostname = a.hostname
      this.port = a.port
      this.pathname = a.pathname
      this.search = a.search
      this.hash = a.hash
      this.origin = a.protocol + '//' + a.host
    }

    toString(): string {
      return this.href
    }

    static createObjectURL(blob: Blob): string {
      if (window.webkitURL) {
        return window.webkitURL.createObjectURL(blob)
      }
      // Fallback for very old browsers
      return 'data:' + blob.type + ';base64,' + btoa(blob as any)
    }

    static revokeObjectURL(url: string): void {
      if (window.webkitURL) {
        window.webkitURL.revokeObjectURL(url)
      }
    }
  }
}

/**
 * URLSearchParams Polyfill
 */
if (!('URLSearchParams' in window)) {
  (window as any).URLSearchParams = class URLSearchParamsPolyfill {
    private params: Map<string, string[]> = new Map()

    constructor(init?: string | URLSearchParamsPolyfill) {
      if (typeof init === 'string') {
        this.parseString(init)
      } else if (init instanceof URLSearchParamsPolyfill) {
        init.params.forEach((values, key) => {
          this.params.set(key, [...values])
        })
      }
    }

    private parseString(str: string): void {
      const pairs = str.replace(/^\?/, '').split('&')
      pairs.forEach(pair => {
        const [key, value] = pair.split('=').map(decodeURIComponent)
        if (key) {
          this.append(key, value || '')
        }
      })
    }

    append(name: string, value: string): void {
      if (!this.params.has(name)) {
        this.params.set(name, [])
      }
      this.params.get(name)!.push(value)
    }

    delete(name: string): void {
      this.params.delete(name)
    }

    get(name: string): string | null {
      const values = this.params.get(name)
      return values ? values[0] : null
    }

    getAll(name: string): string[] {
      return this.params.get(name) || []
    }

    has(name: string): boolean {
      return this.params.has(name)
    }

    set(name: string, value: string): void {
      this.params.set(name, [value])
    }

    toString(): string {
      const pairs: string[] = []
      this.params.forEach((values, key) => {
        values.forEach(value => {
          pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(value))
        })
      })
      return pairs.join('&')
    }
  }
}

/**
 * 初始化核心polyfills
 */
export function initCorePolyfills(): void {
  console.log('Core polyfills initialized')
}
