package com.xhcai.modules.rag.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * RAG模块配置属性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "xhcai.rag")
public class RagProperties {

    /**
     * 文档处理配置
     */
    private Document document = new Document();

    /**
     * 向量化配置
     */
    private Embedding embedding = new Embedding();

    /**
     * 检索配置
     */
    private Retrieval retrieval = new Retrieval();

    /**
     * 限制配置
     */
    private Limits limits = new Limits();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 任务配置
     */
    private Task task = new Task();

    // Getters and Setters
    public Document getDocument() {
        return document;
    }

    public void setDocument(Document document) {
        this.document = document;
    }

    public Embedding getEmbedding() {
        return embedding;
    }

    public void setEmbedding(Embedding embedding) {
        this.embedding = embedding;
    }

    public Retrieval getRetrieval() {
        return retrieval;
    }

    public void setRetrieval(Retrieval retrieval) {
        this.retrieval = retrieval;
    }

    public Limits getLimits() {
        return limits;
    }

    public void setLimits(Limits limits) {
        this.limits = limits;
    }

    public Cache getCache() {
        return cache;
    }

    public void setCache(Cache cache) {
        this.cache = cache;
    }

    public Task getTask() {
        return task;
    }

    public void setTask(Task task) {
        this.task = task;
    }

    /**
     * 文档处理配置
     */
    public static class Document {

        /**
         * 最大文件大小
         */
        private String maxSize = "50MB";

        /**
         * 允许的文件类型
         */
        private List<String> allowedTypes = List.of("pdf", "doc", "docx", "txt", "md", "html");

        /**
         * 分段大小
         */
        private Integer chunkSize = 1024;

        /**
         * 分段重叠
         */
        private Integer chunkOverlap = 50;

        /**
         * 最大分段数
         */
        private Integer maxChunks = 10000;

        /**
         * 文档处理超时时间（秒）
         */
        private Integer processingTimeout = 300;

        // Getters and Setters
        public String getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(String maxSize) {
            this.maxSize = maxSize;
        }

        public List<String> getAllowedTypes() {
            return allowedTypes;
        }

        public void setAllowedTypes(List<String> allowedTypes) {
            this.allowedTypes = allowedTypes;
        }

        public Integer getChunkSize() {
            return chunkSize;
        }

        public void setChunkSize(Integer chunkSize) {
            this.chunkSize = chunkSize;
        }

        public Integer getChunkOverlap() {
            return chunkOverlap;
        }

        public void setChunkOverlap(Integer chunkOverlap) {
            this.chunkOverlap = chunkOverlap;
        }

        public Integer getMaxChunks() {
            return maxChunks;
        }

        public void setMaxChunks(Integer maxChunks) {
            this.maxChunks = maxChunks;
        }

        public Integer getProcessingTimeout() {
            return processingTimeout;
        }

        public void setProcessingTimeout(Integer processingTimeout) {
            this.processingTimeout = processingTimeout;
        }
    }

    /**
     * 向量化配置
     */
    public static class Embedding {

        /**
         * 嵌入模型提供商
         */
        private String provider = "openai";

        /**
         * 默认嵌入模型
         */
        private String model = "text-embedding-ada-002";

        /**
         * 向量维度
         */
        private Integer dimension = 1536;

        /**
         * 批处理大小
         */
        private Integer batchSize = 100;

        /**
         * 向量化超时时间（秒）
         */
        private Integer timeout = 60;

        /**
         * 最大重试次数
         */
        private Integer maxRetries = 3;

        // Getters and Setters
        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public Integer getDimension() {
            return dimension;
        }

        public void setDimension(Integer dimension) {
            this.dimension = dimension;
        }

        public Integer getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(Integer batchSize) {
            this.batchSize = batchSize;
        }

        public Integer getTimeout() {
            return timeout;
        }

        public void setTimeout(Integer timeout) {
            this.timeout = timeout;
        }

        public Integer getMaxRetries() {
            return maxRetries;
        }

        public void setMaxRetries(Integer maxRetries) {
            this.maxRetries = maxRetries;
        }
    }

    /**
     * 检索配置
     */
    public static class Retrieval {

        /**
         * 默认返回结果数量
         */
        private Integer topK = 5;

        /**
         * 最大返回结果数量
         */
        private Integer maxTopK = 100;

        /**
         * 相似度阈值
         */
        private Double scoreThreshold = 0.7;

        /**
         * 是否启用重排序
         */
        private Boolean rerankEnabled = true;

        /**
         * 重排序模型
         */
        private String rerankModel = "gte-rerank-v2";

        /**
         * 检索超时时间（秒）
         */
        private Integer timeout = 30;

        // Getters and Setters
        public Integer getTopK() {
            return topK;
        }

        public void setTopK(Integer topK) {
            this.topK = topK;
        }

        public Integer getMaxTopK() {
            return maxTopK;
        }

        public void setMaxTopK(Integer maxTopK) {
            this.maxTopK = maxTopK;
        }

        public Double getScoreThreshold() {
            return scoreThreshold;
        }

        public void setScoreThreshold(Double scoreThreshold) {
            this.scoreThreshold = scoreThreshold;
        }

        public Boolean getRerankEnabled() {
            return rerankEnabled;
        }

        public void setRerankEnabled(Boolean rerankEnabled) {
            this.rerankEnabled = rerankEnabled;
        }

        public String getRerankModel() {
            return rerankModel;
        }

        public void setRerankModel(String rerankModel) {
            this.rerankModel = rerankModel;
        }

        public Integer getTimeout() {
            return timeout;
        }

        public void setTimeout(Integer timeout) {
            this.timeout = timeout;
        }
    }

    /**
     * 限制配置
     */
    public static class Limits {

        /**
         * 每个租户最大知识库数量
         */
        private Integer maxDatasetsPerTenant = 100;

        /**
         * 每个知识库最大文档数量
         */
        private Integer maxDocumentsPerDataset = 1000;

        /**
         * 每日最大向量化token数
         */
        private Long maxTokensPerDay = 1000000L;

        /**
         * 每日最大检索次数
         */
        private Integer maxRetrievalsPerDay = 10000;

        /**
         * 单个文档最大大小（字节）
         */
        private Long maxDocumentSize = 52428800L; // 50MB

        /**
         * 单次上传最大文件数
         */
        private Integer maxUploadFiles = 100;

        // Getters and Setters
        public Integer getMaxDatasetsPerTenant() {
            return maxDatasetsPerTenant;
        }

        public void setMaxDatasetsPerTenant(Integer maxDatasetsPerTenant) {
            this.maxDatasetsPerTenant = maxDatasetsPerTenant;
        }

        public Integer getMaxDocumentsPerDataset() {
            return maxDocumentsPerDataset;
        }

        public void setMaxDocumentsPerDataset(Integer maxDocumentsPerDataset) {
            this.maxDocumentsPerDataset = maxDocumentsPerDataset;
        }

        public Long getMaxTokensPerDay() {
            return maxTokensPerDay;
        }

        public void setMaxTokensPerDay(Long maxTokensPerDay) {
            this.maxTokensPerDay = maxTokensPerDay;
        }

        public Integer getMaxRetrievalsPerDay() {
            return maxRetrievalsPerDay;
        }

        public void setMaxRetrievalsPerDay(Integer maxRetrievalsPerDay) {
            this.maxRetrievalsPerDay = maxRetrievalsPerDay;
        }

        public Long getMaxDocumentSize() {
            return maxDocumentSize;
        }

        public void setMaxDocumentSize(Long maxDocumentSize) {
            this.maxDocumentSize = maxDocumentSize;
        }

        public Integer getMaxUploadFiles() {
            return maxUploadFiles;
        }

        public void setMaxUploadFiles(Integer maxUploadFiles) {
            this.maxUploadFiles = maxUploadFiles;
        }
    }

    /**
     * 缓存配置
     */
    public static class Cache {

        /**
         * 知识库配置缓存
         */
        private CacheConfig datasetConfig = new CacheConfig(true, 3600);

        /**
         * 文档元数据缓存
         */
        private CacheConfig documentMetadata = new CacheConfig(true, 1800);

        /**
         * 向量缓存
         */
        private CacheConfig embedding = new CacheConfig(true, 7200);

        // Getters and Setters
        public CacheConfig getDatasetConfig() {
            return datasetConfig;
        }

        public void setDatasetConfig(CacheConfig datasetConfig) {
            this.datasetConfig = datasetConfig;
        }

        public CacheConfig getDocumentMetadata() {
            return documentMetadata;
        }

        public void setDocumentMetadata(CacheConfig documentMetadata) {
            this.documentMetadata = documentMetadata;
        }

        public CacheConfig getEmbedding() {
            return embedding;
        }

        public void setEmbedding(CacheConfig embedding) {
            this.embedding = embedding;
        }

        /**
         * 缓存配置
         */
        public static class CacheConfig {

            private Boolean enabled;
            private Integer ttl;

            public CacheConfig() {
            }

            public CacheConfig(Boolean enabled, Integer ttl) {
                this.enabled = enabled;
                this.ttl = ttl;
            }

            public Boolean getEnabled() {
                return enabled;
            }

            public void setEnabled(Boolean enabled) {
                this.enabled = enabled;
            }

            public Integer getTtl() {
                return ttl;
            }

            public void setTtl(Integer ttl) {
                this.ttl = ttl;
            }
        }
    }

    /**
     * 任务配置
     */
    public static class Task {

        /**
         * 文档处理任务配置
         */
        private TaskConfig documentProcessing = new TaskConfig(5, 100, 600);

        /**
         * 向量化任务配置
         */
        private TaskConfig embedding = new TaskConfig(3, 200, 300);

        public TaskConfig getDocumentProcessing() {
            return documentProcessing;
        }

        public void setDocumentProcessing(TaskConfig documentProcessing) {
            this.documentProcessing = documentProcessing;
        }

        public TaskConfig getEmbedding() {
            return embedding;
        }

        public void setEmbedding(TaskConfig embedding) {
            this.embedding = embedding;
        }

        /**
         * 任务配置
         */
        public static class TaskConfig {

            private Integer threadPoolSize;
            private Integer queueSize;
            private Integer timeout;

            public TaskConfig() {
            }

            public TaskConfig(Integer threadPoolSize, Integer queueSize, Integer timeout) {
                this.threadPoolSize = threadPoolSize;
                this.queueSize = queueSize;
                this.timeout = timeout;
            }

            public Integer getThreadPoolSize() {
                return threadPoolSize;
            }

            public void setThreadPoolSize(Integer threadPoolSize) {
                this.threadPoolSize = threadPoolSize;
            }

            public Integer getQueueSize() {
                return queueSize;
            }

            public void setQueueSize(Integer queueSize) {
                this.queueSize = queueSize;
            }

            public Integer getTimeout() {
                return timeout;
            }

            public void setTimeout(Integer timeout) {
                this.timeout = timeout;
            }
        }
    }
}
