package com.xhcai.modules.agent.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 智能体查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体查询条件")
public class AgentQueryDTO extends PageTimeRangeQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体名称（模糊查询）
     */
    @Schema(description = "智能体名称", example = "客服")
    @Size(max = 100, message = "智能体名称长度不能超过100个字符")
    private String name;

    /**
     * 智能体类型
     */
    @Schema(description = "智能体类型", example = "chat", allowableValues = {"chat", "workflow", "completion", "advanced-chat", "agent-chat"})
    @Pattern(regexp = "^(chat|workflow|completion|advanced-chat|agent-chat)$", message = "智能体类型必须为chat、workflow、completion、advanced-chat或agent-chat")
    private String type;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "true")
    private Boolean isPublic;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "admin")
    @Size(max = 36, message = "创建人长度不能超过36个字符")
    private String createBy;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1.0.0")
    @Size(max = 20, message = "版本号长度不能超过20个字符")
    private String version;

    /**
     * 租户ID（平台管理员可用于跨租户查询）
     */
    @Schema(description = "租户ID", example = "1")
    @Size(max = 36, message = "租户ID长度不能超过36个字符")
    private String tenantId;

    /**
     * 业务项目ID
     */
    @Schema(description = "业务项目ID", example = "1")
    @Size(max = 36, message = "业务项目ID长度不能超过36个字符")
    private String projectId;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}
