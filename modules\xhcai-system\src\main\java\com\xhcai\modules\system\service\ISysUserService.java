package com.xhcai.modules.system.service;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysUserQueryDTO;
import com.xhcai.modules.system.dto.UserProfileUpdateDTO;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.vo.SysUserVO;
import com.xhcai.modules.system.vo.UserProfileVO;

/**
 * 用户信息服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysUserService extends IService<SysUser> {

    /**
     * 分页查询用户列表
     *
     * @param queryDTO 查询条件
     * @return 用户分页列表
     */
    PageResult<SysUserVO> selectUserPage(SysUserQueryDTO queryDTO);

    /**
     * 根据用户名查询用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectByUsername(String username);

    /**
     * 根据邮箱查询用户信息
     *
     * @param email 邮箱
     * @return 用户信息
     */
    SysUser selectByEmail(String email);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     */
    SysUser selectByPhone(String phone);

    /**
     * 根据用户ID查询用户权限
     *
     * @param sysUserVO 用户
     * @return 权限集合
     */
    Set<String> selectUserPermissions(SysUserVO sysUserVO);

    /**
     * 根据用户ID查询用户角色
     *
     * @param sysUserVO 用户
     * @return 角色集合
     */
    Set<String> selectUserRoles(SysUserVO sysUserVO);

    /**
     * 新增用户
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean insertUser(SysUser user);

    /**
     * 更新用户
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUser(SysUser user);

    /**
     * 删除用户
     *
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean deleteUsers(List<String> userIds);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param userId 用户ID（排除自己）
     * @return 是否存在
     */
    boolean existsUsername(String username, String userId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param userId 用户ID（排除自己）
     * @return 是否存在
     */
    boolean existsEmail(String email, String userId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param userId 用户ID（排除自己）
     * @return 是否存在
     */
    boolean existsPhone(String phone, String userId);

    /**
     * 更新用户登录信息
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     */
    void updateLoginInfo(String userId, String loginIp);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetPassword(String userId, String newPassword);

    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(String userId, String oldPassword, String newPassword);

    /**
     * 启用/禁用用户
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 是否成功
     */
    boolean changeStatus(String userId, String status);

    /**
     * 分配用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean assignRoles(String userId, List<String> roleIds);

    /**
     * 根据角色ID查询用户列表
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByRoleId(String roleId);

    /**
     * 根据部门ID查询用户列表
     *
     * @param deptId 部门ID
     * @return 用户列表
     */
    List<SysUser> selectUsersByDeptId(String deptId);

    /**
     * 导入用户数据
     *
     * @param userList 用户列表
     * @return 导入结果
     */
    String importUsers(List<SysUser> userList);

    /**
     * 导出用户数据
     *
     * @param queryDTO 查询条件
     * @return 用户列表
     */
    List<SysUserVO> exportUsers(SysUserQueryDTO queryDTO);

    /**
     * 为用户分配角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean assignUserRoles(String userId, List<String> roleIds, String tenantId);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> userIds, String status);

    // ==================== 个人信息相关方法 ====================
    /**
     * 获取当前用户个人信息
     *
     * @return 用户个人信息
     */
    UserProfileVO getCurrentUserProfile();

    /**
     * 更新当前用户个人信息
     *
     * @param updateDTO 更新参数
     */
    void updateCurrentUserProfile(UserProfileUpdateDTO updateDTO);

    /**
     * 修改当前用户密码
     *
     * @param oldPassword 原密码
     * @param newPassword 新密码
     */
    void changeCurrentUserPassword(String oldPassword, String newPassword);
}
