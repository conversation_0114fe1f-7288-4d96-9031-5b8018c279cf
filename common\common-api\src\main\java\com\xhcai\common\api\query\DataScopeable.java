package com.xhcai.common.api.query;

/**
 * 数据权限查询接口
 * 定义数据权限查询的基本方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DataScopeable {

    /**
     * 获取租户ID
     * 
     * @return 租户ID
     */
    Long getTenantId();

    /**
     * 获取数据权限SQL
     * 
     * @return 数据权限SQL
     */
    String getDataScope();

    /**
     * 设置数据权限SQL
     * 
     * @param dataScope 数据权限SQL
     */
    void setDataScope(String dataScope);

    /**
     * 是否有租户条件
     * 
     * @return true-有租户条件，false-无租户条件
     */
    default Boolean hasTenant() {
        return getTenantId() != null;
    }

    /**
     * 是否有数据权限条件
     * 
     * @return true-有数据权限条件，false-无数据权限条件
     */
    default Boolean hasDataScope() {
        return getDataScope() != null && !getDataScope().trim().isEmpty();
    }

    /**
     * 是否为平台管理员（租户ID为0或null）
     * 
     * @return true-平台管理员，false-租户用户
     */
    default Boolean isPlatformAdmin() {
        return getTenantId() == null || getTenantId().equals(0L);
    }

    /**
     * 是否为租户用户（租户ID大于0）
     * 
     * @return true-租户用户，false-平台管理员
     */
    default Boolean isTenantUser() {
        return getTenantId() != null && getTenantId() > 0L;
    }
}
