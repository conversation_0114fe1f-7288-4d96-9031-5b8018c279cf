package com.xhcai.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 业务项目（AI场景）实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "business_project")
@Schema(description = "业务项目（AI场景）")
@TableName("business_project")
public class BusinessProject extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 100, message = "项目名称长度不能超过100个字符")
    @Column(name = "name", nullable = false, length = 100)
    @TableField("name")
    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    /**
     * 项目描述
     */
    @Size(max = 500, message = "项目描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    @TableField("description")
    @Schema(description = "项目描述")
    private String description;

    /**
     * 项目负责人ID
     */
    @NotBlank(message = "项目负责人不能为空")
    @Column(name = "owner_id", nullable = false, length = 36)
    @TableField("owner_id")
    @Schema(description = "项目负责人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerId;

    /**
     * 应用环境
     */
    @NotBlank(message = "应用环境不能为空")
    @Column(name = "environment", nullable = false, length = 20)
    @TableField("environment")
    @Schema(description = "应用环境：production-生产环境，test-测试环境，development-开发环境", requiredMode = Schema.RequiredMode.REQUIRED)
    private String environment;

    /**
     * 项目状态
     */
    @Column(name = "status", length = 20)
    @TableField("status")
    @Schema(description = "项目状态：active-运行中，inactive-已停止，maintenance-维护中")
    private String status = "active";

    /**
     * 项目图标
     */
    @Size(max = 100, message = "项目图标长度不能超过100个字符")
    @Column(name = "icon", length = 100)
    @TableField("icon")
    @Schema(description = "项目图标")
    private String icon;

    /**
     * 图标颜色
     */
    @Size(max = 200, message = "图标颜色长度不能超过200个字符")
    @Column(name = "icon_color", length = 200)
    @TableField("icon_color")
    @Schema(description = "图标颜色")
    private String iconColor;

    // 非数据库字段 - 关联信息
    @TableField(exist = false)
    @Transient
    @Schema(description = "项目负责人信息")
    private Object owner;

    @TableField(exist = false)
    @Transient
    @Schema(description = "团队成员列表")
    private List<ProjectTeamMember> teamMembers;

    @TableField(exist = false)
    @Transient
    @Schema(description = "智能体数量")
    private Integer agentCount;

    @TableField(exist = false)
    @Transient
    @Schema(description = "知识库数量")
    private Integer knowledgeCount;

    @TableField(exist = false)
    @Transient
    @Schema(description = "知识图谱数量")
    private Integer graphCount;

    @TableField(exist = false)
    @Transient
    @Schema(description = "运行中的智能体数量")
    private Integer agentRunningCount;

    @TableField(exist = false)
    @Transient
    @Schema(description = "已构建的知识库数量")
    private Integer knowledgeBuiltCount;

    @TableField(exist = false)
    @Transient
    @Schema(description = "知识图谱关系数量")
    private Integer graphRelationCount;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconColor() {
        return iconColor;
    }

    public void setIconColor(String iconColor) {
        this.iconColor = iconColor;
    }

    public Object getOwner() {
        return owner;
    }

    public void setOwner(Object owner) {
        this.owner = owner;
    }

    public List<ProjectTeamMember> getTeamMembers() {
        return teamMembers;
    }

    public void setTeamMembers(List<ProjectTeamMember> teamMembers) {
        this.teamMembers = teamMembers;
    }

    public Integer getAgentCount() {
        return agentCount;
    }

    public void setAgentCount(Integer agentCount) {
        this.agentCount = agentCount;
    }

    public Integer getKnowledgeCount() {
        return knowledgeCount;
    }

    public void setKnowledgeCount(Integer knowledgeCount) {
        this.knowledgeCount = knowledgeCount;
    }

    public Integer getGraphCount() {
        return graphCount;
    }

    public void setGraphCount(Integer graphCount) {
        this.graphCount = graphCount;
    }

    public Integer getAgentRunningCount() {
        return agentRunningCount;
    }

    public void setAgentRunningCount(Integer agentRunningCount) {
        this.agentRunningCount = agentRunningCount;
    }

    public Integer getKnowledgeBuiltCount() {
        return knowledgeBuiltCount;
    }

    public void setKnowledgeBuiltCount(Integer knowledgeBuiltCount) {
        this.knowledgeBuiltCount = knowledgeBuiltCount;
    }

    public Integer getGraphRelationCount() {
        return graphRelationCount;
    }

    public void setGraphRelationCount(Integer graphRelationCount) {
        this.graphRelationCount = graphRelationCount;
    }
}
