package com.xhcai.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 第三方智能体创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "第三方智能体创建请求")
public class ThirdPartyAgentCreateDTO {

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "客服助手", required = true)
    @NotBlank(message = "智能体名称不能为空")
    @Size(min = 1, max = 100, message = "智能体名称长度必须在1-100个字符之间")
    private String name;


    /**
     * 智能体模式
     */
    @Schema(description = "智能体模式", example = "advanced-chat", allowableValues = {"chat", "workflow", "completion", "advanced-chat", "agent-chat"})
    @NotBlank(message = "智能体模式不能为空")
    @Pattern(regexp = "^(chat|workflow|completion|advanced-chat|agent-chat)$", message = "智能体模式必须为chat、workflow、completion、advanced-chat或agent-chat")
    private String mode;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述", example = "专业的客服助手，能够回答用户问题")
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    private String description = "";

    /**
     * 智能体头像URL
     */
    @Schema(description = "智能体头像图标", example = "fas fa-sitemap")
    @Size(max = 255, message = "图标不能为空")
    private String avatar;

    /**
     * 智能体图标背景颜色
     */
    @Schema(description = "智能体图标背景颜色", example = "linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)")
    @Size(max = 100, message = "图标背景颜色长度不能超过100个字符")
    private String iconBackground;

    /**
     * 平台ID
     */
    @Schema(description = "平台ID", example = "platform-001", required = true)
    @NotBlank(message = "平台ID不能为空")
    private String platformId;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAvatar() {return avatar;}

    public void setAvatar(String avatar) {this.avatar = avatar;}

    public String getIconBackground() {return iconBackground;}

    public void setIconBackground(String iconBackground) {this.iconBackground = iconBackground;}

    public String getPlatformId() {return platformId;}

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        return "ThirdPartyAgentCreateDTO{" +
                "name='" + name + '\'' +
                ", mode='" + mode + '\'' +
                ", description='" + description + '\'' +
                ", avatar='" + avatar + '\'' +
                ", iconBackground='" + iconBackground + '\'' +
                ", platformId='" + platformId + '\'' +
                '}';
    }
}
