package com.xhcai.modules.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysConfigDTO;
import com.xhcai.modules.system.dto.SysConfigQueryDTO;
import com.xhcai.modules.system.entity.SysConfig;
import com.xhcai.modules.system.vo.SysConfigVO;

/**
 * 系统配置服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysConfigService extends IService<SysConfig> {

    /**
     * 分页查询系统配置列表
     *
     * @param queryDTO 查询条件
     * @return 配置分页列表
     */
    PageResult<SysConfigVO> selectConfigPage(SysConfigQueryDTO queryDTO);

    /**
     * 查询系统配置列表
     *
     * @param queryDTO 查询条件
     * @return 配置列表
     */
    List<SysConfigVO> selectConfigList(SysConfigQueryDTO queryDTO);

    /**
     * 根据配置ID查询配置信息
     *
     * @param configId 配置ID
     * @return 配置信息
     */
    SysConfigVO selectConfigById(String configId);

    /**
     * 根据配置键查询配置信息
     *
     * @param configKey 配置键
     * @return 配置信息
     */
    SysConfig selectByConfigKey(String configKey);

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值（带默认值）
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 创建系统配置
     *
     * @param config 配置信息
     * @return 是否成功
     */
    boolean insertConfig(SysConfig config);

    /**
     * 更新系统配置
     *
     * @param config 配置信息
     * @return 是否成功
     */
    boolean updateConfig(SysConfig config);

    /**
     * 删除系统配置
     *
     * @param configIds 配置ID列表
     * @return 是否成功
     */
    boolean deleteConfigs(List<String> configIds);

    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param excludeId 排除的配置ID
     * @return 是否存在
     */
    boolean existsConfigKey(String configKey, String excludeId);

    /**
     * 获取配置分组列表
     *
     * @return 分组列表
     */
    List<String> selectConfigGroups();

    /**
     * 批量更新或创建配置
     *
     * @param configDTOs 配置DTO列表
     * @return 是否成功
     */
    boolean batchUpdateConfigs(List<SysConfigDTO> configDTOs);

    /**
     * 批量更新配置状态
     *
     * @param configIds 配置ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> configIds, String status);

    /**
     * 刷新配置缓存
     */
    void refreshCache();

    /**
     * 初始化系统配置
     */
    void initSystemConfigs();

    /**
     * 导出配置数据
     *
     * @param queryDTO 查询条件
     * @return 配置列表
     */
    List<SysConfigVO> exportConfigs(SysConfigQueryDTO queryDTO);

    /**
     * 导入配置数据
     *
     * @param configList 配置列表
     * @return 导入结果
     */
    String importConfigs(List<SysConfig> configList);
}
