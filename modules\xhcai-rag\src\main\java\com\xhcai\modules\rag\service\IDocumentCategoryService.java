package com.xhcai.modules.rag.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.rag.dto.DocumentCategoryCreateDTO;
import com.xhcai.modules.rag.dto.DocumentCategoryUpdateDTO;
import com.xhcai.modules.rag.entity.DocumentCategory;
import com.xhcai.modules.rag.vo.DocumentCategoryVO;

/**
 * 文档分类服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDocumentCategoryService extends IService<DocumentCategory> {

    /**
     * 获取分类树
     */
    List<DocumentCategoryVO> getCategoryTree();

    /**
     * 获取指定知识库的分类树
     */
    List<DocumentCategoryVO> getCategoryTree(String datasetId);

    /**
     * 根据父分类ID获取子分类
     */
    List<DocumentCategoryVO> getCategoriesByParentId(String parentId);

    /**
     * 根据父分类ID和知识库ID获取子分类
     */
    List<DocumentCategoryVO> getCategoriesByParentId(String parentId, String datasetId);

    /**
     * 创建分类
     */
    DocumentCategoryVO createCategory(DocumentCategoryCreateDTO createDTO);

    /**
     * 更新分类
     */
    DocumentCategoryVO updateCategory(String categoryId, DocumentCategoryUpdateDTO updateDTO);

    /**
     * 删除分类
     */
    void deleteCategory(String categoryId);

    /**
     * 获取分类详情
     */
    DocumentCategoryVO getCategoryById(String categoryId);

    /**
     * 更新分类文件数量
     */
    void updateCategoryFileCount(String categoryId, Integer fileCount);

    /**
     * 检查分类名称是否存在
     */
    boolean existsCategoryName(String name, String parentId, String excludeId);

    /**
     * 检查分类名称是否存在（指定知识库）
     */
    boolean existsCategoryName(String name, String parentId, String excludeId, String datasetId);

    /**
     * 获取分类及其所有子分类ID
     */
    List<String> getCategoryAndChildrenIds(String categoryId);
}
