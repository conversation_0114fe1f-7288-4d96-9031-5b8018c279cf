<template>
  <!-- 共享克隆模态框 -->
  <div class="share-modal" v-show="visible" @click="handleClose">
    <div class="share-modal-content" @click.stop>
      <div class="share-modal-header">
        <div class="header-left">
          <div class="agent-info" v-if="agent">
            <div class="agent-avatar">
              <i :class="agent.icon"></i>
            </div>
            <div class="agent-details">
              <h3>共享克隆</h3>
              <span class="agent-name">{{ agent.name }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="handleClose">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="share-modal-body">
        <div class="share-form">
          <div class="form-group">
            <label>选择共享用户</label>
            <p class="form-desc">选择要共享此智能体克隆的用户</p>

            <div class="selected-users-share">
              <div v-for="user in selectedUsers" :key="user.id" class="selected-user-share">
                <i class="fas fa-user"></i>
                <div class="user-info">
                  <span class="user-name">{{ user.name }}</span>
                  <span class="user-details">{{ user.unit }} - {{ user.role }}</span>
                </div>
                <button class="remove-user-share" @click="removeUser(user.id)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <div class="user-search-share">
              <div class="search-input">
                <i class="fas fa-search"></i>
                <input
                  type="text"
                  v-model="userSearchQuery"
                  placeholder="搜索用户..."
                  @input="searchUsers"
                >
              </div>
              <div class="search-results" v-if="filteredUsers.length > 0">
                <div
                  v-for="user in filteredUsers"
                  :key="user.id"
                  class="search-result-item"
                  @click="addUser(user)"
                >
                  <i class="fas fa-user"></i>
                  <div class="user-info">
                    <span class="user-name">{{ user.name }}</span>
                    <span class="user-details">{{ user.unit }} - {{ user.role }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="share-modal-footer">
        <div class="footer-left">
          <span class="share-summary" v-if="selectedUsers.length > 0">
            将与 <strong>{{ selectedUsers.length }}</strong> 位用户共享
          </span>
        </div>
        <div class="footer-right">
          <button class="btn-modern btn-secondary" @click="handleClose">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button class="btn-modern btn-primary" @click="confirmShare" :disabled="selectedUsers.length === 0">
            <i class="fas fa-share-alt"></i>
            确认共享
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 定义用户类型
interface User {
  id: number
  name: string
  unit: string
  role: string
  email: string
}

// 定义智能体类型
interface Agent {
  id: string
  name: string
  description: string
  icon: string
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
}

// Props
interface Props {
  visible: boolean
  agent: Agent | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  agent: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'share': [data: { agent: Agent, users: User[] }]
}>()

// 响应式数据
const selectedUsers = ref<User[]>([])
const userSearchQuery = ref('')
const filteredUsers = ref<User[]>([])

// 模拟用户数据
const allUsers = ref<User[]>([
  // 技术部
  { id: 1, name: '张三', unit: '技术部', role: '技术总监', email: '<EMAIL>' },
  { id: 5, name: '孙七', unit: '技术部', role: '高级工程师', email: '<EMAIL>' },
  { id: 8, name: '刘十', unit: '技术部', role: '前端工程师', email: '<EMAIL>' },
  { id: 15, name: '马强', unit: '技术部', role: '后端工程师', email: '<EMAIL>' },
  { id: 16, name: '林雪', unit: '技术部', role: '测试工程师', email: '<EMAIL>' },

  // 产品部
  { id: 2, name: '李四', unit: '产品部', role: '产品经理', email: '<EMAIL>' },
  { id: 6, name: '周八', unit: '产品部', role: '产品总监', email: '<EMAIL>' },
  { id: 9, name: '黄十一', unit: '产品部', role: '产品专员', email: '<EMAIL>' },
  { id: 17, name: '王丽', unit: '产品部', role: '用户体验师', email: '<EMAIL>' },
  { id: 18, name: '陈浩', unit: '产品部', role: '产品分析师', email: '<EMAIL>' },

  // 研发中心
  { id: 3, name: '王五', unit: '研发中心', role: '研发总监', email: '<EMAIL>' },
  { id: 10, name: '吴十二', unit: '研发中心', role: '架构师', email: '<EMAIL>' },
  { id: 19, name: '李明', unit: '研发中心', role: '算法工程师', email: '<EMAIL>' },
  { id: 20, name: '张伟', unit: '研发中心', role: 'AI工程师', email: '<EMAIL>' },
  { id: 21, name: '刘芳', unit: '研发中心', role: '数据科学家', email: '<EMAIL>' },

  // 运营部
  { id: 4, name: '赵六', unit: '运营部', role: '运营总监', email: '<EMAIL>' },
  { id: 7, name: '陈九', unit: '运营部', role: '运营专员', email: '<EMAIL>' },
  { id: 22, name: '杨洋', unit: '运营部', role: '内容运营', email: '<EMAIL>' },
  { id: 23, name: '徐静', unit: '运营部', role: '用户运营', email: '<EMAIL>' },
  { id: 24, name: '郑华', unit: '运营部', role: '活动策划', email: '<EMAIL>' },

  // 市场部
  { id: 25, name: '孙磊', unit: '市场部', role: '市场总监', email: '<EMAIL>' },
  { id: 26, name: '周杰', unit: '市场部', role: '市场专员', email: '<EMAIL>' },
  { id: 27, name: '吴琳', unit: '市场部', role: '品牌经理', email: '<EMAIL>' },
  { id: 28, name: '王芳', unit: '市场部', role: '推广专员', email: '<EMAIL>' },
  { id: 29, name: '李涛', unit: '市场部', role: '渠道经理', email: '<EMAIL>' },

  // 客服部
  { id: 30, name: '张敏', unit: '客服部', role: '客服主管', email: '<EMAIL>' },
  { id: 31, name: '刘强', unit: '客服部', role: '客服专员', email: '<EMAIL>' },
  { id: 32, name: '陈静', unit: '客服部', role: '售后专员', email: '<EMAIL>' },
  { id: 33, name: '赵敏', unit: '客服部', role: '客服培训师', email: '<EMAIL>' },

  // 财务部
  { id: 34, name: '王丽华', unit: '财务部', role: '财务总监', email: '<EMAIL>' },
  { id: 35, name: '李财', unit: '财务部', role: '会计师', email: '<EMAIL>' },
  { id: 36, name: '张会计', unit: '财务部', role: '出纳', email: '<EMAIL>' },
  { id: 37, name: '刘审计', unit: '财务部', role: '审计专员', email: '<EMAIL>' },

  // 人事部
  { id: 38, name: '人事王', unit: '人事部', role: '人事总监', email: '<EMAIL>' },
  { id: 39, name: '招聘李', unit: '人事部', role: '招聘专员', email: '<EMAIL>' },
  { id: 40, name: '培训张', unit: '人事部', role: '培训专员', email: '<EMAIL>' },
  { id: 41, name: '薪酬刘', unit: '人事部', role: '薪酬专员', email: '<EMAIL>' },

  // 设计部
  { id: 42, name: '设计师小美', unit: '设计部', role: '设计总监', email: '<EMAIL>' },
  { id: 43, name: 'UI小王', unit: '设计部', role: 'UI设计师', email: '<EMAIL>' },
  { id: 44, name: '视觉小李', unit: '设计部', role: '视觉设计师', email: '<EMAIL>' },
  { id: 45, name: '交互小张', unit: '设计部', role: '交互设计师', email: '<EMAIL>' },

  // 销售部
  { id: 46, name: '销售王总', unit: '销售部', role: '销售总监', email: '<EMAIL>' },
  { id: 47, name: '业务小李', unit: '销售部', role: '销售经理', email: '<EMAIL>' },
  { id: 48, name: '客户小张', unit: '销售部', role: '客户经理', email: '<EMAIL>' },
  { id: 49, name: '渠道小刘', unit: '销售部', role: '渠道专员', email: '<EMAIL>' },

  // 法务部
  { id: 50, name: '法务顾问', unit: '法务部', role: '法务总监', email: '<EMAIL>' },
  { id: 51, name: '合同专员', unit: '法务部', role: '合同专员', email: '<EMAIL>' },
  { id: 52, name: '知产专员', unit: '法务部', role: '知识产权专员', email: '<EMAIL>' }
])

// 方法
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  selectedUsers.value = []
  userSearchQuery.value = ''
  filteredUsers.value = []
}

const searchUsers = () => {
  const query = userSearchQuery.value.toLowerCase().trim()
  if (!query) {
    filteredUsers.value = []
    return
  }

  filteredUsers.value = allUsers.value.filter(user =>
    !selectedUsers.value.find(selected => selected.id === user.id) &&
    (user.name.toLowerCase().includes(query) ||
     user.unit.toLowerCase().includes(query) ||
     user.role.toLowerCase().includes(query) ||
     user.email.toLowerCase().includes(query))
  ).slice(0, 10) // 限制显示10个结果
}

const addUser = (user: User) => {
  if (!selectedUsers.value.find(selected => selected.id === user.id)) {
    selectedUsers.value.push(user)
  }
  userSearchQuery.value = ''
  filteredUsers.value = []
}

const removeUser = (userId: number) => {
  selectedUsers.value = selectedUsers.value.filter(user => user.id !== userId)
}

const confirmShare = () => {
  if (!props.agent || selectedUsers.value.length === 0) return

  emit('share', {
    agent: props.agent,
    users: selectedUsers.value
  })
  
  handleClose()
}

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped>
/* 共享模态框样式 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.share-modal-content {
  background: #fefefe;
  border-radius: 20px;
  width: 100%;
  max-width: 700px;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(147, 197, 253, 0.3);
  animation: slideUp 0.4s ease;
  display: flex;
  flex-direction: column;
}

.share-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.agent-details h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.agent-name {
  font-size: 12px;
  color: #4b5563;
  font-weight: normal;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.share-modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.share-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-desc {
  font-size: 13px;
  color: #4b5563;
  margin: 0 0 16px 0;
}

.selected-users-share {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 36px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px dashed #bae6fd;
  border-radius: 8px;
  margin-bottom: 16px;
  align-items: center;
}

.selected-users-share:empty::before {
  content: "暂未选择任何用户";
  color: #6b7280;
  font-size: 13px;
  font-style: italic;
}

.selected-user-share {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  animation: slideIn 0.3s ease;
}

.remove-user-share {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 8px;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 2px;
}

.remove-user-share:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.user-search-share {
  position: relative;
}

.search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input i {
  position: absolute;
  left: 16px;
  color: #60a5fa;
  font-size: 14px;
  z-index: 1;
}

.search-input input {
  width: 100%;
  padding: 12px 14px 12px 40px;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 13px;
  transition: all 0.3s ease;
  background: #fefefe;
}

.search-input input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-results {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: #fefefe;
  border: 1px solid #bae6fd;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.1);
  z-index: 10;
  margin-bottom: 4px;
}

.search-result-item {
  padding: 10px 14px;
  cursor: pointer;
  font-size: 13px;
  color: #374151;
  border-bottom: 1px solid #f0f9ff;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-result-item:hover {
  background: #f0f9ff;
  color: #111827;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item i {
  color: #60a5fa;
  font-size: 11px;
}

/* 用户信息显示样式 */
.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #1a365d;
  font-size: 13px;
  line-height: 1.2;
}

.user-details {
  font-size: 11px;
  color: #64748b;
  line-height: 1.2;
}

.selected-user-share .user-info {
  margin-right: auto;
}

.selected-user-share .user-name,
.selected-user-share .user-details {
  color: #ffffff;
}

.share-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #dbeafe;
  background: #f8fafc;
}

.share-summary {
  font-size: 13px;
  color: #4b5563;
}

.share-summary strong {
  color: #111827;
  font-weight: 600;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #e2e8f0;
  color: #334155;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: 1px solid #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  background: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
