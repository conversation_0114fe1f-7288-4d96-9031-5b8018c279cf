/**
 * 用户第三方智能体账号管理相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'
import type { ThirdPlatformVO } from '@/api/agents'

// 用户第三方智能体账号查询参数
export interface ThirdPlatformAccountQueryDTO {
  current?: number
  size?: number
  platformId?: string
  accountName?: string
  status?: number
  lastTestResult?: number
  beginTime?: string
  endTime?: string
}

// 用户第三方智能体账号创建参数
export interface ThirdPlatformAccountCreateDTO {
  platformId: string
  accountName: string
  apiKey?: string
  pwd: string
  remark?: string
  status: number
}

// 用户第三方智能体账号更新参数
export interface ThirdPlatformAccountUpdateDTO {
  accountName: string
  apiKey?: string
  pwd?: string
  remark?: string
  status: number
}

// 用户第三方智能体账号VO
export interface ThirdPlatformAccountVO {
  id: string
  userId: string
  platformId: string
  platformName: string
  platformIcon: string
  platformIconBg: string
  platformDescription: string
  accountName: string
  apiKey?: string
  hasPwd: boolean
  remark?: string
  status: number
  statusText: string
  lastTestTime?: string
  lastTestResult?: number
  lastTestResultText?: string
  lastTestError?: string
  totalCalls: number
  successCalls: number
  successRate: number
  avgResponseTime: number
  lastUsedTime?: string
  createTime: string
  updateTime: string
  platformInfo: ThirdPlatformVO
}

// 密码验证参数
export interface PasswordVerifyDTO {
  password: string
}

/**
 * 用户第三方智能体账号API类
 */
export class ThirdPlatformAccountAPI {
  /**
   * 分页查询用户第三方智能体账号
   */
  static async getAccountPage(params: ThirdPlatformAccountQueryDTO): Promise<ApiResponse<ThirdPlatformAccountVO>> {
    return apiClient.get('/api/third-platform/account/page', { params })
  }

  /**
   * 查询用户第三方智能体账号详情
   */
  static async getAccountById(id: string): Promise<ApiResponse<ThirdPlatformAccountVO>> {
    return apiClient.get(`/api/third-platform/account/${id}`)
  }

  /**
   * 创建用户第三方智能体账号
   */
  static async createAccount(data: ThirdPlatformAccountCreateDTO): Promise<ApiResponse<string>> {
    return apiClient.post('/api/third-platform/account', data)
  }

  /**
   * 更新用户第三方智能体账号
   */
  static async updateAccount(id: string, data: ThirdPlatformAccountUpdateDTO): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/third-platform/account/${id}`, data)
  }

  /**
   * 删除用户第三方智能体账号
   */
  static async deleteAccount(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/third-platform/account/${id}`)
  }

  /**
   * 测试连接
   */
  static async testConnection(id: string): Promise<ApiResponse<string>> {
    return apiClient.post(`/api/third-platform/account/${id}/test`)
  }

  /**
   * 查询当前用户的所有第三方账号
   */
  static async getAccountList(): Promise<ApiResponse<ThirdPlatformAccountVO[]>> {
    return apiClient.get('/api/third-platform/account/list')
  }

  /**
   * 检查平台账号
   */
  static async checkPlatformAccount(platformId: string): Promise<ApiResponse<boolean>> {
    return apiClient.get(`/api/third-platform/account/check/${platformId}`)
  }

  /**
   * 验证密码
   */
  static async verifyPassword(id: string, data: PasswordVerifyDTO): Promise<ApiResponse<boolean>> {
    return apiClient.post(`/api/third-platform/account/${id}/verify-password`, data)
  }
}

// 导出便捷函数
export const getThirdPlatformAccountPage = ThirdPlatformAccountAPI.getAccountPage
export const getThirdPlatformAccountById = ThirdPlatformAccountAPI.getAccountById
export const createThirdPlatformAccount = ThirdPlatformAccountAPI.createAccount
export const updateThirdPlatformAccount = ThirdPlatformAccountAPI.updateAccount
export const deleteThirdPlatformAccount = ThirdPlatformAccountAPI.deleteAccount
export const testThirdPlatformAccountConnection = ThirdPlatformAccountAPI.testConnection
export const getThirdPlatformAccountList = ThirdPlatformAccountAPI.getAccountList
export const checkThirdPlatformAccountPlatform = ThirdPlatformAccountAPI.checkPlatformAccount
export const verifyThirdPlatformAccountPassword = ThirdPlatformAccountAPI.verifyPassword

export default ThirdPlatformAccountAPI
