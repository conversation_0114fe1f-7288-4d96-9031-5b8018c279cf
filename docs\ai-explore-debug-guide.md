# AI探索页面调试指南

## 问题描述
AI探索页面选择智能体标签页时没有调用`/api/agent/ai-explore`接口。

## 调试步骤

### 1. 检查前端页面
1. 打开浏览器开发者工具（F12）
2. 访问AI探索页面
3. 查看Console面板的日志输出

### 2. 预期的日志输出
当页面加载时，应该看到：
```
ModelAgentSelector mounted
allCategories: [...]
activeTab: models
```

当点击AI探索智能体标签页时，应该看到：
```
标签页点击: ai-explore
activeTab changed to: ai-explore
切换到AI探索智能体标签页，开始加载数据
shouldTriggerLoad: { currentCategoryId: 'ai-explore', dynamicAgentsLength: 0, agentsLoading: false, result: true }
loadAiExploreAgentsIfNeeded called { dynamicAgentsLength: 0, agentsLoading: false }
调用 loadAiExploreAgents
loadAiExploreAgents 开始执行
开始调用 /api/agent/ai-explore 接口
```

### 3. 检查Network面板
在开发者工具的Network面板中，应该能看到对`/api/agent/ai-explore`的请求。

### 4. 可能的问题和解决方案

#### 问题1：AI探索智能体标签页不显示
**原因**：`allCategories`计算属性可能有问题
**解决方案**：检查`dynamicAgentCategory`是否正确创建

#### 问题2：点击标签页没有触发watch
**原因**：点击事件没有正确设置`activeTab`
**解决方案**：检查`handleTabClick`函数是否正确执行

#### 问题3：watch触发但没有调用API
**原因**：`loadAiExploreAgentsIfNeeded`条件判断有问题
**解决方案**：检查`dynamicAgents.value.length`和`agentsLoading.value`的值

#### 问题4：API调用失败
**原因**：后端服务未启动或接口路径错误
**解决方案**：检查后端服务状态和接口路径

### 5. 手动测试步骤

1. **启动后端服务**
   ```bash
   cd admin-api
   mvn spring-boot:run
   ```

2. **启动前端服务**
   ```bash
   cd web
   npm run dev
   ```

3. **访问页面**
   - 打开浏览器访问 `http://localhost:5173`
   - 导航到AI探索页面

4. **测试标签页切换**
   - 点击"AI探索智能体"标签页
   - 观察Console日志和Network请求

### 6. 验证数据库
确保数据库中有type为'advanced-chat'的智能体记录：

```sql
SELECT id, name, type, external_agent_id as appId, description 
FROM agent 
WHERE type = 'advanced-chat' 
  AND deleted = 0 
  AND status = '1'
ORDER BY sort_order ASC, create_time DESC;
```

### 7. 临时解决方案
如果问题仍然存在，可以尝试以下临时解决方案：

1. **强制调用API**
   在浏览器Console中执行：
   ```javascript
   // 获取modelStore实例
   const { loadAiExploreAgents } = useModelStore()
   // 手动调用API
   loadAiExploreAgents()
   ```

2. **检查API响应**
   直接访问API接口：
   ```
   GET http://localhost:8080/api/agent/ai-explore
   ```

### 8. 常见错误排查

#### 错误1：404 Not Found
- 检查后端服务是否启动
- 检查接口路径是否正确
- 检查Controller是否正确映射

#### 错误2：403 Forbidden
- 检查是否需要认证
- 检查权限配置

#### 错误3：500 Internal Server Error
- 检查后端日志
- 检查数据库连接
- 检查SQL查询是否正确

### 9. 调试完成后清理
测试完成后，记得移除调试日志：
- 删除Console.log语句
- 恢复原始的简洁代码

## 联系支持
如果问题仍然存在，请提供：
1. 浏览器Console的完整日志
2. Network面板的请求详情
3. 后端服务日志
4. 数据库查询结果
