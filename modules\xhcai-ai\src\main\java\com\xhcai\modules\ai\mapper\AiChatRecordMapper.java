package com.xhcai.modules.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.ai.dto.AiChatQueryDTO;
import com.xhcai.modules.ai.entity.AiChatRecord;
import com.xhcai.modules.ai.vo.AiChatRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI聊天记录Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AiChatRecordMapper extends BaseMapper<AiChatRecord> {

    /**
     * 分页查询聊天记录
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @return 聊天记录分页列表
     */
    IPage<AiChatRecordVO> selectChatRecordPage(Page<AiChatRecordVO> page, @Param("query") AiChatQueryDTO queryDTO);

    /**
     * 根据会话ID和用户ID获取聊天历史记录
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 聊天记录列表
     */
    List<AiChatRecordVO> selectChatHistory(@Param("sessionId") String sessionId, @Param("userId") String userId);

    /**
     * 获取用户的会话列表
     *
     * @param userId 用户ID
     * @return 会话ID列表
     */
    @Select("SELECT DISTINCT session_id FROM ai_chat_record WHERE user_id = #{userId} AND deleted = 0 ORDER BY create_time DESC")
    List<String> selectUserSessions(@Param("userId") String userId);

    /**
     * 获取聊天统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select({
        "<script>",
        "SELECT ",
        "  COUNT(*) as totalChats,",
        "  COALESCE(SUM(tokens_used), 0) as totalTokens,",
        "  COUNT(DISTINCT session_id) as totalSessions,",
        "  COALESCE(AVG(cost_time), 0) as avgResponseTime",
        "FROM ai_chat_record",
        "WHERE deleted = 0",
        "<if test='userId != null'>",
        "  AND user_id = #{userId}",
        "</if>",
        "</script>"
    })
    ChatStatisticsResult selectChatStatistics(@Param("userId") String userId);

    /**
     * 聊天统计结果类
     */
    class ChatStatisticsResult {
        private Long totalChats;
        private Long totalTokens;
        private Long totalSessions;
        private Double avgResponseTime;

        public Long getTotalChats() {
            return totalChats;
        }

        public void setTotalChats(Long totalChats) {
            this.totalChats = totalChats;
        }

        public Long getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(Long totalTokens) {
            this.totalTokens = totalTokens;
        }

        public Long getTotalSessions() {
            return totalSessions;
        }

        public void setTotalSessions(Long totalSessions) {
            this.totalSessions = totalSessions;
        }

        public Double getAvgResponseTime() {
            return avgResponseTime;
        }

        public void setAvgResponseTime(Double avgResponseTime) {
            this.avgResponseTime = avgResponseTime;
        }
    }
}
