package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.dify.dto.knowledge.DifyDocumentDTO;
import com.xhcai.modules.dify.dto.knowledge.DifyKnowledgeBaseDTO;
import com.xhcai.modules.dify.service.IDifyKnowledgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 知识库控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Tag(name = "知识库管理", description = "Dify知识库服务对接")
@RestController
@RequestMapping("/api/dify/knowledge")
public class DifyKnowledgeController {

    @Autowired
    private IDifyKnowledgeService knowledgeService;

    @Operation(summary = "创建知识库", description = "创建新的知识库")
    @PostMapping
    @RequiresPermissions("dify:knowledge:create")
    public Mono<Result<DifyKnowledgeBaseDTO>> createKnowledgeBase(@Valid @RequestBody DifyKnowledgeBaseDTO difyKnowledgeBaseDTO) {
        return knowledgeService.createKnowledgeBase(difyKnowledgeBaseDTO);
    }

    @Operation(summary = "更新知识库", description = "更新知识库信息")
    @PutMapping("/{knowledgeId}")
    @RequiresPermissions("dify:knowledge:update")
    public Mono<Result<DifyKnowledgeBaseDTO>> updateKnowledgeBase(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Valid @RequestBody DifyKnowledgeBaseDTO difyKnowledgeBaseDTO) {
        return knowledgeService.updateKnowledgeBase(knowledgeId, difyKnowledgeBaseDTO);
    }

    @Operation(summary = "删除知识库", description = "删除指定的知识库")
    @DeleteMapping("/{knowledgeId}")
    @RequiresPermissions("dify:knowledge:delete")
    public Mono<Result<Object>> deleteKnowledgeBase(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId) {
        return knowledgeService.deleteKnowledgeBase(knowledgeId);
    }

    @Operation(summary = "获取知识库详情", description = "根据ID获取知识库详细信息")
    @GetMapping("/{knowledgeId}")
    @RequiresPermissions("dify:knowledge:view")
    public Mono<Result<DifyKnowledgeBaseDTO>> getKnowledgeBase(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId) {
        return knowledgeService.getKnowledgeBase(knowledgeId);
    }

    @Operation(summary = "获取知识库列表", description = "分页查询知识库列表")
    @GetMapping
    @RequiresPermissions("dify:knowledge:list")
    public Mono<Result<PageResult<DifyKnowledgeBaseDTO>>> getKnowledgeBaseList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态") @RequestParam(required = false) String status) {
        return knowledgeService.getKnowledgeBaseList(page, size, keyword, status);
    }

    @Operation(summary = "更新文档", description = "更新文档信息")
    @PutMapping("/{knowledgeId}/documents/{documentId}")
    @RequiresPermissions("dify:knowledge:document:update")
    public Mono<Result<DifyDocumentDTO>> updateDocument(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Valid @RequestBody DifyDocumentDTO difyDocumentDTO) {
        return knowledgeService.updateDocument(knowledgeId, documentId, difyDocumentDTO);
    }

    @Operation(summary = "删除文档", description = "删除指定的文档")
    @DeleteMapping("/{knowledgeId}/documents/{documentId}")
    @RequiresPermissions("dify:knowledge:document:delete")
    public Mono<Result<Object>> deleteDocument(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "文档ID") @PathVariable String documentId) {
        return knowledgeService.deleteDocument(knowledgeId, documentId);
    }

    @Operation(summary = "获取文档详情", description = "根据ID获取文档详细信息")
    @GetMapping("/{knowledgeId}/documents/{documentId}")
    @RequiresPermissions("dify:knowledge:document:view")
    public Mono<Result<DifyDocumentDTO>> getDocument(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "文档ID") @PathVariable String documentId) {
        return knowledgeService.getDocument(knowledgeId, documentId);
    }

    @Operation(summary = "获取文档列表", description = "分页查询文档列表")
    @GetMapping("/{knowledgeId}/documents")
    @RequiresPermissions("dify:knowledge:document:list")
    public Mono<Result<PageResult<DifyDocumentDTO>>> getDocumentList(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态") @RequestParam(required = false) String status) {
        return knowledgeService.getDocumentList(knowledgeId, page, size, keyword, status);
    }

    @Operation(summary = "重新处理文档", description = "重新处理指定的文档")
    @PostMapping("/{knowledgeId}/documents/{documentId}/reprocess")
    @RequiresPermissions("dify:knowledge:document:update")
    public Mono<Result<Object>> reprocessDocument(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "文档ID") @PathVariable String documentId) {
        return knowledgeService.reprocessDocument(knowledgeId, documentId);
    }

    @Operation(summary = "获取文档处理状态", description = "获取文档的处理状态")
    @GetMapping("/{knowledgeId}/documents/{documentId}/status")
    @RequiresPermissions("dify:knowledge:document:view")
    public Mono<Result<Object>> getDocumentProcessStatus(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "文档ID") @PathVariable String documentId) {
        return knowledgeService.getDocumentProcessStatus(knowledgeId, documentId);
    }

    @Operation(summary = "知识库检索", description = "在知识库中检索相关内容")
    @PostMapping("/{knowledgeId}/search")
    @RequiresPermissions("dify:knowledge:search")
    public Mono<Result<List<Object>>> searchKnowledge(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "查询内容") @RequestParam String query,
            @Parameter(description = "返回数量") @RequestParam(required = false) Integer topK,
            @Parameter(description = "分数阈值") @RequestParam(required = false) Double scoreThreshold) {
        return knowledgeService.searchKnowledge(knowledgeId, query, topK, scoreThreshold);
    }

    @Operation(summary = "批量删除文档", description = "批量删除多个文档")
    @DeleteMapping("/{knowledgeId}/documents/batch")
    @RequiresPermissions("dify:knowledge:document:delete")
    public Mono<Result<Object>> batchDeleteDocuments(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "文档ID列表") @RequestBody List<String> documentIds) {
        return knowledgeService.batchDeleteDocuments(knowledgeId, documentIds);
    }

    @Operation(summary = "获取知识库统计信息", description = "获取知识库的统计信息")
    @GetMapping("/{knowledgeId}/stats")
    @RequiresPermissions("dify:knowledge:view")
    public Mono<Result<Object>> getKnowledgeStats(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId) {
        return knowledgeService.getKnowledgeStats(knowledgeId);
    }
}
