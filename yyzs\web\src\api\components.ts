import apiClient, { ApiResponse } from './client';
import {
  ElasticComponent,
  ComponentInstallRequest,
  ComponentUploadRequest,
  ComponentStatistics,
  BatchOperationResult,
  ComponentType,
  ComponentStatus,
} from '@/types/component';

/**
 * 组件管理API
 */
export class ComponentsAPI {
  /**
   * 上传组件安装包
   */
  static async uploadPackage(request: ComponentUploadRequest): Promise<ApiResponse<{ componentId: string }>> {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('componentType', request.componentType);
    formData.append('version', request.version);
    if (request.description) {
      formData.append('description', request.description);
    }

    return apiClient.upload('/api/components/upload', formData);
  }

  /**
   * 安装组件
   */
  static async installComponent(componentId: string, config?: Record<string, any>): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/components/${componentId}/install`, config);
  }

  /**
   * 卸载组件
   */
  static async uninstallComponent(componentId: string, options?: {
    removeConfig?: boolean;
    removeData?: boolean;
    removeLogs?: boolean;
    backup?: boolean;
    backupConfig?: any;
  }): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/components/${componentId}/uninstall`, options);
  }

  /**
   * 启动组件
   */
  static async startComponent(componentId: string): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/components/${componentId}/start`);
  }

  /**
   * 停止组件
   */
  static async stopComponent(componentId: string): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/components/${componentId}/stop`);
  }

  /**
   * 重启组件
   */
  static async restartComponent(componentId: string): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/components/${componentId}/restart`);
  }

  /**
   * 获取组件列表
   */
  static async getComponents(params?: {
    type?: ComponentType;
    status?: ComponentStatus;
  }): Promise<ApiResponse<ElasticComponent[]>> {
    const queryParams = new URLSearchParams();
    if (params?.type) queryParams.append('type', params.type);
    if (params?.status) queryParams.append('status', params.status);
    
    const url = `/api/components${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get(url);
  }

  /**
   * 获取组件详情
   */
  static async getComponent(componentId: string): Promise<ApiResponse<ElasticComponent>> {
    return apiClient.get(`/api/components/${componentId}`);
  }

  /**
   * 检查组件状态
   */
  static async checkComponentStatus(componentId: string): Promise<ApiResponse<{
    componentId: string;
    status: string;
    statusDescription: string;
  }>> {
    return apiClient.get(`/api/components/${componentId}/status`);
  }

  /**
   * 批量启动组件
   */
  static async batchStartComponents(componentIds: string[]): Promise<ApiResponse<BatchOperationResult>> {
    return apiClient.post('/api/components/batch/start', componentIds);
  }

  /**
   * 批量停止组件
   */
  static async batchStopComponents(componentIds: string[]): Promise<ApiResponse<BatchOperationResult>> {
    return apiClient.post('/api/components/batch/stop', componentIds);
  }

  /**
   * 获取组件统计信息
   */
  static async getComponentStatistics(): Promise<ApiResponse<ComponentStatistics>> {
    return apiClient.get('/api/components/statistics');
  }

  /**
   * 更新组件配置
   */
  static async updateComponentConfig(
    componentId: string,
    config: Record<string, any>
  ): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/components/${componentId}/config`, config);
  }

  /**
   * 获取组件配置
   */
  static async getComponentConfig(componentId: string): Promise<ApiResponse<Record<string, any>>> {
    return apiClient.get(`/api/components/${componentId}/config`);
  }

  /**
   * 获取组件日志
   */
  static async getComponentLogs(
    componentId: string,
    lines: number = 100
  ): Promise<ApiResponse<{
    componentId: string;
    lines: number;
    logs: string[];
  }>> {
    return apiClient.get(`/api/components/${componentId}/logs?lines=${lines}`);
  }

  /**
   * 清理组件日志
   */
  static async clearComponentLogs(componentId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/components/${componentId}/logs`);
  }

  /**
   * 备份组件配置
   */
  static async backupComponentConfig(componentId: string): Promise<ApiResponse<{ backupPath: string }>> {
    return apiClient.post(`/api/components/${componentId}/config/backup`);
  }

  /**
   * 恢复组件配置
   */
  static async restoreComponentConfig(
    componentId: string,
    backupPath: string
  ): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/components/${componentId}/config/restore`, { backupPath });
  }

  /**
   * 验证组件配置
   */
  static async validateComponentConfig(
    componentType: ComponentType,
    config: Record<string, any>
  ): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return apiClient.post('/api/components/config/validate', {
      componentType,
      config,
    });
  }

  /**
   * 获取可用端口
   */
  static async getAvailablePort(startPort: number, endPort: number): Promise<ApiResponse<{ port: number }>> {
    return apiClient.get(`/api/components/ports/available?startPort=${startPort}&endPort=${endPort}`);
  }

  /**
   * 检查端口是否被占用
   */
  static async checkPortInUse(port: number): Promise<ApiResponse<{ inUse: boolean }>> {
    return apiClient.get(`/api/components/ports/${port}/check`);
  }

  /**
   * 同步组件状态
   */
  static async syncComponentStatus(): Promise<ApiResponse<void>> {
    return apiClient.post('/api/components/sync-status');
  }

  /**
   * 自动启动组件
   */
  static async autoStartComponents(): Promise<ApiResponse<void>> {
    return apiClient.post('/api/components/auto-start');
  }
}

export default ComponentsAPI;
