package com.xhcai.modules.agent.service.impl;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.xhcai.modules.agent.vo.HistoryStatsVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.agent.dto.AgentWorkflowHistoryCreateDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowHistoryQueryDTO;
import com.xhcai.modules.agent.entity.AgentWorkflow;
import com.xhcai.modules.agent.entity.AgentWorkflowHistory;
import com.xhcai.modules.agent.mapper.AgentWorkflowHistoryMapper;
import com.xhcai.modules.agent.service.IAgentWorkflowHistoryService;
import com.xhcai.modules.agent.vo.AgentWorkflowHistoryVO;

/**
 * 智能体工作流历史记录服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AgentWorkflowHistoryServiceImpl extends ServiceImpl<AgentWorkflowHistoryMapper, AgentWorkflowHistory>
        implements IAgentWorkflowHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(AgentWorkflowHistoryServiceImpl.class);

    @Autowired
    private AgentWorkflowHistoryMapper historyMapper;

    @Override
    public IPage<AgentWorkflowHistoryVO> getHistoryPage(AgentWorkflowHistoryQueryDTO queryDTO) {
        Page<AgentWorkflowHistoryVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return historyMapper.selectHistoryPage(page, queryDTO);
    }

    @Override
    public List<AgentWorkflowHistoryVO> getHistoryByWorkflowId(String workflowId) {
        if (!StringUtils.hasText(workflowId)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.selectHistoryByWorkflowId(workflowId, tenantId);
    }

    @Override
    public List<AgentWorkflowHistoryVO> getHistoryByAgentId(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            throw new BusinessException("智能体ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.selectHistoryByAgentId(agentId, tenantId);
    }

    @Override
    public AgentWorkflowHistoryVO getHistoryByConfigHash(String configHash) {
        if (!StringUtils.hasText(configHash)) {
            throw new BusinessException("配置哈希值不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.selectHistoryByConfigHash(configHash, tenantId);
    }

    @Override
    public List<AgentWorkflowHistoryVO> getRecentHistory(String workflowId, Integer limit) {
        if (!StringUtils.hasText(workflowId)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        Integer queryLimit = (limit != null && limit > 0) ? limit : 10;
        return historyMapper.selectRecentHistory(workflowId, tenantId, queryLimit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveHistory(AgentWorkflowHistoryCreateDTO createDTO) {
        if (createDTO == null) {
            throw new BusinessException("创建信息不能为空");
        }

        // 计算配置哈希值
        String configHash = calculateConfigHash(
                null, // 不再使用workflowConfig
                createDTO.getNodesData(),
                createDTO.getEdgesData(),
                createDTO.getGlobalVariables()
        );

        // 检查配置是否已存在
        if (isConfigExists(configHash)) {
            logger.debug("配置未变更，跳过历史记录保存，哈希值: {}", configHash);
            return null;
        }

        // 创建历史记录实体
        AgentWorkflowHistory history = new AgentWorkflowHistory();
        BeanUtils.copyProperties(createDTO, history);
        history.setId(UUID.randomUUID().toString());
        history.setConfigHash(configHash);

        // 计算配置大小
        long configSize = calculateConfigSize(
                null, // 不再使用workflowConfig
                createDTO.getNodesData(),
                createDTO.getEdgesData(),
                createDTO.getGlobalVariables()
        );
        history.setConfigSize(configSize);

        // 设置基础信息
        history.setTenantId(SecurityUtils.getCurrentTenantId());
        history.setCreateBy(SecurityUtils.getCurrentUserId());
        history.setCreateTime(LocalDateTime.now());

        // 保存到数据库
        if (!save(history)) {
            throw new BusinessException("保存工作流历史记录失败");
        }

        logger.info("保存工作流历史记录成功，ID: {}, 工作流ID: {}, 哈希值: {}",
                history.getId(), history.getWorkflowId(), configHash);
        return history.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveHistoryFromWorkflow(AgentWorkflow workflow, String operationType,
            String operationDesc, String changeSummary, Boolean isMajorChange) {
        if (workflow == null) {
            throw new BusinessException("工作流实体不能为空");
        }

        // 计算配置哈希值
        String configHash = calculateConfigHash(workflow);

        // 检查配置是否已存在
        if (isConfigExists(configHash)) {
            logger.debug("配置未变更，跳过历史记录保存，工作流ID: {}, 哈希值: {}", workflow.getId(), configHash);
            return null;
        }

        // 创建历史记录DTO
        AgentWorkflowHistoryCreateDTO createDTO = new AgentWorkflowHistoryCreateDTO();
        createDTO.setWorkflowId(workflow.getId());
        createDTO.setAgentId(workflow.getAgentId());
        createDTO.setName(workflow.getName());
        createDTO.setDescription(workflow.getDescription());
        createDTO.setVersion(workflow.getVersion());
        createDTO.setNodesData(workflow.getNodesData());
        createDTO.setEdgesData(workflow.getEdgesData());
        createDTO.setGlobalVariables(workflow.getGlobalVariables());
        createDTO.setOperationType(operationType);
        createDTO.setOperationDesc(operationDesc);
        createDTO.setChangeSummary(changeSummary);
        createDTO.setOperationTime(LocalDateTime.now());
        createDTO.setOperationUserId(SecurityUtils.getCurrentUserId());
        createDTO.setOperationUserName(SecurityUtils.getCurrentUsername());
        createDTO.setIsMajorChange(isMajorChange);

        return saveHistory(createDTO);
    }

    @Override
    public String calculateConfigHash(AgentWorkflow workflow) {
        if (workflow == null) {
            return "";
        }

        return calculateConfigHash(
                null, // 不再使用workflowConfig
                workflow.getNodesData(),
                workflow.getEdgesData(),
                workflow.getGlobalVariables()
        );
    }

    @Override
    public String calculateConfigHash(String workflowConfig, String nodesData,
            String edgesData, String globalVariables) {
        try {
            // 构建配置内容字符串
            StringBuilder configContent = new StringBuilder();
            configContent.append(normalizeJson(nodesData));
            configContent.append("|");
            configContent.append(normalizeJson(edgesData));
            configContent.append("|");
            configContent.append(normalizeJson(globalVariables));

            // 计算SHA-256哈希值
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(configContent.toString().getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            logger.error("计算配置哈希值失败", e);
            throw new BusinessException("计算配置哈希值失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isConfigExists(String configHash) {
        if (!StringUtils.hasText(configHash)) {
            return false;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        LambdaQueryWrapper<AgentWorkflowHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentWorkflowHistory::getConfigHash, configHash)
                .eq(AgentWorkflowHistory::getTenantId, tenantId)
                .eq(AgentWorkflowHistory::getDeleted, 0);

        return count(queryWrapper) > 0;
    }

    @Override
    public Long countHistoryByWorkflowId(String workflowId) {
        if (!StringUtils.hasText(workflowId)) {
            return 0L;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.countHistoryByWorkflowId(workflowId, tenantId);
    }

    @Override
    public Long countHistoryByAgentId(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            return 0L;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.countHistoryByAgentId(agentId, tenantId);
    }

    @Override
    public Long countHistoryByTimeRange(String workflowId, LocalDateTime startTime, LocalDateTime endTime) {
        if (!StringUtils.hasText(workflowId)) {
            return 0L;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.countHistoryByTimeRange(workflowId, tenantId, startTime, endTime);
    }

    @Override
    public List<AgentWorkflowHistoryVO> getMajorChanges(String workflowId) {
        if (!StringUtils.hasText(workflowId)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.selectMajorChanges(workflowId, tenantId);
    }

    @Override
    public List<AgentWorkflowHistoryVO> getHistoryByUser(String workflowId, String userId) {
        if (!StringUtils.hasText(workflowId)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.selectHistoryByUser(workflowId, tenantId, userId);
    }

    @Override
    public List<AgentWorkflowHistoryVO> getHistoryByOperationType(String workflowId, String operationType) {
        if (!StringUtils.hasText(workflowId)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.selectHistoryByOperationType(workflowId, tenantId, operationType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteHistoryBeforeTime(LocalDateTime beforeTime) {
        if (beforeTime == null) {
            throw new BusinessException("时间点不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        int deletedCount = historyMapper.deleteHistoryBeforeTime(beforeTime, tenantId);

        logger.info("删除历史记录完成，删除时间点: {}, 删除数量: {}", beforeTime, deletedCount);
        return deletedCount;
    }

    @Override
    public HistoryStatsVO getHistoryStats(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            throw new BusinessException("智能体ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return historyMapper.selectHistoryStats(agentId, tenantId);
    }

    @Override
    public HistoryCompareVO compareHistory(String fromHistoryId, String toHistoryId) {
        if (!StringUtils.hasText(fromHistoryId) || !StringUtils.hasText(toHistoryId)) {
            throw new BusinessException("历史记录ID不能为空");
        }

        // 查询两个历史记录
        AgentWorkflowHistory fromHistory = getById(fromHistoryId);
        AgentWorkflowHistory toHistory = getById(toHistoryId);

        if (fromHistory == null || toHistory == null) {
            throw new BusinessException("历史记录不存在");
        }

        // 检查租户权限
        String tenantId = SecurityUtils.getCurrentTenantId();
        if (!fromHistory.getTenantId().equals(tenantId) || !toHistory.getTenantId().equals(tenantId)) {
            throw new BusinessException("无权限访问此历史记录");
        }

        // 转换为VO
        AgentWorkflowHistoryVO fromVO = convertToVO(fromHistory);
        AgentWorkflowHistoryVO toVO = convertToVO(toHistory);

        // 生成差异列表
        List<String> differences = generateDifferences(fromHistory, toHistory);

        // 生成摘要
        String summary = generateCompareSummary(differences);

        // 构建对比结果
        HistoryCompareVO compareVO = new HistoryCompareVO();
        compareVO.setFromHistory(fromVO);
        compareVO.setToHistory(toVO);
        compareVO.setDifferences(differences);
        compareVO.setSummary(summary);

        return compareVO;
    }

    @Override
    public String generateChangeSummary(AgentWorkflow oldWorkflow, AgentWorkflow newWorkflow) {
        if (oldWorkflow == null && newWorkflow == null) {
            return "无变更";
        }

        if (oldWorkflow == null) {
            return "新建工作流";
        }

        if (newWorkflow == null) {
            return "删除工作流";
        }

        List<String> changes = new ArrayList<>();

        // 检查名称变更
        if (!objectEquals(oldWorkflow.getName(), newWorkflow.getName())) {
            changes.add("修改名称");
        }

        // 检查描述变更
        if (!objectEquals(oldWorkflow.getDescription(), newWorkflow.getDescription())) {
            changes.add("修改描述");
        }

        // 检查节点配置变更
        if (!objectEquals(oldWorkflow.getNodesData(), newWorkflow.getNodesData())) {
            changes.add("修改节点配置");
        }

        // 检查边配置变更
        if (!objectEquals(oldWorkflow.getEdgesData(), newWorkflow.getEdgesData())) {
            changes.add("修改边配置");
        }

        // 检查全局变量变更
        if (!objectEquals(oldWorkflow.getGlobalVariables(), newWorkflow.getGlobalVariables())) {
            changes.add("修改全局变量");
        }

        // 工作流配置检查已移除，因为不再使用workflowConfig字段
        if (changes.isEmpty()) {
            return "无实质性变更";
        }

        return String.join("，", changes);
    }

    @Override
    public boolean isMajorChange(AgentWorkflow oldWorkflow, AgentWorkflow newWorkflow) {
        if (oldWorkflow == null || newWorkflow == null) {
            return true; // 新建或删除都是重要变更
        }

        // 检查节点数量变化
        int oldNodeCount = countNodes(oldWorkflow.getNodesData());
        int newNodeCount = countNodes(newWorkflow.getNodesData());
        if (Math.abs(oldNodeCount - newNodeCount) > 0) {
            return true; // 节点数量变化是重要变更
        }

        // 检查边数量变化
        int oldEdgeCount = countConnections(oldWorkflow.getEdgesData());
        int newEdgeCount = countConnections(newWorkflow.getEdgesData());
        if (Math.abs(oldEdgeCount - newEdgeCount) > 0) {
            return true; // 边数量变化是重要变更
        }

        // 工作流结构变化检查已移除，因为不再使用workflowConfig字段
        return false; // 其他变更不是重要变更
    }

    /**
     * 规范化JSON字符串
     */
    private String normalizeJson(String jsonStr) {
        if (!StringUtils.hasText(jsonStr)) {
            return "";
        }

        try {
            // 解析并重新序列化，确保格式一致
            Object obj = JSON.parse(jsonStr);
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            // 如果解析失败，返回原字符串
            return jsonStr.trim();
        }
    }

    /**
     * 计算配置大小
     */
    private long calculateConfigSize(String workflowConfig, String nodesConfig,
            String connectionsConfig, String globalVariables) {
        long size = 0;
        if (StringUtils.hasText(workflowConfig)) {
            size += workflowConfig.getBytes(StandardCharsets.UTF_8).length;
        }
        if (StringUtils.hasText(nodesConfig)) {
            size += nodesConfig.getBytes(StandardCharsets.UTF_8).length;
        }
        if (StringUtils.hasText(connectionsConfig)) {
            size += connectionsConfig.getBytes(StandardCharsets.UTF_8).length;
        }
        if (StringUtils.hasText(globalVariables)) {
            size += globalVariables.getBytes(StandardCharsets.UTF_8).length;
        }
        return size;
    }

    /**
     * 转换实体为VO
     */
    private AgentWorkflowHistoryVO convertToVO(AgentWorkflowHistory history) {
        AgentWorkflowHistoryVO vo = new AgentWorkflowHistoryVO();
        BeanUtils.copyProperties(history, vo);
        return vo;
    }

    /**
     * 生成差异列表
     */
    private List<String> generateDifferences(AgentWorkflowHistory fromHistory, AgentWorkflowHistory toHistory) {
        List<String> differences = new ArrayList<>();

        // 比较名称
        if (!objectEquals(fromHistory.getName(), toHistory.getName())) {
            differences.add(String.format("名称: %s → %s", fromHistory.getName(), toHistory.getName()));
        }

        // 比较描述
        if (!objectEquals(fromHistory.getDescription(), toHistory.getDescription())) {
            differences.add(String.format("描述: %s → %s", fromHistory.getDescription(), toHistory.getDescription()));
        }

        // 比较版本
        if (!objectEquals(fromHistory.getVersion(), toHistory.getVersion())) {
            differences.add(String.format("版本: %s → %s", fromHistory.getVersion(), toHistory.getVersion()));
        }

        // 比较配置
        if (!objectEquals(fromHistory.getNodesData(), toHistory.getNodesData())) {
            differences.add("节点配置已变更");
        }

        if (!objectEquals(fromHistory.getEdgesData(), toHistory.getEdgesData())) {
            differences.add("边配置已变更");
        }

        if (!objectEquals(fromHistory.getGlobalVariables(), toHistory.getGlobalVariables())) {
            differences.add("全局变量已变更");
        }

        return differences;
    }

    /**
     * 生成对比摘要
     */
    private String generateCompareSummary(List<String> differences) {
        if (differences.isEmpty()) {
            return "无差异";
        }

        if (differences.size() == 1) {
            return differences.get(0);
        }

        return String.format("共%d项变更: %s", differences.size(), String.join(", ", differences));
    }

    /**
     * 统计节点数量
     */
    private int countNodes(String nodesConfig) {
        if (!StringUtils.hasText(nodesConfig)) {
            return 0;
        }

        try {
            JSONArray nodes = JSON.parseArray(nodesConfig);
            return nodes != null ? nodes.size() : 0;
        } catch (Exception e) {
            logger.warn("解析节点配置失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 统计连接数量
     */
    private int countConnections(String connectionsConfig) {
        if (!StringUtils.hasText(connectionsConfig)) {
            return 0;
        }

        try {
            JSONArray connections = JSON.parseArray(connectionsConfig);
            return connections != null ? connections.size() : 0;
        } catch (Exception e) {
            logger.warn("解析连接配置失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 安全的对象比较
     */
    private boolean objectEquals(Object obj1, Object obj2) {
        if (obj1 == null && obj2 == null) {
            return true;
        }
        if (obj1 == null || obj2 == null) {
            return false;
        }
        return obj1.equals(obj2);
    }
}
