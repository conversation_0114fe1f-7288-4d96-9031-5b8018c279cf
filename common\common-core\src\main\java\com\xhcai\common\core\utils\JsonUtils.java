package com.xhcai.common.core.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.TypeReference;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class JsonUtils {

    /**
     * 对象转JSON字符串
     *
     * @param object 对象
     * @return JSON字符串
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        return JSON.toJSONString(object);
    }

    /**
     * JSON字符串转对象
     *
     * @param jsonString JSON字符串
     * @param clazz      目标类型
     * @param <T>        泛型
     * @return 对象
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (!StringUtils.hasText(jsonString)) {
            return null;
        }
        return JSON.parseObject(jsonString, clazz);
    }

    /**
     * JSON字符串转对象（支持泛型）
     *
     * @param jsonString    JSON字符串
     * @param typeReference 类型引用
     * @param <T>           泛型
     * @return 对象
     */
    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (!StringUtils.hasText(jsonString)) {
            return null;
        }
        return JSON.parseObject(jsonString, typeReference);
    }

    /**
     * JSON字符串转List
     *
     * @param jsonString JSON字符串
     * @param clazz      元素类型
     * @param <T>        泛型
     * @return List对象
     */
    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (!StringUtils.hasText(jsonString)) {
            return null;
        }
        return JSON.parseArray(jsonString, clazz);
    }

    /**
     * JSON字符串转JSONObject
     *
     * @param jsonString JSON字符串
     * @return JSONObject
     */
    public static JSONObject parseObject(String jsonString) {
        if (!StringUtils.hasText(jsonString)) {
            return null;
        }
        return JSON.parseObject(jsonString);
    }

    /**
     * JSON字符串转JSONArray
     *
     * @param jsonString JSON字符串
     * @return JSONArray
     */
    public static JSONArray parseArray(String jsonString) {
        if (!StringUtils.hasText(jsonString)) {
            return null;
        }
        return JSON.parseArray(jsonString);
    }

    /**
     * 对象转Map
     *
     * @param object 对象
     * @return Map
     */
    public static Map<String, Object> toMap(Object object) {
        if (object == null) {
            return null;
        }
        String jsonString = toJsonString(object);
        return parseObject(jsonString, new TypeReference<Map<String, Object>>() {});
    }

    /**
     * Map转对象
     *
     * @param map   Map
     * @param clazz 目标类型
     * @param <T>   泛型
     * @return 对象
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        String jsonString = toJsonString(map);
        return parseObject(jsonString, clazz);
    }

    /**
     * 判断字符串是否为有效的JSON
     *
     * @param jsonString JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonString) {
        if (!StringUtils.hasText(jsonString)) {
            return false;
        }
        try {
            JSON.parse(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为有效的JSON对象
     *
     * @param jsonString JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJsonObject(String jsonString) {
        if (!StringUtils.hasText(jsonString)) {
            return false;
        }
        try {
            Object obj = JSON.parse(jsonString);
            return obj instanceof JSONObject;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为有效的JSON数组
     *
     * @param jsonString JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJsonArray(String jsonString) {
        if (!StringUtils.hasText(jsonString)) {
            return false;
        }
        try {
            Object obj = JSON.parse(jsonString);
            return obj instanceof JSONArray;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 格式化JSON字符串（美化输出）
     *
     * @param jsonString JSON字符串
     * @return 格式化后的JSON字符串
     */
    public static String formatJson(String jsonString) {
        if (!StringUtils.hasText(jsonString)) {
            return jsonString;
        }
        try {
            Object obj = JSON.parse(jsonString);
            return JSON.toJSONString(obj, JSONWriter.Feature.PrettyFormat);
        } catch (Exception e) {
            return jsonString;
        }
    }

    /**
     * 压缩JSON字符串（去除空格和换行）
     *
     * @param jsonString JSON字符串
     * @return 压缩后的JSON字符串
     */
    public static String compactJson(String jsonString) {
        if (!StringUtils.hasText(jsonString)) {
            return jsonString;
        }
        try {
            Object obj = JSON.parse(jsonString);
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            return jsonString;
        }
    }
}
