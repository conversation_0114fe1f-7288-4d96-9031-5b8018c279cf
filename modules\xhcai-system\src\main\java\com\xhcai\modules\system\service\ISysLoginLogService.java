package com.xhcai.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.system.entity.SysLoginLog;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 用户登录日志服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysLoginLogService extends IService<SysLoginLog> {

    /**
     * 记录登录日志
     *
     * @param username 用户名
     * @param status 登录状态：0-成功，1-失败
     * @param message 提示消息
     * @param request HTTP请求对象
     */
    void recordLoginLog(String username, String status, String message, HttpServletRequest request);

    /**
     * 记录登录成功日志
     *
     * @param username 用户名
     * @param request HTTP请求对象
     */
    void recordLoginSuccess(String username, HttpServletRequest request);

    /**
     * 记录登录失败日志
     *
     * @param username 用户名
     * @param message 失败原因
     * @param request HTTP请求对象
     */
    void recordLoginFailure(String username, String message, HttpServletRequest request);

    /**
     * 记录登出日志
     *
     * @param username 用户名
     * @param sessionId 会话ID
     */
    void recordLogout(String username, String sessionId);

    /**
     * 清理过期的登录日志
     *
     * @param days 保留天数
     */
    void cleanExpiredLogs(int days);

    /**
     * 分页查询登录日志
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    com.xhcai.common.api.response.PageResult<com.xhcai.modules.system.vo.SysLoginLogVO> selectLoginLogPage(com.xhcai.modules.system.dto.SysLoginLogQueryDTO queryDTO);

    /**
     * 根据ID查询登录日志详情
     *
     * @param id 登录日志ID
     * @return 登录日志详情
     */
    com.xhcai.modules.system.vo.SysLoginLogVO selectLoginLogById(String id);

    /**
     * 根据ID删除登录日志
     *
     * @param id 登录日志ID
     */
    void deleteLoginLogById(String id);

    /**
     * 批量删除登录日志
     *
     * @param ids 登录日志ID数组
     */
    void deleteLoginLogByIds(String[] ids);

    /**
     * 获取登录统计信息
     *
     * @return 统计信息
     */
    Object getLoginStats();
}
