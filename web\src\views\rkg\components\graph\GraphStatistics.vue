<template>
  <div class="graph-statistics">
    <!-- 基本统计 -->
    <div class="grid grid-cols-2 gap-3 mb-4">
      <div class="text-center p-3 bg-blue-50 rounded-lg">
        <div class="text-lg font-semibold text-blue-600">{{ data.nodeCount }}</div>
        <div class="text-xs text-blue-500">节点总数</div>
      </div>
      <div class="text-center p-3 bg-green-50 rounded-lg">
        <div class="text-lg font-semibold text-green-600">{{ data.edgeCount }}</div>
        <div class="text-xs text-green-500">关系总数</div>
      </div>
    </div>

    <!-- 图谱密度 -->
    <div class="mb-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">图谱密度</span>
        <span class="text-sm text-gray-600">{{ density.toFixed(2) }}</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-purple-500 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${density * 100}%` }"
        ></div>
      </div>
    </div>

    <!-- 连通性 -->
    <div class="mb-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">连通性</span>
        <span class="text-sm text-gray-600">{{ connectivity }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-orange-500 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${connectivity}%` }"
        ></div>
      </div>
    </div>

    <!-- 平均度数 -->
    <div class="mb-4">
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-gray-700">平均度数</span>
        <span class="text-sm text-gray-600">{{ averageDegree.toFixed(1) }}</span>
      </div>
    </div>

    <!-- 聚类系数 -->
    <div class="mb-4">
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-gray-700">聚类系数</span>
        <span class="text-sm text-gray-600">{{ clusteringCoefficient.toFixed(3) }}</span>
      </div>
    </div>

    <!-- 图谱质量评分 -->
    <div class="p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-gray-700">质量评分</span>
        <span class="text-lg font-bold text-purple-600">{{ qualityScore }}/100</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${qualityScore}%` }"
        ></div>
      </div>
      <p class="text-xs text-gray-600 mt-2">{{ getQualityDescription(qualityScore) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  data: {
    nodeCount: number
    edgeCount: number
    description?: string
  }
}

const props = defineProps<Props>()

// 计算属性
const density = computed(() => {
  // 图谱密度 = 实际边数 / 最大可能边数
  const maxEdges = (props.data.nodeCount * (props.data.nodeCount - 1)) / 2
  return maxEdges > 0 ? props.data.edgeCount / maxEdges : 0
})

const connectivity = computed(() => {
  // 模拟连通性计算
  return Math.min(95, Math.max(60, 70 + Math.random() * 25))
})

const averageDegree = computed(() => {
  // 平均度数 = 2 * 边数 / 节点数
  return props.data.nodeCount > 0 ? (2 * props.data.edgeCount) / props.data.nodeCount : 0
})

const clusteringCoefficient = computed(() => {
  // 模拟聚类系数
  return Math.random() * 0.5 + 0.2
})

const qualityScore = computed(() => {
  // 综合质量评分
  const densityScore = Math.min(100, density.value * 200) * 0.3
  const connectivityScore = connectivity.value * 0.3
  const clusteringScore = clusteringCoefficient.value * 100 * 0.2
  const sizeScore = Math.min(100, (props.data.nodeCount / 100) * 100) * 0.2
  
  return Math.round(densityScore + connectivityScore + clusteringScore + sizeScore)
})

// 工具函数
const getQualityDescription = (score: number): string => {
  if (score >= 90) return '优秀 - 图谱结构完整，关系丰富'
  if (score >= 80) return '良好 - 图谱结构较好，关系清晰'
  if (score >= 70) return '一般 - 图谱基本完整，可进一步优化'
  if (score >= 60) return '待改进 - 图谱结构较简单，建议增加关系'
  return '需要优化 - 图谱结构不完整，需要大量改进'
}
</script>
