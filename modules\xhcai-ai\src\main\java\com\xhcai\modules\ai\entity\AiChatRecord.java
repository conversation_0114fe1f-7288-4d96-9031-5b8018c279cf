package com.xhcai.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * AI对话记录实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "ai_chat_record")
@Schema(description = "AI对话记录")
@TableName("ai_chat_record")
public class AiChatRecord extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @Column(name = "session_id", length = 100, nullable = false)
    @Schema(description = "会话ID", example = "session_123456")
    @NotBlank(message = "会话ID不能为空")
    @Size(max = 100, message = "会话ID长度不能超过100个字符")
    @TableField("session_id")
    private String sessionId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", length = 36, nullable = false)
    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    @TableField("user_id")
    private String userId;

    /**
     * AI模型名称
     */
    @Column(name = "model_name", length = 50)
    @Schema(description = "AI模型名称", example = "gpt-3.5-turbo")
    @Size(max = 50, message = "AI模型名称长度不能超过50个字符")
    @TableField("model_name")
    private String modelName;

    /**
     * 用户消息
     */
    @Column(name = "user_message", nullable = false)
    @Schema(description = "用户消息")
    @NotBlank(message = "用户消息不能为空")
    @TableField("user_message")
    private String userMessage;

    /**
     * AI回复
     */
    @Column(name = "ai_response", nullable = false)
    @Schema(description = "AI回复")
    @TableField("ai_response")
    private String aiResponse;

    /**
     * 消息类型
     */
    @Column(name = "message_type", length = 10, nullable = false)
    @Schema(description = "消息类型", example = "text", allowableValues = {"text", "image", "file"})
    @Pattern(regexp = "^(text|image|file)$", message = "消息类型必须为text、image或file")
    @TableField("message_type")
    private String messageType;

    /**
     * 使用的token数量
     */
    @Column(name = "tokens_used")
    @Schema(description = "使用的token数量")
    @TableField("tokens_used")
    private Integer tokensUsed;

    /**
     * 响应时间（毫秒）
     */
    @Column(name = "cost_time")
    @Schema(description = "响应时间（毫秒）")
    @TableField("cost_time")
    private Long costTime;

    /**
     * 状态
     */
    @Column(name = "status", length = 1, nullable = false)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    /**
     * 错误信息
     */
    @Column(name = "error_msg", length = 500)
    @Schema(description = "错误信息")
    @Size(max = 500, message = "错误信息长度不能超过500个字符")
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 应用ID（Dify智能体ID）
     */
    @Column(name = "app_id", length = 100)
    @Schema(description = "应用ID", example = "12345678-1234-1234-1234-123456789abc")
    @Size(max = 100, message = "应用ID长度不能超过100个字符")
    @TableField("app_id")
    private String appId;

    /**
     * 输入参数（JSON格式）
     */
    @Column(name = "input", columnDefinition = "TEXT")
    @Schema(description = "输入参数", example = "{\"temperature\":0.7,\"max_tokens\":1000}")
    @TableField("input")
    private String input;

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getUserMessage() {
        return userMessage;
    }

    public void setUserMessage(String userMessage) {
        this.userMessage = userMessage;
    }

    public String getAiResponse() {
        return aiResponse;
    }

    public void setAiResponse(String aiResponse) {
        this.aiResponse = aiResponse;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Integer getTokensUsed() {
        return tokensUsed;
    }

    public void setTokensUsed(Integer tokensUsed) {
        this.tokensUsed = tokensUsed;
    }

    public Long getCostTime() {
        return costTime;
    }

    public void setCostTime(Long costTime) {
        this.costTime = costTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    @Override
    public String toString() {
        return "AiChatRecord{"
                + "sessionId='" + sessionId + '\''
                + ", userId='" + userId + '\''
                + ", modelName='" + modelName + '\''
                + ", userMessage='" + userMessage + '\''
                + ", aiResponse='" + aiResponse + '\''
                + ", messageType='" + messageType + '\''
                + ", tokensUsed=" + tokensUsed
                + ", costTime=" + costTime
                + ", status='" + status + '\''
                + ", errorMsg='" + errorMsg + '\''
                + ", appId='" + appId + '\''
                + ", input='" + input + '\''
                + "} " + super.toString();
    }
}
