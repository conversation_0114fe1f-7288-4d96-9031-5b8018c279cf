package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 向量数据库更新DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "向量数据库更新DTO")
public class VectorDatabaseUpdateDTO {

    @Schema(description = "数据库名称", example = "Elasticsearch集群")
    @Size(min = 1, max = 100, message = "数据库名称长度必须在1-100个字符之间")
    private String name;

    @Schema(description = "数据库类型", example = "elasticsearch")
    @Size(min = 1, max = 50, message = "数据库类型长度必须在1-50个字符之间")
    private String type;

    @Schema(description = "主机地址", example = "localhost")
    @Size(min = 1, max = 255, message = "主机地址长度必须在1-255个字符之间")
    private String host;

    @Schema(description = "端口", example = "9200")
    private Integer port;

    @Schema(description = "数据库名", example = "vector_db")
    @Size(max = 100, message = "数据库名长度不能超过100个字符")
    private String databaseName;

    @Schema(description = "用户名", example = "admin")
    @Size(max = 100, message = "用户名长度不能超过100个字符")
    private String username;

    @Schema(description = "密码")
    @Size(max = 255, message = "密码长度不能超过255个字符")
    private String password;

    @Schema(description = "连接配置", example = "{\"ssl\": true, \"timeout\": 30}")
    private String connectionConfig;

    @Schema(description = "索引配置", example = "{\"shards\": 1, \"replicas\": 0}")
    private String indexConfig;

    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    @Schema(description = "是否为默认数据库", example = "N", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "是否为默认数据库值必须为Y或N")
    private String isDefault;

    @Schema(description = "描述", example = "主要的Elasticsearch向量搜索集群")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    @Schema(description = "图标", example = "🔍")
    @Size(max = 10, message = "图标长度不能超过10个字符")
    private String icon;

    @Schema(description = "图标颜色", example = "#005571")
    @Size(max = 20, message = "图标颜色长度不能超过20个字符")
    private String iconColor;

    @Schema(description = "连接池配置", example = "{\"maxConnections\": 10, \"minConnections\": 1}")
    private String poolConfig;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
