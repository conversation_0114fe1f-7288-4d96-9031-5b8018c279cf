package com.xhcai.common.datasource.handler;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.function.Supplier;

import org.apache.ibatis.reflection.MetaObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.xhcai.common.datasource.utils.TenantUtils;

/**
 * MyBatis Plus 元数据处理器 智能自动填充审计字段，只填充entity中已定义的字段
 *
 * <p>
 * 支持@NoTenant注解，对于标记了@NoTenant的实体类，不会自动填充租户ID字段</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    private static final Logger log = LoggerFactory.getLogger(MyMetaObjectHandler.class);

    // 字段名常量
    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "updateTime";
    private static final String CREATE_BY = "createBy";
    private static final String UPDATE_BY = "updateBy";
    private static final String DELETED = "deleted";
    private static final String TENANT_ID = "tenantId";

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());

        // 智能填充创建时间
        fillFieldIfExists(metaObject, CREATE_TIME, LocalDateTime.class, LocalDateTime::now);

        // 智能填充更新时间
        fillFieldIfExists(metaObject, UPDATE_TIME, LocalDateTime.class, LocalDateTime::now);

        // 智能填充创建人和更新人
        String userId = getCurrentUserId();
        if (userId != null) {
            fillFieldIfExists(metaObject, CREATE_BY, String.class, () -> userId);
            fillFieldIfExists(metaObject, UPDATE_BY, String.class, () -> userId);
        }

        // 智能填充删除标记（只对通过Mybatis-plus内置方法才生效，如save）
        fillFieldIfExists(metaObject, DELETED, Integer.class, () -> 0);

        // 智能填充租户ID（检查实体是否需要租户字段）
        Class<?> entityClass = metaObject.getOriginalObject().getClass();
        log.debug("检查实体类 {} 是否需要租户字段", entityClass.getSimpleName());

        if (TenantUtils.needsTenantField(entityClass)) {
            log.debug("实体类 {} 需要租户字段，开始填充", entityClass.getSimpleName());
            String tenantId = getCurrentTenantId();
            log.debug("获取到的租户ID: {}", tenantId);

            if (tenantId != null) {
                log.debug("开始填充租户ID字段: {}", tenantId);
                fillFieldIfExists(metaObject, TENANT_ID, String.class, () -> tenantId);
            } else {
                log.debug("租户ID为null，跳过填充");
            }
        } else {
            log.debug("实体类 {} 标记了@NoTenant注解，跳过租户ID填充", entityClass.getSimpleName());
        }

        log.debug("插入填充完成");
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());

        // 智能填充更新时间
        updateFieldIfExists(metaObject, UPDATE_TIME, LocalDateTime.class, LocalDateTime::now);

        // 智能填充更新人
        String userId = getCurrentUserId();
        if (userId != null) {
            updateFieldIfExists(metaObject, UPDATE_BY, String.class, () -> userId);
        }

        log.debug("更新填充完成");
    }

    /**
     * 智能填充字段（插入时） 只有当entity中存在该字段且值为null时才填充
     *
     * @param metaObject 元对象
     * @param fieldName 字段名
     * @param fieldType 字段类型
     * @param valueSupplier 值提供者
     */
    private <T> void fillFieldIfExists(MetaObject metaObject, String fieldName, Class<T> fieldType, Supplier<T> valueSupplier) {
        log.debug("检查字段 {} 是否存在setter方法", fieldName);

        if (metaObject.hasSetter(fieldName)) {
            log.debug("字段 {} 存在setter方法，检查当前值", fieldName);
            Object fieldValue = getFieldValByName(fieldName, metaObject);
            log.debug("字段 {} 当前值: {}", fieldName, fieldValue);

            if (Objects.isNull(fieldValue)) {
                T value = valueSupplier.get();
                log.debug("准备填充字段 {} 的值: {}", fieldName, value);

                if (value != null) {
                    this.strictInsertFill(metaObject, fieldName, fieldType, value);
                    log.debug("成功填充字段 {} = {}", fieldName, value);
                } else {
                    log.debug("字段 {} 的值为null，跳过填充", fieldName);
                }
            } else {
                log.debug("字段 {} 已有值，跳过填充: {}", fieldName, fieldValue);
            }
        } else {
            log.debug("实体中不存在字段 {} 的setter方法，跳过填充", fieldName);
        }
    }

    /**
     * 智能更新字段（更新时） 只有当entity中存在该字段时才更新
     *
     * @param metaObject 元对象
     * @param fieldName 字段名
     * @param fieldType 字段类型
     * @param valueSupplier 值提供者
     */
    private <T> void updateFieldIfExists(MetaObject metaObject, String fieldName, Class<T> fieldType, Supplier<T> valueSupplier) {
        if (metaObject.hasSetter(fieldName)) {
            T value = valueSupplier.get();
            if (value != null) {
                this.strictUpdateFill(metaObject, fieldName, fieldType, value);
                log.debug("更新字段 {} = {}", fieldName, value);
            }
        } else {
            log.debug("实体中不存在字段 {}，跳过更新", fieldName);
        }
    }

    /**
     * 获取当前用户ID 优先从SecurityUtils获取，如果不存在则使用默认值
     */
    private String getCurrentUserId() {
        try {
            // 尝试通过SecurityUtils获取当前用户ID
            Class<?> securityUtilsClass = Class.forName("com.xhcai.common.security.utils.SecurityUtils");
            return (String) securityUtilsClass.getMethod("getCurrentUserId").invoke(null);
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            log.debug("SecurityUtils类不存在或方法不存在，使用默认用户ID");
        } catch (Exception e) {
            log.debug("获取当前用户ID失败，使用默认用户ID: {}", e.getMessage());
        }
        // 返回默认用户ID（系统用户）
        return "1";
    }

    /**
     * 获取当前租户ID 优先从SecurityUtils获取，如果不存在则使用默认值
     */
    private String getCurrentTenantId() {
        try {
            // 尝试通过SecurityUtils获取当前租户ID
            Class<?> securityUtilsClass = Class.forName("com.xhcai.common.security.utils.SecurityUtils");
            return (String) securityUtilsClass.getMethod("getCurrentTenantId").invoke(null);
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            log.debug("SecurityUtils类不存在或方法不存在，使用默认租户ID");
        } catch (Exception e) {
            log.debug("获取当前租户ID失败: {}", e.getMessage());
        }
        // 如果无法获取租户ID，返回null（表示不设置租户ID）
        return null;
    }
}
