package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

/**
 * 系统配置查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "系统配置查询DTO")
public class SysConfigQueryDTO extends PageQueryDTO {

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "系统名称")
    private String configName;

    /**
     * 配置键
     */
    @Schema(description = "配置键", example = "sys.system.name")
    private String configKey;

    /**
     * 配置类型
     */
    @Schema(description = "配置类型", example = "Y", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "配置类型必须为Y或N")
    private String configType;

    /**
     * 配置分组
     */
    @Schema(description = "配置分组", example = "系统配置")
    private String configGroup;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    /**
     * 是否系统内置
     */
    @Schema(description = "是否系统内置")
    private Boolean isSystem;

    // Getters and Setters
    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getConfigGroup() {
        return configGroup;
    }

    public void setConfigGroup(String configGroup) {
        this.configGroup = configGroup;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    @Override
    public String toString() {
        return "SysConfigQueryDTO{"
                + "configName='" + configName + '\''
                + ", configKey='" + configKey + '\''
                + ", configType='" + configType + '\''
                + ", configGroup='" + configGroup + '\''
                + ", status='" + status + '\''
                + ", isSystem=" + isSystem
                + ", current=" + getCurrent()
                + ", size=" + getSize()
                + ", orderBy='" + getOrderBy() + '\''
                + ", orderDirection='" + getOrderDirection() + '\''
                + '}';
    }
}
