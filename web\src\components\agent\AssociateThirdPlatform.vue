<template>
  <div class="associate-modal" v-show="visible" @click="closeModal">
    <div class="associate-content" @click.stop>
      <div class="associate-header">
        <h3>关联第三方智能体</h3>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="associate-body">
        <!-- 第三方智能体平台列表 -->
        <div class="platform-section">
          <h4>选择智能体平台</h4>
          <div v-if="platforms.length === 0" class="empty-platforms">
            <div class="empty-icon">🤖</div>
            <p class="empty-text">暂无第三方智能体平台</p>
            <p class="empty-hint">请联系管理员添加第三方智能体平台</p>
          </div>
          <div v-else class="platform-list">
            <div
              v-for="platform in platforms"
              :key="platform.id"
              :class="['platform-item', { active: selectedPlatform?.id === platform.id }]"
              @click="selectPlatform(platform)"
            >
              <div class="platform-icon" :style="{ backgroundColor: platform.iconBg }">
                {{ platform.icon }}
              </div>
              <div class="platform-info">
                <div class="platform-name">{{ platform.name }}</div>
                <div class="platform-description">{{ platform.description }}</div>
                <div class="platform-meta">
                  <span class="platform-unit">{{ platform.unitName }}</span>
                  <span class="platform-status" :class="platform.status === 1 ? 'active' : 'inactive'">
                    {{ platform.status === 1 ? '启用' : '禁用' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 智能体列表 -->
        <div class="agents-section" v-if="selectedPlatform">
          <h4>选择智能体 ({{ selectedPlatform.name }})</h4>
          <!-- 错误提示 -->
          <div v-if="agentListError" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <span>获取智能体列表失败，请检查是否账号配置错误或者网络连接异常</span>
          </div>
          <div v-if="platformAgents.length === 0 && !agentListError" class="empty-agents">
            <div class="empty-icon">🔍</div>
            <p class="empty-text">该平台暂无可用智能体</p>
          </div>
          <div v-else class="agents-list">
            <!-- 未关联的智能体 -->
            <template v-if="unassociatedAgents.length > 0">
              <div class="agents-group-header">
                <h4>可关联智能体 ({{ unassociatedAgents.length }})</h4>
              </div>
              <div
                v-for="agent in unassociatedAgents"
                :key="agent.id"
                :class="['agent-item', {
                  selected: selectedAgents.has(agent.id)
                }]"
                @click="toggleAgentSelection(agent)"
              >
              <div class="agent-checkbox">
                <i class="fas fa-check" v-if="selectedAgents.has(agent.id)"></i>
                <i class="fas fa-link" v-else-if="agent.isAssociated" title="已关联"></i>
              </div>
              <div class="agent-icon-container">
                <div class="agent-icon" :style="{ backgroundColor: agent.iconBg }">
                  <!-- 根据icon_type显示不同类型的图标 -->
                  <span v-if="agent.iconType === 'emoji'">{{ agent.icon }}</span>
                  <img v-else-if="agent.iconType === 'image' && agent.iconUrl"
                       :src="agent.iconUrl"
                       :alt="agent.name"
                       class="agent-icon-image" />
                  <span v-else>🤖</span>
                </div>
              </div>
                <div class="agent-info">
                  <div class="agent-name">{{ agent.name }}</div>
                  <div class="agent-type">{{ getModeDisplayName(agent.mode) }}</div>
                  <div class="agent-description">{{ agent.description }}</div>
                </div>
              </div>
            </template>

            <!-- 已关联的智能体 -->
            <template v-if="associatedAgents.length > 0">
              <div class="agents-group-header associated-header">
                <h4>已关联智能体 ({{ associatedAgents.length }})</h4>
                <span class="group-hint">这些智能体已经关联，无法重复关联</span>
              </div>
              <div
                v-for="agent in associatedAgents"
                :key="agent.id"
                :class="['agent-item', 'disabled']"
              >
                <div class="agent-checkbox">
                  <i class="fas fa-link" title="已关联"></i>
                </div>
                <div class="agent-icon-container">
                  <div class="agent-icon" :style="{ backgroundColor: agent.iconBg }">
                    <!-- 根据icon_type显示不同类型的图标 -->
                    <span v-if="agent.iconType === 'emoji'">{{ agent.icon }}</span>
                    <img v-else-if="agent.iconType === 'image' && agent.iconUrl"
                         :src="agent.iconUrl"
                         :alt="agent.name"
                         class="agent-icon-image" />
                    <span v-else>🤖</span>
                  </div>
                </div>
                <div class="agent-info">
                  <div class="agent-name">{{ agent.name }}</div>
                  <div class="agent-type">{{ getModeDisplayName(agent.mode) }}</div>
                  <div class="agent-description">{{ agent.description }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="associate-footer">
        <div class="footer-left">
          <span class="selected-count" v-if="selectedAgents.size > 0">
            已选择 {{ selectedAgents.size }} 个智能体
          </span>
        </div>
        <div class="footer-right">
          <button class="btn btn-secondary" @click="closeModal">取消</button>
          <button 
            class="btn btn-primary" 
            @click="confirmAssociation"
            :disabled="selectedAgents.size === 0"
          >
            确认关联
          </button>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ThirdPlatformAPI } from '@/api/agents'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'associate': [agents: any[]]
}>()

// 响应式数据
const platforms = ref<any[]>([])
const selectedPlatform = ref<any>(null)
const platformAgents = ref<any[]>([])
const selectedAgents = ref<Set<string>>(new Set())
const loading = ref(false)
const agentListError = ref(false)

// 计算属性
// 未关联的智能体
const unassociatedAgents = computed(() => {
  return platformAgents.value.filter(agent => !agent.isAssociated)
})

// 已关联的智能体
const associatedAgents = computed(() => {
  return platformAgents.value.filter(agent => agent.isAssociated)
})

const selectedAgentsList = computed(() => {
  return platformAgents.value.filter(agent => selectedAgents.value.has(agent.id))
})

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadPlatforms()
    resetSelection()
  }
})

// 监听选中平台变化
watch(selectedPlatform, (newPlatform) => {
  if (!newPlatform) {
    platformAgents.value = []
    agentListError.value = false
  }
  selectedAgents.value.clear()
})

// 方法
const loadPlatforms = async () => {
  try {
    loading.value = true
    // 调用新的第三方智能体列表接口
    const response = await ThirdPlatformAPI.getThirdPlatformList()

    if (response.success && response.data) {
      // 将第三方智能体作为平台显示
      platforms.value = response.data || []
      // 默认选中第一个平台并加载其智能体列表
      if (platforms.value.length > 0) {
        selectPlatform(platforms.value[0])
      }
    }
  } catch (error) {
    console.error('加载第三方智能体平台失败:', error)
    platforms.value = []
  } finally {
    loading.value = false
  }
}

const loadPlatformAgents = async (platformId: string) => {
  try {
    loading.value = true
    agentListError.value = false // 重置错误状态

    // 调用新的接口获取指定平台下的智能体列表
    const response = await ThirdPlatformAPI.getDifyAgentsList(platformId)

    if (response.success && response.data) {
      // 获取已关联的智能体列表
      const associatedAgentsResponse = await ThirdPlatformAPI.getAssociatedAgents(platformId)
      const associatedAgentIds = new Set(
        associatedAgentsResponse.success && associatedAgentsResponse.data
          ? associatedAgentsResponse.data.map((agent: any) => agent.externalAgentId)
          : []
      )

      // 将响应数据中的智能体列表转换为组件需要的格式，并标记已关联状态
      const agentList = response.data.data.map((agent: any) => ({
        id: agent.id,
        name: agent.name,
        description: agent.description || '',
        icon: agent.icon || '🤖',
        iconBg: agent.icon_background || '#FFEAD5',
        mode: agent.mode,
        tags: agent.tags || [],
        iconType: agent.icon_type,
        iconUrl: agent.icon_url,
        createdAt: agent.created_at,
        updatedAt: agent.updated_at,
        authorName: agent.author_name,
        // 标记是否已关联
        isAssociated: associatedAgentIds.has(agent.id),
        // 保留原始数据以备后用
        originalData: agent
      }))

      // 排序：未关联的智能体在前，已关联的智能体在后
      platformAgents.value = agentList.sort((a, b) => {
        // 如果一个已关联，一个未关联，未关联的排在前面
        if (a.isAssociated !== b.isAssociated) {
          return a.isAssociated ? 1 : -1
        }
        // 如果都是同样的关联状态，按名称排序
        return a.name.localeCompare(b.name)
      })
    } else {
      // 检查是否是500错误或其他服务器错误
      if (response.code === 500 || !response.success) {
        agentListError.value = true
      }
      platformAgents.value = []
    }
  } catch (error: any) {
    // 检查是否是500错误或网络错误
    if (error.response?.status === 500 || error.code === 500 || error.message?.includes('500')) {
      agentListError.value = true
    } else {
      agentListError.value = true // 对于其他错误也显示错误提示
    }
    platformAgents.value = []
  } finally {
    loading.value = false
  }
}

const selectPlatform = (platform: any) => {
  selectedPlatform.value = platform
  agentListError.value = false // 重置错误状态
  // 选择平台后，加载该平台下的智能体列表
  if (platform && platform.id) {
    loadPlatformAgents(platform.id)
  }
}

const toggleAgentSelection = (agent: any) => {
  if (selectedAgents.value.has(agent.id)) {
    selectedAgents.value.delete(agent.id)
  } else {
    selectedAgents.value.add(agent.id)
  }
}

// 获取模式显示名称
const getModeDisplayName = (mode: string) => {
  const modeMap: Record<string, string> = {
    'workflow': '工作流',
    'advanced-chat': 'Chatflow',
    'chat': '聊天助手',
    'agent-chat': 'Agent',
    'completion': '文本生成'
  }
  return modeMap[mode] || '未知'
}



const resetSelection = () => {
  selectedPlatform.value = null
  platformAgents.value = []
  selectedAgents.value.clear()
  agentListError.value = false // 重置错误状态
}

const closeModal = () => {
  emit('update:visible', false)
}

const confirmAssociation = () => {
  if (selectedAgents.value.size > 0) {
    const associatedAgents = selectedAgentsList.value.map(agent => ({
      ...agent,
      sourceType: 'external',
      externalAgentId: agent.id,
      externalAgentName: agent.name,
      platformId: selectedPlatform.value?.id || '',  // 添加platformId
      thirdPlatform: selectedPlatform.value
    }))

    emit('associate', associatedAgents)
    closeModal()
  }
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadPlatforms()
  }
})
</script>

<style scoped>
.associate-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10001;
  backdrop-filter: blur(4px);
}

.associate-content {
  background: #ffffff;
  border-radius: 16px;
  width: 90%;
  max-width: 900px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.associate-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
}

.associate-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.associate-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.platform-section,
.agents-section {
  margin-bottom: 24px;
}

.platform-section h4,
.agents-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.empty-platforms,
.empty-agents {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: #374151;
}

.empty-hint {
  font-size: 14px;
  margin: 0;
  opacity: 0.7;
}

.platform-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.platform-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
}

.platform-item:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.platform-item.active {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.platform-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.platform-info {
  flex: 1;
  min-width: 0;
}

.platform-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.platform-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.platform-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.platform-unit {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.platform-status {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
}

.platform-status.active {
  background: #dcfce7;
  color: #16a34a;
}

.platform-status.inactive {
  background: #fef2f2;
  color: #dc2626;
}

.agents-list {
  display: block;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 0;
}

.agents-group-header {
  padding: 12px 16px 8px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 8px;
  background: #f9fafb;
  border-radius: 6px;
  margin: 8px 0;
}

.agents-group-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.agents-group-header .group-hint {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  display: block;
}

.associated-header {
  background: #fef3f2;
  border-color: #fecaca;
}

.associated-header h4 {
  color: #dc2626;
}

.agent-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 4px 0;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
}

.agent-item:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.agent-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.agent-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
  border-color: #e5e7eb;
}

.agent-item.disabled:hover {
  transform: none;
  box-shadow: none;
  background: #f9fafb;
  border-color: #e5e7eb;
}

.agent-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.agent-item.selected .agent-checkbox {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.agent-icon-container {
  position: relative;
  flex-shrink: 0;
}

.agent-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  overflow: hidden;
}

.agent-icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}



.agent-info {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
}

.agent-description {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-meta {
  font-size: 11px;
  color: #9ca3af;
}

.agent-type {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 4px;
}

.agent-url {
  font-family: monospace;
}

.associate-footer {
  padding: 16px 24px;
  border-top: 1px solid #f1f5f9;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  flex: 1;
}

.selected-count {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 500;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary {
  background: #ffffff;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #f9fafb;
  color: #1f2937;
}

.btn-primary {
  background: #3b82f6;
  color: #ffffff;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 错误提示样式 */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  line-height: 1.4;
}

.error-message i {
  font-size: 16px;
  flex-shrink: 0;
}

.error-message span {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .associate-content {
    width: 95%;
    max-height: 90vh;
  }
  
  .platform-list,
  .agents-list {
    grid-template-columns: 1fr;
  }
  
  .associate-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .footer-right {
    justify-content: center;
  }
}
</style>
