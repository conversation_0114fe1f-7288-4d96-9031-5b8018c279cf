/**
 * 通用选择器组合式函数
 */

import { ref, computed, watch, nextTick } from 'vue'
import type { SelectorConfig, SelectorOption } from '@/types/system'

export interface UseSelectorOptions {
  config?: Partial<SelectorConfig>
  loadData?: () => Promise<any[]>
  transformData?: (data: any[]) => SelectorOption[]
  filterMethod?: (value: string, data: any) => boolean
}

export function useSelector(options: UseSelectorOptions = {}) {
  const {
    config = {},
    loadData,
    transformData,
    filterMethod
  } = options

  // 响应式数据
  const loading = ref(false)
  const options_data = ref<SelectorOption[]>([])
  const filteredOptions = ref<SelectorOption[]>([])
  const selectedValues = ref<string | string[]>(config.multiple ? [] : '')
  const filterText = ref('')
  const expandedKeys = ref<string[]>([])

  // 默认配置
  const defaultConfig: SelectorConfig = {
    multiple: false,
    clearable: true,
    filterable: true,
    placeholder: '请选择',
    size: 'default',
    disabled: false,
    loading: false,
    checkStrictly: false,
    showCheckbox: false,
    expandOnClickNode: true,
    defaultExpandAll: false,
    filterNodeMethod: filterMethod
  }

  // 合并配置
  const selectorConfig = computed(() => ({
    ...defaultConfig,
    ...config,
    loading: loading.value
  }))

  // 计算属性
  const hasSelection = computed(() => {
    if (selectorConfig.value.multiple) {
      return Array.isArray(selectedValues.value) && selectedValues.value.length > 0
    }
    return selectedValues.value !== '' && selectedValues.value !== null && selectedValues.value !== undefined
  })

  const selectedCount = computed(() => {
    if (selectorConfig.value.multiple && Array.isArray(selectedValues.value)) {
      return selectedValues.value.length
    }
    return hasSelection.value ? 1 : 0
  })

  // 方法
  const loadOptions = async () => {
    if (!loadData) return

    try {
      loading.value = true
      const data = await loadData()
      const transformed = transformData ? transformData(data) : data
      options_data.value = transformed
      filteredOptions.value = transformed
    } catch (error) {
      console.error('加载选择器数据失败:', error)
      options_data.value = []
      filteredOptions.value = []
    } finally {
      loading.value = false
    }
  }

  const filterOptions = (value: string) => {
    filterText.value = value
    if (!value) {
      filteredOptions.value = options_data.value
      return
    }

    const filterRecursive = (options: SelectorOption[]): SelectorOption[] => {
      return options.filter(option => {
        const matchesFilter = option.label.toLowerCase().includes(value.toLowerCase()) ||
                            (option.value && option.value.toLowerCase().includes(value.toLowerCase()))
        
        if (option.children && option.children.length > 0) {
          const filteredChildren = filterRecursive(option.children)
          if (filteredChildren.length > 0) {
            return true
          }
        }
        
        return matchesFilter
      }).map(option => ({
        ...option,
        children: option.children ? filterRecursive(option.children) : undefined
      }))
    }

    filteredOptions.value = filterRecursive(options_data.value)
  }

  const clearSelection = () => {
    selectedValues.value = selectorConfig.value.multiple ? [] : ''
  }

  const selectAll = () => {
    if (!selectorConfig.value.multiple) return
    
    const getAllValues = (options: SelectorOption[]): string[] => {
      let values: string[] = []
      options.forEach(option => {
        if (!option.disabled) {
          values.push(option.value)
        }
        if (option.children && option.children.length > 0) {
          values = values.concat(getAllValues(option.children))
        }
      })
      return values
    }

    selectedValues.value = getAllValues(filteredOptions.value)
  }

  const expandAll = () => {
    const getAllKeys = (options: SelectorOption[]): string[] => {
      let keys: string[] = []
      options.forEach(option => {
        if (option.children && option.children.length > 0) {
          keys.push(option.value)
          keys = keys.concat(getAllKeys(option.children))
        }
      })
      return keys
    }

    expandedKeys.value = getAllKeys(options_data.value)
  }

  const collapseAll = () => {
    expandedKeys.value = []
  }

  const getSelectedLabels = () => {
    const findLabel = (value: string, options: SelectorOption[]): string => {
      for (const option of options) {
        if (option.value === value) {
          return option.label
        }
        if (option.children && option.children.length > 0) {
          const childLabel = findLabel(value, option.children)
          if (childLabel) return childLabel
        }
      }
      return value
    }

    if (selectorConfig.value.multiple && Array.isArray(selectedValues.value)) {
      return selectedValues.value.map(value => findLabel(value, options_data.value))
    } else if (selectedValues.value) {
      return [findLabel(selectedValues.value as string, options_data.value)]
    }
    return []
  }

  // 监听器
  watch(filterText, (newValue) => {
    filterOptions(newValue)
  })

  // 初始化
  const init = async () => {
    await loadOptions()
    if (selectorConfig.value.defaultExpandAll) {
      await nextTick()
      expandAll()
    }
  }

  return {
    // 响应式数据
    loading,
    options: filteredOptions,
    selectedValues,
    filterText,
    expandedKeys,
    
    // 计算属性
    config: selectorConfig,
    hasSelection,
    selectedCount,
    
    // 方法
    loadOptions,
    filterOptions,
    clearSelection,
    selectAll,
    expandAll,
    collapseAll,
    getSelectedLabels,
    init
  }
}
