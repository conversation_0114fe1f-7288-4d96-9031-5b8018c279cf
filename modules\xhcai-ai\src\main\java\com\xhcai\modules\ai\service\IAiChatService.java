package com.xhcai.modules.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.ai.dto.AiChatQueryDTO;
import com.xhcai.modules.ai.dto.AiChatRequest;
import com.xhcai.modules.ai.dto.AiChatRequestDTO;
import com.xhcai.modules.ai.dto.ConversationInfo;
import com.xhcai.modules.ai.dto.FileUploadResponseDTO;
import com.xhcai.modules.ai.entity.AiChatRecord;
import com.xhcai.modules.ai.vo.AiChatResponseVO;
import com.xhcai.modules.ai.vo.AiChatRecordVO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI聊天服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IAiChatService extends IService<AiChatRecord> {

    /**
     * 发送聊天消息
     *
     * @param requestDTO 聊天请求
     * @return 聊天响应
     */
    AiChatResponseVO chat(AiChatRequestDTO requestDTO);

    /**
     * 流式聊天
     *
     * @param requestDTO 聊天请求
     * @return SSE发射器
     */
    SseEmitter streamChat(AiChatRequestDTO requestDTO);

    /**
     * 流式聊天（Dify标准格式）
     *
     * @param request Dify标准格式聊天请求
     * @return SSE发射器
     */
    SseEmitter streamChat(AiChatRequest request);


    /**
     * 获取聊天历史记录
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 聊天记录列表
     */
    List<AiChatRecordVO> getChatHistory(String sessionId, String userId);

    /**
     * 分页查询聊天记录
     *
     * @param queryDTO 查询条件
     * @return 聊天记录分页列表
     */
    PageResult<AiChatRecordVO> selectChatRecordPage(AiChatQueryDTO queryDTO);

    /**
     * 清空会话历史
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean clearChatHistory(String sessionId, String userId);

    /**
     * 删除聊天记录
     *
     * @param recordIds 记录ID列表
     * @return 是否成功
     */
    boolean deleteChatRecords(List<String> recordIds);

    /**
     * 生成新的会话ID
     *
     * @param userId 用户ID
     * @return 会话ID
     */
    String generateSessionId(String userId);

    /**
     * 获取用户的会话列表
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    List<String> getUserSessions(String userId);

    /**
     * 重新生成回复
     *
     * @param recordId 记录ID
     * @return 新的回复
     */
    AiChatResponseVO regenerateResponse(String recordId);

    /**
     * 评价回复
     *
     * @param recordId 记录ID
     * @param rating   评分（1-5）
     * @param feedback 反馈内容
     * @return 是否成功
     */
    boolean rateResponse(String recordId, Integer rating, String feedback);

    /**
     * 获取聊天统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    ChatStatistics getChatStatistics(String userId);

    /**
     * 获取智能体会话列表
     *
     * @param appId 应用ID
     * @param limit 限制数量，默认100
     * @param pinned 是否只获取置顶会话，默认false
     * @return 会话列表
     */
    List<ConversationInfo> getConversations(String appId, Integer limit, Boolean pinned);

    /**
     * 获取会话名称
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @param agentId 智能体ID
     * @param autoGenerate 是否自动生成名称，默认true
     * @return 会话信息
     */
    ConversationInfo getConversationName(String appId, String conversationId, String agentId, Boolean autoGenerate);

    /**
     * 修改会话名称
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @param name 新的会话名称
     * @return 会话信息
     */
    ConversationInfo updateConversationName(String appId, String conversationId, String name);

    /**
     * 删除会话记录
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @return 删除是否成功
     */
    boolean deleteConversation(String appId, String conversationId);

    /**
     * 停止工作流
     *
     * @param appId 应用ID
     * @param taskId 任务ID
     * @return 停止是否成功
     */
    boolean stopWorkflow(String appId, String taskId);

    /**
     * 获取会话消息列表
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @param limit 限制数量，默认20
     * @param lastId 最后一个消息ID（用于分页）
     * @return 消息列表
     */
    List<ConversationMessage> getConversationMessages(String appId, String conversationId, Integer limit, String lastId);

    /**
     * 获取会话消息列表（原样返回Dify数据）
     *
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @param limit 限制数量，默认20
     * @param lastId 最后一个消息ID（用于分页）
     * @return Dify原始响应数据
     */
    Object getConversationMessagesRaw(String appId, String conversationId, Integer limit, String lastId);

    /**
     * 获取应用会话参数
     *
     * @param appId 应用ID
     * @return 应用会话参数
     */
    Object getAppParameters(String appId);

    /**
     * 上传文件（上传到Dify平台并存储到MinIO，同时保存记录到数据库）
     *
     * @param fileContent 文件内容
     * @param fileName 文件名
     * @param contentType 内容类型
     * @return 文件上传响应
     */
    FileUploadResponseDTO uploadFile(byte[] fileContent, String fileName, String contentType);

    /**
     * 上传远程文件
     *
     * @param fileUrl 远程文件URL
     * @return 远程文件上传响应
     */
    Object uploadRemoteFile(String fileUrl);

    /**
     * 聊天统计信息
     */
    class ChatStatistics {
        private Long totalChats;
        private Long totalTokens;
        private Long totalSessions;
        private Double avgResponseTime;

        public Long getTotalChats() {
            return totalChats;
        }

        public void setTotalChats(Long totalChats) {
            this.totalChats = totalChats;
        }

        public Long getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(Long totalTokens) {
            this.totalTokens = totalTokens;
        }

        public Long getTotalSessions() {
            return totalSessions;
        }

        public void setTotalSessions(Long totalSessions) {
            this.totalSessions = totalSessions;
        }

        public Double getAvgResponseTime() {
            return avgResponseTime;
        }

        public void setAvgResponseTime(Double avgResponseTime) {
            this.avgResponseTime = avgResponseTime;
        }
    }



    /**
     * 会话消息信息
     */
    class ConversationMessage {
        private String id;
        private String conversationId;
        private String query;
        private String answer;
        private Long createdAt;
        private String role; // user 或 assistant
        private String content;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public String getAnswer() {
            return answer;
        }

        public void setAnswer(String answer) {
            this.answer = answer;
        }

        public Long getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(Long createdAt) {
            this.createdAt = createdAt;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
