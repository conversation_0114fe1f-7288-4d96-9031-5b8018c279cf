<template>
  <div class="tree-node">
    <div 
      class="tree-item" 
      :class="{ active: selectedId === category.id }"
      :style="{ paddingLeft: `${20 + level * 20}px` }"
      @click="$emit('select', category.id)"
    >
      <div class="item-content">
        <button 
          class="expand-btn"
          v-if="hasChildren"
          @click.stop="toggleExpanded"
          :class="{ expanded }"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
        <div class="expand-placeholder" v-else></div>

        <i class="fas fa-folder item-icon" :class="{ 'fa-folder-open': expanded && hasChildren }"></i>
        <span class="item-name">{{ category.name }}</span>
        <span class="item-count">({{ category.fileCount }})</span>

        <div class="item-actions" @click.stop v-if="!selectionMode">
          <button class="action-btn" @click="$emit('create-child', category)" title="创建子分类" v-if="level < 2">
            <i class="fas fa-plus"></i>
          </button>
          <button class="action-btn" @click="$emit('edit', category)" title="编辑分类">
            <i class="fas fa-edit"></i>
          </button>
          <button class="action-btn danger" @click="$emit('delete', category)" title="删除分类">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 子分类 -->
    <div class="children" v-if="expanded && hasChildren">
      <CategoryTreeNode
        v-for="child in allCategories"
        :key="child.id"
        :category="child"
        :level="level + 1"
        :selected-id="selectedId"
        :all-categories="child.children"
        :selection-mode="selectionMode"
        @select="$emit('select', $event)"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
        @create-child="$emit('create-child', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface Props {
  category: any
  level: number
  selectedId: string | null
  allCategories: any[]
  selectionMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selectionMode: false
})

// Emits
const emit = defineEmits<{
  select: [categoryId: string]
  edit: [category: any]
  delete: [category: any]
  'create-child': [category: any]
}>()

// 响应式数据
const expanded = ref(false)

const hasChildren = computed(() => props.allCategories.length > 0)

// 方法
const toggleExpanded = () => {
  expanded.value = !expanded.value
}
</script>

<style scoped>
.tree-node {
  user-select: none;
}

.tree-item {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tree-item:hover {
  background: #f8fafc;
}

.tree-item.active {
  background: #eff6ff;
  border-right: 3px solid #3b82f6;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  min-height: 36px;
}

.expand-btn {
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.expand-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.expand-btn.expanded {
  transform: rotate(90deg);
}

.expand-placeholder {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.item-icon {
  color: #64748b;
  font-size: 14px;
  width: 16px;
  flex-shrink: 0;
}

.item-icon.fa-folder-open {
  color: #3b82f6;
}

.item-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-count {
  font-size: 12px;
  color: #64748b;
  flex-shrink: 0;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-item:hover .item-actions {
  opacity: 1;
}

.action-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #dc2626;
}

.children {
  border-left: 1px solid #f1f5f9;
  margin-left: 8px;
}
</style>
