package com.xhcai.modules.rag.service.processor.impl;

import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.service.processor.AbstractFileSegmentationProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * Excel文件分段处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class ExcelFileSegmentationProcessor extends AbstractFileSegmentationProcessor {

    @Override
    public List<String> getSupportedFileTypes() {
        return Arrays.asList("xls", "xlsx");
    }

    @Override
    public String getProcessorName() {
        return "Excel文件分段处理器";
    }

    @Override
    public int getPriority() {
        return 40;
    }

    @Override
    public List<SegmentResult> processSegmentation(Document document, InputStream inputStream, KnowledgeSegmentConfig knowledgeSegmentConfig) throws Exception {
        log.info("开始处理Excel文件分段: documentId={}, fileName={}", document.getId(), document.getName());
        return null;
//
//        try {
//            String text = extractTextFromExcel(document, inputStream);
//            text = cleanText(text);
//
//            log.debug("Excel文件内容长度: {} 字符", text.length());
//
//            // Excel文件按固定大小分段更合适
//            List<SegmentResult> segments = segmentByFixedSize(text);
//
//            log.info("Excel文件分段完成: documentId={}, 分段数量={}", document.getId(), segments.size());
//            return segments;
//
//        } catch (Exception e) {
//            log.error("Excel文件分段处理失败: documentId={}, error={}", document.getId(), e.getMessage(), e);
//            throw new Exception("Excel文件分段处理失败: " + e.getMessage(), e);
//        }
    }

    /**
     * 从Excel文件提取文本
     *
     * @param document    文档信息
     * @param inputStream 输入流
     * @return 提取的文本
     * @throws Exception 提取异常
     */
    private String extractTextFromExcel(Document document, InputStream inputStream) throws Exception {
        String fileExtension = document.getDocType();
        Workbook workbook = null;
        
        try {
            if ("xlsx".equals(fileExtension)) {
                workbook = new XSSFWorkbook(inputStream);
            } else if ("xls".equals(fileExtension)) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new Exception("不支持的Excel文件格式: " + fileExtension);
            }

            StringBuilder content = new StringBuilder();
            DataFormatter formatter = new DataFormatter();

            // 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();
                
                content.append("工作表: ").append(sheetName).append("\n");

                // 遍历所有行
                for (Row row : sheet) {
                    StringBuilder rowContent = new StringBuilder();
                    
                    // 遍历所有单元格
                    for (Cell cell : row) {
                        String cellValue = formatter.formatCellValue(cell);
                        if (cellValue != null && !cellValue.trim().isEmpty()) {
                            if (rowContent.length() > 0) {
                                rowContent.append("\t");
                            }
                            rowContent.append(cellValue.trim());
                        }
                    }
                    
                    if (rowContent.length() > 0) {
                        content.append(rowContent.toString()).append("\n");
                    }
                }
                
                content.append("\n");
            }

            return content.toString();

        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.warn("关闭Excel工作簿失败: {}", e.getMessage());
                }
            }
        }
    }
}
