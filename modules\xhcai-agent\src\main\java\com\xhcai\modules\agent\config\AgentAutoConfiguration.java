package com.xhcai.modules.agent.config;

import com.xhcai.common.core.config.YamlPropertySourceFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;

/**
 * 智能体模块自动配置类
 * 负责智能体模块的组件扫描、实体扫描、Mapper扫描和配置文件加载
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@AutoConfiguration
@ComponentScan(basePackages = {
    "com.xhcai.modules.agent"
})
@EntityScan(basePackages = "com.xhcai.modules.agent.entity")
@MapperScan(basePackages = "com.xhcai.modules.agent.mapper")
@ConfigurationPropertiesScan(basePackages = "com.xhcai.modules.agent.config")
@PropertySource(value = "classpath:application-xhcai-agent.yml", factory = YamlPropertySourceFactory.class)
public class AgentAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(AgentAutoConfiguration.class);

    public AgentAutoConfiguration() {
        log.info("=== 智能体模块自动配置已启用 ===");
        log.info("组件扫描包: com.xhcai.modules.agent.*");
        log.info("实体扫描包: com.xhcai.modules.agent.entity");
        log.info("Mapper扫描包: com.xhcai.modules.agent.mapper");
        log.info("配置文件: application-xhcai-agent.yml");
    }
}
