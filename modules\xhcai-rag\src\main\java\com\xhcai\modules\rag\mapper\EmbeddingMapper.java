package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.rag.entity.Embedding;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 嵌入向量Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface EmbeddingMapper extends BaseMapper<Embedding> {

    /**
     * 根据哈希值和模型信息查询嵌入向量
     *
     * @param hash 内容哈希值
     * @param modelName 模型名称
     * @param providerName 模型提供商
     * @return 嵌入向量
     */
    @Select("SELECT * FROM embeddings WHERE hash = #{hash} AND model_name = #{modelName} AND provider_name = #{providerName}")
    Embedding selectByHashAndModel(@Param("hash") String hash,
                                  @Param("modelName") String modelName,
                                  @Param("providerName") String providerName);

    /**
     * 根据模型信息查询嵌入向量列表
     *
     * @param modelName 模型名称
     * @param providerName 模型提供商
     * @return 嵌入向量列表
     */
    @Select("SELECT * FROM embeddings WHERE model_name = #{modelName} AND provider_name = #{providerName} ORDER BY create_time DESC")
    List<Embedding> selectByModel(@Param("modelName") String modelName,
                                 @Param("providerName") String providerName);

    /**
     * 根据模型信息统计嵌入向量数量
     *
     * @param modelName 模型名称
     * @param providerName 模型提供商
     * @return 嵌入向量数量
     */
    @Select("SELECT COUNT(*) FROM embeddings WHERE model_name = #{modelName} AND provider_name = #{providerName}")
    Long countByModel(@Param("modelName") String modelName,
                     @Param("providerName") String providerName);

    /**
     * 查询最近创建的嵌入向量
     *
     * @param limit 限制数量
     * @return 嵌入向量列表
     */
    @Select("SELECT * FROM embeddings ORDER BY create_time DESC LIMIT #{limit}")
    List<Embedding> selectRecent(@Param("limit") Integer limit);

    /**
     * 根据哈希值列表查询嵌入向量
     *
     * @param hashes 哈希值列表
     * @param modelName 模型名称
     * @param providerName 模型提供商
     * @return 嵌入向量列表
     */
    @Select("<script>" +
            "SELECT * FROM embeddings " +
            "WHERE model_name = #{modelName} AND provider_name = #{providerName} " +
            "AND hash IN " +
            "<foreach collection='hashes' item='hash' open='(' separator=',' close=')'>" +
            "  #{hash}" +
            "</foreach>" +
            "</script>")
    List<Embedding> selectByHashesAndModel(@Param("hashes") List<String> hashes,
                                          @Param("modelName") String modelName,
                                          @Param("providerName") String providerName);

    /**
     * 删除指定时间之前的嵌入向量
     *
     * @param beforeDate 指定时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 删除数量
     */
    @Select("DELETE FROM embeddings WHERE create_time < #{beforeDate}")
    int deleteBeforeDate(@Param("beforeDate") String beforeDate);

    /**
     * 统计总的嵌入向量数量
     *
     * @return 总数量
     */
    @Select("SELECT COUNT(*) FROM embeddings")
    Long countTotal();

    /**
     * 按模型统计嵌入向量数量
     *
     * @return 统计结果列表 [modelName, providerName, count]
     */
    @Select("SELECT model_name, provider_name, COUNT(*) as count FROM embeddings GROUP BY model_name, provider_name ORDER BY count DESC")
    List<Object[]> countByModelGroup();
}
