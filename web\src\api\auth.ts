/**
 * 认证相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse, TokenInfo } from '@/types/api'

// 登录请求参数
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  uuid?: string
  tenantCode?: string
  rememberMe?: boolean
}

// 登录响应数据
export interface LoginResponse {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
  userInfo: {
    userId: string
    username: string
    nickname: string
    email: string
    phone: string
    avatar: string
    gender: string
    deptId: string
    deptName: string
    tenantId: string
    status: string
    loginIp: string
    loginTime: string
  }
  permissions: string[]
  roles: string[]
  menus: any[]
}

// 注册请求参数
export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  captcha?: string
}

// 重置密码请求参数
export interface ResetPasswordRequest {
  email: string
  captcha?: string
}

// 修改密码请求参数
export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

/**
 * 认证API类
 */
export class AuthAPI {
  /**
   * 用户登录
   */
  static async login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return apiClient.post<LoginResponse>('/api/auth/login', data, {
      skipAuth: true // 登录接口不需要认证
    })
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/auth/logout')
  }

  /**
   * 刷新Token
   */
  static async refreshToken(refreshToken: string): Promise<ApiResponse<TokenInfo>> {
    return apiClient.post<TokenInfo>('/api/auth/refresh', {
      refresh_token: refreshToken
    }, {
      skipAuth: true // 刷新token时不需要认证
    })
  }

  /**
   * 用户注册
   */
  static async register(data: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    return apiClient.post<LoginResponse>('/api/auth/register', data, {
      skipAuth: true
    })
  }

  /**
   * 发送重置密码邮件
   */
  static async sendResetPasswordEmail(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/auth/reset-password', data, {
      skipAuth: true
    })
  }

  /**
   * 修改密码
   */
  static async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/auth/change-password', data)
  }

  /**
   * 验证Token有效性
   */
  static async validateToken(): Promise<ApiResponse<{ valid: boolean }>> {
    return apiClient.get<{ valid: boolean }>('/api/auth/validate')
  }

  /**
   * 获取验证码
   */
  static async getCaptcha(): Promise<ApiResponse<{ image: string; key: string }>> {
    return apiClient.get<{ image: string; key: string }>('/api/auth/captcha', undefined, {
      skipAuth: true
    })
  }

  /**
   * 检查平台初始化状态
   */
  static async checkPlatformInitialization(): Promise<ApiResponse<{
    initialized: boolean;
    hasPlatformAdmin: boolean;
    adminCount: number;
    message: string;
  }>> {
    return apiClient.get('/api/platform/init/status', undefined, {
      skipAuth: true
    })
  }

  /**
   * 创建平台管理员（平台初始化）
   */
  static async createPlatformAdmin(data: {
    username: string;
    password: string;
    email: string;
    realName?: string;
  }): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const params = {
      username: data.username,
      password: data.password,
      email: data.email,
      realName: ''
    }
    if (data.realName) {
      params.realName = data.realName
    } else {
      params.realName = data.username
    }
    return apiClient.postForm('/api/platform/init/admin', params, {
      skipAuth: true
    })
  }
}

export default AuthAPI
