import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { RendererType } from '@/types/renderer'

export const useRendererStore = defineStore('renderer', () => {
  // 当前选中的渲染器类型
  const selectedRenderer = ref<RendererType>('text')

  // 设置选中的渲染器
  const setSelectedRenderer = (renderer: RendererType) => {
    selectedRenderer.value = renderer
    console.log('全局渲染器状态已更新:', renderer)
  }

  // 处理渲染器选择的方法
  const handleRendererSelect = (renderer: RendererType) => {
    console.log('选择渲染器:', renderer)
    setSelectedRenderer(renderer)
  }

  // 重置渲染器为默认值
  const resetRenderer = () => {
    selectedRenderer.value = 'text'
  }

  return {
    selectedRenderer,
    setSelectedRenderer,
    handleRendererSelect,
    resetRenderer
  }
})
