<template>
  <div class="login-page min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- AI风格背景动画 -->
    <div class="bg-animation absolute inset-0 z-10">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-100/40 via-purple-100/30 to-cyan-100/35 animate-pulse-slow"></div>
    </div>

    <!-- 动态网格背景 -->
    <div class="grid-background absolute inset-0 z-10 opacity-30" ref="gridContainer"></div>

    <!-- 浮动粒子效果 -->
    <div class="particles absolute inset-0 z-20" ref="particlesContainer"></div>

    <!-- 神经网络连接线 -->
    <div class="neural-network absolute inset-0 z-20 opacity-25" ref="neuralContainer"></div>

    <!-- 主要内容区域 - 横向布局，两边留少许空白 -->
    <div class="main-content flex w-full h-screen relative z-30 mx-auto" style="max-width: calc(100vw - 4rem); margin-left: 2rem; margin-right: 2rem;">

      <!-- 左侧AI展示区域 -->
      <div class="ai-showcase flex-1 flex flex-col justify-center items-center p-16 relative bg-white/60 backdrop-blur-md rounded-r-2xl border-r border-white/40">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-100/25 via-purple-100/15 to-transparent z-0 rounded-r-2xl"></div>

        <div class="relative z-10 w-full max-w-2xl text-center">
          <!-- 主标题 -->
          <div class="mb-8 text-center">
            <h1 class="text-6xl font-black bg-gradient-to-r from-gray-700 via-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4 leading-tight">
              AI智能体服务平台
            </h1>
            <p class="text-xl text-gray-600 leading-relaxed font-light">
              构建下一代人工智能应用生态，让AI为您的业务赋能
            </p>
          </div>

          <!-- 平台统计数据 -->
          <div class="platform-stats grid grid-cols-2 gap-6 mb-10">
            <div class="stat-card bg-white/70 backdrop-blur-xl rounded-2xl p-6 text-center border border-blue-100/50 transition-all duration-500 hover:bg-white/80 hover:-translate-y-1 hover:scale-105 shadow-sm">
              <div class="stat-number text-4xl font-bold bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent mb-2">50,000+</div>
              <div class="stat-label text-sm text-gray-600 font-medium">活跃智能体</div>
            </div>
            <div class="stat-card bg-white/70 backdrop-blur-xl rounded-2xl p-6 text-center border border-purple-100/50 transition-all duration-500 hover:bg-white/80 hover:-translate-y-1 hover:scale-105 shadow-sm">
              <div class="stat-number text-4xl font-bold bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent mb-2">1M+</div>
              <div class="stat-label text-sm text-gray-600 font-medium">企业用户</div>
            </div>
            <div class="stat-card bg-white/70 backdrop-blur-xl rounded-2xl p-6 text-center border border-green-100/50 transition-all duration-500 hover:bg-white/80 hover:-translate-y-1 hover:scale-105 shadow-sm">
              <div class="stat-number text-4xl font-bold bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent mb-2">99.9%</div>
              <div class="stat-label text-sm text-gray-600 font-medium">服务可用性</div>
            </div>
            <div class="stat-card bg-white/70 backdrop-blur-xl rounded-2xl p-6 text-center border border-orange-100/50 transition-all duration-500 hover:bg-white/80 hover:-translate-y-1 hover:scale-105 shadow-sm">
              <div class="stat-number text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent mb-2">24/7</div>
              <div class="stat-label text-sm text-gray-600 font-medium">技术支持</div>
            </div>
          </div>

          <!-- 核心功能展示 -->
          <div class="core-features grid grid-cols-2 gap-4 mb-8">
            <div class="feature-card bg-white/60 backdrop-blur-md rounded-xl p-5 border border-blue-100/40 transition-all duration-500 hover:bg-white/75 hover:-translate-y-1 shadow-sm">
              <div class="feature-icon text-3xl mb-3">🤖</div>
              <div class="feature-title text-base font-semibold text-gray-700 mb-2">智能体管理</div>
              <div class="feature-desc text-xs text-gray-600 leading-relaxed">创建、部署和管理各类AI智能体，支持多模态交互</div>
            </div>
            <div class="feature-card bg-white/60 backdrop-blur-md rounded-xl p-5 border border-green-100/40 transition-all duration-500 hover:bg-white/75 hover:-translate-y-1 shadow-sm">
              <div class="feature-icon text-3xl mb-3">📚</div>
              <div class="feature-title text-base font-semibold text-gray-700 mb-2">知识库构建</div>
              <div class="feature-desc text-xs text-gray-600 leading-relaxed">构建企业专属知识库，提升AI回答准确性</div>
            </div>
            <div class="feature-card bg-white/60 backdrop-blur-md rounded-xl p-5 border border-purple-100/40 transition-all duration-500 hover:bg-white/75 hover:-translate-y-1 shadow-sm">
              <div class="feature-icon text-3xl mb-3">🔧</div>
              <div class="feature-title text-base font-semibold text-gray-700 mb-2">插件生态</div>
              <div class="feature-desc text-xs text-gray-600 leading-relaxed">丰富的插件市场，扩展AI能力边界</div>
            </div>
            <div class="feature-card bg-white/60 backdrop-blur-md rounded-xl p-5 border border-orange-100/40 transition-all duration-500 hover:bg-white/75 hover:-translate-y-1 shadow-sm">
              <div class="feature-icon text-3xl mb-3">📊</div>
              <div class="feature-title text-base font-semibold text-gray-700 mb-2">数据分析</div>
              <div class="feature-desc text-xs text-gray-600 leading-relaxed">实时监控和分析AI服务性能指标</div>
            </div>
          </div>

          <!-- 技术优势 -->
          <div class="tech-advantages text-center">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">技术优势</h3>
            <div class="advantages-list space-y-3 max-w-lg mx-auto">
              <div class="advantage-item flex items-center text-sm text-gray-600 justify-center">
                <div class="w-2 h-2 bg-cyan-300 rounded-full mr-3 animate-pulse"></div>
                <span>支持多种大语言模型，包括GPT、Claude、文心一言等</span>
              </div>
              <div class="advantage-item flex items-center text-sm text-gray-600 justify-center">
                <div class="w-2 h-2 bg-blue-300 rounded-full mr-3 animate-pulse" style="animation-delay: 0.5s;"></div>
                <span>企业级安全保障，数据隐私严格保护</span>
              </div>
              <div class="advantage-item flex items-center text-sm text-gray-600 justify-center">
                <div class="w-2 h-2 bg-purple-300 rounded-full mr-3 animate-pulse" style="animation-delay: 1s;"></div>
                <span>云原生架构，支持弹性扩容和高可用部署</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录区域 -->
      <div class="login-section w-1/3 flex flex-col justify-center items-center p-16 bg-white/70 backdrop-blur-2xl border-l border-gray-200/50 relative rounded-l-2xl">
        <div class="absolute inset-0 bg-gradient-to-br from-white/50 via-blue-50/30 to-indigo-50/20 z-0 rounded-l-2xl"></div>

        <!-- 登录容器 -->
        <div class="login-container bg-white/95 backdrop-blur-3xl rounded-3xl p-10 shadow-lg border border-gray-200/50 w-full max-w-md relative z-10">
          <!-- Logo区域 -->
          <div class="logo-section text-center mb-8">
            <div class="logo w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-5 text-4xl text-blue-600 shadow-sm border border-blue-200/30">
              🤖
            </div>
            <h1 class="platform-title text-3xl font-bold text-gray-700 mb-2">
              AI智能体平台
            </h1>
            <p class="platform-subtitle text-sm text-gray-500 font-medium">
              登录您的智能体管理中心
            </p>
          </div>

          <!-- 登录表单 -->
          <form @submit.prevent="handleLogin" class="space-y-5">
            <div class="form-group">
              <label class="form-label text-sm font-medium text-gray-700 mb-2 block">用户名</label>
              <input
                v-model="loginForm.username"
                type="text"
                class="form-input w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                placeholder="请输入用户名"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label text-sm font-medium text-gray-700 mb-2 block">密码</label>
              <input
                v-model="loginForm.password"
                type="password"
                class="form-input w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                placeholder="请输入密码"
                required
              />
            </div>

            <button
              type="submit"
              class="login-btn w-full py-4 bg-gradient-to-r from-blue-400 to-indigo-400 text-white border-none rounded-xl text-base font-semibold cursor-pointer transition-all duration-300 relative overflow-hidden hover:-translate-y-1 hover:shadow-lg hover:from-blue-500 hover:to-indigo-500 disabled:opacity-50"
              :disabled="isLoading"
            >
              <span v-if="!isLoading">立即登录</span>
              <span v-else class="flex items-center justify-center gap-2">
                <div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                登录中...
              </span>
            </button>
          </form>

          <!-- 忘记密码 -->
          <div class="forgot-password text-center mt-5">
            <a
              href="#"
              @click.prevent="handleForgotPassword"
              class="text-blue-600 no-underline text-sm font-medium transition-colors duration-300 hover:text-purple-600"
            >
              忘记密码？
            </a>
          </div>

          <!-- 平台特性 -->
          <div class="platform-features mt-8 pt-6 border-t border-gray-100">
            <h4 class="text-sm font-semibold text-gray-700 mb-4 text-center">平台核心功能</h4>
            <div class="features-grid grid grid-cols-2 gap-3">
              <div class="feature-item flex items-center text-xs text-gray-600 p-2 rounded-lg hover:bg-blue-50 transition-all duration-300">
                <div class="feature-icon w-6 h-6 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full flex items-center justify-center mr-2 text-xs text-white">
                  🤖
                </div>
                <span>智能体管理</span>
              </div>
              <div class="feature-item flex items-center text-xs text-gray-600 p-2 rounded-lg hover:bg-green-50 transition-all duration-300">
                <div class="feature-icon w-6 h-6 bg-gradient-to-br from-green-300 to-green-400 rounded-full flex items-center justify-center mr-2 text-xs text-white">
                  📚
                </div>
                <span>知识库构建</span>
              </div>
              <div class="feature-item flex items-center text-xs text-gray-600 p-2 rounded-lg hover:bg-purple-50 transition-all duration-300">
                <div class="feature-icon w-6 h-6 bg-gradient-to-br from-purple-300 to-purple-400 rounded-full flex items-center justify-center mr-2 text-xs text-white">
                  🔧
                </div>
                <span>插件生态</span>
              </div>
              <div class="feature-item flex items-center text-xs text-gray-600 p-2 rounded-lg hover:bg-orange-50 transition-all duration-300">
                <div class="feature-icon w-6 h-6 bg-gradient-to-br from-orange-300 to-orange-400 rounded-full flex items-center justify-center mr-2 text-xs text-white">
                  📊
                </div>
                <span>数据分析</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { useMessage } from '@/composables/useMessage'
import { AuthAPI } from '@/api'
import { logger } from '@/config/env'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const { showSuccess, showError, showInfo } = useMessage()

// 响应式数据
const isLoading = ref(false)
const particlesContainer = ref<HTMLElement>()
const neuralContainer = ref<HTMLElement>()
const gridContainer = ref<HTMLElement>()

// 登录表单
const loginForm = ref({
  username: '',
  password: ''
})

// 处理登录
const handleLogin = async () => {
  // 前端基础校验
  if (!loginForm.value.username || !loginForm.value.password) {
    showError('请填写完整的登录信息')
    return
  }

  // 校验用户名长度
  if (loginForm.value.username.length < 2 || loginForm.value.username.length > 30) {
    showError('用户名长度必须在2-30个字符之间')
    return
  }

  // 校验密码长度
  if (loginForm.value.password.length < 6 || loginForm.value.password.length > 20) {
    showError('密码长度必须在6-20个字符之间')
    return
  }

  isLoading.value = true

  try {
    // 发送登录请求，使用后端期望的字段名和原始密码
    const response = await AuthAPI.login({
      username: loginForm.value.username,
      password: loginForm.value.password,
      captcha: '',
      uuid: '',
      tenantCode: 'default',
      rememberMe: false
    })

    if (response.data && response.code === 200) {
      // 构建用户信息
      const userInfo = {
        id: response.data.userInfo.userId,
        username: response.data.userInfo.username,
        email: response.data.userInfo.email || '',
        avatar: response.data.userInfo.avatar || '',
        roles: response.data.roles || [],
        permissions: response.data.permissions || [],
        isLoggedIn: true,
        loginTime: new Date(),
        lastActiveTime: new Date()
      }

      // 构建token信息
      const tokenInfo = {
        token: response.data.accessToken,
        access_token: response.data.accessToken,
        refresh_token: response.data.refreshToken,
        expires_in: response.data.expiresIn || 86400, // 使用后端返回的过期时间（秒数）
        token_type: response.data.tokenType || 'Bearer'
      }

      // 同时设置token和用户信息
      authStore.setAuth(tokenInfo, userInfo)

      logger.info('Login successful for user:', userInfo.username)
      logger.debug('Auth state after login:', {
        isLoggedIn: authStore.isLoggedIn,
        hasToken: !!authStore.tokens?.token,
        hasUser: !!authStore.currentUser
      })

      showSuccess('登录成功，正在跳转...')

      // 获取重定向路径
      const redirectPath = (route.query.redirect as string) || '/home'
      logger.debug('Redirecting to:', redirectPath)

      // 确保认证状态已更新后再进行跳转
      await nextTick()

      // 使用setTimeout确保状态完全更新后再跳转
      setTimeout(async () => {
        // 强制刷新页面到目标路径，确保路由守卫重新执行
        window.location.replace(redirectPath)
      }, 200)
       
    } else {
      showError(response.message || '登录失败，请检查用户名和密码')
    }
  } catch (error: any) {
    logger.error('Login error:', error)

    // 处理不同类型的错误
    let errorMessage = '登录失败，请稍后重试'

    if (error.code === 401) {
      errorMessage = '用户名或密码错误'
    } else if (error.code === 429) {
      errorMessage = '登录尝试过于频繁，请稍后再试'
    } else if (error.code === 0) {
      errorMessage = '网络连接失败，请检查网络设置'
    } else if (error.message) {
      errorMessage = error.message
    }

    showError(errorMessage)
  } finally {
    isLoading.value = false
  }
}

// 处理忘记密码
const handleForgotPassword = () => {
  showInfo('密码重置功能开发中，请联系管理员')
}



// 创建动态网格背景
const createGridBackground = () => {
  if (!gridContainer.value) return

  const gridSize = 50
  const gridCount = Math.ceil(window.innerWidth / gridSize) * Math.ceil(window.innerHeight / gridSize)

  for (let i = 0; i < 20; i++) {
    const gridLine = document.createElement('div')
    gridLine.className = 'grid-line absolute bg-gradient-to-r from-transparent via-blue-300/15 to-transparent'
    gridLine.style.width = '100%'
    gridLine.style.height = '1px'
    gridLine.style.top = Math.random() * 100 + '%'
    gridLine.style.animationDelay = Math.random() * 5 + 's'
    gridLine.style.animation = 'gridPulse 5s ease-in-out infinite'

    gridContainer.value.appendChild(gridLine)
  }

  for (let i = 0; i < 15; i++) {
    const gridLine = document.createElement('div')
    gridLine.className = 'grid-line absolute bg-gradient-to-b from-transparent via-indigo-300/15 to-transparent'
    gridLine.style.width = '1px'
    gridLine.style.height = '100%'
    gridLine.style.left = Math.random() * 100 + '%'
    gridLine.style.animationDelay = Math.random() * 5 + 's'
    gridLine.style.animation = 'gridPulse 5s ease-in-out infinite'

    gridContainer.value.appendChild(gridLine)
  }
}

// 创建浮动粒子效果
const createParticles = () => {
  if (!particlesContainer.value) return

  const particleCount = 60

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div')
    particle.className = 'particle absolute rounded-full'

    // 随机大小和颜色
    const size = Math.random() * 3 + 1
    particle.style.width = size + 'px'
    particle.style.height = size + 'px'
    particle.style.left = Math.random() * 100 + '%'
    particle.style.top = Math.random() * 100 + '%'

    const colors = ['bg-blue-300/25', 'bg-indigo-300/25', 'bg-cyan-300/25', 'bg-purple-300/20']
    particle.className += ' ' + colors[Math.floor(Math.random() * colors.length)]

    particle.style.animationDelay = Math.random() * 20 + 's'
    particle.style.animationDuration = (Math.random() * 15 + 10) + 's'
    particle.style.animation = `particleFloat ${particle.style.animationDuration} linear infinite`

    particlesContainer.value.appendChild(particle)
  }
}

// 创建神经网络连接效果
const createNeuralNetwork = () => {
  if (!neuralContainer.value) return

  const nodeCount = 15

  for (let i = 0; i < nodeCount; i++) {
    const node = document.createElement('div')
    node.className = 'neural-node absolute w-1.5 h-1.5 bg-cyan-400/60 rounded-full'
    node.style.left = Math.random() * 100 + '%'
    node.style.top = Math.random() * 100 + '%'
    node.style.animationDelay = Math.random() * 4 + 's'
    node.style.animation = 'pulseNode 4s ease-in-out infinite'

    neuralContainer.value.appendChild(node)

    // 创建连接线
    if (i > 0 && Math.random() > 0.6) {
      const connection = document.createElement('div')
      connection.className = 'neural-connection absolute h-px bg-gradient-to-r from-transparent via-blue-300/20 to-transparent'
      connection.style.left = Math.random() * 100 + '%'
      connection.style.top = Math.random() * 100 + '%'
      connection.style.width = Math.random() * 150 + 50 + 'px'
      connection.style.transform = `rotate(${Math.random() * 360}deg)`
      connection.style.animationDelay = Math.random() * 3 + 's'
      connection.style.animation = 'neuralPulse 3s ease-in-out infinite'

      neuralContainer.value.appendChild(connection)
    }
  }
}

// 检查登录状态
const checkLoginStatus = () => {
  // 如果已经登录，重定向到首页
  if (authStore.isLoggedIn) {
    const redirectPath = (route.query.redirect as string) || '/'
    router.push(redirectPath)
  }
}

// 组件挂载时初始化效果
onMounted(() => {
  // 检查登录状态
  checkLoginStatus()

  createGridBackground()
  createParticles()
  createNeuralNetwork()

  // 添加CSS动画样式
  const style = document.createElement('style')
  style.textContent = `
    @keyframes particleFloat {
      0% {
        transform: translateY(100vh) translateX(0) scale(0);
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateY(-100vh) translateX(${Math.random() * 200 - 100}px) scale(1);
        opacity: 0;
      }
    }

    @keyframes pulseNode {
      0%, 100% {
        transform: scale(1);
        opacity: 0.4;
      }
      50% {
        transform: scale(1.8);
        opacity: 1;
      }
    }

    @keyframes neuralPulse {
      0%, 100% {
        opacity: 0.2;
        transform: scaleX(1);
      }
      50% {
        opacity: 0.8;
        transform: scaleX(1.5);
      }
    }

    @keyframes gridPulse {
      0%, 100% {
        opacity: 0.1;
      }
      50% {
        opacity: 0.3;
      }
    }

    .animate-pulse-slow {
      animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
  `
  document.head.appendChild(style)
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理动态创建的样式和元素
  const styles = document.querySelectorAll('style')
  styles.forEach(style => {
    if (style.textContent?.includes('particleFloat') ||
        style.textContent?.includes('pulseNode') ||
        style.textContent?.includes('neuralPulse')) {
      style.remove()
    }
  })
})
</script>

<style scoped>
/* 登录按钮悬停效果 */
.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

/* 自定义动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.ai-showcase {
  animation: slideInLeft 1s ease-out;
}

.login-container {
  animation: fadeInUp 1s ease-out 0.3s both;
}

.stat-card {
  animation: fadeInUp 1s ease-out calc(0.5s + var(--delay, 0s)) both;
}

.stat-card:nth-child(1) { --delay: 0.1s; }
.stat-card:nth-child(2) { --delay: 0.2s; }
.stat-card:nth-child(3) { --delay: 0.3s; }
.stat-card:nth-child(4) { --delay: 0.4s; }

/* 响应式设计 */
@media (max-width: 1400px) {
  .main-content {
    max-width: calc(100vw - 2rem);
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .ai-showcase {
    padding: 60px 40px;
  }

  .login-section {
    width: 480px;
    padding: 60px 40px;
  }
}

@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
    max-width: 100vw;
    margin: 0;
  }

  .ai-showcase {
    padding: 60px 40px 40px;
    text-align: center;
    border-radius: 0;
    background: rgba(0, 0, 0, 0.15);
  }

  .login-section {
    width: 100%;
    padding: 40px;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0;
  }

  .platform-stats {
    max-width: 600px;
    margin: 0 auto 40px;
  }

  .core-features {
    max-width: 600px;
    margin: 0 auto 32px;
  }
}

@media (max-width: 768px) {
  .ai-showcase {
    padding: 40px 30px 30px;
  }

  .login-section {
    padding: 30px;
  }

  .login-container {
    padding: 30px 25px;
  }

  .platform-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .core-features {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  h1 {
    font-size: 3rem;
  }

  .platform-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0;
  }

  .ai-showcase {
    padding: 30px 20px 20px;
  }

  .login-section {
    padding: 20px;
  }

  .login-container {
    padding: 25px 20px;
    margin: 0;
  }

  h1 {
    font-size: 2.25rem;
    line-height: 1.2;
  }

  .platform-title {
    font-size: 1.5rem;
  }

  .stat-card {
    padding: 20px 16px;
  }

  .feature-card {
    padding: 16px;
  }
}

/* 深色主题优化 */
.login-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
