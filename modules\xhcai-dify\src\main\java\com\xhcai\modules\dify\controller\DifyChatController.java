package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresAuthentication;
import com.xhcai.modules.dify.dto.chat.DifyChatRequestDTO;
import com.xhcai.modules.dify.dto.chat.DifyChatResponseDTO;
import com.xhcai.modules.dify.service.IDifyChatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * Dify聊天控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Tag(name = "Dify聊天", description = "Dify聊天相关接口")
@RestController
@RequestMapping("/api/dify/chat")
public class DifyChatController {

    @Autowired
    private IDifyChatService difyChatService;

    /**
     * 发送聊天消息（阻塞模式）
     */
    @Operation(summary = "发送聊天消息（阻塞模式）")
    @PostMapping("/send")
    @RequiresAuthentication
    public Result<DifyChatResponseDTO> chat(@Valid @RequestBody DifyChatRequestDTO requestDTO) {
        DifyChatResponseDTO response = difyChatService.chat(requestDTO);
        return Result.success(response);
    }

    /**
     * 流式聊天
     */
    @Operation(summary = "流式聊天")
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @RequiresAuthentication
    public SseEmitter streamChat(@Valid @RequestBody DifyChatRequestDTO requestDTO) {
        // 强制设置为流式模式，确保接口行为一致
        requestDTO.setResponseMode("streaming");
        return difyChatService.streamChatSse(requestDTO);
    }

    /**
     * 测试接口 - 不需要认证
     */
    @Operation(summary = "测试聊天接口")
    @PostMapping("/test")
    public Result<DifyChatResponseDTO> testChat(@RequestBody TestChatRequest request) {
        DifyChatRequestDTO requestDTO = new DifyChatRequestDTO();
        requestDTO.setQuery(request.getQuery());
        requestDTO.setUser(request.getUser() != null ? request.getUser() : "test-user");
        requestDTO.setConversationId(request.getConversationId());
        
        DifyChatResponseDTO response = difyChatService.chat(requestDTO);
        return Result.success(response);
    }

    /**
     * 测试流式聊天接口 - 不需要认证
     */
    @Operation(summary = "测试流式聊天接口")
    @PostMapping(value = "/test/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testStreamChat(@RequestBody TestChatRequest request) {
        DifyChatRequestDTO requestDTO = new DifyChatRequestDTO();
        requestDTO.setQuery(request.getQuery());
        requestDTO.setUser(request.getUser() != null ? request.getUser() : "test-user");
        requestDTO.setConversationId(request.getConversationId());
        requestDTO.setResponseMode("streaming"); // 明确设置为流式模式

        return difyChatService.streamChatSse(requestDTO);
    }

    /**
     * 测试流式聊天接口 - 调试版本（直接转发所有数据）
     */
    @Operation(summary = "测试流式聊天接口-调试版")
    @PostMapping(value = "/test/debug", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testStreamChatDebug(@RequestBody TestChatRequest request) {
        DifyChatRequestDTO requestDTO = new DifyChatRequestDTO();
        requestDTO.setQuery(request.getQuery());
        requestDTO.setUser(request.getUser() != null ? request.getUser() : "test-user");
        requestDTO.setConversationId(request.getConversationId());
        requestDTO.setResponseMode("streaming");

        return difyChatService.streamChatSseDebug(requestDTO);
    }

    /**
     * 测试聊天请求
     */
    public static class TestChatRequest {
        private String query;
        private String user;
        private String conversationId;

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }
    }
}
