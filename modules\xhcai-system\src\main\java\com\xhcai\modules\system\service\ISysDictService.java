package com.xhcai.modules.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysDictQueryDTO;
import com.xhcai.modules.system.entity.SysDict;
import com.xhcai.modules.system.vo.SysDictVO;

/**
 * 字典类型服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysDictService extends IService<SysDict> {

    /**
     * 分页查询字典类型列表
     *
     * @param queryDTO 查询条件
     * @return 字典类型分页列表
     */
    PageResult<SysDictVO> selectDictPage(SysDictQueryDTO queryDTO);

    /**
     * 查询字典类型列表
     *
     * @param queryDTO 查询条件
     * @return 字典类型列表
     */
    List<SysDictVO> selectDictList(SysDictQueryDTO queryDTO);

    /**
     * 根据字典ID查询字典信息
     *
     * @param dictId 字典ID
     * @return 字典信息
     */
    SysDictVO selectDictById(String dictId);

    /**
     * 根据字典类型查询字典信息
     *
     * @param dictType 字典类型
     * @return 字典信息
     */
    SysDict selectByDictType(String dictType);

    /**
     * 创建字典类型
     *
     * @param dict 字典信息
     * @return 是否成功
     */
    boolean insertDict(SysDict dict);

    /**
     * 更新字典类型
     *
     * @param dict 字典信息
     * @return 是否成功
     */
    boolean updateDict(SysDict dict);

    /**
     * 删除字典类型
     *
     * @param dictIds 字典ID列表
     * @return 是否成功
     */
    boolean deleteDicts(List<String> dictIds);

    /**
     * 创建字典类型（如果不存在）
     */
    void createDictTypeIfNotExists(String dictType, String dictName, String remark);

    /**
     * 创建字典类型（如果不存在）
     */
    void createDictTypeIfNotExists(String dictType, String dictName, String remark, String isSystemDict);

    /**
     * 检查字典类型是否存在
     *
     * @param dictType 字典类型
     * @param excludeId 排除的字典ID
     * @return 是否存在
     */
    boolean existsDictType(String dictType, String excludeId);

    /**
     * 批量更新字典状态
     *
     * @param dictIds 字典ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> dictIds, String status);

    /**
     * 获取所有字典类型
     *
     * @return 字典类型列表
     */
    List<String> selectAllDictTypes();

    /**
     * 刷新字典缓存
     */
    void refreshCache();

    /**
     * 导出字典数据
     *
     * @param queryDTO 查询条件
     * @return 字典列表
     */
    List<SysDictVO> exportDicts(SysDictQueryDTO queryDTO);

    /**
     * 导入字典数据
     *
     * @param dictList 字典列表
     * @return 导入结果
     */
    String importDicts(List<SysDict> dictList);
}
