package com.xhcai.modules.rag.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Component
@ConfigurationProperties(prefix = "xhcai.storage")
public class StorageConfig {
    @Value("${XHCAI_STORAGE_TYPE:minio}")
    private String type;
    @Value("${XHCAI_STORAGE_MINIO_ENDPOINT:localhost:9000}")
    private String endpoint;
    @Value("${XHCAI_STORAGE_MINIO_ACCESS_KEY:minioadmin}")
    private String accessKey;
    @Value("${XHCAI_STORAGE_MINIO_SECRET_KEY:minioadmin}")
    private String secretKey;
    @Value("${XHCAI_STORAGE_MINIO_BUCKETNAME:xhcai-plus}")
    private String bucketName;
}
