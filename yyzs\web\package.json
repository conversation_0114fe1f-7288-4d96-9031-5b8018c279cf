{"name": "yyzs-agent-web", "version": "1.0.0", "description": "YYZS Agent Platform Frontend - React + Next.js + TailwindCSS", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0", "react-query": "^3.39.3", "react-hook-form": "^7.47.0", "react-router-dom": "^6.18.0", "lucide-react": "^0.292.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "react-table": "^7.8.0", "framer-motion": "^10.16.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-table": "^7.7.0", "typescript": "^5.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}