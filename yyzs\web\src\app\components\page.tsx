'use client';

import { useState, useEffect } from 'react';
import { 
  Server, 
  Upload, 
  Activity, 
  Settings,
  Home,
  Menu,
  X
} from 'lucide-react';
import { ElasticComponent } from '@/types/component';
import ComponentList from '@/components/ComponentList';
import ComponentDetail from '@/components/ComponentDetail';
import ComponentUpload from '@/components/ComponentUpload';
import MonitorDashboard from '@/components/MonitorDashboard';
import toast, { Toaster } from 'react-hot-toast';

type ViewType = 'dashboard' | 'components' | 'monitor' | 'upload';

export default function ComponentsPage() {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard');
  const [selectedComponent, setSelectedComponent] = useState<ElasticComponent | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { id: 'dashboard', name: '仪表板', icon: Home, view: 'dashboard' as ViewType },
    { id: 'components', name: '组件管理', icon: Server, view: 'components' as ViewType },
    { id: 'monitor', name: '监控中心', icon: Activity, view: 'monitor' as ViewType },
    { id: 'upload', name: '上传组件', icon: Upload, view: 'upload' as ViewType },
  ];

  const handleComponentSelect = (component: ElasticComponent) => {
    setSelectedComponent(component);
  };

  const handleBackToList = () => {
    setSelectedComponent(null);
  };

  const handleUploadSuccess = (componentId: string) => {
    toast.success('组件上传成功！');
    setCurrentView('components');
  };

  const handleUploadClick = () => {
    setCurrentView('upload');
  };

  const handleComponentUpdate = (component: ElasticComponent) => {
    // 可以在这里更新组件状态
  };

  const renderContent = () => {
    // 如果选择了组件，显示组件详情
    if (selectedComponent) {
      return (
        <ComponentDetail
          component={selectedComponent}
          onBack={handleBackToList}
          onComponentUpdate={handleComponentUpdate}
        />
      );
    }

    // 根据当前视图渲染内容
    switch (currentView) {
      case 'dashboard':
        return <DashboardView onNavigate={setCurrentView} />;
      case 'components':
        return (
          <ComponentList
            onComponentSelect={handleComponentSelect}
            onUploadClick={handleUploadClick}
          />
        );
      case 'monitor':
        return <MonitorDashboard />;
      case 'upload':
        return (
          <ComponentUpload
            onUploadSuccess={handleUploadSuccess}
            onClose={() => setCurrentView('components')}
          />
        );
      default:
        return <ComponentList onComponentSelect={handleComponentSelect} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Server className="h-8 w-8 text-primary-600" />
            <span className="text-xl font-bold text-gray-900">YYZS Agent</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.view && !selectedComponent;
              
              return (
                <button
                  key={item.id}
                  onClick={() => {
                    setCurrentView(item.view);
                    setSelectedComponent(null);
                    setSidebarOpen(false);
                  }}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    isActive
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </button>
              );
            })}
          </div>
        </nav>

        {/* Sidebar Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            YYZS Agent Platform v1.0.0
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-gray-400 hover:text-gray-600"
            >
              <Menu className="h-6 w-6" />
            </button>

            <div className="flex items-center space-x-4">
              {selectedComponent && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Server className="h-4 w-4" />
                  <span>{selectedComponent.name}</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <button className="text-gray-400 hover:text-gray-600">
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-4 sm:p-6 lg:p-8">
          {renderContent()}
        </main>
      </div>

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#10B981',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#EF4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </div>
  );
}

// Dashboard View Component
interface DashboardViewProps {
  onNavigate: (view: ViewType) => void;
}

function DashboardView({ onNavigate }: DashboardViewProps) {
  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          欢迎使用 YYZS Agent Platform
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          专业的 Elastic Stack 组件管理平台，提供组件安装、配置、监控和运维的完整解决方案
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          onClick={() => onNavigate('upload')}
          className="card hover:shadow-medium transition-shadow cursor-pointer"
        >
          <div className="card-body text-center">
            <Upload className="h-12 w-12 text-primary-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">上传组件</h3>
            <p className="text-gray-600">上传新的组件安装包到平台</p>
          </div>
        </div>

        <div 
          onClick={() => onNavigate('components')}
          className="card hover:shadow-medium transition-shadow cursor-pointer"
        >
          <div className="card-body text-center">
            <Server className="h-12 w-12 text-success-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">组件管理</h3>
            <p className="text-gray-600">管理已安装的组件和服务</p>
          </div>
        </div>

        <div 
          onClick={() => onNavigate('monitor')}
          className="card hover:shadow-medium transition-shadow cursor-pointer"
        >
          <div className="card-body text-center">
            <Activity className="h-12 w-12 text-warning-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">监控中心</h3>
            <p className="text-gray-600">实时监控系统和组件状态</p>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-xl font-semibold text-gray-900">平台特性</h2>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">支持的组件</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span>Filebeat - 日志文件采集</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span>Heartbeat - 服务可用性监控</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span>Metricbeat - 系统和服务指标采集</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span>Logstash - 数据处理管道</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span>Elasticsearch - 搜索和分析引擎</span>
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">核心功能</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                  <span>一键安装和配置</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                  <span>实时状态监控</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                  <span>资源使用统计</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                  <span>日志查看和管理</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                  <span>批量操作支持</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
