/**
 * 节点注册系统
 * 提供统一的节点注册、查找和管理功能
 */

import type { Component } from 'vue'
import type { NodeLibraryItem } from '../../../config/nodeLibrary'

// 节点组件类型
export type NodeComponent = Component

// 节点注册信息
export interface NodeRegistration {
  type: string
  category: string
  component: NodeComponent
  config: NodeLibraryItem
}

// 节点类别注册信息
export interface NodeCategoryRegistration {
  name: string
  icon: string
  description: string
  order?: number
}

/**
 * 节点注册表类
 */
class NodeRegistryClass {
  private nodes = new Map<string, NodeRegistration>()
  private categories = new Map<string, NodeCategoryRegistration>()
  private nodesByCategory = new Map<string, string[]>()

  /**
   * 注册节点类别
   */
  registerCategory(category: NodeCategoryRegistration): void {
    this.categories.set(category.name, category)
    if (!this.nodesByCategory.has(category.name)) {
      this.nodesByCategory.set(category.name, [])
    }
  }

  /**
   * 注册节点
   */
  registerNode(registration: NodeRegistration): void {
    // 检查类别是否存在
    if (!this.categories.has(registration.category)) {
      throw new Error(`Category "${registration.category}" not found. Please register the category first.`)
    }

    // 注册节点
    this.nodes.set(registration.type, registration)
    
    // 添加到类别映射
    const categoryNodes = this.nodesByCategory.get(registration.category) || []
    if (!categoryNodes.includes(registration.type)) {
      categoryNodes.push(registration.type)
      this.nodesByCategory.set(registration.category, categoryNodes)
    }
  }

  /**
   * 批量注册节点
   */
  registerNodes(registrations: NodeRegistration[]): void {
    registrations.forEach(registration => this.registerNode(registration))
  }

  /**
   * 获取节点注册信息
   */
  getNode(type: string): NodeRegistration | undefined {
    return this.nodes.get(type)
  }

  /**
   * 获取节点组件
   */
  getNodeComponent(type: string): NodeComponent | undefined {
    const registration = this.nodes.get(type)
    return registration?.component
  }

  /**
   * 获取节点配置
   */
  getNodeConfig(type: string): NodeLibraryItem | undefined {
    const registration = this.nodes.get(type)
    return registration?.config
  }

  /**
   * 获取所有节点类型
   */
  getAllNodeTypes(): string[] {
    return Array.from(this.nodes.keys())
  }

  /**
   * 获取类别下的所有节点
   */
  getNodesByCategory(category: string): string[] {
    return this.nodesByCategory.get(category) || []
  }

  /**
   * 获取所有类别
   */
  getAllCategories(): NodeCategoryRegistration[] {
    return Array.from(this.categories.values()).sort((a, b) => (a.order || 0) - (b.order || 0))
  }

  /**
   * 获取类别信息
   */
  getCategory(name: string): NodeCategoryRegistration | undefined {
    return this.categories.get(name)
  }

  /**
   * 检查节点是否存在
   */
  hasNode(type: string): boolean {
    return this.nodes.has(type)
  }

  /**
   * 检查类别是否存在
   */
  hasCategory(name: string): boolean {
    return this.categories.has(name)
  }

  /**
   * 取消注册节点
   */
  unregisterNode(type: string): boolean {
    const registration = this.nodes.get(type)
    if (!registration) {
      return false
    }

    // 从节点映射中删除
    this.nodes.delete(type)

    // 从类别映射中删除
    const categoryNodes = this.nodesByCategory.get(registration.category)
    if (categoryNodes) {
      const index = categoryNodes.indexOf(type)
      if (index > -1) {
        categoryNodes.splice(index, 1)
      }
    }

    return true
  }

  /**
   * 取消注册类别（会同时删除该类别下的所有节点）
   */
  unregisterCategory(name: string): boolean {
    if (!this.categories.has(name)) {
      return false
    }

    // 删除该类别下的所有节点
    const categoryNodes = this.nodesByCategory.get(name) || []
    categoryNodes.forEach(nodeType => {
      this.nodes.delete(nodeType)
    })

    // 删除类别
    this.categories.delete(name)
    this.nodesByCategory.delete(name)

    return true
  }

  /**
   * 清空所有注册信息
   */
  clear(): void {
    this.nodes.clear()
    this.categories.clear()
    this.nodesByCategory.clear()
  }

  /**
   * 获取注册统计信息
   */
  getStats(): {
    totalNodes: number
    totalCategories: number
    nodesByCategory: Record<string, number>
  } {
    const nodesByCategory: Record<string, number> = {}
    this.nodesByCategory.forEach((nodes, category) => {
      nodesByCategory[category] = nodes.length
    })

    return {
      totalNodes: this.nodes.size,
      totalCategories: this.categories.size,
      nodesByCategory
    }
  }
}

// 导出单例实例
export const NodeRegistry = new NodeRegistryClass()

// 导出类型
export type { NodeRegistryClass }
