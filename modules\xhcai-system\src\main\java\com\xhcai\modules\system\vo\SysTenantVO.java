package com.xhcai.modules.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户信息VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "租户信息VO")
public class SysTenantVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID", example = "1")
    private String id;

    /**
     * 租户编码
     */
    @Schema(description = "租户编码", example = "TENANT001")
    private String tenantCode;

    /**
     * 租户名称
     */
    @Schema(description = "租户名称", example = "示例企业")
    private String tenantName;

    /**
     * 租户简称
     */
    @Schema(description = "租户简称", example = "示例")
    private String tenantShortName;

    /**
     * 联系人
     */
    @Schema(description = "联系人", example = "张三")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    /**
     * 租户地址
     */
    @Schema(description = "租户地址")
    private String address;

    /**
     * 租户LOGO
     */
    @Schema(description = "租户LOGO")
    private String logo;

    /**
     * 租户域名
     */
    @Schema(description = "租户域名", example = "tenant.example.com")
    private String domain;

    /**
     * 租户状态
     */
    @Schema(description = "租户状态", example = "0")
    private String status;

    /**
     * 租户状态名称
     */
    @Schema(description = "租户状态名称", example = "正常")
    private String statusName;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 用户数量限制
     */
    @Schema(description = "用户数量限制", example = "100")
    private Integer userLimit;

    /**
     * 当前用户数量
     */
    @Schema(description = "当前用户数量", example = "10")
    private Integer currentUserCount;

    /**
     * 存储空间限制（MB）
     */
    @Schema(description = "存储空间限制（MB）", example = "1024")
    private Long storageLimit;

    /**
     * 已使用存储空间（MB）
     */
    @Schema(description = "已使用存储空间（MB）", example = "256")
    private Long usedStorage;

    /**
     * 租户描述
     */
    @Schema(description = "租户描述")
    private String description;

    /**
     * 是否过期
     */
    @Schema(description = "是否过期")
    private Boolean isExpired;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getTenantShortName() {
        return tenantShortName;
    }

    public void setTenantShortName(String tenantShortName) {
        this.tenantShortName = tenantShortName;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getUserLimit() {
        return userLimit;
    }

    public void setUserLimit(Integer userLimit) {
        this.userLimit = userLimit;
    }

    public Integer getCurrentUserCount() {
        return currentUserCount;
    }

    public void setCurrentUserCount(Integer currentUserCount) {
        this.currentUserCount = currentUserCount;
    }

    public Long getStorageLimit() {
        return storageLimit;
    }

    public void setStorageLimit(Long storageLimit) {
        this.storageLimit = storageLimit;
    }

    public Long getUsedStorage() {
        return usedStorage;
    }

    public void setUsedStorage(Long usedStorage) {
        this.usedStorage = usedStorage;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsExpired() {
        return isExpired;
    }

    public void setIsExpired(Boolean isExpired) {
        this.isExpired = isExpired;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "SysTenantVO{" +
                "id=" + id +
                ", tenantCode='" + tenantCode + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", tenantShortName='" + tenantShortName + '\'' +
                ", contactPerson='" + contactPerson + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", address='" + address + '\'' +
                ", logo='" + logo + '\'' +
                ", domain='" + domain + '\'' +
                ", status='" + status + '\'' +
                ", statusName='" + statusName + '\'' +
                ", expireTime=" + expireTime +
                ", userLimit=" + userLimit +
                ", currentUserCount=" + currentUserCount +
                ", storageLimit=" + storageLimit +
                ", usedStorage=" + usedStorage +
                ", description='" + description + '\'' +
                ", isExpired=" + isExpired +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
