<template>
  <div class="process-complete-step">
    <div class="step-header">
      <div class="success-icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <h3 class="step-title">处理完成</h3>
      <p class="step-description">
        文档处理已完成，您可以查看处理结果或重新开始处理流程。
      </p>
    </div>

    <div v-if="configStore.processResult" class="result-content">
      <!-- 处理统计 -->
      <div class="result-stats">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ configStore.processResult?.processedFiles }}</div>
              <div class="stat-label">处理文件数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-cut"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ configStore.processResult?.totalChunks }}</div>
              <div class="stat-label">生成分段数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatProcessingTime(configStore.processResult?.processingTime) }}</div>
              <div class="stat-label">处理时间</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ configStore.processResult?.config?.chunkSize }}</div>
              <div class="stat-label">分段大小</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 处理配置摘要 -->
      <div class="config-summary">
        <h4 class="summary-title">
          <i class="fas fa-info-circle"></i>
          处理配置摘要
        </h4>
        
        <div class="config-items">
          <div class="config-item">
            <span class="config-key">分段策略:</span>
            <span class="config-value">{{ getStrategyText(configStore.processResult?.config?.splitStrategy) }}</span>
          </div>

          <div class="config-item">
            <span class="config-key">分段大小:</span>
            <span class="config-value">{{ configStore.processResult?.config?.chunkSize }} 字符</span>
          </div>

          <div class="config-item">
            <span class="config-key">重叠大小:</span>
            <span class="config-value">{{ configStore.processResult?.config?.chunkOverlap }} 字符</span>
          </div>

          <div class="config-item">
            <span class="config-key">文本清洗:</span>
            <span class="config-value">
              <span v-if="configStore.processResult?.config?.removeEmptyLines" class="enabled-feature">移除空行</span>
              <span v-if="configStore.processResult?.config?.removeExtraSpaces" class="enabled-feature">移除多余空格</span>
              <span v-if="configStore.processResult?.config?.removeSpecialChars" class="enabled-feature">移除特殊字符</span>
              <span v-if="configStore.processResult?.config?.normalizeText" class="enabled-feature">文本标准化</span>
              <span v-if="!hasAnyCleaningEnabled" class="disabled-feature">未启用</span>
            </span>
          </div>

          <div class="config-item">
            <span class="config-key">最小分段长度:</span>
            <span class="config-value">{{ configStore.processResult?.config?.minChunkLength }} 字符</span>
          </div>
        </div>
      </div>

      <!-- 质量评估 -->
      <div class="quality-assessment">
        <h4 class="assessment-title">
          <i class="fas fa-chart-line"></i>
          质量评估
        </h4>
        
        <div class="quality-metrics">
          <div class="metric-item">
            <div class="metric-header">
              <span class="metric-name">分段质量</span>
              <span class="metric-score good">优秀</span>
            </div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 85%"></div>
            </div>
            <div class="metric-desc">分段边界合理，语义完整性良好</div>
          </div>
          
          <div class="metric-item">
            <div class="metric-header">
              <span class="metric-name">内容覆盖</span>
              <span class="metric-score excellent">优秀</span>
            </div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 92%"></div>
            </div>
            <div class="metric-desc">文档内容覆盖完整，信息损失极少</div>
          </div>
          
          <div class="metric-item">
            <div class="metric-header">
              <span class="metric-name">处理效率</span>
              <span class="metric-score good">良好</span>
            </div>
            <div class="metric-bar">
              <div class="metric-fill" style="width: 78%"></div>
            </div>
            <div class="metric-desc">处理速度适中，资源使用合理</div>
          </div>
        </div>
      </div>

      <!-- 建议和提示 -->
      <div class="recommendations">
        <h4 class="recommendations-title">
          <i class="fas fa-lightbulb"></i>
          优化建议
        </h4>
        
        <div class="recommendation-list">
          <div class="recommendation-item">
            <i class="fas fa-info-circle"></i>
            <div class="recommendation-content">
              <div class="recommendation-title">分段大小优化</div>
              <div class="recommendation-desc">
                当前分段大小为 {{ configStore.processResult?.config?.chunkSize }} 字符，适合大多数场景。
                如需提高检索精度，可考虑减小到 500-800 字符。
              </div>
            </div>
          </div>
          
          <div class="recommendation-item">
            <i class="fas fa-check-circle"></i>
            <div class="recommendation-content">
              <div class="recommendation-title">处理配置合理</div>
              <div class="recommendation-desc">
                当前配置已经很好地平衡了处理质量和效率，建议保持现有设置。
              </div>
            </div>
          </div>
          
          <div class="recommendation-item">
            <i class="fas fa-star"></i>
            <div class="recommendation-content">
              <div class="recommendation-title">知识库就绪</div>
              <div class="recommendation-desc">
                文档处理质量良好，可以直接用于知识库创建和智能问答。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <button class="btn btn-secondary" @click="handleRestart">
        <i class="fas fa-redo"></i>
        重新处理
      </button>
      
      <button class="btn btn-success" @click="handleComplete">
        <i class="fas fa-check"></i>
        完成创建
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineEmits } from 'vue'
import { useRagConfigStore } from '@/stores/ragConfigStore'

// 定义事件
const emit = defineEmits(['restart', 'complete'])

// 统一状态管理
const configStore = useRagConfigStore()

// 计算属性
const hasAnyCleaningEnabled = computed(() => {
  if (!configStore.processResult?.config) return false
  const config = configStore.processResult.config
  return config.removeEmptyLines || config.removeExtraSpaces ||
         config.removeSpecialChars || config.normalizeText
})

// 方法
const formatProcessingTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / 60000)
  const seconds = Math.floor((diff % 60000) / 1000)
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  }
  return `${seconds}秒`
}

const getStrategyText = (strategy: string) => {
  const strategyMap: Record<string, string> = {
    sentence: '按句子分段',
    paragraph: '按段落分段',
    fixed: '固定长度分段'
  }
  return strategyMap[strategy] || strategy
}

const handleRestart = () => {
  emit('restart')
}

const handleComplete = () => {
  emit('complete')
}
</script>

<style scoped>
.process-complete-step {
  display: flex;
  flex-direction: column;
  gap: 24px;
  text-align: center;
}

.step-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.success-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36px;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.step-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.step-description {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
  max-width: 500px;
}

/* 结果内容 */
.result-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  text-align: left;
}

/* 统计卡片 */
.result-stats {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
}

/* 配置摘要 */
.config-summary {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.summary-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.summary-title i {
  color: #3b82f6;
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.config-item:last-child {
  border-bottom: none;
}

.config-key {
  min-width: 100px;
  font-weight: 500;
  color: #6b7280;
  font-size: 14px;
}

.config-value {
  flex: 1;
  color: #374151;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.enabled-feature {
  background: #dcfce7;
  color: #166534;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.disabled-feature {
  color: #9ca3af;
  font-style: italic;
}

/* 质量评估 */
.quality-assessment {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.assessment-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.assessment-title i {
  color: #3b82f6;
}

.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.metric-score {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.metric-score.excellent {
  background: #dcfce7;
  color: #166534;
}

.metric-score.good {
  background: #dbeafe;
  color: #1e40af;
}

.metric-bar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.5s ease;
}

.metric-desc {
  font-size: 12px;
  color: #6b7280;
}

/* 建议 */
.recommendations {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.recommendations-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.recommendations-title i {
  color: #f59e0b;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #e5e7eb;
}

.recommendation-item i {
  color: #6b7280;
  margin-top: 2px;
  font-size: 14px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  margin-bottom: 4px;
}

.recommendation-desc {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
  justify-content: center;
}

.btn-secondary {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #cbd5e1;
}

.btn-secondary:hover {
  background: #e2e8f0;
  color: #475569;
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.btn-success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
  
  .config-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .config-key {
    min-width: auto;
  }
}
</style>
