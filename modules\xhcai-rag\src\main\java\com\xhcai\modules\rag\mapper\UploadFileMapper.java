package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.rag.entity.UploadFile;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 文件上传记录Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface UploadFileMapper extends BaseMapper<UploadFile> {

    /**
     * 根据数据集ID查询上传文件记录
     *
     * @param datasetId 数据集ID
     * @return 上传文件记录列表
     */
    @Select("SELECT * FROM upload_files WHERE dataset_id = #{datasetId} AND deleted = 0 ORDER BY upload_time DESC")
    List<UploadFile> selectByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 根据批次ID查询上传文件记录
     *
     * @param batchId 批次ID
     * @return 上传文件记录列表
     */
    @Select("SELECT * FROM upload_files WHERE batch_id = #{batchId} AND deleted = 0 ORDER BY upload_time DESC")
    List<UploadFile> selectByBatchId(@Param("batchId") String batchId);

    /**
     * 根据文档ID查询上传文件记录
     *
     * @param documentId 文档ID
     * @return 上传文件记录
     */
    @Select("SELECT * FROM upload_files WHERE document_id = #{documentId} AND deleted = 0 LIMIT 1")
    UploadFile selectByDocumentId(@Param("documentId") String documentId);

    /**
     * 根据文件hash查询上传文件记录
     *
     * @param fileHash 文件hash
     * @param datasetId 数据集ID
     * @return 上传文件记录
     */
    @Select("SELECT * FROM upload_files WHERE file_hash = #{fileHash} AND dataset_id = #{datasetId} AND deleted = 0 LIMIT 1")
    UploadFile selectByFileHash(@Param("fileHash") String fileHash, @Param("datasetId") String datasetId);

    /**
     * 分页查询上传文件记录
     *
     * @param page 分页参数
     * @param datasetId 数据集ID
     * @param uploadStatus 上传状态
     * @param operationType 操作类型
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT * FROM upload_files WHERE deleted = 0 " +
            "<if test='datasetId != null and datasetId != \"\"'> AND dataset_id = #{datasetId} </if>" +
            "<if test='uploadStatus != null and uploadStatus != \"\"'> AND upload_status = #{uploadStatus} </if>" +
            "<if test='operationType != null and operationType != \"\"'> AND operation_type = #{operationType} </if>" +
            "ORDER BY upload_time DESC" +
            "</script>")
    IPage<UploadFile> selectPageByCondition(Page<UploadFile> page, 
                                           @Param("datasetId") String datasetId,
                                           @Param("uploadStatus") String uploadStatus,
                                           @Param("operationType") String operationType);

    /**
     * 统计数据集的上传文件数量
     *
     * @param datasetId 数据集ID
     * @return 文件数量
     */
    @Select("SELECT COUNT(*) FROM upload_files WHERE dataset_id = #{datasetId} AND deleted = 0")
    Long countByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 统计数据集的上传文件数量（按状态）
     *
     * @param datasetId 数据集ID
     * @param uploadStatus 上传状态
     * @return 文件数量
     */
    @Select("SELECT COUNT(*) FROM upload_files WHERE dataset_id = #{datasetId} AND upload_status = #{uploadStatus} AND deleted = 0")
    Long countByDatasetIdAndStatus(@Param("datasetId") String datasetId, @Param("uploadStatus") String uploadStatus);

    /**
     * 根据用户ID查询上传文件记录
     *
     * @param uploadUserId 上传用户ID
     * @return 上传文件记录列表
     */
    @Select("SELECT * FROM upload_files WHERE upload_user_id = #{uploadUserId} AND deleted = 0 ORDER BY upload_time DESC")
    List<UploadFile> selectByUploadUserId(@Param("uploadUserId") String uploadUserId);

    /**
     * 更新文档ID
     *
     * @param id 记录ID
     * @param documentId 文档ID
     * @return 更新行数
     */
    @Update("UPDATE upload_files SET document_id = #{documentId}, update_time = NOW() WHERE id = #{id}")
    int updateDocumentId(@Param("id") String id, @Param("documentId") String documentId);

    /**
     * 更新上传状态
     *
     * @param id 记录ID
     * @param uploadStatus 上传状态
     * @param errorMessage 错误信息
     * @return 更新行数
     */
    @Update("UPDATE upload_files SET upload_status = #{uploadStatus}, error_message = #{errorMessage}, update_time = NOW() WHERE id = #{id}")
    int updateUploadStatus(@Param("id") String id, @Param("uploadStatus") String uploadStatus, @Param("errorMessage") String errorMessage);

    /**
     * 软删除文件记录
     *
     * @param id 记录ID
     * @return 更新行数
     */
    @Update("UPDATE upload_files SET deleted = 1, update_time = NOW() WHERE id = #{id}")
    int softDelete(@Param("id") String id);

    /**
     * 批量软删除文件记录
     *
     * @param ids 记录ID列表
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE upload_files SET deleted = 1, update_time = NOW() WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchSoftDelete(@Param("ids") List<String> ids);
}
