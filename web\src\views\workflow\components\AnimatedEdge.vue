<template>
  <!-- 使用官方的BaseEdge组件 -->
  <BaseEdge
    :id="id"
    ref="edgeRef"
    :path="edgePath"
    :style="edgeStyle"
    :marker-end="markerEnd"
  />

  <!-- 使用EdgeLabelRenderer渲染动画元素 -->
  <EdgeLabelRenderer>
    <!-- 流动的点动画 -->
    <div
      v-for="(dot, index) in flowDots"
      :key="`dot-${index}`"
      ref="dotRefs"
      :style="{
        visibility: isFlowing ? 'visible' : 'hidden',
        position: 'absolute',
        zIndex: 1,
        offsetPath: `path('${edgePath}')`,
        offsetRotate: '0deg',
        offsetAnchor: 'center',
        animationDelay: `${index * dotDelay}s`,
      }"
      class="flow-dot"
      :class="{ 'flow-active': isFlowing }"
    >
      <div
        class="dot-circle"
        :style="{
          backgroundColor: dotColor,
          width: `${dotRadius * 2}px`,
          height: `${dotRadius * 2}px`
        }"
      ></div>
    </div>

    <!-- 数据包动画 -->
    <div
      v-if="dataPacket && isFlowing"
      ref="packetRef"
      :style="{
        visibility: isFlowing ? 'visible' : 'hidden',
        position: 'absolute',
        zIndex: 2,
        offsetPath: `path('${edgePath}')`,
        offsetRotate: 'auto',
        offsetAnchor: 'center',
      }"
      class="data-packet"
    >
      <div class="packet-container">
        <span class="packet-icon">📦</span>
        <span class="packet-label">{{ dataPacket.label || 'Data' }}</span>
      </div>
    </div>
  </EdgeLabelRenderer>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'
import { BaseEdge, EdgeLabelRenderer, Position, getSmoothStepPath } from '@vue-flow/core'

// 使用更简洁的方式定义props，接受所有可能的属性
interface EdgeProps {
  // Vue Flow 必需的props
  id: string
  source: string
  target: string
  sourceX: number
  sourceY: number
  targetX: number
  targetY: number
  sourcePosition?: string
  targetPosition?: string

  // 其他可选props
  data?: any
  markerEnd?: string
  markerStart?: string
  sourceNode?: any
  targetNode?: any
  type?: string
  updatable?: boolean
  selected?: boolean
  animated?: boolean
  label?: any
  labelStyle?: any
  labelShowBg?: boolean
  labelBgStyle?: any
  labelBgPadding?: any
  labelBgBorderRadius?: number
  events?: any
  style?: any
  sourceHandleId?: string
  targetHandleId?: string
  interactionWidth?: number

  // 自定义动画属性
  isAnimated?: boolean
  isFlowing?: boolean
  dataPacket?: any
  animationSpeed?: number
  dotCount?: number
  dotColor?: string
}

const props = withDefaults(defineProps<EdgeProps>(), {
  sourcePosition: Position.Right,
  targetPosition: Position.Left,
  data: () => ({}),
  markerEnd: 'arrowclosed',
  type: 'default',
  updatable: true,
  selected: false,
  animated: false,
  labelShowBg: false,
  labelStyle: () => ({}),
  labelBgStyle: () => ({}),
  labelBgPadding: () => [2, 4],
  labelBgBorderRadius: 2,
  events: () => ({}),
  style: () => ({}),
  interactionWidth: 20,
  isAnimated: false,
  isFlowing: false,
  dataPacket: null,
  animationSpeed: 1,
  dotCount: 3,
  dotColor: '#3b82f6'
})

// 响应式数据
const edgeRef = ref()
const dotRefs = ref<HTMLElement[]>([])
const packetRef = ref<HTMLElement>()

// 由于使用CSS动画而不是Web Animations API，这些变量用于跟踪动画状态
let dotAnimations: boolean[] = []
let packetAnimation: boolean = false

// 计算属性 - 按照官方示例使用getSmoothStepPath
const edgePath = computed(() => {
  const pathData = getSmoothStepPath({
    sourceX: props.sourceX,
    sourceY: props.sourceY,
    targetX: props.targetX,
    targetY: props.targetY,
    sourcePosition: props.sourcePosition as Position,
    targetPosition: props.targetPosition as Position
  })
  return pathData[0] // getSmoothStepPath返回数组，第一个元素是path字符串
})

const edgeStyle = computed(() => ({
  stroke: props.isFlowing ? '#3b82f6' : '#b1b1b7',
  strokeWidth: props.isFlowing ? 3 : 2,
  strokeDasharray: props.isAnimated && !props.isFlowing ? '5,5' : 'none',
  opacity: props.isFlowing ? 1 : 0.7,
  transition: 'all 0.3s ease'
}))

const flowDots = computed(() => {
  return Array.from({ length: props.dotCount }, (_, index) => index)
})

const animationDuration = computed(() => {
  return 2 / props.animationSpeed
})

const dotDelay = computed(() => {
  return animationDuration.value / props.dotCount
})

const dotRadius = computed(() => {
  return props.isFlowing ? 4 : 2
})

// 动画函数
function runDotAnimations() {
  if (!props.isFlowing || !edgePath.value) return

  // 清除之前的动画
  stopDotAnimations()

  nextTick(() => {
    dotRefs.value.forEach((dotEl, index) => {
      if (!dotEl) return

      // 使用CSS动画而不是Web Animations API
      dotEl.style.animation = `flowDot ${animationDuration.value}s linear ${index * dotDelay.value}s infinite`
      dotAnimations[index] = true
    })
  })
}

function stopDotAnimations() {
  dotRefs.value.forEach((dotEl, index) => {
    if (dotEl && dotAnimations[index]) {
      dotEl.style.animation = ''
      dotAnimations[index] = false
    }
  })
}

function runPacketAnimation() {
  if (!props.isFlowing || !props.dataPacket || !edgePath.value) return

  // 清除之前的动画
  stopPacketAnimation()

  nextTick(() => {
    const packetEl = packetRef.value
    if (!packetEl) return

    // 使用CSS动画
    packetEl.style.animation = `flowPacket ${animationDuration.value * 1.5}s ease-in-out infinite`
    packetAnimation = true
  })
}

function stopPacketAnimation() {
  const packetEl = packetRef.value
  if (packetEl && packetAnimation) {
    packetEl.style.animation = ''
    packetAnimation = false
  }
}

// 监听流动状态变化
watch(() => props.isFlowing, (newValue) => {
  if (newValue) {
    runDotAnimations()
    runPacketAnimation()
  } else {
    // 停止动画
    stopDotAnimations()
    stopPacketAnimation()
  }
}, { immediate: true })
</script>

<style scoped>
.flow-dot {
  pointer-events: none;
}

.dot-circle {
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(59, 130, 246, 0.6);
}

.data-packet {
  pointer-events: none;
}

.packet-container {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(16, 185, 129, 0.9);
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transform: scaleX(-1); /* 翻转以匹配方向 */
}

.packet-icon {
  font-size: 14px;
}

.packet-label {
  white-space: nowrap;
}

/* CSS动画关键帧 */
@keyframes flowDot {
  from {
    offset-distance: 0%;
  }
  to {
    offset-distance: 100%;
  }
}

@keyframes flowPacket {
  from {
    offset-distance: 0%;
  }
  to {
    offset-distance: 100%;
  }
}
</style>
