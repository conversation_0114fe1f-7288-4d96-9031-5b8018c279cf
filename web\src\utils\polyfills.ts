/**
 * JavaScript Polyfills
 * 为旧版浏览器提供现代JavaScript特性的兼容性支持
 */

/**
 * Promise polyfill for IE11
 */
if (!window.Promise) {
  // 简单的Promise polyfill实现
  const PromisePolyfill = class Promise<T> {
    private state: 'pending' | 'fulfilled' | 'rejected' = 'pending'
    private value: T | any = undefined
    private handlers: Array<{
      onFulfilled?: (value: T) => any
      onRejected?: (reason: any) => any
      resolve: (value: any) => void
      reject: (reason: any) => void
    }> = []

    constructor(executor: (resolve: (value: T) => void, reject: (reason: any) => void) => void) {
      try {
        executor(this.resolve.bind(this), this.reject.bind(this))
      } catch (error) {
        this.reject(error)
      }
    }

    private resolve(value: T): void {
      if (this.state === 'pending') {
        this.state = 'fulfilled'
        this.value = value
        this.handlers.forEach(handler => this.handle(handler))
        this.handlers = []
      }
    }

    private reject(reason: any): void {
      if (this.state === 'pending') {
        this.state = 'rejected'
        this.value = reason
        this.handlers.forEach(handler => this.handle(handler))
        this.handlers = []
      }
    }

    private handle(handler: any): void {
      if (this.state === 'pending') {
        this.handlers.push(handler)
      } else {
        if (this.state === 'fulfilled' && handler.onFulfilled) {
          handler.onFulfilled(this.value)
        }
        if (this.state === 'rejected' && handler.onRejected) {
          handler.onRejected(this.value)
        }
      }
    }

    then<U>(onFulfilled?: (value: T) => U, onRejected?: (reason: any) => U): Promise<U> {
      return new PromisePolyfill<U>((resolve, reject) => {
        this.handle({
          onFulfilled: (value: T) => {
            if (onFulfilled) {
              try {
                resolve(onFulfilled(value))
              } catch (error) {
                reject(error)
              }
            } else {
              resolve(value as any)
            }
          },
          onRejected: (reason: any) => {
            if (onRejected) {
              try {
                resolve(onRejected(reason))
              } catch (error) {
                reject(error)
              }
            } else {
              reject(reason)
            }
          },
          resolve,
          reject
        })
      }) as any
    }

    catch<U>(onRejected: (reason: any) => U): Promise<U> {
      return this.then(undefined, onRejected) as any
    }

    static resolve<T>(value: T): Promise<T> {
      return new PromisePolyfill<T>(resolve => resolve(value)) as any
    }

    static reject<T>(reason: any): Promise<T> {
      return new PromisePolyfill<T>((_, reject) => reject(reason)) as any
    }

    static all<T>(promises: Promise<T>[]): Promise<T[]> {
      return new PromisePolyfill<T[]>((resolve, reject) => {
        if (promises.length === 0) {
          resolve([])
          return
        }

        const results: T[] = []
        let completed = 0

        promises.forEach((promise, index) => {
          promise.then(
            value => {
              results[index] = value
              completed++
              if (completed === promises.length) {
                resolve(results)
              }
            },
            reject
          )
        })
      }) as any
    }

    // 添加缺失的静态方法以满足 PromiseConstructor 接口
    static race<T>(promises: Promise<T>[]): Promise<T> {
      return new PromisePolyfill<T>((resolve, reject) => {
        promises.forEach(promise => {
          promise.then(resolve, reject)
        })
      }) as any
    }

    static allSettled<T>(promises: Promise<T>[]): Promise<Array<{status: 'fulfilled' | 'rejected', value?: T, reason?: any}>> {
      return new PromisePolyfill<Array<{status: 'fulfilled' | 'rejected', value?: T, reason?: any}>>((resolve) => {
        if (promises.length === 0) {
          resolve([])
          return
        }

        const results: Array<{status: 'fulfilled' | 'rejected', value?: T, reason?: any}> = []
        let completed = 0

        promises.forEach((promise, index) => {
          promise.then(
            value => {
              results[index] = { status: 'fulfilled', value }
              completed++
              if (completed === promises.length) {
                resolve(results)
              }
            },
            reason => {
              results[index] = { status: 'rejected', reason }
              completed++
              if (completed === promises.length) {
                resolve(results)
              }
            }
          )
        })
      }) as any
    }

    static any<T>(promises: Promise<T>[]): Promise<T> {
      return new PromisePolyfill<T>((resolve, reject) => {
        if (promises.length === 0) {
          reject(new Error('All promises were rejected'))
          return
        }

        let rejectedCount = 0
        const errors: any[] = []

        promises.forEach((promise, index) => {
          promise.then(
            resolve,
            error => {
              errors[index] = error
              rejectedCount++
              if (rejectedCount === promises.length) {
                reject(new Error('All promises were rejected'))
              }
            }
          )
        })
      }) as any
    }
  }

  // 添加 Symbol.species 支持
  if (typeof Symbol !== 'undefined' && Symbol.species) {
    (PromisePolyfill as any)[Symbol.species] = PromisePolyfill
  }

  window.Promise = PromisePolyfill as any
}

/**
 * Object.assign polyfill for IE11
 */
if (!Object.assign) {
  Object.assign = function(target: any, ...sources: any[]) {
    if (target == null) {
      throw new TypeError('Cannot convert undefined or null to object')
    }

    const to = Object(target)

    for (let index = 0; index < sources.length; index++) {
      const nextSource = sources[index]

      if (nextSource != null) {
        for (const nextKey in nextSource) {
          if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
            to[nextKey] = nextSource[nextKey]
          }
        }
      }
    }
    return to
  }
}

/**
 * Array.from polyfill for IE11
 */
if (!Array.from) {
  Array.from = function<T>(arrayLike: ArrayLike<T>, mapFn?: (v: T, k: number) => any): any[] {
    const items = Object(arrayLike)
    const len = parseInt(items.length) || 0
    const result = new Array(len)

    for (let i = 0; i < len; i++) {
      const value = items[i]
      result[i] = mapFn ? mapFn(value, i) : value
    }

    return result
  }
}

/**
 * Array.includes polyfill for IE11
 */
if (!Array.prototype.includes) {
  Array.prototype.includes = function<T>(searchElement: T, fromIndex?: number): boolean {
    const len = this.length
    const start = Math.max(fromIndex || 0, 0)

    for (let i = start; i < len; i++) {
      if (this[i] === searchElement) {
        return true
      }
    }
    return false
  }
}

/**
 * String.includes polyfill for IE11
 */
if (!String.prototype.includes) {
  String.prototype.includes = function(search: string, start?: number): boolean {
    if (typeof start !== 'number') {
      start = 0
    }
    return this.indexOf(search, start) !== -1
  }
}

/**
 * String.startsWith polyfill for IE11
 */
if (!String.prototype.startsWith) {
  String.prototype.startsWith = function(search: string, pos?: number): boolean {
    const position = !pos || pos < 0 ? 0 : +pos
    return this.substring(position, position + search.length) === search
  }
}

/**
 * String.endsWith polyfill for IE11
 */
if (!String.prototype.endsWith) {
  String.prototype.endsWith = function(search: string, length?: number): boolean {
    if (length === undefined || length > this.length) {
      length = this.length
    }
    return this.substring(length - search.length, length) === search
  }
}

/**
 * Element.closest polyfill for IE11
 */
if (!Element.prototype.closest) {
  Element.prototype.closest = function(selector: string): Element | null {
    let element: Element | null = this
    while (element && element.nodeType === 1) {
      if (element.matches && element.matches(selector)) {
        return element
      }
      element = element.parentElement
    }
    return null
  }
}

/**
 * Element.matches polyfill for IE11
 */
if (!Element.prototype.matches) {
  Element.prototype.matches =
    (Element.prototype as any).msMatchesSelector ||
    (Element.prototype as any).webkitMatchesSelector ||
    function(this: Element, selector: string): boolean {
      const matches = (this.ownerDocument || document).querySelectorAll(selector)
      let i = matches.length
      while (--i >= 0 && matches.item(i) !== this) {}
      return i > -1
    }
}

/**
 * CustomEvent polyfill for IE11
 */
if (!window.CustomEvent) {
  window.CustomEvent = function CustomEvent<T>(event: string, params?: CustomEventInit<T>): CustomEvent<T> {
    params = params || { bubbles: false, cancelable: false, detail: undefined }
    const evt = document.createEvent('CustomEvent')
    // eslint-disable-next-line deprecation/deprecation
    // initCustomEvent is deprecated but required for IE11 compatibility
    evt.initCustomEvent(event, params.bubbles || false, params.cancelable || false, params.detail)
    return evt
  } as any
}

/**
 * requestAnimationFrame polyfill for IE9+
 */
if (!window.requestAnimationFrame) {
  let lastTime = 0
  window.requestAnimationFrame = function(callback: FrameRequestCallback): number {
    const currTime = new Date().getTime()
    const timeToCall = Math.max(0, 16 - (currTime - lastTime))
    const id = window.setTimeout(() => {
      callback(currTime + timeToCall)
    }, timeToCall)
    lastTime = currTime + timeToCall
    return id
  }
}

if (!window.cancelAnimationFrame) {
  window.cancelAnimationFrame = function(id: number): void {
    clearTimeout(id)
  }
}

/**
 * 初始化所有polyfills
 */
export function initPolyfills(): void {
  // 所有polyfills都在模块加载时自动执行
  console.log('Polyfills initialized')
}
