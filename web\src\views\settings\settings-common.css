/* 设置页面通用样式 */

/* 浅蓝色柔和主题样式 */
.form-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.form-radio {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background: #fefefe;
  color: #111827;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #111827;
}

.btn-primary {
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  color: #1a365d;
  padding: 10px 20px;
  border-radius: 8px;
  border: 1px solid rgba(127, 179, 211, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(127, 179, 211, 0.3);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #90c9e8 0%, #6ba3c7 100%);
  color: #0f2a44;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(127, 179, 211, 0.4);
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.7);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(127, 179, 211, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  color: #1e40af;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #a8d8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 13px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #bae6fd 0%, #93c5fd 100%);
  color: #1e3a8a;
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(168, 216, 240, 0.3);
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.9);
}

/* 表格中的小按钮样式 */
.table-row .btn-secondary {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #a8d8f0;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  min-width: 50px;
  text-align: center;
}

.table-row .btn-secondary:hover {
  background: linear-gradient(135deg, #bae6fd 0%, #93c5fd 100%);
  color: #1e3a8a;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(168, 216, 240, 0.3);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9);
}

.table-row .btn-primary {
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  color: #1a365d;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(127, 179, 211, 0.3);
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
  min-width: 50px;
  text-align: center;
}

.table-row .btn-primary:hover {
  background: linear-gradient(135deg, #90c9e8 0%, #6ba3c7 100%);
  color: #0f2a44;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(127, 179, 211, 0.4);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.7);
}

/* 删除按钮保持红色但增强显眼度 */
.text-red-500 {
  color: #dc2626 !important;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.text-red-500:hover {
  color: #b91c1c !important;
  background: #fef2f2;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

/* API密钥显示/隐藏按钮 */
.text-blue-500 {
  color: #1e40af !important;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.text-blue-500:hover {
  color: #1e3a8a !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

/* 表格样式 */
.table-row {
  transition: all 0.3s ease;
}

.table-row:hover {
  transform: translateX(4px);
}

.tree-row {
  transition: all 0.3s ease;
}

.tree-row:hover {
  background: linear-gradient(90deg, #f8f9ff 0%, #ffffff 100%);
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .btn-primary,
  .btn-secondary {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .form-input {
    padding: 8px 10px;
    font-size: 13px;
  }
  
  .table-row .btn-primary,
  .table-row .btn-secondary {
    padding: 3px 6px;
    font-size: 11px;
    min-width: 40px;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

/* 导航菜单样式 */
.nav-menu {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-menu-item {
  width: 100%;
  text-align: left;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.nav-menu-item.active {
  background: #eff6ff;
  color: #2563eb;
  border-color: #bfdbfe;
}

.nav-menu-item:not(.active) {
  color: #374151;
}

.nav-menu-item:not(.active):hover {
  background: #f9fafb;
}

/* 搜索框样式 */
.search-input {
  position: relative;
}

.search-input input {
  padding-left: 40px;
}

.search-input::before {
  content: '🔍';
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

/* 标签样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
}

.badge-info {
  background: #dbeafe;
  color: #1e40af;
}

.badge-warning {
  background: #fef3c7;
  color: #d97706;
}

.badge-success {
  background: #d1fae5;
  color: #059669;
}

.badge-error {
  background: #fee2e2;
  color: #dc2626;
}

/* 优先级标签 */
.priority-low {
  background: #f3f4f6;
  color: #6b7280;
}

.priority-normal {
  background: #dbeafe;
  color: #2563eb;
}

.priority-high {
  background: #fed7aa;
  color: #ea580c;
}

.priority-urgent {
  background: #fee2e2;
  color: #dc2626;
}

/* 状态标签 */
.status-active {
  background: #d1fae5;
  color: #059669;
}

.status-expired {
  background: #f3f4f6;
  color: #6b7280;
}

.status-draft {
  background: #fef3c7;
  color: #d97706;
}
