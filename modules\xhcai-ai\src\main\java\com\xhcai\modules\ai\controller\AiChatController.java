package com.xhcai.modules.ai.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresAuthentication;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.ai.dto.AiChatQueryDTO;
import com.xhcai.modules.ai.dto.AiChatRequest;
import com.xhcai.modules.ai.dto.AiChatRequestDTO;
import com.xhcai.modules.ai.dto.ConversationInfo;
import com.xhcai.modules.ai.dto.ConversationInfoResponseDTO;
import com.xhcai.modules.ai.dto.FileUploadResponseDTO;
import com.xhcai.modules.ai.dto.MessageListResponseDTO;
import com.xhcai.modules.ai.service.IAiChatService;
import com.xhcai.modules.ai.vo.AiChatRecordVO;
import com.xhcai.modules.ai.vo.AiChatResponseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * AI聊天控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "AI聊天", description = "AI聊天相关接口")
@RestController
@RequestMapping("/api/ai/chat")
public class AiChatController {

    private static final Logger log = LoggerFactory.getLogger(AiChatController.class);

    @Autowired
    private IAiChatService aiChatService;

    /**
     * 发送聊天消息
     */
    @Operation(summary = "发送聊天消息")
    @PostMapping("/send")
    @RequiresAuthentication
    public Result<AiChatResponseVO> chat(@Valid @RequestBody AiChatRequestDTO requestDTO) {
        AiChatResponseVO response = aiChatService.chat(requestDTO);
        return Result.success(response);
    }

    /**
     * 流式聊天-Dify标准格式
     */
    @Operation(summary = "流式聊天")
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @RequiresAuthentication
    public SseEmitter streamChat(@Valid @RequestBody AiChatRequest request) {
        return aiChatService.streamChat(request);
    }

    /**
     * 测试流式聊天接口 - 无需认证
     */
    @Operation(summary = "测试流式聊天接口")
    @PostMapping(value = "/test/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testStreamChat(@Valid @RequestBody AiChatRequest request) {
        return aiChatService.streamChat(request);
    }


    /**
     * 获取聊天历史记录
     */
    @Operation(summary = "获取聊天历史记录")
    @GetMapping("/history/{sessionId}")
    @RequiresAuthentication
    public Result<List<AiChatRecordVO>> getChatHistory(
            @Parameter(description = "会话ID") @PathVariable String sessionId,
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId) {
        List<AiChatRecordVO> history = aiChatService.getChatHistory(sessionId, userId);
        return Result.success(history);
    }

    /**
     * 分页查询聊天记录
     */
    @Operation(summary = "分页查询聊天记录")
    @GetMapping("/records")
    @RequiresPermissions("ai:chat:view")
    public Result<PageResult<AiChatRecordVO>> getChatRecords(@Valid AiChatQueryDTO queryDTO) {
        PageResult<AiChatRecordVO> pageResult = aiChatService.selectChatRecordPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 清空会话历史
     */
    @Operation(summary = "清空会话历史")
    @DeleteMapping("/history/{sessionId}")
    @RequiresAuthentication
    public Result<Void> clearChatHistory(
            @Parameter(description = "会话ID") @PathVariable String sessionId,
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId) {
        boolean success = aiChatService.clearChatHistory(sessionId, userId);
        return Result.status(success);
    }

    /**
     * 删除聊天记录
     */
    @Operation(summary = "删除聊天记录")
    @DeleteMapping("/records/{ids}")
    @RequiresPermissions("ai:chat:remove")
    public Result<Void> deleteChatRecords(
            @Parameter(description = "记录ID列表，多个用逗号分隔") @PathVariable String ids) {
        List<String> recordIds = List.of(ids.split(","));
        boolean success = aiChatService.deleteChatRecords(recordIds);
        return Result.status(success);
    }

    /**
     * 生成新的会话ID
     */
    @Operation(summary = "生成新的会话ID")
    @PostMapping("/session/new")
    @RequiresAuthentication
    public Result<String> generateSessionId(@Parameter(description = "用户ID") @RequestParam(required = false) String userId) {
        String sessionId = aiChatService.generateSessionId(userId);
        return Result.success(sessionId);
    }

    /**
     * 获取用户的会话列表
     */
    @Operation(summary = "获取用户的会话列表")
    @GetMapping("/sessions")
    @RequiresAuthentication
    public Result<List<String>> getUserSessions(@Parameter(description = "用户ID") @RequestParam(required = false) String userId) {
        List<String> sessions = aiChatService.getUserSessions(userId);
        return Result.success(sessions);
    }

    /**
     * 重新生成回复
     */
    @Operation(summary = "重新生成回复")
    @PostMapping("/regenerate/{recordId}")
    @RequiresAuthentication
    public Result<AiChatResponseVO> regenerateResponse(
            @Parameter(description = "记录ID") @PathVariable String recordId) {
        AiChatResponseVO response = aiChatService.regenerateResponse(recordId);
        return Result.success(response);
    }

    /**
     * 评价回复
     */
    @Operation(summary = "评价回复")
    @PostMapping("/rate")
    @RequiresAuthentication
    public Result<Void> rateResponse(@RequestBody RateRequest request) {
        boolean success = aiChatService.rateResponse(request.getRecordId(), request.getRating(), request.getFeedback());
        return Result.status(success);
    }

    /**
     * 获取聊天统计信息
     */
    @Operation(summary = "获取聊天统计信息")
    @GetMapping("/statistics")
    @RequiresAuthentication
    public Result<IAiChatService.ChatStatistics> getChatStatistics(
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId) {
        IAiChatService.ChatStatistics statistics = aiChatService.getChatStatistics(userId);
        return Result.success(statistics);
    }

    /**
     * 获取智能体会话列表-dify
     */
    @Operation(summary = "获取智能体会话列表")
    @GetMapping("/conversations/{appId}")
    @RequiresAuthentication
    public Result<List<ConversationInfoResponseDTO>> getConversations(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "限制数量，默认100") @RequestParam(required = false, defaultValue = "100") Integer limit,
            @Parameter(description = "是否只获取置顶会话，默认false") @RequestParam(required = false, defaultValue = "false") Boolean pinned) {
        List<ConversationInfo> conversations = aiChatService.getConversations(appId, limit, pinned);
        List<ConversationInfoResponseDTO> responseDTOs = conversations.stream()
                .map(ConversationInfoResponseDTO::fromConversationInfo)
                .collect(java.util.stream.Collectors.toList());
        return Result.success(responseDTOs);
    }

    /**
     * 获取会话名称-dify
     */
    @Operation(summary = "获取会话名称")
    @GetMapping("/conversations/{appId}/{conversationId}/name")
    @RequiresAuthentication
    public Result<ConversationInfoResponseDTO> getConversationName(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "会话ID") @PathVariable String conversationId,
            @Parameter(description = "智能体ID") @RequestParam(required = false) String agentId,
            @Parameter(description = "是否自动生成名称，默认true") @RequestParam(required = false, defaultValue = "true") Boolean autoGenerate) {
        ConversationInfo conversation = aiChatService.getConversationName(appId, conversationId, agentId, autoGenerate);
        ConversationInfoResponseDTO responseDTO = ConversationInfoResponseDTO.fromConversationInfo(conversation);
        return Result.success(responseDTO);
    }

    /**
     * 修改会话名称-dify
     */
    @Operation(summary = "修改会话名称")
    @PostMapping("/conversations/{appId}/{conversationId}/name")
    @RequiresAuthentication
    public Result<ConversationInfo> updateConversationName(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "会话ID") @PathVariable String conversationId,
            @Parameter(description = "新的会话名称") @RequestParam String name) {
        ConversationInfo conversation = aiChatService.updateConversationName(appId, conversationId, name);
        return Result.success(conversation);
    }

    /**
     * 删除会话记录-dify
     */
    @Operation(summary = "删除会话记录")
    @GetMapping("/conversations/{appId}/{conversationId}/delete")
    @RequiresAuthentication
    public Result<Void> deleteConversation(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "会话ID") @PathVariable String conversationId) {
        boolean success = aiChatService.deleteConversation(appId, conversationId);
        return success ? Result.success() : Result.fail("删除会话记录失败");
    }

    /**
     * 停止工作流-dify
     */
    @Operation(summary = "停止工作流")
    @PostMapping("/workflow/{appId}/{taskId}/stop")
    @RequiresAuthentication
    public Result<Void> stopWorkflow(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        boolean success = aiChatService.stopWorkflow(appId, taskId);
        return success ? Result.success() : Result.fail("停止工作流失败");
    }

    /**
     * 获取会话消息列表-dify（转换时间格式）
     */
    @Operation(summary = "获取会话消息列表")
    @GetMapping("/conversations/{appId}/{conversationId}/messages")
    @RequiresAuthentication
    public Result<MessageListResponseDTO> getConversationMessages(
            @Parameter(description = "应用ID") @PathVariable String appId,
            @Parameter(description = "会话ID") @PathVariable String conversationId,
            @Parameter(description = "限制数量，默认20") @RequestParam(required = false, defaultValue = "20") Integer limit,
            @Parameter(description = "最后一个消息ID（用于分页）") @RequestParam(required = false) String lastId) {
        Object rawMessages = aiChatService.getConversationMessagesRaw(appId, conversationId, limit, lastId);
        MessageListResponseDTO responseDTO = MessageListResponseDTO.fromRawResponse(rawMessages);
        return Result.success(responseDTO);
    }

    /**
     * 获取应用会话参数
     */
    @Operation(summary = "获取应用会话参数", description = "获取指定应用的会话配置参数")
    @GetMapping("/parameters/{appId}")
    @RequiresAuthentication
    public Result<Object> getAppParameters(
            @Parameter(description = "应用ID", required = true) @PathVariable String appId) {
        Object parameters = aiChatService.getAppParameters(appId);
        return Result.success(parameters);
    }

    /**
     * 上传文件
     */
    @Operation(summary = "上传文件", description = "上传文件到 Dify 平台并存储到 MinIO")
    @PostMapping("/upload")
    @RequiresAuthentication
    public Result<FileUploadResponseDTO> uploadFile(
            @Parameter(description = "上传的文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.fail("文件不能为空");
            }

            byte[] fileContent = file.getBytes();
            String fileName = file.getOriginalFilename();
            String contentType = file.getContentType();

            FileUploadResponseDTO uploadResponse = aiChatService.uploadFile(fileContent, fileName, contentType);
            return Result.success(uploadResponse);

        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.fail("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传远程文件
     */
    @Operation(summary = "上传远程文件", description = "通过URL上传远程文件到 Dify 平台")
    @PostMapping("/upload-remote")
    @RequiresAuthentication
    public Result<Object> uploadRemoteFile(
            @Parameter(description = "远程文件上传请求", required = true)
            @RequestBody @Valid RemoteFileUploadRequest request) {
        try {
            Object uploadResponse = aiChatService.uploadRemoteFile(request.getUrl());
            return Result.success(uploadResponse);
        } catch (Exception e) {
            log.error("远程文件上传失败", e);
            return Result.fail("远程文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 评价请求
     */
    public static class RateRequest {
        private String recordId;
        private Integer rating;
        private String feedback;

        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public Integer getRating() {
            return rating;
        }

        public void setRating(Integer rating) {
            this.rating = rating;
        }

        public String getFeedback() {
            return feedback;
        }

        public void setFeedback(String feedback) {
            this.feedback = feedback;
        }
    }

    /**
     * 远程文件上传请求
     */
    public static class RemoteFileUploadRequest {
        @Parameter(description = "远程文件URL", required = true, example = "https://img.51test.net/pd/pthsp.jpg")
        private String url;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
