package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysConfig;

/**
 * 系统配置Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    /**
     * 分页查询系统配置
     *
     * @param page 分页参数
     * @param configName 配置名称
     * @param configKey 配置键
     * @param configType 配置类型
     * @param configGroup 配置分组
     * @param status 状态
     * @param isSystem 是否系统内置
     * @param tenantId 租户ID
     * @return 配置分页列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_config "
            + "WHERE deleted = 0 "
            + "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if>"
            + "<if test='configName != null and configName != \"\"'> AND config_name LIKE CONCAT('%', #{configName}, '%') </if>"
            + "<if test='configKey != null and configKey != \"\"'> AND config_key LIKE CONCAT('%', #{configKey}, '%') </if>"
            + "<if test='configType != null and configType != \"\"'> AND config_type = #{configType} </if>"
            + "<if test='configGroup != null and configGroup != \"\"'> AND config_group = #{configGroup} </if>"
            + "<if test='status != null and status != \"\"'> AND status = #{status} </if>"
            + "<if test='isSystem != null'> AND is_system = #{isSystem} </if>"
            + "ORDER BY sort_order ASC, create_time DESC"
            + "</script>")
    IPage<SysConfig> selectConfigPage(Page<SysConfig> page,
            @Param("configName") String configName,
            @Param("configKey") String configKey,
            @Param("configType") String configType,
            @Param("configGroup") String configGroup,
            @Param("status") String status,
            @Param("isSystem") Boolean isSystem,
            @Param("tenantId") String tenantId);

    /**
     * 查询系统配置列表
     *
     * @param configName 配置名称
     * @param configKey 配置键
     * @param configType 配置类型
     * @param configGroup 配置分组
     * @param status 状态
     * @param isSystem 是否系统内置
     * @param tenantId 租户ID
     * @return 配置列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_config "
            + "WHERE deleted = 0 "
            + "<if test='tenantId != null'> AND tenant_id = #{tenantId} </if>"
            + "<if test='configName != null and configName != \"\"'> AND config_name LIKE CONCAT('%', #{configName}, '%') </if>"
            + "<if test='configKey != null and configKey != \"\"'> AND config_key LIKE CONCAT('%', #{configKey}, '%') </if>"
            + "<if test='configType != null and configType != \"\"'> AND config_type = #{configType} </if>"
            + "<if test='configGroup != null and configGroup != \"\"'> AND config_group = #{configGroup} </if>"
            + "<if test='status != null and status != \"\"'> AND status = #{status} </if>"
            + "<if test='isSystem != null'> AND is_system = #{isSystem} </if>"
            + "ORDER BY sort_order ASC, create_time DESC"
            + "</script>")
    List<SysConfig> selectConfigList(@Param("configName") String configName,
            @Param("configKey") String configKey,
            @Param("configType") String configType,
            @Param("configGroup") String configGroup,
            @Param("status") String status,
            @Param("isSystem") Boolean isSystem,
            @Param("tenantId") String tenantId);

    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @return 配置信息
     */
    @Select("SELECT * FROM sys_config WHERE config_key = #{configKey} AND tenant_id = #{tenantId} AND deleted = 0")
    SysConfig selectByConfigKey(@Param("configKey") String configKey, @Param("tenantId") String tenantId);

    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param excludeId 排除的配置ID
     * @param tenantId 租户ID
     * @return 数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_config "
            + "WHERE config_key = #{configKey} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "<if test='excludeId != null'> AND id != #{excludeId} </if>"
            + "</script>")
    Integer existsConfigKey(@Param("configKey") String configKey,
            @Param("excludeId") String excludeId,
            @Param("tenantId") String tenantId);

    /**
     * 获取配置分组列表
     *
     * @param tenantId 租户ID
     * @return 分组列表
     */
    @Select("SELECT DISTINCT config_group FROM sys_config WHERE tenant_id = #{tenantId} AND deleted = 0 AND config_group IS NOT NULL ORDER BY config_group")
    List<String> selectConfigGroups(@Param("tenantId") String tenantId);

    /**
     * 获取最大排序号
     *
     * @param tenantId 租户ID
     * @return 最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM sys_config WHERE tenant_id = #{tenantId} AND deleted = 0")
    Integer selectMaxSortOrder(@Param("tenantId") String tenantId);

    /**
     * 批量删除配置（逻辑删除）
     *
     * @param configIds 配置ID列表
     * @param tenantId 租户ID
     * @return 影响行数
     */
    @Select("<script>"
            + "UPDATE sys_config SET deleted = 1 "
            + "WHERE tenant_id = #{tenantId} AND id IN "
            + "<foreach collection='configIds' item='id' open='(' separator=',' close=')'>"
            + "#{id}"
            + "</foreach>"
            + "</script>")
    int batchDelete(@Param("configIds") List<String> configIds, @Param("tenantId") String tenantId);
}
