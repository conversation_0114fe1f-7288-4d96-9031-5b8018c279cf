package com.xhcai.modules.rag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件存储配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "file_storage_config")
@Schema(description = "文件存储配置")
@TableName("file_storage_config")
public class FileStorageConfig extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 存储配置名称
     */
    @Column(name = "name", length = 100, nullable = false)
    @Schema(description = "存储配置名称", example = "默认MinIO存储")
    @NotBlank(message = "存储配置名称不能为空")
    @Size(max = 100, message = "存储配置名称长度不能超过100个字符")
    @TableField("name")
    private String name;

    /**
     * 存储类型
     */
    @Column(name = "storage_type", length = 50, nullable = false)
    @Schema(description = "存储类型", example = "minio")
    @NotBlank(message = "存储类型不能为空")
    @Size(max = 50, message = "存储类型长度不能超过50个字符")
    @TableField("storage_type")
    private String storageType;

    /**
     * 主机地址
     */
    @Column(name = "host", length = 255)
    @Schema(description = "主机地址", example = "*************")
    @Size(max = 255, message = "主机地址长度不能超过255个字符")
    @TableField("host")
    private String host;

    /**
     * 端口号
     */
    @Column(name = "port")
    @Schema(description = "端口号", example = "9000")
    @TableField("port")
    private Integer port;

    /**
     * 访问密钥ID
     */
    @Column(name = "access_key", length = 255)
    @Schema(description = "访问密钥ID")
    @Size(max = 255, message = "访问密钥ID长度不能超过255个字符")
    @TableField("access_key")
    private String accessKey;

    /**
     * 访问密钥Secret
     */
    @Column(name = "secret_key", length = 255)
    @Schema(description = "访问密钥Secret")
    @Size(max = 255, message = "访问密钥Secret长度不能超过255个字符")
    @TableField("secret_key")
    private String secretKey;

    /**
     * 存储桶名称/根目录
     */
    @Column(name = "bucket_name", length = 100)
    @Schema(description = "存储桶名称/根目录", example = "xhcai-files")
    @Size(max = 100, message = "存储桶名称长度不能超过100个字符")
    @TableField("bucket_name")
    private String bucketName;

    /**
     * 区域
     */
    @Column(name = "region", length = 100)
    @Schema(description = "区域", example = "us-east-1")
    @Size(max = 100, message = "区域长度不能超过100个字符")
    @TableField("region")
    private String region;

    /**
     * 是否启用SSL
     */
    @Column(name = "ssl_enabled")
    @Schema(description = "是否启用SSL", example = "Y")
    @TableField("ssl_enabled")
    private String sslEnabled = "N";

    /**
     * 自定义域名
     */
    @Column(name = "custom_domain", length = 255)
    @Schema(description = "自定义域名", example = "files.example.com")
    @Size(max = 255, message = "自定义域名长度不能超过255个字符")
    @TableField("custom_domain")
    private String customDomain;

    /**
     * 连接配置JSON
     */
    @Column(name = "connection_config", columnDefinition = "TEXT")
    @Schema(description = "连接配置JSON")
    @TableField("connection_config")
    private String connectionConfig;

    /**
     * 是否为默认存储
     */
    @Column(name = "is_default", length = 1)
    @Schema(description = "是否为默认存储", example = "Y")
    @TableField("is_default")
    private String isDefault = "N";

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "0")
    @TableField("status")
    private String status = "0";

    /**
     * 描述
     */
    @Column(name = "description", length = 500)
    @Schema(description = "描述")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    @TableField("description")
    private String description;
}
