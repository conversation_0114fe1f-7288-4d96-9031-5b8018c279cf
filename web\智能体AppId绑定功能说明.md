# 智能体AppId绑定功能实现说明

## 功能概述

本次更新实现了AI探索模块中智能体与特定appId的绑定功能，支持：
- 通用助手绑定appId：`3778735c-cc1a-41e3-988f-7a108e3eafb0`
- 编程助手绑定appId：`86109d3f-ef91-49c5-9ab4-c2fbc646f44b`

## 实现的功能

### 1. 智能体数据结构扩展
- 在 `AgentItem` 接口中添加了 `appId` 字段
- 为所有智能体配置了对应的appId
- 技术相关智能体使用编程助手appId
- 其他智能体使用通用助手appId

### 2. 动态AppId传递
- `AIExplore.vue` 根据选择的智能体动态获取appId
- `ExploreDialogModal.vue` 接收并使用对应的appId
- 流式聊天API调用时使用正确的appId

### 3. 快速选择功能
- 在欢迎页面添加了快速选择按钮
- 支持一键选择通用助手或编程助手
- 选择后自动设置对应的智能体和appId

### 4. 测试页面
- 创建了 `/agent-test` 测试页面
- 可以验证智能体选择和appId绑定功能
- 实时显示当前选择的智能体信息

## 文件修改清单

### 核心文件
1. `web/src/stores/modelStore.ts`
   - 添加 `appId` 字段到 `AgentItem` 接口
   - 为所有智能体配置对应的appId

2. `web/src/views/AIExplore.vue`
   - 添加 `currentAppId` 计算属性
   - 修改探索对话框传递动态appId
   - 更新流式聊天API调用使用动态appId

3. `web/src/components/WelcomePage.vue`
   - 添加快速选择智能体按钮
   - 实现 `handleQuickSelectAgent` 方法

### 测试文件
4. `web/src/views/AgentTest.vue`
   - 新建测试页面
   - 验证智能体选择和appId绑定功能

5. `web/src/router/index.ts`
   - 添加测试页面路由

## AppId分配策略

### 通用助手 (3778735c-cc1a-41e3-988f-7a108e3eafb0)
- 通用助手
- 内容创作
- 翻译专家
- 商业分析师
- HR助手
- 个人智能体（我的专属助手、学习伙伴、生活规划师）
- 辩论大师
- 创意天才
- AI顾问
- 战略大师
- 创新领袖

### 编程助手 (86109d3f-ef91-49c5-9ab4-c2fbc646f44b)
- 编程助手
- 技术专家
- 数据分析师
- 代码挑战者
- 技术架构师

## 使用方法

### 1. 在主页面选择智能体
1. 访问AI探索页面
2. 在欢迎页面点击"通用助手"或"编程助手"快速选择
3. 或通过智能体选择器选择具体的智能体
4. 点击"探索对话"按钮开始对话

### 2. 测试功能
1. 访问 `/agent-test` 页面
2. 选择不同的智能体
3. 查看当前选择的智能体信息和对应的appId
4. 点击"打开探索对话"测试功能

## 技术实现细节

### 1. 响应式AppId获取
```typescript
const currentAppId = computed(() => {
  if (selectedModelInfo.value?.type === 'agent' && selectedModelInfo.value.appId) {
    return selectedModelInfo.value.appId
  }
  return '3778735c-cc1a-41e3-988f-7a108e3eafb0' // 默认通用助手AppId
})
```

### 2. 流式聊天API调用
```typescript
await exploreApi.streamChat({
  // ... 其他参数
  appId: currentAppId.value
}, callbacks)
```

### 3. 智能体数据结构
```typescript
export interface AgentItem {
  id: string
  name: string
  type: 'agent'
  description: string
  unit: string
  designer?: string
  tags: string[]
  appId: string // 新增字段
}
```

## 注意事项

1. 确保选择智能体后再进行探索对话
2. 不同智能体会使用不同的appId调用对应的AI服务
3. 测试页面仅用于开发测试，生产环境可以移除
4. 如需添加新的智能体，记得配置对应的appId

## 后续扩展

1. 可以为每个智能体配置独立的appId
2. 支持动态配置appId（通过后端接口）
3. 添加appId配置管理界面
4. 支持智能体的更多个性化配置
