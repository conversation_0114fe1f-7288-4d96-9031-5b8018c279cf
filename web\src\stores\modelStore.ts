import { ref, computed, onMounted } from 'vue'
import { AgentsAPI } from '@/api/agents'

// 接口定义
export interface ModelItem {
  id: string
  name: string
  type: 'model'
  version?: string
  description: string
  icon?: string
  badge?: string
  badgeColor?: string
  contextLength: string
  speed: string
  cost: string
}

export interface AgentItem {
  id: string
  name: string
  type: 'agent'
  description: string
  unit: string
  designer?: string
  tags: string[]
  appId: string // 智能体对应的appId
}

export interface AgentCategory {
  id: string
  name: string
  icon: string
  color: string
  agents: AgentItem[]
}

export type SelectableItem = ModelItem | AgentItem

// 全局状态
const selectedModelId = ref<string>('gpt-4')
const selectedModelInfo = ref<SelectableItem | null>(null)
const dynamicAgents = ref<AgentItem[]>([])
const agentsLoading = ref(false)

// 模型数据
const models = ref<ModelItem[]>([
  {
    id: 'gpt-4',
    name: 'GPT-4',
    type: 'model',
    version: 'v1.0',
    description: 'OpenAI最先进的大语言模型，具有强大的推理和创作能力',
    icon: '🧠',
    badge: '推荐',
    badgeColor: 'bg-blue-100 text-blue-700',
    contextLength: '8K tokens',
    speed: '中等',
    cost: '高'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    type: 'model',
    version: 'v1.0',
    description: '快速响应的对话模型，平衡了性能和成本',
    icon: '⚡',
    badge: '快速',
    badgeColor: 'bg-green-100 text-green-700',
    contextLength: '4K tokens',
    speed: '快',
    cost: '低'
  },
  {
    id: 'claude-3',
    name: 'Claude-3',
    type: 'model',
    version: 'v3.0',
    description: 'Anthropic开发的安全可靠的AI助手',
    icon: '🛡️',
    badge: '安全',
    badgeColor: 'bg-purple-100 text-purple-700',
    contextLength: '100K tokens',
    speed: '中等',
    cost: '中等'
  },
  {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    type: 'model',
    version: 'v1.0',
    description: 'Google最新的多模态大语言模型',
    icon: '🌟',
    badge: '多模态',
    badgeColor: 'bg-orange-100 text-orange-700',
    contextLength: '32K tokens',
    speed: '快',
    cost: '中等'
  }
])

// 智能体分类数据 - 移除默认数据，完全通过API动态加载
const agentCategories = ref<AgentCategory[]>([
  // 移除所有默认智能体数据，现在完全通过API动态加载
])

// 加载AI探索智能体
const loadAiExploreAgents = async () => {
  console.log('loadAiExploreAgents 开始执行')
  try {
    agentsLoading.value = true
    console.log('开始调用 /api/agent/ai-explore 接口')
    const response = await AgentsAPI.getAiExploreAgents()
    console.log('API响应:', response)

    if (response.success && response.data) {
      // 将后端数据转换为前端AgentItem格式
      dynamicAgents.value = response.data.map((agent: any) => ({
        id: agent.id,
        name: agent.name,
        type: 'agent' as const,
        description: agent.description || '暂无描述',
        unit: '系统',
        designer: '系统',
        tags: [],
        appId: agent.appId || agent.externalAgentId || agent.id // 使用appId字段，如果没有则使用externalAgentId或id
      }))
      console.log('AI探索智能体加载成功，数量:', dynamicAgents.value.length)
    }
  } catch (error) {
    console.error('加载AI探索智能体失败:', error)
    dynamicAgents.value = []
  } finally {
    agentsLoading.value = false
    console.log('loadAiExploreAgents 执行完成')
  }
}

// 动态智能体分类
const dynamicAgentCategory = computed<AgentCategory>(() => ({
  id: 'ai-explore',
  name: 'AI探索智能体',
  icon: '🚀',
  color: 'from-indigo-500 to-purple-500',
  agents: dynamicAgents.value
}))

// 所有分类（包含动态分类）
const allCategories = computed(() => {
  const categories = [...agentCategories.value]
  // 始终显示AI探索智能体分类，即使数据未加载
  categories.unshift(dynamicAgentCategory.value) // 将AI探索智能体放在最前面
  return categories
})

// 计算属性
const allAgents = computed(() => {
  // 合并静态智能体和动态加载的智能体
  const staticAgents = agentCategories.value.flatMap(category => category.agents)
  return [...staticAgents, ...dynamicAgents.value]
})

const allItems = computed<SelectableItem[]>(() => [
  ...models.value,
  ...allAgents.value
])

const currentSelectedItem = computed(() => {
  return allItems.value.find(item => item.id === selectedModelId.value) || models.value[0]
})

// 方法
const setSelectedModel = (id: string, item?: SelectableItem) => {
  selectedModelId.value = id
  selectedModelInfo.value = item || allItems.value.find(i => i.id === id) || null
}

const getItemById = (id: string): SelectableItem | undefined => {
  return allItems.value.find(item => item.id === id)
}

// 导出
export const useModelStore = () => {
  // 初始化：如果有默认选中的模型ID但selectedModelInfo为空，则设置selectedModelInfo
  if (selectedModelId.value && !selectedModelInfo.value) {
    const defaultItem = allItems.value.find(item => item.id === selectedModelId.value)
    if (defaultItem) {
      selectedModelInfo.value = defaultItem
    }
  }

  // AI探索智能体现在按需加载，不再自动加载

  return {
    // 状态
    selectedModelId,
    selectedModelInfo,
    models,
    agentCategories,
    dynamicAgents,
    agentsLoading,

    // 计算属性
    allAgents,
    allItems,
    allCategories,
    dynamicAgentCategory,
    currentSelectedItem,

    // 方法
    setSelectedModel,
    getItemById,
    loadAiExploreAgents
  }
}
