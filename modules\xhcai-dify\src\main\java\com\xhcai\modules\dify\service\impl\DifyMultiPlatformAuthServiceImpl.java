package com.xhcai.modules.dify.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.common.security.service.LoginUser;
import com.xhcai.modules.dify.entity.ThirdPlatform;
import com.xhcai.modules.dify.entity.ThirdPlatformAccount;
import com.xhcai.modules.dify.mapper.ThirdPlatformAccountMapper;
import com.xhcai.modules.dify.mapper.ThirdPlatformMapper;
import com.xhcai.modules.dify.dto.auth.DifyApiLoginResponseDTO;
import com.xhcai.modules.dify.dto.auth.DifyLoginRequestDTO;
import com.xhcai.modules.dify.dto.auth.DifyLoginResponseDTO;
import com.xhcai.modules.dify.dto.auth.DifyLoginWrapperDTO;
import com.xhcai.modules.dify.dto.auth.DifyRefreshTokenRequestDTO;
import com.xhcai.modules.dify.dto.auth.DifyRefreshTokenResponseDTO;
import com.xhcai.modules.dify.service.IDifyMultiPlatformAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Base64;
import java.nio.charset.StandardCharsets;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.net.ConnectException;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Dify 多平台认证服务实现
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class DifyMultiPlatformAuthServiceImpl implements IDifyMultiPlatformAuthService {

    private static final Logger log = LoggerFactory.getLogger(DifyMultiPlatformAuthServiceImpl.class);

    private static final String REDIS_ACCESS_TOKEN_KEY_PREFIX = "dify:access_token:";
    private static final String REDIS_REFRESH_TOKEN_KEY_PREFIX = "dify:refresh_token:";
    private static final long TOKEN_EXPIRE_TIME = 24 * 60 * 60; // 24小时，单位：秒

    @Autowired
    private ThirdPlatformMapper thirdPlatformMapper;

    @Autowired
    private ThirdPlatformAccountMapper thirdPlatformAccountMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WebClient.Builder webClientBuilder;

    @Autowired
    private ObjectMapper objectMapper;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public Mono<String> getValidAccessToken(String userId, String platformId) {
        log.debug("获取用户{}在平台{}的有效访问令牌", userId, platformId);

        String redisKey = buildAccessTokenKey(userId, platformId);
        
        return Mono.fromCallable(() -> {
                    log.debug("从Redis获取访问令牌: {}", redisKey);
                    String accessToken = stringRedisTemplate.opsForValue().get(redisKey);
                    log.debug("从Redis获取到的访问令牌: {}", accessToken != null ? "存在" : "不存在");
                    return Optional.ofNullable(accessToken);
                })
                .flatMap(opt -> opt.map(Mono::just).orElseGet(() -> {
                    log.info("Redis中没有用户{}在平台{}的访问令牌，开始登录获取新令牌", userId, platformId);
                    return login(userId, platformId)
                            .switchIfEmpty(Mono.error(new RuntimeException("Dify登录失败：空响应")))
                            .map(loginResponse -> {
                                if (loginResponse == null || loginResponse.getAccessToken() == null) {
                                    throw new RuntimeException("无效的登录响应");
                                }
                                String token = loginResponse.getAccessToken();
                                log.info("获取到用户{}在平台{}的新访问令牌", userId, platformId);
                                return token;
                            });
                }))
                .doOnError(e -> log.error("获取用户{}在平台{}的访问令牌失败", userId, platformId, e));
    }

    @Override
    public String getValidAccessTokenSync(String userId, String platformId) {
        log.debug("同步获取用户{}在平台{}的有效访问令牌", userId, platformId);

        String redisKey = buildAccessTokenKey(userId, platformId);
        String accessToken = stringRedisTemplate.opsForValue().get(redisKey);

        if (StringUtils.hasText(accessToken)) {
            log.debug("从Redis获取到用户{}在平台{}的访问令牌", userId, platformId);
            return accessToken;
        }

        log.info("Redis中没有用户{}在平台{}的访问令牌，开始登录获取新令牌", userId, platformId);
        DifyLoginResponseDTO loginResponse = login(userId, platformId).block();
        if (loginResponse == null || loginResponse.getAccessToken() == null) {
            throw new RuntimeException("登录失败，无法获取访问令牌");
        }
        return loginResponse.getAccessToken();
    }

    @Override
    public Mono<String> getCurrentUserValidAccessToken(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(userId)) {
            return Mono.error(new RuntimeException("用户未登录"));
        }
        return getValidAccessToken(userId, platformId);
    }

    @Override
    public String getCurrentUserValidAccessTokenSync(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(userId)) {
            throw new RuntimeException("用户未登录");
        }
        return getValidAccessTokenSync(userId, platformId);
    }

    @Override
    public Mono<DifyLoginResponseDTO> login(String userId, String platformId) {
        log.info("用户{}开始登录平台{}", userId, platformId);

        return Mono.fromCallable(() -> {
                    // 获取平台配置
                    log.info("查询平台配置，platformId: {}", platformId);
                    ThirdPlatform platform = thirdPlatformMapper.selectById(platformId);
                    if (platform == null) {
                        log.error("平台配置不存在: {}", platformId);
                        throw new RuntimeException("平台配置不存在: " + platformId);
                    }
                    if (platform.getStatus() == null || !platform.getStatus().equals(1)) {
                        log.error("平台已禁用: {}, status: {}", platformId, platform.getStatus());
                        throw new RuntimeException("平台已禁用: " + platformId);
                    }
                    log.info("找到平台配置: name={}, connectionUrl={}", platform.getName(), platform.getConnectionUrl());

                    // 获取用户账号配置
                    String tenantId = SecurityUtils.getCurrentTenantId();
                    log.info("查询用户账号，userId: {}, platformId: {}, tenantId: {}", userId, platformId, tenantId);
                    ThirdPlatformAccount account = thirdPlatformAccountMapper.selectByUserAndPlatform(userId, platformId, tenantId);
                    if (account == null) {
                        log.error("用户在平台{}没有配置账号", platformId);
                        throw new RuntimeException("用户在平台" + platformId + "没有配置账号");
                    }
                    if (!StringUtils.hasText(account.getPwd())) {
                        log.error("用户在平台{}的账号没有配置密码", platformId);
                        throw new RuntimeException("用户在平台" + platformId + "的账号没有配置密码");
                    }
                    log.info("找到用户账号: accountName={}, status={}, pwd长度={}",
                            account.getAccountName(), account.getStatus(),
                            account.getPwd() != null ? account.getPwd().length() : 0);

                    return new PlatformAccountInfo(platform, account);
                })
                .flatMap(info -> {
                    // 解密密码
                    String plainPassword;
                    try {
                        // 检查密码是否是BCrypt加密的
                        if (info.account.getPwd().startsWith("$2a$") || info.account.getPwd().startsWith("$2b$") || info.account.getPwd().startsWith("$2y$")) {
                            log.error("检测到BCrypt加密密码，但BCrypt是单向加密无法解密！");
                            log.error("第三方平台账号密码不应该使用BCrypt加密，应该使用可逆加密方式");
                            log.error("当前密码: {}", info.account.getPwd());
                            throw new RuntimeException("第三方平台账号密码使用了不正确的加密方式，请联系管理员重新设置密码");
                        } else {
                            // 尝试SM4解密
                            try {
                                plainPassword = sm4Decrypt(info.account.getPwd());
                                log.info("密码SM4解密成功");
                            } catch (Exception decryptEx) {
                                log.warn("SM4解密失败，尝试使用原始密码: {}", decryptEx.getMessage());
                                // 如果解密失败，可能是明文密码
                                plainPassword = info.account.getPwd();
                                log.info("使用原始密码（可能是明文）");
                            }
                        }
                    } catch (Exception e) {
                        log.error("密码处理失败", e);
                        throw new RuntimeException("密码处理失败: " + e.getMessage(), e);
                    }

                    // 构建登录请求
                    DifyLoginRequestDTO loginRequest = new DifyLoginRequestDTO();
                    loginRequest.setEmail(info.account.getAccountName()); // 使用账号名称作为邮箱
                    loginRequest.setPassword(plainPassword); // 使用解密后的明文密码
                    loginRequest.setLanguage("zh-Hans");
                    loginRequest.setRememberMe(true);

                    // 调试日志：打印登录信息（注意：生产环境应该移除密码日志）
                    log.info("=== Dify登录调试信息 ===");
                    log.info("用户ID: {}", userId);
                    log.info("平台ID: {}", platformId);
                    log.info("平台连接URL: {}", info.platform.getConnectionUrl());
                    log.info("登录邮箱: {}", info.account.getAccountName());
                    log.info("数据库存储密码: {}", info.account.getPwd());
                    log.info("解密后密码: {}", plainPassword);
                    log.info("账号状态: {}", info.account.getStatus());
                    log.info("=== 登录调试信息结束 ===");

                    log.debug("发送用户{}在平台{}的登录请求", userId, platformId);

                    // 发送登录请求
                    WebClient webClient = webClientBuilder.baseUrl(info.platform.getConnectionUrl()).build();

                    // 打印完整的请求信息
                    log.info("=== 发送登录请求详情 ===");
                    log.info("请求URL: {}/console/api/login", info.platform.getConnectionUrl());
                    log.info("请求方法: POST");
                    log.info("请求头: Content-Type=application/json");
                    log.info("请求体: email={}, password={}, language={}, rememberMe={}",
                            loginRequest.getEmail(),
                            loginRequest.getPassword(),
                            loginRequest.getLanguage(),
                            loginRequest.getRememberMe());
                    log.info("=== 请求详情结束 ===");

                    return webClient.post()
                            .uri("/console/api/login")
                            .header("Content-Type", "application/json")
                            .bodyValue(loginRequest)
                            .retrieve()
                            .onStatus(status -> status.is4xxClientError(), response -> {
                                log.error("用户{}在平台{}登录客户端错误: {}", userId, platformId, response.statusCode());
                                return response.bodyToMono(String.class)
                                        .doOnNext(body -> {
                                            log.error("=== 登录失败详细信息 ===");
                                            log.error("HTTP状态码: {}", response.statusCode());
                                            log.error("响应体: {}", body);
                                            log.error("用户ID: {}", userId);
                                            log.error("平台ID: {}", platformId);
                                            log.error("登录邮箱: {}", loginRequest.getEmail());
                                            log.error("登录密码: {}", loginRequest.getPassword());
                                            log.error("=== 登录失败信息结束 ===");
                                        })
                                        .flatMap(body -> Mono.error(new RuntimeException("登录失败，客户端错误: " + response.statusCode() + ", " + body)));
                            })
                            .onStatus(status -> status.is5xxServerError(), response -> {
                                log.error("用户{}在平台{}登录服务器错误: {}", userId, platformId, response.statusCode());
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> Mono.error(new RuntimeException("登录失败，服务器错误: " + response.statusCode() + ", " + body)));
                            })
                            .bodyToMono(String.class)
                            .flatMap(rawResponse -> {
                                if (rawResponse == null || rawResponse.trim().isEmpty()) {
                                    return Mono.error(new RuntimeException("登录响应为空"));
                                }
                                try {
                                    DifyLoginWrapperDTO wrapperResponse = objectMapper.readValue(rawResponse, DifyLoginWrapperDTO.class);
                                    log.info("用户{}在平台{}登录成功", userId, platformId);
                                    
                                    // 保存令牌到Redis
                                    DifyLoginWrapperDTO.DifyLoginDataDTO loginData = wrapperResponse.getData();
                                    DifyLoginResponseDTO loginResponse = new DifyLoginResponseDTO();
                                    if (loginData != null) {
                                        loginResponse.setAccessToken(loginData.getAccessToken());
                                        loginResponse.setRefreshToken(loginData.getRefreshToken());
                                    }
                                    if (loginResponse != null && StringUtils.hasText(loginResponse.getAccessToken())) {
                                        String accessTokenKey = buildAccessTokenKey(userId, platformId);
                                        String refreshTokenKey = buildRefreshTokenKey(userId, platformId);
                                        
                                        stringRedisTemplate.opsForValue().set(accessTokenKey, loginResponse.getAccessToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                                        if (StringUtils.hasText(loginResponse.getRefreshToken())) {
                                            stringRedisTemplate.opsForValue().set(refreshTokenKey, loginResponse.getRefreshToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                                        }
                                        log.debug("已保存用户{}在平台{}的令牌到Redis", userId, platformId);
                                    }
                                    
                                    return Mono.just(loginResponse);
                                } catch (Exception e) {
                                    log.error("解析用户{}在平台{}的登录响应失败", userId, platformId, e);
                                    return Mono.error(new RuntimeException("解析登录响应失败: " + e.getMessage(), e));
                                }
                            })
                            .timeout(Duration.ofSeconds(30))
                            .doOnError(TimeoutException.class, e -> log.error("用户{}在平台{}登录超时", userId, platformId))
                            .doOnError(ConnectException.class, e -> log.error("无法连接到平台{}服务器", platformId))
                            .doOnError(e -> log.error("用户{}在平台{}登录失败", userId, platformId, e));
                });
    }

    @Override
    public Mono<DifyLoginResponseDTO> currentUserLogin(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(userId)) {
            return Mono.error(new RuntimeException("用户未登录"));
        }
        return login(userId, platformId);
    }

    @Override
    public Mono<DifyLoginResponseDTO> directLogin(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(userId)) {
            return Mono.error(new RuntimeException("用户未登录"));
        }

        log.info("用户{}开始直接登录平台{}（不存储token）", userId, platformId);

        return Mono.fromCallable(() -> {
                    // 获取平台配置
                    log.info("查询平台配置，platformId: {}", platformId);
                    ThirdPlatform platform = thirdPlatformMapper.selectById(platformId);
                    if (platform == null) {
                        log.error("平台配置不存在: {}", platformId);
                        throw new RuntimeException("平台配置不存在: " + platformId);
                    }
                    if (platform.getStatus() == null || !platform.getStatus().equals(1)) {
                        log.error("平台已禁用: {}, status: {}", platformId, platform.getStatus());
                        throw new RuntimeException("平台已禁用: " + platformId);
                    }
                    log.info("找到平台配置: name={}, connectionUrl={}", platform.getName(), platform.getConnectionUrl());

                    // 获取用户账号配置
                    String tenantId = SecurityUtils.getCurrentTenantId();
                    log.info("查询用户账号，userId: {}, platformId: {}, tenantId: {}", userId, platformId, tenantId);
                    ThirdPlatformAccount account = thirdPlatformAccountMapper.selectByUserAndPlatform(userId, platformId, tenantId);
                    if (account == null) {
                        log.error("用户在平台{}没有配置账号", platformId);
                        throw new RuntimeException("用户在平台" + platformId + "没有配置账号");
                    }
                    if (!StringUtils.hasText(account.getPwd())) {
                        log.error("用户在平台{}的账号没有配置密码", platformId);
                        throw new RuntimeException("用户在平台" + platformId + "的账号没有配置密码");
                    }
                    log.info("找到用户账号: accountName={}, status={}, pwd长度={}",
                            account.getAccountName(), account.getStatus(),
                            account.getPwd() != null ? account.getPwd().length() : 0);

                    return new PlatformAccountInfo(platform, account);
                })
                .flatMap(info -> {
                    // 解密密码
                    String plainPassword;
                    try {
                        // 检查密码是否是BCrypt加密的
                        if (info.account.getPwd().startsWith("$2a$") || info.account.getPwd().startsWith("$2b$") || info.account.getPwd().startsWith("$2y$")) {
                            log.error("检测到BCrypt加密密码，但BCrypt是单向加密无法解密！");
                            log.error("第三方平台账号密码不应该使用BCrypt加密，应该使用可逆加密方式");
                            log.error("当前密码: {}", info.account.getPwd());
                            throw new RuntimeException("第三方平台账号密码使用了不正确的加密方式，请联系管理员重新设置密码");
                        } else {
                            // 尝试SM4解密
                            try {
                                plainPassword = sm4Decrypt(info.account.getPwd());
                                log.info("密码SM4解密成功");
                            } catch (Exception decryptEx) {
                                log.warn("SM4解密失败，尝试使用原始密码: {}", decryptEx.getMessage());
                                // 如果解密失败，可能是明文密码
                                plainPassword = info.account.getPwd();
                                log.info("使用原始密码（可能是明文）");
                            }
                        }
                    } catch (Exception e) {
                        log.error("密码处理失败", e);
                        throw new RuntimeException("密码处理失败: " + e.getMessage(), e);
                    }

                    // 构建登录请求
                    DifyLoginRequestDTO loginRequest = new DifyLoginRequestDTO();
                    loginRequest.setEmail(info.account.getAccountName());
                    loginRequest.setPassword(plainPassword);
                    loginRequest.setLanguage("zh-Hans");
                    loginRequest.setRememberMe(true);

                    log.info("直接登录平台{}，邮箱: {}", platformId, info.account.getAccountName());

                    // 发送登录请求
                    WebClient webClient = webClientBuilder.baseUrl(info.platform.getConnectionUrl()).build();

                    return webClient.post()
                            .uri("/console/api/login")
                            .header("Content-Type", "application/json")
                            .bodyValue(loginRequest)
                            .retrieve()
                            .onStatus(status -> status.is4xxClientError(), response -> {
                                log.error("用户{}直接登录平台{}客户端错误: {}", userId, platformId, response.statusCode());
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> Mono.error(new RuntimeException("登录失败，客户端错误: " + response.statusCode() + ", " + body)));
                            })
                            .onStatus(status -> status.is5xxServerError(), response -> {
                                log.error("用户{}直接登录平台{}服务器错误: {}", userId, platformId, response.statusCode());
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> Mono.error(new RuntimeException("登录失败，服务器错误: " + response.statusCode() + ", " + body)));
                            })
                            .bodyToMono(String.class)
                            .flatMap(responseBody -> {
                                try {
                                    log.info("用户{}直接登录平台{}成功，响应: {}", userId, platformId, responseBody);

                                    // 解析Dify API的实际响应格式
                                    ObjectMapper objectMapper = new ObjectMapper();
                                    DifyApiLoginResponseDTO apiResponse = objectMapper.readValue(responseBody, DifyApiLoginResponseDTO.class);

                                    if (apiResponse == null || !"success".equals(apiResponse.getResult()) || apiResponse.getData() == null) {
                                        throw new RuntimeException("登录响应无效，result: " + (apiResponse != null ? apiResponse.getResult() : "null"));
                                    }

                                    if (!StringUtils.hasText(apiResponse.getData().getAccessToken())) {
                                        throw new RuntimeException("登录响应无效，缺少访问令牌");
                                    }

                                    // 转换为标准格式
                                    DifyLoginResponseDTO loginResponse = apiResponse.toStandardResponse();

                                    log.info("用户{}直接登录平台{}成功，获得访问令牌", userId, platformId);

                                    return Mono.just(loginResponse);
                                } catch (Exception e) {
                                    log.error("解析用户{}直接登录平台{}的响应失败", userId, platformId, e);
                                    return Mono.error(new RuntimeException("解析登录响应失败: " + e.getMessage(), e));
                                }
                            })
                            .timeout(Duration.ofSeconds(30))
                            .doOnError(TimeoutException.class, e -> log.error("用户{}直接登录平台{}超时", userId, platformId))
                            .doOnError(ConnectException.class, e -> log.error("无法连接到平台{}服务器", platformId))
                            .doOnError(e -> log.error("用户{}直接登录平台{}失败", userId, platformId, e));
                });
    }

    /**
     * 构建访问令牌的Redis键
     */
    private String buildAccessTokenKey(String userId, String platformId) {
        return REDIS_ACCESS_TOKEN_KEY_PREFIX + userId + ":" + platformId;
    }

    /**
     * 构建刷新令牌的Redis键
     */
    private String buildRefreshTokenKey(String userId, String platformId) {
        return REDIS_REFRESH_TOKEN_KEY_PREFIX + userId + ":" + platformId;
    }

    /**
     * 平台账号信息封装类
     */
    private static class PlatformAccountInfo {
        final ThirdPlatform platform;
        final ThirdPlatformAccount account;

        PlatformAccountInfo(ThirdPlatform platform, ThirdPlatformAccount account) {
            this.platform = platform;
            this.account = account;
        }
    }

    @Override
    public Mono<DifyRefreshTokenResponseDTO> refreshToken(String userId, String platformId) {
        log.info("刷新用户{}在平台{}的访问令牌", userId, platformId);

        String refreshTokenKey = buildRefreshTokenKey(userId, platformId);
        String refreshToken = stringRedisTemplate.opsForValue().get(refreshTokenKey);

        if (!StringUtils.hasText(refreshToken)) {
            log.warn("用户{}在平台{}没有刷新令牌，需要重新登录", userId, platformId);
            return login(userId, platformId).map(loginResponse -> {
                DifyRefreshTokenResponseDTO refreshResponse = new DifyRefreshTokenResponseDTO();
                refreshResponse.setResult("success");
                refreshResponse.setTokenData(loginResponse.getAccessToken(), loginResponse.getRefreshToken());
                return refreshResponse;
            });
        }

        return Mono.fromCallable(() -> {
                    ThirdPlatform platform = thirdPlatformMapper.selectById(platformId);
                    if (platform == null) {
                        throw new RuntimeException("平台配置不存在: " + platformId);
                    }
                    return platform;
                })
                .flatMap(platform -> {
                    DifyRefreshTokenRequestDTO refreshRequest = new DifyRefreshTokenRequestDTO();
                    refreshRequest.setRefreshToken(refreshToken);

                    WebClient webClient = webClientBuilder.baseUrl(platform.getConnectionUrl()).build();

                    return webClient.post()
                            .uri("/console/api/refresh-token")
                            .header("Content-Type", "application/json")
                            .bodyValue(refreshRequest)
                            .retrieve()
                            .bodyToMono(DifyRefreshTokenResponseDTO.class)
                            .doOnNext(response -> {
                                // 更新Redis中的令牌
                                if (response != null) {
                                    try {
                                        String accessToken = response.getAccessTokenFromData();
                                        if (StringUtils.hasText(accessToken)) {
                                            String accessTokenKey = buildAccessTokenKey(userId, platformId);
                                            stringRedisTemplate.opsForValue().set(accessTokenKey, accessToken, TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);

                                            String newRefreshToken = response.getRefreshTokenFromData();
                                            if (StringUtils.hasText(newRefreshToken)) {
                                                stringRedisTemplate.opsForValue().set(refreshTokenKey, newRefreshToken, TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                                            }
                                            log.debug("已更新用户{}在平台{}的令牌", userId, platformId);
                                        }
                                    } catch (Exception e) {
                                        log.warn("解析刷新令牌响应失败", e);
                                    }
                                }
                            })
                            .onErrorResume(e -> {
                                log.warn("刷新用户{}在平台{}的令牌失败，尝试重新登录", userId, platformId, e);
                                return login(userId, platformId).map(loginResponse -> {
                                    // 创建一个简化的刷新令牌响应
                                    DifyRefreshTokenResponseDTO refreshResponse = new DifyRefreshTokenResponseDTO();
                                    refreshResponse.setResult("success");
                                    refreshResponse.setTokenData(loginResponse.getAccessToken(), loginResponse.getRefreshToken());
                                    return refreshResponse;
                                });
                            });
                });
    }

    @Override
    public Mono<String> handleUnauthorized(String userId, String platformId) {
        log.info("处理用户{}在平台{}的401未授权错误", userId, platformId);
        return refreshToken(userId, platformId)
                .flatMap(response -> {
                    try {
                        String accessToken = response.getAccessTokenFromData();
                        if (StringUtils.hasText(accessToken)) {
                            log.info("用户{}在平台{}的令牌刷新成功", userId, platformId);
                            return Mono.just(accessToken);
                        } else {
                            log.warn("用户{}在平台{}的令牌刷新失败：访问令牌为空，尝试重新登录", userId, platformId);
                            return handleRelogin(userId, platformId);
                        }
                    } catch (Exception e) {
                        log.warn("用户{}在平台{}解析访问令牌失败，尝试重新登录", userId, platformId, e);
                        return handleRelogin(userId, platformId);
                    }
                })
                .onErrorResume(e -> {
                    log.warn("用户{}在平台{}的令牌刷新完全失败，尝试重新登录", userId, platformId, e);
                    return handleRelogin(userId, platformId);
                });
    }

    @Override
    public Mono<String> handleCurrentUserUnauthorized(String platformId) {
        return Mono.deferContextual(contextView -> {
            // 首先尝试从Reactor Context获取用户ID
            String userId = contextView.getOrDefault("userId", null);

            if (!StringUtils.hasText(userId)) {
                // 如果Context中没有，尝试从SecurityContext获取
                try {
                    LoginUser currentUser = SecurityUtils.getCurrentUserSafely();
                    if (currentUser != null && StringUtils.hasText(currentUser.getUserId())) {
                        userId = currentUser.getUserId();
                        log.debug("从SecurityContext获取到用户ID: {}", userId);
                    }
                } catch (Exception e) {
                    log.debug("从SecurityContext获取用户ID失败: {}", e.getMessage());
                }
            } else {
                log.debug("从Reactor Context获取到用户ID: {}", userId);
            }

            if (!StringUtils.hasText(userId)) {
                log.error("无法获取当前用户ID，既不在Reactor Context中，也不在SecurityContext中");
                return Mono.error(new RuntimeException("用户未登录或无法获取用户信息"));
            }

            return handleUnauthorized(userId, platformId);
        });
    }

    @Override
    public Mono<String> handleRelogin(String userId, String platformId) {
        log.info("处理用户{}在平台{}的600重新登录错误", userId, platformId);
        clearTokenCache(userId, platformId);
        return login(userId, platformId)
                .map(DifyLoginResponseDTO::getAccessToken);
    }

    @Override
    public Mono<String> handleCurrentUserRelogin(String platformId) {
        return Mono.deferContextual(contextView -> {
            // 首先尝试从Reactor Context获取用户ID
            String userId = contextView.getOrDefault("userId", null);

            if (!StringUtils.hasText(userId)) {
                // 如果Context中没有，尝试从SecurityContext获取
                try {
                    LoginUser currentUser = SecurityUtils.getCurrentUserSafely();
                    if (currentUser != null && StringUtils.hasText(currentUser.getUserId())) {
                        userId = currentUser.getUserId();
                        log.debug("从SecurityContext获取到用户ID: {}", userId);
                    }
                } catch (Exception e) {
                    log.debug("从SecurityContext获取用户ID失败: {}", e.getMessage());
                }
            } else {
                log.debug("从Reactor Context获取到用户ID: {}", userId);
            }

            if (!StringUtils.hasText(userId)) {
                log.error("无法获取当前用户ID，既不在Reactor Context中，也不在SecurityContext中");
                return Mono.error(new RuntimeException("用户未登录或无法获取用户信息"));
            }

            return handleRelogin(userId, platformId);
        });
    }

    @Override
    public String handleUnauthorizedSync(String userId, String platformId) {
        log.info("同步处理用户{}在平台{}的401未授权错误", userId, platformId);
        try {
            DifyRefreshTokenResponseDTO response = refreshToken(userId, platformId).block();
            if (response == null) {
                log.warn("用户{}在平台{}刷新令牌失败：响应为空，尝试重新登录", userId, platformId);
                return handleReloginSync(userId, platformId);
            }

            String accessToken = response.getAccessTokenFromData();
            if (StringUtils.hasText(accessToken)) {
                log.info("用户{}在平台{}的令牌刷新成功", userId, platformId);
                return accessToken;
            } else {
                log.warn("用户{}在平台{}的令牌刷新失败：访问令牌为空，尝试重新登录", userId, platformId);
                return handleReloginSync(userId, platformId);
            }
        } catch (Exception e) {
            log.warn("用户{}在平台{}的令牌刷新失败，尝试重新登录", userId, platformId, e);
            return handleReloginSync(userId, platformId);
        }
    }

    @Override
    public String handleReloginSync(String userId, String platformId) {
        log.info("同步处理用户{}在平台{}的600重新登录错误", userId, platformId);
        clearTokenCache(userId, platformId);
        DifyLoginResponseDTO response = login(userId, platformId).block();
        if (response == null || !StringUtils.hasText(response.getAccessToken())) {
            throw new RuntimeException("重新登录失败");
        }
        return response.getAccessToken();
    }

    @Override
    public void clearTokenCache(String userId, String platformId) {
        log.info("清除用户{}在平台{}的令牌缓存", userId, platformId);
        String accessTokenKey = buildAccessTokenKey(userId, platformId);
        String refreshTokenKey = buildRefreshTokenKey(userId, platformId);

        stringRedisTemplate.delete(accessTokenKey);
        stringRedisTemplate.delete(refreshTokenKey);
    }

    @Override
    public void clearCurrentUserTokenCache(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        if (StringUtils.hasText(userId)) {
            clearTokenCache(userId, platformId);
        }
    }

    @Override
    public boolean hasValidAccount(String userId, String platformId) {
        try {
            String tenantId = SecurityUtils.getCurrentTenantId();
            ThirdPlatformAccount account = thirdPlatformAccountMapper.selectByUserAndPlatform(userId, platformId, tenantId);
            return account != null && StringUtils.hasText(account.getPwd()) && account.getStatus() == 1;
        } catch (Exception e) {
            log.error("检查用户{}在平台{}的账号配置失败", userId, platformId, e);
            return false;
        }
    }

    @Override
    public boolean currentUserHasValidAccount(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(userId)) {
            return false;
        }
        return hasValidAccount(userId, platformId);
    }

    @Override
    public Mono<String> testConnection(String userId, String platformId) {
        log.info("测试用户{}在平台{}的连接", userId, platformId);

        return getValidAccessToken(userId, platformId)
                .flatMap(accessToken -> {
                    ThirdPlatform platform = thirdPlatformMapper.selectById(platformId);
                    if (platform == null) {
                        return Mono.error(new RuntimeException("平台配置不存在"));
                    }

                    WebClient webClient = webClientBuilder.baseUrl(platform.getConnectionUrl()).build();

                    return webClient.get()
                            .uri("/console/api/me")
                            .header("Authorization", "Bearer " + accessToken)
                            .retrieve()
                            .bodyToMono(String.class)
                            .map(response -> "连接测试成功")
                            .onErrorResume(e -> Mono.just("连接测试失败: " + e.getMessage()));
                })
                .onErrorResume(e -> Mono.just("连接测试失败: " + e.getMessage()));
    }

    @Override
    public Mono<String> testCurrentUserConnection(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(userId)) {
            return Mono.error(new RuntimeException("用户未登录"));
        }
        return testConnection(userId, platformId);
    }

    @Override
    public Map<String, Object> checkPlatformStatus(String platformId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查平台配置
            log.info("查询平台配置，platformId: {}", platformId);
            ThirdPlatform platform = thirdPlatformMapper.selectById(platformId);

            if (platform == null) {
                result.put("platformExists", false);
                result.put("error", "平台配置不存在");
                return result;
            }

            result.put("platformExists", true);
            result.put("platformId", platform.getId());
            result.put("platformName", platform.getName());
            result.put("platformStatus", platform.getStatus());
            result.put("platformStatusText", platform.getStatus() != null && platform.getStatus().equals(1) ? "启用" : "禁用");
            result.put("connectionUrl", platform.getConnectionUrl());
            result.put("createTime", platform.getCreateTime());
            result.put("updateTime", platform.getUpdateTime());

            // 检查当前用户账号配置
            String userId = SecurityUtils.getCurrentUserId();
            String tenantId = SecurityUtils.getCurrentTenantId();

            if (StringUtils.hasText(userId)) {
                log.info("查询用户账号，userId: {}, platformId: {}, tenantId: {}", userId, platformId, tenantId);
                ThirdPlatformAccount account = thirdPlatformAccountMapper.selectByUserAndPlatform(userId, platformId, tenantId);

                if (account != null) {
                    result.put("userAccountExists", true);
                    result.put("accountName", account.getAccountName());
                    result.put("accountStatus", account.getStatus());
                    result.put("accountStatusText", Integer.valueOf(1).equals(account.getStatus()) ? "启用" : "禁用");
                    result.put("hasPassword", StringUtils.hasText(account.getPwd()));
                    result.put("accountCreateTime", account.getCreateTime());
                    result.put("accountUpdateTime", account.getUpdateTime());
                } else {
                    result.put("userAccountExists", false);
                    result.put("accountError", "用户在该平台没有配置账号");
                }
            } else {
                result.put("userAccountExists", false);
                result.put("accountError", "用户未登录");
            }

            // 综合状态检查
            boolean canLogin = platform.getStatus() != null && platform.getStatus().equals(1);
            if (StringUtils.hasText(userId)) {
                ThirdPlatformAccount account = thirdPlatformAccountMapper.selectByUserAndPlatform(userId, platformId, tenantId);
                canLogin = canLogin && account != null && Integer.valueOf(1).equals(account.getStatus()) && StringUtils.hasText(account.getPwd());
            } else {
                canLogin = false;
            }

            result.put("canLogin", canLogin);
            result.put("checkTime", java.time.LocalDateTime.now());

        } catch (Exception e) {
            log.error("检查平台{}状态失败", platformId, e);
            result.put("error", "检查平台状态失败: " + e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
        }

        return result;
    }

    // SM4加密解密相关方法
    private static final String SM4_KEY = "XhcaiPlatform123"; // 16字节密钥
    private static final String SM4_ALGORITHM = "SM4/ECB/PKCS5Padding";

    static {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * SM4加密
     */
    private String sm4Encrypt(String plainText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(SM4_KEY.getBytes(StandardCharsets.UTF_8), "SM4");
            Cipher cipher = Cipher.getInstance(SM4_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            log.error("SM4加密失败", e);
            throw new RuntimeException("SM4加密失败", e);
        }
    }

    /**
     * SM4解密
     */
    private String sm4Decrypt(String encryptedText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(SM4_KEY.getBytes(StandardCharsets.UTF_8), "SM4");
            Cipher cipher = Cipher.getInstance(SM4_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("SM4解密失败", e);
            throw new RuntimeException("SM4解密失败: " + e.getMessage(), e);
        }
    }
}
