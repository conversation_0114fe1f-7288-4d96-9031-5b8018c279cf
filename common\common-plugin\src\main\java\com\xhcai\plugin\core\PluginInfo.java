package com.xhcai.plugin.core;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 插件信息
 * 包含插件的元数据信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PluginInfo {
    
    /**
     * 插件ID（唯一标识）
     */
    private String pluginId;
    
    /**
     * 插件名称
     */
    private String pluginName;
    
    /**
     * 插件版本
     */
    private String version;
    
    /**
     * 插件类型
     */
    private PluginType pluginType;
    
    /**
     * 插件描述
     */
    private String description;
    
    /**
     * 插件作者
     */
    private String author;
    
    /**
     * 插件主类
     */
    private String pluginClass;
    
    /**
     * 插件状态
     */
    private PluginStatus status;
    
    /**
     * 插件路径
     */
    private String pluginPath;
    
    /**
     * 插件配置
     */
    private Map<String, Object> configuration;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 插件依赖
     */
    private String[] dependencies;
    
    /**
     * 最小系统版本要求
     */
    private String minSystemVersion;
    
    /**
     * 插件许可证
     */
    private String license;
    
    /**
     * 插件官网
     */
    private String website;
}
