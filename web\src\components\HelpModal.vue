<template>
  <div
    v-if="visible"
    class="fixed inset-0 bg-transparent flex items-center justify-center z-[9999]"
    @click="handleClose"
    style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100vw; height: 100vh;"
  >
    <div
      class="bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-4xl mx-4 relative z-[10000] overflow-hidden"
      @click.stop
      style="min-width: 600px; max-height: 90vh;"
    >
      <!-- 头部区域 -->
      <div class="bg-gradient-to-r from-green-50 via-blue-50 to-indigo-50 px-6 py-5 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white text-xl shadow-lg">
              ❓
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-800">帮助中心</h2>
              <p class="text-sm text-gray-500 mt-1">快速了解平台功能和使用方法</p>
            </div>
          </div>
          <button
            @click="handleClose"
            class="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
            title="关闭"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="flex h-full" style="max-height: calc(90vh - 120px);">
        <!-- 左侧导航 -->
        <div class="w-64 bg-gray-50 border-r border-gray-200 overflow-y-auto">
          <div class="p-4">
            <nav class="space-y-2">
              <button
                v-for="category in helpCategories"
                :key="category.id"
                @click="activeCategory = category.id"
                class="w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                :class="{
                  'bg-blue-100 text-blue-700': activeCategory === category.id,
                  'text-gray-600 hover:bg-gray-100': activeCategory !== category.id
                }"
              >
                <span class="mr-2">{{ category.icon }}</span>
                {{ category.title }}
              </button>
            </nav>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="flex-1 overflow-y-auto">
          <div class="p-6">
            <div v-for="category in helpCategories" :key="category.id" v-show="activeCategory === category.id">
              <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <span>{{ category.icon }}</span>
                {{ category.title }}
              </h3>
              
              <div class="space-y-4">
                <div v-for="item in category.items" :key="item.id" class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-200">
                  <h4 class="font-medium text-gray-800 mb-2">{{ item.title }}</h4>
                  <p class="text-sm text-gray-600 leading-relaxed">{{ item.content }}</p>
                  <div v-if="item.steps" class="mt-3">
                    <p class="text-sm font-medium text-gray-700 mb-2">操作步骤：</p>
                    <ol class="text-sm text-gray-600 space-y-1">
                      <li v-for="(step, index) in item.steps" :key="index" class="flex gap-2">
                        <span class="text-blue-500 font-medium">{{ index + 1 }}.</span>
                        <span>{{ step }}</span>
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div class="bg-gray-50 px-6 py-4 border-t border-gray-100 flex justify-between items-center">
        <div class="text-sm text-gray-500">
          如需更多帮助，请联系客服：<EMAIL>
        </div>
        <button
          @click="handleClose"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 font-medium"
        >
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Close } from '@element-plus/icons-vue'

interface HelpItem {
  id: string
  title: string
  content: string
  steps?: string[]
}

interface HelpCategory {
  id: string
  title: string
  icon: string
  items: HelpItem[]
}

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const activeCategory = ref('getting-started')

const helpCategories = ref<HelpCategory[]>([
  {
    id: 'getting-started',
    title: '快速开始',
    icon: '🚀',
    items: [
      {
        id: 'first-conversation',
        title: '如何开始第一次AI对话？',
        content: '在AI探索页面，您可以轻松开始与AI的对话。选择合适的模型，输入您的问题即可。',
        steps: [
          '点击顶部菜单的"AI探索"或直接访问 /ai',
          '在左侧选择或创建新对话',
          '在底部输入框中输入您的问题',
          '按回车键或点击发送按钮'
        ]
      },
      {
        id: 'model-selection',
        title: '如何选择合适的AI模型？',
        content: '不同的AI模型有不同的特长，选择合适的模型可以获得更好的对话效果。',
        steps: [
          '在AI探索页面顶部找到模型选择器',
          '点击下拉菜单查看可用模型',
          '根据您的需求选择合适的模型',
          '模型会立即切换，影响后续对话'
        ]
      }
    ]
  },
  {
    id: 'agents',
    title: '智能体使用',
    icon: '🤖',
    items: [
      {
        id: 'browse-agents',
        title: '如何浏览和使用智能体？',
        content: '智能体是预配置的AI助手，专门针对特定任务进行优化。',
        steps: [
          '进入"智能体"页面',
          '浏览不同类别的智能体',
          '点击智能体卡片查看详情',
          '点击"开始对话"使用智能体'
        ]
      },
      {
        id: 'create-agent',
        title: '如何创建自定义智能体？',
        content: '您可以根据自己的需求创建专属的智能体助手。',
        steps: [
          '在智能体页面点击"创建智能体"',
          '选择智能体类型和模板',
          '配置智能体的名称、描述和参数',
          '保存并测试您的智能体'
        ]
      }
    ]
  },
  {
    id: 'knowledge',
    title: '知识库管理',
    icon: '📖',
    items: [
      {
        id: 'upload-documents',
        title: '如何上传和管理文档？',
        content: '知识库允许您上传文档，让AI能够基于您的资料回答问题。',
        steps: [
          '进入"知识库"页面',
          '点击"创建知识库"或选择现有知识库',
          '上传PDF、Word、文本等格式的文档',
          '等待文档处理完成后即可使用'
        ]
      },
      {
        id: 'search-knowledge',
        title: '如何在对话中使用知识库？',
        content: '在AI对话中，您可以让AI基于知识库中的内容回答问题。',
        steps: [
          '在AI对话中提及知识库相关内容',
          'AI会自动搜索相关文档',
          '获得基于您文档的准确回答',
          '可以要求AI引用具体的文档来源'
        ]
      }
    ]
  },
  {
    id: 'features',
    title: '功能特性',
    icon: '⚡',
    items: [
      {
        id: 'file-upload',
        title: '如何上传文件和图片？',
        content: '支持多种文件格式的上传，包括图片、文档等。',
        steps: [
          '在对话输入框左侧点击文件上传按钮',
          '选择要上传的文件类型',
          '从本地选择文件',
          '文件会显示在输入框上方，发送消息时一起提交'
        ]
      },
      {
        id: 'voice-input',
        title: '如何使用语音输入？',
        content: '支持语音录制和语音识别功能，让对话更加便捷。',
        steps: [
          '点击输入框右侧的麦克风按钮',
          '允许浏览器访问麦克风权限',
          '开始说话，系统会自动录制',
          '再次点击停止录制，语音会转换为文字'
        ]
      }
    ]
  },
  {
    id: 'troubleshooting',
    title: '常见问题',
    icon: '🔧',
    items: [
      {
        id: 'slow-response',
        title: 'AI回复速度慢怎么办？',
        content: '如果遇到AI回复缓慢的情况，可以尝试以下解决方案。',
        steps: [
          '检查网络连接是否稳定',
          '尝试刷新页面重新开始对话',
          '选择不同的AI模型',
          '如问题持续，请联系技术支持'
        ]
      },
      {
        id: 'login-issues',
        title: '登录遇到问题怎么办？',
        content: '登录问题的常见解决方法。',
        steps: [
          '确认用户名和密码是否正确',
          '清除浏览器缓存和Cookie',
          '尝试使用其他浏览器',
          '联系管理员重置密码'
        ]
      }
    ]
  }
])

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
