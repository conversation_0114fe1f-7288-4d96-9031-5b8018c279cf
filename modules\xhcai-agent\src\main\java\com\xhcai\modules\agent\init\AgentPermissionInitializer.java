package com.xhcai.modules.agent.init;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.xhcai.modules.system.service.ISysDictDataService;
import com.xhcai.modules.system.service.ISysDictService;

/**
 * 智能体模块权限初始化器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class AgentPermissionInitializer {

    private static final Logger log = LoggerFactory.getLogger(AgentPermissionInitializer.class);

    @Autowired
    private ISysDictService dictService;

    @Autowired
    private ISysDictDataService dictDataService;

    /**
     * 手动初始化智能体模块权限
     */
    public void initializeAll() {
        log.info("开始初始化智能体模块权限...");

        try {
            // 初始化字典
            initializeDictionaries();

            // 初始化权限
            initializePermissions();

            // 初始化菜单
            initializeMenus();

            // 初始化角色权限
            initializeRolePermissions();

            log.info("智能体模块权限初始化完成");
        } catch (Exception e) {
            log.error("智能体模块权限初始化失败", e);
            throw new RuntimeException("智能体模块权限初始化失败", e);
        }
    }

    /**
     * 初始化字典
     */
    private void initializeDictionaries() {
        // 初始化智能体类型字典
        initializeAgentTypeDict();
        log.debug("初始化智能体模块字典");
    }

    /**
     * 初始化第三方智能体平台字典
     */
    @Transactional(rollbackFor = Exception.class)
    public void initializeAgentTypeDict() {
        log.debug("初始化智能体类型字典");

        // 字典类型：agent_type
        String dictType = "agent_type";

        // 创建字典类型
        dictService.createDictTypeIfNotExists(dictType, "智能体类型", "智能体类型列表", "Y");

        // 字典数据
        String[][] platforms = {
            {"workflow", "工作流", "1", "🤖", "#10a37f", "Dify中工作流智能体"},
            {"advanced-chat", "Chatflow", "2", "🔍", "#ff6b35", "Dify中对话模式智能体"},
            {"chat", "聊天助手", "3", "⚡", "#3b82f6", "Dify中聊天助手智能体（新手适用）"},
            {"agent-chat", "Agent", "4", "🦜", "#1c3d5a", "Dify中Agent智能体（新手适用）"},
            {"completion", "文本生成", "5", "🦙", "#8b5cf6", "Dify中文本生成智能体（新手适用）"},};

        // 创建字典数据
        for (String[] data : platforms) {
            dictDataService.createDictDataIfNotExists(dictType, data[0], data[1], Integer.parseInt(data[2]), data[5], data[3], data[4], "Y");
        }
    }

    /**
     * 初始化权限
     */
    private void initializePermissions() {
        log.debug("初始化智能体模块权限");

        // 这里可以调用系统模块的权限服务来注册权限
        // 权限列表：
        String[] permissions = {
            // 智能体管理权限
            "agent:list", // 查看智能体列表
            "agent:detail", // 查看智能体详情
            "agent:create", // 创建智能体
            "agent:update", // 更新智能体
            "agent:delete", // 删除智能体
            "agent:publish", // 发布智能体
            "agent:export", // 导出智能体配置
            "agent:import", // 导入智能体配置
            "agent:stats", // 查看智能体统计

            // 聊天权限
            "chat:send", // 发送消息
            "chat:history", // 查看对话历史
            "chat:manage", // 管理对话
            "chat:feedback", // 提交反馈
            "chat:suggestions", // 获取建议问题
            "chat:check", // 检查智能体状态
            "chat:stats", // 查看对话统计
            "chat:admin", // 聊天管理员权限

            // 对话管理权限
            "conversation:list", // 查看对话列表
            "conversation:detail", // 查看对话详情
            "conversation:delete", // 删除对话
            "conversation:export", // 导出对话记录

            // 消息管理权限
            "message:list", // 查看消息列表
            "message:detail", // 查看消息详情
            "message:delete", // 删除消息
            "message:regenerate", // 重新生成消息

            // 工作流管理权限
            "agent:workflow:list", // 查看工作流列表
            "agent:workflow:detail", // 查看工作流详情
            "agent:workflow:create", // 创建工作流
            "agent:workflow:update", // 更新工作流
            "agent:workflow:delete", // 删除工作流
            "agent:workflow:publish", // 发布工作流
            "agent:workflow:export", // 导出工作流配置
            "agent:workflow:import", // 导入工作流配置
            "agent:workflow:version", // 工作流版本管理
            "agent:workflow:rollback", // 工作流回滚

            // 工作流历史记录权限
            "agent:workflow:history:list", // 查看历史记录列表
            "agent:workflow:history:detail", // 查看历史记录详情
            "agent:workflow:history:compare", // 对比历史记录
            "agent:workflow:history:stats", // 查看历史记录统计
            "agent:workflow:history:delete", // 删除历史记录

            // 用户第三方智能体账号管理权限
            "agent:third-platform-account:query", // 查询第三方账号
            "agent:third-platform-account:add", // 添加第三方账号
            "agent:third-platform-account:edit", // 编辑第三方账号
            "agent:third-platform-account:delete", // 删除第三方账号
            "agent:third-platform-account:test" // 测试第三方账号连接
        };

        // 注册权限到系统中
        for (String permission : permissions) {
            registerPermission(permission);
        }
    }

    /**
     * 初始化菜单
     */
    private void initializeMenus() {
        log.debug("初始化智能体模块菜单");

        // 这里可以调用系统模块的菜单服务来注册菜单
        // 菜单结构：
        // - 智能体管理
        //   - 智能体列表
        //   - 创建智能体
        //   - 智能体统计
        // - 对话管理
        //   - 对话列表
        //   - 对话统计
        // - 消息管理
        //   - 消息列表
        //   - 消息统计
        registerMenu("智能体管理", "agent", "/agent", "agent:list", 0, null);
        registerMenu("智能体列表", "agent-list", "/agent/list", "agent:list", 1, "agent");
        registerMenu("创建智能体", "agent-create", "/agent/create", "agent:create", 2, "agent");
        registerMenu("智能体统计", "agent-stats", "/agent/stats", "agent:stats", 3, "agent");

        registerMenu("对话管理", "conversation", "/conversation", "conversation:list", 10, null);
        registerMenu("对话列表", "conversation-list", "/conversation/list", "conversation:list", 11, "conversation");
        registerMenu("对话统计", "conversation-stats", "/conversation/stats", "chat:stats", 12, "conversation");

        registerMenu("消息管理", "message", "/message", "message:list", 20, null);
        registerMenu("消息列表", "message-list", "/message/list", "message:list", 21, "message");
        registerMenu("消息统计", "message-stats", "/message/stats", "chat:stats", 22, "message");

        registerMenu("工作流管理", "workflow", "/workflow", "agent:workflow:list", 30, null);
        registerMenu("工作流列表", "workflow-list", "/workflow/list", "agent:workflow:list", 31, "workflow");
        registerMenu("创建工作流", "workflow-create", "/workflow/create", "agent:workflow:create", 32, "workflow");
        registerMenu("工作流版本", "workflow-version", "/workflow/version", "agent:workflow:version", 33, "workflow");
        registerMenu("工作流历史", "workflow-history", "/workflow/history", "agent:workflow:history:list", 34, "workflow");
    }

    /**
     * 初始化角色权限
     */
    private void initializeRolePermissions() {
        log.debug("初始化智能体模块角色权限");

        // 为默认角色分配权限
        // 管理员角色：所有权限
        assignPermissionsToRole("admin", new String[]{
            "agent:list", "agent:detail", "agent:create", "agent:update", "agent:delete",
            "agent:publish", "agent:export", "agent:import", "agent:stats",
            "chat:send", "chat:history", "chat:manage", "chat:feedback",
            "chat:suggestions", "chat:check", "chat:stats", "chat:admin",
            "conversation:list", "conversation:detail", "conversation:delete", "conversation:export",
            "message:list", "message:detail", "message:delete", "message:regenerate"
        });

        // 普通用户角色：基本权限
        assignPermissionsToRole("user", new String[]{
            "agent:list", "agent:detail", "agent:create", "agent:update",
            "chat:send", "chat:history", "chat:feedback", "chat:suggestions", "chat:check",
            "conversation:list", "conversation:detail",
            "message:list", "message:detail", "message:regenerate"
        });

        // 访客角色：只读权限
        assignPermissionsToRole("guest", new String[]{
            "agent:list", "agent:detail",
            "chat:send", "chat:suggestions", "chat:check"
        });
    }

    /**
     * 注册权限
     */
    private void registerPermission(String permission) {
        try {
            // 这里应该调用系统模块的权限服务来注册权限
            // 示例代码：
            // permissionService.createPermission(permission, getPermissionName(permission), "智能体模块");
            log.debug("注册权限: {}", permission);
        } catch (Exception e) {
            log.warn("注册权限失败: {}", permission, e);
        }
    }

    /**
     * 注册菜单
     */
    private void registerMenu(String name, String code, String path, String permission, int sortOrder, String parentCode) {
        try {
            // 这里应该调用系统模块的菜单服务来注册菜单
            // 示例代码：
            // menuService.createMenu(name, code, path, permission, sortOrder, parentCode);
            log.debug("注册菜单: {} - {}", code, name);
        } catch (Exception e) {
            log.warn("注册菜单失败: {} - {}", code, name, e);
        }
    }

    /**
     * 为角色分配权限
     */
    private void assignPermissionsToRole(String roleCode, String[] permissions) {
        try {
            // 这里应该调用系统模块的角色服务来分配权限
            // 示例代码：
            // roleService.assignPermissions(roleCode, permissions);
            log.debug("为角色 {} 分配权限: {}", roleCode, permissions.length);
        } catch (Exception e) {
            log.warn("为角色 {} 分配权限失败", roleCode, e);
        }
    }

    /**
     * 创建字典类型
     */
    private void createDictType(String dictType, String dictName, String description) {
        try {
            // 这里应该调用系统模块的字典服务来创建字典类型
            // 示例代码：
            // dictTypeService.createDictType(dictType, dictName, description);
            log.debug("创建字典类型: {} - {}", dictType, dictName);
        } catch (Exception e) {
            log.warn("创建字典类型失败: {} - {}", dictType, dictName, e);
        }
    }

    /**
     * 获取权限名称
     */
    private String getPermissionName(String permission) {
        // 权限名称映射
        switch (permission) {
            case "agent:list":
                return "查看智能体列表";
            case "agent:detail":
                return "查看智能体详情";
            case "agent:create":
                return "创建智能体";
            case "agent:update":
                return "更新智能体";
            case "agent:delete":
                return "删除智能体";
            case "agent:publish":
                return "发布智能体";
            case "agent:export":
                return "导出智能体配置";
            case "agent:import":
                return "导入智能体配置";
            case "agent:stats":
                return "查看智能体统计";
            case "chat:send":
                return "发送消息";
            case "chat:history":
                return "查看对话历史";
            case "chat:manage":
                return "管理对话";
            case "chat:feedback":
                return "提交反馈";
            case "chat:suggestions":
                return "获取建议问题";
            case "chat:check":
                return "检查智能体状态";
            case "chat:stats":
                return "查看对话统计";
            case "chat:admin":
                return "聊天管理员权限";
            case "conversation:list":
                return "查看对话列表";
            case "conversation:detail":
                return "查看对话详情";
            case "conversation:delete":
                return "删除对话";
            case "conversation:export":
                return "导出对话记录";
            case "message:list":
                return "查看消息列表";
            case "message:detail":
                return "查看消息详情";
            case "message:delete":
                return "删除消息";
            case "message:regenerate":
                return "重新生成消息";
            default:
                return permission;
        }
    }
}
