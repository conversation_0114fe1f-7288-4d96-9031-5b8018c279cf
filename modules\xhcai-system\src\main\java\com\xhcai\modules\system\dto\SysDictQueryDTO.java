package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 字典类型查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "字典类型查询条件")
public class SysDictQueryDTO extends PageQueryDTO {

    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String dictName;

    /**
     * 字典类型
     */
    @Schema(description = "字典类型")
    private String dictType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    // Getters and Setters
    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getDictType() {
        return dictType;
    }

    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SysDictQueryDTO{"
                + "dictName='" + dictName + '\''
                + ", dictType='" + dictType + '\''
                + ", status='" + status + '\''
                + ", current=" + getCurrent()
                + ", size=" + getSize()
                + ", orderBy='" + getOrderBy() + '\''
                + ", orderDirection='" + getOrderDirection() + '\''
                + '}';
    }
}
