package com.xhcai.modules.dify.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountCreateDTO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountQueryDTO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountUpdateDTO;
import com.xhcai.modules.dify.entity.ThirdPlatformAccount;
import com.xhcai.modules.dify.service.IThirdPlatformAccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Base64;
import java.nio.charset.StandardCharsets;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.dify.mapper.ThirdPlatformAccountMapper;
import com.xhcai.modules.dify.vo.ThirdPlatformAccountVO;

/**
 * 用户第三方智能体账号服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class ThirdPlatformAccountServiceImpl extends ServiceImpl<ThirdPlatformAccountMapper, ThirdPlatformAccount>
        implements IThirdPlatformAccountService {

    private static final Logger log = LoggerFactory.getLogger(ThirdPlatformAccountServiceImpl.class);

    @Autowired
    private ThirdPlatformAccountMapper thirdPlatformAccountMapper;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    // SM4加密解密相关常量
    private static final String SM4_KEY = "XhcaiPlatform123"; // 16字节密钥
    private static final String SM4_ALGORITHM = "SM4/ECB/PKCS5Padding";

    static {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    @Override
    public IPage<ThirdPlatformAccountVO> selectPageVO(ThirdPlatformAccountQueryDTO query) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        Page<ThirdPlatformAccountVO> page = new Page<>(query.getCurrent(), query.getSize());
        return thirdPlatformAccountMapper.selectPageVO(page, query, userId, tenantId);
    }

    @Override
    public ThirdPlatformAccountVO selectVOById(String id) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        return thirdPlatformAccountMapper.selectVOById(id, userId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> createAccount(ThirdPlatformAccountCreateDTO createDTO) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 检查是否已存在相同平台的账号
        int existCount = thirdPlatformAccountMapper.countByUserAndPlatform(userId, createDTO.getPlatformId(), tenantId);
        if (existCount > 0) {
            return Result.error("该平台已存在账号，请勿重复添加");
        }

        // 创建账号
        ThirdPlatformAccount account = new ThirdPlatformAccount();
        BeanUtils.copyProperties(createDTO, account);
        account.setUserId(userId);

        // 密码SM4加密
        if (StringUtils.hasText(createDTO.getPwd())) {
            String encryptedPassword = sm4Encrypt(createDTO.getPwd());
            account.setPwd(encryptedPassword);
            log.info("用户[{}]创建第三方智能体账号，密码已使用SM4加密", userId);
        }

        account.setTotalCalls(0L);
        account.setSuccessCalls(0L);
        account.setAvgResponseTime(0L);

        boolean success = save(account);
        if (success) {
            log.info("用户[{}]创建第三方智能体账号成功，平台：{}, 账号名称：{}", userId, createDTO.getPlatformId(), createDTO.getAccountName());
            return Result.ok("创建成功", account.getId());
        } else {
            return Result.error("创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateAccount(String id, ThirdPlatformAccountUpdateDTO updateDTO) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 查询账号是否存在且属于当前用户
        ThirdPlatformAccount account = lambdaQuery()
                .eq(ThirdPlatformAccount::getId, id)
                .eq(ThirdPlatformAccount::getUserId, userId)
                .eq(ThirdPlatformAccount::getTenantId, tenantId)
                .one();

        if (account == null) {
            return Result.error("账号不存在或无权限操作");
        }

        // 更新账号信息
        BeanUtils.copyProperties(updateDTO, account, "pwd"); // 排除密码字段，单独处理

        // 如果提供了新密码，则SM4加密后更新
        if (StringUtils.hasText(updateDTO.getPwd())) {
            String encryptedPassword = sm4Encrypt(updateDTO.getPwd());
            account.setPwd(encryptedPassword);
            log.info("用户[{}]更新第三方智能体账号密码，账号ID：{}，密码已使用SM4加密", userId, id);
        }

        account.setUpdateTime(LocalDateTime.now());

        boolean success = updateById(account);
        if (success) {
            log.info("用户[{}]更新第三方智能体账号成功，账号ID：{}", userId, id);
            return Result.ok("更新成功");
        } else {
            return Result.error("更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteAccount(String id) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 查询账号是否存在且属于当前用户
        ThirdPlatformAccount account = lambdaQuery()
                .eq(ThirdPlatformAccount::getId, id)
                .eq(ThirdPlatformAccount::getUserId, userId)
                .eq(ThirdPlatformAccount::getTenantId, tenantId)
                .one();

        if (account == null) {
            return Result.error("账号不存在或无权限操作");
        }

        boolean success = removeById(id);
        if (success) {
            log.info("用户[{}]删除第三方智能体账号成功，账号ID：{}", userId, id);
            return Result.ok("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }

    @Override
    public Result<String> testConnection(String id) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 查询账号是否存在且属于当前用户
        ThirdPlatformAccount account = lambdaQuery()
                .eq(ThirdPlatformAccount::getId, id)
                .eq(ThirdPlatformAccount::getUserId, userId)
                .eq(ThirdPlatformAccount::getTenantId, tenantId)
                .one();

        if (account == null) {
            return Result.error("账号不存在或无权限操作");
        }

        try {
            // TODO: 根据不同平台实现具体的连接测试逻辑
            // 这里先模拟测试结果
            boolean testResult = simulateConnectionTest(account);

            // 更新测试结果
            account.setLastTestTime(LocalDateTime.now());
            account.setLastTestResult(testResult ? 1 : 0);
            account.setLastTestError(testResult ? null : "连接测试失败");
            updateById(account);

            if (testResult) {
                log.info("用户[{}]第三方智能体账号连接测试成功，账号ID：{}", userId, id);
                return Result.ok("连接测试成功");
            } else {
                return Result.error("连接测试失败");
            }
        } catch (Exception e) {
            log.error("用户[{}]第三方智能体账号连接测试异常，账号ID：{}", userId, id, e);

            // 更新测试结果
            account.setLastTestTime(LocalDateTime.now());
            account.setLastTestResult(0);
            account.setLastTestError(e.getMessage());
            updateById(account);

            return Result.error("连接测试异常：" + e.getMessage());
        }
    }

    @Override
    public List<ThirdPlatformAccountVO> selectByCurrentUser() {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        return thirdPlatformAccountMapper.selectByUserId(userId, tenantId);
    }

    @Override
    public boolean hasAccountInPlatform(String platformId) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        int count = thirdPlatformAccountMapper.countByUserAndPlatform(userId, platformId, tenantId);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUsageStats(String id, boolean success, long responseTime) {
        ThirdPlatformAccount account = getById(id);
        if (account != null) {
            // 更新统计信息
            long totalCalls = account.getTotalCalls() != null ? account.getTotalCalls() : 0L;
            long successCalls = account.getSuccessCalls() != null ? account.getSuccessCalls() : 0L;
            long avgResponseTime = account.getAvgResponseTime() != null ? account.getAvgResponseTime() : 0L;

            totalCalls++;
            if (success) {
                successCalls++;
            }

            // 计算平均响应时间
            if (totalCalls == 1) {
                avgResponseTime = responseTime;
            } else {
                avgResponseTime = (avgResponseTime * (totalCalls - 1) + responseTime) / totalCalls;
            }

            account.setTotalCalls(totalCalls);
            account.setSuccessCalls(successCalls);
            account.setAvgResponseTime(avgResponseTime);
            account.setLastUsedTime(LocalDateTime.now());

            updateById(account);
        }
    }

    /**
     * 验证密码
     *
     * @param id 账号ID
     * @param rawPassword 原始密码
     * @return 验证结果
     */
    public boolean verifyPassword(String id, String rawPassword) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        ThirdPlatformAccount account = lambdaQuery()
                .eq(ThirdPlatformAccount::getId, id)
                .eq(ThirdPlatformAccount::getUserId, userId)
                .eq(ThirdPlatformAccount::getTenantId, tenantId)
                .one();

        if (account == null || !StringUtils.hasText(account.getPwd())) {
            return false;
        }

        try {
            // 检查是否是BCrypt加密的密码（兼容旧数据）
            if (account.getPwd().startsWith("$2a$") || account.getPwd().startsWith("$2b$") || account.getPwd().startsWith("$2y$")) {
                log.warn("检测到BCrypt加密的密码，使用BCrypt验证（兼容模式）");
                return passwordEncoder.matches(rawPassword, account.getPwd());
            } else {
                // 使用SM4解密验证
                String decryptedPassword = sm4Decrypt(account.getPwd());
                return rawPassword.equals(decryptedPassword);
            }
        } catch (Exception e) {
            log.error("密码验证失败", e);
            return false;
        }
    }

    /**
     * 模拟连接测试
     *
     * @param account 账号信息
     * @return 测试结果
     */
    private boolean simulateConnectionTest(ThirdPlatformAccount account) {
        // TODO: 根据不同平台实现具体的连接测试逻辑
        // 这里先返回随机结果用于演示
        return Math.random() > 0.2; // 80%成功率
    }

    /**
     * SM4加密
     */
    private String sm4Encrypt(String plainText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(SM4_KEY.getBytes(StandardCharsets.UTF_8), "SM4");
            Cipher cipher = Cipher.getInstance(SM4_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            log.error("SM4加密失败", e);
            throw new RuntimeException("SM4加密失败", e);
        }
    }

    /**
     * SM4解密
     */
    private String sm4Decrypt(String encryptedText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(SM4_KEY.getBytes(StandardCharsets.UTF_8), "SM4");
            Cipher cipher = Cipher.getInstance(SM4_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("SM4解密失败", e);
            throw new RuntimeException("SM4解密失败: " + e.getMessage(), e);
        }
    }
}
