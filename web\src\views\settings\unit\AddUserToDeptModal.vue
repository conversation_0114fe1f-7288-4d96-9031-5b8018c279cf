<template>
  <!-- 添加用户到部门模态框 -->
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800">添加用户到部门</h3>
        <button @click="handleClose" class="text-gray-400 hover:text-gray-600">
          <el-icon><Close /></el-icon>
        </button>
      </div>

      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">选择用户</label>
          <el-select
            v-model="selectedUsers"
            multiple
            placeholder="请选择要添加的用户"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :value="user.id"
              :label="`${user.nickname || user.username} (${user.username})`"
            />
          </el-select>
        </div>

        <div class="flex gap-3 pt-4">
          <el-button type="primary" @click="handleAddUsers" :loading="loading" style="flex: 1">
            {{ loading ? '添加中...' : '确定添加' }}
          </el-button>
          <el-button @click="handleClose" style="flex: 1">
            取消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import type { SysUserVO } from '@/types/system'

// Props
interface Props {
  visible: boolean
  availableUsers: SysUserVO[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
const emit = defineEmits<{
  close: []
  'add-users': [userIds: string[]]
}>()

// 选中的用户
const selectedUsers = ref<string[]>([])

// 监听visible变化，重置选中状态
watch(() => props.visible, (newVisible) => {
  if (!newVisible) {
    selectedUsers.value = []
  }
})

// 关闭模态框
const handleClose = () => {
  selectedUsers.value = []
  emit('close')
}

// 添加用户
const handleAddUsers = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要添加的用户')
    return
  }

  emit('add-users', [...selectedUsers.value])
}
</script>

<style scoped>
/* 继承父组件样式 */
</style>
