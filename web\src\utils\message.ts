/**
 * 通用消息提示工具
 * 提供统一的消息提示功能，支持成功、错误、信息、警告等多种类型
 */

export type MessageType = 'success' | 'error' | 'info' | 'warning'

export interface MessageOptions {
  /** 消息内容 */
  message: string
  /** 消息类型 */
  type?: MessageType
  /** 显示持续时间（毫秒），0表示不自动关闭 */
  duration?: number
  /** 是否可手动关闭 */
  closable?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 位置 */
  position?: 'top-right' | 'top-left' | 'top-center' | 'bottom-right' | 'bottom-left' | 'bottom-center'
}

interface MessageInstance {
  id: string
  element: HTMLElement
  timer?: number
}

// 消息实例管理
const messageInstances: MessageInstance[] = []
let messageIdCounter = 0

/**
 * 获取消息类型对应的样式类
 */
const getMessageTypeClass = (type: MessageType): string => {
  const typeClasses = {
    success: 'bg-green-500 border-green-400',
    error: 'bg-red-500 border-red-400', 
    warning: 'bg-yellow-500 border-yellow-400',
    info: 'bg-blue-500 border-blue-400'
  }
  return typeClasses[type] || typeClasses.info
}

/**
 * 获取消息类型对应的图标
 */
const getMessageIcon = (type: MessageType): string => {
  const icons = {
    success: '✓',
    error: '✕',
    warning: '⚠',
    info: 'ℹ'
  }
  return icons[type] || icons.info
}

/**
 * 获取位置样式
 */
const getPositionClass = (position: MessageOptions['position']): string => {
  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
  }
  return positionClasses[position || 'top-right']
}

/**
 * 创建消息元素
 */
const createMessageElement = (options: MessageOptions): HTMLElement => {
  const {
    message,
    type = 'info',
    closable = true,
    className = '',
    position = 'top-right'
  } = options

  const messageDiv = document.createElement('div')
  const messageId = `message-${++messageIdCounter}`
  
  messageDiv.id = messageId
  messageDiv.className = `
    fixed px-4 py-3 rounded-lg text-white font-medium z-50 
    transition-all duration-300 transform translate-x-full opacity-0
    shadow-lg border-l-4 min-w-80 max-w-96
    ${getMessageTypeClass(type)}
    ${getPositionClass(position)}
    ${className}
  `.trim().replace(/\s+/g, ' ')

  // 创建消息内容
  const contentHTML = `
    <div class="flex items-center gap-3">
      <span class="text-lg font-bold">${getMessageIcon(type)}</span>
      <span class="flex-1">${message}</span>
      ${closable ? `
        <button class="ml-2 text-white/80 hover:text-white transition-colors duration-200" 
                onclick="window.removeMessage('${messageId}')">
          <span class="text-lg">×</span>
        </button>
      ` : ''}
    </div>
  `
  
  messageDiv.innerHTML = contentHTML
  return messageDiv
}

/**
 * 显示消息
 */
export const showMessage = (options: string | MessageOptions): void => {
  // 支持字符串参数的简化调用
  const messageOptions: MessageOptions = typeof options === 'string' 
    ? { message: options }
    : options

  const {
    duration = 3000,
    position = 'top-right'
  } = messageOptions

  const messageElement = createMessageElement(messageOptions)
  const messageId = messageElement.id

  // 添加到页面
  document.body.appendChild(messageElement)

  // 计算偏移量，避免消息重叠
  const existingMessages = messageInstances.filter(instance => 
    instance.element.className.includes(getPositionClass(position))
  )
  
  if (existingMessages.length > 0) {
    const offset = existingMessages.length * 70 // 每个消息间隔70px
    if (position.includes('top')) {
      messageElement.style.top = `${16 + offset}px`
    } else {
      messageElement.style.bottom = `${16 + offset}px`
    }
  }

  // 动画显示
  setTimeout(() => {
    messageElement.classList.remove('translate-x-full', 'opacity-0')
  }, 100)

  // 创建消息实例
  const instance: MessageInstance = {
    id: messageId,
    element: messageElement
  }

  // 设置自动关闭
  if (duration > 0) {
    instance.timer = window.setTimeout(() => {
      removeMessage(messageId)
    }, duration)
  }

  messageInstances.push(instance)
}

/**
 * 移除消息
 */
export const removeMessage = (messageId: string): void => {
  const instanceIndex = messageInstances.findIndex(instance => instance.id === messageId)
  if (instanceIndex === -1) return

  const instance = messageInstances[instanceIndex]
  
  // 清除定时器
  if (instance.timer) {
    clearTimeout(instance.timer)
  }

  // 动画隐藏
  instance.element.classList.add('translate-x-full', 'opacity-0')
  
  // 移除元素
  setTimeout(() => {
    if (instance.element.parentNode) {
      instance.element.parentNode.removeChild(instance.element)
    }
  }, 300)

  // 从实例列表中移除
  messageInstances.splice(instanceIndex, 1)
}

/**
 * 清除所有消息
 */
export const clearAllMessages = (): void => {
  messageInstances.forEach(instance => {
    removeMessage(instance.id)
  })
}

// 便捷方法
export const showSuccess = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
  showMessage({ ...options, message, type: 'success' })
}

export const showError = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
  showMessage({ ...options, message, type: 'error' })
}

export const showWarning = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
  showMessage({ ...options, message, type: 'warning' })
}

export const showInfo = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
  showMessage({ ...options, message, type: 'info' })
}

// 将removeMessage函数挂载到window对象，供HTML中的onclick使用
declare global {
  interface Window {
    removeMessage: (messageId: string) => void
  }
}

window.removeMessage = removeMessage
