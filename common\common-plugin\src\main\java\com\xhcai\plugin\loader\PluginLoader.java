package com.xhcai.plugin.loader;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.pf4j.PluginManager;
import org.pf4j.PluginState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xhcai.plugin.core.PluginInfo;
import com.xhcai.plugin.core.PluginStatus;
import com.xhcai.plugin.core.PluginType;

/**
 * 插件加载器 负责插件的动态加载、卸载、升级
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class PluginLoader {

    private static final Logger log = LoggerFactory.getLogger(PluginLoader.class);

    private final PluginManager pluginManager;
    private final PluginType pluginType;
    private final Map<String, PluginInfo> loadedPlugins = new ConcurrentHashMap<>();

    public PluginLoader(PluginManager pluginManager, PluginType pluginType) {
        this.pluginManager = pluginManager;
        this.pluginType = pluginType;
    }

    /**
     * 加载插件
     *
     * @param pluginPath 插件路径
     * @return 插件ID
     */
    public String loadPlugin(Path pluginPath) {
        try {
            log.info("Loading plugin from path: {}", pluginPath);

            // 加载插件
            String pluginId = pluginManager.loadPlugin(pluginPath);
            if (pluginId == null) {
                throw new RuntimeException("Failed to load plugin from path: " + pluginPath);
            }

            // 启动插件
            PluginState state = pluginManager.startPlugin(pluginId);
            if (state != PluginState.STARTED) {
                throw new RuntimeException("Failed to start plugin: " + pluginId);
            }

            // 创建插件信息
            PluginInfo pluginInfo = createPluginInfo(pluginId, pluginPath);
            loadedPlugins.put(pluginId, pluginInfo);

            log.info("Plugin loaded successfully: {} ({})", pluginInfo.getPluginName(), pluginId);
            return pluginId;

        } catch (Exception e) {
            log.error("Failed to load plugin from path: {}", pluginPath, e);
            throw new RuntimeException("Failed to load plugin", e);
        }
    }

    /**
     * 卸载插件
     *
     * @param pluginId 插件ID
     * @return 是否卸载成功
     */
    public boolean unloadPlugin(String pluginId) {
        try {
            log.info("Unloading plugin: {}", pluginId);

            // 停止插件
            PluginState state = pluginManager.stopPlugin(pluginId);
            if (state != PluginState.STOPPED) {
                log.warn("Plugin stop state is not STOPPED: {}", state);
            }

            // 卸载插件
            boolean result = pluginManager.unloadPlugin(pluginId);
            if (result) {
                loadedPlugins.remove(pluginId);
                log.info("Plugin unloaded successfully: {}", pluginId);
            } else {
                log.error("Failed to unload plugin: {}", pluginId);
            }

            return result;

        } catch (Exception e) {
            log.error("Failed to unload plugin: {}", pluginId, e);
            return false;
        }
    }

    /**
     * 重新加载插件
     *
     * @param pluginId 插件ID
     * @return 是否重新加载成功
     */
    public boolean reloadPlugin(String pluginId) {
        try {
            log.info("Reloading plugin: {}", pluginId);

            PluginInfo pluginInfo = loadedPlugins.get(pluginId);
            if (pluginInfo == null) {
                log.error("Plugin not found: {}", pluginId);
                return false;
            }

            Path pluginPath = Path.of(pluginInfo.getPluginPath());

            // 先卸载
            if (!unloadPlugin(pluginId)) {
                log.error("Failed to unload plugin for reload: {}", pluginId);
                return false;
            }

            // 再加载
            String newPluginId = loadPlugin(pluginPath);
            if (newPluginId == null) {
                log.error("Failed to load plugin for reload: {}", pluginId);
                return false;
            }

            log.info("Plugin reloaded successfully: {} -> {}", pluginId, newPluginId);
            return true;

        } catch (Exception e) {
            log.error("Failed to reload plugin: {}", pluginId, e);
            return false;
        }
    }

    /**
     * 升级插件
     *
     * @param pluginId 插件ID
     * @param newPluginPath 新插件路径
     * @return 是否升级成功
     */
    public boolean upgradePlugin(String pluginId, Path newPluginPath) {
        try {
            log.info("Upgrading plugin: {} with new path: {}", pluginId, newPluginPath);

            // 先卸载旧版本
            if (!unloadPlugin(pluginId)) {
                log.error("Failed to unload old plugin for upgrade: {}", pluginId);
                return false;
            }

            // 加载新版本
            String newPluginId = loadPlugin(newPluginPath);
            if (newPluginId == null) {
                log.error("Failed to load new plugin for upgrade: {}", pluginId);
                return false;
            }

            log.info("Plugin upgraded successfully: {} -> {}", pluginId, newPluginId);
            return true;

        } catch (Exception e) {
            log.error("Failed to upgrade plugin: {}", pluginId, e);
            return false;
        }
    }

    /**
     * 获取已加载的插件列表
     */
    public List<PluginInfo> getLoadedPlugins() {
        return List.copyOf(loadedPlugins.values());
    }

    /**
     * 获取插件信息
     */
    public PluginInfo getPluginInfo(String pluginId) {
        return loadedPlugins.get(pluginId);
    }

    /**
     * 检查插件是否已加载
     */
    public boolean isPluginLoaded(String pluginId) {
        return loadedPlugins.containsKey(pluginId);
    }

    /**
     * 创建插件信息
     */
    private PluginInfo createPluginInfo(String pluginId, Path pluginPath) {
        var descriptor = pluginManager.getPlugin(pluginId).getDescriptor();

        return PluginInfo.builder()
                .pluginId(pluginId)
                .pluginName(descriptor.getPluginDescription())
                .version(descriptor.getVersion())
                .pluginType(pluginType)
                .description(descriptor.getPluginDescription())
                .author(descriptor.getProvider())
                .pluginClass(descriptor.getPluginClass())
                .status(PluginStatus.STARTED)
                .pluginPath(pluginPath.toString())
                .dependencies(descriptor.getDependencies().stream()
                        .map(dep -> dep.getPluginId())
                        .toArray(String[]::new))
                .minSystemVersion(descriptor.getRequires())
                .license(descriptor.getLicense())
                .build();
    }
}
