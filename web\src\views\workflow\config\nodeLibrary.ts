/**
 * 静态节点库配置
 * 替代原来的后端API调用，直接在前端定义所有节点类型和配置
 */

import { Position } from '@vue-flow/core'

export interface NodeHandle {
  id: string
  position: Position
  label?: string
}

export interface NodeConfig {
  [key: string]: any
}

export interface NodeLibraryItem {
  type: string
  label: string
  icon: string
  description: string
  category: string
  maxInstances?: number
  color: string
  gradient: string
  labelColor: string
  iconColor: string
  status: 'stable' | 'beta' | 'alpha' | 'deprecated'
  version: string
  tags: string[]
  handles: {
    source: NodeHandle[]
    target: NodeHandle[]
  }
  defaultData: {
    label: string
    config: NodeConfig
  }
}

export interface NodeCategory {
  name: string
  icon: string
  description: string
  nodes: NodeLibraryItem[]
}

export interface NodeLibraryConfig {
  categories: NodeCategory[]
}

/**
 * 静态节点库配置
 */
export const NODE_LIBRARY_CONFIG: NodeLibraryConfig = {
  categories: [
    {
      name: "基础节点",
      icon: "fas fa-cogs",
      description: "工作流的基础控制节点",
      nodes: [
        {
          type: "start",
          label: "开始节点",
          icon: "fas fa-play-circle",
          description: "每个工作流必须有一个开始节点",
          category: "basic",
          maxInstances: 1,
          color: "#10b981",
          gradient: "linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)",
          labelColor: "#065f46",
          iconColor: "#10b981",
          status: "stable",
          version: "1.0.0",
          tags: ["基础", "必需"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: []
          },
          defaultData: {
            label: "开始",
            config: {}
          }
        },
        {
          type: "end",
          label: "结束节点",
          icon: "fas fa-stop-circle",
          description: "可以有多个结束节点",
          category: "basic",
          color: "#ef4444",
          gradient: "linear-gradient(135deg, #fef2f2 0%, #fecaca 100%)",
          labelColor: "#7f1d1d",
          iconColor: "#ef4444",
          status: "stable",
          version: "1.0.0",
          tags: ["基础", "结束"],
          handles: {
            source: [],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "结束",
            config: {}
          }
        },
        {
          type: "condition",
          label: "条件判断",
          icon: "fas fa-question-circle",
          description: "根据条件进行分支判断",
          category: "basic",
          color: "#f59e0b",
          gradient: "linear-gradient(135deg, #fffbeb 0%, #fed7aa 100%)",
          labelColor: "#78350f",
          iconColor: "#f59e0b",
          status: "stable",
          version: "1.0.0",
          tags: ["基础", "逻辑"],
          handles: {
            source: [
              { id: "true", position: Position.Right, label: "是" },
              { id: "false", position: Position.Bottom, label: "否" }
            ],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "条件判断",
            config: {
              condition: "",
              operator: "equals",
              value: ""
            }
          }
        },
        {
          type: "loop",
          label: "循环节点",
          icon: "fas fa-sync-alt",
          description: "循环执行子流程",
          category: "basic",
          color: "#3b82f6",
          gradient: "linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)",
          labelColor: "#1e3a8a",
          iconColor: "#3b82f6",
          status: "stable",
          version: "1.0.0",
          tags: ["基础", "循环"],
          handles: {
            source: [
              { id: "loop", position: Position.Bottom, label: "循环体" },
              { id: "output", position: Position.Right, label: "完成" }
            ],
            target: [
              { id: "input", position: Position.Left },
              { id: "continue", position: Position.Top, label: "继续" }
            ]
          },
          defaultData: {
            label: "循环",
            config: {
              loopType: "for",
              maxIterations: 10,
              condition: ""
            }
          }
        }
      ]
    },
    {
      name: "数据库工具",
      icon: "fas fa-database",
      description: "各种数据库操作工具",
      nodes: [
        {
          type: "mysql",
          label: "MySQL",
          icon: "fas fa-database",
          description: "MySQL数据库操作，支持查询、插入、更新、删除",
          category: "database",
          color: "#0ea5e9",
          gradient: "linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%)",
          labelColor: "#0c4a6e",
          iconColor: "#0ea5e9",
          status: "stable",
          version: "1.0.0",
          tags: ["数据库", "MySQL"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "MySQL",
            config: {
              host: "",
              port: 3306,
              database: "",
              username: "",
              password: "",
              sql: "SELECT * FROM table_name",
              operation: "select"
            }
          }
        },
        {
          type: "postgresql",
          label: "PostgreSQL",
          icon: "fas fa-server",
          description: "PostgreSQL数据库操作",
          category: "database",
          color: "#0ea5e9",
          gradient: "linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%)",
          labelColor: "#0c4a6e",
          iconColor: "#0ea5e9",
          status: "stable",
          version: "1.0.0",
          tags: ["数据库", "PostgreSQL"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "PostgreSQL",
            config: {
              host: "",
              port: 5432,
              database: "",
              username: "",
              password: "",
              sql: "SELECT * FROM table_name",
              operation: "select"
            }
          }
        },
        {
          type: "oracle",
          label: "Oracle",
          icon: "fas fa-hdd",
          description: "Oracle数据库操作",
          category: "database",
          color: "#0ea5e9",
          gradient: "linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%)",
          labelColor: "#0c4a6e",
          iconColor: "#0ea5e9",
          status: "stable",
          version: "1.0.0",
          tags: ["数据库", "Oracle"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "Oracle",
            config: {
              host: "",
              port: 1521,
              serviceName: "",
              username: "",
              password: "",
              sql: "SELECT * FROM table_name",
              operation: "select"
            }
          }
        },
        {
          type: "redis",
          label: "Redis",
          icon: "fas fa-cube",
          description: "Redis缓存数据库操作",
          category: "database",
          color: "#dc2626",
          gradient: "linear-gradient(135deg, #fef2f2 0%, #fecaca 100%)",
          labelColor: "#7f1d1d",
          iconColor: "#dc2626",
          status: "stable",
          version: "1.0.0",
          tags: ["数据库", "缓存"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "Redis",
            config: {
              host: "",
              port: 6379,
              database: 0,
              password: "",
              key: "",
              value: "",
              operation: "get"
            }
          }
        }
      ]
    },
    {
      name: "AI工具",
      icon: "fas fa-brain",
      description: "各种AI模型和智能处理工具",
      nodes: [
        {
          type: "llm-chat",
          label: "LLM对话",
          icon: "fas fa-comments",
          description: "使用大语言模型进行对话和文本生成",
          category: "ai",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%)",
          labelColor: "#581c87",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["AI", "对话"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "LLM对话",
            config: {
              model: "gpt-3.5-turbo",
              prompt: "",
              temperature: 0.7,
              maxTokens: 1000,
              systemMessage: ""
            }
          }
        },
        {
          type: "text-embedding",
          label: "文本向量化",
          icon: "fas fa-project-diagram",
          description: "将文本转换为向量表示",
          category: "ai",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%)",
          labelColor: "#581c87",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["AI", "向量化"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "文本向量化",
            config: {
              model: "text-embedding-ada-002",
              text: "",
              dimensions: 1536
            }
          }
        },
        {
          type: "speech-to-text",
          label: "语音识别",
          icon: "fas fa-microphone",
          description: "将语音转换为文本",
          category: "ai",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%)",
          labelColor: "#581c87",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["AI", "语音"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "语音识别",
            config: {
              audioFile: "",
              language: "zh-CN",
              model: "whisper-1"
            }
          }
        },
        {
          type: "text-to-speech",
          label: "语音合成",
          icon: "fas fa-volume-up",
          description: "将文本转换为语音",
          category: "ai",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%)",
          labelColor: "#581c87",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["AI", "语音"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "语音合成",
            config: {
              text: "",
              voice: "alloy",
              model: "tts-1",
              outputFile: ""
            }
          }
        },
        {
          type: "image-generation",
          label: "图像生成",
          icon: "fas fa-image",
          description: "根据文本描述生成图像",
          category: "ai",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%)",
          labelColor: "#581c87",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["AI", "图像"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "图像生成",
            config: {
              prompt: "",
              model: "dall-e-3",
              size: "1024x1024",
              quality: "standard",
              outputFile: ""
            }
          }
        },
        {
          type: "image-analysis",
          label: "图像分析",
          icon: "fa-solid fa-eye",
          description: "分析图像内容并提取信息",
          category: "ai",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%)",
          labelColor: "#581c87",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["AI", "图像"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "图像分析",
            config: {
              imageFile: "",
              analysisType: "description",
              model: "gpt-4-vision-preview"
            }
          }
        },
        {
          type: "knowledge-base",
          label: "知识库查询",
          icon: "fas fa-search",
          description: "从知识库中检索相关信息",
          category: "ai",
          color: "#8b5cf6",
          gradient: "linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%)",
          labelColor: "#581c87",
          iconColor: "#8b5cf6",
          status: "stable",
          version: "1.0.0",
          tags: ["AI", "知识库"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "知识库查询",
            config: {
              datasetId: "",
              query: "",
              topK: 5,
              threshold: 0.7
            }
          }
        }
      ]
    },
    {
      name: "文件生成工具",
      icon: "fas fa-file-plus",
      description: "生成各种格式的文件",
      nodes: [
        {
          type: "generate-ppt",
          label: "生成PPT",
          icon: "fa-solid fa-file-powerpoint",
          description: "根据模板和数据生成PowerPoint文件",
          category: "file-generator",
          color: "orange",
          gradient: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["文件", "PPT"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "生成PPT",
            config: {
              template: "",
              outputPath: "",
              data: {},
              format: "pptx"
            }
          }
        },
        {
          type: "generate-word",
          label: "生成Word",
          icon: "fa-solid fa-file-word",
          description: "根据模板和数据生成Word文档",
          category: "file-generator",
          color: "orange",
          gradient: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["文件", "Word"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "生成Word",
            config: {
              template: "",
              outputPath: "",
              data: {},
              format: "docx"
            }
          }
        },
        {
          type: "generate-pdf",
          label: "生成PDF",
          icon: "fa-solid fa-file-pdf",
          description: "根据模板和数据生成PDF文件",
          category: "file-generator",
          color: "orange",
          gradient: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["文件", "PDF"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "生成PDF",
            config: {
              template: "",
              outputPath: "",
              data: {},
              pageSize: "A4"
            }
          }
        },
        {
          type: "generate-excel",
          label: "生成Excel",
          icon: "fa-solid fa-file-excel",
          description: "根据数据生成Excel表格",
          category: "file-generator",
          color: "orange",
          gradient: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["文件", "Excel"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "生成Excel",
            config: {
              outputPath: "",
              sheets: [],
              format: "xlsx"
            }
          }
        }
      ]
    },
    {
      name: "文件提取工具",
      icon: "fas fa-file-download",
      description: "从各种格式的文件中提取数据",
      nodes: [
        {
          type: "extract-ppt",
          label: "提取PPT",
          icon: "fa-solid fa-file-powerpoint",
          description: "从PowerPoint文件中提取文本和数据",
          category: "file-extractor",
          color: "purple",
          gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["文件", "PPT"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "提取PPT",
            config: {
              filePath: "",
              extractType: "text",
              outputFormat: "json"
            }
          }
        },
        {
          type: "extract-word",
          label: "提取Word",
          icon: "fa-solid fa-file-word",
          description: "从Word文档中提取文本和数据",
          category: "file-extractor",
          color: "purple",
          gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["文件", "Word"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "提取Word",
            config: {
              filePath: "",
              extractType: "text",
              outputFormat: "json"
            }
          }
        },
        {
          type: "extract-pdf",
          label: "提取PDF",
          icon: "fa-solid fa-file-pdf",
          description: "从PDF文件中提取文本和数据",
          category: "file-extractor",
          color: "purple",
          gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["文件", "PDF"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "提取PDF",
            config: {
              filePath: "",
              extractType: "text",
              pageRange: "all",
              outputFormat: "json"
            }
          }
        }
      ]
    },
    {
      name: "渲染工具",
      icon: "fas fa-palette",
      description: "数据可视化和渲染工具",
      nodes: [
        {
          type: "pie-chart",
          label: "饼图",
          icon: "fas fa-chart-pie",
          description: "生成饼图可视化",
          category: "render",
          color: "teal",
          gradient: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
          labelColor: "#333333",
          iconColor: "#333333",
          status: "stable",
          version: "1.0.0",
          tags: ["渲染", "图表"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "饼图",
            config: {
              data: [],
              title: "",
              width: 400,
              height: 300,
              colors: []
            }
          }
        },
        {
          type: "line-chart",
          label: "折线图",
          icon: "fas fa-chart-line",
          description: "生成折线图可视化",
          category: "render",
          color: "teal",
          gradient: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
          labelColor: "#333333",
          iconColor: "#333333",
          status: "stable",
          version: "1.0.0",
          tags: ["渲染", "图表"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "折线图",
            config: {
              data: [],
              title: "",
              xAxis: "",
              yAxis: "",
              width: 600,
              height: 400
            }
          }
        },
        {
          type: "bar-chart",
          label: "柱状图",
          icon: "fas fa-chart-bar",
          description: "生成柱状图可视化",
          category: "render",
          color: "teal",
          gradient: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
          labelColor: "#333333",
          iconColor: "#333333",
          status: "stable",
          version: "1.0.0",
          tags: ["渲染", "图表"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "柱状图",
            config: {
              data: [],
              title: "",
              xAxis: "",
              yAxis: "",
              width: 600,
              height: 400
            }
          }
        }
      ]
    },
    {
      name: "数据工具",
      icon: "fas fa-table",
      description: "数据处理和转换工具",
      nodes: [
        {
          type: "data-filter",
          label: "数据过滤",
          icon: "fas fa-filter",
          description: "根据条件过滤数据",
          category: "data",
          color: "indigo",
          gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["数据", "过滤"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "数据过滤",
            config: {
              conditions: [],
              operator: "and"
            }
          }
        },
        {
          type: "data-transform",
          label: "数据转换",
          icon: "fas fa-exchange-alt",
          description: "转换数据格式和结构",
          category: "data",
          color: "indigo",
          gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["数据", "转换"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "数据转换",
            config: {
              mapping: {},
              outputFormat: "json"
            }
          }
        }
      ]
    },
    {
      name: "其它工具",
      icon: "fas fa-wrench",
      description: "其他实用工具",
      nodes: [
        {
          type: "http-request",
          label: "HTTP请求",
          icon: "fas fa-paper-plane",
          description: "发送HTTP请求",
          category: "utility",
          color: "gray",
          gradient: "linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["工具", "HTTP"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "HTTP请求",
            config: {
              url: "",
              method: "GET",
              headers: {},
              body: "",
              timeout: 30000
            }
          }
        },
        {
          type: "delay",
          label: "延时等待",
          icon: "fa-solid fa-clock",
          description: "延时等待指定时间",
          category: "utility",
          color: "gray",
          gradient: "linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%)",
          labelColor: "#ffffff",
          iconColor: "#ffffff",
          status: "stable",
          version: "1.0.0",
          tags: ["工具", "延时"],
          handles: {
            source: [{ id: "output", position: Position.Right }],
            target: [{ id: "input", position: Position.Left }]
          },
          defaultData: {
            label: "延时等待",
            config: {
              duration: 1000,
              unit: "milliseconds"
            }
          }
        }
      ]
    }
  ]
}

/**
 * 根据节点类型获取节点配置
 */
export function getNodeByType(type: string): NodeLibraryItem | undefined {
  for (const category of NODE_LIBRARY_CONFIG.categories) {
    const node = category.nodes.find(n => n.type === type)
    if (node) {
      return node
    }
  }
  return undefined
}

/**
 * 获取所有节点类型
 */
export function getAllNodeTypes(): string[] {
  const types: string[] = []
  for (const category of NODE_LIBRARY_CONFIG.categories) {
    for (const node of category.nodes) {
      types.push(node.type)
    }
  }
  return types
}

/**
 * 根据分类获取节点
 */
export function getNodesByCategory(categoryName: string): NodeLibraryItem[] {
  const category = NODE_LIBRARY_CONFIG.categories.find(c => c.name === categoryName)
  return category ? category.nodes : []
}
