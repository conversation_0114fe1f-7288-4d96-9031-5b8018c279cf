package com.xhcai.modules.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.agent.entity.ProjectTeamMember;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 项目团队成员Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface ProjectTeamMemberMapper extends BaseMapper<ProjectTeamMember> {

    /**
     * 查询项目团队成员列表（包含用户信息）
     *
     * @param projectId 项目ID
     * @return 团队成员列表
     */
    @Select({
        "SELECT ptm.*, ",
        "       su.id as user_id, su.username as user_username, su.nickname as user_nickname, ",
        "       su.email as user_email, su.phone as user_phone, su.avatar as user_avatar, ",
        "       su.dept_id as user_dept_id, sd.dept_name as user_dept_name, su.status as user_status, ",
        "       su.create_time as user_create_time, su.update_time as user_update_time ",
        "FROM project_team_member ptm ",
        "LEFT JOIN sys_user su ON ptm.user_id = su.id ",
        "LEFT JOIN sys_dept sd on su.dept_id = sd.id ",
        "WHERE ptm.project_id = #{projectId} AND ptm.deleted = 0 ",
        "ORDER BY ptm.join_time ASC"
    })
    List<ProjectTeamMember> selectTeamMembersByProjectId(@Param("projectId") String projectId);

    /**
     * 根据项目ID和用户ID查询团队成员
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 团队成员
     */
    @Select({
        "SELECT ptm.*, ",
        "       su.id as user_id, su.username as user_username, su.nickname as user_nickname, ",
        "       su.email as user_email, su.phone as user_phone, su.avatar as user_avatar, ",
        "       su.dept_id as user_dept_id, sd.dept_name as user_dept_name, su.status as user_status, ",
        "       su.create_time as user_create_time, su.update_time as user_update_time ",
        "FROM project_team_member ptm ",
        "LEFT JOIN sys_user su ON ptm.user_id = su.id ",
        "LEFT JOIN sys_dept sd on su.dept_id = sd.id ",
        "WHERE ptm.project_id = #{projectId} AND ptm.user_id = #{userId} AND ptm.deleted = 0"
    })
    ProjectTeamMember selectByProjectIdAndUserId(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 删除项目的所有团队成员
     *
     * @param projectId 项目ID
     * @return 删除数量
     */
    @Delete("UPDATE project_team_member SET deleted = 1 WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") String projectId);

    /**
     * 删除指定项目的指定用户
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 删除数量
     */
    @Delete("UPDATE project_team_member SET deleted = 1 WHERE project_id = #{projectId} AND user_id = #{userId}")
    int deleteByProjectIdAndUserId(@Param("projectId") String projectId, @Param("userId") String userId);
}
