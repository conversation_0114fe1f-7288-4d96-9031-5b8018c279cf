'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Save, 
  RotateCcw, 
  Download, 
  Upload, 
  Eye, 
  EyeOff, 
  CheckCircle, 
  AlertTriangle,
  FileText,
  Settings,
  Code,
  Braces
} from 'lucide-react';
import { ElasticComponent } from '@/types/component';
import ComponentsAPI from '@/api/components';
import toast from 'react-hot-toast';

interface ConfigEditorProps {
  component: ElasticComponent;
  onConfigUpdate?: (config: Record<string, any>) => void;
  onClose?: () => void;
}

type EditorMode = 'form' | 'yaml' | 'json';

export default function ConfigEditor({ component, onConfigUpdate, onClose }: ConfigEditorProps) {
  const [config, setConfig] = useState<Record<string, any>>({});
  const [originalConfig, setOriginalConfig] = useState<Record<string, any>>({});
  const [editorMode, setEditorMode] = useState<EditorMode>('form');
  const [configText, setConfigText] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [validating, setValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    errors?: string[];
  } | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    loadConfig();
  }, [component.id]);

  useEffect(() => {
    // 检查是否有变更
    const hasConfigChanges = JSON.stringify(config) !== JSON.stringify(originalConfig);
    setHasChanges(hasConfigChanges);
  }, [config, originalConfig]);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await ComponentsAPI.getComponentConfig(component.id);
      if (response.success && response.data) {
        setConfig(response.data);
        setOriginalConfig(response.data);
        setConfigText(formatConfigText(response.data));
      } else {
        // 如果没有配置，加载默认模板
        const templateResponse = await ComponentsAPI.getDefaultConfigTemplate(component.type);
        if (templateResponse.success && templateResponse.data) {
          setConfig(templateResponse.data);
          setOriginalConfig(templateResponse.data);
          setConfigText(formatConfigText(templateResponse.data));
        }
      }
    } catch (error: any) {
      console.error('加载配置失败:', error);
      toast.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const formatConfigText = (configData: Record<string, any>) => {
    if (editorMode === 'json') {
      return JSON.stringify(configData, null, 2);
    } else {
      // 简单的YAML格式化
      return Object.entries(configData)
        .map(([key, value]) => {
          if (typeof value === 'object' && value !== null) {
            return `${key}:\n${Object.entries(value)
              .map(([k, v]) => `  ${k}: ${v}`)
              .join('\n')}`;
          }
          return `${key}: ${value}`;
        })
        .join('\n');
    }
  };

  const parseConfigText = (text: string): Record<string, any> => {
    try {
      if (editorMode === 'json') {
        return JSON.parse(text);
      } else {
        // 简单的YAML解析
        const result: Record<string, any> = {};
        const lines = text.split('\n');
        let currentKey = '';
        
        for (const line of lines) {
          const trimmed = line.trim();
          if (!trimmed || trimmed.startsWith('#')) continue;
          
          if (line.startsWith('  ')) {
            // 嵌套属性
            const [key, value] = trimmed.split(':').map(s => s.trim());
            if (currentKey && key && value !== undefined) {
              if (!result[currentKey]) result[currentKey] = {};
              result[currentKey][key] = parseValue(value);
            }
          } else {
            // 顶级属性
            const [key, value] = trimmed.split(':').map(s => s.trim());
            if (key) {
              currentKey = key;
              if (value !== undefined && value !== '') {
                result[key] = parseValue(value);
              }
            }
          }
        }
        
        return result;
      }
    } catch (error) {
      throw new Error(`配置格式错误: ${error.message}`);
    }
  };

  const parseValue = (value: string): any => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === 'null') return null;
    if (/^\d+$/.test(value)) return parseInt(value);
    if (/^\d+\.\d+$/.test(value)) return parseFloat(value);
    if (value.startsWith('[') && value.endsWith(']')) {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      let configToSave = config;
      
      // 如果是文本模式，先解析文本
      if (editorMode !== 'form') {
        try {
          configToSave = parseConfigText(configText);
        } catch (error: any) {
          toast.error(error.message);
          return;
        }
      }

      // 验证配置
      await validateConfig(configToSave);
      if (validationResult && !validationResult.valid) {
        toast.error('配置验证失败，请检查错误信息');
        return;
      }

      // 保存配置
      const response = await ComponentsAPI.updateComponentConfig(component.id, {
        config: configToSave
      });

      if (response.success) {
        toast.success('配置保存成功');
        setOriginalConfig(configToSave);
        setConfig(configToSave);
        onConfigUpdate?.(configToSave);
      } else {
        throw new Error(response.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存配置失败:', error);
      toast.error(error.message || '保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const validateConfig = async (configToValidate: Record<string, any>) => {
    try {
      setValidating(true);
      const response = await ComponentsAPI.validateComponentConfig(component.type, configToValidate);
      if (response.success && response.data) {
        setValidationResult(response.data);
      }
    } catch (error: any) {
      console.error('验证配置失败:', error);
      setValidationResult({
        valid: false,
        errors: ['配置验证服务不可用']
      });
    } finally {
      setValidating(false);
    }
  };

  const handleReset = () => {
    if (confirm('确定要重置所有更改吗？')) {
      setConfig(originalConfig);
      setConfigText(formatConfigText(originalConfig));
      setValidationResult(null);
    }
  };

  const handleExport = async () => {
    try {
      const response = await ComponentsAPI.exportComponentConfig(component.id);
      if (response.success && response.data) {
        const blob = response.data as Blob;
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${component.name}-config.yml`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('配置导出成功');
      }
    } catch (error: any) {
      console.error('导出配置失败:', error);
      toast.error('导出配置失败');
    }
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        const importedConfig = parseConfigText(content);
        setConfig(importedConfig);
        setConfigText(content);
        toast.success('配置导入成功');
      } catch (error: any) {
        toast.error(`导入失败: ${error.message}`);
      }
    };
    reader.readAsText(file);
  };

  const handleModeChange = (mode: EditorMode) => {
    if (editorMode !== 'form' && mode === 'form') {
      // 从文本模式切换到表单模式，需要解析文本
      try {
        const parsedConfig = parseConfigText(configText);
        setConfig(parsedConfig);
      } catch (error: any) {
        toast.error(`解析配置失败: ${error.message}`);
        return;
      }
    } else if (editorMode === 'form' && mode !== 'form') {
      // 从表单模式切换到文本模式，需要格式化配置
      setConfigText(formatConfigText(config));
    }
    setEditorMode(mode);
  };

  const handleTextChange = (text: string) => {
    setConfigText(text);
    // 实时验证（防抖）
    clearTimeout(window.configValidationTimeout);
    window.configValidationTimeout = setTimeout(() => {
      try {
        const parsedConfig = parseConfigText(text);
        validateConfig(parsedConfig);
      } catch (error) {
        setValidationResult({
          valid: false,
          errors: [error.message]
        });
      }
    }, 1000);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">加载配置...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-large max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">配置编辑器</h2>
          <p className="text-gray-600">{component.name} - {component.type}</p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Editor Mode Tabs */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => handleModeChange('form')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                editorMode === 'form'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Settings className="h-4 w-4 inline mr-1" />
              表单
            </button>
            <button
              onClick={() => handleModeChange('yaml')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                editorMode === 'yaml'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <FileText className="h-4 w-4 inline mr-1" />
              YAML
            </button>
            <button
              onClick={() => handleModeChange('json')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                editorMode === 'json'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Braces className="h-4 w-4 inline mr-1" />
              JSON
            </button>
          </div>

          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
        </div>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <button
            onClick={handleSave}
            disabled={saving || !hasChanges}
            className="btn-primary btn-sm"
          >
            <Save className="h-4 w-4 mr-1" />
            {saving ? '保存中...' : '保存'}
          </button>
          
          <button
            onClick={handleReset}
            disabled={!hasChanges}
            className="btn-outline btn-sm"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </button>

          <button
            onClick={handleExport}
            className="btn-outline btn-sm"
          >
            <Download className="h-4 w-4 mr-1" />
            导出
          </button>

          <label className="btn-outline btn-sm cursor-pointer">
            <Upload className="h-4 w-4 mr-1" />
            导入
            <input
              type="file"
              accept=".yml,.yaml,.json"
              onChange={handleImport}
              className="hidden"
            />
          </label>
        </div>

        <div className="flex items-center space-x-3">
          {/* Validation Status */}
          {validating && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <div className="loading-spinner w-4 h-4"></div>
              <span>验证中...</span>
            </div>
          )}
          
          {validationResult && (
            <div className={`flex items-center space-x-2 text-sm ${
              validationResult.valid ? 'text-success-600' : 'text-error-600'
            }`}>
              {validationResult.valid ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
              <span>{validationResult.valid ? '配置有效' : '配置无效'}</span>
            </div>
          )}

          {editorMode === 'form' && (
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="btn-outline btn-sm"
            >
              {showAdvanced ? (
                <EyeOff className="h-4 w-4 mr-1" />
              ) : (
                <Eye className="h-4 w-4 mr-1" />
              )}
              {showAdvanced ? '隐藏高级' : '显示高级'}
            </button>
          )}
        </div>
      </div>

      {/* Validation Errors */}
      {validationResult && !validationResult.valid && validationResult.errors && (
        <div className="p-4 bg-error-50 border-b border-error-200">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-5 w-5 text-error-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-error-900">配置验证错误</h4>
              <ul className="mt-2 text-sm text-error-700 space-y-1">
                {validationResult.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Editor Content */}
      <div className="p-6">
        {editorMode === 'form' ? (
          <FormEditor
            config={config}
            onChange={setConfig}
            componentType={component.type}
            showAdvanced={showAdvanced}
          />
        ) : (
          <TextEditor
            value={configText}
            onChange={handleTextChange}
            mode={editorMode}
            ref={textareaRef}
          />
        )}
      </div>

      {/* Footer */}
      {hasChanges && (
        <div className="p-4 bg-warning-50 border-t border-warning-200">
          <div className="flex items-center space-x-2 text-warning-800">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">您有未保存的更改</span>
          </div>
        </div>
      )}
    </div>
  );
}

// Form Editor Component
interface FormEditorProps {
  config: Record<string, any>;
  onChange: (config: Record<string, any>) => void;
  componentType: string;
  showAdvanced: boolean;
}

function FormEditor({ config, onChange, componentType, showAdvanced }: FormEditorProps) {
  const handleFieldChange = (path: string, value: any) => {
    const newConfig = { ...config };
    const keys = path.split('.');
    let current = newConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) current[keys[i]] = {};
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    onChange(newConfig);
  };

  const getFieldValue = (path: string) => {
    const keys = path.split('.');
    let current = config;
    
    for (const key of keys) {
      if (current && typeof current === 'object') {
        current = current[key];
      } else {
        return undefined;
      }
    }
    
    return current;
  };

  // 根据组件类型渲染不同的表单字段
  const renderFields = () => {
    const commonFields = [
      { key: 'name', label: '名称', type: 'text' },
      { key: 'enabled', label: '启用', type: 'boolean' },
    ];

    const typeSpecificFields = getTypeSpecificFields(componentType);
    const allFields = [...commonFields, ...typeSpecificFields];

    return allFields
      .filter(field => showAdvanced || !field.advanced)
      .map(field => (
        <div key={field.key} className="space-y-2">
          <label className="form-label">
            {field.label}
            {field.required && <span className="text-error-500 ml-1">*</span>}
          </label>
          
          {field.type === 'text' && (
            <input
              type="text"
              value={getFieldValue(field.key) || ''}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
              className="form-input"
              placeholder={field.placeholder}
            />
          )}
          
          {field.type === 'number' && (
            <input
              type="number"
              value={getFieldValue(field.key) || ''}
              onChange={(e) => handleFieldChange(field.key, parseInt(e.target.value) || 0)}
              className="form-input"
              placeholder={field.placeholder}
            />
          )}
          
          {field.type === 'boolean' && (
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={getFieldValue(field.key) || false}
                onChange={(e) => handleFieldChange(field.key, e.target.checked)}
                className="form-checkbox"
              />
              <span className="ml-2 text-sm text-gray-600">{field.description}</span>
            </div>
          )}
          
          {field.type === 'select' && (
            <select
              value={getFieldValue(field.key) || ''}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
              className="form-input"
            >
              <option value="">请选择...</option>
              {field.options?.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          )}
          
          {field.type === 'textarea' && (
            <textarea
              value={getFieldValue(field.key) || ''}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
              rows={3}
              className="form-input resize-none"
              placeholder={field.placeholder}
            />
          )}
          
          {field.description && (
            <p className="text-sm text-gray-500">{field.description}</p>
          )}
        </div>
      ));
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {renderFields()}
    </div>
  );
}

// Text Editor Component
interface TextEditorProps {
  value: string;
  onChange: (value: string) => void;
  mode: EditorMode;
}

const TextEditor = React.forwardRef<HTMLTextAreaElement, TextEditorProps>(
  ({ value, onChange, mode }, ref) => {
    return (
      <div className="space-y-4">
        <div className="border border-gray-300 rounded-lg overflow-hidden">
          <div className="bg-gray-50 px-3 py-2 border-b border-gray-300">
            <span className="text-sm font-medium text-gray-700">
              {mode === 'yaml' ? 'YAML 配置' : 'JSON 配置'}
            </span>
          </div>
          <textarea
            ref={ref}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="w-full h-96 p-4 font-mono text-sm border-0 resize-none focus:ring-0"
            placeholder={`请输入 ${mode.toUpperCase()} 格式的配置...`}
            spellCheck={false}
          />
        </div>
      </div>
    );
  }
);

TextEditor.displayName = 'TextEditor';

// Helper function to get type-specific fields
function getTypeSpecificFields(componentType: string) {
  const fields: any[] = [];
  
  switch (componentType) {
    case 'elasticsearch':
      fields.push(
        { key: 'cluster.name', label: '集群名称', type: 'text', required: true },
        { key: 'node.name', label: '节点名称', type: 'text', required: true },
        { key: 'network.host', label: '网络主机', type: 'text', placeholder: '0.0.0.0' },
        { key: 'http.port', label: 'HTTP端口', type: 'number', placeholder: '9200' },
        { key: 'discovery.type', label: '发现类型', type: 'select', options: [
          { value: 'single-node', label: '单节点' },
          { value: 'zen', label: 'Zen发现' }
        ]},
        { key: 'path.data', label: '数据路径', type: 'text', advanced: true },
        { key: 'path.logs', label: '日志路径', type: 'text', advanced: true }
      );
      break;
      
    case 'logstash':
      fields.push(
        { key: 'path.data', label: '数据路径', type: 'text' },
        { key: 'path.logs', label: '日志路径', type: 'text' },
        { key: 'pipeline.workers', label: '管道工作线程', type: 'number', placeholder: '2' },
        { key: 'pipeline.batch.size', label: '批处理大小', type: 'number', placeholder: '125' },
        { key: 'http.host', label: 'HTTP主机', type: 'text', placeholder: '0.0.0.0' },
        { key: 'http.port', label: 'HTTP端口', type: 'number', placeholder: '9600' }
      );
      break;
      
    default:
      // Beat类组件的通用字段
      fields.push(
        { key: 'path.home', label: '主目录', type: 'text' },
        { key: 'path.config', label: '配置目录', type: 'text' },
        { key: 'path.data', label: '数据目录', type: 'text' },
        { key: 'path.logs', label: '日志目录', type: 'text' },
        { key: 'output.elasticsearch.hosts', label: 'Elasticsearch主机', type: 'textarea', 
          placeholder: '- localhost:9200', description: '每行一个主机地址' }
      );
  }
  
  return fields;
}

// Extend Window interface for timeout
declare global {
  interface Window {
    configValidationTimeout: number;
  }
}
