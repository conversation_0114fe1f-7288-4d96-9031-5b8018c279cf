package com.xhcai.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 项目团队成员实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "project_team_member")
@Schema(description = "项目团队成员")
@TableName("project_team_member")
public class ProjectTeamMember extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    @Column(name = "project_id", nullable = false, length = 36)
    @TableField("project_id")
    @Schema(description = "项目ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String projectId;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false, length = 36)
    @TableField("user_id")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;

    /**
     * 成员角色
     */
    @Size(max = 50, message = "成员角色长度不能超过50个字符")
    @Column(name = "role", length = 50)
    @TableField("role")
    @Schema(description = "成员角色")
    private String role;

    /**
     * 加入时间
     */
    @Column(name = "join_time")
    @TableField("join_time")
    @Schema(description = "加入时间")
    private LocalDateTime joinTime;

    // 非数据库字段 - 关联的用户信息
    @TableField(exist = false)
    @Transient
    @Schema(description = "关联的用户信息")
    private Object user;

    // Getters and Setters
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public LocalDateTime getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(LocalDateTime joinTime) {
        this.joinTime = joinTime;
    }

    public Object getUser() {
        return user;
    }

    public void setUser(Object user) {
        this.user = user;
    }
}
