package com.xhcai.modules.dify.dto.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Dify 应用会话参数响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 应用会话参数响应")
public class DifyAppParametersResponseDTO {

    /**
     * 开场白
     */
    @JsonProperty("opening_statement")
    @Schema(description = "开场白")
    private String openingStatement;

    /**
     * 建议问题列表
     */
    @JsonProperty("suggested_questions")
    @Schema(description = "建议问题列表")
    private List<String> suggestedQuestions;

    /**
     * 回答后建议问题配置
     */
    @JsonProperty("suggested_questions_after_answer")
    @Schema(description = "回答后建议问题配置")
    private SuggestedQuestionsAfterAnswer suggestedQuestionsAfterAnswer;

    /**
     * 语音转文字配置
     */
    @JsonProperty("speech_to_text")
    @Schema(description = "语音转文字配置")
    private SpeechToText speechToText;

    /**
     * 文字转语音配置
     */
    @JsonProperty("text_to_speech")
    @Schema(description = "文字转语音配置")
    private TextToSpeech textToSpeech;

    /**
     * 检索资源配置
     */
    @JsonProperty("retriever_resource")
    @Schema(description = "检索资源配置")
    private RetrieverResource retrieverResource;

    /**
     * 注释回复配置
     */
    @JsonProperty("annotation_reply")
    @Schema(description = "注释回复配置")
    private AnnotationReply annotationReply;

    /**
     * 更多类似配置
     */
    @JsonProperty("more_like_this")
    @Schema(description = "更多类似配置")
    private MoreLikeThis moreLikeThis;

    /**
     * 用户输入表单
     */
    @JsonProperty("user_input_form")
    @Schema(description = "用户输入表单")
    private List<Map<String, Object>> userInputForm;

    /**
     * 敏感词规避配置
     */
    @JsonProperty("sensitive_word_avoidance")
    @Schema(description = "敏感词规避配置")
    private SensitiveWordAvoidance sensitiveWordAvoidance;

    /**
     * 文件上传配置
     */
    @JsonProperty("file_upload")
    @Schema(description = "文件上传配置")
    private FileUpload fileUpload;

    /**
     * 系统参数
     */
    @JsonProperty("system_parameters")
    @Schema(description = "系统参数")
    private SystemParameters systemParameters;

    /**
     * 回答后建议问题配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "回答后建议问题配置")
    public static class SuggestedQuestionsAfterAnswer {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 语音转文字配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "语音转文字配置")
    public static class SpeechToText {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 文字转语音配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "文字转语音配置")
    public static class TextToSpeech {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;

        /**
         * 语音
         */
        @Schema(description = "语音")
        private String voice;

        /**
         * 语言
         */
        @Schema(description = "语言")
        private String language;
    }

    /**
     * 检索资源配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "检索资源配置")
    public static class RetrieverResource {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 注释回复配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "注释回复配置")
    public static class AnnotationReply {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 更多类似配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "更多类似配置")
    public static class MoreLikeThis {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 敏感词规避配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "敏感词规避配置")
    public static class SensitiveWordAvoidance {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 文件上传配置
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "文件上传配置")
    public static class FileUpload {
        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled;

        /**
         * 允许的文件类型
         */
        @JsonProperty("allowed_file_types")
        @Schema(description = "允许的文件类型")
        private List<String> allowedFileTypes;

        /**
         * 允许的文件扩展名
         */
        @JsonProperty("allowed_file_extensions")
        @Schema(description = "允许的文件扩展名")
        private List<String> allowedFileExtensions;

        /**
         * 允许的文件上传方法
         */
        @JsonProperty("allowed_file_upload_methods")
        @Schema(description = "允许的文件上传方法")
        private List<String> allowedFileUploadMethods;

        /**
         * 数量限制
         */
        @JsonProperty("number_limits")
        @Schema(description = "数量限制")
        private Integer numberLimits;

        /**
         * 文件上传配置
         */
        @JsonProperty("fileUploadConfig")
        @Schema(description = "文件上传配置")
        private FileUploadConfig fileUploadConfig;
    }

    /**
     * 文件上传配置详情
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "文件上传配置详情")
    public static class FileUploadConfig {
        /**
         * 文件大小限制（MB）
         */
        @JsonProperty("file_size_limit")
        @Schema(description = "文件大小限制（MB）")
        private Integer fileSizeLimit;

        /**
         * 批量数量限制
         */
        @JsonProperty("batch_count_limit")
        @Schema(description = "批量数量限制")
        private Integer batchCountLimit;

        /**
         * 图片文件大小限制（MB）
         */
        @JsonProperty("image_file_size_limit")
        @Schema(description = "图片文件大小限制（MB）")
        private Integer imageFileSizeLimit;

        /**
         * 视频文件大小限制（MB）
         */
        @JsonProperty("video_file_size_limit")
        @Schema(description = "视频文件大小限制（MB）")
        private Integer videoFileSizeLimit;

        /**
         * 音频文件大小限制（MB）
         */
        @JsonProperty("audio_file_size_limit")
        @Schema(description = "音频文件大小限制（MB）")
        private Integer audioFileSizeLimit;

        /**
         * 工作流文件上传限制（MB）
         */
        @JsonProperty("workflow_file_upload_limit")
        @Schema(description = "工作流文件上传限制（MB）")
        private Integer workflowFileUploadLimit;
    }

    /**
     * 系统参数
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "系统参数")
    public static class SystemParameters {
        /**
         * 图片文件大小限制（MB）
         */
        @JsonProperty("image_file_size_limit")
        @Schema(description = "图片文件大小限制（MB）")
        private Integer imageFileSizeLimit;

        /**
         * 视频文件大小限制（MB）
         */
        @JsonProperty("video_file_size_limit")
        @Schema(description = "视频文件大小限制（MB）")
        private Integer videoFileSizeLimit;

        /**
         * 音频文件大小限制（MB）
         */
        @JsonProperty("audio_file_size_limit")
        @Schema(description = "音频文件大小限制（MB）")
        private Integer audioFileSizeLimit;

        /**
         * 文件大小限制（MB）
         */
        @JsonProperty("file_size_limit")
        @Schema(description = "文件大小限制（MB）")
        private Integer fileSizeLimit;

        /**
         * 工作流文件上传限制（MB）
         */
        @JsonProperty("workflow_file_upload_limit")
        @Schema(description = "工作流文件上传限制（MB）")
        private Integer workflowFileUploadLimit;
    }
}
