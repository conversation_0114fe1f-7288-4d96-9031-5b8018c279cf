package com.xhcai.modules.rag.dto;

import com.xhcai.modules.rag.enums.DocumentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文档状态更新DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档状态更新DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentStatusUpdateDTO {

    /**
     * 文档ID
     */
    @Schema(description = "文档ID", example = "doc123")
    private String documentId;

    /**
     * 文档名称
     */
    @Schema(description = "文档名称", example = "用户手册.pdf")
    private String documentName;

    /**
     * 文档状态
     */
    @Schema(description = "文档状态")
    private DocumentStatus status;

    /**
     * 处理进度 (0-100)
     */
    @Schema(description = "处理进度", example = "75")
    private Integer progress;

    /**
     * 当前处理阶段描述
     */
    @Schema(description = "当前处理阶段描述", example = "正在分段处理...")
    private String currentStage;

    /**
     * 分段数量
     */
    @Schema(description = "分段数量", example = "15")
    private Integer segmentCount;

    /**
     * 已处理分段数量
     */
    @Schema(description = "已处理分段数量", example = "10")
    private Integer processedSegmentCount;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> extraData;

    /**
     * 创建处理中状态更新
     */
    public static DocumentStatusUpdateDTO createProcessingUpdate(String documentId, String documentName, 
                                                               String currentStage, Integer progress,
                                                               String tenantId, String userId) {
        return DocumentStatusUpdateDTO.builder()
                .documentId(documentId)
                .documentName(documentName)
                .status(DocumentStatus.PROCESSING)
                .progress(progress)
                .currentStage(currentStage)
                .updateTime(LocalDateTime.now())
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 创建分段中状态更新
     */
    public static DocumentStatusUpdateDTO createSegmentingUpdate(String documentId, String documentName,
                                                               Integer segmentCount, Integer processedSegmentCount,
                                                               String tenantId, String userId) {
        int progress = segmentCount > 0 ? (processedSegmentCount * 100 / segmentCount) : 0;
        return DocumentStatusUpdateDTO.builder()
                .documentId(documentId)
                .documentName(documentName)
                .status(DocumentStatus.SEGMENTING)
                .progress(progress)
                .currentStage("正在分段处理...")
                .segmentCount(segmentCount)
                .processedSegmentCount(processedSegmentCount)
                .updateTime(LocalDateTime.now())
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 创建完成状态更新
     */
    public static DocumentStatusUpdateDTO createCompletedUpdate(String documentId, String documentName,
                                                              Integer segmentCount, String tenantId, String userId) {
        return DocumentStatusUpdateDTO.builder()
                .documentId(documentId)
                .documentName(documentName)
                .status(DocumentStatus.SEGMENTED)
                .progress(100)
                .currentStage("分段完成")
                .segmentCount(segmentCount)
                .processedSegmentCount(segmentCount)
                .updateTime(LocalDateTime.now())
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

    /**
     * 创建错误状态更新
     */
    public static DocumentStatusUpdateDTO createErrorUpdate(String documentId, String documentName,
                                                           String errorMessage, String tenantId, String userId) {
        return DocumentStatusUpdateDTO.builder()
                .documentId(documentId)
                .documentName(documentName)
                .status(DocumentStatus.SEGMENT_ERROR)
                .progress(0)
                .currentStage("分段失败")
                .errorMessage(errorMessage)
                .updateTime(LocalDateTime.now())
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }
}
