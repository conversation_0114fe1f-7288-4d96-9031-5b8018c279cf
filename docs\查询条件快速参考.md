# 查询条件快速参考

## 选择决策树

```
需要什么查询条件？
├── 只要分页
│   └── 继承 PageQueryDTO
├── 分页 + 时间范围
│   └── 继承 PageTimeRangeQueryDTO
├── 分页 + 状态
│   └── 继承 PageStatusQueryDTO
├── 分页 + 关键字搜索
│   └── 继承 PageKeywordQueryDTO
├── 分页 + 多种条件组合
│   └── 继承 PageQueryDTO + 实现对应接口
└── 需要所有条件
    └── 继承 FullQueryDTO
```

## 常用组合模板

### 1. 用户管理类查询

```java
// 用户、角色、部门等管理功能
public class ManagementQueryDTO extends FullQueryDTO {
    // 业务字段
    private String code;
    private String name;
    private Long parentId;
    
    // getter/setter...
}
```

### 2. 日志类查询

```java
// 操作日志、系统日志等
public class LogQueryDTO extends PageTimeRangeQueryDTO implements Keywordable {
    @Schema(description = "搜索关键字")
    private String keyword;
    
    @Schema(description = "日志类型")
    private String logType;
    
    // 实现Keywordable接口
    @Override
    public String getKeyword() {
        return keyword;
    }
    
    // getter/setter...
}
```

### 3. 配置类查询

```java
// 系统配置、字典数据等
public class ConfigQueryDTO extends PageStatusQueryDTO implements Keywordable {
    @Schema(description = "搜索关键字")
    private String keyword;
    
    @Schema(description = "配置分组")
    private String configGroup;
    
    // 实现Keywordable接口
    @Override
    public String getKeyword() {
        return keyword;
    }
    
    // getter/setter...
}
```

### 4. 统计类查询

```java
// 数据统计、报表查询等（通常不需要分页）
public class StatisticsQueryDTO extends TimeRangeQueryDTO implements DataScopeable {
    @Schema(description = "租户ID")
    private Long tenantId;
    
    @Schema(hidden = true)
    private String dataScope;
    
    @Schema(description = "统计维度")
    private String dimension;
    
    // 实现DataScopeable接口
    @Override
    public Long getTenantId() {
        return tenantId;
    }
    
    @Override
    public String getDataScope() {
        return dataScope;
    }
    
    @Override
    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }
    
    // getter/setter...
}
```

## 快速代码模板

### DTO类模板

```java
@Schema(description = "XXX查询条件")
public class XxxQueryDTO extends [选择基类] implements [选择接口] {
    
    // ========== 业务字段 ==========
    @Schema(description = "业务字段1")
    private String field1;
    
    @Schema(description = "业务字段2")
    private Long field2;
    
    // ========== 通用查询条件字段（根据实现的接口添加） ==========
    
    // TimeRangeable需要的字段
    @Schema(description = "开始时间")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$")
    private String beginTime;
    
    @Schema(description = "结束时间")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$")
    private String endTime;
    
    // Statusable需要的字段
    @Schema(description = "状态")
    @Pattern(regexp = "^[01]$")
    private String status;
    
    // Keywordable需要的字段
    @Schema(description = "搜索关键字")
    @Size(max = 100)
    private String keyword;
    
    // DataScopeable需要的字段
    @Schema(description = "租户ID")
    private Long tenantId;
    
    @Schema(hidden = true)
    private String dataScope;
    
    // ========== 接口实现方法 ==========
    // 根据实现的接口添加对应的getter/setter方法
    
    // ========== 业务字段的getter/setter ==========
    // ...
    
    @Override
    public String toString() {
        return "XxxQueryDTO{" +
                "current=" + getCurrent() +
                ", size=" + getSize() +
                // 添加所有字段...
                '}';
    }
}
```

### Service方法模板

```java
public PageResult<Xxx> queryXxx(XxxQueryDTO query) {
    // 设置数据权限（如果需要）
    if (query instanceof DataScopeable) {
        DataScopeUtils.setDataScope((DataScopeable) query);
    }
    
    // 构建查询条件
    QueryConditionBuilder builder = QueryConditionUtils.buildBasicConditions(
        query,
        "create_time",  // 时间字段
        "status",       // 状态字段
        "field1", "field2"  // 搜索字段
    );
    
    // 添加业务特有条件
    if (query.getBusinessField() != null) {
        builder.addEqualCondition("business_field", "businessField", query.getBusinessField());
    }
    
    // 执行查询
    String whereClause = builder.buildWhereClause();
    Map<String, Object> params = builder.getParameters();
    
    Long total = query.getSearchCount() ? 
        xxxMapper.countByCondition(whereClause, params) : 0L;
    List<Xxx> records = xxxMapper.selectByCondition(query, whereClause, params);
    
    return new PageResult<>(records, total, query.getCurrent(), query.getSize());
}
```

### Mapper方法模板

```java
@Select({
    "<script>",
    "SELECT * FROM xxx_table",
    "<if test='whereClause != null and whereClause != \"\"'>",
    "WHERE ${whereClause}",
    "</if>",
    "<if test='query.orderBy != null and query.orderBy != \"\"'>",
    "ORDER BY ${query.orderBy} ${query.orderDirection}",
    "</if>",
    "<if test='query.current != null and query.size != null'>",
    "LIMIT #{query.offset}, #{query.limit}",
    "</if>",
    "</script>"
})
List<Xxx> selectByCondition(@Param("query") XxxQueryDTO query,
                           @Param("whereClause") String whereClause,
                           @Param("params") Map<String, Object> params);

@Select({
    "<script>",
    "SELECT COUNT(*) FROM xxx_table",
    "<if test='whereClause != null and whereClause != \"\"'>",
    "WHERE ${whereClause}",
    "</if>",
    "</script>"
})
Long countByCondition(@Param("whereClause") String whereClause,
                     @Param("params") Map<String, Object> params);
```

## 常见问题解决

### Q1: 如何处理复杂的OR条件？

```java
// 在Service中使用QueryConditionBuilder
QueryConditionBuilder builder = new QueryConditionBuilder();

// 添加基础条件
builder.addStatusCondition(query, "status");

// 添加复杂OR条件
if (query.hasKeyword()) {
    String orCondition = "(username LIKE #{keyword} OR nickname LIKE #{keyword} OR email LIKE #{keyword})";
    builder.addCustomCondition(orCondition, "keyword", query.getLikeKeyword());
}
```

### Q2: 如何处理IN查询？

```java
// 在Service中
if (CollectionUtils.isNotEmpty(query.getIds())) {
    builder.addInCondition("id", "ids", query.getIds());
}
```

### Q3: 如何处理NULL值查询？

```java
// 在Service中
if (query.getNullableField() != null) {
    if ("NULL".equals(query.getNullableField())) {
        builder.addCustomCondition("nullable_field IS NULL", null, null);
    } else {
        builder.addEqualCondition("nullable_field", "nullableField", query.getNullableField());
    }
}
```

### Q4: 如何处理日期范围查询？

```java
// 使用TimeRangeable接口的便捷方法
if (query.hasTimeRange()) {
    LocalDateTime beginDateTime = query.getBeginDateTime();
    LocalDateTime endDateTime = query.getEndDateTime();
    
    if (beginDateTime != null) {
        builder.addCustomCondition("create_time >= #{beginDateTime}", "beginDateTime", beginDateTime);
    }
    if (endDateTime != null) {
        builder.addCustomCondition("create_time <= #{endDateTime}", "endDateTime", endDateTime);
    }
}
```

## 性能优化建议

1. **合理使用searchCount**：不需要总数时设置为false
2. **索引优化**：为常用查询字段建立索引
3. **分页限制**：限制每页最大记录数
4. **缓存策略**：对于相对静态的数据考虑缓存
5. **SQL优化**：避免SELECT *，只查询需要的字段

## 注意事项

1. **数据权限**：DataScope字段由系统设置，前端不应传递
2. **参数验证**：使用@Valid注解进行参数验证
3. **SQL注入**：使用参数化查询，避免字符串拼接
4. **字段映射**：注意数据库字段名与Java字段名的映射
5. **异常处理**：合理处理查询异常情况
