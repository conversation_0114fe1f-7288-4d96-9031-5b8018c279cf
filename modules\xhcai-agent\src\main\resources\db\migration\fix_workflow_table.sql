-- 修复agent_workflow表结构
-- 添加缺失的字段（如果不存在）

-- 检查并添加nodes_data字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent_workflow ADD COLUMN nodes_data TEXT COMMENT "Vue Flow节点数据JSON"',
        'SELECT "nodes_data字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent_workflow' 
        AND COLUMN_NAME = 'nodes_data'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加edges_data字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent_workflow ADD COLUMN edges_data TEXT COMMENT "Vue Flow边数据JSON"',
        'SELECT "edges_data字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent_workflow' 
        AND COLUMN_NAME = 'edges_data'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加viewport_config字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent_workflow ADD COLUMN viewport_config TEXT COMMENT "视口配置JSON"',
        'SELECT "viewport_config字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent_workflow' 
        AND COLUMN_NAME = 'viewport_config'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加node_library字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent_workflow ADD COLUMN node_library TEXT COMMENT "节点库配置JSON"',
        'SELECT "node_library字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent_workflow' 
        AND COLUMN_NAME = 'node_library'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 数据迁移：将旧字段数据复制到新字段（如果需要）
UPDATE agent_workflow 
SET nodes_data = nodes_config 
WHERE nodes_config IS NOT NULL 
    AND (nodes_data IS NULL OR nodes_data = '');

UPDATE agent_workflow 
SET edges_data = connections_config 
WHERE connections_config IS NOT NULL 
    AND (edges_data IS NULL OR edges_data = '');

-- 为新字段设置默认值
UPDATE agent_workflow 
SET viewport_config = '{"x":0,"y":0,"zoom":1}' 
WHERE viewport_config IS NULL OR viewport_config = '';

UPDATE agent_workflow 
SET node_library = '{"categories":[]}' 
WHERE node_library IS NULL OR node_library = '';

-- 显示表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'agent_workflow' 
    AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
