package com.xhcai.modules.agent.mapper;

import java.time.LocalDateTime;
import java.util.List;

import com.xhcai.modules.agent.vo.HistoryStatsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.agent.dto.AgentWorkflowHistoryQueryDTO;
import com.xhcai.modules.agent.entity.AgentWorkflowHistory;
import com.xhcai.modules.agent.vo.AgentWorkflowHistoryVO;

/**
 * 智能体工作流历史记录Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AgentWorkflowHistoryMapper extends BaseMapper<AgentWorkflowHistory> {

    /**
     * 分页查询工作流历史记录
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 历史记录分页列表
     */
    @Select("""
            <script>
            SELECT h.* FROM agent_workflow_history h
            <where>
                h.deleted = 0
                <if test="query.workflowId != null and query.workflowId != ''">
                    AND h.workflow_id = #{query.workflowId}
                </if>
                <if test="query.agentId != null and query.agentId != ''">
                    AND h.agent_id = #{query.agentId}
                </if>
                <if test="query.configHash != null and query.configHash != ''">
                    AND h.config_hash = #{query.configHash}
                </if>
                <if test="query.operationType != null and query.operationType != ''">
                    AND h.operation_type = #{query.operationType}
                </if>
                <if test="query.operationUserId != null and query.operationUserId != ''">
                    AND h.operation_user_id = #{query.operationUserId}
                </if>
                <if test="query.operationUserName != null and query.operationUserName != ''">
                    AND h.operation_user_name LIKE CONCAT('%', #{query.operationUserName}, '%')
                </if>
                <if test="query.isMajorChange != null">
                    AND h.is_major_change = #{query.isMajorChange}
                </if>
                <if test="query.version != null">
                    AND h.version = #{query.version}
                </if>
                <if test="query.keyword != null and query.keyword != ''">
                    AND (h.operation_desc LIKE CONCAT('%', #{query.keyword}, '%')
                         OR h.change_summary LIKE CONCAT('%', #{query.keyword}, '%'))
                </if>
                <if test="query.operationStartTime != null">
                    AND h.operation_time >= #{query.operationStartTime}
                </if>
                <if test="query.operationEndTime != null">
                    AND h.operation_time &lt;= #{query.operationEndTime}
                </if>
                <if test="query.startTime != null">
                    AND h.create_time >= #{query.startTime}
                </if>
                <if test="query.endTime != null">
                    AND h.create_time &lt;= #{query.endTime}
                </if>
            </where>
            ORDER BY
            <choose>
                <when test="query.sortField != null and query.sortField != ''">
                    <choose>
                        <when test="query.sortField == 'operation_time'">h.operation_time</when>
                        <when test="query.sortField == 'config_size'">h.config_size</when>
                        <when test="query.sortField == 'version'">h.version</when>
                        <when test="query.sortField == 'create_time'">h.create_time</when>
                        <otherwise>h.operation_time</otherwise>
                    </choose>
                    <choose>
                        <when test="query.sortOrder != null and query.sortOrder.toLowerCase() == 'asc'">ASC</when>
                        <otherwise>DESC</otherwise>
                    </choose>
                </when>
                <otherwise>h.operation_time DESC, h.create_time DESC</otherwise>
            </choose>
            </script>
            """)
    IPage<AgentWorkflowHistoryVO> selectHistoryPage(Page<AgentWorkflowHistoryVO> page,
            @Param("query") AgentWorkflowHistoryQueryDTO queryDTO);

    /**
     * 根据工作流ID查询历史记录列表
     *
     * @param workflowId 工作流ID
     * @param tenantId 租户ID
     * @return 历史记录列表
     */
    @Select("""
            SELECT h.* FROM agent_workflow_history h
            WHERE h.workflow_id = #{workflowId}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            ORDER BY h.operation_time DESC, h.create_time DESC
            """)
    List<AgentWorkflowHistoryVO> selectHistoryByWorkflowId(@Param("workflowId") String workflowId,
            @Param("tenantId") String tenantId);

    /**
     * 根据智能体ID查询历史记录列表
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 历史记录列表
     */
    @Select("""
            SELECT h.* FROM agent_workflow_history h
            WHERE h.agent_id = #{agentId}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            ORDER BY h.operation_time DESC, h.create_time DESC
            """)
    List<AgentWorkflowHistoryVO> selectHistoryByAgentId(@Param("agentId") String agentId,
            @Param("tenantId") String tenantId);

    /**
     * 根据配置哈希值查询历史记录
     *
     * @param configHash 配置哈希值
     * @param tenantId 租户ID
     * @return 历史记录
     */
    @Select("""
            SELECT h.* FROM agent_workflow_history h
            WHERE h.config_hash = #{configHash}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            ORDER BY h.operation_time DESC, h.create_time DESC
            LIMIT 1
            """)
    AgentWorkflowHistoryVO selectHistoryByConfigHash(@Param("configHash") String configHash,
            @Param("tenantId") String tenantId);

    /**
     * 查询最近的历史记录
     *
     * @param workflowId 工作流ID
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 最近的历史记录列表
     */
    @Select("""
            SELECT h.* FROM agent_workflow_history h
            WHERE h.workflow_id = #{workflowId}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            ORDER BY h.operation_time DESC, h.create_time DESC
            LIMIT #{limit}
            """)
    List<AgentWorkflowHistoryVO> selectRecentHistory(@Param("workflowId") String workflowId,
            @Param("tenantId") String tenantId,
            @Param("limit") Integer limit);

    /**
     * 统计工作流历史记录数量
     *
     * @param workflowId 工作流ID
     * @param tenantId 租户ID
     * @return 历史记录数量
     */
    @Select("""
            SELECT COUNT(*) FROM agent_workflow_history h
            WHERE h.workflow_id = #{workflowId}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            """)
    Long countHistoryByWorkflowId(@Param("workflowId") String workflowId,
            @Param("tenantId") String tenantId);

    /**
     * 统计智能体历史记录数量
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 历史记录数量
     */
    @Select("""
            SELECT COUNT(*) FROM agent_workflow_history h
            WHERE h.agent_id = #{agentId}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            """)
    Long countHistoryByAgentId(@Param("agentId") String agentId,
            @Param("tenantId") String tenantId);

    /**
     * 统计指定时间范围内的历史记录数量
     *
     * @param workflowId 工作流ID
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 历史记录数量
     */
    @Select("""
            <script>
            SELECT COUNT(*) FROM agent_workflow_history h
            WHERE h.workflow_id = #{workflowId}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            <if test="startTime != null">
                AND h.operation_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND h.operation_time &lt;= #{endTime}
            </if>
            </script>
            """)
    Long countHistoryByTimeRange(@Param("workflowId") String workflowId,
            @Param("tenantId") String tenantId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 查询重要变更的历史记录
     *
     * @param workflowId 工作流ID
     * @param tenantId 租户ID
     * @return 重要变更历史记录列表
     */
    @Select("""
            SELECT h.* FROM agent_workflow_history h
            WHERE h.workflow_id = #{workflowId}
              AND h.tenant_id = #{tenantId}
              AND h.is_major_change = true
              AND h.deleted = 0
            ORDER BY h.operation_time DESC, h.create_time DESC
            """)
    List<AgentWorkflowHistoryVO> selectMajorChanges(@Param("workflowId") String workflowId,
            @Param("tenantId") String tenantId);

    /**
     * 查询指定用户的操作历史
     *
     * @param workflowId 工作流ID
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户操作历史列表
     */
    @Select("""
            SELECT h.* FROM agent_workflow_history h
            WHERE h.workflow_id = #{workflowId}
              AND h.tenant_id = #{tenantId}
              AND h.operation_user_id = #{userId}
              AND h.deleted = 0
            ORDER BY h.operation_time DESC, h.create_time DESC
            """)
    List<AgentWorkflowHistoryVO> selectHistoryByUser(@Param("workflowId") String workflowId,
            @Param("tenantId") String tenantId,
            @Param("userId") String userId);

    /**
     * 查询指定操作类型的历史记录
     *
     * @param workflowId 工作流ID
     * @param tenantId 租户ID
     * @param operationType 操作类型
     * @return 操作历史列表
     */
    @Select("""
            SELECT h.* FROM agent_workflow_history h
            WHERE h.workflow_id = #{workflowId}
              AND h.tenant_id = #{tenantId}
              AND h.operation_type = #{operationType}
              AND h.deleted = 0
            ORDER BY h.operation_time DESC, h.create_time DESC
            """)
    List<AgentWorkflowHistoryVO> selectHistoryByOperationType(@Param("workflowId") String workflowId,
            @Param("tenantId") String tenantId,
            @Param("operationType") String operationType);

    /**
     * 删除指定时间之前的历史记录
     *
     * @param beforeTime 时间点
     * @param tenantId 租户ID
     * @return 删除的记录数量
     */
    @Update("""
            UPDATE agent_workflow_history
            SET deleted = 1, update_time = NOW()
            WHERE operation_time < #{beforeTime}
              AND tenant_id = #{tenantId}
              AND deleted = 0
            """)
    int deleteHistoryBeforeTime(@Param("beforeTime") LocalDateTime beforeTime,
            @Param("tenantId") String tenantId);

    /**
     * 查询历史记录统计信息
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 统计信息
     */
    @Select("""
            SELECT
                COUNT(*) as total_count,
                SUM(CASE WHEN is_major_change = true THEN 1 ELSE 0 END) as major_change_count,
                MIN(operation_time) as first_operation_time,
                MAX(operation_time) as last_operation_time,
                SUM(COALESCE(config_size, 0)) as total_config_size,
                COUNT(DISTINCT operation_user_id) as unique_operator_count
            FROM agent_workflow_history h
            WHERE h.agent_id = #{agentId}
              AND h.tenant_id = #{tenantId}
              AND h.deleted = 0
            """)
    HistoryStatsVO selectHistoryStats(@Param("agentId") String agentId,
                                      @Param("tenantId") String tenantId);
}
