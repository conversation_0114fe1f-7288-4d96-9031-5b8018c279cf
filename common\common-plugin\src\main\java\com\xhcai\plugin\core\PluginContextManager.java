package com.xhcai.plugin.core;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.pf4j.DefaultPluginManager;
import org.pf4j.PluginManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

/**
 * 多插件上下文管理器 管理多个独立的插件上下文，每种插件类型有独立的环境
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class PluginContextManager {

    /**
     * 插件根目录
     */
    @Value("${xhcai.plugin.root-path:plugins}")
    private String pluginRootPath;

    /**
     * 是否开发模式
     */
    @Value("${xhcai.plugin.dev-mode:true}")
    private boolean devMode;

    /**
     * 插件上下文缓存
     */
    private final Map<PluginType, PluginContext> contextCache = new ConcurrentHashMap<>();

    /**
     * 初始化插件上下文管理器
     */
    @PostConstruct
    public void initialize() {
        log.info("Initializing Plugin Context Manager...");
        log.info("Plugin root path: {}", pluginRootPath);
        log.info("Development mode: {}", devMode);

        // 为每种插件类型创建独立的上下文
        for (PluginType pluginType : PluginType.values()) {
            createPluginContext(pluginType);
        }

        log.info("Plugin Context Manager initialized successfully");
    }

    /**
     * 销毁插件上下文管理器
     */
    @PreDestroy
    public void destroy() {
        log.info("Destroying Plugin Context Manager...");

        // 停止所有插件上下文
        contextCache.values().forEach(context -> {
            try {
                context.stop();
            } catch (Exception e) {
                log.error("Failed to stop plugin context for type: {}", context.getPluginType(), e);
            }
        });

        contextCache.clear();
        log.info("Plugin Context Manager destroyed");
    }

    /**
     * 创建插件上下文
     */
    private void createPluginContext(PluginType pluginType) {
        try {
            log.info("Creating plugin context for type: {}", pluginType);

            // 构建插件目录路径
            Path pluginPath = Paths.get(pluginRootPath, pluginType.getCode());

            // 创建插件管理器
            PluginManager pluginManager = createPluginManager(pluginPath);

            // 创建插件上下文
            PluginContext context = new PluginContext(pluginType, pluginManager);

            // 启动上下文
            context.start();

            // 缓存上下文
            contextCache.put(pluginType, context);

            log.info("Plugin context for type {} created successfully", pluginType);

        } catch (Exception e) {
            log.error("Failed to create plugin context for type: {}", pluginType, e);
            throw new RuntimeException("Failed to create plugin context", e);
        }
    }

    /**
     * 创建插件管理器
     */
    private PluginManager createPluginManager(Path pluginPath) {
        if (devMode) {
            // 开发模式：直接从源码目录加载
            log.info("Creating development plugin manager for path: {}", pluginPath);
            return new DefaultPluginManager(pluginPath) {
                @Override
                protected void initialize() {
                    super.initialize();
                    // 开发模式特殊配置
                    setSystemVersion("1.0.0");
                }
            };
        } else {
            // 生产模式：从JAR文件加载
            log.info("Creating production plugin manager for path: {}", pluginPath);
            return new DefaultPluginManager(pluginPath) {
                @Override
                protected void initialize() {
                    super.initialize();
                    setSystemVersion("1.0.0");
                }
            };
        }
    }

    /**
     * 获取插件上下文
     */
    public PluginContext getPluginContext(PluginType pluginType) {
        PluginContext context = contextCache.get(pluginType);
        if (context == null) {
            throw new IllegalArgumentException("Plugin context not found for type: " + pluginType);
        }
        return context;
    }

    /**
     * 获取插件实例
     */
    public <T> List<T> getPluginInstances(PluginType pluginType, Class<T> serviceClass) {
        PluginContext context = getPluginContext(pluginType);
        return context.getPluginInstances(serviceClass);
    }

    /**
     * 获取指定插件的实例
     */
    public <T> T getPluginInstance(PluginType pluginType, String pluginId, Class<T> serviceClass) {
        PluginContext context = getPluginContext(pluginType);
        return context.getPluginInstance(pluginId, serviceClass);
    }

    /**
     * 重启插件上下文
     */
    public void restartPluginContext(PluginType pluginType) {
        log.info("Restarting plugin context for type: {}", pluginType);

        PluginContext context = contextCache.get(pluginType);
        if (context != null) {
            context.stop();
            contextCache.remove(pluginType);
        }

        createPluginContext(pluginType);
        log.info("Plugin context for type {} restarted successfully", pluginType);
    }

    /**
     * 获取所有插件上下文
     */
    public Map<PluginType, PluginContext> getAllContexts() {
        return Map.copyOf(contextCache);
    }
}
