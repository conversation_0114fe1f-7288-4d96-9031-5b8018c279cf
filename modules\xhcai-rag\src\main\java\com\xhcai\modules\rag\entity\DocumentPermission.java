package com.xhcai.modules.rag.entity;

import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 文档权限实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档权限实体")
@Entity
@Data
@Table(name = "document_permissions")
@TableName(value = "document_permissions", autoResultMap = true)
public class DocumentPermission extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 文档ID
     */
    @Schema(description = "文档ID", example = "doc123")
    @Column(name = "document_id", nullable = false, length = 32)
    @TableField("document_id")
    private String documentId;

    /**
     * 权限类型
     */
    @Schema(description = "权限类型", example = "public")
    @Column(name = "permission_type", nullable = false, length = 20)
    @TableField("permission_type")
    private String permissionType;

    /**
     * 目标ID（用户ID、角色ID、部门ID等）
     */
    @Schema(description = "目标ID", example = "user123")
    @Column(name = "target_id", length = 32)
    @TableField("target_id")
    private String targetId;

    /**
     * 目标类型（user、role、department等）
     */
    @Schema(description = "目标类型", example = "user")
    @Column(name = "target_type", length = 20)
    @TableField("target_type")
    private String targetType;

    /**
     * 权限级别（read、write、admin等）
     */
    @Schema(description = "权限级别", example = "read")
    @Column(name = "permission_level", nullable = false, length = 20)
    @TableField("permission_level")
    private String permissionLevel = "read";

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    @Column(name = "enabled", nullable = false)
    @TableField("enabled")
    private Boolean enabled = true;
}
