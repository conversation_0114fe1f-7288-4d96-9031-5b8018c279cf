<template>
  <div class="container bg-gradient-secondary">
    <!-- 左侧主要内容区域 -->
    <div class="main-content">
      <!-- 静态信息展示区域 -->
      <div class="info-container">
        <!-- 模型支持信息 -->
        <div class="info-card"
             ref="modelSection"
             @mouseenter="showModelExpandedLayer"
             @mouseleave="hideModelExpandedLayer">
          <div class="info-card-content">
            <div class="info-card-header">
              <div class="info-card-icon bg-gradient-to-br from-blue-500 to-purple-600">
                🧠
              </div>
              <h3 class="info-card-title">模型支持</h3>
              <div class="info-card-meta">{{ totalModelsCount }}个模型</div>
            </div>
            <div class="info-card-body">
              <div v-for="category in modelCategoriesPreview" :key="category.key" class="info-item">
                <div class="info-item-header">
                  <span class="info-item-icon">{{ category.icon }}</span>
                  <span class="info-item-title">{{ category.title }}</span>
                  <span class="info-item-count">({{ category.models.length }}个)</span>
                </div>
                <div class="info-item-content">
                  <div v-for="model in category.models.slice(0, 1)" :key="model.name" class="info-model">
                    <span class="model-name">{{ model.name }}</span>
                    <span class="model-params">{{ model.params }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 展开层 -->
          <div v-if="showModelExpanded"
               class="info-card-expanded"
               ref="modelExpanded"
               @mouseenter="clearModelTimeout"
               @mouseleave="hideModelExpandedLayer">
            <div class="info-card-content">
              <div class="info-card-header">
                <div class="info-card-icon bg-gradient-to-br from-blue-500 to-purple-600">
                  🧠
                </div>
                <h3 class="info-card-title">模型支持</h3>
                <div class="info-card-meta">{{ totalModelsCount }}个模型</div>
              </div>
              <div class="info-card-body expanded">
                <div v-for="category in modelCategories" :key="category.key" class="info-item expanded">
                  <div class="info-item-header">
                    <div class="category-icon">{{ category.icon }}</div>
                    <h4 class="category-title">{{ category.title }}</h4>
                    <span class="category-count">({{ category.models.length }}个)</span>
                  </div>
                  <div class="models-grid">
                    <div v-for="model in category.models" :key="model.name" class="model-card">
                      <span class="model-name">{{ model.name }}</span>
                      <span class="model-params">{{ model.params }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 平台统计信息 -->
        <div class="info-card"
             ref="statsSection"
             @mouseenter="showStatsExpandedLayer"
             @mouseleave="hideStatsExpandedLayer">
          <div class="info-card-content">
            <div class="info-card-header">
              <div class="info-card-icon bg-gradient-to-br from-purple-500 to-pink-600">
                📊
              </div>
              <h3 class="info-card-title">平台统计</h3>
              <div class="info-card-meta">实时数据</div>
            </div>
            <div class="info-card-body">
              <div class="stats-grid">
                <div v-for="stat in keyStats" :key="stat.key" class="stat-item">
                  <div class="stat-header">
                    <span class="stat-icon">{{ stat.icon }}</span>
                    <span class="stat-label">{{ stat.label }}</span>
                  </div>
                  <div class="stat-value">{{ stat.value }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 展开层 -->
          <div v-if="showStatsExpanded"
               class="info-card-expanded"
               ref="statsExpanded"
               @mouseenter="clearStatsTimeout"
               @mouseleave="hideStatsExpandedLayer">
            <div class="info-card-content">
              <div class="info-card-header">
                <div class="info-card-icon bg-gradient-to-br from-purple-500 to-pink-600">
                  📊
                </div>
                <h3 class="info-card-title">平台统计</h3>
                <div class="info-card-meta">实时数据</div>
              </div>
              <div class="info-card-body expanded">
                <div class="stats-grid expanded">
                  <div v-for="stat in allStats" :key="stat.key" class="stat-item expanded">
                    <div class="stat-header">
                      <span class="stat-icon">{{ stat.icon }}</span>
                      <span class="stat-label">{{ stat.label }}</span>
                    </div>
                    <div class="stat-value">{{ stat.value }}</div>
                    <div v-if="stat.trend" class="stat-trend">{{ stat.trend }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 版本更新公告 -->
        <div class="info-card"
             ref="updateSection"
             @mouseenter="showUpdateExpandedLayer"
             @mouseleave="hideUpdateExpandedLayer">
          <div class="info-card-content">
            <div class="info-card-header">
              <div class="info-card-icon bg-gradient-to-br from-green-500 to-emerald-600">
                🚀
              </div>
              <h3 class="info-card-title">版本更新</h3>
              <div class="info-card-meta">最新 {{ latestVersion }}</div>
            </div>
            <div class="info-card-body">
              <div v-for="update in updates.slice(0, 1)" :key="update.version" class="info-item">
                <div class="info-item-header">
                  <span class="version-tag">{{ update.version }}</span>
                  <span class="update-type" :class="getUpdateTypeClass(update.type)">{{ update.type }}</span>
                  <span class="update-date">{{ update.date }}</span>
                </div>
                <div class="info-item-content">
                  <div v-for="feature in update.features.slice(0, 2)" :key="feature" class="update-feature">
                    <span class="feature-icon">✨</span>
                    <span class="feature-text">{{ feature }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 展开层 -->
          <div v-if="showUpdateExpanded"
               class="info-card-expanded"
               ref="updateExpanded"
               @mouseenter="clearUpdateTimeout"
               @mouseleave="hideUpdateExpandedLayer">
            <div class="info-card-content">
              <div class="info-card-header">
                <div class="info-card-icon bg-gradient-to-br from-green-500 to-emerald-600">
                  🚀
                </div>
                <h3 class="info-card-title">版本更新</h3>
                <div class="info-card-meta">最新 {{ latestVersion }}</div>
              </div>
              <div class="info-card-body expanded">
                <div v-for="(update, index) in updates" :key="update.version" class="update-card" :class="{ latest: index === 0 }">
                  <div class="update-header">
                    <div class="update-info">
                      <span class="version-tag" :class="{ latest: index === 0 }">{{ update.version }}</span>
                      <span class="update-type" :class="getUpdateTypeClass(update.type)">{{ update.type }}</span>
                    </div>
                    <span class="update-date">{{ update.date }}</span>
                  </div>
                  <div class="update-features">
                    <div v-for="feature in update.features" :key="feature" class="update-feature">
                      <span class="feature-icon">✨</span>
                      <span class="feature-text">{{ feature }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- 智能体快捷访问 -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">智能体快捷访问</h2>
          <div class="agent-controls">
            <!-- 最小化任务栏 -->
            <div class="minimized-taskbar-header" v-show="minimizedAgents.length > 0">
              <MinimizedTaskbar
                :running-agents="minimizedAgents"
                @restore="restoreRunner"
                @close="closeSpecificAgent"
                class="home-minimized-taskbar"
              />
            </div>

            <div class="agent-search">
              <div class="search-box">
                <input
                  type="text"
                  v-model="searchQuery"
                  @keypress="handleSearchKeypress"
                  @input="handleSearchInput"
                  @focus="handleSearchFocus"
                  @blur="handleSearchBlur"
                  placeholder="搜索智能体..."
                  class="search-input"
                >
                <button class="search-btn" @click="searchAgents">
                  <span>🔍</span>
                </button>
                <button
                  class="clear-search-btn"
                  @click="clearAgentSearch"
                  :style="{ display: isSearching ? 'flex' : 'none' }"
                >
                  <span>✕</span>
                </button>
              </div>
            </div>
            <div class="agent-tabs">
              <button
                class="tab-btn"
                :class="{ active: currentAgentType === 'public' }"
                @click="switchAgentTab('public')"
              >
                公用智能体
              </button>
              <button
                class="tab-btn"
                :class="{ active: currentAgentType === 'unit' }"
                @click="switchAgentTab('unit')"
              >
                单位智能体
              </button>
              <button
                class="tab-btn"
                :class="{ active: currentAgentType === 'personal' }"
                @click="switchAgentTab('personal')"
              >
                个人智能体
              </button>
            </div>
          </div>
        </div>

        <div
          class="search-results-info"
          v-show="isSearching"
        >
          <span class="results-text">{{ searchResultsText }}</span>
          <button class="clear-results-btn" @click="clearAgentSearch">清除搜索</button>
        </div>

        <div class="agent-grid">
          <!-- 智能体卡片 -->
          <div
            v-for="agent in displayedAgents"
            :key="agent.id"
            class="agent-card"
            :class="{ 'search-highlight': isAgentHighlighted(agent) }"
            @click="openAgent(agent.id)"
          >
            <div class="agent-header">
              <div class="agent-avatar" :style="{ background: agent.avatarBg }">
                {{ agent.avatar }}
              </div>
              <div class="agent-info">
                <div class="agent-name" v-html="highlightSearchTerm(agent.name)"></div>
                <div
                  class="agent-type"
                  :style="{ background: agent.typeBg, color: agent.typeColor }"
                  v-html="highlightSearchTerm(agent.type)"
                ></div>
              </div>
            </div>
            <div class="agent-description" v-html="highlightSearchTerm(agent.description)"></div>
            <div class="agent-stats">
              <span>{{ agent.usage }}</span>
              <span>👍 {{ agent.likes }}</span>
            </div>
          </div>

          <!-- 搜索无结果 -->
          <div v-if="isSearching && searchResults.length === 0" class="no-results">
            <div class="no-results-icon">🔍</div>
            <div class="no-results-title">未找到相关智能体</div>
            <div class="no-results-description">
              尝试使用其他关键词搜索，或者<button class="link-btn" @click="clearAgentSearch">浏览所有智能体</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧热门智能体面板 -->
    <div class="sidebar">
      <div class="sidebar-panel">
        <div class="panel-header">
          <div class="panel-title">
            <span>🔥</span>
            <span>热门排行榜</span>
          </div>
          <div class="panel-tabs">
            <button
              class="panel-tab"
              :class="{ active: currentTopAgentsType === 'visits' }"
              @click="switchTopAgentsTab('visits')"
            >
              访问量
            </button>
            <button
              class="panel-tab"
              :class="{ active: currentTopAgentsType === 'likes' }"
              @click="switchTopAgentsTab('likes')"
            >
              点赞量
            </button>
            <button
              class="panel-tab"
              :class="{ active: currentTopAgentsType === 'battle' }"
              @click="switchTopAgentsTab('battle')"
              title="排榜规则：综合使用次数、点赞数、点赞率计算比武得分"
            >
              比武
            </button>
            <div class="panel-tab-more">
              <button class="panel-tab more-btn">更多 ▼</button>
              <div class="more-dropdown">
                <div
                  class="dropdown-item"
                  :class="{ active: currentTopAgentsType === 'units' }"
                  @click="switchTopAgentsTab('units')"
                  title="根据智能体数、知识库数、文档数综合评分排行"
                >
                  单位排行
                </div>
                <div
                  class="dropdown-item"
                  :class="{ active: currentTopAgentsType === 'recent' }"
                  @click="switchTopAgentsTab('recent')"
                >
                  近期上线智能体榜
                </div>
                <div
                  class="dropdown-item"
                  :class="{ active: currentTopAgentsType === 'knowledgeBase' }"
                  @click="switchTopAgentsTab('knowledgeBase')"
                >
                  知识库排行榜
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="top-agents-list">
          <!-- 热门智能体列表 -->
          <div
            v-for="(item, index) in currentTopAgentsData"
            :key="index"
            class="agent-item"
            @click="handleTopItemClick(item)"
          >
            <div class="agent-rank" :class="{ top3: index < 3 }">{{ index + 1 }}</div>
            <div class="agent-info">
              <div class="agent-name">{{ getTopItemName(item) }}</div>
              <div class="agent-meta">
                <span>{{ getTopItemMeta(item) }}</span>
              </div>
              <div v-if="getTopItemStats(item)" class="agent-stats" v-html="getTopItemStats(item)"></div>
            </div>
            <div class="agent-stat">
              {{ getTopItemStat(item) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体运行弹出层 -->
    <AgentRunnerWindow
      :visible="runnerModalVisible"
      :agent-info="currentRunningAgent"
      :is-minimized="isMinimized"
      :is-maximized="isMaximized"
      :position="windowPosition"
      :size="windowSize"
      @minimize="minimizeRunner"
      @close="closeRunnerModal"
      @update:position="updatePosition"
      @update:size="updateSize"
      @update:is-maximized="updateMaximized"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useAgentRunner } from '@/composables/useAgentRunner'
import MinimizedTaskbar from '@/views/agent/MinimizedTaskbar.vue'
import AgentRunnerWindow from '@/views/agent/AgentRunnerWindow.vue'

// 定义智能体类型
interface Agent {
  id: number
  name: string
  description: string
  avatar: string
  avatarBg: string
  type: string
  typeBg: string
  typeColor: string
  usage: string
  likes: string
  category: string
}

// 定义智能体集合类型
interface AgentCollection {
  public: Agent[]
  unit: Agent[]
  personal: Agent[]
}

// 定义热门排行数据类型
interface TopAgentData {
  visits: Array<{ name: string; unit: string; designer: string; visits: number }>
  likes: Array<{ name: string; unit: string; designer: string; likes: number }>
  units: Array<{ unit: string; agentCount: number; knowledgeBaseCount: number; docCount: number; unitScore: number }>
  battle: Array<{ name: string; unit: string; designer: string; usageCount: number; likes: number; likeRate: number; battleScore: number }>
  recent: Array<{ name: string; unit: string; designer: string; onlineDate: string }>
  knowledgeBase: Array<{ name: string; unit: string; creator: string; docCount: number; charCount: number; relatedApps: number; usageCount: number }>
}

// 响应式数据
const searchQuery = ref('')
const isSearching = ref(false)
const searchResults = ref<Agent[]>([])
const currentAgentType = ref<keyof AgentCollection>('public')
const currentTopAgentsType = ref<keyof TopAgentData>('visits')

// 使用统一的智能体运行管理器
const {
  runnerModalVisible,
  isMinimized,
  isMaximized,
  windowPosition,
  windowSize,
  currentRunningAgent,
  minimizedAgents,
  openRunnerModal,
  closeRunnerModal,
  minimizeRunner,
  restoreRunner,
  closeSpecificAgent,
  updatePosition,
  updateSize,
  updateMaximized
} = useAgentRunner({
  onAgentOpened: (agent) => {
    console.log('Home页面 - 智能体已打开:', agent.name)
  },
  onAgentClosed: (agentId) => {
    console.log('Home页面 - 智能体已关闭:', agentId)
  },
  onAgentMinimized: (agentId) => {
    console.log('Home页面 - 智能体已最小化:', agentId)
  },
  onAgentRestored: (agentId) => {
    console.log('Home页面 - 智能体已恢复:', agentId)
  }
})

// 悬浮展开状态
const showModelExpanded = ref(false)
const showUpdateExpanded = ref(false)
const showStatsExpanded = ref(false)

// 模板引用
const modelSection = ref<HTMLElement>()
const modelExpanded = ref<HTMLElement>()
const updateSection = ref<HTMLElement>()
const updateExpanded = ref<HTMLElement>()
const statsSection = ref<HTMLElement>()
const statsExpanded = ref<HTMLElement>()

// 静态信息数据 - 按照原始数据结构
const staticInfoData = ref({
  // 模型支持信息
  models: {
    llm: [
      { name: "GPT-4", params: "1.76T" },
      { name: "Claude-3", params: "175B" },
      { name: "文心一言4.0", params: "260B" },
      { name: "通义千问", params: "72B" },
      { name: "讯飞星火", params: "13B" },
      { name: "ChatGLM-4", params: "9B" },
      { name: "Baichuan2", params: "13B" },
      { name: "Qwen-Max", params: "72B" }
    ],
    speech: [
      { name: "Azure Speech", params: "多语言" },
      { name: "百度语音", params: "中英文" },
      { name: "科大讯飞", params: "多方言" },
      { name: "阿里云语音", params: "实时转写" },
      { name: "腾讯云语音", params: "高精度" },
      { name: "Google Speech", params: "全球化" }
    ],
    multimodal: [
      { name: "GPT-4V", params: "视觉理解" },
      { name: "Claude-3 Vision", params: "图像分析" },
      { name: "文心一言4.0", params: "多模态" },
      { name: "通义千问VL", params: "视觉问答" },
      { name: "Gemini Pro", params: "多模态" }
    ],
    vector: [
      { name: "OpenAI Embeddings", params: "1536维" },
      { name: "BGE-Large", params: "1024维" },
      { name: "M3E-Base", params: "768维" },
      { name: "Text2Vec", params: "512维" },
      { name: "Sentence-BERT", params: "768维" },
      { name: "Universal-Encoder", params: "512维" }
    ]
  },
  // 版本更新信息
  updates: [
    {
      version: "v2.3.1",
      date: "2024-12-20",
      features: ["🎨 新增多模态支持", "⚡ 优化响应速度", "🔧 修复已知问题"],
      type: "重要更新"
    },
    {
      version: "v2.3.0",
      date: "2024-12-15",
      features: ["🏪 智能体市场上线", "📥 新增批量导入", "� 支持自定义模板"],
      type: "功能更新"
    },
    {
      version: "v2.2.8",
      date: "2024-12-10",
      features: ["🚀 性能优化", "✨ 界面美化", "🔒 安全性增强"],
      type: "优化更新"
    },
    {
      version: "v2.2.7",
      date: "2024-12-05",
      features: ["🤖 AI助手升级", "📊 数据分析增强", "🌐 多语言支持"],
      type: "功能更新"
    }
  ],
  // 平台统计信息
  stats: {
    agents: 1247,
    knowledgeBases: 89,
    documents: 15678,
    totalUsers: 3456,
    todayVisits: 8923,
    activeUsers: 1234,
    totalInteractions: 45678,
    avgResponseTime: "0.8s",
    successRate: "99.2%",
    satisfaction: "4.8/5.0",
    uptime: "99.9%",
    apiCalls: 234567
  }
})

// 计算属性 - 模型相关
const modelCategories = computed(() => [
  { key: 'llm', icon: '🧠', title: '大语言模型', models: staticInfoData.value.models.llm },
  { key: 'speech', icon: '🎤', title: '语音模型', models: staticInfoData.value.models.speech },
  { key: 'multimodal', icon: '🎨', title: '多模态模型', models: staticInfoData.value.models.multimodal },
  { key: 'vector', icon: '🔍', title: '向量模型', models: staticInfoData.value.models.vector }
])

const modelCategoriesPreview = computed(() => [
  { key: 'llm', icon: '🧠', title: '大语言模型', models: staticInfoData.value.models.llm },
  { key: 'speech', icon: '🎤', title: '语音模型', models: staticInfoData.value.models.speech }
])

const totalModelsCount = computed(() => {
  const models = staticInfoData.value.models
  return models.llm.length + models.speech.length + models.multimodal.length + models.vector.length
})

// 计算属性 - 更新相关
const updates = computed(() => staticInfoData.value.updates)

const latestVersion = computed(() => staticInfoData.value.updates[0]?.version || 'v1.0.0')

const getUpdateTypeClass = (type: string) => {
  switch (type) {
    case '重要更新':
      return 'bg-red-100 text-red-700 border border-red-200'
    case '功能更新':
      return 'bg-blue-100 text-blue-700 border border-blue-200'
    case '优化更新':
      return 'bg-green-100 text-green-700 border border-green-200'
    default:
      return 'bg-gray-100 text-gray-700 border border-gray-200'
  }
}

// 计算属性 - 统计相关
const keyStats = computed(() => [
  { key: 'agents', icon: '🤖', label: '智能体', value: staticInfoData.value.stats.agents.toLocaleString() },
  { key: 'todayVisits', icon: '👁️', label: '今日访问', value: staticInfoData.value.stats.todayVisits.toLocaleString() },
  { key: 'activeUsers', icon: '🔥', label: '活跃用户', value: staticInfoData.value.stats.activeUsers.toLocaleString() },
  { key: 'satisfaction', icon: '⭐', label: '满意度', value: staticInfoData.value.stats.satisfaction }
])

const allStats = computed(() => [
  { key: 'agents', icon: '🤖', label: '智能体总数', value: staticInfoData.value.stats.agents.toLocaleString(), trend: '+12% 本月' },
  { key: 'knowledgeBases', icon: '📚', label: '知识库总数', value: staticInfoData.value.stats.knowledgeBases.toLocaleString(), trend: '+5% 本月' },
  { key: 'documents', icon: '📄', label: '文档总数', value: staticInfoData.value.stats.documents.toLocaleString(), trend: '+18% 本月' },
  { key: 'totalUsers', icon: '👥', label: '总用户数', value: staticInfoData.value.stats.totalUsers.toLocaleString(), trend: '+8% 本月' },
  { key: 'todayVisits', icon: '👁️', label: '今日访问量', value: staticInfoData.value.stats.todayVisits.toLocaleString(), trend: '+15% 昨日' },
  { key: 'activeUsers', icon: '🔥', label: '活跃用户', value: staticInfoData.value.stats.activeUsers.toLocaleString(), trend: '+6% 本周' },
  { key: 'totalInteractions', icon: '💬', label: '总交互次数', value: staticInfoData.value.stats.totalInteractions.toLocaleString(), trend: '+22% 本月' },
  { key: 'apiCalls', icon: '🔗', label: 'API调用量', value: staticInfoData.value.stats.apiCalls.toLocaleString(), trend: '+25% 本月' },
  { key: 'avgResponseTime', icon: '⚡', label: '平均响应时间', value: staticInfoData.value.stats.avgResponseTime, trend: '-0.2s 优化' },
  { key: 'successRate', icon: '✅', label: '成功率', value: staticInfoData.value.stats.successRate, trend: '+0.1% 提升' },
  { key: 'satisfaction', icon: '⭐', label: '用户满意度', value: staticInfoData.value.stats.satisfaction, trend: '+0.2 提升' },
  { key: 'uptime', icon: '🛡️', label: '系统可用性', value: staticInfoData.value.stats.uptime, trend: '稳定运行' }
])

// 智能体数据 - 完全按照原始数据结构
const allAgents = ref<AgentCollection>({
  public: [
    {
      id: 1,
      name: "通用文档助手",
      description: "帮助您处理各种文档编写、格式化和内容优化任务",
      avatar: "📝",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "文档处理",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "1.2k次使用",
      likes: "1.2k",
      category: 'public'
    },
    {
      id: 2,
      name: "代码审查专家",
      description: "专业的代码质量检查、性能优化建议和最佳实践指导",
      avatar: "👨‍💻",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "代码分析",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "856次使用",
      likes: "856",
      category: 'public'
    },
    {
      id: 3,
      name: "数据分析师",
      description: "提供数据清洗、统计分析、可视化图表生成等数据处理服务",
      avatar: "📊",
      avatarBg: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      type: "数据分析",
      typeBg: "#e8f5e8",
      typeColor: "#388e3c",
      usage: "2.1k次使用",
      likes: "2.1k",
      category: 'public'
    },
    {
      id: 4,
      name: "翻译专家",
      description: "支持多语言翻译，包括技术文档、商务邮件等专业内容翻译",
      avatar: "🌐",
      avatarBg: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      type: "语言翻译",
      typeBg: "#fff3e0",
      typeColor: "#f57c00",
      usage: "3.4k次使用",
      likes: "3.4k",
      category: 'public'
    },
    {
      id: 5,
      name: "创意写作助手",
      description: "协助创作小说、诗歌、广告文案等各类创意内容",
      avatar: "✍️",
      avatarBg: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      type: "创意写作",
      typeBg: "#fce4ec",
      typeColor: "#c2185b",
      usage: "987次使用",
      likes: "987",
      category: 'public'
    },
    {
      id: 6,
      name: "学习导师",
      description: "个性化学习计划制定、知识点解答和学习进度跟踪",
      avatar: "🎓",
      avatarBg: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      type: "教育培训",
      typeBg: "#e1f5fe",
      typeColor: "#0277bd",
      usage: "1.8k次使用",
      likes: "1.8k",
      category: 'public'
    },
    {
      id: 7,
      name: "智能客服机器人",
      description: "24小时在线客服，处理常见问题咨询和用户服务请求",
      avatar: "🤖",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "客户服务",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "5.2k次使用",
      likes: "1.6k",
      category: 'public'
    },
    {
      id: 8,
      name: "邮件智能回复",
      description: "自动分析邮件内容，生成专业的回复建议和模板",
      avatar: "📧",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "邮件处理",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "2.8k次使用",
      likes: "1.7k",
      category: 'public'
    },
    {
      id: 9,
      name: "会议纪要生成器",
      description: "根据会议录音或文字记录，自动生成结构化的会议纪要",
      avatar: "📋",
      avatarBg: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      type: "会议管理",
      typeBg: "#e8f5e8",
      typeColor: "#388e3c",
      usage: "1.5k次使用",
      likes: "1.8k",
      category: 'public'
    },
    {
      id: 10,
      name: "SQL查询助手",
      description: "帮助编写、优化和调试SQL查询语句，支持多种数据库",
      avatar: "🗄️",
      avatarBg: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      type: "数据库",
      typeBg: "#fff3e0",
      typeColor: "#f57c00",
      usage: "1.9k次使用",
      likes: "1.9k",
      category: 'public'
    },
    {
      id: 11,
      name: "PPT制作助手",
      description: "根据主题和内容要求，自动生成演示文稿大纲和页面设计",
      avatar: "📊",
      avatarBg: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      type: "演示制作",
      typeBg: "#fce4ec",
      typeColor: "#c2185b",
      usage: "3.1k次使用",
      likes: "1.5k",
      category: 'public'
    },
    {
      id: 12,
      name: "简历优化师",
      description: "分析简历内容，提供专业的优化建议和格式调整",
      avatar: "📄",
      avatarBg: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      type: "求职辅助",
      typeBg: "#e1f5fe",
      typeColor: "#0277bd",
      usage: "2.3k次使用",
      likes: "1.7k",
      category: 'public'
    },
    {
      id: 13,
      name: "Excel公式专家",
      description: "帮助编写复杂的Excel公式，数据处理和图表制作",
      avatar: "📈",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "数据处理",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "4.2k次使用",
      likes: "1.8k",
      category: 'public'
    },
    {
      id: 14,
      name: "法律条文解读",
      description: "解释法律条文，提供法律咨询和合同条款分析",
      avatar: "⚖️",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "法律咨询",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "1.1k次使用",
      likes: "1.9k",
      category: 'public'
    },
    {
      id: 15,
      name: "健康咨询顾问",
      description: "提供健康建议、症状分析和医疗知识普及",
      avatar: "🏥",
      avatarBg: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      type: "健康医疗",
      typeBg: "#e8f5e8",
      typeColor: "#388e3c",
      usage: "2.7k次使用",
      likes: "1.6k",
      category: 'public'
    },
    {
      id: 16,
      name: "投资理财顾问",
      description: "分析市场趋势，提供投资建议和理财规划",
      avatar: "💰",
      avatarBg: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      type: "金融理财",
      typeBg: "#fff3e0",
      typeColor: "#f57c00",
      usage: "1.8k次使用",
      likes: "1.4k",
      category: 'public'
    },
    {
      id: 17,
      name: "旅游规划师",
      description: "制定旅游路线，推荐景点和住宿，预算规划",
      avatar: "✈️",
      avatarBg: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      type: "旅游规划",
      typeBg: "#fce4ec",
      typeColor: "#c2185b",
      usage: "3.5k次使用",
      likes: "1.7k",
      category: 'public'
    },
    {
      id: 18,
      name: "菜谱推荐师",
      description: "根据食材和口味偏好，推荐菜谱和营养搭配",
      avatar: "🍳",
      avatarBg: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      type: "美食烹饪",
      typeBg: "#e1f5fe",
      typeColor: "#0277bd",
      usage: "2.9k次使用",
      likes: "1.5k",
      category: 'public'
    },
    {
      id: 19,
      name: "心理咨询师",
      description: "提供心理健康建议，情绪管理和压力缓解指导",
      avatar: "🧠",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "心理健康",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "1.6k次使用",
      likes: "1.8k",
      category: 'public'
    },
    {
      id: 20,
      name: "编程学习导师",
      description: "编程语言学习指导，代码练习和项目实战建议",
      avatar: "💻",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "编程教育",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "4.1k次使用",
      likes: "1.9k",
      category: 'public'
    }
  ],
  unit: [
    {
      id: 21,
      name: "技术部代码助手",
      description: "专门为技术部定制的代码生成、调试和架构设计助手",
      avatar: "⚙️",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "技术开发",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "456次使用",
      likes: "1.9k",
      category: 'unit'
    },
    {
      id: 22,
      name: "市场部文案生成器",
      description: "营销文案、产品介绍、广告创意等市场推广内容生成",
      avatar: "📢",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "市场营销",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "723次使用",
      likes: "1.7k",
      category: 'unit'
    },
    {
      id: 23,
      name: "人事部招聘助手",
      description: "简历筛选、面试问题设计、岗位描述优化等人力资源管理",
      avatar: "👥",
      avatarBg: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      type: "人力资源",
      typeBg: "#e8f5e8",
      typeColor: "#388e3c",
      usage: "234次使用",
      likes: "1.6k",
      category: 'unit'
    },
    {
      id: 24,
      name: "财务部报表分析",
      description: "财务数据分析、报表生成、成本控制建议等财务管理支持",
      avatar: "💰",
      avatarBg: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      type: "财务管理",
      typeBg: "#fff3e0",
      typeColor: "#f57c00",
      usage: "345次使用",
      likes: "1.8k",
      category: 'unit'
    },
    {
      id: 25,
      name: "客服部智能问答",
      description: "客户问题自动回复、服务流程优化、满意度分析",
      avatar: "🎧",
      avatarBg: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      type: "客户服务",
      typeBg: "#fce4ec",
      typeColor: "#c2185b",
      usage: "1.1k次使用",
      likes: "1.5k",
      category: 'unit'
    },
    {
      id: 26,
      name: "法务部合同审查",
      description: "合同条款分析、法律风险评估、合规性检查",
      avatar: "⚖️",
      avatarBg: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      type: "法律事务",
      typeBg: "#e1f5fe",
      typeColor: "#0277bd",
      usage: "167次使用",
      likes: "1.9k",
      category: 'unit'
    },
    {
      id: 27,
      name: "运营部数据分析",
      description: "用户行为分析、运营指标监控、增长策略建议",
      avatar: "📊",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "运营分析",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "892次使用",
      likes: "1.8k",
      category: 'unit'
    },
    {
      id: 28,
      name: "产品部需求分析",
      description: "用户需求收集、产品功能设计、竞品分析报告",
      avatar: "🎯",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "产品设计",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "567次使用",
      likes: "1.7k",
      category: 'unit'
    },
    {
      id: 29,
      name: "销售部客户管理",
      description: "客户信息整理、销售机会跟进、业绩分析报告",
      avatar: "💼",
      avatarBg: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      type: "销售管理",
      typeBg: "#e8f5e8",
      typeColor: "#388e3c",
      usage: "1.3k次使用",
      likes: "1.6k",
      category: 'unit'
    },
    {
      id: 30,
      name: "设计部创意助手",
      description: "设计灵感生成、色彩搭配建议、品牌视觉规范",
      avatar: "🎨",
      avatarBg: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      type: "视觉设计",
      typeBg: "#fff3e0",
      typeColor: "#f57c00",
      usage: "445次使用",
      likes: "1.9k",
      category: 'unit'
    },
    {
      id: 31,
      name: "质量部测试助手",
      description: "测试用例生成、缺陷分析、质量报告自动化",
      avatar: "🔍",
      avatarBg: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      type: "质量测试",
      typeBg: "#fce4ec",
      typeColor: "#c2185b",
      usage: "321次使用",
      likes: "1.8k",
      category: 'unit'
    },
    {
      id: 32,
      name: "采购部供应商管理",
      description: "供应商评估、采购成本分析、合同管理",
      avatar: "🏪",
      avatarBg: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      type: "采购管理",
      typeBg: "#e1f5fe",
      typeColor: "#0277bd",
      usage: "198次使用",
      likes: "1.5k",
      category: 'unit'
    }
  ],
  personal: [
    {
      id: 33,
      name: "我的日程管理",
      description: "个人日程安排、任务提醒、时间管理优化建议",
      avatar: "📅",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "时间管理",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "89次使用",
      likes: "2.0k",
      category: 'personal'
    },
    {
      id: 34,
      name: "个人学习助手",
      description: "根据个人兴趣和能力定制的学习计划和知识总结",
      avatar: "📖",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "个人成长",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "156次使用",
      likes: "1.8k",
      category: 'personal'
    },
    {
      id: 35,
      name: "健康生活顾问",
      description: "健康建议、运动计划、饮食搭配等生活方式优化",
      avatar: "🏃‍♂️",
      avatarBg: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      type: "健康管理",
      typeBg: "#e8f5e8",
      typeColor: "#388e3c",
      usage: "67次使用",
      likes: "1.7k",
      category: 'personal'
    },
    {
      id: 36,
      name: "投资理财顾问",
      description: "个人财务规划、投资建议、风险评估等理财指导",
      avatar: "💎",
      avatarBg: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      type: "理财规划",
      typeBg: "#fff3e0",
      typeColor: "#f57c00",
      usage: "43次使用",
      likes: "1.6k",
      category: 'personal'
    },
    {
      id: 37,
      name: "旅行规划师",
      description: "旅行路线规划、景点推荐、预算控制等出行安排",
      avatar: "✈️",
      avatarBg: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      type: "旅行规划",
      typeBg: "#fce4ec",
      typeColor: "#c2185b",
      usage: "78次使用",
      likes: "1.9k",
      category: 'personal'
    },
    {
      id: 38,
      name: "创作灵感库",
      description: "个人创作想法记录、灵感整理、作品管理",
      avatar: "🎨",
      avatarBg: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      type: "创意管理",
      typeBg: "#e1f5fe",
      typeColor: "#0277bd",
      usage: "92次使用",
      likes: "1.8k",
      category: 'personal'
    },
    {
      id: 39,
      name: "个人记账助手",
      description: "日常开支记录、预算管理、消费分析报告",
      avatar: "💳",
      avatarBg: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      type: "财务管理",
      typeBg: "#e3f2fd",
      typeColor: "#1976d2",
      usage: "134次使用",
      likes: "1.7k",
      category: 'personal'
    },
    {
      id: 40,
      name: "读书笔记整理",
      description: "读书心得记录、知识点提取、读书计划制定",
      avatar: "📚",
      avatarBg: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      type: "知识管理",
      typeBg: "#f3e5f5",
      typeColor: "#7b1fa2",
      usage: "87次使用",
      likes: "1.9k",
      category: 'personal'
    },
    {
      id: 41,
      name: "运动健身教练",
      description: "个人健身计划、运动指导、健康数据跟踪",
      avatar: "💪",
      avatarBg: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      type: "健身指导",
      typeBg: "#e8f5e8",
      typeColor: "#388e3c",
      usage: "112次使用",
      likes: "1.8k",
      category: 'personal'
    },
    {
      id: 42,
      name: "家庭理财规划",
      description: "家庭预算管理、保险规划、子女教育基金",
      avatar: "🏠",
      avatarBg: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      type: "家庭理财",
      typeBg: "#fff3e0",
      typeColor: "#f57c00",
      usage: "65次使用",
      likes: "1.6k",
      category: 'personal'
    },
    {
      id: 43,
      name: "职业发展顾问",
      description: "职业规划建议、技能提升方向、面试准备",
      avatar: "🎯",
      avatarBg: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      type: "职业规划",
      typeBg: "#fce4ec",
      typeColor: "#c2185b",
      usage: "98次使用",
      likes: "1.8k",
      category: 'personal'
    },
    {
      id: 44,
      name: "情感咨询师",
      description: "情感问题分析、人际关系建议、心理疏导",
      avatar: "💝",
      avatarBg: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      type: "情感咨询",
      typeBg: "#e1f5fe",
      typeColor: "#0277bd",
      usage: "76次使用",
      likes: "1.7k",
      category: 'personal'
    }
  ]
})

// 计算属性
const displayedAgents = computed(() => {
  if (isSearching.value) {
    return searchResults.value
  }
  return allAgents.value[currentAgentType.value] || []
})

const searchResultsText = computed(() => {
  if (searchResults.value.length === 0) {
    return `未找到包含"${searchQuery.value}"的智能体`
  }
  return `找到 ${searchResults.value.length} 个包含"${searchQuery.value}"的智能体`
})

// 热门排行数据 - 完全按照原始数据结构
const topAgentsData = ref<TopAgentData>({
  visits: [
    { name: "通用文档助手", unit: "公共平台", designer: "系统管理员", visits: 12500 },
    { name: "代码审查专家", unit: "公共平台", designer: "技术团队", visits: 9800 },
    { name: "数据分析师", unit: "公共平台", designer: "数据团队", visits: 8600 },
    { name: "技术部代码助手", unit: "技术部", designer: "张三", visits: 7200 },
    { name: "翻译专家", unit: "公共平台", designer: "语言团队", visits: 6800 },
    { name: "市场部文案生成器", unit: "市场部", designer: "李四", visits: 5900 },
    { name: "智能客服机器人", unit: "公共平台", designer: "客服团队", visits: 5400 },
    { name: "创意写作助手", unit: "公共平台", designer: "内容团队", visits: 4700 },
    { name: "销售部客户管理", unit: "销售部", designer: "王五", visits: 4200 },
    { name: "学习导师", unit: "公共平台", designer: "教育团队", visits: 3800 },
    { name: "运营部数据分析", unit: "运营部", designer: "陈七", visits: 3600 },
    { name: "产品部需求分析", unit: "产品部", designer: "刘八", visits: 3400 },
    { name: "设计部创意助手", unit: "设计部", designer: "赵六", visits: 3200 },
    { name: "人事部招聘助手", unit: "人事部", designer: "孙九", visits: 3000 },
    { name: "财务部报表分析", unit: "财务部", designer: "周十", visits: 2800 },
    { name: "质量部测试助手", unit: "质量部", designer: "吴十一", visits: 2600 },
    { name: "采购部供应商管理", unit: "采购部", designer: "郑十二", visits: 2400 },
    { name: "法务部合同审查", unit: "法务部", designer: "王十三", visits: 2200 },
    { name: "客服部智能问答", unit: "客服部", designer: "李十四", visits: 2000 },
    { name: "行政部文档管理", unit: "行政部", designer: "张十五", visits: 1800 }
  ],
  likes: [
    { name: "创意写作助手", unit: "公共平台", designer: "内容团队", likes: 2340 },
    { name: "学习导师", unit: "公共平台", designer: "教育团队", likes: 2180 },
    { name: "通用文档助手", unit: "公共平台", designer: "系统管理员", likes: 1950 },
    { name: "旅行规划师", unit: "公共平台", designer: "生活团队", likes: 1820 },
    { name: "健康生活顾问", unit: "公共平台", designer: "健康团队", likes: 1650 },
    { name: "代码审查专家", unit: "公共平台", designer: "技术团队", likes: 1480 },
    { name: "设计部创意助手", unit: "设计部", designer: "赵六", likes: 1320 },
    { name: "心理咨询师", unit: "公共平台", designer: "心理团队", likes: 1180 },
    { name: "菜谱推荐师", unit: "公共平台", designer: "美食团队", likes: 1050 },
    { name: "投资理财顾问", unit: "公共平台", designer: "金融团队", likes: 920 },
    { name: "技术部代码助手", unit: "技术部", designer: "张三", likes: 890 },
    { name: "市场部文案生成器", unit: "市场部", designer: "李四", likes: 850 },
    { name: "运营部数据分析", unit: "运营部", designer: "陈七", likes: 820 },
    { name: "产品部需求分析", unit: "产品部", designer: "刘八", likes: 780 },
    { name: "销售部客户管理", unit: "销售部", designer: "王五", likes: 750 },
    { name: "人事部招聘助手", unit: "人事部", designer: "孙九", likes: 720 },
    { name: "财务部报表分析", unit: "财务部", designer: "周十", likes: 680 },
    { name: "质量部测试助手", unit: "质量部", designer: "吴十一", likes: 650 },
    { name: "客服部智能问答", unit: "客服部", designer: "李十四", likes: 620 },
    { name: "法务部合同审查", unit: "法务部", designer: "王十三", likes: 590 }
  ],
  units: [
    { unit: "技术部", agentCount: 15, knowledgeBaseCount: 8, docCount: 1245, unitScore: 92.5 },
    { unit: "市场部", agentCount: 12, knowledgeBaseCount: 6, docCount: 987, unitScore: 88.3 },
    { unit: "产品部", agentCount: 10, knowledgeBaseCount: 5, docCount: 834, unitScore: 84.7 },
    { unit: "销售部", agentCount: 8, knowledgeBaseCount: 4, docCount: 672, unitScore: 79.2 },
    { unit: "运营部", agentCount: 9, knowledgeBaseCount: 5, docCount: 756, unitScore: 82.1 },
    { unit: "设计部", agentCount: 7, knowledgeBaseCount: 4, docCount: 589, unitScore: 76.8 },
    { unit: "人事部", agentCount: 6, knowledgeBaseCount: 3, docCount: 445, unitScore: 71.5 },
    { unit: "财务部", agentCount: 5, knowledgeBaseCount: 3, docCount: 378, unitScore: 68.2 },
    { unit: "客服部", agentCount: 8, knowledgeBaseCount: 4, docCount: 523, unitScore: 75.3 },
    { unit: "质量部", agentCount: 4, knowledgeBaseCount: 2, docCount: 289, unitScore: 62.8 },
    { unit: "法务部", agentCount: 3, knowledgeBaseCount: 2, docCount: 234, unitScore: 58.9 },
    { unit: "采购部", agentCount: 4, knowledgeBaseCount: 2, docCount: 198, unitScore: 56.4 },
    { unit: "行政部", agentCount: 5, knowledgeBaseCount: 3, docCount: 267, unitScore: 64.1 },
    { unit: "研发部", agentCount: 6, knowledgeBaseCount: 3, docCount: 345, unitScore: 69.7 },
    { unit: "安全部", agentCount: 3, knowledgeBaseCount: 2, docCount: 156, unitScore: 52.3 },
    { unit: "培训部", agentCount: 4, knowledgeBaseCount: 2, docCount: 189, unitScore: 54.8 },
    { unit: "审计部", agentCount: 2, knowledgeBaseCount: 1, docCount: 123, unitScore: 45.2 },
    { unit: "投资部", agentCount: 3, knowledgeBaseCount: 2, docCount: 145, unitScore: 49.7 },
    { unit: "公关部", agentCount: 3, knowledgeBaseCount: 2, docCount: 134, unitScore: 48.1 },
    { unit: "战略部", agentCount: 2, knowledgeBaseCount: 1, docCount: 98, unitScore: 42.6 }
  ],
  battle: [
    { name: "代码审查专家", unit: "公共平台", designer: "技术团队", usageCount: 9800, likes: 1480, likeRate: 15.1, battleScore: 98.5 },
    { name: "数据分析师", unit: "公共平台", designer: "数据团队", usageCount: 8600, likes: 1650, likeRate: 19.2, battleScore: 97.2 },
    { name: "技术部代码助手", unit: "技术部", designer: "张三", usageCount: 7200, likes: 890, likeRate: 12.4, battleScore: 96.8 },
    { name: "SQL查询助手", unit: "公共平台", designer: "数据团队", usageCount: 6500, likes: 1250, likeRate: 19.2, battleScore: 96.1 },
    { name: "法律条文解读", unit: "公共平台", designer: "法律团队", usageCount: 4200, likes: 1180, likeRate: 28.1, battleScore: 95.7 },
    { name: "设计部创意助手", unit: "设计部", designer: "赵六", usageCount: 3200, likes: 1320, likeRate: 41.3, battleScore: 95.3 },
    { name: "编程学习导师", unit: "公共平台", designer: "教育团队", usageCount: 5800, likes: 1100, likeRate: 19.0, battleScore: 94.9 },
    { name: "Excel公式专家", unit: "公共平台", designer: "办公团队", usageCount: 4200, likes: 1800, likeRate: 42.9, battleScore: 94.6 },
    { name: "财务部报表分析", unit: "财务部", designer: "周十", usageCount: 2800, likes: 680, likeRate: 24.3, battleScore: 94.2 },
    { name: "运营部数据分析", unit: "运营部", designer: "陈七", usageCount: 3600, likes: 820, likeRate: 22.8, battleScore: 93.8 }
  ],
  recent: [
    { name: "AI绘画助手", unit: "设计部", designer: "李艺术", onlineDate: "2024-12-20" },
    { name: "智能合同生成", unit: "法务部", designer: "王法官", onlineDate: "2024-12-19" },
    { name: "多语言翻译专家", unit: "公共平台", designer: "国际团队", onlineDate: "2024-12-18" },
    { name: "视频剪辑助手", unit: "媒体部", designer: "张导演", onlineDate: "2024-12-17" },
    { name: "智能客服升级版", unit: "客服部", designer: "李十四", onlineDate: "2024-12-16" },
    { name: "数据可视化专家", unit: "技术部", designer: "陈数据", onlineDate: "2024-12-15" },
    { name: "项目管理助手", unit: "项目部", designer: "刘项目", onlineDate: "2024-12-14" },
    { name: "智能招聘筛选", unit: "人事部", designer: "孙九", onlineDate: "2024-12-13" },
    { name: "财务风险评估", unit: "财务部", designer: "周十", onlineDate: "2024-12-12" },
    { name: "营销策略分析", unit: "市场部", designer: "李四", onlineDate: "2024-12-11" }
  ],
  knowledgeBase: [
    { name: "技术文档库", unit: "技术部", creator: "张技术", docCount: 1245, charCount: 2340, relatedApps: 15, usageCount: 8900 },
    { name: "产品规范库", unit: "产品部", creator: "刘产品", docCount: 987, charCount: 1890, relatedApps: 12, usageCount: 7600 },
    { name: "法律条文库", unit: "法务部", creator: "王法务", docCount: 834, charCount: 3200, relatedApps: 8, usageCount: 6800 },
    { name: "市场分析库", unit: "市场部", creator: "李市场", docCount: 756, charCount: 1560, relatedApps: 10, usageCount: 6200 },
    { name: "财务制度库", unit: "财务部", creator: "周财务", docCount: 672, charCount: 1340, relatedApps: 7, usageCount: 5400 },
    { name: "人事政策库", unit: "人事部", creator: "孙人事", docCount: 589, charCount: 1120, relatedApps: 9, usageCount: 4900 },
    { name: "设计规范库", unit: "设计部", creator: "赵设计", docCount: 523, charCount: 980, relatedApps: 6, usageCount: 4300 },
    { name: "客服话术库", unit: "客服部", creator: "钱客服", docCount: 445, charCount: 890, relatedApps: 8, usageCount: 3800 },
    { name: "质量标准库", unit: "质量部", creator: "吴质量", docCount: 378, charCount: 760, relatedApps: 5, usageCount: 3200 },
    { name: "培训资料库", unit: "人事部", creator: "郑培训", docCount: 345, charCount: 680, relatedApps: 7, usageCount: 2900 }
  ]
})

const currentTopAgentsData = computed(() => {
  return topAgentsData.value[currentTopAgentsType.value] || []
})



// 方法
const handleSearchKeypress = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    searchAgents()
  }
}

const handleSearchInput = () => {
  if (searchQuery.value.trim()) {
    searchAgents()
  } else {
    clearAgentSearch()
  }
}

const handleSearchFocus = () => {
  // 搜索框获得焦点时的处理
}

const handleSearchBlur = () => {
  // 搜索框失去焦点时的处理
}

const searchAgents = () => {
  if (!searchQuery.value.trim()) return

  isSearching.value = true
  const query = searchQuery.value.toLowerCase()

  // 在所有类型的智能体中搜索
  searchResults.value = []
  Object.keys(allAgents.value).forEach(type => {
    const typeKey = type as keyof AgentCollection
    const typeAgents = allAgents.value[typeKey].filter((agent: Agent) =>
      agent.name.toLowerCase().includes(query) ||
      agent.description.toLowerCase().includes(query) ||
      agent.type.toLowerCase().includes(query)
    )
    searchResults.value.push(...typeAgents)
  })

  // 去重（如果有重复的智能体）
  searchResults.value = searchResults.value.filter((agent, index, self) =>
    index === self.findIndex(a => a.id === agent.id)
  )
}

const clearAgentSearch = () => {
  searchQuery.value = ''
  isSearching.value = false
  searchResults.value = []
}

const switchAgentTab = (type: keyof AgentCollection) => {
  currentAgentType.value = type
  if (isSearching.value) {
    clearAgentSearch()
  }
}

const switchTopAgentsTab = (type: keyof TopAgentData) => {
  currentTopAgentsType.value = type
}

const openAgent = (agentId: number) => {
  // 找到对应的智能体
  let agent: any = null
  Object.keys(allAgents.value).forEach(type => {
    const found = (allAgents.value as any)[type].find((a: any) => a.id === agentId)
    if (found) {
      agent = found
    }
  })

  if (!agent) return

  // 转换为统一的智能体格式
  const unifiedAgent = {
    id: agent.id.toString(),
    name: agent.name,
    description: agent.description,
    icon: 'fas fa-robot',
    unit: '公共平台',
    creator: '系统',
    createTime: new Date().toISOString(),
    type: agent.type,
    tags: []
  }

  // 使用统一的运行管理器打开智能体
  openRunnerModal(unifiedAgent)
}

const handleTopItemClick = (item: any) => {
  console.log('点击排行榜项目:', item)
  // 这里可以处理排行榜项目点击事件
}

const isAgentHighlighted = (agent: any) => {
  return isSearching.value && searchResults.value.includes(agent)
}

const highlightSearchTerm = (text: string) => {
  if (!isSearching.value || !searchQuery.value.trim()) {
    return text
  }

  const query = searchQuery.value.trim()
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark style="background: #fff3cd; padding: 1px 2px; border-radius: 2px;">$1</mark>')
}

const getTopItemName = (item: any) => {
  if (currentTopAgentsType.value === 'units') {
    return item.unit || ''
  }
  return item.name || ''
}

const getTopItemMeta = (item: any) => {
  if (currentTopAgentsType.value === 'visits' || currentTopAgentsType.value === 'likes') {
    return `${item.unit} | ${item.designer}`
  }
  if (currentTopAgentsType.value === 'battle') {
    return `${item.unit} | ${item.designer}`
  }
  if (currentTopAgentsType.value === 'units') {
    return '' // 单位排行中不显示单位名称，因为名称已经是单位了
  }
  if (currentTopAgentsType.value === 'recent') {
    return `${item.unit} | ${item.designer}`
  }
  if (currentTopAgentsType.value === 'knowledgeBase') {
    return `${item.unit} | ${item.creator}`
  }
  return item.meta || ''
}

const getTopItemStat = (item: any) => {
  if (currentTopAgentsType.value === 'visits') {
    return item.visits?.toLocaleString() || '0'
  }
  if (currentTopAgentsType.value === 'likes') {
    return item.likes?.toLocaleString() || '0'
  }
  if (currentTopAgentsType.value === 'battle') {
    return `${item.battleScore}分`
  }
  if (currentTopAgentsType.value === 'units') {
    return `${item.unitScore}分`
  }
  if (currentTopAgentsType.value === 'recent') {
    return item.onlineDate || ''
  }
  if (currentTopAgentsType.value === 'knowledgeBase') {
    return `${item.usageCount}次`
  }
  return ''
}

const getTopItemStats = (item: any) => {
  if (currentTopAgentsType.value === 'battle' && item.usageCount) {
    return `${item.usageCount}次使用 | ${item.likes}点赞 | ${item.likeRate}%点赞率`
  }
  if (currentTopAgentsType.value === 'units' && item.agentCount) {
    return `${item.agentCount}个智能体 | ${item.knowledgeBaseCount}个知识库 | ${item.docCount}个文档`
  }
  if (currentTopAgentsType.value === 'knowledgeBase' && item.docCount) {
    return `${item.docCount}个文档 | ${item.charCount}K字符 | ${item.relatedApps}个应用`
  }
  return ''
}







// 延时处理器
let modelHoverTimeout: number | null = null
let updateHoverTimeout: number | null = null
let statsHoverTimeout: number | null = null

// 显示延时配置（毫秒）
const SHOW_DELAY = 500  // 鼠标悬停500ms后显示展开层
const HIDE_DELAY = 200  // 鼠标离开200ms后隐藏展开层

// 显示模型展开层
const showModelExpandedLayer = () => {
  // 清除之前的隐藏定时器
  if (modelHoverTimeout) {
    clearTimeout(modelHoverTimeout)
    modelHoverTimeout = null
  }

  // 如果已经显示，直接返回
  if (showModelExpanded.value) {
    return
  }

  // 设置显示延时
  modelHoverTimeout = window.setTimeout(() => {
    showModelExpanded.value = true
    modelHoverTimeout = null
  }, SHOW_DELAY)
}

// 隐藏模型展开层
const hideModelExpandedLayer = () => {
  // 清除显示定时器
  if (modelHoverTimeout) {
    clearTimeout(modelHoverTimeout)
    modelHoverTimeout = null
  }

  // 设置隐藏延时
  modelHoverTimeout = window.setTimeout(() => {
    showModelExpanded.value = false
    modelHoverTimeout = null
  }, HIDE_DELAY)
}

// 显示更新展开层
const showUpdateExpandedLayer = () => {
  // 清除之前的隐藏定时器
  if (updateHoverTimeout) {
    clearTimeout(updateHoverTimeout)
    updateHoverTimeout = null
  }

  // 如果已经显示，直接返回
  if (showUpdateExpanded.value) {
    return
  }

  // 设置显示延时
  updateHoverTimeout = window.setTimeout(() => {
    showUpdateExpanded.value = true
    updateHoverTimeout = null
  }, SHOW_DELAY)
}

// 隐藏更新展开层
const hideUpdateExpandedLayer = () => {
  // 清除显示定时器
  if (updateHoverTimeout) {
    clearTimeout(updateHoverTimeout)
    updateHoverTimeout = null
  }

  // 设置隐藏延时
  updateHoverTimeout = window.setTimeout(() => {
    showUpdateExpanded.value = false
    updateHoverTimeout = null
  }, HIDE_DELAY)
}

// 显示统计展开层
const showStatsExpandedLayer = () => {
  // 清除之前的隐藏定时器
  if (statsHoverTimeout) {
    clearTimeout(statsHoverTimeout)
    statsHoverTimeout = null
  }

  // 如果已经显示，直接返回
  if (showStatsExpanded.value) {
    return
  }

  // 设置显示延时
  statsHoverTimeout = window.setTimeout(() => {
    showStatsExpanded.value = true
    statsHoverTimeout = null
  }, SHOW_DELAY)
}

// 隐藏统计展开层
const hideStatsExpandedLayer = () => {
  // 清除显示定时器
  if (statsHoverTimeout) {
    clearTimeout(statsHoverTimeout)
    statsHoverTimeout = null
  }

  // 设置隐藏延时
  statsHoverTimeout = window.setTimeout(() => {
    showStatsExpanded.value = false
    statsHoverTimeout = null
  }, HIDE_DELAY)
}

// 清除延时器（当鼠标进入展开层时调用，防止隐藏）
const clearModelTimeout = () => {
  if (modelHoverTimeout) {
    clearTimeout(modelHoverTimeout)
    modelHoverTimeout = null
  }
}

const clearUpdateTimeout = () => {
  if (updateHoverTimeout) {
    clearTimeout(updateHoverTimeout)
    updateHoverTimeout = null
  }
}

const clearStatsTimeout = () => {
  if (statsHoverTimeout) {
    clearTimeout(statsHoverTimeout)
    statsHoverTimeout = null
  }
}

onUnmounted(() => {
  if (modelHoverTimeout) clearTimeout(modelHoverTimeout)
  if (updateHoverTimeout) clearTimeout(updateHoverTimeout)
  if (statsHoverTimeout) clearTimeout(statsHoverTimeout)
})
</script>

<style scoped>
/* 从原home/index.html复制的样式，保持原有布局不变 */
.container {
  display: flex;
  max-width: none;
  margin: 0;
  padding: 20px;
  gap: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background-attachment: fixed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 140px);
  overflow: visible;
}

/* 信息卡片容器 */
.info-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

/* 基础信息卡片样式 */
.info-card {
  flex: 1;
  height: 150px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  overflow: visible;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.info-card-content {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 卡片头部 */
.info-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  flex-shrink: 0;
}

.info-card-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.info-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.info-card-meta {
  margin-left: auto;
  font-size: 12px;
  color: #7f8c8d;
}

/* 卡片主体内容 */
.info-card-body {
  flex: 1;
  overflow: hidden;
}

/* 信息项目 */
.info-item {
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.info-item-icon {
  font-size: 12px;
}

.info-item-title {
  font-size: 12px;
  font-weight: 500;
  color: #5a6c7d;
}

.info-item-count {
  font-size: 12px;
  color: #95a5a6;
}

.info-item-content {
  padding-left: 16px;
}

/* 模型相关样式 */
.info-model {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 8px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 2px;
}

.model-name {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
}

.model-params {
  font-size: 12px;
  color: #7f8c8d;
  background: white;
  padding: 1px 4px;
  border-radius: 6px;
}

/* 版本更新相关样式 */
.version-tag {
  background: #28a745;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 6px;
}

.update-type {
  font-size: 12px;
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 6px;
}

.update-date {
  font-size: 12px;
  color: #7f8c8d;
  margin-left: auto;
}

.update-feature {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  margin-bottom: 2px;
}

.feature-icon {
  font-size: 12px;
  color: #28a745;
  margin-top: 1px;
}

.feature-text {
  font-size: 12px;
  color: #5a6c7d;
  line-height: 1.3;
}

/* 统计数据相关样式 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding-top: 10px;
  gap: 8px;
}

.stat-item {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
  padding: 8px;
  text-align: center;
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-bottom: 4px;
}

.stat-icon {
  font-size: 12px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.stat-value {
  font-size: 12px;
  font-weight: 700;
  color: #495057;
}

/* 悬浮展开层样式 */
.info-card-expanded {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  max-height: 80vh;
  overflow-y: auto;
  opacity: 1;
  transform: none;
  transition: opacity 0.2s ease;
  box-sizing: border-box;
  pointer-events: auto;
  min-height: 200px;
}

/* 展开层内容样式 */
.info-card-body.expanded {
  overflow-y: auto;
  max-height: none;
}

.info-item.expanded {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.info-item.expanded:last-child {
  margin-bottom: 0;
}

/* 模型展开样式 */
.category-icon {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.category-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.category-count {
  font-size: 12px;
  color: #7f8c8d;
  margin-left: auto;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.model-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.model-card:hover {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-color: #2196f3;
}

.model-card .model-name {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
}

.model-card .model-params {
  font-size: 12px;
  color: #2196f3;
  background: white;
  padding: 2px 6px;
  border-radius: 6px;
  border: 1px solid #2196f3;
}

/* 版本更新展开样式 */
.update-card {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.update-card:hover {
  background: #f1f3f4;
}

.update-card.latest {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-color: #4caf50;
}

.update-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.update-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-tag.latest {
  background: #4caf50;
}

.update-features {
  display: grid;
  gap: 4px;
}

.update-card .update-feature {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 4px 8px;
  background: white;
  border-radius: 6px;
}

.update-card .feature-text {
  font-size: 12px;
  color: #2c3e50;
  line-height: 1.4;
}

/* 统计数据展开样式 */
.stats-grid.expanded {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

.stat-item.expanded {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.stat-item.expanded:hover {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border-color: #9c27b0;
  transform: translateY(-2px);
}

.stat-item.expanded .stat-header {
  margin-bottom: 8px;
}

.stat-item.expanded .stat-icon {
  font-size: 16px;
}

.stat-item.expanded .stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.stat-item.expanded .stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #9c27b0;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
}

.section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 24px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  flex-shrink: 0;
  padding: 24px 24px 10px 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.agent-controls {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}

.agent-search {
  display: flex;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 280px;
  padding: 10px 40px 10px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.search-btn, .clear-search-btn {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.search-btn:hover, .clear-search-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.clear-search-btn {
  right: 35px;
  color: #999;
}

.agent-tabs {
  display: flex;
  gap: 8px;
}

.tab-btn {
  padding: 8px 16px;
  border: 1px solid #e1e8ed;
  background: white;
  color: #5a6c7d;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-btn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
}

.tab-btn.active {
  background: linear-gradient(135deg, #93a4ed, #b485e5);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #5a6c7d;
  flex-shrink: 0;
}

.clear-results-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 13px;
  text-decoration: underline;
}

.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  flex: 1;
  overflow-y: hidden;
  padding-right: 8px;
  padding: 24px;
}

.agent-grid:hover {
  overflow-y: auto;
}

.agent-grid::-webkit-scrollbar {
  width: 6px;
}

.agent-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.agent-grid::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.agent-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

.agent-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  height: 202px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.agent-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.agent-card.search-highlight {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.agent-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  font-weight: bold;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.agent-type {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.agent-description {
  color: #5a6c7d;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.agent-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #7f8c8d;
  margin-top: auto;
  flex-shrink: 0;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-results-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #5a6c7d;
}

.no-results-description {
  font-size: 14px;
  line-height: 1.5;
}

.link-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.sidebar {
  width: 420px;
  flex-shrink: 0;
}

.sidebar-panel {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(245, 247, 250, 0.95) 100%);
  border-left: 1px solid rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.panel-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  position: relative;
}

.panel-tab {
  padding: 6px 12px;
  border: 1px solid #e1e8ed;
  background: white;
  color: #5a6c7d;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.panel-tab:hover {
  border-color: #667eea;
  color: #667eea;
}

.panel-tab.active {
  background: linear-gradient(135deg, #93a4ed, #b485e5);
  color: white;
  border-color: transparent;
}

.panel-tab-more {
  position: relative;
}

.more-btn::after {
  content: '';
}

.more-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  z-index: 10;
  display: none;
}

.panel-tab-more:hover .more-dropdown {
  display: block;
}

.dropdown-item {
  padding: 8px 12px;
  font-size: 12px;
  color: #5a6c7d;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: #f8f9fa;
  color: #667eea;
}

.dropdown-item.active {
  background: #667eea;
  color: white;
}

.top-agents-list {
  padding: 0;
  height: calc(100vh - 215px);
  overflow-y: hidden;
}

.top-agents-list:hover {
  overflow-y: auto;
}

.agent-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.agent-item:hover {
  background: rgba(102, 126, 234, 0.05);
}

.agent-item:last-child {
  border-bottom: none;
}

.agent-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.agent-rank.top3 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.agent-info {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agent-meta {
  font-size: 11px;
  color: #7f8c8d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agent-stats {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.agent-stat {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    flex-direction: column;
    padding: 16px;
  }

  .sidebar {
    width: 100%;
  }

  .agent-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .info-container {
    flex-direction: column;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .agent-grid {
    grid-template-columns: 1fr;
  }

  .panel-tabs {
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.agent-card, .info-section {
  animation: fadeIn 0.5s ease-out;
}

/* 保留必要的动画效果 */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .minimized-taskbar-header {
    margin-right: 8px;
  }
}

/* Home页面特定的最小化任务栏样式 */
.home-minimized-taskbar {
  margin-right: 16px;
}
</style>