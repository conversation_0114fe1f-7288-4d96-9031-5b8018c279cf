# XHC AI Plus 平台配置文件
server:
  port: ${SERVER_PORT:8000}
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10

spring:
  application:
    name: xhcai-plus
  profiles:
    active: dev
  # Security配置 - 开发环境禁用默认安全配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration

  # 禁用数据库初始化依赖检测，解决 MyBatis Plus 兼容性问题
  sql:
    init:
      mode: never

  # Jackson配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

# JWT配置
jwt:
  secret: ${JWT_SECRET:xhcai-plus-jwt-secret-key-2025-very-long-secret-key-for-security-production}
  expiration: ${JWT_EXPIRATION:86400}
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800}
  issuer: xhcai-plus

# 日志配置
logging:
  level:
    com.xhcai: debug
    org.springframework.security: debug
    org.springframework.ai: debug
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: XHC AI Plus Platform
    description: AI智能体服务平台
    version: 1.0.0
    author: xhcai
    contact: <EMAIL>
