package com.xhcai.modules.rag.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件存储配置查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件存储配置查询DTO")
public class FileStorageConfigQueryDTO extends PageTimeRangeQueryDTO {

    /**
     * 存储配置名称
     */
    @Schema(description = "存储配置名称", example = "MinIO")
    private String name;

    /**
     * 存储类型
     */
    @Schema(description = "存储类型", example = "minio")
    private String storageType;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0")
    private String status;

    /**
     * 是否为默认存储
     */
    @Schema(description = "是否为默认存储", example = "Y")
    private String isDefault;
}
