<template>
  <div class="onlyoffice-preview">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <span>正在加载文档预览...</span>
      </div>
    </div>
    <div v-else-if="error" class="error-container">
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <span>{{ error }}</span>
      </div>
      <button @click="retry" class="retry-btn">重试</button>
    </div>
    <div v-else id="onlyoffice-placeholder" class="onlyoffice-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { RagAPI } from '@/api/rag'

// 路由参数
const route = useRoute()
const documentId = route.params.documentId as string

// 响应式数据
const loading = ref(true)
const error = ref('')
const docEditor = ref<any>(null)

// 加载OnlyOffice配置并初始化编辑器
const loadOnlyOfficeEditor = async () => {
  try {
    loading.value = true
    error.value = ''

    // 获取文档配置
    const response = await RagAPI.getOnlyOfficeConfig(documentId, 'view')
    if (!response.success) {
      throw new Error(response.message || '获取文档配置失败')
    }

    const config = response.data

    // 等待OnlyOffice API加载
    await loadOnlyOfficeAPI()

    // 初始化OnlyOffice编辑器
    docEditor.value = new (window as any).DocsAPI.DocEditor('onlyoffice-placeholder', {
      ...config,
      events: {
        onAppReady: () => {
          console.log('OnlyOffice应用已准备就绪')
          loading.value = false
        },
        onError: (event: any) => {
          console.error('OnlyOffice错误:', event)
          error.value = '文档加载失败: ' + (event.data || '未知错误')
          loading.value = false
        },
        onDocumentStateChange: (event: any) => {
          console.log('文档状态变化:', event)
        },
        onRequestEditRights: () => {
          console.log('请求编辑权限')
        }
      }
    })

  } catch (err: any) {
    console.error('加载OnlyOffice编辑器失败:', err)
    error.value = err.message || '加载文档预览失败'
    loading.value = false
  }
}

// 加载OnlyOffice API
const loadOnlyOfficeAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if ((window as any).DocsAPI) {
      resolve()
      return
    }

    // 创建script标签加载OnlyOffice API
    const script = document.createElement('script')
    script.src = 'http://localhost:8080/web-apps/apps/api/documents/api.js' // 这里应该从配置中获取
    script.onload = () => resolve()
    script.onerror = () => reject(new Error('OnlyOffice API加载失败'))
    document.head.appendChild(script)
  })
}

// 重试加载
const retry = () => {
  loadOnlyOfficeEditor()
}

// 生命周期
onMounted(() => {
  if (documentId) {
    loadOnlyOfficeEditor()
  } else {
    error.value = '缺少文档ID参数'
    loading.value = false
  }
})

onUnmounted(() => {
  // 销毁编辑器实例
  if (docEditor.value) {
    try {
      docEditor.value.destroyEditor()
    } catch (err) {
      console.warn('销毁OnlyOffice编辑器失败:', err)
    }
  }
})
</script>

<style scoped>
.onlyoffice-preview {
  width: 100%;
  height: 100vh;
  position: relative;
  background: #f5f5f5;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: white;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #666;
}

.loading-spinner i {
  font-size: 32px;
  color: #3b82f6;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: white;
  gap: 20px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #dc2626;
  font-size: 16px;
}

.error-message i {
  font-size: 24px;
}

.retry-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #2563eb;
}

.onlyoffice-container {
  width: 100%;
  height: 100%;
}

/* 隐藏OnlyOffice的一些默认元素 */
:deep(.asc-window) {
  z-index: 1000 !important;
}

:deep(.toolbar) {
  background: #f8f9fa !important;
}
</style>
