package com.xhcai.common.datasource.health;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

/**
 * 动态数据源健康检查指示器
 * 专门用于检查动态数据源的连接状态
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("dynamicDataSourceHealthIndicator")
@ConditionalOnClass({HealthIndicator.class, DynamicRoutingDataSource.class})
public class DynamicDataSourceHealthIndicator implements HealthIndicator {

    private static final Logger log = LoggerFactory.getLogger(DynamicDataSourceHealthIndicator.class);

    @Autowired(required = false)
    private DynamicRoutingDataSource dynamicDataSource;

    @Override
    public Health health() {
        try {
            return checkDynamicDataSourceHealth();
        } catch (Exception e) {
            log.error("动态数据源健康检查失败", e);
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("type", "DynamicDataSourceError")
                    .build();
        }
    }

    private Health checkDynamicDataSourceHealth() {
        Health.Builder builder = Health.up();
        
        if (dynamicDataSource == null) {
            return builder
                    .withDetail("status", "动态数据源未配置")
                    .withDetail("message", "请检查 dynamic-datasource 配置")
                    .build();
        }

        try {
            Map<String, DataSource> dataSources = dynamicDataSource.getDataSources();
            
            builder.withDetail("type", "DynamicRoutingDataSource");
            builder.withDetail("totalDataSources", dataSources.size());
            builder.withDetail("dataSourceNames", dataSources.keySet());
            
            int healthyCount = 0;
            int totalCount = dataSources.size();
            
            for (Map.Entry<String, DataSource> entry : dataSources.entrySet()) {
                String dsName = entry.getKey();
                DataSource ds = entry.getValue();
                
                try {
                    checkSingleDataSource(ds);
                    builder.withDetail(dsName + "_status", "UP");
                    healthyCount++;
                    log.debug("数据源 {} 连接正常", dsName);
                } catch (Exception e) {
                    builder.withDetail(dsName + "_status", "DOWN");
                    builder.withDetail(dsName + "_error", e.getMessage());
                    log.warn("数据源 {} 连接异常: {}", dsName, e.getMessage());
                }
            }
            
            builder.withDetail("healthyDataSources", healthyCount);
            builder.withDetail("unhealthyDataSources", totalCount - healthyCount);
            
            // 如果所有数据源都不健康，则整体状态为DOWN
            if (healthyCount == 0 && totalCount > 0) {
                builder.down();
                builder.withDetail("overallStatus", "所有数据源都不可用");
            } else if (healthyCount < totalCount) {
                builder.withDetail("overallStatus", "部分数据源不可用");
            } else {
                builder.withDetail("overallStatus", "所有数据源正常");
            }
            
        } catch (Exception e) {
            log.error("获取动态数据源信息失败", e);
            builder.down()
                    .withDetail("error", "获取数据源信息失败: " + e.getMessage());
        }
        
        return builder.build();
    }

    private void checkSingleDataSource(DataSource dataSource) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (!connection.isValid(5)) {
                throw new SQLException("数据源连接无效");
            }
        }
    }
}
