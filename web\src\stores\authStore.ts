/**
 * 认证状态管理
 * 管理用户登录状态、token信息等
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { envConfig, logger } from '@/config/env'
import type { TokenInfo } from '@/types/api'

// 用户角色枚举
export enum UserRole {
  USER = 'user',      // 使用者
  DESIGNER = 'designer' // 设计者
}

export interface UserInfo {
  id: string
  username: string
  name?: string
  email?: string
  avatar?: string
  role?: UserRole
  roles?: string[]
  permissions?: string[]
  isLoggedIn?: boolean
  loginTime?: Date
  lastActiveTime?: Date
}

export interface AuthState {
  tokens: TokenInfo | null
  userInfo: UserInfo | null
  isLoggedIn: boolean
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const tokens = ref<TokenInfo | null>(null)
  const userInfo = ref<UserInfo | null>(null)
  
  // 计算属性
  const isLoggedIn = computed(() => {
    return !!(tokens.value?.token && tokens.value?.access_token) || !!userInfo.value?.isLoggedIn
  })

  const hasPermission = computed(() => {
    return (permission: string) => {
      if (!userInfo.value?.permissions) return false
      return userInfo.value.permissions.includes(permission)
    }
  })

  const hasRole = computed(() => {
    return (role: string) => {
      if (!userInfo.value?.roles) return false
      return userInfo.value.roles.includes(role)
    }
  })

  // 用户角色相关计算属性
  const isUser = computed(() => userInfo.value?.role === UserRole.USER)
  const isDesigner = computed(() => userInfo.value?.role === UserRole.DESIGNER)
  const userDisplayName = computed(() => userInfo.value?.name || userInfo.value?.username || '未登录')
  const userAvatar = computed(() => userInfo.value?.avatar || '')

  // 检测是否通过 /ai 路由直接访问
  const isDirectAccess = computed(() => {
    return window.location.pathname === '/ai'
  })

  // 是否显示用户区域（使用者角色且直接访问时显示）
  const shouldShowUserArea = computed(() => {
    return isUser.value && isDirectAccess.value
  })

  /**
   * 从localStorage加载token信息
   */
  const loadTokensFromStorage = (): TokenInfo | null => {
    try {
      const stored = localStorage.getItem(envConfig.tokenStorageKey)
      if (stored) {
        const parsed = JSON.parse(stored)
        logger.debug('Loaded tokens from storage:', parsed)
        return parsed
      }
    } catch (error) {
      logger.error('Failed to load tokens from storage:', error)
      clearTokensFromStorage()
    }
    return null
  }

  /**
   * 从localStorage加载用户信息
   */
  const loadUserFromStorage = (): UserInfo | null => {
    try {
      const savedUser = localStorage.getItem('current-user')
      const token = localStorage.getItem('user-token')

      if (savedUser && token) {
        const parsed = JSON.parse(savedUser)
        // 转换日期字符串为Date对象
        const user: UserInfo = {
          ...parsed,
          loginTime: parsed.loginTime ? new Date(parsed.loginTime) : undefined,
          lastActiveTime: parsed.lastActiveTime ? new Date(parsed.lastActiveTime) : undefined
        }
        logger.debug('Loaded user from storage:', user)
        return user
      }
    } catch (error) {
      logger.error('Failed to load user from storage:', error)
      clearUserFromStorage()
    }
    return null
  }

  /**
   * 保存token信息到localStorage
   */
  const saveTokensToStorage = (tokenInfo: TokenInfo): void => {
    try {
      localStorage.setItem(envConfig.tokenStorageKey, JSON.stringify(tokenInfo))
      logger.debug('Saved tokens to storage:', tokenInfo)
    } catch (error) {
      logger.error('Failed to save tokens to storage:', error)
    }
  }

  /**
   * 保存用户信息到localStorage
   */
  const saveUserToStorage = (user: UserInfo): void => {
    try {
      localStorage.setItem('current-user', JSON.stringify(user))
      localStorage.setItem('user-token', user.id) // 简单的token存储
      logger.debug('Saved user to storage:', user)
    } catch (error) {
      logger.error('Failed to save user to storage:', error)
    }
  }

  /**
   * 从localStorage清除token信息
   */
  const clearTokensFromStorage = (): void => {
    try {
      localStorage.removeItem(envConfig.tokenStorageKey)
      // 兼容旧版本
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('username')
      logger.debug('Cleared tokens from storage')
    } catch (error) {
      logger.error('Failed to clear tokens from storage:', error)
    }
  }

  /**
   * 从localStorage清除用户信息
   */
  const clearUserFromStorage = (): void => {
    try {
      localStorage.removeItem('current-user')
      localStorage.removeItem('user-token')
      logger.debug('Cleared user from storage')
    } catch (error) {
      logger.error('Failed to clear user from storage:', error)
    }
  }

  /**
   * 彻底清除所有认证相关信息（用于退出登录）
   */
  const clearAllAuthData = (): void => {
    try {
      // 清除内存中的状态
      tokens.value = null
      userInfo.value = null

      // 清除localStorage中的所有相关数据
      localStorage.removeItem(envConfig.tokenStorageKey)
      localStorage.removeItem('current-user')
      localStorage.removeItem('user-token')
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('username')

      logger.info('All authentication data cleared')
    } catch (error) {
      logger.error('Failed to clear all auth data:', error)
    }
  }

  /**
   * 检查token是否过期
   */
  const isTokenExpired = (tokenInfo?: TokenInfo): boolean => {
    if (!tokenInfo?.expires_in || !tokenInfo?.token) return true

    // 直接使用JWT工具验证token是否过期
    // 因为JWT token本身包含过期时间信息，这是最准确的方式
    try {
      // 简单的JWT过期检查：解析token的payload部分
      const payload = JSON.parse(atob(tokenInfo.token.split('.')[1]))
      const now = Math.floor(Date.now() / 1000)

      // JWT的exp字段是绝对时间戳（秒）
      if (payload.exp && now > payload.exp) {
        logger.debug('Token已过期:', { exp: payload.exp, now })
        return true
      }

      return false
    } catch (error) {
      logger.error('检查token过期状态失败:', error)
      return true // 解析失败时认为已过期
    }
  }

  /**
   * 设置认证信息
   */
  const setAuth = (tokenInfo: TokenInfo, user?: UserInfo): void => {
    logger.debug('设置认证信息:', { tokenInfo, user })

    tokens.value = tokenInfo
    if (user) {
      userInfo.value = user
      if (user.isLoggedIn) {
        saveUserToStorage(user)
      }
    }
    saveTokensToStorage(tokenInfo)

    logger.info('Authentication set successfully')
    logger.debug('认证状态设置后:', {
      hasTokens: !!tokens.value,
      hasUser: !!userInfo.value,
      isLoggedIn: isLoggedIn.value
    })
  }

  /**
   * 用户登录（简化版本，适用于开发环境）
   */
  const login = (userData: Omit<UserInfo, 'isLoggedIn' | 'loginTime' | 'lastActiveTime'>): void => {
    const user: UserInfo = {
      ...userData,
      isLoggedIn: true,
      loginTime: new Date(),
      lastActiveTime: new Date()
    }
    userInfo.value = user
    saveUserToStorage(user)
    logger.info('User logged in successfully:', user)
  }

  /**
   * 用户登出
   */
  const logout = (): void => {
    const shouldLogout = confirm('确定要退出登录吗？')
    if (shouldLogout) {
      // 彻底清除所有认证相关信息
      clearAllAuthData()

      logger.info('User logged out')

      // 强制刷新页面到登录页面
      window.location.replace(envConfig.loginRedirectPath)
    }
  }

  /**
   * 清除认证信息
   */
  const clearAuth = (): void => {
    tokens.value = null
    userInfo.value = null
    clearTokensFromStorage()
    logger.info('Authentication cleared')
  }

  /**
   * 初始化认证状态
   */
  const initAuth = (): void => {
    logger.debug('开始初始化认证状态')

    // 加载token信息
    const storedTokens = loadTokensFromStorage()
    logger.debug('从存储加载的tokens:', storedTokens)

    if (storedTokens) {
      const expired = isTokenExpired(storedTokens)
      logger.debug('Token过期检查结果:', expired)

      if (!expired) {
        tokens.value = storedTokens
        logger.info('Authentication initialized from storage')
      } else {
        logger.warn('Stored tokens expired, clearing auth')
        clearAuth()
      }
    } else {
      logger.debug('没有找到存储的tokens')
    }

    // 加载用户信息
    const storedUser = loadUserFromStorage()
    if (storedUser) {
      userInfo.value = storedUser
      logger.info('User info initialized from storage')
    } else {
      logger.debug('没有找到存储的用户信息')
    }

    logger.debug('认证状态初始化完成:', {
      hasTokens: !!tokens.value,
      hasUser: !!userInfo.value,
      isLoggedIn: isLoggedIn.value
    })
  }

  /**
   * 更新用户信息
   */
  const setUserInfo = (user: UserInfo): void => {
    userInfo.value = user
    if (user.isLoggedIn) {
      saveUserToStorage(user)
    }
    logger.debug('User info updated:', user)
  }

  /**
   * 更新用户信息（部分更新）
   */
  const updateUserInfo = (updates: Partial<UserInfo>): void => {
    if (userInfo.value) {
      Object.assign(userInfo.value, updates)
      userInfo.value.lastActiveTime = new Date()
      if (userInfo.value.isLoggedIn) {
        saveUserToStorage(userInfo.value)
      }
      logger.debug('User info updated:', userInfo.value)
    }
  }

  /**
   * 更新最后活跃时间
   */
  const updateLastActiveTime = (): void => {
    if (userInfo.value) {
      userInfo.value.lastActiveTime = new Date()
      if (userInfo.value.isLoggedIn) {
        saveUserToStorage(userInfo.value)
      }
    }
  }



  /**
   * 更新token信息
   */
  const updateTokens = (tokenInfo: Partial<TokenInfo>): void => {
    if (tokens.value) {
      tokens.value = { ...tokens.value, ...tokenInfo }
      saveTokensToStorage(tokens.value)
      logger.debug('Tokens updated:', tokens.value)
    }
  }

  /**
   * 获取认证头
   * 为除登录接口外的所有请求添加三个token
   * token -> Authorization (Bearer格式)
   */
  const getAuthHeaders = (): Record<string, string> => {
    const headers: Record<string, string> = {}

    // token对应Authorization头，需要添加Bearer前缀
    if (tokens.value?.token) {
      // 检查token是否已经包含Bearer前缀
      const token = tokens.value.token
      if (token.startsWith('Bearer ')) {
        headers['Authorization'] = token
      } else {
        headers['Authorization'] = `Bearer ${token}`
      }
    }

    return headers
  }

  /**
   * 检查是否需要刷新token
   */
  const shouldRefreshToken = (): boolean => {
    if (!tokens.value) return false
    
    // 如果token即将过期（提前5分钟刷新）
    if (tokens.value.expires_in) {
      const now = Date.now() / 1000
      const expiresIn = tokens.value.expires_in
      return (expiresIn - now) < 300 // 5分钟
    }
    
    return false
  }

  // 初始化
  initAuth()

  return {
    // 状态
    tokens: readonly(tokens),
    currentUser: readonly(userInfo), // 直接使用 userInfo，保持向后兼容

    // 计算属性
    isLoggedIn,
    hasPermission,
    hasRole,
    isUser,
    isDesigner,
    userDisplayName,
    userAvatar,
    isDirectAccess,
    shouldShowUserArea,

    // 认证方法
    setAuth,
    clearAuth,
    initAuth,
    setUserInfo,
    updateTokens,
    getAuthHeaders,
    shouldRefreshToken,
    isTokenExpired: () => isTokenExpired(tokens.value || undefined),

    // 用户管理方法
    login,
    logout,
    updateUserInfo,
    updateLastActiveTime,

    // 存储方法
    saveUserToStorage: () => userInfo.value && saveUserToStorage(userInfo.value),
    loadUserFromStorage,
    clearUserFromStorage
  }
})

export default useAuthStore
