package com.xhcai.common.core.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 时间转换工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TimeUtils {

    /**
     * 北京时区
     */
    public static final ZoneId BEIJING_ZONE = ZoneId.of("Asia/Shanghai");
    
    /**
     * 默认日期时间格式
     */
    public static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 将Unix时间戳（秒）转换为北京时间的LocalDateTime
     * 
     * @param timestamp Unix时间戳（秒）
     * @return 北京时间的LocalDateTime，如果timestamp为null则返回null
     */
    public static LocalDateTime convertTimestampToBeijingTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofInstant(
            Instant.ofEpochSecond(timestamp), 
            BEIJING_ZONE
        );
    }

    /**
     * 将Unix时间戳（毫秒）转换为北京时间的LocalDateTime
     * 
     * @param timestampMillis Unix时间戳（毫秒）
     * @return 北京时间的LocalDateTime，如果timestampMillis为null则返回null
     */
    public static LocalDateTime convertTimestampMillisToBeijingTime(Long timestampMillis) {
        if (timestampMillis == null) {
            return null;
        }
        return LocalDateTime.ofInstant(
            Instant.ofEpochMilli(timestampMillis), 
            BEIJING_ZONE
        );
    }

    /**
     * 将Unix时间戳（秒）转换为北京时间的格式化字符串
     * 
     * @param timestamp Unix时间戳（秒）
     * @return 格式化的北京时间字符串，如果timestamp为null则返回null
     */
    public static String formatTimestampToBeijingTime(Long timestamp) {
        LocalDateTime dateTime = convertTimestampToBeijingTime(timestamp);
        return dateTime != null ? dateTime.format(DEFAULT_DATETIME_FORMATTER) : null;
    }

    /**
     * 将Unix时间戳（毫秒）转换为北京时间的格式化字符串
     * 
     * @param timestampMillis Unix时间戳（毫秒）
     * @return 格式化的北京时间字符串，如果timestampMillis为null则返回null
     */
    public static String formatTimestampMillisToBeijingTime(Long timestampMillis) {
        LocalDateTime dateTime = convertTimestampMillisToBeijingTime(timestampMillis);
        return dateTime != null ? dateTime.format(DEFAULT_DATETIME_FORMATTER) : null;
    }

    /**
     * 将LocalDateTime转换为Unix时间戳（秒）
     * 
     * @param dateTime LocalDateTime对象
     * @return Unix时间戳（秒），如果dateTime为null则返回null
     */
    public static Long convertToTimestamp(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.atZone(BEIJING_ZONE).toEpochSecond();
    }

    /**
     * 将LocalDateTime转换为Unix时间戳（毫秒）
     * 
     * @param dateTime LocalDateTime对象
     * @return Unix时间戳（毫秒），如果dateTime为null则返回null
     */
    public static Long convertToTimestampMillis(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.atZone(BEIJING_ZONE).toInstant().toEpochMilli();
    }
}
