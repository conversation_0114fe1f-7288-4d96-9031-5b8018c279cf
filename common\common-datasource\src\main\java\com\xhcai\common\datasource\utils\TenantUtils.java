package com.xhcai.common.datasource.utils;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.xhcai.common.datasource.annotation.NoTenant;

/**
 * 租户工具类 提供租户相关的工具方法，包括检查实体是否需要租户字段等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TenantUtils {

    private static final Logger log = LoggerFactory.getLogger(TenantUtils.class);

    /**
     * 不需要租户隔离的表名列表（硬编码配置） 这些表即使没有@NoTenant注解也会被忽略
     *
     * <p>
     * 包含以下类型的表：</p>
     * <ul>
     * <li>租户管理相关表：sys_tenant</li>
     * <li>全局系统配置表：sys_dict, sys_permission</li>
     * <li>跨租户日志表：sys_log_operation, sys_log_login</li>
     * </ul>
     */
    private static final List<String> IGNORE_TABLES = Arrays.asList(
            // 租户管理表
            "sys_tenant", // 租户表本身，不需要租户隔离

            // 全局系统配置表（使用@NoTenant注解的表）
            "sys_dict", // 字典类型表，全局共享
            "sys_permission", // 权限信息表，全局共享

            // 跨租户日志表
            "sys_log_operation", // 操作日志表，记录跨租户操作
            "sys_log_login" // 登录日志表，记录跨租户登录
    );

    /**
     * 缓存实体类的租户检查结果，避免重复反射
     */
    private static final ConcurrentHashMap<Class<?>, Boolean> TENANT_CACHE = new ConcurrentHashMap<>();

    /**
     * 缓存表名的租户检查结果，避免重复计算
     */
    private static final ConcurrentHashMap<String, Boolean> TABLE_CACHE = new ConcurrentHashMap<>();

    /**
     * 检查实体类是否需要租户字段
     *
     * @param entityClass 实体类
     * @return true-需要租户字段，false-不需要租户字段
     */
    public static boolean needsTenantField(Class<?> entityClass) {
        if (entityClass == null) {
            return false;
        }

        // 从缓存中获取结果
        return TENANT_CACHE.computeIfAbsent(entityClass, clazz -> {
            // 检查是否有@NoTenant注解
            NoTenant noTenant = clazz.getAnnotation(NoTenant.class);
            if (noTenant != null) {
                log.debug("实体类 {} 标记了@NoTenant注解，原因: {}", clazz.getSimpleName(),
                        StringUtils.hasText(noTenant.reason()) ? noTenant.reason() : "未指定");
                return false;
            }

            // 默认需要租户字段
            log.debug("实体类 {} 需要租户字段", clazz.getSimpleName());
            return true;
        });
    }

    /**
     * 检查表名是否需要租户隔离
     *
     * @param tableName 表名
     * @return true-需要租户隔离，false-不需要租户隔离
     */
    public static boolean needsTenantIsolation(String tableName) {
        if (!StringUtils.hasText(tableName)) {
            return false;
        }

        // 从缓存中获取结果
        return TABLE_CACHE.computeIfAbsent(tableName.toLowerCase(), name -> {
            // 检查是否在忽略列表中
            boolean ignore = IGNORE_TABLES.contains(name);
            if (ignore) {
                log.debug("表 {} 在忽略列表中，不需要租户隔离", name);
                return false;
            }

            // 默认需要租户隔离
            log.debug("表 {} 需要租户隔离", name);
            return true;
        });
    }
}
