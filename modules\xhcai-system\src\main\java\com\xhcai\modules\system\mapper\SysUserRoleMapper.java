package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.system.entity.SysUserRole;

/**
 * 用户角色关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     */
    @Delete("DELETE FROM sys_user_role WHERE user_id = #{userId} AND tenant_id = #{tenantId}")
    void deleteByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @param tenantId 租户ID
     */
    @Delete("DELETE FROM sys_user_role WHERE role_id = #{roleId} AND tenant_id = #{tenantId}")
    void deleteByRoleId(@Param("roleId") String roleId, @Param("tenantId") String tenantId);

    /**
     * 根据用户ID查询角色ID列表
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_user_role WHERE user_id = #{userId} AND tenant_id = #{tenantId} AND deleted = 0")
    List<String> selectRoleIdsByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM sys_user_role WHERE role_id = #{roleId} AND tenant_id = #{tenantId} AND deleted = 0")
    List<String> selectUserIdsByRoleId(@Param("roleId") String roleId, @Param("tenantId") String tenantId);

    /**
     * 检查用户角色关联是否存在
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_user_role WHERE user_id = #{userId} AND role_id = #{roleId} AND tenant_id = #{tenantId} AND deleted = 0")
    boolean existsUserRole(@Param("userId") String userId, @Param("roleId") String roleId, @Param("tenantId") String tenantId);

    /**
     * 统计角色下的用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM sys_user_role "
            + "WHERE role_id = #{roleId} AND deleted = 0")
    Integer countUsersByRoleId(@Param("roleId") String roleId);

    /**
     * 查询平台管理员用户数量（绕过多租户插件）
     *
     * @param roleCode 角色编码
     * @param tenantPrefix 租户前缀
     * @return 用户数量
     */
    @Select("SELECT COUNT(DISTINCT ur.user_id) " +
            "FROM sys_user_role ur " +
            "INNER JOIN sys_role r ON ur.role_id = r.id " +
            "INNER JOIN sys_tenant t ON ur.tenant_id = t.id " +
            "WHERE r.role_code = #{roleCode} " +
            "AND t.tenant_code LIKE CONCAT(#{tenantPrefix}, '%') " +
            "AND ur.deleted = 0 " +
            "AND r.deleted = 0 " +
            "AND t.deleted = 0")
    Long countPlatformAdminUsers(@Param("roleCode") String roleCode, @Param("tenantPrefix") String tenantPrefix);
}
