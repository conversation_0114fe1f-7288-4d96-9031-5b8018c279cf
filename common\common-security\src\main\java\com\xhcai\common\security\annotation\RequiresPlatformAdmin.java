package com.xhcai.common.security.annotation;

import java.lang.annotation.*;

/**
 * 平台管理员权限校验注解
 * 用于标识需要平台管理员权限的方法或类
 * 平台管理员拥有跨租户操作权限
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPlatformAdmin {

    /**
     * 权限校验失败时的提示信息
     */
    String message() default "需要平台管理员权限";

    /**
     * 是否允许租户管理员访问
     * 如果为true，租户管理员也可以访问（但只能操作自己租户的数据）
     * 如果为false，只有平台管理员可以访问
     */
    boolean allowTenantAdmin() default false;
}
