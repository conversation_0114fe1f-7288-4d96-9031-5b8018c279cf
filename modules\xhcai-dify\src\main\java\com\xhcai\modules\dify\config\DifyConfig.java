package com.xhcai.modules.dify.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Dify平台配置
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Configuration
@ConfigurationProperties(prefix = "dify")
public class DifyConfig {

    private static final Logger log = LoggerFactory.getLogger(DifyConfig.class);

    /**
     * Dify API基础URL
     */
    private String baseUrl = "http://192.168.50.142";

    /**
     * Dify 登录邮箱
     */
    private String email = "<EMAIL>";

    /**
     * Dify 登录密码
     */
    private String password = "XH12345@";

    /**
     * 语言设置
     */
    private String language = "zh-Hans";

    /**
     * 应用ID
     */
    private String appId;



    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 是否启用SSL验证
     */
    private boolean sslEnabled = true;

    /**
     * 重试次数
     */
    private int retryCount = 3;

    /**
     * 重试间隔（毫秒）
     */
    private long retryInterval = 1000;

    /**
     * 是否启用测试模式（使用Mock响应）
     */
    private boolean testMode = true;

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }



    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public boolean isSslEnabled() {
        return sslEnabled;
    }

    public void setSslEnabled(boolean sslEnabled) {
        this.sslEnabled = sslEnabled;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public long getRetryInterval() {
        return retryInterval;
    }

    public void setRetryInterval(long retryInterval) {
        this.retryInterval = retryInterval;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public boolean isTestMode() {
        return testMode;
    }

    public void setTestMode(boolean testMode) {
        this.testMode = testMode;
    }

    @PostConstruct
    public void logConfiguration() {
        log.info("=== Dify 配置信息 ===");
        log.info("Base URL: {}", baseUrl);
        log.info("Connect Timeout: {}ms", connectTimeout);
        log.info("Read Timeout: {}ms", readTimeout);
        log.info("SSL Enabled: {}", sslEnabled);
        log.info("Retry Count: {}", retryCount);
        log.info("Retry Interval: {}ms", retryInterval);
        log.info("Test Mode: {}", testMode);
        log.info("===================");
    }
}
