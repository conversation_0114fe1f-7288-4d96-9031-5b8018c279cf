package com.xhcai.modules.dify.dto.plugin;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 插件DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyPluginDTO {

    /**
     * 插件ID
     */
    private String id;

    /**
     * 插件名称
     */
    @NotBlank(message = "插件名称不能为空")
    @Size(max = 100, message = "插件名称长度不能超过100个字符")
    private String name;

    /**
     * 插件描述
     */
    @Size(max = 500, message = "插件描述长度不能超过500个字符")
    private String description;

    /**
     * 插件图标
     */
    private String icon;

    /**
     * 插件版本
     */
    private String version;

    /**
     * 插件作者
     */
    private String author;

    /**
     * 插件类型：builtin-内置, custom-自定义, third_party-第三方
     */
    private String type;

    /**
     * 插件分类
     */
    private String category;

    /**
     * 插件状态：active-活跃, inactive-非活跃, deprecated-已弃用
     */
    private String status;

    /**
     * 插件配置
     */
    private PluginConfig config;

    /**
     * 插件工具列表
     */
    private List<PluginTool> tools;

    /**
     * 插件权限
     */
    private List<String> permissions;

    /**
     * 插件标签
     */
    private List<String> tags;

    /**
     * 安装数量
     */
    @JsonProperty("install_count")
    private Integer installCount;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 扩展属性
     */
    private Map<String, Object> metadata;

    /**
     * 插件配置
     */
    public static class PluginConfig {
        /**
         * API端点
         */
        @JsonProperty("api_endpoint")
        private String apiEndpoint;

        /**
         * 认证配置
         */
        @JsonProperty("auth_config")
        private AuthConfig authConfig;

        /**
         * 超时配置
         */
        @JsonProperty("timeout_config")
        private TimeoutConfig timeoutConfig;

        /**
         * 重试配置
         */
        @JsonProperty("retry_config")
        private RetryConfig retryConfig;

        /**
         * 自定义配置
         */
        @JsonProperty("custom_config")
        private Map<String, Object> customConfig;

        public static class AuthConfig {
            private String type; // none, api_key, oauth, basic
            @JsonProperty("api_key")
            private String apiKey;
            private String username;
            private String password;
            @JsonProperty("oauth_config")
            private Map<String, Object> oauthConfig;

            // Getters and Setters
            public String getType() { return type; }
            public void setType(String type) { this.type = type; }
            public String getApiKey() { return apiKey; }
            public void setApiKey(String apiKey) { this.apiKey = apiKey; }
            public String getUsername() { return username; }
            public void setUsername(String username) { this.username = username; }
            public String getPassword() { return password; }
            public void setPassword(String password) { this.password = password; }
            public Map<String, Object> getOauthConfig() { return oauthConfig; }
            public void setOauthConfig(Map<String, Object> oauthConfig) { this.oauthConfig = oauthConfig; }
        }

        public static class TimeoutConfig {
            @JsonProperty("connect_timeout")
            private Integer connectTimeout;
            @JsonProperty("read_timeout")
            private Integer readTimeout;

            // Getters and Setters
            public Integer getConnectTimeout() { return connectTimeout; }
            public void setConnectTimeout(Integer connectTimeout) { this.connectTimeout = connectTimeout; }
            public Integer getReadTimeout() { return readTimeout; }
            public void setReadTimeout(Integer readTimeout) { this.readTimeout = readTimeout; }
        }

        public static class RetryConfig {
            @JsonProperty("max_retries")
            private Integer maxRetries;
            @JsonProperty("retry_interval")
            private Long retryInterval;

            // Getters and Setters
            public Integer getMaxRetries() { return maxRetries; }
            public void setMaxRetries(Integer maxRetries) { this.maxRetries = maxRetries; }
            public Long getRetryInterval() { return retryInterval; }
            public void setRetryInterval(Long retryInterval) { this.retryInterval = retryInterval; }
        }

        // Getters and Setters
        public String getApiEndpoint() { return apiEndpoint; }
        public void setApiEndpoint(String apiEndpoint) { this.apiEndpoint = apiEndpoint; }
        public AuthConfig getAuthConfig() { return authConfig; }
        public void setAuthConfig(AuthConfig authConfig) { this.authConfig = authConfig; }
        public TimeoutConfig getTimeoutConfig() { return timeoutConfig; }
        public void setTimeoutConfig(TimeoutConfig timeoutConfig) { this.timeoutConfig = timeoutConfig; }
        public RetryConfig getRetryConfig() { return retryConfig; }
        public void setRetryConfig(RetryConfig retryConfig) { this.retryConfig = retryConfig; }
        public Map<String, Object> getCustomConfig() { return customConfig; }
        public void setCustomConfig(Map<String, Object> customConfig) { this.customConfig = customConfig; }
    }

    /**
     * 插件工具
     */
    public static class PluginTool {
        private String name;
        private String description;
        private List<ToolParameter> parameters;
        @JsonProperty("return_type")
        private String returnType;

        public static class ToolParameter {
            private String name;
            private String type;
            private String description;
            private boolean required;
            @JsonProperty("default_value")
            private Object defaultValue;
            private List<String> options;

            // Getters and Setters
            public String getName() { return name; }
            public void setName(String name) { this.name = name; }
            public String getType() { return type; }
            public void setType(String type) { this.type = type; }
            public String getDescription() { return description; }
            public void setDescription(String description) { this.description = description; }
            public boolean isRequired() { return required; }
            public void setRequired(boolean required) { this.required = required; }
            public Object getDefaultValue() { return defaultValue; }
            public void setDefaultValue(Object defaultValue) { this.defaultValue = defaultValue; }
            public List<String> getOptions() { return options; }
            public void setOptions(List<String> options) { this.options = options; }
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public List<ToolParameter> getParameters() { return parameters; }
        public void setParameters(List<ToolParameter> parameters) { this.parameters = parameters; }
        public String getReturnType() { return returnType; }
        public void setReturnType(String returnType) { this.returnType = returnType; }
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getIcon() { return icon; }
    public void setIcon(String icon) { this.icon = icon; }
    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
    public String getAuthor() { return author; }
    public void setAuthor(String author) { this.author = author; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public PluginConfig getConfig() { return config; }
    public void setConfig(PluginConfig config) { this.config = config; }
    public List<PluginTool> getTools() { return tools; }
    public void setTools(List<PluginTool> tools) { this.tools = tools; }
    public List<String> getPermissions() { return permissions; }
    public void setPermissions(List<String> permissions) { this.permissions = permissions; }
    public List<String> getTags() { return tags; }
    public void setTags(List<String> tags) { this.tags = tags; }
    public Integer getInstallCount() { return installCount; }
    public void setInstallCount(Integer installCount) { this.installCount = installCount; }
    public Double getRating() { return rating; }
    public void setRating(Double rating) { this.rating = rating; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
}
