<template>
  <div class="datasource-flow-editor">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="flow-info">
          <i class="fas fa-stream"></i>
          <span class="flow-name">{{ knowledgeBaseName }} - 数据源流程</span>
          <span class="version">v1.0.0</span>
        </div>
      </div>

      <div class="toolbar-center">
        <div class="panel-controls">
          <button
            class="btn btn-icon"
            @click="toggleNodeLibrary"
            :class="{ active: showNodeLibrary }"
            title="节点库面板"
          >
            <i class="fas fa-th-large"></i>
          </button>
          <button
            class="btn btn-icon"
            @click="togglePropertyPanel"
            :class="{ active: showPropertyPanel }"
            title="属性面板"
          >
            <i class="fas fa-sliders-h"></i>
          </button>
        </div>
        <div class="canvas-controls">
          <button
            class="btn btn-icon"
            @click="zoomIn"
            title="放大"
          >
            <i class="fas fa-search-plus"></i>
          </button>
          <span class="zoom-level">{{ Math.round(viewport.zoom * 100) }}%</span>
          <button
            class="btn btn-icon"
            @click="zoomOut"
            title="缩小"
          >
            <i class="fas fa-search-minus"></i>
          </button>
          <button
            class="btn btn-icon"
            @click="fitView"
            title="适应画布"
          >
            <i class="fas fa-expand-arrows-alt"></i>
          </button>
        </div>
      </div>

      <div class="toolbar-right">
        <button
          class="btn btn-secondary"
          @click="validateFlow"
          :disabled="!hasNodes"
        >
          <i class="fas fa-check-circle"></i>
          验证
        </button>
        <button
          class="btn btn-secondary"
          @click="clearCanvas"
          :disabled="!hasNodes"
        >
          <i class="fas fa-trash"></i>
          清除
        </button>
        <button
          class="btn btn-secondary"
          @click="toggleConfigPanel"
          :class="{ active: showConfigPanel }"
        >
          <i class="fas fa-cog"></i>
          配置
        </button>
        <button
          class="btn btn-primary"
          @click="runFlow"
          :disabled="!hasNodes || !isFlowValid"
        >
          <i class="fas fa-play"></i>
          运行
        </button>
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-container">
      <!-- 左侧节点库面板 -->
      <div
        class="node-library-panel"
        :class="{ collapsed: !showNodeLibrary }"
        :style="{ width: showNodeLibrary ? `${leftPanelWidth}px` : '50px' }"
      >
        <NodeLibrary
          v-if="showNodeLibrary"
          @toggle="toggleNodeLibrary"
        />
        <div v-else class="collapsed-panel">
          <button
            class="expand-btn"
            @click="toggleNodeLibrary"
            title="展开节点库"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        <!-- 右边框拖拽调整 -->
        <div
          v-if="showNodeLibrary"
          class="resize-handle resize-handle-right"
          @mousedown="startResize('left')"
        ></div>
      </div>

      <!-- 画布区域 -->
      <div class="canvas-container">
        <FlowCanvas
          :nodes="nodes"
          :edges="edges"
          :viewport="viewport"
          @connect="onConnect"
          @pane-ready="onPaneReady"
          @drop="onDrop"
          @dragover="onDragOver"
          @node-click="onNodeClick"
        />
      </div>

      <!-- 右侧属性面板 -->
      <div
        class="property-panel"
        :class="{ collapsed: !showPropertyPanel }"
        :style="{ width: showPropertyPanel ? `${rightPanelWidth}px` : '50px' }"
      >
        <!-- 左边框拖拽调整 -->
        <div
          v-if="showPropertyPanel"
          class="resize-handle resize-handle-left"
          @mousedown="startResize('right')"
        ></div>

        <div v-if="showPropertyPanel" class="property-content">
          <div class="property-header">
            <div class="header-info">
              <div class="header-icon" v-if="selectedNodeId">
                <i :class="getNodeIcon(selectedNodeData.type)"></i>
              </div>
              <div class="header-text">
                <h3>{{ selectedNodeId ? selectedNodeData.label || '节点属性' : '节点属性' }}</h3>
                <p v-if="selectedNodeId">{{ getNodeTypeLabel(selectedNodeData.type) }}</p>
              </div>
            </div>
            <button class="collapse-btn" @click="togglePropertyPanel">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>

          <div class="property-body">
            <div v-if="selectedNodeId" class="node-properties">
              <!-- 节点状态指示器 -->
              <div class="node-status">
                <div class="status-indicator" :class="getNodeStatus(selectedNodeData.type)">
                  <div class="status-dot"></div>
                  <span class="status-text">{{ getNodeStatusText(selectedNodeData.type) }}</span>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="property-section">
                <div class="section-header">
                  <div class="section-icon">
                    <i class="fas fa-info-circle"></i>
                  </div>
                  <h4>基本信息</h4>
                </div>
                <div class="section-content">
                  <div class="form-group">
                    <label class="form-label">
                      <i class="fas fa-tag"></i>
                      节点名称
                    </label>
                    <input
                      type="text"
                      v-model="selectedNodeData.label"
                      class="form-input"
                      placeholder="请输入节点名称"
                    >
                  </div>
                  <div class="form-group">
                    <label class="form-label">
                      <i class="fas fa-cube"></i>
                      节点类型
                    </label>
                    <div class="readonly-field">
                      <span class="type-badge" :class="getNodeTypeClass(selectedNodeData.type)">
                        {{ getNodeTypeLabel(selectedNodeData.type) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 配置参数 -->
              <div class="property-section">
                <div class="section-header">
                  <div class="section-icon">
                    <i class="fas fa-cogs"></i>
                  </div>
                  <h4>配置参数</h4>
                </div>
                <div class="section-content">
                  <div v-for="(value, key) in selectedNodeData.config" :key="key" class="form-group">
                    <label class="form-label">
                      <i :class="getConfigIcon(key)"></i>
                      {{ getConfigLabel(key) }}
                    </label>
                    <input
                      v-if="getConfigType(key) === 'text'"
                      type="text"
                      v-model="selectedNodeData.config[key]"
                      class="form-input"
                      :placeholder="getConfigPlaceholder(key)"
                    >
                    <input
                      v-else-if="getConfigType(key) === 'number'"
                      type="number"
                      v-model.number="selectedNodeData.config[key]"
                      class="form-input"
                      :placeholder="getConfigPlaceholder(key)"
                    >
                    <input
                      v-else-if="getConfigType(key) === 'password'"
                      type="password"
                      v-model="selectedNodeData.config[key]"
                      class="form-input"
                      :placeholder="getConfigPlaceholder(key)"
                    >
                    <textarea
                      v-else-if="getConfigType(key) === 'textarea'"
                      v-model="selectedNodeData.config[key]"
                      class="form-textarea"
                      rows="3"
                      :placeholder="getConfigPlaceholder(key)"
                    ></textarea>
                    <select
                      v-else-if="getConfigType(key) === 'select'"
                      v-model="selectedNodeData.config[key]"
                      class="form-select"
                    >
                      <option v-for="option in getConfigOptions(key)" :key="option.value" :value="option.value">
                        {{ option.label }}
                      </option>
                    </select>
                    <input
                      v-else
                      type="text"
                      v-model="selectedNodeData.config[key]"
                      class="form-input"
                      :placeholder="getConfigPlaceholder(key)"
                    >
                    <div v-if="getConfigHelp(key)" class="form-help">
                      <i class="fas fa-info-circle"></i>
                      <span>{{ getConfigHelp(key) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="property-actions">
                <button class="btn btn-outline btn-sm" @click="resetNodeConfig">
                  <i class="fas fa-undo"></i>
                  重置
                </button>
                <button class="btn btn-primary btn-sm" @click="saveNodeConfig">
                  <i class="fas fa-save"></i>
                  保存
                </button>
              </div>
            </div>

            <div v-else class="no-selection">
              <div class="no-selection-content">
                <div class="no-selection-icon">
                  <i class="fas fa-mouse-pointer"></i>
                </div>
                <h4>选择节点</h4>
                <p>点击画布上的节点查看和编辑属性</p>
                <div class="selection-tips">
                  <div class="tip-item">
                    <i class="fas fa-click"></i>
                    <span>单击选择节点</span>
                  </div>
                  <div class="tip-item">
                    <i class="fas fa-edit"></i>
                    <span>编辑节点配置</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="collapsed-panel">
          <button
            class="expand-btn"
            @click="togglePropertyPanel"
            title="展开属性面板"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 配置面板 -->
    <div
      class="config-panel"
      :class="{ visible: showConfigPanel }"
      :style="{ width: showConfigPanel ? `${configPanelWidth}px` : '0px' }"
    >
      <div class="config-overlay" @click="toggleConfigPanel" v-if="showConfigPanel"></div>
      <div class="config-content" v-if="showConfigPanel">
        <WorkflowConfigPage
          :datasetId="datasetId"
          @close="toggleConfigPanel"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { Node, Edge, Connection } from '@vue-flow/core'
import FlowCanvas from './flow/components/FlowCanvas.vue'
import NodeLibrary from './flow/components/NodeLibrary.vue'
import WorkflowConfigPage from './components/WorkflowConfigPage.vue'
import { generateId } from '@/utils/common'

// 视口配置类型
interface ViewportConfig {
  x: number
  y: number
  zoom: number
}

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const datasetId = computed(() => route.params.id as string)
const knowledgeBaseName = ref('知识库')
const showNodeLibrary = ref(true)
const showPropertyPanel = ref(false)
const showConfigPanel = ref(false)
const selectedNodeId = ref<string | null>(null)
const leftPanelWidth = ref(300)
const rightPanelWidth = ref(300)
const configPanelWidth = ref(400)
const viewport = ref<ViewportConfig>({ x: 0, y: 0, zoom: 1 })
const flowInstance = ref<any>(null)

// 流程数据
const nodes = ref<Node[]>([])
const edges = ref<Edge[]>([])

// 选中节点数据
const selectedNodeData = ref<any>({
  label: '',
  type: '',
  config: {}
})

// 拖拽调整相关
const isResizing = ref(false)
const resizeType = ref<'left' | 'right' | null>(null)

// 计算属性
const hasNodes = computed(() => nodes.value.length > 0)
const isFlowValid = computed(() => {
  // 简单验证：至少有一个数据源节点和一个输出节点
  const hasDataSource = nodes.value.some(node =>
    ['ftp', 'mysql', 'elasticsearch', 'minio', 'postgresql', 'oracle', 'local-file'].includes(node.type || '')
  )
  const hasOutput = nodes.value.some(node =>
    ['weaviate', 'pinecone', 'milvus', 'chroma', 'qdrant'].includes(node.type || '')
  )
  return hasDataSource && hasOutput
})

const toggleNodeLibrary = () => {
  showNodeLibrary.value = !showNodeLibrary.value
}

const togglePropertyPanel = () => {
  showPropertyPanel.value = !showPropertyPanel.value
}

const toggleConfigPanel = () => {
  showConfigPanel.value = !showConfigPanel.value
}

// 节点点击事件处理
const onNodeClick = (event: any) => {
  const nodeId = event.node?.id
  if (nodeId) {
    selectedNodeId.value = nodeId
    const node = nodes.value.find(n => n.id === nodeId)
    if (node) {
      selectedNodeData.value = {
        label: node.data?.label || node.label || '',
        type: node.type || '',
        config: { ...node.data?.config || {} }
      }
      showPropertyPanel.value = true
    }
  }
}

// 面板拖拽调整
const startResize = (type: 'left' | 'right') => {
  isResizing.value = true
  resizeType.value = type
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

const handleResize = (event: MouseEvent) => {
  if (!isResizing.value || !resizeType.value) return

  const containerRect = document.querySelector('.editor-container')?.getBoundingClientRect()
  if (!containerRect) return

  if (resizeType.value === 'left') {
    // 调整左侧面板宽度
    const newWidth = event.clientX - containerRect.left
    leftPanelWidth.value = Math.max(200, Math.min(500, newWidth))
  } else if (resizeType.value === 'right') {
    // 调整右侧面板宽度
    const newWidth = containerRect.right - event.clientX
    rightPanelWidth.value = Math.max(200, Math.min(500, newWidth))
  }
}

const stopResize = () => {
  isResizing.value = false
  resizeType.value = null
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// 节点属性面板辅助方法
const getNodeIcon = (nodeType: string) => {
  const iconMap: Record<string, string> = {
    'ftp': 'fas fa-server',
    'mysql': 'fas fa-database',
    'postgresql': 'fas fa-elephant',
    'oracle': 'fas fa-database',
    'elasticsearch': 'fas fa-search',
    'minio': 'fas fa-cloud',
    'local-file': 'fas fa-upload',
    'segment': 'fas fa-cut',
    'vectorize': 'fas fa-vector-square',
    'weaviate': 'fas fa-database',
    'pinecone': 'fas fa-tree',
    'milvus': 'fas fa-cubes',
    'chroma': 'fas fa-palette',
    'qdrant': 'fas fa-dot-circle'
  }
  return iconMap[nodeType] || 'fas fa-cube'
}

const getNodeTypeLabel = (nodeType: string) => {
  const labelMap: Record<string, string> = {
    'ftp': 'FTP数据源',
    'mysql': 'MySQL数据库',
    'postgresql': 'PostgreSQL数据库',
    'oracle': 'Oracle数据库',
    'elasticsearch': 'Elasticsearch',
    'minio': 'MinIO对象存储',
    'local-file': '本地文件上传',
    'segment': '文档分段',
    'vectorize': '向量化',
    'weaviate': 'Weaviate向量库',
    'pinecone': 'Pinecone向量库',
    'milvus': 'Milvus向量库',
    'chroma': 'ChromaDB向量库',
    'qdrant': 'Qdrant向量库'
  }
  return labelMap[nodeType] || nodeType
}

const getNodeTypeClass = (nodeType: string) => {
  if (['ftp', 'mysql', 'postgresql', 'oracle', 'elasticsearch', 'minio', 'local-file'].includes(nodeType)) {
    return 'datasource'
  } else if (['segment', 'vectorize'].includes(nodeType)) {
    return 'processing'
  } else if (['weaviate', 'pinecone', 'milvus', 'chroma', 'qdrant'].includes(nodeType)) {
    return 'output'
  }
  return 'default'
}

const getNodeStatus = (nodeType: string) => {
  // 模拟节点状态
  return 'ready' // ready, running, error, success
}

const getNodeStatusText = (nodeType: string) => {
  const status = getNodeStatus(nodeType)
  const statusMap: Record<string, string> = {
    'ready': '就绪',
    'running': '运行中',
    'error': '错误',
    'success': '成功'
  }
  return statusMap[status] || '未知'
}

const getConfigIcon = (key: string) => {
  const iconMap: Record<string, string> = {
    'host': 'fas fa-server',
    'port': 'fas fa-plug',
    'username': 'fas fa-user',
    'password': 'fas fa-lock',
    'database': 'fas fa-database',
    'query': 'fas fa-code',
    'endpoint': 'fas fa-link',
    'accessKey': 'fas fa-key',
    'secretKey': 'fas fa-key',
    'bucket': 'fas fa-folder',
    'chunkSize': 'fas fa-ruler',
    'overlapSize': 'fas fa-arrows-alt-h',
    'model': 'fas fa-brain',
    'apiKey': 'fas fa-key'
  }
  return iconMap[key] || 'fas fa-cog'
}

const getConfigLabel = (key: string) => {
  const labelMap: Record<string, string> = {
    'host': '主机地址',
    'port': '端口号',
    'username': '用户名',
    'password': '密码',
    'database': '数据库名',
    'query': 'SQL查询',
    'endpoint': '端点地址',
    'accessKey': '访问密钥',
    'secretKey': '秘密密钥',
    'bucket': '存储桶',
    'chunkSize': '分段大小',
    'overlapSize': '重叠大小',
    'model': '模型名称',
    'apiKey': 'API密钥'
  }
  return labelMap[key] || key
}

const getConfigType = (key: string) => {
  const typeMap: Record<string, string> = {
    'password': 'password',
    'secretKey': 'password',
    'apiKey': 'password',
    'port': 'number',
    'chunkSize': 'number',
    'overlapSize': 'number',
    'query': 'textarea'
  }
  return typeMap[key] || 'text'
}

const getConfigPlaceholder = (key: string) => {
  const placeholderMap: Record<string, string> = {
    'host': '例如：localhost',
    'port': '例如：3306',
    'username': '请输入用户名',
    'password': '请输入密码',
    'database': '请输入数据库名',
    'query': 'SELECT * FROM table_name',
    'endpoint': 'https://example.com',
    'accessKey': '请输入访问密钥',
    'secretKey': '请输入秘密密钥',
    'bucket': '请输入存储桶名称',
    'chunkSize': '1000',
    'overlapSize': '200',
    'model': 'text-embedding-ada-002',
    'apiKey': '请输入API密钥'
  }
  return placeholderMap[key] || `请输入${getConfigLabel(key)}`
}

const getConfigHelp = (key: string) => {
  const helpMap: Record<string, string> = {
    'query': 'SQL查询语句，用于从数据库获取数据',
    'chunkSize': '文档分段的最大字符数',
    'overlapSize': '相邻分段之间的重叠字符数',
    'model': '用于向量化的嵌入模型'
  }
  return helpMap[key] || ''
}

const getConfigOptions = (key: string) => {
  const optionsMap: Record<string, Array<{value: string, label: string}>> = {
    'model': [
      { value: 'text-embedding-ada-002', label: 'OpenAI Ada-002' },
      { value: 'text-embedding-3-small', label: 'OpenAI Embedding-3-Small' },
      { value: 'text-embedding-3-large', label: 'OpenAI Embedding-3-Large' }
    ]
  }
  return optionsMap[key] || []
}

const resetNodeConfig = () => {
  if (selectedNodeId.value) {
    // 重置为默认配置
    const node = nodes.value.find(n => n.id === selectedNodeId.value)
    if (node && node.data?.config) {
      // 这里可以从节点库配置中获取默认值
      console.log('重置节点配置')
    }
  }
}

const saveNodeConfig = () => {
  if (selectedNodeId.value) {
    // 保存节点配置
    const node = nodes.value.find(n => n.id === selectedNodeId.value)
    if (node) {
      node.data = {
        ...node.data,
        label: selectedNodeData.value.label,
        config: { ...selectedNodeData.value.config }
      }
      console.log('保存节点配置:', node.data)
    }
  }
}

const zoomIn = () => {
  if (flowInstance.value) {
    flowInstance.value.zoomIn()
  }
}

const zoomOut = () => {
  if (flowInstance.value) {
    flowInstance.value.zoomOut()
  }
}

const fitView = () => {
  if (flowInstance.value) {
    flowInstance.value.fitView()
  }
}

const validateFlow = () => {
  if (isFlowValid.value) {
    alert('流程验证通过！')
  } else {
    alert('流程验证失败：请确保至少包含一个数据源节点和一个输出节点')
  }
}

const clearCanvas = () => {
  if (confirm('确定要清除所有节点吗？')) {
    nodes.value = []
    edges.value = []
  }
}

const runFlow = () => {
  if (!isFlowValid.value) {
    alert('请先完善流程配置')
    return
  }
  alert('开始运行数据源流程...')
  // 这里可以实现实际的流程执行逻辑
}

// 流程事件处理 - 新版本Vue Flow使用v-model直接绑定，不需要手动处理changes

const onConnect = (connection: Connection) => {
  const edge: Edge = {
    id: generateId(),
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle,
    targetHandle: connection.targetHandle,
    type: 'default'
  }
  edges.value.push(edge)
}

const onPaneReady = (instance: any) => {
  flowInstance.value = instance
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()

  if (!event.dataTransfer) return

  const nodeData = event.dataTransfer.getData('application/vueflow')
  if (!nodeData) return

  try {
    const nodeConfig = JSON.parse(nodeData)
    const position = flowInstance.value?.screenToFlowCoordinate({
      x: event.clientX,
      y: event.clientY
    }) || { x: 0, y: 0 }

    const newNode: Node = {
      id: generateId(),
      type: nodeConfig.type,
      position,
      data: {
        ...nodeConfig.defaultData,
        type: nodeConfig.type
      }
    }

    nodes.value.push(newNode)
  } catch (error) {
    console.error('Failed to parse node data:', error)
  }
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

// 初始化默认流程
const initializeDefaultFlow = () => {
  // 创建默认的FTP -> 分段 -> 向量化 -> Weaviate流程
  const defaultNodes: Node[] = [
    {
      id: 'ftp-1',
      type: 'ftp',
      position: { x: 100, y: 200 },
      data: {
        type: 'ftp',
        label: 'FTP数据源',
        config: {
          host: '',
          port: 21,
          username: '',
          password: '',
          path: '/',
          filePattern: '*.*'
        },
        status: 'idle'
      }
    },
    {
      id: 'segment-1',
      type: 'segment',
      position: { x: 400, y: 200 },
      data: {
        type: 'segment',
        label: '文档分段',
        config: {
          chunkSize: 1000,
          chunkOverlap: 200,
          separator: '\n\n',
          keepSeparator: true
        },
        status: 'idle'
      }
    },
    {
      id: 'vectorize-1',
      type: 'vectorize',
      position: { x: 700, y: 200 },
      data: {
        type: 'vectorize',
        label: '向量化',
        config: {
          model: 'text-embedding-ada-002',
          batchSize: 100,
          dimensions: 1536
        },
        status: 'idle'
      }
    },
    {
      id: 'weaviate-1',
      type: 'weaviate',
      position: { x: 1000, y: 200 },
      data: {
        type: 'weaviate',
        label: 'Weaviate向量库',
        config: {
          host: '',
          port: 8080,
          className: '',
          apiKey: ''
        },
        status: 'idle',
        connectionStatus: 'disconnected'
      }
    }
  ]

  const defaultEdges: Edge[] = [
    {
      id: 'ftp-segment',
      source: 'ftp-1',
      target: 'segment-1',
      sourceHandle: 'output',
      targetHandle: 'input'
    },
    {
      id: 'segment-vectorize',
      source: 'segment-1',
      target: 'vectorize-1',
      sourceHandle: 'output',
      targetHandle: 'input'
    },
    {
      id: 'vectorize-weaviate',
      source: 'vectorize-1',
      target: 'weaviate-1',
      sourceHandle: 'output',
      targetHandle: 'input'
    }
  ]

  nodes.value = defaultNodes
  edges.value = defaultEdges

  // 延迟适应视图
  nextTick(() => {
    setTimeout(() => {
      if (flowInstance.value) {
        flowInstance.value.fitView({ padding: 0.2 })
      }
    }, 100)
  })
}

// 生命周期
onMounted(() => {
  // 初始化默认流程
  initializeDefaultFlow()

  // 可以在这里加载知识库信息
  // loadKnowledgeBaseInfo()
})
</script>

<style scoped>
.datasource-flow-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  position: relative;
}

.toolbar {
  background: white;
  height: 60px;
  padding: 0 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  gap: 16px;
  z-index: 10;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  color: #64748b;
  font-size: 14px;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.flow-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.flow-info i {
  color: #3b82f6;
  font-size: 18px;
}

.flow-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.version {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.toolbar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 16px;
}

.panel-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.canvas-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.zoom-level {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  height: 36px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-icon {
  padding: 8px;
  width: 36px;
  height: 36px;
  justify-content: center;
}

.btn-secondary {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background: #f1f5f9;
  color: #334155;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.editor-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.node-library-panel {
  background: white;
  border-right: 1px solid #e2e8f0;
  transition: width 0.3s ease;
  overflow-y: auto;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

.node-library-panel.collapsed {
  width: 50px !important;
}

.collapsed-panel {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
}

.expand-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 属性面板样式 */
.property-panel {
  background: white;
  border-left: 1px solid #e2e8f0;
  transition: width 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
}

.property-panel.collapsed {
  border-left: none;
}

.property-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.property-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.header-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.header-text h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 2px 0;
}

.header-text p {
  font-size: 12px;
  color: #64748b;
  margin: 0;
}

.property-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.node-properties {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 节点状态指示器 */
.node-status {
  margin-bottom: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.ready {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-indicator.running {
  background: #eff6ff;
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
}

.status-indicator.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-indicator.success {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.property-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.section-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.section-header h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.section-content {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-label i {
  font-size: 12px;
  color: #64748b;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1e293b;
  background: white;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:readonly {
  background: #f8fafc;
  color: #64748b;
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-help {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 6px;
  font-size: 12px;
  color: #64748b;
}

.form-help i {
  font-size: 11px;
}

.readonly-field {
  padding: 10px 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.type-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge.datasource {
  background: #eff6ff;
  color: #1d4ed8;
}

.type-badge.processing {
  background: #f0fdf4;
  color: #166534;
}

.type-badge.output {
  background: #fef3c7;
  color: #92400e;
}

.type-badge.default {
  background: #f3f4f6;
  color: #6b7280;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.no-selection-content {
  text-align: center;
  max-width: 280px;
}

.no-selection-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #94a3b8;
  margin: 0 auto 20px;
}

.no-selection-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.no-selection-content p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.selection-tips {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 13px;
  color: #64748b;
}

.tip-item i {
  width: 16px;
  font-size: 12px;
  color: #94a3b8;
}

/* 属性面板操作按钮 */
.property-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 13px;
}

/* 拖拽调整手柄样式 */
.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
  transition: background-color 0.2s ease;
  z-index: 10;
}

.resize-handle:hover {
  background: #3b82f6;
}

.resize-handle-right {
  right: 0;
}

.resize-handle-left {
  left: 0;
}

/* 配置面板样式 */
.config-panel {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
  transition: width 0.3s ease;
  overflow: hidden;
}

.config-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.config-content {
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  z-index: 1002;
}
</style>