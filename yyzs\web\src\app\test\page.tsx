'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Play } from 'lucide-react';
import InstallProgress from '@/components/InstallProgress';
import FileUpload from '@/components/FileUpload';
import ComponentConfigEditor from '@/components/ComponentConfigEditor';
import { ComponentType } from '@/types/component';

export default function TestPage() {
  const router = useRouter();
  const [progress, setProgress] = useState(0);
  const [isInstalling, setIsInstalling] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [config, setConfig] = useState({});

  const startInstallTest = () => {
    setIsInstalling(true);
    setProgress(0);
    
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsInstalling(false);
          return 100;
        }
        return prev + Math.random() * 10;
      });
    }, 500);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="btn-outline mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">组件测试页面</h1>
                <p className="text-sm text-gray-500">测试各种组件功能</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        
        {/* 文件上传测试 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">文件上传测试</h3>
          </div>
          <div className="card-body">
            <FileUpload
              onFileSelect={(file) => {
                setSelectedFile(file);
                console.log('Selected file:', file);
              }}
            />
            {selectedFile && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                <p className="text-sm text-green-800">
                  已选择文件: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </p>
              </div>
            )}
          </div>
        </div>

        {/* 配置编辑器测试 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">配置编辑器测试</h3>
          </div>
          <div className="card-body">
            <ComponentConfigEditor
              componentType="elasticsearch" as ComponentType
              config={config}
              onChange={(newConfig) => {
                setConfig(newConfig);
                console.log('Config changed:', newConfig);
              }}
            />
          </div>
        </div>

        {/* 安装进度测试 */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">安装进度测试</h3>
              <button
                onClick={startInstallTest}
                disabled={isInstalling}
                className="btn-primary"
              >
                <Play className="h-4 w-4 mr-2" />
                {isInstalling ? '安装中...' : '开始测试安装'}
              </button>
            </div>
          </div>
          <div className="card-body">
            {isInstalling || progress > 0 ? (
              <InstallProgress
                componentName="Elasticsearch"
                progress={progress}
                onComplete={() => {
                  console.log('Installation completed!');
                }}
                onError={(error) => {
                  console.error('Installation error:', error);
                }}
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                点击"开始测试安装"按钮来测试安装进度组件
              </div>
            )}
          </div>
        </div>

        {/* 样式测试 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">样式测试</h3>
          </div>
          <div className="card-body space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="btn-primary">主要按钮</button>
              <button className="btn-secondary">次要按钮</button>
              <button className="btn-outline">轮廓按钮</button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="btn-success">成功按钮</button>
              <button className="btn-warning">警告按钮</button>
              <button className="btn-error">错误按钮</button>
            </div>

            <div className="space-y-2">
              <span className="status-badge text-success-600 bg-success-50">运行中</span>
              <span className="status-badge text-warning-600 bg-warning-50">安装中</span>
              <span className="status-badge text-error-600 bg-error-50">错误</span>
              <span className="status-badge text-secondary-600 bg-secondary-50">已停止</span>
            </div>

            <div className="space-y-4">
              <div>
                <label className="form-label">测试输入框</label>
                <input type="text" className="form-input" placeholder="请输入内容..." />
              </div>
              
              <div>
                <label className="form-label">测试文本域</label>
                <textarea className="form-input" rows={3} placeholder="请输入多行内容..."></textarea>
              </div>
              
              <div>
                <label className="form-label">测试选择框</label>
                <select className="form-input">
                  <option>选项 1</option>
                  <option>选项 2</option>
                  <option>选项 3</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
