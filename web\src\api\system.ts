/**
 * 系统管理API接口
 */

import { apiClient } from '@/utils/apiClient'
import type { ApiResponse, PaginatedResponse } from '@/types/api'
import type {
  SysUserQueryDTO,
  SysUserVO,
  SysUser,
  SysDeptQueryDTO,
  SysDeptVO,
  SysDept,
  SysTenantQueryDTO,
  SysTenantVO,
  SysTenant,
  SysRoleQueryDTO,
  SysRoleVO,
  SysRole,
  SysPermissionQueryDTO,
  SysPermissionVO,
  SysPermission,
  SysConfigQueryDTO,
  SysConfigDTO,
  SysConfigVO,
  SysConfig,
  SystemSettingsDTO,
  PageResult,
  ResetPasswordRequest,
  UserAuthRequest
} from '@/types/system'

/**
 * 用户管理API
 */
export class UserAPI {
  /**
   * 分页查询用户列表
   */
  static async getUserPage(queryDTO: SysUserQueryDTO): Promise<ApiResponse<PageResult<SysUserVO>>> {
    return apiClient.get('/api/system/user/page', queryDTO)
  }

  /**
   * 根据ID查询用户详情
   */
  static async getUserById(id: string): Promise<ApiResponse<SysUser>> {
    return apiClient.get(`/api/system/user/${id}`)
  }

  /**
   * 新增用户
   */
  static async addUser(user: SysUser): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/user', user)
  }

  /**
   * 修改用户
   */
  static async updateUser(user: SysUser): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/user', user)
  }

  /**
   * 删除用户
   */
  static async deleteUsers(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/user/${ids.join(',')}`)
  }

  /**
   * 重置用户密码
   */
  static async resetPassword(request: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/user/resetPassword', request)
  }

  /**
   * 用户授权
   */
  static async authorizeUser(request: UserAuthRequest): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/user/authorize', request)
  }

  /**
   * 检查用户名是否存在
   */
  static async checkUsername(username: string, excludeId?: string): Promise<ApiResponse<boolean>> {
    const params: any = { username }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return apiClient.get('/api/system/user/check-username', params)
  }

  /**
   * 批量更新用户状态
   */
  static async batchUpdateStatus(userIds: string[], status: string): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/user/batch-status', userIds, {
      params: { status }
    })
  }

  /**
   * 导出用户数据
   */
  static async exportUsers(queryDTO: SysUserQueryDTO): Promise<ApiResponse<SysUserVO[]>> {
    return apiClient.post('/api/system/user/export', queryDTO)
  }

  /**
   * 导入用户数据
   */
  static async importUsers(users: SysUser[]): Promise<ApiResponse<string>> {
    return apiClient.post('/api/system/user/import', users)
  }
}

/**
 * 角色管理API
 */
export class RoleAPI {
  /**
   * 分页查询角色列表
   */
  static async getRolePage(queryDTO: SysRoleQueryDTO): Promise<ApiResponse<PageResult<SysRoleVO>>> {
    return apiClient.get('/api/system/role/page', queryDTO)
  }

  /**
   * 查询角色列表
   */
  static async getRoleList(queryDTO: SysRoleQueryDTO): Promise<ApiResponse<SysRoleVO[]>> {
    return apiClient.get('/api/system/role/list', queryDTO)
  }

  /**
   * 根据ID查询角色详情
   */
  static async getRoleById(id: string): Promise<ApiResponse<SysRole>> {
    return apiClient.get(`/api/system/role/${id}`)
  }

  /**
   * 新增角色
   */
  static async addRole(role: SysRole): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/role', role)
  }

  /**
   * 修改角色
   */
  static async updateRole(role: SysRole): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/role', role)
  }

  /**
   * 删除角色
   */
  static async deleteRoles(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/role/${ids.join(',')}`)
  }

  /**
   * 为角色分配权限
   */
  static async assignPermissions(roleId: string, permissionIds: string[]): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/system/role/${roleId}/permissions`, permissionIds)
  }

  /**
   * 查询角色已分配的权限ID列表
   */
  static async getRolePermissionIds(roleId: string): Promise<ApiResponse<string[]>> {
    return apiClient.get(`/api/system/role/${roleId}/permissions`)
  }

  /**
   * 根据用户ID查询角色列表
   */
  static async getRolesByUserId(userId: string): Promise<ApiResponse<SysRoleVO[]>> {
    return apiClient.get(`/api/system/role/user/${userId}`)
  }

  /**
   * 检查角色编码是否存在
   */
  static async checkRoleCode(roleCode: string, excludeId?: string): Promise<ApiResponse<boolean>> {
    const params: any = { roleCode }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return apiClient.get('/api/system/role/check-code', params)
  }

  /**
   * 批量更新角色状态
   */
  static async batchUpdateStatus(roleIds: string[], status: string): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/role/batch-status', roleIds, {
      params: { status }
    })
  }

  /**
   * 导出角色数据
   */
  static async exportRoles(queryDTO: SysRoleQueryDTO): Promise<ApiResponse<SysRoleVO[]>> {
    return apiClient.post('/api/system/role/export', queryDTO)
  }

  /**
   * 导入角色数据
   */
  static async importRoles(roles: SysRole[]): Promise<ApiResponse<string>> {
    return apiClient.post('/api/system/role/import', roles)
  }

  /**
   * 获取角色选择列表（用于下拉选择）
   */
  static async getRoleSelectList(): Promise<ApiResponse<SysRoleVO[]>> {
    return this.getRoleList({ status: '0' })
  }
}

/**
 * 权限管理API
 */
export class PermissionAPI {
  /**
   * 分页查询权限列表
   */
  static async getPermissionPage(queryDTO: SysPermissionQueryDTO): Promise<ApiResponse<PageResult<SysPermissionVO>>> {
    return apiClient.get('/api/system/permission/page', queryDTO)
  }

  /**
   * 查询权限列表
   */
  static async getPermissionList(queryDTO: SysPermissionQueryDTO): Promise<ApiResponse<SysPermissionVO[]>> {
    return apiClient.get('/api/system/permission/list', queryDTO)
  }

  /**
   * 查询权限树
   */
  static async getPermissionTree(queryDTO: SysPermissionQueryDTO): Promise<ApiResponse<SysPermissionVO[]>> {
    return apiClient.get('/api/system/permission/tree', queryDTO)
  }

  /**
   * 查询权限选择树
   */
  static async getPermissionSelectTree(excludePermissionId?: string): Promise<ApiResponse<SysPermissionVO[]>> {
    const params = excludePermissionId ? { excludePermissionId } : {}
    return apiClient.get('/api/system/permission/select-tree', params)
  }

  /**
   * 根据ID查询权限详情
   */
  static async getPermissionById(id: string): Promise<ApiResponse<SysPermission>> {
    return apiClient.get(`/api/system/permission/${id}`)
  }

  /**
   * 新增权限
   */
  static async addPermission(permission: SysPermission): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/permission', permission)
  }

  /**
   * 修改权限
   */
  static async updatePermission(permission: SysPermission): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/permission', permission)
  }

  /**
   * 删除权限
   */
  static async deletePermissions(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/permission/${ids.join(',')}`)
  }

  /**
   * 根据用户ID查询权限列表
   */
  static async getPermissionsByUserId(userId: string): Promise<ApiResponse<SysPermissionVO[]>> {
    return apiClient.get(`/api/system/permission/user/${userId}`)
  }

  /**
   * 根据角色ID查询权限列表
   */
  static async getPermissionsByRoleId(roleId: string): Promise<ApiResponse<SysPermissionVO[]>> {
    return apiClient.get(`/api/system/permission/role/${roleId}`)
  }

  /**
   * 检查权限编码是否存在
   */
  static async checkPermissionCode(permissionCode: string, excludeId?: string): Promise<ApiResponse<boolean>> {
    const params: any = { permissionCode }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return apiClient.get('/api/system/permission/check-code', params)
  }

  /**
   * 批量更新权限状态
   */
  static async batchUpdateStatus(permissionIds: string[], status: string): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/permission/batch-status', permissionIds, {
      params: { status }
    })
  }

  /**
   * 同步权限排序
   */
  static async syncOrder(permissions: SysPermission[]): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/permission/sync-order', permissions)
  }

  /**
   * 获取用户菜单权限树
   */
  static async getUserMenuTree(): Promise<ApiResponse<SysPermissionVO[]>> {
    return apiClient.get('/api/system/permission/user-menu-tree')
  }

  /**
   * 获取用户按钮权限列表
   */
  static async getUserButtonPermissions(): Promise<ApiResponse<string[]>> {
    return apiClient.get('/api/system/permission/user-button-permissions')
  }
}

/**
 * 租户管理API
 */
export class TenantAPI {
  /**
   * 分页查询租户列表
   */
  static async getTenantPage(queryDTO: SysTenantQueryDTO): Promise<ApiResponse<PageResult<SysTenantVO>>> {
    return apiClient.get('/api/system/tenant/page', queryDTO)
  }

  /**
   * 根据ID查询租户详情
   */
  static async getTenantById(id: string): Promise<ApiResponse<SysTenantVO>> {
    return apiClient.get(`/api/system/tenant/${id}`)
  }

  /**
   * 创建租户
   */
  static async createTenant(tenant: SysTenant): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/tenant', tenant)
  }

  /**
   * 更新租户信息
   */
  static async updateTenant(tenant: SysTenant): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/tenant', tenant)
  }

  /**
   * 删除租户
   */
  static async deleteTenants(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete('/api/system/tenant', { data: ids })
  }

  /**
   * 启用租户
   */
  static async enableTenant(tenantId: string): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/system/tenant/${tenantId}/enable`)
  }

  /**
   * 禁用租户
   */
  static async disableTenant(tenantId: string): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/system/tenant/${tenantId}/disable`)
  }

  /**
   * 获取租户统计信息
   */
  static async getTenantStatistics(tenantId: string): Promise<ApiResponse<SysTenantVO>> {
    return apiClient.get(`/api/system/tenant/${tenantId}/statistics`)
  }

  /**
   * 初始化租户数据
   */
  static async initializeTenantData(params: {
    tenantId: string
    adminUsername: string
    adminPassword: string
    adminEmail: string
  }): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/tenant/initialize', params)
  }
}

/**
 * 部门管理API
 */
export class DeptAPI {
  /**
   * 查询部门列表
   */
  static async getDeptList(queryDTO: SysDeptQueryDTO): Promise<ApiResponse<SysDeptVO[]>> {
    return apiClient.get('/api/system/dept/list', queryDTO)
  }

  /**
   * 查询部门树
   */
  static async getDeptTree(queryDTO: SysDeptQueryDTO): Promise<ApiResponse<SysDeptVO[]>> {
    return apiClient.get('/api/system/dept/tree', queryDTO)
  }

  /**
   * 查询部门选择树（排除指定部门）
   */
  static async getDeptSelectTree(excludeDeptId?: string): Promise<ApiResponse<SysDeptVO[]>> {
    const params = excludeDeptId ? { excludeDeptId } : {}
    return apiClient.get('/api/system/dept/select-tree', params)
  }

  /**
   * 根据ID查询部门详情
   */
  static async getDeptById(id: string): Promise<ApiResponse<SysDept>> {
    return apiClient.get(`/api/system/dept/${id}`)
  }

  /**
   * 新增部门
   */
  static async addDept(dept: SysDept): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/dept', dept)
  }

  /**
   * 修改部门
   */
  static async updateDept(dept: SysDept): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/dept', dept)
  }

  /**
   * 删除部门
   */
  static async deleteDepts(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/dept/${ids.join(',')}`)
  }

  /**
   * 移动部门
   */
  static async moveDept(id: string, newParentId: string): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/system/dept/${id}/move`, null, {
      params: { newParentId }
    })
  }

  /**
   * 查询子部门
   */
  static async getChildren(id: string): Promise<ApiResponse<SysDeptVO[]>> {
    return apiClient.get(`/api/system/dept/${id}/children`)
  }

  /**
   * 获取部门用户数量
   */
  static async getUserCount(id: string): Promise<ApiResponse<number>> {
    return apiClient.get(`/api/system/dept/${id}/user-count`)
  }

  /**
   * 检查部门编码是否存在
   */
  static async checkDeptCode(deptCode: string, excludeId?: string): Promise<ApiResponse<boolean>> {
    const params: any = { deptCode }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return apiClient.get('/api/system/dept/check-code', params)
  }

  /**
   * 批量更新部门状态
   */
  static async batchUpdateStatus(deptIds: string[], status: string): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/dept/batch-status', deptIds, {
      params: { status }
    })
  }

  /**
   * 同步部门排序
   */
  static async syncOrder(depts: SysDept[]): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/dept/sync-order', depts)
  }
}

/**
 * 系统配置管理API
 */
export class ConfigAPI {
  /**
   * 分页查询配置列表
   */
  static async getConfigPage(queryDTO: SysConfigQueryDTO): Promise<ApiResponse<PageResult<SysConfigVO>>> {
    return apiClient.get('/api/system/config/page', queryDTO)
  }

  /**
   * 查询配置列表
   */
  static async getConfigList(queryDTO: SysConfigQueryDTO): Promise<ApiResponse<SysConfigVO[]>> {
    return apiClient.get('/api/system/config/list', queryDTO)
  }

  /**
   * 根据ID查询配置详情
   */
  static async getConfigById(id: string): Promise<ApiResponse<SysConfigVO>> {
    return apiClient.get(`/api/system/config/${id}`)
  }

  /**
   * 根据配置键查询配置值
   */
  static async getConfigByKey(configKey: string): Promise<ApiResponse<string>> {
    return apiClient.get(`/api/system/config/key/${configKey}`)
  }

  /**
   * 创建配置
   */
  static async createConfig(config: SysConfig): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/config', config)
  }

  /**
   * 更新配置
   */
  static async updateConfig(config: SysConfig): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/config', config)
  }

  /**
   * 删除配置
   */
  static async deleteConfigs(configIds: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete('/api/system/config', { data: configIds })
  }

  /**
   * 批量更新配置
   */
  static async batchUpdateConfigs(configs: SysConfigDTO[]): Promise<ApiResponse<void>> {
    return apiClient.put('/api/system/config/batch', configs)
  }

  /**
   * 获取系统设置
   */
  static async getSystemSettings(): Promise<ApiResponse<SystemSettingsDTO>> {
    const settingsKeys = ['system.logo', 'system.title', 'system.subtitle', 'system.description', 'system.language', 'system.timezone']
    const promises = settingsKeys.map(key => this.getConfigByKey(key))

    try {
      const results = await Promise.allSettled(promises)
      const settings: SystemSettingsDTO = {}

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          const key = settingsKeys[index].replace('system.', '')
          settings[key as keyof SystemSettingsDTO] = result.value.data
        }
      })

      return { success: true, code: 200, data: settings, message: '获取系统设置成功' }
    } catch (error) {
      return { success: false, code: 500, data: {} as SystemSettingsDTO, message: '获取系统设置失败' }
    }
  }

  /**
   * 保存系统设置
   */
  static async saveSystemSettings(settings: SystemSettingsDTO): Promise<ApiResponse<void>> {
    const configs: SysConfigDTO[] = []

    Object.entries(settings).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        configs.push({
          configKey: `system.${key}`,
          configValue: String(value),
          configName: this.getConfigName(key),
          configType: 'Y',
          configGroup: 'system'
        })
      }
    })

    return this.batchUpdateConfigs(configs)
  }

  /**
   * 获取配置名称
   */
  private static getConfigName(key: string): string {
    const nameMap: Record<string, string> = {
      logo: '系统Logo',
      title: '系统名称',
      subtitle: '系统副标题',
      description: '系统描述',
      language: '系统语言',
      timezone: '系统时区'
    }
    return nameMap[key] || key
  }

  /**
   * 检查配置键是否存在
   */
  static async checkConfigKey(configKey: string, excludeId?: string): Promise<ApiResponse<boolean>> {
    const params: any = { configKey }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return apiClient.get('/api/system/config/check-key', params)
  }

  /**
   * 刷新配置缓存
   */
  static async refreshCache(): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/config/refresh-cache')
  }
}

export default {
  UserAPI,
  DeptAPI,
  RoleAPI,
  PermissionAPI,
  TenantAPI,
  ConfigAPI
}
