<template>
  <div class="flowchart-renderer">
    <div class="flowchart-container">
      <div class="flowchart-header">
        <div class="flowchart-info">
          <el-icon class="flowchart-icon"><Share /></el-icon>
          <div class="flowchart-details">
            <div class="flowchart-title">{{ flowchartTitle }}</div>
            <div class="flowchart-type">{{ typeLabel }}</div>
          </div>
        </div>
        <div class="flowchart-actions">
          <el-button @click="exportFlowchart" size="small" type="primary" text>
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="toggleFullscreen" size="small" type="primary" text>
            <el-icon><FullScreen /></el-icon>
            全屏
          </el-button>
        </div>
      </div>

      <div class="flowchart-content" ref="flowchartContainer">
        <!-- Mermaid流程图 -->
        <div 
          v-if="data.type === 'mermaid'" 
          class="mermaid-container"
          ref="mermaidContainer"
        >
          <div class="mermaid" ref="mermaidElement">{{ mermaidData }}</div>
        </div>

        <!-- 自定义流程图 -->
        <div 
          v-else-if="data.type === 'custom'" 
          class="custom-flowchart"
          ref="customContainer"
        >
          <svg ref="svgElement" class="flowchart-svg"></svg>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-overlay">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>正在渲染流程图...</span>
        </div>

        <!-- 错误状态 -->
        <div v-if="hasError" class="error-overlay">
          <el-icon class="error-icon"><Warning /></el-icon>
          <span>流程图渲染失败</span>
          <p class="error-message">{{ errorMessage }}</p>
        </div>
      </div>

      <!-- 流程图说明 -->
      <div v-if="flowchartDescription" class="flowchart-description">
        <p>{{ flowchartDescription }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, defineProps, defineEmits, nextTick } from 'vue'
import { Share, Download, FullScreen, Loading, Warning } from '@element-plus/icons-vue'

// 接口定义
interface FlowchartData {
  type: 'mermaid' | 'drawio' | 'custom'
  data: string | object
  title?: string
  description?: string
}

// Props定义
const props = defineProps<{
  data: FlowchartData
}>()

// Emits定义
const emit = defineEmits<{
  export: [type: string]
  fullscreen: [isFullscreen: boolean]
}>()

// 响应式数据
const flowchartContainer = ref<HTMLElement>()
const mermaidContainer = ref<HTMLElement>()
const mermaidElement = ref<HTMLElement>()
const customContainer = ref<HTMLElement>()
const svgElement = ref<SVGElement>()

const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const isFullscreen = ref(false)

// 计算属性
const flowchartTitle = computed(() => {
  if (typeof props.data.data === 'object' && props.data.data && 'title' in props.data.data) {
    return (props.data.data as any).title
  }
  return props.data.title || '流程图'
})

const flowchartDescription = computed(() => {
  if (typeof props.data.data === 'object' && props.data.data && 'description' in props.data.data) {
    return (props.data.data as any).description
  }
  return props.data.description
})

const typeLabel = computed(() => {
  const labels = {
    mermaid: 'Mermaid流程图',
    drawio: 'Draw.io流程图',
    custom: '自定义流程图'
  }
  return labels[props.data.type] || '流程图'
})

const mermaidData = computed(() => {
  if (props.data.type === 'mermaid' && typeof props.data.data === 'string') {
    return props.data.data
  }
  return ''
})

// 渲染Mermaid流程图
const renderMermaid = async () => {
  if (!mermaidElement.value || !mermaidData.value) return
  
  try {
    isLoading.value = true
    hasError.value = false
    
    // 动态导入mermaid
    const mermaid = await import('mermaid')
    
    // 初始化mermaid
    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true
      }
    })
    
    // 清空容器
    mermaidElement.value.innerHTML = mermaidData.value
    
    // 渲染图表
    await mermaid.default.run({
      nodes: [mermaidElement.value]
    })
    
    isLoading.value = false
  } catch (error) {
    console.error('Mermaid渲染错误:', error)
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : '未知错误'
    isLoading.value = false
  }
}

// 渲染自定义流程图
const renderCustomFlowchart = () => {
  if (!svgElement.value || typeof props.data.data !== 'object') return
  
  try {
    isLoading.value = true
    hasError.value = false
    
    // 这里可以根据自定义数据格式渲染SVG
    // 示例：简单的节点连接图
    const data = props.data.data as any
    
    if (data.nodes && data.edges) {
      renderNodeEdgeGraph(data)
    }
    
    isLoading.value = false
  } catch (error) {
    console.error('自定义流程图渲染错误:', error)
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : '渲染失败'
    isLoading.value = false
  }
}

// 渲染节点边图
const renderNodeEdgeGraph = (data: any) => {
  if (!svgElement.value) return
  
  const svg = svgElement.value
  const width = 600
  const height = 400
  
  svg.setAttribute('width', width.toString())
  svg.setAttribute('height', height.toString())
  svg.setAttribute('viewBox', `0 0 ${width} ${height}`)
  
  // 清空SVG
  svg.innerHTML = ''
  
  // 创建定义区域
  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs')
  
  // 箭头标记
  const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker')
  marker.setAttribute('id', 'arrowhead')
  marker.setAttribute('markerWidth', '10')
  marker.setAttribute('markerHeight', '7')
  marker.setAttribute('refX', '9')
  marker.setAttribute('refY', '3.5')
  marker.setAttribute('orient', 'auto')
  
  const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon')
  polygon.setAttribute('points', '0 0, 10 3.5, 0 7')
  polygon.setAttribute('fill', '#374151')
  
  marker.appendChild(polygon)
  defs.appendChild(marker)
  svg.appendChild(defs)
  
  // 计算节点位置
  const nodePositions = new Map()
  const nodeCount = data.nodes.length
  const cols = Math.ceil(Math.sqrt(nodeCount))
  const rows = Math.ceil(nodeCount / cols)
  
  data.nodes.forEach((node: any, index: number) => {
    const col = index % cols
    const row = Math.floor(index / cols)
    const x = (col + 1) * (width / (cols + 1))
    const y = (row + 1) * (height / (rows + 1))
    nodePositions.set(node.id, { x, y })
  })
  
  // 绘制边
  data.edges.forEach((edge: any) => {
    const fromPos = nodePositions.get(edge.from)
    const toPos = nodePositions.get(edge.to)
    
    if (fromPos && toPos) {
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
      line.setAttribute('x1', fromPos.x.toString())
      line.setAttribute('y1', fromPos.y.toString())
      line.setAttribute('x2', toPos.x.toString())
      line.setAttribute('y2', toPos.y.toString())
      line.setAttribute('stroke', '#374151')
      line.setAttribute('stroke-width', '2')
      line.setAttribute('marker-end', 'url(#arrowhead)')
      svg.appendChild(line)
    }
  })
  
  // 绘制节点
  data.nodes.forEach((node: any) => {
    const pos = nodePositions.get(node.id)
    if (!pos) return
    
    // 节点圆形
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
    circle.setAttribute('cx', pos.x.toString())
    circle.setAttribute('cy', pos.y.toString())
    circle.setAttribute('r', '30')
    circle.setAttribute('fill', '#3b82f6')
    circle.setAttribute('stroke', '#1d4ed8')
    circle.setAttribute('stroke-width', '2')
    svg.appendChild(circle)
    
    // 节点文本
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text')
    text.setAttribute('x', pos.x.toString())
    text.setAttribute('y', (pos.y + 5).toString())
    text.setAttribute('text-anchor', 'middle')
    text.setAttribute('fill', 'white')
    text.setAttribute('font-size', '12')
    text.setAttribute('font-weight', 'bold')
    text.textContent = node.label || node.id
    svg.appendChild(text)
  })
}

// 导出流程图
const exportFlowchart = () => {
  if (props.data.type === 'mermaid' && mermaidElement.value) {
    const svg = mermaidElement.value.querySelector('svg')
    if (svg) {
      exportSVG(svg)
    }
  } else if (props.data.type === 'custom' && svgElement.value) {
    exportSVG(svgElement.value)
  }
  
  emit('export', 'svg')
}

// 导出SVG
const exportSVG = (svg: SVGElement) => {
  const serializer = new XMLSerializer()
  const svgString = serializer.serializeToString(svg)
  const blob = new Blob([svgString], { type: 'image/svg+xml' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.download = `${flowchartTitle.value}.svg`
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

// 全屏切换
const toggleFullscreen = () => {
  if (!flowchartContainer.value) return
  
  if (!isFullscreen.value) {
    if (flowchartContainer.value.requestFullscreen) {
      flowchartContainer.value.requestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 全屏状态监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  emit('fullscreen', isFullscreen.value)
}

// 渲染流程图
const renderFlowchart = () => {
  if (props.data.type === 'mermaid') {
    renderMermaid()
  } else if (props.data.type === 'custom') {
    renderCustomFlowchart()
  }
}

onMounted(() => {
  nextTick(() => {
    renderFlowchart()
  })
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.flowchart-renderer {
  width: 100%;
}

.flowchart-container {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.flowchart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.flowchart-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.flowchart-icon {
  color: #6b7280;
  font-size: 24px;
}

.flowchart-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.flowchart-title {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.flowchart-type {
  color: #6b7280;
  font-size: 12px;
}

.flowchart-actions {
  display: flex;
  gap: 8px;
}

.flowchart-content {
  position: relative;
  min-height: 400px;
  padding: 16px;
  overflow: auto;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mermaid {
  width: 100%;
  text-align: center;
}

.mermaid :deep(svg) {
  max-width: 100%;
  height: auto;
}

.custom-flowchart {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.flowchart-svg {
  max-width: 100%;
  height: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  gap: 12px;
}

.loading-icon,
.error-icon {
  font-size: 32px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.error-icon {
  color: #ef4444;
}

.error-message {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #9ca3af;
  text-align: center;
  max-width: 300px;
}

.flowchart-description {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.flowchart-description p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 全屏样式 */
.flowchart-container:fullscreen {
  border-radius: 0;
}

.flowchart-container:fullscreen .flowchart-content {
  min-height: calc(100vh - 120px);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .flowchart-content {
    min-height: 300px;
    padding: 12px;
  }

  .flowchart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .flowchart-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .mermaid :deep(svg) {
    width: 100%;
    height: auto;
  }
}
</style>
