package com.xhcai.common.datasource.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * PostgreSQL JSON 类型处理器 专门处理 PostgreSQL 的 JSON/JSONB 字段类型转换 支持
 * Map<String, Object> 和 List<String> 等 JSON 类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedTypes({Map.class, List.class})
@MappedJdbcTypes({JdbcType.OTHER})
public class PostgreSQLJsonTypeHandler extends BaseTypeHandler<Object> {

    private static final Logger log = LoggerFactory.getLogger(PostgreSQLJsonTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 创建 PGobject 来处理 PostgreSQL 的 JSON 类型
            PGobject jsonObject = new PGobject();
            jsonObject.setType("json");

            if (parameter == null) {
                jsonObject.setValue(null);
            } else {
                String jsonString = objectMapper.writeValueAsString(parameter);
                jsonObject.setValue(jsonString);
                log.debug("设置JSON参数: {}", jsonString);
            }

            ps.setObject(i, jsonObject);
        } catch (JsonProcessingException e) {
            log.error("JSON序列化失败", e);
            throw new SQLException("JSON序列化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonString = rs.getString(columnName);
        return parseJson(jsonString);
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonString = rs.getString(columnIndex);
        return parseJson(jsonString);
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonString = cs.getString(columnIndex);
        return parseJson(jsonString);
    }

    private Object parseJson(String jsonString) throws SQLException {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析为通用的 Object，让 Jackson 自动推断类型
            return objectMapper.readValue(jsonString, Object.class);
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化失败: {}", jsonString, e);
            throw new SQLException("JSON反序列化失败: " + e.getMessage(), e);
        }
    }
}
