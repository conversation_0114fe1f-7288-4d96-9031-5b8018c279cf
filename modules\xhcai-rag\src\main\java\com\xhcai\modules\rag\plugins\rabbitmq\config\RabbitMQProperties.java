package com.xhcai.modules.rag.plugins.rabbitmq.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.xhcai.modules.rag.plugins.rabbitmq.model.MessageType;

import lombok.Data;

/**
 * RabbitMQ配置属性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "xhcai.plugin.types.queue.config")
public class RabbitMQProperties {

    /**
     * 队列类型
     */
    private String type = "rabbitmq";

    /**
     * RabbitMQ主机地址
     */
    private String host;

    /**
     * RabbitMQ端口
     */
    private Integer port;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 虚拟主机
     */
    private String virtualHost;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectionTimeout = 30000;

    /**
     * 心跳间隔（秒）
     */
    private Integer requestedHeartbeat = 60;

    /**
     * 是否启用发布确认
     */
    private Boolean publisherConfirms = true;

    /**
     * 是否启用发布返回
     */
    private Boolean publisherReturns = true;

    /**
     * 是否启用强制路由
     */
    private Boolean mandatory = true;

    /**
     * 消费者确认模式
     */
    private String acknowledgeMode = "manual";

    /**
     * 消费者并发数
     */
    private Integer concurrency = 1;

    /**
     * 消费者最大并发数
     */
    private Integer maxConcurrency = 5;

    /**
     * 预取数量
     */
    private Integer prefetchCount = 1;

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 队列配置
     */
    private Queue queue = new Queue();

    /**
     * 交换机配置
     */
    private Exchange exchange = new Exchange();

    /**
     * 重试配置
     */
    @Data
    public static class Retry {

        /**
         * 是否启用重试
         */
        private Boolean enabled = true;

        /**
         * 最大重试次数
         */
        private Integer maxAttempts = 3;

        /**
         * 初始重试间隔（毫秒）
         */
        private Long initialInterval = 1000L;

        /**
         * 重试间隔倍数
         */
        private Double multiplier = 2.0;

        /**
         * 最大重试间隔（毫秒）
         */
        private Long maxInterval = 10000L;
    }

    /**
     * 队列配置
     */
    @Data
    public static class Queue {

        /**
         * 文档分段处理队列名称
         */
        private String documentSegmentation = MessageType.DOCUMENT_SEGMENTATION.getQueueName();

        /**
         * 文档状态推送队列名称
         */
        private String documentStatusPush = MessageType.DOCUMENT_STATUS_PUSH.getQueueName();

        /**
         * 向量化处理队列名称
         */
        private String embeddingProcessing = MessageType.EMBEDDING_PROCESSING.getQueueName();

        /**
         * 通知队列名称
         */
        private String notification = MessageType.NOTIFICATION.getQueueName();

        /**
         * 死信队列名称
         */
        private String deadLetter = MessageType.DEAD_LETTER.getQueueName();

        /**
         * 是否持久化
         */
        private Boolean durable = true;

        /**
         * 是否排他
         */
        private Boolean exclusive = false;

        /**
         * 是否自动删除
         */
        private Boolean autoDelete = false;

        /**
         * 消息TTL（毫秒） 默认1小时，与现有队列保持一致，避免参数冲突
         */
        private Long messageTtl = 3600000L; // 1小时
    }

    /**
     * 交换机配置
     */
    @Data
    public static class Exchange {

        /**
         * 主交换机名称
         */
        private String main = "rag.exchange";

        /**
         * 死信交换机名称
         */
        private String deadLetter = "rag.dead.letter.exchange";

        /**
         * 交换机类型
         */
        private String type = "direct";

        /**
         * 是否持久化
         */
        private Boolean durable = true;

        /**
         * 是否自动删除
         */
        private Boolean autoDelete = false;
    }
}
