<template>
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] flex flex-col">
      <!-- 对话框头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">文档导入</h3>
        <button @click="close" class="text-gray-400 hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- 对话框内容 -->
      <div class="flex-1 overflow-y-auto p-6">
        <!-- 拖拽上传区域 -->
        <div
          @drop="handleDrop"
          @dragover.prevent
          @dragenter.prevent
          class="border-2 border-dashed border-blue-300 rounded-lg p-8 text-center mb-6 transition-colors duration-300"
          :class="{
            'border-blue-500 bg-blue-50': isDragging,
            'hover:border-blue-400 hover:bg-blue-25': !isDragging
          }"
        >
          <div class="text-4xl text-blue-400 mb-4">
            <i class="fas fa-cloud-upload-alt"></i>
          </div>
          <h4 class="text-lg font-medium text-gray-700 mb-2">拖拽文件到此处</h4>
          <p class="text-gray-500 mb-4">或者点击下方按钮选择文件</p>
          
          <button
            @click="triggerFileSelect"
            class="btn-primary inline-flex items-center"
          >
            <i class="fas fa-folder-open mr-2"></i>
            选择文件
          </button>
          
          <input
            ref="fileInput"
            type="file"
            multiple
            accept=".md,.docx,.doc,.pdf,.txt"
            @change="handleFileSelect"
            class="hidden"
          />
        </div>

        <!-- 支持的文件格式说明 -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
          <h5 class="font-medium text-gray-800 mb-3">支持的文件格式：</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mr-3">
                <i class="fab fa-markdown text-blue-600"></i>
              </div>
              <div>
                <div class="font-medium text-sm">Markdown</div>
                <div class="text-xs text-gray-500">.md, .markdown</div>
              </div>
            </div>
            <div class="flex items-center">
              <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mr-3">
                <i class="fas fa-file-word text-blue-600"></i>
              </div>
              <div>
                <div class="font-medium text-sm">Word文档</div>
                <div class="text-xs text-gray-500">.docx, .doc</div>
              </div>
            </div>

            <div class="flex items-center">
              <div class="w-8 h-8 bg-red-100 rounded flex items-center justify-center mr-3">
                <i class="fas fa-file-pdf text-red-600"></i>
              </div>
              <div>
                <div class="font-medium text-sm">PDF文档</div>
                <div class="text-xs text-gray-500">.pdf</div>
              </div>
            </div>
            <div class="flex items-center">
              <div class="w-8 h-8 bg-gray-100 rounded flex items-center justify-center mr-3">
                <i class="fas fa-file-alt text-gray-600"></i>
              </div>
              <div>
                <div class="font-medium text-sm">纯文本</div>
                <div class="text-xs text-gray-500">.txt</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文件列表 -->
        <div v-if="selectedFiles.length > 0" class="mb-6">
          <h5 class="font-medium text-gray-800 mb-3">选择的文件：</h5>
          <div class="space-y-2">
            <div
              v-for="(file, index) in selectedFiles"
              :key="index"
              class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg"
            >
              <div class="flex items-center flex-1">
                <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mr-3">
                  <i :class="getFileIcon(file.name)" class="text-blue-600"></i>
                </div>
                <div class="flex-1">
                  <div class="font-medium text-sm text-gray-800">{{ file.name }}</div>
                  <div class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</div>
                </div>
              </div>
              <button
                @click="removeFile(index)"
                class="text-red-500 hover:text-red-700 p-1"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 导入选项 -->
        <div v-if="selectedFiles.length > 0" class="mb-6">
          <h5 class="font-medium text-gray-800 mb-3">导入选项：</h5>
          <div class="space-y-3">
            <label class="flex items-center">
              <input
                v-model="importOptions.appendToContent"
                type="checkbox"
                class="form-checkbox mr-2"
              />
              <span class="text-sm">追加到现有内容后面</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="importOptions.addSeparator"
                type="checkbox"
                class="form-checkbox mr-2"
              />
              <span class="text-sm">在文档之间添加分隔符</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="importOptions.preserveFormatting"
                type="checkbox"
                class="form-checkbox mr-2"
              />
              <span class="text-sm">尽可能保留原始格式</span>
            </label>
          </div>
        </div>

        <!-- 进度显示 -->
        <div v-if="isProcessing" class="mb-6">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">处理进度</span>
            <span class="text-sm text-gray-500">{{ processedCount }}/{{ totalCount }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(processedCount / totalCount) * 100}%` }"
            ></div>
          </div>
          <div v-if="currentProcessingFile" class="text-xs text-gray-500 mt-1">
            正在处理: {{ currentProcessingFile }}
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="errors.length > 0" class="mb-6">
          <h5 class="font-medium text-red-600 mb-2">处理错误：</h5>
          <div class="space-y-1">
            <div
              v-for="(error, index) in errors"
              :key="index"
              class="text-sm text-red-600 bg-red-50 p-2 rounded"
            >
              {{ error }}
            </div>
          </div>
        </div>
      </div>

      <!-- 对话框底部 -->
      <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
        <button @click="close" class="btn-secondary">取消</button>
        <button
          @click="startImport"
          :disabled="selectedFiles.length === 0 || isProcessing"
          class="btn-primary"
        >
          <i v-if="isProcessing" class="fas fa-spinner fa-spin mr-2"></i>
          <i v-else class="fas fa-upload mr-2"></i>
          {{ isProcessing ? '导入中...' : '开始导入' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { DocumentConverter, type ConversionResult } from '@/utils/documentConverter'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'imported', data: { content: string, files: string[] }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const fileInput = ref<HTMLInputElement | null>(null)
const selectedFiles = ref<File[]>([])
const isDragging = ref(false)
const isProcessing = ref(false)
const processedCount = ref(0)
const totalCount = ref(0)
const currentProcessingFile = ref('')
const errors = ref<string[]>([])

// 导入选项
const importOptions = ref({
  appendToContent: true,
  addSeparator: true,
  preserveFormatting: true
})

// 方法
const close = () => {
  emit('close')
  resetState()
}

const resetState = () => {
  selectedFiles.value = []
  isProcessing.value = false
  processedCount.value = 0
  totalCount.value = 0
  currentProcessingFile.value = ''
  errors.value = []
}

const triggerFileSelect = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files) {
    addFiles(Array.from(files))
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragging.value = false
  
  const files = event.dataTransfer?.files
  if (files) {
    addFiles(Array.from(files))
  }
}

const addFiles = (files: File[]) => {
  const validFiles = files.filter(file => {
    const extension = file.name.split('.').pop()?.toLowerCase()
    return ['md', 'markdown', 'txt', 'docx', 'doc', 'pdf'].includes(extension || '')
  })
  
  selectedFiles.value.push(...validFiles)
  
  if (validFiles.length < files.length) {
    errors.value.push(`${files.length - validFiles.length} 个文件格式不支持，已忽略`)
  }
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

const getFileIcon = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'md':
    case 'markdown':
      return 'fab fa-markdown'
    case 'docx':
    case 'doc':
      return 'fas fa-file-word'

    case 'pdf':
      return 'fas fa-file-pdf'
    case 'txt':
      return 'fas fa-file-alt'
    default:
      return 'fas fa-file'
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const startImport = async () => {
  if (selectedFiles.value.length === 0) return
  
  isProcessing.value = true
  processedCount.value = 0
  totalCount.value = selectedFiles.value.length
  errors.value = []
  
  const converter = new DocumentConverter()
  const results: string[] = []
  const processedFiles: string[] = []
  
  for (const file of selectedFiles.value) {
    currentProcessingFile.value = file.name
    
    try {
      const result: ConversionResult = await converter.convertToHtml(file)
      
      if (result.success && result.content) {
        results.push(result.content)
        processedFiles.push(file.name)
      } else {
        errors.value.push(`${file.name}: ${result.error}`)
      }
    } catch (error) {
      errors.value.push(`${file.name}: 处理失败`)
    }
    
    processedCount.value++
  }
  
  // 合并内容
  let finalContent = ''
  if (results.length > 0) {
    if (importOptions.value.addSeparator && results.length > 1) {
      finalContent = results.join('<hr style="margin: 20px 0; border: 1px solid #e5e7eb;">')
    } else {
      finalContent = results.join('<br><br>')
    }
    
    emit('imported', {
      content: finalContent,
      files: processedFiles
    })
  }
  
  isProcessing.value = false
  currentProcessingFile.value = ''
  
  if (errors.value.length === 0) {
    close()
  }
}
</script>

<style scoped>
.form-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.btn-primary {
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  color: #1a365d;
  padding: 10px 20px;
  border-radius: 8px;
  border: 1px solid rgba(127, 179, 211, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(127, 179, 211, 0.3);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #90c9e8 0%, #6ba3c7 100%);
  color: #0f2a44;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(127, 179, 211, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  color: #1e40af;
  padding: 10px 20px;
  border-radius: 8px;
  border: 1px solid #a8d8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #bae6fd 0%, #93c5fd 100%);
  color: #1e3a8a;
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(168, 216, 240, 0.3);
}
</style>
