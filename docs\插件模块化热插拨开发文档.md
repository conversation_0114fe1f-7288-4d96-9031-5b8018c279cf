# XHC AI Plus 插件模块化热插拔开发文档

## 概述

XHC AI Plus 平台基于 Spring + PF4J 框架实现了完整的模块热插拔功能，支持在不重启应用的情况下动态安装、启动、停止、重载和卸载插件模块。

目前已实现热插拔的模块：
- **xhcai-system** - 系统核心模块（用户、角色、权限、部门、租户等）
- **xhcai-agent** - 智能体模块（对话、消息、会话管理等）
- **xhcai-ai** - AI功能模块（聊天记录、模型管理等）
- **xhcai-dify** - Dify集成模块（智能体、知识库、插件管理等）

## 架构设计

### 核心组件

1. **ModuleLifecycle** - 模块生命周期接口，定义模块的初始化、启动、停止、销毁流程
2. **ModuleExtensionPoint** - 模块扩展点接口，专门用于模块插件的PF4J集成
3. **ModuleManager** - 模块管理器，统一管理所有模块的生命周期
4. **DynamicConfigurationLoader** - 动态配置加载器，支持模块配置的热加载
5. **AbstractModuleLifecycle** - 模块生命周期抽象实现，简化模块开发
6. **XhcaiPluginManager** - 插件管理器，基于PF4J实现
7. **XhcaiPluginService** - 插件服务，提供高级API

### 技术栈

- **PF4J 3.13.0** - 插件框架
- **PF4J-Spring 0.10.0** - Spring集成
- **Spring Boot 3.5.3** - 基础框架
- **MyBatis Plus 3.5.12** - 数据访问层
- **Spring Data JPA** - JPA实现
- **Maven** - 构建工具

### 热插拔架构特点

1. **统一的扫描机制**：每个模块通过 `@ComponentScan`、`@EntityScan`、`@MapperScan` 自动扫描组件
2. **动态配置加载**：模块注册时自动加载 `application-{moduleId}.yml` 配置文件
3. **生命周期管理**：统一的模块初始化、启动、停止、销毁流程
4. **真正的热插拔**：无需重启应用，无需手动配置 `profiles.include`

## 模块开发指南

### 1. 模块目录结构

每个热插拔模块都遵循统一的目录结构：

```
modules/{module-name}/
├── src/main/java/com/xhcai/modules/{module}/
│   ├── plugin/                           # 插件相关
│   │   ├── {Module}Plugin.java          # PF4J插件主类
│   │   ├── {Module}ModuleExtension.java # 模块扩展点实现
│   │   └── {Module}PluginSpringConfig.java # Spring配置（扫描配置）
│   ├── config/                          # 配置类
│   │   └── {Module}ModuleConfig.java    # 模块生命周期配置
│   ├── controller/                      # 控制器
│   ├── service/                         # 服务层
│   ├── mapper/                          # MyBatis Mapper
│   ├── entity/                          # JPA实体
│   └── ...
├── src/main/resources/
│   ├── plugin.properties               # PF4J插件描述文件
│   └── application-{module}.yml        # 模块配置文件
└── pom.xml
```

### 2. 创建插件主类

```java
public class MyModulePlugin extends Plugin {

    private static final Logger log = LoggerFactory.getLogger(MyModulePlugin.class);

    public MyModulePlugin(PluginWrapper wrapper) {
        super(wrapper);
    }

    @Override
    public void start() {
        log.info("启动{}模块插件...", getWrapper().getPluginId());
        super.start();
        log.info("{}模块插件启动成功", getWrapper().getPluginId());
    }

    @Override
    public void stop() {
        log.info("停止{}模块插件...", getWrapper().getPluginId());
        super.stop();
        log.info("{}模块插件停止成功", getWrapper().getPluginId());
    }
}
```

### 3. 实现模块扩展点

```java
@Extension
public class MyModuleExtension implements ModuleExtensionPoint {

    private static final Logger log = LoggerFactory.getLogger(MyModuleExtension.class);

    // XhcaiExtensionPoint 接口方法
    @Override
    public String getExtensionId() {
        return "my-module-extension";
    }

    @Override
    public String getExtensionName() {
        return "我的模块扩展";
    }

    @Override
    public String getExtensionDescription() {
        return "我的模块功能描述";
    }

    @Override
    public String getExtensionVersion() {
        return "1.0.0";
    }

    // ModuleExtensionPoint 接口方法
    @Override
    public ModuleMetadata getModuleMetadata() {
        return ModuleMetadata.builder()
                .moduleId("my-module")
                .name("我的模块")
                .version("1.0.0")
                .description("我的模块功能描述")
                .author("xhcai")
                .systemModule(false)
                .build();
    }

    @Override
    public boolean checkModuleHealth() {
        try {
            // 检查模块健康状态
            return checkServices() && checkDatabase();
        } catch (Exception e) {
            log.error("检查模块健康状态失败", e);
            return false;
        }
    }

    @Override
    public void initializeModule() {
        log.info("通过扩展点初始化模块...");
        // 模块初始化逻辑
    }

    @Override
    public void startModule() {
        log.info("通过扩展点启动模块...");
        // 模块启动逻辑
    }

    @Override
    public void stopModule() {
        log.info("通过扩展点停止模块...");
        // 模块停止逻辑
    }

    @Override
    public void destroyModule() {
        log.info("通过扩展点销毁模块...");
        // 模块销毁逻辑
    }

    private boolean checkServices() {
        // 检查服务状态
        return true;
    }

    private boolean checkDatabase() {
        // 检查数据库连接
        return true;
    }
}
```

### 4. 创建Spring配置类

```java
@Configuration
@ComponentScan(basePackages = {
    "com.xhcai.modules.mymodule"
})
@EntityScan(basePackages = "com.xhcai.modules.mymodule.entity")
@ConditionalOnProperty(name = "spring.jpa.hibernate.ddl-auto", havingValue = "update", matchIfMissing = false)
@MapperScan(basePackages = "com.xhcai.modules.mymodule.mapper")
public class MyModulePluginSpringConfig {

    private static final Logger log = LoggerFactory.getLogger(MyModulePluginSpringConfig.class);

    public MyModulePluginSpringConfig() {
        log.info("初始化我的模块插件Spring配置...");
    }

    // Spring配置将通过组件扫描自动加载
    // @ComponentScan: 扫描Spring组件 (Controller, Service, Config)
    // @EntityScan: 扫描JPA实体类，仅在开发环境(ddl-auto=update)生效
    // @MapperScan: 扫描MyBatis Mapper接口
}
```

### 5. 创建模块生命周期配置

```java
@Configuration
public class MyModuleConfig {

    private static final Logger log = LoggerFactory.getLogger(MyModuleConfig.class);

    @Bean
    public ModuleMetadata myModuleMetadata() {
        return ModuleMetadata.builder()
                .moduleId("my-module")
                .name("我的模块")
                .version("1.0.0")
                .description("我的模块功能描述")
                .author("xhcai")
                .dependencies("common-core", "common-security", "common-datasource", "common-api")
                .systemModule(false)
                .build();
    }

    @Bean
    public ModuleLifecycle myModuleLifecycle(ModuleMetadata myModuleMetadata) {
        return new AbstractModuleLifecycle(myModuleMetadata) {

            @Override
            protected void doInitialize() {
                log.info("初始化我的模块资源...");
                // 模块初始化逻辑
            }

            @Override
            protected void doStart() {
                log.info("启动我的模块服务...");
                // 模块启动逻辑
            }

            @Override
            protected void doStop() {
                log.info("停止我的模块服务...");
                // 模块停止逻辑
            }

            @Override
            protected void doDestroy() {
                log.info("清理我的模块资源...");
                // 模块清理逻辑
            }

            @Override
            protected boolean doHealthCheck() {
                // 健康检查逻辑
                return true;
            }
        };
    }
}
```

### 6. 配置插件描述文件

创建 `src/main/resources/plugin.properties`：

```properties
plugin.id=my-module
plugin.class=com.xhcai.modules.mymodule.plugin.MyModulePlugin
plugin.version=1.0.0
plugin.provider=xhcai
plugin.description=我的模块插件
plugin.requires=*
```

### 7. 创建模块配置文件

创建 `src/main/resources/application-mymodule.yml`：

```yaml
# 我的模块配置
mymodule:
  enabled: true
  default:
    setting1: value1
    setting2: value2

# 日志配置
logging:
  level:
    com.xhcai.modules.mymodule: DEBUG
```

### 8. 配置Maven构建

在模块的 `pom.xml` 中添加：

```xml
<dependencies>
    <!-- 公共依赖 -->
    <dependency>
        <groupId>com.xhcai</groupId>
        <artifactId>common-core</artifactId>
    </dependency>
    <dependency>
        <groupId>com.xhcai</groupId>
        <artifactId>common-api</artifactId>
    </dependency>
    <dependency>
        <groupId>com.xhcai</groupId>
        <artifactId>common-security</artifactId>
    </dependency>
    <dependency>
        <groupId>com.xhcai</groupId>
        <artifactId>common-datasource</artifactId>
    </dependency>

    <!-- PF4J依赖 -->
    <dependency>
        <groupId>org.pf4j</groupId>
        <artifactId>pf4j</artifactId>
    </dependency>
    <dependency>
        <groupId>org.pf4j</groupId>
        <artifactId>pf4j-spring</artifactId>
    </dependency>
</dependencies>

<build>
    <plugins>
        <!-- Maven Assembly Plugin for plugin packaging -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <configuration>
                <descriptorRefs>
                    <descriptorRef>jar-with-dependencies</descriptorRef>
                </descriptorRefs>
                <finalName>${project.artifactId}-${project.version}-plugin</finalName>
                <archive>
                    <manifestEntries>
                        <Plugin-Class>com.xhcai.modules.mymodule.plugin.MyModulePlugin</Plugin-Class>
                        <Plugin-Id>my-module</Plugin-Id>
                        <Plugin-Version>${project.version}</Plugin-Version>
                    </manifestEntries>
                </archive>
            </configuration>
        </plugin>
    </plugins>
</build>
```

## 已实现的模块示例

### 1. xhcai-system 模块（系统核心模块）

```
modules/xhcai-system/
├── src/main/java/com/xhcai/modules/system/
│   ├── plugin/
│   │   ├── SystemPlugin.java
│   │   ├── SystemModuleExtension.java
│   │   └── SystemPluginSpringConfig.java
│   ├── config/
│   │   ├── SystemModuleConfig.java
│   │   └── DictInitializationRunner.java
│   ├── controller/                    # 用户、角色、权限等控制器
│   ├── service/                       # 系统服务
│   ├── mapper/                        # MyBatis Mapper
│   ├── entity/                        # JPA实体
│   └── ...
└── src/main/resources/
    ├── plugin.properties
    └── application-system.yml
```

**特点**：
- `systemModule=true` - 标记为系统核心模块
- `priority=100` - 最高优先级，优先于其他模块加载
- 包含用户、角色、权限、部门、租户等基础功能

### 2. xhcai-agent 模块（智能体模块）

```
modules/xhcai-agent/
├── src/main/java/com/xhcai/modules/agent/
│   ├── plugin/
│   │   ├── AgentPlugin.java
│   │   ├── AgentModuleExtension.java
│   │   └── AgentPluginSpringConfig.java
│   ├── config/
│   │   └── AgentModuleConfig.java
│   ├── controller/                    # 智能体相关控制器
│   ├── service/                       # 智能体服务
│   ├── mapper/                        # MyBatis Mapper
│   ├── entity/                        # JPA实体
│   └── ...
└── src/main/resources/
    ├── plugin.properties
    └── application-agent.yml
```

**特点**：
- 智能体对话、消息、会话管理
- 支持多种AI模型集成
- 实时对话功能

### 3. xhcai-ai 模块（AI功能模块）

```
modules/xhcai-ai/
├── src/main/java/com/xhcai/modules/ai/
│   ├── plugin/
│   │   ├── AiPlugin.java
│   │   ├── AiModuleExtension.java
│   │   └── AiPluginSpringConfig.java
│   ├── config/
│   │   └── AiModuleConfig.java
│   ├── controller/                    # AI功能控制器
│   ├── service/                       # AI服务
│   ├── mapper/                        # MyBatis Mapper
│   ├── entity/                        # JPA实体
│   └── ...
└── src/main/resources/
    ├── plugin.properties
    └── application-ai.yml
```

**特点**：
- Spring AI集成
- 聊天记录管理
- 模型配置管理

### 4. xhcai-dify 模块（Dify集成模块）

```
modules/xhcai-dify/
├── src/main/java/com/xhcai/modules/dify/
│   ├── plugin/
│   │   ├── DifyPlugin.java
│   │   ├── DifyModuleExtension.java
│   │   └── DifyPluginSpringConfig.java
│   ├── config/
│   │   └── DifyModuleConfig.java
│   ├── controller/                    # Dify集成控制器
│   ├── service/                       # Dify服务
│   └── ...
└── src/main/resources/
    ├── plugin.properties
    └── application-dify.yml
```

**特点**：
- Dify平台集成
- 智能体、知识库、插件管理
- 目前没有Entity和Mapper（纯API集成）

## 使用指南

### 1. 配置热插拔系统

#### 基础配置

在 `application.yml` 中配置：

```yaml
xhcai:
  plugin:
    enabled: true          # 启用插件系统
    mode: development      # 运行模式：development/production
    root: plugins          # 插件目录
    hotswap: true          # 启用热插拔
    autostart: true        # 自动启动插件
    system-version: 1.0.0  # 系统版本

spring:
  profiles:
    active: dev
    # 注意：不再需要手动配置 include，改为动态加载

  jpa:
    hibernate:
      ddl-auto: update     # 开发环境启用Entity扫描和自动建表
```

#### 环境差异配置

**开发环境** (`application-dev.yml`)：
- 支持加载 `target/classes` 目录的编译文件
- 启用热插拔功能
- 详细的调试日志

**生产环境** (`application-prod.yml`)：
- 只加载 JAR 文件
- 禁用热插拔功能
- 启用安全验证
- 优化的日志级别

### 2. 管理模块

#### 通过API管理

```bash
# 获取所有模块
GET /api/admin/modules

# 获取模块详情
GET /api/admin/modules/{moduleId}

# 启用模块
POST /api/admin/modules/{moduleId}/enable

# 禁用模块
POST /api/admin/modules/{moduleId}/disable

# 重启模块
POST /api/admin/modules/{moduleId}/restart

# 获取模块健康状态
GET /api/admin/modules/{moduleId}/health

# 动态加载模块配置
POST /api/admin/modules/config/{moduleId}/load

# 重新加载模块配置
POST /api/admin/modules/config/{moduleId}/reload

# 获取模块配置状态
GET /api/admin/modules/config/{moduleId}/status
```

#### 通过代码管理

```java
@Autowired
private ModuleManager moduleManager;

@Autowired
private DynamicConfigurationLoader configurationLoader;

// 注册模块
ModuleLifecycle moduleLifecycle = // ... 创建模块生命周期实例
boolean success = moduleManager.registerModule("module-id", moduleLifecycle);

// 启用模块
moduleManager.enableModule("module-id");

// 禁用模块
moduleManager.disableModule("module-id");

// 重启模块
moduleManager.restartModule("module-id");

// 卸载模块
moduleManager.unregisterModule("module-id");

// 动态加载模块配置
configurationLoader.loadModuleConfiguration("module-id");

// 检查模块健康状态
boolean healthy = moduleManager.isModuleHealthy("module-id");

// 获取模块状态
ModuleStatus status = moduleManager.getModuleStatus("module-id");
```

## 热插拔工作流程

### 1. 应用启动流程

```
1. Spring Boot 应用启动
2. ModuleManager 初始化
3. DynamicConfigurationLoader 初始化
4. 扫描并注册所有模块：
   ├── xhcai-system (priority=100, systemModule=true)
   ├── xhcai-agent
   ├── xhcai-ai
   └── xhcai-dify
5. 每个模块注册时：
   ├── 动态加载 application-{moduleId}.yml 配置
   ├── @ComponentScan 扫描Spring组件
   ├── @EntityScan 扫描JPA实体（开发环境）
   ├── @MapperScan 扫描MyBatis Mapper
   └── 执行模块生命周期：initialize() → start()
6. 所有模块启动完成
```

### 2. 模块热插拔流程

```
添加新模块：
1. 开发模块（按照上述开发指南）
2. 构建模块JAR包
3. 调用 POST /api/admin/modules/{moduleId}/enable
4. 系统自动：
   ├── 注册模块生命周期
   ├── 加载模块配置文件
   ├── 扫描模块组件
   ├── 创建数据库表（如果有Entity）
   └── 启动模块服务
5. 模块立即可用，无需重启应用

移除模块：
1. 调用 POST /api/admin/modules/{moduleId}/disable
2. 系统自动：
   ├── 停止模块服务
   ├── 清理模块资源
   ├── 移除模块配置
   └── 注销模块组件
3. 模块完全卸载，无需重启应用
```

### 3. 构建和部署

#### 构建单个模块

```bash
# 构建系统模块
cd modules/xhcai-system
mvn clean package

# 构建智能体模块
cd modules/xhcai-agent
mvn clean package

# 构建AI模块
cd modules/xhcai-ai
mvn clean package

# 构建Dify模块
cd modules/xhcai-dify
mvn clean package
```

#### 构建所有模块

```bash
# 在项目根目录执行
mvn clean package -pl modules/xhcai-system,modules/xhcai-agent,modules/xhcai-ai,modules/xhcai-dify
```

构建完成后，每个模块的 `target/` 目录下会生成对应的插件JAR文件。

## 测试验证

### 1. 功能测试

```bash
# 启动应用
mvn spring-boot:run -pl admin-api

# 测试模块状态
curl -X GET http://localhost:8080/api/admin/modules

# 测试模块健康检查
curl -X GET http://localhost:8080/api/admin/modules/xhcai-system/health
curl -X GET http://localhost:8080/api/admin/modules/xhcai-agent/health
curl -X GET http://localhost:8080/api/admin/modules/xhcai-ai/health
curl -X GET http://localhost:8080/api/admin/modules/xhcai-dify/health

# 测试配置动态加载
curl -X POST http://localhost:8080/api/admin/modules/config/xhcai-agent/reload
```

### 2. 数据库验证

检查数据库表是否正确创建：

```sql
-- 系统模块表
SELECT table_name FROM information_schema.tables WHERE table_name LIKE 'sys_%';

-- 智能体模块表
SELECT table_name FROM information_schema.tables WHERE table_name LIKE 'agent_%';

-- AI模块表
SELECT table_name FROM information_schema.tables WHERE table_name LIKE 'ai_%';
```

### 3. 日志验证

查看应用启动日志，确认模块正确加载：

```
[INFO] 初始化系统模块插件Spring配置...
[INFO] 初始化智能体模块插件Spring配置...
[INFO] 初始化AI模块插件Spring配置...
[INFO] 初始化Dify模块插件Spring配置...
[INFO] 模块 xhcai-system 注册成功
[INFO] 模块 xhcai-agent 注册成功
[INFO] 模块 xhcai-ai 注册成功
[INFO] 模块 xhcai-dify 注册成功
```

## 注意事项

### 1. 开发环境 vs 生产环境

**开发环境**：
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: update  # 启用@EntityScan，自动建表
```

**生产环境**：
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: validate  # 不启用@EntityScan，使用Liquibase管理表结构
```

### 2. 模块优先级

- **系统模块**：`systemModule=true`，`priority=100`，最先加载
- **业务模块**：按照依赖关系和注册顺序加载

### 3. 配置管理

- 每个模块有独立的配置文件：`application-{moduleId}.yml`
- 配置通过 `DynamicConfigurationLoader` 动态加载
- 避免配置键冲突，使用模块前缀

### 4. 数据库事务

- 所有模块共享同一个数据源
- 跨模块操作需要注意事务边界
- 建议使用 `@Transactional` 注解管理事务

### 5. 安全考虑

- 模块注册需要相应的权限
- 生产环境应该严格控制模块的安装和卸载
- 模块间的API调用需要权限验证

## 故障排除

### 常见问题

1. **Mapper扫描失败**
   ```
   Error: No qualifying bean of type 'com.xhcai.modules.xxx.mapper.XxxMapper'
   ```
   **解决方案**：检查模块的 `PluginSpringConfig` 中是否正确配置了 `@MapperScan`

2. **Entity扫描失败**
   ```
   Error: Not a managed type: class com.xhcai.modules.xxx.entity.XxxEntity
   ```
   **解决方案**：检查 `@EntityScan` 配置和 `spring.jpa.hibernate.ddl-auto` 设置

3. **模块配置加载失败**
   ```
   Error: Could not resolve placeholder 'xxx.property'
   ```
   **解决方案**：检查 `application-{moduleId}.yml` 文件是否存在和格式是否正确

4. **Bean冲突**
   ```
   Error: ConflictingBeanDefinitionException
   ```
   **解决方案**：检查是否有重复的Bean名称，使用 `@Qualifier` 或重命名Bean

5. **模块启动失败**
   ```
   Error: Module initialization failed
   ```
   **解决方案**：检查模块的 `doInitialize()` 方法中的逻辑，查看具体异常信息

### 调试配置

```yaml
logging:
  level:
    org.pf4j: DEBUG
    com.xhcai.common.core.plugin: DEBUG
    com.xhcai.modules: DEBUG
    org.springframework.boot.autoconfigure: DEBUG
    org.mybatis: DEBUG
    org.hibernate: DEBUG
```

## 最佳实践

### 1. 模块设计原则

- **单一职责**：每个模块专注于特定的业务领域
- **松耦合**：模块间通过接口和事件通信，避免直接依赖
- **高内聚**：模块内部功能紧密相关，对外提供清晰的API

### 2. 开发规范

- **命名规范**：模块ID使用 `xhcai-{domain}` 格式
- **包结构**：遵循 `com.xhcai.modules.{domain}` 包结构
- **配置前缀**：使用模块名作为配置前缀，如 `agent.xxx`

### 3. 版本管理

- **语义化版本**：使用 `MAJOR.MINOR.PATCH` 格式
- **兼容性**：向后兼容的修改增加MINOR版本
- **破坏性变更**：增加MAJOR版本

### 4. 测试策略

- **单元测试**：覆盖核心业务逻辑
- **集成测试**：测试模块间的交互
- **热插拔测试**：验证模块的动态加载和卸载

### 5. 监控和运维

- **健康检查**：实现 `checkModuleHealth()` 方法
- **日志规范**：使用统一的日志格式和级别
- **性能监控**：监控模块的资源使用情况

### 6. 安全考虑

- **权限控制**：模块操作需要相应的权限
- **输入验证**：严格验证外部输入
- **敏感信息**：配置文件中的敏感信息需要加密

## 总结

XHC AI Plus 平台的插件模块化热插拔机制提供了：

✅ **真正的热插拔**：无需重启应用即可动态管理模块
✅ **统一的架构**：所有模块遵循相同的开发和部署模式
✅ **自动化扫描**：Spring组件、JPA实体、MyBatis Mapper自动扫描
✅ **动态配置**：模块配置文件动态加载，无需手动配置profiles
✅ **生命周期管理**：完整的模块初始化、启动、停止、销毁流程
✅ **健康监控**：模块健康状态实时监控
✅ **开发友好**：简化的开发流程和丰富的示例

通过这套机制，开发者可以快速开发新的业务模块，并在不影响系统稳定性的前提下进行热插拔部署。
