<template>
  <div class="business-project-container max-w-full">
    <!-- 页面头部（整合搜索和过滤） -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="fas fa-project-diagram"></i>
            AI场景管理
          </h1>
        </div>
        <div class="header-right">
          <div class="search-box">
              <i class="fas fa-search"></i>
              <input
                type="text"
                v-model="searchQuery"
                placeholder="搜索AI场景名称或描述..."
                class="search-input"
              />
          </div>
          <div class="filter-controls">
            <select v-model="selectedEnvironment" class="filter-select">
              <option value="">全部环境</option>
              <option value="production">生产环境</option>
              <option value="test">测试环境</option>
              <option value="development">开发环境</option>
            </select>
            <select v-model="selectedStatus" class="filter-select">
              <option value="">全部状态</option>
              <option value="active">运行中</option>
              <option value="inactive">已停止</option>
              <option value="maintenance">维护中</option>
            </select>
          </div>
          <button class="btn btn-primary" @click="showCreateModal">
            <i class="fas fa-plus"></i>
            创建AI场景
          </button>
        </div>
      </div>
    </div>

    <!-- 项目卡片网格 -->
    <div class="projects-grid">
      <ProjectCard
        v-for="project in filteredProjects"
        :key="project.id"
        :project="project"
        @edit="handleEdit"
        @delete="handleDelete"
        @create-agent="handleCreateAgent"
        @create-knowledge="handleCreateKnowledge"
        @create-graph="handleCreateGraph"
        @link-agent="handleLinkAgent"
        @link-knowledge="handleLinkKnowledge"
        @link-graph="handleLinkGraph"
        @manage-team="handleManageTeam"
        @switch-environment="handleSwitchEnvironment"
        @navigate-knowledge="navigateToKnowledge"
        @navigate-graph="navigateToGraph"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="filteredProjects.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-project-diagram"></i>
      </div>
      <h3>暂无AI场景</h3>
      <p>创建您的第一个AI场景，开始构建智能化业务流程</p>
      <button class="btn btn-primary" @click="showCreateModal">
        <i class="fas fa-plus"></i>
        创建AI场景
      </button>
    </div>

    <!-- 创建/编辑模态框 -->
    <ProjectFormModal
      v-if="showModal"
      :visible="showModal"
      :project="editingProject"
      :mode="modalMode"
      @close="closeModal"
      @save="handleSave"
    />

    <!-- 确认删除模态框 -->
    <ConfirmModal
      v-if="showDeleteModal"
      :visible="showDeleteModal"
      title="删除AI场景"
      :message="`确定要删除AI场景 ${deletingProject?.name} 吗？此操作不可撤销。`"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />

    <!-- 团队管理模态框 -->
    <TeamManageModal
      v-if="showTeamModal"
      :visible="showTeamModal"
      :project="managingProject"
      @close="closeTeamModal"
      @save="handleTeamSave"
    />

    <!-- 环境切换模态框 -->
    <EnvironmentSwitchModal
      v-if="showEnvModal"
      :visible="showEnvModal"
      :project="switchingProject"
      @close="closeEnvModal"
      @save="handleEnvSwitch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from '@/composables/useMessage'
import { businessProjectApi } from '@/api/business'
import type {
  BusinessProject as ApiBusinessProject,
  ProjectTeamMember,
  SysUser,
  CreateBusinessProjectRequest,
  UpdateBusinessProjectRequest
} from '@/api/business'
import ProjectCard from './components/ProjectCard.vue'
import ProjectFormModal from './components/ProjectFormModal.vue'
import ConfirmModal from './components/ConfirmModal.vue'
import TeamManageModal from './components/TeamManageModal.vue'
import EnvironmentSwitchModal from './components/EnvironmentSwitchModal.vue'

// 使用API中定义的类型
type BusinessProject = ApiBusinessProject

// 响应式数据
const router = useRouter()
const { showMessage } = useMessage()

const projects = ref<BusinessProject[]>([])
const searchQuery = ref('')
const selectedEnvironment = ref<'production' | 'test' | 'development' | ''>('')
const selectedStatus = ref<'active' | 'inactive' | 'maintenance' | ''>('')

// 模态框状态
const showModal = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const editingProject = ref<BusinessProject | null>(null)

const showDeleteModal = ref(false)
const deletingProject = ref<BusinessProject | null>(null)

const showTeamModal = ref(false)
const managingProject = ref<BusinessProject | null>(null)

const showEnvModal = ref(false)
const switchingProject = ref<BusinessProject | null>(null)

// 计算属性
const filteredProjects = computed(() => {
  return projects.value.filter(project => {
    const matchesSearch = !searchQuery.value ||
      project.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (project.description && project.description.toLowerCase().includes(searchQuery.value.toLowerCase()))

    const matchesEnvironment = !selectedEnvironment.value ||
      project.environment === selectedEnvironment.value

    const matchesStatus = !selectedStatus.value ||
      project.status === selectedStatus.value

    return matchesSearch && matchesEnvironment && matchesStatus
  })
})

// 方法
const loadProjects = async () => {
  try {
    const response = await businessProjectApi.getProjects({
      name: searchQuery.value || undefined,
      environment: selectedEnvironment.value || undefined,
      status: selectedStatus.value || undefined
    })

    if (response.success && response.data) {
      projects.value = response.data.records || response.data
    } else {
      // 如果API调用失败，使用模拟数据
      projects.value = [
        {
          id: '1',
          name: '智能客服系统',
          description: '基于AI的智能客服解决方案，提供7x24小时客户服务',
          ownerId: 'user1',
          environment: 'production' as const,
          status: 'active' as const,
          icon: 'fas fa-headset',
          iconColor: 'linear-gradient(135deg, #667eea, #764ba2)',
          deleted: 0,
          createTime: '2024-01-15T10:00:00',
          updateTime: '2024-01-20T15:30:00',
          owner: {
            id: 'user1',
            username: 'zhangsan',
            nickname: '张三',
            email: '<EMAIL>',
            status: 1,
            createTime: '2024-01-01T00:00:00',
            updateTime: '2024-01-01T00:00:00'
          },
          teamMembers: [
            {
              id: 'tm1',
              projectId: '1',
              userId: 'user2',
              role: '开发工程师',
              joinTime: '2024-01-16T09:00:00',
              user: {
                id: 'user2',
                username: 'lisi',
                nickname: '李四',
                email: '<EMAIL>',
                status: 1,
                createTime: '2024-01-01T00:00:00',
                updateTime: '2024-01-01T00:00:00'
              }
            }
          ],
          agentCount: 5,
          knowledgeCount: 3,
          graphCount: 2,
          agentRunningCount: 4,
          knowledgeBuiltCount: 3,
          graphRelationCount: 156
        },
        {
          id: '2',
          name: '文档智能分析',
          description: '企业文档智能分析和知识提取系统',
          ownerId: 'user3',
          environment: 'test' as const,
          status: 'active' as const,
          icon: 'fas fa-file-alt',
          iconColor: 'linear-gradient(135deg, #f093fb, #f5576c)',
          deleted: 0,
          createTime: '2024-01-10T14:00:00',
          updateTime: '2024-01-18T16:45:00',
          owner: {
            id: 'user3',
            username: 'wangwu',
            nickname: '王五',
            email: '<EMAIL>',
            status: 1,
            createTime: '2024-01-01T00:00:00',
            updateTime: '2024-01-01T00:00:00'
          },
          teamMembers: [],
          agentCount: 3,
          knowledgeCount: 8,
          graphCount: 1,
          agentRunningCount: 2,
          knowledgeBuiltCount: 6,
          graphRelationCount: 89
        }
      ]
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
    showMessage('加载项目列表失败')

    // 出错时使用空数组
    projects.value = []
  }
}

// 模态框操作
const showCreateModal = () => {
  modalMode.value = 'create'
  editingProject.value = null
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  editingProject.value = null
}

const handleSave = async (projectData: CreateBusinessProjectRequest) => {
  try {
    if (modalMode.value === 'create') {
      await businessProjectApi.createProject(projectData)
      showMessage('AI场景创建成功')
    } else {
      const updateData: UpdateBusinessProjectRequest = {
        id: editingProject.value!.id,
        name: projectData.name,
        description: projectData.description,
        ownerId: projectData.ownerId,
        environment: projectData.environment,
        icon: projectData.icon,
        iconColor: projectData.iconColor,
        remark: projectData.remark
      }
      await businessProjectApi.updateProject(editingProject.value!.id, updateData)
      showMessage('AI场景更新成功')
    }
    closeModal()
    await loadProjects()
  } catch (error) {
    console.error('保存失败:', error)
    showMessage('保存失败')
  }
}

// 项目操作
const handleEdit = (project: BusinessProject) => {
  modalMode.value = 'edit'
  editingProject.value = { ...project }
  showModal.value = true
}

const handleDelete = (project: BusinessProject) => {
  deletingProject.value = project
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  try {
    await businessProjectApi.deleteProject(deletingProject.value!.id)
    showMessage('AI场景删除成功')
    showDeleteModal.value = false
    deletingProject.value = null
    await loadProjects()
  } catch (error) {
    console.error('删除失败:', error)
    showMessage('删除失败')
  }
}

const cancelDelete = () => {
  showDeleteModal.value = false
  deletingProject.value = null
}

// 创建子资源
const handleCreateAgent = (project: BusinessProject) => {
  router.push({
    name: 'Agents',
    query: { projectId: project.id, action: 'create' }
  })
}

const handleCreateKnowledge = (project: BusinessProject) => {
  router.push({
    name: 'Knowledge',
    query: { projectId: project.id, action: 'create' }
  })
}

const handleCreateGraph = (project: BusinessProject) => {
  router.push({
    name: 'KnowledgeGraph',
    query: { projectId: project.id, action: 'create' }
  })
}

// 关联子资源
const handleLinkAgent = (project: BusinessProject) => {
  router.push({
    name: 'Agents',
    query: { projectId: project.id, action: 'link' }
  })
}

const handleLinkKnowledge = (project: BusinessProject) => {
  router.push({
    name: 'Knowledge',
    query: { projectId: project.id, action: 'link' }
  })
}

const handleLinkGraph = (project: BusinessProject) => {
  router.push({
    name: 'KnowledgeGraph',
    query: { projectId: project.id, action: 'link' }
  })
}

const navigateToKnowledge = (project: BusinessProject) => {
  router.push({
    name: 'Knowledge',
    query: { projectId: project.id }
  })
}

const navigateToGraph = (project: BusinessProject) => {
  router.push({
    name: 'KnowledgeGraph',
    query: { projectId: project.id }
  })
}

// 团队管理
const handleManageTeam = (project: BusinessProject) => {
  managingProject.value = project
  showTeamModal.value = true
}

const closeTeamModal = () => {
  showTeamModal.value = false
  managingProject.value = null
}

const handleTeamSave = async (teamData: any[]) => {
  try {
    const teamMembers = teamData.map(member => ({
      userId: member.userId || member.id,
      role: member.role
    }))
    await businessProjectApi.updateProjectTeam(managingProject.value!.id, teamMembers)
    showMessage('团队信息更新成功')
    closeTeamModal()
    await loadProjects()
  } catch (error) {
    console.error('更新团队失败:', error)
    showMessage('更新团队失败')
  }
}

// 环境切换
const handleSwitchEnvironment = (project: BusinessProject) => {
  switchingProject.value = project
  showEnvModal.value = true
}

const closeEnvModal = () => {
  showEnvModal.value = false
  switchingProject.value = null
}

const handleEnvSwitch = async (environment: string) => {
  try {
    const env = environment as 'production' | 'test' | 'development'
    await businessProjectApi.switchProjectEnvironment(switchingProject.value!.id, env)
    showMessage('环境切换成功')
    closeEnvModal()
    await loadProjects()
  } catch (error) {
    console.error('环境切换失败:', error)
    showMessage('环境切换失败')
  }
}

// 生命周期
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.business-project-container {
  background: var(--secondary-gradient);
  min-height: 100vh;
}


.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--background-white);
  padding: 24px 32px;
  gap: 24px;
}

.header-left {
  flex-shrink: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: flex-end;
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
}

.page-title i {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 28px;
}

.search-box {
  position: relative;
  min-width: 300px;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 16px;
}

.search-input {
  width: 100%;
  padding: 10px 16px 10px 48px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  font-size: 14px;
  background: white;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  font-size: 13px;
  background: white;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
  min-width: 110px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  padding: 20px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 32px;
}

.empty-icon {
  margin-bottom: 24px;
}

.empty-icon i {
  font-size: 64px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0.6;
}

.empty-state h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.empty-state p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0 0 32px 0;
  line-height: 1.6;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-primary:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-center {
    flex-direction: column;
    gap: 12px;
  }

  .search-box {
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .business-project-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    padding: 20px;
  }

  .header-center {
    width: 100%;
    flex-direction: column;
    gap: 12px;
  }

  .search-box {
    min-width: auto;
    max-width: none;
  }

  .filter-controls {
    width: 100%;
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-title i {
    font-size: 24px;
  }

  .empty-state {
    padding: 60px 20px;
  }
}

@media (max-width: 480px) {
  .business-project-container {
    padding: 12px;
  }

  .header-content,
  .filter-section {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-title i {
    font-size: 24px;
  }

  .empty-icon i {
    font-size: 48px;
  }

  .empty-state h3 {
    font-size: 20px;
  }
}
</style>
