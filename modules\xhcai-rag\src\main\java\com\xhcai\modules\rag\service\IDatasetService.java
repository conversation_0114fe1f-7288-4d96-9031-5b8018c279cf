package com.xhcai.modules.rag.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.rag.entity.Dataset;
import com.xhcai.modules.rag.vo.DatasetVO;

/**
 * 知识库服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDatasetService extends IService<Dataset> {

    /**
     * 分页查询知识库列表
     *
     * @param current 当前页
     * @param size 每页大小
     * @param tenantId 租户ID
     * @param name 知识库名称（模糊查询）
     * @param dataSourceType 数据源类型
     * @return 知识库分页列表
     */
    IPage<DatasetVO> pageByTenant(Long current, Long size, String tenantId, String name, String dataSourceType);

    /**
     * 分页查询知识库列表（包含用户信息）
     *
     * @param current 当前页
     * @param size 每页大小
     * @param tenantId 租户ID
     * @param name 知识库名称（模糊查询）
     * @param dataSourceType 数据源类型
     * @return 知识库分页列表（包含用户和部门信息）
     */
    IPage<DatasetVO> pageByTenantWithUserInfo(Long current, Long size, String tenantId, String name, String dataSourceType);

    /**
     * 根据租户ID查询知识库列表
     *
     * @param tenantId 租户ID
     * @return 知识库列表
     */
    List<DatasetVO> listByTenantId(String tenantId);

    /**
     * 创建知识库
     *
     * @param dataset 知识库信息
     * @return 创建的知识库
     */
    Dataset createDataset(Dataset dataset);

    /**
     * 更新知识库
     *
     * @param dataset 知识库信息
     * @return 更新的知识库
     */
    Dataset updateDataset(Dataset dataset);

    /**
     * 删除知识库
     *
     * @param id 知识库ID
     * @return 是否删除成功
     */
    boolean deleteDataset(String id);

    /**
     * 根据ID查询知识库详情
     *
     * @param id 知识库ID
     * @return 知识库详情
     */
    Dataset getDatasetById(String id);

    /**
     * 检查知识库名称是否存在
     *
     * @param name 知识库名称
     * @param tenantId 租户ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByName(String name, String tenantId, String excludeId);

    /**
     * 统计租户的知识库数量
     *
     * @param tenantId 租户ID
     * @return 知识库数量
     */
    Long countByTenantId(String tenantId);

    /**
     * 根据模型ID查询使用该模型的知识库数量
     *
     * @param modelId 模型ID
     * @return 使用该模型的知识库数量
     */
    Long countByModelId(String modelId);

    /**
     * 分页查询用户有权限的知识库列表
     *
     * @param current 当前页
     * @param size 每页大小
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @param name 知识库名称（模糊查询）
     * @return 知识库分页列表
     */
    IPage<Dataset> pageByUserPermission(Long current, Long size, String tenantId, String userId,
                                        String deptId, List<String> roleIds, String name);

    /**
     * 检查用户是否有知识库权限
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @return 是否有权限
     */
    boolean hasPermission(String datasetId, String userId, String deptId, List<String> roleIds);

    /**
     * 检查用户是否有知识库管理权限
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @return 是否有管理权限
     */
    boolean hasManagePermission(String datasetId, String userId);

    /**
     * 获取知识库统计信息
     *
     * @param datasetId 知识库ID
     * @return 统计信息
     */
    Object getDatasetStats(String datasetId);

    /**
     * 复制知识库
     *
     * @param sourceId 源知识库ID
     * @param newName 新知识库名称
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 复制的知识库
     */
    Dataset copyDataset(String sourceId, String newName, String tenantId, String userId);

    /**
     * 导出知识库配置
     *
     * @param datasetId 知识库ID
     * @return 配置信息
     */
    Object exportDatasetConfig(String datasetId);

    /**
     * 导入知识库配置
     *
     * @param config 配置信息
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 导入的知识库
     */
    Dataset importDatasetConfig(Object config, String tenantId, String userId);
}
