package com.xhcai.modules.system.service;

import java.util.List;
import java.util.Map;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysUserQueryDTO;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.vo.SysUserVO;

/**
 * 平台管理员服务接口 提供跨租户的管理功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IPlatformAdminService {

    /**
     * 跨租户分页查询用户列表
     *
     * @param queryDTO 查询条件
     * @param tenantId 指定租户ID，null表示查询所有租户
     * @return 用户分页列表
     */
    PageResult<SysUserVO> selectCrossTenantUserPage(SysUserQueryDTO queryDTO, String tenantId);

    /**
     * 跨租户查询用户详情
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 用户详情
     */
    SysUserVO getCrossTenantUserDetail(String userId, String tenantId);

    /**
     * 跨租户创建用户
     *
     * @param user 用户信息
     * @param targetTenantId 目标租户ID
     * @return 是否成功
     */
    boolean createCrossTenantUser(SysUser user, String targetTenantId);

    /**
     * 跨租户更新用户
     *
     * @param user 用户信息
     * @param targetTenantId 目标租户ID
     * @return 是否成功
     */
    boolean updateCrossTenantUser(SysUser user, String targetTenantId);

    /**
     * 跨租户删除用户
     *
     * @param userIds 用户ID列表
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean deleteCrossTenantUsers(List<String> userIds, String tenantId);

    /**
     * 跨租户重置用户密码
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetCrossTenantUserPassword(String userId, String tenantId, String newPassword);

    /**
     * 跨租户启用/禁用用户
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean toggleCrossTenantUserStatus(String userId, String tenantId, boolean enabled);

    /**
     * 获取系统统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getSystemStatistics();

    /**
     * 获取租户统计信息
     *
     * @return 租户统计信息列表
     */
    List<Map<String, Object>> getTenantStatistics();

    /**
     * 获取用户统计信息
     *
     * @param tenantId 租户ID，null表示所有租户
     * @return 用户统计信息
     */
    Map<String, Object> getUserStatistics(String tenantId);

    /**
     * 获取系统性能监控信息
     *
     * @return 性能监控信息
     */
    Map<String, Object> getSystemPerformance();

    /**
     * 获取系统日志统计
     *
     * @param days 统计天数
     * @return 日志统计信息
     */
    Map<String, Object> getLogStatistics(Integer days);

    /**
     * 清理系统数据
     *
     * @param dataType 数据类型（logs, temp_files, cache等）
     * @param days 保留天数
     * @return 清理结果
     */
    Map<String, Object> cleanupSystemData(String dataType, Integer days);

    /**
     * 执行系统维护任务
     *
     * @param taskType 任务类型
     * @return 执行结果
     */
    Map<String, Object> executeMaintenanceTask(String taskType);

    /**
     * 获取系统配置信息
     *
     * @return 系统配置
     */
    Map<String, Object> getSystemConfig();

    /**
     * 更新系统配置
     *
     * @param configs 配置信息
     * @return 是否成功
     */
    boolean updateSystemConfig(Map<String, Object> configs);

    /**
     * 获取所有租户的配置概览
     *
     * @return 租户配置概览
     */
    List<Map<String, Object>> getTenantConfigOverview();

    /**
     * 批量更新租户配置
     *
     * @param tenantIds 租户ID列表
     * @param configs 配置信息
     * @return 是否成功
     */
    boolean batchUpdateTenantConfigs(List<String> tenantIds, Map<String, String> configs);

    /**
     * 获取系统健康检查结果
     *
     * @return 健康检查结果
     */
    Map<String, Object> getSystemHealthCheck();

    /**
     * 获取数据库连接状态
     *
     * @return 数据库连接状态
     */
    Map<String, Object> getDatabaseStatus();

    /**
     * 获取缓存状态
     *
     * @return 缓存状态
     */
    Map<String, Object> getCacheStatus();

    /**
     * 清理缓存
     *
     * @param cacheType 缓存类型
     * @return 是否成功
     */
    boolean clearCache(String cacheType);

    /**
     * 获取系统错误日志
     *
     * @param hours 最近小时数
     * @param level 日志级别
     * @return 错误日志列表
     */
    List<Map<String, Object>> getSystemErrorLogs(Integer hours, String level);

    /**
     * 导出系统数据
     *
     * @param dataType 数据类型
     * @param tenantId 租户ID，null表示所有租户
     * @return 导出文件路径
     */
    String exportSystemData(String dataType, String tenantId);

    /**
     * 导入系统数据
     *
     * @param dataType 数据类型
     * @param filePath 文件路径
     * @param tenantId 目标租户ID
     * @return 导入结果
     */
    Map<String, Object> importSystemData(String dataType, String filePath, String tenantId);

    /**
     * 发送系统通知
     *
     * @param title 通知标题
     * @param content 通知内容
     * @param tenantIds 目标租户ID列表，null表示所有租户
     * @param userIds 目标用户ID列表，null表示所有用户
     * @return 是否成功
     */
    boolean sendSystemNotification(String title, String content, List<String> tenantIds, List<String> userIds);

    /**
     * 获取系统版本信息
     *
     * @return 版本信息
     */
    Map<String, Object> getSystemVersion();

    /**
     * 检查系统更新
     *
     * @return 更新信息
     */
    Map<String, Object> checkSystemUpdate();

    /**
     * 检查平台是否已初始化（是否存在平台管理员）
     *
     * @return 初始化状态信息
     */
    Map<String, Object> checkPlatformInitialization();

    /**
     * 创建平台管理员账号（平台初始化）
     *
     * @param username 管理员用户名
     * @param password 管理员密码
     * @param email 管理员邮箱
     * @param realName 管理员真实姓名
     * @return 创建结果
     */
    Map<String, Object> createPlatformAdmin(String username, String password, String email, String realName);
}
