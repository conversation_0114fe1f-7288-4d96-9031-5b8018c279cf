<template>
  <div
    class="conversation-sidebar bg-white/90 backdrop-blur-xl border-r border-gray-200/50 flex flex-col shadow-lg relative"
    style="width: 320px; min-width: 320px; max-width: 320px; flex-shrink: 0;"
  >
    <!-- 侧边栏顶部工具区 -->
    <div class="p-4 border-b border-gray-200/50 space-y-3">
      <!-- 标题行：AI探索 + 隐藏按钮 -->
      <div class="flex items-center justify-between gap-2 mb-3">
        <div class="flex items-center gap-2">
          <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-md flex items-center justify-center">
            <span class="text-white text-xs">🧠</span>
          </div>
          <h3 class="font-semibold text-gray-800 text-sm">AI探索</h3>
        </div>

        <button
          @click="$emit('hide-sidebar')"
          class="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 flex items-center justify-center"
          title="隐藏侧边栏"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>
      </div>

      <!-- 新对话按钮 -->
      <div>
        <button
          @click="handleCreateNewConversation"
          class="w-full px-3 py-2 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-lg text-sm font-medium hover:from-green-600 hover:to-teal-600 transition-all duration-200 flex items-center justify-center gap-2"
          title="新建对话"
        >
          <span>➕</span>
          新建对话
        </button>
      </div>
    </div>

    <!-- 对话记录标题 - 优化版本 -->
    <div class="px-4 py-3 bg-gradient-to-r from-gray-50 to-gray-100/80 border-b border-gray-200/50">
      <div class="flex items-center justify-between">
        <!-- 左侧标题区域 -->
        <div class="flex items-center gap-2">
          <div class="w-6 h-6 rounded-lg flex items-center justify-center transition-all duration-300"
               :class="showArchived ? 'bg-amber-100 text-amber-600' : 'bg-blue-100 text-blue-600'">
            <span class="text-sm">{{ showArchived ? '📁' : '📄' }}</span>
          </div>
          <h4 class="text-sm font-semibold transition-colors duration-300"
              :class="showArchived ? 'text-amber-700' : 'text-blue-700'">
            <span v-if="!showArchived">对话记录</span>
            <span v-else>归档记录</span>
          </h4>
          <!-- 数量徽章 -->
          <span class="px-2 py-0.5 text-xs font-medium rounded-full transition-all duration-300"
                :class="showArchived
                  ? 'bg-amber-100 text-amber-700 border border-amber-200'
                  : 'bg-blue-100 text-blue-700 border border-blue-200'">
            {{ showArchived ? archivedConversations.length : activeConversations.length }}
          </span>
        </div>

        <!-- 右侧切换按钮 -->
        <div class="flex items-center gap-2">
          <!-- 切换开关样式的按钮 -->
          <div class="relative">
            <button
              @click="toggleArchiveView"
              class="group relative flex items-center gap-2 px-3 py-1.5 rounded-lg border transition-all duration-300 hover:shadow-sm"
              :class="showArchived
                ? 'bg-amber-50 border-amber-200 text-amber-700 hover:bg-amber-100'
                : 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100'"
              :title="showArchived ? '切换到活跃对话' : '切换到归档记录'"
            >
              <!-- 切换图标 -->
              <div class="flex items-center gap-1.5">
                <div class="w-4 h-4 rounded-full flex items-center justify-center transition-transform duration-300 group-hover:scale-110"
                     :class="showArchived ? 'bg-amber-200' : 'bg-blue-200'">
                  <span class="text-xs">{{ showArchived ? '📄' : '📁' }}</span>
                </div>
                <span class="text-xs font-medium">
                  {{ showArchived ? '活跃' : '归档' }}
                </span>
              </div>

              <!-- 切换箭头指示器 -->
              <div class="w-3 h-3 flex items-center justify-center">
                <svg class="w-2.5 h-2.5 transition-transform duration-300 group-hover:translate-x-0.5"
                     fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </button>

            <!-- 悬浮提示增强 -->
            <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
              <div class="px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap">
                {{ showArchived ? '查看活跃对话' : '查看归档记录' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态指示条 -->
      <div class="mt-2 h-0.5 bg-gray-200 rounded-full overflow-hidden">
        <div class="h-full transition-all duration-500 rounded-full"
             :class="showArchived ? 'bg-amber-400' : 'bg-blue-400'"
             :style="{ width: showArchived ? '60%' : '40%' }">
        </div>
      </div>
    </div>

    <!-- 对话记录列表 -->
    <div class="flex-1 overflow-y-auto" style="padding-bottom: 60px;">
      <!-- 加载状态 -->
      <div v-if="loadingConversations" class="flex flex-col items-center justify-center py-12 px-4">
        <div class="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
        <p class="text-sm text-gray-600">正在加载对话记录...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="conversationError" class="flex flex-col items-center justify-center py-12 px-4">
        <div class="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mb-4">
          <span class="text-2xl">⚠️</span>
        </div>
        <h3 class="text-sm font-medium text-red-700 mb-2">加载失败</h3>
        <p class="text-xs text-red-500 text-center leading-relaxed mb-4">{{ conversationError }}</p>
        <button
          @click="fetchBackendConversations"
          class="px-4 py-2 bg-red-500 text-white text-xs rounded-lg hover:bg-red-600 transition-colors duration-200">
          重试
        </button>
      </div>

      <!-- 空状态显示 -->
      <div v-else-if="groupedConversations.length === 0" class="flex flex-col items-center justify-center py-12 px-4">
        <div class="w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300"
             :class="showArchived ? 'bg-amber-100' : 'bg-blue-100'">
          <span class="text-2xl">{{ showArchived ? '📁' : '💬' }}</span>
        </div>
        <h3 class="text-sm font-medium text-gray-700 mb-2">
          {{ showArchived ? '暂无归档对话' : '暂无对话记录' }}
        </h3>
        <p class="text-xs text-gray-500 text-center leading-relaxed">
          {{ showArchived ? '您还没有归档任何对话' : '开始您的第一次AI对话吧' }}
        </p>
        <button v-if="!showArchived"
                @click="createNewConversation"
                class="mt-4 px-4 py-2 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors duration-200">
          开始新对话
        </button>
      </div>

      <!-- 按时间分组显示对话 -->
      <div v-for="(group, groupIndex) in groupedConversations" :key="group.title"
           class="mb-4 animate-fade-in"
           :style="{ animationDelay: `${groupIndex * 50}ms` }">
        <!-- 分组标题 -->
        <div class="px-4 py-2 mb-2 bg-gradient-to-r from-gray-100/80 to-gray-50/50 border-b border-gray-200/30">
          <div class="flex items-center justify-between">
            <h5 class="text-xs font-medium text-gray-600 flex items-center gap-2">
              <span class="w-1.5 h-1.5 rounded-full"
                    :class="showArchived ? 'bg-amber-400' : 'bg-blue-400'"></span>
              {{ group.title }}
            </h5>
            <span class="text-xs text-gray-400 font-medium">
              {{ group.conversations.length }}
            </span>
          </div>
        </div>

        <!-- 对话列表 -->
        <div class="px-2">
          <div
            v-for="(conversation, index) in group.conversations"
            :key="conversation.id || 'new-conversation'"
            class="conversation-item relative py-1 px-3 mb-1 transition-all duration-300 cursor-pointer group rounded-lg animate-slide-in"
            :style="{ animationDelay: `${(groupIndex * 100) + (index * 30)}ms` }"
            :class="{
              // 新对话的选中样式
              'bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 shadow-sm': conversation.isNewConversation && isConversationSelected(conversation.id),
              // 普通对话的选中样式
              'bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 shadow-sm': !conversation.isNewConversation && isConversationSelected(conversation.id) && !showArchived,
              'bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200/50 shadow-sm': !conversation.isNewConversation && isConversationSelected(conversation.id) && showArchived,
              // 悬停样式
              'hover:bg-gray-50 hover:shadow-sm': !isConversationSelected(conversation.id) && !conversation.isNewConversation,
              'hover:bg-green-25 hover:shadow-sm': !isConversationSelected(conversation.id) && conversation.isNewConversation
            }"
            @click="handleSelectConversation(conversation.id)"
          >
            <!-- 选中指示器 -->
            <div
              v-if="isConversationSelected(conversation.id)"
              class="selected-indicator absolute left-0 top-0 bottom-0 w-1 rounded-l-lg transition-all duration-300"
              :class="{
                'bg-gradient-to-b from-green-500 to-emerald-600': conversation.isNewConversation,
                'bg-gradient-to-b from-amber-500 to-orange-500': !conversation.isNewConversation && showArchived,
                'bg-gradient-to-b from-blue-500 to-indigo-600': !conversation.isNewConversation && !showArchived
              }"
            ></div>

            <div class="flex items-center justify-between">
              <!-- 左侧：消息数量 + 标题 -->
              <div class="flex items-center gap-2 flex-1 min-w-0">
                <!-- 消息数量或新对话图标 -->
                <div class="flex-shrink-0">
                  <span v-if="conversation.isNewConversation"
                        class="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-green-600 bg-green-100 rounded-full">
                    ✨
                  </span>
                  <span v-else
                        class="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">
                    {{ conversation.messages.length }}
                  </span>
                </div>

                <!-- 对话标题 -->
                <div class="flex-1 min-w-0">
                  <div
                    v-if="editingConversationId === conversation.id"
                    class="flex items-center gap-1"
                  >
                    <input
                      v-model="editingTitle"
                      @keyup.enter="saveConversationTitle(conversation.id, ($event.target as HTMLInputElement).value)"
                      @keyup.esc="cancelEditTitle"
                      class="flex-1 px-1 py-0.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      ref="titleInput"
                    />
                    <button
                      @click="saveConversationTitle(conversation.id, editingTitle)"
                      class="flex items-center justify-center w-6 h-6 text-green-600 hover:text-green-700 hover:bg-green-50 rounded transition-colors duration-200"
                      title="确定"
                    >
                      <span class="text-sm font-bold">✓</span>
                    </button>
                    <button
                      @click="cancelEditTitle"
                      class="flex items-center justify-center w-6 h-6 text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors duration-200"
                      title="取消"
                    >
                      <span class="text-sm font-bold">✕</span>
                    </button>
                  </div>
                  <div v-else>
                    <h4
                      class="font-medium text-sm truncate transition-colors duration-200"
                      :class="{
                        // 新对话的标题样式
                        'text-green-800 font-semibold': conversation.isNewConversation && isConversationSelected(conversation.id),
                        'text-green-600': conversation.isNewConversation && !isConversationSelected(conversation.id),
                        // 普通对话的标题样式
                        'text-blue-800 font-semibold': !conversation.isNewConversation && isConversationSelected(conversation.id) && !showArchived,
                        'text-amber-800 font-semibold': !conversation.isNewConversation && isConversationSelected(conversation.id) && showArchived,
                        'text-gray-800': !conversation.isNewConversation && !isConversationSelected(conversation.id)
                      }"
                    >
                      {{ conversation.title }}
                    </h4>
                  </div>
                </div>
              </div>

              <!-- 右侧：下拉菜单（新对话不显示） -->
              <div v-if="!conversation.isNewConversation" class="flex items-center gap-2 flex-shrink-0">
                <!-- 下拉菜单 -->
                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <el-dropdown @command="handleConversationAction" trigger="hover" placement="bottom-end">
                  <button
                    @click.stop
                    class="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100 transition-colors duration-200"
                    title="更多操作"
                  >
                    <el-icon size="12"><MoreFilled /></el-icon>
                  </button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        :command="{ action: 'rename', id: conversation.id, title: conversation.title }"
                        icon="Edit"
                      >
                        重命名
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="{ action: 'archive', id: conversation.id }"
                        icon="FolderAdd"
                      >
                        归档
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="{ action: 'delete', id: conversation.id }"
                        icon="Delete"
                        class="text-red-600"
                      >
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 当前登录用户区域 - 固定在底部（仅使用者角色且直接访问时显示） -->
    <div v-if="shouldShowUserArea && currentUser" class="user-area absolute bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-gray-200/50 p-4">
      <div class="flex items-center justify-between">
        <!-- 左侧：用户信息 -->
        <div class="flex items-center gap-3 flex-1 min-w-0">
          <!-- 用户头像 -->
          <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-sm">
            {{ (currentUser.name || currentUser.username || '用户').charAt(0).toUpperCase() }}
          </div>
          <!-- 用户信息 -->
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 truncate">{{ currentUser.name || currentUser.username || '未知用户' }}</div>
            <div class="text-xs text-gray-500 truncate">{{ currentUser.email || '未设置邮箱' }}</div>
          </div>
        </div>

        <!-- 右侧：更多菜单 -->
        <el-dropdown @command="handleUserAction" trigger="click" placement="top-end">
          <div class="w-9 h-9 flex items-center justify-center rounded-lg hover:bg-gray-100/80 cursor-pointer transition-all duration-200 hover:shadow-sm">
            <el-icon class="text-gray-600 hover:text-gray-800" size="18">
              <MoreFilled />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="min-w-36">
              <el-dropdown-item command="profile" class="flex items-center gap-2 py-2">
                <el-icon><User /></el-icon>
                <span>个人信息</span>
              </el-dropdown-item>
              <el-dropdown-item command="help" class="flex items-center gap-2 py-2">
                <el-icon><QuestionFilled /></el-icon>
                <span>帮助</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout" class="flex items-center gap-2 py-2 text-red-600 hover:bg-red-50">
                <el-icon><SwitchButton /></el-icon>
                <span>退出</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div
      v-if="showDeleteConfirm"
      class="fixed inset-0 bg-transparent flex items-center justify-center z-[9999]"
      @click="cancelDelete"
      style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100vw; height: 100vh;"
    >
      <div
        class="bg-white rounded-xl shadow-2xl border border-gray-200 p-6 w-full max-w-md mx-4 relative z-[10000]"
        @click.stop
        style="min-width: 320px;"
      >
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <span class="text-2xl">⚠️</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-800 mb-2">删除对话</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            确定要删除这个对话吗？<br>
            <span class="text-red-600 text-sm font-medium">此操作无法撤销</span>
          </p>
          <div class="flex gap-3">
            <button
              @click="cancelDelete"
              class="flex-1 px-4 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 font-medium"
            >
              取消
            </button>
            <button
              @click="confirmDelete"
              class="flex-1 px-4 py-2.5 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 font-medium"
            >
              确定删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人信息弹窗 -->
    <ProfileModal
      v-if="currentUser"
      v-model:visible="showProfileModalVisible"
      :user="currentUser"
      @edit="handleEditProfile"
    />

    <!-- 帮助弹窗 -->
    <HelpModal
      v-model:visible="showHelpModalVisible"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import {
  MoreFilled,
  User,
  QuestionFilled,
  SwitchButton
} from '@element-plus/icons-vue'
import { useConversationStore } from '@/stores/conversationStore'
import { useAuthStore } from '@/stores/authStore'
import { exploreApi, type ConversationInfo } from '@/api/explore'
import ProfileModal from '@/views/aiExplore/ProfileModal.vue'
import HelpModal from '@/components/HelpModal.vue'

// 使用对话存储
const conversationStore = useConversationStore()
const {
  currentConversationId,
  activeConversations,
  archivedConversations,
  activeGroupedConversations,
  archivedGroupedConversations
} = storeToRefs(conversationStore)

const {
  createNewConversation,
  selectConversation,
  deleteConversation,
  archiveConversation,
  updateConversationTitle
} = conversationStore

// 使用认证存储（整合了用户管理功能）
const authStore = useAuthStore()
const { currentUser, shouldShowUserArea } = storeToRefs(authStore)
const { logout } = authStore

// Props 接口
interface Props {
  appId?: string // 智能体应用ID，用于获取对话记录
}

const props = defineProps<Props>()

// 简化的 Emits - 只保留必要的跨组件通信
const emit = defineEmits<{
  'hide-sidebar': []
  'conversation-selected': [id: string]
}>()

// 内部状态管理
const showArchived = ref(false)
const editingConversationId = ref<string | null>(null)
const editingTitle = ref('')
const showDeleteConfirm = ref(false)
const deleteConversationId = ref<string | null>(null)

// 后端对话记录状态
const backendConversations = ref<ConversationInfo[]>([])
const loadingConversations = ref(false)
const conversationError = ref('')
// 当前选中的后端对话记录ID（用于显示选中效果）
const selectedBackendConversationId = ref<string | null>(null)
// 手动创建的新对话项状态
const manualNewConversations = ref<Array<{
  id: string
  title: string
  createdAt: Date
  updatedAt: Date
  messages: any[]
  archived: boolean
  isNewConversation: boolean
}>>([])
// 新对话计数器，用于生成唯一的临时ID
const newConversationCounter = ref(0)

// 将后端对话记录转换为本地格式
const convertBackendConversations = (backendConvs: ConversationInfo[]) => {
  return backendConvs.map(conv => ({
    id: conv.id,
    title: conv.name,
    createdAt: new Date(conv.createdAt * 1000), // 转换秒级时间戳为毫秒
    updatedAt: new Date(conv.updatedAt * 1000),
    messages: [], // 后端对话记录不包含消息内容
    archived: false // 默认为非归档状态
  }))
}

// 按时间分组后端对话记录
const groupBackendConversations = (conversations: any[]) => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
  const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

  const groups = [
    { title: '今天', conversations: [] as any[] },
    { title: '昨天', conversations: [] as any[] },
    { title: '7天内', conversations: [] as any[] },
    { title: '30天内', conversations: [] as any[] }
  ]

  const monthGroups: { [key: string]: any[] } = {}

  conversations.forEach(conv => {
    const convDate = new Date(conv.updatedAt)
    const convDateOnly = new Date(convDate.getFullYear(), convDate.getMonth(), convDate.getDate())

    if (convDateOnly.getTime() === today.getTime()) {
      groups[0].conversations.push(conv)
    } else if (convDateOnly.getTime() === yesterday.getTime()) {
      groups[1].conversations.push(conv)
    } else if (convDate >= sevenDaysAgo) {
      groups[2].conversations.push(conv)
    } else if (convDate >= thirtyDaysAgo) {
      groups[3].conversations.push(conv)
    } else {
      // 超过30天的按月分组
      const monthKey = convDate.getFullYear() + '年' + (convDate.getMonth() + 1) + '月'
      if (!monthGroups[monthKey]) {
        monthGroups[monthKey] = []
      }
      monthGroups[monthKey].push(conv)
    }
  })

  // 添加月份分组
  Object.keys(monthGroups).sort().reverse().forEach(monthKey => {
    groups.push({
      title: monthKey,
      conversations: monthGroups[monthKey].sort((a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      )
    })
  })

  // 过滤掉空的分组
  return groups.filter(group => group.conversations.length > 0)
}

// 计算属性 - 根据当前视图状态返回正确的分组对话数据
const groupedConversations = computed(() => {
  // 如果有appId，显示后端对话记录（包括空状态）
  if (props.appId) {
    const convertedConversations = convertBackendConversations(backendConversations.value)
    let groupedResult = groupBackendConversations(convertedConversations)

    // 合并手动创建的新对话项
    const allManualNewConversations = [...manualNewConversations.value]

    // 处理新对话的显示逻辑
    if (manualNewConversations.value.length > 0) {
      // 如果有手动创建的新对话，将它们添加到"今天"分组
      // 查找是否已有"今天"分组
      let todayGroup = groupedResult.find(group => group.title === '今天')
      if (todayGroup) {
        // 如果已有"今天"分组，将新对话添加到开头
        todayGroup.conversations.unshift(...allManualNewConversations)
      } else {
        // 如果没有"今天"分组，创建一个新的"今天"分组
        const newTodayGroup = {
          title: '今天',
          conversations: allManualNewConversations
        }
        groupedResult.unshift(newTodayGroup)
      }
    } else if (backendConversations.value.length === 0) {
      // 只有在没有后端对话记录且没有手动创建的新对话时，才显示默认的"新对话"
      groupedResult = [{
        title: '今天',
        conversations: [{
          id: '', // 空ID表示新对话
          title: '新对话',
          createdAt: new Date(),
          updatedAt: new Date(),
          messages: [],
          archived: false,
          isNewConversation: true // 标记为新对话
        }]
      }]
    }

    console.log('ConversationSidebar 后端对话记录:', {
      appId: props.appId,
      backendConversationsCount: backendConversations.value.length,
      convertedCount: convertedConversations.length,
      manualNewConversationsCount: manualNewConversations.value.length,
      groupedCount: groupedResult.length,
      showArchived: showArchived.value,
      hasNewConversation: backendConversations.value.length === 0 || manualNewConversations.value.length > 0
    })
    return groupedResult
  }

  // 否则显示本地对话记录
  const localResult = showArchived.value ? archivedGroupedConversations.value : activeGroupedConversations.value
  console.log('ConversationSidebar 本地对话记录:', {
    appId: props.appId,
    showArchived: showArchived.value,
    localCount: localResult.length
  })
  return localResult
})

// 内部事件处理函数
const toggleArchiveView = () => {
  showArchived.value = !showArchived.value
}

// 处理新建对话按钮点击
const handleCreateNewConversation = () => {
  if (props.appId) {
    // 如果有appId，在对话记录列表中添加新对话项
    newConversationCounter.value++
    const newConversationId = `new-${newConversationCounter.value}-${Date.now()}`

    const newConversation = {
      id: newConversationId,
      title: '新对话',
      createdAt: new Date(),
      updatedAt: new Date(),
      messages: [],
      archived: false,
      isNewConversation: true
    }

    // 添加到手动创建的新对话列表
    manualNewConversations.value.unshift(newConversation)

    // 自动选中新创建的对话
    selectedBackendConversationId.value = newConversationId

    console.log('ConversationSidebar: 创建新对话项:', {
      appId: props.appId,
      newConversationId: newConversationId,
      manualNewConversationsCount: manualNewConversations.value.length
    })

    // 发送事件通知父组件
    emit('conversation-selected', newConversationId)
  } else {
    // 如果没有appId，使用原有的本地对话创建逻辑
    createNewConversation()
  }
}

// 判断对话是否被选中
const isConversationSelected = (conversationId: string) => {
  // 如果有appId，说明是后端对话记录，使用后端选中状态
  if (props.appId) {
    // 特殊处理：如果是新对话（ID为空或以new-开头）
    if (conversationId === '' || conversationId.startsWith('new-')) {
      // 如果没有其他对话记录且没有手动创建的新对话，默认选中空ID的新对话
      if (conversationId === '' && backendConversations.value.length === 0 && manualNewConversations.value.length === 0) {
        return selectedBackendConversationId.value === '' || selectedBackendConversationId.value === null
      }
      // 否则精确匹配ID
      return selectedBackendConversationId.value === conversationId
    }
    return selectedBackendConversationId.value === conversationId
  } else {
    // 否则是本地对话记录，使用本地选中状态
    return currentConversationId.value === conversationId
  }
}

const handleSelectConversation = (conversationId: string) => {
  // 如果有appId，说明是后端对话记录，更新后端选中状态
  if (props.appId) {
    selectedBackendConversationId.value = conversationId
    console.log('ConversationSidebar: 选中后端对话记录:', {
      appId: props.appId,
      conversationId: conversationId,
      selectedBackendConversationId: selectedBackendConversationId.value,
      isNewConversation: conversationId === ''
    })
  } else {
    // 否则是本地对话记录，使用原有逻辑
    selectConversation(conversationId)
    console.log('ConversationSidebar: 选中本地对话记录:', conversationId)
  }

  console.log('ConversationSidebar: 发送conversation-selected事件:', conversationId)
  emit('conversation-selected', conversationId)
}

const startEditTitle = (conversationId: string, currentTitle: string) => {
  editingConversationId.value = conversationId
  editingTitle.value = currentTitle

  nextTick(() => {
    const input = document.querySelector('input[ref="titleInput"]') as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

const saveConversationTitle = async (conversationId: string, title?: string) => {
  const titleToSave = title || editingTitle.value.trim()
  if (!titleToSave) {
    cancelEditTitle()
    return
  }

  try {
    // 如果有appId，说明是后端对话记录，需要调用后端API
    if (props.appId) {
      console.log('ConversationSidebar: 重命名后端对话记录:', {
        appId: props.appId,
        conversationId: conversationId,
        newTitle: titleToSave
      })

      // 调用后端API重命名对话
      const response = await exploreApi.renameConversation(props.appId, conversationId, titleToSave)
      console.log('ConversationSidebar: 重命名成功:', response)

      // 更新本地状态
      // 1. 更新后端对话记录列表中的标题
      const conversation = backendConversations.value.find(conv => conv.id === conversationId)
      if (conversation) {
        conversation.name = titleToSave
        console.log('ConversationSidebar: 更新后端对话记录标题成功')
      }

      // 2. 更新手动创建的新对话列表中的标题（如果存在）
      const manualConversation = manualNewConversations.value.find(conv => conv.id === conversationId)
      if (manualConversation) {
        manualConversation.title = titleToSave
        console.log('ConversationSidebar: 更新手动创建对话记录标题成功')
      }

      // 3. 如果是当前选中的对话，也更新本地对话存储中的标题
      updateConversationTitle(conversationId, titleToSave)

    } else {
      // 本地对话记录，使用原有逻辑
      console.log('ConversationSidebar: 重命名本地对话记录:', {
        conversationId: conversationId,
        newTitle: titleToSave
      })
      updateConversationTitle(conversationId, titleToSave)
    }

    cancelEditTitle()
  } catch (error) {
    console.error('ConversationSidebar: 重命名对话失败:', error)
    // 显示错误提示
    ElMessage.error('重命名失败，请重试')
    // 不取消编辑状态，让用户可以重试
  }
}

const cancelEditTitle = () => {
  editingConversationId.value = null
  editingTitle.value = ''
}

const confirmDeleteConversation = (conversationId: string) => {
  deleteConversationId.value = conversationId
  showDeleteConfirm.value = true
}

const cancelDelete = () => {
  showDeleteConfirm.value = false
  deleteConversationId.value = null
}

// 获取后端对话记录
const fetchBackendConversations = async () => {
  if (!props.appId) {
    console.log('ConversationSidebar: 没有提供appId，跳过获取后端对话记录')
    // 清空后端对话记录和选中状态
    backendConversations.value = []
    selectedBackendConversationId.value = null
    // 清空右侧聊天窗口
    emit('conversation-selected', '')
    return
  }

  loadingConversations.value = true
  conversationError.value = ''

  try {
    console.log('ConversationSidebar: 开始获取后端对话记录，appId:', props.appId)
    const conversations = await exploreApi.getConversations(props.appId, {
      limit: 100,
      pinned: false
    })

    console.log('ConversationSidebar: 获取后端对话记录成功:', conversations)
    console.log('ConversationSidebar: 对话记录数量:', conversations.length)
    console.log('ConversationSidebar: 是否为空数组:', Array.isArray(conversations) && conversations.length === 0)
    backendConversations.value = conversations

    // 自动选中逻辑
    if (conversations && conversations.length > 0) {
      // 如果有对话记录，检查当前选中的ID是否在对话列表中
      if (selectedBackendConversationId.value &&
          !selectedBackendConversationId.value.startsWith('new-') &&
          conversations.find(conv => conv.id === selectedBackendConversationId.value)) {
        // 如果当前选中的ID在对话列表中，保持选中状态
        console.log('ConversationSidebar: 保持当前选中的对话:', selectedBackendConversationId.value)
      } else if (!selectedBackendConversationId.value || selectedBackendConversationId.value.startsWith('new-')) {
        // 如果没有选中项或选中的是新对话，自动选中第一条
        const firstConversation = conversations[0]
        console.log('ConversationSidebar: 自动选中第一条对话记录:', firstConversation.id)
        // 延迟一点时间确保UI更新完成
        setTimeout(() => {
          handleSelectConversation(firstConversation.id)
        }, 100)
      }
    } else {
      console.log('ConversationSidebar: 没有对话记录，自动选中新对话')
      // 没有对话记录时，只在没有手动创建的新对话时才自动选中默认新对话
      if (manualNewConversations.value.length === 0) {
        selectedBackendConversationId.value = ''
        // 延迟一点时间确保UI更新完成
        setTimeout(() => {
          emit('conversation-selected', '')
        }, 100)
      }
    }
  } catch (error: any) {
    console.error('ConversationSidebar: 获取后端对话记录失败:', error)
    conversationError.value = error.message || '获取对话记录失败'
    // 出错时也清空对话记录、选中状态和右侧聊天窗口
    backendConversations.value = []
    selectedBackendConversationId.value = null
    emit('conversation-selected', '')
  } finally {
    loadingConversations.value = false
  }
}

const confirmDelete = async () => {
  if (!deleteConversationId.value) {
    showDeleteConfirm.value = false
    return
  }

  try {
    // 如果有appId，说明是后端对话记录，需要调用后端API
    if (props.appId) {
      console.log('ConversationSidebar: 删除后端对话记录:', {
        appId: props.appId,
        conversationId: deleteConversationId.value
      })

      // 调用后端API删除对话
      await exploreApi.deleteConversation(props.appId, deleteConversationId.value)
      console.log('ConversationSidebar: 删除成功')

      // 从本地状态中移除对话记录
      // 1. 从后端对话记录列表中移除
      const backendIndex = backendConversations.value.findIndex(conv => conv.id === deleteConversationId.value)
      if (backendIndex !== -1) {
        backendConversations.value.splice(backendIndex, 1)
        console.log('ConversationSidebar: 从后端对话记录列表中移除成功')
      }

      // 2. 从手动创建的新对话列表中移除（如果存在）
      const manualIndex = manualNewConversations.value.findIndex(conv => conv.id === deleteConversationId.value)
      if (manualIndex !== -1) {
        manualNewConversations.value.splice(manualIndex, 1)
        console.log('ConversationSidebar: 从手动创建对话记录列表中移除成功')
      }

      // 3. 如果删除的是当前选中的对话，需要选中其他对话或清空选中状态
      if (selectedBackendConversationId.value === deleteConversationId.value) {
        // 尝试选中第一个可用的对话
        const remainingConversations = [...backendConversations.value, ...manualNewConversations.value]
        if (remainingConversations.length > 0) {
          const nextConversation = remainingConversations[0]
          selectedBackendConversationId.value = nextConversation.id
          emit('conversation-selected', nextConversation.id)
          console.log('ConversationSidebar: 自动选中下一个对话:', nextConversation.id)
        } else {
          // 没有其他对话，清空选中状态
          selectedBackendConversationId.value = null
          emit('conversation-selected', '')
          console.log('ConversationSidebar: 清空选中状态')
        }
      }

      // 4. 同时删除本地对话存储中的记录（如果存在）
      deleteConversation(deleteConversationId.value)

    } else {
      // 本地对话记录，使用原有逻辑
      console.log('ConversationSidebar: 删除本地对话记录:', deleteConversationId.value)
      deleteConversation(deleteConversationId.value)
    }

    // 显示成功提示
    ElMessage.success('对话删除成功')

  } catch (error) {
    console.error('ConversationSidebar: 删除对话失败:', error)
    // 显示错误提示
    ElMessage.error('删除失败，请重试')
  } finally {
    // 关闭确认弹窗
    showDeleteConfirm.value = false
    deleteConversationId.value = null
  }
}

const handleArchiveConversation = (conversationId: string) => {
  // 如果当前选中的对话被归档，切换到其他对话
  if (currentConversationId.value === conversationId) {
    const activeConvs = activeConversations.value.filter(c => c.id !== conversationId)
    if (activeConvs.length > 0) {
      handleSelectConversation(activeConvs[0].id)
    } else {
      selectConversation('')
    }
  }

  archiveConversation(conversationId)
  console.log('已归档对话')
}

const handleConversationAction = (command: { action: string, id: string, title?: string }) => {
  switch (command.action) {
    case 'rename':
      startEditTitle(command.id, command.title || '')
      break
    case 'archive':
      handleArchiveConversation(command.id)
      break
    case 'delete':
      confirmDeleteConversation(command.id)
      break
  }
}

const handleUserAction = (command: string) => {
  switch (command) {
    case 'profile':
      showProfileModal()
      break
    case 'help':
      showHelpModal()
      break
    case 'logout':
      logout()
      break
  }
}

// 弹窗状态
const showProfileModalVisible = ref(false)
const showHelpModalVisible = ref(false)

const showProfileModal = () => {
  showProfileModalVisible.value = true
}

const showHelpModal = () => {
  showHelpModalVisible.value = true
}

const handleEditProfile = () => {
  showProfileModalVisible.value = false
  // 这里可以打开编辑弹窗或跳转到编辑页面
  console.log('编辑个人资料')
}

// 更新选中的对话ID（供外部调用）
const updateSelectedConversationId = (conversationId: string) => {
  console.log('ConversationSidebar: 更新选中的对话ID:', conversationId)
  selectedBackendConversationId.value = conversationId

  // 如果是真实对话ID，需要从手动创建的新对话列表中移除对应项
  if (conversationId && !conversationId.startsWith('new-')) {
    // 移除所有手动创建的新对话项，因为现在有了真实的对话ID
    manualNewConversations.value = []
    console.log('清空手动创建的新对话列表，因为现在有了真实对话ID')
  }

  // 发送事件通知父组件对话选择变化（但不触发重新加载消息）
  console.log('ConversationSidebar: 发送conversation-selected事件（仅更新选中状态）:', conversationId)
  // 这里不调用 emit，因为我们只是更新选中状态，不需要重新加载对话内容
}

// 更新新对话记录的ID和名称（供外部调用）
const updateNewConversationInfo = (oldId: string, newId: string, newName: string) => {
  console.log('ConversationSidebar: 更新新对话记录信息:', {
    oldId,
    newId,
    newName
  })

  let foundAndUpdated = false

  // 1. 查找并更新手动创建的新对话项
  const newConversationIndex = manualNewConversations.value.findIndex(conv => conv.id === oldId)
  if (newConversationIndex !== -1) {
    // 更新新对话的ID和名称
    manualNewConversations.value[newConversationIndex].id = newId
    manualNewConversations.value[newConversationIndex].title = newName
    manualNewConversations.value[newConversationIndex].isNewConversation = false // 标记为非新对话
    console.log('更新手动创建的新对话项成功')
    foundAndUpdated = true
  }

  // 2. 如果没有找到手动创建的新对话项，且oldId为空，说明是默认的新对话
  if (!foundAndUpdated && oldId === '') {
    // 对于默认的新对话（空ID），我们创建一个新的对话项来替代它
    // 这样在UI中就会显示真实的对话记录而不是默认的新对话
    const newConversationItem = {
      id: newId,
      title: newName,
      createdAt: new Date(),
      updatedAt: new Date(),
      messages: [],
      archived: false,
      isNewConversation: false
    }

    // 将新对话项添加到手动创建的新对话列表的开头
    manualNewConversations.value.unshift(newConversationItem)
    console.log('为默认新对话创建真实对话项')
    foundAndUpdated = true
  }

  // 3. 更新选中状态
  if (selectedBackendConversationId.value === oldId ||
      selectedBackendConversationId.value === null ||
      selectedBackendConversationId.value === '') {
    selectedBackendConversationId.value = newId
    console.log('更新选中的对话ID为新ID:', newId)
  }

  if (!foundAndUpdated) {
    console.warn('未找到要更新的新对话项:', { oldId, newId, newName })
  }
}

// 获取当前选中的对话ID（供外部调用）
const getCurrentSelectedId = () => {
  console.log('ConversationSidebar: 获取当前选中的对话ID:', selectedBackendConversationId.value)
  return selectedBackendConversationId.value || ''
}

// 暴露方法给父组件
defineExpose({
  refreshConversations: fetchBackendConversations,
  updateSelectedConversationId,
  updateNewConversationInfo,
  getCurrentSelectedId
})

// 监听appId变化，自动获取对话记录
watch(() => props.appId, (newAppId) => {
  console.log('appId变化:', newAppId)
  // 清空手动创建的新对话列表
  manualNewConversations.value = []
  selectedBackendConversationId.value = null

  if (newAppId) {
    fetchBackendConversations()
  } else {
    // 清空后端对话记录
    backendConversations.value = []
    conversationError.value = ''
  }
}, { immediate: true })

// 组件挂载时获取对话记录
onMounted(() => {
  if (props.appId) {
    fetchBackendConversations()
  }
})


</script>

<style scoped>
/* 思考动画相关样式 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-slide-in {
  animation: fade-in 0.3s ease-out;
}

/* 对话项选中效果增强 */
.conversation-item {
  position: relative;
  overflow: hidden;
}

/* 选中状态的微妙动画效果 */
.conversation-item.selected {
  transform: translateX(2px);
}

/* 选中指示器动画 */
.conversation-item .selected-indicator {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 4px;
    opacity: 1;
  }
}

/* 选中状态的发光效果 */
.conversation-item:has(.selected-indicator) {
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* 悬停效果增强 */
.conversation-item:hover {
  transform: translateX(1px);
}

.conversation-item:hover:not(:has(.selected-indicator)) {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 用户区域样式 */
.user-area {
  z-index: 30;
}

/* 移动端用户区域优化 */
@media (max-width: 768px) {
  .user-area {
    padding: 12px 16px;
  }

  .user-area .flex {
    gap: 8px;
  }

  .user-area .w-8 {
    width: 32px;
    height: 32px;
  }

  .user-area .w-9 {
    width: 36px;
    height: 36px;
  }

  .user-area .text-sm {
    font-size: 13px;
  }

  .user-area .text-xs {
    font-size: 11px;
  }
}
</style>
