package com.xhcai.modules.rag.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.rag.dto.DocumentPermissionDTO;
import com.xhcai.modules.rag.entity.DocumentPermission;

import java.util.List;
import java.util.Set;

/**
 * 文档权限服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDocumentPermissionService extends IService<DocumentPermission> {

    /**
     * 设置文档权限
     */
    void setDocumentPermission(DocumentPermissionDTO permissionDTO);

    /**
     * 获取文档权限列表
     */
    List<DocumentPermission> getDocumentPermissions(String documentId);

    /**
     * 批量获取文档权限
     */
    List<DocumentPermission> getDocumentPermissions(List<String> documentIds);

    /**
     * 删除文档权限
     */
    void deleteDocumentPermissions(String documentId);

    /**
     * 批量删除文档权限
     */
    void deleteDocumentPermissions(List<String> documentIds);

    /**
     * 检查用户是否有文档访问权限
     */
    boolean hasDocumentAccess(String documentId, String userId);

    /**
     * 检查用户是否有文档访问权限（指定角色和部门）
     */
    boolean hasDocumentAccess(String documentId, String userId, Set<String> roleIds, String departmentId);

    /**
     * 获取用户可访问的文档ID列表
     */
    List<String> getAccessibleDocumentIds(List<String> documentIds, String userId);
}
