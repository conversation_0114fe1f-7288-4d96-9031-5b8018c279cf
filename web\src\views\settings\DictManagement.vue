<template>
  <div class="dict-management h-full flex">
    <!-- 左侧字典类型列表 -->
    <div class="dict-type-panel w-1/3 bg-white rounded-lg shadow-sm border border-gray-200 mr-4">
      <div class="panel-header p-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">字典类型</h3>
          <button
            @click="showDictTypeForm = true"
            class="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors"
          >
            <span class="mr-1">+</span>新增
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="mt-3">
          <input
            v-model="dictTypeQuery.dictName"
            @input="searchDictTypes"
            type="text"
            placeholder="搜索字典类型..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div class="panel-content p-4 overflow-y-auto" style="height: calc(100vh - 280px);">
        <div class="space-y-2">
          <div
            v-for="dictType in dictTypes"
            :key="dictType.id"
            @click="selectDictType(dictType)"
            class="dict-type-item p-3 rounded-lg border cursor-pointer transition-all"
            :class="{
              'border-blue-500 bg-blue-50': selectedDictType?.id === dictType.id,
              'border-gray-200 hover:border-gray-300 hover:bg-gray-50': selectedDictType?.id !== dictType.id
            }"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="font-medium text-gray-900">{{ dictType.dictName }}</div>
                <div class="text-sm text-gray-500">{{ dictType.dictType }}</div>
                <div v-if="dictType.remark" class="text-xs text-gray-400 mt-1">{{ dictType.remark }}</div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  v-if="dictType.isSystemDict === 'Y'"
                  class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800"
                  title="系统字典"
                >
                  系统
                </span>
                <span
                  class="px-2 py-1 text-xs rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': dictType.status === '0',
                    'bg-red-100 text-red-800': dictType.status === '1'
                  }"
                >
                  {{ dictType.status === '0' ? '正常' : '停用' }}
                </span>
                <div class="flex space-x-1" v-if="dictType.isSystemDict !== 'Y'">
                  <button
                    @click.stop="editDictType(dictType)"
                    class="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                    title="编辑"
                  >
                    ✏️
                  </button>
                  <button
                    @click.stop="deleteDictType(dictType)"
                    class="p-1 text-gray-400 hover:text-red-500 transition-colors"
                    title="删除"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧字典数据列表 -->
    <div class="dict-data-panel flex-1 bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="panel-header p-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">
            字典数据
            <span v-if="selectedDictType" class="text-sm text-gray-500 ml-2">
              ({{ selectedDictType.dictName }})
            </span>
          </h3>
          <button
            v-if="selectedDictType"
            @click="showDictDataForm = true"
            class="px-3 py-1.5 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors"
          >
            <span class="mr-1">+</span>新增数据
          </button>
        </div>

        <!-- 搜索框 -->
        <div v-if="selectedDictType" class="mt-3">
          <input
            v-model="dictDataQuery.dictLabel"
            @input="searchDictData"
            type="text"
            placeholder="搜索字典数据..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div class="panel-content p-4 overflow-y-auto" style="height: calc(100vh - 280px);">
        <div v-if="!selectedDictType" class="flex items-center justify-center h-full text-gray-500">
          <div class="text-center">
            <div class="text-4xl mb-4">📚</div>
            <div>请选择左侧字典类型查看数据</div>
          </div>
        </div>

        <div v-else-if="dictData.length === 0" class="flex items-center justify-center h-full text-gray-500">
          <div class="text-center">
            <div class="text-4xl mb-4">📝</div>
            <div>暂无字典数据</div>
          </div>
        </div>

        <div v-else class="space-y-2">
          <div
            v-for="data in dictData"
            :key="data.id"
            class="dict-data-item p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <div class="font-medium text-gray-900">{{ data.dictLabel }}</div>
                  <div class="text-sm text-gray-500">值: {{ data.dictValue }}</div>
                  <div v-if="data.dictSort" class="text-xs text-gray-400">排序: {{ data.dictSort }}</div>
                </div>
                <div v-if="data.remark" class="text-xs text-gray-400 mt-1">{{ data.remark }}</div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  v-if="data.isSystemDict === 'Y'"
                  class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800"
                  title="系统字典"
                >
                  系统
                </span>
                <span
                  class="px-2 py-1 text-xs rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': data.status === '0',
                    'bg-red-100 text-red-800': data.status === '1'
                  }"
                >
                  {{ data.status === '0' ? '正常' : '停用' }}
                </span>
                <div class="flex space-x-1" v-if="data.isSystemDict !== 'Y'">
                  <button
                    @click="editDictData(data)"
                    class="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                    title="编辑"
                  >
                    ✏️
                  </button>
                  <button
                    @click="deleteDictData(data)"
                    class="p-1 text-gray-400 hover:text-red-500 transition-colors"
                    title="删除"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 字典类型表单弹窗 -->
    <DictTypeForm
      v-if="showDictTypeForm"
      :dict-type="editingDictType"
      @close="closeDictTypeForm"
      @success="handleDictTypeSuccess"
    />

    <!-- 字典数据表单弹窗 -->
    <DictDataForm
      v-if="showDictDataForm"
      :dict-data="editingDictData"
      :dict-type="selectedDictType?.dictType"
      @close="closeDictDataForm"
      @success="handleDictDataSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DictAPI } from '@/api/dict'
import type { SysDictTypeVO, SysDictDataVO, DictTypeQueryDTO, DictDataQueryDTO } from '@/types/system'
import DictTypeForm from './components/DictTypeForm.vue'
import DictDataForm from './components/DictDataForm.vue'

// 为了与后端保持一致，创建类型别名
type SysDictVO = SysDictTypeVO

// 响应式数据
const dictTypes = ref<SysDictVO[]>([])
const dictData = ref<SysDictDataVO[]>([])
const selectedDictType = ref<SysDictVO | null>(null)
const showDictTypeForm = ref(false)
const showDictDataForm = ref(false)
const editingDictType = ref<SysDictVO | null>(null)
const editingDictData = ref<SysDictDataVO | null>(null)

// 查询参数
const dictTypeQuery = reactive<DictTypeQueryDTO>({
  dictName: '',
  pageNum: 1,
  pageSize: 100
})

const dictDataQuery = reactive<DictDataQueryDTO>({
  dictType: '',
  dictLabel: '',
  pageNum: 1,
  pageSize: 100
})

// 加载字典类型列表
const loadDictTypes = async () => {
  try {
    const response = await DictAPI.getDictTypeList(dictTypeQuery)
    if (response.success && response.data) {
      dictTypes.value = response.data.records || []

      // 默认加载第一个字典类型数据
      selectedDictType.value = dictTypes.value[0]
      loadDictData()
    }
  } catch (error) {
    console.error('加载字典类型失败:', error)
    ElMessage.error('加载字典类型失败')
  }
}

// 加载字典数据列表
const loadDictData = async () => {
  if (!selectedDictType.value) {
    dictData.value = []
    return
  }

  try {
    dictDataQuery.dictType = selectedDictType.value.dictType
    const response = await DictAPI.getDictDataList(dictDataQuery)
    if (response.success && response.data) {
      dictData.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
    ElMessage.error('加载字典数据失败')
  }
}

// 选择字典类型
const selectDictType = (dictType: SysDictVO) => {
  selectedDictType.value = dictType
  dictDataQuery.dictLabel = ''
  loadDictData()
}

// 搜索字典类型
const searchDictTypes = () => {
  loadDictTypes()
}

// 搜索字典数据
const searchDictData = () => {
  loadDictData()
}

// 编辑字典类型
const editDictType = (dictType: SysDictVO) => {
  editingDictType.value = dictType
  showDictTypeForm.value = true
}

// 删除字典类型
const deleteDictType = async (dictType: SysDictVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字典类型"${dictType.dictName}"吗？删除后该类型下的所有字典数据也将被删除！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DictAPI.deleteDictType(dictType.id)
    ElMessage.success('删除成功')
    
    // 如果删除的是当前选中的类型，清空选择
    if (selectedDictType.value?.id === dictType.id) {
      selectedDictType.value = null
      dictData.value = []
    }
    
    loadDictTypes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除字典类型失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 编辑字典数据
const editDictData = (data: SysDictDataVO) => {
  editingDictData.value = data
  showDictDataForm.value = true
}

// 删除字典数据
const deleteDictData = async (data: SysDictDataVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字典数据"${data.dictLabel}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DictAPI.deleteDictData(data.id)
    ElMessage.success('删除成功')
    loadDictData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除字典数据失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 关闭字典类型表单
const closeDictTypeForm = () => {
  showDictTypeForm.value = false
  editingDictType.value = null
}

// 关闭字典数据表单
const closeDictDataForm = () => {
  showDictDataForm.value = false
  editingDictData.value = null
}

// 字典类型操作成功
const handleDictTypeSuccess = () => {
  closeDictTypeForm()
  loadDictTypes()
}

// 字典数据操作成功
const handleDictDataSuccess = () => {
  closeDictDataForm()
  loadDictData()
}

// 监听选中的字典类型变化
watch(selectedDictType, () => {
  loadDictData()
})

// 组件挂载时加载数据
onMounted(() => {
  loadDictTypes()
})
</script>

<style scoped>
.dict-management {
  height: calc(100vh - 140px);
}

.panel-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.dict-type-item:hover,
.dict-data-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dict-type-item.border-blue-500 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
}
</style>
