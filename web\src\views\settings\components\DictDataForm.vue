<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEdit ? '编辑字典数据' : '新增字典数据' }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          ✕
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            字典类型
          </label>
          <input
            :value="dictType"
            type="text"
            disabled
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            数据标签 <span class="text-red-500">*</span>
          </label>
          <input
            v-model="form.dictLabel"
            type="text"
            required
            placeholder="请输入数据标签"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            数据键值 <span class="text-red-500">*</span>
          </label>
          <input
            v-model="form.dictValue"
            type="text"
            required
            placeholder="请输入数据键值"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            显示排序
          </label>
          <input
            v-model.number="form.dictSort"
            type="number"
            min="0"
            placeholder="请输入显示排序"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            状态 <span class="text-red-500">*</span>
          </label>
          <select
            v-model="form.status"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="0">正常</option>
            <option value="1">停用</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            样式属性
          </label>
          <input
            v-model="form.cssClass"
            type="text"
            placeholder="请输入样式属性"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p class="text-xs text-gray-500 mt-1">用于前端显示的CSS类名</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            表格回显样式
          </label>
          <select
            v-model="form.listClass"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">默认</option>
            <option value="default">默认</option>
            <option value="primary">主要</option>
            <option value="success">成功</option>
            <option value="info">信息</option>
            <option value="warning">警告</option>
            <option value="danger">危险</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            是否默认
          </label>
          <select
            v-model="form.isDefault"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="N">否</option>
            <option value="Y">是</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            备注
          </label>
          <textarea
            v-model="form.remark"
            rows="3"
            placeholder="请输入备注信息"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
          ></textarea>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50"
          >
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DictAPI } from '@/api/dict'
import type { SysDictDataVO, DictDataDTO } from '@/types/system'

// Props
interface Props {
  dictData?: SysDictDataVO | null
  dictType?: string
}

const props = withDefaults(defineProps<Props>(), {
  dictData: null,
  dictType: ''
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// 响应式数据
const loading = ref(false)
const form = reactive<DictDataDTO>({
  dictLabel: '',
  dictValue: '',
  dictType: '',
  dictSort: 0,
  status: '0',
  cssClass: '',
  listClass: '',
  isDefault: 'N',
  remark: ''
})

// 计算属性
const isEdit = computed(() => !!props.dictData)

// 初始化表单
const initForm = () => {
  if (props.dictData) {
    form.id = props.dictData.id
    form.dictLabel = props.dictData.dictLabel
    form.dictValue = props.dictData.dictValue
    form.dictType = props.dictData.dictType
    form.dictSort = props.dictData.dictSort || 0
    form.status = props.dictData.status
    form.cssClass = props.dictData.cssClass || ''
    form.listClass = props.dictData.listClass || ''
    form.isDefault = props.dictData.isDefault || 'N'
    form.remark = props.dictData.remark || ''
  } else {
    form.id = undefined
    form.dictLabel = ''
    form.dictValue = ''
    form.dictType = props.dictType || ''
    form.dictSort = 0
    form.status = '0'
    form.cssClass = ''
    form.listClass = ''
    form.isDefault = 'N'
    form.remark = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true

    if (isEdit.value) {
      await DictAPI.updateDictData(form.id!, form)
      ElMessage.success('更新成功')
    } else {
      await DictAPI.createDictData(form)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('保存字典数据失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时初始化表单
onMounted(() => {
  initForm()
})
</script>

<style scoped>
/* 动画效果 */
.fixed {
  animation: fadeIn 0.3s ease-out;
}

.bg-white {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
