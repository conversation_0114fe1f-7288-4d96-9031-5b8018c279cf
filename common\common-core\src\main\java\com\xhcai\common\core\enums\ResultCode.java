package com.xhcai.common.core.enums;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ResultCode {

    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 失败
     */
    FAIL(500, "操作失败"),

    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),

    /**
     * 未授权
     */
    UNAUTHORIZED(401, "未授权"),

    /**
     * 禁止访问
     */
    FORBIDDEN(403, "禁止访问"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),

    /**
     * 请求超时
     */
    REQUEST_TIMEOUT(408, "请求超时"),

    /**
     * 数据冲突
     */
    CONFLICT(409, "数据冲突"),

    /**
     * 请求实体过大
     */
    PAYLOAD_TOO_LARGE(413, "请求实体过大"),

    /**
     * 请求过于频繁
     */
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    /**
     * 系统内部错误
     */
    INTERNAL_SERVER_ERROR(500, "系统内部错误"),

    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    /**
     * 网关超时
     */
    GATEWAY_TIMEOUT(504, "网关超时"),

    /**
     * 用户不存在
     */
    USER_NOT_FOUND(1001, "用户不存在"),

    /**
     * 用户已存在
     */
    USER_ALREADY_EXISTS(1002, "用户已存在"),

    /**
     * 用户名或密码错误
     */
    INVALID_CREDENTIALS(1003, "用户名或密码错误"),

    /**
     * 用户已被锁定
     */
    USER_LOCKED(1004, "用户已被锁定"),

    /**
     * 用户已被禁用
     */
    USER_DISABLED(1005, "用户已被禁用"),

    /**
     * 密码已过期
     */
    PASSWORD_EXPIRED(1006, "密码已过期"),

    /**
     * 账户已过期
     */
    ACCOUNT_EXPIRED(1007, "账户已过期"),

    /**
     * 验证码错误
     */
    INVALID_CAPTCHA(1008, "验证码错误"),

    /**
     * 验证码已过期
     */
    CAPTCHA_EXPIRED(1009, "验证码已过期"),

    /**
     * Token无效
     */
    INVALID_TOKEN(1010, "Token无效"),

    /**
     * Token已过期
     */
    TOKEN_EXPIRED(1011, "Token已过期"),

    /**
     * 权限不足
     */
    INSUFFICIENT_PERMISSIONS(1012, "权限不足"),

    /**
     * 角色不存在
     */
    ROLE_NOT_FOUND(1013, "角色不存在"),

    /**
     * 权限不存在
     */
    PERMISSION_NOT_FOUND(1014, "权限不存在"),

    /**
     * 部门不存在
     */
    DEPT_NOT_FOUND(1015, "部门不存在"),

    /**
     * 菜单不存在
     */
    MENU_NOT_FOUND(1016, "菜单不存在"),

    /**
     * 模块不存在
     */
    MODULE_NOT_FOUND(2001, "模块不存在"),

    /**
     * 模块已存在
     */
    MODULE_ALREADY_EXISTS(2002, "模块已存在"),

    /**
     * 模块未启用
     */
    MODULE_NOT_ENABLED(2003, "模块未启用"),

    /**
     * 模块启动失败
     */
    MODULE_START_FAILED(2004, "模块启动失败"),

    /**
     * 模块停止失败
     */
    MODULE_STOP_FAILED(2005, "模块停止失败"),

    /**
     * 模块配置错误
     */
    MODULE_CONFIG_ERROR(2006, "模块配置错误"),

    /**
     * AI服务不可用
     */
    AI_SERVICE_UNAVAILABLE(3001, "AI服务不可用"),

    /**
     * AI模型不存在
     */
    AI_MODEL_NOT_FOUND(3002, "AI模型不存在"),

    /**
     * AI对话超时
     */
    AI_CHAT_TIMEOUT(3003, "AI对话超时"),

    /**
     * AI配额不足
     */
    AI_QUOTA_EXCEEDED(3004, "AI配额不足"),

    /**
     * 文件不存在
     */
    FILE_NOT_FOUND(4001, "文件不存在"),

    /**
     * 文件上传失败
     */
    FILE_UPLOAD_FAILED(4002, "文件上传失败"),

    /**
     * 文件类型不支持
     */
    FILE_TYPE_NOT_SUPPORTED(4003, "文件类型不支持"),

    /**
     * 文件大小超限
     */
    FILE_SIZE_EXCEEDED(4004, "文件大小超限"),

    /**
     * 数据库连接失败
     */
    DATABASE_CONNECTION_FAILED(5001, "数据库连接失败"),

    /**
     * 数据库操作失败
     */
    DATABASE_OPERATION_FAILED(5002, "数据库操作失败"),

    /**
     * 数据不存在
     */
    DATA_NOT_FOUND(5003, "数据不存在"),

    /**
     * 数据已存在
     */
    DATA_ALREADY_EXISTS(5004, "数据已存在"),

    /**
     * 数据完整性约束违反
     */
    DATA_INTEGRITY_VIOLATION(5005, "数据完整性约束违反");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
