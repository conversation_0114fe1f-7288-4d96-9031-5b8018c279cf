<template>
  <div 
    class="hover-tooltip-container"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 触发元素 -->
    <slot name="trigger"></slot>
    
    <!-- 提示层 -->
    <Teleport to="body">
      <div
        v-if="visible"
        ref="tooltipRef"
        class="hover-tooltip"
        :style="tooltipStyle"
        @mouseenter="handleTooltipMouseEnter"
        @mouseleave="handleTooltipMouseLeave"
      >
        <slot name="content"></slot>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'

interface Props {
  placement?: 'top' | 'bottom' | 'left' | 'right'
  offset?: number
  delay?: number
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'top',
  offset: 8,
  delay: 300,
  disabled: false
})

const visible = ref(false)
const tooltipRef = ref<HTMLElement>()
const triggerRect = ref<DOMRect>()
const showTimer = ref<number>()
const hideTimer = ref<number>()

// 计算提示层位置
const tooltipStyle = computed(() => {
  if (!triggerRect.value) return {}
  
  const rect = triggerRect.value
  const offset = props.offset
  
  let top = 0
  let left = 0
  
  switch (props.placement) {
    case 'top':
      top = rect.top - offset
      left = rect.left + rect.width / 2
      break
    case 'bottom':
      top = rect.bottom + offset
      left = rect.left + rect.width / 2
      break
    case 'left':
      top = rect.top + rect.height / 2
      left = rect.left - offset
      break
    case 'right':
      top = rect.top + rect.height / 2
      left = rect.right + offset
      break
  }
  
  return {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    zIndex: 9999,
    transform: getTransform()
  }
})

// 获取变换样式
const getTransform = () => {
  switch (props.placement) {
    case 'top':
      return 'translate(-50%, -100%)'
    case 'bottom':
      return 'translate(-50%, 0)'
    case 'left':
      return 'translate(-100%, -50%)'
    case 'right':
      return 'translate(0, -50%)'
    default:
      return 'translate(-50%, -100%)'
  }
}

// 鼠标进入触发元素
const handleMouseEnter = (event: MouseEvent) => {
  if (props.disabled) return
  
  clearTimeout(hideTimer.value)
  
  const target = event.currentTarget as HTMLElement
  triggerRect.value = target.getBoundingClientRect()

  nextTick(() => {
    adjustPosition()
    showTimer.value = window.setTimeout(() => {
      visible.value = true
    }, props.delay)
  })

}

// 鼠标离开触发元素
const handleMouseLeave = () => {
  clearTimeout(showTimer.value)
  
  hideTimer.value = window.setTimeout(() => {
    visible.value = false
  }, 100)
}

// 鼠标进入提示层
const handleTooltipMouseEnter = () => {
  clearTimeout(hideTimer.value)
}

// 鼠标离开提示层
const handleTooltipMouseLeave = () => {
  hideTimer.value = window.setTimeout(() => {
    visible.value = false
  }, 100)
}

// 调整位置，防止超出视窗
const adjustPosition = () => {
  if (!tooltipRef.value || !triggerRect.value) return
  
  const tooltip = tooltipRef.value
  const tooltipRect = tooltip.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  
  let adjustedStyle: any = {}
  
  // 水平方向调整
  if (tooltipRect.right > viewportWidth) {
    adjustedStyle.left = `${viewportWidth - tooltipRect.width - 10}px`
    adjustedStyle.transform = getTransform().replace('translate(-50%', 'translate(0')
  } else if (tooltipRect.left < 0) {
    adjustedStyle.left = '10px'
    adjustedStyle.transform = getTransform().replace('translate(-50%', 'translate(0')
  }
  
  // 垂直方向调整
  if (tooltipRect.bottom > viewportHeight) {
    const rect = triggerRect.value
    adjustedStyle.top = `${rect.top - tooltipRect.height - props.offset}px`
    adjustedStyle.transform = adjustedStyle.transform?.replace('-100%)', '0)') || getTransform().replace('-100%)', '0)')
  } else if (tooltipRect.top < 0) {
    const rect = triggerRect.value
    adjustedStyle.top = `${rect.bottom + props.offset}px`
    adjustedStyle.transform = adjustedStyle.transform?.replace('-100%)', '0)') || getTransform().replace('-100%)', '0)')
  }

  // 应用调整后的样式
  Object.assign(tooltip.style, adjustedStyle)
}
</script>

<style scoped>
.hover-tooltip-container {
  display: inline-block;
}

.hover-tooltip {
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
}
</style>
