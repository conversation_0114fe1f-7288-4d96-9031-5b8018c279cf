/**
 * 配置组件系统统一入口
 * 导出所有配置相关的功能和类型
 */

// 导入依赖
import { ConfigRegistry, ConfigFactory } from './registry/ConfigRegistry'
import {
  autoRegisterConfigs,
  registerSingleConfig,
  getConfigRegistrationStats,
  isConfigRegistered,
  getRegisteredConfigTypes,
  getConfigTypesByCategory
} from './registry/AutoRegisterConfigs'

// 核心注册系统
export { ConfigRegistry, ConfigFactory }
export type {
  ConfigComponent,
  ConfigRegistration,
  ConfigRegistryClass
} from './registry/ConfigRegistry'

// 自动注册
export {
  autoRegisterConfigs,
  registerSingleConfig,
  getConfigRegistrationStats,
  isConfigRegistered,
  getRegisteredConfigTypes,
  getConfigTypesByCategory
}

// 基础配置组件
export { default as BaseConfig } from './BaseConfig.vue'

// 基础节点配置组件
export { default as StartNodeConfig } from './basic/StartNodeConfig.vue'

// 数据库配置组件
export { default as MySQLConfig } from './database/MySQLConfig.vue'

/**
 * 初始化配置组件系统
 */
export async function initializeConfigSystem() {
  try {
    await autoRegisterConfigs()
    console.log('Config system initialized successfully')
    return true
  } catch (error) {
    console.error('Failed to initialize config system:', error)
    return false
  }
}

/**
 * 获取配置组件系统状态
 */
export function getConfigSystemStatus() {
  return {
    stats: ConfigRegistry.getStats(),
    availableTypes: ConfigRegistry.getAllNodeTypes()
  }
}

/**
 * 创建配置组件的便捷方法
 */
export function createConfigComponent(nodeType: string, props: Record<string, any> = {}) {
  return ConfigFactory.createConfig(nodeType, props)
}

/**
 * 搜索配置组件的便捷方法
 */
export function searchConfigComponents(options: {
  category?: string
  keyword?: string
}) {
  return ConfigRegistry.searchConfigs(options)
}

/**
 * 获取所有可用配置组件类型的便捷方法
 */
export function getAvailableConfigTypes() {
  return ConfigRegistry.getAllNodeTypes()
}

/**
 * 检查配置组件类型是否可用的便捷方法
 */
export function isConfigTypeAvailable(nodeType: string) {
  return ConfigRegistry.hasConfig(nodeType)
}

/**
 * 获取配置组件的便捷方法
 */
export function getConfigComponent(nodeType: string) {
  return ConfigRegistry.getConfigComponent(nodeType)
}

/**
 * 获取配置组件注册信息的便捷方法
 */
export function getConfigRegistration(nodeType: string) {
  return ConfigRegistry.getConfig(nodeType)
}

/**
 * 配置组件管理器
 * 提供统一的配置组件管理接口
 */
export class ConfigManager {
  private static initialized = false

  /**
   * 初始化配置管理器
   */
  static async initialize(): Promise<boolean> {
    if (this.initialized) {
      return true
    }

    try {
      await autoRegisterConfigs()
      this.initialized = true
      console.log('ConfigManager initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize ConfigManager:', error)
      return false
    }
  }

  /**
   * 检查是否已初始化
   */
  static isInitialized(): boolean {
    return this.initialized
  }

  /**
   * 创建配置组件
   */
  static createConfig(nodeType: string, props: Record<string, any> = {}) {
    this.ensureInitialized()
    return ConfigFactory.createConfig(nodeType, props)
  }

  /**
   * 获取配置组件
   */
  static getComponent(nodeType: string) {
    this.ensureInitialized()
    return ConfigRegistry.getConfigComponent(nodeType)
  }

  /**
   * 检查配置组件是否存在
   */
  static hasConfig(nodeType: string): boolean {
    return ConfigRegistry.hasConfig(nodeType)
  }

  /**
   * 获取所有配置组件类型
   */
  static getAllTypes(): string[] {
    return ConfigRegistry.getAllNodeTypes()
  }

  /**
   * 根据类别获取配置组件类型
   */
  static getTypesByCategory(category: string): string[] {
    return ConfigRegistry.getConfigsByCategory(category)
  }

  /**
   * 搜索配置组件
   */
  static search(options: { category?: string; keyword?: string }): string[] {
    return ConfigRegistry.searchConfigs(options)
  }

  /**
   * 注册配置组件
   */
  static register(
    nodeType: string,
    category: string,
    component: any,
    title: string,
    description: string
  ): void {
    ConfigRegistry.registerConfig({
      nodeType,
      category,
      component,
      title,
      description
    })
  }

  /**
   * 取消注册配置组件
   */
  static unregister(nodeType: string): boolean {
    return ConfigRegistry.unregisterConfig(nodeType)
  }

  /**
   * 获取统计信息
   */
  static getStats() {
    return {
      registry: ConfigRegistry.getStats(),
      initialized: this.initialized
    }
  }

  /**
   * 清空所有注册信息
   */
  static clear(): void {
    ConfigRegistry.clear()
    this.initialized = false
  }

  /**
   * 确保已初始化
   */
  private static ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error('ConfigManager not initialized. Call initialize() first.')
    }
  }
}
