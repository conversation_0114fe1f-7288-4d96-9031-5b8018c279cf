import { useAuthStore } from '@/stores/authStore'
import { envConfig } from '@/config/env'

// 探索对话请求接口
export interface ExploreStreamChatRequest {
  response_mode: string
  conversation_id: string
  files: any[]
  query: string
  inputs: {

  }
  parent_message_id: string | null
  appId: string
  agentId?: string
}

// 流式响应回调接口
export interface StreamCallbacks {
  onMessage?: (data: any) => void
  onError?: (error: any) => void
  onComplete?: () => void
  onOpen?: () => void
}

// 当前流式请求的控制器
let currentAbortController: AbortController | null = null

// 对话记录信息接口
export interface ConversationInfo {
  id: string
  name: string
  status: string
  introduction: string
  createdAt: number
  updatedAt: number
}

// 会话消息接口
export interface ConversationMessage {
  id: string
  inputs: any
  query: string
  answer: string
  feedback: any
  status: string
  error: any
  conversation_id: string
  message_files: any[]
  retriever_resources: any[]
  created_at: number
  createdTime?: string // 北京时间格式的创建时间
  agent_thoughts: any[]
  parent_message_id: string | null
}

// 会话消息响应接口
export interface ConversationMessagesResponse {
  data: ConversationMessage[]
  has_more: boolean
  limit: number
  total: number | null
  first_id: string | null
  last_id: string | null
}

// 对话名称响应接口
export interface ConversationNameResponse {
  id: string
  name: string
  status: string
  introduction: string
  createdAt: number
  updatedAt: number
}

// 探索API
export const exploreApi = {
  /**
   * 获取智能体对话记录列表
   */
  async getConversations(appId: string, params?: {
    limit?: number
    pinned?: boolean
  }): Promise<ConversationInfo[]> {
    const authStore = useAuthStore()
    const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

    const queryParams = new URLSearchParams()
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.pinned !== undefined) queryParams.append('pinned', params.pinned.toString())

    const url = `${envConfig.apiBaseUrl}/api/ai/chat/conversations/${appId}${queryParams.toString() ? '?' + queryParams.toString() : ''}`

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 检查响应格式
      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error(result.message || '获取对话记录失败')
      }
    } catch (error) {
      console.error('获取对话记录失败:', error)
      throw error
    }
  },

  /**
   * 获取对话名称
   */
  async getConversationName(appId: string, conversationId: string, agentId?: string): Promise<ConversationNameResponse> {
    const authStore = useAuthStore()
    const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

    // 构建URL，如果有agentId则添加查询参数
    let url = `${envConfig.apiBaseUrl}/api/ai/chat/conversations/${appId}/${conversationId}/name`
    if (agentId) {
      url += `?agentId=${encodeURIComponent(agentId)}`
    }

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 检查响应格式
      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error(result.message || '获取对话名称失败')
      }
    } catch (error) {
      console.error('获取对话名称失败:', error)
      throw error
    }
  },

  /**
   * 重命名对话
   */
  async renameConversation(appId: string, conversationId: string, newName: string): Promise<ConversationNameResponse> {
    const authStore = useAuthStore()
    const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

    const url = `${envConfig.apiBaseUrl}/api/ai/chat/conversations/${appId}/${conversationId}/name`

    try {
      // 创建FormData对象
      const formData = new FormData()
      formData.append('name', newName)

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          // 注意：使用FormData时不要设置Content-Type，让浏览器自动设置
          ...authHeaders,
        },
        body: formData
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 检查响应格式
      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error(result.message || '重命名对话失败')
      }
    } catch (error) {
      console.error('重命名对话失败:', error)
      throw error
    }
  },

  /**
   * 删除对话
   */
  async deleteConversation(appId: string, conversationId: string): Promise<void> {
    const authStore = useAuthStore()
    const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

    const url = `${envConfig.apiBaseUrl}/api/ai/chat/conversations/${appId}/${conversationId}/delete`

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 检查响应格式
      if (!result.success) {
        throw new Error(result.message || '删除对话失败')
      }

      console.log('删除对话成功:', result)
    } catch (error) {
      console.error('删除对话失败:', error)
      throw error
    }
  },

  /**
   * 停止工作流
   */
  async stopWorkflow(appId: string, taskId: string): Promise<void> {
    const authStore = useAuthStore()
    const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

    const url = `${envConfig.apiBaseUrl}/api/ai/chat/workflow/${appId}/${taskId}/stop`

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 检查响应格式
      if (!result.success) {
        throw new Error(result.message || '停止工作流失败')
      }

      console.log('停止工作流成功:', result)
    } catch (error) {
      console.error('停止工作流失败:', error)
      throw error
    }
  },

  /**
   * 获取会话消息列表
   */
  async getConversationMessages(appId: string, conversationId: string, params?: {
    limit?: number
    first_id?: string
    last_id?: string
  }): Promise<ConversationMessagesResponse> {
    const authStore = useAuthStore()
    const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

    const queryParams = new URLSearchParams()
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.first_id) queryParams.append('first_id', params.first_id)
    if (params?.last_id) queryParams.append('last_id', params.last_id)

    const url = `${envConfig.apiBaseUrl}/api/ai/chat/conversations/${appId}/${conversationId}/messages${queryParams.toString() ? '?' + queryParams.toString() : ''}`

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 检查响应格式
      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error(result.message || '获取会话消息失败')
      }
    } catch (error) {
      console.error('获取会话消息失败:', error)
      throw error
    }
  },

  /**
   * 流式聊天 - 使用fetch流式响应（更适合POST请求）
   */
  async streamChat(request: ExploreStreamChatRequest, callbacks: StreamCallbacks = {}): Promise<void> {
    const { onMessage, onError, onComplete, onOpen } = callbacks

    // 取消之前的请求
    if (currentAbortController) {
      currentAbortController.abort()
    }

    // 创建新的AbortController
    currentAbortController = new AbortController()

    try {
      // 获取认证头部
      const authStore = useAuthStore()
      const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

      // 构建完整URL
      const url = `${envConfig.apiBaseUrl}/api/ai/chat/test/stream`

      // 发送POST请求启动流式聊天
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          ...authHeaders,
        },
        body: JSON.stringify(request),
        signal: currentAbortController.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 触发连接打开事件
      if (onOpen) {
        onOpen()
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            // 处理剩余的buffer
            if (buffer.trim()) {
              this.processSSEChunk(buffer, onMessage, onError)
            }
            break
          }

          // 解码数据
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          // 按行分割处理
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留最后一行（可能不完整）

          for (const line of lines) {
            this.processSSEChunk(line, onMessage, onError)
          }
        }
      } finally {
        reader.releaseLock()
        currentAbortController = null
        if (onComplete) {
          onComplete()
        }
      }

    } catch (error) {
      currentAbortController = null

      // 如果是用户主动取消，不触发错误回调
      if (error instanceof Error && error.name === 'AbortError') {
        return
      }

      // 只在开发环境输出详细错误信息
      if (import.meta.env.DEV) {
        console.error('流式聊天请求失败:', error)
      }
      if (onError) {
        onError(error)
      }
    }
  },

  /**
   * 处理SSE数据块
   */
  processSSEChunk(chunk: string, onMessage?: (data: any) => void, onError?: (error: any) => void) {
    const line = chunk.trim()

    if (!line || line === '') {
      return
    }

    // 处理SSE格式的数据
    if (line.startsWith('data:')) {
      const dataStr = line.substring(5).trim()

      if (dataStr === '[DONE]') {
        // 流结束标记
        return
      }

      try {
        const data = JSON.parse(dataStr)
        if (onMessage) {
          onMessage(data)
        }
      } catch (error) {
        // 只在开发环境输出详细错误信息
        if (import.meta.env.DEV) {
          console.error('解析SSE数据失败:', error, 'data:', dataStr)
        }
        if (onError) {
          onError(error)
        }
      }
    }
  },

  /**
   * 停止当前的流式聊天
   */
  stopStreamChat() {
    if (currentAbortController) {
      currentAbortController.abort()
      currentAbortController = null
    }
  },

  /**
   * 检查是否有活跃的流式连接
   */
  isStreaming() {
    return currentAbortController !== null
  },

  /**
   * 获取智能体聊天参数配置
   */
  async getChatParameters(appId: string) {
    try {
      const authStore = useAuthStore()
      const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

      const url = `${envConfig.apiBaseUrl}/api/ai/chat/parameters/${appId}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('获取智能体参数失败:', error)
      throw error
    }
  },

  /**
   * 获取消息建议问题
   */
  async getSuggestedQuestions(appId: string, messageId: string) {
    try {
      const authStore = useAuthStore()
      const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

      const url = `${envConfig.apiBaseUrl}/api/dify/apps/${appId}/messages/${messageId}/suggested-questions`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('获取建议问题失败:', error)
      throw error
    }
  },

  /**
   * 上传文件到智能体聊天
   */
  async uploadChatFile(file: File) {
    try {
      const authStore = useAuthStore()
      const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

      const url = `${envConfig.apiBaseUrl}/api/ai/chat/upload`

      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          ...authHeaders,
          // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
        },
        body: formData
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    }
  },

  /**
   * 上传远程文件链接到智能体聊天
   */
  async uploadRemoteChatFile(fileUrl: string) {
    try {
      const authStore = useAuthStore()
      const authHeaders = authStore.isLoggedIn ? authStore.getAuthHeaders() : {}

      const url = `${envConfig.apiBaseUrl}/api/ai/chat/upload-remote`

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        },
        body: JSON.stringify({
          url: fileUrl
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('远程文件上传失败:', error)
      throw error
    }
  }
}

export default exploreApi
