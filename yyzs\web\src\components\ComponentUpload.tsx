'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { ComponentType, ComponentTypeLabels } from '@/types/component';
import ComponentsAPI from '@/api/components';
import toast from 'react-hot-toast';

interface ComponentUploadProps {
  onUploadSuccess?: (componentId: string) => void;
  onClose?: () => void;
}

export default function ComponentUpload({ onUploadSuccess, onClose }: ComponentUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [componentType, setComponentType] = useState<ComponentType>('filebeat');
  const [version, setVersion] = useState('');
  const [description, setDescription] = useState('');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setSelectedFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/gzip': ['.tar.gz', '.tgz'],
      'application/zip': ['.zip'],
      'application/x-tar': ['.tar'],
    },
    maxFiles: 1,
    maxSize: 500 * 1024 * 1024, // 500MB
  });

  const handleUpload = async () => {
    if (!selectedFile || !version.trim()) {
      toast.error('请选择文件并填写版本号');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 200);

      const response = await ComponentsAPI.uploadPackage({
        file: selectedFile,
        componentType,
        version: version.trim(),
        description: description.trim() || undefined,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.success && response.data) {
        toast.success('组件上传成功！');
        onUploadSuccess?.(response.data.componentId);
        handleReset();
      } else {
        throw new Error(response.message || '上传失败');
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      toast.error(error.message || '上传失败，请重试');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleReset = () => {
    setSelectedFile(null);
    setComponentType('filebeat');
    setVersion('');
    setDescription('');
    setUploadProgress(0);
  };

  const removeFile = () => {
    setSelectedFile(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="bg-white rounded-lg shadow-large max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">上传组件安装包</h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        )}
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* File Upload Area */}
        <div>
          <label className="form-label">安装包文件</label>
          {!selectedFile ? (
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                {isDragActive ? '释放文件到这里' : '拖拽文件到这里，或点击选择'}
              </p>
              <p className="text-sm text-gray-500">
                支持 .tar.gz, .tgz, .zip 格式，最大 500MB
              </p>
            </div>
          ) : (
            <div className="border border-gray-300 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-8 w-8 text-success-600" />
                  <div>
                    <p className="font-medium text-gray-900">{selectedFile.name}</p>
                    <p className="text-sm text-gray-500">
                      {formatFileSize(selectedFile.size)}
                    </p>
                  </div>
                </div>
                <button
                  onClick={removeFile}
                  className="text-gray-400 hover:text-error-600 transition-colors"
                  disabled={uploading}
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              {uploading && (
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>上传进度</span>
                    <span>{Math.round(uploadProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Component Type */}
        <div>
          <label className="form-label">组件类型</label>
          <select
            value={componentType}
            onChange={(e) => setComponentType(e.target.value as ComponentType)}
            className="form-input"
            disabled={uploading}
          >
            {Object.entries(ComponentTypeLabels).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </select>
        </div>

        {/* Version */}
        <div>
          <label className="form-label">版本号 *</label>
          <input
            type="text"
            value={version}
            onChange={(e) => setVersion(e.target.value)}
            placeholder="例如: 8.11.0"
            className="form-input"
            disabled={uploading}
          />
        </div>

        {/* Description */}
        <div>
          <label className="form-label">描述信息</label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="可选的描述信息..."
            rows={3}
            className="form-input resize-none"
            disabled={uploading}
          />
        </div>

        {/* Upload Progress Info */}
        {uploading && (
          <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <Loader2 className="h-5 w-5 text-primary-600 animate-spin" />
              <div>
                <p className="font-medium text-primary-900">正在上传组件...</p>
                <p className="text-sm text-primary-700">
                  请耐心等待，大文件上传可能需要一些时间
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
        <button
          onClick={handleReset}
          className="btn-outline"
          disabled={uploading}
        >
          重置
        </button>
        <button
          onClick={handleUpload}
          disabled={!selectedFile || !version.trim() || uploading}
          className="btn-primary"
        >
          {uploading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              上传中...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              上传组件
            </>
          )}
        </button>
      </div>
    </div>
  );
}
