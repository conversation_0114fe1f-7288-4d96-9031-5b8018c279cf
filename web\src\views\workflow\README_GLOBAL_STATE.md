# Workflow 全局状态管理重构

## 概述

本次重构将 `web/src/views/workflow` 目录下的智能体编排流程从本地状态管理迁移到全局状态管理，使用 Pinia 进行状态管理，实现了组件间数据的统一管理和自动同步。

## 主要变更

### 1. 创建全局状态管理 Store

**文件**: `web/src/stores/workflowStore.ts`

- 使用 Pinia 创建全局状态管理
- 管理 `workflowConfig`、`nodes`、`edges`、`viewport` 等核心数据
- 提供选择状态、面板状态、执行状态的统一管理
- 实现自动保存机制和数据一致性验证

**核心功能**:
- 基础数据管理：节点、连接线、视口配置
- 选择状态管理：当前选中的节点或连接线
- 面板状态管理：各个面板的显示/隐藏状态
- 执行状态管理：工作流执行过程中的状态跟踪
- 自动同步机制：状态变更时自动同步到 workflowConfig
- 数据验证：检查数据一致性，清理无效连接线

### 2. 重构主组件

**文件**: `web/src/views/workflow/index.vue`

**主要变更**:
- 移除本地状态定义（`workflowConfig`、`nodes`、`edges` 等）
- 使用 `storeToRefs` 获取全局状态的响应式引用
- 更新所有方法调用，使用全局状态管理的方法
- 简化事件处理逻辑，直接调用 store 方法
- 添加数据一致性检查和清理功能

**删除的本地状态**:
```typescript
// 删除了这些本地状态定义
const agentInfo = ref<any>({})
const workflowConfig = ref<WorkflowConfig>({...})
const nodes = ref<Node[]>([])
const edges = ref<Edge[]>([])
const selectedNode = ref<Node | null>(null)
const selectedEdge = ref<Edge | null>(null)
// ... 等等
```

**新的全局状态使用**:
```typescript
// 使用全局状态管理
const workflowStore = useWorkflowStore()
const {
  agentInfo,
  workflowConfig,
  nodes,
  edges,
  viewport,
  selectionState,
  panelState,
  executionState,
  hasNodes
} = storeToRefs(workflowStore)
```

### 3. 更新子组件

#### NodeProperties 组件
**文件**: `web/src/views/workflow/components/NodeProperties.vue`

- 导入全局状态管理
- 优先使用全局状态中的 `nodes` 和 `edges`，fallback 到 props
- 更新参数依赖管理逻辑

#### EdgeProperties 组件
**文件**: `web/src/views/workflow/components/EdgeProperties.vue`

- 导入全局状态管理
- 优先使用全局状态中的 `nodes`，fallback 到 props
- 更新节点信息获取逻辑

#### WorkflowRunner 组件
**文件**: `web/src/views/workflow/components/WorkflowRunner.vue`

- 导入全局状态管理
- 优先使用全局状态中的 `nodes`、`edges` 和 `globalVariables`
- 保持向后兼容性，支持通过 props 传递数据

### 4. 状态同步机制

**自动保存机制**:
- 监听 `nodes`、`edges`、`viewport` 变化
- 使用防抖机制，避免频繁保存
- 不同类型的变更使用不同的延迟时间

**数据一致性验证**:
- 检查连接线是否引用了不存在的节点
- 检查节点和连接线 ID 是否唯一
- 提供自动清理无效连接线的功能

**workflowConfig 同步**:
- 自动将 `nodes`、`edges`、`viewport` 同步到 `workflowConfig`
- 自动更新 `lastModified` 时间戳

## 使用方式

### 在组件中使用全局状态

```typescript
import { useWorkflowStore } from '@/stores/workflowStore'
import { storeToRefs } from 'pinia'

// 在组件中
const workflowStore = useWorkflowStore()
const { nodes, edges, selectionState } = storeToRefs(workflowStore)

// 调用方法
workflowStore.addNode(newNode)
workflowStore.selectNode(node)
workflowStore.toggleNodeLibrary()
```

### 初始化工作流

```typescript
// 初始化特定智能体的工作流
await workflowStore.initializeWorkflow(agentId)

// 数据一致性检查
const validation = workflowStore.validateDataConsistency()
if (!validation.valid) {
  workflowStore.cleanupInvalidEdges()
}
```

### 状态管理方法

**节点操作**:
- `addNode(node)` - 添加节点
- `updateNode(nodeId, updates)` - 更新节点
- `deleteNode(nodeId)` - 删除节点

**连接线操作**:
- `addEdge(edge)` - 添加连接线
- `updateEdge(edgeId, updates)` - 更新连接线
- `deleteEdge(edgeId)` - 删除连接线

**选择状态**:
- `selectNode(node)` - 选择节点
- `selectEdge(edge)` - 选择连接线
- `clearSelection()` - 清除选择

**面板状态**:
- `toggleNodeLibrary()` - 切换节点库面板
- `toggleProperties()` - 切换属性面板
- `toggleRunner()` - 切换运行器面板

**执行状态**:
- `startExecution()` - 开始执行
- `completeExecution(stats)` - 完成执行
- `cancelExecution()` - 取消执行

## 测试

### 测试文件

1. **单元测试**: `web/src/views/workflow/test/workflowStoreTest.ts`
   - 测试基础状态管理功能
   - 测试状态同步机制
   - 可在浏览器控制台中运行

2. **可视化测试**: `web/src/views/workflow/test/TestWorkflowStore.vue`
   - 提供可视化的测试界面
   - 实时显示状态变化
   - 交互式测试各种功能

### 运行测试

```typescript
// 在浏览器控制台中运行
runWorkflowStoreTests()

// 或访问测试页面
// /workflow/test
```

## 兼容性

- **向后兼容**: 子组件仍然支持通过 props 接收数据
- **渐进式迁移**: 可以逐步将其他组件迁移到全局状态管理
- **类型安全**: 保持完整的 TypeScript 类型支持

## 优势

1. **统一状态管理**: 所有组件共享同一份数据，避免数据不一致
2. **自动同步**: 状态变更自动同步到所有相关组件
3. **性能优化**: 减少不必要的 props 传递和事件冒泡
4. **开发体验**: 更清晰的数据流，更容易调试和维护
5. **扩展性**: 易于添加新的状态管理功能

## 注意事项

1. **内存管理**: 大型工作流可能占用较多内存，需要注意清理
2. **性能监控**: 复杂工作流的状态变更可能影响性能
3. **数据持久化**: 确保重要数据及时保存到后端
4. **错误处理**: 状态管理中的错误需要适当处理和恢复

## 后续优化

1. **状态持久化**: 实现本地存储备份
2. **撤销/重做**: 基于状态历史实现撤销重做功能
3. **协作编辑**: 支持多用户同时编辑工作流
4. **性能优化**: 大型工作流的性能优化
5. **状态压缩**: 对历史状态进行压缩存储
