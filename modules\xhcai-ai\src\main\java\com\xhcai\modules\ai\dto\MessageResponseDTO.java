package com.xhcai.modules.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xhcai.common.core.utils.TimeUtils;
import com.xhcai.modules.dify.dto.conversation.DifyMessageDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息响应DTO
 * 用于返回给前端的消息信息，包含正确的时间格式转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "消息响应")
public class MessageResponseDTO {
    
    @Schema(description = "消息ID")
    private String id;
    
    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;
    
    @Schema(description = "输入参数")
    private Map<String, Object> inputs;
    
    @Schema(description = "查询内容")
    private String query;
    
    @Schema(description = "答案内容")
    private String answer;
    
    @Schema(description = "消息文件列表")
    @JsonProperty("message_files")
    private List<Object> messageFiles;
    
    @Schema(description = "反馈信息")
    private Object feedback;
    
    @Schema(description = "检索资源信息")
    @JsonProperty("retriever_resources")
    private List<Object> retrieverResources;
    
    @Schema(description = "创建时间戳（秒）")
    @JsonProperty("created_at")
    private Long createdAt;
    
    @Schema(description = "创建时间（北京时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createdTime;
    
    @Schema(description = "智能体思考过程")
    @JsonProperty("agent_thoughts")
    private List<Object> agentThoughts;
    
    @Schema(description = "父消息ID")
    @JsonProperty("parent_message_id")
    private String parentMessageId;
    
    @Schema(description = "消息状态")
    private String status;
    
    @Schema(description = "错误信息")
    private String error;

    /**
     * 从原始消息数据转换为响应DTO
     */
    public static MessageResponseDTO fromRawMessage(Object rawMessage) {
        MessageResponseDTO dto = new MessageResponseDTO();
        
        if (rawMessage instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> messageMap = (Map<String, Object>) rawMessage;
            
            dto.setId((String) messageMap.get("id"));
            dto.setConversationId((String) messageMap.get("conversation_id"));
            dto.setInputs((Map<String, Object>) messageMap.get("inputs"));
            dto.setQuery((String) messageMap.get("query"));
            dto.setAnswer((String) messageMap.get("answer"));
            dto.setMessageFiles((List<Object>) messageMap.get("message_files"));
            dto.setFeedback(messageMap.get("feedback"));
            dto.setRetrieverResources((List<Object>) messageMap.get("retriever_resources"));
            dto.setAgentThoughts((List<Object>) messageMap.get("agent_thoughts"));
            dto.setParentMessageId((String) messageMap.get("parent_message_id"));
            dto.setStatus((String) messageMap.get("status"));
            dto.setError((String) messageMap.get("error"));
            
            // 处理时间戳转换
            Object createdAtObj = messageMap.get("created_at");
            if (createdAtObj != null) {
                Long createdAtTimestamp = null;
                if (createdAtObj instanceof Number) {
                    createdAtTimestamp = ((Number) createdAtObj).longValue();
                } else if (createdAtObj instanceof String) {
                    try {
                        createdAtTimestamp = Long.parseLong((String) createdAtObj);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
                
                if (createdAtTimestamp != null) {
                    dto.setCreatedAt(createdAtTimestamp);
                    // 转换时间戳为北京时间
                    dto.setCreatedTime(TimeUtils.convertTimestampToBeijingTime(createdAtTimestamp));
                }
            }
        }
        
        return dto;
    }

    /**
     * 从DifyMessageDTO转换为响应DTO
     */
    public static MessageResponseDTO fromDifyMessage(DifyMessageDTO difyMessage) {
        MessageResponseDTO dto = new MessageResponseDTO();

        dto.setId(difyMessage.getId());
        dto.setConversationId(difyMessage.getConversationId());
        dto.setInputs(difyMessage.getInputs());
        dto.setQuery(difyMessage.getQuery());
        dto.setAnswer(difyMessage.getAnswer());
        dto.setParentMessageId(difyMessage.getParentMessageId());
        dto.setStatus(difyMessage.getStatus());
        dto.setError(difyMessage.getError());

        // 转换消息文件列表
        if (difyMessage.getMessageFiles() != null) {
            dto.setMessageFiles(difyMessage.getMessageFiles().stream()
                    .map(messageFile -> (Object) messageFile)
                    .collect(java.util.stream.Collectors.toList()));
        }

        // 转换检索资源列表
        if (difyMessage.getRetrieverResources() != null) {
            dto.setRetrieverResources(difyMessage.getRetrieverResources().stream()
                    .map(resource -> (Object) resource)
                    .collect(java.util.stream.Collectors.toList()));
        }

        // 转换智能体思考过程
        if (difyMessage.getAgentThoughts() != null) {
            dto.setAgentThoughts(difyMessage.getAgentThoughts().stream()
                    .map(thought -> (Object) thought)
                    .collect(java.util.stream.Collectors.toList()));
        }

        // 设置反馈信息
        dto.setFeedback(difyMessage.getFeedback());

        // 处理时间戳转换
        if (difyMessage.getCreatedAt() != null) {
            dto.setCreatedAt(difyMessage.getCreatedAt());
            // 转换时间戳为北京时间
            dto.setCreatedTime(TimeUtils.convertTimestampToBeijingTime(difyMessage.getCreatedAt()));
        }

        return dto;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public List<Object> getMessageFiles() {
        return messageFiles;
    }

    public void setMessageFiles(List<Object> messageFiles) {
        this.messageFiles = messageFiles;
    }

    public Object getFeedback() {
        return feedback;
    }

    public void setFeedback(Object feedback) {
        this.feedback = feedback;
    }

    public List<Object> getRetrieverResources() {
        return retrieverResources;
    }

    public void setRetrieverResources(List<Object> retrieverResources) {
        this.retrieverResources = retrieverResources;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public List<Object> getAgentThoughts() {
        return agentThoughts;
    }

    public void setAgentThoughts(List<Object> agentThoughts) {
        this.agentThoughts = agentThoughts;
    }

    public String getParentMessageId() {
        return parentMessageId;
    }

    public void setParentMessageId(String parentMessageId) {
        this.parentMessageId = parentMessageId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }
}
