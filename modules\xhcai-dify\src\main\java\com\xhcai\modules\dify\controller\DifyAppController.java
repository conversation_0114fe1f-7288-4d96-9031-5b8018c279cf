package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.dify.dto.app.DifyAppParametersResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.dto.app.DifySuggestedQuestionsResponseDTO;
import com.xhcai.modules.dify.service.IDifyAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * Dify 应用控制器
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Tag(name = "Dify应用管理", description = "Dify应用服务对接")
@RestController
@RequestMapping("/api/dify/apps")
public class DifyAppController {

    private static final Logger log = LoggerFactory.getLogger(DifyAppController.class);

    @Autowired
    private IDifyAppService difyAppService;

    @Operation(summary = "获取已安装应用列表", description = "获取Dify平台已安装的应用列表")
    @GetMapping("/installed")
    @RequiresPermissions("dify:app:list")
    public Mono<Result<DifyInstalledAppsResponseDTO>> getInstalledApps() {
        log.info("获取已安装应用列表");
        return difyAppService.getInstalledApps();
    }

    @Operation(summary = "获取应用会话参数", description = "获取指定应用的会话参数配置")
    @GetMapping("/{installedAppId}/parameters")
    @RequiresPermissions("dify:app:parameters")
    public Mono<Result<DifyAppParametersResponseDTO>> getAppParameters(
            @Parameter(description = "已安装应用ID", required = true)
            @PathVariable String installedAppId) {
        log.info("获取应用会话参数: installedAppId={}", installedAppId);
        return difyAppService.getAppParameters(installedAppId);
    }

    @Operation(summary = "获取消息建议问题", description = "获取指定消息的下一步问题建议")
    @GetMapping("/{appId}/messages/{messageId}/suggested-questions")
    @RequiresPermissions("dify:app:suggested-questions")
    public Result<DifySuggestedQuestionsResponseDTO> getSuggestedQuestions(
            @Parameter(description = "应用ID", required = true)
            @PathVariable String appId,
            @Parameter(description = "消息ID", required = true)
            @PathVariable String messageId) {
        log.info("获取消息建议问题: appId={}, messageId={}", appId, messageId);

        try {
            // 使用 block() 方法将 Mono 转换为同步调用，设置30秒超时
            return difyAppService.getSuggestedQuestionsByAppId(appId, messageId)
                    .timeout(java.time.Duration.ofSeconds(30))
                    .block();
        } catch (Exception e) {
            // 检查是否是超时异常
            if (e.getCause() instanceof java.util.concurrent.TimeoutException ||
                e.getMessage() != null && e.getMessage().contains("timeout")) {
                log.error("获取建议问题超时: appId={}, messageId={}, 超时时间: 30秒", appId, messageId);
                return Result.fail("获取建议问题超时，请稍后重试");
            }
            log.error("获取建议问题失败: appId={}, messageId={}, error={}", appId, messageId, e.getMessage(), e);
            return Result.fail("获取建议问题失败: " + e.getMessage());
        }
    }
}
