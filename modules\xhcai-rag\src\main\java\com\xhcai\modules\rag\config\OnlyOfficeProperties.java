package com.xhcai.modules.rag.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * OnlyOffice配置属性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "xhcai.onlyoffice")
public class OnlyOfficeProperties {

    /**
     * OnlyOffice服务器地址
     */
    private String serverUrl = "http://localhost:8080";

    /**
     * OnlyOffice文档服务器地址
     */
    private String documentServerUrl = "http://localhost:8080/";

    /**
     * OnlyOffice API地址
     */
    private String apiUrl = "/web-apps/apps/api/documents/api.js";

    /**
     * 回调地址
     */
    private String callbackUrl = "http://localhost:8080/api/rag/onlyoffice/callback";

    /**
     * JWT密钥
     */
    private String jwtSecret = "xhcai-onlyoffice-secret";

    /**
     * 是否启用JWT
     */
    private boolean jwtEnabled = false;

    /**
     * JWT头部名称
     */
    private String jwtHeader = "Authorization";

    /**
     * 文档预览超时时间（秒）
     */
    private int previewTimeout = 300;

    /**
     * 支持的文档类型
     */
    private String[] supportedFormats = {
        "doc", "docx", "docm", "dot", "dotx", "dotm", "odt", "fodt", "ott", "rtf", "txt",
        "html", "htm", "mht", "pdf", "djvu", "fb2", "epub", "xps",
        "xls", "xlsx", "xlsm", "xlt", "xltx", "xltm", "ods", "fods", "ots", "csv",
        "pps", "ppsx", "ppsm", "ppt", "pptx", "pptm", "pot", "potx", "potm", "odp", "fodp", "otp"
    };

    /**
     * 可编辑的文档类型
     */
    private String[] editableFormats = {
        "docx", "xlsx", "pptx", "odt", "ods", "odp"
    };

    /**
     * 获取完整的文档服务器URL
     */
    public String getFullDocumentServerUrl() {
        return documentServerUrl.endsWith("/") ? documentServerUrl : documentServerUrl + "/";
    }

    /**
     * 获取完整的API URL
     */
    public String getFullApiUrl() {
        return getFullDocumentServerUrl() + apiUrl.replaceFirst("^/", "");
    }

    /**
     * 检查文件格式是否支持
     */
    public boolean isSupportedFormat(String format) {
        if (format == null) {
            return false;
        }
        String lowerFormat = format.toLowerCase();
        for (String supportedFormat : supportedFormats) {
            if (supportedFormat.equals(lowerFormat)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文件格式是否可编辑
     */
    public boolean isEditableFormat(String format) {
        if (format == null) {
            return false;
        }
        String lowerFormat = format.toLowerCase();
        for (String editableFormat : editableFormats) {
            if (editableFormat.equals(lowerFormat)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据文件扩展名获取文档类型
     */
    public String getDocumentType(String format) {
        if (format == null) {
            return "word";
        }
        
        String lowerFormat = format.toLowerCase();
        
        // 文本文档
        if (lowerFormat.matches("doc|docx|docm|dot|dotx|dotm|odt|fodt|ott|rtf|txt|html|htm|mht|pdf|djvu|fb2|epub|xps")) {
            return "word";
        }
        
        // 电子表格
        if (lowerFormat.matches("xls|xlsx|xlsm|xlt|xltx|xltm|ods|fods|ots|csv")) {
            return "cell";
        }
        
        // 演示文稿
        if (lowerFormat.matches("pps|ppsx|ppsm|ppt|pptx|pptm|pot|potx|potm|odp|fodp|otp")) {
            return "slide";
        }
        
        return "word";
    }
}
