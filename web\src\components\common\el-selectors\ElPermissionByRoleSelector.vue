<template>
  <div class="el-permission-by-role-selector">
    <el-card class="selector-card" shadow="never">
      <!-- 头部信息 -->
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon><Key /></el-icon>
            <span>按角色选择权限</span>
            <el-tag v-if="hasSelection" type="info" size="small">
              已选择 {{ selectedCount }} 个权限
            </el-tag>
          </div>
          <div class="header-actions">
            <el-button 
              v-if="config.multiple" 
              @click="selectAllCurrentRolePermissions" 
              size="small" 
              type="primary" 
              plain
              :disabled="loading || !currentRolePermissions.length"
            >
              全选当前角色
            </el-button>
            <el-button 
              @click="clearSelection" 
              size="small" 
              :disabled="!hasSelection"
            >
              清空
            </el-button>
          </div>
        </div>
      </template>

      <div class="selector-content">
        <el-row :gutter="16">
          <!-- 左侧：角色选择 -->
          <el-col :span="8">
            <div class="role-panel">
              <div class="panel-title">
                <el-icon><UserFilled /></el-icon>
                <span>选择角色</span>
                <el-tag v-if="selectedRole" type="success" size="small">
                  {{ selectedRole.label }}
                </el-tag>
              </div>
              
              <!-- 角色搜索 -->
              <el-input
                v-model="roleFilterText"
                placeholder="搜索角色..."
                :prefix-icon="Search"
                clearable
                size="small"
                :disabled="roleLoading"
                class="role-search"
              />

              <!-- 角色列表 -->
              <div class="role-list">
                <el-scrollbar height="400px">
                  <el-skeleton v-if="roleLoading" :rows="5" animated />
                  
                  <el-empty v-else-if="!filteredRoles.length" 
                    :description="roleFilterText ? '未找到匹配的角色' : '暂无角色数据'"
                    :image-size="80"
                  />
                  
                  <div v-else class="role-items">
                    <div
                      v-for="role in filteredRoles"
                      :key="role.value"
                      class="role-item"
                      :class="{
                        'is-selected': selectedRoleId === role.value,
                        'is-disabled': role.disabled
                      }"
                      @click="handleRoleClick(role)"
                    >
                      <el-avatar :size="32" class="role-avatar">
                        <el-icon><Key /></el-icon>
                      </el-avatar>
                      
                      <div class="role-info">
                        <div class="role-name">{{ role.label }}</div>
                        <div class="role-details">
                          <span>{{ role.roleCode }}</span>
                          <span v-if="role.permissionIds?.length" class="permission-count">
                            {{ role.permissionIds.length }} 个权限
                          </span>
                        </div>
                      </div>
                      
                      <el-icon v-if="selectedRoleId === role.value" class="selected-icon" color="#409eff">
                        <Check />
                      </el-icon>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </el-col>

          <!-- 右侧：权限选择 -->
          <el-col :span="16">
            <div class="permission-panel">
              <div class="panel-title">
                <el-icon><Lock /></el-icon>
                <span>选择权限</span>
                <el-tag v-if="currentRolePermissions.length" type="info" size="small">
                  {{ currentRolePermissions.length }} 个可选权限
                </el-tag>
              </div>

              <!-- 权限搜索 -->
              <el-input
                v-model="permissionFilterText"
                placeholder="搜索权限..."
                :prefix-icon="Search"
                clearable
                size="small"
                :disabled="permissionLoading || !selectedRoleId"
                class="permission-search"
              />

              <!-- 权限树 -->
              <div class="permission-tree">
                <el-scrollbar height="400px">
                  <el-skeleton v-if="permissionLoading" :rows="8" animated />
                  
                  <el-empty v-else-if="!selectedRoleId" 
                    description="请先选择角色"
                    :image-size="80"
                  />
                  
                  <el-empty v-else-if="!filteredPermissions.length" 
                    :description="permissionFilterText ? '未找到匹配的权限' : '该角色暂无权限'"
                    :image-size="80"
                  />
                  
                  <el-tree
                    v-else
                    ref="permissionTreeRef"
                    :data="filteredPermissions"
                    :props="permissionTreeProps"
                    :show-checkbox="config.multiple"
                    :check-strictly="config.checkStrictly"
                    :default-expand-all="true"
                    :filter-node-method="filterPermissionNode"
                    :highlight-current="!config.multiple"
                    node-key="value"
                    @check="handlePermissionCheck"
                    @node-click="handlePermissionClick"
                  >
                    <template #default="{ node, data }">
                      <div class="permission-node">
                        <el-icon class="permission-icon" :color="getPermissionIconColor(data.type)">
                          <component :is="getPermissionIcon(data.type)" />
                        </el-icon>
                        <span class="permission-label">{{ data.label }}</span>
                        <el-tag v-if="data.type" :type="getPermissionTagType(data.type)" size="small">
                          {{ getPermissionTypeLabel(data.type) }}
                        </el-tag>
                      </div>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 已选择权限标签 -->
        <div v-if="hasSelection && config.multiple" class="selected-permissions">
          <el-divider content-position="left">
            <el-icon><Collection /></el-icon>
            <span>已选择的权限</span>
          </el-divider>
          
          <div class="permission-tags">
            <el-tag
              v-for="permission in selectedPermissionOptions"
              :key="permission.value"
              :type="getPermissionTagType(permission.type)"
              :closable="!config.disabled"
              :size="config.size"
              @close="handlePermissionRemove(permission.value)"
            >
              <el-icon class="tag-icon">
                <component :is="getPermissionIcon(permission.type)" />
              </el-icon>
              {{ permission.label }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import {
  ElCard,
  ElRow,
  ElCol,
  ElInput,
  ElButton,
  ElTag,
  ElIcon,
  ElAvatar,
  ElTree,
  ElScrollbar,
  ElSkeleton,
  ElEmpty,
  ElDivider
} from 'element-plus'
import {
  Key,
  UserFilled,
  Lock,
  Search,
  Check,
  Collection,
  Menu,
  Operation,
  Link
} from '@element-plus/icons-vue'
import { RoleAPI, PermissionAPI } from '@/api/system'
import type { 
  PermissionSelectorOption, 
  SelectorConfig,
  SelectorEmits 
} from './types'
import type { SysRoleVO, SysPermissionVO } from '@/types/system'

interface RoleOption {
  value: string
  label: string
  roleCode: string
  disabled?: boolean
  permissionIds?: string[]
}

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludePermissionIds?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludePermissionIds: () => []
})

const emit = defineEmits<SelectorEmits>()

// 响应式数据
const roleLoading = ref(false)
const permissionLoading = ref(false)
const roles = ref<RoleOption[]>([])
const permissions = ref<PermissionSelectorOption[]>([])
const selectedRoleId = ref<string>('')
const selectedRole = ref<RoleOption | null>(null)
const selectedPermissionIds = ref<string | string[]>()
const selectedPermissionOptions = ref<PermissionSelectorOption[]>([])
const roleFilterText = ref('')
const permissionFilterText = ref('')
const permissionTreeRef = ref()

// 默认配置
const defaultConfig: SelectorConfig = {
  multiple: true,
  clearable: true,
  placeholder: '请选择权限',
  size: 'default',
  disabled: false,
  checkStrictly: false
}

// 合并配置
const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 权限树属性
const permissionTreeProps = {
  children: 'children',
  label: 'label',
  disabled: 'disabled'
}

// 计算属性
const loading = computed(() => roleLoading.value || permissionLoading.value)

const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedPermissionIds.value) && selectedPermissionIds.value.length > 0
  }
  return selectedPermissionIds.value !== undefined && selectedPermissionIds.value !== null && selectedPermissionIds.value !== ''
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
    return selectedPermissionIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

const filteredRoles = computed(() => {
  if (!roleFilterText.value) return roles.value
  const filter = roleFilterText.value.toLowerCase()
  return roles.value.filter(role => 
    role.label.toLowerCase().includes(filter) ||
    role.roleCode.toLowerCase().includes(filter)
  )
})

const currentRolePermissions = computed(() => {
  if (!selectedRole.value?.permissionIds) return []
  return permissions.value.filter(permission => 
    selectedRole.value!.permissionIds!.includes(permission.value)
  )
})

const filteredPermissions = computed(() => {
  let result = currentRolePermissions.value

  if (permissionFilterText.value) {
    const filter = permissionFilterText.value.toLowerCase()
    result = result.filter(permission =>
      permission.label.toLowerCase().includes(filter)
    )
  }

  return result
})

// 方法
const loadRoles = async () => {
  try {
    roleLoading.value = true
    const response = await RoleAPI.getRoleList({
      status: props.onlyEnabled ? '0' : undefined
    })
    const data = response.data || []
    roles.value = data.map((role: SysRoleVO) => ({
      value: role.id,
      label: role.roleName,
      roleCode: role.roleCode,
      disabled: role.status !== '0',
      permissionIds: role.permissionIds || []
    }))
  } catch (error) {
    console.error('加载角色列表失败:', error)
    roles.value = []
  } finally {
    roleLoading.value = false
  }
}

const loadPermissions = async () => {
  try {
    permissionLoading.value = true
    const response = await PermissionAPI.getPermissionTree({
      status: props.onlyEnabled ? '0' : undefined
    })
    const data = response.data || []
    permissions.value = transformPermissionData(data)
  } catch (error) {
    console.error('加载权限列表失败:', error)
    permissions.value = []
  } finally {
    permissionLoading.value = false
  }
}

const transformPermissionData = (data: SysPermissionVO[]): PermissionSelectorOption[] => {
  const transform = (permissions: SysPermissionVO[]): PermissionSelectorOption[] => {
    return permissions
      .filter(permission => !props.excludePermissionIds.includes(permission.id))
      .map(permission => ({
        value: permission.id,
        label: permission.permissionName,
        type: permission.type,
        disabled: permission.status !== '0',
        parentId: permission.parentId,
        children: permission.children && permission.children.length > 0 ? transform(permission.children) : undefined
      }))
  }
  return transform(data)
}

const getPermissionIcon = (type?: string) => {
  switch (type) {
    case 'menu': return Menu
    case 'button': return Operation
    case 'api': return Link
    default: return Lock
  }
}

const getPermissionIconColor = (type?: string) => {
  switch (type) {
    case 'menu': return '#409eff'
    case 'button': return '#67c23a'
    case 'api': return '#e6a23c'
    default: return '#909399'
  }
}

const getPermissionTagType = (type?: string) => {
  switch (type) {
    case 'menu': return 'primary'
    case 'button': return 'success'
    case 'api': return 'warning'
    default: return 'info'
  }
}

const getPermissionTypeLabel = (type?: string) => {
  switch (type) {
    case 'menu': return '菜单'
    case 'button': return '按钮'
    case 'api': return '接口'
    default: return '权限'
  }
}

const filterPermissionNode = (value: string, data: PermissionSelectorOption) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}

const findPermissionsByIds = (ids: string | string[]): PermissionSelectorOption[] => {
  const targetIds = Array.isArray(ids) ? ids : (ids ? [ids] : [])
  const result: PermissionSelectorOption[] = []

  const findInTree = (permissions: PermissionSelectorOption[]) => {
    for (const permission of permissions) {
      if (targetIds.includes(permission.value)) {
        result.push(permission)
      }
      if (permission.children) {
        findInTree(permission.children)
      }
    }
  }

  findInTree(permissions.value)
  return result
}

// 事件处理
const handleRoleClick = (role: RoleOption) => {
  if (role.disabled) return

  selectedRoleId.value = role.value
  selectedRole.value = role

  // 清空权限搜索
  permissionFilterText.value = ''

  // 重置权限树的选中状态
  nextTick(() => {
    if (permissionTreeRef.value) {
      if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
        permissionTreeRef.value.setCheckedKeys(selectedPermissionIds.value)
      } else if (selectedPermissionIds.value) {
        permissionTreeRef.value.setCurrentKey(selectedPermissionIds.value)
      }
    }
  })
}

const handlePermissionCheck = (data: PermissionSelectorOption, checkedInfo: any) => {
  if (!config.value.multiple) return

  const checkedKeys = checkedInfo.checkedKeys
  selectedPermissionIds.value = checkedKeys
  selectedPermissionOptions.value = findPermissionsByIds(checkedKeys)

  emit('update:modelValue', checkedKeys)
  emit('change', checkedKeys, selectedPermissionOptions.value)
}

const handlePermissionClick = (data: PermissionSelectorOption) => {
  if (config.value.multiple || data.disabled) return

  selectedPermissionIds.value = data.value
  selectedPermissionOptions.value = [data]

  emit('update:modelValue', data.value)
  emit('change', data.value, data)
  emit('select', data.value, data)
}

const handlePermissionRemove = (permissionId: string) => {
  if (config.value.multiple && Array.isArray(selectedPermissionIds.value)) {
    const newIds = selectedPermissionIds.value.filter(id => id !== permissionId)
    selectedPermissionIds.value = newIds
    selectedPermissionOptions.value = findPermissionsByIds(newIds)

    emit('update:modelValue', newIds)
    emit('change', newIds, selectedPermissionOptions.value)
    emit('remove', permissionId)

    // 更新权限树选中状态
    nextTick(() => {
      if (permissionTreeRef.value) {
        permissionTreeRef.value.setCheckedKeys(newIds)
      }
    })
  }
}

const selectAllCurrentRolePermissions = () => {
  if (!config.value.multiple || !selectedRole.value?.permissionIds) return

  const allPermissionIds = selectedRole.value.permissionIds
  selectedPermissionIds.value = [...allPermissionIds]
  selectedPermissionOptions.value = findPermissionsByIds(allPermissionIds)

  emit('update:modelValue', allPermissionIds)
  emit('change', allPermissionIds, selectedPermissionOptions.value)

  // 更新权限树选中状态
  nextTick(() => {
    if (permissionTreeRef.value) {
      permissionTreeRef.value.setCheckedKeys(allPermissionIds)
    }
  })
}

const clearSelection = () => {
  const clearValue = config.value.multiple ? [] : ''
  selectedPermissionIds.value = clearValue
  selectedPermissionOptions.value = []

  emit('update:modelValue', clearValue)
  emit('change', clearValue, null)
  emit('clear')

  // 清空权限树选中状态
  nextTick(() => {
    if (permissionTreeRef.value) {
      if (config.value.multiple) {
        permissionTreeRef.value.setCheckedKeys([])
      } else {
        permissionTreeRef.value.setCurrentKey(null)
      }
    }
  })
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedPermissionIds.value = newValue || (config.value.multiple ? [] : '')
  selectedPermissionOptions.value = findPermissionsByIds(selectedPermissionIds.value)

  // 同步权限树选中状态
  nextTick(() => {
    if (permissionTreeRef.value) {
      if (config.value.multiple && Array.isArray(newValue)) {
        permissionTreeRef.value.setCheckedKeys(newValue)
      } else if (newValue) {
        permissionTreeRef.value.setCurrentKey(newValue)
      }
    }
  })
}, { immediate: true })

// 监听权限搜索
watch(permissionFilterText, (value) => {
  if (permissionTreeRef.value) {
    permissionTreeRef.value.filter(value)
  }
})

// 组件挂载
onMounted(async () => {
  await Promise.all([loadRoles(), loadPermissions()])
})

// 暴露方法
defineExpose({
  refresh: () => Promise.all([loadRoles(), loadPermissions()]),
  clearSelection,
  selectAllCurrentRolePermissions
})
</script>

<style scoped>
.el-permission-by-role-selector {
  width: 100%;
}

.selector-card {
  border: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.role-search,
.permission-search {
  margin-bottom: 12px;
}

.role-list,
.permission-tree {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.role-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  gap: 12px;
}

.role-item:hover {
  background: var(--el-fill-color-light);
}

.role-item.is-selected {
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
}

.role-item.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.role-info {
  flex: 1;
  min-width: 0;
}

.role-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.role-details {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  display: flex;
  gap: 8px;
}

.permission-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.permission-icon {
  flex-shrink: 0;
}

.permission-label {
  flex: 1;
  min-width: 0;
}

.selected-permissions {
  margin-top: 16px;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-icon {
  margin-right: 4px;
}
</style>
