-- 为ai_chat_record表添加input字段
-- 用于存储输入参数（JSON格式），增强AI对话记录的功能

-- 检查并添加input字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE ai_chat_record ADD COLUMN input TEXT COMMENT "输入参数（JSON格式）"',
        'SELECT "input字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ai_chat_record' 
        AND COLUMN_NAME = 'input'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构变更结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ai_chat_record' 
    AND TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'input'
ORDER BY ORDINAL_POSITION;

-- 显示字段添加成功信息
SELECT 'ai_chat_record表input字段添加完成' as result;
