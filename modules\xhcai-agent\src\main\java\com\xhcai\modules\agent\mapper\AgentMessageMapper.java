package com.xhcai.modules.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.agent.entity.AgentMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 智能体消息Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AgentMessageMapper extends BaseMapper<AgentMessage> {

    /**
     * 分页查询对话消息列表
     *
     * @param page 分页参数
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return 消息列表
     */
    @Select("SELECT * FROM agent_message "
            + "WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "ORDER BY sequence_number ASC")
    IPage<AgentMessage> selectConversationMessages(Page<AgentMessage> page,
            @Param("conversationId") String conversationId,
            @Param("tenantId") String tenantId);

    /**
     * 查询对话的所有消息（按序号排序）
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return 消息列表
     */
    @Select("SELECT * FROM agent_message WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND deleted = 0 ORDER BY sequence_number ASC")
    List<AgentMessage> selectMessagesByConversation(@Param("conversationId") String conversationId, @Param("tenantId") String tenantId);

    /**
     * 查询对话的最新消息
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 消息列表
     */
    @Select("<script>"
            + "SELECT * FROM agent_message "
            + "WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND deleted = 0 "
            + "ORDER BY sequence_number DESC "
            + "<if test='limit != null'>"
            + "    LIMIT #{limit}"
            + "</if>"
            + "</script>")
    List<AgentMessage> selectLatestMessages(@Param("conversationId") String conversationId,
            @Param("tenantId") String tenantId,
            @Param("limit") Integer limit);

    /**
     * 获取对话中的下一个序号
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return 下一个序号
     */
    @Select("SELECT COALESCE(MAX(sequence_number), 0) + 1 FROM agent_message WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND deleted = 0")
    Integer getNextSequenceNumber(@Param("conversationId") String conversationId, @Param("tenantId") String tenantId);

    /**
     * 统计对话的消息数量
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return 消息数量
     */
    @Select("SELECT COUNT(*) FROM agent_message WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND deleted = 0")
    Integer countByConversationId(@Param("conversationId") String conversationId, @Param("tenantId") String tenantId);

    /**
     * 统计对话中用户消息数量
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return 用户消息数量
     */
    @Select("SELECT COUNT(*) FROM agent_message WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND message_type = 'user' AND deleted = 0")
    Integer countUserMessages(@Param("conversationId") String conversationId, @Param("tenantId") String tenantId);

    /**
     * 统计对话中助手消息数量
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return 助手消息数量
     */
    @Select("SELECT COUNT(*) FROM agent_message WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND message_type = 'assistant' AND deleted = 0")
    Integer countAssistantMessages(@Param("conversationId") String conversationId, @Param("tenantId") String tenantId);

    /**
     * 更新消息状态
     *
     * @param id 消息ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE agent_message SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateMessageStatus(@Param("id") String id, @Param("status") String status);

    /**
     * 更新消息处理时间和token统计
     *
     * @param id 消息ID
     * @param processingTime 处理时间
     * @param tokens token消耗
     * @param inputTokens 输入token消耗
     * @param outputTokens 输出token消耗
     * @param cost 费用
     * @return 更新行数
     */
    @Update("UPDATE agent_message SET processing_time = #{processingTime}, tokens = #{tokens}, input_tokens = #{inputTokens}, output_tokens = #{outputTokens}, cost = #{cost}, update_time = NOW() WHERE id = #{id}")
    int updateMessageStats(@Param("id") String id,
            @Param("processingTime") Long processingTime,
            @Param("tokens") Integer tokens,
            @Param("inputTokens") Integer inputTokens,
            @Param("outputTokens") Integer outputTokens,
            @Param("cost") Long cost);

    /**
     * 更新消息错误信息
     *
     * @param id 消息ID
     * @param errorMessage 错误信息
     * @return 更新行数
     */
    @Update("UPDATE agent_message SET error_message = #{errorMessage}, status = 'failed', update_time = NOW() WHERE id = #{id}")
    int updateMessageError(@Param("id") String id, @Param("errorMessage") String errorMessage);

    /**
     * 统计对话的token消耗
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return token统计信息
     */
    @Select("SELECT "
            + "    COALESCE(SUM(tokens), 0) as totalTokens, "
            + "    COALESCE(SUM(input_tokens), 0) as inputTokens, "
            + "    COALESCE(SUM(output_tokens), 0) as outputTokens, "
            + "    COALESCE(SUM(cost), 0) as totalCost, "
            + "    COUNT(*) as messageCount "
            + "FROM agent_message "
            + "WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId} AND deleted = 0")
    TokenStatsVO getTokenStatsByConversation(@Param("conversationId") String conversationId, @Param("tenantId") String tenantId);

    /**
     * 统计智能体的token消耗
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return token统计信息
     */
    @Select("<script>"
            + "SELECT "
            + "    COALESCE(SUM(am.tokens), 0) as totalTokens, "
            + "    COALESCE(SUM(am.input_tokens), 0) as inputTokens, "
            + "    COALESCE(SUM(am.output_tokens), 0) as outputTokens, "
            + "    COALESCE(SUM(am.cost), 0) as totalCost, "
            + "    COUNT(*) as messageCount "
            + "FROM agent_message am "
            + "INNER JOIN agent_conversation ac ON am.conversation_id = ac.id "
            + "WHERE ac.agent_id = #{agentId} AND am.tenant_id = #{tenantId} AND am.deleted = 0 "
            + "<if test='startTime != null'>"
            + "    AND am.create_time >= #{startTime} "
            + "</if>"
            + "<if test='endTime != null'>"
            + "    AND am.create_time &lt;= #{endTime} "
            + "</if>"
            + "</script>")
    TokenStatsVO getTokenStatsByAgent(@Param("agentId") String agentId,
            @Param("tenantId") String tenantId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 批量删除对话的消息
     *
     * @param conversationId 对话ID
     * @param tenantId 租户ID
     * @return 删除行数
     */
    @Update("UPDATE agent_message SET deleted = 1, update_time = NOW() WHERE conversation_id = #{conversationId} AND tenant_id = #{tenantId}")
    int deleteByConversationId(@Param("conversationId") String conversationId, @Param("tenantId") String tenantId);

    /**
     * Token统计信息VO
     */
    class TokenStatsVO {

        private Long totalTokens;
        private Long inputTokens;
        private Long outputTokens;
        private Long totalCost;
        private Integer messageCount;

        // Getters and Setters
        public Long getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(Long totalTokens) {
            this.totalTokens = totalTokens;
        }

        public Long getInputTokens() {
            return inputTokens;
        }

        public void setInputTokens(Long inputTokens) {
            this.inputTokens = inputTokens;
        }

        public Long getOutputTokens() {
            return outputTokens;
        }

        public void setOutputTokens(Long outputTokens) {
            this.outputTokens = outputTokens;
        }

        public Long getTotalCost() {
            return totalCost;
        }

        public void setTotalCost(Long totalCost) {
            this.totalCost = totalCost;
        }

        public Integer getMessageCount() {
            return messageCount;
        }

        public void setMessageCount(Integer messageCount) {
            this.messageCount = messageCount;
        }
    }
}
