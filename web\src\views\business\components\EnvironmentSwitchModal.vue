<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="fas fa-exchange-alt"></i>
          切换环境
        </h3>
        <button class="modal-close" @click="handleClose">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <div class="project-info">
          <h4>{{ project?.name }}</h4>
          <p>{{ project?.description }}</p>
        </div>

        <div class="current-env">
          <div class="env-label">当前环境:</div>
          <div :class="['env-badge', `env-${project?.environment}`]">
            {{ getEnvironmentLabel(project?.environment) }}
          </div>
        </div>

        <div class="env-selection">
          <h5 class="section-title">选择目标环境</h5>
          <div class="env-options">
            <div
              v-for="env in environmentOptions"
              :key="env.value"
              class="env-option"
              :class="{ 
                active: selectedEnvironment === env.value,
                disabled: env.value === project?.environment
              }"
              @click="selectEnvironment(env.value)"
            >
              <div class="env-icon" :style="{ background: env.color }">
                <i :class="env.icon"></i>
              </div>
              <div class="env-details">
                <div class="env-name">{{ env.label }}</div>
                <div class="env-description">{{ env.description }}</div>
              </div>
              <div v-if="env.value === project?.environment" class="current-indicator">
                当前
              </div>
            </div>
          </div>
        </div>

        <div v-if="selectedEnvironment && selectedEnvironment !== project?.environment" class="warning-section">
          <div class="warning-box">
            <div class="warning-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="warning-content">
              <h6>注意事项</h6>
              <ul>
                <li>切换环境将影响所有相关的智能体、知识库和知识图谱</li>
                <li>请确保目标环境的配置和资源已准备就绪</li>
                <li>切换过程中可能会有短暂的服务中断</li>
                <li>建议在业务低峰期进行环境切换</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="handleClose">
          取消
        </button>
        <button 
          class="btn btn-primary" 
          @click="handleConfirm"
          :disabled="!selectedEnvironment || selectedEnvironment === project?.environment"
        >
          确认切换
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 类型定义
interface BusinessProject {
  id: string
  name: string
  description: string
  environment: 'production' | 'test' | 'development'
}

// Props
const props = defineProps<{
  visible: boolean
  project?: BusinessProject | null
}>()

// Emits
const emit = defineEmits<{
  close: []
  save: [environment: string]
}>()

// 响应式数据
const selectedEnvironment = ref<string>('')

// 环境选项
const environmentOptions = [
  {
    value: 'production',
    label: '生产环境',
    description: '面向最终用户的正式环境',
    icon: 'fas fa-server',
    color: 'linear-gradient(135deg, #2ecc71, #27ae60)'
  },
  {
    value: 'test',
    label: '测试环境',
    description: '用于功能测试和验证的环境',
    icon: 'fas fa-flask',
    color: 'linear-gradient(135deg, #3498db, #2980b9)'
  },
  {
    value: 'development',
    label: '开发环境',
    description: '用于开发和调试的环境',
    icon: 'fas fa-code',
    color: 'linear-gradient(135deg, #f1c40f, #f39c12)'
  }
]

// 方法
const getEnvironmentLabel = (env?: string) => {
  const option = environmentOptions.find(opt => opt.value === env)
  return option?.label || env
}

const selectEnvironment = (env: string) => {
  if (env !== props.project?.environment) {
    selectedEnvironment.value = env
  }
}

const handleConfirm = () => {
  if (selectedEnvironment.value && selectedEnvironment.value !== props.project?.environment) {
    emit('save', selectedEnvironment.value)
  }
}

const handleClose = () => {
  emit('close')
}

const handleOverlayClick = () => {
  handleClose()
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    selectedEnvironment.value = ''
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-light);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-light);
}

/* 项目信息 */
.project-info {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.project-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.project-info p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* 当前环境 */
.current-env {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius);
}

.env-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.env-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.env-badge.env-production {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.env-badge.env-test {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.env-badge.env-development {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

/* 环境选择 */
.env-selection {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.env-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.env-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.env-option:hover:not(.disabled) {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.02);
}

.env-option.active {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.env-option.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(0, 0, 0, 0.02);
}

.env-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.env-icon i {
  font-size: 20px;
  color: white;
}

.env-details {
  flex: 1;
  min-width: 0;
}

.env-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.env-description {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.current-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: var(--primary-gradient);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
}

/* 警告区域 */
.warning-section {
  margin-bottom: 0;
}

.warning-box {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: rgba(230, 126, 34, 0.05);
  border: 1px solid rgba(230, 126, 34, 0.2);
  border-radius: var(--border-radius);
}

.warning-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.warning-icon i {
  font-size: 18px;
  color: #e67e22;
}

.warning-content {
  flex: 1;
}

.warning-content h6 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.warning-content ul {
  margin: 0;
  padding-left: 16px;
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.warning-content li {
  margin-bottom: 4px;
}

.warning-content li:last-child {
  margin-bottom: 0;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.btn-secondary {
  background: white;
  color: var(--text-primary);
  border: 2px solid var(--border-light);
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .modal-container {
    max-width: 90vw;
  }
  
  .env-option {
    padding: 12px;
  }
  
  .env-icon {
    width: 40px;
    height: 40px;
  }
  
  .env-icon i {
    font-size: 16px;
  }
  
  .warning-box {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
