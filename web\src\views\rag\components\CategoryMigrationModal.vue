<template>
  <div class="modal-overlay" v-if="visible" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h4>
          <i class="fas fa-folder-open"></i>
          迁移分类
        </h4>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <!-- 选中文件信息 -->
        <div class="selected-files-info">
          <h5>已选择 {{ selectedFiles.length }} 个文件</h5>
          <div class="file-list">
            <div 
              v-for="file in selectedFiles.slice(0, 5)" 
              :key="file.id" 
              class="file-item"
            >
              <div class="file-icon" :style="{background: file.docStyle}">
                {{ file.docIcon }}
              </div>
              <span class="file-name">{{ file.name }}</span>
            </div>
            <div v-if="selectedFiles.length > 5" class="more-files">
              还有 {{ selectedFiles.length - 5 }} 个文件...
            </div>
          </div>
        </div>

        <!-- 分类选择 -->
        <div class="category-selection">
          <h5>选择目标分类</h5>
          <div class="category-tree">
            <!-- 根分类选项 -->
            <div 
              class="tree-item root-item" 
              :class="{ active: selectedCategoryId === null }" 
              @click="selectCategory(null)"
            >
              <div class="item-content">
                <i class="fas fa-home item-icon"></i>
                <span class="item-name">根目录（无分类）</span>
              </div>
            </div>

            <!-- 分类树 -->
            <template v-if="Array.isArray(categories)">
              <CategoryTreeNode
                v-for="category in rootCategories"
                :key="category.id"
                :category="category"
                :level="0"
                :selected-id="selectedCategoryId"
                :all-categories="categories"
                :selection-mode="true"
                @select="selectCategory"
              />
            </template>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-outline" @click="closeModal">取消</button>
        <button 
          class="btn btn-primary" 
          @click="confirmMigration"
          :disabled="migrating"
        >
          <i class="fas fa-spinner fa-spin" v-if="migrating"></i>
          <i class="fas fa-check" v-else></i>
          {{ migrating ? '迁移中...' : '确认迁移' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import CategoryTreeNode from './CategoryTreeNode.vue'
import KnowledgeAPI from '@/api/knowledge'

// Props
interface Props {
  visible: boolean
  selectedFiles: any[]
  datasetId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  migrationSuccess: []
}>()

// 响应式数据
const categories = ref<any[]>([])
const selectedCategoryId = ref<string | null>(null)
const migrating = ref(false)

// 计算属性
const rootCategories = computed(() => {
  return categories.value.filter(cat => !cat.parentId)
})

// 方法
const loadCategories = async () => {
  try {
    const response = await KnowledgeAPI.getCategoryTree(props.datasetId)
    if (response.success && response.data) {
      categories.value = response.data
    } else {
      // 使用模拟数据作为后备
      categories.value = [
        {
          id: '1',
          name: '产品文档',
          description: '产品相关的文档资料',
          parentId: null,
          level: 0,
          sortOrder: 1,
          fileCount: 15,
          enabled: true
        },
        {
          id: '2',
          name: '技术文档',
          description: '技术开发相关文档',
          parentId: null,
          level: 0,
          sortOrder: 2,
          fileCount: 23,
          enabled: true
        },
        {
          id: '3',
          name: '需求文档',
          description: '产品需求相关文档',
          parentId: '1',
          level: 1,
          sortOrder: 1,
          fileCount: 8,
          enabled: true
        },
        {
          id: '4',
          name: '设计文档',
          description: '产品设计相关文档',
          parentId: '1',
          level: 1,
          sortOrder: 2,
          fileCount: 7,
          enabled: true
        },
        {
          id: '5',
          name: 'API文档',
          description: 'API接口文档',
          parentId: '2',
          level: 1,
          sortOrder: 1,
          fileCount: 12,
          enabled: true
        }
      ]
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    categories.value = []
  }
}

const selectCategory = (categoryId: string | null) => {
  selectedCategoryId.value = categoryId
}

const confirmMigration = async () => {
  if (props.selectedFiles.length === 0) {
    return
  }

  migrating.value = true
  
  try {
    const documentIds = props.selectedFiles.map(file => file.id)
    const response = await KnowledgeAPI.batchUpdateDocumentCategory(documentIds, selectedCategoryId.value || undefined)
    
    if (response.success) {
      emit('migrationSuccess')
      alert(`成功将 ${props.selectedFiles.length} 个文件迁移到${selectedCategoryId.value ? '指定分类' : '根目录'}`)
    } else {
      alert('迁移失败：' + response.message)
    }
  } catch (error) {
    console.error('迁移分类失败:', error)
    alert('迁移失败，请重试')
  } finally {
    migrating.value = false
  }
}

const closeModal = () => {
  emit('close')
}

// 生命周期
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.modal-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.selected-files-info {
  margin-bottom: 24px;
}

.selected-files-info h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.file-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.file-name {
  font-size: 13px;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-files {
  font-size: 12px;
  color: #64748b;
  text-align: center;
  padding: 8px;
  background: #f1f5f9;
  border-radius: 4px;
}

.category-selection h5 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.category-tree {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  max-height: 300px;
  overflow-y: auto;
}

.tree-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.tree-item:hover {
  background: #f8fafc;
}

.tree-item.active {
  background: #eff6ff;
  border-right: 3px solid #3b82f6;
}

.root-item .item-content {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-icon {
  color: #64748b;
  font-size: 14px;
  width: 16px;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-outline {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-outline:hover {
  background: #f8fafc;
  color: #334155;
}
</style>
