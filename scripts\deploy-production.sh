#!/bin/bash

# 生产环境部署脚本
# 用于部署XHC AI Plus平台到生产环境

set -e

echo "=== XHC AI Plus 生产环境部署开始 ==="

# 配置变量
APP_NAME="xhcai-plus"
APP_VERSION="1.0.0"
DEPLOY_USER="xhcai"
DEPLOY_DIR="/opt/xhcai-plus"
SERVICE_NAME="xhcai-plus"
BACKUP_DIR="/opt/xhcai-plus/backup"
LOG_DIR="/var/log/xhcai-plus"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本"
    exit 1
fi

# 创建部署用户
create_deploy_user() {
    echo "创建部署用户..."
    if ! id "$DEPLOY_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d "$DEPLOY_DIR" "$DEPLOY_USER"
        echo "用户 $DEPLOY_USER 创建成功"
    else
        echo "用户 $DEPLOY_USER 已存在"
    fi
}

# 创建目录结构
create_directories() {
    echo "创建目录结构..."
    
    mkdir -p "$DEPLOY_DIR"/{bin,config,plugins/{storage,model,notify},logs,temp,backup}
    mkdir -p "$LOG_DIR"
    
    # 设置目录权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR"
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$LOG_DIR"
    
    chmod 755 "$DEPLOY_DIR"
    chmod 755 "$LOG_DIR"
    chmod 750 "$DEPLOY_DIR/config"
    
    echo "目录结构创建完成"
}

# 部署应用
deploy_application() {
    echo "部署应用..."
    
    # 停止现有服务
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        echo "停止现有服务..."
        systemctl stop "$SERVICE_NAME"
    fi
    
    # 备份现有版本
    if [ -f "$DEPLOY_DIR/bin/$APP_NAME.jar" ]; then
        echo "备份现有版本..."
        BACKUP_NAME="$APP_NAME-$(date +%Y%m%d-%H%M%S).jar"
        cp "$DEPLOY_DIR/bin/$APP_NAME.jar" "$BACKUP_DIR/$BACKUP_NAME"
        echo "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    fi
    
    # 复制新版本
    if [ -f "admin-api/target/admin-api-$APP_VERSION.jar" ]; then
        cp "admin-api/target/admin-api-$APP_VERSION.jar" "$DEPLOY_DIR/bin/$APP_NAME.jar"
        chown "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR/bin/$APP_NAME.jar"
        chmod 755 "$DEPLOY_DIR/bin/$APP_NAME.jar"
        echo "应用部署完成"
    else
        echo "错误: 找不到应用JAR文件"
        exit 1
    fi
}

# 部署插件
deploy_plugins() {
    echo "部署插件..."
    
    # 检查插件构建包
    PLUGIN_PACKAGE=$(ls build/plugins-*.tar.gz 2>/dev/null | head -1)
    if [ -n "$PLUGIN_PACKAGE" ]; then
        echo "部署插件包: $PLUGIN_PACKAGE"
        
        # 备份现有插件
        if [ -d "$DEPLOY_DIR/plugins" ] && [ "$(ls -A $DEPLOY_DIR/plugins)" ]; then
            PLUGIN_BACKUP_NAME="plugins-$(date +%Y%m%d-%H%M%S).tar.gz"
            tar -czf "$BACKUP_DIR/$PLUGIN_BACKUP_NAME" -C "$DEPLOY_DIR" plugins
            echo "插件备份完成: $BACKUP_DIR/$PLUGIN_BACKUP_NAME"
        fi
        
        # 解压新插件
        tar -xzf "$PLUGIN_PACKAGE" -C "$DEPLOY_DIR/plugins"
        chown -R "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR/plugins"
        
        echo "插件部署完成"
    else
        echo "警告: 未找到插件包，跳过插件部署"
    fi
}

# 创建systemd服务
create_systemd_service() {
    echo "创建systemd服务..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=XHC AI Plus Application
After=network.target postgresql.service redis.service

[Service]
Type=forking
User=$DEPLOY_USER
Group=$DEPLOY_USER
WorkingDirectory=$DEPLOY_DIR
ExecStart=/bin/bash $DEPLOY_DIR/bin/start.sh
ExecStop=/bin/bash $DEPLOY_DIR/bin/stop.sh
ExecReload=/bin/bash $DEPLOY_DIR/bin/restart.sh
PIDFile=$DEPLOY_DIR/logs/application.pid
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-17-openjdk
Environment=SPRING_PROFILES_ACTIVE=prod
Environment=LOG_FILE_PATH=$LOG_DIR/application.log

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    echo "systemd服务创建完成"
}

# 创建启动脚本
create_start_scripts() {
    echo "创建启动脚本..."
    
    # 启动脚本
    cat > "$DEPLOY_DIR/bin/start.sh" << 'EOF'
#!/bin/bash

APP_NAME="xhcai-plus"
DEPLOY_DIR="/opt/xhcai-plus"
PID_FILE="$DEPLOY_DIR/logs/application.pid"
LOG_FILE="/var/log/xhcai-plus/application.log"

# JVM参数
JAVA_OPTS="-server"
JAVA_OPTS="$JAVA_OPTS -Xms2g -Xmx4g"
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -XX:+UnlockExperimentalVMOptions"
JAVA_OPTS="$JAVA_OPTS -XX:+UseStringDeduplication"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=$DEPLOY_DIR/logs/"
JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Duser.timezone=Asia/Shanghai"

# Spring Boot参数
SPRING_OPTS="--spring.profiles.active=prod"
SPRING_OPTS="$SPRING_OPTS --server.port=8080"
SPRING_OPTS="$SPRING_OPTS --logging.file.name=$LOG_FILE"

cd "$DEPLOY_DIR"

echo "启动 $APP_NAME..."
nohup java $JAVA_OPTS -jar bin/$APP_NAME.jar $SPRING_OPTS > /dev/null 2>&1 &
echo $! > "$PID_FILE"

echo "$APP_NAME 启动完成，PID: $(cat $PID_FILE)"
EOF

    # 停止脚本
    cat > "$DEPLOY_DIR/bin/stop.sh" << 'EOF'
#!/bin/bash

APP_NAME="xhcai-plus"
DEPLOY_DIR="/opt/xhcai-plus"
PID_FILE="$DEPLOY_DIR/logs/application.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "停止 $APP_NAME (PID: $PID)..."
        kill $PID
        
        # 等待进程结束
        for i in {1..30}; do
            if ! ps -p $PID > /dev/null; then
                break
            fi
            sleep 1
        done
        
        # 强制杀死进程
        if ps -p $PID > /dev/null; then
            echo "强制停止 $APP_NAME..."
            kill -9 $PID
        fi
        
        rm -f "$PID_FILE"
        echo "$APP_NAME 已停止"
    else
        echo "$APP_NAME 未运行"
        rm -f "$PID_FILE"
    fi
else
    echo "PID文件不存在，$APP_NAME 可能未运行"
fi
EOF

    # 重启脚本
    cat > "$DEPLOY_DIR/bin/restart.sh" << 'EOF'
#!/bin/bash

DEPLOY_DIR="/opt/xhcai-plus"

echo "重启应用..."
bash "$DEPLOY_DIR/bin/stop.sh"
sleep 3
bash "$DEPLOY_DIR/bin/start.sh"
EOF

    # 状态检查脚本
    cat > "$DEPLOY_DIR/bin/status.sh" << 'EOF'
#!/bin/bash

APP_NAME="xhcai-plus"
DEPLOY_DIR="/opt/xhcai-plus"
PID_FILE="$DEPLOY_DIR/logs/application.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "$APP_NAME 正在运行 (PID: $PID)"
        
        # 检查端口
        if netstat -tlnp | grep ":8080 " > /dev/null; then
            echo "端口 8080 正在监听"
        else
            echo "警告: 端口 8080 未监听"
        fi
        
        # 检查健康状态
        if curl -s http://localhost:8080/actuator/health > /dev/null; then
            echo "应用健康检查通过"
        else
            echo "警告: 应用健康检查失败"
        fi
    else
        echo "$APP_NAME 未运行"
        rm -f "$PID_FILE"
    fi
else
    echo "$APP_NAME 未运行"
fi
EOF

    # 设置脚本权限
    chmod +x "$DEPLOY_DIR/bin"/*.sh
    chown "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR/bin"/*.sh
    
    echo "启动脚本创建完成"
}

# 配置日志轮转
configure_logrotate() {
    echo "配置日志轮转..."
    
    cat > "/etc/logrotate.d/$APP_NAME" << EOF
$LOG_DIR/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $DEPLOY_USER $DEPLOY_USER
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF

    echo "日志轮转配置完成"
}

# 主函数
main() {
    echo "开始部署到生产环境..."
    
    create_deploy_user
    create_directories
    deploy_application
    deploy_plugins
    create_systemd_service
    create_start_scripts
    configure_logrotate
    
    echo ""
    echo "=== 部署完成 ==="
    echo "应用目录: $DEPLOY_DIR"
    echo "日志目录: $LOG_DIR"
    echo "服务名称: $SERVICE_NAME"
    echo ""
    echo "启动服务: systemctl start $SERVICE_NAME"
    echo "查看状态: systemctl status $SERVICE_NAME"
    echo "查看日志: journalctl -u $SERVICE_NAME -f"
    echo "应用状态: $DEPLOY_DIR/bin/status.sh"
    echo ""
    echo "健康检查: curl http://localhost:8080/actuator/health"
    echo "插件状态: curl http://localhost:8080/api/plugin-context/status"
}

# 执行主函数
main "$@"
