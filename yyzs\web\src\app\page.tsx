'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Activity, Server, Database, Monitor, Upload, Settings, Play, Square, RotateCcw, Trash2, AlertTriangle, Bell, Globe } from 'lucide-react';
import ComponentsAPI from '@/api/components';
import MonitorAPI from '@/api/monitor';
import { ElasticComponent, ComponentStatistics, ComponentStatus } from '@/types/component';
import { SystemResourceUsage } from '@/types/monitor';
import toast from 'react-hot-toast';

export default function HomePage() {
  const router = useRouter();
  const [components, setComponents] = useState<ElasticComponent[]>([]);
  const [statistics, setStatistics] = useState<ComponentStatistics | null>(null);
  const [systemResources, setSystemResources] = useState<SystemResourceUsage | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 并行加载数据
      const [componentsRes, statisticsRes, resourcesRes] = await Promise.all([
        ComponentsAPI.getComponents(),
        ComponentsAPI.getComponentStatistics(),
        MonitorAPI.getSystemResourceUsage(),
      ]);

      if (componentsRes.success && componentsRes.data) {
        setComponents(componentsRes.data);
      }

      if (statisticsRes.success && statisticsRes.data) {
        setStatistics(statisticsRes.data);
      }

      if (resourcesRes.success && resourcesRes.data) {
        setSystemResources(resourcesRes.data);
      }
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 启动组件
  const handleStartComponent = async (componentId: string) => {
    try {
      const response = await ComponentsAPI.startComponent(componentId);
      if (response.success) {
        toast.success('组件启动成功');
        loadDashboardData();
      } else {
        toast.error(response.message || '启动失败');
      }
    } catch (error) {
      console.error('启动组件失败:', error);
      toast.error('启动失败，请重试');
    }
  };

  // 停止组件
  const handleStopComponent = async (componentId: string) => {
    try {
      const response = await ComponentsAPI.stopComponent(componentId);
      if (response.success) {
        toast.success('组件停止成功');
        loadDashboardData();
      } else {
        toast.error(response.message || '停止失败');
      }
    } catch (error) {
      console.error('停止组件失败:', error);
      toast.error('停止失败，请重试');
    }
  };

  // 重启组件
  const handleRestartComponent = async (componentId: string) => {
    try {
      const response = await ComponentsAPI.restartComponent(componentId);
      if (response.success) {
        toast.success('组件重启成功');
        loadDashboardData();
      } else {
        toast.error(response.message || '重启失败');
      }
    } catch (error) {
      console.error('重启组件失败:', error);
      toast.error('重启失败，请重试');
    }
  };

  // 卸载组件
  const handleUninstallComponent = (component: ElasticComponent) => {
    router.push(`/uninstall/${component.id}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Server className="h-8 w-8 text-primary-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">YYZS Agent Platform</h1>
                <p className="text-sm text-gray-500">Elastic Stack 组件管理平台</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                className="btn-outline"
                onClick={() => router.push('/settings')}
              >
                <Settings className="h-4 w-4 mr-2" />
                设置
              </button>
              <button
                className="btn-outline"
                onClick={() => router.push('/alerts')}
              >
                <Bell className="h-4 w-4 mr-2" />
                告警管理
              </button>
              <button
                className="btn-outline"
                onClick={() => router.push('/api-docs')}
              >
                <Globe className="h-4 w-4 mr-2" />
                接口开放
              </button>
              <button
                className="btn-primary"
                onClick={() => router.push('/install')}
              >
                <Upload className="h-4 w-4 mr-2" />
                安装组件
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Database className="h-8 w-8 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总组件数</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {statistics?.totalCount || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-8 w-8 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">运行中</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {statistics?.runningCount || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Server className="h-8 w-8 text-warning-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">已安装</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {statistics?.installedCount || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Monitor className="h-8 w-8 text-secondary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">CPU使用率</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {systemResources?.cpuUsage?.toFixed(1) || 0}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 系统资源使用情况 */}
        {systemResources && (
          <div className="card mb-8">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">系统资源使用情况</h3>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* CPU使用率 */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">CPU</span>
                    <span className="text-sm text-gray-500">
                      {systemResources.cpuUsage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${systemResources.cpuUsage}%` }}
                    ></div>
                  </div>
                </div>

                {/* 内存使用率 */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">内存</span>
                    <span className="text-sm text-gray-500">
                      {systemResources.memoryUsagePercent.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-success-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${systemResources.memoryUsagePercent}%` }}
                    ></div>
                  </div>
                </div>

                {/* 磁盘使用率 */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">磁盘</span>
                    <span className="text-sm text-gray-500">
                      {systemResources.diskUsagePercent.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-warning-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${systemResources.diskUsagePercent}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 组件列表 */}
        <div className="card">
          <div className="card-header">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">组件列表</h3>
              <button 
                onClick={loadDashboardData}
                className="btn-outline btn-sm"
              >
                刷新
              </button>
            </div>
          </div>
          <div className="card-body p-0">
            {components.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="table">
                  <thead className="table-header">
                    <tr>
                      <th className="table-header-cell">组件名称</th>
                      <th className="table-header-cell">类型</th>
                      <th className="table-header-cell">版本</th>
                      <th className="table-header-cell">状态</th>
                      <th className="table-header-cell">端口</th>
                      <th className="table-header-cell">更新时间</th>
                      <th className="table-header-cell">操作</th>
                    </tr>
                  </thead>
                  <tbody className="table-body">
                    {components.slice(0, 10).map((component) => (
                      <tr key={component.id} className="table-row">
                        <td className="table-cell font-medium">{component.name}</td>
                        <td className="table-cell">{component.type}</td>
                        <td className="table-cell">{component.version}</td>
                        <td className="table-cell">
                          <span className={`status-badge ${getStatusColor(component.status)}`}>
                            {getStatusLabel(component.status)}
                          </span>
                        </td>
                        <td className="table-cell">{component.port || '-'}</td>
                        <td className="table-cell">
                          {new Date(component.updateTime).toLocaleString()}
                        </td>
                        <td className="table-cell">
                          <div className="flex items-center space-x-2">
                            {component.status === ComponentStatus.UNINSTALLED ? (
                              <button
                                onClick={() => router.push(`/install/${component.type}`)}
                                className="btn-sm btn-primary"
                                title="安装组件"
                              >
                                <Upload className="h-3 w-3" />
                              </button>
                            ) : (
                              <>
                                {component.status === ComponentStatus.STOPPED && (
                                  <button
                                    onClick={() => handleStartComponent(component.id)}
                                    className="btn-sm btn-success"
                                    title="启动组件"
                                  >
                                    <Play className="h-3 w-3" />
                                  </button>
                                )}
                                {component.status === ComponentStatus.RUNNING && (
                                  <button
                                    onClick={() => handleStopComponent(component.id)}
                                    className="btn-sm btn-warning"
                                    title="停止组件"
                                  >
                                    <Square className="h-3 w-3" />
                                  </button>
                                )}
                                {(component.status === ComponentStatus.RUNNING || component.status === ComponentStatus.STOPPED) && (
                                  <button
                                    onClick={() => handleRestartComponent(component.id)}
                                    className="btn-sm btn-secondary"
                                    title="重启组件"
                                  >
                                    <RotateCcw className="h-3 w-3" />
                                  </button>
                                )}
                                <button
                                  onClick={() => handleUninstallComponent(component)}
                                  className="btn-sm btn-error"
                                  title="卸载组件"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <Server className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">暂无组件数据</p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'RUNNING':
      return 'text-success-600 bg-success-50';
    case 'STOPPED':
      return 'text-secondary-600 bg-secondary-100';
    case 'ERROR':
      return 'text-error-600 bg-error-50';
    case 'INSTALLING':
      return 'text-warning-600 bg-warning-50';
    default:
      return 'text-secondary-400 bg-secondary-50';
  }
}

function getStatusLabel(status: string): string {
  switch (status) {
    case 'RUNNING':
      return '运行中';
    case 'STOPPED':
      return '已停止';
    case 'ERROR':
      return '错误';
    case 'INSTALLING':
      return '安装中';
    case 'INSTALLED':
      return '已安装';
    case 'UNINSTALLED':
      return '未安装';
    default:
      return status;
  }
}
