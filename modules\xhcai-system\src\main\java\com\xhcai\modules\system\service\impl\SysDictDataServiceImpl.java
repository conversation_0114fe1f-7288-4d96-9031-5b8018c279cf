package com.xhcai.modules.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.utils.XhcaiUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.SysDictDataQueryDTO;
import com.xhcai.modules.system.entity.SysDictData;
import com.xhcai.modules.system.mapper.SysDictDataMapper;
import com.xhcai.modules.system.service.ISysDictDataService;
import com.xhcai.modules.system.vo.SysDictDataVO;

/**
 * 字典数据服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysDictDataServiceImpl extends ServiceImpl<SysDictDataMapper, SysDictData> implements ISysDictDataService {

    private static final Logger log = LoggerFactory.getLogger(SysDictDataServiceImpl.class);

    @Autowired
    private SysDictDataMapper dictDataMapper;

    /**
     * 字典缓存
     */
    private final Map<String, String> dictCache = new ConcurrentHashMap<>();

    @Override
    public PageResult<SysDictDataVO> selectDictDataPage(SysDictDataQueryDTO queryDTO) {
        Page<SysDictData> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

        // 使用数据库分页查询
        Page<SysDictData> dictDataPage = dictDataMapper.selectDictDataPage(
                page,
                queryDTO.getDictType(),
                queryDTO.getDictLabel(),
                queryDTO.getStatus()
        );

        List<SysDictDataVO> dictDataVOs = dictDataPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.of(dictDataVOs, dictDataPage.getTotal(), dictDataPage.getCurrent(), dictDataPage.getSize());
    }

    @Override
    public List<SysDictDataVO> selectDictDataList(SysDictDataQueryDTO queryDTO) {
        List<SysDictData> dictDatas = dictDataMapper.selectDictDataList(
                queryDTO.getDictType(),
                queryDTO.getDictLabel(),
                queryDTO.getStatus()
        );

        return dictDatas.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public SysDictDataVO selectDictDataById(String dictDataId) {
        if (dictDataId == null) {
            return null;
        }

        SysDictData dictData = getById(dictDataId);
        return convertToVO(dictData);
    }

    @Override
    public List<SysDictDataVO> selectByDictType(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            return List.of();
        }

        List<SysDictData> dictDatas = dictDataMapper.selectByDictType(dictType);
        return dictDatas.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 创建字典数据（如果不存在）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDictDataIfNotExists(String dictType, String dictValue, String dictLabel, Integer dictSort, String remark, String cssClass, String listClass) {
        createDictDataIfNotExists(dictType, dictValue, dictLabel, dictSort, remark, cssClass, listClass, "N");
    }

    /**
     * 创建字典数据（如果不存在）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDictDataIfNotExists(String dictType, String dictValue, String dictLabel, Integer dictSort, String remark, String cssClass, String listClass, String isSystemDict) {
        // 检查字典数据是否已存在
        if (!existsDictValue(dictType, dictValue, null)) {
            SysDictData dictData = new SysDictData();
            dictData.setDictType(dictType);
            dictData.setDictValue(dictValue);
            dictData.setDictLabel(dictLabel);
            dictData.setDictSort(dictSort);
            dictData.setStatus("0");
            dictData.setIsDefault("N");
            dictData.setRemark(remark);
            dictData.setCssClass(cssClass);
            dictData.setListClass(listClass);
            dictData.setIsSystemDict(isSystemDict);
            insertDictData(dictData);
            log.debug("创建字典数据: {} - {} - {} (系统字典: {})", dictType, dictValue, dictLabel, isSystemDict);
        } else {
            log.debug("字典数据已存在: {} - {} - {}", dictType, dictValue, dictLabel);
        }
    }

    @Override
    public SysDictData selectByDictTypeAndValue(String dictType, String dictValue) {
        if (!StringUtils.hasText(dictType) || !StringUtils.hasText(dictValue)) {
            return null;
        }
        return dictDataMapper.selectByDictTypeAndValue(dictType, dictValue);
    }

    @Override
    public String getDictLabel(String dictType, String dictValue) {
        if (!StringUtils.hasText(dictType) || !StringUtils.hasText(dictValue)) {
            return dictValue;
        }

        // 先从缓存获取
        String cacheKey = dictType + ":" + dictValue;
        String label = dictCache.get(cacheKey);
        if (label != null) {
            return label;
        }

        // 从数据库获取
        SysDictData dictData = selectByDictTypeAndValue(dictType, dictValue);
        if (dictData != null) {
            label = dictData.getDictLabel();
            // 放入缓存
            dictCache.put(cacheKey, label);
            return label;
        }

        return dictValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDictData(SysDictData dictData) {
        // 参数校验
        validateDictData(dictData, true);

        // 检查字典值是否已存在
        if (existsDictValue(dictData.getDictType(), dictData.getDictValue(), null)) {
            throw new BusinessException("字典值已存在");
        }

        // 设置字典ID生成规则，当用户指定了id，则不进行处理，生成规则： tenantId + "-" + dictType + "-" + dictLabel
        if (!StringUtils.hasText(dictData.getId())) {
            dictData.setId(XhcaiUtils.generateSimpleUuidFromString(SecurityUtils.getCurrentTenantId() + "-" + dictData.getDictType() + "-" + dictData.getDictLabel()));
        }

        // 设置排序号
        if (dictData.getDictSort() == null) {
            Integer maxSort = dictDataMapper.selectMaxSortByDictType(dictData.getDictType());
            dictData.setDictSort(maxSort + 1);
        }

        // 设置默认状态
        if (!StringUtils.hasText(dictData.getStatus())) {
            dictData.setStatus(CommonConstants.STATUS_NORMAL);
        }

        // 设置默认值
        if (!StringUtils.hasText(dictData.getIsDefault())) {
            dictData.setIsDefault("N");
        }

        boolean result = saveOrUpdate(dictData);
        if (result) {
            log.info("创建字典数据成功: {}:{}", dictData.getDictType(), dictData.getDictValue());
            // 刷新缓存
            refreshCache();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDictData(SysDictData dictData) {
        // 参数校验
        validateDictData(dictData, false);

        // 检查字典值是否已存在
        if (StringUtils.hasText(dictData.getDictValue())
                && existsDictValue(dictData.getDictType(), dictData.getDictValue(), dictData.getId())) {
            throw new BusinessException("字典值已存在");
        }

        boolean result = updateById(dictData);
        if (result) {
            log.info("更新字典数据成功: {}:{}", dictData.getDictType(), dictData.getDictValue());
            // 刷新缓存
            refreshCache();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDictDatas(List<String> dictDataIds) {
        if (CollectionUtils.isEmpty(dictDataIds)) {
            throw new BusinessException("删除的字典数据ID不能为空");
        }

        boolean result = removeByIds(dictDataIds);
        if (result) {
            log.info("删除字典数据成功，数量: {}", dictDataIds.size());
            // 刷新缓存
            refreshCache();
        }
        return result;
    }

    @Override
    public boolean existsDictValue(String dictType, String dictValue, String excludeId) {
        if (!StringUtils.hasText(dictType) || !StringUtils.hasText(dictValue)) {
            return false;
        }
        return dictDataMapper.checkDictValueExists(dictType, dictValue, excludeId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> dictDataIds, String status) {
        if (CollectionUtils.isEmpty(dictDataIds) || !StringUtils.hasText(status)) {
            throw new BusinessException("参数不能为空");
        }

        for (String dictDataId : dictDataIds) {
            SysDictData dictData = new SysDictData();
            dictData.setId(dictDataId);
            dictData.setStatus(status);
            updateById(dictData);
        }

        // 刷新缓存
        refreshCache();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByDictType(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            return false;
        }

        Integer result = dictDataMapper.deleteByDictType(dictType);
        int count = result != null ? result : 0;
        if (count > 0) {
            log.info("根据字典类型删除字典数据成功: {}, 数量: {}", dictType, count);
            // 刷新缓存
            refreshCache();
        }
        return count > 0;
    }

    @Override
    public void refreshCache() {
        dictCache.clear();
        log.info("字典数据缓存刷新完成");
    }

    @Override
    public List<SysDictDataVO> exportDictDatas(SysDictDataQueryDTO queryDTO) {
        return selectDictDataList(queryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importDictDatas(List<SysDictData> dictDataList) {
        if (CollectionUtils.isEmpty(dictDataList)) {
            throw new BusinessException("导入数据不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder failureMsg = new StringBuilder();

        for (SysDictData dictData : dictDataList) {
            try {
                if (!existsDictValue(dictData.getDictType(), dictData.getDictValue(), null)) {
                    insertDictData(dictData);
                    successCount++;
                } else {
                    failCount++;
                    failureMsg.append("字典值 ").append(dictData.getDictType()).append(":").append(dictData.getDictValue()).append(" 已存在; ");
                }
            } catch (Exception e) {
                failCount++;
                failureMsg.append("字典值 ").append(dictData.getDictType()).append(":").append(dictData.getDictValue()).append(" 导入失败: ").append(e.getMessage()).append("; ");
            }
        }

        return String.format("导入完成，成功 %d 条，失败 %d 条。%s", successCount, failCount, failureMsg.toString());
    }

    /**
     * 参数校验
     */
    private void validateDictData(SysDictData dictData, boolean isInsert) {
        if (dictData == null) {
            throw new BusinessException("字典数据信息不能为空");
        }

        if (!StringUtils.hasText(dictData.getDictLabel())) {
            throw new BusinessException("字典标签不能为空");
        }

        if (!StringUtils.hasText(dictData.getDictValue())) {
            throw new BusinessException("字典值不能为空");
        }

        if (!StringUtils.hasText(dictData.getDictType())) {
            throw new BusinessException("字典类型不能为空");
        }

        if (StringUtils.hasText(dictData.getStatus()) && !CommonConstants.STATUS_NORMAL.equals(dictData.getStatus())
                && !CommonConstants.STATUS_DISABLE.equals(dictData.getStatus())) {
            throw new BusinessException("状态值必须为0或1");
        }

        if (StringUtils.hasText(dictData.getIsDefault()) && !"Y".equals(dictData.getIsDefault())
                && !"N".equals(dictData.getIsDefault())) {
            throw new BusinessException("是否默认值必须为Y或N");
        }
    }

    /**
     * 转换为VO对象
     */
    private SysDictDataVO convertToVO(SysDictData dictData) {
        if (dictData == null) {
            return null;
        }

        SysDictDataVO dictDataVO = new SysDictDataVO();
        BeanUtils.copyProperties(dictData, dictDataVO);
        return dictDataVO;
    }
}
