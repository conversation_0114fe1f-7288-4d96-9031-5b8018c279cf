<template>
  <div class="node-style-editor">
    <div class="editor-header">
      <h3>节点样式配置</h3>
      <button class="btn btn-sm btn-secondary" @click="resetToDefault">
        <i class="fas fa-undo"></i>
        重置
      </button>
    </div>

    <div class="editor-content">
      <!-- 节点样式配置 -->
      <div v-if="node" class="config-section">
        <h4>节点样式</h4>
        <div class="form-row">
          <div class="form-group">
            <label>宽度</label>
            <input
              v-model.number="localStyle.width"
              type="number"
              class="form-input"
              @input="updateStyle"
            />
          </div>
          <div class="form-group">
            <label>高度</label>
            <input
              v-model.number="localStyle.height"
              type="number"
              class="form-input"
              @input="updateStyle"
            />
          </div>
        </div>
      </div>

      <div v-if="node && currentConfig" class="config-sections">
        <!-- 图标和颜色配置 -->
        <div class="config-section">
          <h4>外观配置</h4>

          <!-- 图标配置 -->
          <div class="form-group">
            <label>图标</label>
            <div class="icon-selector-wrapper">
              <div class="current-icon-display" @click="showIconSelector = true">
                <div class="icon-preview">
                  <i :class="currentConfig.icon || 'fa-solid fa-cube'"></i>
                </div>
                <span>点击选择图标</span>
              </div>
            </div>
          </div>

          <!-- 颜色配置行 -->
          <div class="form-row">
            <div class="form-group">
              <label>图标颜色</label>
              <input v-model="currentConfig.iconColor" type="color" class="form-color" @input="updateConfig" />
            </div>
            <div class="form-group">
              <label>主色调</label>
              <input v-model="currentConfig.color" type="color" class="form-color" @input="updateConfig" />
            </div>
            <div class="form-group">
              <label>标签颜色</label>
              <input v-model="currentConfig.labelColor" type="color" class="form-color" @input="updateConfig" />
            </div>
          </div>

          <!-- 渐变背景选择 -->
          <div class="form-group">
            <label>渐变背景</label>
            <div class="gradient-selector">
              <div class="gradient-options">
                <div
                  v-for="gradient in gradientOptions"
                  :key="gradient.name"
                  :class="['gradient-option', { selected: currentConfig.gradient === gradient.value }]"
                  :style="{ background: gradient.value }"
                  @click="selectGradient(gradient.value)"
                  :title="gradient.name"
                >
                  <i class="fas fa-check" v-if="currentConfig.gradient === gradient.value"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- 节点样式配置行 -->
          <div class="form-row">
            <div class="form-group">
              <label>节点背景色</label>
              <input
                v-model="localStyle.backgroundColor"
                type="color"
                class="form-color"
                @input="updateStyle"
              />
            </div>
            <div class="form-group">
              <label>节点边框色</label>
              <input
                v-model="localStyle.borderColor"
                type="color"
                class="form-color"
                @input="updateStyle"
              />
            </div>
          </div>

          <!-- 预览效果 -->
          <div class="form-group">
            <label>预览效果</label>
            <div class="preview-container">
              <div class="preview-node" :style="previewStyle">
                <div class="preview-header" :style="previewHeaderStyle">
                  <div class="preview-icon" :style="previewIconStyle">
                    <i :class="currentConfig.icon || 'fa-solid fa-cube'"></i>
                  </div>
                  <div class="preview-title" :style="previewTitleStyle">
                    {{ currentConfig.label }}
                  </div>
                </div>
                <div class="preview-content" :style="previewContentStyle">
                  <div class="preview-description">{{ currentConfig.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图标选择器 -->
    <IconSelector
      v-model:visible="showIconSelector"
      :selected-icon="currentConfig?.icon || ''"
      :selected-background="currentConfig?.gradient || ''"
      @select="onIconSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { updateNodeConfig, resetNodeConfig, useNodeConfig } from '../composables/useNodeConfig'
import type { NodeLibraryItem } from '../config/nodeLibrary'
import type { Node } from '@vue-flow/core'
import IconSelector from '@/components/common/IconSelector.vue'

// Props
interface Props {
  selectedNodeType?: string
  node?: Node
}

const props = withDefaults(defineProps<Props>(), {
  selectedNodeType: '',
  node: undefined
})

// Emits
const emit = defineEmits<{
  'position-update': [position: { x: number, y: number }]
  'style-update': [style: any]
}>()

// 响应式数据
const selectedNodeType = ref(props.selectedNodeType || '')
const currentConfig = ref<NodeLibraryItem | null>(null)
const showIconSelector = ref(false)

// 节点位置和样式
const localPosition = ref({
  x: props.node?.position.x || 0,
  y: props.node?.position.y || 0
})

const localStyle = ref({
  width: 200,
  height: 100,
  backgroundColor: '#ffffff',
  borderColor: '#d1d5db'
})

// 渐变背景选项
const gradientOptions = ref([
  { name: '蓝色渐变', value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
  { name: '绿色渐变', value: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' },
  { name: '紫色渐变', value: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
  { name: '橙色渐变', value: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' },
  { name: '红色渐变', value: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' },
  { name: '青色渐变', value: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
  { name: '黄色渐变', value: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' },
  { name: '灰色渐变', value: 'linear-gradient(135deg, #e3ffe7 0%, #d9e7ff 100%)' }
])

// 计算属性

// 预览样式
const previewStyle = computed(() => ({
  background: localStyle.value.backgroundColor || '#ffffff',
  border: `1px solid ${localStyle.value.borderColor || '#e5e7eb'}`,
  borderRadius: '12px',
  overflow: 'hidden',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
}))

const previewHeaderStyle = computed(() => ({
  background: currentConfig.value?.gradient || currentConfig.value?.color || '#f3f4f6',
  padding: '12px 16px',
  display: 'flex',
  alignItems: 'center',
  gap: '10px'
}))

const previewIconStyle = computed(() => ({
  width: '28px',
  height: '28px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '6px',
  background: 'rgba(255, 255, 255, 0.2)',
  color: currentConfig.value?.iconColor || '#6b7280',
  fontSize: '16px'
}))

const previewTitleStyle = computed(() => ({
  color: currentConfig.value?.labelColor || '#1f2937',
  fontWeight: '600',
  fontSize: '15px'
}))

const previewContentStyle = computed(() => ({
  background: localStyle.value.backgroundColor || '#ffffff'
}))

// 方法
const loadNodeConfig = () => {
  const nodeType = props.node?.data?.nodeType || props.node?.type || props.selectedNodeType
  if (nodeType) {
    const reactiveConfig = useNodeConfig(nodeType)
    currentConfig.value = reactiveConfig.value ? { ...reactiveConfig.value } : null
    selectedNodeType.value = nodeType
  } else {
    currentConfig.value = null
  }
}

const updateConfig = () => {
  if (selectedNodeType.value && currentConfig.value) {
    updateNodeConfig(selectedNodeType.value, currentConfig.value)
  }
}

const resetToDefault = () => {
  if (selectedNodeType.value && confirm('确定要重置为默认配置吗？')) {
    resetNodeConfig(selectedNodeType.value)
    loadNodeConfig()
  }
}

const selectGradient = (gradient: string) => {
  if (currentConfig.value) {
    currentConfig.value.gradient = gradient
    updateConfig()
  }
}

const onIconSelect = (data: { icon: string; background: string }) => {
  if (currentConfig.value) {
    currentConfig.value.icon = data.icon
    currentConfig.value.gradient = data.background
    updateConfig()
  }
  showIconSelector.value = false
}

const updatePosition = () => {
  emit('position-update', localPosition.value)
}

const updateStyle = () => {
  emit('style-update', localStyle.value)
}

// 监听节点变化
watch(() => props.node?.data?.nodeType || props.node?.type, loadNodeConfig, { immediate: true })

// 监听props变化
watch(() => props.node, (newNode) => {
  if (newNode) {
    localPosition.value = {
      x: newNode.position.x,
      y: newNode.position.y
    }

    // 处理样式，考虑到style可能是函数或对象
    const nodeStyle = newNode.style
    if (nodeStyle && typeof nodeStyle === 'object' && !('call' in nodeStyle)) {
      localStyle.value = {
        width: (nodeStyle as any).width || 200,
        height: (nodeStyle as any).height || 100,
        backgroundColor: (nodeStyle as any).backgroundColor || '#ffffff',
        borderColor: (nodeStyle as any).borderColor || '#d1d5db'
      }
    }
  }
}, { immediate: true })

watch(() => props.selectedNodeType, (newType) => {
  selectedNodeType.value = newType || ''
}, { immediate: true })
</script>

<style scoped>
.node-style-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e5e7eb;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.editor-content {
  flex: 1;
  margin-top: 20px;
  overflow-y: auto;
}

.config-sections {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.config-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px;
}

.config-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.form-group {
  margin-bottom: 12px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-row {
  display: flex;
  gap: 10px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  transition: border-color 0.2s;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-color {
  width: 60px;
  height: 36px;
  padding: 2px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
}

.tags-editor {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  font-size: 12px;
  color: #6b7280;
}

.tag-remove {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-remove:hover {
  color: #ef4444;
}

.tag-input {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag-input .form-input {
  flex: 1;
}

.preview-container {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.preview-node {
  width: 180px;
  margin: 0 auto;
}

.preview-header {
  border-radius: 12px 12px 0 0;
}

.preview-content {
  padding: 12px 16px;
  border-radius: 0 0 12px 12px;
}

.preview-description {
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

/* 图标选择器样式 */
.icon-selector-wrapper {
  margin-bottom: 12px;
}

.current-icon-display {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: #f9fafb;
}

.current-icon-display:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.icon-preview {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 18px;
}

.current-icon-display span {
  color: #6b7280;
  font-size: 14px;
}

/* 渐变选择器样式 */
.gradient-selector {
  margin-bottom: 12px;
}

.gradient-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.gradient-option {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  position: relative;
}

.gradient-option:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gradient-option.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.gradient-option i {
  color: white;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
</style>
