package com.xhcai.common.security.service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.xhcai.common.core.utils.JsonUtils;

/**
 * 用户缓存服务
 * 
 * 管理用户登录信息的Redis缓存，避免每次请求都查询数据库
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class UserCacheService {

    private static final Logger log = LoggerFactory.getLogger(UserCacheService.class);

    private static final String USER_CACHE_PREFIX = "user:cache:";
    private static final String USER_PERMISSIONS_PREFIX = "user:permissions:";
    private static final String USER_ROLES_PREFIX = "user:roles:";
    
    // 缓存过期时间（秒）
    private static final long CACHE_EXPIRE_TIME = 3600; // 1小时

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存用户信息
     * 
     * @param loginUser 登录用户信息
     */
    public void cacheUser(LoginUser loginUser) {
        if (loginUser == null || !StringUtils.hasText(loginUser.getUserId())) {
            return;
        }
        
        try {
            String userKey = USER_CACHE_PREFIX + loginUser.getUserId();
            String permissionsKey = USER_PERMISSIONS_PREFIX + loginUser.getUserId();
            String rolesKey = USER_ROLES_PREFIX + loginUser.getUserId();
            
            // 缓存用户基本信息
            redisTemplate.opsForValue().set(userKey, loginUser, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            
            // 缓存用户权限
            if (loginUser.getPermissions() != null) {
                redisTemplate.opsForValue().set(permissionsKey, loginUser.getPermissions(), CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            }
            
            // 缓存用户角色
            if (loginUser.getRoles() != null) {
                redisTemplate.opsForValue().set(rolesKey, loginUser.getRoles(), CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            }
            
            log.debug("用户信息已缓存: userId={}, username={}", loginUser.getUserId(), loginUser.getUsername());
            
        } catch (Exception e) {
            log.error("缓存用户信息失败: userId={}, error={}", loginUser.getUserId(), e.getMessage(), e);
        }
    }

    /**
     * 从缓存获取用户信息
     * 
     * @param userId 用户ID
     * @return 登录用户信息，如果缓存中不存在返回null
     */
    public LoginUser getCachedUser(String userId) {
        if (!StringUtils.hasText(userId)) {
            return null;
        }
        
        try {
            String userKey = USER_CACHE_PREFIX + userId;
            Object cachedUser = redisTemplate.opsForValue().get(userKey);
            
            if (cachedUser instanceof LoginUser) {
                LoginUser loginUser = (LoginUser) cachedUser;
                log.debug("从缓存获取用户信息: userId={}, username={}", userId, loginUser.getUsername());
                return loginUser;
            }
            
        } catch (Exception e) {
            log.error("从缓存获取用户信息失败: userId={}, error={}", userId, e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 从缓存获取用户权限
     * 
     * @param userId 用户ID
     * @return 用户权限集合
     */
    @SuppressWarnings("unchecked")
    public Set<String> getCachedUserPermissions(String userId) {
        if (!StringUtils.hasText(userId)) {
            return Set.of();
        }
        
        try {
            String permissionsKey = USER_PERMISSIONS_PREFIX + userId;
            Object cachedPermissions = redisTemplate.opsForValue().get(permissionsKey);
            
            if (cachedPermissions instanceof Set) {
                return (Set<String>) cachedPermissions;
            }
            
        } catch (Exception e) {
            log.error("从缓存获取用户权限失败: userId={}, error={}", userId, e.getMessage(), e);
        }
        
        return Set.of();
    }

    /**
     * 从缓存获取用户角色
     * 
     * @param userId 用户ID
     * @return 用户角色集合
     */
    @SuppressWarnings("unchecked")
    public Set<String> getCachedUserRoles(String userId) {
        if (!StringUtils.hasText(userId)) {
            return Set.of();
        }
        
        try {
            String rolesKey = USER_ROLES_PREFIX + userId;
            Object cachedRoles = redisTemplate.opsForValue().get(rolesKey);
            
            if (cachedRoles instanceof Set) {
                return (Set<String>) cachedRoles;
            }
            
        } catch (Exception e) {
            log.error("从缓存获取用户角色失败: userId={}, error={}", userId, e.getMessage(), e);
        }
        
        return Set.of();
    }

    /**
     * 清除用户缓存
     * 
     * @param userId 用户ID
     */
    public void clearUserCache(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }
        
        try {
            String userKey = USER_CACHE_PREFIX + userId;
            String permissionsKey = USER_PERMISSIONS_PREFIX + userId;
            String rolesKey = USER_ROLES_PREFIX + userId;
            
            redisTemplate.delete(userKey);
            redisTemplate.delete(permissionsKey);
            redisTemplate.delete(rolesKey);
            
            log.debug("用户缓存已清除: userId={}", userId);
            
        } catch (Exception e) {
            log.error("清除用户缓存失败: userId={}, error={}", userId, e.getMessage(), e);
        }
    }

    /**
     * 刷新用户缓存过期时间
     * 
     * @param userId 用户ID
     */
    public void refreshUserCache(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }
        
        try {
            String userKey = USER_CACHE_PREFIX + userId;
            String permissionsKey = USER_PERMISSIONS_PREFIX + userId;
            String rolesKey = USER_ROLES_PREFIX + userId;
            
            redisTemplate.expire(userKey, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            redisTemplate.expire(permissionsKey, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            redisTemplate.expire(rolesKey, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            
            log.debug("用户缓存过期时间已刷新: userId={}", userId);
            
        } catch (Exception e) {
            log.error("刷新用户缓存过期时间失败: userId={}, error={}", userId, e.getMessage(), e);
        }
    }

    /**
     * 检查用户缓存是否存在
     * 
     * @param userId 用户ID
     * @return 是否存在缓存
     */
    public boolean hasUserCache(String userId) {
        if (!StringUtils.hasText(userId)) {
            return false;
        }
        
        try {
            String userKey = USER_CACHE_PREFIX + userId;
            return Boolean.TRUE.equals(redisTemplate.hasKey(userKey));
        } catch (Exception e) {
            log.error("检查用户缓存失败: userId={}, error={}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量清除用户缓存
     * 
     * @param userIds 用户ID集合
     */
    public void clearUserCaches(Set<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        
        for (String userId : userIds) {
            clearUserCache(userId);
        }
    }
}
