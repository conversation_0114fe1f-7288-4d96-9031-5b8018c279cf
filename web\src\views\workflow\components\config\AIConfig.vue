<template>
  <div class="ai-config">
    <div class="config-section">
      <h5 class="section-title">AI工具类型</h5>
      
      <div class="form-group">
        <label>工具类型</label>
        <select v-model="localConfig.aiType" class="form-select" @change="onTypeChange">
          <option value="llm-chat">LLM对话</option>
          <option value="text-embedding">文本向量化</option>
          <option value="speech-to-text">语音识别</option>
          <option value="text-to-speech">语音合成</option>
          <option value="image-generation">图像生成</option>
          <option value="image-analysis">图像分析</option>
          <option value="knowledge-base">知识库查询</option>
        </select>
      </div>
    </div>

    <!-- LLM对话配置 -->
    <div class="config-section" v-if="localConfig.aiType === 'llm-chat'">
      <h5 class="section-title">LLM配置</h5>
      
      <div class="form-group">
        <label>模型</label>
        <select v-model="localConfig.model" class="form-select" @change="onUpdate">
          <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          <option value="gpt-4">GPT-4</option>
          <option value="gpt-4-turbo">GPT-4 Turbo</option>
          <option value="claude-3-sonnet">Claude 3 Sonnet</option>
          <option value="claude-3-opus">Claude 3 Opus</option>
        </select>
      </div>

      <div class="form-group">
        <label>系统消息</label>
        <textarea 
          v-model="localConfig.systemMessage" 
          class="form-textarea"
          rows="3"
          placeholder="你是一个有用的AI助手..."
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group">
        <label>用户提示</label>
        <textarea 
          v-model="localConfig.prompt" 
          class="form-textarea"
          rows="4"
          placeholder="输入用户提示内容，可以使用变量 {{variable_name}}"
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group">
        <label>温度 ({{ localConfig.temperature }})</label>
        <input 
          v-model.number="localConfig.temperature" 
          type="range" 
          min="0" 
          max="2" 
          step="0.1"
          class="form-range"
          @input="onUpdate"
        />
        <div class="range-labels">
          <span>保守</span>
          <span>创造性</span>
        </div>
      </div>

      <div class="form-group">
        <label>最大令牌数</label>
        <input 
          v-model.number="localConfig.maxTokens" 
          type="number" 
          class="form-input"
          min="1"
          max="4096"
          placeholder="1000"
          @input="onUpdate"
        />
      </div>
    </div>

    <!-- 文本向量化配置 -->
    <div class="config-section" v-if="localConfig.aiType === 'text-embedding'">
      <h5 class="section-title">向量化配置</h5>
      
      <div class="form-group">
        <label>模型</label>
        <select v-model="localConfig.model" class="form-select" @change="onUpdate">
          <option value="text-embedding-ada-002">text-embedding-ada-002</option>
          <option value="text-embedding-3-small">text-embedding-3-small</option>
          <option value="text-embedding-3-large">text-embedding-3-large</option>
        </select>
      </div>

      <div class="form-group">
        <label>文本内容</label>
        <textarea 
          v-model="localConfig.text" 
          class="form-textarea"
          rows="4"
          placeholder="输入要向量化的文本内容"
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group">
        <label>向量维度</label>
        <input 
          v-model.number="localConfig.dimensions" 
          type="number" 
          class="form-input"
          min="1"
          max="3072"
          placeholder="1536"
          @input="onUpdate"
        />
      </div>
    </div>

    <!-- 语音识别配置 -->
    <div class="config-section" v-if="localConfig.aiType === 'speech-to-text'">
      <h5 class="section-title">语音识别配置</h5>
      
      <div class="form-group">
        <label>模型</label>
        <select v-model="localConfig.model" class="form-select" @change="onUpdate">
          <option value="whisper-1">Whisper-1</option>
        </select>
      </div>

      <div class="form-group">
        <label>音频文件路径</label>
        <input 
          v-model="localConfig.audioFile" 
          type="text" 
          class="form-input"
          placeholder="/path/to/audio.mp3"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <label>语言</label>
        <select v-model="localConfig.language" class="form-select" @change="onUpdate">
          <option value="zh-CN">中文</option>
          <option value="en-US">英语</option>
          <option value="ja-JP">日语</option>
          <option value="ko-KR">韩语</option>
          <option value="auto">自动检测</option>
        </select>
      </div>
    </div>

    <!-- 语音合成配置 -->
    <div class="config-section" v-if="localConfig.aiType === 'text-to-speech'">
      <h5 class="section-title">语音合成配置</h5>
      
      <div class="form-group">
        <label>模型</label>
        <select v-model="localConfig.model" class="form-select" @change="onUpdate">
          <option value="tts-1">TTS-1</option>
          <option value="tts-1-hd">TTS-1 HD</option>
        </select>
      </div>

      <div class="form-group">
        <label>文本内容</label>
        <textarea 
          v-model="localConfig.text" 
          class="form-textarea"
          rows="4"
          placeholder="输入要合成语音的文本内容"
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group">
        <label>语音</label>
        <select v-model="localConfig.voice" class="form-select" @change="onUpdate">
          <option value="alloy">Alloy</option>
          <option value="echo">Echo</option>
          <option value="fable">Fable</option>
          <option value="onyx">Onyx</option>
          <option value="nova">Nova</option>
          <option value="shimmer">Shimmer</option>
        </select>
      </div>

      <div class="form-group">
        <label>输出文件路径</label>
        <input 
          v-model="localConfig.outputFile" 
          type="text" 
          class="form-input"
          placeholder="/path/to/output.mp3"
          @input="onUpdate"
        />
      </div>
    </div>

    <!-- 图像生成配置 -->
    <div class="config-section" v-if="localConfig.aiType === 'image-generation'">
      <h5 class="section-title">图像生成配置</h5>
      
      <div class="form-group">
        <label>模型</label>
        <select v-model="localConfig.model" class="form-select" @change="onUpdate">
          <option value="dall-e-3">DALL-E 3</option>
          <option value="dall-e-2">DALL-E 2</option>
        </select>
      </div>

      <div class="form-group">
        <label>提示词</label>
        <textarea 
          v-model="localConfig.prompt" 
          class="form-textarea"
          rows="4"
          placeholder="描述你想要生成的图像..."
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group">
        <label>图像尺寸</label>
        <select v-model="localConfig.size" class="form-select" @change="onUpdate">
          <option value="1024x1024">1024x1024</option>
          <option value="1792x1024">1792x1024</option>
          <option value="1024x1792">1024x1792</option>
        </select>
      </div>

      <div class="form-group">
        <label>质量</label>
        <select v-model="localConfig.quality" class="form-select" @change="onUpdate">
          <option value="standard">标准</option>
          <option value="hd">高清</option>
        </select>
      </div>

      <div class="form-group">
        <label>输出文件路径</label>
        <input 
          v-model="localConfig.outputFile" 
          type="text" 
          class="form-input"
          placeholder="/path/to/output.png"
          @input="onUpdate"
        />
      </div>
    </div>

    <!-- 知识库查询配置 -->
    <div class="config-section" v-if="localConfig.aiType === 'knowledge-base'">
      <h5 class="section-title">知识库查询配置</h5>
      
      <div class="form-group">
        <label>知识库ID</label>
        <input 
          v-model="localConfig.datasetId"
          type="text" 
          class="form-input"
          placeholder="knowledge_base_id"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <label>查询内容</label>
        <textarea 
          v-model="localConfig.query" 
          class="form-textarea"
          rows="3"
          placeholder="输入查询问题"
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group">
        <label>返回结果数量</label>
        <input 
          v-model.number="localConfig.topK" 
          type="number" 
          class="form-input"
          min="1"
          max="20"
          placeholder="5"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <label>相似度阈值</label>
        <input 
          v-model.number="localConfig.threshold" 
          type="number" 
          class="form-input"
          min="0"
          max="1"
          step="0.1"
          placeholder="0.7"
          @input="onUpdate"
        />
      </div>
    </div>

    <div class="config-actions">
      <button class="btn btn-secondary btn-sm" @click="testAI">
        <i class="fas fa-play"></i>
        测试运行
      </button>
      
      <button class="btn btn-primary btn-sm" @click="saveTemplate">
        <i class="fas fa-save"></i>
        保存模板
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'

// Props
interface Props {
  config: Record<string, any>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [config: Record<string, any>]
}>()

// 响应式数据
const localConfig = reactive({
  aiType: 'llm-chat',
  model: 'gpt-3.5-turbo',
  systemMessage: '',
  prompt: '',
  temperature: 0.7,
  maxTokens: 1000,
  text: '',
  dimensions: 1536,
  audioFile: '',
  language: 'zh-CN',
  voice: 'alloy',
  outputFile: '',
  size: '1024x1024',
  quality: 'standard',
  datasetId: '',
  query: '',
  topK: 5,
  threshold: 0.7
})

// 方法
const onUpdate = () => {
  emit('update', { ...localConfig })
}

const onTypeChange = () => {
  // 根据AI类型设置默认配置
  switch (localConfig.aiType) {
    case 'llm-chat':
      localConfig.model = 'gpt-3.5-turbo'
      break
    case 'text-embedding':
      localConfig.model = 'text-embedding-ada-002'
      break
    case 'speech-to-text':
      localConfig.model = 'whisper-1'
      break
    case 'text-to-speech':
      localConfig.model = 'tts-1'
      break
    case 'image-generation':
      localConfig.model = 'dall-e-3'
      break
  }
  onUpdate()
}

const testAI = async () => {
  try {
    // TODO: 实现AI测试逻辑
    console.log('测试AI工具:', localConfig)
    alert('AI测试功能待实现')
  } catch (error) {
    console.error('AI测试失败:', error)
    alert('AI测试失败')
  }
}

const saveTemplate = () => {
  // TODO: 实现保存模板逻辑
  console.log('保存AI配置模板:', localConfig)
  alert('保存模板功能待实现')
}

// 初始化配置
const initializeConfig = () => {
  Object.assign(localConfig, props.config)
}

// 监听配置变化
watch(() => props.config, () => {
  initializeConfig()
}, { immediate: true })

// 生命周期
onMounted(() => {
  initializeConfig()
})
</script>

<style scoped>
.ai-config {
  padding: 16px;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
}

.form-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.config-actions {
  display: flex;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}
</style>
