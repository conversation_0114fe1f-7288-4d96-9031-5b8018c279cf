<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEdit ? '编辑字典类型' : '新增字典类型' }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          ✕
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            字典名称 <span class="text-red-500">*</span>
          </label>
          <input
            v-model="form.dictName"
            type="text"
            required
            placeholder="请输入字典名称"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            字典类型 <span class="text-red-500">*</span>
          </label>
          <input
            v-model="form.dictType"
            type="text"
            required
            :disabled="isEdit"
            placeholder="请输入字典类型（如：sys_user_status）"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
          />
          <p class="text-xs text-gray-500 mt-1">字典类型创建后不可修改</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            状态 <span class="text-red-500">*</span>
          </label>
          <select
            v-model="form.status"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="0">正常</option>
            <option value="1">停用</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            备注
          </label>
          <textarea
            v-model="form.remark"
            rows="3"
            placeholder="请输入备注信息"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
          ></textarea>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50"
          >
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DictAPI } from '@/api/dict'
import type { SysDictTypeVO, DictTypeDTO } from '@/types/system'

// 为了与后端保持一致，创建类型别名
type SysDictVO = SysDictTypeVO

// Props
interface Props {
  dictType?: SysDictVO | null
}

const props = withDefaults(defineProps<Props>(), {
  dictType: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// 响应式数据
const loading = ref(false)
const form = reactive<DictTypeDTO>({
  dictName: '',
  dictType: '',
  status: '0',
  remark: ''
})

// 计算属性
const isEdit = computed(() => !!props.dictType)

// 初始化表单
const initForm = () => {
  if (props.dictType) {
    form.id = props.dictType.id
    form.dictName = props.dictType.dictName
    form.dictType = props.dictType.dictType
    form.status = props.dictType.status
    form.remark = props.dictType.remark || ''
  } else {
    form.id = undefined
    form.dictName = ''
    form.dictType = ''
    form.status = '0'
    form.remark = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true

    if (isEdit.value) {
      await DictAPI.updateDictType(form.id!, form)
      ElMessage.success('更新成功')
    } else {
      await DictAPI.createDictType(form)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('保存字典类型失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时初始化表单
onMounted(() => {
  initForm()
})
</script>

<style scoped>
/* 动画效果 */
.fixed {
  animation: fadeIn 0.3s ease-out;
}

.bg-white {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
