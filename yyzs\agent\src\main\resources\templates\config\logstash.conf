# Logstash Configuration Template
# This is a basic Logstash configuration template
# Customize the input, filter, and output sections based on your needs

# ================================== Input ====================================
input {
  # Beats input - receives data from Filebeat, Metricbeat, etc.
  beats {
    port => ${BEATS_PORT:5044}
    host => "${BEATS_HOST:0.0.0.0}"
  }
  
  # HTTP input - for receiving data via HTTP POST
  # http {
  #   port => ${HTTP_PORT:8080}
  #   host => "${HTTP_HOST:0.0.0.0}"
  # }
  
  # TCP input - for receiving raw TCP data
  # tcp {
  #   port => ${TCP_PORT:5000}
  #   host => "${TCP_HOST:0.0.0.0}"
  # }
  
  # File input - for reading from log files directly
  # file {
  #   path => "${LOG_PATH:/var/log/*.log}"
  #   start_position => "beginning"
  # }
  
  # Custom input configuration
  ${CUSTOM_INPUT:}
}

# ================================== Filter ===================================
filter {
  # Parse JSON logs
  if [message] =~ /^\{.*\}$/ {
    json {
      source => "message"
    }
  }
  
  # Parse common log formats
  if [fields][log_type] == "apache" {
    grok {
      match => { "message" => "%{COMBINEDAPACHELOG}" }
    }
  }
  
  if [fields][log_type] == "nginx" {
    grok {
      match => { "message" => "%{NGINXACCESS}" }
    }
  }
  
  # Add timestamp
  if ![timestamp] {
    mutate {
      add_field => { "timestamp" => "%{@timestamp}" }
    }
  }
  
  # Remove unwanted fields
  mutate {
    remove_field => [ "host", "agent", "ecs", "log", "input" ]
  }
  
  # Custom filter configuration
  ${CUSTOM_FILTER:}
}

# ================================== Output ===================================
output {
  # Elasticsearch output - primary destination
  elasticsearch {
    hosts => [${ELASTICSEARCH_HOSTS:"localhost:9200"}]
    index => "${INDEX_PATTERN:logstash-%{+YYYY.MM.dd}}"
    
    # Authentication (if enabled)
    # user => "${ES_USER:elastic}"
    # password => "${ES_PASSWORD:changeme}"
    
    # SSL/TLS settings (if enabled)
    # ssl => ${ES_SSL:false}
    # ssl_certificate_verification => ${ES_SSL_VERIFY:true}
    # cacert => "${ES_CACERT:/path/to/ca.crt}"
  }
  
  # File output - for debugging or backup
  # file {
  #   path => "${OUTPUT_PATH:/var/log/logstash/output.log}"
  #   codec => line { format => "%{message}" }
  # }
  
  # Stdout output - for debugging
  # stdout {
  #   codec => rubydebug
  # }
  
  # Kafka output - for streaming to Kafka
  # kafka {
  #   bootstrap_servers => "${KAFKA_SERVERS:localhost:9092}"
  #   topic_id => "${KAFKA_TOPIC:logstash}"
  # }
  
  # Custom output configuration
  ${CUSTOM_OUTPUT:}
}

# ================================== Settings =================================
# Pipeline settings
pipeline.workers: ${PIPELINE_WORKERS:2}
pipeline.batch.size: ${PIPELINE_BATCH_SIZE:125}
pipeline.batch.delay: ${PIPELINE_BATCH_DELAY:50}

# Queue settings
queue.type: ${QUEUE_TYPE:memory}
# queue.max_bytes: ${QUEUE_MAX_BYTES:1gb}
# queue.checkpoint.writes: ${QUEUE_CHECKPOINT_WRITES:1024}

# Dead letter queue
dead_letter_queue.enable: ${DLQ_ENABLE:false}
# dead_letter_queue.max_bytes: ${DLQ_MAX_BYTES:1gb}

# HTTP API settings
http.host: "${HTTP_API_HOST:127.0.0.1}"
http.port: ${HTTP_API_PORT:9600}

# Log settings
log.level: ${LOG_LEVEL:info}
path.logs: ${LOG_PATH:/var/log/logstash}

# Custom settings
${CUSTOM_SETTINGS:}
