package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * 文档更新DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档更新DTO")
@Data
public class DocumentUpdateDTO {

    /**
     * 文档名称
     */
    @Schema(description = "文档名称", example = "技术文档.pdf")
    @Size(max = 255, message = "文档名称长度不能超过255个字符")
    private String name;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID", example = "category_123")
    private String categoryId;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 是否暂停
     */
    @Schema(description = "是否暂停", example = "false")
    private Boolean isPaused;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "文档备注信息")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
