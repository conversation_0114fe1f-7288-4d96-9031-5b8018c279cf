package com.xhcai.common.api.dto;

import com.xhcai.common.api.query.Keywordable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

import java.io.Serializable;

/**
 * 关键字搜索查询DTO基类
 * 提供关键字搜索的通用字段和方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "关键字搜索查询DTO基类")
public class KeywordQueryDTO implements Keywordable, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索关键字
     */
    @Schema(description = "搜索关键字", example = "用户")
    @Size(max = 100, message = "搜索关键字长度不能超过100个字符")
    private String keyword;

    @Override
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return "KeywordQueryDTO{" +
                "keyword='" + keyword + '\'' +
                '}';
    }
}
