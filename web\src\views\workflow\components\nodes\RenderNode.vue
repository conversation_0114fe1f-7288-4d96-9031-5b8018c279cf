<template>
  <div class="render-node" :class="{ selected: selected }">
    <div class="node-header">
      <div class="node-icon">
        <i class="fas" :class="getRenderIcon(data.config?.renderType || 'chart')"></i>
      </div>
      <div class="node-title">{{ data.label || getRenderName(data.config?.renderType || 'chart') }}</div>
    </div>
    
    <div class="node-content">
      <p class="node-description" v-if="data.description">{{ data.description }}</p>
    </div>

    <Handle type="target" :position="Position.Left" id="input" class="node-handle node-handle-input" />
    <Handle type="source" :position="Position.Right" id="output" class="node-handle node-handle-output" />
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'

interface RenderNodeData {
  label?: string
  description?: string
  config?: {
    renderType?: string
  }
}

interface Props extends NodeProps {
  data: RenderNodeData
}

defineProps<Props>()

const getRenderIcon = (renderType: string) => {
  const iconMap: Record<string, string> = {
    'pie-chart': 'fa-chart-pie',
    'line-chart': 'fa-chart-line',
    'bar-chart': 'fa-chart-bar',
    'audio-player': 'fa-play',
    'image-display': 'fa-image',
    'data-table': 'fa-table'
  }
  return iconMap[renderType] || 'fa-chart-bar'
}

const getRenderName = (renderType: string) => {
  const nameMap: Record<string, string> = {
    'pie-chart': '饼图',
    'line-chart': '折线图',
    'bar-chart': '柱状图',
    'audio-player': '音频播放',
    'image-display': '图片显示',
    'data-table': '数据表格'
  }
  return nameMap[renderType] || '渲染工具'
}
</script>

<style scoped>
.render-node {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
  border: 2px solid #ec4899;
  border-radius: 12px;
  min-width: 160px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  position: relative;
}

.render-node:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.render-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.node-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.node-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.node-title {
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex: 1;
}

.node-content {
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 0 0 12px 12px;
}

.node-description {
  color: #374151;
  font-size: 13px;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

.node-handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  background: #ec4899;
  border-radius: 50%;
}

.node-handle-input {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-handle-output {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-handle:hover {
  width: 16px;
  height: 16px;
  border-width: 3px;
}

.node-handle-input:hover {
  left: -8px;
}

.node-handle-output:hover {
  right: -8px;
}
</style>
