<template>
  <!-- 创建智能体模态框 -->
  <div class="create-agent-modal" v-show="visible" @click="closeModal">
    <div class="create-agent-modal-content" @click.stop>
      <!-- 模态框头部 -->
      <div class="create-agent-modal-header">
        <h2>创建智能体</h2>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 模态框主体 -->
      <div class="create-agent-modal-body">
        <!-- 左侧面板 -->
        <div class="create-left-panel">
          <!-- 智能体来源选择 -->
          <div class="form-section">
            <h3>选择智能体来源</h3>
            <div class="source-types-grid">
              <div
                :class="['source-type-card', { active: selectedSourceType === 'platform' }]"
                @click="selectSourceType('platform')"
              >
                <div class="type-icon" style="backgroundColor: #f0f7ff; color: #3b82f6;">
                  <i class="fas fa-home"></i>
                </div>
                <div class="type-info">
                  <h4>本平台智能体</h4>
                  <p>使用平台内置的智能体类型</p>
                </div>
                <div class="type-check">
                  <i class="fas fa-check"></i>
                </div>
              </div>
              <div
                :class="['source-type-card', { active: selectedSourceType === 'external' }]"
                @click="selectSourceType('external')"
                style="position: relative;"
              >
                <div class="type-icon" style="background-color: #ecfdf5; color: #10b981;">
                  <i class="fas fa-external-link-alt"></i>
                </div>
                <div class="type-info">
                  <h4>外部智能体</h4>
                  <p>{{ selectedExternalAgentName || '连接第三方智能体服务' }}</p>
                </div>
                <div class="type-check">
                  <i class="fas fa-check"></i>
                </div>

                <!-- 外部智能体选择下拉层 -->
                <div
                  v-if="showExternalAgentDropdown && selectedSourceType === 'external'"
                  class="external-agent-dropdown"
                  @click.stop
                >
                  <div class="dropdown-header">
                    <h4>选择外部智能体</h4>
                    <button @click="closeExternalAgentDropdown" class="close-btn">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                  <div class="dropdown-content">
                    <div v-if="loadingExternalAgents" class="loading-state">
                      <i class="fas fa-spinner fa-spin"></i>
                      <span>加载中...</span>
                    </div>
                    <div v-else-if="externalAgents.length === 0" class="empty-state">
                      <i class="fas fa-exclamation-circle"></i>
                      <span>暂无可用的外部智能体</span>
                    </div>
                    <div v-else class="agent-list">
                      <div
                        v-for="agent in externalAgents"
                        :key="agent.id"
                        :class="['agent-item', { selected: selectedExternalAgentId === agent.id }]"
                        @click="selectExternalAgent(agent)"
                      >
                        <div class="agent-info">
                          <h5>{{ agent.name }}</h5>
                          <p>{{ agent.description }}</p>
                        </div>
                        <div class="agent-check" v-if="selectedExternalAgentId === agent.id">
                          <i class="fas fa-check"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 智能体类型选择 -->
          <div class="form-section" v-if="selectedSourceType === 'platform' || (selectedSourceType === 'external' && selectedExternalAgentId)">
            <h3>选择智能体类型</h3>
            <div class="agent-types-grid">
              <div
                v-for="type in agentTypes"
                :key="type.value"
                :class="['agent-type-card', { active: selectedAgentType === type.value }]"
                @click="selectAgentType(type.value)"
              >
                <!-- 新手适用标记 -->
                <div v-if="type.isBeginnerFriendly" class="beginner-badge">
                  <i class="fas fa-star"></i>
                  <span>新手适用</span>
                </div>

                <div class="type-icon" :style="{ backgroundColor: type.iconBg, color: type.iconColor }">
                  <i :class="type.icon"></i>
                </div>
                <div class="type-info">
                  <h4>{{ type.label }}</h4>
                  <p>{{ type.description }}</p>
                </div>
                <div class="type-check">
                  <i class="fas fa-check"></i>
                </div>
              </div>
            </div>
          </div>



          <!-- 智能体名称 -->
          <div class="form-section">
            <h3>智能体名称</h3>
            <input
              type="text"
              v-model="newAgentName"
              placeholder="请输入智能体名称"
              class="form-input"
              maxlength="50"
            >
            <div class="input-hint">{{ newAgentName.length }}/50</div>
          </div>

          <!-- 智能体图标 -->
          <div class="form-section">
            <h3>智能体图标</h3>
            <div class="icon-selector">
              <div class="current-icon" @click="showIconSelector = true">
                <div class="icon-preview" :style="{ background: selectedIconBackground }">
                  <i :class="selectedIcon" v-if="selectedIcon"></i>
                  <i class="fas fa-image" v-else></i>
                </div>
                <span>点击选择图标</span>
              </div>
            </div>
          </div>

          <!-- 智能体描述 -->
          <div class="form-section">
            <h3>智能体描述</h3>
            <textarea
              v-model="newAgentDescription"
              placeholder="请输入智能体的详细描述信息"
              class="form-textarea"
              rows="4"
              maxlength="500"
            ></textarea>
            <div class="input-hint">{{ newAgentDescription.length }}/500</div>
          </div>
        </div>

        <!-- 右侧面板 -->
        <div class="create-right-panel">
          <!-- 本平台智能体预览 -->
          <div class="type-preview" v-if="selectedSourceType === 'platform' && selectedAgentType">
            <h3>{{ getSelectedTypeInfo().label }}</h3>
            <div class="type-description">
              <p>{{ getSelectedTypeInfo().fullDescription }}</p>
            </div>
            <div class="type-example" v-if="getSelectedTypeInfo().example">
              <h4>示例图</h4>
              <div class="example-image">
                <img :src="getSelectedTypeInfo().example" :alt="getSelectedTypeInfo().label + '示例'">
              </div>
            </div>
          </div>

          <!-- 外部智能体预览 -->
          <div class="type-preview" v-else-if="selectedSourceType === 'external' && selectedExternalAgentId">
            <h3>{{ getSelectedExternalAgentInfo().name }}</h3>
            <div class="type-description">
              <p>{{ getSelectedExternalAgentInfo().description }}</p>
            </div>
            <div class="external-agent-info">
              <h4>连接信息</h4>
              <div class="info-item">
                <span class="label">连接地址:</span>
                <span class="value">{{ getSelectedExternalAgentInfo().connectionUrl }}</span>
              </div>
              <div class="info-item">
                <span class="label">访问范围:</span>
                <span class="value">{{ getSelectedExternalAgentInfo().accessScopeText }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态:</span>
                <span class="value" :class="{ 'status-active': getSelectedExternalAgentInfo().status === 1 }">
                  {{ getSelectedExternalAgentInfo().statusText }}
                </span>
              </div>
            </div>
          </div>

          <!-- 占位符 -->
          <div class="type-preview-placeholder" v-else>
            <div class="placeholder-icon">
              <i class="fas fa-robot"></i>
            </div>
            <p v-if="selectedSourceType === 'platform'">请选择智能体类型查看详细信息</p>
            <p v-else-if="selectedSourceType === 'external'">请选择外部智能体查看详细信息</p>
            <p v-else>请选择智能体来源</p>
          </div>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="create-agent-modal-footer">
        <div class="footer-left">
          <span class="create-tip">创建后可以进一步配置智能体的详细功能</span>
        </div>
        <div class="footer-right">
          <button class="btn-modern btn-secondary" @click="closeModal">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button
            class="btn-modern btn-primary"
            @click="confirmCreateAgent"
            :disabled="!canCreateAgent || isCreating"
          >
            <i class="fas fa-spinner fa-spin" v-if="isCreating"></i>
            <i class="fas fa-plus" v-else></i>
            {{ isCreating ? '创建中...' : '创建智能体' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 图标选择器组件 -->
  <IconSelector
    v-model:visible="showIconSelector"
    :selected-icon="selectedIcon"
    :selected-background="selectedIconBackground"
    @select="handleIconSelect"
  />

  <!-- 遮罩层 -->
  <div class="overlay" v-show="visible || showIconSelector" @click="closeModal"></div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import IconSelector from '../../components/common/IconSelector.vue'
import { AgentsAPI, ThirdPlatformAPI, type CreateAgentRequest } from '@/api/agents'

// 定义 props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'create': [createRequest: CreateAgentRequest | null]
  'refresh': []
}>()

// 响应式数据
const selectedSourceType = ref('platform') // 智能体来源类型：platform-本平台，external-外部智能体
const selectedAgentType = ref('')
const selectedExternalAgentId = ref('')
const selectedPlatformId = ref('')  // 添加选中的平台ID
const newAgentName = ref('')
const newAgentDescription = ref('')
const selectedIcon = ref('fas fa-robot')
const selectedIconBackground = ref('linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)')
const showIconSelector = ref(false)
const isCreating = ref(false)
const loadingExternalAgents = ref(false)
const externalAgents = ref<any[]>([])
const showExternalAgentDropdown = ref(false)
const selectedExternalAgentName = ref('')

// 智能体类型配置 - 只显示后端支持的5种类型
const agentTypes = ref([
  {
    value: 'advanced-chat',
    label: '高级聊天',
    icon: 'fas fa-brain',
    iconColor: '#ff6b35', // 橙红色
    iconBg: '#fff5f3',
    description: '高级聊天功能，支持复杂对话场景',
    fullDescription: '高级聊天智能体具备更强的对话理解和生成能力，支持多轮对话、上下文记忆、情感识别等高级功能。适用于需要深度交互的客户服务、心理咨询、教育辅导等复杂对话场景。',
    example: '/images/advanced-chat-example.png'
  },
  {
    value: 'workflow',
    label: '工作流',
    icon: 'fas fa-sitemap',
    iconColor: '#8b5cf6', // 紫色
    iconBg: '#f3f0ff',
    description: '复杂业务流程自动化处理',
    fullDescription: '工作流智能体专门用于处理复杂的业务流程自动化。它可以将多个步骤串联起来，实现端到端的业务流程管理，包括任务分配、状态跟踪、异常处理等功能。适用于需要多步骤协调的复杂业务场景。',
    example: '/images/workflow-example.png'
  },
  {
    value: 'chat',
    label: '聊天助手',
    icon: 'fas fa-headset',
    iconColor: '#10b981', // 绿色
    iconBg: '#ecfdf5',
    description: '智能对话交互助手',
    fullDescription: '聊天助手是最基础的对话型智能体，专注于提供自然流畅的对话体验。它可以理解用户意图，提供准确的回答和建议。适用于客户咨询、知识问答、日常助理等需要对话交互的场景。',
    example: '/images/chat-assistant-example.png',
    isBeginnerFriendly: true
  },
  {
    value: 'agent-chat',
    label: 'Agent',
    icon: 'fas fa-user-robot',
    iconColor: '#1c3d5a', // 深蓝色
    iconBg: '#f0f4f8',
    description: '智能体模式的聊天功能',
    fullDescription: 'Agent结合了传统聊天和智能代理的优势，具备自主思考和决策能力。可以主动提出建议、执行任务、调用工具，提供更加智能化的交互体验。适用于个人助理、智能客服、业务咨询等场景。',
    example: '/images/agent-chat-example.png',
    isBeginnerFriendly: true
  },
  {
    value: 'completion',
    label: '文本生成',
    icon: 'fas fa-pen-fancy',
    iconColor: '#ef4444', // 红色
    iconBg: '#fef2f2',
    description: '专业的文本内容生成工具',
    fullDescription: '文本生成应用专门用于创建各种类型的文本内容。它具备强大的语言生成能力，可以根据用户需求生成文章、报告、创意文案等。支持多种写作风格和格式，适用于内容创作、文档生成、营销文案等场景。',
    example: '/images/text-generation-example.png',
    isBeginnerFriendly: true
  }
])



// 计算属性
const canCreateAgent = computed(() => {
  if (selectedSourceType.value === 'platform') {
    return selectedAgentType.value && newAgentName.value.trim() && selectedIcon.value
  } else if (selectedSourceType.value === 'external') {
    return selectedExternalAgentId.value && selectedPlatformId.value && selectedAgentType.value && newAgentName.value.trim() && selectedIcon.value
  }
  return false
})

// 方法
const closeModal = () => {
  emit('update:visible', false)
}

const selectSourceType = (sourceType: string) => {
  selectedSourceType.value = sourceType
  // 重置相关字段
  if (sourceType === 'platform') {
    selectedExternalAgentId.value = ''
    selectedPlatformId.value = ''
    selectedExternalAgentName.value = ''
    showExternalAgentDropdown.value = false
  } else if (sourceType === 'external') {
    selectedAgentType.value = ''
    // 显示外部智能体选择下拉层
    showExternalAgentDropdown.value = true
    // 加载外部智能体列表
    loadExternalAgents()
  }
}

const selectAgentType = (typeValue: string) => {
  selectedAgentType.value = typeValue
}

const selectExternalAgent = (agent: any) => {
  selectedExternalAgentId.value = agent.id
  selectedPlatformId.value = agent.platformId || agent.thirdPlatformId || ''  // 设置平台ID
  selectedExternalAgentName.value = agent.name
  showExternalAgentDropdown.value = false
  console.log('Selected external agent:', agent)
}

const closeExternalAgentDropdown = () => {
  showExternalAgentDropdown.value = false
}



const loadExternalAgents = async () => {
  if (loadingExternalAgents.value) return

  loadingExternalAgents.value = true
  try {
    // 使用新的第三方智能体列表接口
    const response = await ThirdPlatformAPI.getThirdPlatformList()
    if (response.success && response.data) {
      externalAgents.value = response.data || []
    } else {
      console.error('加载外部智能体列表失败:', response.message)
      externalAgents.value = []
    }
  } catch (error) {
    console.error('加载外部智能体列表失败:', error)
    externalAgents.value = []
  } finally {
    loadingExternalAgents.value = false
  }
}

const getSelectedTypeInfo = () => {
  return agentTypes.value.find(type => type.value === selectedAgentType.value) || {
    label: '',
    fullDescription: '',
    example: ''
  }
}

const getSelectedExternalAgentInfo = () => {
  return externalAgents.value.find(agent => agent.id === selectedExternalAgentId.value) || {
    name: '',
    description: '',
    connectionUrl: '',
    accessScopeText: '',
    status: 0,
    statusText: ''
  }
}

const handleIconSelect = (data: { icon: string; background: string }) => {
  selectedIcon.value = data.icon
  selectedIconBackground.value = data.background
}

const confirmCreateAgent = async () => {
  if (!canCreateAgent.value || isCreating.value) return

  // 设置加载状态
  isCreating.value = true

  try {
    if (selectedSourceType.value === 'platform') {
      // 本平台智能体 - 直接使用选择的类型，无需映射
      const createRequest: CreateAgentRequest = {
        name: newAgentName.value.trim(),
        description: newAgentDescription.value.trim(),
        avatar: selectedIcon.value,
        iconBackground: selectedIconBackground.value,
        type: selectedAgentType.value as 'advanced-chat' | 'workflow' | 'chat' | 'agent-chat' | 'completion',
        modelConfig: JSON.stringify({
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 2000
        }),
        systemPrompt: '你是一个智能助手，请根据用户的问题提供准确、有用的回答。',
        isPublic: false,
        version: '1.0.0',
        sourceType: 'platform'
      }

      // 发送创建事件，传递请求数据
      emit('create', createRequest)
    } else {
      // 外部智能体 - 直接调用第三方智能体创建API
      const selectedAgent = externalAgents.value.find(agent => agent.id === selectedExternalAgentId.value)

      // 验证选中的智能体类型是否有效
      const validTypes = ['advanced-chat', 'workflow', 'chat', 'agent-chat', 'completion']
      const agentMode = selectedAgent?.mode || selectedAgentType.value
      const finalMode = validTypes.includes(agentMode) ? agentMode : 'advanced-chat'

      const thirdPartyRequest = {
        name: newAgentName.value.trim(),
        mode: finalMode,
        avatar: selectedIcon.value,
        iconBackground: selectedIconBackground.value,
        description: newAgentDescription.value.trim() || selectedAgent?.description || '',
        platformId: selectedAgent?.id || selectedPlatformId.value
      }

      console.log('创建第三方智能体请求:', thirdPartyRequest)

      // 直接调用第三方智能体创建API
      const response = await AgentsAPI.createThirdPartyAgent(thirdPartyRequest)

      if (response.success) {
        console.log('创建第三方智能体成功:', response)
        // 显示成功消息
        alert(`智能体"${thirdPartyRequest.name}"创建成功！`)

        // 触发父组件刷新列表
        emit('refresh')
      } else {
        console.error('创建第三方智能体失败:', response.message)
        alert(`创建第三方智能体失败：${response.message || '未知错误'}`)
        return // 不关闭模态框，让用户可以重试
      }
    }

    // 重置表单
    resetForm()

    // 关闭模态框
    closeModal()
  } catch (error) {
    console.error('创建智能体失败:', error)
    alert('创建智能体失败，请稍后重试')
  } finally {
    isCreating.value = false
  }
}

const resetForm = () => {
  selectedSourceType.value = 'platform'
  selectedAgentType.value = ''
  selectedExternalAgentId.value = ''
  selectedPlatformId.value = ''
  selectedExternalAgentName.value = ''
  newAgentName.value = ''
  newAgentDescription.value = ''
  selectedIcon.value = 'fas fa-robot'
  selectedIconBackground.value = 'linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)'
  showIconSelector.value = false
  showExternalAgentDropdown.value = false
}

// 初始化外部智能体列表
onMounted(() => {
  // 预加载外部智能体列表
  loadExternalAgents()
})

// 监听 visible 变化，重置表单
import { watch } from 'vue'
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 默认选择本平台智能体
    selectedSourceType.value = 'platform'
    selectedAgentType.value = 'advanced-chat'
    selectedIcon.value = 'fas fa-brain'
    selectedIconBackground.value = 'linear-gradient(135deg, #ff6b35 0%, #ff6b35aa 100%)'
  } else {
    resetForm()
  }
})
</script>

<style scoped>
/* 创建智能体模态框 */
.create-agent-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.create-agent-modal-content {
  background: #ffffff;
  border-radius: 12px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  border: 1px solid #f1f5f9;
}

.create-agent-modal-header {
  padding: 24px 32px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
}

.create-agent-modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
}

.create-agent-modal-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.create-left-panel {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #ffffff;
}

.create-right-panel {
  width: 350px;
  padding: 24px;
  background: #fafbfc;
  border-left: 1px solid #f1f5f9;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
}

.form-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 智能体来源类型选择 */
.source-types-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.source-type-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  min-height: 70px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.source-type-card:hover {
  border-color: #d1e9ff;
  background: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.source-type-card.active {
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 智能体类型卡片 */
.agent-types-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.agent-type-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  min-height: 70px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.agent-type-card:hover {
  border-color: #d1e9ff;
  background: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.agent-type-card.active {
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-size: 18px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.agent-type-card.active .type-icon {
  opacity: 0.9;
}

.type-info {
  flex: 1;
}

.type-info h4 {
  margin: 0 0 4px 0;
  font-size: 15px;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.2;
}

.agent-type-card.active .type-info h4 {
  color: #1a202c;
}

.type-info p {
  margin: 0;
  font-size: 13px;
  color: #4a5568;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-type-card.active .type-info p {
  color: #4a5568;
}

.type-check {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  flex-shrink: 0;
  font-size: 12px;
}

.agent-type-card.active .type-check {
  background: #3b82f6;
  border-color: #3b82f6;
  opacity: 1;
  color: #ffffff;
}

/* 新手适用标记样式 */
.beginner-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
  color: #92400e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 3px;
  box-shadow: 0 2px 6px rgba(251, 191, 36, 0.3);
  z-index: 10;
  border: 1px solid rgba(251, 191, 36, 0.4);
}

.beginner-badge i {
  font-size: 8px;
  color: #f59e0b;
}

.beginner-badge span {
  white-space: nowrap;
}

/* 激活状态下的新手标记 */
.agent-type-card.active .beginner-badge {
  background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
  color: #92400e;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
  border: 1px solid rgba(251, 191, 36, 0.6);
}

/* 外部智能体选择器 */
.external-agent-selector {
  margin-bottom: 16px;
}

/* 外部智能体下拉层 */
.external-agent-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow: hidden;
  margin-top: 8px;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.dropdown-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.dropdown-content {
  max-height: 320px;
  overflow-y: auto;
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 32px 16px;
  color: #6b7280;
  font-size: 14px;
}

.loading-state i {
  color: #3b82f6;
}

.empty-state i {
  color: #f59e0b;
}

.agent-list {
  padding: 8px 0;
}

.agent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.agent-item:hover {
  background: #f9fafb;
}

.agent-item.selected {
  background: #eff6ff;
  border-color: #dbeafe;
}

.agent-item:last-child {
  border-bottom: none;
}

.agent-info {
  flex: 1;
}

.agent-info h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.agent-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.agent-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #1a202c;
  cursor: pointer;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.loading-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  font-size: 13px;
  color: #6b7280;
}

.loading-hint i {
  color: #3b82f6;
}

/* 认证字段 */
.auth-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-field label {
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
}

/* 表单输入样式 */
.form-section .form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #1a202c;
}

.form-section .form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-section .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #1a202c;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-section .form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-hint {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
  margin-top: 6px;
  font-weight: 500;
}

/* 图标选择器 */
.icon-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-icon {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
}

.current-icon:hover {
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.icon-preview {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f7ff;
  color: #ffffff;
  font-size: 18px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.current-icon span {
  font-size: 14px;
  color: #1a202c;
  font-weight: 500;
}

/* 右侧预览面板 */
.type-preview {
  background: #ffffff;
  border-radius: 10px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
}

.type-preview h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.type-description {
  margin-bottom: 24px;
}

.type-description p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

.type-example h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.example-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e0;
}

.example-image img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 6px;
}

/* 外部智能体信息 */
.external-agent-info {
  margin-top: 24px;
}

.external-agent-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.info-item .value {
  font-size: 14px;
  color: #1a202c;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.info-item .value.status-active {
  color: #10b981;
}

.type-preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6b7280;
  text-align: center;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d1d5db;
}

/* 模态框底部 */
.create-agent-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-top: 1px solid #f1f5f9;
  background: #ffffff;
}

.create-tip {
  font-size: 13px;
  color: #059669;
  font-weight: 500;
  background: #ecfdf5;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #d1fae5;
}

.footer-right {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn-modern.btn-secondary {
  background: #ffffff;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.btn-modern.btn-secondary:hover {
  background: #f9fafb;
  color: #1f2937;
  border-color: #9ca3af;
}

.btn-modern.btn-primary {
  background: #3b82f6;
  color: #ffffff;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-modern.btn-primary:hover {
  background: #2563eb;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
}

.btn-modern.btn-primary:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}



.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e7eb;
}

.close-btn:hover {
  background: #ffffff;
  color: #374151;
  border-color: #d1d5db;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .create-agent-modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .create-agent-modal-body {
    flex-direction: column;
  }

  .create-right-panel {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e2e8f0;
  }

  .agent-types-grid {
    grid-template-columns: 1fr;
  }

  .create-left-panel,
  .create-right-panel {
    padding: 20px;
  }
}

@media (max-width: 600px) {
  .create-agent-modal-header {
    padding: 16px 20px;
  }

  .create-agent-modal-header h2 {
    font-size: 20px;
  }

  .form-section {
    margin-bottom: 20px;
  }

  .agent-type-card {
    padding: 10px;
    min-height: 60px;
  }

  .beginner-badge {
    top: -4px;
    right: -4px;
    padding: 2px 5px;
    font-size: 8px;
  }

  .beginner-badge i {
    font-size: 6px;
  }

  .type-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
    margin-right: 10px;
  }

  .type-info h4 {
    font-size: 13px;
  }

  .type-info p {
    font-size: 11px;
  }
}
</style>
