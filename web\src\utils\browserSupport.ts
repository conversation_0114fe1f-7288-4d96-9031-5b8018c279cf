/**
 * 浏览器特性支持检测工具
 * 用于检测各种现代浏览器特性的支持情况
 */

export interface BrowserSupport {
  cssGrid: boolean
  flexbox: boolean
  cssVariables: boolean
  backdropFilter: boolean
  objectFit: boolean
  stickyPosition: boolean
  clipPath: boolean
  webp: boolean
  avif: boolean
  intersectionObserver: boolean
  resizeObserver: boolean
  customElements: boolean
  es6Modules: boolean
  asyncAwait: boolean
  optionalChaining: boolean
  nullishCoalescing: boolean
}

/**
 * 检测CSS Grid支持
 */
export function supportsCSSGrid(): boolean {
  return CSS.supports('display', 'grid')
}

/**
 * 检测Flexbox支持
 */
export function supportsFlexbox(): boolean {
  return CSS.supports('display', 'flex')
}

/**
 * 检测CSS变量支持
 */
export function supportsCSSVariables(): boolean {
  return CSS.supports('color', 'var(--test)')
}

/**
 * 检测backdrop-filter支持
 */
export function supportsBackdropFilter(): boolean {
  return CSS.supports('backdrop-filter', 'blur(1px)') || 
         CSS.supports('-webkit-backdrop-filter', 'blur(1px)')
}

/**
 * 检测object-fit支持
 */
export function supportsObjectFit(): boolean {
  return CSS.supports('object-fit', 'cover')
}

/**
 * 检测sticky定位支持
 */
export function supportsStickyPosition(): boolean {
  return CSS.supports('position', 'sticky') || 
         CSS.supports('position', '-webkit-sticky')
}

/**
 * 检测clip-path支持
 */
export function supportsClipPath(): boolean {
  return CSS.supports('clip-path', 'polygon(0 0, 100% 0, 100% 100%, 0 100%)')
}

/**
 * 检测WebP图片格式支持
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image()
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2)
    }
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
  })
}

/**
 * 检测AVIF图片格式支持
 */
export function supportsAVIF(): Promise<boolean> {
  return new Promise((resolve) => {
    const avif = new Image()
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2)
    }
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A='
  })
}

/**
 * 检测Intersection Observer支持
 */
export function supportsIntersectionObserver(): boolean {
  return 'IntersectionObserver' in window
}

/**
 * 检测Resize Observer支持
 */
export function supportsResizeObserver(): boolean {
  return 'ResizeObserver' in window
}

/**
 * 检测Custom Elements支持
 */
export function supportsCustomElements(): boolean {
  return 'customElements' in window
}

/**
 * 检测ES6模块支持
 */
export function supportsES6Modules(): boolean {
  const script = document.createElement('script')
  return 'noModule' in script
}

/**
 * 检测async/await支持
 */
export function supportsAsyncAwait(): boolean {
  try {
    return (async () => {})().constructor === (async () => {}).constructor
  } catch {
    return false
  }
}

/**
 * 检测可选链操作符支持
 */
export function supportsOptionalChaining(): boolean {
  try {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const test = (({} as any)?.test)
    return true
  } catch {
    return false
  }
}

/**
 * 检测空值合并操作符支持
 * 通过检查浏览器版本来判断是否支持空值合并操作符
 */
export function supportsNullishCoalescing(): boolean {
  // 空值合并操作符 (??) 在以下版本开始支持：
  // Chrome 80+, Firefox 72+, Safari 13.1+, Edge 80+

  const userAgent = navigator.userAgent

  // Chrome/Chromium (包括 Edge Chromium)
  if (/Chrome\/(\d+)/.test(userAgent)) {
    const version = parseInt(RegExp.$1, 10)
    return version >= 80
  }

  // Firefox
  if (/Firefox\/(\d+)/.test(userAgent)) {
    const version = parseInt(RegExp.$1, 10)
    return version >= 72
  }

  // Safari
  if (/Version\/(\d+\.\d+).*Safari/.test(userAgent)) {
    const version = parseFloat(RegExp.$1)
    return version >= 13.1
  }

  // 对于其他浏览器，假设支持（现代浏览器基本都支持）
  return true
}

/**
 * 获取完整的浏览器支持信息
 */
export async function getBrowserSupport(): Promise<BrowserSupport> {
  const [webp, avif] = await Promise.all([
    supportsWebP(),
    supportsAVIF()
  ])

  return {
    cssGrid: supportsCSSGrid(),
    flexbox: supportsFlexbox(),
    cssVariables: supportsCSSVariables(),
    backdropFilter: supportsBackdropFilter(),
    objectFit: supportsObjectFit(),
    stickyPosition: supportsStickyPosition(),
    clipPath: supportsClipPath(),
    webp,
    avif,
    intersectionObserver: supportsIntersectionObserver(),
    resizeObserver: supportsResizeObserver(),
    customElements: supportsCustomElements(),
    es6Modules: supportsES6Modules(),
    asyncAwait: supportsAsyncAwait(),
    optionalChaining: supportsOptionalChaining(),
    nullishCoalescing: supportsNullishCoalescing()
  }
}

/**
 * 为不支持的特性添加CSS类
 */
export function addFeatureClasses(): void {
  const html = document.documentElement

  // CSS特性检测
  if (!supportsCSSGrid()) {
    html.classList.add('no-css-grid')
  }
  
  if (!supportsFlexbox()) {
    html.classList.add('no-flexbox')
  }
  
  if (!supportsCSSVariables()) {
    html.classList.add('no-css-variables')
  }
  
  if (!supportsBackdropFilter()) {
    html.classList.add('no-backdrop-filter')
  }
  
  if (!supportsObjectFit()) {
    html.classList.add('no-object-fit')
  }
  
  if (!supportsStickyPosition()) {
    html.classList.add('no-sticky')
  }
  
  if (!supportsClipPath()) {
    html.classList.add('no-clip-path')
  }

  // JavaScript特性检测
  if (!supportsIntersectionObserver()) {
    html.classList.add('no-intersection-observer')
  }
  
  if (!supportsResizeObserver()) {
    html.classList.add('no-resize-observer')
  }
  
  if (!supportsOptionalChaining()) {
    html.classList.add('no-optional-chaining')
  }
  
  if (!supportsNullishCoalescing()) {
    html.classList.add('no-nullish-coalescing')
  }
}

/**
 * 初始化浏览器兼容性检测
 */
export function initBrowserSupport(): void {
  // 添加特性检测类
  addFeatureClasses()
  
  // 异步检测图片格式支持
  Promise.all([supportsWebP(), supportsAVIF()]).then(([webp, avif]) => {
    const html = document.documentElement
    if (webp) html.classList.add('webp')
    if (avif) html.classList.add('avif')
  })
  
  // 输出浏览器支持信息（开发环境）
  if (import.meta.env?.DEV) {
    getBrowserSupport().then(support => {
      console.log('Browser Support:', support)
    })
  }
}
