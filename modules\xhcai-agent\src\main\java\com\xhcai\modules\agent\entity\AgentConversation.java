package com.xhcai.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 智能体对话实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent_conversation")
@Schema(description = "智能体对话")
@TableName("agent_conversation")
public class AgentConversation extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @Column(name = "agent_id", length = 36)
    @Schema(description = "智能体ID", example = "agent_001")
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    @TableField("agent_id")
    private String agentId;

    /**
     * 对话标题
     */
    @Column(name = "title", length = 200)
    @Schema(description = "对话标题", example = "关于产品咨询的对话")
    @Size(max = 200, message = "对话标题长度不能超过200个字符")
    @TableField("title")
    private String title;

    /**
     * 用户ID
     */
    @Column(name = "user_id", length = 36)
    @Schema(description = "用户ID", example = "user_001")
    @Size(max = 36, message = "用户ID长度不能超过36个字符")
    @TableField("user_id")
    private String userId;

    /**
     * 会话ID（外部系统）
     */
    @Column(name = "session_id", length = 100)
    @Schema(description = "会话ID", example = "session_12345")
    @Size(max = 100, message = "会话ID长度不能超过100个字符")
    @TableField("session_id")
    private String sessionId;

    /**
     * 对话状态
     */
    @Column(name = "status", length = 20)
    @Schema(description = "对话状态", example = "active", allowableValues = {"active", "ended", "error"})
    @Size(max = 20, message = "对话状态长度不能超过20个字符")
    @TableField("status")
    private String status;

    /**
     * 消息数量
     */
    @Column(name = "message_count")
    @Schema(description = "消息数量", example = "10")
    @TableField("message_count")
    private Integer messageCount;

    /**
     * 开始时间
     */
    @Column(name = "started_at")
    @Schema(description = "开始时间")
    @TableField("started_at")
    private LocalDateTime startedAt;

    /**
     * 结束时间
     */
    @Column(name = "ended_at")
    @Schema(description = "结束时间")
    @TableField("ended_at")
    private LocalDateTime endedAt;

    /**
     * 最后活动时间
     */
    @Column(name = "last_activity_at")
    @Schema(description = "最后活动时间")
    @TableField("last_activity_at")
    private LocalDateTime lastActivityAt;

    /**
     * 总token消耗
     */
    @Column(name = "total_tokens")
    @Schema(description = "总token消耗", example = "1500")
    @TableField("total_tokens")
    private Long totalTokens;

    /**
     * 输入token消耗
     */
    @Column(name = "input_tokens")
    @Schema(description = "输入token消耗", example = "800")
    @TableField("input_tokens")
    private Long inputTokens;

    /**
     * 输出token消耗
     */
    @Column(name = "output_tokens")
    @Schema(description = "输出token消耗", example = "700")
    @TableField("output_tokens")
    private Long outputTokens;

    /**
     * 费用（分）
     */
    @Column(name = "cost")
    @Schema(description = "费用（分）", example = "50")
    @TableField("cost")
    private Long cost;

    /**
     * 用户反馈评分
     */
    @Column(name = "rating")
    @Schema(description = "用户反馈评分", example = "5")
    @TableField("rating")
    private Integer rating;

    /**
     * 用户反馈内容
     */
    @Column(name = "feedback", length = 1000)
    @Schema(description = "用户反馈内容", example = "回答很有帮助")
    @Size(max = 1000, message = "用户反馈内容长度不能超过1000个字符")
    @TableField("feedback")
    private String feedback;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "metadata", columnDefinition = "TEXT")
    @Schema(description = "扩展信息", example = "{\"source\":\"web\",\"device\":\"mobile\"}")
    @TableField("metadata")
    private String metadata;

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getMessageCount() {
        return messageCount;
    }

    public void setMessageCount(Integer messageCount) {
        this.messageCount = messageCount;
    }

    public LocalDateTime getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }

    public LocalDateTime getEndedAt() {
        return endedAt;
    }

    public void setEndedAt(LocalDateTime endedAt) {
        this.endedAt = endedAt;
    }

    public LocalDateTime getLastActivityAt() {
        return lastActivityAt;
    }

    public void setLastActivityAt(LocalDateTime lastActivityAt) {
        this.lastActivityAt = lastActivityAt;
    }

    public Long getTotalTokens() {
        return totalTokens;
    }

    public void setTotalTokens(Long totalTokens) {
        this.totalTokens = totalTokens;
    }

    public Long getInputTokens() {
        return inputTokens;
    }

    public void setInputTokens(Long inputTokens) {
        this.inputTokens = inputTokens;
    }

    public Long getOutputTokens() {
        return outputTokens;
    }

    public void setOutputTokens(Long outputTokens) {
        this.outputTokens = outputTokens;
    }

    public Long getCost() {
        return cost;
    }

    public void setCost(Long cost) {
        this.cost = cost;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }
}
