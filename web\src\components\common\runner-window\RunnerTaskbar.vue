<template>
  <div class="runner-taskbar" v-if="Array.isArray(minimizedWindows) && minimizedWindows.length > 0">
    <div class="taskbar-container">
      <div class="taskbar-title">
        <el-icon><Monitor /></el-icon>
        <span>运行窗口</span>
      </div>
      <div class="taskbar-items">
        <div
          v-for="window in minimizedWindows"
          :key="window.id"
          class="taskbar-item"
          @click="restoreWindow(window.id)"
          :title="window.title"
        >
          <div class="item-icon" v-if="window.headerInfo?.icon" :style="{ backgroundColor: window.headerInfo.iconBg || '#3b82f6' }">
            <i :class="window.headerInfo.icon" v-if="window.headerInfo.icon.startsWith('fa')"></i>
            <span v-else>{{ window.headerInfo.icon }}</span>
          </div>
          <div class="item-icon default-icon" v-else>
            <el-icon><Document /></el-icon>
          </div>
          <span class="item-title">{{ window.title }}</span>
          <button class="item-close" @click.stop="closeMinimizedWindow(window.id)" title="关闭">
            <el-icon><Close /></el-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Monitor, Document, Close } from '@element-plus/icons-vue'

interface MinimizedWindow {
  id: string
  title: string
  icon?: string
  headerInfo?: {
    icon?: string
    iconBg?: string
    title?: string
  }
}

interface Props {
  minimizedWindows: MinimizedWindow[] | any
}

interface Emits {
  restore: [windowId: string]
  close: [windowId: string]
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const restoreWindow = (windowId: string) => {
  emit('restore', windowId)
}

const closeMinimizedWindow = (windowId: string) => {
  emit('close', windowId)
}
</script>

<style scoped>
.runner-taskbar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
}

.taskbar-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px;
}

.taskbar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 8px;
  padding: 0 4px;
}

.taskbar-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.taskbar-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid transparent;
}

.taskbar-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateX(-2px);
}

.item-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  flex-shrink: 0;
}

.default-icon {
  background: #6b7280;
  color: white;
}

.item-title {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.item-close {
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
  flex-shrink: 0;
}

.taskbar-item:hover .item-close {
  opacity: 1;
}

.item-close:hover {
  background: #ef4444;
  color: white;
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .runner-taskbar {
    bottom: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .taskbar-container {
    border-radius: 8px;
    padding: 8px;
  }

  .taskbar-items {
    max-height: 200px;
    overflow-y: auto;
  }

  .taskbar-item {
    padding: 6px 8px;
  }

  .item-icon {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }

  .item-title {
    font-size: 12px;
  }

  .item-close {
    width: 18px;
    height: 18px;
  }
}

/* 滚动条样式 */
.taskbar-items::-webkit-scrollbar {
  width: 4px;
}

.taskbar-items::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.taskbar-items::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.taskbar-items::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
