package com.xhcai.modules.agent.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

public class HistoryStatsVO implements Serializable {

    private Long totalCount;
    private Long majorChangeCount;
    private LocalDateTime firstOperationTime;
    private LocalDateTime lastOperationTime;
    private Long totalConfigSize;
    private Integer uniqueOperatorCount;

    // Getters and Setters
    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getMajorChangeCount() {
        return majorChangeCount;
    }

    public void setMajorChangeCount(Long majorChangeCount) {
        this.majorChangeCount = majorChangeCount;
    }

    public LocalDateTime getFirstOperationTime() {
        return firstOperationTime;
    }

    public void setFirstOperationTime(LocalDateTime firstOperationTime) {
        this.firstOperationTime = firstOperationTime;
    }

    public LocalDateTime getLastOperationTime() {
        return lastOperationTime;
    }

    public void setLastOperationTime(LocalDateTime lastOperationTime) {
        this.lastOperationTime = lastOperationTime;
    }

    public Long getTotalConfigSize() {
        return totalConfigSize;
    }

    public void setTotalConfigSize(Long totalConfigSize) {
        this.totalConfigSize = totalConfigSize;
    }

    public Integer getUniqueOperatorCount() {
        return uniqueOperatorCount;
    }

    public void setUniqueOperatorCount(Integer uniqueOperatorCount) {
        this.uniqueOperatorCount = uniqueOperatorCount;
    }
}
