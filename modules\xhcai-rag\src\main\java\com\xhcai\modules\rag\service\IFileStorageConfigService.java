package com.xhcai.modules.rag.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.rag.dto.FileStorageConfigCreateDTO;
import com.xhcai.modules.rag.dto.FileStorageConfigQueryDTO;
import com.xhcai.modules.rag.dto.FileStorageConfigUpdateDTO;
import com.xhcai.modules.rag.entity.FileStorageConfig;
import com.xhcai.modules.rag.vo.FileStorageConfigVO;

import java.util.List;

/**
 * 文件存储配置服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IFileStorageConfigService extends IService<FileStorageConfig> {

    /**
     * 分页查询文件存储配置列表
     */
    PageResult<FileStorageConfigVO> selectFileStorageConfigPage(FileStorageConfigQueryDTO queryDTO);

    /**
     * 查询文件存储配置列表
     */
    List<FileStorageConfigVO> selectFileStorageConfigList(FileStorageConfigQueryDTO queryDTO);

    /**
     * 根据ID查询文件存储配置
     */
    FileStorageConfigVO selectFileStorageConfigById(String id);

    /**
     * 创建文件存储配置
     */
    boolean createFileStorageConfig(FileStorageConfigCreateDTO createDTO);

    /**
     * 更新文件存储配置
     */
    boolean updateFileStorageConfig(String id, FileStorageConfigUpdateDTO updateDTO);

    /**
     * 删除文件存储配置
     */
    boolean deleteFileStorageConfigs(List<String> ids);

    /**
     * 批量更新状态
     */
    boolean batchUpdateStatus(List<String> ids, String status);

    /**
     * 设置默认存储配置
     */
    boolean setDefaultConfig(String id);

    /**
     * 获取默认存储配置
     */
    FileStorageConfigVO getDefaultConfig();

    /**
     * 根据存储类型获取配置
     */
    List<FileStorageConfigVO> getConfigsByType(String storageType);

    /**
     * 测试存储连接
     */
    boolean testConnection(String id);

    /**
     * 测试存储连接（根据配置）
     */
    boolean testConnection(FileStorageConfigCreateDTO config);
}
