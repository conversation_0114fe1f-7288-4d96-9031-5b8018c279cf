@echo off
echo Building plugins...

set PROJECT_ROOT=%~dp0..
cd /d "%PROJECT_ROOT%"

echo Building MinIO Storage Plugin...
cd plugins\storage\minio-storage-plugin
call mvn clean package -DskipTests
if exist target\minio-storage-plugin-1.0.0.jar (
    copy target\minio-storage-plugin-1.0.0.jar ..\..\..\plugins\storage\
    echo MinIO Storage Plugin built successfully
) else (
    echo Failed to build MinIO Storage Plugin
)

echo.
echo Building OpenAI Model Plugin...
cd ..\..\..\plugins\model\openai-model-plugin
call mvn clean package -DskipTests
if exist target\openai-model-plugin-1.0.0.jar (
    copy target\openai-model-plugin-1.0.0.jar ..\..\..\plugins\model\
    echo OpenAI Model Plugin built successfully
) else (
    echo Failed to build OpenAI Model Plugin
)

echo.
echo Building Email Notification Plugin...
cd ..\..\..\plugins\notify\email-notification-plugin
call mvn clean package -DskipTests
if exist target\email-notification-plugin-1.0.0.jar (
    copy target\email-notification-plugin-1.0.0.jar ..\..\..\plugins\notify\
    echo Email Notification Plugin built successfully
) else (
    echo Failed to build Email Notification Plugin
)

cd "%PROJECT_ROOT%"
echo.
echo Plugin build completed!
pause
