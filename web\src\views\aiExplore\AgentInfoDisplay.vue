<template>
  <div class="agent-info-display">
    <!-- 智能体头部信息 -->
    <div class="agent-header text-center py-8">
      <div class="agent-avatar mb-4">
        <div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-2xl font-bold shadow-lg">
          {{ agentInfo?.icon || agentInfo?.name?.charAt(0) || '🤖' }}
        </div>
      </div>
      
      <h1 class="text-2xl font-bold text-gray-900 mb-2">
        {{ agentInfo?.name || '智能助手' }}
      </h1>
      
      <div class="agent-meta flex items-center justify-center gap-4 text-sm text-gray-600 mb-4">
        <span v-if="agentInfo?.type" class="flex items-center gap-1">
          <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
          {{ agentInfo.type }}
        </span>
        <span v-if="agentInfo?.category" class="flex items-center gap-1">
          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
          {{ agentInfo.category }}
        </span>
        <span v-if="agentInfo?.version" class="flex items-center gap-1">
          <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
          v{{ agentInfo.version }}
        </span>
      </div>
    </div>

    <!-- 智能体描述 -->
    <div class="agent-description bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
        <span class="text-blue-500">📝</span>
        智能体介绍
      </h3>
      <p class="text-gray-700 leading-relaxed">
        {{ agentInfo?.description || '这是一个智能助手，可以帮助您解决各种问题。' }}
      </p>
    </div>

    <!-- 功能特性 -->
    <div v-if="agentInfo?.features && agentInfo.features.length > 0" class="agent-features mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <span class="text-green-500">⚡</span>
        主要功能
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div
          v-for="(feature, index) in agentInfo.features"
          :key="index"
          class="feature-item flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
        >
          <span class="text-blue-500 text-lg">{{ feature.icon || '✨' }}</span>
          <div>
            <h4 class="font-medium text-gray-900">{{ feature.name }}</h4>
            <p v-if="feature.description" class="text-sm text-gray-600">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用提示 -->
    <div class="usage-tips bg-amber-50 rounded-xl p-6 mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
        <span class="text-amber-500">💡</span>
        使用提示
      </h3>
      <div class="space-y-2">
        <p class="text-gray-700 text-sm flex items-start gap-2">
          <span class="text-amber-500 mt-0.5">•</span>
          {{ agentInfo?.usageTip || '您可以直接在下方输入框中与我对话，我会尽力为您提供帮助。' }}
        </p>
        <p v-if="agentInfo?.examples && agentInfo.examples.length > 0" class="text-gray-700 text-sm flex items-start gap-2">
          <span class="text-amber-500 mt-0.5">•</span>
          试试问我：{{ agentInfo.examples.slice(0, 2).join('、') }}
        </p>
      </div>
    </div>

    <!-- 快速开始示例 -->
    <div class="quick-start">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <span class="text-purple-500">🚀</span>
        快速开始
      </h3>
      <div class="grid grid-cols-1 gap-3">
        <button
          v-for="(example, index) in quickStartExamples"
          :key="index"
          @click="handleQuickStart(example)"
          class="quick-start-item text-left p-4 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 group"
        >
          <div class="flex items-center gap-3">
            <span class="text-purple-500 text-lg group-hover:scale-110 transition-transform">{{ example.icon || '💬' }}</span>
            <div class="flex-1">
              <h4 class="font-medium text-gray-900 group-hover:text-purple-700">{{ example.title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ example.description }}</p>
            </div>
            <span class="text-gray-400 group-hover:text-purple-500 transition-colors">→</span>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义智能体信息的类型
interface AgentInfo {
  id?: string
  name?: string
  type?: string
  description?: string
  avatar?: string
  quickStart?: QuickStartExample[]
  [key: string]: any // 允许其他属性
}

// Props
interface Props {
  agentInfo: AgentInfo
}

const props = defineProps<Props>()

// 定义示例项的类型
interface QuickStartExample {
  icon: string
  title: string
  description: string
  content: string
}

// 定义事件
const emit = defineEmits<{
  'quick-start': [example: QuickStartExample]
}>()

// 快速开始示例
const quickStartExamples = computed(() => {
  // 如果智能体有自定义的快速开始示例，使用它们
  if (props.agentInfo?.quickStart && props.agentInfo.quickStart.length > 0) {
    return props.agentInfo.quickStart
  }

  // 否则根据智能体类型提供默认示例
  const defaultExamples: Record<string, QuickStartExample[]> = {
    '聊天助手': [
      { icon: '💬', title: '开始对话', description: '你好，我想了解一下...', content: '你好，我想了解一下...' },
      { icon: '❓', title: '提出问题', description: '请帮我解答一个问题', content: '请帮我解答一个问题' }
    ],
    'Agent': [
      { icon: '🤖', title: '智能分析', description: '帮我分析一下这个问题', content: '帮我分析一下这个问题' },
      { icon: '📊', title: '数据处理', description: '请处理这些数据', content: '请处理这些数据' }
    ],
    '文本生成': [
      { icon: '✍️', title: '文本创作', description: '帮我写一篇关于...的文章', content: '帮我写一篇关于...的文章' },
      { icon: '📝', title: '内容优化', description: '请优化这段文字', content: '请优化这段文字' }
    ]
  }

  // 使用类型安全的方式访问 defaultExamples
  const agentType = props.agentInfo?.type as string
  return defaultExamples[agentType] || [
    { icon: '💬', title: '开始对话', description: '你好，我想了解一下...', content: '你好，我想了解一下...' },
    { icon: '❓', title: '提出问题', description: '请帮我解答一个问题', content: '请帮我解答一个问题' }
  ]
})

// 处理快速开始
const handleQuickStart = (example: QuickStartExample) => {
  emit('quick-start', example)
}
</script>

<style scoped>
.agent-info-display {
  max-width: 800px;
  margin: 0 auto;
}

.agent-avatar {
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
}

.feature-item {
  transition: all 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-start-item {
  transition: all 0.2s ease;
}

.quick-start-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-header {
    padding: 1.5rem 0;
  }
  
  .agent-description,
  .usage-tips {
    padding: 1rem;
  }
  
  .quick-start-item {
    padding: 0.75rem;
  }
}
</style>
