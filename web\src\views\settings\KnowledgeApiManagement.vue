<template>
  <div class="knowledge-api-management bg-white rounded-lg shadow-sm p-6">
    <div class="api-header flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-800">知识库API密钥列表</h3>
      <button @click="showAddApiKeyModal = true" class="btn-primary">
        <span class="mr-2">➕</span>
        分配密钥
      </button>
    </div>

    <div class="knowledge-api-table">
      <div class="table-header grid grid-cols-9 gap-2 p-4 bg-gray-50 rounded-t-lg font-medium text-gray-700 text-sm">
        <div>知识库名</div>
        <div>单位</div>
        <div>设计者</div>
        <div>授权密钥</div>
        <div>授权时间</div>
        <div>使用单位</div>
        <div>使用者</div>
        <div>联系方式</div>
        <div>访问频率</div>
      </div>
      <div class="table-body">
        <div
          v-for="apiKey in paginatedKnowledgeApiKeys"
          :key="apiKey.id"
          class="table-row grid grid-cols-9 gap-2 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-300 text-sm"
        >
          <div class="font-medium text-gray-800">{{ apiKey.knowledgeName }}</div>
          <div class="text-gray-600">{{ apiKey.unit }}</div>
          <div class="text-gray-600">{{ apiKey.designer }}</div>
          <div class="text-gray-600">
            <span v-if="apiKey.showKey">{{ apiKey.apiKey }}</span>
            <span v-else>{{ apiKey.maskedKey }}</span>
            <button @click="toggleKeyVisibility(apiKey)" class="ml-2 text-blue-500 hover:text-blue-700">
              {{ apiKey.showKey ? '隐藏' : '显示' }}
            </button>
          </div>
          <div class="text-gray-600">{{ apiKey.authorizedAt }}</div>
          <div class="text-gray-600">{{ apiKey.useUnit }}</div>
          <div class="text-gray-600">{{ apiKey.userName }}</div>
          <div class="text-gray-600">{{ apiKey.contact }}</div>
          <div class="text-gray-600">{{ apiKey.frequency }}</div>
        </div>
      </div>
    </div>

    <!-- 知识库API密钥管理分页 -->
    <Pagination
      v-model:currentPage="knowledgeApiPagination.currentPage"
      v-model:pageSize="knowledgeApiPagination.pageSize"
      :total="knowledgeApiKeys.length"
    />

    <!-- 添加API密钥模态框 -->
    <div v-if="showAddApiKeyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">分配知识库API密钥</h3>
          <button @click="closeApiKeyModal" class="text-gray-400 hover:text-gray-600">
            <span class="text-xl">×</span>
          </button>
        </div>

        <form @submit.prevent="saveApiKey" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="form-label">知识库名称</label>
              <select v-model="currentApiKey.knowledgeId" class="form-input" required>
                <option value="">请选择知识库</option>
                <option v-for="kb in knowledgeBases" :key="kb.id" :value="kb.id">{{ kb.name }}</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="form-label">设计者单位</label>
              <select v-model="currentApiKey.unitId" class="form-input" required>
                <option value="">请选择单位</option>
                <option v-for="unit in units" :key="unit.id" :value="unit.id">{{ unit.name }}</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="form-label">设计者</label>
              <input v-model="currentApiKey.designer" type="text" class="form-input" required />
            </div>
            
            <div class="form-group">
              <label class="form-label">使用单位</label>
              <input v-model="currentApiKey.useUnit" type="text" class="form-input" required />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="form-label">使用者</label>
              <input v-model="currentApiKey.userName" type="text" class="form-input" required />
            </div>
            
            <div class="form-group">
              <label class="form-label">联系方式</label>
              <input v-model="currentApiKey.contact" type="text" class="form-input" required />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="form-label">项目名称</label>
              <input v-model="currentApiKey.projectName" type="text" class="form-input" />
            </div>
            
            <div class="form-group">
              <label class="form-label">访问频率限制</label>
              <select v-model="currentApiKey.frequency" class="form-input">
                <option value="50次/天">50次/天</option>
                <option value="100次/天">100次/天</option>
                <option value="200次/天">200次/天</option>
                <option value="500次/天">500次/天</option>
                <option value="1000次/天">1000次/天</option>
                <option value="无限制">无限制</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">使用说明</label>
            <textarea v-model="currentApiKey.description" class="form-input" rows="3" placeholder="请描述API密钥的使用用途和场景"></textarea>
          </div>

          <div class="flex gap-3 pt-4">
            <button type="submit" class="btn-primary flex-1">生成并分配密钥</button>
            <button type="button" @click="closeApiKeyModal" class="btn-secondary flex-1">取消</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Pagination from '@/components/common/Pagination.vue'

// 知识库API密钥数据
const knowledgeApiKeys = ref([
  {
    id: 1,
    knowledgeName: '技术文档库',
    unit: '技术部',
    designer: '张三',
    apiKey: 'kb_1234567890abcdef',
    maskedKey: 'kb_****cdef',
    showKey: false,
    authorizedAt: '2024-01-15',
    useUnit: '产品部',
    userName: '李四',
    projectName: '智能助手项目',
    contact: '13800138001',
    frequency: '100次/天'
  },
  {
    id: 2,
    knowledgeName: '产品规范库',
    unit: '产品部',
    designer: '王五',
    apiKey: 'kb_abcdef1234567890',
    maskedKey: 'kb_****7890',
    showKey: false,
    authorizedAt: '2024-01-20',
    useUnit: '技术部',
    userName: '赵六',
    projectName: '产品开发',
    contact: '13900139002',
    frequency: '50次/天'
  }
])

// 知识库列表
const knowledgeBases = ref([
  { id: 1, name: '技术文档库' },
  { id: 2, name: '产品规范库' },
  { id: 3, name: '运营手册库' },
  { id: 4, name: '客服知识库' }
])

// 单位列表
const units = ref([
  { id: 1, name: '技术部' },
  { id: 2, name: '产品部' },
  { id: 3, name: '运营部' },
  { id: 4, name: '客服部' }
])

// 分页数据
const knowledgeApiPagination = ref({ currentPage: 1, pageSize: 10 })

// 分页计算属性
const paginatedKnowledgeApiKeys = computed(() => {
  const start = (knowledgeApiPagination.value.currentPage - 1) * knowledgeApiPagination.value.pageSize
  const end = start + knowledgeApiPagination.value.pageSize
  return knowledgeApiKeys.value.slice(start, end)
})

// 模态框状态
const showAddApiKeyModal = ref(false)

// 当前API密钥
const currentApiKey = ref({
  knowledgeId: '',
  unitId: '',
  designer: '',
  useUnit: '',
  userName: '',
  contact: '',
  projectName: '',
  frequency: '100次/天',
  description: ''
})

// API密钥管理方法
const toggleKeyVisibility = (apiKey: any) => {
  apiKey.showKey = !apiKey.showKey
}

const closeApiKeyModal = () => {
  showAddApiKeyModal.value = false
  currentApiKey.value = {
    knowledgeId: '',
    unitId: '',
    designer: '',
    useUnit: '',
    userName: '',
    contact: '',
    projectName: '',
    frequency: '100次/天',
    description: ''
  }
}

const generateApiKey = () => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = 'kb_'
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

const saveApiKey = () => {
  const knowledgeBase = knowledgeBases.value.find(kb => kb.id === Number(currentApiKey.value.knowledgeId))
  const unit = units.value.find(u => u.id === Number(currentApiKey.value.unitId))
  
  if (!knowledgeBase || !unit) {
    alert('请选择知识库和单位')
    return
  }

  const apiKey = generateApiKey()
  const newApiKeyRecord = {
    id: Date.now(),
    knowledgeName: knowledgeBase.name,
    unit: unit.name,
    designer: currentApiKey.value.designer,
    apiKey: apiKey,
    maskedKey: `kb_****${apiKey.slice(-4)}`,
    showKey: false,
    authorizedAt: new Date().toISOString().split('T')[0],
    useUnit: currentApiKey.value.useUnit,
    userName: currentApiKey.value.userName,
    projectName: currentApiKey.value.projectName,
    contact: currentApiKey.value.contact,
    frequency: currentApiKey.value.frequency
  }

  knowledgeApiKeys.value.push(newApiKeyRecord)
  closeApiKeyModal()
  alert('API密钥生成并分配成功')
}
</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');
</style>
