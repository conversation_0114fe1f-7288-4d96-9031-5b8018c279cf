package com.xhcai.modules.system.init;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.xhcai.modules.system.service.ISysDictDataService;
import com.xhcai.modules.system.service.ISysDictService;

/**
 * System模块权限初始化器 负责初始化System模块相关的权限数据和字典数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class SystemInitializer {

    private static final Logger log = LoggerFactory.getLogger(SystemInitializer.class);

    @Autowired
    private ISysDictService dictService;

    @Autowired
    private ISysDictDataService dictDataService;

    /**
     * 初始化系统模块权限和字典数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void initializePermissions() {
        log.info("开始初始化系统模块权限和字典数据...");
        try {
            // 初始化系统聊天权限
            initializeSystemChatPermissions();

            // 初始化系统模型管理权限
            initializeSystemModelPermissions();

            // 初始化系统文档处理权限
            initializeSystemDocumentPermissions();

            log.info("系统模块权限和字典数据初始化完成");
        } catch (Exception e) {
            log.error("系统模块权限和字典数据初始化失败", e);
            throw new RuntimeException("系统模块权限和字典数据初始化失败", e);
        }
    }

    /**
     * 初始化系统聊天权限
     */
    private void initializeSystemChatPermissions() {
        log.debug("初始化系统聊天权限");
        // 这里可以初始化以下权限：
        // - system:chat:send - 发送系统聊天消息
    }

    /**
     * 初始化系统模型管理权限
     */
    private void initializeSystemModelPermissions() {
        log.debug("初始化系统模型管理权限");
        // 这里可以初始化以下权限：
        // - system:model:list - 查看系统列表
    }

    /**
     * 初始化系统文档处理权限
     */
    private void initializeSystemDocumentPermissions() {
        log.debug("初始化系统文档处理权限");
        // 这里可以初始化以下权限：
        // - system:document:upload - 上传文档进行系统处理
    }

    /**
     * 初始化系统字典数据（全局，不分租户）
     */
    @Transactional(rollbackFor = Exception.class)
    public void initializeSystemDictionaries() {
        log.info("开始初始化系统字典数据...");
        try {
            // 初始化系统提供商字典
            initGlobalDictTypes();

            // 初始化系统模型类型字典
            initializeSystemTypeDictData();;

            log.info("系统字典数据初始化完成");
        } catch (Exception e) {
            log.error("系统字典数据初始化失败", e);
            throw new RuntimeException("系统字典数据初始化失败", e);
        }
    }

    /**
     * 初始化全局字典类型（不需要租户上下文）
     */
    private void initGlobalDictTypes() {
        log.info("开始初始化全局字典类型...");

        // 初始化各种字典类型（只创建SysDict记录，不创建SysDictData）
        dictService.createDictTypeIfNotExists("sys_user_gender", "用户性别", "用户性别列表", "Y");
        dictService.createDictTypeIfNotExists("sys_user_status", "用户状态", "用户状态列表", "Y");
        dictService.createDictTypeIfNotExists("sys_role_status", "角色状态", "角色状态列表", "Y");
        dictService.createDictTypeIfNotExists("sys_dept_status", "部门状态", "部门状态列表", "Y");
        dictService.createDictTypeIfNotExists("sys_permission_status", "权限状态", "权限状态列表", "Y");
        dictService.createDictTypeIfNotExists("sys_config_status", "配置状态", "配置状态列表", "Y");
        dictService.createDictTypeIfNotExists("sys_data_scope", "数据范围", "数据范围列表", "Y");
        dictService.createDictTypeIfNotExists("sys_yes_no", "是否", "是否列表", "Y");

        log.info("全局字典类型初始化完成");
    }

    /**
     * 初始化类型字典数据
     */
    private void initializeSystemTypeDictData() {
        log.debug("初始化System类型字典数据");

        // 初始化用户性别字典数据
        dictDataService.createDictDataIfNotExists("sys_user_gender", "0", "未知", 1, "", null, "default", "Y");
        dictDataService.createDictDataIfNotExists("sys_user_gender", "1", "男", 2, "", null, "primary", "Y");
        dictDataService.createDictDataIfNotExists("sys_user_gender", "2", "女", 3, "", null, "danger", "Y");

        // 初始化用户状态字典数据
        dictDataService.createDictDataIfNotExists("sys_user_status", "0", "正常", 1, "", null, "success", "Y");
        dictDataService.createDictDataIfNotExists("sys_user_status", "1", "停用", 2, "", null, "danger", "Y");
        dictDataService.createDictDataIfNotExists("sys_user_status", "9", "锁定", 3, "", null, "warning", "Y");

        // 初始化角色状态字典数据
        dictDataService.createDictDataIfNotExists("sys_role_status", "0", "正常", 1, "Y", null, "success", "Y");
        dictDataService.createDictDataIfNotExists("sys_role_status", "1", "停用", 2, "N", null, "danger", "Y");

        // 初始化部门状态字典数据
        dictDataService.createDictDataIfNotExists("sys_dept_status", "0", "正常", 1, "Y", null, "success", "Y");
        dictDataService.createDictDataIfNotExists("sys_dept_status", "1", "停用", 2, "N", null, "danger", "Y");

        // 初始化权限状态字典数据
        dictDataService.createDictDataIfNotExists("sys_permission_status", "0", "正常", 1, "Y", null, "success", "Y");
        dictDataService.createDictDataIfNotExists("sys_permission_status", "1", "停用", 2, "N", null, "danger", "Y");

        // 初始化配置状态字典数据
        dictDataService.createDictDataIfNotExists("sys_config_status", "0", "正常", 1, "Y", null, "success", "Y");
        dictDataService.createDictDataIfNotExists("sys_config_status", "1", "停用", 2, "N", null, "danger", "Y");

        // 初始化数据范围字典数据
        dictDataService.createDictDataIfNotExists("sys_data_scope", "1", "全部数据权限", 1, "N", null, "primary", "Y");
        dictDataService.createDictDataIfNotExists("sys_data_scope", "2", "自定数据权限", 2, "N", null, "info", "Y");
        dictDataService.createDictDataIfNotExists("sys_data_scope", "3", "部门数据权限", 3, "N", null, "warning", "Y");
        dictDataService.createDictDataIfNotExists("sys_data_scope", "4", "部门及以下数据权限", 4, "N", null, "success", "Y");
        dictDataService.createDictDataIfNotExists("sys_data_scope", "5", "仅本人数据权限", 5, "Y", null, "danger", "Y");

        // 初始化是否字典数据
        dictDataService.createDictDataIfNotExists("sys_yes_no", "Y", "是", 1, "N", null, "success", "Y");
        dictDataService.createDictDataIfNotExists("sys_yes_no", "N", "否", 2, "Y", null, "danger", "Y");

    }
}
