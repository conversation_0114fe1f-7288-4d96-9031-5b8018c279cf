package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "sys_dict_data")
@Schema(description = "字典数据")
@TableName("sys_dict_data")
public class SysDictData extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 字典排序
     */
    @Column(name = "dict_sort")
    @Schema(description = "字典排序", example = "1")
    @TableField("dict_sort")
    private Integer dictSort;

    /**
     * 字典标签
     */
    @Column(name = "dict_label", length = 100)
    @Schema(description = "字典标签", example = "男")
    @NotBlank(message = "字典标签不能为空")
    @Size(min = 1, max = 100, message = "字典标签长度必须在1-100个字符之间")
    @TableField("dict_label")
    private String dictLabel;

    /**
     * 字典键值
     */
    @Column(name = "dict_value", length = 100)
    @Schema(description = "字典键值", example = "1")
    @NotBlank(message = "字典键值不能为空")
    @Size(min = 1, max = 100, message = "字典键值长度必须在1-100个字符之间")
    @TableField("dict_value")
    private String dictValue;

    /**
     * 字典类型
     */
    @Column(name = "dict_type", length = 100)
    @Schema(description = "字典类型")
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 1, max = 100, message = "字典类型长度必须在1-100个字符之间")
    @Pattern(regexp = "^[a-z0-9_]+$", message = "字典类型只能包含小写字母、数字和下划线")
    @TableField("dict_type")
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @Column(name = "css_class", length = 100)
    @Schema(description = "样式属性")
    @Size(max = 100, message = "样式属性长度不能超过100个字符")
    @TableField("css_class")
    private String cssClass;

    /**
     * 表格回显样式
     */
    @Column(name = "list_class", length = 100)
    @Schema(description = "表格回显样式")
    @Size(max = 100, message = "表格回显样式长度不能超过100个字符")
    @TableField("list_class")
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @Column(name = "is_default", length = 1)
    @Schema(description = "是否默认", example = "N", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "是否默认值必须为Y或N")
    @TableField("is_default")
    private String isDefault;

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    /**
     * 是否为系统字典
     */
    @Column(name = "is_system_dict", length = 1)
    @Schema(description = "是否为系统字典", example = "N", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "是否为系统字典值必须为Y或N")
    @TableField("is_system_dict")
    private String isSystemDict = "N";

    @Override
    public String toString() {
        return "SysDictData{"
                + "id=" + getId()
                + ", dictSort=" + dictSort
                + ", dictLabel='" + dictLabel + '\''
                + ", dictValue='" + dictValue + '\''
                + ", dictType='" + dictType + '\''
                + ", cssClass='" + cssClass + '\''
                + ", listClass='" + listClass + '\''
                + ", isDefault='" + isDefault + '\''
                + ", status='" + status + '\''
                + ", isSystemDict='" + isSystemDict + '\''
                + ", tenantId=" + getTenantId()
                + ", remark='" + getRemark() + '\''
                + ", createBy=" + getCreateBy()
                + ", createTime=" + getCreateTime()
                + ", updateBy=" + getUpdateBy()
                + ", updateTime=" + getUpdateTime()
                + '}';
    }
}
