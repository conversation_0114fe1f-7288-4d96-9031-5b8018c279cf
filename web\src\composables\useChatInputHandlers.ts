/**
 * ChatInputArea 统一事件处理 Composable
 * 用于在不同页面中复用 ChatInputArea 的事件处理逻辑
 */

import { ref, type Ref } from 'vue'
import { useRendererStore } from '@/stores/rendererStore'

// 文件上传相关类型
export interface UploadedFile {
  name: string
  file: File
}

export interface UploadedImage {
  name: string
  url: string
  file: File
}

// 事件处理配置接口
export interface ChatInputConfig {
  // 必需的回调函数
  onSendMessage: (messageData: any) => void | Promise<void>
  
  // 可选的自定义处理函数
  onFileUpload?: (file: File) => void
  onImageUpload?: (file: File) => void
  onToolSelect?: (tool: string) => void
  onVoiceRecordingStart?: () => Promise<void>
  onVoiceRecordingStop?: () => void
  onVoiceModeToggle?: () => void
  onInputMessageUpdate?: (value: string) => void
  
  // 可选的状态引用（如果不提供，将使用内部状态）
  inputMessage?: Ref<string>
  uploadedFiles?: Ref<UploadedFile[]>
  uploadedImages?: Ref<UploadedImage[]>
  recordedAudio?: Ref<Blob | null>
  isRecording?: Ref<boolean>
  isVoiceMode?: Ref<boolean>
  recordingTime?: Ref<number>
  recordingDuration?: Ref<number>
}

export function useChatInputHandlers(config: ChatInputConfig) {
  // 使用渲染器存储
  const { handleRendererSelect } = useRendererStore()
  
  // 内部状态（如果外部没有提供）
  const internalInputMessage = ref('')
  const internalUploadedFiles = ref<UploadedFile[]>([])
  const internalUploadedImages = ref<UploadedImage[]>([])
  const internalRecordedAudio = ref<Blob | null>(null)
  const internalIsRecording = ref(false)
  const internalIsVoiceMode = ref(false)
  const internalRecordingTime = ref(0)
  const internalRecordingDuration = ref(0)
  
  // 使用外部状态或内部状态
  const inputMessage = config.inputMessage || internalInputMessage
  const uploadedFiles = config.uploadedFiles || internalUploadedFiles
  const uploadedImages = config.uploadedImages || internalUploadedImages
  const recordedAudio = config.recordedAudio || internalRecordedAudio
  const isRecording = config.isRecording || internalIsRecording
  const isVoiceMode = config.isVoiceMode || internalIsVoiceMode
  const recordingTime = config.recordingTime || internalRecordingTime
  const recordingDuration = config.recordingDuration || internalRecordingDuration
  
  // 统一的事件处理方法
  const handlers = {
    // 发送消息
    handleSendMessage: async (messageData: any) => {
      await config.onSendMessage(messageData)
    },
    
    // 文件上传处理
    handleFileUploadMenu: (files: FileList, type: 'file' | 'image' | 'id') => {
      console.log('上传文件:', type, files)
      
      if (type === 'file') {
        handlers.handleFileUpload(files[0])
      } else if (type === 'image') {
        handlers.handleImageUpload(files[0])
      } else if (type === 'id') {
        // 处理证件上传
        console.log('证件上传:', files)
      }
    },
    
    // 单个文件上传
    handleFileUpload: (file: File) => {
      if (config.onFileUpload) {
        config.onFileUpload(file)
      } else {
        // 默认处理
        uploadedFiles.value.push({
          name: file.name,
          file: file
        })
      }
    },
    
    // 图片上传
    handleImageUpload: (file: File) => {
      if (config.onImageUpload) {
        config.onImageUpload(file)
      } else {
        // 默认处理
        const reader = new FileReader()
        reader.onload = (e) => {
          uploadedImages.value.push({
            name: file.name,
            url: e.target?.result as string,
            file: file
          })
        }
        reader.readAsDataURL(file)
      }
    },
    
    // 工具选择
    handleToolSelect: (tool: string) => {
      if (config.onToolSelect) {
        config.onToolSelect(tool)
      } else {
        console.log('选择工具:', tool)
      }
    },
    
    // 渲染器选择（使用全局状态）
    handleRendererSelect: (renderer: string) => {
      handleRendererSelect(renderer as any)
    },
    
    // 语音录制切换
    handleToggleVoiceRecording: async () => {
      if (isRecording.value) {
        if (config.onVoiceRecordingStop) {
          config.onVoiceRecordingStop()
        } else {
          // 默认停止录制
          isRecording.value = false
          recordingTime.value = 0
        }
      } else {
        if (config.onVoiceRecordingStart) {
          await config.onVoiceRecordingStart()
        } else {
          // 默认开始录制
          isRecording.value = true
        }
      }
    },
    
    // 语音模式切换
    handleToggleVoiceMode: () => {
      if (config.onVoiceModeToggle) {
        config.onVoiceModeToggle()
      } else {
        isVoiceMode.value = !isVoiceMode.value
        console.log('语音模式:', isVoiceMode.value ? '开启' : '关闭')
      }
    },
    
    // 移除文件
    handleRemoveFile: (index: number) => {
      uploadedFiles.value.splice(index, 1)
    },
    
    // 移除图片
    handleRemoveImage: (index: number) => {
      uploadedImages.value.splice(index, 1)
    },
    
    // 移除录音
    handleRemoveRecordedAudio: () => {
      recordedAudio.value = null
      recordingDuration.value = 0
    },
    
    // 播放录音
    handlePlayRecordedAudio: () => {
      if (recordedAudio.value) {
        const audioUrl = URL.createObjectURL(recordedAudio.value)
        const audio = new Audio(audioUrl)
        audio.play()
      }
    },
    
    // 输入消息更新
    handleInputMessageUpdate: (value: string) => {
      if (config.onInputMessageUpdate) {
        config.onInputMessageUpdate(value)
      } else {
        inputMessage.value = value
      }
    }
  }
  
  // 返回状态和处理方法
  return {
    // 状态
    inputMessage,
    uploadedFiles,
    uploadedImages,
    recordedAudio,
    isRecording,
    isVoiceMode,
    recordingTime,
    recordingDuration,
    
    // 事件处理方法
    ...handlers,
    
    // 便捷的 props 对象（用于直接传递给 ChatInputArea）
    chatInputProps: {
      'onSend-message': handlers.handleSendMessage,
      'onFile-upload': handlers.handleFileUploadMenu,
      'onTool-select': handlers.handleToolSelect,
      'onRenderer-select': handlers.handleRendererSelect,
      'onToggle-voice-recording': handlers.handleToggleVoiceRecording,
      'onToggle-voice-mode': handlers.handleToggleVoiceMode,
      'onRemove-file': handlers.handleRemoveFile,
      'onRemove-image': handlers.handleRemoveImage,
      'onRemove-recorded-audio': handlers.handleRemoveRecordedAudio,
      'onPlay-recorded-audio': handlers.handlePlayRecordedAudio,
      'onUpdate:input-message': handlers.handleInputMessageUpdate
    }
  }
}
