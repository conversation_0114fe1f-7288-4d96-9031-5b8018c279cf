'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Settings, 
  Server, 
  Clock, 
  Key, 
  Shield, 
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  Save,
  RefreshCw
} from 'lucide-react';
import toast from 'react-hot-toast';

// 定时监听任务接口
interface MonitorTask {
  id: string;
  name: string;
  description: string;
  type: 'system' | 'component' | 'custom';
  interval: number; // 秒
  enabled: boolean;
  lastRun?: string;
  nextRun?: string;
  dataRetentionDays: number;
  createTime: string;
  updateTime: string;
}

// Agent节点信息接口
interface AgentInfo {
  id: string;
  name: string;
  version: string;
  host: string;
  port: number;
  description: string;
  tags: string[];
  updateTime: string;
}

// 访问密钥接口
interface AccessKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  ipWhitelist: string[];
  enabled: boolean;
  expiresAt?: string;
  createTime: string;
  lastUsed?: string;
}

// 设置标签页
enum SettingsTab {
  MONITOR_TASKS = 'monitor-tasks',
  AGENT_INFO = 'agent-info',
  ACCESS_KEYS = 'access-keys',
  IP_RESTRICTIONS = 'ip-restrictions'
}

const TAB_LABELS = {
  [SettingsTab.MONITOR_TASKS]: '监听任务',
  [SettingsTab.AGENT_INFO]: 'Agent信息',
  [SettingsTab.ACCESS_KEYS]: '访问密钥',
  [SettingsTab.IP_RESTRICTIONS]: 'IP限制'
};

export default function SettingsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<SettingsTab>(SettingsTab.MONITOR_TASKS);
  const [loading, setLoading] = useState(false);
  
  // 监听任务相关状态
  const [monitorTasks, setMonitorTasks] = useState<MonitorTask[]>([]);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [editingTask, setEditingTask] = useState<MonitorTask | null>(null);
  
  // Agent信息相关状态
  const [agentInfo, setAgentInfo] = useState<AgentInfo | null>(null);
  const [editingAgent, setEditingAgent] = useState(false);
  
  // 访问密钥相关状态
  const [accessKeys, setAccessKeys] = useState<AccessKey[]>([]);
  const [showKeyModal, setShowKeyModal] = useState(false);
  const [editingKey, setEditingKey] = useState<AccessKey | null>(null);

  useEffect(() => {
    loadSettingsData();
  }, [activeTab]);

  const loadSettingsData = async () => {
    setLoading(true);
    try {
      switch (activeTab) {
        case SettingsTab.MONITOR_TASKS:
          await loadMonitorTasks();
          break;
        case SettingsTab.AGENT_INFO:
          await loadAgentInfo();
          break;
        case SettingsTab.ACCESS_KEYS:
          await loadAccessKeys();
          break;
        case SettingsTab.IP_RESTRICTIONS:
          // TODO: 加载IP限制配置
          break;
      }
    } catch (error) {
      console.error('加载设置数据失败:', error);
      toast.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadMonitorTasks = async () => {
    // TODO: 调用API加载监听任务
    setMonitorTasks([
      {
        id: '1',
        name: '系统资源监控',
        description: '监控CPU、内存、磁盘使用情况',
        type: 'system',
        interval: 60,
        enabled: true,
        dataRetentionDays: 30,
        createTime: '2024-01-01T00:00:00Z',
        updateTime: '2024-01-01T00:00:00Z'
      }
    ]);
  };

  const loadAgentInfo = async () => {
    // TODO: 调用API加载Agent信息
    setAgentInfo({
      id: 'agent-001',
      name: 'YYZS Agent Node 1',
      version: '1.0.0',
      host: 'localhost',
      port: 8080,
      description: 'Elastic Stack 组件管理节点',
      tags: ['production', 'elastic'],
      updateTime: '2024-01-01T00:00:00Z'
    });
  };

  const loadAccessKeys = async () => {
    // TODO: 调用API加载访问密钥
    setAccessKeys([
      {
        id: '1',
        name: '默认API密钥',
        key: 'ak_xxxxxxxxxxxxxxxx',
        permissions: ['read', 'write'],
        ipWhitelist: ['127.0.0.1', '***********/24'],
        enabled: true,
        createTime: '2024-01-01T00:00:00Z'
      }
    ]);
  };

  // 切换监听任务状态
  const toggleTaskStatus = async (taskId: string, enabled: boolean) => {
    try {
      // TODO: 调用API切换任务状态
      setMonitorTasks(prev => 
        prev.map(task => 
          task.id === taskId ? { ...task, enabled } : task
        )
      );
      toast.success(enabled ? '任务已启用' : '任务已暂停');
    } catch (error) {
      console.error('切换任务状态失败:', error);
      toast.error('操作失败');
    }
  };

  // 删除监听任务
  const deleteTask = async (taskId: string) => {
    if (!confirm('确定要删除这个监听任务吗？')) return;
    
    try {
      // TODO: 调用API删除任务
      setMonitorTasks(prev => prev.filter(task => task.id !== taskId));
      toast.success('任务已删除');
    } catch (error) {
      console.error('删除任务失败:', error);
      toast.error('删除失败');
    }
  };

  // 保存Agent信息
  const saveAgentInfo = async () => {
    try {
      // TODO: 调用API保存Agent信息
      setEditingAgent(false);
      toast.success('Agent信息已保存');
    } catch (error) {
      console.error('保存Agent信息失败:', error);
      toast.error('保存失败');
    }
  };

  // 生成新的访问密钥
  const generateAccessKey = async () => {
    try {
      // TODO: 调用API生成新密钥
      const newKey: AccessKey = {
        id: Date.now().toString(),
        name: '新密钥',
        key: 'ak_' + Math.random().toString(36).substring(2, 18),
        permissions: ['read'],
        ipWhitelist: [],
        enabled: true,
        createTime: new Date().toISOString()
      };
      setAccessKeys(prev => [...prev, newKey]);
      toast.success('新密钥已生成');
    } catch (error) {
      console.error('生成密钥失败:', error);
      toast.error('生成失败');
    }
  };

  // 删除访问密钥
  const deleteAccessKey = async (keyId: string) => {
    if (!confirm('确定要删除这个访问密钥吗？')) return;
    
    try {
      // TODO: 调用API删除密钥
      setAccessKeys(prev => prev.filter(key => key.id !== keyId));
      toast.success('密钥已删除');
    } catch (error) {
      console.error('删除密钥失败:', error);
      toast.error('删除失败');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="btn-outline mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </button>
              <div className="flex items-center">
                <Settings className="h-8 w-8 text-primary-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
                  <p className="text-sm text-gray-500">管理Agent节点配置和监听任务</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 侧边栏导航 */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {Object.entries(TAB_LABELS).map(([tab, label]) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as SettingsTab)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  {label}
                </button>
              ))}
            </nav>
          </div>

          {/* 主内容区域 */}
          <div className="flex-1">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {TAB_LABELS[activeTab]}
                </h3>
              </div>
              <div className="card-body">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
                    <p className="text-gray-600">加载中...</p>
                  </div>
                ) : (
                  <>
                    {/* 监听任务管理 */}
                    {activeTab === SettingsTab.MONITOR_TASKS && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">管理定时监听任务和数据保存配置</p>
                          <button
                            onClick={() => setShowTaskModal(true)}
                            className="btn-primary"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            新建任务
                          </button>
                        </div>

                        <div className="space-y-4">
                          {monitorTasks.map((task) => (
                            <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium text-gray-900">{task.name}</h4>
                                  <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span>间隔: {task.interval}秒</span>
                                    <span>保留: {task.dataRetentionDays}天</span>
                                    <span>类型: {task.type}</span>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => toggleTaskStatus(task.id, !task.enabled)}
                                    className={`btn-sm ${task.enabled ? 'btn-warning' : 'btn-success'}`}
                                    title={task.enabled ? '暂停任务' : '启动任务'}
                                  >
                                    {task.enabled ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                                  </button>
                                  <button
                                    onClick={() => {
                                      setEditingTask(task);
                                      setShowTaskModal(true);
                                    }}
                                    className="btn-sm btn-secondary"
                                    title="编辑任务"
                                  >
                                    <Edit className="h-3 w-3" />
                                  </button>
                                  <button
                                    onClick={() => deleteTask(task.id)}
                                    className="btn-sm btn-error"
                                    title="删除任务"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Agent信息管理 */}
                    {activeTab === SettingsTab.AGENT_INFO && agentInfo && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">查看和修改当前Agent节点信息</p>
                          <button
                            onClick={() => setEditingAgent(!editingAgent)}
                            className="btn-outline"
                          >
                            {editingAgent ? '取消编辑' : '编辑信息'}
                          </button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="form-label">节点名称</label>
                            {editingAgent ? (
                              <input
                                type="text"
                                value={agentInfo.name}
                                onChange={(e) => setAgentInfo(prev => prev ? {...prev, name: e.target.value} : null)}
                                className="form-input"
                              />
                            ) : (
                              <p className="form-input bg-gray-50">{agentInfo.name}</p>
                            )}
                          </div>
                          <div>
                            <label className="form-label">版本</label>
                            <p className="form-input bg-gray-50">{agentInfo.version}</p>
                          </div>
                          <div>
                            <label className="form-label">主机地址</label>
                            {editingAgent ? (
                              <input
                                type="text"
                                value={agentInfo.host}
                                onChange={(e) => setAgentInfo(prev => prev ? {...prev, host: e.target.value} : null)}
                                className="form-input"
                              />
                            ) : (
                              <p className="form-input bg-gray-50">{agentInfo.host}</p>
                            )}
                          </div>
                          <div>
                            <label className="form-label">端口</label>
                            {editingAgent ? (
                              <input
                                type="number"
                                value={agentInfo.port}
                                onChange={(e) => setAgentInfo(prev => prev ? {...prev, port: parseInt(e.target.value)} : null)}
                                className="form-input"
                              />
                            ) : (
                              <p className="form-input bg-gray-50">{agentInfo.port}</p>
                            )}
                          </div>
                          <div className="md:col-span-2">
                            <label className="form-label">描述</label>
                            {editingAgent ? (
                              <textarea
                                value={agentInfo.description}
                                onChange={(e) => setAgentInfo(prev => prev ? {...prev, description: e.target.value} : null)}
                                rows={3}
                                className="form-input"
                              />
                            ) : (
                              <p className="form-input bg-gray-50">{agentInfo.description}</p>
                            )}
                          </div>
                          <div className="md:col-span-2">
                            <label className="form-label">标签</label>
                            {editingAgent ? (
                              <input
                                type="text"
                                value={agentInfo.tags.join(', ')}
                                onChange={(e) => setAgentInfo(prev => prev ? {...prev, tags: e.target.value.split(',').map(t => t.trim())} : null)}
                                placeholder="用逗号分隔多个标签"
                                className="form-input"
                              />
                            ) : (
                              <div className="flex flex-wrap gap-2">
                                {agentInfo.tags.map((tag, index) => (
                                  <span key={index} className="px-2 py-1 bg-primary-100 text-primary-700 rounded text-sm">
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        {editingAgent && (
                          <div className="flex justify-end mt-6">
                            <button
                              onClick={saveAgentInfo}
                              className="btn-primary"
                            >
                              <Save className="h-4 w-4 mr-2" />
                              保存更改
                            </button>
                          </div>
                        )}
                      </div>
                    )}

                    {/* 访问密钥管理 */}
                    {activeTab === SettingsTab.ACCESS_KEYS && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">管理API访问密钥和权限配置</p>
                          <button
                            onClick={generateAccessKey}
                            className="btn-primary"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            生成密钥
                          </button>
                        </div>

                        <div className="space-y-4">
                          {accessKeys.map((key) => (
                            <div key={key.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium text-gray-900">{key.name}</h4>
                                  <p className="text-sm text-gray-600 mt-1 font-mono">{key.key}</p>
                                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span>权限: {key.permissions.join(', ')}</span>
                                    <span>IP白名单: {key.ipWhitelist.length > 0 ? key.ipWhitelist.join(', ') : '无限制'}</span>
                                    <span className={`px-2 py-1 rounded ${key.enabled ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                                      {key.enabled ? '启用' : '禁用'}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => {
                                      setEditingKey(key);
                                      setShowKeyModal(true);
                                    }}
                                    className="btn-sm btn-secondary"
                                    title="编辑密钥"
                                  >
                                    <Edit className="h-3 w-3" />
                                  </button>
                                  <button
                                    onClick={() => deleteAccessKey(key.id)}
                                    className="btn-sm btn-error"
                                    title="删除密钥"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* IP限制管理 */}
                    {activeTab === SettingsTab.IP_RESTRICTIONS && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">配置全局IP访问限制规则</p>
                          <button className="btn-primary">
                            <Plus className="h-4 w-4 mr-2" />
                            添加规则
                          </button>
                        </div>

                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                          <div className="flex items-center">
                            <Shield className="h-5 w-5 text-yellow-600 mr-2" />
                            <p className="text-sm text-yellow-800">
                              IP限制功能正在开发中，敬请期待。
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
