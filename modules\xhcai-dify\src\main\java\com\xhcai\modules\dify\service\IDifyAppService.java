package com.xhcai.modules.dify.service;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.dto.app.DifyAppParametersResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.dto.app.DifySuggestedQuestionsResponseDTO;
import reactor.core.publisher.Mono;

/**
 * Dify 应用服务接口
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface IDifyAppService {

    /**
     * 获取已安装应用列表
     *
     * @return 已安装应用列表
     */
    Mono<Result<DifyInstalledAppsResponseDTO>> getInstalledApps();

    /**
     * 获取应用会话参数
     *
     * @param installedAppId 已安装应用ID
     * @return 应用会话参数
     */
    Mono<Result<DifyAppParametersResponseDTO>> getAppParameters(String installedAppId);

    /**
     * 获取消息建议问题
     *
     * @param installedAppId 已安装应用ID
     * @param messageId 消息ID
     * @return 建议问题列表
     */
    Mono<Result<DifySuggestedQuestionsResponseDTO>> getSuggestedQuestions(String installedAppId, String messageId);

    /**
     * 通过应用ID获取消息建议问题
     *
     * @param appId 应用ID
     * @param messageId 消息ID
     * @return 建议问题列表
     */
    Mono<Result<DifySuggestedQuestionsResponseDTO>> getSuggestedQuestionsByAppId(String appId, String messageId);
}
