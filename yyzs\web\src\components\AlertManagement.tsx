'use client';

import { useState, useEffect } from 'react';
import { 
  AlertTriangle, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Power,
  PowerOff,
  Search,
  Filter,
  Download,
  Upload,
  Settings,
  Bell,
  BellOff,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { AlertRule, AlertRecord } from '@/types/monitor';
import AlertsAPI from '@/api/alerts';
import toast from 'react-hot-toast';

type TabType = 'rules' | 'records' | 'settings';

export default function AlertManagement() {
  const [activeTab, setActiveTab] = useState<TabType>('rules');
  const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
  const [alertRecords, setAlertRecords] = useState<AlertRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRules, setSelectedRules] = useState<Set<string>>(new Set());
  const [selectedRecords, setSelectedRecords] = useState<Set<string>>(new Set());
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null);

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      if (activeTab === 'rules') {
        await loadAlertRules();
      } else if (activeTab === 'records') {
        await loadAlertRecords();
      }
    } catch (error: any) {
      console.error('加载数据失败:', error);
      toast.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAlertRules = async () => {
    const response = await AlertsAPI.getAlertRules();
    if (response.success && response.data) {
      setAlertRules(response.data);
    }
  };

  const loadAlertRecords = async () => {
    const response = await AlertsAPI.getAlertRecords();
    if (response.success && response.data) {
      setAlertRecords(response.data.records);
    }
  };

  const handleToggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      const response = await AlertsAPI.toggleAlertRule(ruleId, enabled);
      if (response.success) {
        toast.success(`告警规则已${enabled ? '启用' : '禁用'}`);
        await loadAlertRules();
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || '操作失败');
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!confirm('确定要删除这个告警规则吗？')) return;

    try {
      const response = await AlertsAPI.deleteAlertRule(ruleId);
      if (response.success) {
        toast.success('告警规则已删除');
        await loadAlertRules();
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || '删除失败');
    }
  };

  const handleAcknowledgeAlert = async (recordId: string) => {
    try {
      const response = await AlertsAPI.acknowledgeAlert(recordId);
      if (response.success) {
        toast.success('告警已确认');
        await loadAlertRecords();
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || '确认失败');
    }
  };

  const handleResolveAlert = async (recordId: string) => {
    try {
      const response = await AlertsAPI.resolveAlert(recordId);
      if (response.success) {
        toast.success('告警已解决');
        await loadAlertRecords();
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || '解决失败');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-error-600 bg-error-100';
      case 'high': return 'text-warning-600 bg-warning-100';
      case 'medium': return 'text-primary-600 bg-primary-100';
      case 'low': return 'text-secondary-600 bg-secondary-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-error-600 bg-error-100';
      case 'acknowledged': return 'text-warning-600 bg-warning-100';
      case 'resolved': return 'text-success-600 bg-success-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const tabs = [
    { id: 'rules', label: '告警规则', icon: Settings },
    { id: 'records', label: '告警记录', icon: AlertTriangle },
    { id: 'settings', label: '通知设置', icon: Bell },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">告警管理</h2>
          <p className="text-gray-600">管理告警规则和处理告警事件</p>
        </div>
        <div className="flex items-center space-x-3">
          {activeTab === 'rules' && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary"
            >
              <Plus className="h-4 w-4 mr-2" />
              创建规则
            </button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-soft">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as TabType)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Search and Filters */}
          <div className="mb-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 form-input"
                  />
                </div>
              </div>
              <button className="btn-outline">
                <Filter className="h-4 w-4 mr-2" />
                筛选
              </button>
            </div>
          </div>

          {/* Content */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            </div>
          ) : (
            <>
              {activeTab === 'rules' && (
                <AlertRulesTab
                  rules={alertRules}
                  searchTerm={searchTerm}
                  selectedRules={selectedRules}
                  onToggleRule={handleToggleRule}
                  onEditRule={setEditingRule}
                  onDeleteRule={handleDeleteRule}
                  onSelectionChange={setSelectedRules}
                />
              )}

              {activeTab === 'records' && (
                <AlertRecordsTab
                  records={alertRecords}
                  searchTerm={searchTerm}
                  selectedRecords={selectedRecords}
                  onAcknowledge={handleAcknowledgeAlert}
                  onResolve={handleResolveAlert}
                  onSelectionChange={setSelectedRecords}
                  getSeverityColor={getSeverityColor}
                  getStatusColor={getStatusColor}
                />
              )}

              {activeTab === 'settings' && (
                <NotificationSettingsTab />
              )}
            </>
          )}
        </div>
      </div>

      {/* Create/Edit Rule Modal */}
      {(showCreateModal || editingRule) && (
        <AlertRuleModal
          rule={editingRule}
          onClose={() => {
            setShowCreateModal(false);
            setEditingRule(null);
          }}
          onSave={async () => {
            await loadAlertRules();
            setShowCreateModal(false);
            setEditingRule(null);
          }}
        />
      )}
    </div>
  );
}

// Alert Rules Tab Component
interface AlertRulesTabProps {
  rules: AlertRule[];
  searchTerm: string;
  selectedRules: Set<string>;
  onToggleRule: (ruleId: string, enabled: boolean) => void;
  onEditRule: (rule: AlertRule) => void;
  onDeleteRule: (ruleId: string) => void;
  onSelectionChange: (selected: Set<string>) => void;
}

function AlertRulesTab({ 
  rules, 
  searchTerm, 
  selectedRules, 
  onToggleRule, 
  onEditRule, 
  onDeleteRule,
  onSelectionChange 
}: AlertRulesTabProps) {
  const filteredRules = rules.filter(rule =>
    rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.metric.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-error-600 bg-error-100';
      case 'high': return 'text-warning-600 bg-warning-100';
      case 'medium': return 'text-primary-600 bg-primary-100';
      case 'low': return 'text-secondary-600 bg-secondary-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (filteredRules.length === 0) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无告警规则</h3>
        <p className="text-gray-600">创建第一个告警规则来监控系统状态</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="table">
        <thead className="table-header">
          <tr>
            <th className="table-header-cell w-12">
              <input
                type="checkbox"
                className="form-checkbox"
                checked={selectedRules.size === filteredRules.length}
                onChange={(e) => {
                  if (e.target.checked) {
                    onSelectionChange(new Set(filteredRules.map(r => r.id)));
                  } else {
                    onSelectionChange(new Set());
                  }
                }}
              />
            </th>
            <th className="table-header-cell">规则名称</th>
            <th className="table-header-cell">指标</th>
            <th className="table-header-cell">条件</th>
            <th className="table-header-cell">严重程度</th>
            <th className="table-header-cell">状态</th>
            <th className="table-header-cell">更新时间</th>
            <th className="table-header-cell w-32">操作</th>
          </tr>
        </thead>
        <tbody className="table-body">
          {filteredRules.map((rule) => (
            <tr key={rule.id} className="table-row">
              <td className="table-cell">
                <input
                  type="checkbox"
                  className="form-checkbox"
                  checked={selectedRules.has(rule.id)}
                  onChange={(e) => {
                    const newSelected = new Set(selectedRules);
                    if (e.target.checked) {
                      newSelected.add(rule.id);
                    } else {
                      newSelected.delete(rule.id);
                    }
                    onSelectionChange(newSelected);
                  }}
                />
              </td>
              <td className="table-cell">
                <div>
                  <div className="font-medium text-gray-900">{rule.name}</div>
                  {rule.description && (
                    <div className="text-sm text-gray-500">{rule.description}</div>
                  )}
                </div>
              </td>
              <td className="table-cell">
                <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                  {rule.metric}
                </span>
              </td>
              <td className="table-cell">
                <span className="text-sm">
                  {rule.operator} {rule.threshold}
                </span>
              </td>
              <td className="table-cell">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                  {rule.severity}
                </span>
              </td>
              <td className="table-cell">
                <button
                  onClick={() => onToggleRule(rule.id, !rule.enabled)}
                  className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                    rule.enabled 
                      ? 'text-success-600 bg-success-100' 
                      : 'text-gray-600 bg-gray-100'
                  }`}
                >
                  {rule.enabled ? (
                    <>
                      <Power className="h-3 w-3" />
                      <span>启用</span>
                    </>
                  ) : (
                    <>
                      <PowerOff className="h-3 w-3" />
                      <span>禁用</span>
                    </>
                  )}
                </button>
              </td>
              <td className="table-cell">
                {new Date(rule.updateTime).toLocaleString()}
              </td>
              <td className="table-cell">
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => onEditRule(rule)}
                    className="p-1 text-primary-600 hover:text-primary-700"
                    title="编辑"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onDeleteRule(rule.id)}
                    className="p-1 text-error-600 hover:text-error-700"
                    title="删除"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Alert Records Tab Component
interface AlertRecordsTabProps {
  records: AlertRecord[];
  searchTerm: string;
  selectedRecords: Set<string>;
  onAcknowledge: (recordId: string) => void;
  onResolve: (recordId: string) => void;
  onSelectionChange: (selected: Set<string>) => void;
  getSeverityColor: (severity: string) => string;
  getStatusColor: (status: string) => string;
}

function AlertRecordsTab({ 
  records, 
  searchTerm, 
  selectedRecords, 
  onAcknowledge, 
  onResolve,
  onSelectionChange,
  getSeverityColor,
  getStatusColor
}: AlertRecordsTabProps) {
  const filteredRecords = records.filter(record =>
    record.ruleName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.message.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (filteredRecords.length === 0) {
    return (
      <div className="text-center py-12">
        <CheckCircle className="h-12 w-12 text-success-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无告警记录</h3>
        <p className="text-gray-600">系统运行正常，没有触发告警</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="table">
        <thead className="table-header">
          <tr>
            <th className="table-header-cell w-12">
              <input
                type="checkbox"
                className="form-checkbox"
                checked={selectedRecords.size === filteredRecords.length}
                onChange={(e) => {
                  if (e.target.checked) {
                    onSelectionChange(new Set(filteredRecords.map(r => r.id)));
                  } else {
                    onSelectionChange(new Set());
                  }
                }}
              />
            </th>
            <th className="table-header-cell">告警信息</th>
            <th className="table-header-cell">严重程度</th>
            <th className="table-header-cell">状态</th>
            <th className="table-header-cell">开始时间</th>
            <th className="table-header-cell">持续时间</th>
            <th className="table-header-cell w-32">操作</th>
          </tr>
        </thead>
        <tbody className="table-body">
          {filteredRecords.map((record) => (
            <tr key={record.id} className="table-row">
              <td className="table-cell">
                <input
                  type="checkbox"
                  className="form-checkbox"
                  checked={selectedRecords.has(record.id)}
                  onChange={(e) => {
                    const newSelected = new Set(selectedRecords);
                    if (e.target.checked) {
                      newSelected.add(record.id);
                    } else {
                      newSelected.delete(record.id);
                    }
                    onSelectionChange(newSelected);
                  }}
                />
              </td>
              <td className="table-cell">
                <div>
                  <div className="font-medium text-gray-900">{record.ruleName}</div>
                  <div className="text-sm text-gray-500">{record.message}</div>
                  {record.componentName && (
                    <div className="text-xs text-gray-400">组件: {record.componentName}</div>
                  )}
                </div>
              </td>
              <td className="table-cell">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(record.severity)}`}>
                  {record.severity}
                </span>
              </td>
              <td className="table-cell">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                  {record.status === 'active' ? '活跃' : 
                   record.status === 'acknowledged' ? '已确认' : '已解决'}
                </span>
              </td>
              <td className="table-cell">
                {new Date(record.startTime).toLocaleString()}
              </td>
              <td className="table-cell">
                {record.endTime ? (
                  <span className="text-sm">
                    {Math.round((new Date(record.endTime).getTime() - new Date(record.startTime).getTime()) / 60000)}分钟
                  </span>
                ) : (
                  <span className="text-sm text-warning-600">进行中</span>
                )}
              </td>
              <td className="table-cell">
                <div className="flex items-center space-x-1">
                  {record.status === 'active' && (
                    <>
                      <button
                        onClick={() => onAcknowledge(record.id)}
                        className="p-1 text-warning-600 hover:text-warning-700"
                        title="确认"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => onResolve(record.id)}
                        className="p-1 text-success-600 hover:text-success-700"
                        title="解决"
                      >
                        <XCircle className="h-4 w-4" />
                      </button>
                    </>
                  )}
                  {record.status === 'acknowledged' && (
                    <button
                      onClick={() => onResolve(record.id)}
                      className="p-1 text-success-600 hover:text-success-700"
                      title="解决"
                    >
                      <XCircle className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Notification Settings Tab Component
function NotificationSettingsTab() {
  return (
    <div className="space-y-6">
      <div className="text-center py-12">
        <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">通知设置</h3>
        <p className="text-gray-600">配置告警通知方式和接收人</p>
      </div>
    </div>
  );
}

// Alert Rule Modal Component (placeholder)
interface AlertRuleModalProps {
  rule: AlertRule | null;
  onClose: () => void;
  onSave: () => void;
}

function AlertRuleModal({ rule, onClose, onSave }: AlertRuleModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {rule ? '编辑告警规则' : '创建告警规则'}
          </h3>
          <p className="text-gray-600">告警规则配置功能开发中...</p>
          <div className="flex justify-end space-x-3 mt-6">
            <button onClick={onClose} className="btn-outline">
              取消
            </button>
            <button onClick={onSave} className="btn-primary">
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
