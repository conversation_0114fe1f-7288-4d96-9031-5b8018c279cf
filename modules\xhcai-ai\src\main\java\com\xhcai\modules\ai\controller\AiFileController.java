package com.xhcai.modules.ai.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresAuthentication;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.ai.dto.FileUploadResponseDTO;
import com.xhcai.modules.ai.entity.AiFileRecord;
import com.xhcai.modules.ai.mapper.AiFileRecordMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI文件管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/ai/files")
@Tag(name = "AI文件管理", description = "AI文件上传、查询、管理接口")
public class AiFileController {

    private static final Logger log = LoggerFactory.getLogger(AiFileController.class);

    @Autowired
    private AiFileRecordMapper aiFileRecordMapper;

    /**
     * 获取当前用户的文件列表
     */
    @Operation(summary = "获取文件列表", description = "获取当前用户上传的文件列表")
    @GetMapping("/list")
    @RequiresAuthentication
    public Result<PageResult<FileUploadResponseDTO>> getFileList(
            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize,
            @Parameter(description = "文件状态") @RequestParam(required = false) String status,
            @Parameter(description = "文件名关键词") @RequestParam(required = false) String filename) {
        
        try {
            String currentUserId = SecurityUtils.getCurrentUserId();
            
            // 构建查询条件
            QueryWrapper<AiFileRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("upload_user_id", currentUserId);
            queryWrapper.eq("deleted", 0);
            
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            
            if (StringUtils.hasText(filename)) {
                queryWrapper.like("original_filename", filename);
            }
            
            queryWrapper.orderByDesc("upload_time");
            
            // 分页查询
            Page<AiFileRecord> page = new Page<>(pageNum, pageSize);
            Page<AiFileRecord> resultPage = aiFileRecordMapper.selectPage(page, queryWrapper);
            
            // 转换为DTO
            List<FileUploadResponseDTO> dtoList = resultPage.getRecords().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            PageResult<FileUploadResponseDTO> pageResult = new PageResult<>();
            pageResult.setRecords(dtoList);
            pageResult.setTotal(resultPage.getTotal());
            pageResult.setSize(resultPage.getSize());
            pageResult.setCurrent(resultPage.getCurrent());
            pageResult.setPages(resultPage.getPages());
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取文件列表失败", e);
            return Result.fail("获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取文件详情
     */
    @Operation(summary = "获取文件详情", description = "根据文件ID获取文件详细信息")
    @GetMapping("/{fileId}")
    @RequiresAuthentication
    public Result<FileUploadResponseDTO> getFileById(
            @Parameter(description = "文件ID") @PathVariable String fileId) {
        
        try {
            String currentUserId = SecurityUtils.getCurrentUserId();
            
            QueryWrapper<AiFileRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", fileId);
            queryWrapper.eq("upload_user_id", currentUserId);
            queryWrapper.eq("deleted", 0);
            
            AiFileRecord fileRecord = aiFileRecordMapper.selectOne(queryWrapper);
            
            if (fileRecord == null) {
                return Result.fail("文件不存在或无权限访问");
            }
            
            FileUploadResponseDTO dto = convertToDTO(fileRecord);
            return Result.success(dto);
            
        } catch (Exception e) {
            log.error("获取文件详情失败: fileId={}", fileId, e);
            return Result.fail("获取文件详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据Dify文件ID获取文件详情
     */
    @Operation(summary = "根据Dify文件ID获取文件详情", description = "根据Dify文件ID获取文件详细信息")
    @GetMapping("/dify/{difyFileId}")
    @RequiresAuthentication
    public Result<FileUploadResponseDTO> getFileByDifyId(
            @Parameter(description = "Dify文件ID") @PathVariable String difyFileId) {
        
        try {
            AiFileRecord fileRecord = aiFileRecordMapper.selectByDifyFileId(difyFileId);
            
            if (fileRecord == null) {
                return Result.fail("文件不存在");
            }
            
            // 检查权限
            String currentUserId = SecurityUtils.getCurrentUserId();
            if (!currentUserId.equals(fileRecord.getUploadUserId())) {
                return Result.fail("无权限访问该文件");
            }
            
            FileUploadResponseDTO dto = convertToDTO(fileRecord);
            return Result.success(dto);
            
        } catch (Exception e) {
            log.error("根据Dify文件ID获取文件详情失败: difyFileId={}", difyFileId, e);
            return Result.fail("获取文件详情失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件（软删除）
     */
    @Operation(summary = "删除文件", description = "删除指定的文件（软删除）")
    @DeleteMapping("/{fileId}")
    @RequiresAuthentication
    public Result<Void> deleteFile(
            @Parameter(description = "文件ID") @PathVariable String fileId) {
        
        try {
            String currentUserId = SecurityUtils.getCurrentUserId();
            
            QueryWrapper<AiFileRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", fileId);
            queryWrapper.eq("upload_user_id", currentUserId);
            queryWrapper.eq("deleted", 0);
            
            AiFileRecord fileRecord = aiFileRecordMapper.selectOne(queryWrapper);
            
            if (fileRecord == null) {
                return Result.fail("文件不存在或无权限访问");
            }
            
            // 软删除
            fileRecord.setDeleted(1);
            fileRecord.setStatus("deleted");
            fileRecord.setUpdateTime(LocalDateTime.now());
            fileRecord.setUpdateBy(currentUserId);
            
            aiFileRecordMapper.updateById(fileRecord);
            
            log.info("文件删除成功: fileId={}, difyFileId={}", fileId, fileRecord.getDifyFileId());
            return Result.success();
            
        } catch (Exception e) {
            log.error("删除文件失败: fileId={}", fileId, e);
            return Result.fail("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 转换实体为DTO
     */
    private FileUploadResponseDTO convertToDTO(AiFileRecord fileRecord) {
        FileUploadResponseDTO dto = new FileUploadResponseDTO();
        dto.setId(fileRecord.getId());
        dto.setOriginalFilename(fileRecord.getOriginalFilename());
        dto.setFileSize(fileRecord.getFileSize());
        dto.setFileExtension(fileRecord.getFileExtension());
        dto.setMimeType(fileRecord.getMimeType());
        dto.setDifyFileId(fileRecord.getDifyFileId());
        dto.setDifyPreviewUrl(fileRecord.getDifyPreviewUrl());
        dto.setMinioBucket(fileRecord.getMinioBucket());
        dto.setMinioObjectName(fileRecord.getMinioObjectName());
        dto.setMinioUrl(fileRecord.getMinioUrl());
        dto.setStatus(fileRecord.getStatus());
        dto.setUploadTime(fileRecord.getUploadTime());
        dto.setErrorMessage(fileRecord.getErrorMessage());
        return dto;
    }
}
