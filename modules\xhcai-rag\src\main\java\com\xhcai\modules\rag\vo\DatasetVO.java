package com.xhcai.modules.rag.vo;

import java.time.LocalDateTime;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.xhcai.modules.system.vo.SysUserVO;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 知识库视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "知识库视图对象")
public class DatasetVO {

    @Schema(description = "知识库ID", example = "dataset_123")
    private String id;

    @Schema(description = "知识库名称", example = "技术文档库")
    private String name;

    @Schema(description = "描述", example = "存储技术相关文档的知识库")
    private String description;

    @Schema(description = "数据源类型", example = "upload_file")
    private String dataSourceType;

    @Schema(description = "数据源类型描述", example = "文件上传")
    private String dataSourceTypeDesc;

    @Schema(description = "模型ID", example = "model_123")
    private String modelId;

    @Schema(description = "检索配置", example = "{\"top_k\": 5, \"score_threshold\": 0.7}")
    private Map<String, Object> vectorizationConfig;

    @Schema(description = "租户ID", example = "tenant_123")
    private String tenantId;

    @Schema(description = "创建人ID", example = "user_123")
    private String createBy;

    @Schema(description = "创建人信息")
    private SysUserVO createUser;

    @Schema(description = "创建时间", example = "2023-12-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID", example = "user_456")
    private String updateBy;

    @Schema(description = "更新人信息")
    private SysUserVO updateUser;

    @Schema(description = "更新时间", example = "2023-12-01 15:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "文档数量", example = "25")
    private Long documentCount;

    @Schema(description = "分段数量", example = "150")
    private Long segmentCount;

    @Schema(description = "总字符数", example = "50000")
    private Long totalWordCount;

    @Schema(description = "总Token数", example = "12500")
    private Long totalTokens;

    @Schema(description = "处理中文档数", example = "2")
    private Long processingDocumentCount;

    @Schema(description = "已完成文档数", example = "23")
    private Long completedDocumentCount;

    @Schema(description = "错误文档数", example = "0")
    private Long errorDocumentCount;

    /**
     * 知识库图标
     */
    @Schema(description = "知识库图标", example = "📚")
    private String icon;

    /**
     * 知识库图标背景色
     */
    @Schema(description = "知识库图标背景色", example = "#3b82f6")
    private String iconBg;

    // ==================== Getters and Setters ====================
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDataSourceType() {
        return dataSourceType;
    }

    public void setDataSourceType(String dataSourceType) {
        this.dataSourceType = dataSourceType;
    }

    public String getDataSourceTypeDesc() {
        return dataSourceTypeDesc;
    }

    public void setDataSourceTypeDesc(String dataSourceTypeDesc) {
        this.dataSourceTypeDesc = dataSourceTypeDesc;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public Map<String, Object> getVectorizationConfig() {
        return vectorizationConfig;
    }

    public void setVectorizationConfig(Map<String, Object> vectorizationConfig) {
        this.vectorizationConfig = vectorizationConfig;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public SysUserVO getCreateUser() {
        return createUser;
    }

    public void setCreateUser(SysUserVO createUser) {
        this.createUser = createUser;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public SysUserVO getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(SysUserVO updateUser) {
        this.updateUser = updateUser;
    }

    public Long getDocumentCount() {
        return documentCount;
    }

    public void setDocumentCount(Long documentCount) {
        this.documentCount = documentCount;
    }

    public Long getSegmentCount() {
        return segmentCount;
    }

    public void setSegmentCount(Long segmentCount) {
        this.segmentCount = segmentCount;
    }

    public Long getTotalWordCount() {
        return totalWordCount;
    }

    public void setTotalWordCount(Long totalWordCount) {
        this.totalWordCount = totalWordCount;
    }

    public Long getTotalTokens() {
        return totalTokens;
    }

    public void setTotalTokens(Long totalTokens) {
        this.totalTokens = totalTokens;
    }

    public Long getProcessingDocumentCount() {
        return processingDocumentCount;
    }

    public void setProcessingDocumentCount(Long processingDocumentCount) {
        this.processingDocumentCount = processingDocumentCount;
    }

    public Long getCompletedDocumentCount() {
        return completedDocumentCount;
    }

    public void setCompletedDocumentCount(Long completedDocumentCount) {
        this.completedDocumentCount = completedDocumentCount;
    }

    public Long getErrorDocumentCount() {
        return errorDocumentCount;
    }

    public void setErrorDocumentCount(Long errorDocumentCount) {
        this.errorDocumentCount = errorDocumentCount;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBg() {
        return iconBg;
    }

    public void setIconBg(String iconBg) {
        this.iconBg = iconBg;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "DatasetVO{"
                + "id='" + id + '\''
                + ", name='" + name + '\''
                + ", description='" + description + '\''
                + ", dataSourceType='" + dataSourceType + '\''
                + ", modelId='" + modelId + '\''
                + ", tenantId='" + tenantId + '\''
                + ", createBy='" + createBy + '\''
                + ", createTime=" + createTime + '\''
                + ", updateTime=" + updateTime + '\''
                + ", documentCount=" + documentCount + '\''
                + ", segmentCount=" + segmentCount + '\''
                + ", totalTokens=" + totalTokens + '\''
                + ", icon=" + getIcon() + '\''
                + ", iconBg=" + getIconBg()
                + '}';
    }
}
