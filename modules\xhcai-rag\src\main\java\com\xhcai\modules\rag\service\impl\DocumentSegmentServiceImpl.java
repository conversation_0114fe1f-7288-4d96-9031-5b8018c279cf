package com.xhcai.modules.rag.service.impl;

import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;

import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.rag.dto.BatchSegmentationRequestDTO;
import com.xhcai.modules.rag.entity.inner.FileCleanSegmentConfig;
import com.xhcai.modules.rag.service.IDocumentService;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.DocumentStatusUpdateDTO;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.DocumentSegment;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.mapper.DocumentMapper;
import com.xhcai.modules.rag.mapper.DocumentSegmentMapper;
import com.xhcai.modules.rag.plugins.rabbitmq.model.RabbitMQMessage;
import com.xhcai.modules.rag.plugins.rabbitmq.producer.RabbitMQProducer;
import com.xhcai.modules.rag.service.DocumentStatusSSEService;
import com.xhcai.modules.rag.service.IDocumentSegmentService;
import com.xhcai.modules.rag.service.processor.IFileSegmentationProcessor;

/**
 * 文档分段服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class DocumentSegmentServiceImpl extends ServiceImpl<DocumentSegmentMapper, DocumentSegment> implements IDocumentSegmentService {

    private static final Logger log = LoggerFactory.getLogger(DocumentSegmentServiceImpl.class);

    @Autowired
    private DocumentMapper documentMapper;

    @Autowired
    private IDocumentService documentService;

    @Autowired
    private DocumentSegmentMapper documentSegmentMapper;

    @Autowired
    private List<IFileSegmentationProcessor> segmentationProcessors;

    @Autowired(required = false)
    private DocumentStatusSSEService documentStatusSSEService;

    @Autowired(required = false)
    private RabbitMQProducer rabbitMQProducer;

    @Override
    public IPage<DocumentSegment> pageByDocument(Long current, Long size, String documentId,
            String datasetId, String status, Boolean enabled) {
        Page<DocumentSegment> page = new Page<>(current, size);
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();

        if (documentId != null) {
            wrapper.eq(DocumentSegment::getDocumentId, documentId);
        }
        if (datasetId != null) {
            wrapper.eq(DocumentSegment::getDatasetId, datasetId);
        }
        if (status != null) {
            wrapper.eq(DocumentSegment::getStatus, status);
        }
        if (enabled != null) {
            wrapper.eq(DocumentSegment::getEnabled, enabled);
        }

        wrapper.orderByAsc(DocumentSegment::getPosition);

        return page(page, wrapper);
    }

    /**
     * 处理文档分段
     *
     * @param documentId 文档ID
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processDocumentSegmentation(String documentId) {
        return processDocumentSegmentation(documentId, null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchStartSegmentation(BatchSegmentationRequestDTO request) {
        log.info("开始批量分段处理: documentIds={}, configSize={}",
                request.getDocumentIds(), request.getDocConfigs().size());

        try {
            String tenantId = SecurityUtils.getCurrentTenantId();
            String userId = SecurityUtils.getCurrentUserId();

            // 验证文档是否存在且属于当前租户
            List<Document> documents = documentService.listByIds(request.getDocumentIds());
            if (documents.size() != request.getDocumentIds().size()) {
                throw new BusinessException("部分文档不存在或无权限访问");
            }

            // 更新文档状态为等待处理
            for (Document document : documents) {
                document.setDocumentStatus(DocumentStatus.WAITING.getCode());
                document.setProcessingStartedAt(LocalDateTime.now());

                // 更新文档元数据
                FileCleanSegmentConfig fileCleanSegmentConfig = request.getDocConfigs().get(document.getId());
                if (fileCleanSegmentConfig != null) {
                    document.setSegmentConfig(fileCleanSegmentConfig.getSegmentConfig());
                    document.setCleaningConfig(fileCleanSegmentConfig.getCleaningConfig());
                }
            }
            documentService.updateBatchById(documents);

            // 发送批量分段处理消息到RabbitMQ ，TODO 此处要改成每个文件往队列发送
            if (rabbitMQProducer != null) {
                Map<String, Object> segmentationData = new HashMap<>();
                segmentationData.put("documentIds", request.getDocumentIds());
                segmentationData.put("docConfigs", request.getDocConfigs());
                segmentationData.put("segmentationConfig", request.getSegmentationConfig());
                segmentationData.put("tenantId", tenantId);
                segmentationData.put("userId", userId);

                rabbitMQProducer.sendDocumentSegmentationMessage(segmentationData, tenantId, userId);
                log.info("批量分段处理消息发送成功: documentIds={}", request.getDocumentIds());
            } else {
                log.warn("RabbitMQ生产者未配置，无法发送批量分段处理消息");
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("批量分段处理启动失败: {}", e.getMessage(), e);
            throw new BusinessException("批量分段处理启动失败: " + e.getMessage());
        }
    }

    /**
     * 处理文档分段（带用户信息）
     *
     * @param documentId 文档ID
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processDocumentSegmentation(String documentId, String tenantId, String userId) {
        log.info("开始处理文档分段: documentId={}, tenantId={}, userId={}", documentId, tenantId, userId);

        try {
            // 1. 查询文档信息
            Document document = documentMapper.selectByDocumentId(documentId);

            // 推送开始处理状态
            documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.PROCESSING, 0, "开始分段处理...", tenantId, userId);
            if (document == null) {
                log.error("文档不存在: documentId={}", documentId);
                return false;
            }

            // 2. 更新文档状态为处理中
            documentMapper.updateDocumentStatus(documentId, DocumentStatus.SEGMENTING.getCode());
            documentMapper.updateProcessingStartedAt(documentId);

            // 推送分段中状态
            documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.SEGMENTING, 10, "正在分段处理...", tenantId, userId);

            // 3. 获取文件下载URL
            String previewUrl = document.getPreviewUrl();
            if (previewUrl == null || previewUrl.trim().isEmpty()) {
                log.error("文档预览URL为空: documentId={}", documentId);
                documentMapper.updateError(documentId, DocumentStatus.DOWNLOAD_FILE_ERROR.getCode(), "文档预览URL为空");

                // 推送错误状态
                documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.DOWNLOAD_FILE_ERROR, 0, "文档预览URL为空", tenantId, userId);
                return false;
            }

            // 4. 下载文件并进行分段处理
            documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.SEGMENTING, 30, "正在下载和解析文件...", tenantId, userId);
            List<SegmentResult> segmentResults = downloadAndProcessFile(document, previewUrl);
            if (segmentResults.isEmpty()) {
                log.warn("文档分段结果为空: documentId={}", documentId);
                documentMapper.updateError(documentId, DocumentStatus.SEGMENT_ERROR.getCode(), "文档分段结果为空");

                // 推送错误状态
                documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.SEGMENT_ERROR, 0, "文档分段结果为空", tenantId, userId);
                return false;
            }

            // 5. 删除已存在的分段
            documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.SEGMENTING, 50, "正在清理旧分段数据...", tenantId, userId);
            documentSegmentMapper.deleteByDocumentId(documentId);

            // 6. 保存分段结果
            documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.SEGMENTING, 70, "正在保存分段结果...", tenantId, userId);
            List<DocumentSegment> segments = convertToDocumentSegments(document, segmentResults);
            if (!segments.isEmpty()) {
                documentSegmentMapper.batchInsert(segments);
            }

            // 推送分段进度更新
            pushSegmentationProgress(document, segments.size(), segments.size(), tenantId, userId);

            // 7. 更新文档统计信息
            documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.SEGMENTING, 90, "正在更新统计信息...", tenantId, userId);
            int totalWordCount = segmentResults.stream().mapToInt(SegmentResult::getWordCount).sum();
            int totalTokens = segmentResults.stream().mapToInt(SegmentResult::getTokens).sum();
            documentMapper.updateProcessingProgress(documentId, DocumentStatus.SEGMENTED.getCode(), totalWordCount, totalTokens);

            // 8. 更新文档状态为分段完成
            documentMapper.updateSplittingCompletedAt(documentId);

            // 推送完成状态
            documentStatusSSEService.pushDocumentStatusUpdate(document, DocumentStatus.SEGMENTED, 100, "分段处理完成", tenantId, userId);

            log.info("文档分段处理完成: documentId={}, 分段数量={}, 总字符数={}, 总token数={}",
                    documentId, segments.size(), totalWordCount, totalTokens);
            return true;

        } catch (Exception e) {
            log.error("文档分段处理失败: documentId={}, error={}", documentId, e.getMessage(), e);
            documentMapper.updateError(documentId, DocumentStatus.SEGMENT_ERROR.getCode(), "文档分段处理失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<DocumentSegment> listByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId)
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getPosition);
        return list(wrapper);
    }

    @Override
    public List<DocumentSegment> listByDatasetId(String datasetId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId)
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getPosition);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<DocumentSegment> segments) {
        return saveBatch(segments);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVector(String segmentId, float[] vector) {
        DocumentSegment segment = getById(segmentId);
        if (segment != null) {
            segment.setVector(vector);
            segment.setUpdateTime(LocalDateTime.now());
            return updateById(segment);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String segmentId, String status, String error) {
        DocumentSegment segment = getById(segmentId);
        if (segment != null) {
            segment.setStatus(status);
            segment.setError(error);
            segment.setUpdateTime(LocalDateTime.now());

            if ("completed".equals(status)) {
                segment.setCompletedAt(LocalDateTime.now());
            } else if ("error".equals(status)) {
                segment.setStoppedAt(LocalDateTime.now());
            }

            return updateById(segment);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<String> segmentIds, String status) {
        if (segmentIds == null || segmentIds.isEmpty()) {
            return 0;
        }

        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DocumentSegment::getId, segmentIds);

        DocumentSegment updateEntity = new DocumentSegment();
        updateEntity.setStatus(status);
        updateEntity.setUpdateTime(LocalDateTime.now());

        return baseMapper.update(updateEntity, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId);
        return baseMapper.delete(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByDatasetId(String datasetId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId);
        return baseMapper.delete(wrapper);
    }

    @Override
    public Long countByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId);
        return count(wrapper);
    }

    @Override
    public Long countByDatasetId(String datasetId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId);
        return count(wrapper);
    }

    @Override
    public Long countVectorizedByDocumentId(String documentId) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDocumentId, documentId)
                .eq(DocumentSegment::getStatus, "completed")
                .isNotNull(DocumentSegment::getVector);
        return count(wrapper);
    }

    @Override
    public List<DocumentSegment> getWaitingSegments(Integer limit) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getStatus, "waiting")
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getCreateTime);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        return list(wrapper);
    }

    @Override
    public List<DocumentSegment> getProcessingSegments() {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getStatus, "processing")
                .eq(DocumentSegment::getEnabled, true)
                .orderByAsc(DocumentSegment::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<DocumentSegment> searchSimilarSegments(float[] queryVector, String datasetId,
            int topK, double threshold) {
        // TODO: 实现向量相似度搜索
        // 这里需要集成向量数据库进行相似度搜索
        log.info("搜索相似分段: datasetId={}, topK={}, threshold={}", datasetId, topK, threshold);
        return Collections.emptyList();
    }

    @Override
    public List<DocumentSegment> searchByKeyword(String keyword, String datasetId, int topK) {
        LambdaQueryWrapper<DocumentSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentSegment::getDatasetId, datasetId)
                .eq(DocumentSegment::getEnabled, true)
                .like(DocumentSegment::getContent, keyword)
                .orderByDesc(DocumentSegment::getHitCount)
                .last("LIMIT " + topK);
        return list(wrapper);
    }

    @Override
    public List<DocumentSegment> hybridSearch(float[] queryVector, String keyword, String datasetId,
            int topK, double vectorWeight, double keywordWeight) {
        // TODO: 实现混合搜索逻辑
        // 这里需要结合向量搜索和关键字搜索的结果
        log.info("混合搜索: datasetId={}, keyword={}, topK={}, vectorWeight={}, keywordWeight={}",
                datasetId, keyword, topK, vectorWeight, keywordWeight);

        // 临时实现：只返回关键字搜索结果
        return searchByKeyword(keyword, datasetId, topK);
    }

    @Override
    public Object getSegmentStats(String documentId) {
        // TODO: 实现分段统计信息
        return Collections.emptyMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resegmentDocument(String documentId, int chunkSize, int chunkOverlap) {
        // TODO: 实现文档重新分段
        log.info("重新分段文档: documentId={}, chunkSize={}, chunkOverlap={}",
                documentId, chunkSize, chunkOverlap);
        return true;
    }

    /**
     * 下载文件并进行分段处理
     *
     * @param document 文档信息
     * @param previewUrl 预览URL
     * @return 分段结果列表
     * @throws Exception 处理异常
     */
    @Override
    public List<SegmentResult> downloadAndProcessFile(Document document, String previewUrl) throws Exception {
        log.info("开始下载文件: documentId={}, url={}", document.getId(), previewUrl);

        try (InputStream inputStream = new URL(previewUrl).openStream()) {
            // 获取文件扩展名
            String fileExtension = document.getDocType();
            if (fileExtension.isEmpty()) {
                throw new Exception("无法确定文件类型");
            }

            // 查找合适的分段处理器
            IFileSegmentationProcessor processor = findProcessor(fileExtension);
            if (processor == null) {
                throw new Exception("不支持的文件类型: " + fileExtension);
            }

            log.info("使用分段处理器: {}, 文件类型: {}", processor.getProcessorName(), fileExtension);

            KnowledgeSegmentConfig knowledgeSegmentConfig = new KnowledgeSegmentConfig();
            knowledgeSegmentConfig.setCleaningConfig(document.getCleaningConfig());
            knowledgeSegmentConfig.setSegmentConfig(document.getSegmentConfig());
            // 执行分段处理
            return processor.processSegmentation(document, inputStream, knowledgeSegmentConfig);
        } catch (Exception e) {
            log.error("下载文件或分段处理失败: documentId={}, url={}, error={}",
                    document.getId(), previewUrl, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查找合适的分段处理器
     *
     * @param fileExtension 文件扩展名
     * @return 分段处理器
     */
    private IFileSegmentationProcessor findProcessor(String fileExtension) {
        return segmentationProcessors.stream()
                .filter(processor -> processor.supports(fileExtension))
                .min(Comparator.comparingInt(IFileSegmentationProcessor::getPriority))
                .orElse(null);
    }

    /**
     * 转换为文档分段实体
     *
     * @param document 文档信息
     * @param segmentResults 分段结果列表
     * @return 文档分段实体列表
     */
    private List<DocumentSegment> convertToDocumentSegments(Document document, List<SegmentResult> segmentResults) {
        List<DocumentSegment> segments = new ArrayList<>();

        for (SegmentResult result : segmentResults) {
            DocumentSegment segment = new DocumentSegment();
            segment.setId(UUID.randomUUID().toString().replace("-", ""));
            segment.setDatasetId(document.getDatasetId());
            segment.setDocumentId(document.getId());
            segment.setPosition(result.getPosition());
            segment.setContent(result.getContent());
            segment.setWordCount(result.getWordCount());
            segment.setTokens(result.getTokens());
            segment.setKeywords(result.getKeywords());
            segment.setHitCount(0);  // 设置命中次数默认值
            segment.setEnabled(true);
            segment.setStatus(DocumentStatus.INDEX_WAITING.getCode());
            segment.setDocType(document.getDocType());
            segment.setDocLanguage(document.getDocLanguage());

            // 设置租户信息
            segment.setTenantId(document.getTenantId());
            segment.setCreateBy(document.getCreateBy());
            segment.setCreateTime(LocalDateTime.now());
            segment.setUpdateTime(LocalDateTime.now());
            segment.setDeleted(0);

            segments.add(segment);
        }

        return segments;
    }

    /**
     * 推送分段进度更新（带用户信息）
     *
     * @param document 文档对象
     * @param segmentCount 总分段数
     * @param processedCount 已处理分段数
     * @param tenantId 租户ID（可选，为null时尝试从SecurityUtils获取）
     * @param userId 用户ID（可选，为null时尝试从SecurityUtils获取）
     */
    private void pushSegmentationProgress(Document document, Integer segmentCount, Integer processedCount,
            String tenantId, String userId) {
        if (document == null) {
            return;
        }

        try {
            // 如果没有传入用户信息，尝试从SecurityUtils获取
            if (tenantId == null || userId == null) {
                try {
                    tenantId = tenantId != null ? tenantId : SecurityUtils.getCurrentTenantId();
                    userId = userId != null ? userId : SecurityUtils.getCurrentUserId();
                } catch (Exception e) {
                    // 如果无法获取用户信息，使用文档的创建者信息
                    log.warn("无法获取当前用户信息，使用文档创建者信息: {}", e.getMessage());
                    tenantId = document.getTenantId();
                    userId = document.getCreateBy();
                }
            }

            // 创建分段进度更新DTO
            DocumentStatusUpdateDTO statusUpdate = DocumentStatusUpdateDTO.createSegmentingUpdate(
                    document.getId(), document.getName(), segmentCount, processedCount, tenantId, userId);

            // 通过SSE推送状态更新
            if (documentStatusSSEService != null) {
                documentStatusSSEService.pushDocumentStatusUpdate(statusUpdate);
            }

            log.debug("推送分段进度更新: documentId={}, segmentCount={}, processedCount={}",
                    document.getId(), segmentCount, processedCount);

        } catch (Exception e) {
            log.error("推送分段进度更新失败: documentId={}, error={}",
                    document.getId(), e.getMessage(), e);
        }
    }
}
