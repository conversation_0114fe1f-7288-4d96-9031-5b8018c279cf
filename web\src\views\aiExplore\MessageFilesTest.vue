<template>
  <div class="message-files-test p-6">
    <h2 class="text-2xl font-bold mb-4">Message Files 显示测试</h2>
    
    <div class="mb-4">
      <el-button @click="addTestMessage" type="primary">添加测试消息</el-button>
      <el-button @click="clearMessages" type="danger">清空消息</el-button>
      <el-button @click="logMessages" type="info">打印消息数据</el-button>
    </div>

    <!-- 调试信息 -->
    <div class="mb-4 p-4 bg-gray-100 rounded">
      <h3 class="text-lg font-semibold mb-2">调试信息</h3>
      <p>消息数量: {{ testMessages.length }}</p>
      <p>包含message_files的消息: {{ messagesWithFiles }}</p>
    </div>

    <!-- 消息列表 -->
    <div class="messages-container">
      <MessageList
        :messages="testMessages"
        :get-message-data="getMessageData"
        @link-click="handleLinkClick"
        @image-click="handleImageClick"
        @file-download="handleFileDownload"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import MessageList from './MessageList.vue'
import type { Message } from '@/stores/conversationStore'

// 测试消息数据
const testMessages = ref<Message[]>([])

// 计算属性
const messagesWithFiles = computed(() => {
  return testMessages.value.filter(msg => msg.message_files && msg.message_files.length > 0).length
})

// 添加测试消息
const addTestMessage = () => {
  const testMessage: Message = {
    id: `test-${Date.now()}`,
    role: 'user',
    content: '请分析这个文件的内容',
    contentType: 'text',
    timestamp: new Date(),
    message_files: [
      {
        id: "fc6fb521-9416-4d23-a660-75ae19277c16",
        filename: "ubt命令.txt",
        type: "document",
        url: "http://**************/files/b3a3abaf-f66e-4580-8d9c-3db942c23a98/file-preview?timestamp=1755590569&nonce=fa9aae18e988fbd71c4d5105a4624df7&sign=ZofEMIfF6WMZBg9KiNbOaSEbJSiwhMi2Jjt4Mb8Zu24=",
        size: 399,
        mime_type: "text/plain",
        transfer_method: "local_file",
        belongs_to: "user",
        upload_file_id: "b3a3abaf-f66e-4580-8d9c-3db942c23a98"
      },
      {
        id: "test-image-1",
        filename: "示例图片.jpg",
        type: "image",
        url: "https://via.placeholder.com/300x200/4CAF50/white?text=Test+Image",
        size: 51299,
        mime_type: "image/jpeg",
        transfer_method: "remote_url",
        belongs_to: "user",
        upload_file_id: "test-image-upload-1"
      }
    ]
  }

  testMessages.value.push(testMessage)

  // 添加AI回复
  const aiMessage: Message = {
    id: `ai-${Date.now()}`,
    role: 'assistant',
    content: '我已经分析了您提供的文件。这是一个包含Ubuntu命令的文本文件，文件大小为399字节。',
    contentType: 'markdown',
    timestamp: new Date()
  }

  testMessages.value.push(aiMessage)
}

// 清空消息
const clearMessages = () => {
  testMessages.value = []
}

// 打印消息数据用于调试
const logMessages = () => {
  console.log('=== 测试消息数据 ===')
  testMessages.value.forEach((msg, index) => {
    console.log(`消息 ${index + 1}:`, {
      id: msg.id,
      role: msg.role,
      content: msg.content,
      message_files: msg.message_files,
      hasMessageFiles: !!(msg.message_files && msg.message_files.length > 0)
    })
  })
  console.log('=== 结束 ===')
}

// 获取消息数据
const getMessageData = (message: Message) => {
  return {
    images: message.images,
    audioContent: message.audioContent,
    videoContent: message.videoContent,
    files: message.files,
    chartData: message.chartData,
    chartType: message.chartType,
    flowchartData: message.flowchartData
  }
}

// 事件处理
const handleLinkClick = (url: string) => {
  console.log('Link clicked:', url)
}

const handleImageClick = (imageUrl: string) => {
  console.log('Image clicked:', imageUrl)
}

const handleFileDownload = (file: any) => {
  console.log('File download:', file)
  // 测试页面的下载逻辑会在MessageList组件中处理
}
</script>

<style scoped>
.message-files-test {
  max-width: 800px;
  margin: 0 auto;
}

.messages-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  min-height: 400px;
  background: #f9f9f9;
}
</style>
