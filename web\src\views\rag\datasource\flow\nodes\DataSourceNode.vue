<template>
  <BaseNode
    :type="data.type"
    :label="data.label"
    :icon="nodeConfig.icon"
    :color="nodeConfig.color"
    :gradient="nodeConfig.gradient"
    :label-color="nodeConfig.labelColor"
    :icon-color="nodeConfig.iconColor"
    :selected="selected"
    :status="data.status"
    :error-message="data.errorMessage"
    :source-handles="nodeConfig.handles.source"
    :target-handles="nodeConfig.handles.target"
    @configure="handleConfigure"
    @delete="handleDelete"
  >
    <!-- 数据源特定内容 -->
    <div class="datasource-content">
      <div class="config-summary">
        <div v-if="data.config.host" class="config-item">
          <i class="fas fa-server"></i>
          <span>{{ data.config.host }}:{{ data.config.port || getDefaultPort() }}</span>
        </div>
        <div v-if="data.config.database" class="config-item">
          <i class="fas fa-database"></i>
          <span>{{ data.config.database }}</span>
        </div>
        <div v-if="data.config.bucket" class="config-item">
          <i class="fas fa-folder"></i>
          <span>{{ data.config.bucket }}</span>
        </div>
        <div v-if="data.config.index" class="config-item">
          <i class="fas fa-search"></i>
          <span>{{ data.config.index }}</span>
        </div>
      </div>
      
      <div v-if="data.stats" class="stats">
        <div class="stat-item">
          <span class="stat-label">记录数</span>
          <span class="stat-value">{{ formatNumber(data.stats.recordCount) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">大小</span>
          <span class="stat-value">{{ formatSize(data.stats.size) }}</span>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseNode from './BaseNode.vue'
import { NODE_LIBRARY_CONFIG } from '../config/nodeLibrary'
import type { NodeProps } from '@vue-flow/core'

// Props
interface DataSourceNodeData {
  type: string
  label: string
  config: Record<string, any>
  status?: 'idle' | 'running' | 'success' | 'error'
  errorMessage?: string
  stats?: {
    recordCount: number
    size: number
  }
}

const props = defineProps<NodeProps<DataSourceNodeData>>()

// Emits
const emit = defineEmits<{
  'configure': [nodeId: string]
  'delete': [nodeId: string]
}>()

// 计算属性
const nodeConfig = computed(() => {
  for (const category of NODE_LIBRARY_CONFIG.categories) {
    const node = category.nodes.find(n => n.type === props.data.type)
    if (node) return node
  }
  // 默认配置
  return {
    icon: 'fas fa-database',
    color: '#64748b',
    gradient: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
    labelColor: '#1e293b',
    iconColor: '#64748b',
    handles: {
      source: [{ id: 'output', position: 'right' }],
      target: []
    }
  }
})

// 方法
const handleConfigure = () => {
  emit('configure', props.id)
}

const handleDelete = () => {
  emit('delete', props.id)
}

const getDefaultPort = () => {
  switch (props.data.type) {
    case 'mysql':
      return 3306
    case 'ftp':
      return 21
    case 'elasticsearch':
      return 9200
    default:
      return 80
  }
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatSize = (bytes: number) => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return size.toFixed(1) + units[unitIndex]
}
</script>

<style scoped>
.datasource-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-summary {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.config-item i {
  width: 14px;
  text-align: center;
}

.stats {
  display: flex;
  gap: 16px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}
</style>
