package com.xhcai.modules.agent.dto;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 智能体工作流历史记录创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体工作流历史记录创建DTO")
public class AgentWorkflowHistoryCreateDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "workflow_001")
    @NotBlank(message = "工作流ID不能为空")
    @Size(max = 36, message = "工作流ID长度不能超过36个字符")
    private String workflowId;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 工作流名称（快照）
     */
    @Schema(description = "工作流名称", example = "客服工作流")
    @Size(max = 100, message = "工作流名称长度不能超过100个字符")
    private String name;

    /**
     * 工作流描述（快照）
     */
    @Schema(description = "工作流描述", example = "智能客服的工作流程配置")
    @Size(max = 500, message = "工作流描述长度不能超过500个字符")
    private String description;

    /**
     * 版本号（快照）
     */
    @Schema(description = "版本号", example = "1")
    private Integer version;

    /**
     * Vue Flow节点数据快照（JSON格式）
     */
    @Schema(description = "Vue Flow节点数据快照", example = "[{\"id\":\"node1\",\"type\":\"start\",\"position\":{\"x\":100,\"y\":100},\"data\":{\"label\":\"开始\"}}]")
    private String nodesData;

    /**
     * Vue Flow边数据快照（JSON格式）
     */
    @Schema(description = "Vue Flow边数据快照", example = "[{\"id\":\"edge1\",\"source\":\"node1\",\"target\":\"node2\",\"type\":\"default\"}]")
    private String edgesData;

    /**
     * 视口配置快照（JSON格式）
     */
    @Schema(description = "视口配置快照", example = "{\"x\":0,\"y\":0,\"zoom\":1}")
    private String viewportConfig;

    /**
     * 全局变量配置（JSON格式快照）
     */
    @Schema(description = "全局变量配置", example = "{\"var1\":\"value1\",\"var2\":\"value2\"}")
    private String globalVariables;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "node_add")
    @NotBlank(message = "操作类型不能为空")
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    private String operationType;

    /**
     * 操作描述
     */
    @Schema(description = "操作描述", example = "添加了开始节点")
    @Size(max = 500, message = "操作描述长度不能超过500个字符")
    private String operationDesc;

    /**
     * 变更摘要（记录主要变更内容）
     */
    @Schema(description = "变更摘要", example = "新增2个节点，修改1个连接")
    @Size(max = 1000, message = "变更摘要长度不能超过1000个字符")
    private String changeSummary;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @NotNull(message = "操作时间不能为空")
    private LocalDateTime operationTime;

    /**
     * 操作用户ID
     */
    @Schema(description = "操作用户ID", example = "user_001")
    @Size(max = 36, message = "操作用户ID长度不能超过36个字符")
    private String operationUserId;

    /**
     * 操作用户名称
     */
    @Schema(description = "操作用户名称", example = "张三")
    @Size(max = 100, message = "操作用户名称长度不能超过100个字符")
    private String operationUserName;

    /**
     * 是否为重要变更
     */
    @Schema(description = "是否为重要变更", example = "true")
    private Boolean isMajorChange;

    // Getters and Setters
    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getNodesData() {
        return nodesData;
    }

    public void setNodesData(String nodesData) {
        this.nodesData = nodesData;
    }

    public String getEdgesData() {
        return edgesData;
    }

    public void setEdgesData(String edgesData) {
        this.edgesData = edgesData;
    }

    public String getViewportConfig() {
        return viewportConfig;
    }

    public void setViewportConfig(String viewportConfig) {
        this.viewportConfig = viewportConfig;
    }

    public String getGlobalVariables() {
        return globalVariables;
    }

    public void setGlobalVariables(String globalVariables) {
        this.globalVariables = globalVariables;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public String getChangeSummary() {
        return changeSummary;
    }

    public void setChangeSummary(String changeSummary) {
        this.changeSummary = changeSummary;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationUserId() {
        return operationUserId;
    }

    public void setOperationUserId(String operationUserId) {
        this.operationUserId = operationUserId;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public Boolean getIsMajorChange() {
        return isMajorChange;
    }

    public void setIsMajorChange(Boolean isMajorChange) {
        this.isMajorChange = isMajorChange;
    }
}
