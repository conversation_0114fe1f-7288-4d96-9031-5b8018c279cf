package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 系统配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_config")
@Schema(description = "系统配置")
@TableName("sys_config")
public class SysConfig extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 配置名称
     */
    @Column(name = "config_name", length = 100, nullable = false)
    @Schema(description = "配置名称", example = "系统名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "配置名称不能为空")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    @TableField("config_name")
    private String configName;

    /**
     * 配置键
     */
    @Column(name = "config_key", length = 100, nullable = false, unique = true)
    @Schema(description = "配置键", example = "sys.system.name", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100个字符")
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @Column(name = "config_value", length = 500)
    @Schema(description = "配置值", example = "XHCAI智能平台")
    @Size(max = 500, message = "配置值长度不能超过500个字符")
    @TableField("config_value")
    private String configValue;

    /**
     * 配置类型
     */
    @Column(name = "config_type", length = 1)
    @Schema(description = "配置类型", example = "Y", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "配置类型必须为Y或N")
    @TableField("config_type")
    private String configType;

    /**
     * 配置分组
     */
    @Column(name = "config_group", length = 50)
    @Schema(description = "配置分组", example = "系统配置")
    @Size(max = 50, message = "配置分组长度不能超过50个字符")
    @TableField("config_group")
    private String configGroup;

    /**
     * 配置描述
     */
    @Column(name = "config_desc", length = 200)
    @Schema(description = "配置描述", example = "系统名称配置")
    @Size(max = 200, message = "配置描述长度不能超过200个字符")
    @TableField("config_desc")
    private String configDesc;

    /**
     * 是否系统内置
     */
    @Column(name = "is_system")
    @Schema(description = "是否系统内置")
    @TableField("is_system")
    private Boolean isSystem;

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    /**
     * 排序号
     */
    @Column(name = "sort_order")
    @Schema(description = "排序号", example = "1")
    @TableField("sort_order")
    private Integer sortOrder;

    // 租户ID字段由BaseEntity提供
    // Getters and Setters
    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getConfigGroup() {
        return configGroup;
    }

    public void setConfigGroup(String configGroup) {
        this.configGroup = configGroup;
    }

    public String getConfigDesc() {
        return configDesc;
    }

    public void setConfigDesc(String configDesc) {
        this.configDesc = configDesc;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // getTenantId和setTenantId方法由BaseEntity提供
    @Override
    public String toString() {
        return "SysConfig{"
                + "configName='" + configName + '\''
                + ", configKey='" + configKey + '\''
                + ", configValue='" + configValue + '\''
                + ", configType='" + configType + '\''
                + ", configGroup='" + configGroup + '\''
                + ", configDesc='" + configDesc + '\''
                + ", isSystem=" + isSystem
                + ", status='" + status + '\''
                + ", sortOrder=" + sortOrder
                + ", " + super.toString()
                + '}';
    }
}
