-- 为第三方智能体账号表添加密码字段
-- 调整第三方智能体用户密码字段为pwd，密码与系统用户密码一致的加密方式

-- 检查并添加pwd字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE third_platform_account ADD COLUMN pwd VARCHAR(100) COMMENT "密码（BCrypt加密存储）"',
        'SELECT "pwd字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'third_platform_account' 
        AND COLUMN_NAME = 'pwd'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修改api_key字段为可空（因为现在有了pwd字段作为主要认证方式）
SET @sql = (
    SELECT IF(
        COUNT(*) > 0 AND IS_NULLABLE = 'NO',
        'ALTER TABLE third_platform_account MODIFY COLUMN api_key VARCHAR(500) NULL COMMENT "API密钥（可选）"',
        'SELECT "api_key字段已经是可空或不存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'third_platform_account' 
        AND COLUMN_NAME = 'api_key'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为pwd字段添加索引（提高查询性能）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'CREATE INDEX idx_third_platform_account_pwd ON third_platform_account(pwd)',
        'SELECT "pwd字段索引已存在" as message'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'third_platform_account' 
        AND INDEX_NAME = 'idx_third_platform_account_pwd'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构变更结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'third_platform_account' 
    AND TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME IN ('api_key', 'pwd')
ORDER BY ORDINAL_POSITION;

-- 显示相关索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'third_platform_account' 
    AND TABLE_SCHEMA = DATABASE()
    AND INDEX_NAME LIKE '%pwd%'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;
