package com.xhcai.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.LoginDTO;
import com.xhcai.modules.system.service.IAuthService;
import com.xhcai.modules.system.service.ISysLoginLogService;
import com.xhcai.modules.system.service.ISysMenuService;
import com.xhcai.modules.system.vo.LoginVO;
import com.xhcai.modules.system.vo.SysPermissionVO;
import com.xhcai.modules.system.vo.SysUserVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "用户认证", description = "用户认证相关接口")
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private IAuthService authService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysLoginLogService loginLogService;

    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "用户登录获取访问令牌")
    @PostMapping("/login")
    public Result<LoginVO> login(@Valid @RequestBody LoginDTO loginDTO, HttpServletRequest request) {
        try {
            LoginVO loginVO = authService.login(loginDTO);
            // 记录登录成功日志
            loginLogService.recordLoginSuccess(loginDTO.getUsername(), request);
            return Result.success(loginVO);
        } catch (Exception e) {
            // 记录登录失败日志
            loginLogService.recordLoginFailure(loginDTO.getUsername(), e.getMessage(), request);
            throw e;
        }
    }

    /**
     * 用户登出
     */
    @Operation(summary = "用户登出", description = "用户登出清除令牌")
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        boolean success = authService.logout(token);
        return success ? Result.success() : Result.fail("登出失败");
    }

    /**
     * 刷新令牌
     */
    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    @PostMapping("/refresh")
    public Result<LoginVO> refreshToken(
            @Parameter(description = "刷新令牌", required = true) @RequestParam String refreshToken) {
        LoginVO loginVO = authService.refreshToken(refreshToken);
        return Result.success(loginVO);
    }

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/userinfo")
    public Result<LoginVO.UserInfo> getUserInfo() {
        LoginVO.UserInfo userInfo = authService.getCurrentUserInfo();
        return Result.success(userInfo);
    }

    /**
     * 获取当前用户菜单
     */
    @Operation(summary = "获取用户菜单", description = "获取当前登录用户的菜单树")
    @GetMapping("/menus")
    public Result<List<SysPermissionVO>> getUserMenus() {
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setId(SecurityUtils.getCurrentUserId());
        sysUserVO.setTenantId(SecurityUtils.getCurrentTenantId());
        List<SysPermissionVO> menus = menuService.selectMenuTreeByUserId(sysUserVO);
        return Result.success(menus);
    }

    /**
     * 获取当前用户权限
     */
    @Operation(summary = "获取用户权限", description = "获取当前登录用户的权限列表")
    @GetMapping("/permissions")
    public Result<Object> getUserPermissions() {
        // 从SecurityContext中获取权限信息
        return Result.success(SecurityUtils.getCurrentUser().getPermissions());
    }

    /**
     * 获取当前用户角色
     */
    @Operation(summary = "获取用户角色", description = "获取当前登录用户的角色列表")
    @GetMapping("/roles")
    public Result<Object> getUserRoles() {
        // 从SecurityContext中获取角色信息
        return Result.success(SecurityUtils.getCurrentUser().getRoles());
    }

    /**
     * 验证令牌
     */
    @Operation(summary = "验证令牌", description = "验证访问令牌是否有效")
    @PostMapping("/validate")
    public Result<Boolean> validateToken(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        if (!StringUtils.hasText(token)) {
            return Result.success(false);
        }
        boolean valid = authService.validateToken(token);
        return Result.success(valid);
    }

    /**
     * 生成验证码
     */
    @Operation(summary = "生成验证码", description = "生成登录验证码")
    @GetMapping("/captcha")
    public Result<Object> generateCaptcha() {
        Object captcha = authService.generateCaptcha();
        return Result.success(captcha);
    }

    /**
     * 检查登录状态
     */
    @Operation(summary = "检查登录状态", description = "检查用户是否已登录")
    @GetMapping("/status")
    public Result<Boolean> checkLoginStatus() {
        boolean authenticated = SecurityUtils.isAuthenticated();
        return Result.success(authenticated);
    }

    /**
     * 修改密码
     */
    @Operation(summary = "修改密码", description = "修改当前用户密码")
    @PostMapping("/change-password")
    public Result<Void> changePassword(
            @Parameter(description = "旧密码", required = true) @RequestParam String oldPassword,
            @Parameter(description = "新密码", required = true) @RequestParam String newPassword) {
        // 这里应该调用用户服务的修改密码方法
        // 暂时返回成功
        return Result.success();
    }

    /**
     * 忘记密码
     */
    @Operation(summary = "忘记密码", description = "通过邮箱或手机号重置密码")
    @PostMapping("/forgot-password")
    public Result<Void> forgotPassword(
            @Parameter(description = "邮箱或手机号", required = true) @RequestParam("account") String account) {
        // 这里应该发送重置密码邮件或短信
        // 暂时返回成功
        return Result.success();
    }

    /**
     * 重置密码
     */
    @Operation(summary = "重置密码", description = "使用重置码重置密码")
    @PostMapping("/reset-password")
    public Result<Void> resetPassword(
            @Parameter(description = "重置码", required = true) @RequestParam("resetCode") String resetCode,
            @Parameter(description = "新密码", required = true) @RequestParam("newPassword") String newPassword) {
        // 这里应该验证重置码并重置密码
        // 暂时返回成功
        return Result.success();
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
