package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.rag.entity.DatasetPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 知识库权限Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface DatasetPermissionMapper extends BaseMapper<DatasetPermission> {

    /**
     * 根据知识库ID查询权限列表
     *
     * @param datasetId 知识库ID
     * @return 权限列表
     */
    @Select("SELECT * FROM dataset_permission WHERE dataset_id = #{datasetId}")
    List<DatasetPermission> selectByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 根据对象查询权限列表
     *
     * @param objectType 对象类型
     * @param objectId 对象ID
     * @return 权限列表
     */
    @Select("SELECT * FROM dataset_permission WHERE object_type = #{objectType} AND object_id = #{objectId}")
    List<DatasetPermission> selectByObject(@Param("objectType") String objectType,
                                          @Param("objectId") String objectId);

    /**
     * 查询用户对知识库的权限
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @return 权限列表
     */
    @Select("<script>" +
            "SELECT * FROM dataset_permission " +
            "WHERE dataset_id = #{datasetId} " +
            "AND (" +
            "  (object_type = 'account' AND object_id = #{userId}) " +
            "  OR (object_type = 'dept' AND object_id = #{deptId}) " +
            "  <if test='roleIds != null and roleIds.size() > 0'>" +
            "    OR (object_type = 'rule' AND object_id IN " +
            "    <foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>" +
            "      #{roleId}" +
            "    </foreach>)" +
            "  </if>" +
            ")" +
            "</script>")
    List<DatasetPermission> selectUserPermissions(@Param("datasetId") String datasetId,
                                                 @Param("userId") String userId,
                                                 @Param("deptId") String deptId,
                                                 @Param("roleIds") List<String> roleIds);

    /**
     * 检查用户是否有知识库权限
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @param permissionType 权限类型
     * @return 是否有权限
     */
    @Select("<script>" +
            "SELECT COUNT(*) > 0 FROM dataset_permission " +
            "WHERE dataset_id = #{datasetId} " +
            "<if test='permissionType != null and permissionType != \"\"'>" +
            "  AND permission_type >= #{permissionType} " +
            "</if>" +
            "AND (" +
            "  (object_type = 'account' AND object_id = #{userId}) " +
            "  OR (object_type = 'dept' AND object_id = #{deptId}) " +
            "  <if test='roleIds != null and roleIds.size() > 0'>" +
            "    OR (object_type = 'rule' AND object_id IN " +
            "    <foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>" +
            "      #{roleId}" +
            "    </foreach>)" +
            "  </if>" +
            ")" +
            "</script>")
    boolean hasPermission(@Param("datasetId") String datasetId,
                         @Param("userId") String userId,
                         @Param("deptId") String deptId,
                         @Param("roleIds") List<String> roleIds,
                         @Param("permissionType") String permissionType);

    /**
     * 根据知识库ID和对象删除权限
     *
     * @param datasetId 知识库ID
     * @param objectType 对象类型
     * @param objectId 对象ID
     * @return 删除数量
     */
    @Delete("DELETE FROM dataset_permission WHERE dataset_id = #{datasetId} AND object_type = #{objectType} AND object_id = #{objectId}")
    int deleteByDatasetAndObject(@Param("datasetId") String datasetId,
                                @Param("objectType") String objectType,
                                @Param("objectId") String objectId);

    /**
     * 根据知识库ID删除所有权限
     *
     * @param datasetId 知识库ID
     * @return 删除数量
     */
    @Delete("DELETE FROM dataset_permission WHERE dataset_id = #{datasetId}")
    int deleteByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 根据对象删除权限
     *
     * @param objectType 对象类型
     * @param objectId 对象ID
     * @return 删除数量
     */
    @Delete("DELETE FROM dataset_permission WHERE object_type = #{objectType} AND object_id = #{objectId}")
    int deleteByObject(@Param("objectType") String objectType,
                      @Param("objectId") String objectId);

    /**
     * 统计知识库的权限数量
     *
     * @param datasetId 知识库ID
     * @return 权限数量
     */
    @Select("SELECT COUNT(*) FROM dataset_permission WHERE dataset_id = #{datasetId}")
    Long countByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 统计对象的权限数量
     *
     * @param objectType 对象类型
     * @param objectId 对象ID
     * @return 权限数量
     */
    @Select("SELECT COUNT(*) FROM dataset_permission WHERE object_type = #{objectType} AND object_id = #{objectId}")
    Long countByObject(@Param("objectType") String objectType,
                      @Param("objectId") String objectId);

    /**
     * 查询用户有权限的知识库ID列表
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @return 知识库ID列表
     */
    @Select("<script>" +
            "SELECT DISTINCT dataset_id FROM dataset_permission " +
            "WHERE " +
            "  (object_type = 'account' AND object_id = #{userId}) " +
            "  OR (object_type = 'dept' AND object_id = #{deptId}) " +
            "  <if test='roleIds != null and roleIds.size() > 0'>" +
            "    OR (object_type = 'rule' AND object_id IN " +
            "    <foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>" +
            "      #{roleId}" +
            "    </foreach>)" +
            "  </if>" +
            "</script>")
    List<String> selectDatasetIdsByUser(@Param("userId") String userId,
                                       @Param("deptId") String deptId,
                                       @Param("roleIds") List<String> roleIds);
}
