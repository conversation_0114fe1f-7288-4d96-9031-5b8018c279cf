<template>
  <div class="node-library">
    <div class="library-header">
      <h3>节点库</h3>
      <button class="collapse-btn" @click="$emit('toggle')">
        <i class="fas fa-chevron-left"></i>
      </button>
    </div>
    
    <div class="library-content">
      <!-- 搜索框 -->
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索节点..."
          class="search-input"
        >
      </div>

      <!-- 节点分类 -->
      <div class="node-categories">
        <div
          v-for="category in filteredCategories"
          :key="category.name"
          class="category"
        >
          <div class="category-header" @click="toggleCategory(category.name)">
            <i :class="category.icon"></i>
            <span>{{ category.name }}</span>
            <i
              class="fas fa-chevron-down toggle-icon"
              :class="{ expanded: expandedCategories.includes(category.name) }"
            ></i>
          </div>
          
          <div
            v-show="expandedCategories.includes(category.name)"
            class="category-nodes"
          >
            <div
              v-for="node in category.nodes"
              :key="node.type"
              class="node-item"
              :draggable="true"
              @dragstart="onDragStart($event, node)"
            >
              <div class="node-icon" :style="{ background: node.gradient }">
                <i :class="node.icon" :style="{ color: node.iconColor }"></i>
              </div>
              <div class="node-info">
                <div class="node-label">{{ node.label }}</div>
                <div class="node-description">{{ node.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NODE_LIBRARY_CONFIG } from '../config/nodeLibrary'
import type { NodeLibraryItem } from '../config/nodeLibrary'

// Emits
defineEmits<{
  'toggle': []
}>()

// 响应式数据
const searchQuery = ref('')
const expandedCategories = ref(['数据接入', '数据处理', '数据输出'])

// 计算属性
const filteredCategories = computed(() => {
  if (!searchQuery.value) {
    return NODE_LIBRARY_CONFIG.categories
  }
  
  return NODE_LIBRARY_CONFIG.categories.map(category => ({
    ...category,
    nodes: category.nodes.filter(node =>
      node.label.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      node.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  })).filter(category => category.nodes.length > 0)
})

// 方法
const toggleCategory = (categoryName: string) => {
  const index = expandedCategories.value.indexOf(categoryName)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(categoryName)
  }
}

const onDragStart = (event: DragEvent, node: NodeLibraryItem) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', JSON.stringify(node))
    event.dataTransfer.effectAllowed = 'move'
  }
}
</script>

<style scoped>
.node-library {
  height: 100%;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.library-header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.library-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.collapse-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.library-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  background: #f8fafc;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
}

.node-categories {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
  transition: all 0.2s ease;
}

.category-header:hover {
  background: #f1f5f9;
}

.toggle-icon {
  margin-left: auto;
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.category-nodes {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.2s ease;
  background: white;
}

.node-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.node-item:active {
  cursor: grabbing;
}

.node-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.node-info {
  flex: 1;
  min-width: 0;
}

.node-label {
  font-weight: 500;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 2px;
}

.node-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}
</style>
