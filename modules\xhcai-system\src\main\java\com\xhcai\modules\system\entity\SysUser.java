package com.xhcai.modules.system.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 用户信息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_user", indexes = {
    @Index(name = "idx_username", columnList = "username", unique = true),
    @Index(name = "idx_email", columnList = "email"),
    @Index(name = "idx_phone", columnList = "phone"),
    @Index(name = "idx_dept_id", columnList = "dept_id"),
    @Index(name = "idx_status", columnList = "status")
})
@Schema(description = "用户信息")
@TableName("sys_user")
public class SysUser extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    @Column(name = "username", length = 50)
    @Schema(description = "用户名", example = "admin")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间")
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @Column(name = "password", length = 100)
    @Schema(description = "密码")
    @JsonIgnore
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    @TableField("password")
    private String password;

    /**
     * 昵称
     */
    @Column(name = "nickname", length = 50)
    @Schema(description = "昵称", example = "管理员")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @TableField("nickname")
    private String nickname;

    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @Column(name = "phone", length = 11)
    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @TableField("phone")
    private String phone;

    /**
     * 头像URL
     */
    @Column(name = "avatar", length = 500)
    @Schema(description = "头像URL")
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    @TableField("avatar")
    private String avatar;

    /**
     * 性别
     */
    @Column(name = "gender", length = 1)
    @Schema(description = "性别", example = "0", allowableValues = {"0", "1", "2"})
    @Pattern(regexp = "^[012]$", message = "性别值必须为0、1或2")
    @TableField("gender")
    private String gender;

    /**
     * 生日
     */
    @Column(name = "birthday")
    @Schema(description = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("birthday")
    private LocalDate birthday;

    /**
     * 部门ID
     */
    @Column(name = "dept_id", length = 36)
    @Schema(description = "部门ID")
    @TableField("dept_id")
    private String deptId;

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1", "9"})
    @Pattern(regexp = "^[019]$", message = "状态值必须为0、1或9")
    @TableField("status")
    private String status;

    /**
     * 用户类型
     */
    @Column(name = "user_type", length = 20)
    @Schema(description = "用户类型", example = "normal", allowableValues = {"platform", "tenant", "normal"})
    @Pattern(regexp = "^(platform|tenant|normal)$", message = "用户类型值必须为platform、tenant或normal")
    @TableField("user_type")
    private String userType;

    /**
     * 最后登录IP
     */
    @Column(name = "login_ip", length = 50)
    @Schema(description = "最后登录IP")
    @TableField("login_ip")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @Column(name = "login_time")
    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("login_time")
    private LocalDateTime loginTime;

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public LocalDateTime getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(LocalDateTime loginTime) {
        this.loginTime = loginTime;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    @Override
    public String toString() {
        return "SysUser{"
                + "username='" + username + '\''
                + ", nickname='" + nickname + '\''
                + ", email='" + email + '\''
                + ", phone='" + phone + '\''
                + ", avatar='" + avatar + '\''
                + ", gender='" + gender + '\''
                + ", birthday=" + birthday
                + ", deptId=" + deptId
                + ", status='" + status + '\''
                + ", userType='" + userType + '\''
                + ", loginIp='" + loginIp + '\''
                + ", loginTime=" + loginTime
                + "} " + super.toString();
    }
}
