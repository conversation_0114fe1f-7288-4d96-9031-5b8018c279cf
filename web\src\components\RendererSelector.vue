<template>
  <div class="renderer-selector">
    <div class="renderer-buttons flex items-center gap-2">
      <button
        v-for="renderer in renderers"
        :key="renderer.type"
        @click="selectRenderer(renderer.type)"
        class="renderer-btn px-3 py-2 text-sm rounded-lg border transition-all duration-200 flex items-center gap-2"
        :class="[
          selectedRenderer === renderer.type
            ? 'bg-blue-50 border-blue-200 text-blue-700'
            : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300'
        ]"
        :title="renderer.description"
      >
        <el-icon class="text-base">
          <component :is="renderer.icon" />
        </el-icon>
        <span>{{ renderer.name }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import {
  Document,
  Picture,
  Microphone,
  VideoPlay,
  DataAnalysis,
  Connection,
  Files,
  ChatDotRound
} from '@element-plus/icons-vue'
import type { RendererType, RendererConfig } from '@/types/renderer'

// Props
const props = defineProps<{
  selectedRenderer: RendererType
}>()

// Emits
const emit = defineEmits<{
  'renderer-select': [renderer: RendererType]
}>()

// 渲染器列表
const renderers: RendererConfig[] = [
  {
    type: 'text',
    name: '纯文本',
    description: '显示纯文本内容，支持链接识别',
    icon: ChatDotRound
  },
  {
    type: 'markdown',
    name: 'Markdown',
    description: '渲染Markdown格式内容，支持代码高亮、表格等',
    icon: Document
  },
  {
    type: 'image',
    name: '图片',
    description: '展示图片画廊，支持预览和缩放',
    icon: Picture
  },
  {
    type: 'audio',
    name: '音频',
    description: '播放音频文件，支持进度控制',
    icon: Microphone
  },
  {
    type: 'video',
    name: '视频',
    description: '播放视频文件，支持全屏和控制',
    icon: VideoPlay
  },
  {
    type: 'chart',
    name: '图表',
    description: '渲染各种类型的数据图表',
    icon: DataAnalysis
  },
  {
    type: 'flowchart',
    name: '流程图',
    description: '显示Mermaid流程图和图表',
    icon: Connection
  },
  {
    type: 'file',
    name: '文件',
    description: '预览PDF、Word、Excel等文件',
    icon: Files
  },
  {
    type: 'html',
    name: 'HTML',
    description: '渲染HTML内容，支持自定义样式',
    icon: Document
  },
  {
    type: 'mixed',
    name: '混合内容',
    description: '同时展示多种类型的内容',
    icon: ChatDotRound
  }
]

// 方法
const selectRenderer = (renderer: RendererType) => {
  emit('renderer-select', renderer)
}
</script>

<style scoped>
.renderer-selector {
  display: flex;
  align-items: center;
}

.renderer-buttons {
  flex-wrap: wrap;
}

.renderer-btn {
  font-weight: 500;
  white-space: nowrap;
  user-select: none;
  cursor: pointer;
}

.renderer-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.renderer-btn:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .renderer-buttons {
    gap: 0.5rem;
  }
  
  .renderer-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .renderer-btn span {
    display: none;
  }
}
</style>
