package com.xhcai.modules.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.modules.system.vo.SysUserVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.modules.system.dto.SysPermissionQueryDTO;
import com.xhcai.modules.system.entity.SysPermission;
import com.xhcai.modules.system.service.ISysMenuService;
import com.xhcai.modules.system.service.ISysPermissionService;
import com.xhcai.modules.system.vo.SysPermissionVO;

/**
 * 菜单管理服务实现类
 *
 * 注意：菜单是权限的一种特殊类型，这里委托给权限服务处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysMenuServiceImpl implements ISysMenuService {

    private static final Logger log = LoggerFactory.getLogger(SysMenuServiceImpl.class);

    @Autowired
    private ISysPermissionService permissionService;

    @Override
    public List<SysPermissionVO> selectMenuTree(SysPermissionQueryDTO queryDTO) {
        // 设置查询菜单类型（1-菜单，2-按钮，3-接口）
        queryDTO.setPermissionType("1");
        return permissionService.selectPermissionTree(queryDTO);
    }

    @Override
    public List<SysPermissionVO> selectMenuTreeByUserId(SysUserVO sysUserVO) {
        List<SysPermissionVO> userPermissions = permissionService.selectPermissionsByUserId(sysUserVO);

        // 过滤出菜单类型的权限
        List<SysPermissionVO> menuPermissions = userPermissions.stream()
                .filter(permission -> "1".equals(permission.getPermissionType()))
                .collect(Collectors.toList());

        // 构建菜单树
        return buildMenuTree(menuPermissions, CommonConstants.MENU_TREE_ROOT_ID);
    }

    @Override
    public List<SysPermissionVO> buildMenuSelectTree(String excludeMenuId) {
        return permissionService.buildPermissionSelectTree(excludeMenuId);
    }

    @Override
    public boolean insertMenu(SysPermission menu) {
        // 设置为菜单类型
        menu.setPermissionType("1");
        return permissionService.insertPermission(menu);
    }

    @Override
    public boolean updateMenu(SysPermission menu) {
        // 确保是菜单类型
        menu.setPermissionType("1");
        return permissionService.updatePermission(menu);
    }

    @Override
    public boolean deleteMenus(List<String> menuIds) {
        return permissionService.deletePermissions(menuIds);
    }

    @Override
    public List<SysPermissionVO> selectMenuPermissionsByRoleId(String roleId) {
        List<SysPermissionVO> rolePermissions = permissionService.selectPermissionsByRoleId(roleId);

        // 过滤出菜单类型的权限
        return rolePermissions.stream()
                .filter(permission -> "1".equals(permission.getPermissionType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SysPermissionVO> selectAllMenus() {
        List<SysPermissionVO> allPermissions = permissionService.selectAllAvailablePermissions();

        // 过滤出菜单类型的权限
        return allPermissions.stream()
                .filter(permission -> "1".equals(permission.getPermissionType()))
                .collect(Collectors.toList());
    }

    /**
     * 构建菜单树
     *
     * @param menus 菜单列表
     * @param parentId 父菜单ID
     * @return 菜单树
     */
    private List<SysPermissionVO> buildMenuTree(List<SysPermissionVO> menus, String parentId) {
        return menus.stream()
                .filter(menu -> parentId.equals(menu.getParentId()))
                .peek(menu -> {
                    List<SysPermissionVO> children = buildMenuTree(menus, menu.getId());
                    menu.setChildren(children);
                    menu.setHasChildren(!children.isEmpty());
                })
                .collect(Collectors.toList());
    }
}
