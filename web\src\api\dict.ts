/**
 * 字典数据API接口
 */

import { apiClient } from '@/utils/apiClient'
import type { ApiResponse, PageResult } from '@/types/api'
import type {
  SysDictDataVO,
  SysDictTypeVO,
  DictTypeQueryDTO,
  DictDataQueryDTO,
  DictTypeDTO,
  DictDataDTO
} from '@/types/system'

// 为了与后端保持一致，创建类型别名
type SysDictVO = SysDictTypeVO

/**
 * 字典管理API
 */
export class DictAPI {
  // ==================== 字典类型管理 ====================

  /**
   * 分页查询字典类型
   */
  static async getDictTypeList(params: DictTypeQueryDTO): Promise<ApiResponse<PageResult<SysDictVO>>> {
    return apiClient.get('/api/system/dict/page', params)
  }

  /**
   * 获取字典类型详情
   */
  static async getDictTypeById(id: string): Promise<ApiResponse<SysDictVO>> {
    return apiClient.get(`/api/system/dict/${id}`)
  }

  /**
   * 创建字典类型
   */
  static async createDictType(data: DictTypeDTO): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/dict', data)
  }

  /**
   * 更新字典类型
   */
  static async updateDictType(id: string, data: DictTypeDTO): Promise<ApiResponse<void>> {
    data.id = id
    return apiClient.put('/api/system/dict', data)
  }

  /**
   * 删除字典类型
   */
  static async deleteDictType(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/dict/${id}`)
  }

  /**
   * 批量删除字典类型
   */
  static async batchDeleteDictType(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/dict/${ids.join(',')}`)
  }

  /**
   * 获取所有字典类型（不分页）
   */
  static async getAllDictTypes(): Promise<ApiResponse<SysDictVO[]>> {
    return apiClient.get('/api/system/dict/list')
  }

  // ==================== 字典数据管理 ====================

  /**
   * 分页查询字典数据
   */
  static async getDictDataList(params: DictDataQueryDTO): Promise<ApiResponse<PageResult<SysDictDataVO>>> {
    return apiClient.get('/api/system/dict/data/page', params)
  }

  /**
   * 获取字典数据详情
   */
  static async getDictDataById(id: string): Promise<ApiResponse<SysDictDataVO>> {
    return apiClient.get(`/api/system/dict/data/${id}`)
  }

  /**
   * 创建字典数据
   */
  static async createDictData(data: DictDataDTO): Promise<ApiResponse<void>> {
    return apiClient.post('/api/system/dict/data', data)
  }

  /**
   * 更新字典数据
   */
  static async updateDictData(id: string, data: DictDataDTO): Promise<ApiResponse<void>> {
    data.id = id
    return apiClient.put('/api/system/dict/data', data)
  }

  /**
   * 删除字典数据
   */
  static async deleteDictData(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/dict/data/${id}`)
  }

  /**
   * 批量删除字典数据
   */
  static async batchDeleteDictData(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/dict/data/${ids.join(',')}`)
  }

  // ==================== 字典查询功能（保持原有功能） ====================
  /**
   * 根据字典类型查询字典数据
   */
  static async getDictDataByType(dictType: string): Promise<ApiResponse<SysDictDataVO[]>> {
    return apiClient.get(`/api/system/dict/data/type/${dictType}`)
  }

  /**
   * 根据字典类型和值获取字典标签
   */
  static async getDictLabel(dictType: string, dictValue: string): Promise<ApiResponse<string>> {
    return apiClient.get('/api/system/dict/data/label', {
      dictType,
      dictValue
    })
  }

  /**
   * 批量获取多个字典类型的数据
   */
  static async getBatchDictData(dictTypes: string[]): Promise<ApiResponse<Record<string, SysDictDataVO[]>>> {
    const promises = dictTypes.map(async (dictType) => {
      const response = await this.getDictDataByType(dictType)
      return { [dictType]: response.data || [] }
    })
    
    const results = await Promise.all(promises)
    const dictData = results.reduce((acc, curr) => ({ ...acc, ...curr }), {})
    
    return {
      code: 200,
      message: 'success',
      data: dictData,
      success: true
    }
  }

  /**
   * 获取用户性别字典
   */
  static async getUserGenderDict(): Promise<ApiResponse<SysDictDataVO[]>> {
    return this.getDictDataByType('sys_user_gender')
  }

  /**
   * 获取用户状态字典
   */
  static async getUserStatusDict(): Promise<ApiResponse<SysDictDataVO[]>> {
    return this.getDictDataByType('sys_user_status')
  }

  /**
   * 获取部门状态字典
   */
  static async getDeptStatusDict(): Promise<ApiResponse<SysDictDataVO[]>> {
    return this.getDictDataByType('sys_dept_status')
  }

  /**
   * 获取角色状态字典
   */
  static async getRoleStatusDict(): Promise<ApiResponse<SysDictDataVO[]>> {
    return this.getDictDataByType('sys_role_status')
  }

  /**
   * 获取是否字典
   */
  static async getYesNoDict(): Promise<ApiResponse<SysDictDataVO[]>> {
    return this.getDictDataByType('sys_yes_no')
  }

  /**
   * 获取数据范围字典
   */
  static async getDataScopeDict(): Promise<ApiResponse<SysDictDataVO[]>> {
    return this.getDictDataByType('sys_data_scope')
  }

  /**
   * 获取系统常用字典数据
   */
  static async getSystemDicts(): Promise<ApiResponse<Record<string, SysDictDataVO[]>>> {
    const dictTypes = [
      'sys_user_gender',
      'sys_user_status', 
      'sys_dept_status',
      'sys_role_status',
      'sys_yes_no',
      'sys_data_scope'
    ]
    
    return this.getBatchDictData(dictTypes)
  }
}

export default DictAPI
