package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.system.entity.SysDept;

/**
 * 部门信息Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 查询部门列表
     *
     * @param deptName 部门名称
     * @param deptCode 部门编码
     * @param leaderId 负责人ID
     * @param status 状态
     * @param parentId 父部门ID
     * @return 部门列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_dept "
            + "WHERE deleted = 0 "
            + "<if test='deptName != null and deptName != \"\"'>"
            + "  AND dept_name LIKE CONCAT('%', #{deptName}, '%') "
            + "</if>"
            + "<if test='deptCode != null and deptCode != \"\"'>"
            + "  AND dept_code LIKE CONCAT('%', #{deptCode}, '%') "
            + "</if>"
            + "<if test='leaderId != null and leaderId != \"\"'>"
            + "  AND leader_id = #{leaderId} "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "  AND status = #{status} "
            + "</if>"
            + "<if test='parentId != null'>"
            + "  AND parent_id = #{parentId} "
            + "</if>"
            + "ORDER BY order_num ASC, create_time ASC"
            + "</script>")
    List<SysDept> selectDeptList(
            @Param("deptName") String deptName,
            @Param("deptCode") String deptCode,
            @Param("leaderId") String leaderId,
            @Param("status") String status,
            @Param("parentId") String parentId
    );

    /**
     * 根据部门编码查询部门信息
     *
     * @param deptCode 部门编码
     * @return 部门信息
     */
    @Select("SELECT * FROM sys_dept WHERE dept_code = #{deptCode} AND deleted = 0")
    SysDept selectByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 查询子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @Select("SELECT * FROM sys_dept WHERE parent_id = #{parentId} AND deleted = 0 ORDER BY order_num ASC")
    List<SysDept> selectChildrenByParentId(@Param("parentId") String parentId);

    /**
     * 查询部门及其所有子部门
     *
     * @param deptId 部门ID
     * @return 部门及子部门列表
     */
    @Select("SELECT * FROM sys_dept WHERE (id = #{deptId} OR FIND_IN_SET(#{deptId}, ancestors)) AND deleted = 0 ORDER BY order_num ASC")
    List<SysDept> selectDeptAndChildren(@Param("deptId") String deptId);

    /**
     * 统计部门下的用户数量
     *
     * @param deptId 部门ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE dept_id = #{deptId} AND deleted = 0")
    Integer countUsersByDeptId(@Param("deptId") String deptId);

    /**
     * 检查部门编码是否存在
     *
     * @param deptCode 部门编码
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_dept "
            + "WHERE dept_code = #{deptCode} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "  AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsDeptCode(@Param("deptCode") String deptCode, @Param("excludeId") String excludeId);

    /**
     * 检查部门名称是否存在（同级部门）
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_dept "
            + "WHERE dept_name = #{deptName} AND parent_id = #{parentId} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "  AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsDeptName(@Param("deptName") String deptName, @Param("parentId") String parentId, @Param("excludeId") String excludeId);

    /**
     * 检查是否存在子部门
     *
     * @param deptId 部门ID
     * @return 子部门数量
     */
    @Select("SELECT COUNT(*) FROM sys_dept WHERE parent_id = #{deptId} AND deleted = 0")
    Integer hasChildren(@Param("deptId") String deptId);

    /**
     * 更新子部门的祖级列表
     *
     * @param deptId 部门ID
     * @param newAncestors 新的祖级列表
     * @param oldAncestors 旧的祖级列表
     * @return 更新行数
     */
    @Update("UPDATE sys_dept SET ancestors = REPLACE(ancestors, #{oldAncestors}, #{newAncestors}) "
            + "WHERE FIND_IN_SET(#{deptId}, ancestors) AND deleted = 0")
    int updateChildrenAncestors(@Param("deptId") String deptId, @Param("newAncestors") String newAncestors, @Param("oldAncestors") String oldAncestors);

    /**
     * 根据祖级列表查询部门
     *
     * @param ancestors 祖级列表
     * @return 部门列表
     */
    @Select("SELECT * FROM sys_dept WHERE FIND_IN_SET(id, #{ancestors}) AND deleted = 0 ORDER BY order_num ASC")
    List<SysDept> selectByAncestors(@Param("ancestors") String ancestors);

    /**
     * 查询部门树（根据状态）
     *
     * @param status 状态
     * @return 部门列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_dept "
            + "WHERE deleted = 0 "
            + "<if test='status != null and status != \"\"'>"
            + "  AND status = #{status} "
            + "</if>"
            + "ORDER BY order_num ASC, create_time ASC"
            + "</script>")
    List<SysDept> selectDeptTree(@Param("status") String status);

    /**
     * 查询最大排序号
     *
     * @param parentId 父部门ID
     * @return 最大排序号
     */
    @Select("SELECT COALESCE(MAX(order_num), 0) FROM sys_dept WHERE parent_id = #{parentId} AND deleted = 0")
    Integer selectMaxOrderNum(@Param("parentId") String parentId);
}
