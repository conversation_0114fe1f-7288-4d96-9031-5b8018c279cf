package com.xhcai.modules.agent.controller;

import java.time.LocalDateTime;
import java.util.List;

import com.xhcai.modules.agent.vo.HistoryStatsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.agent.dto.AgentWorkflowHistoryQueryDTO;
import com.xhcai.modules.agent.service.IAgentWorkflowHistoryService;
import com.xhcai.modules.agent.service.IAgentWorkflowHistoryService.HistoryCompareVO;
import com.xhcai.modules.agent.vo.AgentWorkflowHistoryVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 智能体工作流历史记录控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "智能体工作流历史记录管理", description = "智能体工作流历史记录的增删改查操作")
@RestController
@RequestMapping("/api/agent/workflow/history")
public class AgentWorkflowHistoryController {

    @Autowired
    private IAgentWorkflowHistoryService historyService;

    @Operation(summary = "分页查询工作流历史记录", description = "根据条件分页查询工作流历史记录列表")
    @GetMapping("/page")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<IPage<AgentWorkflowHistoryVO>> getHistoryPage(@Valid AgentWorkflowHistoryQueryDTO queryDTO) {
        IPage<AgentWorkflowHistoryVO> page = historyService.getHistoryPage(queryDTO);
        return Result.success(page);
    }

    @Operation(summary = "根据工作流ID查询历史记录", description = "查询指定工作流的所有历史记录")
    @GetMapping("/workflow/{workflowId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<List<AgentWorkflowHistoryVO>> getHistoryByWorkflowId(
            @Parameter(description = "工作流ID", required = true) @PathVariable String workflowId) {
        List<AgentWorkflowHistoryVO> historyList = historyService.getHistoryByWorkflowId(workflowId);
        return Result.success(historyList);
    }

    @Operation(summary = "根据智能体ID查询历史记录", description = "查询指定智能体的所有工作流历史记录")
    @GetMapping("/agent/{agentId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<List<AgentWorkflowHistoryVO>> getHistoryByAgentId(
            @Parameter(description = "智能体ID", required = true) @PathVariable String agentId) {
        List<AgentWorkflowHistoryVO> historyList = historyService.getHistoryByAgentId(agentId);
        return Result.success(historyList);
    }

    @Operation(summary = "根据配置哈希值查询历史记录", description = "根据配置哈希值查询历史记录")
    @GetMapping("/config/{configHash}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<AgentWorkflowHistoryVO> getHistoryByConfigHash(
            @Parameter(description = "配置哈希值", required = true) @PathVariable String configHash) {
        AgentWorkflowHistoryVO history = historyService.getHistoryByConfigHash(configHash);
        return Result.success(history);
    }

    @Operation(summary = "查询最近的历史记录", description = "查询指定工作流最近的历史记录")
    @GetMapping("/recent/{workflowId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<List<AgentWorkflowHistoryVO>> getRecentHistory(
            @Parameter(description = "工作流ID", required = true) @PathVariable String workflowId,
            @Parameter(description = "限制数量", required = false) @RequestParam(defaultValue = "10") Integer limit) {
        List<AgentWorkflowHistoryVO> historyList = historyService.getRecentHistory(workflowId, limit);
        return Result.success(historyList);
    }

    @Operation(summary = "查询重要变更历史", description = "查询指定工作流的重要变更历史记录")
    @GetMapping("/major-changes/{workflowId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<List<AgentWorkflowHistoryVO>> getMajorChanges(
            @Parameter(description = "工作流ID", required = true) @PathVariable String workflowId) {
        List<AgentWorkflowHistoryVO> historyList = historyService.getMajorChanges(workflowId);
        return Result.success(historyList);
    }

    @Operation(summary = "查询用户操作历史", description = "查询指定用户在指定工作流上的操作历史")
    @GetMapping("/user/{workflowId}/{userId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<List<AgentWorkflowHistoryVO>> getHistoryByUser(
            @Parameter(description = "工作流ID", required = true) @PathVariable String workflowId,
            @Parameter(description = "用户ID", required = true) @PathVariable String userId) {
        List<AgentWorkflowHistoryVO> historyList = historyService.getHistoryByUser(workflowId, userId);
        return Result.success(historyList);
    }

    @Operation(summary = "查询操作类型历史", description = "查询指定操作类型的历史记录")
    @GetMapping("/operation/{workflowId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<List<AgentWorkflowHistoryVO>> getHistoryByOperationType(
            @Parameter(description = "工作流ID", required = true) @PathVariable String workflowId,
            @Parameter(description = "操作类型", required = true) @RequestParam String operationType) {
        List<AgentWorkflowHistoryVO> historyList = historyService.getHistoryByOperationType(workflowId, operationType);
        return Result.success(historyList);
    }

    @Operation(summary = "对比历史记录", description = "对比两个历史记录的差异")
    @GetMapping("/compare")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<HistoryCompareVO> compareHistory(
            @Parameter(description = "源历史记录ID", required = true) @RequestParam String fromHistoryId,
            @Parameter(description = "目标历史记录ID", required = true) @RequestParam String toHistoryId) {
        HistoryCompareVO compareResult = historyService.compareHistory(fromHistoryId, toHistoryId);
        return Result.success(compareResult);
    }

    @Operation(summary = "查询历史记录统计", description = "查询指定智能体的历史记录统计信息")
    @GetMapping("/stats/{agentId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<HistoryStatsVO> getHistoryStats(
            @Parameter(description = "智能体ID", required = true) @PathVariable String agentId) {
        HistoryStatsVO stats = historyService.getHistoryStats(agentId);
        return Result.success(stats);
    }

    @Operation(summary = "统计工作流历史记录数量", description = "统计指定工作流的历史记录数量")
    @GetMapping("/count/workflow/{workflowId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<Long> countHistoryByWorkflowId(
            @Parameter(description = "工作流ID", required = true) @PathVariable String workflowId) {
        Long count = historyService.countHistoryByWorkflowId(workflowId);
        return Result.success(count);
    }

    @Operation(summary = "统计智能体历史记录数量", description = "统计指定智能体的历史记录数量")
    @GetMapping("/count/agent/{agentId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<Long> countHistoryByAgentId(
            @Parameter(description = "智能体ID", required = true) @PathVariable String agentId) {
        Long count = historyService.countHistoryByAgentId(agentId);
        return Result.success(count);
    }

    @Operation(summary = "统计时间范围内历史记录数量", description = "统计指定时间范围内的历史记录数量")
    @GetMapping("/count/time-range/{workflowId}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<Long> countHistoryByTimeRange(
            @Parameter(description = "工作流ID", required = true) @PathVariable String workflowId,
            @Parameter(description = "开始时间", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        Long count = historyService.countHistoryByTimeRange(workflowId, startTime, endTime);
        return Result.success(count);
    }

    @Operation(summary = "检查配置是否存在", description = "检查指定配置哈希值是否已存在")
    @GetMapping("/exists/{configHash}")
    @RequiresPermissions("agent:workflow:history:list")
    public Result<Boolean> isConfigExists(
            @Parameter(description = "配置哈希值", required = true) @PathVariable String configHash) {
        Boolean exists = historyService.isConfigExists(configHash);
        return Result.success(exists);
    }

    @Operation(summary = "清理历史记录", description = "删除指定时间之前的历史记录")
    @DeleteMapping("/cleanup")
    @RequiresPermissions("agent:workflow:history:delete")
    public Result<Integer> deleteHistoryBeforeTime(
            @Parameter(description = "时间点", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beforeTime) {
        Integer deletedCount = historyService.deleteHistoryBeforeTime(beforeTime);
        return Result.success(deletedCount);
    }

    /**
     * 历史记录详情VO
     */
    public static class HistoryDetailVO {
        private AgentWorkflowHistoryVO history;
        private String configDiff;
        private List<String> changePoints;

        // Getters and Setters
        public AgentWorkflowHistoryVO getHistory() {
            return history;
        }

        public void setHistory(AgentWorkflowHistoryVO history) {
            this.history = history;
        }

        public String getConfigDiff() {
            return configDiff;
        }

        public void setConfigDiff(String configDiff) {
            this.configDiff = configDiff;
        }

        public List<String> getChangePoints() {
            return changePoints;
        }

        public void setChangePoints(List<String> changePoints) {
            this.changePoints = changePoints;
        }
    }
}
