package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.BatchSegmentationRequestDTO;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.service.IDocumentSegmentService;
import com.xhcai.modules.rag.vo.DocumentPreviewVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文档管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Tag(name = "文档管理", description = "文档分段预览，查询等操作")
@RestController
@RequestMapping("/api/rag/document-segments")
@Validated
public class DocumentSegmentController {
    @Autowired
    private IDocumentSegmentService documentSegmentService;

    @Operation(summary = "批量开始分段处理", description = "批量开始文档分段处理")
    @PostMapping("/batch/start")
    @RequiresPermissions("rag:document:update")
    public Result<Void> batchStartSegmentation(
            @Parameter(description = "批量分段处理请求") @RequestBody BatchSegmentationRequestDTO request) {
        log.info("批量开始分段处理: documentIds={}, configSize={}",
                request.getDocumentIds(), request.getDocConfigs().size());

        try {
            boolean success = documentSegmentService.batchStartSegmentation(request);
            if (success) {
                return Result.success();
            } else {
                return Result.fail("批量分段处理启动失败");
            }
        } catch (Exception e) {
            log.error("批量分段处理启动异常: {}", e.getMessage(), e);
            return Result.fail("批量分段处理启动异常: " + e.getMessage());
        }
    }

    @Operation(summary = "预览文档分段", description = "预览文档的分段内容")
    @PostMapping("/preview")
    @RequiresPermissions("rag:document:list")
    public PageResult<SegmentResult> previewDocumentSegments(@RequestBody DocumentPreviewVO documentPreviewVO) {
        Document document = new Document();
        document.setId(documentPreviewVO.getDocumentId());
        document.setDocType(documentPreviewVO.getDocType());
        document.setSegmentConfig(documentPreviewVO.getSegmentConfig());
        document.setCleaningConfig(documentPreviewVO.getCleanConfig());

        try {
            List<SegmentResult> segmentResults = documentSegmentService.downloadAndProcessFile(document, documentPreviewVO.getPreviewUrl());

            // 按分页的请求条件，进行分布结果返回
            return PageResult.of(segmentResults, (long) segmentResults.size(), documentPreviewVO.getCurrent(), documentPreviewVO.getSize());
        }catch (Exception e){
            log.error("预览文档分段失败: documentId={} previewUrl={} error={}", documentPreviewVO.getDocumentId(), documentPreviewVO.getPreviewUrl(), e.getMessage(), e);
            return PageResult.empty(documentPreviewVO.getCurrent(), documentPreviewVO.getSize());
        }
    }
}
