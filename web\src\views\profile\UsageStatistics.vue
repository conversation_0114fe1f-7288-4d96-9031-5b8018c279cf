<template>
  <div class="usage-statistics">
    <div class="section-header">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">使用统计</h2>
      <p class="text-gray-600">查看您的平台使用情况和数据统计</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6 mt-6">
      <!-- 统计卡片 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">对话数量</p>
            <p class="text-3xl font-bold text-blue-600">{{ stats.conversations }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-blue-600 text-xl"><ChatDotRound /></el-icon>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <span class="text-green-600 font-medium">+12%</span>
          <span class="text-gray-600 ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">消息总数</p>
            <p class="text-3xl font-bold text-green-600">{{ stats.messages }}</p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-green-600 text-xl"><Message /></el-icon>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <span class="text-green-600 font-medium">+8%</span>
          <span class="text-gray-600 ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">使用智能体</p>
            <p class="text-3xl font-bold text-purple-600">{{ stats.agents }}</p>
          </div>
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-purple-600 text-xl"><Management /></el-icon>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <span class="text-green-600 font-medium">+3</span>
          <span class="text-gray-600 ml-2">新增</span>
        </div>
      </div>

      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">知识库</p>
            <p class="text-3xl font-bold text-orange-600">{{ stats.knowledgeBases }}</p>
          </div>
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-orange-600 text-xl"><Document /></el-icon>
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
          <span class="text-green-600 font-medium">+1</span>
          <span class="text-gray-600 ml-2">新增</span>
        </div>
      </div>
    </div>

    <!-- 详细统计图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
      <!-- 使用趋势图 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <el-icon class="text-blue-500"><TrendCharts /></el-icon>
          使用趋势
        </h3>
        <div class="h-64 flex items-center justify-center text-gray-500">
          <!-- 这里可以集成图表库，如 ECharts -->
          <div class="text-center">
            <el-icon class="text-4xl mb-2"><TrendCharts /></el-icon>
            <p>使用趋势图表</p>
            <p class="text-sm">显示最近30天的使用情况</p>
          </div>
        </div>
      </div>

      <!-- 功能使用分布 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <el-icon class="text-purple-500"><PieChart /></el-icon>
          功能使用分布
        </h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-4 h-4 bg-blue-500 rounded"></div>
              <span class="text-sm text-gray-700">AI对话</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-24 bg-gray-200 rounded-full h-2">
                <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
              </div>
              <span class="text-sm font-medium text-gray-600">65%</span>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-4 h-4 bg-green-500 rounded"></div>
              <span class="text-sm text-gray-700">知识库查询</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-24 bg-gray-200 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full" style="width: 25%"></div>
              </div>
              <span class="text-sm font-medium text-gray-600">25%</span>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-4 h-4 bg-purple-500 rounded"></div>
              <span class="text-sm text-gray-700">智能体使用</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-24 bg-gray-200 rounded-full h-2">
                <div class="bg-purple-500 h-2 rounded-full" style="width: 10%"></div>
              </div>
              <span class="text-sm font-medium text-gray-600">10%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近使用记录 -->
    <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6 mt-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <el-icon class="text-orange-500"><Clock /></el-icon>
        最近使用记录
      </h3>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-3 px-4 font-medium text-gray-600">时间</th>
              <th class="text-left py-3 px-4 font-medium text-gray-600">功能</th>
              <th class="text-left py-3 px-4 font-medium text-gray-600">详情</th>
              <th class="text-left py-3 px-4 font-medium text-gray-600">状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="record in usageRecords" :key="record.id" class="border-b border-gray-100 hover:bg-gray-50">
              <td class="py-3 px-4 text-sm text-gray-600">{{ formatTime(record.time) }}</td>
              <td class="py-3 px-4">
                <div class="flex items-center gap-2">
                  <span class="text-lg">{{ getFeatureIcon(record.feature) }}</span>
                  <span class="text-sm font-medium text-gray-800">{{ record.feature }}</span>
                </div>
              </td>
              <td class="py-3 px-4 text-sm text-gray-600">{{ record.detail }}</td>
              <td class="py-3 px-4">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                      :class="getStatusClass(record.status)">
                  {{ record.status }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ChatDotRound, Message, Management, Document, TrendCharts, PieChart, Clock } from '@element-plus/icons-vue'

interface UsageRecord {
  id: string
  time: Date
  feature: string
  detail: string
  status: string
}

// 统计数据
const stats = ref({
  conversations: 42,
  messages: 1286,
  agents: 8,
  knowledgeBases: 5
})

// 使用记录
const usageRecords = ref<UsageRecord[]>([
  {
    id: '1',
    time: new Date(Date.now() - 1000 * 60 * 30),
    feature: 'AI对话',
    detail: '与智能助手进行技术咨询',
    status: '成功'
  },
  {
    id: '2',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2),
    feature: '知识库查询',
    detail: '搜索技术文档',
    status: '成功'
  },
  {
    id: '3',
    time: new Date(Date.now() - 1000 * 60 * 60 * 4),
    feature: '智能体使用',
    detail: '使用代码生成助手',
    status: '成功'
  },
  {
    id: '4',
    time: new Date(Date.now() - 1000 * 60 * 60 * 24),
    feature: '文件上传',
    detail: '上传技术规范文档',
    status: '失败'
  }
])

const getFeatureIcon = (feature: string) => {
  const iconMap: Record<string, string> = {
    'AI对话': '💬',
    '知识库查询': '📖',
    '智能体使用': '🤖',
    '文件上传': '📄'
  }
  return iconMap[feature] || '📝'
}

const getStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    '成功': 'bg-green-100 text-green-800',
    '失败': 'bg-red-100 text-red-800',
    '处理中': 'bg-yellow-100 text-yellow-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}
</script>

<style scoped>
.section-header {
  margin-bottom: 1.5rem;
}
</style>
