package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Dify 会话消息列表响应 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify 会话消息列表响应")
public class DifyMessageListResponseDTO {

    /**
     * 消息列表
     */
    @Schema(description = "消息列表")
    @JsonProperty("data")
    private List<DifyMessageDTO> data;

    /**
     * 是否有更多数据
     */
    @Schema(description = "是否有更多数据")
    @JsonProperty("has_more")
    private Boolean hasMore;

    /**
     * 限制数量
     */
    @Schema(description = "限制数量")
    @JsonProperty("limit")
    private Integer limit;

    /**
     * 总数量
     */
    @Schema(description = "总数量")
    @JsonProperty("total")
    private Integer total;

    /**
     * 第一个消息ID（用于分页）
     */
    @Schema(description = "第一个消息ID")
    @JsonProperty("first_id")
    private String firstId;

    /**
     * 最后一个消息ID（用于分页）
     */
    @Schema(description = "最后一个消息ID")
    @JsonProperty("last_id")
    private String lastId;

    public DifyMessageListResponseDTO() {
    }

    public List<DifyMessageDTO> getData() {
        return data;
    }

    public void setData(List<DifyMessageDTO> data) {
        this.data = data;
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getFirstId() {
        return firstId;
    }

    public void setFirstId(String firstId) {
        this.firstId = firstId;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    @Override
    public String toString() {
        return "DifyMessageListResponseDTO{" +
                "data=" + data +
                ", hasMore=" + hasMore +
                ", limit=" + limit +
                ", total=" + total +
                ", firstId='" + firstId + '\'' +
                ", lastId='" + lastId + '\'' +
                '}';
    }
}
