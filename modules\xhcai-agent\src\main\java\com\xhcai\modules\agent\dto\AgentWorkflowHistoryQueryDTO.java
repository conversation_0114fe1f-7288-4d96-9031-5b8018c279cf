package com.xhcai.modules.agent.dto;

import java.time.LocalDateTime;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 智能体工作流历史记录查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体工作流历史记录查询DTO")
public class AgentWorkflowHistoryQueryDTO extends PageTimeRangeQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "workflow_001")
    @Size(max = 36, message = "工作流ID长度不能超过36个字符")
    private String workflowId;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 配置哈希值
     */
    @Schema(description = "配置哈希值", example = "a1b2c3d4e5f6...")
    @Size(max = 64, message = "配置哈希值长度不能超过64个字符")
    private String configHash;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "node_add")
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    private String operationType;

    /**
     * 操作用户ID
     */
    @Schema(description = "操作用户ID", example = "user_001")
    @Size(max = 36, message = "操作用户ID长度不能超过36个字符")
    private String operationUserId;

    /**
     * 操作用户名称
     */
    @Schema(description = "操作用户名称", example = "张三")
    @Size(max = 100, message = "操作用户名称长度不能超过100个字符")
    private String operationUserName;

    /**
     * 是否为重要变更
     */
    @Schema(description = "是否为重要变更", example = "true")
    private Boolean isMajorChange;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1")
    private Integer version;

    /**
     * 关键词搜索（搜索操作描述、变更摘要等）
     */
    @Schema(description = "关键词搜索", example = "添加节点")
    @Size(max = 100, message = "关键词长度不能超过100个字符")
    private String keyword;

    /**
     * 操作开始时间
     */
    @Schema(description = "操作开始时间")
    private LocalDateTime operationStartTime;

    /**
     * 操作结束时间
     */
    @Schema(description = "操作结束时间")
    private LocalDateTime operationEndTime;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "operation_time", allowableValues = {
        "operation_time", "config_size", "version", "create_time"
    })
    private String sortField;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder;

    // Getters and Setters
    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getConfigHash() {
        return configHash;
    }

    public void setConfigHash(String configHash) {
        this.configHash = configHash;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationUserId() {
        return operationUserId;
    }

    public void setOperationUserId(String operationUserId) {
        this.operationUserId = operationUserId;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public Boolean getIsMajorChange() {
        return isMajorChange;
    }

    public void setIsMajorChange(Boolean isMajorChange) {
        this.isMajorChange = isMajorChange;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public LocalDateTime getOperationStartTime() {
        return operationStartTime;
    }

    public void setOperationStartTime(LocalDateTime operationStartTime) {
        this.operationStartTime = operationStartTime;
    }

    public LocalDateTime getOperationEndTime() {
        return operationEndTime;
    }

    public void setOperationEndTime(LocalDateTime operationEndTime) {
        this.operationEndTime = operationEndTime;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }
}
