package com.xhcai.modules.agent.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.agent.dto.AgentWorkflowCreateDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowQueryDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowUpdateDTO;
import com.xhcai.modules.agent.dto.RealtimeSaveDTO;
import com.xhcai.modules.agent.entity.AgentWorkflow;
import com.xhcai.modules.agent.mapper.AgentWorkflowMapper;
import com.xhcai.modules.agent.service.IAgentWorkflowHistoryService;
import com.xhcai.modules.agent.service.IAgentWorkflowService;
import com.xhcai.modules.agent.vo.AgentWorkflowVO;
import com.xhcai.modules.agent.vo.AgentWorkflowVersionVO;

/**
 * 智能体工作流服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AgentWorkflowServiceImpl extends ServiceImpl<AgentWorkflowMapper, AgentWorkflow> implements IAgentWorkflowService {

    private static final Logger logger = LoggerFactory.getLogger(AgentWorkflowServiceImpl.class);

    @Autowired
    private AgentWorkflowMapper agentWorkflowMapper;

    @Autowired
    private IAgentWorkflowHistoryService historyService;

    @Override
    public IPage<AgentWorkflowVO> getWorkflowPage(AgentWorkflowQueryDTO queryDTO) {

        Page<AgentWorkflowVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return agentWorkflowMapper.selectWorkflowPage(page, queryDTO);
    }

    @Override
    public AgentWorkflowVO getWorkflowById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        AgentWorkflowVO workflow = agentWorkflowMapper.selectWorkflowById(id, tenantId);
        if (workflow == null) {
            throw new BusinessException("工作流不存在");
        }

        return workflow;
    }

    @Override
    public AgentWorkflowVO getLatestWorkflowByAgentId(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            throw new BusinessException("智能体ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return agentWorkflowMapper.selectLatestWorkflowByAgentId(agentId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createWorkflow(AgentWorkflowCreateDTO createDTO) {
        // 参数验证
        if (createDTO == null) {
            throw new BusinessException("创建信息不能为空");
        }
        if (!StringUtils.hasText(createDTO.getAgentId())) {
            throw new BusinessException("智能体ID不能为空");
        }
        if (!StringUtils.hasText(createDTO.getName())) {
            throw new BusinessException("工作流名称不能为空");
        }

        // 检查名称是否重复
        if (checkNameExists(createDTO.getAgentId(), createDTO.getName(), null)) {
            throw new BusinessException("工作流名称已存在");
        }

        // 获取下一个版本号
        Integer nextVersion = getMaxVersionByAgentId(createDTO.getAgentId()) + 1;

        // 创建工作流实体
        AgentWorkflow workflow = new AgentWorkflow();
        BeanUtils.copyProperties(createDTO, workflow);
        workflow.setId(UUID.randomUUID().toString());
        workflow.setVersion(nextVersion);
        workflow.setStatus("1"); // 默认启用
        workflow.setIsPublished(false);
        workflow.setLastModified(LocalDateTime.now());
        workflow.setTenantId(SecurityUtils.getCurrentTenantId());
        workflow.setCreateBy(SecurityUtils.getCurrentUserId());
        workflow.setCreateTime(LocalDateTime.now());

        // 保存到数据库
        if (!save(workflow)) {
            throw new BusinessException("创建工作流失败");
        }

        // 保存历史记录
        try {
            String changeSummary = historyService.generateChangeSummary(null, workflow);
            boolean isMajorChange = historyService.isMajorChange(null, workflow);
            historyService.saveHistoryFromWorkflow(workflow, "create", "创建工作流", changeSummary, isMajorChange);
        } catch (Exception e) {
            logger.warn("保存工作流历史记录失败: {}", e.getMessage());
        }

        logger.info("创建工作流成功，ID: {}, 智能体ID: {}, 版本: {}", workflow.getId(), workflow.getAgentId(), workflow.getVersion());
        return workflow.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWorkflow(AgentWorkflowUpdateDTO updateDTO) {
        // 参数验证
        if (updateDTO == null) {
            throw new BusinessException("更新信息不能为空");
        }
        if (!StringUtils.hasText(updateDTO.getId())) {
            throw new BusinessException("工作流ID不能为空");
        }

        // 查询现有工作流
        AgentWorkflow existingWorkflow = getById(updateDTO.getId());
        if (existingWorkflow == null) {
            throw new BusinessException("工作流不存在");
        }

        // 检查租户权限
        if (!existingWorkflow.getTenantId().equals(SecurityUtils.getCurrentTenantId())) {
            throw new BusinessException("无权限操作此工作流");
        }

        // 检查名称是否重复（如果修改了名称）
        if (StringUtils.hasText(updateDTO.getName())
                && !updateDTO.getName().equals(existingWorkflow.getName())
                && checkNameExists(updateDTO.getAgentId(), updateDTO.getName(), updateDTO.getId())) {
            throw new BusinessException("工作流名称已存在");
        }

        // 判断是否需要创建新版本
        boolean createNewVersion = updateDTO.getCreateNewVersion() != null && updateDTO.getCreateNewVersion();

        AgentWorkflow workflow;
        if (createNewVersion) {
            // 创建新版本
            workflow = new AgentWorkflow();
            BeanUtils.copyProperties(existingWorkflow, workflow);
            workflow.setId(UUID.randomUUID().toString());
            workflow.setVersion(getMaxVersionByAgentId(updateDTO.getAgentId()) + 1);
            workflow.setIsPublished(false); // 新版本默认未发布
        } else {
            // 更新现有版本
            workflow = existingWorkflow;
        }

        // 更新字段
        if (StringUtils.hasText(updateDTO.getName())) {
            workflow.setName(updateDTO.getName());
        }
        if (StringUtils.hasText(updateDTO.getDescription())) {
            workflow.setDescription(updateDTO.getDescription());
        }
        if (StringUtils.hasText(updateDTO.getNodesData())) {
            workflow.setNodesData(updateDTO.getNodesData());
        }
        if (StringUtils.hasText(updateDTO.getEdgesData())) {
            workflow.setEdgesData(updateDTO.getEdgesData());
        }
        if (StringUtils.hasText(updateDTO.getViewportConfig())) {
            workflow.setViewportConfig(updateDTO.getViewportConfig());
        }
        if (StringUtils.hasText(updateDTO.getNodeLibrary())) {
            workflow.setNodeLibrary(updateDTO.getNodeLibrary());
        }
        if (StringUtils.hasText(updateDTO.getGlobalVariables())) {
            workflow.setGlobalVariables(updateDTO.getGlobalVariables());
        }
        if (StringUtils.hasText(updateDTO.getOperationType())) {
            workflow.setOperationType(updateDTO.getOperationType());
        }
        if (StringUtils.hasText(updateDTO.getOperationDesc())) {
            workflow.setOperationDesc(updateDTO.getOperationDesc());
        }

        workflow.setLastModified(LocalDateTime.now());
        workflow.setUpdateBy(SecurityUtils.getCurrentUserId());
        workflow.setUpdateTime(LocalDateTime.now());

        // 保存到数据库
        boolean result = createNewVersion ? save(workflow) : updateById(workflow);
        if (!result) {
            throw new BusinessException("更新工作流失败");
        }

        // 保存历史记录
        try {
            String operationType = createNewVersion ? "create_version" : "update";
            String operationDesc = createNewVersion ? "创建新版本" : "更新工作流";
            String changeSummary = historyService.generateChangeSummary(existingWorkflow, workflow);
            boolean isMajorChange = historyService.isMajorChange(existingWorkflow, workflow);
            historyService.saveHistoryFromWorkflow(workflow, operationType, operationDesc, changeSummary, isMajorChange);
        } catch (Exception e) {
            logger.warn("保存工作流历史记录失败: {}", e.getMessage());
        }

        logger.info("更新工作流成功，ID: {}, 智能体ID: {}, 版本: {}, 创建新版本: {}",
                workflow.getId(), workflow.getAgentId(), workflow.getVersion(), createNewVersion);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWorkflow(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("工作流ID不能为空");
        }

        AgentWorkflow workflow = getById(id);
        if (workflow == null) {
            throw new BusinessException("工作流不存在");
        }

        // 检查租户权限
        if (!workflow.getTenantId().equals(SecurityUtils.getCurrentTenantId())) {
            throw new BusinessException("无权限操作此工作流");
        }

        // 逻辑删除
        workflow.setDeleted(1);
        workflow.setUpdateBy(SecurityUtils.getCurrentUserId());
        workflow.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(workflow);
        if (result) {
            logger.info("删除工作流成功，ID: {}", id);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWorkflowBatch(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("工作流ID列表不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        for (String id : ids) {
            AgentWorkflow workflow = getById(id);
            if (workflow != null && workflow.getTenantId().equals(tenantId)) {
                workflow.setDeleted(1);
                workflow.setUpdateBy(currentUserId);
                workflow.setUpdateTime(now);
                updateById(workflow);
            }
        }

        logger.info("批量删除工作流成功，数量: {}", ids.size());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishWorkflow(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        int result = agentWorkflowMapper.updatePublishStatus(id, true, tenantId);

        if (result > 0) {
            logger.info("发布工作流成功，ID: {}", id);
            return true;
        } else {
            throw new BusinessException("发布工作流失败，工作流不存在或无权限");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unpublishWorkflow(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("工作流ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        int result = agentWorkflowMapper.updatePublishStatus(id, false, tenantId);

        if (result > 0) {
            logger.info("取消发布工作流成功，ID: {}", id);
            return true;
        } else {
            throw new BusinessException("取消发布工作流失败，工作流不存在或无权限");
        }
    }

    @Override
    public List<AgentWorkflowVersionVO> getVersionHistory(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            throw new BusinessException("智能体ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return agentWorkflowMapper.selectVersionHistory(agentId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackToVersion(String agentId, Integer version) {
        if (!StringUtils.hasText(agentId)) {
            throw new BusinessException("智能体ID不能为空");
        }
        if (version == null || version <= 0) {
            throw new BusinessException("版本号无效");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();

        // 查询目标版本工作流
        LambdaQueryWrapper<AgentWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentWorkflow::getAgentId, agentId)
                .eq(AgentWorkflow::getVersion, version)
                .eq(AgentWorkflow::getTenantId, tenantId)
                .eq(AgentWorkflow::getDeleted, 0);

        AgentWorkflow targetWorkflow = getOne(queryWrapper);
        if (targetWorkflow == null) {
            throw new BusinessException("目标版本不存在");
        }

        // 创建新版本（基于目标版本）
        AgentWorkflow newWorkflow = new AgentWorkflow();
        BeanUtils.copyProperties(targetWorkflow, newWorkflow);
        newWorkflow.setId(UUID.randomUUID().toString());
        newWorkflow.setVersion(getMaxVersionByAgentId(agentId) + 1);
        newWorkflow.setIsPublished(false); // 回滚版本默认未发布
        newWorkflow.setOperationType("rollback");
        newWorkflow.setOperationDesc("回滚到版本 " + version);
        newWorkflow.setLastModified(LocalDateTime.now());
        newWorkflow.setCreateBy(SecurityUtils.getCurrentUserId());
        newWorkflow.setCreateTime(LocalDateTime.now());
        newWorkflow.setUpdateBy(SecurityUtils.getCurrentUserId());
        newWorkflow.setUpdateTime(LocalDateTime.now());

        boolean result = save(newWorkflow);
        if (result) {
            logger.info("回滚工作流成功，智能体ID: {}, 目标版本: {}, 新版本: {}",
                    agentId, version, newWorkflow.getVersion());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyToNewVersion(String sourceId, String operationDesc) {
        if (!StringUtils.hasText(sourceId)) {
            throw new BusinessException("源工作流ID不能为空");
        }

        AgentWorkflow sourceWorkflow = getById(sourceId);
        if (sourceWorkflow == null) {
            throw new BusinessException("源工作流不存在");
        }

        // 检查租户权限
        if (!sourceWorkflow.getTenantId().equals(SecurityUtils.getCurrentTenantId())) {
            throw new BusinessException("无权限操作此工作流");
        }

        // 创建新版本
        AgentWorkflow newWorkflow = new AgentWorkflow();
        BeanUtils.copyProperties(sourceWorkflow, newWorkflow);
        newWorkflow.setId(UUID.randomUUID().toString());
        newWorkflow.setVersion(getMaxVersionByAgentId(sourceWorkflow.getAgentId()) + 1);
        newWorkflow.setIsPublished(false); // 新版本默认未发布
        newWorkflow.setOperationType("copy");
        newWorkflow.setOperationDesc(StringUtils.hasText(operationDesc) ? operationDesc : "复制版本");
        newWorkflow.setLastModified(LocalDateTime.now());
        newWorkflow.setCreateBy(SecurityUtils.getCurrentUserId());
        newWorkflow.setCreateTime(LocalDateTime.now());
        newWorkflow.setUpdateBy(SecurityUtils.getCurrentUserId());
        newWorkflow.setUpdateTime(LocalDateTime.now());

        boolean result = save(newWorkflow);
        if (!result) {
            throw new BusinessException("复制工作流失败");
        }

        logger.info("复制工作流成功，源ID: {}, 新ID: {}, 新版本: {}",
                sourceId, newWorkflow.getId(), newWorkflow.getVersion());
        return newWorkflow.getId();
    }

    @Override
    public boolean checkNameExists(String agentId, String name, String excludeId) {
        if (!StringUtils.hasText(agentId) || !StringUtils.hasText(name)) {
            return false;
        }

        LambdaQueryWrapper<AgentWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentWorkflow::getAgentId, agentId)
                .eq(AgentWorkflow::getName, name)
                .eq(AgentWorkflow::getTenantId, SecurityUtils.getCurrentTenantId())
                .eq(AgentWorkflow::getDeleted, 0);

        if (StringUtils.hasText(excludeId)) {
            queryWrapper.ne(AgentWorkflow::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public Integer getMaxVersionByAgentId(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            return 0;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return agentWorkflowMapper.selectMaxVersionByAgentId(agentId, tenantId);
    }

    @Override
    public Long countByAgentId(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            return 0L;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return agentWorkflowMapper.countByAgentId(agentId, tenantId);
    }

    @Override
    public Long countVersionsByAgentId(String agentId) {
        if (!StringUtils.hasText(agentId)) {
            return 0L;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return agentWorkflowMapper.countVersionsByAgentId(agentId, tenantId);
    }

    @Override
    public boolean validateWorkflowConfig(String workflowConfig) {
        if (!StringUtils.hasText(workflowConfig)) {
            return true; // 空配置认为是有效的
        }

        try {
            JSON.parseObject(workflowConfig);
            return true;
        } catch (Exception e) {
            logger.warn("工作流配置JSON格式无效: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String exportWorkflowConfig(String id) {
        AgentWorkflowVO workflow = getWorkflowById(id);
        if (workflow == null) {
            throw new BusinessException("工作流不存在");
        }

        // 构建导出配置
        return JSON.toJSONString(workflow);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importWorkflowConfig(String agentId, String configJson) {
        if (!StringUtils.hasText(agentId)) {
            throw new BusinessException("智能体ID不能为空");
        }
        if (!StringUtils.hasText(configJson)) {
            throw new BusinessException("配置JSON不能为空");
        }

        try {
            AgentWorkflowVO importConfig = JSON.parseObject(configJson, AgentWorkflowVO.class);

            // 创建新工作流
            AgentWorkflowCreateDTO createDTO = new AgentWorkflowCreateDTO();
            createDTO.setAgentId(agentId);
            createDTO.setName(importConfig.getName() + "_导入");
            createDTO.setDescription(importConfig.getDescription());
            createDTO.setNodesData(importConfig.getNodesData());
            createDTO.setEdgesData(importConfig.getEdgesData());
            createDTO.setGlobalVariables(importConfig.getGlobalVariables());
            createDTO.setOperationType("import");
            createDTO.setOperationDesc("导入工作流配置");

            return createWorkflow(createDTO);
        } catch (Exception e) {
            logger.error("导入工作流配置失败: {}", e.getMessage(), e);
            throw new BusinessException("导入工作流配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupOldVersions(String agentId, int keepVersions) {
        if (!StringUtils.hasText(agentId) || keepVersions <= 0) {
            return 0;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();

        // 查询所有版本，按版本号降序排列
        LambdaQueryWrapper<AgentWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentWorkflow::getAgentId, agentId)
                .eq(AgentWorkflow::getTenantId, tenantId)
                .eq(AgentWorkflow::getDeleted, 0)
                .orderByDesc(AgentWorkflow::getVersion);

        List<AgentWorkflow> allVersions = list(queryWrapper);

        if (allVersions.size() <= keepVersions) {
            return 0; // 不需要清理
        }

        // 保留最新的版本，删除旧版本
        List<AgentWorkflow> versionsToDelete = allVersions.subList(keepVersions, allVersions.size());

        int deletedCount = 0;
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        for (AgentWorkflow workflow : versionsToDelete) {
            // 不删除已发布的版本
            if (workflow.getIsPublished() != null && workflow.getIsPublished()) {
                continue;
            }

            workflow.setDeleted(1);
            workflow.setUpdateBy(currentUserId);
            workflow.setUpdateTime(now);
            updateById(workflow);
            deletedCount++;
        }

        logger.info("清理旧版本工作流完成，智能体ID: {}, 清理数量: {}", agentId, deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional
    public String realtimeSave(RealtimeSaveDTO saveDTO) {
        String currentUserId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();
        LocalDateTime now = LocalDateTime.now();

        try {
            // 查找当前智能体的最新工作流
            AgentWorkflowVO latestWorkflow = getLatestWorkflowByAgentId(saveDTO.getAgentId());

            if (latestWorkflow == null) {
                // 如果没有工作流，创建新的
                AgentWorkflow newWorkflow = new AgentWorkflow();
                newWorkflow.setId(UUID.randomUUID().toString().replace("-", ""));
                newWorkflow.setAgentId(saveDTO.getAgentId());
                newWorkflow.setName("默认工作流");
                newWorkflow.setDescription("自动创建的工作流");
                newWorkflow.setVersion(1);
                newWorkflow.setNodesData(saveDTO.getNodesData());
                newWorkflow.setEdgesData(saveDTO.getEdgesData());
                newWorkflow.setViewportConfig(saveDTO.getViewportConfig());
                newWorkflow.setNodeLibrary(saveDTO.getNodeLibrary());
                newWorkflow.setGlobalVariables(saveDTO.getGlobalVariables());
                newWorkflow.setStatus("1");
                newWorkflow.setIsPublished(false);
                newWorkflow.setLastModified(now);
                newWorkflow.setOperationType(saveDTO.getOperationType());
                newWorkflow.setOperationDesc(saveDTO.getOperationDesc());
                newWorkflow.setTenantId(tenantId);
                newWorkflow.setCreateBy(currentUserId);
                newWorkflow.setCreateTime(now);
                newWorkflow.setUpdateBy(currentUserId);
                newWorkflow.setUpdateTime(now);
                newWorkflow.setDeleted(0);

                save(newWorkflow);

                // 保存历史记录
                historyService.saveHistoryFromWorkflow(newWorkflow, saveDTO.getOperationType(),
                        saveDTO.getOperationDesc(), "创建新工作流", true);

                logger.info("创建新工作流成功，智能体ID: {}, 工作流ID: {}", saveDTO.getAgentId(), newWorkflow.getId());
                return "创建成功";
            } else {
                // 更新现有工作流
                AgentWorkflow workflow = getById(latestWorkflow.getId());
                if (workflow == null) {
                    throw new BusinessException("工作流不存在");
                }

                // 更新字段
                if (StringUtils.hasText(saveDTO.getNodesData())) {
                    workflow.setNodesData(saveDTO.getNodesData());
                }
                if (StringUtils.hasText(saveDTO.getEdgesData())) {
                    workflow.setEdgesData(saveDTO.getEdgesData());
                }
                if (StringUtils.hasText(saveDTO.getViewportConfig())) {
                    workflow.setViewportConfig(saveDTO.getViewportConfig());
                }
                if (StringUtils.hasText(saveDTO.getNodeLibrary())) {
                    workflow.setNodeLibrary(saveDTO.getNodeLibrary());
                }
                if (StringUtils.hasText(saveDTO.getGlobalVariables())) {
                    workflow.setGlobalVariables(saveDTO.getGlobalVariables());
                }

                workflow.setLastModified(now);
                workflow.setOperationType(saveDTO.getOperationType());
                workflow.setOperationDesc(saveDTO.getOperationDesc());
                workflow.setUpdateBy(currentUserId);
                workflow.setUpdateTime(now);

                updateById(workflow);

                // 保存历史记录
                historyService.saveHistoryFromWorkflow(workflow, saveDTO.getOperationType(),
                        saveDTO.getOperationDesc(), "更新工作流", false);

                logger.info("更新工作流成功，智能体ID: {}, 工作流ID: {}", saveDTO.getAgentId(), workflow.getId());
                return "保存成功";
            }
        } catch (Exception e) {
            logger.error("实时保存工作流失败，智能体ID: {}", saveDTO.getAgentId(), e);
            throw new BusinessException("保存失败: " + e.getMessage());
        }
    }

    @Override
    public String getDefaultNodeLibrary() {
        // 直接返回默认的静态节点库配置
        return getDefaultNodeLibraryFallback();
    }

    /**
     * 获取默认节点库配置（备用方案）
     */
    private String getDefaultNodeLibraryFallback() {
        return """
            {
              "categories": [
                {
                  "name": "基础节点",
                  "icon": "settings",
                  "description": "工作流的基础控制节点",
                  "nodes": [
                    {
                      "type": "start",
                      "label": "开始节点",
                      "icon": "play-circle",
                      "description": "工作流的起始节点",
                      "category": "basic",
                      "handles": {
                        "source": [{"id": "output", "position": "right"}],
                        "target": []
                      },
                      "defaultData": {
                        "label": "开始",
                        "config": {}
                      }
                    },
                    {
                      "type": "end",
                      "label": "结束节点",
                      "icon": "stop-circle",
                      "description": "工作流的结束节点",
                      "category": "basic",
                      "handles": {
                        "source": [],
                        "target": [{"id": "input", "position": "left"}]
                      },
                      "defaultData": {
                        "label": "结束",
                        "config": {}
                      }
                    }
                  ]
                }
              ]
            }
            """;
    }

}
