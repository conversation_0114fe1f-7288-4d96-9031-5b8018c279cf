# XHC AI Plus 插件系统

## 概述

基于 Spring Boot + PF4J + pf4j-spring 的多插件上下文管理系统，支持存储插件、模型插件等不同类型插件的热插拔，实现真正的插件隔离和模块解耦。

## 核心特性

### 🔥 多插件上下文隔离
- 每种插件类型（存储、模型、通知、数据源）都有独立的 ApplicationContext 和 PluginManager
- 避免插件间资源污染和冲突
- 支持插件类型级别的独立管理和重启

### 🚀 热插拔支持
- 支持插件的动态加载、卸载、升级
- 文件监控自动检测插件变更
- 无需重启应用即可更新插件

### 🛠️ 开发生产环境适配
- **开发环境**：直接加载 `plugins/` 目录下的源码项目，支持调试
- **生产环境**：加载 JAR 文件，支持签名验证

### 🔌 统一接口设计
- 同类型插件实现统一接口（如 `IStorageService`、`IModelService`）
- 支持插件优先级和服务发现
- 便于插件替换和扩展

### ⚙️ 配置解耦
- 插件配置在各自插件中管理
- 支持环境变量和外部配置
- 与主程序保持解耦

## 架构设计

```
xhcai-plus/
├── common/common-plugin/           # 插件框架核心
│   ├── core/                      # 核心类型和管理器
│   ├── storage/                   # 存储插件接口
│   ├── model/                     # 模型插件接口
│   ├── base/                      # 插件基础类
│   ├── annotation/                # 插件注解
│   ├── loader/                    # 插件加载器
│   ├── service/                   # 插件服务管理
│   └── config/                    # 自动配置
│
├── plugins/                       # 插件实现目录
│   ├── storage/                   # 存储插件
│   │   └── minio-storage-plugin/  # MinIO 存储插件
│   └── model/                     # 模型插件
│       └── openai-model-plugin/   # OpenAI 模型插件
│
└── admin-api/                     # 主应用
    ├── controller/PluginController.java  # 插件管理API
    └── service/PluginDemoService.java    # 插件使用示例
```

## 插件类型

### 1. 存储插件 (PluginType.STORAGE)
- **接口**：`IStorageService`
- **功能**：文件上传、下载、删除、列表、预签名URL等
- **实现示例**：MinIO、FTP、OSS、本地存储

### 2. 模型插件 (PluginType.MODEL)
- **接口**：`IModelService`
- **功能**：文本生成、嵌入、图像生成、语音转换等
- **实现示例**：OpenAI、Claude、本地模型

### 3. 通知插件 (PluginType.NOTIFICATION)
- **接口**：`INotificationService`
- **功能**：消息发送、模板管理、批量通知等
- **实现示例**：邮件、短信、微信、钉钉

### 4. 数据源插件 (PluginType.DATASOURCE)
- **接口**：`IDatasourceService`
- **功能**：数据查询、同步、转换等
- **实现示例**：MySQL、PostgreSQL、MongoDB、API

## 快速开始

### 1. 在业务代码中使用插件

```java
@Service
public class FileService {
    
    @Autowired
    private PluginServiceManager pluginServiceManager;
    
    public String uploadFile(MultipartFile file) {
        // 获取默认存储服务
        IStorageService storageService = pluginServiceManager.getDefaultStorageService();
        
        // 上传文件
        return storageService.uploadFile("bucket", "object", 
                file.getInputStream(), file.getContentType());
    }
}
```

### 2. 使用模型服务

```java
@Service
public class AIService {
    
    @Autowired
    private PluginServiceManager pluginServiceManager;
    
    public String generateText(String prompt) {
        IModelService modelService = pluginServiceManager.getDefaultModelService();
        
        ModelRequest request = ModelRequest.builder()
                .model("gpt-3.5-turbo")
                .prompt(prompt)
                .maxTokens(1000)
                .build();
        
        ModelResponse response = modelService.generateText(request);
        return response.getChoices().get(0).getMessage().getContent();
    }
}
```

### 3. 插件管理 API

```bash
# 获取插件类型列表
GET /api/plugin/types

# 获取存储插件列表
GET /api/plugin/list/storage

# 上传并加载插件
POST /api/plugin/load/storage
Content-Type: multipart/form-data
file: plugin.jar

# 卸载插件
DELETE /api/plugin/unload/storage/minio-storage

# 重新加载插件
POST /api/plugin/reload/storage/minio-storage

# 检查插件健康状态
GET /api/plugin/health/storage/minio-storage
```

## 插件开发

### 1. 创建插件项目

```xml
<parent>
    <groupId>com.xhcai</groupId>
    <artifactId>xhcai-plus</artifactId>
    <version>1.0.0</version>
</parent>

<artifactId>my-storage-plugin</artifactId>

<dependencies>
    <dependency>
        <groupId>com.xhcai</groupId>
        <artifactId>common-plugin</artifactId>
    </dependency>
</dependencies>
```

### 2. 实现插件主类

```java
public class MyStoragePlugin extends BasePlugin {
    
    public MyStoragePlugin(PluginWrapper wrapper) {
        super(wrapper);
    }
    
    @Override
    protected void doStart() {
        log.info("Starting My Storage Plugin...");
    }
    
    @Override
    protected void doStop() {
        log.info("Stopping My Storage Plugin...");
    }
}
```

### 3. 实现服务接口

```java
@Extension
@PluginService(
    type = PluginType.STORAGE,
    name = "My Storage",
    description = "我的存储服务",
    version = "1.0.0"
)
public class MyStorageService implements IStorageService {
    
    @Override
    public String getServiceName() {
        return "My Storage";
    }
    
    @Override
    public void initialize(Map<String, Object> config) {
        // 初始化逻辑
    }
    
    // 实现其他接口方法...
}
```

## 配置说明

```yaml
xhcai:
  plugin:
    root-path: plugins          # 插件根目录
    dev-mode: true             # 开发模式
    hot-swap-enabled: true     # 启用热插拔
    
    types:
      storage:
        enabled: true
        config:
          minio:
            endpoint: http://localhost:9000
            access-key: minioadmin
            secret-key: minioadmin
      
      model:
        enabled: true
        config:
          openai:
            api-key: ${OPENAI_API_KEY}
            base-url: https://api.openai.com/v1
```

## 部署方式

### 开发环境
1. 将插件项目放在 `plugins/{type}/` 目录下
2. 插件会自动被检测和加载
3. 支持热重载和调试

### 生产环境
1. 使用 `mvn package` 打包插件为 JAR 文件
2. 将 JAR 文件放入 `plugins/{type}/` 目录
3. 插件会自动被检测和加载

## 核心组件

### PluginContextManager
- 管理多个独立的插件上下文
- 为每种插件类型创建独立的 PluginManager 和 ApplicationContext
- 支持上下文的启动、停止、重启

### HotSwapPluginManager
- 实现插件的热插拔功能
- 文件监控自动检测插件变更
- 支持插件的动态加载、卸载、升级

### PluginServiceManager
- 提供统一的插件服务访问接口
- 支持服务发现和负载均衡
- 管理插件服务的生命周期

### PluginLoader
- 负责插件的加载和卸载
- 处理插件依赖关系
- 管理插件状态和元数据

## 最佳实践

1. **插件隔离**：确保插件之间不直接依赖
2. **配置外部化**：使用环境变量和配置文件
3. **错误处理**：优雅处理插件错误，不影响主应用
4. **资源管理**：插件停止时正确释放资源
5. **版本兼容**：注意插件与平台的版本兼容性
6. **测试覆盖**：编写充分的单元测试和集成测试

## 示例插件

### MinIO 存储插件
- 位置：`plugins/storage/minio-storage-plugin/`
- 功能：提供 MinIO 对象存储服务
- 配置：endpoint、access-key、secret-key

### OpenAI 模型插件
- 位置：`plugins/model/openai-model-plugin/`
- 功能：提供 OpenAI API 模型服务
- 配置：api-key、base-url

## 扩展指南

详细的插件开发和使用指南请参考：[docs/plugin-system.md](../../docs/plugin-system.md)
