package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.SearchRequest;
import com.xhcai.modules.rag.dto.SearchResult;
import com.xhcai.modules.rag.entity.DocumentSegment;
import com.xhcai.modules.rag.service.IRetrievalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检索控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "检索管理", description = "向量检索和智能问答功能")
@RestController
@RequestMapping("/api/rag/retrieval")
@Validated
public class RetrievalController {

    private static final Logger log = LoggerFactory.getLogger(RetrievalController.class);

    @Autowired
    private IRetrievalService retrievalService;

    @Operation(summary = "向量检索", description = "基于向量相似度的语义检索")
    @PostMapping("/search")
    @RequiresPermissions("rag:search")
    public Result<SearchResult> search(@Parameter(description = "检索请求") @RequestBody SearchRequest request) {
        log.info("执行向量检索: {}", request);

        SearchResult result = retrievalService.vectorSearch(request);

        return Result.success(result);
    }

    @Operation(summary = "关键字检索", description = "基于关键字的全文检索")
    @PostMapping("/keyword-search")
    @RequiresPermissions("rag:search")
    public Result<SearchResult> keywordSearch(@Parameter(description = "检索请求") @RequestBody SearchRequest request) {
        log.info("执行关键字检索: {}", request);

        request.setSearchType("keyword");
        SearchResult result = retrievalService.keywordSearch(request);

        return Result.success(result);
    }

    @Operation(summary = "混合检索", description = "结合向量检索和关键字检索的混合检索")
    @PostMapping("/hybrid-search")
    @RequiresPermissions("rag:search")
    public Result<SearchResult> hybridSearch(@Parameter(description = "检索请求") @RequestBody SearchRequest request) {
        log.info("执行混合检索: {}", request);

        request.setSearchType("hybrid");
        SearchResult result = retrievalService.hybridSearch(request);

        return Result.success(result);
    }

    @Operation(summary = "智能问答", description = "基于检索增强生成的智能问答")
    @PostMapping("/qa")
    @RequiresPermissions("rag:search")
    public Result<Object> qa(@Parameter(description = "问答请求") @RequestBody Map<String, Object> request) {
        log.info("执行智能问答: {}", request);

        String datasetId = (String) request.get("datasetId");
        String question = (String) request.get("question");
        Integer topK = (Integer) request.getOrDefault("topK", 5);
        String modelId = (String) request.get("modelId");

        Map<String, Object> response = retrievalService.qa(datasetId, question, topK, modelId);

        return Result.success(response);
    }

    @Operation(summary = "获取相似文档", description = "获取与指定文档相似的其他文档")
    @GetMapping("/similar-documents/{documentId}")
    @RequiresPermissions("rag:search")
    public Result<Object> getSimilarDocuments(
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "5") Integer topK,
            @Parameter(description = "相似度阈值") @RequestParam(defaultValue = "0.7") Double threshold) {
        log.info("获取相似文档: documentId={}, topK={}, threshold={}", documentId, topK, threshold);
        List<DocumentSegment> similarDocuments = retrievalService.getSimilarDocuments(documentId, topK, threshold);

        Map<String, Object> response = new HashMap<>();
        response.put("documentId", documentId);
        response.put("similarDocuments", similarDocuments);
        response.put("totalCount", similarDocuments.size());

        return Result.success(response);
    }

    @Operation(summary = "获取推荐内容", description = "基于用户行为和内容相似度的推荐")
    @GetMapping("/recommendations")
    @RequiresPermissions("rag:search")
    public Result<Object> getRecommendations(
            @Parameter(description = "知识库ID") @RequestParam String datasetId,
            @Parameter(description = "用户ID") @RequestParam String userId,
            @Parameter(description = "推荐数量") @RequestParam(defaultValue = "10") Integer limit) {
        log.info("获取推荐内容: datasetId={}, userId={}, limit={}", datasetId, userId, limit);

        List<DocumentSegment> recommendations = retrievalService.getRecommendations(datasetId, userId, limit);

        Map<String, Object> response = new HashMap<>();
        response.put("datasetId", datasetId);
        response.put("userId", userId);
        response.put("recommendations", recommendations);
        response.put("totalCount", recommendations.size());

        return Result.success(response);
    }

    @Operation(summary = "检索统计", description = "获取检索相关的统计信息")
    @GetMapping("/stats")
    @RequiresPermissions("rag:search")
    public Result<Object> getStats(
            @Parameter(description = "知识库ID") @RequestParam(required = false) String datasetId,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime) {
        log.info("获取检索统计: datasetId={}, startTime={}, endTime={}", datasetId, startTime, endTime);

        Map<String, Object> stats = retrievalService.getSearchStats(datasetId, startTime, endTime);

        return Result.success(stats);
    }

    @Operation(summary = "测试检索配置", description = "测试知识库的检索配置是否正常")
    @PostMapping("/test-config")
    @RequiresPermissions("rag:search")
    public Result<Object> testConfig(@Parameter(description = "测试请求") @RequestBody Map<String, Object> request) {
        log.info("测试检索配置: {}", request);

        String datasetId = (String) request.get("datasetId");
        String testQuery = (String) request.getOrDefault("testQuery", "测试查询");

        Map<String, Object> response = retrievalService.testSearchConfig(datasetId, testQuery);

        return Result.success(response);
    }
}
