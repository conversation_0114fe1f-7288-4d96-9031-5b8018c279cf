package com.xhcai.modules.rag.plugins.rabbitmq.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.rag.plugins.rabbitmq.model.MessageType;
import com.xhcai.modules.rag.plugins.rabbitmq.util.RabbitMQUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/rabbitmq")
@Tag(name = "RabbitMQ管理", description = "RabbitMQ消息队列管理接口")
@ConditionalOnProperty(prefix = "xhcai.plugin.types.queue.config", name = "type", havingValue = "rabbitmq")
public class RabbitMQController {

    @Autowired
    private RabbitMQUtil rabbitMQUtil;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired(required = false)
    private RabbitAdmin rabbitAdmin;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "RabbitMQ健康检查", description = "检查RabbitMQ连接状态和队列状态")
    public Result<Map<String, Object>> health() {
        try {
            Map<String, Object> healthInfo = new HashMap<>();
            healthInfo.put("timestamp", LocalDateTime.now());
            healthInfo.put("service", "rabbitmq");

            // 检查连接状态
            boolean connectionHealthy = checkConnection();
            healthInfo.put("connectionHealthy", connectionHealthy);

            // 检查队列状态
            Map<String, Object> queueStatus = checkQueues();
            healthInfo.put("queues", queueStatus);

            // 发送健康检查消息
            rabbitMQUtil.sendHealthCheckMessage();

            if (connectionHealthy) {
                healthInfo.put("status", "healthy");
                return Result.success("RabbitMQ健康检查通过", healthInfo);
            } else {
                healthInfo.put("status", "unhealthy");
                return Result.fail("RabbitMQ健康检查失败");
            }

        } catch (Exception e) {
            log.error("RabbitMQ健康检查异常", e);
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("timestamp", LocalDateTime.now());
            errorInfo.put("status", "error");
            errorInfo.put("error", e.getMessage());
            return Result.fail("RabbitMQ健康检查异常: " + e.getMessage());
        }
    }

    /**
     * 检查连接状态
     */
    private boolean checkConnection() {
        try {
            // 尝试获取连接信息
            rabbitTemplate.execute(channel -> {
                return channel.isOpen();
            });
            return true;
        } catch (Exception e) {
            log.error("RabbitMQ连接检查失败", e);
            return false;
        }
    }

    /**
     * 检查队列状态
     */
    private Map<String, Object> checkQueues() {
        Map<String, Object> queueStatus = new HashMap<>();

        try {
            if (rabbitAdmin != null) {
                // 检查各个队列的状态
                queueStatus.put("embeddingProcessing", checkQueueExists(MessageType.EMBEDDING_PROCESSING.getQueueName()));
                queueStatus.put("notification", checkQueueExists(MessageType.NOTIFICATION.getQueueName()));
                queueStatus.put("deadLetter", checkQueueExists(MessageType.DEAD_LETTER.getQueueName()));
            } else {
                queueStatus.put("error", "RabbitAdmin not available");
            }
        } catch (Exception e) {
            log.error("检查队列状态失败", e);
            queueStatus.put("error", e.getMessage());
        }

        return queueStatus;
    }

    /**
     * 检查队列是否存在
     */
    private boolean checkQueueExists(String queueName) {
        try {
            return rabbitAdmin.getQueueProperties(queueName) != null;
        } catch (Exception e) {
            log.warn("检查队列{}失败: {}", queueName, e.getMessage());
            return false;
        }
    }

    // ==================== 状态统计API ====================
    /**
     * 获取连接状态
     */
    @GetMapping("/status/connections")
    @Operation(summary = "获取连接状态", description = "获取RabbitMQ连接状态统计")
    public Result<Map<String, Object>> getConnectionStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("active", checkConnection() ? 1 : 0);
            status.put("total", 1);
            status.put("healthy", checkConnection());
            return Result.success("获取连接状态成功", status);
        } catch (Exception e) {
            log.error("获取连接状态失败", e);
            return Result.fail("获取连接状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列统计
     */
    @GetMapping("/status/queues")
    @Operation(summary = "获取队列统计", description = "获取队列状态统计信息")
    public Result<Map<String, Object>> getQueueStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            Map<String, Object> queueStatus = checkQueues();

            int total = 0;
            int active = 0;
            for (Object value : queueStatus.values()) {
                if (value instanceof Boolean) {
                    total++;
                    if ((Boolean) value) {
                        active++;
                    }
                }
            }

            stats.put("total", total);
            stats.put("active", active);
            stats.put("messages", 0); // TODO: 实现消息数量统计

            return Result.success("获取队列统计成功", stats);
        } catch (Exception e) {
            log.error("获取队列统计失败", e);
            return Result.fail("获取队列统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取消息统计
     */
    @GetMapping("/status/messages")
    @Operation(summary = "获取消息统计", description = "获取消息处理统计信息")
    public Result<Map<String, Object>> getMessageStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("pending", 0); // TODO: 实现待处理消息统计
            stats.put("processed", 0); // TODO: 实现已处理消息统计
            stats.put("failed", 0); // TODO: 实现失败消息统计
            stats.put("rate", 0.0); // TODO: 实现消息处理速率统计

            return Result.success("获取消息统计成功", stats);
        } catch (Exception e) {
            log.error("获取消息统计失败", e);
            return Result.fail("获取消息统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取交换机统计
     */
    @GetMapping("/status/exchanges")
    @Operation(summary = "获取交换机统计", description = "获取交换机状态统计信息")
    public Result<Map<String, Object>> getExchangeStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("total", 4); // TODO: 实现交换机数量统计
            stats.put("active", 4); // TODO: 实现活跃交换机统计

            return Result.success("获取交换机统计成功", stats);
        } catch (Exception e) {
            log.error("获取交换机统计失败", e);
            return Result.fail("获取交换机统计失败: " + e.getMessage());
        }
    }

    // ==================== 连接管理API ====================
    /**
     * 获取连接列表
     */
    @GetMapping("/connections")
    @Operation(summary = "获取连接列表", description = "获取所有RabbitMQ连接信息")
    public Result<java.util.List<Map<String, Object>>> getConnections() {
        try {
            java.util.List<Map<String, Object>> connections = new java.util.ArrayList<>();

            // TODO: 实现获取实际连接信息
            Map<String, Object> connection = new HashMap<>();
            connection.put("id", "connection-1");
            connection.put("name", "default-connection");
            connection.put("host", "localhost");
            connection.put("port", 5672);
            connection.put("vhost", "/");
            connection.put("user", "guest");
            connection.put("status", checkConnection() ? "connected" : "disconnected");
            connection.put("channels", 1);
            connection.put("lastActivity", LocalDateTime.now());
            connection.put("createTime", LocalDateTime.now());

            connections.add(connection);

            return Result.success("获取连接列表成功", connections);
        } catch (Exception e) {
            log.error("获取连接列表失败", e);
            return Result.fail("获取连接列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取连接详情
     */
    @GetMapping("/connections/{connectionId}")
    @Operation(summary = "获取连接详情", description = "获取指定连接的详细信息")
    public Result<Map<String, Object>> getConnectionDetail(
            @Parameter(description = "连接ID") @PathVariable String connectionId) {
        try {
            // TODO: 实现获取指定连接详情
            Map<String, Object> connection = new HashMap<>();
            connection.put("id", connectionId);
            connection.put("name", "default-connection");
            connection.put("host", "localhost");
            connection.put("port", 5672);
            connection.put("vhost", "/");
            connection.put("user", "guest");
            connection.put("status", checkConnection() ? "connected" : "disconnected");
            connection.put("channels", 1);
            connection.put("lastActivity", LocalDateTime.now());
            connection.put("createTime", LocalDateTime.now());

            return Result.success("获取连接详情成功", connection);
        } catch (Exception e) {
            log.error("获取连接详情失败", e);
            return Result.fail("获取连接详情失败: " + e.getMessage());
        }
    }

    /**
     * 关闭连接
     */
    @DeleteMapping("/connections/{connectionId}")
    @Operation(summary = "关闭连接", description = "关闭指定的RabbitMQ连接")
    public Result<Void> closeConnection(
            @Parameter(description = "连接ID") @PathVariable String connectionId) {
        try {
            // TODO: 实现关闭指定连接
            log.info("关闭连接: {}", connectionId);
            return Result.success("连接关闭成功");
        } catch (Exception e) {
            log.error("关闭连接失败", e);
            return Result.fail("关闭连接失败: " + e.getMessage());
        }
    }

    // ==================== 队列管理API ====================
    /**
     * 获取队列列表
     */
    @GetMapping("/queues")
    @Operation(summary = "获取队列列表", description = "获取所有RabbitMQ队列信息")
    public Result<java.util.List<Map<String, Object>>> getQueues() {
        try {
            java.util.List<Map<String, Object>> queues = new java.util.ArrayList<>();

            // TODO: 实现获取实际队列信息
            Map<String, Object> queue1 = new HashMap<>();
            queue1.put("name", MessageType.DOCUMENT_SEGMENTATION.getRoutingKey());
            queue1.put("vhost", "/");
            queue1.put("durable", true);
            queue1.put("autoDelete", false);
            queue1.put("exclusive", false);
            queue1.put("messages", 0);
            queue1.put("consumers", 1);
            queue1.put("memory", 1024);
            queue1.put("status", "running");
            queue1.put("node", "rabbit@localhost");
            queues.add(queue1);

            Map<String, Object> queue2 = new HashMap<>();
            queue2.put("name", "embedding.processing");
            queue2.put("vhost", "/");
            queue2.put("durable", true);
            queue2.put("autoDelete", false);
            queue2.put("exclusive", false);
            queue2.put("messages", 5);
            queue2.put("consumers", 2);
            queue2.put("memory", 2048);
            queue2.put("status", "running");
            queue2.put("node", "rabbit@localhost");
            queues.add(queue2);

            return Result.success("获取队列列表成功", queues);
        } catch (Exception e) {
            log.error("获取队列列表失败", e);
            return Result.fail("获取队列列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列详情
     */
    @GetMapping("/queues/{queueName}")
    @Operation(summary = "获取队列详情", description = "获取指定队列的详细信息")
    public Result<Map<String, Object>> getQueueDetail(
            @Parameter(description = "队列名称") @PathVariable String queueName) {
        try {
            // TODO: 实现获取指定队列详情
            Map<String, Object> queue = new HashMap<>();
            queue.put("name", queueName);
            queue.put("vhost", "/");
            queue.put("durable", true);
            queue.put("autoDelete", false);
            queue.put("exclusive", false);
            queue.put("messages", 0);
            queue.put("consumers", 1);
            queue.put("memory", 1024);
            queue.put("status", "running");
            queue.put("node", "rabbit@localhost");

            return Result.success("获取队列详情成功", queue);
        } catch (Exception e) {
            log.error("获取队列详情失败", e);
            return Result.fail("获取队列详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建队列
     */
    @PostMapping("/queues")
    @Operation(summary = "创建队列", description = "创建新的RabbitMQ队列")
    public Result<Map<String, Object>> createQueue(@RequestBody Map<String, Object> queueInfo) {
        try {
            // TODO: 实现创建队列
            log.info("创建队列: {}", queueInfo);
            return Result.success("队列创建成功", queueInfo);
        } catch (Exception e) {
            log.error("创建队列失败", e);
            return Result.fail("创建队列失败: " + e.getMessage());
        }
    }

    /**
     * 删除队列
     */
    @DeleteMapping("/queues/{queueName}")
    @Operation(summary = "删除队列", description = "删除指定的RabbitMQ队列")
    public Result<Void> deleteQueue(
            @Parameter(description = "队列名称") @PathVariable String queueName,
            @Parameter(description = "仅在未使用时删除") @RequestParam(required = false) Boolean ifUnused,
            @Parameter(description = "仅在为空时删除") @RequestParam(required = false) Boolean ifEmpty) {
        try {
            // TODO: 实现删除队列
            log.info("删除队列: {}, ifUnused: {}, ifEmpty: {}", queueName, ifUnused, ifEmpty);
            return Result.success("队列删除成功");
        } catch (Exception e) {
            log.error("删除队列失败", e);
            return Result.fail("删除队列失败: " + e.getMessage());
        }
    }

    /**
     * 清空队列
     */
    @PostMapping("/queues/{queueName}/purge")
    @Operation(summary = "清空队列", description = "清空指定队列中的所有消息")
    public Result<Void> purgeQueue(
            @Parameter(description = "队列名称") @PathVariable String queueName) {
        try {
            // TODO: 实现清空队列
            log.info("清空队列: {}", queueName);
            return Result.success("队列清空成功");
        } catch (Exception e) {
            log.error("清空队列失败", e);
            return Result.fail("清空队列失败: " + e.getMessage());
        }
    }

    // ==================== 交换机管理API ====================
    /**
     * 获取交换机列表
     */
    @GetMapping("/exchanges")
    @Operation(summary = "获取交换机列表", description = "获取所有RabbitMQ交换机信息")
    public Result<java.util.List<Map<String, Object>>> getExchanges() {
        try {
            java.util.List<Map<String, Object>> exchanges = new java.util.ArrayList<>();

            // TODO: 实现获取实际交换机信息
            Map<String, Object> exchange1 = new HashMap<>();
            exchange1.put("name", "document.exchange");
            exchange1.put("vhost", "/");
            exchange1.put("type", "direct");
            exchange1.put("durable", true);
            exchange1.put("autoDelete", false);
            exchange1.put("internal", false);
            exchanges.add(exchange1);

            Map<String, Object> exchange2 = new HashMap<>();
            exchange2.put("name", "embedding.exchange");
            exchange2.put("vhost", "/");
            exchange2.put("type", "topic");
            exchange2.put("durable", true);
            exchange2.put("autoDelete", false);
            exchange2.put("internal", false);
            exchanges.add(exchange2);

            Map<String, Object> exchange3 = new HashMap<>();
            exchange3.put("name", "notification.exchange");
            exchange3.put("vhost", "/");
            exchange3.put("type", "fanout");
            exchange3.put("durable", true);
            exchange3.put("autoDelete", false);
            exchange3.put("internal", false);
            exchanges.add(exchange3);

            Map<String, Object> exchange4 = new HashMap<>();
            exchange4.put("name", "dlx.exchange");
            exchange4.put("vhost", "/");
            exchange4.put("type", "direct");
            exchange4.put("durable", true);
            exchange4.put("autoDelete", false);
            exchange4.put("internal", false);
            exchanges.add(exchange4);

            return Result.success("获取交换机列表成功", exchanges);
        } catch (Exception e) {
            log.error("获取交换机列表失败", e);
            return Result.fail("获取交换机列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取交换机详情
     */
    @GetMapping("/exchanges/{exchangeName}")
    @Operation(summary = "获取交换机详情", description = "获取指定交换机的详细信息")
    public Result<Map<String, Object>> getExchangeDetail(
            @Parameter(description = "交换机名称") @PathVariable String exchangeName) {
        try {
            // TODO: 实现获取指定交换机详情
            Map<String, Object> exchange = new HashMap<>();
            exchange.put("name", exchangeName);
            exchange.put("vhost", "/");
            exchange.put("type", "direct");
            exchange.put("durable", true);
            exchange.put("autoDelete", false);
            exchange.put("internal", false);

            return Result.success("获取交换机详情成功", exchange);
        } catch (Exception e) {
            log.error("获取交换机详情失败", e);
            return Result.fail("获取交换机详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建交换机
     */
    @PostMapping("/exchanges")
    @Operation(summary = "创建交换机", description = "创建新的RabbitMQ交换机")
    public Result<Map<String, Object>> createExchange(@RequestBody Map<String, Object> exchangeInfo) {
        try {
            // TODO: 实现创建交换机
            log.info("创建交换机: {}", exchangeInfo);
            return Result.success("交换机创建成功", exchangeInfo);
        } catch (Exception e) {
            log.error("创建交换机失败", e);
            return Result.fail("创建交换机失败: " + e.getMessage());
        }
    }

    /**
     * 删除交换机
     */
    @DeleteMapping("/exchanges/{exchangeName}")
    @Operation(summary = "删除交换机", description = "删除指定的RabbitMQ交换机")
    public Result<Void> deleteExchange(
            @Parameter(description = "交换机名称") @PathVariable String exchangeName,
            @Parameter(description = "仅在未使用时删除") @RequestParam(required = false) Boolean ifUnused) {
        try {
            // TODO: 实现删除交换机
            log.info("删除交换机: {}, ifUnused: {}", exchangeName, ifUnused);
            return Result.success("交换机删除成功");
        } catch (Exception e) {
            log.error("删除交换机失败", e);
            return Result.fail("删除交换机失败: " + e.getMessage());
        }
    }

    // ==================== 绑定管理API ====================
    /**
     * 获取绑定列表
     */
    @GetMapping("/bindings")
    @Operation(summary = "获取绑定列表", description = "获取所有RabbitMQ绑定信息")
    public Result<java.util.List<Map<String, Object>>> getBindings() {
        try {
            java.util.List<Map<String, Object>> bindings = new java.util.ArrayList<>();

            // TODO: 实现获取实际绑定信息
            Map<String, Object> binding1 = new HashMap<>();
            binding1.put("source", "document.exchange");
            binding1.put("destination", MessageType.DOCUMENT_SEGMENTATION.getRoutingKey());
            binding1.put("destinationType", "queue");
            binding1.put("routingKey", "document.upload");
            bindings.add(binding1);

            Map<String, Object> binding2 = new HashMap<>();
            binding2.put("source", "embedding.exchange");
            binding2.put("destination", "embedding.processing");
            binding2.put("destinationType", "queue");
            binding2.put("routingKey", "embedding.process");
            bindings.add(binding2);

            return Result.success("获取绑定列表成功", bindings);
        } catch (Exception e) {
            log.error("获取绑定列表失败", e);
            return Result.fail("获取绑定列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建绑定
     */
    @PostMapping("/bindings")
    @Operation(summary = "创建绑定", description = "创建新的RabbitMQ绑定")
    public Result<Void> createBinding(@RequestBody Map<String, Object> bindingInfo) {
        try {
            // TODO: 实现创建绑定
            log.info("创建绑定: {}", bindingInfo);
            return Result.success("绑定创建成功");
        } catch (Exception e) {
            log.error("创建绑定失败", e);
            return Result.fail("创建绑定失败: " + e.getMessage());
        }
    }

    /**
     * 删除绑定
     */
    @DeleteMapping("/bindings")
    @Operation(summary = "删除绑定", description = "删除指定的RabbitMQ绑定")
    public Result<Void> deleteBinding(@RequestBody Map<String, Object> bindingInfo) {
        try {
            // TODO: 实现删除绑定
            log.info("删除绑定: {}", bindingInfo);
            return Result.success("绑定删除成功");
        } catch (Exception e) {
            log.error("删除绑定失败", e);
            return Result.fail("删除绑定失败: " + e.getMessage());
        }
    }

    // ==================== 消息管理API ====================
    /**
     * 获取消息列表
     */
    @GetMapping("/messages")
    @Operation(summary = "获取消息列表", description = "获取RabbitMQ消息信息")
    public Result<java.util.List<Map<String, Object>>> getMessages(
            @Parameter(description = "队列名称") @RequestParam(required = false) String queue,
            @Parameter(description = "限制数量") @RequestParam(required = false, defaultValue = "100") Integer limit) {
        try {
            java.util.List<Map<String, Object>> messages = new java.util.ArrayList<>();

            // TODO: 实现获取实际消息信息
            for (int i = 1; i <= Math.min(limit, 10); i++) {
                Map<String, Object> message = new HashMap<>();
                message.put("id", "msg-" + i);
                message.put("messageId", "message-id-" + i);
                message.put("messageType", MessageType.DOCUMENT_SEGMENTATION.getRoutingKey());
                message.put("payload", Map.of("documentId", "doc-" + i, "action", "process"));
                message.put("properties", Map.of("priority", 1, "timestamp", System.currentTimeMillis()));
                message.put("status", i % 3 == 0 ? "failed" : (i % 2 == 0 ? "delivered" : "sent"));
                message.put("queue", queue != null ? queue : MessageType.DOCUMENT_SEGMENTATION.getRoutingKey());
                message.put("exchange", "document.exchange");
                message.put("routingKey", "document.upload");
                message.put("priority", 1);
                message.put("retryCount", i % 3);
                message.put("createTime", LocalDateTime.now().minusMinutes(i * 5));
                if (i % 3 == 0) {
                    message.put("errorMessage", "Processing failed");
                }
                messages.add(message);
            }

            return Result.success("获取消息列表成功", messages);
        } catch (Exception e) {
            log.error("获取消息列表失败", e);
            return Result.fail("获取消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 发送测试消息
     */
    @PostMapping("/test/send")
    @Operation(summary = "发送测试消息", description = "发送测试消息到指定队列")
    public Result<Map<String, Object>> sendTestMessage(@RequestBody Map<String, Object> request) {
        try {
            String messageType = (String) request.get("messageType");
            String content = (String) request.get("content");
            String tenantId = (String) request.get("tenantId");
            String userId = (String) request.get("userId");

            // TODO: 实现发送测试消息
            log.info("发送测试消息: type={}, content={}, tenantId={}, userId={}",
                    messageType, content, tenantId, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("messageId", "test-msg-" + System.currentTimeMillis());
            result.put("status", "sent");
            result.put("timestamp", LocalDateTime.now());

            return Result.success("测试消息发送成功", result);
        } catch (Exception e) {
            log.error("发送测试消息失败", e);
            return Result.fail("发送测试消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送文档处理任务
     */
    @PostMapping("/task/document")
    @Operation(summary = "发送文档处理任务", description = "发送文档处理任务到队列")
    public Result<Map<String, Object>> sendDocumentTask(@RequestBody Map<String, Object> request) {
        try {
            String documentId = (String) request.get("documentId");
            String tenantId = (String) request.get("tenantId");
            String userId = (String) request.get("userId");

            // TODO: 实现发送文档处理任务
            log.info("发送文档处理任务: documentId={}, tenantId={}, userId={}",
                    documentId, tenantId, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", "doc-task-" + System.currentTimeMillis());
            result.put("status", "queued");
            result.put("timestamp", LocalDateTime.now());

            return Result.success("文档处理任务发送成功", result);
        } catch (Exception e) {
            log.error("发送文档处理任务失败", e);
            return Result.fail("发送文档处理任务失败: " + e.getMessage());
        }
    }

    /**
     * 发送向量化处理任务
     */
    @PostMapping("/task/embedding")
    @Operation(summary = "发送向量化处理任务", description = "发送向量化处理任务到队列")
    public Result<Map<String, Object>> sendEmbeddingTask(@RequestBody Map<String, Object> request) {
        try {
            String segmentId = (String) request.get("segmentId");
            String tenantId = (String) request.get("tenantId");
            String userId = (String) request.get("userId");

            // TODO: 实现发送向量化处理任务
            log.info("发送向量化处理任务: segmentId={}, tenantId={}, userId={}",
                    segmentId, tenantId, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", "emb-task-" + System.currentTimeMillis());
            result.put("status", "queued");
            result.put("timestamp", LocalDateTime.now());

            return Result.success("向量化处理任务发送成功", result);
        } catch (Exception e) {
            log.error("发送向量化处理任务失败", e);
            return Result.fail("发送向量化处理任务失败: " + e.getMessage());
        }
    }

    /**
     * 发送通知消息
     */
    @PostMapping("/notification")
    @Operation(summary = "发送通知消息", description = "发送通知消息到队列")
    public Result<Map<String, Object>> sendNotification(@RequestBody Map<String, Object> request) {
        try {
            String title = (String) request.get("title");
            String content = (String) request.get("content");
            String type = (String) request.get("type");
            String tenantId = (String) request.get("tenantId");
            String userId = (String) request.get("userId");

            // TODO: 实现发送通知消息
            log.info("发送通知消息: title={}, content={}, type={}, tenantId={}, userId={}",
                    title, content, type, tenantId, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("notificationId", "notif-" + System.currentTimeMillis());
            result.put("status", "sent");
            result.put("timestamp", LocalDateTime.now());

            return Result.success("通知消息发送成功", result);
        } catch (Exception e) {
            log.error("发送通知消息失败", e);
            return Result.fail("发送通知消息失败: " + e.getMessage());
        }
    }

    // ==================== 监控和统计API ====================
    /**
     * 获取队列信息
     */
    @GetMapping("/queues/info")
    @Operation(summary = "获取队列信息", description = "获取队列详细信息统计")
    public Result<Map<String, Object>> getQueuesInfo() {
        try {
            Map<String, Object> info = new HashMap<>();

            // TODO: 实现获取实际队列信息
            Map<String, Object> queueInfo = new HashMap<>();
            queueInfo.put(MessageType.DOCUMENT_SEGMENTATION.getRoutingKey(), Map.of(
                    "messages", 0,
                    "consumers", 1,
                    "memory", 1024,
                    "messageRate", 0.5,
                    "deliveryRate", 0.5
            ));
            queueInfo.put(MessageType.EMBEDDING_PROCESSING.getRoutingKey(), Map.of(
                    "messages", 5,
                    "consumers", 2,
                    "memory", 2048,
                    "messageRate", 1.2,
                    "deliveryRate", 1.1
            ));

            info.put("queues", queueInfo);
            info.put("timestamp", LocalDateTime.now());

            return Result.success("获取队列信息成功", info);
        } catch (Exception e) {
            log.error("获取队列信息失败", e);
            return Result.fail("获取队列信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取性能指标
     */
    @GetMapping("/metrics/performance")
    @Operation(summary = "获取性能指标", description = "获取RabbitMQ性能指标")
    public Result<Map<String, Object>> getPerformanceMetrics() {
        try {
            Map<String, Object> metrics = new HashMap<>();

            // TODO: 实现获取实际性能指标
            metrics.put("messageRate", 2.5);
            metrics.put("deliveryRate", 2.3);
            metrics.put("ackRate", 2.2);
            metrics.put("publishRate", 2.5);
            metrics.put("consumerUtilization", 0.75);
            metrics.put("memoryUsage", 0.45);
            metrics.put("diskUsage", 0.23);
            metrics.put("connectionCount", 3);
            metrics.put("channelCount", 8);
            metrics.put("timestamp", LocalDateTime.now());

            return Result.success("获取性能指标成功", metrics);
        } catch (Exception e) {
            log.error("获取性能指标失败", e);
            return Result.fail("获取性能指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取消息流量统计
     */
    @GetMapping("/metrics/flow")
    @Operation(summary = "获取消息流量统计", description = "获取消息流量统计信息")
    public Result<Map<String, Object>> getMessageFlowStats(
            @Parameter(description = "时间范围") @RequestParam(required = false, defaultValue = "1h") String timeRange) {
        try {
            Map<String, Object> stats = new HashMap<>();

            // TODO: 实现获取实际消息流量统计
            java.util.List<Map<String, Object>> flowData = new java.util.ArrayList<>();
            for (int i = 0; i < 24; i++) {
                Map<String, Object> point = new HashMap<>();
                point.put("timestamp", LocalDateTime.now().minusHours(23 - i));
                point.put("published", Math.random() * 100);
                point.put("delivered", Math.random() * 95);
                point.put("acked", Math.random() * 90);
                point.put("failed", Math.random() * 5);
                flowData.add(point);
            }

            stats.put("timeRange", timeRange);
            stats.put("data", flowData);
            stats.put("summary", Map.of(
                    "totalPublished", 2400,
                    "totalDelivered", 2280,
                    "totalAcked", 2160,
                    "totalFailed", 120
            ));

            return Result.success("获取消息流量统计成功", stats);
        } catch (Exception e) {
            log.error("获取消息流量统计失败", e);
            return Result.fail("获取消息流量统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取错误统计
     */
    @GetMapping("/metrics/errors")
    @Operation(summary = "获取错误统计", description = "获取错误统计信息")
    public Result<Map<String, Object>> getErrorStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // TODO: 实现获取实际错误统计
            stats.put("totalErrors", 25);
            stats.put("errorRate", 0.05);
            stats.put("errorsByType", Map.of(
                    "connection_error", 5,
                    "timeout_error", 8,
                    "processing_error", 12
            ));
            stats.put("errorsByQueue", Map.of(
                    MessageType.DOCUMENT_SEGMENTATION.getRoutingKey(), 15,
                    MessageType.EMBEDDING_PROCESSING.getRoutingKey(), 10
            ));
            stats.put("recentErrors", java.util.List.of(
                    Map.of(
                            "timestamp", LocalDateTime.now().minusMinutes(5),
                            "type", "processing_error",
                            "queue", MessageType.DOCUMENT_SEGMENTATION.getRoutingKey(),
                            "message", "Document processing failed"
                    ),
                    Map.of(
                            "timestamp", LocalDateTime.now().minusMinutes(10),
                            "type", "timeout_error",
                            "queue", MessageType.EMBEDDING_PROCESSING.getRoutingKey(),
                            "message", "Embedding timeout"
                    )
            ));

            return Result.success("获取错误统计成功", stats);
        } catch (Exception e) {
            log.error("获取错误统计失败", e);
            return Result.fail("获取错误统计失败: " + e.getMessage());
        }
    }
}
