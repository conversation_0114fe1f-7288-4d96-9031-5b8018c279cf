package com.xhcai.common.core.config;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * 环境变量验证器 验证关键环境变量的配置状态
 *
 * 在应用完全启动后执行验证，确保环境变量已经正确加载
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class EnvironmentValidator implements HealthIndicator, ApplicationListener<ApplicationReadyEvent> {

    private static final Logger log = LoggerFactory.getLogger(EnvironmentValidator.class);

    @Autowired
    private Environment environment;

    /**
     * 关键环境变量配置
     */
    private static final Map<String, EnvironmentVariable> CRITICAL_VARIABLES = new HashMap<>();
    private static final Map<String, EnvironmentVariable> OPTIONAL_VARIABLES = new HashMap<>();

    static {
        // 关键环境变量（必须配置）
        CRITICAL_VARIABLES.put("DB_HOST", new EnvironmentVariable("DB_HOST", "数据库主机地址", true));
        CRITICAL_VARIABLES.put("DB_PORT", new EnvironmentVariable("DB_PORT", "数据库端口", true));
        CRITICAL_VARIABLES.put("DB_NAME", new EnvironmentVariable("DB_NAME", "数据库名称", true));
        CRITICAL_VARIABLES.put("DB_USERNAME", new EnvironmentVariable("DB_USERNAME", "数据库用户名", true));
        CRITICAL_VARIABLES.put("DB_PASSWORD", new EnvironmentVariable("DB_PASSWORD", "数据库密码", true, true));
        CRITICAL_VARIABLES.put("REDIS_HOST", new EnvironmentVariable("REDIS_HOST", "Redis主机地址", true));
        CRITICAL_VARIABLES.put("REDIS_PORT", new EnvironmentVariable("REDIS_PORT", "Redis端口", true));
        CRITICAL_VARIABLES.put("JWT_SECRET", new EnvironmentVariable("JWT_SECRET", "JWT密钥", true, true));

        // 可选环境变量（有默认值）
        OPTIONAL_VARIABLES.put("SERVER_PORT", new EnvironmentVariable("SERVER_PORT", "应用服务端口", false));
        OPTIONAL_VARIABLES.put("SPRING_PROFILES_ACTIVE", new EnvironmentVariable("SPRING_PROFILES_ACTIVE", "激活的配置文件", false));
        OPTIONAL_VARIABLES.put("OPENAI_API_KEY", new EnvironmentVariable("OPENAI_API_KEY", "OpenAI API密钥", false, true));
        OPTIONAL_VARIABLES.put("DIFY_API_KEY", new EnvironmentVariable("DIFY_API_KEY", "Dify API密钥", false, true));
        OPTIONAL_VARIABLES.put("REDIS_PASSWORD", new EnvironmentVariable("REDIS_PASSWORD", "Redis密码", false, true));
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("Starting environment validation after application ready...");

        boolean isValid = true;
        StringBuilder errorMessage = new StringBuilder();

        // 验证关键环境变量
        for (EnvironmentVariable envVar : CRITICAL_VARIABLES.values()) {
            if (!validateVariable(envVar)) {
                isValid = false;
                if (errorMessage.length() > 0) {
                    errorMessage.append(", ");
                }
                errorMessage.append(envVar.getKey());
            }
        }

        // 记录可选环境变量状态
        logOptionalVariables();

        if (!isValid) {
            String message = "Critical environment variables are missing or invalid: " + errorMessage.toString();
            log.error(message);

            // 在生产环境中抛出异常，开发环境只记录警告
            if (EnvironmentConfig.isProductionEnvironment()) {
                throw new IllegalStateException(message);
            } else {
                log.warn("Development mode: continuing with missing critical environment variables");
            }
        } else {
            log.info("Environment validation completed successfully");
        }
    }

    /**
     * 验证单个环境变量
     */
    private boolean validateVariable(EnvironmentVariable envVar) {
        String value = environment.getProperty(envVar.getKey());

        if (envVar.isRequired() && (value == null || value.trim().isEmpty())) {
            log.error("Missing required environment variable: {} ({})", envVar.getKey(), envVar.getDescription());
            return false;
        }

        if (value != null && !value.trim().isEmpty()) {
            if (envVar.isSensitive()) {
                log.info("Environment variable {} is configured (sensitive value hidden)", envVar.getKey());
            } else {
                log.info("Environment variable {} = {}", envVar.getKey(), value);
            }
        }

        return true;
    }

    /**
     * 记录可选环境变量状态
     */
    private void logOptionalVariables() {
        log.info("Optional environment variables status:");
        for (EnvironmentVariable envVar : OPTIONAL_VARIABLES.values()) {
            String value = environment.getProperty(envVar.getKey());
            if (value != null && !value.trim().isEmpty()) {
                if (envVar.isSensitive()) {
                    log.info("  {} = *** (configured)", envVar.getKey());
                } else {
                    log.info("  {} = {}", envVar.getKey(), value);
                }
            } else {
                log.info("  {} = (using default)", envVar.getKey());
            }
        }
    }

    @Override
    public Health health() {
        Health.Builder builder = Health.up();

        // 检查关键环境变量
        Map<String, Object> details = new HashMap<>();
        boolean allCriticalPresent = true;

        for (EnvironmentVariable envVar : CRITICAL_VARIABLES.values()) {
            String value = environment.getProperty(envVar.getKey());
            boolean isPresent = value != null && !value.trim().isEmpty();

            if (!isPresent && envVar.isRequired()) {
                allCriticalPresent = false;
            }

            if (envVar.isSensitive()) {
                details.put(envVar.getKey(), isPresent ? "configured" : "missing");
            } else {
                details.put(envVar.getKey(), isPresent ? value : "missing");
            }
        }

        // 添加环境信息
        details.put("activeProfile", environment.getProperty("spring.profiles.active", "default"));
        details.put("isDevelopment", EnvironmentConfig.isDevelopmentEnvironment());
        details.put("isProduction", EnvironmentConfig.isProductionEnvironment());

        if (!allCriticalPresent) {
            builder.down().withDetail("error", "Some critical environment variables are missing");
        }

        return builder.withDetails(details).build();
    }

    /**
     * 环境变量定义类
     */
    private static class EnvironmentVariable {

        private final String key;
        private final String description;
        private final boolean required;
        private final boolean sensitive;

        public EnvironmentVariable(String key, String description, boolean required) {
            this(key, description, required, false);
        }

        public EnvironmentVariable(String key, String description, boolean required, boolean sensitive) {
            this.key = key;
            this.description = description;
            this.required = required;
            this.sensitive = sensitive;
        }

        public String getKey() {
            return key;
        }

        public String getDescription() {
            return description;
        }

        public boolean isRequired() {
            return required;
        }

        public boolean isSensitive() {
            return sensitive;
        }
    }
}
