package com.xhcai.modules.ai.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.ai.init.AiPermissionInitializer;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * AI模块初始化控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "AI模块初始化", description = "AI模块初始化相关接口")
@RestController
@RequestMapping("/api/ai/init")
public class AiInitController {

    private static final Logger log = LoggerFactory.getLogger(AiInitController.class);

    @Autowired
    private AiPermissionInitializer aiPermissionInitializer;

    @Operation(summary = "检查AI模块初始化状态", description = "检查AI模块是否已初始化")
    @GetMapping("/status")
    @RequiresPermissions("ai:init:status")
    public Result<Map<String, Object>> checkInitStatus() {
        log.info("检查AI模块初始化状态");

        Map<String, Object> status = new HashMap<>();
        status.put("moduleId", "ai");
        status.put("moduleName", "AI模块");
        // 简单检查，实际可以通过查询数据库来判断
        boolean initialized = checkAiInitialized();
        status.put("initialized", initialized);
        status.put("status", initialized ? "INITIALIZED" : "NOT_INITIALIZED");
        status.put("progress", initialized ? 100 : 0);
        status.put("message", initialized ? "AI模块已初始化" : "AI模块未初始化");

        return Result.success(status);
    }

    @Operation(summary = "初始化AI模块", description = "执行AI模块初始化")
    @PostMapping("/execute")
    @RequiresPermissions("ai:init:execute")
    public Result<Map<String, Object>> executeInit(
            @Parameter(description = "是否强制重新初始化") @RequestParam(defaultValue = "false") Boolean forceReinit) {
        // 从当前登录用户获取租户ID
        String tenantId = com.xhcai.common.security.utils.SecurityUtils.getCurrentTenantId();
        log.info("执行AI模块初始化: tenantId={}, forceReinit={}", tenantId, forceReinit);

        Map<String, Object> result = new HashMap<>();
        result.put("moduleId", "ai");
        result.put("moduleName", "AI模块");
        result.put("startTime", System.currentTimeMillis());

        try {
            // 检查是否已初始化
            if (checkAiInitialized() && !forceReinit) {
                result.put("status", "SKIPPED");
                result.put("message", "AI模块已初始化，跳过");
                result.put("progress", 100);
                return Result.success(result);
            }

            // 执行初始化
            result.put("progress", 30);
            aiPermissionInitializer.initializePermissions();

            result.put("progress", 70);
            aiPermissionInitializer.initializeAiDictionaries();

            result.put("status", "SUCCESS");
            result.put("message", "AI模块初始化成功");
            result.put("progress", 100);

        } catch (Exception e) {
            log.error("AI模块初始化失败", e);
            result.put("status", "FAILED");
            result.put("message", "AI模块初始化失败: " + e.getMessage());
            result.put("progress", 0);
        } finally {
            result.put("endTime", System.currentTimeMillis());
            long duration = (Long) result.get("endTime") - (Long) result.get("startTime");
            result.put("duration", duration);
        }

        return Result.success(result);
    }

    @Operation(summary = "获取AI模块信息", description = "获取AI模块的基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> getModuleInfo() {
        log.info("获取AI模块信息");

        Map<String, Object> info = new HashMap<>();
        info.put("moduleId", "ai");
        info.put("moduleName", "AI模块");
        info.put("version", "1.0.0");
        info.put("author", "xhcai");
        info.put("description", "AI功能模块，集成Spring AI，提供AI问答、文档嵌入等功能");
        info.put("features", new String[]{
            "AI聊天对话", "AI模型管理", "AI文档处理",
            "向量嵌入", "智能问答", "AI权限管理"
        });
        info.put("apiPrefix", "/api/ai");
        info.put("order", 200);

        return Result.success(info);
    }

    /**
     * 检查AI模块是否已初始化
     */
    private boolean checkAiInitialized() {
        try {
            // 这里可以通过查询数据库中的字典数据来判断是否已初始化
            // 暂时返回false，表示未初始化
            return false;
        } catch (Exception e) {
            log.debug("检查AI初始化状态失败", e);
            return false;
        }
    }
}
