package com.xhcai.modules.rag.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.enums.FileTypeEnum;
import com.xhcai.modules.system.service.ISysDictDataService;
import com.xhcai.modules.system.service.ISysDictService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * RAG模块初始化控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Tag(name = "RAG模块初始化", description = "RAG模块初始化相关接口")
@RestController
@RequestMapping("/api/rag/init")
public class RagInitController {

    @Autowired
    private ISysDictService dictService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Operation(summary = "检查RAG模块初始化状态", description = "检查RAG模块是否已初始化")
    @GetMapping("/status")
    @RequiresPermissions("rag:init:status")
    public Result<Map<String, Object>> checkInitStatus() {
        log.info("检查RAG模块初始化状态");

        Map<String, Object> status = new HashMap<>();
        status.put("moduleId", "rag");
        status.put("moduleName", "RAG模块");
        boolean initialized = checkRagInitialized();
        status.put("initialized", initialized);
        status.put("status", initialized ? "INITIALIZED" : "NOT_INITIALIZED");
        status.put("progress", initialized ? 100 : 0);
        status.put("message", initialized ? "RAG模块已初始化" : "RAG模块未初始化");

        return Result.success(status);
    }

    @Operation(summary = "初始化RAG模块", description = "执行RAG模块初始化")
    @PostMapping("/execute")
    @RequiresPermissions("rag:init:execute")
    public Result<Map<String, Object>> executeInit(
            @Parameter(description = "是否强制重新初始化") @RequestParam(defaultValue = "false") Boolean forceReinit) {
        // 从当前登录用户获取租户ID
        String tenantId = com.xhcai.common.security.utils.SecurityUtils.getCurrentTenantId();
        log.info("执行RAG模块初始化: tenantId={}, forceReinit={}", tenantId, forceReinit);

        Map<String, Object> result = new HashMap<>();
        result.put("moduleId", "rag");
        result.put("moduleName", "RAG模块");
        result.put("startTime", System.currentTimeMillis());

        try {
            // 检查是否已初始化
            if (checkRagInitialized() && !forceReinit) {
                result.put("status", "SKIPPED");
                result.put("message", "RAG模块已初始化，跳过");
                result.put("progress", 100);
                return Result.success(result);
            }

            // 初始化字典数据
            initializeDictionaries();

            // 初始化向量存储配置
            initializeVectorStore();

            // 初始化检索配置
            initializeRetrievalConfig();

            result.put("status", "SUCCESS");
            result.put("message", "RAG模块初始化成功");
            result.put("progress", 100);

        } catch (Exception e) {
            log.error("RAG模块初始化失败", e);
            result.put("status", "FAILED");
            result.put("message", "RAG模块初始化失败: " + e.getMessage());
            result.put("progress", 0);
        } finally {
            result.put("endTime", System.currentTimeMillis());
            long duration = (Long) result.get("endTime") - (Long) result.get("startTime");
            result.put("duration", duration);
        }

        return Result.success(result);
    }

    @Operation(summary = "获取RAG模块信息", description = "获取RAG模块的基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> getModuleInfo() {
        log.info("获取RAG模块信息");

        Map<String, Object> info = new HashMap<>();
        info.put("moduleId", "rag");
        info.put("moduleName", "RAG模块");
        info.put("version", "1.0.0");
        info.put("author", "xhcai");
        info.put("description", "RAG知识库管理模块，提供文档上传、向量化、检索增强生成等功能");
        info.put("features", new String[]{
            "文档上传处理", "文本向量化", "语义检索",
            "知识库管理", "检索增强生成", "向量存储管理"
        });
        info.put("supportedFileTypes", new String[]{
            "pdf", "doc", "docx", "txt", "md", "html", "csv", "xlsx", "pptx"
        });
        info.put("apiPrefix", "/api/rag");
        info.put("order", 400);

        return Result.success(info);
    }

    /**
     * 检查RAG模块是否已初始化
     */
    private boolean checkRagInitialized() {
        try {
            // 这里可以通过检查向量存储配置等来判断是否已初始化
            // 暂时返回false，表示未初始化
            return false;
        } catch (Exception e) {
            log.debug("检查RAG初始化状态失败", e);
            return false;
        }
    }

    /**
     * 初始化AI提供商字典
     */
    private void initializeAiProviderDict() {
        log.info("开始初始化AI提供商字典...");

        // 创建字典类型（全局，不分租户）
        dictService.createDictTypeIfNotExists("ai_provider", "AI提供商", "AI模型提供商列表", "Y");

        // 初始化字典数据（需要在租户上下文中）
        initializeAiProviderDictData();

        log.info("AI提供商字典初始化完成");
    }

    /**
     * 初始化AI模型类型字典
     */
    private void initializeAiModelTypeDict() {
        log.info("开始初始化AI模型类型字典...");

        // 创建字典类型（全局，不分租户）
        dictService.createDictTypeIfNotExists("ai_model_type", "AI模型类型", "AI模型类型列表", "Y");

        // 初始化字典数据（需要在租户上下文中）
        initializeAiModelTypeDictData();

        log.info("AI模型类型字典初始化完成");
    }

    /**
     * 初始化推理平台字典
     */
    private void initializeModelRunPlatformDict() {
        log.debug("初始化初始化推理平台字典");

        // 字典类型：model_inference_platform
        String dictType = "model_inference_platform";

        // 创建字典类型
        dictService.createDictTypeIfNotExists(dictType, "推理平台字典", "支持的初始化推理平台列表", "Y");

        // 字典数据
        String[][] platforms = {
            {"Xinference", "Xinference", "1", "🤖", "#10a37f", "Xinference模型推理平台"},
            {"MindIE", "MindIE", "2", "🔍", "#ff6b35", "华为旗下的推理平台"},
            {"Ollama", "Ollama", "3", "⚡", "#3b82f6", "Ollama推理平台"},
            {"GPUStack", "GPUStack", "4", "🦜", "#1c3d5a", "GPUStack推理平台"}
        };

        // 创建字典数据
        for (String[] data : platforms) {
            dictDataService.createDictDataIfNotExists(dictType, data[0], data[1], Integer.parseInt(data[2]), data[5], data[3], data[4], "Y");
        }
    }

    /**
     * 初始化第三方智能体平台字典
     */
    private void initializeThirdPlatformDict() {
        log.debug("初始化第三方智能体平台字典");

        // 字典类型：third_platform
        String dictType = "third_platform";

        // 创建字典类型
        dictService.createDictTypeIfNotExists(dictType, "第三方智能体平台", "支持的第三方智能体平台列表");

        // 字典数据
        String[][] platforms = {
            {"dify", "Dify", "1", "🤖", "#10a37f", "Dify是一个开源的LLM应用开发平台"},
            {"ragflow", "RAGFlow", "2", "🔍", "#ff6b35", "RAGFlow是一个基于深度文档理解的开源RAG引擎"},
            {"fastgpt", "FastGPT", "3", "⚡", "#3b82f6", "FastGPT是一个基于LLM大语言模型的知识库问答系统"},
            {"langchain", "LangChain", "4", "🦜", "#1c3d5a", "LangChain是一个用于开发由语言模型驱动的应用程序的框架"},
            {"llamaindex", "LlamaIndex", "5", "🦙", "#8b5cf6", "LlamaIndex是一个数据框架，用于LLM应用程序"},
            {"openai", "OpenAI", "6", "🧠", "#412991", "OpenAI官方API平台"},
            {"anthropic", "Anthropic", "7", "🎭", "#ff6b6b", "Anthropic Claude AI平台"},
            {"cohere", "Cohere", "8", "🔮", "#39c5bb", "Cohere自然语言处理平台"}
        };

        // 创建字典数据
        for (String[] data : platforms) {
            dictDataService.createDictDataIfNotExists(dictType, data[0], data[1], Integer.parseInt(data[2]), data[5], data[3], data[4]);
        }
    }

    /**
     * 初始化AI提供商字典数据
     */
    private void initializeAiProviderDictData() {
        log.debug("初始化AI提供商字典数据");

        // AI提供商列表
        String[][] providerData = {
            {"DeepSeek", "DeepSeek", "1", "深度求索公司提供的AI模型"},
            {"OpenAI", "OpenAI", "7", "OpenAI公司提供的AI模型"},
            {"Azure", "微软", "6", "微软Azure平台提供的OpenAI服务"},
            {"Google", "Google", "4", "Google公司提供的AI模型"},
            {"baidu", "百度", "5", "百度公司提供的文心系列模型"},
            {"ModelScope", "阿里", "2", "阿里云提供的通义系列模型"},
            {"Tencent", "腾讯", "8", "腾讯云提供的混元系列模型"},
            {"ZhiPu", "智谱", "3", "智谱AI提供的ChatGLM系列模型"},
            {"Others", "其他", "99", "其他AI模型提供商"}
        };

        for (String[] data : providerData) {
            dictDataService.createDictDataIfNotExists("ai_provider", data[0], data[1], Integer.parseInt(data[2]), data[3], null, null, "Y");
        }
    }

    /**
     * 初始化AI模型类型字典数据
     */
    private void initializeAiModelTypeDictData() {
        log.debug("初始化AI模型类型字典数据");

        // AI模型类型列表
        String[][] typeData = {
            {"TextGeneration", "文本生成", "1", "用于生成文本内容的AI模型"},
            {"ConversationalAI", "对话", "2", "用于对话交互的AI模型"},
            {"CodeGeneration", "代码生成", "3", "用于生成代码的AI模型"},
            {"ImageGeneration", "图像生成", "4", "用于生成图像的AI模型"},
            {"ImageUnderstanding", "图像理解", "5", "用于理解和分析图像的AI模型"},
            {"ASR", "语音识别", "6", "用于语音转文字的AI模型"},
            {"TTS", "语音合成", "7", "用于文字转语音的AI模型"},
            {"Multimodal", "多模态", "8", "支持多种输入输出模态的AI模型"},
            {"Embeddings", "嵌入向量", "9", "用于生成文本嵌入向量的AI模型"},
            {"Reranker", "重排序", "9", "用于通过主义重排序的AI模型"},
            {"Others", "其他", "99", "其他类型的AI模型"}
        };

        for (String[] data : typeData) {
            dictDataService.createDictDataIfNotExists("ai_model_type", data[0], data[1], Integer.parseInt(data[2]), data[3], null, null, "Y");
        }
    }

    /**
     * 初始化文档类型字典
     */
    private void initializeFileTypeDictData() {
        // 初始化文档类型字典
        Set<String> typeList = Arrays.stream(FileTypeEnum.values()).map(FileTypeEnum::getType).collect(Collectors.toSet());

        for (String type : typeList) {
            String typeName = switch (type) {
                case "documents" ->
                    "文档类";
                case "images" ->
                    "图片类";
                case "audios" ->
                    "语音类";
                case "videos" ->
                    "视频类";
                case "code" ->
                    "代码类";
                default ->
                    "其它";
            };
            dictService.createDictTypeIfNotExists(type, typeName, typeName, "Y");
        }

        Arrays.stream(FileTypeEnum.values()).forEach(fileType -> {
            dictDataService.createDictDataIfNotExists(fileType.getType(), fileType.getCode(), fileType.getDescription(), fileType.getOrder(), fileType.getDescription(), fileType.getIcon(), fileType.getCss(), "Y");
        });

        // 初始化文档状态字典
        dictService.createDictTypeIfNotExists("document_status", "文档处理状态", "文档处理状态", "Y");
        Arrays.stream(DocumentStatus.values()).forEach(doc -> {
            dictDataService.createDictDataIfNotExists("document_status", doc.getCode(), doc.getDescription(), doc.getOrder(), doc.getDescription(), doc.getIcon(), doc.getCss(), "Y");
        });
    }

    /**
     * 初始化字典数据
     */
    private void initializeDictionaries() {

        // 初始化AI提供商字典
        initializeAiProviderDict();

        // 初始化AI模型类型字典
        initializeAiModelTypeDict();

        // 初始化第三方智能体平台字典
        initializeThirdPlatformDict();

        // 初始模型推理平台字典
        initializeModelRunPlatformDict();

        // 初始化文档类型字典
        initializeFileTypeDictData();

        // 初始化向量数据库类型字典
        initializeVectorDatabaseTypeDict();

        // 初始化文件存储类型字典
        initializeFileStorageTypeDict();

    }

    /**
     * 初始化向量数据库类型字典
     */
    private void initializeVectorDatabaseTypeDict() {
        log.debug("初始化向量数据库类型字典");

        // 字典类型：vector_database_type
        String dictType = "vector_database_type";

        // 创建字典类型
        dictService.createDictTypeIfNotExists(dictType, "向量数据库类型", "支持的向量数据库类型列表", "Y");

        // 字典数据
        String[][] vectorDbTypes = {
            {"elasticsearch", "Elasticsearch", "1", "🔍", "#005571", "Elasticsearch向量搜索引擎"},
            {"weaviate", "Weaviate", "2", "🌐", "#00C9A7", "Weaviate向量数据库"},
            {"postgresql_pgvector", "PostgreSQL + pgvector", "3", "🐘", "#336791", "PostgreSQL数据库配合pgvector扩展"},
            {"milvus", "Milvus", "4", "🚀", "#00D4AA", "Milvus向量数据库"},
            {"qdrant", "Qdrant", "5", "⚡", "#DC382C", "Qdrant向量搜索引擎"}
        };

        // 创建字典数据
        for (String[] data : vectorDbTypes) {
            dictDataService.createDictDataIfNotExists(dictType, data[0], data[1], Integer.parseInt(data[2]), data[5], data[3], data[4], "Y");
        }
    }

    /**
     * 初始化文件存储类型字典
     */
    private void initializeFileStorageTypeDict() {
        log.debug("初始化文件存储类型字典");

        // 字典类型：file_storage_type
        String dictType = "file_storage_type";

        // 创建字典类型
        dictService.createDictTypeIfNotExists(dictType, "文件存储类型", "支持的文件存储类型列表", "Y");

        // 字典数据
        String[][] storageTypes = {
            {"minio", "MinIO", "1", "🗄️", "#C72E29", "MinIO对象存储服务"},
            {"ftp", "FTP", "2", "📁", "#4CAF50", "FTP文件传输协议"},
            {"obs", "华为云OBS", "3", "☁️", "#FF6B35", "华为云对象存储服务"},
            {"local", "本地存储", "4", "💾", "#607D8B", "本地文件系统存储"}
        };

        // 创建字典数据
        for (String[] data : storageTypes) {
            dictDataService.createDictDataIfNotExists(dictType, data[0], data[1], Integer.parseInt(data[2]), data[5], data[3], data[4], "Y");
        }
    }

    /**
     * 初始化向量存储
     */
    private void initializeVectorStore() {
        log.info("初始化向量存储配置");
        // 这里可以初始化向量数据库连接、索引配置等
    }

    /**
     * 初始化检索配置
     */
    private void initializeRetrievalConfig() {
        log.info("初始化检索配置");
        // 这里可以初始化检索参数、相似度阈值等配置
    }
}
