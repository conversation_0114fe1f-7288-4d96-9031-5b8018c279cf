'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Globe, 
  Code, 
  Copy, 
  ExternalLink,
  Book,
  Key,
  Shield,
  CheckCircle,
  AlertCircle,
  Search,
  Filter,
  Download
} from 'lucide-react';
import toast from 'react-hot-toast';

// API接口分类
enum ApiCategory {
  COMPONENTS = 'components',
  MONITORING = 'monitoring',
  ALERTS = 'alerts',
  SYSTEM = 'system'
}

// HTTP方法
enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE'
}

// API接口定义
interface ApiEndpoint {
  id: string;
  name: string;
  description: string;
  method: HttpMethod;
  path: string;
  category: ApiCategory;
  parameters?: ApiParameter[];
  requestBody?: ApiRequestBody;
  responses: ApiResponse[];
  examples: ApiExample[];
  requiresAuth: boolean;
  permissions: string[];
  deprecated?: boolean;
}

// API参数定义
interface ApiParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
  example?: string;
}

// API请求体定义
interface ApiRequestBody {
  contentType: string;
  schema: any;
  example: any;
}

// API响应定义
interface ApiResponse {
  statusCode: number;
  description: string;
  schema?: any;
  example?: any;
}

// API示例定义
interface ApiExample {
  name: string;
  description: string;
  request: {
    method: string;
    url: string;
    headers?: Record<string, string>;
    body?: any;
  };
  response: {
    statusCode: number;
    body: any;
  };
}

const CATEGORY_LABELS = {
  [ApiCategory.COMPONENTS]: '组件管理',
  [ApiCategory.MONITORING]: '监控数据',
  [ApiCategory.ALERTS]: '告警管理',
  [ApiCategory.SYSTEM]: '系统管理'
};

const METHOD_COLORS = {
  [HttpMethod.GET]: 'text-blue-600 bg-blue-50',
  [HttpMethod.POST]: 'text-green-600 bg-green-50',
  [HttpMethod.PUT]: 'text-yellow-600 bg-yellow-50',
  [HttpMethod.DELETE]: 'text-red-600 bg-red-50'
};

export default function ApiDocsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [apiEndpoints, setApiEndpoints] = useState<ApiEndpoint[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<ApiCategory | ''>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedEndpoint, setSelectedEndpoint] = useState<ApiEndpoint | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);

  useEffect(() => {
    loadApiEndpoints();
  }, []);

  const loadApiEndpoints = async () => {
    setLoading(true);
    try {
      // TODO: 调用API加载接口列表
      const mockEndpoints: ApiEndpoint[] = [
        {
          id: '1',
          name: '获取组件列表',
          description: '获取所有已安装的Elastic组件列表',
          method: HttpMethod.GET,
          path: '/api/components',
          category: ApiCategory.COMPONENTS,
          parameters: [
            {
              name: 'type',
              type: 'string',
              required: false,
              description: '组件类型过滤',
              example: 'elasticsearch'
            },
            {
              name: 'status',
              type: 'string',
              required: false,
              description: '组件状态过滤',
              example: 'RUNNING'
            }
          ],
          responses: [
            {
              statusCode: 200,
              description: '成功返回组件列表',
              example: {
                success: true,
                data: [
                  {
                    id: 'es-001',
                    name: 'Elasticsearch',
                    type: 'elasticsearch',
                    version: '8.11.0',
                    status: 'RUNNING',
                    port: 9200
                  }
                ]
              }
            }
          ],
          examples: [
            {
              name: '获取所有组件',
              description: '获取系统中所有组件的信息',
              request: {
                method: 'GET',
                url: '/api/components'
              },
              response: {
                statusCode: 200,
                body: {
                  success: true,
                  data: []
                }
              }
            }
          ],
          requiresAuth: true,
          permissions: ['components:read']
        },
        {
          id: '2',
          name: '启动组件',
          description: '启动指定的组件服务',
          method: HttpMethod.POST,
          path: '/api/components/{componentId}/start',
          category: ApiCategory.COMPONENTS,
          parameters: [
            {
              name: 'componentId',
              type: 'string',
              required: true,
              description: '组件ID',
              example: 'es-001'
            }
          ],
          responses: [
            {
              statusCode: 200,
              description: '组件启动成功',
              example: {
                success: true,
                message: '组件启动成功'
              }
            },
            {
              statusCode: 400,
              description: '启动失败',
              example: {
                success: false,
                message: '组件启动失败: 端口被占用'
              }
            }
          ],
          examples: [
            {
              name: '启动Elasticsearch',
              description: '启动ID为es-001的Elasticsearch组件',
              request: {
                method: 'POST',
                url: '/api/components/es-001/start'
              },
              response: {
                statusCode: 200,
                body: {
                  success: true,
                  message: '组件启动成功'
                }
              }
            }
          ],
          requiresAuth: true,
          permissions: ['components:write']
        },
        {
          id: '3',
          name: '获取系统资源使用情况',
          description: '获取当前系统的CPU、内存、磁盘使用情况',
          method: HttpMethod.GET,
          path: '/api/monitor/system/resources',
          category: ApiCategory.MONITORING,
          responses: [
            {
              statusCode: 200,
              description: '成功返回系统资源信息',
              example: {
                success: true,
                data: {
                  cpuUsage: 45.2,
                  memoryUsagePercent: 68.5,
                  diskUsagePercent: 32.1,
                  timestamp: '2024-01-01T12:00:00Z'
                }
              }
            }
          ],
          examples: [
            {
              name: '获取系统资源',
              description: '获取当前系统资源使用情况',
              request: {
                method: 'GET',
                url: '/api/monitor/system/resources'
              },
              response: {
                statusCode: 200,
                body: {
                  success: true,
                  data: {
                    cpuUsage: 45.2,
                    memoryUsagePercent: 68.5,
                    diskUsagePercent: 32.1
                  }
                }
              }
            }
          ],
          requiresAuth: true,
          permissions: ['monitoring:read']
        },
        {
          id: '4',
          name: '获取告警列表',
          description: '获取系统告警历史记录',
          method: HttpMethod.GET,
          path: '/api/alerts',
          category: ApiCategory.ALERTS,
          parameters: [
            {
              name: 'level',
              type: 'string',
              required: false,
              description: '告警级别过滤',
              example: 'warning'
            },
            {
              name: 'status',
              type: 'string',
              required: false,
              description: '告警状态过滤',
              example: 'active'
            },
            {
              name: 'limit',
              type: 'number',
              required: false,
              description: '返回记录数量限制',
              example: '100'
            }
          ],
          responses: [
            {
              statusCode: 200,
              description: '成功返回告警列表',
              example: {
                success: true,
                data: [
                  {
                    id: 'alert-001',
                    level: 'warning',
                    status: 'active',
                    message: 'CPU使用率过高',
                    triggerTime: '2024-01-01T12:00:00Z'
                  }
                ]
              }
            }
          ],
          examples: [
            {
              name: '获取活跃告警',
              description: '获取当前所有活跃状态的告警',
              request: {
                method: 'GET',
                url: '/api/alerts?status=active'
              },
              response: {
                statusCode: 200,
                body: {
                  success: true,
                  data: []
                }
              }
            }
          ],
          requiresAuth: true,
          permissions: ['alerts:read']
        }
      ];
      
      setApiEndpoints(mockEndpoints);
    } catch (error) {
      console.error('加载API接口失败:', error);
      toast.error('加载接口列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 过滤API接口
  const filteredEndpoints = apiEndpoints.filter(endpoint => {
    const matchesCategory = !selectedCategory || endpoint.category === selectedCategory;
    const matchesSearch = !searchQuery || 
      endpoint.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      endpoint.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      endpoint.path.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('已复制到剪贴板');
    }).catch(() => {
      toast.error('复制失败');
    });
  };

  // 生成curl命令
  const generateCurlCommand = (endpoint: ApiEndpoint, example: ApiExample) => {
    let curl = `curl -X ${example.request.method} \\
  "${window.location.origin}${example.request.url}" \\`;
    
    if (endpoint.requiresAuth) {
      curl += `
  -H "Authorization: Bearer YOUR_API_KEY" \\`;
    }
    
    if (example.request.headers) {
      Object.entries(example.request.headers).forEach(([key, value]) => {
        curl += `
  -H "${key}: ${value}" \\`;
      });
    }
    
    if (example.request.body) {
      curl += `
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(example.request.body, null, 2)}'`;
    } else {
      curl = curl.slice(0, -2); // 移除最后的反斜杠
    }
    
    return curl;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="btn-outline mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </button>
              <div className="flex items-center">
                <Globe className="h-8 w-8 text-primary-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">API接口文档</h1>
                  <p className="text-sm text-gray-500">查看和测试可用的API接口</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowApiKey(!showApiKey)}
                className="btn-outline"
              >
                <Key className="h-4 w-4 mr-2" />
                API密钥
              </button>
              <button className="btn-primary">
                <Download className="h-4 w-4 mr-2" />
                下载文档
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* API密钥提示 */}
      {showApiKey && (
        <div className="bg-blue-50 border-b border-blue-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-start space-x-3">
              <Key className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-blue-900">API认证说明</h3>
                <div className="mt-2 text-sm text-blue-800">
                  <p>所有API请求都需要在请求头中包含有效的API密钥：</p>
                  <div className="mt-2 bg-blue-100 rounded p-2 font-mono text-xs">
                    Authorization: Bearer YOUR_API_KEY
                  </div>
                  <p className="mt-2">
                    您可以在 <button onClick={() => router.push('/settings')} className="underline">系统设置</button> 中管理您的API密钥。
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowApiKey(false)}
                className="text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 侧边栏 */}
          <div className="lg:w-80">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">接口分类</h3>
              </div>
              <div className="card-body">
                {/* 搜索框 */}
                <div className="relative mb-4">
                  <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索接口..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md text-sm"
                  />
                </div>

                {/* 分类过滤 */}
                <div className="space-y-1">
                  <button
                    onClick={() => setSelectedCategory('')}
                    className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                      selectedCategory === ''
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    所有接口 ({apiEndpoints.length})
                  </button>
                  {Object.entries(CATEGORY_LABELS).map(([category, label]) => {
                    const count = apiEndpoints.filter(ep => ep.category === category).length;
                    return (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category as ApiCategory)}
                        className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                          selectedCategory === category
                            ? 'bg-primary-100 text-primary-700'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                        }`}
                      >
                        {label} ({count})
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="flex-1">
            {loading ? (
              <div className="text-center py-12">
                <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredEndpoints.length === 0 ? (
                  <div className="text-center py-12">
                    <Book className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">没有找到匹配的API接口</p>
                  </div>
                ) : (
                  filteredEndpoints.map((endpoint) => (
                    <div key={endpoint.id} className="card">
                      <div className="card-body">
                        {/* 接口基本信息 */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <span className={`px-2 py-1 rounded text-xs font-medium ${METHOD_COLORS[endpoint.method]}`}>
                                {endpoint.method}
                              </span>
                              <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                {endpoint.path}
                              </code>
                              {endpoint.requiresAuth && (
                                <Shield className="h-4 w-4 text-yellow-600" title="需要认证" />
                              )}
                              {endpoint.deprecated && (
                                <AlertCircle className="h-4 w-4 text-red-600" title="已废弃" />
                              )}
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-1">
                              {endpoint.name}
                            </h3>
                            <p className="text-gray-600">{endpoint.description}</p>
                          </div>
                          <button
                            onClick={() => setSelectedEndpoint(selectedEndpoint?.id === endpoint.id ? null : endpoint)}
                            className="btn-outline btn-sm"
                          >
                            {selectedEndpoint?.id === endpoint.id ? '收起' : '展开'}
                          </button>
                        </div>

                        {/* 详细信息 */}
                        {selectedEndpoint?.id === endpoint.id && (
                          <div className="border-t border-gray-200 pt-4 space-y-6">
                            {/* 请求参数 */}
                            {endpoint.parameters && endpoint.parameters.length > 0 && (
                              <div>
                                <h4 className="font-medium text-gray-900 mb-3">请求参数</h4>
                                <div className="overflow-x-auto">
                                  <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                      <tr>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">参数名</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">必需</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">说明</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">示例</th>
                                      </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                      {endpoint.parameters.map((param, index) => (
                                        <tr key={index}>
                                          <td className="px-4 py-2 text-sm font-mono text-gray-900">{param.name}</td>
                                          <td className="px-4 py-2 text-sm text-gray-600">{param.type}</td>
                                          <td className="px-4 py-2 text-sm">
                                            {param.required ? (
                                              <CheckCircle className="h-4 w-4 text-green-600" />
                                            ) : (
                                              <span className="text-gray-400">-</span>
                                            )}
                                          </td>
                                          <td className="px-4 py-2 text-sm text-gray-600">{param.description}</td>
                                          <td className="px-4 py-2 text-sm font-mono text-gray-500">{param.example || '-'}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            )}

                            {/* 响应示例 */}
                            <div>
                              <h4 className="font-medium text-gray-900 mb-3">响应示例</h4>
                              <div className="space-y-3">
                                {endpoint.responses.map((response, index) => (
                                  <div key={index} className="border border-gray-200 rounded">
                                    <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                                      <div className="flex items-center space-x-2">
                                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                                          response.statusCode >= 200 && response.statusCode < 300
                                            ? 'text-green-600 bg-green-50'
                                            : 'text-red-600 bg-red-50'
                                        }`}>
                                          {response.statusCode}
                                        </span>
                                        <span className="text-sm text-gray-600">{response.description}</span>
                                      </div>
                                    </div>
                                    {response.example && (
                                      <div className="p-4">
                                        <pre className="text-xs bg-gray-900 text-gray-100 p-3 rounded overflow-x-auto">
                                          {JSON.stringify(response.example, null, 2)}
                                        </pre>
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* 代码示例 */}
                            <div>
                              <h4 className="font-medium text-gray-900 mb-3">代码示例</h4>
                              <div className="space-y-4">
                                {endpoint.examples.map((example, index) => (
                                  <div key={index} className="border border-gray-200 rounded">
                                    <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                                      <div className="flex items-center justify-between">
                                        <div>
                                          <h5 className="font-medium text-gray-900">{example.name}</h5>
                                          <p className="text-sm text-gray-600">{example.description}</p>
                                        </div>
                                        <button
                                          onClick={() => copyToClipboard(generateCurlCommand(endpoint, example))}
                                          className="btn-sm btn-outline"
                                        >
                                          <Copy className="h-3 w-3 mr-1" />
                                          复制cURL
                                        </button>
                                      </div>
                                    </div>
                                    <div className="p-4">
                                      <pre className="text-xs bg-gray-900 text-gray-100 p-3 rounded overflow-x-auto">
                                        {generateCurlCommand(endpoint, example)}
                                      </pre>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
