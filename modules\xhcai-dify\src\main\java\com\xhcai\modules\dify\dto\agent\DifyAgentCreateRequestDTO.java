package com.xhcai.modules.dify.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * Dify智能体创建请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify智能体创建请求")
public class DifyAgentCreateRequestDTO {

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "客服助手", required = true)
    @NotBlank(message = "智能体名称不能为空")
    @Size(min = 1, max = 100, message = "智能体名称长度必须在1-100个字符之间")
    private String name;

    /**
     * 图标类型
     */
    @Schema(description = "图标类型", example = "emoji", allowableValues = {"emoji", "image"})
    @Pattern(regexp = "^(emoji|image)$", message = "图标类型必须为emoji或image")
    private String iconType = "emoji";

    /**
     * 图标
     */
    @Schema(description = "图标", example = "🤖")
    private String icon = "🤖";

    /**
     * 图标背景色
     */
    @Schema(description = "图标背景色", example = "#FFEAD5")
    private String iconBackground = "#FFEAD5";

    /**
     * 智能体模式
     */
    @Schema(description = "智能体模式", example = "advanced-chat", allowableValues = {"chat", "workflow", "completion", "advanced-chat", "agent-chat"})
    @NotBlank(message = "智能体模式不能为空")
    @Pattern(regexp = "^(chat|workflow|completion|advanced-chat|agent-chat)$", message = "智能体模式必须为chat、workflow、completion、advanced-chat或agent-chat")
    private String mode;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述", example = "专业的客服助手，能够回答用户问题")
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    private String description = "";

    /**
     * 平台ID
     */
    @Schema(description = "平台ID", example = "platform-001", required = true)
    @NotBlank(message = "平台ID不能为空")
    private String platformId;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconType() {
        return iconType;
    }

    public void setIconType(String iconType) {
        this.iconType = iconType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBackground() {
        return iconBackground;
    }

    public void setIconBackground(String iconBackground) {
        this.iconBackground = iconBackground;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        return "DifyAgentCreateRequestDTO{" +
                "name='" + name + '\'' +
                ", iconType='" + iconType + '\'' +
                ", icon='" + icon + '\'' +
                ", iconBackground='" + iconBackground + '\'' +
                ", mode='" + mode + '\'' +
                ", description='" + description + '\'' +
                ", platformId='" + platformId + '\'' +
                '}';
    }
}
