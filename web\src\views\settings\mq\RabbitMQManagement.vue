<template>
  <div class="rabbitmq-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <i class="fas fa-rabbit"></i>
          RabbitMQ 管理
        </h2>
        <p class="page-description">管理消息队列连接、交换机、队列和消息监控</p>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline" @click="refreshAll">
          <i class="fas fa-sync-alt"></i>
          刷新
        </button>
        <button class="btn btn-primary" @click="openConnectionModal">
          <i class="fas fa-plus"></i>
          新建连接
        </button>
      </div>
    </div>

    <!-- 连接状态卡片 -->
    <div class="status-cards">
      <div class="status-card">
        <div class="card-icon connection">
          <i class="fas fa-link"></i>
        </div>
        <div class="card-content">
          <div class="card-value">{{ connectionStatus.active }}</div>
          <div class="card-label">活跃连接</div>
        </div>
      </div>
      <div class="status-card">
        <div class="card-icon queue">
          <i class="fas fa-list"></i>
        </div>
        <div class="card-content">
          <div class="card-value">{{ queueStats.total }}</div>
          <div class="card-label">队列总数</div>
        </div>
      </div>
      <div class="status-card">
        <div class="card-icon message">
          <i class="fas fa-envelope"></i>
        </div>
        <div class="card-content">
          <div class="card-value">{{ messageStats.pending }}</div>
          <div class="card-label">待处理消息</div>
        </div>
      </div>
      <div class="status-card">
        <div class="card-icon exchange">
          <i class="fas fa-exchange-alt"></i>
        </div>
        <div class="card-content">
          <div class="card-value">{{ exchangeStats.total }}</div>
          <div class="card-label">交换机数量</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="activeTab = tab.key"
          class="tab-button"
          :class="{ active: activeTab === tab.key }"
        >
          <i :class="tab.icon"></i>
          {{ tab.name }}
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 连接管理 -->
        <div v-if="activeTab === 'connections'" class="tab-panel">
          <div class="panel-header">
            <h3>连接管理</h3>
            <div class="panel-actions">
              <button class="btn btn-sm btn-outline" @click="refreshConnections">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
            </div>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>连接名称</th>
                  <th>主机地址</th>
                  <th>端口</th>
                  <th>虚拟主机</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="connection in connections" :key="connection.id">
                  <td class="connection-name">{{ connection.name }}</td>
                  <td>{{ connection.host }}</td>
                  <td>{{ connection.port }}</td>
                  <td>{{ connection.vhost }}</td>
                  <td>
                    <span class="status-badge" :class="connection.status">
                      {{ getStatusText(connection.status) }}
                    </span>
                  </td>
                  <td>{{ formatDate(connection.createdAt) }}</td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-icon" @click="testConnection(connection)" title="测试连接">
                        <i class="fas fa-plug"></i>
                      </button>
                      <button class="btn-icon" @click="editConnection(connection)" title="编辑">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn-icon danger" @click="deleteConnection(connection)" title="删除">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 队列管理 -->
        <div v-if="activeTab === 'queues'" class="tab-panel">
          <div class="panel-header">
            <h3>队列管理</h3>
            <div class="panel-actions">
              <button class="btn btn-sm btn-outline" @click="refreshQueues">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button class="btn btn-sm btn-primary" @click="createQueue">
                <i class="fas fa-plus"></i>
                创建队列
              </button>
            </div>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>队列名称</th>
                  <th>连接</th>
                  <th>消息数量</th>
                  <th>消费者数量</th>
                  <th>状态</th>
                  <th>持久化</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="queue in queues" :key="queue.id">
                  <td class="queue-name">{{ queue.name }}</td>
                  <td>{{ queue.connection }}</td>
                  <td>
                    <span class="message-count" :class="{ warning: queue.messageCount > 1000 }">
                      {{ queue.messageCount }}
                    </span>
                  </td>
                  <td>{{ queue.consumerCount }}</td>
                  <td>
                    <span class="status-badge" :class="queue.status">
                      {{ getStatusText(queue.status) }}
                    </span>
                  </td>
                  <td>
                    <span class="boolean-badge" :class="{ active: queue.durable }">
                      {{ queue.durable ? '是' : '否' }}
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-icon" @click="viewQueueDetails(queue)" title="查看详情">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn-icon" @click="purgeQueue(queue)" title="清空队列">
                        <i class="fas fa-broom"></i>
                      </button>
                      <button class="btn-icon danger" @click="deleteQueue(queue)" title="删除队列">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 交换机管理 -->
        <div v-if="activeTab === 'exchanges'" class="tab-panel">
          <div class="panel-header">
            <h3>交换机管理</h3>
            <div class="panel-actions">
              <button class="btn btn-sm btn-outline" @click="refreshExchanges">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button class="btn btn-sm btn-primary" @click="createExchange">
                <i class="fas fa-plus"></i>
                创建交换机
              </button>
            </div>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>交换机名称</th>
                  <th>类型</th>
                  <th>连接</th>
                  <th>绑定数量</th>
                  <th>持久化</th>
                  <th>自动删除</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="exchange in exchanges" :key="exchange.id">
                  <td class="exchange-name">{{ exchange.name }}</td>
                  <td>
                    <span class="type-badge" :class="exchange.type">
                      {{ exchange.type }}
                    </span>
                  </td>
                  <td>{{ exchange.connection }}</td>
                  <td>{{ exchange.bindingCount }}</td>
                  <td>
                    <span class="boolean-badge" :class="{ active: exchange.durable }">
                      {{ exchange.durable ? '是' : '否' }}
                    </span>
                  </td>
                  <td>
                    <span class="boolean-badge" :class="{ active: exchange.autoDelete }">
                      {{ exchange.autoDelete ? '是' : '否' }}
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-icon" @click="viewExchangeDetails(exchange)" title="查看详情">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn-icon" @click="editExchange(exchange)" title="编辑">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn-icon danger" @click="deleteExchange(exchange)" title="删除">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 消息监控 -->
        <div v-if="activeTab === 'monitoring'" class="tab-panel">
          <div class="panel-header">
            <h3>消息监控</h3>
            <div class="panel-actions">
              <button class="btn btn-sm btn-outline" @click="refreshMonitoring">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
            </div>
          </div>
          
          <!-- 监控图表区域 -->
          <div class="monitoring-charts">
            <div class="chart-card">
              <h4>消息流量</h4>
              <div class="chart-placeholder">
                <i class="fas fa-chart-line"></i>
                <p>消息流量图表</p>
              </div>
            </div>
            <div class="chart-card">
              <h4>队列深度</h4>
              <div class="chart-placeholder">
                <i class="fas fa-chart-bar"></i>
                <p>队列深度图表</p>
              </div>
            </div>
          </div>

          <!-- 实时消息列表 -->
          <div class="message-list">
            <h4>最近消息</h4>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>队列</th>
                    <th>消息ID</th>
                    <th>大小</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="message in recentMessages" :key="message.id">
                    <td>{{ formatTime(message.timestamp) }}</td>
                    <td>{{ message.queue }}</td>
                    <td class="message-id">{{ message.id }}</td>
                    <td>{{ formatSize(message.size) }}</td>
                    <td>
                      <span class="status-badge" :class="message.status">
                        {{ getStatusText(message.status) }}
                      </span>
                    </td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn-icon" @click="viewMessage(message)" title="查看消息">
                          <i class="fas fa-eye"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { RabbitMQAPI } from '@/api/rabbitmq'
import type {
  ConnectionStatus,
  QueueStats,
  MessageStats,
  ExchangeStats,
  ConnectionInfo,
  QueueInfo,
  ExchangeInfo,
  MessageInfo
} from '@/api/rabbitmq'

// 响应式数据
const activeTab = ref('connections')
const loading = ref(false)

// 统计数据
const connectionStatus = reactive<ConnectionStatus>({
  active: 0,
  total: 0,
  healthy: false
})

const queueStats = reactive<QueueStats>({
  total: 0,
  active: 0,
  messages: 0
})

const messageStats = reactive<MessageStats>({
  pending: 0,
  processed: 0,
  failed: 0,
  rate: 0
})

const exchangeStats = reactive<ExchangeStats>({
  total: 0,
  active: 0
})

// 标签页配置
const tabs = [
  { key: 'connections', name: '连接管理', icon: 'fas fa-link' },
  { key: 'queues', name: '队列管理', icon: 'fas fa-list' },
  { key: 'exchanges', name: '交换机管理', icon: 'fas fa-exchange-alt' },
  { key: 'monitoring', name: '消息监控', icon: 'fas fa-chart-line' }
]

// 数据列表
const connections = ref<ConnectionInfo[]>([])
const queues = ref<QueueInfo[]>([])
const exchanges = ref<ExchangeInfo[]>([])
const messages = ref<MessageInfo[]>([])

// 加载状态
const loadingConnections = ref(false)
const loadingQueues = ref(false)
const loadingExchanges = ref(false)
const loadingMessages = ref(false)

// API调用方法
const loadConnectionStatus = async () => {
  try {
    const response = await RabbitMQAPI.getConnectionStatus()
    if (response.success) {
      Object.assign(connectionStatus, response.data)
    }
  } catch (error) {
    console.error('加载连接状态失败:', error)
  }
}

const loadQueueStats = async () => {
  try {
    const response = await RabbitMQAPI.getQueueStats()
    if (response.success) {
      Object.assign(queueStats, response.data)
    }
  } catch (error) {
    console.error('加载队列统计失败:', error)
  }
}

const loadMessageStats = async () => {
  try {
    const response = await RabbitMQAPI.getMessageStats()
    if (response.success) {
      Object.assign(messageStats, response.data)
    }
  } catch (error) {
    console.error('加载消息统计失败:', error)
  }
}

const loadExchangeStats = async () => {
  try {
    const response = await RabbitMQAPI.getExchangeStats()
    if (response.success) {
      Object.assign(exchangeStats, response.data)
    }
  } catch (error) {
    console.error('加载交换机统计失败:', error)
  }
}

const loadConnections = async () => {
  try {
    loadingConnections.value = true
    const response = await RabbitMQAPI.getConnections()
    if (response.success) {
      connections.value = response.data
    }
  } catch (error) {
    console.error('加载连接列表失败:', error)
  } finally {
    loadingConnections.value = false
  }
}

const loadQueues = async () => {
  try {
    loadingQueues.value = true
    const response = await RabbitMQAPI.getQueues()
    if (response.success) {
      queues.value = response.data
    }
  } catch (error) {
    console.error('加载队列列表失败:', error)
  } finally {
    loadingQueues.value = false
  }
}

const loadExchanges = async () => {
  try {
    loadingExchanges.value = true
    const response = await RabbitMQAPI.getExchanges()
    if (response.success) {
      exchanges.value = response.data
    }
  } catch (error) {
    console.error('加载交换机列表失败:', error)
  } finally {
    loadingExchanges.value = false
  }
}

const loadMessages = async () => {
  try {
    loadingMessages.value = true
    const response = await RabbitMQAPI.getMessages()
    if (response.success) {
      messages.value = response.data
    }
  } catch (error) {
    console.error('加载消息列表失败:', error)
  } finally {
    loadingMessages.value = false
  }
}

const loadAllStats = async () => {
  await Promise.all([
    loadConnectionStatus(),
    loadQueueStats(),
    loadMessageStats(),
    loadExchangeStats()
  ])
}

// 刷新方法
const refreshAll = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadAllStats(),
      loadConnections(),
      loadQueues(),
      loadExchanges(),
      loadMessages()
    ])
  } finally {
    loading.value = false
  }
}

const refreshConnections = async () => {
  await loadConnections()
}

const refreshQueues = async () => {
  await loadQueues()
}

const refreshExchanges = async () => {
  await loadExchanges()
}

const refreshMonitoring = async () => {
  await Promise.all([
    loadAllStats(),
    loadMessages()
  ])
}

const openConnectionModal = () => {
  console.log('打开新建连接模态框')
  // 这里可以打开连接配置模态框
}

const testConnection = async (connection: ConnectionInfo) => {
  try {
    const response = await RabbitMQAPI.healthCheck()
    if (response.success) {
      ElMessage.success(`连接 "${connection.name}" 测试成功`)
    } else {
      ElMessage.error(`连接 "${connection.name}" 测试失败`)
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error(`连接 "${connection.name}" 测试失败`)
  }
}

const editConnection = (connection: ConnectionInfo) => {
  console.log('编辑连接:', connection.name)
  // TODO: 打开编辑连接模态框
}

const deleteConnection = async (connection: ConnectionInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除连接 "${connection.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await RabbitMQAPI.closeConnection(connection.id)
    if (response.success) {
      ElMessage.success('连接删除成功')
      await refreshConnections()
    } else {
      ElMessage.error('连接删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除连接失败:', error)
      ElMessage.error('删除连接失败')
    }
  }
}

const createQueue = () => {
  console.log('创建队列')
  // TODO: 打开创建队列模态框
}

const viewQueueDetails = async (queue: QueueInfo) => {
  try {
    const response = await RabbitMQAPI.getQueueDetail(queue.name)
    if (response.success) {
      console.log('队列详情:', response.data)
      // TODO: 显示队列详情模态框
    }
  } catch (error) {
    console.error('获取队列详情失败:', error)
    ElMessage.error('获取队列详情失败')
  }
}

const purgeQueue = async (queue: QueueInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要清空队列 "${queue.name}" 中的所有消息吗？`,
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await RabbitMQAPI.purgeQueue(queue.name)
    if (response.success) {
      ElMessage.success('队列清空成功')
      await refreshQueues()
    } else {
      ElMessage.error('队列清空失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空队列失败:', error)
      ElMessage.error('清空队列失败')
    }
  }
}

const deleteQueue = async (queue: QueueInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除队列 "${queue.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await RabbitMQAPI.deleteQueue(queue.name)
    if (response.success) {
      ElMessage.success('队列删除成功')
      await refreshQueues()
    } else {
      ElMessage.error('队列删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除队列失败:', error)
      ElMessage.error('删除队列失败')
    }
  }
}

const createExchange = () => {
  console.log('创建交换机')
  // 这里可以打开创建交换机模态框
}

const viewExchangeDetails = (exchange: any) => {
  console.log('查看交换机详情:', exchange.name)
  // 这里可以打开交换机详情模态框
}

const editExchange = (exchange: any) => {
  console.log('编辑交换机:', exchange.name)
  // 这里可以打开编辑交换机模态框
}

const deleteExchange = (exchange: any) => {
  if (confirm(`确定要删除交换机 "${exchange.name}" 吗？`)) {
    console.log('删除交换机:', exchange.name)
    // 这里可以调用API删除交换机
  }
}

const viewMessage = (message: any) => {
  console.log('查看消息:', message.id)
  // 这里可以打开消息详情模态框
}

// 工具方法
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    connected: '已连接',
    disconnected: '已断开',
    active: '活跃',
    idle: '空闲',
    delivered: '已投递',
    pending: '待处理',
    failed: '失败'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('zh-CN')
}

const formatSize = (bytes: number) => {
  if (bytes < 1024) return bytes + ' B'
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB'
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB'
}

// 生命周期
onMounted(() => {
  refreshAll()
})
</script>

<style scoped>
.rabbitmq-management {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
}

.page-title i {
  color: #f59e0b;
}

.page-description {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline {
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* 状态卡片 */
.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.status-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.card-icon.connection {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.card-icon.queue {
  background: linear-gradient(135deg, #10b981, #047857);
}

.card-icon.message {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.card-icon.exchange {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.card-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  border: none;
  background: none;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: #f1f5f9;
  color: #374151;
}

.tab-button.active {
  background: white;
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

/* 标签页内容 */
.tab-content {
  min-height: 500px;
}

.tab-panel {
  padding: 24px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.data-table th {
  background: #f8fafc;
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e2e8f0;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 14px;
  color: #374151;
}

.data-table tr:hover {
  background: #f8fafc;
}

/* 表格内容样式 */
.connection-name,
.queue-name,
.exchange-name {
  font-weight: 600;
  color: #1e293b;
}

.message-count.warning {
  color: #f59e0b;
  font-weight: 600;
}

.message-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #64748b;
}

/* 状态徽章 */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.connected,
.status-badge.active,
.status-badge.delivered {
  background: #dcfce7;
  color: #166534;
}

.status-badge.disconnected,
.status-badge.failed {
  background: #fef2f2;
  color: #dc2626;
}

.status-badge.idle,
.status-badge.pending {
  background: #fef3c7;
  color: #d97706;
}

.boolean-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  background: #f1f5f9;
  color: #64748b;
}

.boolean-badge.active {
  background: #dcfce7;
  color: #166534;
}

.type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.direct {
  background: #dbeafe;
  color: #1d4ed8;
}

.type-badge.topic {
  background: #f3e8ff;
  color: #7c3aed;
}

.type-badge.fanout {
  background: #fef3c7;
  color: #d97706;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
}

.btn-icon {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: #e2e8f0;
  color: #374151;
}

.btn-icon.danger {
  color: #dc2626;
}

.btn-icon.danger:hover {
  background: #fef2f2;
  color: #dc2626;
}

/* 监控图表 */
.monitoring-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.chart-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.chart-card h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  background: white;
  border: 2px dashed #e2e8f0;
  border-radius: 8px;
}

.chart-placeholder i {
  font-size: 48px;
  margin-bottom: 12px;
}

.chart-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* 消息列表 */
.message-list {
  margin-top: 32px;
}

.message-list h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .status-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .monitoring-charts {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .rabbitmq-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .status-cards {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-container {
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .card-value {
    font-size: 20px;
  }

  .tab-button {
    padding: 12px 16px;
    font-size: 12px;
  }
}
</style>
