package com.xhcai.modules.rag.mapper;


import com.xhcai.modules.rag.entity.DocumentSegment;

import java.util.List;
import java.util.Map;

/**
 * 文档分段SQL提供者
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DocumentSegmentSqlProvider {

    /**
     * 批量插入分段的SQL
     *
     * @param params 参数Map
     * @return SQL语句
     */
    public String batchInsert(Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<DocumentSegment> segments = (List<DocumentSegment>) params.get("segments");

        if (segments == null || segments.isEmpty()) {
            return "";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO document_segments (");
        sql.append("id, dataset_id, document_id, position, content, word_count, tokens, keywords, ");
        sql.append("hit_count, enabled, status, doc_type, doc_language, tenant_id, create_by, create_time, ");
        sql.append("update_time, deleted");
        sql.append(") VALUES ");

        for (int i = 0; i < segments.size(); i++) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append("(");
            sql.append("#{segments[").append(i).append("].id}, ");
            sql.append("#{segments[").append(i).append("].datasetId}, ");
            sql.append("#{segments[").append(i).append("].documentId}, ");
            sql.append("#{segments[").append(i).append("].position}, ");
            sql.append("#{segments[").append(i).append("].content}, ");
            sql.append("#{segments[").append(i).append("].wordCount}, ");
            sql.append("#{segments[").append(i).append("].tokens}, ");
            sql.append("#{segments[").append(i).append("].keywords, typeHandler=com.xhcai.common.datasource.handler.PostgreSQLJsonTypeHandler}, ");
            sql.append("#{segments[").append(i).append("].hitCount}, ");
            sql.append("#{segments[").append(i).append("].enabled}, ");
            sql.append("#{segments[").append(i).append("].status}, ");
            sql.append("#{segments[").append(i).append("].docType}, ");
            sql.append("#{segments[").append(i).append("].docLanguage}, ");
            sql.append("#{segments[").append(i).append("].tenantId}, ");
            sql.append("#{segments[").append(i).append("].createBy}, ");
            sql.append("#{segments[").append(i).append("].createTime}, ");
            sql.append("#{segments[").append(i).append("].updateTime}, ");
            sql.append("#{segments[").append(i).append("].deleted}");
            sql.append(")");
        }

        return sql.toString();
    }
}
