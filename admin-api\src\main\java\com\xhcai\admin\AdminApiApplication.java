package com.xhcai.admin;

import java.net.InetAddress;
import java.net.UnknownHostException;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * XHC AI Plus 管理后台启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class,
    // 排除Ollama自动配置，避免多个EmbeddingModel Bean冲突
    org.springframework.ai.autoconfigure.ollama.OllamaAutoConfiguration.class
})
@ComponentScan(basePackages = {
    "com.xhcai.admin",
    "com.xhcai.common",
    "com.xhcai.plugin"
})
@EnableAsync
@EnableScheduling
public class AdminApiApplication implements ApplicationListener<ApplicationReadyEvent> {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(AdminApiApplication.class, args);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        Environment env = event.getApplicationContext().getEnvironment();

        // 获取服务器端口
        String port = env.getProperty("server.port", "8080");

        // 获取服务器IP地址
        String hostAddress = "localhost";
        try {
            hostAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            // 如果获取失败，尝试从环境变量获取
            String serverHost = env.getProperty("server.address");
            if (serverHost != null && !serverHost.isEmpty()) {
                hostAddress = serverHost;
            }
        }

        // 构建访问地址
        String baseUrl = "http://" + hostAddress + ":" + port;

        // 检查是否启用了Swagger
        boolean swaggerEnabled = "true".equals(env.getProperty("springdoc.swagger-ui.enabled", "false"));

        System.out.println("""

                ====================================================================================================

                    ██╗  ██╗██╗  ██╗ ██████╗     █████╗ ██╗    ██████╗ ██╗     ██╗   ██╗███████╗
                    ╚██╗██╔╝██║  ██║██╔════╝    ██╔══██╗██║    ██╔══██╗██║     ██║   ██║██╔════╝
                     ╚███╔╝ ███████║██║         ███████║██║    ██████╔╝██║     ██║   ██║███████╗
                     ██╔██╗ ██╔══██║██║         ██╔══██║██║    ██╔═══╝ ██║     ██║   ██║╚════██║
                    ██╔╝ ██╗██║  ██║╚██████╗    ██║  ██║██║    ██║     ███████╗╚██████╔╝███████║
                    ╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝    ╚═╝  ╚═╝╚═╝    ╚═╝     ╚══════╝ ╚═════╝ ╚══════╝

                    XHC AI Plus 平台启动成功！

                    访问地址：""" + baseUrl + """

                    接口文档：""" + (swaggerEnabled ? baseUrl + "/swagger-ui.html" : "已禁用") + """

                    监控中心：""" + baseUrl + "/actuator" + """

                    环境信息：
                    - 运行环境：""" + env.getProperty("spring.profiles.active", "default") + """

                    - 服务器地址：""" + hostAddress + """

                    - 服务器端口：""" + port + """

                ====================================================================================================
                """);
    }
}
