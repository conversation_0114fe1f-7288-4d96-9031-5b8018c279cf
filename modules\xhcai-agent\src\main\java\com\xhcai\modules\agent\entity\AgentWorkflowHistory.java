package com.xhcai.modules.agent.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 智能体工作流历史记录实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent_workflow_history")
@Schema(description = "智能体工作流历史记录")
@TableName("agent_workflow_history")
public class AgentWorkflowHistory extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 工作流ID
     */
    @Column(name = "workflow_id", length = 36)
    @Schema(description = "工作流ID", example = "workflow_001")
    @NotBlank(message = "工作流ID不能为空")
    @Size(max = 36, message = "工作流ID长度不能超过36个字符")
    @TableField("workflow_id")
    private String workflowId;

    /**
     * 智能体ID
     */
    @Column(name = "agent_id", length = 36)
    @Schema(description = "智能体ID", example = "agent_001")
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    @TableField("agent_id")
    private String agentId;

    /**
     * 配置哈希值（用作唯一标识）
     */
    @Column(name = "config_hash", length = 64)
    @Schema(description = "配置哈希值", example = "a1b2c3d4e5f6...")
    @NotBlank(message = "配置哈希值不能为空")
    @Size(max = 64, message = "配置哈希值长度不能超过64个字符")
    @TableField("config_hash")
    private String configHash;

    /**
     * 工作流名称（快照）
     */
    @Column(name = "name", length = 100)
    @Schema(description = "工作流名称", example = "客服工作流")
    @Size(max = 100, message = "工作流名称长度不能超过100个字符")
    @TableField("name")
    private String name;

    /**
     * 工作流描述（快照）
     */
    @Column(name = "description", length = 500)
    @Schema(description = "工作流描述", example = "智能客服的工作流程配置")
    @Size(max = 500, message = "工作流描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 版本号（快照）
     */
    @Column(name = "version")
    @Schema(description = "版本号", example = "1")
    @TableField("version")
    private Integer version;

    /**
     * Vue Flow节点数据快照（JSON格式）
     */
    @Column(name = "nodes_data", columnDefinition = "TEXT")
    @Schema(description = "Vue Flow节点数据快照", example = "[{\"id\":\"node1\",\"type\":\"start\",\"position\":{\"x\":100,\"y\":100},\"data\":{\"label\":\"开始\"}}]")
    @TableField("nodes_data")
    private String nodesData;

    /**
     * Vue Flow边数据快照（JSON格式）
     */
    @Column(name = "edges_data", columnDefinition = "TEXT")
    @Schema(description = "Vue Flow边数据快照", example = "[{\"id\":\"edge1\",\"source\":\"node1\",\"target\":\"node2\",\"type\":\"default\"}]")
    @TableField("edges_data")
    private String edgesData;

    /**
     * 视口配置快照（JSON格式）
     */
    @Column(name = "viewport_config", columnDefinition = "TEXT")
    @Schema(description = "视口配置快照", example = "{\"x\":0,\"y\":0,\"zoom\":1}")
    @TableField("viewport_config")
    private String viewportConfig;

    /**
     * 全局变量配置（JSON格式快照）
     */
    @Column(name = "global_variables", columnDefinition = "TEXT")
    @Schema(description = "全局变量配置", example = "{\"var1\":\"value1\",\"var2\":\"value2\"}")
    @TableField("global_variables")
    private String globalVariables;

    /**
     * 操作类型
     */
    @Column(name = "operation_type", length = 50)
    @Schema(description = "操作类型", example = "node_add")
    @NotBlank(message = "操作类型不能为空")
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @Column(name = "operation_desc", length = 500)
    @Schema(description = "操作描述", example = "添加了开始节点")
    @Size(max = 500, message = "操作描述长度不能超过500个字符")
    @TableField("operation_desc")
    private String operationDesc;

    /**
     * 变更摘要（记录主要变更内容）
     */
    @Column(name = "change_summary", length = 1000)
    @Schema(description = "变更摘要", example = "新增2个节点，修改1个连接")
    @Size(max = 1000, message = "变更摘要长度不能超过1000个字符")
    @TableField("change_summary")
    private String changeSummary;

    /**
     * 操作时间
     */
    @Column(name = "operation_time")
    @Schema(description = "操作时间")
    @NotNull(message = "操作时间不能为空")
    @TableField("operation_time")
    private LocalDateTime operationTime;

    /**
     * 操作用户ID
     */
    @Column(name = "operation_user_id", length = 36)
    @Schema(description = "操作用户ID", example = "user_001")
    @Size(max = 36, message = "操作用户ID长度不能超过36个字符")
    @TableField("operation_user_id")
    private String operationUserId;

    /**
     * 操作用户名称
     */
    @Column(name = "operation_user_name", length = 100)
    @Schema(description = "操作用户名称", example = "张三")
    @Size(max = 100, message = "操作用户名称长度不能超过100个字符")
    @TableField("operation_user_name")
    private String operationUserName;

    /**
     * 是否为重要变更
     */
    @Column(name = "is_major_change")
    @Schema(description = "是否为重要变更", example = "true")
    @TableField("is_major_change")
    private Boolean isMajorChange;

    /**
     * 配置大小（字节数）
     */
    @Column(name = "config_size")
    @Schema(description = "配置大小", example = "1024")
    @TableField("config_size")
    private Long configSize;

    // Getters and Setters
    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getConfigHash() {
        return configHash;
    }

    public void setConfigHash(String configHash) {
        this.configHash = configHash;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getNodesData() {
        return nodesData;
    }

    public void setNodesData(String nodesData) {
        this.nodesData = nodesData;
    }

    public String getEdgesData() {
        return edgesData;
    }

    public void setEdgesData(String edgesData) {
        this.edgesData = edgesData;
    }

    public String getViewportConfig() {
        return viewportConfig;
    }

    public void setViewportConfig(String viewportConfig) {
        this.viewportConfig = viewportConfig;
    }

    public String getGlobalVariables() {
        return globalVariables;
    }

    public void setGlobalVariables(String globalVariables) {
        this.globalVariables = globalVariables;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public String getChangeSummary() {
        return changeSummary;
    }

    public void setChangeSummary(String changeSummary) {
        this.changeSummary = changeSummary;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationUserId() {
        return operationUserId;
    }

    public void setOperationUserId(String operationUserId) {
        this.operationUserId = operationUserId;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public Boolean getIsMajorChange() {
        return isMajorChange;
    }

    public void setIsMajorChange(Boolean isMajorChange) {
        this.isMajorChange = isMajorChange;
    }

    public Long getConfigSize() {
        return configSize;
    }

    public void setConfigSize(Long configSize) {
        this.configSize = configSize;
    }
}
