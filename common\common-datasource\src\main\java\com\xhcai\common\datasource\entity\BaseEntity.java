package com.xhcai.common.datasource.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;

/**
 * 抽象基础实体类 包含所有公共的审计字段
 *
 * <p>
 * 此抽象基类定义了所有实体类的公共字段，避免在子类中重复定义。</p>
 * <p>
 * 包含的公共字段：</p>
 * <ul>
 * <li>id - 主键ID</li>
 * <li>remark - 备注信息</li>
 * <li>deleted - 逻辑删除标记</li>
 * <li>create_by - 创建人</li>
 * <li>create_time - 创建时间</li>
 * <li>update_by - 更新人</li>
 * <li>update_time - 更新时间</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedSuperclass
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36)
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    // ========== 审计字段（在表中显示在最后） ==========
    /**
     * 备注 审计字段 - 通用备注信息
     */
    @Column(name = "remark", length = 500)
    @TableField(value = "remark")
    private String remark;

    /**
     * 删除标记 审计字段 - 逻辑删除标记，0=正常，1=删除
     * 参考 DeletedEnum 的枚举类，0=正常，1=删除
     */
    @Column(name = "deleted", length = 1)
    @TableField(value = "deleted")
    @TableLogic
    private Integer deleted = 0;

    /**
     * 创建人 审计字段 - 记录创建者用户ID
     */
    @Column(name = "create_by", length = 36)
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间 审计字段 - 记录创建时间
     */
    @Column(name = "create_time")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人 审计字段 - 记录最后更新者用户ID
     */
    @Column(name = "update_by", length = 36)
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间 审计字段 - 记录最后更新时间
     */
    @Column(name = "update_time")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AbstractBaseEntity{"
                + "id='" + id + '\''
                + ", remark='" + remark + '\''
                + ", deleted='" + deleted + '\''
                + ", createBy='" + createBy + '\''
                + ", createTime=" + createTime
                + ", updateBy='" + updateBy + '\''
                + ", updateTime=" + updateTime
                + '}';
    }
}
