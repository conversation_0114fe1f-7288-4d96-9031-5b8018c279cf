package com.xhcai.modules.rag.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.DocumentCategoryCreateDTO;
import com.xhcai.modules.rag.dto.DocumentCategoryUpdateDTO;
import com.xhcai.modules.rag.entity.DocumentCategory;
import com.xhcai.modules.rag.mapper.DocumentCategoryMapper;
import com.xhcai.modules.rag.service.IDocumentCategoryService;
import com.xhcai.modules.rag.vo.DocumentCategoryVO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档分类服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
@RequiredArgsConstructor
public class DocumentCategoryServiceImpl extends ServiceImpl<DocumentCategoryMapper, DocumentCategory> implements IDocumentCategoryService {

    private final DocumentCategoryMapper documentCategoryMapper;

    @Override
    public List<DocumentCategoryVO> getCategoryTree() {
        return getCategoryTree(null);
    }

    @Override
    public List<DocumentCategoryVO> getCategoryTree(String datasetId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 使用Mapper方法获取带文件数统计的分类树
        List<DocumentCategoryVO> categoryTree = documentCategoryMapper.selectCategoryTreeWithFileCount(tenantId, datasetId);

        // 构建树形结构
        return buildCategoryTreeFromVO(categoryTree, null);
    }

    @Override
    public List<DocumentCategoryVO> getCategoriesByParentId(String parentId) {
        return getCategoriesByParentId(parentId, null);
    }

    @Override
    public List<DocumentCategoryVO> getCategoriesByParentId(String parentId, String datasetId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        List<DocumentCategory> categories = documentCategoryMapper.selectByParentId(tenantId, datasetId, parentId);

        return categories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DocumentCategoryVO createCategory(DocumentCategoryCreateDTO createDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        // 检查名称是否重复
        if (existsCategoryName(createDTO.getName(), createDTO.getParentId(), null, createDTO.getDatasetId())) {
            throw new BusinessException("分类名称已存在");
        }

        // 计算层级
        int level = 0;
        if (StringUtils.hasText(createDTO.getParentId())) {
            DocumentCategory parentCategory = getById(createDTO.getParentId());
            if (parentCategory == null) {
                throw new BusinessException("父分类不存在");
            }
            level = parentCategory.getLevel() + 1;
            if (level > 2) {
                throw new BusinessException("分类层级不能超过3级");
            }
        }

        // 获取排序号
        Integer sortOrder = createDTO.getSortOrder();
        if (sortOrder == null) {
            sortOrder = documentCategoryMapper.selectMaxSortOrder(tenantId, createDTO.getParentId()) + 1;
        }

        // 创建分类
        DocumentCategory category = new DocumentCategory();
        BeanUtils.copyProperties(createDTO, category);
        category.setLevel(level);
        category.setSortOrder(sortOrder);
        category.setFileCount(0);
        category.setEnabled(true);
        category.setTenantId(tenantId);
        category.setCreateBy(currentUserId);

        save(category);

        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DocumentCategoryVO updateCategory(String categoryId, DocumentCategoryUpdateDTO updateDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        DocumentCategory category = getById(categoryId);
        if (category == null || !tenantId.equals(category.getTenantId())) {
            throw new BusinessException("分类不存在");
        }

        // 检查名称是否重复
        if (existsCategoryName(updateDTO.getName(), category.getParentId(), categoryId, category.getDatasetId())) {
            throw new BusinessException("分类名称已存在");
        }

        // 更新分类
        category.setName(updateDTO.getName());
        category.setDescription(updateDTO.getDescription());
        if (updateDTO.getSortOrder() != null) {
            category.setSortOrder(updateDTO.getSortOrder());
        }
        if (updateDTO.getEnabled() != null) {
            category.setEnabled(updateDTO.getEnabled());
        }
        category.setUpdateBy(currentUserId);

        updateById(category);

        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        DocumentCategory category = getById(categoryId);
        if (category == null || !tenantId.equals(category.getTenantId())) {
            throw new BusinessException("分类不存在");
        }

        // 检查是否有子分类
        LambdaQueryWrapper<DocumentCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentCategory::getTenantId, tenantId)
                .eq(DocumentCategory::getParentId, categoryId)
                .eq(DocumentCategory::getDeleted, 0);

        long childCount = count(wrapper);
        if (childCount > 0) {
            throw new BusinessException("存在子分类，无法删除");
        }

        // 检查是否有文档
        if (category.getFileCount() > 0) {
            throw new BusinessException("分类下存在文档，无法删除");
        }

        // 软删除
        category.setDeleted(1);
        category.setUpdateBy(currentUserId);
        updateById(category);
    }

    @Override
    public DocumentCategoryVO getCategoryById(String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        DocumentCategory category = getById(categoryId);
        if (category == null || !tenantId.equals(category.getTenantId()) || category.getDeleted() == 1) {
            return null;
        }

        return convertToVO(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCategoryFileCount(String categoryId, Integer fileCount) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        documentCategoryMapper.updateFileCount(tenantId, categoryId, fileCount);
    }

    @Override
    public boolean existsCategoryName(String name, String parentId, String excludeId) {
        return existsCategoryName(name, parentId, excludeId, null);
    }

    @Override
    public boolean existsCategoryName(String name, String parentId, String excludeId, String datasetId) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        LambdaQueryWrapper<DocumentCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentCategory::getTenantId, tenantId)
                .eq(DocumentCategory::getName, name)
                .eq(DocumentCategory::getDeleted, 0);

        if (StringUtils.hasText(datasetId)) {
            wrapper.eq(DocumentCategory::getDatasetId, datasetId);
        }

        if (StringUtils.hasText(parentId)) {
            wrapper.eq(DocumentCategory::getParentId, parentId);
        } else {
            wrapper.isNull(DocumentCategory::getParentId);
        }

        if (StringUtils.hasText(excludeId)) {
            wrapper.ne(DocumentCategory::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public List<String> getCategoryAndChildrenIds(String categoryId) {
        String tenantId = SecurityUtils.getCurrentTenantId();
        return documentCategoryMapper.selectCategoryAndChildrenIds(tenantId, categoryId);
    }

    /**
     * 构建分类树
     */
    private List<DocumentCategoryVO> buildCategoryTree(List<DocumentCategory> allCategories, String parentId) {
        List<DocumentCategoryVO> result = new ArrayList<>();

        // 按父ID分组
        Map<String, List<DocumentCategory>> categoryMap = allCategories.stream()
                .collect(Collectors.groupingBy(category
                        -> category.getParentId() == null ? "root" : category.getParentId()));

        // 获取指定父ID的分类
        String mapKey = parentId == null ? "root" : parentId;
        List<DocumentCategory> categories = categoryMap.getOrDefault(mapKey, new ArrayList<>());

        for (DocumentCategory category : categories) {
            DocumentCategoryVO vo = convertToVO(category);

            // 递归获取子分类
            List<DocumentCategoryVO> children = buildCategoryTree(allCategories, category.getId());
            vo.setChildren(children);

            result.add(vo);
        }

        return result;
    }

    /**
     * 构建分类树（从VO列表）
     */
    private List<DocumentCategoryVO> buildCategoryTreeFromVO(List<DocumentCategoryVO> allCategories, String parentId) {
        List<DocumentCategoryVO> result = new ArrayList<>();

        // 按父ID分组
        Map<String, List<DocumentCategoryVO>> categoryMap = allCategories.stream()
                .collect(Collectors.groupingBy(category
                        -> category.getParentId() == null ? "root" : category.getParentId()));

        // 获取指定父ID的分类
        String mapKey = parentId == null ? "root" : parentId;
        List<DocumentCategoryVO> categories = categoryMap.getOrDefault(mapKey, new ArrayList<>());

        for (DocumentCategoryVO category : categories) {
            // 递归获取子分类
            List<DocumentCategoryVO> children = buildCategoryTreeFromVO(allCategories, category.getId());
            category.setChildren(children);

            result.add(category);
        }

        return result;
    }

    /**
     * 转换为VO
     */
    private DocumentCategoryVO convertToVO(DocumentCategory category) {
        DocumentCategoryVO vo = new DocumentCategoryVO();
        BeanUtils.copyProperties(category, vo);
        return vo;
    }
}
