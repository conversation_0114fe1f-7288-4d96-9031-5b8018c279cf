package com.xhcai.modules.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.agent.entity.AgentConversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 智能体对话Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AgentConversationMapper extends BaseMapper<AgentConversation> {

    /**
     * 分页查询用户的对话列表
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 对话列表
     */
    @Select("SELECT "
            + "    ac.*, a.name as agent_name, a.avatar as agent_avatar "
            + "FROM agent_conversation ac "
            + "LEFT JOIN agent a ON ac.agent_id = a.id "
            + "WHERE ac.user_id = #{userId} AND ac.tenant_id = #{tenantId} AND ac.deleted = 0 "
            + "ORDER BY ac.last_activity_at DESC")
    IPage<AgentConversation> selectUserConversations(Page<AgentConversation> page,
            @Param("userId") String userId,
            @Param("tenantId") String tenantId);

    /**
     * 分页查询智能体的对话列表
     *
     * @param page 分页参数
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 对话列表
     */
    @Select("SELECT "
            + "    ac.*, u.username as user_name "
            + "FROM agent_conversation ac "
            + "LEFT JOIN sys_user u ON ac.user_id = u.id "
            + "WHERE ac.agent_id = #{agentId} AND ac.tenant_id = #{tenantId} AND ac.deleted = 0 "
            + "ORDER BY ac.last_activity_at DESC")
    IPage<AgentConversation> selectAgentConversations(Page<AgentConversation> page,
            @Param("agentId") String agentId,
            @Param("tenantId") String tenantId);

    /**
     * 根据会话ID查询对话
     * 注意：不需要手动添加tenant_id条件，多租户插件会自动处理
     *
     * @param sessionId 会话ID
     * @return 对话信息
     */
    @Select("SELECT * FROM agent_conversation WHERE session_id = #{sessionId} AND deleted = 0")
    AgentConversation selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 查询用户最近的对话列表
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 对话列表
     */
    @Select("<script>"
            + "SELECT "
            + "    ac.*, a.name as agent_name, a.avatar as agent_avatar "
            + "FROM agent_conversation ac "
            + "LEFT JOIN agent a ON ac.agent_id = a.id "
            + "WHERE ac.user_id = #{userId} AND ac.tenant_id = #{tenantId} AND ac.deleted = 0 "
            + "ORDER BY ac.last_activity_at DESC "
            + "<if test='limit != null'>"
            + "    LIMIT #{limit}"
            + "</if>"
            + "</script>")
    List<AgentConversation> selectRecentConversations(@Param("userId") String userId,
            @Param("tenantId") String tenantId,
            @Param("limit") Integer limit);

    /**
     * 更新对话消息统计
     *
     * @param id 对话ID
     * @param messageCount 消息数量
     * @param lastActivityAt 最后活动时间
     * @return 更新行数
     */
    @Update("UPDATE agent_conversation SET message_count = #{messageCount}, last_activity_at = #{lastActivityAt}, update_time = NOW() WHERE id = #{id}")
    int updateMessageStats(@Param("id") String id,
            @Param("messageCount") Integer messageCount,
            @Param("lastActivityAt") LocalDateTime lastActivityAt);

    /**
     * 更新对话token统计
     *
     * @param id 对话ID
     * @param totalTokens 总token消耗
     * @param inputTokens 输入token消耗
     * @param outputTokens 输出token消耗
     * @param cost 费用
     * @return 更新行数
     */
    @Update("UPDATE agent_conversation SET total_tokens = #{totalTokens}, input_tokens = #{inputTokens}, output_tokens = #{outputTokens}, cost = #{cost}, update_time = NOW() WHERE id = #{id}")
    int updateTokenStats(@Param("id") String id,
            @Param("totalTokens") Long totalTokens,
            @Param("inputTokens") Long inputTokens,
            @Param("outputTokens") Long outputTokens,
            @Param("cost") Long cost);

    /**
     * 结束对话
     *
     * @param id 对话ID
     * @param endedAt 结束时间
     * @return 更新行数
     */
    @Update("UPDATE agent_conversation SET status = 'ended', ended_at = #{endedAt}, update_time = NOW() WHERE id = #{id}")
    int endConversation(@Param("id") String id, @Param("endedAt") LocalDateTime endedAt);

    /**
     * 更新用户反馈
     *
     * @param id 对话ID
     * @param rating 评分
     * @param feedback 反馈内容
     * @return 更新行数
     */
    @Update("UPDATE agent_conversation SET rating = #{rating}, feedback = #{feedback}, update_time = NOW() WHERE id = #{id}")
    int updateFeedback(@Param("id") String id, @Param("rating") Integer rating, @Param("feedback") String feedback);

    /**
     * 统计智能体的对话数量
     *
     * @param agentId 智能体ID
     * @param tenantId 租户ID
     * @return 对话数量
     */
    @Select("SELECT COUNT(*) FROM agent_conversation WHERE agent_id = #{agentId} AND tenant_id = #{tenantId} AND deleted = 0")
    Long countByAgentId(@Param("agentId") String agentId, @Param("tenantId") String tenantId);

    /**
     * 统计用户的对话数量
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 对话数量
     */
    @Select("SELECT COUNT(*) FROM agent_conversation WHERE user_id = #{userId} AND tenant_id = #{tenantId} AND deleted = 0")
    Long countByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 统计今日对话数量
     *
     * @param tenantId 租户ID
     * @return 今日对话数量
     */
    @Select("SELECT COUNT(*) FROM agent_conversation WHERE tenant_id = #{tenantId} AND DATE(create_time) = CURDATE() AND deleted = 0")
    Long countTodayConversations(@Param("tenantId") String tenantId);

    /**
     * 统计活跃对话数量
     *
     * @param tenantId 租户ID
     * @return 活跃对话数量
     */
    @Select("SELECT COUNT(*) FROM agent_conversation WHERE tenant_id = #{tenantId} AND status = 'active' AND deleted = 0")
    Long countActiveConversations(@Param("tenantId") String tenantId);

    /**
     * 清理过期的对话
     *
     * @param expiredTime 过期时间
     * @param tenantId 租户ID
     * @return 清理数量
     */
    @Update("UPDATE agent_conversation SET status = 'ended', ended_at = NOW(), update_time = NOW() WHERE tenant_id = #{tenantId} AND status = 'active' AND last_activity_at < #{expiredTime}")
    int cleanExpiredConversations(@Param("expiredTime") LocalDateTime expiredTime, @Param("tenantId") String tenantId);
}
