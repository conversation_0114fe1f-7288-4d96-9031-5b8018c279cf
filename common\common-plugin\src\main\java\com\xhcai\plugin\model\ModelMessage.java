package com.xhcai.plugin.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 模型消息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelMessage {
    
    /**
     * 消息角色
     */
    private MessageRole role;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息名称（可选）
     */
    private String name;
    
    /**
     * 函数调用信息（可选）
     */
    private Map<String, Object> functionCall;
    
    /**
     * 额外属性
     */
    private Map<String, Object> metadata;
    
    /**
     * 消息角色枚举
     */
    public enum MessageRole {
        SYSTEM,     // 系统消息
        USER,       // 用户消息
        ASSISTANT,  // 助手消息
        FUNCTION    // 函数消息
    }
}
