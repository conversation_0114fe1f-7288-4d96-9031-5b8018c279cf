# 通用选择器组件

基于 Vue 3 + Element Plus + TailwindCSS 开发的通用选择器组件库，用于在表单或筛选条件中选择用户、部门、角色、权限等。

## 组件列表

### 1. DeptTreeSelector - 部门层级选择器

支持部门层级树形选择，包含搜索、展开/折叠、单选/多选功能。

**基本用法：**
```vue
<template>
  <DeptTreeSelector
    v-model="selectedDepts"
    :config="{ multiple: true, checkStrictly: false }"
    @change="handleDeptChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import { DeptTreeSelector } from '@/components/common/selectors'

const selectedDepts = ref([])
const handleDeptChange = (value, options) => {
  console.log('选择的部门:', value, options)
}
</script>
```

**Props：**
- `modelValue`: 选中的值，单选时为字符串，多选时为字符串数组
- `config`: 选择器配置对象
- `excludeDeptId`: 排除的部门ID
- `onlyEnabled`: 是否只显示启用的部门，默认 true

**Events：**
- `update:modelValue`: 值变化时触发
- `change`: 选择项变化时触发，参数：(value, options)
- `select`: 选择某一项时触发，参数：(value, option)
- `remove`: 移除某一项时触发，参数：(value)

### 2. UserByDeptSelector - 按部门选择用户

先选择部门，再在该部门下选择用户，支持搜索和多选。

**基本用法：**
```vue
<template>
  <UserByDeptSelector
    v-model="selectedUsers"
    :config="{ multiple: true }"
    :only-enabled="true"
    @change="handleUserChange"
    @dept-change="handleDeptChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import { UserByDeptSelector } from '@/components/common/selectors'

const selectedUsers = ref([])
const handleUserChange = (value, options) => {
  console.log('选择的用户:', value, options)
}
const handleDeptChange = (deptId, dept) => {
  console.log('选择的部门:', deptId, dept)
}
</script>
```

**Props：**
- `modelValue`: 选中的用户ID
- `config`: 选择器配置对象
- `onlyEnabled`: 是否只显示启用的用户，默认 true
- `excludeUserIds`: 排除的用户ID数组

**Events：**
- `update:modelValue`: 值变化时触发
- `change`: 用户选择变化时触发，参数：(value, options)
- `select`: 选择某个用户时触发，参数：(value, option)
- `remove`: 移除某个用户时触发，参数：(value)
- `deptChange`: 部门选择变化时触发，参数：(deptId, dept)

### 3. UserByRoleSelector - 按角色选择用户

先选择角色，再在该角色下选择用户，支持搜索和多选。

**基本用法：**
```vue
<template>
  <UserByRoleSelector
    v-model="selectedUsers"
    :config="{ multiple: true }"
    @change="handleUserChange"
    @role-change="handleRoleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import { UserByRoleSelector } from '@/components/common/selectors'

const selectedUsers = ref([])
const handleUserChange = (value, options) => {
  console.log('选择的用户:', value, options)
}
const handleRoleChange = (roleId, role) => {
  console.log('选择的角色:', roleId, role)
}
</script>
```

**Props：**
- `modelValue`: 选中的用户ID
- `config`: 选择器配置对象
- `onlyEnabled`: 是否只显示启用的用户，默认 true
- `excludeUserIds`: 排除的用户ID数组

**Events：**
- `update:modelValue`: 值变化时触发
- `change`: 用户选择变化时触发，参数：(value, options)
- `select`: 选择某个用户时触发，参数：(value, option)
- `remove`: 移除某个用户时触发，参数：(value)
- `roleChange`: 角色选择变化时触发，参数：(roleId, role)

### 4. PermissionByRoleSelector - 按角色选择权限

先选择角色，再选择该角色的权限，支持权限树形结构。

**基本用法：**
```vue
<template>
  <PermissionByRoleSelector
    v-model="selectedPermissions"
    :config="{ multiple: true, checkStrictly: false }"
    @change="handlePermissionChange"
    @role-change="handleRoleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import { PermissionByRoleSelector } from '@/components/common/selectors'

const selectedPermissions = ref([])
const handlePermissionChange = (value, options) => {
  console.log('选择的权限:', value, options)
}
const handleRoleChange = (roleId, role) => {
  console.log('选择的角色:', roleId, role)
}
</script>
```

**Props：**
- `modelValue`: 选中的权限ID
- `config`: 选择器配置对象
- `onlyEnabled`: 是否只显示启用的权限，默认 true
- `excludePermissionIds`: 排除的权限ID数组

**Events：**
- `update:modelValue`: 值变化时触发
- `change`: 权限选择变化时触发，参数：(value, options)
- `select`: 选择某个权限时触发，参数：(value, option)
- `remove`: 移除某个权限时触发，参数：(value)
- `roleChange`: 角色选择变化时触发，参数：(roleId, role)

## 配置选项 (SelectorConfig)

```typescript
interface SelectorConfig {
  multiple?: boolean          // 是否多选，默认 false
  clearable?: boolean         // 是否可清空，默认 true
  filterable?: boolean        // 是否可搜索，默认 true
  placeholder?: string        // 占位符文本，默认 '请选择'
  size?: 'large' | 'default' | 'small'  // 尺寸，默认 'default'
  disabled?: boolean          // 是否禁用，默认 false
  loading?: boolean           // 是否加载中，默认 false
  checkStrictly?: boolean     // 父子节点是否关联，默认 false
  showCheckbox?: boolean      // 是否显示复选框，默认 false
  expandOnClickNode?: boolean // 是否点击节点展开，默认 true
  defaultExpandAll?: boolean  // 是否默认展开所有节点，默认 false
  filterNodeMethod?: Function // 自定义过滤方法
}
```

## 样式定制

组件使用 TailwindCSS 进行样式设计，支持以下自定义：

1. **主题色彩**：通过修改 TailwindCSS 配置文件自定义主题色
2. **尺寸调整**：通过 `size` 配置项调整组件尺寸
3. **自定义样式**：通过 CSS 类名覆盖默认样式

## 注意事项

1. **数据权限**：组件会自动根据当前用户的数据权限过滤可选项
2. **性能优化**：大数据量时建议启用虚拟滚动或分页加载
3. **错误处理**：组件内置了错误处理机制，API 调用失败时会显示空状态
4. **国际化**：支持多语言，可通过 i18n 配置文本内容

## 示例页面

访问 `/examples/selector-examples` 查看完整的使用示例和效果演示。

## API 依赖

组件依赖以下 API 接口：

- `DeptAPI.getDeptTree()` - 获取部门树
- `UserAPI.getUserPage()` - 分页查询用户
- `RoleAPI.getRoleList()` - 查询角色列表
- `RoleAPI.getRolePermissionIds()` - 查询角色权限ID
- `PermissionAPI.getPermissionsByRoleId()` - 查询角色权限

确保后端提供相应的接口支持。

### 5. DropdownSelector - 通用下拉选择器

智能下拉选择器基础组件，支持自定义内容和智能位置定位。

**功能特性：**
- ✅ 智能位置定位，自动避免边界溢出
- ✅ 支持左对齐、右对齐、居中对齐
- ✅ 响应式设计，适配移动端
- ✅ 支持清空功能和禁用状态
- ✅ 支持自定义尺寸（large/default/small）
- ✅ 支持操作按钮（确定/取消）
- ✅ 平滑动画效果

**对齐方式优化：**
- **左对齐 (align="left")**：下拉面板左边沿与触发器左边沿对齐
- **右对齐 (align="right")**：下拉面板右边沿与触发器右边沿对齐
- **居中对齐 (align="center")**：下拉面板居中对齐，靠近边缘时自动调整

**基本用法：**
```vue
<template>
  <DropdownSelector
    v-model="selectedValue"
    placeholder="请选择"
    align="left"
    :clearable="true"
    @change="handleChange"
  >
    <!-- 自定义显示内容 -->
    <template #display>
      <span>{{ displayText }}</span>
    </template>

    <!-- 下拉内容 -->
    <div class="custom-content">
      <!-- 你的选择器内容 -->
    </div>
  </DropdownSelector>
</template>
```

**Props：**
- `modelValue`: 绑定值
- `placeholder`: 占位符文本，默认 '请选择'
- `disabled`: 是否禁用，默认 false
- `clearable`: 是否可清空，默认 true
- `size`: 尺寸大小，默认 'default'
- `align`: 对齐方式，默认 'left'
- `dropdownClass`: 下拉面板自定义类名
- `showActions`: 是否显示操作按钮，默认 false
- `closeOnSelect`: 选择后是否关闭，默认 true
- `width`: 自定义宽度
- `maxHeight`: 最大高度
- `placement`: 弹出位置，默认 'bottom-start'
- `displayText`: 自定义显示文本

### 6. StatusSelector - 状态选择器

专门用于状态选择的组件，支持多种预设状态类型和自定义状态。

**功能特性：**
- ✅ 多种预设状态类型（basic/agent/user/system）
- ✅ 支持嵌入模式和下拉模式
- ✅ 自定义状态选项
- ✅ 状态徽章样式
- ✅ 描述信息显示
- ✅ 继承 DropdownSelector 的所有对齐功能

**预设状态类型：**
- **basic**: 基础状态（全部、启用、禁用）
- **agent**: 智能体状态（全部状态、启用、禁用）
- **user**: 用户状态（全部状态、正常、禁用、锁定）
- **system**: 系统状态（全部状态、在线、离线、维护中）

**基本用法：**
```vue
<template>
  <!-- 下拉模式 -->
  <StatusSelector
    v-model="selectedStatus"
    mode="dropdown"
    preset="agent"
    align="left"
    :config="{
      showDescription: true,
      size: 'default'
    }"
    @change="handleStatusChange"
  />

  <!-- 嵌入模式 -->
  <StatusSelector
    v-model="selectedStatus"
    mode="embedded"
    preset="user"
    :config="{
      showDescription: true
    }"
  />
</template>
```

**自定义状态选项：**
```vue
<template>
  <StatusSelector
    v-model="selectedStatus"
    mode="dropdown"
    preset="custom"
    :options="customOptions"
    align="center"
  />
</template>

<script setup>
const customOptions = [
  {
    value: 'draft',
    label: '草稿',
    color: '#909399',
    bgColor: '#f4f4f5',
    description: '内容草稿状态'
  },
  {
    value: 'published',
    label: '已发布',
    color: '#16a34a',
    bgColor: '#dcfce7',
    description: '内容已发布'
  }
]
</script>
```

**Props：**
- `modelValue`: 绑定值，默认 null
- `config`: 配置选项对象，默认 {}
- `mode`: 显示模式，默认 'embedded'
- `options`: 自定义选项数组，默认 []
- `preset`: 预设类型，默认 'basic'
- `align`: 对齐方式，默认 'left'

**Config 配置选项：**
- `multiple`: 是否多选，默认 false
- `clearable`: 是否可清空，默认 true
- `placeholder`: 占位符，默认 '请选择状态'
- `size`: 尺寸大小，默认 'default'
- `disabled`: 是否禁用，默认 false
- `showDescription`: 显示描述，默认 false
- `showBadge`: 显示徽章，默认 true

## 测试页面

访问 `/status-selector-test` 查看状态选择器的完整演示和测试用例。
