package com.xhcai.modules.system.utils;

import com.xhcai.modules.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字典工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DictUtils {

    private static ISysDictDataService dictDataService;

    /**
     * 本地缓存，避免频繁查询数据库
     */
    private static final Map<String, String> DICT_CACHE = new ConcurrentHashMap<>();

    @Autowired
    public void setDictDataService(ISysDictDataService dictDataService) {
        DictUtils.dictDataService = dictDataService;
    }

    /**
     * 根据字典类型和字典值获取字典标签
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    public static String getDictLabel(String dictType, String dictValue) {
        if (!StringUtils.hasText(dictType) || !StringUtils.hasText(dictValue)) {
            return dictValue;
        }

        // 先从本地缓存获取
        String cacheKey = dictType + ":" + dictValue;
        String label = DICT_CACHE.get(cacheKey);
        if (label != null) {
            return label;
        }

        // 从服务获取
        if (dictDataService != null) {
            label = dictDataService.getDictLabel(dictType, dictValue);
            // 放入本地缓存
            DICT_CACHE.put(cacheKey, label != null ? label : dictValue);
            return label != null ? label : dictValue;
        }

        return dictValue;
    }

    /**
     * 获取用户性别标签
     *
     * @param genderValue 性别值
     * @return 性别标签
     */
    public static String getUserGenderLabel(String genderValue) {
        return getDictLabel("sys_user_gender", genderValue);
    }

    /**
     * 获取用户状态标签
     *
     * @param statusValue 状态值
     * @return 状态标签
     */
    public static String getUserStatusLabel(String statusValue) {
        return getDictLabel("sys_user_status", statusValue);
    }

    /**
     * 获取角色状态标签
     *
     * @param statusValue 状态值
     * @return 状态标签
     */
    public static String getRoleStatusLabel(String statusValue) {
        return getDictLabel("sys_role_status", statusValue);
    }

    /**
     * 获取部门状态标签
     *
     * @param statusValue 状态值
     * @return 状态标签
     */
    public static String getDeptStatusLabel(String statusValue) {
        return getDictLabel("sys_dept_status", statusValue);
    }

    /**
     * 获取权限状态标签
     *
     * @param statusValue 状态值
     * @return 状态标签
     */
    public static String getPermissionStatusLabel(String statusValue) {
        return getDictLabel("sys_permission_status", statusValue);
    }

    /**
     * 获取配置状态标签
     *
     * @param statusValue 状态值
     * @return 状态标签
     */
    public static String getConfigStatusLabel(String statusValue) {
        return getDictLabel("sys_config_status", statusValue);
    }

    /**
     * 获取数据范围标签
     *
     * @param dataScopeValue 数据范围值
     * @return 数据范围标签
     */
    public static String getDataScopeLabel(String dataScopeValue) {
        return getDictLabel("sys_data_scope", dataScopeValue);
    }

    /**
     * 获取是否标签
     *
     * @param yesNoValue 是否值
     * @return 是否标签
     */
    public static String getYesNoLabel(String yesNoValue) {
        return getDictLabel("sys_yes_no", yesNoValue);
    }

    /**
     * 清除本地缓存
     */
    public static void clearCache() {
        DICT_CACHE.clear();
    }

    /**
     * 清除指定字典类型的缓存
     *
     * @param dictType 字典类型
     */
    public static void clearCache(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            return;
        }

        DICT_CACHE.entrySet().removeIf(entry -> entry.getKey().startsWith(dictType + ":"));
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public static int getCacheSize() {
        return DICT_CACHE.size();
    }
}
