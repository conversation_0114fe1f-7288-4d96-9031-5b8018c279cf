package com.xhcai.modules.ai.dto;

import com.xhcai.common.api.dto.PageQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI聊天记录查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "AI聊天记录查询条件")
public class AiChatQueryDTO extends PageQueryDTO {

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private String sessionId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * AI模型名称
     */
    @Schema(description = "AI模型名称")
    private String modelName;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型", allowableValues = {"text", "image", "file"})
    private String messageType;

    /**
     * 状态
     */
    @Schema(description = "状态", allowableValues = {"0", "1"})
    private String status;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private String endTime;

    /**
     * 用户消息关键词
     */
    @Schema(description = "用户消息关键词")
    private String userMessage;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUserMessage() {
        return userMessage;
    }

    public void setUserMessage(String userMessage) {
        this.userMessage = userMessage;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "AiChatQueryDTO{"
                + "current=" + getCurrent()
                + ", size=" + getSize()
                + ", sessionId='" + sessionId + '\''
                + ", userId=" + userId
                + ", modelName='" + modelName + '\''
                + ", messageType='" + messageType + '\''
                + ", status='" + status + '\''
                + ", beginTime='" + beginTime + '\''
                + ", endTime='" + endTime + '\''
                + ", userMessage='" + userMessage + '\''
                + ", tenantId=" + tenantId
                + ", orderBy='" + getOrderBy() + '\''
                + ", orderDirection='" + getOrderDirection() + '\''
                + '}';
    }
}
