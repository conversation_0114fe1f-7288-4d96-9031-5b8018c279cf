-- 为agent表添加platform_id字段
-- 用于关联第三方智能体平台

-- 检查并添加platform_id字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent ADD COLUMN platform_id VARCHAR(36) COMMENT "第三方智能体平台ID，关联third_platform表"',
        'SELECT "platform_id字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent' 
        AND COLUMN_NAME = 'platform_id'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为platform_id字段添加索引（提高查询性能）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'CREATE INDEX idx_agent_platform_id ON agent(platform_id)',
        'SELECT "platform_id字段索引已存在" as message'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'agent' 
        AND INDEX_NAME = 'idx_agent_platform_id'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为platform_id和source_type组合添加复合索引（优化查询性能）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'CREATE INDEX idx_agent_platform_source ON agent(platform_id, source_type)',
        'SELECT "platform_id和source_type复合索引已存在" as message'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'agent' 
        AND INDEX_NAME = 'idx_agent_platform_source'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加外键约束（可选，根据需要启用）
-- 注意：如果third_platform表不存在或数据不完整，请注释掉以下部分
/*
SET @sql = (
    SELECT IF(
        COUNT(*) = 0 AND 
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'third_platform' AND TABLE_SCHEMA = DATABASE()) > 0,
        'ALTER TABLE agent ADD CONSTRAINT fk_agent_platform_id FOREIGN KEY (platform_id) REFERENCES third_platform(id) ON DELETE SET NULL ON UPDATE CASCADE',
        'SELECT "外键约束已存在或third_platform表不存在" as message'
    )
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE TABLE_NAME = 'agent' 
        AND CONSTRAINT_NAME = 'fk_agent_platform_id'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
*/

-- 显示表结构变更结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'agent' 
    AND TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME = 'platform_id'
ORDER BY ORDINAL_POSITION;

-- 显示相关索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'agent' 
    AND TABLE_SCHEMA = DATABASE()
    AND (INDEX_NAME LIKE '%platform%' OR COLUMN_NAME = 'platform_id')
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 显示字段添加成功信息
SELECT 'agent表platform_id字段添加完成' as result;
