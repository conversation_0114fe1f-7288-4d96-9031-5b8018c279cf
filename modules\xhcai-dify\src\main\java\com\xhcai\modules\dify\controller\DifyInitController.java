package com.xhcai.modules.dify.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.dify.init.DifyPermissionInitializer;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * Dify模块初始化控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Tag(name = "Dify模块初始化", description = "Dify模块初始化相关接口")
@RestController
@RequestMapping("/api/dify/init")
public class DifyInitController {

    @Autowired
    private DifyPermissionInitializer difyPermissionInitializer;

    @Operation(summary = "检查Dify模块初始化状态", description = "检查Dify模块是否已初始化")
    @GetMapping("/status")
    @RequiresPermissions("dify:init:status")
    public Result<Map<String, Object>> checkInitStatus() {
        log.info("检查Dify模块初始化状态");

        Map<String, Object> status = new HashMap<>();
        status.put("moduleId", "dify");
        status.put("moduleName", "Dify模块");
        boolean initialized = checkDifyInitialized();
        status.put("initialized", initialized);
        status.put("status", initialized ? "INITIALIZED" : "NOT_INITIALIZED");
        status.put("progress", initialized ? 100 : 0);
        status.put("message", initialized ? "Dify模块已初始化" : "Dify模块未初始化");

        return Result.success(status);
    }

    @Operation(summary = "初始化Dify模块", description = "执行Dify模块初始化")
    @PostMapping("/execute")
    @RequiresPermissions("dify:init:execute")
    public Result<Map<String, Object>> executeInit(
            @Parameter(description = "是否强制重新初始化") @RequestParam(defaultValue = "false") Boolean forceReinit) {
        // 从当前登录用户获取租户ID
        String tenantId = com.xhcai.common.security.utils.SecurityUtils.getCurrentTenantId();
        log.info("执行Dify模块初始化: tenantId={}, forceReinit={}", tenantId, forceReinit);

        Map<String, Object> result = new HashMap<>();
        result.put("moduleId", "dify");
        result.put("moduleName", "Dify模块");
        result.put("startTime", System.currentTimeMillis());

        try {
            // 检查是否已初始化
            if (checkDifyInitialized() && !forceReinit) {
                result.put("status", "SKIPPED");
                result.put("message", "Dify模块已初始化，跳过");
                result.put("progress", 100);
                return Result.success(result);
            }

            // 执行初始化
            result.put("progress", 50);
            difyPermissionInitializer.initializePermissions();

            result.put("status", "SUCCESS");
            result.put("message", "Dify模块初始化成功");
            result.put("progress", 100);

        } catch (Exception e) {
            log.error("Dify模块初始化失败", e);
            result.put("status", "FAILED");
            result.put("message", "Dify模块初始化失败: " + e.getMessage());
            result.put("progress", 0);
        } finally {
            result.put("endTime", System.currentTimeMillis());
            long duration = (Long) result.get("endTime") - (Long) result.get("startTime");
            result.put("duration", duration);
        }

        return Result.success(result);
    }

    @Operation(summary = "获取Dify模块信息", description = "获取Dify模块的基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> getModuleInfo() {
        log.info("获取Dify模块信息");

        Map<String, Object> info = new HashMap<>();
        info.put("moduleId", "dify");
        info.put("moduleName", "Dify模块");
        info.put("version", "1.0.0");
        info.put("author", "xhcai");
        info.put("description", "Dify平台集成模块，提供智能体和知识库管理功能");
        info.put("features", new String[]{
            "Dify智能体管理", "Dify知识库管理", "Dify插件管理",
            "智能体对话", "知识库检索", "Dify权限管理"
        });
        info.put("apiPrefix", "/api/dify");
        info.put("order", 300);

        return Result.success(info);
    }

    /**
     * 检查Dify模块是否已初始化
     */
    private boolean checkDifyInitialized() {
        try {
            // 这里可以通过查询数据库中的权限数据来判断是否已初始化
            // 暂时返回false，表示未初始化
            return false;
        } catch (Exception e) {
            log.debug("检查Dify初始化状态失败", e);
            return false;
        }
    }
}
