package com.yyzs.agent.service.impl;

import com.yyzs.agent.service.FileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * 文件存储服务实现类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class FileStorageServiceImpl implements FileStorageService {

    @Override
    public String storeFile(MultipartFile file, String directory) throws IOException {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 创建目录
        Path directoryPath = Paths.get(directory);
        if (!Files.exists(directoryPath)) {
            Files.createDirectories(directoryPath);
        }

        // 生成唯一文件名
        String uniqueFileName = generateUniqueFileName(file.getOriginalFilename());
        Path targetPath = directoryPath.resolve(uniqueFileName);

        // 存储文件
        Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
        
        log.info("文件存储成功: {}", targetPath.toString());
        return targetPath.toString();
    }

    @Override
    public boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.info("文件删除成功: {}", filePath);
                return true;
            }
            return false;
        } catch (IOException e) {
            log.error("删除文件失败: " + filePath, e);
            return false;
        }
    }

    @Override
    public Path getFilePath(String fileName, String directory) {
        return Paths.get(directory, fileName);
    }

    @Override
    public boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }

    @Override
    public long getFileSize(String filePath) {
        try {
            return Files.size(Paths.get(filePath));
        } catch (IOException e) {
            log.error("获取文件大小失败: " + filePath, e);
            return 0;
        }
    }

    @Override
    public boolean createDirectory(String directory) {
        try {
            Path directoryPath = Paths.get(directory);
            if (!Files.exists(directoryPath)) {
                Files.createDirectories(directoryPath);
                log.info("目录创建成功: {}", directory);
            }
            return true;
        } catch (IOException e) {
            log.error("创建目录失败: " + directory, e);
            return false;
        }
    }

    @Override
    public boolean cleanDirectory(String directory) {
        try {
            Path directoryPath = Paths.get(directory);
            if (Files.exists(directoryPath)) {
                FileUtils.cleanDirectory(directoryPath.toFile());
                log.info("目录清理成功: {}", directory);
            }
            return true;
        } catch (IOException e) {
            log.error("清理目录失败: " + directory, e);
            return false;
        }
    }

    @Override
    public String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1);
    }

    @Override
    public String generateUniqueFileName(String originalFileName) {
        String extension = getFileExtension(originalFileName);
        String baseName = originalFileName;
        
        if (!extension.isEmpty()) {
            baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
        }
        
        String uniqueId = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        if (extension.isEmpty()) {
            return baseName + "_" + timestamp + "_" + uniqueId;
        } else {
            return baseName + "_" + timestamp + "_" + uniqueId + "." + extension;
        }
    }
}
