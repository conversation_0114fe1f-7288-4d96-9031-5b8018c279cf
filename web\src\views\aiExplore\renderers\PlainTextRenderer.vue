<template>
  <div class="plain-text-renderer">
    <!-- 思考内容块 -->
    <ThinkingBlock
      v-if="thinkingContent"
      :content="thinkingContent"
      :streaming="streaming && isThinkingStreaming"
      :completed="!streaming || !isThinkingStreaming"
    />

    <!-- 主要内容 -->
    <div
      v-if="mainProcessedContent && streaming"
      class="streaming-text"
      v-html="mainProcessedContent"
    ></div>
    <div
      v-else-if="mainProcessedContent"
      class="static-text"
      v-html="mainProcessedContent"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue'
import ThinkingBlock from '@/views/aiExplore/ThinkingBlock.vue'

// Props定义
const props = defineProps<{
  content: string
  streaming?: boolean
}>()

// Emits定义
const emit = defineEmits<{
  linkClick: [url: string]
}>()

// URL正则表达式
const urlRegex = /(https?:\/\/[^\s]+)/g

// 解析思考内容和主要内容
const parseThinkingContent = (content: string) => {
  const thinkRegex = /<think>([\s\S]*?)<\/think>/g
  let match
  let thinking = ''
  let main = content

  // 提取思考内容
  while ((match = thinkRegex.exec(content)) !== null) {
    thinking += match[1]
  }

  // 移除思考标签，保留主要内容
  main = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim()

  return { thinking, main }
}

// 检查是否在思考标签内流式输出
const checkThinkingStreaming = (content: string) => {
  const openTags = (content.match(/<think>/g) || []).length
  const closeTags = (content.match(/<\/think>/g) || []).length
  return openTags > closeTags
}

// 提取流式思考内容（处理未完成的标签）
const extractStreamingThinkingContent = (content: string) => {
  // 检查是否有未完成的 <think> 标签
  const openTagIndex = content.lastIndexOf('<think>')
  const closeTagIndex = content.lastIndexOf('</think>')

  if (openTagIndex > closeTagIndex) {
    // 有未完成的 <think> 标签，提取其中的内容
    const thinkingStart = openTagIndex + 7 // '<think>'.length
    return content.substring(thinkingStart)
  }

  return ''
}

// 清理内容中的思考标签（包括未完成的标签）
const cleanContentFromThinkingTags = (content: string) => {
  // 移除完整的思考标签
  let cleaned = content.replace(/<think>[\s\S]*?<\/think>/g, '')

  // 移除未完成的 <think> 标签及其后面的内容
  const openTagIndex = cleaned.lastIndexOf('<think>')
  const closeTagIndex = cleaned.lastIndexOf('</think>')

  if (openTagIndex > closeTagIndex) {
    // 有未完成的 <think> 标签，移除它及其后面的内容
    cleaned = cleaned.substring(0, openTagIndex)
  }

  return cleaned.trim()
}

// 思考内容
const thinkingContent = computed(() => {
  if (!props.content) return ''

  // 检查是否在思考中流式输出
  if (props.streaming && checkThinkingStreaming(props.content)) {
    // 正在思考中，提取流式思考内容
    return extractStreamingThinkingContent(props.content)
  } else {
    // 思考完成或没有思考内容，正常解析
    const { thinking } = parseThinkingContent(props.content)
    return thinking
  }
})

// 是否在思考中流式输出
const isThinkingStreaming = computed(() => {
  return props.streaming && checkThinkingStreaming(props.content)
})

// 处理主要文本内容
const mainProcessedContent = computed(() => {
  if (!props.content) return ''

  // 移除PlainTextRenderer内容更新的控制台输出
  // if (import.meta.env.DEV && props.streaming) {
  //   console.log('PlainTextRenderer 内容更新:', props.content.length, '字符')
  // }

  let main = ''

  // 检查是否在思考中流式输出
  if (props.streaming && checkThinkingStreaming(props.content)) {
    // 正在思考中，清理主要内容中的思考标签
    main = cleanContentFromThinkingTags(props.content)
  } else {
    // 思考完成或没有思考内容，正常解析
    const parsed = parseThinkingContent(props.content)
    main = parsed.main
  }

  if (!main) return ''

  let processed = main

  // 转义HTML特殊字符
  processed = processed
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')

  // 处理换行
  processed = processed.replace(/\n/g, '<br>')

  // 处理URL链接
  processed = processed.replace(urlRegex, (url) => {
    return `<a href="${url}" class="text-link" data-url="${url}">${url}</a>`
  })

  // 如果是流式输出且不在思考中，添加光标
  if (props.streaming && !isThinkingStreaming.value) {
    processed += '<span class="streaming-cursor">|</span>'
  }

  return processed
})

// 处理点击事件
const handleClick = (event: Event) => {
  const target = event.target as HTMLElement
  
  if (target.classList.contains('text-link')) {
    event.preventDefault()
    const url = target.getAttribute('data-url')
    if (url) {
      emit('linkClick', url)
    }
  }
}
</script>

<style scoped>
.plain-text-renderer {
  width: 100%;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: 1.5;
}

.plain-text-renderer :deep(.text-link) {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s;
}

.plain-text-renderer :deep(.text-link:hover) {
  color: #2563eb;
}

.plain-text-renderer :deep(.streaming-cursor) {
  animation: blink 1s infinite;
  color: #3b82f6;
  font-weight: bold;
  margin-left: 1px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
