package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.rag.service.IOnlyOfficeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * OnlyOffice控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/onlyoffice")
@Tag(name = "OnlyOffice文档预览", description = "OnlyOffice文档预览相关接口")
public class OnlyOfficeController {

    @Autowired
    private IOnlyOfficeService onlyOfficeService;

    @Operation(summary = "获取文档配置", description = "获取OnlyOffice文档预览配置")
    @GetMapping("/config/{documentId}")
    public Result<Map<String, Object>> getDocumentConfig(
            @Parameter(description = "文档ID") @PathVariable String documentId,
            @Parameter(description = "模式") @RequestParam(defaultValue = "view") String mode) {
        
        log.info("获取OnlyOffice文档配置: documentId={}, mode={}", documentId, mode);

        try {
            // TODO: 这里需要获取实际的文件信息
            String fileName = "document_" + documentId + ".docx";
            String fileUrl = "http://localhost:8080/api/rag/documents/" + documentId + "/download";
            
            Map<String, Object> config = onlyOfficeService.generateDocumentConfig(documentId, fileName, fileUrl, mode);
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取OnlyOffice文档配置失败: documentId={}, error={}", documentId, e.getMessage(), e);
            return Result.fail("获取文档配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "OnlyOffice回调", description = "处理OnlyOffice编辑器回调")
    @PostMapping("/callback")
    public Map<String, Object> callback(
            @Parameter(description = "文档ID") @RequestParam(required = false) String documentId,
            @RequestBody Map<String, Object> callbackData) {
        
        log.info("OnlyOffice回调: documentId={}, data={}", documentId, callbackData);

        try {
            return onlyOfficeService.handleCallback(callbackData);
        } catch (Exception e) {
            log.error("处理OnlyOffice回调失败: documentId={}, error={}", documentId, e.getMessage(), e);
            return Map.of("error", 1);
        }
    }

    @Operation(summary = "检查文档格式支持", description = "检查文档格式是否支持预览")
    @GetMapping("/check-format/{format}")
    public Result<Map<String, Boolean>> checkFormat(
            @Parameter(description = "文件格式") @PathVariable String format) {
        
        boolean supported = onlyOfficeService.isSupportedFormat(format);
        boolean editable = onlyOfficeService.isEditableFormat(format);
        
        Map<String, Boolean> result = Map.of(
            "supported", supported,
            "editable", editable
        );
        
        return Result.success(result);
    }

    @Operation(summary = "生成预览URL", description = "生成文档预览URL")
    @GetMapping("/preview-url/{documentId}")
    public Result<String> generatePreviewUrl(
            @Parameter(description = "文档ID") @PathVariable String documentId) {
        
        try {
            String previewUrl = onlyOfficeService.generatePreviewUrl(documentId);
            return Result.success(previewUrl);
        } catch (Exception e) {
            log.error("生成预览URL失败: documentId={}, error={}", documentId, e.getMessage(), e);
            return Result.fail("生成预览URL失败: " + e.getMessage());
        }
    }
}
