package com.xhcai.modules.dify.service;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.dto.plugin.DifyPluginDTO;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 插件服务接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IDifyPluginService {

    /**
     * 获取插件列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param keyword 关键词
     * @param category 分类
     * @param type 类型
     * @param status 状态
     * @return 插件列表
     */
    Mono<Result<PageResult<DifyPluginDTO>>> getPluginList(int page, int size, String keyword, String category, String type, String status);

    /**
     * 获取插件详情
     *
     * @param pluginId 插件ID
     * @return 插件详情
     */
    Mono<Result<DifyPluginDTO>> getPlugin(String pluginId);

    /**
     * 安装插件
     *
     * @param pluginId 插件ID
     * @param config 插件配置
     * @return 安装结果
     */
    Mono<Result<DifyPluginDTO>> installPlugin(String pluginId, Map<String, Object> config);

    /**
     * 卸载插件
     *
     * @param pluginId 插件ID
     * @return 卸载结果
     */
    Mono<Result<Object>> uninstallPlugin(String pluginId);

    /**
     * 更新插件配置
     *
     * @param pluginId 插件ID
     * @param config 插件配置
     * @return 更新结果
     */
    Mono<Result<DifyPluginDTO>> updatePluginConfig(String pluginId, Map<String, Object> config);

    /**
     * 启用插件
     *
     * @param pluginId 插件ID
     * @return 启用结果
     */
    Mono<Result<Object>> enablePlugin(String pluginId);

    /**
     * 禁用插件
     *
     * @param pluginId 插件ID
     * @return 禁用结果
     */
    Mono<Result<Object>> disablePlugin(String pluginId);

    /**
     * 调用插件工具
     *
     * @param pluginId 插件ID
     * @param toolName 工具名称
     * @param parameters 参数
     * @return 调用结果
     */
    Mono<Result<Object>> invokePluginTool(String pluginId, String toolName, Map<String, Object> parameters);

    /**
     * 获取插件工具列表
     *
     * @param pluginId 插件ID
     * @return 工具列表
     */
    Mono<Result<Object>> getPluginTools(String pluginId);

    /**
     * 获取已安装插件列表
     *
     * @param page 页码
     * @param size 每页大小
     * @return 已安装插件列表
     */
    Mono<Result<PageResult<DifyPluginDTO>>> getInstalledPlugins(int page, int size);

    /**
     * 获取插件使用统计
     *
     * @param pluginId 插件ID
     * @return 使用统计
     */
    Mono<Result<Object>> getPluginStats(String pluginId);

    /**
     * 检查插件更新
     *
     * @param pluginId 插件ID
     * @return 更新信息
     */
    Mono<Result<Object>> checkPluginUpdate(String pluginId);

    /**
     * 更新插件
     *
     * @param pluginId 插件ID
     * @return 更新结果
     */
    Mono<Result<DifyPluginDTO>> updatePlugin(String pluginId);

    /**
     * 获取插件日志
     *
     * @param pluginId 插件ID
     * @param page 页码
     * @param size 每页大小
     * @return 插件日志
     */
    Mono<Result<PageResult<Object>>> getPluginLogs(String pluginId, int page, int size);

    /**
     * 测试插件连接
     *
     * @param pluginId 插件ID
     * @return 测试结果
     */
    Mono<Result<Object>> testPluginConnection(String pluginId);
}
