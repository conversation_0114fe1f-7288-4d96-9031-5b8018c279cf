package com.xhcai.modules.dify.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.dify.vo.ThirdPlatformVO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformQueryDTO;
import com.xhcai.modules.dify.entity.ThirdPlatform;

import java.util.List;

/**
 * 第三方智能体服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IThirdPlatformService extends IService<ThirdPlatform> {

    /**
     * 分页查询第三方智能体列表
     *
     * @param queryDTO 查询条件
     * @return 第三方智能体VO分页结果
     */
    PageResult<ThirdPlatformVO> getPlatformPage(ThirdPlatformQueryDTO queryDTO);

    List<ThirdPlatformVO> getPlatformList(ThirdPlatformQueryDTO queryDTO);

    /**
     * 根据ID查询第三方智能体详情
     *
     * @param id 主键ID
     * @return 第三方智能体VO
     */
    ThirdPlatformVO getPlatformById(String id);

    /**
     * 新增第三方智能体
     *
     * @param agent 第三方智能体实体
     * @return 是否成功
     */
    boolean addPlatform(ThirdPlatform agent);

    /**
     * 修改第三方智能体
     *
     * @param agent 第三方智能体实体
     * @return 是否成功
     */
    boolean updatePlatform(ThirdPlatform agent);

    /**
     * 删除第三方智能体
     *
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deletePlatform(String id);

    /**
     * 批量删除第三方智能体
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean batchDeletePlatforms(List<String> ids);

    /**
     * 启用/禁用第三方智能体
     *
     * @param id 主键ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    boolean toggleStatus(String id, Integer status);

    /**
     * 批量启用/禁用第三方智能体
     *
     * @param ids 主键ID列表
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    boolean batchToggleStatus(List<String> ids, Integer status);

    /**
     * 测试第三方智能体连接
     *
     * @param id 主键ID
     * @return 测试结果信息
     */
    String testConnection(String id);

    /**
     * 查询用户可访问的第三方智能体列表
     *
     * @param userId 用户ID
     * @return 第三方智能体列表
     */
    List<ThirdPlatformVO> getAccessiblePlatforms(String userId);

    /**
     * 检查用户是否有权限访问指定智能体
     *
     * @param agentId 智能体ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean checkUserAccess(String agentId, String userId);

    /**
     * 获取智能体统计信息
     *
     * @return 统计信息Map
     */
    java.util.Map<String, Object> getPlatformStatistics();

    /**
     * 根据访问范围统计智能体数量
     *
     * @return 访问范围统计Map
     */
    java.util.Map<String, Long> getAccessScopeStatistics();

    /**
     * 验证智能体配置
     *
     * @param agent 智能体实体
     * @return 验证结果信息
     */
    String validatePlatformConfig(ThirdPlatform agent);

    /**
     * 复制智能体配置
     *
     * @param sourceId 源智能体ID
     * @param newName 新智能体名称
     * @return 是否成功
     */
    boolean copyPlatform(String sourceId, String newName);

    /**
     * 导出智能体配置
     *
     * @param ids 智能体ID列表
     * @return 导出数据
     */
    List<ThirdPlatformVO> exportPlatforms(List<String> ids);

    /**
     * 导入智能体配置
     *
     * @param agents 智能体列表
     * @return 导入结果信息
     */
    String importPlatforms(List<ThirdPlatform> agents);
}
