package com.xhcai.modules.dify.vo;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户第三方智能体账号VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "用户第三方智能体账号VO")
public class ThirdPlatformAccountVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID", example = "1")
    private String id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private String userId;

    /**
     * 第三方平台ID（字典值）
     */
    @Schema(description = "第三方平台ID", example = "dify")
    private String platformId;

    /**
     * 第三方平台名称
     */
    @Schema(description = "第三方平台名称", example = "Dify")
    private String platformName;

    /**
     * 第三方平台图标
     */
    @Schema(description = "第三方平台图标", example = "🤖")
    private String platformIcon;

    /**
     * 第三方平台图标背景色
     */
    @Schema(description = "第三方平台图标背景色", example = "#3b82f6")
    private String platformIconBg;

    /**
     * 第三方平台描述
     */
    @Schema(description = "第三方平台描述", example = "Dify是一个开源的LLM应用开发平台")
    private String platformDescription;

    /**
     * 账号名称
     */
    @Schema(description = "账号名称", example = "我的Dify账号")
    private String accountName;

    /**
     * API密钥（脱敏显示）
     */
    @Schema(description = "API密钥（脱敏显示）", example = "app-***")
    private String apiKey;

    /**
     * 是否设置了密码
     */
    @Schema(description = "是否设置了密码", example = "true")
    private Boolean hasPwd;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "用于测试的账号")
    private String remark;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 状态文本
     */
    @Schema(description = "状态文本", example = "启用")
    private String statusText;

    /**
     * 最后连接测试时间
     */
    @Schema(description = "最后连接测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastTestTime;

    /**
     * 最后连接测试结果：0-失败，1-成功
     */
    @Schema(description = "最后连接测试结果：0-失败，1-成功", example = "1")
    private Integer lastTestResult;

    /**
     * 最后连接测试结果文本
     */
    @Schema(description = "最后连接测试结果文本", example = "成功")
    private String lastTestResultText;

    /**
     * 最后连接测试错误信息
     */
    @Schema(description = "最后连接测试错误信息")
    private String lastTestError;

    /**
     * 总调用次数
     */
    @Schema(description = "总调用次数", example = "100")
    private Long totalCalls;

    /**
     * 成功调用次数
     */
    @Schema(description = "成功调用次数", example = "95")
    private Long successCalls;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "95.0")
    private Double successRate;

    /**
     * 平均响应时间（毫秒）
     */
    @Schema(description = "平均响应时间（毫秒）", example = "1200")
    private Long avgResponseTime;

    /**
     * 最后使用时间
     */
    @Schema(description = "最后使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUsedTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getPlatformIcon() {
        return platformIcon;
    }

    public void setPlatformIcon(String platformIcon) {
        this.platformIcon = platformIcon;
    }

    public String getPlatformIconBg() {
        return platformIconBg;
    }

    public void setPlatformIconBg(String platformIconBg) {
        this.platformIconBg = platformIconBg;
    }

    public String getPlatformDescription() {
        return platformDescription;
    }

    public void setPlatformDescription(String platformDescription) {
        this.platformDescription = platformDescription;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public Boolean getHasPwd() {
        return hasPwd;
    }

    public void setHasPwd(Boolean hasPwd) {
        this.hasPwd = hasPwd;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }

    public LocalDateTime getLastTestTime() {
        return lastTestTime;
    }

    public void setLastTestTime(LocalDateTime lastTestTime) {
        this.lastTestTime = lastTestTime;
    }

    public Integer getLastTestResult() {
        return lastTestResult;
    }

    public void setLastTestResult(Integer lastTestResult) {
        this.lastTestResult = lastTestResult;
    }

    public String getLastTestResultText() {
        return lastTestResultText;
    }

    public void setLastTestResultText(String lastTestResultText) {
        this.lastTestResultText = lastTestResultText;
    }

    public String getLastTestError() {
        return lastTestError;
    }

    public void setLastTestError(String lastTestError) {
        this.lastTestError = lastTestError;
    }

    public Long getTotalCalls() {
        return totalCalls;
    }

    public void setTotalCalls(Long totalCalls) {
        this.totalCalls = totalCalls;
    }

    public Long getSuccessCalls() {
        return successCalls;
    }

    public void setSuccessCalls(Long successCalls) {
        this.successCalls = successCalls;
    }

    public Double getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(Double successRate) {
        this.successRate = successRate;
    }

    public Long getAvgResponseTime() {
        return avgResponseTime;
    }

    public void setAvgResponseTime(Long avgResponseTime) {
        this.avgResponseTime = avgResponseTime;
    }

    public LocalDateTime getLastUsedTime() {
        return lastUsedTime;
    }

    public void setLastUsedTime(LocalDateTime lastUsedTime) {
        this.lastUsedTime = lastUsedTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ThirdPlatformAccountVO{" +
                "id='" + id + '\'' +
                ", userId='" + userId + '\'' +
                ", platformId='" + platformId + '\'' +
                ", platformName='" + platformName + '\'' +
                ", accountName='" + accountName + '\'' +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", statusText='" + statusText + '\'' +
                ", lastTestTime=" + lastTestTime +
                ", lastTestResult=" + lastTestResult +
                ", lastTestResultText='" + lastTestResultText + '\'' +
                ", totalCalls=" + totalCalls +
                ", successCalls=" + successCalls +
                ", successRate=" + successRate +
                ", avgResponseTime=" + avgResponseTime +
                ", lastUsedTime=" + lastUsedTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
