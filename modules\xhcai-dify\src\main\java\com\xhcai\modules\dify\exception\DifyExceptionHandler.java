package com.xhcai.modules.dify.exception;

import com.xhcai.common.api.response.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.reactive.function.client.WebClientResponseException;

/**
 * Dify模块全局异常处理器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@RestControllerAdvice(basePackages = "com.xhcai.modules.dify")
public class DifyExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(DifyExceptionHandler.class);

    /**
     * 处理Dify自定义异常
     */
    @ExceptionHandler(DifyException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleDifyException(DifyException e) {
        log.error("Dify模块异常: {}", e.getMessage(), e);
        return Result.fail(e.getCode(), e.getMessage());
    }

    /**
     * 处理WebClient响应异常
     */
    @ExceptionHandler(WebClientResponseException.class)
    public Result<Void> handleWebClientResponseException(WebClientResponseException e) {
        log.error("Dify API调用异常: {} - {}", e.getStatusCode(), e.getResponseBodyAsString(), e);
        
        HttpStatus status = (HttpStatus) e.getStatusCode();
        String message = "API调用失败";
        
        switch (status) {
            case UNAUTHORIZED:
                message = "API密钥无效或已过期";
                break;
            case FORBIDDEN:
                message = "没有权限访问该资源";
                break;
            case NOT_FOUND:
                message = "请求的资源不存在";
                break;
            case TOO_MANY_REQUESTS:
                message = "请求过于频繁，请稍后重试";
                break;
            case INTERNAL_SERVER_ERROR:
            case BAD_GATEWAY:
            case SERVICE_UNAVAILABLE:
            case GATEWAY_TIMEOUT:
                message = "服务器内部错误，请稍后重试";
                break;
            default:
                message = "API调用失败: " + status.getReasonPhrase();
        }
        
        return Result.fail(status.value(), message);
    }

    /**
     * 处理其他运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleRuntimeException(RuntimeException e) {
        log.error("Dify模块运行时异常", e);
        return Result.fail(500, "系统异常: " + e.getMessage());
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e) {
        log.error("Dify模块未知异常", e);
        return Result.fail(500, "系统异常，请联系管理员");
    }
}
