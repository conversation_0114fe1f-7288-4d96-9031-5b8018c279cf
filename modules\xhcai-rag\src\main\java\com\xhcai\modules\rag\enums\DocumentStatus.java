package com.xhcai.modules.rag.enums;

import lombok.Getter;

/**
 * 文档状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum DocumentStatus {

    UPLOADING("uploading", "上传中", 1, "", "#dbeafe"),
    UPLOADED("uploaded", "上传成功", 2, "", "#e9eaeb"),
    UPLOAD_ERROR("upload_error", "上传失败", 3, "", "#fef2f2"),
    SAVE_ERROR("save_error", "保存失败", 3, "", "#fef2f2"),
    WAITING("waiting", "等待处理", 4, "", "#dbeafe"),
    WAITING_ERROR("waiting_error", "等待失败", 4, "", "#fef2f2"),
    DOWNLOAD_FILE_ERROR("download_file_error", "拉取文件失败", 4, "", "#fef2f2"),
    PROCESSING("processing", "处理中", 5, "", "#dbeafe"),
    PROCESSING_ERROR("processing_error", "处理失败", 5, "", "#fef2f2"),
    PARSING("parsing", "解析中", 6, "", "#dbeafe"),
    CLEANING("cleaning", "清理中", 7, "", "#dbeafe"),
    SEGMENTING("segmenting", "分段中", 8, "", "#dbeafe"),
    SEGMENTED("segmented", "分段完成", 9, "", "#e9eaeb"),
    SEGMENT_ERROR("segment_error", "分段失败", 10, "", "#fef2f2"),
    INDEX_WAITING("index_waiting", "等待索引", 11, "", "#dbeafe"),
    INDEXING("indexing", "索引中", 11, "", "#dbeafe"),
    INDEXED("indexed", "索引完成", 12, "", "#e9eaeb"),
    INDEX_ERROR("index_error", "索引错误", 13, "", "#fef2f2"),
    PAUSED("paused", "已暂停", 14, "", "#fef3c7"),
    STOPPED("stopped", "已停止", 15, "", "#f3f4f6");

    private final String code;
    private final String description;
    private final int order;
    private final String icon;
    private final String css;

    DocumentStatus(String code, String description, int order, String icon, String css) {
        this.code = code;
        this.description = description;
        this.order = order;
        this.icon = icon;
        this.css = css;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 文档状态枚举
     */
    public static DocumentStatus fromCode(String code) {
        for (DocumentStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的文档状态代码: " + code);
    }

    /**
     * 判断是否为处理中状态
     *
     * @return 是否为处理中状态
     */
    public boolean isProcessing() {
        return this == PROCESSING || this == PARSING || this == CLEANING ||
                this == SEGMENTING || this == INDEXING;
    }
}
