package com.xhcai.common.datasource.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * JPA配置类
 * 用于自动建表功能，仅在开发环境启用
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableJpaRepositories(basePackages = {
    "com.xhcai.modules.*.repository"
})
@EnableTransactionManagement
@ConditionalOnProperty(name = "spring.jpa.hibernate.ddl-auto", havingValue = "update", matchIfMissing = false)
public class JpaConfig {
    
    // JPA配置将通过application.yml进行配置
    // 这里主要用于启用JPA Repository扫描
}
