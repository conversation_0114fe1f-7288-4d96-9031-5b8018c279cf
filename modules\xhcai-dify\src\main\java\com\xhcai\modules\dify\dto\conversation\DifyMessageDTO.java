package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * Dify 会话消息 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify 会话消息")
public class DifyMessageDTO {

    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    private String id;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 输入参数
     */
    @Schema(description = "输入参数")
    private Map<String, Object> inputs;

    /**
     * 查询内容
     */
    @Schema(description = "查询内容")
    private String query;

    /**
     * 答案内容
     */
    @Schema(description = "答案内容")
    private String answer;

    /**
     * 消息文件列表
     */
    @Schema(description = "消息文件列表")
    @JsonProperty("message_files")
    private List<MessageFile> messageFiles;

    /**
     * 反馈信息
     */
    @Schema(description = "反馈信息")
    private Feedback feedback;

    /**
     * 检索资源信息
     */
    @Schema(description = "检索资源信息")
    @JsonProperty("retriever_resources")
    private List<Retrieval> retrieverResources;

    /**
     * 创建时间戳
     */
    @Schema(description = "创建时间戳")
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * 智能体思考过程
     */
    @Schema(description = "智能体思考过程")
    @JsonProperty("agent_thoughts")
    private List<AgentThought> agentThoughts;

    /**
     * 父消息ID
     */
    @Schema(description = "父消息ID")
    @JsonProperty("parent_message_id")
    private String parentMessageId;

    /**
     * 消息状态
     */
    @Schema(description = "消息状态")
    private String status;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String error;

    /**
     * 消息文件
     */
    @Schema(description = "消息文件")
    public static class MessageFile {
        /**
         * 文件ID
         */
        @Schema(description = "文件ID")
        private String id;

        /**
         * 文件名
         */
        @Schema(description = "文件名")
        private String filename;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "document")
        private String type;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL")
        private String url;

        /**
         * MIME类型
         */
        @Schema(description = "MIME类型", example = "text/plain")
        @JsonProperty("mime_type")
        private String mimeType;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）")
        private Long size;

        /**
         * 传输方式
         */
        @Schema(description = "传输方式", example = "local_file")
        @JsonProperty("transfer_method")
        private String transferMethod;

        /**
         * 归属者
         */
        @Schema(description = "归属者", example = "user")
        @JsonProperty("belongs_to")
        private String belongsTo;

        /**
         * 上传文件ID
         */
        @Schema(description = "上传文件ID")
        @JsonProperty("upload_file_id")
        private String uploadFileId;

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFilename() {
            return filename;
        }

        public void setFilename(String filename) {
            this.filename = filename;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getMimeType() {
            return mimeType;
        }

        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }

        public Long getSize() {
            return size;
        }

        public void setSize(Long size) {
            this.size = size;
        }

        public String getTransferMethod() {
            return transferMethod;
        }

        public void setTransferMethod(String transferMethod) {
            this.transferMethod = transferMethod;
        }

        public String getBelongsTo() {
            return belongsTo;
        }

        public void setBelongsTo(String belongsTo) {
            this.belongsTo = belongsTo;
        }

        public String getUploadFileId() {
            return uploadFileId;
        }

        public void setUploadFileId(String uploadFileId) {
            this.uploadFileId = uploadFileId;
        }

        @Override
        public String toString() {
            return "MessageFile{" +
                    "id='" + id + '\'' +
                    ", filename='" + filename + '\'' +
                    ", type='" + type + '\'' +
                    ", url='" + url + '\'' +
                    ", mimeType='" + mimeType + '\'' +
                    ", size=" + size +
                    ", transferMethod='" + transferMethod + '\'' +
                    ", belongsTo='" + belongsTo + '\'' +
                    ", uploadFileId='" + uploadFileId + '\'' +
                    '}';
        }
    }

    /**
     * 反馈信息
     */
    @Schema(description = "反馈信息")
    public static class Feedback {
        @Schema(description = "评分")
        private String rating;

        @Schema(description = "反馈内容")
        private String content;

        // Getters and Setters
        public String getRating() {
            return rating;
        }

        public void setRating(String rating) {
            this.rating = rating;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    /**
     * 检索信息
     */
    @Schema(description = "检索信息")
    public static class Retrieval {
        @Schema(description = "检索ID")
        private String id;

        @Schema(description = "检索内容")
        private String content;

        @Schema(description = "相似度分数")
        private Double score;

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Double getScore() {
            return score;
        }

        public void setScore(Double score) {
            this.score = score;
        }
    }

    /**
     * 智能体思考过程
     */
    @Schema(description = "智能体思考过程")
    public static class AgentThought {
        @Schema(description = "思考ID")
        private String id;

        @Schema(description = "思考内容")
        private String thought;

        @Schema(description = "工具调用")
        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls;

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getThought() {
            return thought;
        }

        public void setThought(String thought) {
            this.thought = thought;
        }

        public List<ToolCall> getToolCalls() {
            return toolCalls;
        }

        public void setToolCalls(List<ToolCall> toolCalls) {
            this.toolCalls = toolCalls;
        }
    }

    /**
     * 工具调用
     */
    @Schema(description = "工具调用")
    public static class ToolCall {
        @Schema(description = "工具名称")
        @JsonProperty("tool_name")
        private String toolName;

        @Schema(description = "工具输入")
        @JsonProperty("tool_input")
        private Map<String, Object> toolInput;

        @Schema(description = "工具输出")
        @JsonProperty("tool_output")
        private String toolOutput;

        // Getters and Setters
        public String getToolName() {
            return toolName;
        }

        public void setToolName(String toolName) {
            this.toolName = toolName;
        }

        public Map<String, Object> getToolInput() {
            return toolInput;
        }

        public void setToolInput(Map<String, Object> toolInput) {
            this.toolInput = toolInput;
        }

        public String getToolOutput() {
            return toolOutput;
        }

        public void setToolOutput(String toolOutput) {
            this.toolOutput = toolOutput;
        }
    }

    // Main class Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public List<MessageFile> getMessageFiles() {
        return messageFiles;
    }

    public void setMessageFiles(List<MessageFile> messageFiles) {
        this.messageFiles = messageFiles;
    }

    public Feedback getFeedback() {
        return feedback;
    }

    public void setFeedback(Feedback feedback) {
        this.feedback = feedback;
    }

    public List<Retrieval> getRetrieverResources() {
        return retrieverResources;
    }

    public void setRetrieverResources(List<Retrieval> retrieverResources) {
        this.retrieverResources = retrieverResources;
    }

    public String getParentMessageId() {
        return parentMessageId;
    }

    public void setParentMessageId(String parentMessageId) {
        this.parentMessageId = parentMessageId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public List<AgentThought> getAgentThoughts() {
        return agentThoughts;
    }

    public void setAgentThoughts(List<AgentThought> agentThoughts) {
        this.agentThoughts = agentThoughts;
    }

    @Override
    public String toString() {
        return "DifyMessageDTO{" +
                "id='" + id + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", inputs=" + inputs +
                ", query='" + query + '\'' +
                ", answer='" + answer + '\'' +
                ", messageFiles=" + messageFiles +
                ", feedback=" + feedback +
                ", retrieverResources=" + retrieverResources +
                ", parentMessageId='" + parentMessageId + '\'' +
                ", status='" + status + '\'' +
                ", error='" + error + '\'' +
                ", createdAt=" + createdAt +
                ", agentThoughts=" + agentThoughts +
                '}';
    }
}
