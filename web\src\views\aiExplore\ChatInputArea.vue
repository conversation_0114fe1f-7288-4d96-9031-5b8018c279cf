<template>
  <!-- 输入区域 -->
  <div class="chat-input-area shadow-lg transition-all duration-200 ease-out"
       :class="containerClass">
    <div class="input-container flex flex-col">
      <!-- 文件/图片/语音预览区域 -->
      <div v-if="chatInputStore.uploadedFiles.length > 0 || chatInputStore.uploadedImages.length > 0 || chatInputStore.recordedAudio || agentUploadedFiles.length > 0"
           class="preview-area p-4 border-b border-gray-100 bg-gray-50/80 backdrop-blur-sm">
        <!-- 文件预览 -->
        <div v-if="chatInputStore.uploadedFiles.length > 0" class="files-preview mb-3">
          <h4 class="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            <el-icon><Document /></el-icon>
            已上传文件:
          </h4>
          <div class="flex flex-wrap gap-2">
            <el-tag
              v-for="(file, index) in chatInputStore.uploadedFiles"
              :key="index"
              type="info"
              closable
              @close="removeFile(index)"
              class="file-tag"
            >
              <template #icon>
                <el-icon><Paperclip /></el-icon>
              </template>
              {{ file.name }}
            </el-tag>
          </div>
        </div>

        <!-- 图片预览 -->
        <div v-if="chatInputStore.uploadedImages.length > 0" class="images-preview mb-3">
          <h4 class="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            <el-icon><Picture /></el-icon>
            已上传图片:
          </h4>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(image, index) in chatInputStore.uploadedImages"
              :key="index"
              class="image-preview relative group"
            >
              <el-image
                :src="image.url"
                :alt="image.name"
                class="w-16 h-16 rounded-lg border border-gray-200"
                fit="cover"
                :preview-src-list="[image.url]"
              />
              <el-button
                @click="removeImage(index)"
                type="danger"
                size="small"
                circle
                class="absolute -top-1 -right-1 w-5 h-5 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <el-icon size="12"><Close /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 语音预览 -->
        <div v-if="chatInputStore.recordedAudio" class="audio-preview">
          <h4 class="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            <el-icon><Microphone /></el-icon>
            录音文件:
          </h4>
          <div class="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200">
            <el-button
              @click="playRecordedAudio"
              type="primary"
              size="small"
              circle
            >
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <span class="text-sm text-gray-600">录音时长: {{ chatInputStore.recordingDuration }}s</span>
            <el-button
              @click="removeRecordedAudio"
              type="danger"
              size="small"
              text
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>

        <!-- 智能体上传文件预览 -->
        <div v-if="agentUploadedFiles.length > 0" class="agent-files-preview mb-3">
          <h4 class="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
            <el-icon><Paperclip /></el-icon>
            智能体文件:
          </h4>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(file, index) in agentUploadedFiles"
              :key="index"
              class="agent-file-item relative group"
            >
              <!-- 图片文件预览 -->
              <div v-if="file.type === 'image'" class="image-preview relative">
                <div class="w-16 h-16 rounded-lg border border-gray-200 bg-gray-100 flex items-center justify-center overflow-hidden">
                  <!-- 如果有预览链接，显示实际图片 -->
                  <img
                    v-if="file.preview_url"
                    :src="file.preview_url"
                    :alt="file.name"
                    class="w-full h-full object-cover"
                    @error="handleImageError"
                  />
                  <!-- 否则显示图标 -->
                  <el-icon v-else class="text-gray-400" size="24"><Picture /></el-icon>
                </div>
                <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <span class="text-white text-xs text-center px-1">{{ file.name }}</span>
                </div>
                <el-button
                  @click="removeAgentFile(index)"
                  type="danger"
                  size="small"
                  circle
                  class="absolute -top-1 -right-1 w-5 h-5 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <el-icon size="12"><Close /></el-icon>
                </el-button>
              </div>

              <!-- 其他文件类型预览 -->
              <div v-else class="file-preview relative">
                <el-tag
                  type="info"
                  class="file-tag pr-8"
                >
                  <template #icon>
                    <el-icon>
                      <Document v-if="file.type === 'document'" />
                      <Microphone v-else-if="file.type === 'audio'" />
                      <VideoPlay v-else-if="file.type === 'video'" />
                      <Paperclip v-else />
                    </el-icon>
                  </template>
                  {{ file.name }}
                </el-tag>
                <el-button
                  @click="removeAgentFile(index)"
                  type="danger"
                  size="small"
                  circle
                  class="absolute -top-1 -right-1 w-5 h-5 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <el-icon size="12"><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 文件上传中状态 -->
        <div v-if="isUploadingFile" class="uploading-status mb-3">
          <div class="flex items-center gap-2 text-blue-600">
            <el-icon class="animate-spin"><Loading /></el-icon>
            <span class="text-sm">正在上传文件...</span>
          </div>
        </div>
      </div>

      <!-- 输入框区域 -->
      <div class="input-wrapper p-4">
        <!-- 整体输入框容器 -->
        <div class="input-container border border-gray-200 rounded-xl bg-white transition-all duration-200 ease-out hover:border-gray-300 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20">
          <!-- 文本输入框 -->
          <div class="px-4 pt-3">
            <el-input
              v-model="chatInputStore.inputMessage"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              :placeholder="computedPlaceholder"
              @keydown="handleKeyDown"
              :maxlength="20000"
              show-word-limit
              resize="none"
              class="borderless-input"
            />
          </div>

          <!-- 底部按钮区域 -->
          <div class="flex items-center justify-between px-4 pb-3 pt-2 min-h-[48px] relative">
            <!-- 功能按钮组 -->
            <div class="flex items-center gap-2">
              <!-- 展开参数配置按钮 - 只有当参数配置被收起且有参数时显示 -->
              <button
                v-if="props.canExpandParameterConfig"
                @click="expandParameterConfig"
                class="expand-parameter-btn p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors duration-200"
                title="展开参数配置"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>

              <!-- 文件上传菜单 - 只有选择模型时显示（不包括智能体） -->
              <FileUploadMenu
                v-if="selectedModelInfo && selectedModelInfo.type === 'model'"
                @file-upload="handleFileUploadMenu"
              />

              <!-- 智能体专用文件上传按钮 - 只有选择智能体且file_upload.enabled为true时显示 -->
              <div v-if="selectedModelInfo && selectedModelInfo.type === 'agent' && agentParameters?.file_upload?.enabled" class="agent-file-upload relative">
                <!-- 别针图标按钮 -->
                <button
                  @click="toggleAgentFileMenu"
                  class="agent-file-trigger p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                  :class="{ 'text-blue-600 bg-blue-50': showAgentFileMenu }"
                  title="文件上传"
                >
                  <el-icon class="text-lg">
                    <Paperclip />
                  </el-icon>
                </button>

                <!-- 上拉菜单 -->
                <div
                  v-if="showAgentFileMenu"
                  class="agent-file-menu absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-48"
                >
                  <div class="py-2">
                    <!-- 输入文件链接 - 根据allowed_file_upload_methods控制显示 -->
                    <button
                      v-if="agentParameters?.file_upload?.allowed_file_upload_methods?.includes('remote_url')"
                      @click="showFileLinkDialog"
                      class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
                    >
                      <el-icon class="text-blue-500"><Link /></el-icon>
                      输入文件链接
                    </button>

                    <!-- 上传本地文件 - 根据allowed_file_upload_methods控制显示 -->
                    <button
                      v-if="agentParameters?.file_upload?.allowed_file_upload_methods?.includes('local_file')"
                      @click="triggerAgentFileUpload"
                      class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
                    >
                      <el-icon class="text-green-500"><Upload /></el-icon>
                      上传本地文件
                    </button>

                    <!-- 如果没有可用的上传方式，显示提示 -->
                    <div
                      v-if="!agentParameters?.file_upload?.allowed_file_upload_methods?.length"
                      class="px-4 py-2 text-sm text-gray-500 text-center"
                    >
                      暂不支持文件上传
                    </div>
                  </div>
                </div>
              </div>

              <!-- 工具菜单 - 可通过props隐藏，且只有选择模型时显示（不包括智能体） -->
              <ToolsMenu
                v-if="!props.hideToolsMenu && selectedModelInfo && selectedModelInfo.type === 'model'"
                @tool-select="handleToolSelect"
              />
            </div>

            <!-- 右侧按钮组 -->
            <div class="flex items-center gap-2 relative">
              <!-- 语音录入 -->
              <div class="voice-recording-container relative">
                <el-button
                  @click="toggleVoiceRecording"
                  :type="chatInputStore.isRecording ? 'danger' : 'warning'"
                  :text="!chatInputStore.isRecording"
                  circle
                  size="small"
                  :class="{
                    'recording-active': chatInputStore.isRecording,
                    'recording-button': true
                  }"
                  :title="chatInputStore.isRecording ? '停止录音' : '开始录音'"
                >
                  <el-icon size="20" class="voice-recording-icon">
                    <Microphone v-if="!chatInputStore.isRecording" />
                    <VideoPause v-else />
                  </el-icon>
                </el-button>

                <!-- 录音时的波纹效果 -->
                <div
                  v-if="chatInputStore.isRecording"
                  class="recording-ripples absolute inset-0 pointer-events-none"
                >
                  <div class="ripple ripple-1"></div>
                  <div class="ripple ripple-2"></div>
                  <div class="ripple ripple-3"></div>
                </div>

                <!-- 录音时的声波可视化 -->
                <div
                  v-if="chatInputStore.isRecording"
                  class="voice-waves-mini absolute -bottom-6 left-1/2 transform -translate-x-1/2 z-10"
                >
                  <div class="flex items-end gap-0.5">
                    <div
                      v-for="i in 12"
                      :key="i"
                      class="wave-bar bg-red-500 rounded-full dynamic-wave"
                      :style="{
                        width: '2px',
                        height: `${getWaveHeight(i)}px`,
                        animationDelay: `${i * 0.08}s`,
                        animationDuration: `${0.6 + Math.random() * 0.4}s`
                      }"
                    ></div>
                  </div>
                </div>

                <!-- 录音时的环形进度指示器 -->
                <div
                  v-if="chatInputStore.isRecording"
                  class="recording-progress absolute inset-0 pointer-events-none"
                >
                  <svg class="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                    <circle
                      cx="18"
                      cy="18"
                      r="16"
                      fill="none"
                      stroke="#ef4444"
                      stroke-width="2"
                      stroke-dasharray="100"
                      stroke-dashoffset="0"
                      class="recording-circle"
                    />
                  </svg>
                </div>

                <!-- 录音时的粒子效果 -->
                <div
                  v-if="chatInputStore.isRecording"
                  class="recording-particles absolute inset-0 pointer-events-none overflow-hidden rounded-full"
                >
                  <div
                    v-for="i in 6"
                    :key="i"
                    class="particle"
                    :style="{
                      animationDelay: `${i * 0.3}s`,
                      left: `${20 + Math.random() * 60}%`,
                      top: `${20 + Math.random() * 60}%`
                    }"
                  ></div>
                </div>
              </div>

              <!-- 语音模式 -->
              <el-button
                @click="toggleVoiceMode"
                :type="chatInputStore.isVoiceMode ? 'success' : 'info'"
                :text="!chatInputStore.isVoiceMode"
                circle
                size="small"
                :title="chatInputStore.isVoiceMode ? '退出语音模式' : '语音模式对话'"
                :class="{ 'voice-wave-active': chatInputStore.isVoiceMode }"
              >
                <el-icon size="20" class="voice-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <!-- 麦克风主体 -->
                    <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                    <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                    <!-- 声波效果 -->
                    <g class="voice-waves" opacity="0.6">
                      <path d="M19 11c0-1.66-.67-3.16-1.76-4.24l-1.42 1.42C16.57 8.93 17 9.93 17 11s-.43 2.07-1.18 2.82l1.42 1.42C18.33 14.16 19 12.66 19 11z" class="wave-1"/>
                      <path d="M21 11c0-2.49-1.01-4.75-2.64-6.36l-1.42 1.42C18.17 7.29 19 9.06 19 11s-.83 3.71-2.06 4.94l1.42 1.42C19.99 15.75 21 13.49 21 11z" class="wave-2"/>
                      <path d="M5 11c0 1.07.43 2.07 1.18 2.82l-1.42 1.42C3.67 14.16 3 12.66 3 11s.67-3.16 1.76-4.24l1.42 1.42C5.43 8.93 5 9.93 5 11z" class="wave-3"/>
                      <path d="M3 11c0 2.43.99 4.69 2.58 6.36l1.42-1.42C5.83 14.71 5 12.94 5 11s.83-3.71 2-4.94L5.58 4.64C4.01 6.31 3 8.57 3 11z" class="wave-4"/>
                    </g>
                  </svg>
                </el-icon>
              </el-button>

              <!-- 停止工作流按钮 -->
              <el-button
                v-if="props.canStopWorkflow"
                @click="stopWorkflow"
                type="danger"
                circle
                title="停止工作流"
                class="stop-workflow-btn"
              >
                <el-icon><VideoPause /></el-icon>
              </el-button>

              <!-- 发送按钮 -->
              <el-button
                @click="sendMessage"
                :disabled="!chatInputStore.canSend"
                :loading="chatInputStore.isSending"
                type="primary"
                circle
                title="发送消息"
              >
                <el-icon v-if="!chatInputStore.isSending"><Promotion /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 录音状态提示 -->
        <div v-if="chatInputStore.isRecording" class="recording-indicator mt-3">
          <div class="flex items-center justify-center gap-2">
            <div class="recording-status-card bg-red-50 border border-red-200 rounded-lg px-4 py-3 flex items-center gap-3">
              <!-- 录音图标 -->
              <div class="recording-icon-wrapper relative">
                <el-icon class="text-red-600 text-lg recording-mic-icon">
                  <Microphone />
                </el-icon>
                <!-- 录音图标周围的脉冲效果 -->
                <div class="absolute inset-0 bg-red-600 rounded-full opacity-20 animate-ping"></div>
              </div>

              <!-- 录音信息 -->
              <div class="flex flex-col">
                <span class="text-red-700 font-medium text-sm">正在录音</span>
                <span class="text-red-600 text-xs">{{ chatInputStore.recordingTime }}s</span>
              </div>

              <!-- 声波可视化 -->
              <div class="recording-waves flex items-center gap-0.5 ml-2">
                <div
                  v-for="i in 5"
                  :key="i"
                  class="wave-line bg-red-500 rounded-full"
                  :style="{
                    width: '2px',
                    height: `${Math.random() * 16 + 8}px`,
                    animationDelay: `${i * 0.15}s`
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 语音对话界面弹窗 -->
    <VoiceDialogModal
      v-model:visible="showVoiceDialog"
      @voice-input="handleVoiceInput"
      @close="closeVoiceDialog"
    />

    <!-- 文件链接输入对话框 -->
    <el-dialog
      v-model="showFileLinkDialogVisible"
      title="输入文件链接"
      width="500px"
      :before-close="() => { fileLinkInput = ''; showFileLinkDialogVisible = false }"
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            文件链接地址
          </label>
          <el-input
            v-model="fileLinkInput"
            placeholder="请输入文件链接地址，如：https://example.com/file.pdf"
            @keydown.enter="handleFileLinkSubmit"
          />
        </div>
        <div class="text-sm text-gray-500">
          <p>支持的链接类型：</p>
          <ul class="list-disc list-inside mt-1 space-y-1">
            <li>直接文件下载链接</li>
            <li>云存储分享链接（如百度网盘、阿里云盘等）</li>
            <li>在线文档链接</li>
          </ul>

          <!-- 显示智能体支持的文件类型 -->
          <div v-if="agentParameters?.file_upload" class="mt-3 p-2 bg-blue-50 rounded">
            <p class="font-medium text-blue-700 mb-1">当前智能体支持：</p>
            <div class="space-y-1">
              <p v-if="agentParameters.file_upload.allowed_file_types?.length">
                <span class="text-blue-600">文件类型：</span>
                {{ agentParameters.file_upload.allowed_file_types.join(', ') }}
              </p>
              <p v-if="getAllowedFileExtensions().length">
                <span class="text-blue-600">文件格式：</span>
                {{ getAllowedFileExtensions().join(', ') }}
              </p>
              <p>
                <span class="text-blue-600">最大数量：</span>
                {{ agentParameters.file_upload.number_limits || 1 }} 个文件
              </p>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="showFileLinkDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleFileLinkSubmit"
            :disabled="!fileLinkInput.trim()"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import {
  Document,
  Paperclip,
  Picture,
  Microphone,
  VideoPause,
  VideoPlay,
  Delete,
  Close,
  Promotion,
  Link,
  Upload,
  Loading
} from '@element-plus/icons-vue'
import FileUploadMenu from '@/components/FileUploadMenu.vue'
import ToolsMenu from '@/views/aiExplore/ToolsMenu.vue'
import VoiceDialogModal from '@/views/aiExplore/VoiceDialogModal.vue'

import { useModelStore } from '@/stores/modelStore'
import { useChatInputStore } from '@/stores/chatInputStore'
import { exploreApi } from '@/api/explore'

// 支持的文件类型枚举
enum SupportUploadFileTypes {
  image = 'image',
  document = 'document',
  audio = 'audio',
  video = 'video'
}

// 文件类型与后缀映射
const FILE_TYPE_EXTENSIONS = {
  [SupportUploadFileTypes.image]: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
  [SupportUploadFileTypes.document]: ['TXT', 'MD', 'MDX', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOC', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
  [SupportUploadFileTypes.audio]: ['MP3', 'M4A', 'WAV', 'AMR', 'MPGA'],
  [SupportUploadFileTypes.video]: ['MP4', 'MOV', 'MPEG', 'WEBM']
}

// 上传文件响应接口
interface UploadFileResponse {
  id: string
  status: string
  original_filename: string
  file_size: number
  file_extension: string
  mime_type: string
  dify_file_id: string
  minio_bucket?: string
  minio_object_name?: string
  minio_url?: string
  upload_time: string
  dify_preview_url?: string | null
  error_message?: string | null
}

// 聊天文件接口
interface ChatFile {
  type: string
  transfer_method: 'local_file' | 'remote_url'
  url: string
  upload_file_id: string
  name?: string
  size?: number
  mime_type?: string
  preview_url?: string // 后端返回的预览链接
}

// 智能体参数配置接口
interface AgentChatParameters {
  opening_statement: string
  suggested_questions: string[]
  suggested_questions_after_answer: {
    enabled: boolean
  }
  speech_to_text: {
    enabled: boolean
  }
  text_to_speech: {
    enabled: boolean
    voice: string
    language: string
  }
  retriever_resource: {
    enabled: boolean
  }
  annotation_reply: {
    enabled: boolean
  }
  more_like_this: {
    enabled: boolean
  }
  user_input_form: any[]
  sensitive_word_avoidance: {
    enabled: boolean
  }
  file_upload: {
    enabled: boolean
    allowed_file_types: string[]
    allowed_file_extensions: string[]
    allowed_file_upload_methods: string[]
    number_limits: number
    fileUploadConfig: {
      file_size_limit: number
      batch_count_limit: number
      image_file_size_limit: number
      video_file_size_limit: number
      audio_file_size_limit: number
      workflow_file_upload_limit: number
    }
  }
  system_parameters: {
    image_file_size_limit: number
    video_file_size_limit: number
    audio_file_size_limit: number
    file_size_limit: number
    workflow_file_upload_limit: number
  }
}

// Props
interface Props {
  placeholder?: string
  containerClass?: string
  hideToolsMenu?: boolean
  canStopWorkflow?: boolean
  canExpandParameterConfig?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入您的消息...',
  containerClass: '',
  hideToolsMenu: false,
  canStopWorkflow: false,
  canExpandParameterConfig: false
})

// Emits
const emit = defineEmits<{
  'stop-workflow': []
  'expand-parameter-config': []
}>()

// 使用模型存储
const { selectedModelInfo } = useModelStore()

// 使用全局聊天输入状态
const chatInputStore = useChatInputStore()

// 语音对话弹窗状态
const showVoiceDialog = ref(false)

// 智能体文件上传相关状态
const showAgentFileMenu = ref(false)
const showFileLinkDialogVisible = ref(false)
const fileLinkInput = ref('')

// 智能体参数配置状态
const agentParameters = ref<AgentChatParameters | null>(null)
const isLoadingParameters = ref(false)

// 智能体上传文件状态
const agentUploadedFiles = ref<ChatFile[]>([])
const isUploadingFile = ref(false)

// 声波动画相关
const waveHeights = ref<number[]>([])
const waveAnimationTimer = ref<number | null>(null)

// 计算属性：动态placeholder
const computedPlaceholder = computed(() => {
  if (selectedModelInfo.value) {
    const type = selectedModelInfo.value.type === 'agent' ? '智能体' : '模型'
    return `与${type} ${selectedModelInfo.value.name} 对话，输入您的问题...`
  }
  return props.placeholder || '输入您的问题，按 Enter 发送...'
})

// 方法
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    if (chatInputStore.canSend) {
      chatInputStore.handleSendMessage()
    }
  }
}

const sendMessage = async () => {
  await chatInputStore.handleSendMessage()
}

const stopWorkflow = () => {
  emit('stop-workflow')
}

const expandParameterConfig = () => {
  console.log('🔧 展开参数配置按钮点击')
  emit('expand-parameter-config')
}

const toggleVoiceRecording = () => {
  if (chatInputStore.isRecording) {
    stopWaveAnimation()
  } else {
    startWaveAnimation()
  }
  chatInputStore.handleToggleVoiceRecording()
}

// 动态声波高度计算
const getWaveHeight = (index: number) => {
  if (waveHeights.value.length === 0) {
    return 4 + Math.random() * 8
  }
  return waveHeights.value[index] || 4
}

// 开始声波动画
const startWaveAnimation = () => {
  // 初始化声波高度数组
  waveHeights.value = Array.from({ length: 12 }, () => 4 + Math.random() * 8)

  // 启动动画定时器
  waveAnimationTimer.value = window.setInterval(() => {
    // 模拟实时音频数据，创建动态声波效果
    waveHeights.value = waveHeights.value.map((_, index) => {
      // 使用正弦波和随机数创建更自然的声波效果
      const time = Date.now() / 1000
      const baseHeight = 4 + Math.sin(time * 2 + index * 0.5) * 3
      const randomVariation = Math.random() * 6
      return Math.max(2, baseHeight + randomVariation)
    })
  }, 100) // 每100ms更新一次
}

// 停止声波动画
const stopWaveAnimation = () => {
  if (waveAnimationTimer.value) {
    clearInterval(waveAnimationTimer.value)
    waveAnimationTimer.value = null
  }
  waveHeights.value = []
}

const toggleVoiceMode = () => {
  // 打开语音对话弹窗
  showVoiceDialog.value = true
  chatInputStore.handleToggleVoiceMode()
}

// 处理语音输入
const handleVoiceInput = (text: string) => {
  // 将语音识别的文本设置到输入框
  chatInputStore.inputMessage = text
  // 自动发送消息
  chatInputStore.handleSendMessage()
}

// 关闭语音对话弹窗
const closeVoiceDialog = () => {
  showVoiceDialog.value = false
}

const handleFileUploadMenu = (files: FileList, type: 'file' | 'image' | 'id') => {
  chatInputStore.handleFileUpload(files, type)
}

const handleToolSelect = (tool: string) => {
  chatInputStore.handleToolSelect(tool as any)
}

// 智能体文件上传相关方法
const toggleAgentFileMenu = () => {
  showAgentFileMenu.value = !showAgentFileMenu.value
}

const closeAgentFileMenu = () => {
  showAgentFileMenu.value = false
}

const showFileLinkDialog = () => {
  closeAgentFileMenu()
  showFileLinkDialogVisible.value = true
}

const handleFileLinkSubmit = async () => {
  if (fileLinkInput.value.trim()) {
    try {
      isUploadingFile.value = true
      console.log('开始上传远程文件:', fileLinkInput.value)

      // 调用远程文件上传API
      const response = await exploreApi.uploadRemoteChatFile(fileLinkInput.value)

      if (response.success && response.data) {
        const uploadedFile = response.data

        // 根据文件类型确定type
        let fileType = 'document'
        if (uploadedFile.mime_type.startsWith('image/')) {
          fileType = 'image'
        } else if (uploadedFile.mime_type.startsWith('audio/')) {
          fileType = 'audio'
        } else if (uploadedFile.mime_type.startsWith('video/')) {
          fileType = 'video'
        }

        // 创建聊天文件对象
        const chatFile: ChatFile = {
          type: fileType,
          transfer_method: 'remote_url',
          url: fileLinkInput.value, // 原始链接
          upload_file_id: uploadedFile.dify_file_id, // 使用dify_file_id
          // 以下字段仅用于前端显示
          name: uploadedFile.original_filename, // 使用original_filename
          size: uploadedFile.file_size, // 使用file_size
          mime_type: uploadedFile.mime_type,
          preview_url: uploadedFile.dify_preview_url || undefined // 使用dify_preview_url
        }

        // 添加到上传文件列表
        agentUploadedFiles.value.push(chatFile)

        console.log('远程文件上传成功:', chatFile)

        // 清空输入并关闭对话框
        fileLinkInput.value = ''
        showFileLinkDialogVisible.value = false
      } else {
        console.error('远程文件上传失败:', response.message)
      }
    } catch (error) {
      console.error('远程文件上传异常:', error)
    } finally {
      isUploadingFile.value = false
    }
  }
}

const triggerAgentFileUpload = () => {
  closeAgentFileMenu()

  // 根据allowed_file_types生成允许的文件扩展名
  const allowedExtensions = getAllowedFileExtensions()
  let acceptTypes = '*/*'

  if (allowedExtensions.length > 0) {
    acceptTypes = allowedExtensions.join(',')
  }

  console.log('文件上传accept类型:', acceptTypes)

  // 创建隐藏的文件输入元素
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = acceptTypes
  fileInput.onchange = async (e) => {
    const files = (e.target as HTMLInputElement).files
    if (files && files.length > 0) {
      // 检查文件数量限制
      const currentFileCount = agentUploadedFiles.value.length
      const maxFiles = agentParameters.value?.file_upload?.number_limits || 1

      if (currentFileCount >= maxFiles) {
        console.warn(`最多只能上传 ${maxFiles} 个文件`)
        return
      }

      // 上传文件到后端
      await handleAgentFileUpload(files[0])
    }
  }
  fileInput.click()
}

// 处理智能体文件上传
const handleAgentFileUpload = async (file: File) => {
  try {
    isUploadingFile.value = true
    console.log('开始上传文件:', file.name)

    // 调用上传API
    const response = await exploreApi.uploadChatFile(file)

    if (response.success && response.data) {
      const uploadedFile = response.data as UploadFileResponse

      // 根据文件类型确定type
      let fileType = 'document'
      if (uploadedFile.mime_type.startsWith('image/')) {
        fileType = 'image'
      } else if (uploadedFile.mime_type.startsWith('audio/')) {
        fileType = 'audio'
      } else if (uploadedFile.mime_type.startsWith('video/')) {
        fileType = 'video'
      }

      // 创建聊天文件对象 - upload_file_id对应dify_file_id
      const chatFile: ChatFile = {
        type: fileType,
        transfer_method: 'local_file',
        url: '',
        upload_file_id: uploadedFile.dify_file_id, // 使用dify_file_id
        // 以下字段仅用于前端显示，不会传递给API
        name: uploadedFile.original_filename, // 使用original_filename
        size: uploadedFile.file_size, // 使用file_size
        mime_type: uploadedFile.mime_type,
        preview_url: uploadedFile.dify_preview_url || undefined // 使用dify_preview_url
      }

      // 添加到上传文件列表
      agentUploadedFiles.value.push(chatFile)

      console.log('文件上传成功:', chatFile)
    } else {
      console.error('文件上传失败:', response.message)
    }
  } catch (error) {
    console.error('文件上传异常:', error)
  } finally {
    isUploadingFile.value = false
  }
}

// 根据allowed_file_types生成允许的文件扩展名
const getAllowedFileExtensions = (): string[] => {
  if (!agentParameters.value?.file_upload?.allowed_file_types?.length) {
    return []
  }

  const allowedExtensions: string[] = []

  // 遍历允许的文件类型，收集对应的扩展名
  agentParameters.value.file_upload.allowed_file_types.forEach(fileType => {
    const extensions = FILE_TYPE_EXTENSIONS[fileType as SupportUploadFileTypes]
    if (extensions) {
      allowedExtensions.push(...extensions.map(ext => `.${ext.toLowerCase()}`))
    }
  })

  return allowedExtensions
}

// 获取智能体参数配置
const fetchAgentParameters = async (appId: string) => {
  if (!appId) return

  try {
    isLoadingParameters.value = true
    const response = await exploreApi.getChatParameters(appId)

    if (response.success && response.data) {
      agentParameters.value = response.data
      console.log('智能体参数配置加载成功:', agentParameters.value)
      console.log('允许的文件扩展名:', getAllowedFileExtensions())
    } else {
      console.error('获取智能体参数失败:', response.message)
      agentParameters.value = null
    }
  } catch (error) {
    console.error('获取智能体参数异常:', error)
    agentParameters.value = null
  } finally {
    isLoadingParameters.value = false
  }
}

const removeFile = (index: number) => {
  chatInputStore.handleRemoveFile(index)
}

// 点击外部关闭智能体文件菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.agent-file-upload')) {
    closeAgentFileMenu()
  }
}

// 监听模型选择变化，当选择智能体时获取参数配置
watch(selectedModelInfo, async (newValue, oldValue) => {
  if (newValue?.type === 'agent' && newValue.appId) {
    // 选择了智能体，获取参数配置
    await fetchAgentParameters(newValue.appId)
  } else {
    // 选择了模型或清空选择，清空参数配置
    agentParameters.value = null
  }
}, { immediate: true })

// 组件挂载时添加事件监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时清理定时器和事件监听器
onUnmounted(() => {
  stopWaveAnimation()
  document.removeEventListener('click', handleClickOutside)
})

const removeImage = (index: number) => {
  chatInputStore.handleRemoveImage(index)
}

const removeRecordedAudio = () => {
  chatInputStore.handleRemoveRecordedAudio()
}

const playRecordedAudio = () => {
  chatInputStore.handlePlayRecordedAudio()
}

// 移除智能体上传的文件
const removeAgentFile = (index: number) => {
  agentUploadedFiles.value.splice(index, 1)
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 图片加载失败时隐藏图片，显示默认图标
  img.style.display = 'none'
  const parent = img.parentElement
  if (parent) {
    parent.innerHTML = '<el-icon class="text-gray-400" size="24"><Picture /></el-icon>'
  }
}

// 获取智能体上传的文件（供外部组件使用）
const getAgentUploadedFiles = (): ChatFile[] => {
  return agentUploadedFiles.value
}

// 清空智能体上传的文件
const clearAgentUploadedFiles = () => {
  agentUploadedFiles.value = []
}

// 暴露方法给父组件
defineExpose({
  getAgentUploadedFiles,
  clearAgentUploadedFiles
})
</script>

<style scoped>
/* 输入框样式 */
.borderless-input :deep(.el-textarea__inner) {
  border: none;
  box-shadow: none;
  padding: 0;
  resize: none;
  background: transparent;
}

.borderless-input :deep(.el-textarea__inner):focus {
  border: none;
  box-shadow: none;
}

.borderless-input :deep(.el-input__count) {
  background: transparent;
  bottom: 8px;
  right: 8px;
}

/* 文件标签样式 */
.file-tag {
  max-width: 200px;
}

.file-tag :deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 语音模式图标动画 */
.voice-icon {
  transition: all 0.3s ease;
}

.voice-wave-active .voice-icon {
  animation: voicePulse 2s ease-in-out infinite;
}

@keyframes voicePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 声波动画 */
.voice-waves {
  animation: waveAnimation 1.5s ease-in-out infinite;
}

.voice-waves .wave-1 {
  animation: wave1 2s ease-in-out infinite;
}

.voice-waves .wave-2 {
  animation: wave2 2.5s ease-in-out infinite;
}

.voice-waves .wave-3 {
  animation: wave3 2s ease-in-out infinite;
}

.voice-waves .wave-4 {
  animation: wave4 2.5s ease-in-out infinite;
}

@keyframes wave1 {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes wave2 {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.6; }
}

@keyframes wave3 {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes wave4 {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.6; }
}

/* 语音模式激活时的特殊效果 */
.voice-wave-active {
  position: relative;
  overflow: visible;
}

.voice-wave-active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid currentColor;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 1.5s ease-out infinite;
  opacity: 0.3;
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* 语音录入动态效果 */
.voice-recording-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.recording-button {
  position: relative;
  overflow: visible;
  transition: all 0.3s ease;
}

.recording-active {
  animation: recordingPulse 1.5s ease-in-out infinite;
}

@keyframes recordingPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.voice-recording-icon {
  transition: all 0.3s ease;
}

.recording-active .voice-recording-icon {
  animation: iconBounce 0.8s ease-in-out infinite;
}

@keyframes iconBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 录音波纹效果 */
.recording-ripples {
  border-radius: 50%;
}

.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid #f56565;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: recordingRipple 2s ease-out infinite;
}

.ripple-1 {
  animation-delay: 0s;
}

.ripple-2 {
  animation-delay: 0.6s;
}

.ripple-3 {
  animation-delay: 1.2s;
}

@keyframes recordingRipple {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(2.5);
    opacity: 0;
  }
}

/* 迷你声波可视化 */
.voice-waves-mini {
  z-index: 10;
  pointer-events: none;
  /* 确保不影响父容器的布局 */
  position: absolute;
  white-space: nowrap;
}

.wave-bar {
  animation: waveBarAnimation 0.8s ease-in-out infinite;
}

@keyframes waveBarAnimation {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(2.5);
  }
}

/* 为不同的波形条添加不同的动画延迟 */
.wave-bar:nth-child(1) { animation-delay: 0s; }
.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }
.wave-bar:nth-child(6) { animation-delay: 0.3s; }
.wave-bar:nth-child(7) { animation-delay: 0.2s; }
.wave-bar:nth-child(8) { animation-delay: 0.1s; }

/* 录音状态下的特殊效果 */
.recording-active {
  box-shadow: 0 0 20px rgba(245, 101, 101, 0.4);
}

/* 录音状态卡片样式 */
.recording-status-card {
  animation: slideInUp 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recording-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.recording-mic-icon {
  animation: micBounce 1s ease-in-out infinite;
}

@keyframes micBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 录音状态声波线条 */
.wave-line {
  animation: waveLine 0.6s ease-in-out infinite;
}

@keyframes waveLine {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(2);
  }
}

/* 为不同的声波线条添加不同的动画延迟 */
.recording-waves .wave-line:nth-child(1) { animation-delay: 0s; }
.recording-waves .wave-line:nth-child(2) { animation-delay: 0.1s; }
.recording-waves .wave-line:nth-child(3) { animation-delay: 0.2s; }
.recording-waves .wave-line:nth-child(4) { animation-delay: 0.1s; }
.recording-waves .wave-line:nth-child(5) { animation-delay: 0s; }

/* 动态声波效果增强 */
.dynamic-wave {
  animation: dynamicWaveAnimation 0.8s ease-in-out infinite;
  transform-origin: bottom;
}

@keyframes dynamicWaveAnimation {
  0%, 100% {
    transform: scaleY(1);
    opacity: 0.8;
  }
  50% {
    transform: scaleY(2.5);
    opacity: 1;
  }
}

/* 环形进度指示器 */
.recording-progress {
  animation: progressRotate 3s linear infinite;
}

.recording-circle {
  animation: progressDash 2s ease-in-out infinite;
}

@keyframes progressRotate {
  0% {
    transform: rotate(-90deg);
  }
  100% {
    transform: rotate(270deg);
  }
}

@keyframes progressDash {
  0% {
    stroke-dashoffset: 100;
    opacity: 0.3;
  }
  50% {
    stroke-dashoffset: 50;
    opacity: 0.8;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 0.3;
  }
}

/* 粒子效果 */
.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: #ef4444;
  border-radius: 50%;
  animation: particleFloat 2s ease-in-out infinite;
  opacity: 0;
}

@keyframes particleFloat {
  0% {
    transform: translateY(0) scale(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateY(-20px) scale(1.5);
    opacity: 0;
  }
}

/* 为不同粒子添加变化 */
.particle:nth-child(1) { animation-duration: 1.8s; background: #f87171; }
.particle:nth-child(2) { animation-duration: 2.2s; background: #ef4444; }
.particle:nth-child(3) { animation-duration: 1.9s; background: #dc2626; }
.particle:nth-child(4) { animation-duration: 2.1s; background: #f87171; }
.particle:nth-child(5) { animation-duration: 2.0s; background: #ef4444; }
.particle:nth-child(6) { animation-duration: 1.7s; background: #dc2626; }

/* 按钮对齐优化 */
.flex.items-center.gap-2 {
  align-items: center;
  min-height: 32px; /* 确保按钮容器有足够高度 */
}

/* 确保所有按钮垂直居中 */
.el-button.is-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}

/* 停止工作流按钮样式 */
.stop-workflow-btn {
  animation: pulse 2s infinite;
}

/* 展开参数配置按钮样式 */
.expand-parameter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.expand-parameter-btn:hover {
  transform: scale(1.05);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .voice-waves-mini {
    display: none; /* 移动端隐藏声波效果，避免布局问题 */
  }

  .recording-ripples {
    transform: scale(0.8); /* 移动端缩小波纹效果 */
  }

  .recording-status-card {
    padding: 0.75rem 1rem; /* 移动端减小内边距 */
  }

  .recording-waves {
    display: none; /* 移动端隐藏状态卡片中的声波 */
  }
}
</style>
