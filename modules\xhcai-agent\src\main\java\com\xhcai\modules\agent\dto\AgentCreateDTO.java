package com.xhcai.modules.agent.dto;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 智能体创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体创建信息")
public class AgentCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "客服助手", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "智能体名称不能为空")
    @Size(min = 1, max = 100, message = "智能体名称长度必须在1-100个字符之间")
    private String name;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述", example = "专业的客服助手，能够回答用户问题")
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    private String description;

    /**
     * 智能体头像URL
     */
    @Schema(description = "智能体头像URL", example = "https://example.com/avatar.jpg")
    @Size(max = 255, message = "头像URL长度不能超过255个字符")
    private String avatar;

    /**
     * 智能体图标背景颜色
     */
    @Schema(description = "智能体图标背景颜色", example = "linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)")
    @Size(max = 100, message = "图标背景颜色长度不能超过100个字符")
    private String iconBackground;

    /**
     * 智能体类型
     */
    @Schema(description = "智能体类型", example = "chat", allowableValues = {"chat", "workflow", "completion", "advanced-chat", "agent-chat"}, requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "智能体类型不能为空")
    @Pattern(regexp = "^(chat|workflow|completion|advanced-chat|agent-chat)$", message = "智能体类型必须为chat、workflow、completion、advanced-chat或agent-chat")
    private String type;

    /**
     * 模型配置（JSON格式）
     */
    @Schema(description = "模型配置", example = "{\"model\":\"gpt-3.5-turbo\",\"temperature\":0.7}")
    private String modelConfig;

    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词", example = "你是一个专业的客服助手...")
    private String systemPrompt;

    /**
     * 工具配置（JSON格式）
     */
    @Schema(description = "工具配置", example = "[{\"name\":\"search\",\"enabled\":true}]")
    private String toolsConfig;

    /**
     * 知识库配置（JSON格式）
     */
    @Schema(description = "知识库配置", example = "{\"datasets\":[\"kb_001\",\"kb_002\"]}")
    private String knowledgeConfig;

    /**
     * 对话配置（JSON格式）
     */
    @Schema(description = "对话配置", example = "{\"maxTokens\":2000,\"temperature\":0.7}")
    private String conversationConfig;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "false")
    private Boolean isPublic;

    /**
     * 排序号
     */
    @Schema(description = "排序号", example = "1")
    private Integer sortOrder;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1.0.0")
    @Size(max = 20, message = "版本号长度不能超过20个字符")
    private String version;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "这是一个测试智能体")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 智能体来源类型：platform-本平台，external-外部智能体
     */
    @Schema(description = "智能体来源类型", example = "platform", allowableValues = {"platform", "external"})
    @Pattern(regexp = "^(platform|external)$", message = "智能体来源类型必须为platform或external")
    private String sourceType;

    /**
     * 外部智能体ID（当sourceType为external时使用）
     */
    @Schema(description = "外部智能体ID", example = "ext_agent_001")
    @Size(max = 36, message = "外部智能体ID长度不能超过36个字符")
    private String externalAgentId;

    /**
     * 第三方智能体平台ID
     */
    @Schema(description = "第三方智能体平台ID", example = "platform_001")
    @Size(max = 36, message = "平台ID长度不能超过36个字符")
    private String platformId;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getIconBackground() {
        return iconBackground;
    }

    public void setIconBackground(String iconBackground) {
        this.iconBackground = iconBackground;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getModelConfig() {
        return modelConfig;
    }

    public void setModelConfig(String modelConfig) {
        this.modelConfig = modelConfig;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public String getToolsConfig() {
        return toolsConfig;
    }

    public void setToolsConfig(String toolsConfig) {
        this.toolsConfig = toolsConfig;
    }

    public String getKnowledgeConfig() {
        return knowledgeConfig;
    }

    public void setKnowledgeConfig(String knowledgeConfig) {
        this.knowledgeConfig = knowledgeConfig;
    }

    public String getConversationConfig() {
        return conversationConfig;
    }

    public void setConversationConfig(String conversationConfig) {
        this.conversationConfig = conversationConfig;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getExternalAgentId() {
        return externalAgentId;
    }

    public void setExternalAgentId(String externalAgentId) {
        this.externalAgentId = externalAgentId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }
}
