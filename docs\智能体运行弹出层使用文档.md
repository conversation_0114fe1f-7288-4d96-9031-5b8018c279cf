# 智能体运行管理组件使用指南

本文档介绍如何在项目中使用智能体运行弹出层和最小化任务栏功能。

## 组件结构

```
components/agent/
├── AgentRunnerWindow.vue      # 智能体运行弹出层组件
├── MinimizedTaskbar.vue       # 最小化任务栏组件
├── AgentRunnerManager.vue     # 运行管理器组件
└── GlobalAgentRunner.vue      # 全局运行管理器组件

composables/
└── useAgentRunner.ts          # 状态管理 composable

services/
└── agentRunnerService.ts      # 全局服务
```

## 使用方式

### 方式一：使用 Composable（推荐）

在任何组件中直接使用 `useAgentRunner` composable：

```vue
<template>
  <div>
    <!-- 最小化任务栏 -->
    <MinimizedTaskbar
      :running-agents="minimizedAgents"
      @restore="restoreRunner"
      @close="closeSpecificAgent"
    />

    <!-- 智能体运行弹出层 -->
    <AgentRunnerWindow
      :visible="runnerModalVisible"
      :agent-info="currentRunningAgent"
      :is-minimized="isMinimized"
      :is-maximized="isMaximized"
      :position="windowPosition"
      :size="windowSize"
      @minimize="minimizeRunner"
      @close="closeRunnerModal"
      @update:position="updatePosition"
      @update:size="updateSize"
      @update:is-maximized="updateMaximized"
    />

    <!-- 触发按钮 -->
    <button @click="openAgent(agent)">运行智能体</button>
  </div>
</template>

<script setup lang="ts">
import { useAgentRunner } from '@/composables/useAgentRunner'
import MinimizedTaskbar from '@/components/agent/MinimizedTaskbar.vue'
import AgentRunnerWindow from '@/components/agent/AgentRunnerWindow.vue'

// 使用智能体运行管理器
const {
  runnerModalVisible,
  isMinimized,
  isMaximized,
  windowPosition,
  windowSize,
  currentRunningAgent,
  minimizedAgents,
  openRunnerModal,
  closeRunnerModal,
  minimizeRunner,
  restoreRunner,
  closeSpecificAgent,
  updatePosition,
  updateSize,
  updateMaximized
} = useAgentRunner({
  onAgentOpened: (agent) => {
    console.log('智能体已打开:', agent.name)
  },
  onAgentClosed: (agentId) => {
    console.log('智能体已关闭:', agentId)
  },
  onAgentMinimized: (agentId) => {
    console.log('智能体已最小化:', agentId)
  },
  onAgentRestored: (agentId) => {
    console.log('智能体已恢复:', agentId)
  }
})

// 打开智能体
const openAgent = (agent) => {
  openRunnerModal(agent)
}
</script>
```

### 方式二：使用全局服务

在任何组件中使用全局服务：

```vue
<template>
  <div>
    <button @click="openAgent(agent)">运行智能体</button>
    <button @click="closeCurrentAgent">关闭当前智能体</button>
    <button @click="minimizeCurrentAgent">最小化当前智能体</button>
  </div>
</template>

<script setup lang="ts">
import { agentRunnerService } from '@/services/agentRunnerService'

const openAgent = (agent) => {
  agentRunnerService.openAgent(agent)
}

const closeCurrentAgent = () => {
  agentRunnerService.closeAgent()
}

const minimizeCurrentAgent = () => {
  agentRunnerService.minimizeCurrentAgent()
}

// 获取运行状态
const getRunningState = () => {
  const state = agentRunnerService.getRunningState()
  console.log('当前运行状态:', state)
}

// 检查智能体是否正在运行
const checkAgentRunning = (agentId) => {
  return agentRunnerService.isAgentRunning(agentId)
}
</script>
```

### 方式三：使用管理器组件

使用封装好的管理器组件：

```vue
<template>
  <div>
    <!-- 使用管理器组件 -->
    <AgentRunnerManager
      ref="runnerManager"
      :events="events"
    />

    <button @click="openAgent(agent)">运行智能体</button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AgentRunnerManager from '@/components/agent/AgentRunnerManager.vue'

const runnerManager = ref()

const events = {
  onAgentOpened: (agent) => {
    console.log('智能体已打开:', agent.name)
  },
  onAgentClosed: (agentId) => {
    console.log('智能体已关闭:', agentId)
  }
}

const openAgent = (agent) => {
  runnerManager.value?.openRunnerModal(agent)
}
</script>
```

### 方式四：使用全局管理器组件（推荐用于应用级别）

`GlobalAgentRunner.vue` 是一个全局智能体运行管理器组件，适合在应用根级别使用。

#### 在应用根组件中配置

在 `App.vue` 中添加全局管理器：

```vue
<template>
  <div id="app">
    <!-- 你的应用内容 -->
    <router-view />

    <!-- 全局智能体运行管理器 -->
    <GlobalAgentRunner ref="globalAgentRunner" />
  </div>
</template>

<script setup lang="ts">
import { ref, provide } from 'vue'
import GlobalAgentRunner from '@/components/agent/GlobalAgentRunner.vue'

const globalAgentRunner = ref()

// 提供全局访问
provide('globalAgentRunner', globalAgentRunner)
</script>

<style>
#app {
  position: relative;
  min-height: 100vh;
}
</style>
```

#### 在主布局组件中使用

```vue
<template>
  <div class="main-layout">
    <!-- 头部 -->
    <header>
      <!-- 导航栏等 -->
    </header>

    <!-- 主要内容 -->
    <main>
      <router-view />
    </main>

    <!-- 全局智能体运行管理器 -->
    <GlobalAgentRunner ref="globalAgentRunner" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import GlobalAgentRunner from '@/components/agent/GlobalAgentRunner.vue'

const globalAgentRunner = ref()

// 暴露给父组件
defineExpose({
  openAgent: (agent) => globalAgentRunner.value?.openAgent(agent)
})
</script>
```

#### 在子组件中使用全局管理器

**方式 A：通过 inject 使用**

```vue
<template>
  <div>
    <button @click="runAgent">运行智能体</button>
  </div>
</template>

<script setup lang="ts">
import { inject } from 'vue'

const globalAgentRunner = inject('globalAgentRunner')

const agent = {
  id: '1',
  name: '测试智能体',
  description: '这是一个测试智能体',
  icon: 'fas fa-robot',
  unit: '技术部',
  creator: '开发者',
  createTime: '2024-01-01',
  type: '聊天助手',
  tags: ['测试']
}

const runAgent = () => {
  globalAgentRunner.value?.openAgent(agent)
}
</script>
```

**方式 B：通过全局服务使用（推荐）**

```vue
<template>
  <div>
    <button @click="runAgent">运行智能体</button>
    <button @click="checkStatus">检查运行状态</button>
  </div>
</template>

<script setup lang="ts">
import { agentRunnerService } from '@/services/agentRunnerService'

const agent = {
  id: '1',
  name: '测试智能体',
  // ... 其他属性
}

const runAgent = () => {
  agentRunnerService.openAgent(agent)
}

const checkStatus = () => {
  const state = agentRunnerService.getRunningState()
  console.log('当前运行状态:', state)
}
</script>
```

#### 自定义全局事件处理

如果需要自定义全局事件处理，可以修改 `GlobalAgentRunner.vue` 或创建自定义管理器：

```vue
<template>
  <AgentRunnerManager :events="customEvents" ref="runnerManager" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AgentRunnerManager from '@/components/agent/AgentRunnerManager.vue'

const runnerManager = ref()

const customEvents = {
  onAgentOpened: (agent) => {
    console.log('自定义事件 - 智能体已打开:', agent.name)
    // 发送统计数据
    // 显示通知
  },
  onAgentClosed: (agentId) => {
    console.log('自定义事件 - 智能体已关闭:', agentId)
    // 清理资源
  },
  onAgentMinimized: (agentId) => {
    console.log('自定义事件 - 智能体已最小化:', agentId)
  },
  onAgentRestored: (agentId) => {
    console.log('自定义事件 - 智能体已恢复:', agentId)
  }
}
</script>
```

#### 完整的业务组件使用示例

```vue
<template>
  <div class="agent-list">
    <div
      v-for="agent in agents"
      :key="agent.id"
      class="agent-card"
    >
      <h3>{{ agent.name }}</h3>
      <p>{{ agent.description }}</p>
      <div class="agent-actions">
        <button @click="runAgent(agent)" class="btn-run">
          运行
        </button>
        <button
          v-if="isAgentRunning(agent.id)"
          @click="closeAgent(agent.id)"
          class="btn-close"
        >
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { agentRunnerService } from '@/services/agentRunnerService'

const agents = ref([
  {
    id: '1',
    name: '客服助手',
    description: '智能客服助手，可以回答常见问题',
    icon: 'fas fa-headset',
    unit: '客服部',
    creator: '客服主管',
    createTime: '2024-01-01',
    type: '聊天助手',
    tags: ['客服', '问答']
  },
  // ... 更多智能体
])

const runAgent = (agent) => {
  agentRunnerService.openAgent(agent)
}

const closeAgent = (agentId) => {
  agentRunnerService.closeAgent(agentId)
}

const isAgentRunning = (agentId) => {
  return agentRunnerService.isAgentRunning(agentId)
}
</script>

<style scoped>
.agent-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 20px;
}

.agent-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.agent-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.btn-run {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-close {
  background: #f56565;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
```

## 使用注意事项

### GlobalAgentRunner 使用注意事项

1. **唯一实例**：`GlobalAgentRunner` 在整个应用中只需要一个实例，通常放在根组件（`App.vue`）中。

2. **状态共享**：所有组件共享同一个运行状态，任何地方的操作都会影响全局状态。

3. **事件处理**：可以在 `GlobalAgentRunner` 中自定义事件处理逻辑，也可以通过服务监听状态变化。

4. **性能考虑**：由于是全局组件，建议将其放在应用的最外层，避免不必要的重新渲染。

5. **类型安全**：确保传递给智能体运行器的数据符合 `Agent` 接口定义。

6. **依赖注入**：使用 `provide/inject` 时，确保在正确的组件层级中提供和注入。

### 推荐的项目结构

```
src/
├── App.vue                    # 包含 GlobalAgentRunner
├── components/
│   ├── agent/
│   │   ├── GlobalAgentRunner.vue
│   │   ├── AgentRunnerManager.vue
│   │   ├── AgentRunnerWindow.vue
│   │   └── MinimizedTaskbar.vue
│   └── business/
│       └── AgentList.vue      # 业务组件，使用 agentRunnerService
├── composables/
│   └── useAgentRunner.ts      # 核心状态管理
├── services/
│   └── agentRunnerService.ts  # 全局服务接口
└── views/
    └── Agents.vue             # 智能体管理页面
```

### 最佳实践

1. **在根组件中配置 GlobalAgentRunner**
2. **在业务组件中使用 agentRunnerService**
3. **使用 TypeScript 确保类型安全**
4. **自定义事件处理以满足业务需求**
5. **合理使用 provide/inject 或全局服务**

## API 参考

### useAgentRunner

#### 参数
- `events?: AgentRunnerEvents` - 事件回调配置

#### 返回值
- `runnerModalVisible` - 运行窗口是否可见
- `isMinimized` - 是否最小化
- `isMaximized` - 是否最大化
- `windowPosition` - 窗口位置
- `windowSize` - 窗口大小
- `currentRunningAgent` - 当前运行的智能体
- `minimizedAgents` - 最小化的智能体列表
- `hasMinimizedAgents` - 是否有最小化的智能体
- `openRunnerModal(agent)` - 打开智能体
- `closeRunnerModal()` - 关闭运行窗口
- `minimizeRunner()` - 最小化当前智能体
- `restoreRunner(agentId)` - 恢复智能体
- `closeSpecificAgent(agentId)` - 关闭指定智能体
- `updatePosition(position)` - 更新窗口位置
- `updateSize(size)` - 更新窗口大小
- `updateMaximized(maximized)` - 更新最大化状态

### agentRunnerService

#### 方法
- `openAgent(agent)` - 打开智能体
- `closeAgent(agentId?)` - 关闭智能体
- `minimizeCurrentAgent()` - 最小化当前智能体
- `restoreAgent(agentId)` - 恢复智能体
- `getRunningState()` - 获取运行状态
- `isAgentRunning(agentId)` - 检查智能体是否正在运行
- `getRunningAgentIds()` - 获取所有运行中的智能体ID
- `getAgentRunningState(agentId)` - 获取指定智能体的运行状态

### GlobalAgentRunner

#### Props
- `events?: AgentRunnerEvents` - 事件回调配置（可选）

#### 暴露的方法
- `openAgent(agent)` - 打开智能体

#### 使用示例
```vue
<GlobalAgentRunner ref="globalRunner" />

<script setup>
const globalRunner = ref()

const openAgent = (agent) => {
  globalRunner.value?.openAgent(agent)
}
</script>
```

### AgentRunnerManager

#### Props
- `events?: AgentRunnerEvents` - 事件回调配置（可选）

#### 暴露的方法
- `openRunnerModal(agent)` - 打开智能体运行窗口

#### 使用示例
```vue
<AgentRunnerManager ref="runnerManager" :events="events" />

<script setup>
const runnerManager = ref()
const events = {
  onAgentOpened: (agent) => console.log('打开:', agent.name)
}

const openAgent = (agent) => {
  runnerManager.value?.openRunnerModal(agent)
}
</script>
```

## 事件类型

```typescript
interface AgentRunnerEvents {
  onAgentOpened?: (agent: Agent) => void
  onAgentClosed?: (agentId: string) => void
  onAgentMinimized?: (agentId: string) => void
  onAgentRestored?: (agentId: string) => void
}
```

## 智能体类型

```typescript
interface Agent {
  id: string
  name: string
  description: string
  icon: string
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
}
```

## 特性

1. **全局状态管理** - 所有组件共享同一个运行状态
2. **事件驱动** - 支持自定义事件回调
3. **多窗口支持** - 支持同时运行多个智能体
4. **最小化任务栏** - 最小化的智能体显示在任务栏中
5. **窗口管理** - 支持拖拽、调整大小、最大化/还原
6. **类型安全** - 完整的 TypeScript 类型支持

## 快速开始

### 1. 在应用中配置全局管理器

```vue
<!-- App.vue -->
<template>
  <div id="app">
    <router-view />
    <GlobalAgentRunner />
  </div>
</template>

<script setup>
import GlobalAgentRunner from '@/components/agent/GlobalAgentRunner.vue'
</script>
```

### 2. 在业务组件中使用

```vue
<!-- 任意业务组件 -->
<template>
  <button @click="runAgent">运行智能体</button>
</template>

<script setup>
import { agentRunnerService } from '@/services/agentRunnerService'

const agent = { /* 智能体数据 */ }

const runAgent = () => {
  agentRunnerService.openAgent(agent)
}
</script>
```

### 3. 完成！

现在你可以在应用的任何地方使用智能体运行功能，无需重复配置。

## 总结

这套智能体运行管理系统提供了：

1. **统一的状态管理** - 通过 `useAgentRunner` composable
2. **全局服务接口** - 通过 `agentRunnerService`
3. **即插即用的组件** - `GlobalAgentRunner`、`AgentRunnerManager` 等
4. **完整的类型支持** - TypeScript 类型定义
5. **灵活的事件系统** - 自定义事件回调
6. **多种使用方式** - 适应不同的使用场景

选择最适合你项目需求的使用方式，享受便捷的智能体运行管理功能！
