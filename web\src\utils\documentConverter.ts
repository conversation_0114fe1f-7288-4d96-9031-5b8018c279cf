/**
 * 文档转换工具类
 * 支持将 Markdown、Word、PDF 等文档转换为富文本HTML
 */

import { marked } from 'marked'

export interface ConversionResult {
  success: boolean
  content?: string
  error?: string
  metadata?: {
    fileName: string
    fileSize: number
    fileType: string
    convertedAt: string
  }
}

export class DocumentConverter {
  private readonly maxFileSize = 10 * 1024 * 1024 // 10MB

  /**
   * 将文件转换为HTML格式
   */
  async convertToHtml(file: File): Promise<ConversionResult> {
    try {
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        return {
          success: false,
          error: `文件大小超过限制 (${Math.round(this.maxFileSize / 1024 / 1024)}MB)`
        }
      }

      const fileExtension = this.getFileExtension(file.name).toLowerCase()
      let content = ''

      switch (fileExtension) {
        case 'md':
        case 'markdown':
          content = await this.convertMarkdown(file)
          break
        case 'txt':
          content = await this.convertText(file)
          break
        case 'docx':
        case 'doc':
          content = await this.convertWord(file)
          break

        case 'pdf':
          content = await this.convertPdf(file)
          break
        default:
          return {
            success: false,
            error: `不支持的文件格式: ${fileExtension}`
          }
      }

      return {
        success: true,
        content,
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: fileExtension,
          convertedAt: new Date().toISOString()
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '转换过程中发生未知错误'
      }
    }
  }

  /**
   * 转换Markdown文件
   */
  private async convertMarkdown(file: File): Promise<string> {
    const text = await this.readFileAsText(file)
    
    // 配置marked选项
    marked.setOptions({
      breaks: true,
      gfm: true
    })

    return marked(text)
  }

  /**
   * 转换纯文本文件
   */
  private async convertText(file: File): Promise<string> {
    const text = await this.readFileAsText(file)
    
    // 将纯文本转换为HTML，保留换行
    return text
      .split('\n')
      .map(line => `<p>${this.escapeHtml(line) || '&nbsp;'}</p>`)
      .join('')
  }

  /**
   * 转换Word文档
   */
  private async convertWord(file: File): Promise<string> {
    // 注意：这里需要使用mammoth.js库来解析Word文档
    // 由于mammoth.js较大，这里提供一个简化的实现
    try {
      // 如果安装了mammoth.js，可以使用以下代码：
      // const mammoth = await import('mammoth')
      // const result = await mammoth.convertToHtml({ arrayBuffer: await file.arrayBuffer() })
      // return result.value
      
      // 简化实现：提示用户手动复制粘贴
      return `
        <div class="import-notice" style="padding: 20px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; margin: 10px 0;">
          <h3 style="color: #495057; margin-top: 0;">Word文档导入提示</h3>
          <p style="color: #6c757d; margin-bottom: 10px;">
            检测到Word文档 "${file.name}"。由于浏览器限制，请按以下步骤操作：
          </p>
          <ol style="color: #6c757d; padding-left: 20px;">
            <li>在Word中打开文档</li>
            <li>选择所有内容 (Ctrl+A)</li>
            <li>复制内容 (Ctrl+C)</li>
            <li>在此编辑器中粘贴 (Ctrl+V)</li>
          </ol>
          <p style="color: #28a745; margin-bottom: 0;">
            <strong>提示：</strong>直接粘贴可以保留大部分格式
          </p>
        </div>
      `
    } catch (error) {
      throw new Error(`Word文档转换失败: ${error}`)
    }
  }



  /**
   * 转换PDF文件
   */
  private async convertPdf(file: File): Promise<string> {
    // 注意：PDF解析需要pdf-lib或pdf2pic等库
    // 这里提供一个简化的实现
    return `
      <div class="pdf-import-notice" style="padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; margin: 10px 0;">
        <h3 style="color: #856404; margin-top: 0;">PDF文档导入提示</h3>
        <p style="color: #856404; margin-bottom: 10px;">
          检测到PDF文档 "${file.name}"。由于浏览器安全限制，请按以下方式处理：
        </p>
        <ol style="color: #856404; padding-left: 20px;">
          <li>使用PDF阅读器打开文档</li>
          <li>选择需要的文本内容</li>
          <li>复制文本 (Ctrl+C)</li>
          <li>在此编辑器中粘贴 (Ctrl+V)</li>
        </ol>
        <p style="color: #856404; margin-bottom: 0;">
          <strong>建议：</strong>对于复杂的PDF文档，建议先转换为Word格式再导入
        </p>
      </div>
    `
  }

  /**
   * 读取文件为文本
   */
  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result
        if (typeof result === 'string') {
          resolve(result)
        } else {
          reject(new Error('无法读取文件内容'))
        }
      }
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'UTF-8')
    })
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : ''
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }
}
