package com.xhcai.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.system.dto.SysDeptQueryDTO;
import com.xhcai.modules.system.entity.SysDept;
import com.xhcai.modules.system.vo.SysDeptVO;

import java.util.List;

/**
 * 部门信息服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysDeptService extends IService<SysDept> {

    /**
     * 查询部门列表
     *
     * @param queryDTO 查询条件
     * @return 部门列表
     */
    List<SysDeptVO> selectDeptList(SysDeptQueryDTO queryDTO);

    /**
     * 查询部门树
     *
     * @param queryDTO 查询条件
     * @return 部门树
     */
    List<SysDeptVO> selectDeptTree(SysDeptQueryDTO queryDTO);

    /**
     * 根据部门ID查询部门信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    SysDeptVO selectDeptById(String deptId);

    /**
     * 根据部门编码查询部门信息
     *
     * @param deptCode 部门编码
     * @return 部门信息
     */
    SysDept selectByDeptCode(String deptCode);

    /**
     * 创建部门
     *
     * @param dept 部门信息
     * @return 是否成功
     */
    boolean insertDept(SysDept dept);

    /**
     * 更新部门信息
     *
     * @param dept 部门信息
     * @return 是否成功
     */
    boolean updateDept(SysDept dept);

    /**
     * 删除部门
     *
     * @param deptIds 部门ID列表
     * @return 是否成功
     */
    boolean deleteDepts(List<String> deptIds);

    /**
     * 检查部门编码是否存在
     *
     * @param deptCode 部门编码
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    boolean existsDeptCode(String deptCode, String excludeId);

    /**
     * 检查部门名称是否存在（同级部门）
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    boolean existsDeptName(String deptName, String parentId, String excludeId);

    /**
     * 检查是否存在子部门
     *
     * @param deptId 部门ID
     * @return 是否存在子部门
     */
    boolean hasChildren(String deptId);

    /**
     * 检查部门下是否有用户
     *
     * @param deptId 部门ID
     * @return 是否有用户
     */
    boolean hasUsers(String deptId);

    /**
     * 查询子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<SysDeptVO> selectChildrenByParentId(String parentId);

    /**
     * 查询部门及其所有子部门
     *
     * @param deptId 部门ID
     * @return 部门及子部门列表
     */
    List<SysDeptVO> selectDeptAndChildren(String deptId);

    /**
     * 移动部门
     *
     * @param deptId 部门ID
     * @param newParentId 新父部门ID
     * @return 是否成功
     */
    boolean moveDept(String deptId, String newParentId);

    /**
     * 启用部门
     *
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean enableDept(String deptId);

    /**
     * 停用部门
     *
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean disableDept(String deptId);

    /**
     * 获取部门用户数量
     *
     * @param deptId 部门ID
     * @return 用户数量
     */
    Integer getDeptUserCount(String deptId);

    /**
     * 构建部门树
     *
     * @param depts 部门列表
     * @return 部门树
     */
    List<SysDeptVO> buildDeptTree(List<SysDept> depts);

    /**
     * 构建部门选择树（排除指定部门及其子部门）
     *
     * @param excludeDeptId 排除的部门ID
     * @return 部门选择树
     */
    List<SysDeptVO> buildDeptSelectTree(String excludeDeptId);

    /**
     * 获取部门路径
     *
     * @param deptId 部门ID
     * @return 部门路径（如：总公司/技术部/开发组）
     */
    String getDeptPath(String deptId);

    /**
     * 批量更新部门状态
     *
     * @param deptIds 部门ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> deptIds, String status);

    /**
     * 同步部门排序
     *
     * @param depts 部门列表（包含新的排序）
     * @return 是否成功
     */
    boolean syncDeptOrder(List<SysDept> depts);
}
