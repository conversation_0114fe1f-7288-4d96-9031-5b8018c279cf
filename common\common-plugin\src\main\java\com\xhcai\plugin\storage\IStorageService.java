package com.xhcai.plugin.storage;

import org.pf4j.ExtensionPoint;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 存储服务接口
 * 统一的存储服务接口，支持 MinIO、FTP、OSS 等不同存储实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IStorageService extends ExtensionPoint {
    
    /**
     * 获取存储服务名称
     */
    String getServiceName();
    
    /**
     * 获取存储服务类型
     */
    String getServiceType();
    
    /**
     * 初始化存储服务
     */
    void initialize(Map<String, Object> config);
    
    /**
     * 上传文件
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param inputStream 文件输入流
     * @param contentType 文件类型
     * @return 文件访问URL
     */
    String uploadFile(String bucketName, String objectName, InputStream inputStream, String contentType);
    
    /**
     * 上传文件（带元数据）
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param inputStream 文件输入流
     * @param contentType 文件类型
     * @param metadata 文件元数据
     * @return 文件访问URL
     */
    String uploadFile(String bucketName, String objectName, InputStream inputStream, String contentType, Map<String, String> metadata);
    
    /**
     * 下载文件
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件输入流
     */
    InputStream downloadFile(String bucketName, String objectName);
    
    /**
     * 删除文件
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否删除成功
     */
    boolean deleteFile(String bucketName, String objectName);
    
    /**
     * 批量删除文件
     * 
     * @param bucketName 存储桶名称
     * @param objectNames 对象名称列表
     * @return 删除结果
     */
    Map<String, Boolean> deleteFiles(String bucketName, List<String> objectNames);
    
    /**
     * 检查文件是否存在
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否存在
     */
    boolean fileExists(String bucketName, String objectName);
    
    /**
     * 获取文件信息
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件信息
     */
    StorageFileInfo getFileInfo(String bucketName, String objectName);
    
    /**
     * 列出文件
     * 
     * @param bucketName 存储桶名称
     * @param prefix 前缀
     * @param maxKeys 最大数量
     * @return 文件列表
     */
    List<StorageFileInfo> listFiles(String bucketName, String prefix, int maxKeys);
    
    /**
     * 生成预签名URL
     * 
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expireSeconds 过期时间（秒）
     * @return 预签名URL
     */
    String generatePresignedUrl(String bucketName, String objectName, int expireSeconds);
    
    /**
     * 创建存储桶
     * 
     * @param bucketName 存储桶名称
     * @return 是否创建成功
     */
    boolean createBucket(String bucketName);
    
    /**
     * 删除存储桶
     * 
     * @param bucketName 存储桶名称
     * @return 是否删除成功
     */
    boolean deleteBucket(String bucketName);
    
    /**
     * 检查存储桶是否存在
     * 
     * @param bucketName 存储桶名称
     * @return 是否存在
     */
    boolean bucketExists(String bucketName);
    
    /**
     * 获取存储服务健康状态
     */
    boolean isHealthy();
}
