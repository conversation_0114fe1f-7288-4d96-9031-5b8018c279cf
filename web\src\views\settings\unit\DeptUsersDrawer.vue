<template>
  <!-- 部门用户管理侧边栏 -->
  <div v-if="visible" class="fixed inset-0 z-50 overflow-hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50" @click="handleClose"></div>
    <div class="absolute right-0 top-0 h-full w-96 bg-white shadow-2xl transform transition-transform duration-300 ease-in-out">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
            <el-icon class="text-white text-lg"><User /></el-icon>
          </div>
          <div>
            <h3 class="text-lg font-bold text-gray-900">部门用户管理</h3>
            <p class="text-sm text-gray-500">{{ currentDept?.deptName }}</p>
          </div>
        </div>
        <button @click="handleClose" class="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200">
          <el-icon class="text-gray-500"><Close /></el-icon>
        </button>
      </div>

      <!-- 操作区域 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <span class="text-sm font-medium text-gray-700">
            共 {{ deptUsers.length }} 名用户
          </span>
          <el-button type="primary" size="small" @click="showAddUserModal = true" :icon="Plus">
            添加用户
          </el-button>
        </div>

        <!-- 搜索框 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户..."
          size="small"
          :prefix-icon="Search"
          clearable
        />
      </div>

      <!-- 用户列表 -->
      <div class="flex-1 overflow-y-auto p-4">
        <div v-if="filteredUsers.length === 0" class="text-center py-8 text-gray-500">
          <el-icon class="text-4xl mb-2"><User /></el-icon>
          <p>暂无用户</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="user in filteredUsers"
            :key="user.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <div class="flex items-center space-x-3">
              <el-avatar :size="32">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div>
                <p class="font-medium text-gray-900">{{ user.nickname || user.username }}</p>
                <p class="text-sm text-gray-500">{{ user.username }}</p>
              </div>
            </div>
            <el-button
              type="danger"
              size="small"
              @click="handleRemoveUser(user.id)"
              :icon="Delete"
              circle
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 添加用户到部门模态框 -->
    <AddUserToDeptModal
      :visible="showAddUserModal"
      :available-users="availableUsers"
      :loading="loading"
      @close="showAddUserModal = false"
      @add-users="handleAddUsers"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Close, Plus, Search, Delete } from '@element-plus/icons-vue'
import { UserAPI } from '@/api/system'
import AddUserToDeptModal from './AddUserToDeptModal.vue'
import type {
  SysDeptVO,
  SysUserVO
} from '@/types/system'

// Props
interface Props {
  visible: boolean
  currentDept: SysDeptVO | null
  deptUsers: SysUserVO[]
  availableUsers: SysUserVO[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
const emit = defineEmits<{
  close: []
  'load-dept-users': [deptId: string]
  'refresh-users': []
}>()

// 搜索查询
const searchQuery = ref('')

// 添加用户模态框状态
const showAddUserModal = ref(false)

// 过滤后的用户列表
const filteredUsers = computed(() => {
  if (!searchQuery.value) {
    return props.deptUsers
  }

  const query = searchQuery.value.toLowerCase()
  return props.deptUsers.filter(user =>
    user.username.toLowerCase().includes(query) ||
    user.nickname?.toLowerCase().includes(query)
  )
})

// 关闭侧边栏
const handleClose = () => {
  searchQuery.value = ''
  showAddUserModal.value = false
  emit('close')
}

// 添加用户到部门
const handleAddUsers = async (userIds: string[]) => {
  if (!props.currentDept) return

  try {
    // 批量更新用户的部门信息
    for (const userId of userIds) {
      // 先获取用户完整信息
      const userResponse = await UserAPI.getUserById(userId)
      if (userResponse.success && userResponse.data) {
        const userData = userResponse.data
        userData.deptId = props.currentDept.id
        await UserAPI.updateUser(userData)
      }
    }

    ElMessage.success('用户添加成功')
    emit('load-dept-users', props.currentDept.id)
    emit('refresh-users')
    showAddUserModal.value = false
  } catch (error) {
    console.error('添加用户到部门失败:', error)
    ElMessage.error('添加用户失败')
  }
}

// 从部门移除用户
const handleRemoveUser = async (userId: string) => {
  if (!props.currentDept) return

  try {
    await ElMessageBox.confirm(
      '确定要将该用户从部门中移除吗？',
      '移除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 先获取用户完整信息
    const userResponse = await UserAPI.getUserById(userId)
    if (userResponse.success && userResponse.data) {
      const userData = userResponse.data
      userData.deptId = '' // 清空部门ID
      await UserAPI.updateUser(userData)
    }

    ElMessage.success('用户移除成功')
    emit('load-dept-users', props.currentDept.id)
    emit('refresh-users')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除用户失败:', error)
      ElMessage.error('移除用户失败')
    }
  }
}
</script>

<style scoped>
/* 继承父组件样式 */
</style>
