package com.xhcai.common.security.annotation;

import java.lang.annotation.*;

/**
 * 跨租户访问注解
 * 用于标识允许跨租户访问的方法或类
 * 只有平台管理员可以进行跨租户访问
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CrossTenantAccess {

    /**
     * 权限校验失败时的提示信息
     */
    String message() default "跨租户访问需要平台管理员权限";

    /**
     * 是否记录跨租户访问日志
     */
    boolean logAccess() default true;
}
