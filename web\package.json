{"name": "xhc-ai-web", "version": "0.0.0", "type": "module", "private": true, "scripts": {"build": "run-p type-check \"build-only {@}\" --", "build:modern": "vite build --mode production", "build:legacy": "vite build --mode legacy", "build:prod": "vite build --mode production", "build:analyze": "vite build --mode production && node scripts/analyze-bundle.js", "analyze": "node scripts/analyze-bundle.js", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "dev": "vite", "serve": "vite preview", "test:compat": "node scripts/test-compatibility.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@tiptap/extension-code-block-lowlight": "^2.23.1", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-font-family": "^2.23.0", "@tiptap/extension-text-align": "^2.23.0", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "@tiptap/vue-3": "^2.23.0", "@types/lodash-es": "^4.17.12", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "1.45.0", "@vue-flow/minimap": "^1.5.3", "@vue-flow/node-resizer": "^1.5.0", "chart.js": "^4.4.0", "dompurify": "^3.2.6", "element-plus": "^2.10.2", "lodash-es": "^4.17.21", "lowlight": "^3.3.0", "marked": "^11.1.1", "md5": "^2.3.0", "mermaid": "^11.4.0", "pinia": "^2.1.7", "uuid": "^11.1.0", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/preset-env": "^7.23.0", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^20.19.0", "@vitejs/plugin-legacy": "^7.1.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "core-js": "^3.35.0", "cssnano": "^6.0.0", "eslint": "9.32.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "postcss": "^8.4.32", "postcss-preset-env": "^9.3.0", "prettier": "^3.0.3", "tailwindcss": "^3.4.0", "terser": "^5.26.0", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vue-tsc": "^3.0.0"}}