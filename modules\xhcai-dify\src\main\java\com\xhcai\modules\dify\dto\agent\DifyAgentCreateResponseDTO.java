package com.xhcai.modules.dify.dto.agent;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Dify智能体创建响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "Dify智能体创建响应")
public class DifyAgentCreateResponseDTO {

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "9dc52c66-6e7d-45c1-9ec3-5037b8ef171a")
    private String id;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "客服助手")
    private String name;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述", example = "专业的客服助手")
    private String description;

    /**
     * 智能体模式
     */
    @Schema(description = "智能体模式", example = "advanced-chat")
    private String mode;

    /**
     * 图标
     */
    @Schema(description = "图标", example = "🤖")
    private String icon;

    /**
     * 图标背景色
     */
    @Schema(description = "图标背景色", example = "#FFEAD5")
    @JSONField(name = "icon_background")
    private String iconBackground;

    /**
     * 是否启用站点
     */
    @Schema(description = "是否启用站点", example = "true")
    @JSONField(name = "enable_site")
    private Boolean enableSite;

    /**
     * 是否启用API
     */
    @Schema(description = "是否启用API", example = "true")
    @JSONField(name = "enable_api")
    private Boolean enableApi;

    /**
     * 模型配置
     */
    @Schema(description = "模型配置")
    @JSONField(name = "model_config")
    private Object modelConfig;

    /**
     * 工作流配置
     */
    @Schema(description = "工作流配置")
    private Object workflow;

    /**
     * 追踪配置
     */
    @Schema(description = "追踪配置")
    private Object tracing;

    /**
     * 是否使用图标作为答案图标
     */
    @Schema(description = "是否使用图标作为答案图标", example = "false")
    @JSONField(name = "use_icon_as_answer_icon")
    private Boolean useIconAsAnswerIcon;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID", example = "1aca5f24-80d1-43fa-8690-c59a84db9016")
    @JSONField(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "1756871116")
    @JSONField(name = "created_at")
    private Long createdAt;

    /**
     * 更新者ID
     */
    @Schema(description = "更新者ID", example = "1aca5f24-80d1-43fa-8690-c59a84db9016")
    @JSONField(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "1756871116")
    @JSONField(name = "updated_at")
    private Long updatedAt;

    /**
     * 访问模式
     */
    @Schema(description = "访问模式")
    @JSONField(name = "access_mode")
    private String accessMode;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBackground() {
        return iconBackground;
    }

    public void setIconBackground(String iconBackground) {
        this.iconBackground = iconBackground;
    }

    public Boolean getEnableSite() {
        return enableSite;
    }

    public void setEnableSite(Boolean enableSite) {
        this.enableSite = enableSite;
    }

    public Boolean getEnableApi() {
        return enableApi;
    }

    public void setEnableApi(Boolean enableApi) {
        this.enableApi = enableApi;
    }

    public Object getModelConfig() {
        return modelConfig;
    }

    public void setModelConfig(Object modelConfig) {
        this.modelConfig = modelConfig;
    }

    public Object getWorkflow() {
        return workflow;
    }

    public void setWorkflow(Object workflow) {
        this.workflow = workflow;
    }

    public Object getTracing() {
        return tracing;
    }

    public void setTracing(Object tracing) {
        this.tracing = tracing;
    }

    public Boolean getUseIconAsAnswerIcon() {
        return useIconAsAnswerIcon;
    }

    public void setUseIconAsAnswerIcon(Boolean useIconAsAnswerIcon) {
        this.useIconAsAnswerIcon = useIconAsAnswerIcon;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getAccessMode() {
        return accessMode;
    }

    public void setAccessMode(String accessMode) {
        this.accessMode = accessMode;
    }

    @Override
    public String toString() {
        return "DifyAgentCreateResponseDTO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", mode='" + mode + '\'' +
                ", icon='" + icon + '\'' +
                ", iconBackground='" + iconBackground + '\'' +
                ", enableSite=" + enableSite +
                ", enableApi=" + enableApi +
                ", modelConfig=" + modelConfig +
                ", workflow=" + workflow +
                ", tracing=" + tracing +
                ", useIconAsAnswerIcon=" + useIconAsAnswerIcon +
                ", createdBy='" + createdBy + '\'' +
                ", createdAt=" + createdAt +
                ", updatedBy='" + updatedBy + '\'' +
                ", updatedAt=" + updatedAt +
                ", accessMode='" + accessMode + '\'' +
                '}';
    }
}
