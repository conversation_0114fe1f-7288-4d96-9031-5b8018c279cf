<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="fas fa-users"></i>
          团队人员管理
        </h3>
        <button class="modal-close" @click="handleClose">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <div class="project-info">
          <h4>{{ project?.name }}</h4>
          <p>{{ project?.description }}</p>
        </div>

        <!-- 负责人信息 -->
        <div class="section">
          <h5 class="section-title">项目负责人</h5>
          <div class="owner-info">
            <div class="user-avatar">{{ project?.ownerName?.charAt(0) }}</div>
            <div class="user-details">
              <div class="user-name">{{ project?.ownerName }}</div>
              <div class="user-role">项目负责人</div>
            </div>
          </div>
        </div>

        <!-- 团队成员 -->
        <div class="section">
          <div class="section-header">
            <h5 class="section-title">团队成员</h5>
            <button class="btn btn-sm btn-primary" @click="showAddMember = true">
              <i class="fas fa-plus"></i>
              添加成员
            </button>
          </div>

          <div v-if="teamMembers.length === 0" class="empty-state">
            <i class="fas fa-users"></i>
            <p>暂无团队成员</p>
          </div>

          <div v-else class="members-list">
            <div
              v-for="member in teamMembers"
              :key="member.id"
              class="member-item"
            >
              <div class="user-avatar">{{ member.nickname?.charAt(0) }}</div>
              <div class="user-details">
                <div class="user-name">{{ member.nickname }}</div>
                <div class="user-email">{{ member.email }}</div>
              </div>
              <div class="role-section">
                <input
                  type="text"
                  v-model="member.roles"
                  class="role-input"
                  placeholder="角色"
                />
              </div>
              <button
                class="btn-remove"
                @click="removeMember(member.id)"
                title="移除成员"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 添加成员 -->
        <div v-if="showAddMember" class="add-member-section">
          <div class="section-header">
            <h5 class="section-title">添加新成员</h5>
            <button class="btn-close" @click="showAddMember = false">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="user-selector">
            <ElUserByDeptSelector
              v-model="selectedUserIds"
              :config="{
                multiple: true,
                size: 'default',
                disabled: false
              }"
              :exclude-user-ids="existingMemberIds"
              @change="handleUserSelectionChange"
            />

            <div class="add-member-actions">
              <button
                class="btn btn-secondary"
                @click="showAddMember = false"
              >
                取消
              </button>
              <button
                class="btn btn-primary"
                @click="confirmAddMembers"
                :disabled="selectedUserIds.length === 0"
              >
                添加选中成员 ({{ selectedUserIds.length }})
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="handleClose">
          取消
        </button>
        <button class="btn btn-primary" @click="handleSave">
          保存
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {type SysUserVO} from '@/types/system'
import ElUserByDeptSelector from '@/components/common/el-selectors/ElUserByDeptSelector.vue'

interface BusinessProject {
  id: string
  name: string
  description: string
  ownerName: string
  teamMembers: SysUserVO[]
}

// Props
const props = defineProps<{
  visible: boolean
  project?: BusinessProject | null
}>()

// Emits
const emit = defineEmits<{
  close: []
  save: [members: SysUserVO[]]
}>()

// 响应式数据
const teamMembers = ref<SysUserVO[]>([])
const showAddMember = ref(false)
const searchQuery = ref('')
const users = ref<SysUserVO[]>([])
const filteredUsers = ref<SysUserVO[]>([])
const selectedUserIds = ref<string[]>([])

// 计算属性
const existingMemberIds = computed(() => {
  return [
    ...teamMembers.value.map(m => m.id),
    props.project?.ownerName // 排除负责人
  ].filter(Boolean) as string[]
})

// 方法
const loadUsers = async () => {
  try {
    // TODO: 调用API获取用户列表
    // const response = await api.getUsers()
    // users.value = response.data
    
    // 模拟数据
    users.value = [
      { id: '1', nickname: '张三', email: '<EMAIL>', status: "0" },
      { id: '2', nickname: '李四', email: '<EMAIL>', status: "0" },
      { id: '3', nickname: '王五', email: '<EMAIL>', status: "0" },
      { id: '4', nickname: '赵六', email: '<EMAIL>', status: "0" },
      { id: '5', nickname: '钱七', email: '<EMAIL>', status: "0" },
      { id: '6', nickname: '孙八', email: '<EMAIL>', status: "0" }
    ]
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

const searchUsers = () => {
  if (!searchQuery.value.trim()) {
    filteredUsers.value = []
    return
  }

  const query = searchQuery.value.toLowerCase()
  filteredUsers.value = users.value.filter(user =>
    !existingMemberIds.value.includes(user.id) &&
    (user.nickname?.toLowerCase().includes(query) ||
     user.email?.toLowerCase().includes(query))
  )
}

const handleUserSelectionChange = (userIds: string[]) => {
  selectedUserIds.value = userIds
}

const confirmAddMembers = async () => {
  if (selectedUserIds.value.length === 0) return

  try {
    // TODO: 调用API获取选中用户的详细信息
    // const response = await api.getUsersByIds(selectedUserIds.value)
    // const selectedUsers = response.data

    // 模拟获取用户信息
    const selectedUsers = users.value.filter(user =>
      selectedUserIds.value.includes(user.id)
    )

    // 添加到团队成员列表
    selectedUsers.forEach(user => {
      if (!teamMembers.value.find(m => m.id === user.id)) {
        teamMembers.value.push({
          ...user,
          roles: []
        })
      }
    })

    // 重置选择状态
    selectedUserIds.value = []
    showAddMember.value = false
  } catch (error) {
    console.error('添加成员失败:', error)
  }
}



const removeMember = (userId: string) => {
  teamMembers.value = teamMembers.value.filter(member => member.id !== userId)
}

const handleSave = () => {
  emit('save', [...teamMembers.value])
}

const handleClose = () => {
  emit('close')
}

const handleOverlayClick = () => {
  handleClose()
}

// 监听器
watch(() => props.project, (newProject) => {
  if (newProject) {
    teamMembers.value = [...newProject.teamMembers]
  }
}, { immediate: true })

watch(searchQuery, () => {
  searchUsers()
})

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-light);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-light);
}

/* 项目信息 */
.project-info {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.project-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.project-info p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* 区域样式 */
.section {
  margin-bottom: 24px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 负责人信息 */
.owner-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: var(--border-radius);
}

/* 用户样式 */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
}

.user-email {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.user-role {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.4;
}

/* 成员列表 */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
}

.role-section {
  flex-shrink: 0;
}

.role-input {
  width: 100px;
  padding: 6px 8px;
  border: 1px solid var(--border-light);
  border-radius: 4px;
  font-size: 12px;
}

.btn-remove {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
}

.btn-remove:hover {
  background: rgba(231, 76, 60, 0.2);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 添加成员 */
.add-member-section {
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 16px;
  margin-top: 16px;
}

.btn-close {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
}

.btn-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.user-selector {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  font-size: 14px;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: var(--transition);
}

.user-option:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.btn-secondary {
  background: white;
  color: var(--text-primary);
  border: 2px solid var(--border-light);
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* 添加成员操作按钮 */
.add-member-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
}

.add-member-actions .btn {
  min-width: 120px;
}
</style>
