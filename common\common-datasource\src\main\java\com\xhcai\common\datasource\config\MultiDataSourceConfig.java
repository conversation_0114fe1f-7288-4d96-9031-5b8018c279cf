package com.xhcai.common.datasource.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

/**
 * 多数据源配置
 * 基于 dynamic-datasource-spring-boot-starter 实现
 * 
 * 使用说明：
 * 1. 在方法或类上使用 @DS("数据源名称") 注解来切换数据源
 * 2. 支持的数据源名称：
 *    - master: 主数据源（系统核心数据）
 *    - ai: AI数据源（AI相关数据）
 *    - log: 日志数据源（日志数据）
 * 3. 如果不指定数据源，默认使用 master 数据源
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class MultiDataSourceConfig {

    private static final Logger log = LoggerFactory.getLogger(MultiDataSourceConfig.class);

    /**
     * 配置类构造函数
     * 在应用启动时输出多数据源配置信息
     */
    public MultiDataSourceConfig() {
        log.info("=== 多数据源配置已启用 ===");
        log.info("支持的数据源:");
        log.info("  - master: 主数据源（系统核心数据）");
        log.info("  - ai: AI数据源（AI相关数据）");
        log.info("  - log: 日志数据源（日志数据）");
        log.info("使用方法: 在方法或类上添加 @DS(\"数据源名称\") 注解");
        log.info("========================");
    }
}
