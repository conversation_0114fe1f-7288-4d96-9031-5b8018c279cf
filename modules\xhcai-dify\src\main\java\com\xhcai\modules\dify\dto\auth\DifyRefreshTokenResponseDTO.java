package com.xhcai.modules.dify.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Dify 刷新令牌响应 DTO
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Schema(description = "Dify 刷新令牌响应")
public class DifyRefreshTokenResponseDTO {

    /**
     * 结果状态
     */
    @Schema(description = "结果状态")
    private String result;

    /**
     * 数据 - 成功时为 TokenData 对象，失败时为错误消息字符串
     */
    @Schema(description = "数据")
    private JsonNode data;

    /**
     * 令牌数据
     */
    @Schema(description = "令牌数据")
    public static class TokenData {
        /**
         * 访问令牌
         */
        @JsonProperty("access_token")
        @Schema(description = "访问令牌")
        private String accessToken;

        /**
         * 刷新令牌
         */
        @JsonProperty("refresh_token")
        @Schema(description = "刷新令牌")
        private String refreshToken;

        // Getters and Setters
        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        @Override
        public String toString() {
            return "TokenData{" +
                    "accessToken='" + (accessToken != null ? accessToken.substring(0, Math.min(20, accessToken.length())) + "..." : "null") + '\'' +
                    ", refreshToken='" + (refreshToken != null ? refreshToken.substring(0, Math.min(20, refreshToken.length())) + "..." : "null") + '\'' +
                    '}';
        }
    }

    // Getters and Setters
    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public JsonNode getData() {
        return data;
    }

    public void setData(JsonNode data) {
        this.data = data;
    }

    /**
     * 获取令牌数据（仅在成功时有效）
     *
     * @return 令牌数据，失败时返回 null
     */
    public TokenData getTokenData() {
        if ("success".equals(result) && data != null && data.isObject()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                return mapper.treeToValue(data, TokenData.class);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取错误消息（仅在失败时有效）
     *
     * @return 错误消息，成功时返回 null
     */
    public String getErrorMessage() {
        if ("fail".equals(result) && data != null && data.isTextual()) {
            return data.asText();
        }
        return null;
    }

    /**
     * 检查刷新是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "success".equals(result) && data != null && data.isObject();
    }

    /**
     * 获取访问令牌
     */
    public String getAccessTokenFromData() {
        if (data != null && data.isObject()) {
            JsonNode accessTokenNode = data.get("access_token");
            return accessTokenNode != null ? accessTokenNode.asText() : null;
        }
        return null;
    }

    /**
     * 获取刷新令牌
     */
    public String getRefreshTokenFromData() {
        if (data != null && data.isObject()) {
            JsonNode refreshTokenNode = data.get("refresh_token");
            return refreshTokenNode != null ? refreshTokenNode.asText() : null;
        }
        return null;
    }

    /**
     * 设置令牌数据
     */
    public void setTokenData(String accessToken, String refreshToken) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            TokenData tokenData = new TokenData();
            tokenData.setAccessToken(accessToken);
            tokenData.setRefreshToken(refreshToken);
            this.data = mapper.valueToTree(tokenData);
        } catch (Exception e) {
            // 如果转换失败，创建一个简单的JsonNode
            this.data = mapper.createObjectNode()
                    .put("access_token", accessToken)
                    .put("refresh_token", refreshToken);
        }
    }

    @Override
    public String toString() {
        return "DifyRefreshTokenResponseDTO{" +
                "result='" + result + '\'' +
                ", data=" + data +
                '}';
    }
}
