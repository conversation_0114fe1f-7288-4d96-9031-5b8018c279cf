package com.xhcai.modules.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.system.dto.SysRoleQueryDTO;
import com.xhcai.modules.system.entity.SysRole;
import com.xhcai.modules.system.entity.SysRolePermission;
import com.xhcai.modules.system.mapper.SysRoleMapper;
import com.xhcai.modules.system.mapper.SysRolePermissionMapper;
import com.xhcai.modules.system.service.ISysRoleService;
import com.xhcai.modules.system.utils.DictUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.vo.SysRoleVO;

/**
 * 角色信息服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements ISysRoleService {

    private static final Logger log = LoggerFactory.getLogger(SysRoleServiceImpl.class);

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRolePermissionMapper rolePermissionMapper;

    @Override
    public PageResult<SysRoleVO> selectRolePage(SysRoleQueryDTO queryDTO) {
        Page<SysRole> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        Page<SysRole> rolePage = roleMapper.selectRolePage(page,
                queryDTO.getRoleCode(),
                queryDTO.getRoleName(),
                queryDTO.getStatus(),
                queryDTO.getDataScope());

        List<SysRoleVO> roleVOs = rolePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<>(roleVOs, rolePage.getTotal(), rolePage.getCurrent(), rolePage.getSize());
    }

    @Override
    public List<SysRoleVO> selectRoleList(SysRoleQueryDTO queryDTO) {
        List<SysRole> roles = roleMapper.selectRoleList(
                queryDTO.getRoleCode(),
                queryDTO.getRoleName(),
                queryDTO.getStatus(),
                queryDTO.getDataScope());

        return roles.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public SysRoleVO selectRoleById(String roleId) {
        if (roleId == null) {
            return null;
        }

        SysRole role = getById(roleId);
        if (role == null) {
            return null;
        }

        SysRoleVO roleVO = convertToVO(role);

        // 设置权限ID列表
        List<String> permissionIds = roleMapper.selectRolePermissionIds(roleId);
        roleVO.setPermissionIds(permissionIds);

        // 设置用户数量
        Integer userCount = roleMapper.countUsersByRoleId(roleId);
        roleVO.setUserCount(userCount);

        return roleVO;
    }

    @Override
    public SysRole selectByRoleCode(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            return null;
        }
        return roleMapper.selectByRoleCode(roleCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertRole(SysRole role) {
        // 参数校验
        validateRole(role, true);

        // 检查角色编码是否已存在
        if (StringUtils.hasText(role.getRoleCode()) && existsRoleCode(role.getRoleCode(), null)) {
            throw new BusinessException("角色编码已存在");
        }

        // 检查角色名称是否已存在
        if (existsRoleName(role.getRoleName(), null)) {
            throw new BusinessException("角色名称已存在");
        }

        // 设置排序号
        if (role.getRoleSort() == null) {
            Integer maxSort = roleMapper.selectMaxRoleSort();
            role.setRoleSort(maxSort + 1);
        }

        // 设置默认状态
        if (!StringUtils.hasText(role.getStatus())) {
            role.setStatus(CommonConstants.STATUS_NORMAL);
        }

        // 设置默认数据范围
        if (!StringUtils.hasText(role.getDataScope())) {
            role.setDataScope("5"); // 仅本人数据权限
        }

        // 设置租户ID
        if (role.getTenantId() == null) {
            role.setTenantId(SecurityUtils.getCurrentTenantId());
        }

        return save(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(SysRole role) {
        // 参数校验
        validateRole(role, false);

        // 检查角色是否存在
        SysRole existRole = getById(role.getId());
        if (existRole == null) {
            throw new BusinessException("角色不存在");
        }

        // 检查角色编码是否已存在（排除自己）
        if (StringUtils.hasText(role.getRoleCode()) && existsRoleCode(role.getRoleCode(), role.getId())) {
            throw new BusinessException("角色编码已存在");
        }

        // 检查角色名称是否已存在（排除自己）
        if (existsRoleName(role.getRoleName(), role.getId())) {
            throw new BusinessException("角色名称已存在");
        }

        return updateById(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoles(List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }

        for (String roleId : roleIds) {
            // 检查角色是否被用户使用
            if (isRoleUsedByUsers(roleId)) {
                SysRole role = getById(roleId);
                throw new BusinessException("角色【" + role.getRoleName() + "】已分配给用户，不能删除");
            }
        }

        // 删除角色权限关联
        rolePermissionMapper.deleteByRoleIds(roleIds);

        return removeByIds(roleIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableRole(String roleId) {
        return updateRoleStatus(roleId, CommonConstants.STATUS_NORMAL);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableRole(String roleId) {
        return updateRoleStatus(roleId, CommonConstants.STATUS_DISABLE);
    }

    @Override
    public boolean existsRoleCode(String roleCode, String excludeId) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }
        return roleMapper.existsRoleCode(roleCode, excludeId) > 0;
    }

    @Override
    public boolean existsRoleName(String roleName, String excludeId) {
        if (!StringUtils.hasText(roleName)) {
            return false;
        }
        return roleMapper.existsRoleName(roleName, excludeId) > 0;
    }

    @Override
    public List<SysRoleVO> selectRolesByUserId(String userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        List<SysRole> roles = roleMapper.selectRolesByUserId(userId);
        return roles.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysRole> selectRoleEntitiesByUserId(String userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        return roleMapper.selectRolesByUserId(userId);
    }

    @Override
    public Set<String> selectRoleCodesByUserId(String userId) {
        if (userId == null) {
            return Set.of();
        }
        return roleMapper.selectRoleCodesByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(String roleId, List<String> permissionIds) {
        if (roleId == null) {
            throw new BusinessException("角色ID不能为空");
        }

        // 先删除原有权限
        rolePermissionMapper.deleteByRoleId(roleId);

        // 分配新权限
        if (!CollectionUtils.isEmpty(permissionIds)) {
            // 使用MyBatis Plus的insert方法，会自动触发MetaObjectHandler
            for (String permissionId : permissionIds) {
                SysRolePermission rolePermission = new SysRolePermission();
                rolePermission.setRoleId(roleId);
                rolePermission.setPermissionId(permissionId);
                rolePermissionMapper.insert(rolePermission);
            }
            return true;
        }

        return true;
    }

    @Override
    public List<String> selectRolePermissionIds(String roleId) {
        if (roleId == null) {
            return new ArrayList<>();
        }
        return roleMapper.selectRolePermissionIds(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> roleIds, String status) {
        if (CollectionUtils.isEmpty(roleIds) || !StringUtils.hasText(status)) {
            return false;
        }

        return lambdaUpdate()
                .in(SysRole::getId, roleIds)
                .set(SysRole::getStatus, status)
                .update();
    }

    @Override
    public boolean isRoleUsedByUsers(String roleId) {
        if (roleId == null) {
            return false;
        }
        return roleMapper.countUsersByRoleId(roleId) > 0;
    }

    @Override
    public List<SysRoleVO> selectAllAvailableRoles() {
        List<SysRole> roles = roleMapper.selectAllAvailableRoles();
        return roles.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysRoleVO> selectRolesByDataScope(String dataScope) {
        if (!StringUtils.hasText(dataScope)) {
            return new ArrayList<>();
        }

        List<SysRole> roles = roleMapper.selectRolesByDataScope(dataScope);
        return roles.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyRolePermissions(String sourceRoleId, String targetRoleId) {
        if (sourceRoleId == null || targetRoleId == null) {
            throw new BusinessException("源角色ID和目标角色ID不能为空");
        }

        // 获取源角色权限
        List<String> permissionIds = selectRolePermissionIds(sourceRoleId);

        // 分配给目标角色
        return assignPermissions(targetRoleId, permissionIds);
    }

    /**
     * 参数校验
     */
    private void validateRole(SysRole role, boolean isCreate) {
        if (role == null) {
            throw new BusinessException("角色信息不能为空");
        }

        if (!StringUtils.hasText(role.getRoleName())) {
            throw new BusinessException("角色名称不能为空");
        }

        if (isCreate && !StringUtils.hasText(role.getRoleCode())) {
            throw new BusinessException("角色编码不能为空");
        }
    }

    /**
     * 更新角色状态
     */
    private boolean updateRoleStatus(String roleId, String status) {
        if (roleId == null || !StringUtils.hasText(status)) {
            throw new BusinessException("角色ID和状态不能为空");
        }

        SysRole role = getById(roleId);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        role.setStatus(status);
        return updateById(role);
    }

    /**
     * 转换为VO
     */
    private SysRoleVO convertToVO(SysRole role) {
        if (role == null) {
            return null;
        }

        SysRoleVO vo = new SysRoleVO();
        BeanUtils.copyProperties(role, vo);

        // 设置状态名称 - 使用字典工具类
        vo.setStatusName(DictUtils.getRoleStatusLabel(role.getStatus()));

        // 设置数据范围名称 - 使用字典工具类
        vo.setDataScopeName(DictUtils.getDataScopeLabel(role.getDataScope()));

        return vo;
    }
}
