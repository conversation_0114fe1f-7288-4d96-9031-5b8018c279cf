package com.xhcai.modules.rag.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.rag.dto.VectorDatabaseCreateDTO;
import com.xhcai.modules.rag.dto.VectorDatabaseQueryDTO;
import com.xhcai.modules.rag.dto.VectorDatabaseUpdateDTO;
import com.xhcai.modules.rag.entity.VectorDatabase;
import com.xhcai.modules.rag.vo.VectorDatabaseVO;

import java.util.List;
import java.util.Map;

/**
 * 向量数据库服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IVectorDatabaseService extends IService<VectorDatabase> {

    /**
     * 分页查询向量数据库列表
     *
     * @param queryDTO 查询条件
     * @return 向量数据库分页列表
     */
    PageResult<VectorDatabaseVO> selectVectorDatabasePage(VectorDatabaseQueryDTO queryDTO);

    /**
     * 查询向量数据库列表
     *
     * @param queryDTO 查询条件
     * @return 向量数据库列表
     */
    List<VectorDatabaseVO> selectVectorDatabaseList(VectorDatabaseQueryDTO queryDTO);

    /**
     * 根据ID查询向量数据库详情
     *
     * @param id 向量数据库ID
     * @return 向量数据库详情
     */
    VectorDatabaseVO selectVectorDatabaseById(String id);

    /**
     * 创建向量数据库
     *
     * @param createDTO 创建DTO
     * @return 是否成功
     */
    boolean insertVectorDatabase(VectorDatabaseCreateDTO createDTO);

    /**
     * 更新向量数据库
     *
     * @param id        向量数据库ID
     * @param updateDTO 更新DTO
     * @return 是否成功
     */
    boolean updateVectorDatabase(String id, VectorDatabaseUpdateDTO updateDTO);

    /**
     * 删除向量数据库
     *
     * @param ids 向量数据库ID列表
     * @return 是否成功
     */
    boolean deleteVectorDatabases(List<String> ids);

    /**
     * 批量更新状态
     *
     * @param ids    向量数据库ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> ids, String status);

    /**
     * 设置默认向量数据库
     *
     * @param id 向量数据库ID
     * @return 是否成功
     */
    boolean setDefaultVectorDatabase(String id);

    /**
     * 查询默认向量数据库
     *
     * @return 默认向量数据库
     */
    VectorDatabaseVO getDefaultVectorDatabase();

    /**
     * 查询启用的向量数据库列表
     *
     * @return 启用的向量数据库列表
     */
    List<VectorDatabaseVO> getEnabledVectorDatabases();

    /**
     * 测试向量数据库连接
     *
     * @param id 向量数据库ID
     * @return 测试结果
     */
    Map<String, Object> testConnection(String id);

    /**
     * 测试向量数据库连接（使用配置信息）
     *
     * @param createDTO 向量数据库配置
     * @return 测试结果
     */
    Map<String, Object> testConnection(VectorDatabaseCreateDTO createDTO);

    /**
     * 获取向量数据库统计信息
     *
     * @param id 向量数据库ID
     * @return 统计信息
     */
    Map<String, Object> getStatistics(String id);

    /**
     * 初始化向量数据库
     *
     * @param id 向量数据库ID
     * @return 是否成功
     */
    boolean initializeVectorDatabase(String id);

    /**
     * 清理向量数据库
     *
     * @param id 向量数据库ID
     * @return 是否成功
     */
    boolean cleanupVectorDatabase(String id);

    /**
     * 备份向量数据库
     *
     * @param id 向量数据库ID
     * @return 备份结果
     */
    Map<String, Object> backupVectorDatabase(String id);

    /**
     * 恢复向量数据库
     *
     * @param id         向量数据库ID
     * @param backupPath 备份路径
     * @return 恢复结果
     */
    Map<String, Object> restoreVectorDatabase(String id, String backupPath);

    /**
     * 检查向量数据库名称是否存在
     *
     * @param name      数据库名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsVectorDatabaseName(String name, String excludeId);

    /**
     * 导出向量数据库配置
     *
     * @param queryDTO 查询条件
     * @return 向量数据库列表
     */
    List<VectorDatabaseVO> exportVectorDatabases(VectorDatabaseQueryDTO queryDTO);

    /**
     * 导入向量数据库配置
     *
     * @param vectorDatabases 向量数据库列表
     * @return 导入结果
     */
    String importVectorDatabases(List<VectorDatabase> vectorDatabases);
}
