package com.xhcai.modules.dify.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 第三方智能体实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "third_platform", indexes = {
    @Index(name = "idx_third_platform_name", columnList = "name"),
    @Index(name = "idx_third_platform_id", columnList = "platform_id"),
    @Index(name = "idx_third_platform_unit_id", columnList = "unit_id"),
    @Index(name = "idx_third_platform_status", columnList = "status"),
    @Index(name = "idx_third_platform_access_scope", columnList = "access_scope"),
    @Index(name = "idx_third_platform_create_time", columnList = "create_time"),
    @Index(name = "idx_third_platform_tenant_id", columnList = "tenant_id"),
    @Index(name = "idx_third_platform_deleted", columnList = "deleted")
})
@Schema(description = "第三方智能体")
@TableName("third_platform")
public class ThirdPlatform extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体平台ID（字典数据ID）
     */
    @Column(name = "platform_id", length = 36)
    @Schema(description = "智能体平台ID", example = "1")
    @TableField("platform_id")
    private String platformId;

    /**
     * 智能体名称
     */
    @Column(name = "name", length = 100, nullable = false)
    @Schema(description = "智能体名称", example = "ChatGPT智能助手")
    @NotBlank(message = "智能体名称不能为空")
    @Size(min = 1, max = 100, message = "智能体名称长度必须在1-100个字符之间")
    @TableField("name")
    private String name;

    /**
     * 智能体描述
     */
    @Column(name = "description", length = 500)
    @Schema(description = "智能体描述", example = "OpenAI GPT-4 智能对话助手")
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 所属单位ID
     */
    @Column(name = "unit_id", length = 36, nullable = false)
    @Schema(description = "所属单位ID", example = "1")
    @NotBlank(message = "所属单位不能为空")
    @TableField("unit_id")
    private String unitId;

    /**
     * 连接地址
     */
    @Column(name = "connection_url", length = 500, nullable = false)
    @Schema(description = "连接地址", example = "https://api.openai.com/v1/chat/completions")
    @NotBlank(message = "连接地址不能为空")
    @Size(min = 1, max = 500, message = "连接地址长度必须在1-500个字符之间")
    @TableField("connection_url")
    private String connectionUrl;

    /**
     * API密钥
     */
    @Column(name = "api_key", length = 200)
    @Schema(description = "API密钥", example = "sk-***")
    @Size(max = 200, message = "API密钥长度不能超过200个字符")
    @TableField("api_key")
    private String apiKey;

    /**
     * 超时时间（秒）
     */
    @Column(name = "timeout", nullable = false)
    @Schema(description = "超时时间（秒）", example = "30")
    @NotNull(message = "超时时间不能为空")
    @Min(value = 1, message = "超时时间不能小于1秒")
    @Max(value = 300, message = "超时时间不能大于300秒")
    @TableField("timeout")
    private Integer timeout;

    /**
     * 访问范围：personal-个人，public-公开，partial_users-部分人，partial_units-部分单位
     */
    @Column(name = "access_scope", length = 20, nullable = false)
    @Schema(description = "访问范围", example = "public", allowableValues = {"personal", "public", "partial_users", "partial_units"})
    @NotBlank(message = "访问范围不能为空")
    @TableField("access_scope")
    private String accessScope;

    /**
     * 授权用户ID列表（JSON格式）
     */
    @Column(name = "authorized_users", columnDefinition = "TEXT")
    @Schema(description = "授权用户ID列表（JSON格式）", example = "[\"1\",\"2\",\"3\"]")
    @TableField("authorized_users")
    private String authorizedUsers;

    /**
     * 授权单位ID列表（JSON格式）
     */
    @Column(name = "authorized_units", columnDefinition = "TEXT")
    @Schema(description = "授权单位ID列表（JSON格式）", example = "[\"1\",\"2\"]")
    @TableField("authorized_units")
    private String authorizedUnits;

    /**
     * 状态：0-禁用，1-启用
     */
    @Column(name = "status", nullable = false)
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    @NotNull(message = "状态不能为空")
    @TableField("status")
    private Integer status;

    /**
     * 智能体图标
     */
    @Column(name = "icon", length = 10)
    @Schema(description = "智能体图标", example = "🤖")
    @Size(max = 10, message = "智能体图标长度不能超过10个字符")
    @TableField("icon")
    private String icon;

    /**
     * 图标背景色
     */
    @Column(name = "icon_bg", length = 20)
    @Schema(description = "图标背景色", example = "#3b82f6")
    @Size(max = 20, message = "图标背景色长度不能超过20个字符")
    @TableField("icon_bg")
    private String iconBg;

    /**
     * 最后连接测试时间
     */
    @Column(name = "last_test_time")
    @Schema(description = "最后连接测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_test_time")
    private LocalDateTime lastTestTime;

    /**
     * 最后连接测试结果：0-失败，1-成功
     */
    @Column(name = "last_test_result")
    @Schema(description = "最后连接测试结果：0-失败，1-成功", example = "1")
    @TableField("last_test_result")
    private Integer lastTestResult;

    /**
     * 最后连接测试错误信息
     */
    @Column(name = "last_test_error", length = 1000)
    @Schema(description = "最后连接测试错误信息")
    @Size(max = 1000, message = "连接测试错误信息长度不能超过1000个字符")
    @TableField("last_test_error")
    private String lastTestError;

    // Getters and Setters
    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getConnectionUrl() {
        return connectionUrl;
    }

    public void setConnectionUrl(String connectionUrl) {
        this.connectionUrl = connectionUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getAccessScope() {
        return accessScope;
    }

    public void setAccessScope(String accessScope) {
        this.accessScope = accessScope;
    }

    public String getAuthorizedUsers() {
        return authorizedUsers;
    }

    public void setAuthorizedUsers(String authorizedUsers) {
        this.authorizedUsers = authorizedUsers;
    }

    public String getAuthorizedUnits() {
        return authorizedUnits;
    }

    public void setAuthorizedUnits(String authorizedUnits) {
        this.authorizedUnits = authorizedUnits;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBg() {
        return iconBg;
    }

    public void setIconBg(String iconBg) {
        this.iconBg = iconBg;
    }

    public LocalDateTime getLastTestTime() {
        return lastTestTime;
    }

    public void setLastTestTime(LocalDateTime lastTestTime) {
        this.lastTestTime = lastTestTime;
    }

    public Integer getLastTestResult() {
        return lastTestResult;
    }

    public void setLastTestResult(Integer lastTestResult) {
        this.lastTestResult = lastTestResult;
    }

    public String getLastTestError() {
        return lastTestError;
    }

    public void setLastTestError(String lastTestError) {
        this.lastTestError = lastTestError;
    }

    @Override
    public String toString() {
        return "ThirdPlatform{"
                + "name='" + name + '\''
                + ", description='" + description + '\''
                + ", unitId='" + unitId + '\''
                + ", connectionUrl='" + connectionUrl + '\''
                + ", timeout=" + timeout
                + ", accessScope='" + accessScope + '\''
                + ", status=" + status
                + ", icon='" + icon + '\''
                + ", iconBg='" + iconBg + '\''
                + ", lastTestTime=" + lastTestTime
                + ", lastTestResult=" + lastTestResult
                + ", " + super.toString()
                + '}';
    }
}
