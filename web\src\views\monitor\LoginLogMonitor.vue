<template>
  <div class="login-log-monitor">
    <!-- 统计卡片 -->
    <div class="stats-grid mb-6">
      <div class="stat-card">
        <div class="stat-icon bg-blue-100 text-blue-600">
          <i class="fas fa-sign-in-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalLogins }}</div>
          <div class="stat-label">总登录次数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon bg-green-100 text-green-600">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.successLogins }}</div>
          <div class="stat-label">成功登录</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon bg-red-100 text-red-600">
          <i class="fas fa-times-circle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.failedLogins }}</div>
          <div class="stat-label">失败登录</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon bg-purple-100 text-purple-600">
          <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.activeUsers }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <div class="search-box">
          <i class="fas fa-search search-icon"></i>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索用户名、IP地址..."
            class="search-input"
            @input="handleSearch"
          />
        </div>
        
        <select v-model="statusFilter" @change="handleFilter" class="filter-select">
          <option value="">全部状态</option>
          <option value="0">登录成功</option>
          <option value="1">登录失败</option>
        </select>
        
        <div class="date-range">
          <input
            v-model="dateRange.start"
            type="date"
            class="date-input"
            @change="handleFilter"
          />
          <span class="mx-2">至</span>
          <input
            v-model="dateRange.end"
            type="date"
            class="date-input"
            @change="handleFilter"
          />
        </div>
        
        <button @click="refreshData" class="btn btn-primary">
          <i class="fas fa-sync-alt mr-2"></i>
          刷新
        </button>
      </div>
    </div>

    <!-- 登录日志表格 -->
    <div class="log-table-container">
      <div class="table-header">
        <h3 class="text-lg font-semibold text-gray-900">登录日志记录</h3>
        <div class="table-actions">
          <button @click="exportLogs" class="btn btn-outline">
            <i class="fas fa-download mr-2"></i>
            导出日志
          </button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <table class="log-table">
          <thead>
            <tr>
              <th>用户名</th>
              <th>登录状态</th>
              <th>登录IP</th>
              <th>登录地点</th>
              <th>浏览器</th>
              <th>操作系统</th>
              <th>登录时间</th>
              <th>登出时间</th>
              <th>会话时长</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="log in paginatedLogs" :key="log.id" class="table-row">
              <td class="font-medium">{{ log.username }}</td>
              <td>
                <span :class="getStatusClass(log.status)" class="status-badge">
                  {{ getStatusText(log.status) }}
                </span>
              </td>
              <td class="font-mono text-sm">{{ log.loginIp }}</td>
              <td>{{ log.loginLocation || '-' }}</td>
              <td>{{ log.browser || '-' }}</td>
              <td>{{ log.os || '-' }}</td>
              <td>{{ formatDateTime(log.loginTime) }}</td>
              <td>{{ log.logoutTime ? formatDateTime(log.logoutTime) : '-' }}</td>
              <td>{{ calculateDuration(log.loginTime, log.logoutTime) }}</td>
              <td>
                <div class="action-buttons">
                  <button @click="viewLogDetail(log)" class="btn-icon" title="查看详情">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <Pagination
        v-model:currentPage="currentPage"
        v-model:pageSize="pageSize"
        :total="filteredLogs.length"
        :show-page-size-selector="true"
        :show-jumper="true"
        @change="handlePageChange"
      />
    </div>

    <!-- 日志详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">登录日志详情</h3>
          <button @click="closeDetailModal" class="modal-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div v-if="selectedLog" class="detail-grid">
            <div class="detail-item">
              <label>用户名</label>
              <span>{{ selectedLog.username }}</span>
            </div>
            <div class="detail-item">
              <label>登录状态</label>
              <span :class="getStatusClass(selectedLog.status)" class="status-badge">
                {{ getStatusText(selectedLog.status) }}
              </span>
            </div>
            <div class="detail-item">
              <label>登录IP</label>
              <span class="font-mono">{{ selectedLog.loginIp }}</span>
            </div>
            <div class="detail-item">
              <label>登录地点</label>
              <span>{{ selectedLog.loginLocation || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>浏览器</label>
              <span>{{ selectedLog.browser || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>操作系统</label>
              <span>{{ selectedLog.os || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>登录时间</label>
              <span>{{ formatDateTime(selectedLog.loginTime) }}</span>
            </div>
            <div class="detail-item">
              <label>登出时间</label>
              <span>{{ selectedLog.logoutTime ? formatDateTime(selectedLog.logoutTime) : '未登出' }}</span>
            </div>
            <div class="detail-item">
              <label>会话ID</label>
              <span class="font-mono text-sm">{{ selectedLog.sessionId }}</span>
            </div>
            <div class="detail-item">
              <label>提示消息</label>
              <span>{{ selectedLog.msg || '-' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { LoginLogAPI, type SysLoginLogVO, type SysLoginLogQueryDTO, type LoginStats } from '@/api/loginLog'
import Pagination from '@/components/common/Pagination.vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = defineProps<Props>()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const dateRange = ref({
  start: '',
  end: ''
})
const currentPage = ref(1)
const pageSize = ref(20)
const showDetailModal = ref(false)
const selectedLog = ref<any>(null)

// 统计数据
const stats = ref<LoginStats>({
  totalLogins: 0,
  successLogins: 0,
  failedLogins: 0,
  activeUsers: 0
})

// 登录日志数据
const loginLogs = ref<SysLoginLogVO[]>([])
const loading = ref(false)

// 计算属性
const filteredLogs = computed(() => {
  let filtered = [...loginLogs.value]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(log =>
      log.username.toLowerCase().includes(query) ||
      log.loginIp.toLowerCase().includes(query) ||
      (log.loginLocation && log.loginLocation.toLowerCase().includes(query))
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(log => log.status === statusFilter.value)
  }

  // 日期范围过滤
  if (dateRange.value.start) {
    filtered = filtered.filter(log => log.loginTime >= dateRange.value.start)
  }
  if (dateRange.value.end) {
    filtered = filtered.filter(log => log.loginTime <= dateRange.value.end + ' 23:59:59')
  }

  return filtered.sort((a, b) => new Date(b.loginTime).getTime() - new Date(a.loginTime).getTime())
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})

// 方法
const loadLoginLogs = async () => {
  if (loading.value) return

  loading.value = true
  try {
    // 构建查询参数
    const queryDTO: SysLoginLogQueryDTO = {
      current: 1,
      size: 50, // 加载更多数据用于前端分页
      orderBy: 'loginTime',
      orderDirection: 'DESC'
    }

    // 添加搜索条件
    if (searchQuery.value) {
      queryDTO.username = searchQuery.value
      queryDTO.loginIp = searchQuery.value
    }

    if (statusFilter.value) {
      queryDTO.status = statusFilter.value
    }

    if (dateRange.value.start) {
      queryDTO.beginTime = dateRange.value.start + ' 00:00:00'
    }

    if (dateRange.value.end) {
      queryDTO.endTime = dateRange.value.end + ' 23:59:59'
    }

    // 调用API
    const [logsResponse, statsResponse] = await Promise.all([
      LoginLogAPI.getLoginLogPage(queryDTO),
      LoginLogAPI.getLoginStats()
    ])

    if (logsResponse.success && logsResponse.data) {
      loginLogs.value = logsResponse.data.records || []
    }

    if (statsResponse.success && statsResponse.data) {
      stats.value = statsResponse.data
    }
  } catch (error) {
    console.error('加载登录日志失败:', error)
    updateStats()
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  stats.value.totalLogins = loginLogs.value.length
  stats.value.successLogins = loginLogs.value.filter(log => log.status === '0').length
  stats.value.failedLogins = loginLogs.value.filter(log => log.status === '1').length
  stats.value.activeUsers = new Set(loginLogs.value.filter(log => log.status === '0').map(log => log.username)).size
}

const refreshData = () => {
  loadLoginLogs()
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const handlePageChange = (_page: number, _size: number) => {
  // 分页变化时的处理逻辑，这里主要是响应式数据已经自动更新
  // 如果需要重新加载数据，可以在这里调用 loadLoginLogs()
}

const getStatusClass = (status: string) => {
  return status === '0' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
}

const getStatusText = (status: string) => {
  return status === '0' ? '成功' : '失败'
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const calculateDuration = (loginTime: string, logoutTime: string | null) => {
  if (!logoutTime) return '在线中'
  
  const login = new Date(loginTime)
  const logout = new Date(logoutTime)
  const duration = logout.getTime() - login.getTime()
  
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const viewLogDetail = async (log: SysLoginLogVO) => {
  try {
    // 获取详细信息
    const response = await LoginLogAPI.getLoginLogById(log.id)
    if (response.success) {
      selectedLog.value = response.data
    } else {
      selectedLog.value = log
    }
    showDetailModal.value = true
  } catch (error) {
    console.error('获取登录日志详情失败:', error)
    selectedLog.value = log
    showDetailModal.value = true
  }
}

const closeDetailModal = () => {
  showDetailModal.value = false
  selectedLog.value = null
}

const exportLogs = () => {
  // 实现导出功能
  const csvContent = generateCSV(filteredLogs.value)
  downloadCSV(csvContent, 'login_logs.csv')
}

const generateCSV = (logs: any[]) => {
  const headers = ['用户名', '状态', '登录IP', '登录地点', '浏览器', '操作系统', '登录时间', '登出时间', '会话ID', '消息']
  const rows = logs.map(log => [
    log.username,
    getStatusText(log.status),
    log.loginIp,
    log.loginLocation || '',
    log.browser || '',
    log.os || '',
    log.loginTime,
    log.logoutTime || '',
    log.sessionId,
    log.msg || ''
  ])
  
  return [headers, ...rows].map(row => row.join(',')).join('\n')
}

const downloadCSV = (content: string, filename: string) => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  refreshData()
})

// 生命周期
onMounted(() => {
  loadLoginLogs()
})
</script>

<style scoped>
.login-log-monitor {
  padding: 0;
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* 搜索和筛选样式 */
.search-filters {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.search-box {
  position: relative;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select,
.date-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.filter-select:focus,
.date-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-outline {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* 表格样式 */
.log-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.table-wrapper {
  overflow-x: auto;
}

.log-table {
  width: 100%;
  border-collapse: collapse;
}

.log-table th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
}

.log-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
}

.table-row:hover {
  background: #f9fafb;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f3f4f6;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: #e5e7eb;
  color: #374151;
}



/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f3f4f6;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-item span {
  font-size: 14px;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .search-filters .flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .date-range {
    flex-direction: column;
    align-items: stretch;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }



  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>
