# 生产环境配置
# NODE_ENV 由 Vite 自动管理，不需要在 .env 文件中设置

# API配置
# 生产环境下，前后端在同一域名下，使用空字符串作为基础路径
# 这样 /api/xxx 会被解析为相对于当前域名的路径
VITE_API_BASE_URL=
VITE_API_TIMEOUT=30000

# 如果前后端部署在不同域名下，请使用完整的后端域名，例如：
# VITE_API_BASE_URL=https://api.yourdomain.com
# 或者如果后端在同一域名的不同端口：
# VITE_API_BASE_URL=http://yourdomain.com:8000

# 应用配置
VITE_APP_TITLE=AI智能体平台
VITE_APP_VERSION=1.0.0

# 认证配置
VITE_TOKEN_STORAGE_KEY=ai_platform_tokens
VITE_LOGIN_REDIRECT_PATH=/login
VITE_DEFAULT_REDIRECT_PATH=/home

# 调试配置
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error
