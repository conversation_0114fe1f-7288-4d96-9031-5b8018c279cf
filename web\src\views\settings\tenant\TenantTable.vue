<template>
  <div class="tenant-table">
    <!-- 租户表格 -->
    <el-table
      :data="tenants"
      v-loading="loading"
      stripe
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      :row-style="{ height: '60px' }"
      empty-text="暂无数据"
    >
      <el-table-column prop="tenantCode" label="租户编码" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex items-center">
            <el-icon class="mr-2 text-blue-500"><Key /></el-icon>
            <span class="font-medium">{{ row.tenantCode }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="tenantName" label="租户名称" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex items-center">
            <el-icon class="mr-2 text-green-500"><OfficeBuilding /></el-icon>
            <span>{{ row.tenantName }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="contactPerson" label="联系人" width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.contactPerson" class="flex items-center">
            <el-icon class="mr-1 text-purple-500"><User /></el-icon>
            <span>{{ row.contactPerson }}</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="contactPhone" label="联系电话" width="130" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.contactPhone" class="flex items-center">
            <el-icon class="mr-1 text-green-500"><Phone /></el-icon>
            <span>{{ row.contactPhone }}</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="contactEmail" label="联系邮箱" min-width="180" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.contactEmail" class="flex items-center">
            <el-icon class="mr-1 text-blue-500"><Message /></el-icon>
            <span>{{ row.contactEmail }}</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="userLimit" label="用户限制" width="100" align="center">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <span class="text-blue-600 font-medium">{{ row.currentUserCount || 0 }}</span>
            <span class="text-gray-400 mx-1">/</span>
            <span class="text-gray-600">{{ row.userLimit || '∞' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="expireTime" label="过期时间" width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.expireTime" class="flex items-center">
            <el-icon class="mr-1" :class="row.isExpired ? 'text-red-500' : 'text-orange-500'">
              <Clock />
            </el-icon>
            <span :class="row.isExpired ? 'text-red-600' : 'text-gray-600'">{{ row.expireTime }}</span>
          </div>
          <span v-else class="text-gray-400">永久</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag
            :type="row.status === '0' ? 'success' : row.status === '1' ? 'danger' : 'warning'"
            size="small"
          >
            {{ row.statusName || '未知' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="280" align="center" fixed="right">
        <template #default="{ row }">
          <div class="flex gap-1 justify-center">
            <el-button
              type="info"
              size="small"
              @click="$emit('view', row)"
              :icon="View"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="$emit('edit', row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-dropdown @command="handleTenantCommand" trigger="click">
              <el-button type="info" size="small" :icon="MoreFilled">
                更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="$emit('init', row)" :icon="Setting">
                    初始化数据
                  </el-dropdown-item>
                  <el-dropdown-item :command="`delete:${row.id}`" :icon="Delete" divided>
                    删除租户
                  </el-dropdown-item>
                  <el-dropdown-item v-if="row.status === '0'" @click="handleDisableTenant(row)" :icon="Close">
                    禁用
                  </el-dropdown-item>
                  <el-dropdown-item v-if="row.status === '1'" @click="handleEnableTenant(row)" :icon="Check">
                    启用
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-center mt-4">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Key,
  OfficeBuilding,
  User,
  Phone,
  Message,
  Clock,
  View,
  Edit,
  Check,
  Close,
  MoreFilled,
  Setting,
  Delete
} from '@element-plus/icons-vue'
import type { SysTenantVO } from '@/types/system'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTenantData } from './composables/useTenantData'

// 使用租户数据管理 composable
const {
  tenants,
  loading,
  pagination,
  handleSizeChange,
  handleCurrentChange,
  deleteTenant,
  enableTenant,
  disableTenant
} = useTenantData()

// Emits - 只保留必要的事件
defineEmits<{
  'view': [tenant: SysTenantVO]
  'edit': [tenant: SysTenantVO]
  'init': [tenant: SysTenantVO]
}>()

const handleTenantCommand = (command: string) => {
  const [action, tenantId] = command.split(':')
  const tenant = tenants.value.find(t => t.id === tenantId)

  if (!tenant) return

  switch (action) {
    case 'delete':
      handleDeleteTenant(tenant)
      break
  }
}

const handleDeleteTenant = async (tenant: SysTenantVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除租户 "${tenant.tenantName}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        type: 'error',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )
    await deleteTenant(tenant.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除租户操作取消或失败:', error)
    }
  }
}

const handleEnableTenant = async (tenant: SysTenantVO) => {
  await enableTenant(tenant.id)
}

const handleDisableTenant = async (tenant: SysTenantVO) => {
  try {
    await ElMessageBox.confirm('确定要禁用该租户吗？', '确认禁用', {
      type: 'warning'
    })
    await disableTenant(tenant.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('禁用租户操作取消或失败:', error)
    }
  }
}
</script>

<style scoped>
.tenant-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

:deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

:deep(.el-table__cell) {
  border-bottom: 1px solid #f1f5f9;
}

/* 操作按钮样式 */
:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-table__column--fixed-right) {
    position: static !important;
  }
  
  .tenant-table {
    overflow-x: auto;
  }
}
</style>
