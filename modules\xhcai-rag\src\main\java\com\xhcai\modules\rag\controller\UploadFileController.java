package com.xhcai.modules.rag.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.entity.UploadFile;
import com.xhcai.modules.rag.service.IUploadFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 文件上传记录控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/upload-files")
@Tag(name = "文件上传记录管理", description = "文件上传记录相关接口")
public class UploadFileController {

    @Autowired
    private IUploadFileService uploadFileService;

    @Operation(summary = "分页查询上传文件记录", description = "分页查询上传文件记录")
    @GetMapping("/page")
    @RequiresPermissions("rag:upload-file:list")
    public PageResult<UploadFile> page(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "数据集ID") @RequestParam(required = false) String datasetId,
            @Parameter(description = "上传状态") @RequestParam(required = false) String uploadStatus,
            @Parameter(description = "操作类型") @RequestParam(required = false) String operationType) {
        
        Page<UploadFile> page = new Page<>(current, size);
        IPage<UploadFile> result = uploadFileService.getPageByCondition(page, datasetId, uploadStatus, operationType);
        
        return PageResult.of(result.getRecords(), result.getTotal(), page.getCurrent(), page.getSize());
    }

    @Operation(summary = "根据数据集ID查询上传文件记录", description = "根据数据集ID查询上传文件记录")
    @GetMapping("/dataset/{datasetId}")
    @RequiresPermissions("rag:upload-file:list")
    public Result<List<UploadFile>> getByDatasetId(
            @Parameter(description = "数据集ID") @PathVariable String datasetId) {
        
        List<UploadFile> uploadFiles = uploadFileService.getByDatasetId(datasetId);
        return Result.success(uploadFiles);
    }

    @Operation(summary = "根据批次ID查询上传文件记录", description = "根据批次ID查询上传文件记录")
    @GetMapping("/batch/{batchId}")
    @RequiresPermissions("rag:upload-file:list")
    public Result<List<UploadFile>> getByBatchId(
            @Parameter(description = "批次ID") @PathVariable String batchId) {
        
        List<UploadFile> uploadFiles = uploadFileService.getByBatchId(batchId);
        return Result.success(uploadFiles);
    }

    @Operation(summary = "根据文档ID查询上传文件记录", description = "根据文档ID查询上传文件记录")
    @GetMapping("/document/{documentId}")
    @RequiresPermissions("rag:upload-file:list")
    public Result<UploadFile> getByDocumentId(
            @Parameter(description = "文档ID") @PathVariable String documentId) {
        
        UploadFile uploadFile = uploadFileService.getByDocumentId(documentId);
        return Result.success(uploadFile);
    }

    @Operation(summary = "记录文件删除操作", description = "记录文件删除操作到上传文件表")
    @PostMapping("/record-delete")
    @RequiresPermissions("rag:upload-file:create")
    public Result<Void> recordFileDeleteOperation(@RequestBody Map<String, Object> deleteData) {
        log.info("记录文件删除操作: {}", deleteData);

        try {
            // 创建删除记录
            UploadFile uploadFile = new UploadFile();
            uploadFile.setOriginalFilename((String) deleteData.get("originalFilename"));
            uploadFile.setFileSize(((Number) deleteData.get("fileSize")).longValue());
            uploadFile.setFileExtension((String) deleteData.get("fileExtension"));
            uploadFile.setMimeType((String) deleteData.get("mimeType"));
            uploadFile.setFileHash((String) deleteData.get("fileHash"));
            uploadFile.setDatasetId((String) deleteData.get("datasetId"));
            uploadFile.setBatchId((String) deleteData.get("batchId"));
            uploadFile.setDocumentId((String) deleteData.get("documentId"));
            uploadFile.setMinioUrl((String) deleteData.get("minioUrl"));
            uploadFile.setUploadStatus("deleted");
            uploadFile.setOperationType("delete");
            uploadFile.setErrorMessage((String) deleteData.get("deleteReason"));

            boolean success = uploadFileService.createUploadFile(uploadFile);
            if (success) {
                return Result.success();
            } else {
                return Result.fail("记录删除操作失败");
            }
        } catch (Exception e) {
            log.error("记录文件删除操作失败", e);
            return Result.fail("记录删除操作失败: " + e.getMessage());
        }
    }

    @Operation(summary = "统计数据集的上传文件数量", description = "统计数据集的上传文件数量")
    @GetMapping("/count/dataset/{datasetId}")
    @RequiresPermissions("rag:upload-file:list")
    public Result<Long> countByDatasetId(
            @Parameter(description = "数据集ID") @PathVariable String datasetId) {
        
        Long count = uploadFileService.countByDatasetId(datasetId);
        return Result.success(count);
    }

    @Operation(summary = "统计数据集的上传文件数量（按状态）", description = "统计数据集的上传文件数量（按状态）")
    @GetMapping("/count/dataset/{datasetId}/status/{uploadStatus}")
    @RequiresPermissions("rag:upload-file:list")
    public Result<Long> countByDatasetIdAndStatus(
            @Parameter(description = "数据集ID") @PathVariable String datasetId,
            @Parameter(description = "上传状态") @PathVariable String uploadStatus) {
        
        Long count = uploadFileService.countByDatasetIdAndStatus(datasetId, uploadStatus);
        return Result.success(count);
    }

    @Operation(summary = "软删除上传文件记录", description = "软删除上传文件记录")
    @DeleteMapping("/{id}")
    @RequiresPermissions("rag:upload-file:delete")
    public Result<Void> softDelete(@Parameter(description = "记录ID") @PathVariable String id) {
        log.info("软删除上传文件记录: id={}", id);

        boolean success = uploadFileService.softDeleteUploadFile(id);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("删除失败");
        }
    }

    @Operation(summary = "批量软删除上传文件记录", description = "批量软删除上传文件记录")
    @DeleteMapping("/batch")
    @RequiresPermissions("rag:upload-file:delete")
    public Result<Void> batchSoftDelete(@RequestBody List<String> ids) {
        log.info("批量软删除上传文件记录: ids={}", ids);

        boolean success = uploadFileService.batchSoftDeleteUploadFiles(ids);
        if (success) {
            return Result.success();
        } else {
            return Result.fail("批量删除失败");
        }
    }
}
