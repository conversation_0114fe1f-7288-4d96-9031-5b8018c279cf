package com.xhcai.modules.agent.service;

import com.xhcai.modules.agent.dto.ChatRequestDTO;
import com.xhcai.modules.agent.vo.ChatResponseVO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 聊天服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IChatService {

    /**
     * 发送消息（阻塞式）
     *
     * @param requestDTO 聊天请求
     * @return 聊天响应
     */
    ChatResponseVO sendMessage(ChatRequestDTO requestDTO);

    /**
     * 发送消息（流式）
     *
     * @param requestDTO 聊天请求
     * @return SSE发射器
     */
    SseEmitter sendMessageStream(ChatRequestDTO requestDTO);

    /**
     * 结束对话
     *
     * @param conversationId 对话ID
     * @return 是否成功
     */
    boolean endConversation(String conversationId);

    /**
     * 清理过期对话
     *
     * @return 清理数量
     */
    int cleanExpiredConversations();

    /**
     * 重新生成回复
     *
     * @param messageId 消息ID
     * @return 聊天响应
     */
    ChatResponseVO regenerateResponse(String messageId);

    /**
     * 获取对话历史
     *
     * @param conversationId 对话ID
     * @param limit 限制数量
     * @return 消息列表
     */
    java.util.List<ChatResponseVO> getConversationHistory(String conversationId, Integer limit);

    /**
     * 提交用户反馈
     *
     * @param conversationId 对话ID
     * @param rating 评分
     * @param feedback 反馈内容
     * @return 是否成功
     */
    boolean submitFeedback(String conversationId, Integer rating, String feedback);

    /**
     * 获取建议问题
     *
     * @param agentId 智能体ID
     * @param conversationId 对话ID（可选）
     * @return 建议问题列表
     */
    java.util.List<String> getSuggestedQuestions(String agentId, String conversationId);

    /**
     * 验证聊天请求
     *
     * @param requestDTO 聊天请求
     * @return 验证结果
     */
    boolean validateChatRequest(ChatRequestDTO requestDTO);

    /**
     * 检查智能体是否可用
     *
     * @param agentId 智能体ID
     * @return 是否可用
     */
    boolean isAgentAvailable(String agentId);

    /**
     * 获取对话统计信息
     *
     * @param conversationId 对话ID
     * @return 统计信息
     */
    ConversationStatsVO getConversationStats(String conversationId);

    /**
     * 对话统计信息VO
     */
    class ConversationStatsVO {
        private Integer messageCount;
        private Integer userMessageCount;
        private Integer assistantMessageCount;
        private Long totalTokens;
        private Long inputTokens;
        private Long outputTokens;
        private Long totalCost;
        private java.time.LocalDateTime startedAt;
        private java.time.LocalDateTime lastActivityAt;
        private String status;

        // Getters and Setters
        public Integer getMessageCount() {
            return messageCount;
        }

        public void setMessageCount(Integer messageCount) {
            this.messageCount = messageCount;
        }

        public Integer getUserMessageCount() {
            return userMessageCount;
        }

        public void setUserMessageCount(Integer userMessageCount) {
            this.userMessageCount = userMessageCount;
        }

        public Integer getAssistantMessageCount() {
            return assistantMessageCount;
        }

        public void setAssistantMessageCount(Integer assistantMessageCount) {
            this.assistantMessageCount = assistantMessageCount;
        }

        public Long getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(Long totalTokens) {
            this.totalTokens = totalTokens;
        }

        public Long getInputTokens() {
            return inputTokens;
        }

        public void setInputTokens(Long inputTokens) {
            this.inputTokens = inputTokens;
        }

        public Long getOutputTokens() {
            return outputTokens;
        }

        public void setOutputTokens(Long outputTokens) {
            this.outputTokens = outputTokens;
        }

        public Long getTotalCost() {
            return totalCost;
        }

        public void setTotalCost(Long totalCost) {
            this.totalCost = totalCost;
        }

        public java.time.LocalDateTime getStartedAt() {
            return startedAt;
        }

        public void setStartedAt(java.time.LocalDateTime startedAt) {
            this.startedAt = startedAt;
        }

        public java.time.LocalDateTime getLastActivityAt() {
            return lastActivityAt;
        }

        public void setLastActivityAt(java.time.LocalDateTime lastActivityAt) {
            this.lastActivityAt = lastActivityAt;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }
}
