/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AssociateThirdPlatform: typeof import('./src/components/agent/AssociateThirdPlatform.vue')['default']
    DeptTreeSelector: typeof import('./src/components/common/selectors/DeptTreeSelector.vue')['default']
    DeptTreeSelectorContent: typeof import('./src/components/common/selectors/DeptTreeSelectorContent.vue')['default']
    DocumentImportDialog: typeof import('./src/components/DocumentImportDialog.vue')['default']
    DropdownSelector: typeof import('./src/components/common/selectors/DropdownSelector.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDeptTreeSelector: typeof import('./src/components/common/el-selectors/ElDeptTreeSelector.vue')['default']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElPermissionByRoleSelector: typeof import('./src/components/common/el-selectors/ElPermissionByRoleSelector.vue')['default']
    ElRoleSelector: typeof import('./src/components/common/el-selectors/ElRoleSelector.vue')['default']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStatusSelector: typeof import('./src/components/common/el-selectors/ElStatusSelector.vue')['default']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElUserByDeptSelector: typeof import('./src/components/common/el-selectors/ElUserByDeptSelector.vue')['default']
    ElUserByRoleSelector: typeof import('./src/components/common/el-selectors/ElUserByRoleSelector.vue')['default']
    FileUploadMenu: typeof import('./src/components/FileUploadMenu.vue')['default']
    HelpModal: typeof import('./src/components/HelpModal.vue')['default']
    HoverTooltip: typeof import('./src/components/common/HoverTooltip.vue')['default']
    IconSelector: typeof import('./src/components/common/IconSelector.vue')['default']
    Pagination: typeof import('./src/components/common/Pagination.vue')['default']
    ParameterConfig: typeof import('./src/components/ParameterConfig.vue')['default']
    PermissionByRoleSelector: typeof import('./src/components/common/selectors/PermissionByRoleSelector.vue')['default']
    PermissionByRoleSelectorContent: typeof import('./src/components/common/selectors/PermissionByRoleSelectorContent.vue')['default']
    RendererSelector: typeof import('./src/components/RendererSelector.vue')['default']
    RichTextEditor: typeof import('./src/components/RichTextEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RunnerTaskbar: typeof import('./src/components/common/runner-window/RunnerTaskbar.vue')['default']
    RunnerWindow: typeof import('./src/components/common/runner-window/RunnerWindow.vue')['default']
    StatusSelector: typeof import('./src/components/common/selectors/StatusSelector.vue')['default']
    StatusSelectorContent: typeof import('./src/components/common/selectors/StatusSelectorContent.vue')['default']
    SuggestedQuestions: typeof import('./src/components/SuggestedQuestions.vue')['default']
    UserByDeptSelector: typeof import('./src/components/common/selectors/UserByDeptSelector.vue')['default']
    UserByDeptSelectorContent: typeof import('./src/components/common/selectors/UserByDeptSelectorContent.vue')['default']
    UserByRoleSelector: typeof import('./src/components/common/selectors/UserByRoleSelector.vue')['default']
    UserByRoleSelectorContent: typeof import('./src/components/common/selectors/UserByRoleSelectorContent.vue')['default']
  }
}
