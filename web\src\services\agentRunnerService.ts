import { useAgentRunner, type Agent } from '@/composables/useAgentRunner'

// 创建全局实例
const globalAgentRunner = useAgentRunner({
  onAgentOpened: (agent: Agent) => {
    console.log('全局事件 - 智能体已打开:', agent.name)
  },
  onAgentClosed: (agentId: string) => {
    console.log('全局事件 - 智能体已关闭:', agentId)
  },
  onAgentMinimized: (agentId: string) => {
    console.log('全局事件 - 智能体已最小化:', agentId)
  },
  onAgentRestored: (agentId: string) => {
    console.log('全局事件 - 智能体已恢复:', agentId)
  }
})

// 导出全局服务
export const agentRunnerService = {
  // 打开智能体
  openAgent: (agent: Agent) => {
    globalAgentRunner.openRunnerModal(agent)
  },

  // 关闭智能体
  closeAgent: (agentId?: string) => {
    if (agentId) {
      globalAgentRunner.closeSpecificAgent(agentId)
    } else {
      globalAgentRunner.closeRunnerModal()
    }
  },

  // 最小化当前智能体
  minimizeCurrentAgent: () => {
    globalAgentRunner.minimizeRunner()
  },

  // 恢复智能体
  restoreAgent: (agentId: string) => {
    globalAgentRunner.restoreRunner(agentId)
  },

  // 获取运行状态
  getRunningState: () => ({
    runningAgents: globalAgentRunner.runningAgents,
    activeAgentId: globalAgentRunner.activeAgentId.value,
    currentAgent: globalAgentRunner.currentRunningAgent.value,
    minimizedAgents: globalAgentRunner.minimizedAgents.value,
    hasMinimizedAgents: globalAgentRunner.hasMinimizedAgents.value,
    isVisible: globalAgentRunner.runnerModalVisible.value,
    isMinimized: globalAgentRunner.isMinimized.value,
    isMaximized: globalAgentRunner.isMaximized.value
  }),

  // 检查智能体是否正在运行
  isAgentRunning: (agentId: string) => {
    return globalAgentRunner.isAgentRunning(agentId)
  },

  // 获取所有运行中的智能体ID
  getRunningAgentIds: () => {
    return globalAgentRunner.getRunningAgentIds()
  },

  // 获取指定智能体的运行状态
  getAgentRunningState: (agentId: string) => {
    return globalAgentRunner.getAgentRunningState(agentId)
  }
}

// 默认导出
export default agentRunnerService

// 类型导出
export type { Agent } from '@/composables/useAgentRunner'
