package com.xhcai.modules.dify.service.impl;

import com.alibaba.fastjson2.JSON;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.constant.DifyConstants;
import com.xhcai.modules.dify.dto.app.DifyAppParametersResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.dto.app.DifySuggestedQuestionsResponseDTO;
import com.xhcai.modules.dify.service.IDifyAppService;
import com.xhcai.modules.dify.service.IDifyAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;

/**
 * Dify 应用服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class DifyAppServiceImpl implements IDifyAppService {

    private static final Logger log = LoggerFactory.getLogger(DifyAppServiceImpl.class);

    @Autowired
    @Qualifier("difyWebClient")
    private WebClient difyWebClient;

    @Autowired
    private DifyConfig difyConfig;

    @Autowired
    private IDifyAuthService difyAuthService;

    @Override
    public Mono<Result<DifyInstalledAppsResponseDTO>> getInstalledApps() {
        log.info("获取已安装应用列表");

        // 如果启用测试模式，返回Mock响应
        if (difyConfig.isTestMode()) {
            log.info("测试模式已启用，返回Mock已安装应用列表");
            DifyInstalledAppsResponseDTO mockResponse = createMockInstalledApps();
            return Mono.just(Result.success(mockResponse));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用已安装应用列表接口");
                    return difyWebClient.get()
                            .uri(DifyConstants.ApiPath.INSTALLED_APPS)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                            .retrieve()
                            .bodyToMono(String.class)
                            .map(response -> {
                                log.debug("已安装应用列表响应: {}", response);
                                DifyInstalledAppsResponseDTO responseDTO = JSON.parseObject(response, DifyInstalledAppsResponseDTO.class);
                                return Result.success(responseDTO);
                            });
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyAppParametersResponseDTO>> getAppParameters(String installedAppId) {
        log.info("获取应用会话参数: installedAppId={}", installedAppId);

        if (!StringUtils.hasText(installedAppId)) {
            return Mono.just(Result.fail("已安装应用ID不能为空"));
        }

        // 如果启用测试模式，返回Mock响应
        if (difyConfig.isTestMode()) {
            log.info("测试模式已启用，返回Mock应用会话参数");
            DifyAppParametersResponseDTO mockResponse = createMockAppParameters();
            return Mono.just(Result.success(mockResponse));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用应用会话参数接口: installedAppId={}", installedAppId);
                    String uri = DifyConstants.ApiPath.APP_PARAMETERS.replace("{installedAppId}", installedAppId);
                    return difyWebClient.get()
                            .uri(uri)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                            .retrieve()
                            .bodyToMono(String.class)
                            .map(response -> {
                                log.debug("应用会话参数响应: {}", response);
                                DifyAppParametersResponseDTO responseDTO = JSON.parseObject(response, DifyAppParametersResponseDTO.class);
                                return Result.success(responseDTO);
                            });
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifySuggestedQuestionsResponseDTO>> getSuggestedQuestions(String installedAppId, String messageId) {
        log.info("获取消息建议问题: installedAppId={}, messageId={}", installedAppId, messageId);

        if (!StringUtils.hasText(installedAppId)) {
            return Mono.just(Result.fail("已安装应用ID不能为空"));
        }

        if (!StringUtils.hasText(messageId)) {
            return Mono.just(Result.fail("消息ID不能为空"));
        }

        // 如果启用测试模式，返回Mock响应
        if (difyConfig.isTestMode()) {
            log.info("测试模式已启用，返回Mock建议问题");
            DifySuggestedQuestionsResponseDTO mockResponse = createMockSuggestedQuestions();
            return Mono.just(Result.success(mockResponse));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用建议问题接口: installedAppId={}, messageId={}", installedAppId, messageId);
                    String uri = DifyConstants.ApiPath.SUGGESTED_QUESTIONS
                            .replace("{installedAppId}", installedAppId)
                            .replace("{messageId}", messageId);
                    return difyWebClient.get()
                            .uri(uri)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                            .retrieve()
                            .bodyToMono(String.class)
                            .map(response -> {
                                log.debug("建议问题响应: {}", response);
                                DifySuggestedQuestionsResponseDTO responseDTO = JSON.parseObject(response, DifySuggestedQuestionsResponseDTO.class);
                                return Result.success(responseDTO);
                            });
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifySuggestedQuestionsResponseDTO>> getSuggestedQuestionsByAppId(String appId, String messageId) {
        log.info("通过应用ID获取消息建议问题: appId={}, messageId={}", appId, messageId);

        if (!StringUtils.hasText(appId)) {
            return Mono.just(Result.<DifySuggestedQuestionsResponseDTO>fail("应用ID不能为空"));
        }

        if (!StringUtils.hasText(messageId)) {
            return Mono.just(Result.<DifySuggestedQuestionsResponseDTO>fail("消息ID不能为空"));
        }

        // 如果启用测试模式，返回Mock响应
        if (difyConfig.isTestMode()) {
            log.info("测试模式已启用，返回Mock建议问题");
            DifySuggestedQuestionsResponseDTO mockResponse = createMockSuggestedQuestions();
            return Mono.just(Result.success(mockResponse));
        }

        // 第一步：获取已安装应用列表，找到对应的已安装应用ID
        return getInstalledApps()
                .timeout(Duration.ofSeconds(20)) // 为获取已安装应用列表设置20秒超时
                .flatMap(installedAppsResult -> {
                    if (!installedAppsResult.isSuccess() || installedAppsResult.getData() == null) {
                        log.error("获取已安装应用列表失败");
                        return Mono.just(Result.<DifySuggestedQuestionsResponseDTO>fail("获取已安装应用列表失败"));
                    }

                    // 查找对应的已安装应用ID
                    String installedAppId = installedAppsResult.getData().findInstalledAppIdByAppId(appId);
                    if (installedAppId == null) {
                        log.error("未找到应用ID {} 对应的已安装应用", appId);
                        return Mono.just(Result.<DifySuggestedQuestionsResponseDTO>fail("未找到对应的已安装应用"));
                    }

                    log.info("找到已安装应用ID: {}", installedAppId);

                    // 第二步：使用已安装应用ID获取建议问题
                    return getSuggestedQuestions(installedAppId, messageId)
                            .timeout(Duration.ofSeconds(30)); // 为获取建议问题设置25秒超时
                })
                .timeout(Duration.ofSeconds(30)) // 整个流程设置30秒总超时
                .onErrorResume(throwable -> {
                    if (throwable instanceof java.util.concurrent.TimeoutException) {
                        log.error("获取建议问题超时: appId={}, messageId={}", appId, messageId);
                        return Mono.just(Result.<DifySuggestedQuestionsResponseDTO>fail("获取建议问题超时，请稍后重试"));
                    }
                    log.error("获取建议问题失败: appId={}, messageId={}, error={}", appId, messageId, throwable.getMessage(), throwable);
                    return Mono.just(Result.<DifySuggestedQuestionsResponseDTO>fail("获取建议问题失败: " + throwable.getMessage()));
                });
    }

    /**
     * 创建Mock已安装应用列表
     */
    private DifyInstalledAppsResponseDTO createMockInstalledApps() {
        DifyInstalledAppsResponseDTO response = new DifyInstalledAppsResponseDTO();

        // 创建Mock已安装应用列表
        java.util.List<DifyInstalledAppsResponseDTO.InstalledApp> installedApps = new java.util.ArrayList<>();

        // 创建测试应用1
        DifyInstalledAppsResponseDTO.InstalledApp app1 = new DifyInstalledAppsResponseDTO.InstalledApp();
        app1.setId("installed-app-1");

        DifyInstalledAppsResponseDTO.App appInfo1 = new DifyInstalledAppsResponseDTO.App();
        appInfo1.setId("3778735c-cc1a-41e3-988f-7a108e3eafb0"); // 通用助手
        appInfo1.setName("通用助手");
        app1.setApp(appInfo1);

        installedApps.add(app1);

        // 创建测试应用2
        DifyInstalledAppsResponseDTO.InstalledApp app2 = new DifyInstalledAppsResponseDTO.InstalledApp();
        app2.setId("installed-app-2");

        DifyInstalledAppsResponseDTO.App appInfo2 = new DifyInstalledAppsResponseDTO.App();
        appInfo2.setId("86109d3f-ef91-49c5-9ab4-c2fbc646f44b"); // 编程助手
        appInfo2.setName("编程助手");
        app2.setApp(appInfo2);

        installedApps.add(app2);

        response.setInstalledApps(installedApps);
        return response;
    }

    /**
     * 创建Mock应用会话参数
     */
    private DifyAppParametersResponseDTO createMockAppParameters() {
        DifyAppParametersResponseDTO response = new DifyAppParametersResponseDTO();
        response.setOpeningStatement("");
        
        // 设置回答后建议问题配置
        DifyAppParametersResponseDTO.SuggestedQuestionsAfterAnswer suggestedQuestionsAfterAnswer = 
            new DifyAppParametersResponseDTO.SuggestedQuestionsAfterAnswer();
        suggestedQuestionsAfterAnswer.setEnabled(false);
        response.setSuggestedQuestionsAfterAnswer(suggestedQuestionsAfterAnswer);

        // 设置语音转文字配置
        DifyAppParametersResponseDTO.SpeechToText speechToText = 
            new DifyAppParametersResponseDTO.SpeechToText();
        speechToText.setEnabled(false);
        response.setSpeechToText(speechToText);

        // 设置文字转语音配置
        DifyAppParametersResponseDTO.TextToSpeech textToSpeech = 
            new DifyAppParametersResponseDTO.TextToSpeech();
        textToSpeech.setEnabled(false);
        textToSpeech.setVoice("");
        textToSpeech.setLanguage("");
        response.setTextToSpeech(textToSpeech);

        // 设置检索资源配置
        DifyAppParametersResponseDTO.RetrieverResource retrieverResource = 
            new DifyAppParametersResponseDTO.RetrieverResource();
        retrieverResource.setEnabled(true);
        response.setRetrieverResource(retrieverResource);

        // 设置注释回复配置
        DifyAppParametersResponseDTO.AnnotationReply annotationReply = 
            new DifyAppParametersResponseDTO.AnnotationReply();
        annotationReply.setEnabled(false);
        response.setAnnotationReply(annotationReply);

        // 设置更多类似配置
        DifyAppParametersResponseDTO.MoreLikeThis moreLikeThis = 
            new DifyAppParametersResponseDTO.MoreLikeThis();
        moreLikeThis.setEnabled(false);
        response.setMoreLikeThis(moreLikeThis);

        // 设置敏感词规避配置
        DifyAppParametersResponseDTO.SensitiveWordAvoidance sensitiveWordAvoidance =
            new DifyAppParametersResponseDTO.SensitiveWordAvoidance();
        sensitiveWordAvoidance.setEnabled(false);
        response.setSensitiveWordAvoidance(sensitiveWordAvoidance);

        // 设置文件上传配置
        DifyAppParametersResponseDTO.FileUpload fileUpload =
            new DifyAppParametersResponseDTO.FileUpload();
        fileUpload.setEnabled(true);
        fileUpload.setAllowedFileTypes(java.util.Arrays.asList("image", "document"));
        fileUpload.setAllowedFileExtensions(java.util.Arrays.asList(".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG"));
        fileUpload.setAllowedFileUploadMethods(java.util.Arrays.asList("remote_url", "local_file"));
        fileUpload.setNumberLimits(3);

        // 设置文件上传配置详情
        DifyAppParametersResponseDTO.FileUploadConfig fileUploadConfig =
            new DifyAppParametersResponseDTO.FileUploadConfig();
        fileUploadConfig.setFileSizeLimit(15);
        fileUploadConfig.setBatchCountLimit(5);
        fileUploadConfig.setImageFileSizeLimit(10);
        fileUploadConfig.setVideoFileSizeLimit(100);
        fileUploadConfig.setAudioFileSizeLimit(50);
        fileUploadConfig.setWorkflowFileUploadLimit(10);
        fileUpload.setFileUploadConfig(fileUploadConfig);
        response.setFileUpload(fileUpload);

        // 设置系统参数
        DifyAppParametersResponseDTO.SystemParameters systemParameters =
            new DifyAppParametersResponseDTO.SystemParameters();
        systemParameters.setImageFileSizeLimit(10);
        systemParameters.setVideoFileSizeLimit(100);
        systemParameters.setAudioFileSizeLimit(50);
        systemParameters.setFileSizeLimit(15);
        systemParameters.setWorkflowFileUploadLimit(10);
        response.setSystemParameters(systemParameters);

        // 设置用户输入表单（Mock数据）
        java.util.List<java.util.Map<String, Object>> userInputForm = new java.util.ArrayList<>();

        // 添加下拉选择框参数
        java.util.Map<String, Object> selectParam = new java.util.HashMap<>();
        java.util.Map<String, Object> selectConfig = new java.util.HashMap<>();
        selectConfig.put("variable", "model");
        selectConfig.put("label", "模型");
        selectConfig.put("type", "select");
        selectConfig.put("max_length", 48);
        selectConfig.put("required", true);
        selectConfig.put("options", java.util.Arrays.asList("deepseek-r1", "qwen3", "gpt-4"));
        selectParam.put("select", selectConfig);
        userInputForm.add(selectParam);

        // 添加文本输入框参数（使用包装结构）
        java.util.Map<String, Object> textParam = new java.util.HashMap<>();
        java.util.Map<String, Object> textInputConfig = new java.util.HashMap<>();
        textInputConfig.put("variable", "key");
        textInputConfig.put("label", "关键字");
        textInputConfig.put("type", "text-input");
        textInputConfig.put("max_length", 48);
        textInputConfig.put("required", true);
        textInputConfig.put("options", java.util.Arrays.asList());
        textParam.put("text-input", textInputConfig);
        userInputForm.add(textParam);

        // 添加段落文本框参数
        java.util.Map<String, Object> paragraphParam = new java.util.HashMap<>();
        java.util.Map<String, Object> paragraphConfig = new java.util.HashMap<>();
        paragraphConfig.put("variable", "text");
        paragraphConfig.put("label", "内容");
        paragraphConfig.put("type", "paragraph");
        paragraphConfig.put("max_length", 500);
        paragraphConfig.put("required", true);
        paragraphConfig.put("options", java.util.Arrays.asList());
        paragraphParam.put("paragraph", paragraphConfig);
        userInputForm.add(paragraphParam);

        // 添加数字输入框参数
        java.util.Map<String, Object> numberParam = new java.util.HashMap<>();
        java.util.Map<String, Object> numberConfig = new java.util.HashMap<>();
        numberConfig.put("variable", "num");
        numberConfig.put("label", "数量");
        numberConfig.put("type", "number");
        numberConfig.put("max_length", 100);
        numberConfig.put("required", true);
        numberConfig.put("options", java.util.Arrays.asList());
        numberParam.put("number", numberConfig);
        userInputForm.add(numberParam);

        // 添加单个文件上传参数
        java.util.Map<String, Object> fileParam = new java.util.HashMap<>();
        java.util.Map<String, Object> fileConfig = new java.util.HashMap<>();
        fileConfig.put("variable", "file");
        fileConfig.put("label", "单个文件");
        fileConfig.put("type", "file");
        fileConfig.put("max_length", 1);
        fileConfig.put("required", false);
        fileConfig.put("options", java.util.Arrays.asList());
        fileConfig.put("allowed_file_upload_methods", java.util.Arrays.asList("local_file", "remote_url"));
        fileConfig.put("allowed_file_types", java.util.Arrays.asList("document", "image"));
        fileConfig.put("allowed_file_extensions", java.util.Arrays.asList(".pdf", ".doc", ".docx", ".jpg", ".png"));
        fileParam.put("file", fileConfig);
        userInputForm.add(fileParam);

        // 添加批量文件上传参数
        java.util.Map<String, Object> fileListParam = new java.util.HashMap<>();
        java.util.Map<String, Object> fileListConfig = new java.util.HashMap<>();
        fileListConfig.put("variable", "files");
        fileListConfig.put("label", "批量文件");
        fileListConfig.put("type", "file-list");
        fileListConfig.put("max_length", 5);
        fileListConfig.put("required", false);
        fileListConfig.put("options", java.util.Arrays.asList());
        fileListConfig.put("allowed_file_upload_methods", java.util.Arrays.asList("local_file", "remote_url"));
        fileListConfig.put("allowed_file_types", java.util.Arrays.asList("document"));
        fileListConfig.put("allowed_file_extensions", java.util.Arrays.asList(".pdf", ".doc", ".docx", ".txt"));
        fileListParam.put("file-list", fileListConfig);
        userInputForm.add(fileListParam);

        response.setUserInputForm(userInputForm);

        return response;
    }

    /**
     * 创建Mock建议问题
     */
    private DifySuggestedQuestionsResponseDTO createMockSuggestedQuestions() {
        DifySuggestedQuestionsResponseDTO response = new DifySuggestedQuestionsResponseDTO();
        java.util.List<String> mockQuestions = java.util.Arrays.asList(
                "什么是正常体温",
                "多久见效",
                "老人低烧怎么办"
        );
        response.setData(mockQuestions);
        return response;
    }

    /**
     * 处理错误
     */
    private <T> Mono<Result<T>> handleError(Throwable throwable) {
        log.error("Dify应用服务调用失败", throwable);
        
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            HttpStatus status = (HttpStatus) ex.getStatusCode();
            String responseBody = ex.getResponseBodyAsString();
            
            log.error("HTTP错误: status={}, body={}", status, responseBody);
            
            if (status == HttpStatus.UNAUTHORIZED) {
                return Mono.just(Result.fail(status.value(), "认证失败，请检查访问令牌"));
            } else if (status == HttpStatus.NOT_FOUND) {
                return Mono.just(Result.fail(status.value(), "请求的资源不存在"));
            } else if (status == HttpStatus.BAD_REQUEST) {
                return Mono.just(Result.fail(status.value(), "请求参数错误"));
            } else {
                return Mono.just(Result.fail(status.value(), "服务调用失败: " + status.getReasonPhrase()));
            }
        } else if (throwable instanceof BusinessException) {
            return Mono.just(Result.fail(500, throwable.getMessage()));
        } else {
            return Mono.just(Result.fail(500, "服务调用异常: " + throwable.getMessage()));
        }
    }
}
