package com.xhcai.modules.rag.service;

import com.xhcai.modules.rag.dto.SearchRequest;
import com.xhcai.modules.rag.dto.SearchResult;
import com.xhcai.modules.rag.entity.DocumentSegment;

import java.util.List;
import java.util.Map;

/**
 * 检索服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IRetrievalService {

    /**
     * 向量检索
     *
     * @param request 检索请求
     * @return 检索结果
     */
    SearchResult vectorSearch(SearchRequest request);

    /**
     * 关键字检索
     *
     * @param request 检索请求
     * @return 检索结果
     */
    SearchResult keywordSearch(SearchRequest request);

    /**
     * 混合检索
     *
     * @param request 检索请求
     * @return 检索结果
     */
    SearchResult hybridSearch(SearchRequest request);

    /**
     * 智能问答
     *
     * @param datasetId 知识库ID
     * @param question 问题
     * @param topK 检索数量
     * @param modelId 模型ID
     * @return 问答结果
     */
    Map<String, Object> qa(String datasetId, String question, Integer topK, String modelId);

    /**
     * 获取相似文档
     *
     * @param documentId 文档ID
     * @param topK 返回数量
     * @param threshold 相似度阈值
     * @return 相似文档列表
     */
    List<DocumentSegment> getSimilarDocuments(String documentId, Integer topK, Double threshold);

    /**
     * 获取推荐内容
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐内容列表
     */
    List<DocumentSegment> getRecommendations(String datasetId, String userId, Integer limit);

    /**
     * 获取检索统计信息
     *
     * @param datasetId 知识库ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getSearchStats(String datasetId, String startTime, String endTime);

    /**
     * 测试检索配置
     *
     * @param datasetId 知识库ID
     * @param testQuery 测试查询
     * @return 测试结果
     */
    Map<String, Object> testSearchConfig(String datasetId, String testQuery);

    /**
     * 记录搜索日志
     *
     * @param datasetId 知识库ID
     * @param query 查询内容
     * @param searchType 搜索类型
     * @param resultCount 结果数量
     * @param responseTime 响应时间
     * @param userId 用户ID
     */
    void logSearch(String datasetId, String query, String searchType, 
                   Integer resultCount, Double responseTime, String userId);

    /**
     * 获取热门查询
     *
     * @param datasetId 知识库ID
     * @param limit 返回数量
     * @return 热门查询列表
     */
    List<String> getPopularQueries(String datasetId, Integer limit);

    /**
     * 获取搜索趋势
     *
     * @param datasetId 知识库ID
     * @param days 天数
     * @return 搜索趋势数据
     */
    List<Map<String, Object>> getSearchTrends(String datasetId, Integer days);

    /**
     * 重新排序搜索结果
     *
     * @param segments 原始结果
     * @param query 查询内容
     * @param rerankModel 重排序模型
     * @return 重排序后的结果
     */
    List<DocumentSegment> rerankResults(List<DocumentSegment> segments, String query, String rerankModel);

    /**
     * 计算查询扩展
     *
     * @param query 原始查询
     * @param datasetId 知识库ID
     * @return 扩展查询列表
     */
    List<String> expandQuery(String query, String datasetId);

    /**
     * 过滤搜索结果
     *
     * @param segments 原始结果
     * @param filters 过滤条件
     * @return 过滤后的结果
     */
    List<DocumentSegment> filterResults(List<DocumentSegment> segments, Map<String, Object> filters);

    /**
     * 高亮搜索结果
     *
     * @param content 内容
     * @param query 查询内容
     * @return 高亮后的内容
     */
    String highlightContent(String content, String query);

    /**
     * 获取搜索建议
     *
     * @param query 查询内容
     * @param datasetId 知识库ID
     * @param limit 建议数量
     * @return 搜索建议列表
     */
    List<String> getSearchSuggestions(String query, String datasetId, Integer limit);
}
