<template>
  <BaseNode
    :type="data.type"
    :label="data.label"
    :icon="nodeConfig.icon"
    :color="nodeConfig.color"
    :gradient="nodeConfig.gradient"
    :label-color="nodeConfig.labelColor"
    :icon-color="nodeConfig.iconColor"
    :selected="selected"
    :status="data.status"
    :error-message="data.errorMessage"
    :source-handles="nodeConfig.handles.source"
    :target-handles="nodeConfig.handles.target"
    @configure="handleConfigure"
    @delete="handleDelete"
  >
    <!-- 分段节点特定内容 -->
    <div class="segment-content">
      <div class="config-summary">
        <div class="config-item">
          <i class="fas fa-ruler"></i>
          <span>分段大小: {{ data.config.chunkSize || 1000 }}</span>
        </div>
        <div class="config-item">
          <i class="fas fa-overlap"></i>
          <span>重叠: {{ data.config.chunkOverlap || 200 }}</span>
        </div>
        <div class="config-item">
          <i class="fas fa-cut"></i>
          <span>分隔符: {{ getSeparatorDisplay() }}</span>
        </div>
      </div>
      
      <div v-if="data.stats" class="stats">
        <div class="stat-item">
          <span class="stat-label">输入文档</span>
          <span class="stat-value">{{ data.stats.inputDocuments || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">输出分段</span>
          <span class="stat-value">{{ data.stats.outputSegments || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">处理进度</span>
          <span class="stat-value">{{ data.stats.progress || 0 }}%</span>
        </div>
      </div>

      <!-- 进度条 -->
      <div v-if="data.status === 'running' && data.stats?.progress" class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: `${data.stats.progress}%` }"
        ></div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseNode from './BaseNode.vue'
import { NODE_LIBRARY_CONFIG } from '../config/nodeLibrary'
import type { NodeProps } from '@vue-flow/core'

// Props
interface SegmentNodeData {
  type: string
  label: string
  config: {
    chunkSize?: number
    chunkOverlap?: number
    separator?: string
    keepSeparator?: boolean
  }
  status?: 'idle' | 'running' | 'success' | 'error'
  errorMessage?: string
  stats?: {
    inputDocuments: number
    outputSegments: number
    progress: number
  }
}

const props = defineProps<NodeProps<SegmentNodeData>>()

// Emits
const emit = defineEmits<{
  'configure': [nodeId: string]
  'delete': [nodeId: string]
}>()

// 计算属性
const nodeConfig = computed(() => {
  for (const category of NODE_LIBRARY_CONFIG.categories) {
    const node = category.nodes.find(n => n.type === props.data.type)
    if (node) return node
  }
  // 默认配置
  return {
    icon: 'fas fa-cut',
    color: '#10b981',
    gradient: 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)',
    labelColor: '#065f46',
    iconColor: '#10b981',
    handles: {
      source: [{ id: 'output', position: 'right' }],
      target: [{ id: 'input', position: 'left' }]
    }
  }
})

// 方法
const handleConfigure = () => {
  emit('configure', props.id)
}

const handleDelete = () => {
  emit('delete', props.id)
}

const getSeparatorDisplay = () => {
  const separator = props.data.config.separator || '\n\n'
  switch (separator) {
    case '\n':
      return '换行'
    case '\n\n':
      return '双换行'
    case '\t':
      return '制表符'
    case ' ':
      return '空格'
    default:
      return separator.length > 10 ? separator.substring(0, 10) + '...' : separator
  }
}
</script>

<style scoped>
.segment-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-summary {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.config-item i {
  width: 14px;
  text-align: center;
}

.stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}

.progress-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 2px;
  transition: width 0.3s ease;
}
</style>
