package com.xhcai.modules.rag.plugins.minio;

import com.xhcai.plugin.config.PluginProperties;
import com.xhcai.plugin.storage.IStorageService;
import com.xhcai.plugin.storage.StorageFileInfo;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class MinioStorageService implements IStorageService {
    private static final Logger log = LoggerFactory.getLogger(MinioStorageService.class);

    private MinioClient minioClient;
    private String endpoint;
    private String accessKey;
    private String secretKey;
    private String bucketName;

    private boolean initialized = false;
    @Autowired
    private PluginProperties pluginProperties;


    @PostConstruct
    public void initialize() {
        Map<String, PluginProperties.PluginTypeConfig> types = pluginProperties.getTypes();
        Map<String, Object> config = types.get("storage").getConfig();
        try {
            if(!"minio".equals( config.get("type") )) {
                log.info("storage type is not minio, skip minio store config: {}", config);
                return;
            }

            Map<String, Object> mapMinio = (Map<String, Object>) config.get("minio");
            this.endpoint = (String) mapMinio.get("endpoint");
            this.accessKey = (String) mapMinio.get("accessKey");
            this.secretKey = (String) mapMinio.get("secretKey");
            this.bucketName = (String) mapMinio.get("bucket");
            log.info("minio store config: {}", mapMinio);

            if (endpoint == null || accessKey == null || secretKey == null) {
                throw new IllegalArgumentException("MinIO configuration is incomplete");
            }

            this.minioClient = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();

            this.initialized = true;
            log.info("MinIO Storage Service initialized successfully");

            if (!bucketExists(bucketName)) {
                createBucket(bucketName);
            }

        } catch (Exception e) {
            log.error("Failed to initialize MinIO Storage Service", e);
            throw new RuntimeException("Failed to initialize MinIO Storage Service", e);
        }
    }

    /**
     * 上传文件到默认存储桶
     * @param objectName
     * @param inputStream
     * @param contentType
     * @return
     */
    public String uploadFile(String objectName, InputStream inputStream, String contentType) {
        return uploadFile(bucketName, objectName, inputStream, contentType, null);
    }

    /**
     * 上传文件到指定存储桶
     * @param bucketName
     * @param objectName
     * @param inputStream
     * @param contentType
     * @return
     */
    public String uploadFile(String bucketName, String objectName, InputStream inputStream, String contentType) {
        return uploadFile(bucketName, objectName, inputStream, contentType, null);
    }

    public String uploadFile(String bucketName, String objectName, InputStream inputStream,
            String contentType, Map<String, String> metadata) {
        checkInitialized();

        try {
            // 确保存储桶存在
            if (!bucketExists(bucketName)) {
                createBucket(bucketName);
            }

            PutObjectArgs.Builder builder = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, -1, 10485760) // 10MB part size
                    .contentType(contentType);

            if (metadata != null && !metadata.isEmpty()) {
                builder.userMetadata(metadata);
            }

            minioClient.putObject(builder.build());

            // 生成访问URL
            return generatePresignedUrl(bucketName, objectName, 3600 * 24 * 3); // 10分钟有效期

        } catch (Exception e) {
            log.error("Failed to upload file: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("Failed to upload file", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error("Failed to close input stream", e);
                }
            }
        }
    }

    public InputStream downloadFile(String bucketName, String objectName) {
        checkInitialized();

        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("Failed to download file: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("Failed to download file", e);
        }
    }

    public boolean deleteFile(String bucketName, String objectName) {
        checkInitialized();

        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
            return true;
        } catch (Exception e) {
            log.error("Failed to delete file: {}/{}", bucketName, objectName, e);
            return false;
        }
    }

    public Map<String, Boolean> deleteFiles(String bucketName, List<String> objectNames) {
        checkInitialized();

        Map<String, Boolean> results = new HashMap<>();
        for (String objectName : objectNames) {
            results.put(objectName, deleteFile(bucketName, objectName));
        }
        return results;
    }

    public boolean fileExists(String bucketName, String objectName) {
        checkInitialized();

        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public StorageFileInfo getFileInfo(String bucketName, String objectName) {
        checkInitialized();

        try {
            StatObjectResponse stat = minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );

            return StorageFileInfo.builder()
                    .bucketName(bucketName)
                    .objectName(objectName)
                    .size(stat.size())
                    .contentType(stat.contentType())
                    .etag(stat.etag())
                    .lastModified(LocalDateTime.ofInstant(stat.lastModified().toInstant(), ZoneId.systemDefault()))
                    .metadata(stat.userMetadata())
                    .isDirectory(false)
                    .build();

        } catch (Exception e) {
            log.error("Failed to get file info: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("Failed to get file info", e);
        }
    }

    public List<StorageFileInfo> listFiles(String bucketName, String prefix, int maxKeys) {
        checkInitialized();

        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucketName)
                            .prefix(prefix)
                            .maxKeys(maxKeys)
                            .build()
            );

            List<StorageFileInfo> fileInfos = new ArrayList<>();
            for (Result<Item> result : results) {
                Item item = result.get();

                StorageFileInfo fileInfo = StorageFileInfo.builder()
                        .bucketName(bucketName)
                        .objectName(item.objectName())
                        .size(item.size())
                        .etag(item.etag())
                        .lastModified(LocalDateTime.ofInstant(item.lastModified().toInstant(), ZoneId.systemDefault()))
                        .isDirectory(item.isDir())
                        .build();

                fileInfos.add(fileInfo);
            }

            return fileInfos;

        } catch (Exception e) {
            log.error("Failed to list files: {}", bucketName, e);
            throw new RuntimeException("Failed to list files", e);
        }
    }

    public String generatePresignedUrl(String bucketName, String objectName, int expireSeconds) {
        checkInitialized();

        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)  // 明确指定HTTP方法
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(expireSeconds)
                            .build()
            );
        } catch (Exception e) {
            log.error("Failed to generate presigned URL: {}/{}", bucketName, objectName, e);
            throw new RuntimeException("Failed to generate presigned URL", e);
        }
    }

    public boolean createBucket(String bucketName) {
        checkInitialized();

        try {
            if (!bucketExists(bucketName)) {
                minioClient.makeBucket(
                        MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build()
                );
            }
            return true;
        } catch (Exception e) {
            log.error("Failed to create bucket: {}", bucketName, e);
            return false;
        }
    }

    public boolean deleteBucket(String bucketName) {
        checkInitialized();

        try {
            minioClient.removeBucket(
                    RemoveBucketArgs.builder()
                            .bucket(bucketName)
                            .build()
            );
            return true;
        } catch (Exception e) {
            log.error("Failed to delete bucket: {}", bucketName, e);
            return false;
        }
    }

    public boolean bucketExists(String bucketName) {
        checkInitialized();

        try {
            return minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .build()
            );
        } catch (Exception e) {
            log.error("Failed to check bucket existence: {}", bucketName, e);
            return false;
        }
    }

    public boolean isHealthy() {
        if (!initialized) {
            return false;
        }

        try {
            // 尝试列出存储桶来检查连接
            minioClient.listBuckets();
            return true;
        } catch (Exception e) {
            log.error("MinIO health check failed", e);
            return false;
        }
    }

    @Override
    public String getServiceName() {
        return "";
    }

    @Override
    public String getServiceType() {
        return "";
    }

    @Override
    public void initialize(Map<String, Object> config) {

    }

    private void checkInitialized() {
        if (!initialized) {
            throw new IllegalStateException("MinIO Storage Service is not initialized");
        }
    }
}