<template>
  <div class="plugin-monitor space-y-6">
    <!-- 插件概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-puzzle-piece text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总插件数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ pluginStats.total }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-play text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日使用次数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ pluginStats.todayUsage.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-check text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">活跃插件</p>
            <p class="text-2xl font-semibold text-gray-900">{{ pluginStats.active }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">异常插件</p>
            <p class="text-2xl font-semibold text-gray-900">{{ pluginStats.error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 插件监控表格 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">插件监控详情</h3>
          <div class="flex items-center gap-4">
            <select v-model="pluginTypeFilter" class="form-select text-sm">
              <option value="">全部类型</option>
              <option value="工具">工具</option>
              <option value="模型">模型</option>
              <option value="Agent策略">Agent策略</option>
              <option value="扩展">扩展</option>
              <option value="插件集">插件集</option>
              <option value="MCP服务">MCP服务</option>
              <option value="自定义">自定义</option>
              <option value="工作流">工作流</option>
            </select>
            <input
              v-model="pluginSearchQuery"
              type="text"
              placeholder="搜索插件..."
              class="form-input text-sm w-64"
            />
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">插件信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性能指标</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">错误率</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="plugin in paginatedPlugins" :key="plugin.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-purple-400 to-purple-600 flex items-center justify-center">
                      <i class="fas fa-puzzle-piece text-white"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ plugin.name }}</div>
                    <div class="text-sm text-gray-500">{{ plugin.version }}</div>
                    <div class="text-xs text-gray-400">
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                        {{ plugin.type }}
                      </span>
                      {{ formatDate(plugin.createdAt) }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">总量: {{ plugin.totalUsage.toLocaleString() }}</div>
                <div class="text-sm text-gray-500">今日: {{ plugin.todayUsage.toLocaleString() }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">响应: {{ plugin.avgResponseTime }}ms</div>
                <div class="text-sm text-gray-500">CPU: {{ plugin.cpuUsage }}%</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ plugin.errorRate }}%</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(plugin.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusText(plugin.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 插件分页 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredPlugins.length) }} 条，
            共 {{ filteredPlugins.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="text-sm text-gray-700">
              第 {{ currentPage }} / {{ totalPages }} 页
            </span>
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  refreshTrigger: 0
})

// 搜索和过滤
const pluginSearchQuery = ref('')
const pluginTypeFilter = ref('')

// 分页配置
const pageSize = ref(10)
const currentPage = ref(1)

// 插件统计数据
const pluginStats = ref({
  total: 234,
  todayUsage: 3450,
  active: 198,
  error: 12
})

// 插件监控数据
const plugins = ref([
  {
    id: 1,
    name: '数据分析工具',
    type: '工具',
    version: 'v2.1.0',
    createdAt: '2024-01-20',
    totalUsage: 5670,
    todayUsage: 89,
    avgResponseTime: 145,
    cpuUsage: 12.5,
    errorRate: 0.8,
    status: 'healthy'
  },
  {
    id: 2,
    name: 'GPT-4模型接口',
    type: '模型',
    version: 'v1.5.2',
    createdAt: '2024-02-10',
    totalUsage: 12450,
    todayUsage: 234,
    avgResponseTime: 280,
    cpuUsage: 25.8,
    errorRate: 1.2,
    status: 'healthy'
  },
  {
    id: 3,
    name: '智能决策策略',
    type: 'Agent策略',
    version: 'v3.0.1',
    createdAt: '2024-03-15',
    totalUsage: 3450,
    todayUsage: 56,
    avgResponseTime: 95,
    cpuUsage: 8.9,
    errorRate: 0.3,
    status: 'healthy'
  },
  {
    id: 4,
    name: '文档处理扩展',
    type: '扩展',
    version: 'v1.2.8',
    createdAt: '2024-04-05',
    totalUsage: 2340,
    todayUsage: 34,
    avgResponseTime: 520,
    cpuUsage: 45.2,
    errorRate: 5.6,
    status: 'error'
  }
])

// 计算属性：过滤后的数据
const filteredPlugins = computed(() => {
  let filtered = plugins.value

  if (pluginTypeFilter.value) {
    filtered = filtered.filter(plugin => plugin.type === pluginTypeFilter.value)
  }

  if (pluginSearchQuery.value) {
    const query = pluginSearchQuery.value.toLowerCase()
    filtered = filtered.filter(plugin =>
      plugin.name.toLowerCase().includes(query) ||
      plugin.type.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 分页后的数据
const paginatedPlugins = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPlugins.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredPlugins.value.length / pageSize.value)
})

// 方法
const updatePluginStats = () => {
  plugins.value.forEach(plugin => {
    plugin.todayUsage = Math.floor(Math.random() * 300) + 20
    plugin.avgResponseTime = Math.floor(Math.random() * 400) + 50
    plugin.cpuUsage = Math.round(Math.random() * 50 * 10) / 10
    plugin.errorRate = Math.round(Math.random() * 8 * 10) / 10

    // 更新状态
    if (plugin.errorRate > 5) {
      plugin.status = 'error'
    } else if (plugin.errorRate > 2 || plugin.cpuUsage > 40) {
      plugin.status = 'warning'
    } else {
      plugin.status = 'healthy'
    }
  })

  // 更新统计数据
  pluginStats.value.todayUsage = plugins.value.reduce((sum, plugin) => sum + plugin.todayUsage, 0)
  pluginStats.value.active = plugins.value.filter(plugin => plugin.status === 'healthy').length
  pluginStats.value.error = plugins.value.filter(plugin => plugin.status === 'error').length
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return statusMap[status] || status
}

// 分页方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const resetPagination = () => {
  currentPage.value = 1
}

// 时间格式化方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 监听搜索和过滤条件变化，重置分页
watch([pluginSearchQuery, pluginTypeFilter], () => {
  resetPagination()
})

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  updatePluginStats()
})

onMounted(() => {
  updatePluginStats()
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-input, .form-select {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  background-color: #f8fafc;
  transform: scale(1.01);
}

/* 渐变背景 */
.from-purple-400 {
  --tw-gradient-from: #c084fc;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(192, 132, 252, 0));
}

.to-purple-600 {
  --tw-gradient-to: #9333ea;
}
</style>
