<template>
  <div class="pagination-section flex items-center justify-between py-4 px-6 bg-white border-t border-gray-200">
    <!-- 分页信息 -->
    <div class="pagination-info text-sm text-gray-600">
      显示 {{ startIndex }}-{{ endIndex }} 条，共 {{ total }} 条记录
    </div>
    
    <!-- 分页控件 -->
    <div class="pagination-controls flex items-center gap-2">
      <!-- 上一页 -->
      <button
        @click="goToPrevPage"
        :disabled="currentPage === 1"
        class="pagination-btn flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
        上一页
      </button>
      
      <!-- 页码按钮 -->
      <div class="pagination-numbers flex items-center gap-1">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page)"
          :class="[
            'pagination-number px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
            page === currentPage
              ? 'bg-primary-500 text-white shadow-md'
              : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700'
          ]"
        >
          {{ page }}
        </button>
      </div>
      
      <!-- 下一页 -->
      <button
        @click="goToNextPage"
        :disabled="currentPage === totalPages"
        class="pagination-btn flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
      >
        下一页
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
    
    <!-- 每页显示数量选择器 -->
    <div class="page-size-selector flex items-center gap-2 text-sm text-gray-600">
      <label>每页显示</label>
      <select
        :value="pageSize"
        @change="changePageSize(($event.target as HTMLSelectElement)?.value)"
        class="px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
      >
        <option value="10">10条</option>
        <option value="20">20条</option>
        <option value="50">50条</option>
        <option value="100">100条</option>
      </select>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  currentPage: number
  pageSize: number
  total: number
}

interface Emits {
  (e: 'update:currentPage', page: number): void
  (e: 'update:pageSize', size: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算总页数
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

// 计算当前页显示的数据范围
const startIndex = computed(() => (props.currentPage - 1) * props.pageSize + 1)
const endIndex = computed(() => Math.min(props.currentPage * props.pageSize, props.total))

// 计算可见的页码
const visiblePages = computed(() => {
  const maxVisiblePages = 5
  const pages: number[] = []
  
  if (totalPages.value <= maxVisiblePages) {
    // 如果总页数小于等于最大可见页数，显示所有页码
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i)
    }
  } else {
    // 计算显示范围
    let startPage = Math.max(1, props.currentPage - Math.floor(maxVisiblePages / 2))
    let endPage = Math.min(totalPages.value, startPage + maxVisiblePages - 1)
    
    // 调整起始页，确保显示足够的页码
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }
  }
  
  return pages
})

// 跳转到指定页
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
    emit('update:currentPage', page)
  }
}

// 上一页
const goToPrevPage = () => {
  if (props.currentPage > 1) {
    emit('update:currentPage', props.currentPage - 1)
  }
}

// 下一页
const goToNextPage = () => {
  if (props.currentPage < totalPages.value) {
    emit('update:currentPage', props.currentPage + 1)
  }
}

// 改变每页显示数量
const changePageSize = (size: string) => {
  const newSize = parseInt(size)
  emit('update:pageSize', newSize)
  // 重新计算当前页，确保不超出范围
  const newTotalPages = Math.ceil(props.total / newSize)
  if (props.currentPage > newTotalPages) {
    emit('update:currentPage', Math.max(1, newTotalPages))
  }
}
</script>

<style scoped>
.pagination-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.pagination-number {
  min-width: 36px;
  text-align: center;
}

.page-size-selector select {
  min-width: 80px;
}
</style>
