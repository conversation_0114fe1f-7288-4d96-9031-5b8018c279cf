package com.xhcai.common.core.event;

import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 基础事件类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class BaseEvent extends ApplicationEvent {

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 事件来源
     */
    private String eventSource;

    /**
     * 事件数据
     */
    private Object eventData;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 用户ID
     */
    private Long userId;

    public BaseEvent(Object source) {
        super(source);
        this.eventTime = LocalDateTime.now();
        this.eventId = generateEventId();
    }

    public BaseEvent(Object source, String eventType) {
        super(source);
        this.eventType = eventType;
        this.eventTime = LocalDateTime.now();
        this.eventId = generateEventId();
    }

    public BaseEvent(Object source, String eventType, Object eventData) {
        super(source);
        this.eventType = eventType;
        this.eventData = eventData;
        this.eventTime = LocalDateTime.now();
        this.eventId = generateEventId();
    }

    /**
     * 生成事件ID
     *
     * @return 事件ID
     */
    private String generateEventId() {
        return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }

    // Getters and Setters
    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public LocalDateTime getEventTime() {
        return eventTime;
    }

    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
    }

    public String getEventSource() {
        return eventSource;
    }

    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }

    public Object getEventData() {
        return eventData;
    }

    public void setEventData(Object eventData) {
        this.eventData = eventData;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "BaseEvent{" +
                "eventId='" + eventId + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventTime=" + eventTime +
                ", eventSource='" + eventSource + '\'' +
                ", eventData=" + eventData +
                ", tenantId=" + tenantId +
                ", userId=" + userId +
                '}';
    }
}
