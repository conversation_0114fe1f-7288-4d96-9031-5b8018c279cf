version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: yyzs-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: yyzs_agent
      POSTGRES_USER: yyzs
      POSTGRES_PASSWORD: yyzs123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - yyzs-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U yyzs -d yyzs_agent"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存已移除，使用内存缓存

  # YYZS Agent应用
  yyzs-agent:
    build:
      context: ./yyzs/agent
      dockerfile: Dockerfile
    container_name: yyzs-agent
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # 数据库配置
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: yyzs_agent
      DB_USERNAME: yyzs
      DB_PASSWORD: yyzs123
      DDL_AUTO: update
      
      # Redis配置已移除，使用内存缓存
      
      # 应用配置
      SERVER_PORT: 8080
      MANAGEMENT_PORT: 8081
      SPRING_PROFILES_ACTIVE: docker
      
      # 日志配置
      LOG_LEVEL: INFO
      APP_LOG_LEVEL: DEBUG
      SECURITY_LOG_LEVEL: WARN
      
      # JVM配置
      JAVA_OPTS: >-
        -Xms512m 
        -Xmx1024m 
        -XX:+UseG1GC 
        -XX:+UseContainerSupport
        -XX:MaxRAMPercentage=75.0
        -XX:+HeapDumpOnOutOfMemoryError
        -XX:HeapDumpPath=/app/logs/
      
      # 应用特定配置
      ELASTIC_INSTALL_PATH: /opt/elastic
      MAX_UPLOAD_SIZE: 500MB
      MONITOR_ENABLED: true
      MONITOR_INTERVAL: 30s
      
      # 安全配置
      JWT_SECRET: yyzs-agent-jwt-secret-key-2024-docker
      JWT_EXPIRATION: 86400
      
      # 时区配置
      TZ: Asia/Shanghai
    volumes:
      - agent_data:/app/data
      - agent_logs:/app/logs
      - agent_temp:/app/temp
      - elastic_components:/opt/elastic
      - /var/run/docker.sock:/var/run/docker.sock:ro  # 用于容器管理（可选）
    ports:
      - "8080:8080"  # 应用端口
      - "8081:8081"  # 管理端口
    networks:
      - yyzs-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # YYZS Web前端（可选）
  yyzs-web:
    build:
      context: ./yyzs/web
      dockerfile: Dockerfile
    container_name: yyzs-web
    restart: unless-stopped
    depends_on:
      - yyzs-agent
    environment:
      NEXT_PUBLIC_API_BASE_URL: http://yyzs-agent:8080
      NEXT_PUBLIC_WS_URL: ws://yyzs-agent:8080
    ports:
      - "3000:3000"
    networks:
      - yyzs-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: yyzs-nginx
    restart: unless-stopped
    depends_on:
      - yyzs-agent
      - yyzs-web
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro  # SSL证书目录（如果需要HTTPS）
    ports:
      - "80:80"
      - "443:443"
    networks:
      - yyzs-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: yyzs-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - yyzs-network

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: yyzs-grafana
    restart: unless-stopped
    depends_on:
      - prometheus
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3001:3000"
    networks:
      - yyzs-network

# 网络配置
networks:
  yyzs-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  # redis_data 已移除
  agent_data:
    driver: local
  agent_logs:
    driver: local
  agent_temp:
    driver: local
  elastic_components:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
