/**
 * 环境配置管理
 * 统一管理不同环境下的配置信息
 */

export interface EnvConfig {
  // API配置
  apiBaseUrl: string
  apiTimeout: number
  
  // 应用配置
  appTitle: string
  appVersion: string
  
  // 认证配置
  tokenStorageKey: string
  loginRedirectPath: string
  defaultRedirectPath: string
  
  // 调试配置
  debugMode: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}

/**
 * 获取环境变量值
 */
function getEnvValue(key: string, defaultValue: string = ''): string {
  const value = import.meta.env[key]
  // 如果环境变量存在（即使是空字符串），就使用它；否则使用默认值
  return value !== undefined ? value : defaultValue
}

/**
 * 获取布尔类型环境变量
 */
function getBooleanEnvValue(key: string, defaultValue: boolean = false): boolean {
  const value = getEnvValue(key)
  return value === 'true' || value === '1'
}

/**
 * 获取数字类型环境变量
 */
function getNumberEnvValue(key: string, defaultValue: number = 0): number {
  const value = getEnvValue(key)
  const parsed = parseInt(value, 10)
  return isNaN(parsed) ? defaultValue : parsed
}

/**
 * 当前环境配置
 */
export const envConfig: EnvConfig = {
  // API配置
  apiBaseUrl: getEnvValue('VITE_API_BASE_URL', ''),
  apiTimeout: getNumberEnvValue('VITE_API_TIMEOUT', 30000),
  
  // 应用配置
  appTitle: getEnvValue('VITE_APP_TITLE', 'AI智能体平台'),
  appVersion: getEnvValue('VITE_APP_VERSION', '1.0.0'),
  
  // 认证配置
  tokenStorageKey: getEnvValue('VITE_TOKEN_STORAGE_KEY', 'ai_platform_tokens'),
  loginRedirectPath: getEnvValue('VITE_LOGIN_REDIRECT_PATH', '/login'),
  defaultRedirectPath: getEnvValue('VITE_DEFAULT_REDIRECT_PATH', '/home'),
  
  // 调试配置
  debugMode: getBooleanEnvValue('VITE_DEBUG_MODE', false),
  logLevel: (getEnvValue('VITE_LOG_LEVEL', 'info') as EnvConfig['logLevel'])
}

/**
 * 环境类型
 */
export const isDevelopment = import.meta.env.DEV
export const isProduction = import.meta.env.PROD
export const isTest = import.meta.env.MODE === 'test'

/**
 * 日志工具
 */
export const logger = {
  debug: (...args: any[]) => {
    if (envConfig.debugMode && ['debug'].includes(envConfig.logLevel)) {
      console.debug('[DEBUG]', ...args)
    }
  },
  info: (...args: any[]) => {
    if (envConfig.debugMode && ['debug', 'info'].includes(envConfig.logLevel)) {
      console.info('[INFO]', ...args)
    }
  },
  warn: (...args: any[]) => {
    if (envConfig.debugMode && ['debug', 'info', 'warn'].includes(envConfig.logLevel)) {
      console.warn('[WARN]', ...args)
    }
  },
  error: (...args: any[]) => {
    console.error('[ERROR]', ...args)
  }
}

export default envConfig
