package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;

/**
 * 用户角色关联实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_user_role", indexes = {
    @Index(name = "uk_sys_user_role", columnList = "user_id, role_id, tenant_id", unique = true),
    @Index(name = "idx_sys_user_role_user_id", columnList = "user_id"),
    @Index(name = "idx_sys_user_role_role_id", columnList = "role_id"),
    @Index(name = "idx_sys_user_role_tenant_id", columnList = "tenant_id")
})
@Schema(description = "用户角色关联")
@TableName("sys_user_role")
public class SysUserRole extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @jakarta.persistence.Column(name = "user_id", nullable = false, length = 36)
    @jakarta.validation.constraints.NotBlank(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    /**
     * 角色ID
     */
    @jakarta.persistence.Column(name = "role_id", nullable = false, length = 36)
    @jakarta.validation.constraints.NotBlank(message = "角色ID不能为空")
    @Schema(description = "角色ID")
    @TableField("role_id")
    private String roleId;

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    @Override
    public String toString() {
        return "SysUserRole{"
                + "userId=" + userId
                + ", roleId=" + roleId
                + ", id=" + getId()
                + ", tenantId=" + getTenantId()
                + ", createBy=" + getCreateBy()
                + ", createTime=" + getCreateTime()
                + ", updateBy=" + getUpdateBy()
                + ", updateTime=" + getUpdateTime()
                + ", deleted='" + getDeleted() + '\''
                + ", remark='" + getRemark() + '\''
                + '}';
    }
}
