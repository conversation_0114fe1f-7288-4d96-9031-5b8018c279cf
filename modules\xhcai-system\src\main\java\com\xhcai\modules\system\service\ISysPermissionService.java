package com.xhcai.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysPermissionQueryDTO;
import com.xhcai.modules.system.entity.SysPermission;
import com.xhcai.modules.system.vo.SysPermissionVO;
import com.xhcai.modules.system.vo.SysUserVO;

import java.util.List;
import java.util.Set;

/**
 * 权限信息服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysPermissionService extends IService<SysPermission> {

    /**
     * 分页查询权限列表
     *
     * @param queryDTO 查询条件
     * @return 权限分页列表
     */
    PageResult<SysPermissionVO> selectPermissionPage(SysPermissionQueryDTO queryDTO);

    /**
     * 查询权限列表
     *
     * @param queryDTO 查询条件
     * @return 权限列表
     */
    List<SysPermissionVO> selectPermissionList(SysPermissionQueryDTO queryDTO);

    /**
     * 查询权限树
     *
     * @param queryDTO 查询条件
     * @return 权限树
     */
    List<SysPermissionVO> selectPermissionTree(SysPermissionQueryDTO queryDTO);

    /**
     * 根据权限ID查询权限信息
     *
     * @param permissionId 权限ID
     * @return 权限信息
     */
    SysPermissionVO selectPermissionById(String permissionId);

    /**
     * 根据权限编码查询权限信息
     *
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    SysPermission selectByPermissionCode(String permissionCode);

    /**
     * 创建权限
     *
     * @param permission 权限信息
     * @return 是否成功
     */
    boolean insertPermission(SysPermission permission);

    /**
     * 更新权限信息
     *
     * @param permission 权限信息
     * @return 是否成功
     */
    boolean updatePermission(SysPermission permission);

    /**
     * 删除权限
     *
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean deletePermissions(List<String> permissionIds);

    /**
     * 启用权限
     *
     * @param permissionId 权限ID
     * @return 是否成功
     */
    boolean enablePermission(String permissionId);

    /**
     * 停用权限
     *
     * @param permissionId 权限ID
     * @return 是否成功
     */
    boolean disablePermission(String permissionId);

    /**
     * 检查权限编码是否存在
     *
     * @param permissionCode 权限编码
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    boolean existsPermissionCode(String permissionCode, String excludeId);

    /**
     * 检查权限名称是否存在（同级权限）
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    boolean existsPermissionName(String permissionName, String parentId, String excludeId);

    /**
     * 检查是否存在子权限
     *
     * @param permissionId 权限ID
     * @return 是否存在子权限
     */
    boolean hasChildren(String permissionId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param sysUserVO 用户
     * @return 权限列表
     */
    List<SysPermissionVO> selectPermissionsByUserId(SysUserVO sysUserVO);

    /**
     * 根据用户ID查询权限编码集合
     *
     * @param userId 用户ID
     * @return 权限编码集合
     */
    Set<String> selectPermissionCodesByUserId(String userId);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermissionVO> selectPermissionsByRoleId(String roleId);

    /**
     * 构建权限树
     *
     * @param permissions 权限列表
     * @return 权限树
     */
    List<SysPermissionVO> buildPermissionTree(List<SysPermission> permissions);

    /**
     * 构建权限选择树（排除指定权限及其子权限）
     *
     * @param excludePermissionId 排除的权限ID
     * @return 权限选择树
     */
    List<SysPermissionVO> buildPermissionSelectTree(String excludePermissionId);

    /**
     * 获取权限路径
     *
     * @param permissionId 权限ID
     * @return 权限路径
     */
    String getPermissionPath(String permissionId);

    /**
     * 批量更新权限状态
     *
     * @param permissionIds 权限ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> permissionIds, String status);

    /**
     * 同步权限排序
     *
     * @param permissions 权限列表
     * @return 是否成功
     */
    boolean syncPermissionOrder(List<SysPermission> permissions);

    /**
     * 获取所有可用权限（用于角色分配）
     *
     * @return 权限列表
     */
    List<SysPermissionVO> selectAllAvailablePermissions();

    /**
     * 根据权限类型查询权限列表
     *
     * @param permissionType 权限类型
     * @return 权限列表
     */
    List<SysPermissionVO> selectPermissionsByType(String permissionType);

    /**
     * 移动权限
     *
     * @param permissionId 权限ID
     * @param newParentId 新父权限ID
     * @return 是否成功
     */
    boolean movePermission(String permissionId, String newParentId);
}
