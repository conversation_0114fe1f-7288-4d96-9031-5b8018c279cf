<template>
  <div class="welcome-page flex flex-col h-full">
    <!-- 内容展示区域 -->
    <div class="content-display-area flex-1 overflow-y-auto px-4 pb-4">
      <!-- 智能体信息显示区域 - 当传入智能体信息时显示 -->
      <div v-if="props.agentInfo" class="agent-info-section">
        <AgentInfoDisplay :agent-info="props.agentInfo" @quick-start="handleQuickStart"/>
      </div>

      <!-- 模型与智能体选择区域 - 当没有传入智能体信息时显示 -->
      <div v-else class="text-center py-12">
        <div class="model-agent-selector-section flex-1 overflow-hidden">
          <ModelAgentSelector
            @model-select="handleModelSelect"
            @agent-select="handleAgentSelect"
          />
        </div>

        <!-- 快速智能体选择按钮 -->
        <div class="mt-8 space-y-4">
          <div class="text-center">
            <h4 class="text-lg font-semibold text-gray-700 mb-4">快速开始</h4>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <!-- 通用助手按钮 -->
              <el-button
                type="primary"
                size="large"
                @click="handleQuickSelectAgent('general-assistant')"
                class="px-6 py-3 flex items-center justify-center"
              >
                <span class="mr-2">🤖</span>
                <span>通用助手</span>
              </el-button>

              <!-- 编程助手按钮 -->
              <el-button
                type="success"
                size="large"
                @click="handleQuickSelectAgent('coding-assistant')"
                class="px-6 py-3 flex items-center justify-center"
              >
                <span class="mr-2">💻</span>
                <span>编程助手</span>
              </el-button>
            </div>
          </div>


        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import ModelAgentSelector from '@/views/aiExplore/ModelAgentSelector.vue'
import AgentInfoDisplay from '@/views/aiExplore/AgentInfoDisplay.vue'
import { useModelStore } from '@/stores/modelStore'

// 使用模型存储
const { agentCategories, setSelectedModel } = useModelStore()

// Props
interface Props {
  agentInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  agentInfo: null
})

// 定义事件
const emit = defineEmits<{
  'model-select': [model: any]
  'agent-select': [agent: any]
  'quick-start': [example: any]
}>()

// 处理模型选择
const handleModelSelect = (model: any) => {
  emit('model-select', model)
}

// 处理智能体选择
const handleAgentSelect = (agent: any) => {
  emit('agent-select', agent)
}

const handleQuickStart = (example: any) => {
  emit('quick-start', example)
}



// 快速选择智能体
const handleQuickSelectAgent = (agentId: string) => {
  // 从所有智能体中找到对应的智能体
  const allAgents = agentCategories.value.flatMap(category => category.agents)
  const agent = allAgents.find(a => a.id === agentId)

  if (agent) {
    // 设置选中的智能体
    setSelectedModel(agent.id, agent)
    // 触发智能体选择事件
    emit('agent-select', agent)
  }
}

// 消息发送现在由全局状态管理处理，不再需要事件传递
</script>

<style scoped>
/* 模型智能体选择器区域样式 */
.model-agent-selector-section {
  height: 100%;
  min-height: 500px;
  padding: 0;
}

.model-agent-selector-section .model-agent-selector {
  height: 100%;
  padding: 1rem;
}
</style>
