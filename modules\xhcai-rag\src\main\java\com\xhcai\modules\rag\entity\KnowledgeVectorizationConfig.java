package com.xhcai.modules.rag.entity;

import java.math.BigDecimal;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库向量化配置实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "datasets_vectorization_config")
@TableName(value = "datasets_vectorization_config", autoResultMap = true)
@Schema(description = "知识库向量化配置")
public class KnowledgeVectorizationConfig extends BaseWithTenantIDEntity {

    /**
     * 索引模式：high_quality（高质量）、economy（经济）
     */
    @Schema(description = "索引模式")
    @Column(name = "index_mode", length = 50)
    private String indexMode;

    /**
     * 嵌入模型（仅高质量模式）
     */
    @Schema(description = "嵌入模型")
    @Column(name = "embedding_model", length = 100)
    private String embeddingModel;

    /**
     * 向量数据库ID（默认选择）
     */
    @Schema(description = "向量数据库ID")
    @Column(name = "vector_database_id", length = 36)
    private String vectorDatabaseId;

    /**
     * 文件存储ID（默认选择）
     */
    @Schema(description = "文件存储ID")
    @Column(name = "file_storage_id", length = 36)
    private String fileStorageId;

    /**
     * 检索设置
     */
    @Schema(description = "检索设置")
    @Column(name = "retrieval_settings", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    @TableField(value = "retrieval_settings", typeHandler = com.xhcai.modules.rag.handler.RetrievalSettingsTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private RetrievalSettings retrievalSettings;

    /**
     * 检索设置内部类
     */
    @Data
    @Schema(description = "检索设置")
    public static class RetrievalSettings {

        @Schema(description = "检索模式：vector-向量检索，fulltext-全文检索，hybrid-混合检索")
        private String retrievalMode;

        @Schema(description = "是否启用Rerank模型")
        private Boolean enableRerank;

        @Schema(description = "Rerank模型")
        private String rerankModel;

        @Schema(description = "Top K")
        private Integer topK;

        @Schema(description = "相似度阈值")
        private BigDecimal scoreThreshold;

        @Schema(description = "混合检索权重设置")
        private HybridWeights hybridWeights;
    }

    /**
     * 混合检索权重内部类
     */
    @Data
    @Schema(description = "混合检索权重")
    public static class HybridWeights {

        @Schema(description = "语义权重")
        private BigDecimal semanticWeight;

        @Schema(description = "关键词权重")
        private BigDecimal keywordWeight;
    }
}
