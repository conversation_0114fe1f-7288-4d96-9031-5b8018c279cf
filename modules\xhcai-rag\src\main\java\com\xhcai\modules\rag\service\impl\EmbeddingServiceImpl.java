package com.xhcai.modules.rag.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.rag.config.RagProperties;
import com.xhcai.modules.rag.entity.DocumentSegment;
import com.xhcai.modules.rag.service.IEmbeddingService;
import com.xhcai.modules.rag.service.IDocumentSegmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 向量化服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class EmbeddingServiceImpl implements IEmbeddingService {

    private static final Logger log = LoggerFactory.getLogger(EmbeddingServiceImpl.class);

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private RagProperties ragProperties;

    @Autowired
    private IDocumentSegmentService documentSegmentService;

    @Override
    public float[] embedText(String text) {
        if (!StringUtils.hasText(text)) {
            throw new BusinessException("文本内容不能为空");
        }

        try {
            log.debug("开始向量化文本: length={}", text.length());

            EmbeddingResponse response = embeddingModel.embedForResponse(List.of(text));

            if (response.getResults().isEmpty()) {
                throw new BusinessException("向量化失败：无返回结果");
            }

            float[] result = response.getResults().get(0).getOutput();

            log.debug("文本向量化完成: dimension={}", result.length);

            return result;

        } catch (Exception e) {
            log.error("文本向量化失败: error={}", e.getMessage());
            throw new BusinessException("文本向量化失败: " + e.getMessage());
        }
    }

    @Override
    public List<float[]> embedTexts(List<String> texts) {
        if (texts == null || texts.isEmpty()) {
            return Collections.emptyList();
        }

        log.info("开始批量向量化文本: count={}", texts.size());

        try {
            // 过滤空文本
            List<String> validTexts = texts.stream()
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());

            if (validTexts.isEmpty()) {
                return Collections.emptyList();
            }

            EmbeddingResponse response = embeddingModel.embedForResponse(validTexts);

            List<float[]> vectors = new ArrayList<>();
            for (org.springframework.ai.embedding.Embedding result : response.getResults()) {
                float[] vector = result.getOutput();
                vectors.add(vector);
            }

            log.info("批量文本向量化完成: count={}", vectors.size());

            return vectors;

        } catch (Exception e) {
            log.error("批量文本向量化失败: error={}", e.getMessage());
            throw new BusinessException("批量文本向量化失败: " + e.getMessage());
        }
    }

    @Override
    public float[] embedSegment(DocumentSegment segment) {
        if (segment == null || !StringUtils.hasText(segment.getContent())) {
            throw new BusinessException("文档分段内容不能为空");
        }

        log.debug("开始向量化文档分段: segmentId={}", segment.getId());

        float[] vector = embedText(segment.getContent());

        // 更新分段的向量信息
        segment.setVector(vector);
        segment.setVectorHash(calculateVectorHash(vector));
        segment.setEmbeddingModel(ragProperties.getEmbedding().getModel());
        segment.setUpdateTime(LocalDateTime.now());

        log.debug("文档分段向量化完成: segmentId={}", segment.getId());

        return vector;
    }

    @Override
    public List<float[]> embedSegments(List<DocumentSegment> segments) {
        if (segments == null || segments.isEmpty()) {
            return Collections.emptyList();
        }

        log.info("开始批量向量化文档分段: count={}", segments.size());

        List<String> texts = segments.stream()
                .filter(segment -> StringUtils.hasText(segment.getContent()))
                .map(DocumentSegment::getContent)
                .collect(Collectors.toList());

        List<float[]> vectors = embedTexts(texts);

        // 更新分段的向量信息
        for (int i = 0; i < segments.size() && i < vectors.size(); i++) {
            DocumentSegment segment = segments.get(i);
            float[] vector = vectors.get(i);

            segment.setVector(vector);
            segment.setVectorHash(calculateVectorHash(vector));
            segment.setEmbeddingModel(ragProperties.getEmbedding().getModel());
            segment.setUpdateTime(LocalDateTime.now());
        }

        log.info("批量文档分段向量化完成: count={}", vectors.size());

        return vectors;
    }

    @Override
    @Async("embeddingProcessingExecutor")
    public void embedSegmentAsync(String segmentId) {
        log.info("开始异步向量化文档分段: segmentId={}", segmentId);

        try {
            DocumentSegment segment = documentSegmentService.getById(segmentId);
            if (segment == null) {
                log.error("文档分段不存在: segmentId={}", segmentId);
                return;
            }

            // 向量化
            float[] vector = embedSegment(segment);

            // 保存向量
            documentSegmentService.updateVector(segmentId, vector);

            log.info("异步文档分段向量化完成: segmentId={}", segmentId);

        } catch (Exception e) {
            log.error("异步文档分段向量化失败: segmentId={}, error={}", segmentId, e.getMessage());
            // 更新分段状态为错误
            documentSegmentService.updateStatus(segmentId, "error", e.getMessage());
        }
    }

    @Override
    @Async("embeddingProcessingExecutor")
    public void embedSegmentsAsync(List<String> segmentIds) {
        log.info("开始批量异步向量化文档分段: count={}", segmentIds.size());

        try {
            List<DocumentSegment> segments = documentSegmentService.listByIds(segmentIds);

            // 批量向量化
            List<float[]> vectors = embedSegments(segments);

            // 批量保存向量
            for (int i = 0; i < segments.size() && i < vectors.size(); i++) {
                DocumentSegment segment = segments.get(i);
                float[] vector = vectors.get(i);
                documentSegmentService.updateVector(segment.getId(), vector);
            }

            log.info("批量异步文档分段向量化完成: count={}", vectors.size());

        } catch (Exception e) {
            log.error("批量异步文档分段向量化失败: error={}", e.getMessage());
            // 更新所有分段状态为错误
            segmentIds.forEach(segmentId
                    -> documentSegmentService.updateStatus(segmentId, "error", e.getMessage()));
        }
    }

    @Override
    public double calculateSimilarity(String text1, String text2) {
        if (!StringUtils.hasText(text1) || !StringUtils.hasText(text2)) {
            return 0.0;
        }

        float[] vector1 = embedText(text1);
        float[] vector2 = embedText(text2);

        return calculateSimilarity(vector1, vector2);
    }

    @Override
    public double calculateSimilarity(float[] vector1, float[] vector2) {
        if (vector1 == null || vector2 == null || vector1.length != vector2.length) {
            return 0.0;
        }

        // 计算余弦相似度
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    @Override
    public List<DocumentSegment> searchSimilarVectors(float[] queryVector, String datasetId, int topK) {
        if (queryVector == null || queryVector.length == 0) {
            return Collections.emptyList();
        }

        log.info("开始向量相似度搜索: datasetId={}, topK={}", datasetId, topK);

        // TODO: 实现向量数据库搜索
        // 这里应该调用向量数据库（如Milvus、Pinecone等）进行相似度搜索
        // 目前返回空列表作为占位符
        return Collections.emptyList();
    }

    @Override
    public List<DocumentSegment> searchSimilarTexts(String queryText, String datasetId, int topK) {
        if (!StringUtils.hasText(queryText)) {
            return Collections.emptyList();
        }

        log.info("开始文本相似度搜索: datasetId={}, topK={}, queryLength={}",
                datasetId, topK, queryText.length());

        // 向量化查询文本
        float[] queryVector = embedText(queryText);

        // 搜索相似向量
        return searchSimilarVectors(queryVector, datasetId, topK);
    }

    @Override
    public int getVectorDimension() {
        return ragProperties.getEmbedding().getDimension();
    }

    @Override
    public Map<String, Object> getModelInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("model", ragProperties.getEmbedding().getModel());
        info.put("dimension", ragProperties.getEmbedding().getDimension());
        info.put("provider", ragProperties.getEmbedding().getProvider());
        info.put("available", isAvailable());
        return info;
    }

    @Override
    public boolean isAvailable() {
        try {
            // 测试向量化一个简单文本
            embedText("test");
            return true;
        } catch (Exception e) {
            log.warn("向量化服务不可用: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, Object> getEmbeddingStats(String datasetId) {
        // TODO: 实现向量化统计信息查询
        Map<String, Object> stats = new HashMap<>();
        stats.put("datasetId", datasetId);
        stats.put("totalVectors", 0);
        stats.put("dimension", getVectorDimension());
        stats.put("model", ragProperties.getEmbedding().getModel());
        return stats;
    }

    @Override
    public int deleteDocumentVectors(String documentId) {
        log.info("删除文档向量: documentId={}", documentId);
        // TODO: 实现文档向量删除
        return 0;
    }

    @Override
    public int deleteDatasetVectors(String datasetId) {
        log.info("删除知识库向量: datasetId={}", datasetId);
        // TODO: 实现知识库向量删除
        return 0;
    }

    @Override
    public boolean rebuildVectorIndex(String datasetId) {
        log.info("重建知识库向量索引: datasetId={}", datasetId);
        // TODO: 实现向量索引重建
        return true;
    }

    /**
     * 计算向量哈希值
     */
    private String calculateVectorHash(float[] vector) {
        if (vector == null || vector.length == 0) {
            return "";
        }

        // 简单的哈希计算，实际应该使用更好的哈希算法
        int hash = Arrays.hashCode(vector);
        return String.valueOf(Math.abs(hash));
    }
}
