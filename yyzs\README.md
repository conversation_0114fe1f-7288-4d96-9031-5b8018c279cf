# YYZS Agent Platform

## 项目概述

YYZS Agent Platform 是一个完整的 Elastic Stack 组件管理平台，提供了组件的自动化安装、配置、监控和运维功能。该平台采用前后端分离架构，后端基于 Spring Boot 3.5.3，前端基于 React + Next.js + TailwindCSS。

## 项目结构

```
yyzs/
├── agent/                     # 后端服务 (Spring Boot)
│   ├── src/main/java/         # Java源代码
│   ├── src/main/resources/    # 配置文件和资源
│   ├── pom.xml               # Maven配置
│   └── README.md             # 后端文档
├── web/                      # 前端应用 (React + Next.js)
│   ├── src/                  # 前端源代码
│   ├── public/               # 静态资源
│   ├── package.json          # npm配置
│   └── README.md             # 前端文档
└── README.md                 # 项目总体文档
```

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.5.3
- **数据库**: PostgreSQL + JPA + MyBatis Plus
- **缓存**: Redis
- **监控**: OSHI (系统监控)
- **文档**: Swagger/OpenAPI 3
- **构建**: Maven
- **Java版本**: 21

### 前端技术栈
- **框架**: Next.js 14 (React 18)
- **样式**: TailwindCSS 3.3
- **语言**: TypeScript 5.2
- **状态管理**: React Query
- **HTTP客户端**: Axios
- **图表**: Recharts
- **构建**: Next.js内置

## 主要功能

### 1. 组件管理
- **支持的组件**: Filebeat、Heartbeat、Metricbeat、Packetbeat、Winlogbeat、Auditbeat、Logstash、Elasticsearch、Kafka
- **安装包管理**: 上传、存储、版本管理
- **自动化安装**: 根据配置自动安装和配置组件
- **生命周期管理**: 启动、停止、重启、卸载
- **批量操作**: 支持批量启停和管理

### 2. 配置管理
- **配置模板**: 内置各组件的默认配置模板
- **配置生成**: 根据用户输入自动生成配置文件
- **配置验证**: 验证配置的正确性
- **配置备份**: 自动备份和恢复配置

### 3. 监控功能
- **实时监控**: CPU、内存、磁盘、网络使用情况
- **健康检查**: 组件运行状态和健康状态监控
- **性能统计**: 历史性能数据统计和分析
- **告警通知**: 异常情况自动告警
- **数据可视化**: 图表展示监控数据

### 4. 系统管理
- **系统资源**: 系统整体资源使用情况
- **日志管理**: 组件日志查看和管理
- **端口管理**: 端口占用检查和分配
- **自动启动**: 系统启动时自动启动组件

## 快速开始

### 环境要求
- Java 21+
- Node.js 18+
- PostgreSQL 12+
- Redis 6+
- Maven 3.8+

### 1. 克隆项目
```bash
git clone <repository-url>
cd yyzs
```

### 2. 启动后端服务

```bash
cd agent

# 配置数据库
# 修改 src/main/resources/application-dev.yml 中的数据库连接信息

# 启动后端服务
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 3. 启动前端应用

```bash
cd web

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 http://localhost:3001 启动

### 4. 访问应用

- **前端界面**: http://localhost:3001
- **API文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health

## API接口

### 组件管理 API
- `POST /api/components/upload` - 上传组件安装包
- `POST /api/components/{id}/install` - 安装组件
- `POST /api/components/{id}/start` - 启动组件
- `POST /api/components/{id}/stop` - 停止组件
- `GET /api/components` - 获取组件列表
- `GET /api/components/{id}` - 获取组件详情

### 监控管理 API
- `GET /api/monitor/components/{id}/latest` - 获取最新监控数据
- `GET /api/monitor/components/{id}/history` - 获取历史监控数据
- `GET /api/monitor/system/resources` - 获取系统资源使用情况
- `POST /api/monitor/start` - 启动监控
- `POST /api/monitor/stop` - 停止监控

详细的API文档请访问 Swagger UI。

## 部署指南

### Docker部署

#### 后端服务
```dockerfile
FROM openjdk:21-jdk-slim
COPY target/yyzs-agent-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 前端应用
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "start"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: yyzs_agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./agent
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis

  frontend:
    build: ./web
    ports:
      - "3001:3001"
    depends_on:
      - backend
```

## 开发指南

### 代码规范
- **Java**: 遵循阿里巴巴Java开发手册
- **TypeScript**: 使用ESLint和Prettier
- **Git**: 使用Conventional Commits规范

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

### 测试
```bash
# 后端测试
cd agent
mvn test

# 前端测试
cd web
npm run test
```

## 监控和运维

### 健康检查
- 后端: `/actuator/health`
- 前端: 通过API调用检查

### 日志管理
- 后端日志: `logs/yyzs-agent.log`
- 前端日志: 浏览器控制台

### 性能监控
- JVM监控: 通过Actuator端点
- 系统监控: 通过OSHI库
- 前端性能: 通过浏览器开发者工具

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证数据库连接配置

2. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证Redis连接配置

3. **前端API调用失败**
   - 检查后端服务是否启动
   - 验证API代理配置

4. **组件安装失败**
   - 检查安装目录权限
   - 验证组件包格式
   - 查看详细错误日志

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

Apache License 2.0

## 联系方式

- **邮箱**: <EMAIL>
- **网站**: https://www.yyzs.com
- **文档**: 详见各子项目的README文档

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础的组件管理功能
- 实现实时监控功能
- 提供完整的Web界面
