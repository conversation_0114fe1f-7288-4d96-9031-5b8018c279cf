# Dify令牌刷新问题修复方案 v2.1

## 问题描述

在调用Dify平台API时，当访问令牌过期（收到401错误）需要自动刷新令牌时，系统在响应式上下文中无法获取当前用户信息，导致令牌刷新失败。

### 错误日志（第一阶段）
```
-2025-08-21 15:47:57.194 - WARN 9284 --- [oundedElastic-2] c.x.m.dify.config.DifyWebClientConfig    : 未获取到用户信息，可能未登录或在响应式上下文中
-2025-08-21 15:47:57.197 -DEBUG 9284 --- [nio-8000-exec-9] m.m.a.RequestResponseBodyMethodProcessor : Nothing to write: null body
调用dify接口令牌过期，无法刷新令牌
```

### 错误日志（第二阶段）
```
-2025-08-21 16:43:49.143 - INFO 6360 --- [ctor-http-nio-2] c.x.m.dify.config.DifyWebClientConfig    : 平台5668647c8ac87db478939ddd8810c098令牌刷新成功，重试API调用
-2025-08-21 16:43:49.143 -ERROR 6360 --- [ctor-http-nio-2] c.x.m.dify.config.DifyWebClientConfig    : 平台5668647c8ac87db478939ddd8810c098令牌刷新过程失败: 您当前没有登录
com.xhcai.common.core.exception.BusinessException: 您当前没有登录
	at com.xhcai.common.security.utils.SecurityUtils.getCurrentUser(SecurityUtils.java:45)
	at com.xhcai.modules.dify.service.impl.DifyMultiPlatformAuthServiceImpl.getCurrentUserValidAccessToken(DifyMultiPlatformAuthServiceImpl.java:122)
```

## 根本原因分析

### 第一阶段问题
1. **响应式上下文问题**：在Spring WebFlux的响应式链中，`SecurityContextHolder` 无法正确传递用户信息
2. **用户信息获取失败**：`getCurrentUserIdForContext()` 方法只从 `SecurityContext` 获取用户信息，在响应式上下文中会失败
3. **令牌刷新中断**：无法获取用户ID导致令牌刷新流程中断

### 第二阶段问题（修复第一阶段后出现）
1. **令牌刷新成功但重试失败**：令牌刷新本身成功，但在重试API调用时又出现"您当前没有登录"错误
2. **重复获取用户信息**：重试时调用 `makeAuthenticatedGetRequestWithRetryForPlatform` 方法，该方法内部又调用 `getCurrentUserValidAccessToken`，再次尝试从 `SecurityContext` 获取用户信息
3. **响应式链中的用户信息丢失**：在响应式链的深层调用中，`SecurityContext` 无法访问

## 解决方案

### 1. 优化用户信息获取机制

修改 `getCurrentUserIdForContext()` 方法，优先从Reactor Context获取用户ID：

```java
private Mono<String> getCurrentUserIdForContext() {
    return Mono.deferContextual(contextView -> {
        // 首先尝试从Reactor Context获取用户ID
        String userId = contextView.getOrDefault("userId", null);
        
        if (StringUtils.hasText(userId)) {
            log.debug("从Reactor Context获取到用户ID: {}", userId);
            return Mono.just(userId);
        }
        
        // 如果Context中没有，尝试从SecurityContext获取
        return Mono.fromCallable(() -> {
            try {
                LoginUser currentUser = SecurityUtils.getCurrentUserSafely();
                if (currentUser != null && StringUtils.hasText(currentUser.getUserId())) {
                    return currentUser.getUserId();
                }
            } catch (Exception e) {
                log.error("获取当前用户ID失败: {}", e.getMessage(), e);
            }
            return null;
        }).subscribeOn(Schedulers.boundedElastic());
    });
}
```

### 2. 增强错误处理机制

改进令牌刷新失败时的错误处理：

```java
.flatMap(userId -> {
    if (StringUtils.hasText(userId)) {
        log.info("使用用户ID {}刷新平台{}的令牌", userId, platformId);
        return difyMultiPlatformAuthService.handleCurrentUserUnauthorized(platformId)
                .contextWrite(context -> context.put("userId", userId));
    } else {
        log.error("无法获取用户ID，无法刷新平台{}的令牌", platformId);
        return Mono.error(new RuntimeException("用户未登录或无法获取用户信息，无法刷新令牌"));
    }
})
.onErrorResume(refreshError -> {
    log.error("平台{}令牌刷新过程失败: {}", platformId, refreshError.getMessage(), refreshError);
    return Mono.error(new RuntimeException("调用dify接口令牌过期，无法刷新令牌: " + refreshError.getMessage(), refreshError));
});
```

### 3. 修复重试逻辑

创建专用的API调用方法，直接使用刷新后的令牌：

```java
.flatMap(newToken -> {
    if (StringUtils.hasText(newToken)) {
        log.info("平台{}令牌刷新成功，使用新令牌重试API调用", platformId);
        // 直接使用新令牌发起请求，避免重新获取用户信息
        return callDifyApiWithAuthTokenForPlatform(platformId, uri, newToken, responseType);
    } else {
        log.error("平台{}令牌刷新失败：新令牌为空", platformId);
        return Mono.error(new RuntimeException("令牌刷新失败：新令牌为空"));
    }
})
```

### 4. 新增专用API调用方法

```java
private <T> Mono<T> callDifyApiWithAuthTokenForPlatform(String platformId, String uri, String accessToken, Class<T> responseType) {
    // 获取平台配置并直接使用提供的令牌发起请求
    // 避免重新获取用户信息和令牌
}
```

### 5. 确保上下文传递

在Controller层确保用户ID正确传递到响应式上下文：

```java
@GetMapping("/list/{platformId}")
@RequiresPermissions("dify:agent:view")
public Mono<Result<DifyAppsListResponseDTO>> getAgentsListByPlatform(
        @PathVariable String platformId,
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "30") Integer limit,
        @RequestParam(required = false) String name,
        @RequestParam(defaultValue = "true") Boolean isCreatedByMe) {

    // 在Controller层获取用户信息，然后传递到响应式上下文中
    String userId = SecurityUtils.getCurrentUserId();
    if (userId == null || userId.trim().isEmpty()) {
        return Mono.just(Result.fail("用户未登录"));
    }

    return agentService.getAgentsList(platformId, page, limit, name, isCreatedByMe)
            .contextWrite(context -> context.put("userId", userId));
}
```

## 修复效果

1. **用户信息获取**：优先从Reactor Context获取用户ID，确保在响应式链中能够正确获取用户信息
2. **令牌自动刷新**：当令牌过期时，系统能够自动刷新令牌并重试API调用
3. **错误处理**：提供更清晰的错误信息，便于问题排查
4. **上下文传递**：确保用户信息在整个响应式链中正确传递

## 测试验证

创建了相应的单元测试来验证修复效果：

- `testGetCurrentUserIdFromReactorContext()` - 测试从Reactor Context获取用户ID
- `testGetCurrentUserIdFromSecurityContext()` - 测试从SecurityContext获取用户ID
- `testGetCurrentUserIdWhenNoUserAvailable()` - 测试无用户信息时的处理
- `testTokenRefreshWithValidUserId()` - 测试令牌刷新流程

## 修复验证

修复已通过编译验证，主要改动包括：

1. **优化用户信息获取机制**：`getCurrentUserIdForContext()` 方法现在优先从Reactor Context获取用户ID
2. **增强错误处理**：提供更清晰的错误信息和异常处理
3. **确保上下文传递**：Controller层正确传递用户ID到响应式上下文

## 部署说明

1. 重新编译并部署 `xhcai-dify` 模块：
   ```bash
   mvn clean compile -pl modules/xhcai-dify
   ```
2. 重启应用服务
3. 测试Dify API调用，验证令牌自动刷新功能

## 预期效果

修复后，当调用 `/api/dify/agents/list/{platformId}` 接口时：

1. 如果令牌有效，正常返回数据
2. 如果令牌过期（401错误），系统会自动：
   - 从响应式上下文获取用户ID
   - 调用令牌刷新接口
   - 使用新令牌重试API调用
   - 返回正确的数据

不再出现 "调用dify接口令牌过期，无法刷新令牌" 的错误。

## 注意事项

1. 确保在所有调用Dify API的地方都正确传递用户上下文
2. 监控日志，确认令牌刷新流程正常工作
3. 如果仍有问题，检查用户认证状态和权限配置

## 相关文件

- `modules/xhcai-dify/src/main/java/com/xhcai/modules/dify/config/DifyWebClientConfig.java` - 主要修复文件
- `modules/xhcai-dify/src/main/java/com/xhcai/modules/dify/controller/DifyAgentController.java` - 上下文传递
- `docs/dify-token-refresh-fix-v2.md` - 本修复文档
