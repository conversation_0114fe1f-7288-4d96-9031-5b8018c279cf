<template>
  <el-dialog
    v-model="visible"
    title="AI 探索对话"
    width="80%"
    :before-close="handleClose"
    class="explore-dialog"
    :close-on-click-modal="false"
  >
    <div class="dialog-content h-96 flex flex-col">
      <!-- 消息列表 -->
      <div class="messages-container flex-1 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
        <div v-if="messages.length === 0" class="text-center text-gray-500 mt-8">
          <div class="text-4xl mb-4">🤖</div>
          <p>开始与AI对话吧！</p>
        </div>
        
        <div v-for="message in messages" :key="message.id" class="message-item mb-4">
          <!-- 用户消息 -->
          <div v-if="message.role === 'user'" class="flex justify-end">
            <div class="bg-blue-500 text-white px-4 py-2 rounded-lg max-w-xs lg:max-w-md">
              {{ message.content }}
            </div>
          </div>
          
          <!-- AI消息 -->
          <div v-else class="flex justify-start">
            <div class="bg-white px-4 py-2 rounded-lg max-w-xs lg:max-w-md shadow-sm border">
              <div v-if="message.streaming" class="flex items-center">
                <span>{{ message.content }}</span>
                <span class="ml-2 animate-pulse">|</span>
              </div>
              <div v-else>{{ message.content }}</div>
            </div>
          </div>
        </div>
        
        <!-- 思考状态 -->
        <div v-if="isThinking" class="flex justify-start mb-4">
          <div class="bg-white px-4 py-2 rounded-lg shadow-sm border">
            <div class="flex items-center">
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
              <span class="ml-2 text-gray-600">AI正在思考...</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="input-area">
        <div class="flex gap-2">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="2"
            placeholder="输入您的问题..."
            :disabled="isSending"
            @keydown.enter.prevent="handleSend"
            class="flex-1"
          />
          <el-button
            type="primary"
            :loading="isSending"
            :disabled="!inputMessage.trim()"
            @click="handleSend"
            class="self-end"
          >
            发送
          </el-button>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          <span v-if="conversationId">会话ID: {{ conversationId.substring(0, 8) }}...</span>
        </div>
        <div>
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="danger" @click="clearMessages" :disabled="messages.length === 0">
            清空对话
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { exploreApi } from '@/api/explore'

// 定义props
interface Props {
  modelValue: boolean
  appId?: string
  agentId?: string
}

const props = withDefaults(defineProps<Props>(), {
  appId: '3778735c-cc1a-41e3-988f-7a108e3eafb0',
  agentId: ''
})

// 定义emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 消息接口
interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  streaming?: boolean
  timestamp: Date
  backendMessageId?: string
}

// 状态管理
const messages = ref<Message[]>([])
const inputMessage = ref('')
const isSending = ref(false)
const isThinking = ref(false)
const conversationId = ref('')
const backendConversationId = ref('')

// 生成消息ID
const generateMessageId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

// 发送消息
const handleSend = async () => {
  if (!inputMessage.value.trim() || isSending.value) return

  const userMessage: Message = {
    id: generateMessageId(),
    role: 'user',
    content: inputMessage.value.trim(),
    timestamp: new Date()
  }

  // 添加用户消息
  messages.value.push(userMessage)
  const currentInput = inputMessage.value.trim()
  inputMessage.value = ''
  
  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 开始发送
  isSending.value = true
  isThinking.value = true

  try {
    // 创建AI消息
    const aiMessage: Message = {
      id: generateMessageId(),
      role: 'assistant',
      content: '',
      streaming: true,
      timestamp: new Date()
    }
    
    // 延迟添加AI消息，模拟思考时间
    setTimeout(() => {
      isThinking.value = false
      messages.value.push(aiMessage)
      scrollToBottom()
    }, 1000)

    // 获取最后一条AI回复消息ID作为parent_message_id（优先使用后端ID）
    const getLastMessageId = () => {
      if (messages.value.length === 0) {
        console.log('ExploreDialogModal: 没有消息历史，返回null')
        return null
      }

      // 从后往前查找最后一条AI回复消息
      for (let i = messages.value.length - 1; i >= 0; i--) {
        const message = messages.value[i]
        if (message.role === 'assistant') {
          console.log('ExploreDialogModal: 找到最后一条AI消息:', {
            localId: message.id,
            backendId: message.backendMessageId,
            content: message.content.substring(0, 50) + '...'
          })
          return message.backendMessageId || message.id
        }
      }

      console.log('ExploreDialogModal: 没有找到AI回复消息，返回null')
      return null
    }

    const parentMessageId = getLastMessageId()
    const useConversationId = backendConversationId.value || conversationId.value

    console.log('ExploreDialogModal 流式聊天参数:', {
      conversationId: conversationId.value,
      backendConversationId: backendConversationId.value,
      useConversationId,
      parentMessageId: parentMessageId ? `${parentMessageId} (最后一条AI回复的ID)` : null,
      messagesCount: messages.value.length
    })

    // 调用API
    await exploreApi.streamChat({
      response_mode: 'streaming',
      conversation_id: useConversationId,
      files: [],
      query: currentInput,
      inputs: {
        xuqiu: {
          type: 'document',
          transfer_method: 'local_file',
          url: '',
          upload_file_id: 'f28173e3-0a8a-4863-814c-794cdc648c80'
        }
      },
      parent_message_id: parentMessageId,
      appId: props.appId,
      agentId: props.agentId || ''
    }, {
      onMessage: (data: any) => {
        handleStreamMessage(aiMessage, data)
      },
      onError: (error: any) => {
        console.error('流式聊天错误:', error)
        aiMessage.content = '抱歉，发生了错误，请重试。'
        aiMessage.streaming = false
        ElMessage.error('发送失败，请重试')
      },
      onComplete: () => {
        aiMessage.streaming = false
        console.log('流式聊天完成')
      }
    })

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    isSending.value = false
    isThinking.value = false
  }
}

// 处理流式消息
const handleStreamMessage = (aiMessage: Message, data: any) => {
  try {
    // 移除流式数据的控制台输出，避免打印样例数据
    // if (import.meta.env.DEV) {
    //   console.log('收到流式数据:', data)
    // }

    // 解析流式数据
    if (data.event === 'workflow_started') {
      // 工作流开始
      if (data.conversation_id) {
        backendConversationId.value = data.conversation_id
        conversationId.value = data.conversation_id
        console.log('设置会话ID:', data.conversation_id)
      }
    } else if (data.event === 'node_started') {
      // 节点开始
      console.log('节点开始:', data.data?.title || data.data?.node_type)
    } else if (data.event === 'node_finished') {
      // 节点完成
      console.log('节点完成:', data.data?.title || data.data?.node_type)
    } else if (data.event === 'message') {
      // 消息内容
      if (data.answer) {
        aiMessage.content += data.answer
        scrollToBottom()
      }
    } else if (data.event === 'workflow_finished') {
      // 工作流完成
      aiMessage.streaming = false
      console.log('工作流完成')
    } else if (data.event === 'message_end') {
      // 消息结束
      aiMessage.streaming = false
      console.log('ExploreDialogModal 消息结束，消息ID:', data.message_id, '元数据:', data.metadata)
      // 在消息完全生成后保存AI回复消息的ID（更可靠）
      if (data.message_id) {
        console.log('ExploreDialogModal 保存前:', {
          localId: aiMessage.id,
          oldBackendId: aiMessage.backendMessageId,
          newBackendId: data.message_id
        })
        aiMessage.backendMessageId = data.message_id
        console.log('ExploreDialogModal 保存AI回复消息ID成功:', data.message_id)
        console.log('ExploreDialogModal 保存后验证:', {
          localId: aiMessage.id,
          backendId: aiMessage.backendMessageId
        })
      } else {
        console.warn('ExploreDialogModal message_end事件中没有message_id字段')
      }
    }
  } catch (error) {
    console.error('解析流式数据失败:', error)
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    const container = document.querySelector('.messages-container')
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  })
}

// 清空消息
const clearMessages = () => {
  messages.value = []
  conversationId.value = ''
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 监听对话框打开状态
watch(visible, (newVal) => {
  if (newVal) {
    // 对话框打开时，聚焦到输入框
    nextTick(() => {
      const input = document.querySelector('.input-area textarea') as HTMLTextAreaElement
      if (input) {
        input.focus()
      }
    })
  }
})
</script>

<style scoped>
.explore-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.explore-dialog :deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.explore-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.explore-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
}

.messages-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

.message-item {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-area textarea {
  resize: none;
}
</style>
