<template>
  <div class="edge-details">
    <!-- 关系基本信息 -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
      <div class="flex items-center gap-3 mb-3">
        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white">
          🔗
        </div>
        <div>
          <h4 class="font-medium text-gray-900">{{ edge.label || '关系' }}</h4>
          <p class="text-sm text-gray-500">{{ getRelationshipTypeName(edge.data?.relationship) }}</p>
        </div>
      </div>
      
      <!-- 关系ID -->
      <div class="text-xs text-gray-500">
        <span class="font-medium">ID:</span> {{ edge.id }}
      </div>
    </div>

    <!-- 连接的节点 -->
    <div class="mb-4">
      <h5 class="font-medium text-gray-900 mb-3">连接节点</h5>
      <div class="space-y-3">
        <!-- 源节点 -->
        <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
          <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
            📤
          </div>
          <div class="flex-1">
            <div class="font-medium text-gray-900">源节点</div>
            <div class="text-sm text-gray-600">{{ getNodeLabel(edge.source) }}</div>
          </div>
        </div>

        <!-- 关系箭头 -->
        <div class="flex justify-center">
          <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
          </div>
        </div>

        <!-- 目标节点 -->
        <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
          <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
            📥
          </div>
          <div class="flex-1">
            <div class="font-medium text-gray-900">目标节点</div>
            <div class="text-sm text-gray-600">{{ getNodeLabel(edge.target) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关系属性 -->
    <div v-if="edge.data && Object.keys(edge.data).length > 0" class="mb-4">
      <h5 class="font-medium text-gray-900 mb-3">关系属性</h5>
      <div class="space-y-2">
        <div
          v-for="(value, key) in edge.data"
          :key="key"
          class="flex justify-between items-start p-2 bg-gray-50 rounded"
        >
          <span class="text-sm font-medium text-gray-600 capitalize">{{ key }}:</span>
          <span class="text-sm text-gray-900 text-right flex-1 ml-2">{{ formatValue(value) }}</span>
        </div>
      </div>
    </div>

    <!-- 关系强度 -->
    <div v-if="edge.data?.weight" class="mb-4">
      <h5 class="font-medium text-gray-900 mb-3">关系强度</h5>
      <div class="flex items-center gap-3">
        <div class="flex-1 bg-gray-200 rounded-full h-2">
          <div
            class="bg-purple-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${(edge.data.weight * 100)}%` }"
          ></div>
        </div>
        <span class="text-sm font-medium text-gray-900">{{ Math.round(edge.data.weight * 100) }}%</span>
      </div>
    </div>

    <!-- 关系统计 -->
    <div class="mb-4">
      <h5 class="font-medium text-gray-900 mb-3">统计信息</h5>
      <div class="grid grid-cols-2 gap-3">
        <div class="text-center p-3 bg-purple-50 rounded-lg">
          <div class="text-lg font-semibold text-purple-600">{{ getRelationshipCount() }}</div>
          <div class="text-xs text-purple-500">同类关系</div>
        </div>
        <div class="text-center p-3 bg-orange-50 rounded-lg">
          <div class="text-lg font-semibold text-orange-600">{{ getConfidenceScore() }}%</div>
          <div class="text-xs text-orange-500">置信度</div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <button
        @click="$emit('editEdge', edge)"
        class="flex-1 px-3 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
      >
        编辑
      </button>
      <button
        @click="$emit('deleteEdge', edge)"
        class="px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
      >
        删除
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Edge } from '@vue-flow/core'

interface Props {
  edge: Edge
}

interface Emits {
  (e: 'editEdge', edge: Edge): void
  (e: 'deleteEdge', edge: Edge): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 工具函数
const getRelationshipTypeName = (type?: string): string => {
  const typeMap: Record<string, string> = {
    contains: '包含',
    implements: '实现',
    relates: '关联',
    depends: '依赖',
    inherits: '继承',
    uses: '使用'
  }
  return typeMap[type || ''] || type || '未知关系'
}

const getNodeLabel = (nodeId: string): string => {
  // 这里应该从图谱数据中获取节点标签
  // 暂时返回节点ID
  return `节点 ${nodeId}`
}

const formatValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  if (typeof value === 'number' && value < 1) {
    return (value * 100).toFixed(1) + '%'
  }
  return String(value)
}

const getRelationshipCount = (): number => {
  // 这里应该从图谱数据中计算同类关系数量
  return Math.floor(Math.random() * 20) + 1
}

const getConfidenceScore = (): number => {
  // 这里应该从关系数据中获取置信度分数
  return Math.floor(Math.random() * 40) + 60
}
</script>
