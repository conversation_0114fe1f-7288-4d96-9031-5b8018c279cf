'use client';

import { useState, useEffect } from 'react';
import { ComponentType } from '@/types/component';
import { Settings, Plus, Minus, AlertCircle } from 'lucide-react';

interface ComponentConfigEditorProps {
  componentType: ComponentType;
  config: Record<string, any>;
  onChange: (config: Record<string, any>) => void;
  className?: string;
}

// 组件默认配置模板
const DEFAULT_CONFIGS: Record<ComponentType, Record<string, any>> = {
  filebeat: {
    installPath: '/opt/elastic/filebeat',
    port: 5044,
    host: 'localhost',
    autoStart: true,
    inputs: [
      {
        type: 'log',
        enabled: true,
        paths: ['/var/log/*.log']
      }
    ],
    output: {
      elasticsearch: {
        hosts: ['localhost:9200']
      }
    }
  },
  heartbeat: {
    installPath: '/opt/elastic/heartbeat',
    port: 5066,
    host: 'localhost',
    autoStart: true,
    monitors: [
      {
        type: 'http',
        urls: ['http://localhost:9200'],
        schedule: '@every 10s'
      }
    ]
  },
  metricbeat: {
    installPath: '/opt/elastic/metricbeat',
    port: 5067,
    host: 'localhost',
    autoStart: true,
    modules: [
      {
        module: 'system',
        metricsets: ['cpu', 'memory', 'network', 'process'],
        enabled: true,
        period: '10s'
      }
    ]
  },
  packetbeat: {
    installPath: '/opt/elastic/packetbeat',
    port: 5068,
    host: 'localhost',
    autoStart: true,
    interfaces: {
      device: 'any'
    },
    protocols: {
      http: {
        ports: [80, 8080, 8000, 5000, 8002]
      }
    }
  },
  winlogbeat: {
    installPath: '/opt/elastic/winlogbeat',
    port: 5069,
    host: 'localhost',
    autoStart: true,
    event_logs: [
      {
        name: 'Application'
      },
      {
        name: 'System'
      },
      {
        name: 'Security'
      }
    ]
  },
  auditbeat: {
    installPath: '/opt/elastic/auditbeat',
    port: 5070,
    host: 'localhost',
    autoStart: true,
    modules: [
      {
        module: 'file_integrity',
        paths: ['/bin', '/usr/bin', '/sbin', '/usr/sbin', '/etc']
      }
    ]
  },
  logstash: {
    installPath: '/opt/elastic/logstash',
    port: 5044,
    host: 'localhost',
    autoStart: true,
    pipeline: {
      workers: 2,
      batch: {
        size: 125,
        delay: 50
      }
    },
    input: {
      beats: {
        port: 5044
      }
    },
    output: {
      elasticsearch: {
        hosts: ['localhost:9200']
      }
    }
  },
  elasticsearch: {
    installPath: '/opt/elastic/elasticsearch',
    port: 9200,
    host: 'localhost',
    autoStart: true,
    cluster: {
      name: 'elasticsearch'
    },
    node: {
      name: 'node-1'
    },
    network: {
      host: '0.0.0.0'
    },
    discovery: {
      type: 'single-node'
    }
  },
  kafka: {
    installPath: '/opt/kafka',
    port: 9092,
    host: 'localhost',
    autoStart: true,
    server: {
      properties: {
        'broker.id': 0,
        'listeners': 'PLAINTEXT://localhost:9092',
        'log.dirs': '/tmp/kafka-logs',
        'num.network.threads': 3,
        'num.io.threads': 8,
        'socket.send.buffer.bytes': 102400,
        'socket.receive.buffer.bytes': 102400,
        'socket.request.max.bytes': 104857600
      }
    }
  }
};

export default function ComponentConfigEditor({
  componentType,
  config,
  onChange,
  className = ''
}: ComponentConfigEditorProps) {
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced'>('basic');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 初始化默认配置
  useEffect(() => {
    if (Object.keys(config).length === 0) {
      const defaultConfig = DEFAULT_CONFIGS[componentType] || {};
      onChange(defaultConfig);
    }
  }, [componentType, config, onChange]);

  // 验证配置
  const validateConfig = (newConfig: Record<string, any>) => {
    const newErrors: Record<string, string> = {};

    // 基本验证
    if (!newConfig.installPath) {
      newErrors.installPath = '安装路径不能为空';
    }

    if (newConfig.port && (newConfig.port < 1 || newConfig.port > 65535)) {
      newErrors.port = '端口号必须在 1-65535 之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 更新配置
  const updateConfig = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    if (validateConfig(newConfig)) {
      onChange(newConfig);
    }
  };

  // 渲染基本配置
  const renderBasicConfig = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="form-label">安装路径 *</label>
          <input
            type="text"
            value={config.installPath || ''}
            onChange={(e) => updateConfig('installPath', e.target.value)}
            className={`form-input ${errors.installPath ? 'border-error-300' : ''}`}
            placeholder="/opt/elastic"
          />
          {errors.installPath && (
            <p className="form-error">{errors.installPath}</p>
          )}
        </div>

        <div>
          <label className="form-label">端口</label>
          <input
            type="number"
            value={config.port || ''}
            onChange={(e) => updateConfig('port', parseInt(e.target.value) || '')}
            className={`form-input ${errors.port ? 'border-error-300' : ''}`}
            placeholder="默认端口"
            min="1"
            max="65535"
          />
          {errors.port && (
            <p className="form-error">{errors.port}</p>
          )}
        </div>
      </div>

      <div>
        <label className="form-label">主机地址</label>
        <input
          type="text"
          value={config.host || ''}
          onChange={(e) => updateConfig('host', e.target.value)}
          className="form-input"
          placeholder="localhost"
        />
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="autoStart"
            checked={config.autoStart || false}
            onChange={(e) => updateConfig('autoStart', e.target.checked)}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label htmlFor="autoStart" className="ml-2 text-sm text-gray-700">
            安装完成后自动启动
          </label>
        </div>
      </div>

      {/* 组件特定配置 */}
      {componentType === 'elasticsearch' && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900">Elasticsearch 配置</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">集群名称</label>
              <input
                type="text"
                value={config.cluster?.name || ''}
                onChange={(e) => updateConfig('cluster', { ...config.cluster, name: e.target.value })}
                className="form-input"
                placeholder="elasticsearch"
              />
            </div>
            <div>
              <label className="form-label">节点名称</label>
              <input
                type="text"
                value={config.node?.name || ''}
                onChange={(e) => updateConfig('node', { ...config.node, name: e.target.value })}
                className="form-input"
                placeholder="node-1"
              />
            </div>
          </div>
        </div>
      )}

      {componentType === 'kafka' && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900">Kafka 配置</h4>
          <div>
            <label className="form-label">Broker ID</label>
            <input
              type="number"
              value={config.server?.properties?.['broker.id'] || ''}
              onChange={(e) => updateConfig('server', {
                ...config.server,
                properties: {
                  ...config.server?.properties,
                  'broker.id': parseInt(e.target.value) || 0
                }
              })}
              className="form-input"
              placeholder="0"
            />
          </div>
        </div>
      )}
    </div>
  );

  // 渲染高级配置
  const renderAdvancedConfig = () => (
    <div className="space-y-4">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
          <p className="text-sm text-yellow-800">
            高级配置需要对 {componentType} 有深入了解，错误的配置可能导致组件无法正常工作。
          </p>
        </div>
      </div>

      <div>
        <label className="form-label">JSON 配置</label>
        <textarea
          value={JSON.stringify(config, null, 2)}
          onChange={(e) => {
            try {
              const newConfig = JSON.parse(e.target.value);
              if (validateConfig(newConfig)) {
                onChange(newConfig);
              }
            } catch (error) {
              // 忽略 JSON 解析错误，用户可能正在编辑
            }
          }}
          className="form-input font-mono text-sm"
          rows={15}
          placeholder="JSON 配置..."
        />
        <p className="mt-1 text-sm text-gray-500">
          直接编辑 JSON 配置，请确保格式正确
        </p>
      </div>
    </div>
  );

  return (
    <div className={className}>
      <div className="flex items-center mb-4">
        <Settings className="h-5 w-5 text-gray-600 mr-2" />
        <h4 className="text-lg font-medium text-gray-900">组件配置</h4>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('basic')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'basic'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            基本配置
          </button>
          <button
            onClick={() => setActiveTab('advanced')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'advanced'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            高级配置
          </button>
        </nav>
      </div>

      {/* 配置内容 */}
      {activeTab === 'basic' ? renderBasicConfig() : renderAdvancedConfig()}
    </div>
  );
}
