<template>
  <div
    class="concept-node bg-white border-2 rounded-lg p-3 min-w-32 shadow-sm hover:shadow-md transition-all duration-200"
    :class="[
      selected ? 'border-green-500 bg-green-50' : 'border-green-200',
      'hover:border-green-400'
    ]"
  >
    <!-- 节点图标和标题 -->
    <div class="flex items-center gap-2 mb-2">
      <div class="w-6 h-6 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
        <span class="text-white text-xs">{{ getConceptIcon(data.type) }}</span>
      </div>
      <div class="flex-1 min-w-0">
        <h4 class="font-medium text-gray-900 text-sm truncate">{{ data.label }}</h4>
        <p class="text-xs text-gray-500">{{ getConceptTypeName(data.type) }}</p>
      </div>
    </div>

    <!-- 描述 -->
    <div v-if="data.properties?.description" class="text-xs text-gray-600 mb-2">
      <p class="line-clamp-2">{{ data.properties.description }}</p>
    </div>

    <!-- 其他属性 -->
    <div v-if="data.properties && hasOtherProperties" class="text-xs text-gray-600">
      <div
        v-for="(value, key) in getOtherProperties(data.properties)"
        :key="key"
        class="flex justify-between items-center"
      >
        <span class="text-gray-500">{{ key }}:</span>
        <span class="font-medium truncate ml-1">{{ value }}</span>
      </div>
    </div>

    <!-- 连接点 -->
    <div class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-top"></div>
    <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-bottom"></div>
    <div class="absolute -top-1 -left-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-left"></div>
    <div class="absolute -bottom-1 -left-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white vue-flow__handle vue-flow__handle-right"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  data: {
    label: string
    type: string
    properties?: Record<string, any>
  }
  selected?: boolean
}

const props = defineProps<Props>()

// 计算属性
const hasOtherProperties = computed(() => {
  if (!props.data.properties) return false
  const otherProps = getOtherProperties(props.data.properties)
  return Object.keys(otherProps).length > 0
})

// 工具函数
const getConceptIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    concept: '💡',
    category: '📂',
    topic: '🎯',
    theme: '🎨',
    domain: '🌐',
    field: '📊',
    default: '🟢'
  }
  return iconMap[type] || iconMap.default
}

const getConceptTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    concept: '概念',
    category: '类别',
    topic: '主题',
    theme: '主题',
    domain: '领域',
    field: '字段'
  }
  return typeMap[type] || type
}

const getOtherProperties = (properties: Record<string, any>): Record<string, any> => {
  // 排除description，显示其他属性的前2个
  const filtered = Object.entries(properties)
    .filter(([key]) => key !== 'description')
    .slice(0, 2)
  return Object.fromEntries(filtered)
}
</script>

<style scoped>
.concept-node {
  position: relative;
  transform: translateY(0);
}

.concept-node:hover {
  transform: translateY(-1px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.vue-flow__handle {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  border: 2px solid white;
}

.vue-flow__handle-top {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.vue-flow__handle-bottom {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.vue-flow__handle-left {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.vue-flow__handle-right {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}
</style>
