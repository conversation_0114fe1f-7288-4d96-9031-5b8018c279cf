import apiClient from '@/utils/apiClient'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

// 向量数据库相关类型定义
export interface VectorDatabaseVO {
  id: string
  name: string
  type: string
  typeName?: string
  host: string
  port: number
  databaseName?: string
  username?: string
  password?: string
  connectionConfig?: string
  indexConfig?: string
  status: string
  isDefault: string
  description?: string
  icon?: string
  iconColor?: string
  poolConfig?: string
  lastTestTime?: string
  lastTestResult?: string
  testErrorMessage?: string
  tenantId?: string
  remark?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  deleted?: number
}

export interface VectorDatabaseQueryDTO {
  pageNum?: number
  pageSize?: number
  name?: string
  type?: string
  host?: string
  status?: string
  isDefault?: string
  keyword?: string
  startTime?: string
  endTime?: string
}

export interface VectorDatabaseCreateDTO {
  name: string
  type: string
  host: string
  port: number
  databaseName?: string
  username?: string
  password?: string
  connectionConfig?: string
  indexConfig?: string
  status?: string
  isDefault?: string
  description?: string
  icon?: string
  iconColor?: string
  poolConfig?: string
  remark?: string
  // SSL配置
  sslEnabled?: string
  caCertPath?: string
  clientCert?: string
  clientKey?: string
  // API配置
  apiKey?: string
  token?: string
  // 超时配置
  connectionTimeout?: number
  readTimeout?: number
}

export interface VectorDatabaseUpdateDTO {
  name?: string
  type?: string
  host?: string
  port?: number
  databaseName?: string
  username?: string
  password?: string
  connectionConfig?: string
  indexConfig?: string
  status?: string
  isDefault?: string
  description?: string
  icon?: string
  iconColor?: string
  poolConfig?: string
  remark?: string
}

export interface ConnectionTestResult {
  success: boolean
  message: string
  testTime?: string
  testResult?: string
}

export interface VectorDatabaseStatistics {
  id: string
  name: string
  type: string
  status: string
  lastTestTime?: string
  lastTestResult?: string
  indexCount: number
  documentCount: number
  storageSize: string
}

// 向量数据库API
export const VectorDatabaseAPI = {
  /**
   * 分页查询向量数据库列表
   */
  getVectorDatabasePage(params: VectorDatabaseQueryDTO): Promise<ApiResponse<PaginatedResponse<VectorDatabaseVO>>> {
    return apiClient.get('/api/rag/vector-database/page', { params })
  },

  /**
   * 查询向量数据库列表
   */
  getVectorDatabaseList(params?: VectorDatabaseQueryDTO): Promise<ApiResponse<VectorDatabaseVO[]>> {
    return apiClient.get('/api/rag/vector-database/list', { params })
  },

  /**
   * 查询启用的向量数据库列表
   */
  getEnabledVectorDatabases(): Promise<ApiResponse<VectorDatabaseVO[]>> {
    return apiClient.get('/api/rag/vector-database/enabled')
  },

  /**
   * 查询默认向量数据库
   */
  getDefaultVectorDatabase(): Promise<ApiResponse<VectorDatabaseVO>> {
    return apiClient.get('/api/rag/vector-database/default')
  },

  /**
   * 根据ID查询向量数据库详情
   */
  getVectorDatabaseById(id: string): Promise<ApiResponse<VectorDatabaseVO>> {
    return apiClient.get(`/api/rag/vector-database/${id}`)
  },

  /**
   * 创建向量数据库
   */
  createVectorDatabase(data: VectorDatabaseCreateDTO): Promise<ApiResponse<boolean>> {
    return apiClient.post('/api/rag/vector-database', data)
  },

  /**
   * 更新向量数据库
   */
  updateVectorDatabase(id: string, data: VectorDatabaseUpdateDTO): Promise<ApiResponse<boolean>> {
    return apiClient.put(`/api/rag/vector-database/${id}`, data)
  },

  /**
   * 删除向量数据库
   */
  deleteVectorDatabases(ids: string[]): Promise<ApiResponse<boolean>> {
    return apiClient.delete(`/api/rag/vector-database/${ids.join(',')}`)
  },

  /**
   * 批量更新状态
   */
  batchUpdateStatus(ids: string[], status: string): Promise<ApiResponse<boolean>> {
    return apiClient.put(`/api/rag/vector-database/status/${ids.join(',')}/${status}`)
  },

  /**
   * 设置默认向量数据库
   */
  setDefaultVectorDatabase(id: string): Promise<ApiResponse<boolean>> {
    return apiClient.put(`/api/rag/vector-database/default/${id}`)
  },

  /**
   * 测试向量数据库连接（使用ID）
   */
  testConnectionById(id: string): Promise<ApiResponse<ConnectionTestResult>> {
    return apiClient.post(`/api/rag/vector-database/${id}/test`)
  },

  /**
   * 测试向量数据库连接（使用配置）
   */
  testConnection(data: VectorDatabaseCreateDTO): Promise<ApiResponse<ConnectionTestResult>> {
    return apiClient.post('/api/rag/vector-database/test', data)
  },

  /**
   * 查询向量数据库统计信息
   */
  getStatistics(id: string): Promise<ApiResponse<VectorDatabaseStatistics>> {
    return apiClient.get(`/api/rag/vector-database/${id}/statistics`)
  },

  /**
   * 初始化向量数据库
   */
  initializeVectorDatabase(id: string): Promise<ApiResponse<boolean>> {
    return apiClient.post(`/api/rag/vector-database/${id}/initialize`)
  },

  /**
   * 清理向量数据库
   */
  cleanupVectorDatabase(id: string): Promise<ApiResponse<boolean>> {
    return apiClient.post(`/api/rag/vector-database/${id}/cleanup`)
  },

  /**
   * 备份向量数据库
   */
  backupVectorDatabase(id: string): Promise<ApiResponse<any>> {
    return apiClient.post(`/api/rag/vector-database/${id}/backup`)
  },

  /**
   * 恢复向量数据库
   */
  restoreVectorDatabase(id: string, backupPath: string): Promise<ApiResponse<any>> {
    return apiClient.post(`/api/rag/vector-database/${id}/restore`, null, {
      params: { backupPath }
    })
  },

  /**
   * 导出向量数据库配置
   */
  exportVectorDatabases(params?: VectorDatabaseQueryDTO): Promise<ApiResponse<VectorDatabaseVO[]>> {
    return apiClient.get('/api/rag/vector-database/export', { params })
  },

  /**
   * 检查向量数据库名称是否存在
   */
  existsVectorDatabaseName(name: string, excludeId?: string): Promise<ApiResponse<boolean>> {
    return apiClient.get('/api/rag/vector-database/exists', {
      params: { name, excludeId }
    })
  }
}
