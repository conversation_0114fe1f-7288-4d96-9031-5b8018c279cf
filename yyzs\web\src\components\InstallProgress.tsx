'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, Loader2, AlertCircle, Clock } from 'lucide-react';

interface InstallStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  duration?: number;
}

interface InstallProgressProps {
  componentName: string;
  progress: number;
  currentStep?: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export default function InstallProgress({
  componentName,
  progress,
  currentStep,
  onComplete,
  onError
}: InstallProgressProps) {
  const [steps, setSteps] = useState<InstallStep[]>([
    {
      id: 'extract',
      title: '解压安装包',
      description: '正在解压组件安装包...',
      status: 'pending'
    },
    {
      id: 'validate',
      title: '验证文件',
      description: '验证安装包完整性和依赖...',
      status: 'pending'
    },
    {
      id: 'install',
      title: '安装组件',
      description: '复制文件到目标目录...',
      status: 'pending'
    },
    {
      id: 'configure',
      title: '配置组件',
      description: '生成配置文件和启动脚本...',
      status: 'pending'
    },
    {
      id: 'initialize',
      title: '初始化服务',
      description: '注册系统服务和设置权限...',
      status: 'pending'
    },
    {
      id: 'verify',
      title: '验证安装',
      description: '检查安装结果和服务状态...',
      status: 'pending'
    }
  ]);

  const [startTime] = useState(Date.now());
  const [elapsedTime, setElapsedTime] = useState(0);

  // 更新经过时间
  useEffect(() => {
    const timer = setInterval(() => {
      setElapsedTime(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  // 根据进度更新步骤状态
  useEffect(() => {
    setSteps(prevSteps => {
      const newSteps = [...prevSteps];
      const progressPerStep = 100 / newSteps.length;
      
      newSteps.forEach((step, index) => {
        const stepProgress = (index + 1) * progressPerStep;
        
        if (progress >= stepProgress) {
          step.status = 'completed';
          step.duration = Math.floor(Math.random() * 3000) + 1000; // 模拟持续时间
        } else if (progress > index * progressPerStep) {
          step.status = 'running';
        } else {
          step.status = 'pending';
        }
      });

      return newSteps;
    });

    // 检查是否完成
    if (progress >= 100) {
      setTimeout(() => {
        onComplete?.();
      }, 1000);
    }
  }, [progress, onComplete]);

  // 格式化时间
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${remainingSeconds}s`;
  };

  // 获取状态图标
  const getStatusIcon = (status: InstallStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-success-600" />;
      case 'running':
        return <Loader2 className="h-5 w-5 text-primary-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-error-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: InstallStep['status']) => {
    switch (status) {
      case 'completed':
        return 'text-success-600';
      case 'running':
        return 'text-primary-600';
      case 'error':
        return 'text-error-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* 总体进度 */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold text-gray-900">
            正在安装 {componentName}
          </h3>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>已用时: {formatTime(elapsedTime)}</span>
            <span>{Math.round(progress)}%</span>
          </div>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className="bg-primary-600 h-3 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* 详细步骤 */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900">安装步骤</h4>
        
        <div className="space-y-3">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex items-start space-x-3 p-3 rounded-lg transition-colors ${
                step.status === 'running' 
                  ? 'bg-primary-50 border border-primary-200' 
                  : step.status === 'completed'
                  ? 'bg-success-50 border border-success-200'
                  : step.status === 'error'
                  ? 'bg-error-50 border border-error-200'
                  : 'bg-gray-50 border border-gray-200'
              }`}
            >
              <div className="flex-shrink-0 mt-0.5">
                {getStatusIcon(step.status)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className={`text-sm font-medium ${getStatusColor(step.status)}`}>
                    {step.title}
                  </p>
                  {step.status === 'completed' && step.duration && (
                    <span className="text-xs text-gray-500">
                      {formatTime(step.duration)}
                    </span>
                  )}
                </div>
                
                <p className="text-sm text-gray-600 mt-1">
                  {step.description}
                </p>
                
                {step.status === 'running' && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div className="bg-primary-600 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 安装提示 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">安装提示:</p>
            <ul className="space-y-1 text-xs">
              <li>• 安装过程中请勿关闭浏览器或刷新页面</li>
              <li>• 安装时间取决于组件大小和系统性能</li>
              <li>• 如果安装失败，请检查系统权限和磁盘空间</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
