package com.xhcai.modules.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.ai.entity.AiFileRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI文件记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AiFileRecordMapper extends BaseMapper<AiFileRecord> {

    /**
     * 根据Dify文件ID查询文件记录
     *
     * @param difyFileId Dify文件ID
     * @return 文件记录
     */
    @Select("SELECT * FROM ai_file_record WHERE dify_file_id = #{difyFileId} AND deleted = 0")
    AiFileRecord selectByDifyFileId(@Param("difyFileId") String difyFileId);

    /**
     * 根据用户ID查询文件记录列表
     *
     * @param uploadUserId 上传用户ID
     * @return 文件记录列表
     */
    @Select("SELECT * FROM ai_file_record WHERE upload_user_id = #{uploadUserId} AND deleted = 0 ORDER BY upload_time DESC")
    List<AiFileRecord> selectByUploadUserId(@Param("uploadUserId") String uploadUserId);

    /**
     * 根据状态查询文件记录列表
     *
     * @param status 文件状态
     * @return 文件记录列表
     */
    @Select("SELECT * FROM ai_file_record WHERE status = #{status} AND deleted = 0 ORDER BY upload_time DESC")
    List<AiFileRecord> selectByStatus(@Param("status") String status);
}
