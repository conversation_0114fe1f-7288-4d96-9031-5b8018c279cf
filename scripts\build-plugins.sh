#!/bin/bash

echo "Building plugins..."

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "Building MinIO Storage Plugin..."
cd plugins/storage/minio-storage-plugin
mvn clean package -DskipTests
if [ -f target/minio-storage-plugin-1.0.0.jar ]; then
    cp target/minio-storage-plugin-1.0.0.jar ../../../plugins/storage/
    echo "MinIO Storage Plugin built successfully"
else
    echo "Failed to build MinIO Storage Plugin"
fi

echo ""
echo "Building OpenAI Model Plugin..."
cd ../../../plugins/model/openai-model-plugin
mvn clean package -DskipTests
if [ -f target/openai-model-plugin-1.0.0.jar ]; then
    cp target/openai-model-plugin-1.0.0.jar ../../../plugins/model/
    echo "OpenAI Model Plugin built successfully"
else
    echo "Failed to build OpenAI Model Plugin"
fi

echo ""
echo "Building Email Notification Plugin..."
cd ../../../plugins/notify/email-notification-plugin
mvn clean package -DskipTests
if [ -f target/email-notification-plugin-1.0.0.jar ]; then
    cp target/email-notification-plugin-1.0.0.jar ../../../plugins/notify/
    echo "Email Notification Plugin built successfully"
else
    echo "Failed to build Email Notification Plugin"
fi

cd "$PROJECT_ROOT"
echo ""
echo "Plugin build completed!"
