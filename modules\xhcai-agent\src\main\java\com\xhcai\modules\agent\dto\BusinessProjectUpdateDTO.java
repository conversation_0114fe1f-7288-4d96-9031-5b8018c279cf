package com.xhcai.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 业务项目更新DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "业务项目更新DTO")
public class BusinessProjectUpdateDTO {

    /**
     * 项目名称
     */
    @Size(max = 100, message = "项目名称长度不能超过100个字符")
    @Schema(description = "项目名称")
    private String name;

    /**
     * 项目描述
     */
    @Size(max = 500, message = "项目描述长度不能超过500个字符")
    @Schema(description = "项目描述")
    private String description;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID")
    private String ownerId;

    /**
     * 应用环境
     */
    @Pattern(regexp = "^(production|test|development)$", message = "应用环境只能是production、test或development")
    @Schema(description = "应用环境：production-生产环境，test-测试环境，development-开发环境")
    private String environment;

    /**
     * 项目状态
     */
    @Pattern(regexp = "^(active|inactive|maintenance)$", message = "项目状态只能是active、inactive或maintenance")
    @Schema(description = "项目状态：active-运行中，inactive-已停止，maintenance-维护中")
    private String status;

    /**
     * 项目图标
     */
    @Size(max = 100, message = "项目图标长度不能超过100个字符")
    @Schema(description = "项目图标")
    private String icon;

    /**
     * 图标颜色
     */
    @Size(max = 200, message = "图标颜色长度不能超过200个字符")
    @Schema(description = "图标颜色")
    private String iconColor;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconColor() {
        return iconColor;
    }

    public void setIconColor(String iconColor) {
        this.iconColor = iconColor;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
