package com.xhcai.modules.dify.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Dify模块配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class DifyModuleConfig {

    /**
     * Dify模块API分组配置
     */
    @Bean
    public GroupedOpenApi difyApi() {
        return GroupedOpenApi.builder()
                .group("dify")
                .displayName("Dify平台对接")
                .pathsToMatch("/api/dify/**")
                .build();
    }

}
