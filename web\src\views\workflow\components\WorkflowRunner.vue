<template>
  <div class="workflow-runner">
    <!-- 运行控制面板 -->
    <div class="runner-controls">
      <div class="control-buttons">
        <button
          class="btn btn-primary"
          :disabled="isRunning"
          @click="startExecution"
          title="开始执行"
        >
          <i class="fas fa-play"></i>
          开始
        </button>
        
        <button
          class="btn btn-warning"
          :disabled="!isRunning || isPaused"
          @click="pauseExecution"
          title="暂停执行"
        >
          <i class="fas fa-pause"></i>
          暂停
        </button>
        
        <button
          class="btn btn-success"
          :disabled="!isPaused"
          @click="resumeExecution"
          title="恢复执行"
        >
          <i class="fas fa-play"></i>
          恢复
        </button>
        
        <button
          class="btn btn-danger"
          :disabled="!isRunning"
          @click="stopExecution"
          title="停止执行"
        >
          <i class="fas fa-stop"></i>
          停止
        </button>
        
        <button
          class="btn btn-secondary"
          :disabled="isRunning"
          @click="resetExecution"
          title="重置状态"
        >
          <i class="fas fa-redo"></i>
          重置
        </button>
      </div>
      
      <!-- 执行状态显示 -->
      <div class="execution-status">
        <div class="status-info">
          <span class="status-label">状态:</span>
          <span 
            class="status-value" 
            :class="`status-${executionStatus}`"
          >
            {{ getStatusText(executionStatus) }}
          </span>
        </div>
        
        <div class="progress-info" v-if="executionProgress > 0">
          <span class="progress-label">进度:</span>
          <div class="progress-container">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${executionProgress}%` }"
              ></div>
            </div>
            <span class="progress-text">{{ executionProgress }}%</span>
          </div>
        </div>
        
        <div class="current-node" v-if="currentNodeName">
          <span class="node-label">当前节点:</span>
          <span class="node-name">{{ currentNodeName }}</span>
        </div>
      </div>
    </div>
    
    <!-- 执行统计 -->
    <div class="execution-stats" v-if="executionMetrics">
      <div class="stat-item">
        <span class="stat-label">总节点:</span>
        <span class="stat-value">{{ executionMetrics.totalNodes }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已完成:</span>
        <span class="stat-value completed">{{ executionMetrics.completedNodes }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">错误:</span>
        <span class="stat-value error">{{ executionMetrics.errorNodes }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">跳过:</span>
        <span class="stat-value skipped">{{ executionMetrics.skippedNodes }}</span>
      </div>
      <div class="stat-item" v-if="executionMetrics.totalDuration > 0">
        <span class="stat-label">耗时:</span>
        <span class="stat-value">{{ formatDuration(executionMetrics.totalDuration) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { Node, Edge } from '@vue-flow/core'
import { storeToRefs } from 'pinia'
import { WorkflowEngine, type ExecutionStatus, type ExecutionContext } from '@/utils/workflow-engine'
import {
  startWorkflowExecution,
  pauseWorkflowExecution,
  resumeWorkflowExecution,
  stopWorkflowExecution,
  getWorkflowExecutionStatus,
  type WorkflowExecutionRequest,
  type WorkflowExecutionStatus
} from '@/api/workflow'

// 导入全局状态管理
import { useWorkflowStore } from '@/stores/workflowStore'

// Props
interface WorkflowRunnerProps {
  nodes?: Node[]
  edges?: Edge[]
  agentId?: string
  globalVariables?: Record<string, any>
  useRemoteExecution?: boolean
}

const props = withDefaults(defineProps<WorkflowRunnerProps>(), {
  nodes: () => [],
  edges: () => [],
  globalVariables: () => ({}),
  useRemoteExecution: false
})

// 使用全局状态管理
const workflowStore = useWorkflowStore()
const { nodes: globalNodes, edges: globalEdges, workflowConfig } = storeToRefs(workflowStore)

// Emits
const emit = defineEmits<{
  'execution-start': [context: ExecutionContext]
  'execution-complete': [status: WorkflowExecutionStatus]
  'execution-error': [data: { error: any; status: WorkflowExecutionStatus }]
  'execution-pause': [context: ExecutionContext]
  'execution-resume': [context: ExecutionContext]
  'execution-cancel': [context: ExecutionContext]
  'node-start': [data: { node: Node; step: any }]
  'node-complete': [data: { node: Node; step: any; result: any }]
  'node-error': [data: { node: Node; step: any; error: any }]
  'progress-update': [data: { progress: number; currentNode: string }]
}>()

// 响应式数据
const workflowEngine = ref<WorkflowEngine | null>(null)
const executionStatus = ref<ExecutionStatus>('idle')
const executionProgress = ref(0)
const currentNodeName = ref('')
const executionMetrics = ref<any>(null)
const currentExecutionId = ref<string>('')

// 计算属性
const isRunning = computed(() => {
  return executionStatus.value === 'running'
})

const isPaused = computed(() => {
  return executionStatus.value === 'paused'
})

// 计算当前使用的nodes和edges - 优先使用全局状态
const currentNodes = computed(() => globalNodes.value.length > 0 ? globalNodes.value : props.nodes)
const currentEdges = computed(() => globalEdges.value.length > 0 ? globalEdges.value : props.edges)

// 监听节点和边的变化，重新创建引擎
watch([currentNodes, currentEdges], () => {
  if (!isRunning.value) {
    createEngine()
  }
}, { deep: true })

// 方法
const createEngine = () => {
  if (workflowEngine.value) {
    workflowEngine.value.removeAllListeners()
  }

  workflowEngine.value = new WorkflowEngine(currentNodes.value, currentEdges.value, 'normal')
  
  // 设置事件监听
  setupEventListeners()
}

const setupEventListeners = () => {
  if (!workflowEngine.value) return
  
  workflowEngine.value.on('execution-start', (context: any) => {
    executionStatus.value = 'running'
    executionMetrics.value = context.metrics
    emit('execution-start', context)
  })

  workflowEngine.value.on('execution-complete', (context: any) => {
    executionStatus.value = 'completed'
    executionMetrics.value = context.metrics
    executionProgress.value = 100
    currentNodeName.value = ''
    emit('execution-complete', context)
  })
  
  workflowEngine.value.on('execution-error', (data: any) => {
    executionStatus.value = 'error'
    executionMetrics.value = data.context.metrics
    emit('execution-error', data)
  })

  workflowEngine.value.on('execution-pause', (context: any) => {
    executionStatus.value = 'paused'
    emit('execution-pause', context)
  })

  workflowEngine.value.on('execution-resume', (context: any) => {
    executionStatus.value = 'running'
    emit('execution-resume', context)
  })
  
  workflowEngine.value.on('execution-cancel', (context: any) => {
    executionStatus.value = 'cancelled'
    executionMetrics.value = context.metrics
    currentNodeName.value = ''
    emit('execution-cancel', context)
  })

  workflowEngine.value.on('node-start', (data: any) => {
    currentNodeName.value = data.step.nodeName
    emit('node-start', data)
  })

  workflowEngine.value.on('node-complete', (data: any) => {
    emit('node-complete', data)
  })

  workflowEngine.value.on('node-error', (data: any) => {
    emit('node-error', data)
  })
  
  workflowEngine.value.on('progress-update', (data: any) => {
    executionProgress.value = data.progress
    executionMetrics.value = workflowEngine.value?.getContext().metrics
    emit('progress-update', data)
  })
}

const startExecution = async () => {
  if (isRunning.value) return

  try {
    if (props.useRemoteExecution && props.agentId) {
      // 使用远程执行
      await startRemoteExecution()
    } else {
      // 使用本地执行引擎
      await startLocalExecution()
    }
  } catch (error) {
    console.error('执行失败:', error)
  }
}

const startLocalExecution = async () => {
  if (!workflowEngine.value) return

  // 设置全局变量 - 优先使用全局状态中的变量
  const globalVars = workflowConfig.value?.globalVariables || props.globalVariables
  Object.entries(globalVars).forEach(([key, value]) => {
    workflowEngine.value?.setVariable(key, value)
  })

  await workflowEngine.value.execute()
}

const startRemoteExecution = async () => {
  if (!props.agentId) return

  const globalVars = workflowConfig.value?.globalVariables || props.globalVariables
  const request: WorkflowExecutionRequest = {
    agentId: props.agentId,
    mode: 'normal',
    globalVariables: globalVars
  }

  const response = await startWorkflowExecution(request)
  if (response.success) {
    currentExecutionId.value = response.data.executionId
    executionStatus.value = 'running'

    // 开始轮询执行状态
    startStatusPolling()
  }
}

const startStatusPolling = () => {
  const pollInterval = setInterval(async () => {
    if (!currentExecutionId.value || executionStatus.value === 'completed' || executionStatus.value === 'error' || executionStatus.value === 'cancelled') {
      clearInterval(pollInterval)
      return
    }

    try {
      const statusResponse = await getWorkflowExecutionStatus(currentExecutionId.value)
      if (statusResponse.success) {
        const status = statusResponse.data
        executionStatus.value = status.status as ExecutionStatus
        executionProgress.value = status.progress
        currentNodeName.value = status.currentNodeName || ''
        executionMetrics.value = status.metrics

        // 触发相应的事件
        if (status.status === 'completed') {
          emit('execution-complete', status)
        } else if (status.status === 'error') {
          emit('execution-error', { error: 'Remote execution failed', status })
        }
      }
    } catch (error) {
      console.error('获取执行状态失败:', error)
      clearInterval(pollInterval)
    }
  }, 1000) // 每秒轮询一次
}

const pauseExecution = async () => {
  if (props.useRemoteExecution && currentExecutionId.value) {
    try {
      await pauseWorkflowExecution(currentExecutionId.value)
    } catch (error) {
      console.error('暂停执行失败:', error)
    }
  } else {
    workflowEngine.value?.pause()
  }
}

const resumeExecution = async () => {
  if (props.useRemoteExecution && currentExecutionId.value) {
    try {
      await resumeWorkflowExecution(currentExecutionId.value)
    } catch (error) {
      console.error('恢复执行失败:', error)
    }
  } else {
    workflowEngine.value?.resume()
  }
}

const stopExecution = async () => {
  if (props.useRemoteExecution && currentExecutionId.value) {
    try {
      await stopWorkflowExecution(currentExecutionId.value)
    } catch (error) {
      console.error('停止执行失败:', error)
    }
  } else {
    workflowEngine.value?.stop()
  }
}

const resetExecution = () => {
  if (props.useRemoteExecution) {
    currentExecutionId.value = ''
  } else {
    workflowEngine.value?.reset()
  }

  executionStatus.value = 'idle'
  executionProgress.value = 0
  currentNodeName.value = ''
  executionMetrics.value = null
}

const getStatusText = (status: ExecutionStatus): string => {
  const statusMap: Record<ExecutionStatus, string> = {
    idle: '空闲',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    error: '执行错误',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatDuration = (duration: number): string => {
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

// 初始化
createEngine()

// 暴露方法给父组件
defineExpose({
  startExecution,
  pauseExecution,
  resumeExecution,
  stopExecution,
  resetExecution,
  getContext: () => workflowEngine.value?.getContext(),
  setBreakpoint: (nodeId: string) => workflowEngine.value?.setBreakpoint(nodeId),
  removeBreakpoint: (nodeId: string) => workflowEngine.value?.removeBreakpoint(nodeId),
  clearBreakpoints: () => workflowEngine.value?.clearBreakpoints()
})
</script>

<style scoped>
.workflow-runner {
  background: white;
  border-radius: 0;
  padding: 16px;
  box-shadow: none;
  margin-bottom: 0;
  border-bottom: 1px solid #e5e7eb;
}

.runner-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.btn {
  padding: 3px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 2px;
  flex: 1;
  min-width: 0;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-warning {
  background: #f59e0b;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #d97706;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.execution-status {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-label,
.progress-label,
.node-label {
  color: #6b7280;
  font-weight: 500;
}

.status-value {
  font-weight: 600;
}

.status-idle { color: #6b7280; }
.status-running { color: #3b82f6; }
.status-paused { color: #f59e0b; }
.status-completed { color: #10b981; }
.status-error { color: #ef4444; }
.status-cancelled { color: #6b7280; }

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  width: 100px;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  min-width: 35px;
}

.current-node {
  display: flex;
  align-items: center;
  gap: 4px;
}

.node-name {
  font-weight: 600;
  color: #1f2937;
}

.execution-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
  font-size: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  color: #6b7280;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

.stat-value.completed {
  color: #10b981;
}

.stat-value.error {
  color: #ef4444;
}

.stat-value.skipped {
  color: #6b7280;
}
</style>
