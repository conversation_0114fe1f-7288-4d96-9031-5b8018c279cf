# AgentMessage表字段扩展修改文档

## 修改概述

为`agent_message`表增加了三个新字段：`inputs`、`query`、`external_info_id`，用于增强智能体消息的功能和扩展性。

## 修改内容

### 1. 数据库层修改

#### 1.1 数据库迁移脚本
**文件**: `modules/xhcai-agent/src/main/resources/db/migration/add_fields_to_agent_message.sql`

- 添加`inputs`字段：`TEXT`，可空，用于存储输入参数（JSON格式）
- 添加`query`字段：`TEXT`，可空，用于存储查询内容
- 添加`external_info_id`字段：`VARCHAR(36)`，可空，用于关联外部信息
- 创建索引：`idx_agent_message_external_info_id`
- 创建复合索引：`idx_agent_message_conv_external`（conversation_id + external_info_id）

#### 1.2 字段规格
```sql
ALTER TABLE agent_message ADD COLUMN inputs TEXT COMMENT "输入参数（JSON格式）";
ALTER TABLE agent_message ADD COLUMN query TEXT COMMENT "查询内容";
ALTER TABLE agent_message ADD COLUMN external_info_id VARCHAR(36) COMMENT "外部信息ID";
CREATE INDEX idx_agent_message_external_info_id ON agent_message(external_info_id);
CREATE INDEX idx_agent_message_conv_external ON agent_message(conversation_id, external_info_id);
```

### 2. 实体层修改

#### 2.1 AgentMessage实体类
**文件**: `modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/entity/AgentMessage.java`

**新增字段**:
```java
/**
 * 输入参数（JSON格式）
 */
@Column(name = "inputs", columnDefinition = "TEXT")
@Schema(description = "输入参数", example = "{\"temperature\":0.7,\"max_tokens\":1000}")
@TableField("inputs")
private String inputs;

/**
 * 查询内容
 */
@Column(name = "query", columnDefinition = "TEXT")
@Schema(description = "查询内容", example = "用户的具体问题或查询内容")
@TableField("query")
private String query;

/**
 * 外部信息ID
 */
@Column(name = "external_info_id", length = 36)
@Schema(description = "外部信息ID", example = "ext_info_001")
@Size(max = 36, message = "外部信息ID长度不能超过36个字符")
@TableField("external_info_id")
private String externalInfoId;
```

**新增方法**:
```java
// Getter和Setter方法
public String getInputs() { return inputs; }
public void setInputs(String inputs) { this.inputs = inputs; }

public String getQuery() { return query; }
public void setQuery(String query) { this.query = query; }

public String getExternalInfoId() { return externalInfoId; }
public void setExternalInfoId(String externalInfoId) { this.externalInfoId = externalInfoId; }
```

### 3. 前端接口修改

#### 3.1 TypeScript接口定义
**文件**: `web/src/api/agents.ts`

**更新AgentMessage接口**:
```typescript
export interface AgentMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: Record<string, any>
  inputs?: Record<string, any>  // 输入参数（JSON格式）
  query?: string               // 查询内容
  externalInfoId?: string      // 外部信息ID
}
```

### 4. 文档更新

#### 4.1 README.md更新
**文件**: `modules/xhcai-agent/README.md`

更新了agent_message表的SQL创建语句，包含新增的三个字段。

## 字段说明

### inputs字段
- **类型**: TEXT
- **用途**: 存储智能体处理消息时的输入参数，以JSON格式存储
- **示例**: `{"temperature":0.7,"max_tokens":1000,"model":"gpt-3.5-turbo"}`
- **应用场景**: 记录AI模型调用时的参数配置，便于调试和优化

### query字段
- **类型**: TEXT
- **用途**: 存储用户的原始查询内容或处理后的查询内容
- **示例**: `"请帮我分析一下这个产品的优缺点"`
- **应用场景**: 保存用户的具体问题，便于分析用户意图和优化回答质量

### external_info_id字段
- **类型**: VARCHAR(36)
- **用途**: 关联外部信息系统的ID，建立消息与外部数据的关联
- **示例**: `"ext_info_001"`
- **应用场景**: 
  - 关联知识库文档
  - 关联外部API调用结果
  - 关联第三方系统数据

## 索引优化

为了提高查询性能，添加了以下索引：

1. **单字段索引**: `idx_agent_message_external_info_id`
   - 优化基于external_info_id的查询

2. **复合索引**: `idx_agent_message_conv_external`
   - 优化同时基于conversation_id和external_info_id的查询
   - 支持对话中外部信息关联的快速检索

## 兼容性说明

- 所有新增字段均为可空字段，不影响现有数据
- 现有代码无需修改即可正常运行
- 新功能可以逐步集成这些字段

## 使用建议

1. **inputs字段**: 建议在AI模型调用时记录关键参数，便于后续分析和优化
2. **query字段**: 建议记录用户的原始问题，有助于改进智能体的理解能力
3. **external_info_id字段**: 建议在需要关联外部数据时使用，保持数据的可追溯性

## 后续计划

1. 在相关服务类中集成新字段的使用
2. 在前端界面中展示相关信息（如需要）
3. 基于新字段开发数据分析和优化功能
