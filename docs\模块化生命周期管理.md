# xhcai-agent 模块完整生命周期管理集成文档

## 🎯 实现目标

将xhcai-agent模块通过common-core下的plugin完整模块生命周期管理方式来加载，实现：

- ✅ 自动模块注册和发现
- ✅ 完整的生命周期状态管理
- ✅ 事件驱动的模块通信
- ✅ 热插拔支持（启用/禁用/重载）
- ✅ 健康检查和监控
- ✅ REST API管理接口

## 📁 文件结构

```
xhcai-plus/
├── common/common-core/src/main/java/com/xhcai/common/core/plugin/
│   ├── ModuleLifecycle.java              # 模块生命周期接口
│   ├── AbstractModuleLifecycle.java      # 抽象基类（新增）
│   ├── ModuleMetadata.java               # 模块元信息（增强Builder）
│   ├── ModuleManager.java                # 模块管理器
│   └── ModuleRegistry.java               # 模块注册器
├── modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/
│   ├── config/AgentModuleConfig.java     # 模块配置（重构）
│   └── listener/AgentModuleEventListener.java  # 事件监听器（新增）
├── admin-api/src/main/java/com/xhcai/admin/controller/
│   └── ModuleController.java             # 模块管理API（新增）
└── docs/
    ├── module-lifecycle-management.md    # 生命周期管理文档
    └── xhcai-agent-module-integration.md # 本文档
```

## 🔧 核心实现

### 1. 增强的ModuleMetadata Builder模式

<augment_code_snippet path="common/common-core/src/main/java/com/xhcai/common/core/plugin/ModuleMetadata.java" mode="EXCERPT">
```java
public static Builder builder() {
    return new Builder();
}

public static class Builder {
    public Builder moduleId(String moduleId) { ... }
    public Builder name(String moduleName) { ... }
    public Builder dependencies(String... dependencies) { ... }
    public ModuleMetadata build() { ... }
}
```
</augment_code_snippet>

### 2. AbstractModuleLifecycle抽象基类

<augment_code_snippet path="common/common-core/src/main/java/com/xhcai/common/core/plugin/AbstractModuleLifecycle.java" mode="EXCERPT">
```java
public abstract class AbstractModuleLifecycle implements ModuleLifecycle {
    private ModuleStatus status = ModuleStatus.UNINITIALIZED;
    
    // 模板方法，提供状态管理和异常处理
    public final void initialize() { ... }
    public final void start() { ... }
    public final void stop() { ... }
    public final void destroy() { ... }
    
    // 钩子方法，子类重写
    protected void doInitialize() { }
    protected void doStart() { }
    protected void doStop() { }
    protected void doDestroy() { }
    protected boolean doHealthCheck() { return true; }
}
```
</augment_code_snippet>

### 3. xhcai-agent模块配置

<augment_code_snippet path="modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/config/AgentModuleConfig.java" mode="EXCERPT">
```java
@Configuration
public class AgentModuleConfig {

    @Bean
    public ModuleMetadata agentModuleMetadata() {
        return ModuleMetadata.builder()
                .moduleId("xhcai-agent")
                .name("智能体模块")
                .version("1.0.0")
                .dependencies("common-core", "common-security")
                .systemModule(false)
                .priority(200)
                .build();
    }

    @Bean
    public ModuleLifecycle agentModuleLifecycle(ModuleMetadata metadata) {
        return new AbstractModuleLifecycle(metadata) {
            @Override
            protected void doInitialize() {
                initializeModule();
            }
            // ... 其他生命周期方法
        };
    }
}
```
</augment_code_snippet>

### 4. 模块事件监听器

<augment_code_snippet path="modules/xhcai-agent/src/main/java/com/xhcai/modules/agent/listener/AgentModuleEventListener.java" mode="EXCERPT">
```java
@Component
public class AgentModuleEventListener {

    @EventListener
    public void handleModuleEvent(ModuleEvent event) {
        if (!"xhcai-agent".equals(event.getModuleId())) {
            return;
        }

        switch (event.getOperation()) {
            case INSTALL:
                handleInstallEvent(event);
                break;
            case ENABLE:
                handleEnableEvent(event);
                break;
            // ... 其他事件处理
        }
    }
}
```
</augment_code_snippet>

### 5. 模块管理REST API

<augment_code_snippet path="admin-api/src/main/java/com/xhcai/admin/controller/ModuleController.java" mode="EXCERPT">
```java
@RestController
@RequestMapping("/api/admin/modules")
@RequiresPlatformAdmin
public class ModuleController {

    @GetMapping
    public Result<List<ModuleInfo>> getAllModules() { ... }

    @PostMapping("/{moduleId}/enable")
    public Result<Void> enableModule(@PathVariable String moduleId) { ... }

    @PostMapping("/{moduleId}/disable")
    public Result<Void> disableModule(@PathVariable String moduleId) { ... }

    @GetMapping("/{moduleId}/health")
    public Result<Map<String, Object>> getModuleHealth(@PathVariable String moduleId) { ... }
}
```
</augment_code_snippet>

## 🔄 生命周期流程

### 应用启动时的自动加载流程

```mermaid
sequenceDiagram
    participant App as 应用启动
    participant Registry as ModuleRegistry
    participant Manager as ModuleManager
    participant Agent as xhcai-agent
    participant Listener as EventListener

    App->>Registry: ApplicationRunner.run()
    Registry->>Registry: 扫描ModuleLifecycle Bean
    Registry->>Manager: registerModule(agentLifecycle)
    Manager->>Manager: 检查依赖关系
    Manager->>Manager: 缓存模块实例
    Manager->>Listener: 发布INSTALL事件
    Listener->>Agent: 处理安装后逻辑
    Manager->>Agent: initialize()
    Agent->>Agent: doInitialize()
    Manager->>Agent: start()
    Agent->>Agent: doStart()
    Manager->>Listener: 发布ENABLE事件
    Listener->>Agent: 处理启用后逻辑
```

### 运行时热插拔流程

```mermaid
sequenceDiagram
    participant API as REST API
    participant Manager as ModuleManager
    participant Agent as xhcai-agent
    participant Listener as EventListener

    API->>Manager: disableModule("xhcai-agent")
    Manager->>Agent: stop()
    Agent->>Agent: doStop()
    Manager->>Listener: 发布DISABLE事件
    Listener->>Agent: 处理禁用后逻辑

    API->>Manager: enableModule("xhcai-agent")
    Manager->>Agent: start()
    Agent->>Agent: doStart()
    Manager->>Listener: 发布ENABLE事件
    Listener->>Agent: 处理启用后逻辑
```

## 🎛️ 管理接口

### 获取模块列表

```bash
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/admin/modules
```

响应：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "moduleId": "xhcai-agent",
      "moduleName": "智能体模块",
      "version": "1.0.0",
      "status": "STARTED",
      "healthy": true,
      "canStart": false,
      "canStop": true,
      "systemModule": false,
      "priority": 200
    }
  ]
}
```

### 禁用模块

```bash
curl -X POST \
     -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/admin/modules/xhcai-agent/disable
```

### 启用模块

```bash
curl -X POST \
     -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/admin/modules/xhcai-agent/enable
```

### 健康检查

```bash
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/admin/modules/xhcai-agent/health
```

## 📊 监控和日志

### 启动日志示例

```
2024-01-14 10:00:00 INFO  ModuleRegistry - 开始注册模块...
2024-01-14 10:00:01 INFO  ModuleRegistry - 注册模块: xhcai-agent (智能体模块)
2024-01-14 10:00:02 INFO  ModuleManager - 模块 xhcai-agent 注册成功
2024-01-14 10:00:03 INFO  AbstractModuleLifecycle - 初始化模块: xhcai-agent
2024-01-14 10:00:04 INFO  AgentModuleConfig - 初始化智能体模块资源...
2024-01-14 10:00:05 INFO  AbstractModuleLifecycle - 模块 xhcai-agent 初始化完成
2024-01-14 10:00:06 INFO  AbstractModuleLifecycle - 启动模块: xhcai-agent
2024-01-14 10:00:07 INFO  AgentModuleConfig - 启动智能体模块服务...
2024-01-14 10:00:08 INFO  AbstractModuleLifecycle - 模块 xhcai-agent 启动完成
2024-01-14 10:00:09 INFO  AgentModuleEventListener - 收到智能体模块事件: 操作=ENABLE, 成功=true
2024-01-14 10:00:10 INFO  ModuleRegistry - 模块注册完成，成功: 1, 失败: 0
```

### 状态变化日志

```
2024-01-14 11:00:00 INFO  ModuleController - 收到禁用模块请求: xhcai-agent
2024-01-14 11:00:01 INFO  AbstractModuleLifecycle - 停止模块: xhcai-agent
2024-01-14 11:00:02 INFO  AgentModuleConfig - 停止智能体模块服务...
2024-01-14 11:00:03 INFO  AbstractModuleLifecycle - 模块 xhcai-agent 停止完成
2024-01-14 11:00:04 INFO  AgentModuleEventListener - 收到智能体模块事件: 操作=DISABLE, 成功=true
2024-01-14 11:00:05 INFO  ModuleController - 模块 xhcai-agent 禁用成功
```

## 🧪 测试验证

### 单元测试

创建了完整的单元测试 `AgentModuleConfigTest.java`，覆盖：

- ✅ 模块元信息创建和验证
- ✅ 生命周期状态转换
- ✅ Builder模式功能
- ✅ 健康检查
- ✅ 异常处理

### 集成测试

```bash
# 启动应用
mvn spring-boot:run -pl admin-api

# 检查模块状态
curl http://localhost:8080/api/admin/modules/xhcai-agent

# 测试热插拔
curl -X POST http://localhost:8080/api/admin/modules/xhcai-agent/disable
curl -X POST http://localhost:8080/api/admin/modules/xhcai-agent/enable
```

## 🎉 实现效果

### ✅ 已实现的功能

1. **自动模块发现**: 应用启动时自动扫描和注册xhcai-agent模块
2. **完整生命周期**: 支持初始化→启动→停止→销毁的完整流程
3. **状态管理**: 实时跟踪模块状态，防止非法状态转换
4. **事件驱动**: 模块状态变化时自动发布事件，支持业务逻辑响应
5. **热插拔**: 运行时动态启用/禁用模块，无需重启应用
6. **健康检查**: 实时监控模块健康状态
7. **REST API**: 完整的模块管理接口
8. **权限控制**: 只有平台管理员可以管理模块
9. **依赖管理**: 自动检查模块依赖关系
10. **异常处理**: 完善的错误处理和日志记录

### 🚀 架构优势

- **松耦合**: 模块间通过事件通信，降低耦合度
- **高内聚**: 模块内部功能紧密相关
- **可扩展**: 新模块可以轻松集成到系统中
- **可维护**: 清晰的生命周期管理和状态跟踪
- **可监控**: 完整的日志记录和健康检查
- **可测试**: 提供完善的单元测试和集成测试

## 📝 后续扩展

1. **模块配置管理**: 支持运行时配置更新
2. **模块版本管理**: 支持模块版本升级和回滚
3. **模块依赖图**: 可视化模块依赖关系
4. **性能监控**: 添加模块性能指标收集
5. **模块市场**: 支持第三方模块安装和管理

通过这套完整的模块生命周期管理系统，xhcai-agent模块实现了真正的插件化架构，为系统的模块化发展奠定了坚实的基础。
