package com.xhcai.common.api.dto;

import com.xhcai.common.api.query.Statusable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

/**
 * 分页+状态查询DTO基类
 * 组合分页查询和状态查询功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "分页+状态查询DTO基类")
public class PageStatusQueryDTO extends PageQueryDTO implements Statusable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    @Override
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "PageStatusQueryDTO{" +
                "current=" + getCurrent() +
                ", size=" + getSize() +
                ", orderBy='" + getOrderBy() + '\'' +
                ", orderDirection='" + getOrderDirection() + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
