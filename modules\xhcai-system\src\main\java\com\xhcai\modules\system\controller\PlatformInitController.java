package com.xhcai.modules.system.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.system.service.IPlatformAdminService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 平台初始化控制器 提供平台初始化相关的公开接口，无需认证
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "平台初始化", description = "平台初始化相关接口")
@RestController
@RequestMapping("/api/platform/init")
public class PlatformInitController {

    @Autowired
    private IPlatformAdminService platformAdminService;

    /**
     * 检查平台初始化状态
     */
    @Operation(summary = "检查平台初始化状态", description = "检查平台是否已初始化（是否存在平台管理员）")
    @GetMapping("/status")
    public Result<Map<String, Object>> checkInitializationStatus() {
        Map<String, Object> status = platformAdminService.checkPlatformInitialization();
        return Result.success(status);
    }

    /**
     * 创建平台管理员（平台初始化）
     */
    @Operation(summary = "创建平台管理员", description = "创建平台管理员账号，完成平台初始化")
    @PostMapping("/admin")
    public Result<Map<String, Object>> createPlatformAdmin(
            @Parameter(description = "管理员用户名", required = true)
            @RequestParam("username") @NotBlank(message = "用户名不能为空")
            @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间") String username,
            @Parameter(description = "管理员密码", required = true)
            @RequestParam("password") @NotBlank(message = "密码不能为空")
            @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间") String password,
            @Parameter(description = "管理员邮箱", required = true)
            @RequestParam("email") @NotBlank(message = "邮箱不能为空")
            @Email(message = "邮箱格式不正确") String email,
            @Parameter(description = "管理员真实姓名")
            @RequestParam(value = "realName", required = false) String realName) {

        Map<String, Object> result = platformAdminService.createPlatformAdmin(username, password, email, realName);
        return Result.success(result);
    }
}
