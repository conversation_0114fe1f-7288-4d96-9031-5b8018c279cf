package com.xhcai.modules.rag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 向量数据库配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "vector_databases")
@Schema(description = "向量数据库配置")
@TableName("vector_databases")
public class VectorDatabase extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 数据库名称
     */
    @Column(name = "name", length = 100, nullable = false)
    @Schema(description = "数据库名称", example = "Elasticsearch集群")
    @NotBlank(message = "数据库名称不能为空")
    @Size(min = 1, max = 100, message = "数据库名称长度必须在1-100个字符之间")
    @TableField("name")
    private String name;

    /**
     * 数据库类型
     */
    @Column(name = "type", length = 50, nullable = false)
    @Schema(description = "数据库类型", example = "elasticsearch")
    @NotBlank(message = "数据库类型不能为空")
    @Size(min = 1, max = 50, message = "数据库类型长度必须在1-50个字符之间")
    @TableField("type")
    private String type;

    /**
     * 主机地址
     */
    @Column(name = "host", length = 255, nullable = false)
    @Schema(description = "主机地址", example = "localhost")
    @NotBlank(message = "主机地址不能为空")
    @Size(min = 1, max = 255, message = "主机地址长度必须在1-255个字符之间")
    @TableField("host")
    private String host;

    /**
     * 端口
     */
    @Column(name = "port", nullable = false)
    @Schema(description = "端口", example = "9200")
    @TableField("port")
    private Integer port;

    /**
     * 数据库名
     */
    @Column(name = "database_name", length = 100)
    @Schema(description = "数据库名", example = "vector_db")
    @Size(max = 100, message = "数据库名长度不能超过100个字符")
    @TableField("database_name")
    private String databaseName;

    /**
     * 用户名
     */
    @Column(name = "username", length = 100)
    @Schema(description = "用户名", example = "admin")
    @Size(max = 100, message = "用户名长度不能超过100个字符")
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @Column(name = "password", length = 255)
    @Schema(description = "密码")
    @Size(max = 255, message = "密码长度不能超过255个字符")
    @TableField("password")
    private String password;

    /**
     * 连接配置（JSON格式）
     */
    @Column(name = "connection_config", columnDefinition = "TEXT")
    @Schema(description = "连接配置", example = "{\"ssl\": true, \"timeout\": 30}")
    @TableField("connection_config")
    private String connectionConfig;

    /**
     * 索引配置（JSON格式）
     */
    @Column(name = "index_config", columnDefinition = "TEXT")
    @Schema(description = "索引配置", example = "{\"shards\": 1, \"replicas\": 0}")
    @TableField("index_config")
    private String indexConfig;

    /**
     * 状态
     */
    @Column(name = "status", length = 1, nullable = false)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status = "0";

    /**
     * 是否为默认数据库
     */
    @Column(name = "is_default", length = 1, nullable = false)
    @Schema(description = "是否为默认数据库", example = "N", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "是否为默认数据库值必须为Y或N")
    @TableField("is_default")
    private String isDefault = "N";

    /**
     * 描述
     */
    @Column(name = "description", length = 500)
    @Schema(description = "描述", example = "主要的Elasticsearch向量搜索集群")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 图标
     */
    @Column(name = "icon", length = 10)
    @Schema(description = "图标", example = "🔍")
    @Size(max = 10, message = "图标长度不能超过10个字符")
    @TableField("icon")
    private String icon;

    /**
     * 图标颜色
     */
    @Column(name = "icon_color", length = 20)
    @Schema(description = "图标颜色", example = "#005571")
    @Size(max = 20, message = "图标颜色长度不能超过20个字符")
    @TableField("icon_color")
    private String iconColor;

    /**
     * 连接池配置（JSON格式）
     */
    @Column(name = "pool_config", columnDefinition = "TEXT")
    @Schema(description = "连接池配置", example = "{\"maxConnections\": 10, \"minConnections\": 1}")
    @TableField("pool_config")
    private String poolConfig;

    /**
     * 最后连接测试时间
     */
    @Column(name = "last_test_time")
    @Schema(description = "最后连接测试时间")
    @TableField("last_test_time")
    private java.time.LocalDateTime lastTestTime;

    /**
     * 最后连接测试结果
     */
    @Column(name = "last_test_result", length = 1)
    @Schema(description = "最后连接测试结果", example = "Y", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "连接测试结果值必须为Y或N")
    @TableField("last_test_result")
    private String lastTestResult;

    /**
     * 连接测试错误信息
     */
    @Column(name = "test_error_message", length = 1000)
    @Schema(description = "连接测试错误信息")
    @Size(max = 1000, message = "连接测试错误信息长度不能超过1000个字符")
    @TableField("test_error_message")
    private String testErrorMessage;

    @Override
    public String toString() {
        return "VectorDatabase{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", databaseName='" + databaseName + '\'' +
                ", username='" + username + '\'' +
                ", status='" + status + '\'' +
                ", isDefault='" + isDefault + '\'' +
                ", description='" + description + '\'' +
                ", icon='" + icon + '\'' +
                ", iconColor='" + iconColor + '\'' +
                ", lastTestTime=" + lastTestTime +
                ", lastTestResult='" + lastTestResult + '\'' +
                ", tenantId=" + getTenantId() +
                ", remark='" + getRemark() + '\'' +
                ", createBy=" + getCreateBy() +
                ", createTime=" + getCreateTime() +
                ", updateBy=" + getUpdateBy() +
                ", updateTime=" + getUpdateTime() +
                '}';
    }
}
