package com.xhcai.modules.rag.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.entity.UploadFile;
import com.xhcai.modules.rag.mapper.UploadFileMapper;
import com.xhcai.modules.rag.service.IUploadFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传记录服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class UploadFileServiceImpl extends ServiceImpl<UploadFileMapper, UploadFile> implements IUploadFileService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUploadFile(UploadFile uploadFile) {
        try {
            // 设置基础信息
            if (uploadFile.getId() == null) {
                uploadFile.setId(UUID.randomUUID().toString());
            }
            uploadFile.setTenantId(SecurityUtils.getCurrentTenantId());
            uploadFile.setCreateBy(SecurityUtils.getCurrentUserId());
            uploadFile.setCreateTime(LocalDateTime.now());
            uploadFile.setUpdateBy(SecurityUtils.getCurrentUserId());
            uploadFile.setUpdateTime(LocalDateTime.now());
            uploadFile.setDeleted(0);

            // 设置默认值
            if (uploadFile.getUploadTime() == null) {
                uploadFile.setUploadTime(LocalDateTime.now());
            }
            if (uploadFile.getOperationTime() == null) {
                uploadFile.setOperationTime(LocalDateTime.now());
            }
            if (uploadFile.getOperationType() == null) {
                uploadFile.setOperationType("upload");
            }
            if (uploadFile.getUploadStatus() == null) {
                uploadFile.setUploadStatus("uploading");
            }

            return save(uploadFile);
        } catch (Exception e) {
            log.error("创建上传文件记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<UploadFile> getByDatasetId(String datasetId) {
        return baseMapper.selectByDatasetId(datasetId);
    }

    @Override
    public List<UploadFile> getByBatchId(String batchId) {
        return baseMapper.selectByBatchId(batchId);
    }

    @Override
    public UploadFile getByDocumentId(String documentId) {
        return baseMapper.selectByDocumentId(documentId);
    }

    @Override
    public UploadFile getByFileHash(String fileHash, String datasetId) {
        return baseMapper.selectByFileHash(fileHash, datasetId);
    }

    @Override
    public IPage<UploadFile> getPageByCondition(Page<UploadFile> page, String datasetId, String uploadStatus, String operationType) {
        return baseMapper.selectPageByCondition(page, datasetId, uploadStatus, operationType);
    }

    @Override
    public Long countByDatasetId(String datasetId) {
        return baseMapper.countByDatasetId(datasetId);
    }

    @Override
    public Long countByDatasetIdAndStatus(String datasetId, String uploadStatus) {
        return baseMapper.countByDatasetIdAndStatus(datasetId, uploadStatus);
    }

    @Override
    public List<UploadFile> getByUploadUserId(String uploadUserId) {
        return baseMapper.selectByUploadUserId(uploadUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDocumentId(String id, String documentId) {
        try {
            int result = baseMapper.updateDocumentId(id, documentId);
            return result > 0;
        } catch (Exception e) {
            log.error("更新文档ID失败: id={}, documentId={}, error={}", id, documentId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUploadStatus(String id, String uploadStatus, String errorMessage) {
        try {
            int result = baseMapper.updateUploadStatus(id, uploadStatus, errorMessage);
            return result > 0;
        } catch (Exception e) {
            log.error("更新上传状态失败: id={}, status={}, error={}", id, uploadStatus, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean softDeleteUploadFile(String id) {
        try {
            int result = baseMapper.softDelete(id);
            return result > 0;
        } catch (Exception e) {
            log.error("软删除上传文件记录失败: id={}, error={}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSoftDeleteUploadFiles(List<String> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return true;
            }
            int result = baseMapper.batchSoftDelete(ids);
            return result > 0;
        } catch (Exception e) {
            log.error("批量软删除上传文件记录失败: ids={}, error={}", ids, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordFileDeleteOperation(UploadFile originalUploadFile, String deleteReason) {
        try {
            // 创建删除操作记录
            UploadFile deleteRecord = new UploadFile();
            deleteRecord.setOriginalFilename(originalUploadFile.getOriginalFilename());
            deleteRecord.setFileSize(originalUploadFile.getFileSize());
            deleteRecord.setFileExtension(originalUploadFile.getFileExtension());
            deleteRecord.setMimeType(originalUploadFile.getMimeType());
            deleteRecord.setFileHash(originalUploadFile.getFileHash());
            deleteRecord.setDatasetId(originalUploadFile.getDatasetId());
            deleteRecord.setBatchId(originalUploadFile.getBatchId());
            deleteRecord.setDocumentId(originalUploadFile.getDocumentId());
            deleteRecord.setMinioBucket(originalUploadFile.getMinioBucket());
            deleteRecord.setMinioObjectName(originalUploadFile.getMinioObjectName());
            deleteRecord.setMinioUrl(originalUploadFile.getMinioUrl());
            deleteRecord.setUploadStatus("deleted");
            deleteRecord.setUploadTime(originalUploadFile.getUploadTime());
            deleteRecord.setUploadUserId(originalUploadFile.getUploadUserId());
            deleteRecord.setOperationType("delete");
            deleteRecord.setOperationTime(LocalDateTime.now());
            deleteRecord.setErrorMessage(deleteReason);

            return createUploadFile(deleteRecord);
        } catch (Exception e) {
            log.error("记录文件删除操作失败: originalFile={}, reason={}, error={}", 
                     originalUploadFile.getId(), deleteReason, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean isFileExists(String fileHash, String datasetId) {
        UploadFile existingFile = getByFileHash(fileHash, datasetId);
        return existingFile != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateUploadFiles(List<UploadFile> uploadFiles) {
        try {
            if (uploadFiles == null || uploadFiles.isEmpty()) {
                return true;
            }

            for (UploadFile uploadFile : uploadFiles) {
                if (!createUploadFile(uploadFile)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量创建上传文件记录失败: error={}", e.getMessage(), e);
            return false;
        }
    }
}
