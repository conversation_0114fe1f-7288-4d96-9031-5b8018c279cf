# AiChatRecord表添加input字段修改文档

## 修改概述

为`ai_chat_record`表增加了`input`字段，用于存储输入参数（JSON格式），增强AI对话记录的功能和扩展性。

## 修改内容

### 1. 数据库层修改

#### 1.1 数据库迁移脚本
**文件**: `modules/xhcai-ai/src/main/resources/db/migration/add_input_field_to_ai_chat_record.sql`

- 添加`input`字段：`TEXT`，可空，用于存储输入参数（JSON格式）
- 包含字段存在性检查，避免重复执行错误

#### 1.2 字段规格
```sql
ALTER TABLE ai_chat_record ADD COLUMN input TEXT COMMENT "输入参数（JSON格式）";
```

### 2. 实体层修改

#### 2.1 AiChatRecord实体类
**文件**: `modules/xhcai-ai/src/main/java/com/xhcai/modules/ai/entity/AiChatRecord.java`

**新增字段**:
```java
/**
 * 输入参数（JSON格式）
 */
@Column(name = "input", columnDefinition = "TEXT")
@Schema(description = "输入参数", example = "{\"temperature\":0.7,\"max_tokens\":1000}")
@TableField("input")
private String input;
```

**新增方法**:
```java
// Getter和Setter方法
public String getInput() { return input; }
public void setInput(String input) { this.input = input; }
```

**更新toString方法**:
```java
// 在toString方法中添加input字段的输出
+ ", input='" + input + '\''
```

#### 2.2 AiChatRecordVO类
**文件**: `modules/xhcai-ai/src/main/java/com/xhcai/modules/ai/vo/AiChatRecordVO.java`

**新增字段**:
```java
/**
 * 输入参数
 */
@Schema(description = "输入参数")
private String input;
```

**新增方法**:
```java
// Getter和Setter方法
public String getInput() { return input; }
public void setInput(String input) { this.input = input; }
```

### 3. 数据访问层修改

#### 3.1 MyBatis映射文件
**文件**: `modules/xhcai-ai/src/main/resources/mapper/AiChatRecordMapper.xml`

**更新BaseResultMap**:
```xml
<result column="input" property="input" />
```

**更新VoResultMap**:
```xml
<result column="input" property="input" />
```

**更新Base_Column_List**:
```sql
id, session_id, user_id, model_name, user_message, ai_response, 
message_type, tokens_used, cost_time, status, error_msg, 
create_time, update_time, create_by, update_by, deleted, tenant_id, app_id, input
```

## 字段说明

### input字段
- **类型**: TEXT
- **用途**: 存储AI对话时的输入参数，以JSON格式存储
- **示例**: `{"temperature":0.7,"max_tokens":1000,"model":"gpt-3.5-turbo","stream":true}`
- **应用场景**: 
  - 记录AI模型调用时的参数配置
  - 便于调试和分析对话质量
  - 支持参数优化和A/B测试
  - 提供对话重现的基础数据

## 兼容性说明

- 新增字段为可空字段，不影响现有数据
- 现有代码无需修改即可正常运行
- 新功能可以逐步集成这个字段

## 使用建议

1. **参数记录**: 建议在AI模型调用时记录关键参数，如temperature、max_tokens、model等
2. **JSON格式**: 使用标准JSON格式存储，便于后续解析和分析
3. **数据分析**: 可基于此字段分析不同参数配置对对话质量的影响
4. **调试支持**: 在出现问题时，可通过此字段快速定位参数配置问题

## 示例用法

### 存储输入参数
```java
AiChatRecord chatRecord = new AiChatRecord();
// ... 设置其他字段
chatRecord.setInput("{\"temperature\":0.7,\"max_tokens\":1000,\"model\":\"gpt-3.5-turbo\"}");
```

### 解析输入参数
```java
String inputJson = chatRecord.getInput();
if (StringUtils.hasText(inputJson)) {
    // 使用JSON工具解析参数
    ObjectMapper mapper = new ObjectMapper();
    Map<String, Object> params = mapper.readValue(inputJson, Map.class);
    Double temperature = (Double) params.get("temperature");
    Integer maxTokens = (Integer) params.get("max_tokens");
    // ... 使用参数
}
```

## 后续计划

1. 在相关服务类中集成新字段的使用
2. 在管理界面中展示输入参数信息（如需要）
3. 基于输入参数开发数据分析和优化功能
4. 支持基于历史参数的智能推荐功能

## 相关文件

- 实体类: `modules/xhcai-ai/src/main/java/com/xhcai/modules/ai/entity/AiChatRecord.java`
- VO类: `modules/xhcai-ai/src/main/java/com/xhcai/modules/ai/vo/AiChatRecordVO.java`
- 映射文件: `modules/xhcai-ai/src/main/resources/mapper/AiChatRecordMapper.xml`
- 迁移脚本: `modules/xhcai-ai/src/main/resources/db/migration/add_input_field_to_ai_chat_record.sql`
