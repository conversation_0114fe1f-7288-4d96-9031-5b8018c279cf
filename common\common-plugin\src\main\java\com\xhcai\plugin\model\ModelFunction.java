package com.xhcai.plugin.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 模型函数定义
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFunction {
    
    /**
     * 函数名称
     */
    private String name;
    
    /**
     * 函数描述
     */
    private String description;
    
    /**
     * 函数参数定义（JSON Schema）
     */
    private Map<String, Object> parameters;
}
