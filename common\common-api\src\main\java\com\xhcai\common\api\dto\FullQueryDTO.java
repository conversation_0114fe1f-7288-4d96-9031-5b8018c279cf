package com.xhcai.common.api.dto;

import com.xhcai.common.api.query.DataScopeable;
import com.xhcai.common.api.query.Keywordable;
import com.xhcai.common.api.query.Statusable;
import com.xhcai.common.api.query.TimeRangeable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 完整查询DTO基类
 * 组合所有常用查询条件：分页+时间范围+状态+关键字+数据权限
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "完整查询DTO基类")
public class FullQueryDTO extends PageQueryDTO implements TimeRangeable, Statusable, Keywordable, DataScopeable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "开始时间格式必须为：yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "结束时间格式必须为：yyyy-MM-dd HH:mm:ss")
    private String endTime;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    /**
     * 搜索关键字
     */
    @Schema(description = "搜索关键字", example = "用户")
    @Size(max = 100, message = "搜索关键字长度不能超过100个字符")
    private String keyword;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    /**
     * 数据权限SQL
     */
    @Schema(hidden = true)
    private String dataScope;

    // TimeRangeable implementation
    @Override
    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    @Override
    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    // Statusable implementation
    @Override
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    // Keywordable implementation
    @Override
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    // DataScopeable implementation
    @Override
    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String getDataScope() {
        return dataScope;
    }

    @Override
    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    @Override
    public String toString() {
        return "FullQueryDTO{" +
                "current=" + getCurrent() +
                ", size=" + getSize() +
                ", orderBy='" + getOrderBy() + '\'' +
                ", orderDirection='" + getOrderDirection() + '\'' +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", status='" + status + '\'' +
                ", keyword='" + keyword + '\'' +
                ", tenantId=" + tenantId +
                ", dataScope='" + dataScope + '\'' +
                '}';
    }
}
