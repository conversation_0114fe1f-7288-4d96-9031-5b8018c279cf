import type { Node, Edge } from '@vue-flow/core'

// 浏览器兼容的事件发射器
class BrowserEventEmitter {
  private listeners: Map<string, Function[]> = new Map()

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  emit(event: string, data?: any) {
    const callbacks = this.listeners.get(event) || []
    callbacks.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error)
      }
    })
  }

  removeAllListeners(event?: string) {
    if (event) {
      this.listeners.delete(event)
    } else {
      this.listeners.clear()
    }
  }

  off(event: string, callback: Function) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index !== -1) {
        callbacks.splice(index, 1)
      }
    }
  }
}

// 工作流执行状态
export type ExecutionStatus = 'idle' | 'running' | 'paused' | 'completed' | 'error' | 'cancelled'

// 节点执行状态
export type NodeExecutionStatus = 'pending' | 'running' | 'completed' | 'error' | 'skipped' | 'waiting'

// 执行模式
export type ExecutionMode = 'normal' | 'debug' | 'step'

// 执行步骤
export interface ExecutionStep {
  nodeId: string
  nodeName: string
  nodeType: string
  status: NodeExecutionStatus
  startTime?: number
  endTime?: number
  duration?: number
  input?: any
  output?: any
  error?: string
  progress?: number // 0-100
  logs: Array<{
    level: 'debug' | 'info' | 'warn' | 'error'
    message: string
    timestamp: number
  }>
}

// 执行事件类型
export interface ExecutionEvents {
  'execution-start': (context: ExecutionContext) => void
  'execution-complete': (context: ExecutionContext) => void
  'execution-error': (data: { error: any; context: ExecutionContext }) => void
  'execution-pause': (context: ExecutionContext) => void
  'execution-resume': (context: ExecutionContext) => void
  'execution-cancel': (context: ExecutionContext) => void
  'node-start': (data: { node: Node; step: ExecutionStep }) => void
  'node-complete': (data: { node: Node; step: ExecutionStep; result: any }) => void
  'node-error': (data: { node: Node; step: ExecutionStep; error: any }) => void
  'node-skip': (data: { node: Node; step: ExecutionStep; reason: string }) => void
  'edge-traverse': (data: { edge: Edge; data: any }) => void
  'progress-update': (data: { progress: number; currentNode: string }) => void
}

// 执行上下文
export interface ExecutionContext {
  id: string
  status: ExecutionStatus
  mode: ExecutionMode
  startTime: number
  endTime?: number
  currentNodeId?: string
  steps: ExecutionStep[]
  variables: Record<string, any>
  breakpoints: Set<string> // 断点节点ID
  metrics: {
    totalNodes: number
    completedNodes: number
    errorNodes: number
    skippedNodes: number
    totalDuration: number
    progress: number // 0-100
  }
}

// 工作流执行引擎
export class WorkflowEngine extends BrowserEventEmitter {
  private nodes: Node[]
  private edges: Edge[]
  private context: ExecutionContext
  private isRunning = false
  private isPaused = false
  private shouldStop = false
  private currentPromise: Promise<any> | null = null

  constructor(nodes: Node[], edges: Edge[], mode: ExecutionMode = 'normal') {
    super()
    this.nodes = nodes
    this.edges = edges
    this.context = {
      id: `execution_${Date.now()}`,
      status: 'idle',
      mode,
      startTime: 0,
      steps: [],
      variables: {},
      breakpoints: new Set(),
      metrics: {
        totalNodes: nodes.length,
        completedNodes: 0,
        errorNodes: 0,
        skippedNodes: 0,
        totalDuration: 0,
        progress: 0
      }
    }
  }

  // 获取执行上下文
  getContext(): ExecutionContext {
    return { ...this.context }
  }

  // 获取执行状态
  getStatus(): ExecutionStatus {
    return this.context.status
  }

  // 是否正在运行
  isExecuting(): boolean {
    return this.isRunning
  }

  // 设置断点
  setBreakpoint(nodeId: string) {
    this.context.breakpoints.add(nodeId)
  }

  // 移除断点
  removeBreakpoint(nodeId: string) {
    this.context.breakpoints.delete(nodeId)
  }

  // 清除所有断点
  clearBreakpoints() {
    this.context.breakpoints.clear()
  }

  // 设置变量
  setVariable(key: string, value: any) {
    this.context.variables[key] = value
  }

  // 获取变量
  getVariable(key: string): any {
    return this.context.variables[key]
  }

  // 更新进度
  private updateProgress() {
    const total = this.context.metrics.totalNodes
    const completed = this.context.metrics.completedNodes + this.context.metrics.errorNodes + this.context.metrics.skippedNodes
    this.context.metrics.progress = total > 0 ? Math.round((completed / total) * 100) : 0
    this.emit('progress-update', {
      progress: this.context.metrics.progress,
      currentNode: this.context.currentNodeId || ''
    })
  }

  // 开始执行
  async execute(): Promise<ExecutionContext> {
    if (this.isRunning) {
      throw new Error('工作流正在执行中')
    }

    try {
      this.isRunning = true
      this.isPaused = false
      this.shouldStop = false
      this.context.status = 'running'
      this.context.startTime = Date.now()
      this.emit('execution-start', this.context)

      // 找到开始节点
      const startNodes = this.nodes.filter(node => node.type === 'start')
      if (startNodes.length === 0) {
        throw new Error('未找到开始节点')
      }

      // 从开始节点开始执行
      this.currentPromise = this.executeFromNodes(startNodes)
      await this.currentPromise

      if (!this.shouldStop) {
        this.context.status = 'completed'
        this.context.endTime = Date.now()
        this.context.metrics.totalDuration = this.context.endTime - this.context.startTime
        this.updateProgress()
        this.emit('execution-complete', this.context)
      }

      return this.context
    } catch (error) {
      this.context.status = 'error'
      this.context.endTime = Date.now()
      this.context.metrics.totalDuration = this.context.endTime - this.context.startTime
      this.emit('execution-error', { error, context: this.context })
      throw error
    } finally {
      this.isRunning = false
      this.currentPromise = null
    }
  }

  // 暂停执行
  pause() {
    if (!this.isRunning || this.isPaused) {
      return
    }
    this.isPaused = true
    this.context.status = 'paused'
    this.emit('execution-pause', this.context)
  }

  // 恢复执行
  resume() {
    if (!this.isRunning || !this.isPaused) {
      return
    }
    this.isPaused = false
    this.context.status = 'running'
    this.emit('execution-resume', this.context)
  }

  // 停止执行
  stop() {
    if (!this.isRunning) {
      return
    }
    this.shouldStop = true
    this.context.status = 'cancelled'
    this.context.endTime = Date.now()
    this.context.metrics.totalDuration = this.context.endTime - this.context.startTime
    this.emit('execution-cancel', this.context)
    this.isRunning = false
  }

  // 重置执行状态
  reset() {
    this.stop()
    this.context = {
      id: `execution_${Date.now()}`,
      status: 'idle',
      mode: this.context.mode,
      startTime: 0,
      steps: [],
      variables: {},
      breakpoints: this.context.breakpoints, // 保留断点
      metrics: {
        totalNodes: this.nodes.length,
        completedNodes: 0,
        errorNodes: 0,
        skippedNodes: 0,
        totalDuration: 0,
        progress: 0
      }
    }
  }

  // 单步执行（调试模式）
  async stepNext(): Promise<void> {
    if (this.context.mode !== 'debug' && this.context.mode !== 'step') {
      throw new Error('单步执行仅在调试模式下可用')
    }

    if (!this.isRunning) {
      throw new Error('工作流未在运行中')
    }

    // 恢复执行一步
    this.resume()
    // 在下一个节点处暂停
    // 这里需要在executeNode中实现相应的逻辑
  }

  // 从多个节点开始执行
  private async executeFromNodes(nodes: Node[]): Promise<void> {
    const promises = nodes.map(node => this.executeNode(node.id))
    await Promise.all(promises)
  }

  // 执行单个节点
  private async executeNode(nodeId: string): Promise<any> {
    // 检查是否应该停止
    if (this.shouldStop) {
      return
    }

    // 检查是否暂停
    while (this.isPaused && !this.shouldStop) {
      await this.delay(100)
    }

    const node = this.nodes.find(n => n.id === nodeId)
    if (!node) {
      throw new Error(`节点不存在: ${nodeId}`)
    }

    // 检查断点
    if (this.context.breakpoints.has(nodeId) && this.context.mode === 'debug') {
      this.pause()
      while (this.isPaused && !this.shouldStop) {
        await this.delay(100)
      }
    }

    // 创建执行步骤
    const step: ExecutionStep = {
      nodeId: node.id,
      nodeName: node.data?.label || node.type,
      nodeType: node.data?.nodeType || node.type,
      status: 'running',
      startTime: Date.now(),
      progress: 0,
      logs: []
    }

    this.context.steps.push(step)
    this.context.currentNodeId = nodeId
    this.emit('node-start', { node, step })

    try {
      // 添加日志
      this.addLog(step, 'info', `开始执行节点: ${step.nodeName}`)

      // 根据节点类型执行不同的逻辑
      const result = await this.executeNodeByType(node, step)

      // 更新步骤状态
      step.status = 'completed'
      step.endTime = Date.now()
      step.duration = step.endTime - step.startTime!
      step.output = result
      step.progress = 100

      this.context.metrics.completedNodes++
      this.updateProgress()
      this.addLog(step, 'info', `节点执行完成: ${step.nodeName}, 耗时: ${step.duration}ms`)

      this.emit('node-complete', { node, step, result })

      // 执行后续节点
      if (!this.shouldStop) {
        await this.executeNextNodes(nodeId, result)
      }

      return result
    } catch (error) {
      step.status = 'error'
      step.endTime = Date.now()
      step.duration = step.endTime! - step.startTime!
      step.error = error instanceof Error ? error.message : String(error)
      step.progress = 0

      this.context.metrics.errorNodes++
      this.updateProgress()
      this.addLog(step, 'error', `节点执行失败: ${step.error}`)

      this.emit('node-error', { node, step, error })

      // 根据错误处理策略决定是否继续执行
      if (this.shouldContinueOnError(node)) {
        this.addLog(step, 'warn', '忽略错误，继续执行后续节点')
        if (!this.shouldStop) {
          await this.executeNextNodes(nodeId, null)
        }
      } else {
        throw error
      }
    }
  }

  // 判断是否在错误时继续执行
  private shouldContinueOnError(node: Node): boolean {
    return node.data?.config?.continueOnError === true
  }

  // 根据节点类型执行
  private async executeNodeByType(node: Node, step: ExecutionStep): Promise<any> {
    const nodeType = node.data?.nodeType || node.type
    const config = node.data?.config || {}

    // 更新进度为开始状态
    step.progress = 10
    this.updateProgress()

    switch (nodeType) {
      case 'start':
        return this.executeStartNode(node, step)

      case 'end':
        return this.executeEndNode(node, step)

      case 'condition':
      case 'loop':
        return this.executeConditionNode(node, step)

      case 'mysql':
      case 'postgresql':
      case 'oracle':
      case 'dameng':
      case 'redis':
        return this.executeDatabaseNode(node, step)

      case 'llm-chat':
      case 'text-embedding':
      case 'speech-to-text':
      case 'text-to-speech':
      case 'image-generation':
      case 'image-analysis':
      case 'knowledge-base':
        return this.executeAINode(node, step)
      
      case 'http-request':
        return this.executeHttpNode(node, step)
      
      case 'delay':
        return this.executeDelayNode(node, step)

      case 'generate-ppt':
      case 'generate-word':
      case 'generate-pdf':
      case 'generate-excel':
      case 'extract-ppt':
      case 'extract-word':
      case 'extract-pdf':
        return this.executeFileNode(node, step)

      case 'pie-chart':
      case 'line-chart':
      case 'bar-chart':
        return this.executeRenderNode(node, step)

      case 'data-filter':
      case 'data-transform':
        return this.executeDataNode(node, step)

      default:
        this.addLog(step, 'warn', `未知节点类型: ${nodeType}, 跳过执行`)
        step.progress = 50
        this.updateProgress()
        return { message: `节点类型 ${nodeType} 暂不支持执行` }
    }
  }

  // 执行开始节点
  private async executeStartNode(node: Node, step: ExecutionStep): Promise<any> {
    this.addLog(step, 'info', '工作流开始执行')
    step.progress = 50
    this.updateProgress()
    await this.delay(200) // 模拟处理时间
    step.progress = 90
    this.updateProgress()
    return { status: 'started', timestamp: Date.now() }
  }

  // 执行结束节点
  private async executeEndNode(node: Node, step: ExecutionStep): Promise<any> {
    this.addLog(step, 'info', '工作流执行结束')
    step.progress = 50
    this.updateProgress()
    await this.delay(200)
    step.progress = 90
    this.updateProgress()
    return { status: 'ended', timestamp: Date.now() }
  }

  // 执行条件节点
  private async executeConditionNode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    const condition = config.condition || 'true'

    this.addLog(step, 'info', `评估条件: ${condition}`)
    step.progress = 30
    this.updateProgress()

    // 简单的条件评估（实际应用中需要更复杂的表达式解析）
    const result = this.evaluateCondition(condition)

    step.progress = 70
    this.updateProgress()
    await this.delay(300)

    this.addLog(step, 'info', `条件结果: ${result}`)
    step.progress = 90
    this.updateProgress()
    return { condition, result }
  }

  // 执行数据库节点
  private async executeDatabaseNode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    
    this.addLog(step, 'info', `连接数据库: ${config.host}:${config.port}`)
    
    // 模拟数据库操作
    await this.delay(Math.random() * 1000 + 500)
    
    const mockResult = {
      type: node.type,
      operation: config.operation || 'select',
      rowsAffected: Math.floor(Math.random() * 100),
      data: [
        { id: 1, name: '示例数据1' },
        { id: 2, name: '示例数据2' }
      ]
    }
    
    this.addLog(step, 'info', `数据库操作完成, 影响行数: ${mockResult.rowsAffected}`)
    return mockResult
  }

  // 执行AI节点
  private async executeAINode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    const nodeType = node.data?.nodeType || node.type

    this.addLog(step, 'info', `调用AI服务: ${nodeType}`)
    step.progress = 20
    this.updateProgress()

    // 模拟AI调用过程
    const totalTime = Math.random() * 2000 + 1000
    const steps = 5
    const stepTime = totalTime / steps

    for (let i = 1; i <= steps; i++) {
      if (this.shouldStop) break
      await this.delay(stepTime)
      step.progress = 20 + (i / steps) * 60
      this.updateProgress()
      this.addLog(step, 'debug', `AI处理进度: ${Math.round(step.progress)}%`)
    }

    const mockResult = {
      type: nodeType,
      model: config.model || 'default',
      result: `AI处理结果 - ${nodeType}`,
      tokens: Math.floor(Math.random() * 1000),
      processingTime: totalTime
    }

    step.progress = 90
    this.updateProgress()
    this.addLog(step, 'info', `AI调用完成, 消耗令牌: ${mockResult.tokens}`)
    return mockResult
  }

  // 执行文件节点
  private async executeFileNode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    const nodeType = node.data?.nodeType || node.type

    this.addLog(step, 'info', `执行文件操作: ${nodeType}`)
    step.progress = 25
    this.updateProgress()

    // 模拟文件处理
    await this.delay(1500)
    step.progress = 75
    this.updateProgress()

    const result = {
      type: nodeType,
      fileName: config.fileName || 'output.file',
      size: Math.floor(Math.random() * 1000000),
      status: 'completed'
    }

    step.progress = 90
    this.updateProgress()
    this.addLog(step, 'info', `文件操作完成: ${result.fileName}`)
    return result
  }

  // 执行渲染节点
  private async executeRenderNode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    const nodeType = node.data?.nodeType || node.type

    this.addLog(step, 'info', `生成图表: ${nodeType}`)
    step.progress = 30
    this.updateProgress()

    // 模拟图表生成
    await this.delay(800)
    step.progress = 80
    this.updateProgress()

    const result = {
      type: nodeType,
      chartData: config.data || [],
      imageUrl: `/charts/${Date.now()}.png`,
      status: 'rendered'
    }

    step.progress = 90
    this.updateProgress()
    this.addLog(step, 'info', `图表生成完成: ${result.imageUrl}`)
    return result
  }

  // 执行数据处理节点
  private async executeDataNode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    const nodeType = node.data?.nodeType || node.type

    this.addLog(step, 'info', `数据处理: ${nodeType}`)
    step.progress = 35
    this.updateProgress()

    // 模拟数据处理
    await this.delay(600)
    step.progress = 85
    this.updateProgress()

    const result = {
      type: nodeType,
      processedRecords: Math.floor(Math.random() * 1000),
      status: 'processed'
    }

    step.progress = 90
    this.updateProgress()
    this.addLog(step, 'info', `数据处理完成: ${result.processedRecords} 条记录`)
    return result
  }

  // 执行HTTP节点
  private async executeHttpNode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    
    this.addLog(step, 'info', `发送HTTP请求: ${config.method || 'GET'} ${config.url}`)
    
    // 模拟HTTP请求
    await this.delay(Math.random() * 1000 + 200)
    
    const mockResult = {
      status: 200,
      statusText: 'OK',
      data: { message: 'HTTP请求成功' },
      headers: { 'content-type': 'application/json' }
    }
    
    this.addLog(step, 'info', `HTTP请求完成, 状态码: ${mockResult.status}`)
    return mockResult
  }

  // 执行延时节点
  private async executeDelayNode(node: Node, step: ExecutionStep): Promise<any> {
    const config = node.data?.config || {}
    const duration = config.duration || 1000
    
    this.addLog(step, 'info', `开始延时: ${duration}ms`)
    await this.delay(duration)
    this.addLog(step, 'info', `延时结束`)
    
    return { duration, timestamp: Date.now() }
  }

  // 执行后续节点
  private async executeNextNodes(currentNodeId: string, currentResult: any): Promise<void> {
    const outgoingEdges = this.edges.filter(edge => edge.source === currentNodeId)

    for (const edge of outgoingEdges) {
      // 检查边的条件（如果有）
      if (this.shouldExecuteEdge(edge, currentResult)) {
        // 触发连接线遍历事件（用于动画）
        this.emit('edge-traverse', {
          edge,
          data: currentResult,
          sourceNode: currentNodeId,
          targetNode: edge.target
        })

        // 传递数据到下一个节点
        this.context.variables[`${currentNodeId}_output`] = currentResult

        // 添加小延迟以显示动画效果
        await this.delay(300)

        await this.executeNode(edge.target)
      }
    }
  }

  // 检查是否应该执行边
  private shouldExecuteEdge(edge: Edge, nodeResult: any): boolean {
    // 如果是条件节点的输出边，需要检查条件
    if (edge.sourceHandle === 'true') {
      return nodeResult?.result === true
    } else if (edge.sourceHandle === 'false') {
      return nodeResult?.result === false
    }
    
    // 默认执行
    return true
  }

  // 条件评估
  private evaluateCondition(condition: string): boolean {
    try {
      // 简单的条件评估，实际应用中需要更安全的表达式解析
      // 这里只是示例，不要在生产环境中使用eval
      return Math.random() > 0.5 // 随机返回true/false用于演示
    } catch (error) {
      return false
    }
  }

  // 添加日志
  private addLog(step: ExecutionStep, level: 'debug' | 'info' | 'warn' | 'error', message: string) {
    step.logs.push({
      level,
      message,
      timestamp: Date.now()
    })
    
    this.emit('log', { step, level, message })
  }

  // 延时工具方法
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

}
