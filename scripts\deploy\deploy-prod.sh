#!/bin/bash

# XHC AI Plus 生产环境部署脚本
# 用于生产环境的安全部署

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
ENV_FILE="/etc/xhcai-plus/.env"
SERVICE_NAME="xhcai-plus"
SERVICE_USER="xhcai"
INSTALL_DIR="/opt/xhcai-plus"
LOG_DIR="/var/log/xhcai-plus"
PID_FILE="/var/run/xhcai-plus.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    logger -t "$SERVICE_NAME" "INFO: $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    logger -t "$SERVICE_NAME" "SUCCESS: $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    logger -t "$SERVICE_NAME" "WARNING: $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    logger -t "$SERVICE_NAME" "ERROR: $1"
}

# 检查运行权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 root 权限运行此脚本"
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装，请先安装 Java 17 或更高版本"
        exit 1
    fi
    
    # 检查 Java 版本
    java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$java_version" -lt 17 ]; then
        log_error "Java 版本过低，需要 Java 17 或更高版本"
        exit 1
    fi
    
    # 检查 systemctl
    if ! command -v systemctl &> /dev/null; then
        log_error "systemctl 未找到，请确保使用 systemd 系统"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建系统用户
create_system_user() {
    log_info "创建系统用户..."
    
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd -r -s /bin/false -d "$INSTALL_DIR" "$SERVICE_USER"
        log_success "已创建系统用户: $SERVICE_USER"
    else
        log_info "系统用户已存在: $SERVICE_USER"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$(dirname "$ENV_FILE")"
    
    chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR"
    chown -R "$SERVICE_USER:$SERVICE_USER" "$LOG_DIR"
    
    log_success "目录结构创建完成"
}

# 验证环境配置文件
validate_env_file() {
    log_info "验证环境配置文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境配置文件不存在: $ENV_FILE"
        log_error "请先创建环境配置文件"
        exit 1
    fi
    
    # 检查文件权限
    if [ "$(stat -c %a "$ENV_FILE")" != "600" ]; then
        log_warning "环境配置文件权限不安全，正在修复..."
        chmod 600 "$ENV_FILE"
        chown root:root "$ENV_FILE"
    fi
    
    # 加载环境变量
    export $(grep -v '^#' "$ENV_FILE" | grep -v '^$' | xargs)
    
    # 验证必需变量
    required_vars=(
        "DB_HOST" "DB_PORT" "DB_NAME" "DB_USERNAME" "DB_PASSWORD"
        "REDIS_HOST" "REDIS_PORT" "JWT_SECRET"
        "OPENAI_API_KEY" "DIFY_API_KEY"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        log_error "缺少必需的环境变量: ${missing_vars[*]}"
        exit 1
    fi
    
    log_success "环境配置验证通过"
}

# 构建应用
build_application() {
    log_info "构建应用..."
    
    cd "$PROJECT_ROOT"
    
    # 清理并打包
    mvn clean package -DskipTests -Pprod
    
    if [ $? -eq 0 ]; then
        log_success "应用构建成功"
    else
        log_error "应用构建失败"
        exit 1
    fi
    
    # 复制 JAR 文件
    cp "$PROJECT_ROOT/admin-api/target/admin-api-*.jar" "$INSTALL_DIR/xhcai-plus.jar"
    chown "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR/xhcai-plus.jar"
    
    log_success "应用文件部署完成"
}

# 创建 systemd 服务文件
create_systemd_service() {
    log_info "创建 systemd 服务..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=XHC AI Plus Application
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR
ExecStart=/usr/bin/java -jar $INSTALL_DIR/xhcai-plus.jar
ExecStop=/bin/kill -TERM \$MAINPID
EnvironmentFile=$ENV_FILE
Environment=SPRING_PROFILES_ACTIVE=prod
Environment=JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME
PIDFile=$PID_FILE
TimeoutStopSec=30

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$LOG_DIR
ReadWritePaths=/tmp

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    log_success "systemd 服务创建完成"
}

# 配置日志轮转
configure_log_rotation() {
    log_info "配置日志轮转..."
    
    cat > "/etc/logrotate.d/$SERVICE_NAME" << EOF
$LOG_DIR/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $SERVICE_USER $SERVICE_USER
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF

    log_success "日志轮转配置完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        ufw allow "${SERVER_PORT:-8000}/tcp"
        log_success "UFW 防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port="${SERVER_PORT:-8000}/tcp"
        firewall-cmd --reload
        log_success "firewalld 防火墙规则已添加"
    else
        log_warning "未检测到防火墙，请手动配置端口 ${SERVER_PORT:-8000}"
    fi
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 10
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
        systemctl status "$SERVICE_NAME" --no-pager
    else
        log_error "服务启动失败"
        journalctl -u "$SERVICE_NAME" --no-pager -n 20
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "http://localhost:${SERVER_PORT:-8000}/actuator/health" > /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        log_info "健康检查失败，重试 ($attempt/$max_attempts)..."
        sleep 5
        ((attempt++))
    done
    
    log_error "健康检查失败，服务可能未正常启动"
    return 1
}

# 主函数
main() {
    log_info "开始部署 XHC AI Plus 生产环境..."
    
    check_permissions
    check_dependencies
    create_system_user
    create_directories
    validate_env_file
    build_application
    create_systemd_service
    configure_log_rotation
    configure_firewall
    start_service
    
    if health_check; then
        log_success "生产环境部署完成！"
        log_info "服务状态: systemctl status $SERVICE_NAME"
        log_info "服务日志: journalctl -u $SERVICE_NAME -f"
        log_info "应用访问地址: http://localhost:${SERVER_PORT:-8000}"
    else
        log_error "部署完成但健康检查失败，请检查日志"
        exit 1
    fi
}

# 处理中断信号
trap 'log_warning "部署被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
