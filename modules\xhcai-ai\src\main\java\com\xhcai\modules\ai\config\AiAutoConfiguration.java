package com.xhcai.modules.ai.config;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;

import com.xhcai.common.core.config.YamlPropertySourceFactory;

/**
 * AI模块自动配置类 负责AI模块的组件扫描、实体扫描、Mapper扫描和配置文件加载
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@AutoConfiguration
@ComponentScan(basePackages = {
    "com.xhcai.modules.ai"
})
@EntityScan(basePackages = "com.xhcai.modules.ai.entity")
@MapperScan(basePackages = "com.xhcai.modules.ai.mapper")
@ConfigurationPropertiesScan(basePackages = "com.xhcai.modules.ai.config")
@PropertySource(value = "classpath:application-xhcai-ai.yml", factory = YamlPropertySourceFactory.class)
public class AiAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(AiAutoConfiguration.class);

    public AiAutoConfiguration() {
        log.info("=== AI模块自动配置已启用 ===");
        log.info("组件扫描包: com.xhcai.modules.ai.*");
        log.info("实体扫描包: com.xhcai.modules.ai.entity");
        log.info("Mapper扫描包: com.xhcai.modules.ai.mapper");
        log.info("配置文件: application-xhcai-ai.yml");
    }
}
