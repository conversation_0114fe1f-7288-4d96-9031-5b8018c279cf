<template>
  <div class="knowledge-graph bg-white min-h-screen overflow-x-auto">
    <!-- 页面头部 -->
    <div class="header bg-gradient-to-r from-purple-50 to-pink-50 border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-6 py-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">知识图谱</h1>
            <p class="text-gray-600">探索和管理您的知识关系网络</p>
          </div>
          <div class="flex items-center gap-4">
            <button
              @click="showCreateModal = true"
              class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
            >
              <span class="mr-2">➕</span>
              新建图谱
            </button>
            <button
              @click="refreshGraphs"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <span class="mr-2">🔄</span>
              刷新
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-full mx-auto px-6 py-8">
      <!-- 统计信息 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-blue-100 text-sm">总图谱数</p>
              <p class="text-2xl font-bold">{{ statistics.totalGraphs }}</p>
            </div>
            <span class="text-3xl opacity-80">🕸️</span>
          </div>
        </div>
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-green-100 text-sm">总节点数</p>
              <p class="text-2xl font-bold">{{ statistics.totalNodes }}</p>
            </div>
            <span class="text-3xl opacity-80">🔵</span>
          </div>
        </div>
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-purple-100 text-sm">总关系数</p>
              <p class="text-2xl font-bold">{{ statistics.totalEdges }}</p>
            </div>
            <span class="text-3xl opacity-80">🔗</span>
          </div>
        </div>
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-orange-100 text-sm">活跃图谱</p>
              <p class="text-2xl font-bold">{{ statistics.activeGraphs }}</p>
            </div>
            <span class="text-3xl opacity-80">⚡</span>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="mb-8">
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索知识图谱..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
              <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex gap-2">
            <select
              v-model="selectedCategory"
              class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="">所有类别</option>
              <option value="business">业务知识</option>
              <option value="technical">技术文档</option>
              <option value="product">产品信息</option>
              <option value="customer">客户关系</option>
            </select>
            <select
              v-model="sortBy"
              class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="updated">最近更新</option>
              <option value="created">创建时间</option>
              <option value="name">名称</option>
              <option value="nodes">节点数量</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 知识图谱列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <KnowledgeGraphCard
          v-for="graph in filteredGraphs"
          :key="graph.id"
          :graph="graph"
          @view="viewGraph"
          @edit="editGraph"
          @delete="deleteGraph"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="filteredGraphs.length === 0" class="text-center py-12">
        <div class="text-6xl mb-4">🕸️</div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无知识图谱</h3>
        <p class="text-gray-500 mb-6">创建您的第一个知识图谱，开始构建知识关系网络</p>
        <button
          @click="showCreateModal = true"
          class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
        >
          创建知识图谱
        </button>
      </div>
    </div>

    <!-- 创建图谱模态框 -->
    <CreateGraphModal
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @create="handleCreateGraph"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import KnowledgeGraphCard from './components/KnowledgeGraphCard.vue'
import CreateGraphModal from './components/CreateGraphModal.vue'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('updated')
const showCreateModal = ref(false)

// 统计信息
const statistics = ref({
  totalGraphs: 6,
  totalNodes: 3248,
  totalEdges: 8567,
  activeGraphs: 6
})

// 知识图谱数据
const knowledgeGraphs = ref([
  {
    id: 'kg-1',
    name: '案件关系知识图谱',
    description: '涵盖案件当事人、证据、时间线、法律条文等复杂关系网络，支持案件分析和关联发现',
    category: 'business',
    nodeCount: 856,
    edgeCount: 2134,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    status: 'active',
    thumbnail: '⚖️'
  },
  {
    id: 'kg-2',
    name: '人员关系知识图谱',
    description: '构建人员身份、社会关系、行为轨迹、通信记录等多维度关系网络，用于人员画像分析',
    category: 'business',
    nodeCount: 1245,
    edgeCount: 3567,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18',
    status: 'active',
    thumbnail: '👤'
  },
  {
    id: 'kg-3',
    name: '车辆图知识图谱',
    description: '整合车辆信息、行驶轨迹、违章记录、保险理赔等数据，构建车辆全生命周期关系图',
    category: 'business',
    nodeCount: 678,
    edgeCount: 1456,
    createdAt: '2024-01-08',
    updatedAt: '2024-01-19',
    status: 'active',
    thumbnail: '🚗'
  },
  {
    id: 'kg-4',
    name: '金融风控知识图谱',
    description: '基于交易行为、资金流向、风险事件等构建的金融风险识别和预警关系网络',
    category: 'business',
    nodeCount: 423,
    edgeCount: 1234,
    createdAt: '2024-01-05',
    updatedAt: '2024-01-17',
    status: 'active',
    thumbnail: '💰'
  },
  {
    id: 'kg-5',
    name: '企业关联知识图谱',
    description: '企业股权结构、高管关系、业务往来、投资关系等企业间复杂关联关系分析',
    category: 'business',
    nodeCount: 567,
    edgeCount: 1678,
    createdAt: '2024-01-03',
    updatedAt: '2024-01-16',
    status: 'active',
    thumbnail: '🏢'
  },
  {
    id: 'kg-6',
    name: '网络安全威胁图谱',
    description: '网络攻击链、恶意软件家族、攻击者画像、漏洞利用等网络安全威胁情报关系网络',
    category: 'technical',
    nodeCount: 789,
    edgeCount: 2345,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15',
    status: 'active',
    thumbnail: '🛡️'
  }
])

// 计算属性
const filteredGraphs = computed(() => {
  let filtered = knowledgeGraphs.value

  // 按类别筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(graph => graph.category === selectedCategory.value)
  }

  // 按搜索词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(graph =>
      graph.name.toLowerCase().includes(query) ||
      graph.description.toLowerCase().includes(query)
    )
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'created':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case 'nodes':
        return b.nodeCount - a.nodeCount
      case 'updated':
      default:
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    }
  })

  return filtered
})

// 生命周期
onMounted(() => {
  loadKnowledgeGraphs()
})

// 事件处理
const loadKnowledgeGraphs = async () => {
  // 模拟加载数据
  console.log('Loading knowledge graphs...')
}

const refreshGraphs = () => {
  loadKnowledgeGraphs()
}

const viewGraph = (graph: any) => {
  console.log('Viewing graph:', graph.name, 'ID:', graph.id)
  router.push({
    name: 'KnowledgeGraphDetail',
    params: { id: graph.id }
  })
}

const editGraph = (graph: any) => {
  console.log('Edit graph:', graph)
}

const deleteGraph = (graph: any) => {
  console.log('Delete graph:', graph)
  // 实现删除逻辑
}

const handleCreateGraph = (graphData: any) => {
  console.log('Create graph:', graphData)
  showCreateModal.value = false
  // 实现创建逻辑
}
</script>

<style scoped>
.header {
  background: linear-gradient(135deg, #faf5ff 0%, #fdf2f8 100%);
}
</style>
