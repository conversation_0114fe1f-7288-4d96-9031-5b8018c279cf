package com.xhcai.modules.system.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.modules.system.dto.ModuleInitDTO;
import com.xhcai.modules.system.service.IModuleDiscoveryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模块发现服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class ModuleDiscoveryServiceImpl implements IModuleDiscoveryService {

    private static final Logger log = LoggerFactory.getLogger(ModuleDiscoveryServiceImpl.class);

    private final Map<String, ModuleInitDTO> moduleCache = new ConcurrentHashMap<>();

    @Override
    public List<ModuleInitDTO> getAllModules() {
        if (moduleCache.isEmpty()) {
            initializeModuleCache();
        }
        return new ArrayList<>(moduleCache.values());
    }

    @Override
    public ModuleInitDTO getModuleInfo(String moduleId) {
        if (moduleCache.isEmpty()) {
            initializeModuleCache();
        }
        return moduleCache.get(moduleId);
    }

    @Override
    public void refreshModules() {
        log.info("刷新模块列表");
        moduleCache.clear();
        initializeModuleCache();
    }

    /**
     * 初始化模块缓存
     */
    private void initializeModuleCache() {
        log.info("初始化模块缓存");
        
        // 手动添加已知模块
        addModuleToCache("system", "系统模块", "/api/system/init", 100, 
            Arrays.asList("字典初始化", "权限初始化", "基础数据初始化"));
            
        addModuleToCache("ai", "AI模块", "/api/ai/init", 200, 
            Arrays.asList("AI权限初始化", "AI字典初始化", "AI模型配置"));
            
        addModuleToCache("dify", "Dify模块", "/api/dify/init", 300, 
            Arrays.asList("Dify权限初始化", "智能体管理", "知识库管理"));
            
        addModuleToCache("agent", "智能体模块", "/api/agent/init", 150, 
            Arrays.asList("智能体权限初始化", "智能体模板", "智能体配置"));
            
        addModuleToCache("rag", "RAG模块", "/api/rag/init", 400, 
            Arrays.asList("知识库初始化", "向量存储配置", "检索配置"));
        
        log.info("模块缓存初始化完成，共 {} 个模块", moduleCache.size());
    }

    /**
     * 添加模块到缓存
     */
    private void addModuleToCache(String moduleId, String moduleName, String apiPrefix, int order, List<String> features) {
        ModuleInitDTO module = new ModuleInitDTO();
        module.setModuleId(moduleId);
        module.setModuleName(moduleName);
        module.setDescription(moduleName + "初始化器");
        module.setVersion("1.0.0");
        module.setAuthor("xhcai");
        module.setOrder(order);
        module.setManualInit(true);
        module.setApiPrefix(apiPrefix);
        module.setInitialized(false);
        module.setStatus("NOT_INITIALIZED");
        module.setProgress(0);
        module.setFeatures(features);
        
        moduleCache.put(moduleId, module);
    }
}
