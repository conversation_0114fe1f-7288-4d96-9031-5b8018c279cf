<template>
  <div class="thinking-block">
    <!-- 思考中状态 -->
    <div 
      v-if="isThinking"
      class="thinking-header thinking-active"
      @click="toggleExpanded"
    >
      <div class="thinking-indicator">
        <div class="thinking-dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
        <span class="thinking-text">思考中...</span>
      </div>
      <el-icon class="expand-icon" :class="{ 'expanded': isExpanded }">
        <ArrowDown />
      </el-icon>
    </div>

    <!-- 思考完成状态 -->
    <div 
      v-else
      class="thinking-header thinking-completed"
      @click="toggleExpanded"
    >
      <div class="thinking-indicator">
        <el-icon class="thinking-icon">
          <ChatDotRound />
        </el-icon>
        <span class="thinking-text">思考过程</span>
      </div>
      <el-icon class="expand-icon" :class="{ 'expanded': isExpanded }">
        <ArrowDown />
      </el-icon>
    </div>

    <!-- 思考内容 -->
    <div 
      v-show="isExpanded"
      class="thinking-content"
      :class="{ 'thinking-streaming': isThinking }"
    >
      <div class="thinking-text-content">
        {{ content }}
        <span v-if="isThinking && streaming" class="thinking-cursor">|</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ArrowDown, ChatDotRound } from '@element-plus/icons-vue'

// Props定义
const props = defineProps<{
  content: string
  streaming?: boolean
  completed?: boolean
}>()

// 响应式数据
const isExpanded = ref(false)

// 计算属性
const isThinking = computed(() => !props.completed && props.streaming)

// 监听思考状态变化，自动展开/合上
watch(isThinking, (newIsThinking) => {
  if (newIsThinking) {
    // 开始思考时自动展开
    isExpanded.value = true
  } else {
    // 思考结束时自动合上
    isExpanded.value = false
  }
}, { immediate: true })

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style scoped>
.thinking-block {
  margin: 12px 0;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
  overflow: hidden;
  transition: all 0.3s ease;
}

.thinking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.thinking-header:hover {
  background: #f0f2f5;
}

.thinking-active {
  background: linear-gradient(90deg, #e3f2fd 0%, #f3e5f5 100%);
  border-bottom: 1px solid #e1e5e9;
}

.thinking-completed {
  background: #f5f5f5;
}

.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #1976d2;
  animation: thinking-pulse 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes thinking-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.thinking-icon {
  color: #666;
  font-size: 16px;
}

.thinking-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.thinking-active .thinking-text {
  color: #1976d2;
}

.expand-icon {
  color: #666;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.thinking-content {
  border-top: 1px solid #e1e5e9;
  background: #fff;
  animation: slideDown 0.3s ease;
}

.thinking-streaming {
  background: #fafbfc;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

.thinking-text-content {
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.thinking-cursor {
  color: #1976d2;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .thinking-header {
    padding: 10px 12px;
  }
  
  .thinking-text-content {
    padding: 12px;
    font-size: 13px;
  }
  
  .thinking-text {
    font-size: 13px;
  }
}
</style>
