<template>
  <div class="alert-monitor space-y-6">
    <!-- 告警概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总告警数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ alertStats.total }}</p>
            <div class="text-xs text-gray-500 mt-1">
              严重: {{ alertStats.critical }} | 警告: {{ alertStats.warning }} | 信息: {{ alertStats.info }}
            </div>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-clock text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">待处理告警</p>
            <p class="text-2xl font-semibold text-gray-900">{{ alertStats.pending }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-check-circle text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已解决告警</p>
            <p class="text-2xl font-semibold text-gray-900">{{ alertStats.resolved }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-tachometer-alt text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均响应时间</p>
            <p class="text-2xl font-semibold text-gray-900">{{ alertStats.avgResponseTime }}分钟</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警统计分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 告警趋势图 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">告警趋势分析</h3>
        <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <div class="text-center text-gray-500">
            <i class="fas fa-chart-line text-4xl mb-2"></i>
            <p>告警趋势图表</p>
            <p class="text-sm">显示最近7天的告警数量变化</p>
          </div>
        </div>
      </div>

      <!-- 告警分类统计 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">告警分类统计</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
              <span class="text-sm text-gray-700">系统告警</span>
            </div>
            <span class="text-sm font-medium text-gray-900">15</span>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
              <span class="text-sm text-gray-700">性能告警</span>
            </div>
            <span class="text-sm font-medium text-gray-900">12</span>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
              <span class="text-sm text-gray-700">业务告警</span>
            </div>
            <span class="text-sm font-medium text-gray-900">8</span>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
              <span class="text-sm text-gray-700">插件告警</span>
            </div>
            <span class="text-sm font-medium text-gray-900">6</span>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
              <span class="text-sm text-gray-700">模型告警</span>
            </div>
            <span class="text-sm font-medium text-gray-900">4</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警列表管理 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">告警列表管理</h3>
          <div class="flex items-center gap-4">
            <select v-model="alertStatusFilter" class="form-select text-sm">
              <option value="">全部状态</option>
              <option value="pending">待处理</option>
              <option value="processing">处理中</option>
              <option value="resolved">已解决</option>
            </select>
            <select v-model="alertLevelFilter" class="form-select text-sm">
              <option value="">全部级别</option>
              <option value="critical">严重</option>
              <option value="warning">警告</option>
              <option value="info">信息</option>
            </select>
            <select v-model="alertTypeFilter" class="form-select text-sm">
              <option value="">全部类型</option>
              <option value="系统告警">系统告警</option>
              <option value="性能告警">性能告警</option>
              <option value="业务告警">业务告警</option>
              <option value="插件告警">插件告警</option>
              <option value="模型告警">模型告警</option>
            </select>
            <input
              v-model="alertSearchQuery"
              type="text"
              placeholder="搜索告警..."
              class="form-input text-sm w-64"
            />
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">告警信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">来源</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="alert in paginatedAlerts" :key="alert.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="max-w-xs">
                  <div class="text-sm font-medium text-gray-900">{{ alert.title }}</div>
                  <div class="text-sm text-gray-500 mt-1">{{ alert.description }}</div>
                  <div class="text-xs text-gray-400 mt-1">
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                      {{ alert.type }}
                    </span>
                    {{ formatDate(alert.createdAt) }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getAlertLevelClass(alert.level)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getAlertLevelText(alert.level) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ alert.source }}</div>
                <div class="text-sm text-gray-500">{{ alert.sourceType }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ alert.assignee }}</div>
                <div v-if="alert.responseTime" class="text-sm text-gray-500">响应: {{ alert.responseTime }}分钟</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getAlertStatusClass(alert.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getAlertStatusText(alert.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getNotificationStatusClass(alert.notificationStatus)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getNotificationStatusText(alert.notificationStatus) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center gap-2">
                  <button
                    v-if="alert.status === 'pending'"
                    @click="processAlert(alert.id)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    处理
                  </button>
                  <button
                    v-if="alert.status === 'processing'"
                    @click="resolveAlert(alert.id)"
                    class="text-green-600 hover:text-green-900"
                  >
                    解决
                  </button>
                  <button
                    @click="viewAlertDetail(alert.id)"
                    class="text-gray-600 hover:text-gray-900"
                  >
                    详情
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 告警分页 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredAlerts.length) }} 条，
            共 {{ filteredAlerts.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="text-sm text-gray-700">
              第 {{ currentPage }} / {{ totalPages }} 页
            </span>
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  refreshTrigger: 0
})

// 搜索和过滤
const alertSearchQuery = ref('')
const alertStatusFilter = ref('')
const alertTypeFilter = ref('')
const alertLevelFilter = ref('')

// 分页配置
const pageSize = ref(10)
const currentPage = ref(1)

// 告警统计数据
const alertStats = ref({
  total: 45,
  critical: 3,
  warning: 12,
  info: 30,
  resolved: 38,
  pending: 7,
  todayAlerts: 15,
  avgResponseTime: 8.5 // 分钟
})

// 告警监控数据
const alerts = ref([
  {
    id: 1,
    title: 'AI-Server-04 CPU使用率过高',
    type: '系统告警',
    level: 'critical',
    source: 'AI-Server-04',
    sourceType: 'server',
    description: 'CPU使用率持续超过80%，当前为78.9%',
    status: 'pending',
    createdAt: '2024-06-30 14:25:30',
    updatedAt: '2024-06-30 14:25:30',
    resolvedAt: null,
    assignee: '张三',
    responseTime: null,
    feedback: '',
    notificationStatus: 'sent',
    relatedMetrics: {
      cpuUsage: 78.9,
      memoryUsage: 89.2,
      threshold: 80
    }
  },
  {
    id: 2,
    title: '智能客服助手响应时间异常',
    type: '性能告警',
    level: 'warning',
    source: '智能客服助手',
    sourceType: 'agent',
    description: '平均响应时间超过预设阈值，当前为120ms，阈值为100ms',
    status: 'processing',
    createdAt: '2024-06-30 13:45:15',
    updatedAt: '2024-06-30 14:10:22',
    resolvedAt: null,
    assignee: '李四',
    responseTime: 25, // 分钟
    feedback: '正在调优模型参数',
    notificationStatus: 'sent',
    relatedMetrics: {
      avgResponseTime: 120,
      threshold: 100,
      todayRequests: 234
    }
  },
  {
    id: 3,
    title: '法律条文数据库召回率异常',
    type: '业务告警',
    level: 'warning',
    source: '法律条文数据库',
    sourceType: 'knowledge',
    description: '召回率低于预期，当前为89.5%，预期为90%以上',
    status: 'resolved',
    createdAt: '2024-06-30 12:30:45',
    updatedAt: '2024-06-30 13:15:30',
    resolvedAt: '2024-06-30 13:15:30',
    assignee: '王五',
    responseTime: 45, // 分钟
    feedback: '已重新索引知识库，召回率恢复正常',
    notificationStatus: 'sent',
    relatedMetrics: {
      recallRate: 89.5,
      threshold: 90,
      todayCalls: 45
    }
  }
])

// 计算属性：过滤后的数据
const filteredAlerts = computed(() => {
  let filtered = alerts.value

  if (alertStatusFilter.value) {
    filtered = filtered.filter(alert => alert.status === alertStatusFilter.value)
  }

  if (alertTypeFilter.value) {
    filtered = filtered.filter(alert => alert.type === alertTypeFilter.value)
  }

  if (alertLevelFilter.value) {
    filtered = filtered.filter(alert => alert.level === alertLevelFilter.value)
  }

  if (alertSearchQuery.value) {
    const query = alertSearchQuery.value.toLowerCase()
    filtered = filtered.filter(alert =>
      alert.title.toLowerCase().includes(query) ||
      alert.source.toLowerCase().includes(query) ||
      alert.assignee.toLowerCase().includes(query) ||
      alert.description.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 分页后的数据
const paginatedAlerts = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredAlerts.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredAlerts.value.length / pageSize.value)
})

// 方法
const updateAlertStats = () => {
  // 更新告警统计数据
  alertStats.value.total = alerts.value.length
  alertStats.value.critical = alerts.value.filter(alert => alert.level === 'critical').length
  alertStats.value.warning = alerts.value.filter(alert => alert.level === 'warning').length
  alertStats.value.info = alerts.value.filter(alert => alert.level === 'info').length
  alertStats.value.resolved = alerts.value.filter(alert => alert.status === 'resolved').length
  alertStats.value.pending = alerts.value.filter(alert => alert.status === 'pending').length

  // 计算今日告警数量
  const today = new Date().toISOString().slice(0, 10)
  alertStats.value.todayAlerts = alerts.value.filter(alert =>
    alert.createdAt.slice(0, 10) === today
  ).length

  // 计算平均响应时间（只计算已处理的告警）
  const processedAlerts = alerts.value.filter(alert => alert.responseTime !== null)
  if (processedAlerts.length > 0) {
    alertStats.value.avgResponseTime = Math.round(
      processedAlerts.reduce((sum, alert) => sum + (alert.responseTime || 0), 0) / processedAlerts.length * 10
    ) / 10
  }
}

// 告警相关方法
const getAlertLevelClass = (level: string) => {
  const classMap: Record<string, string> = {
    'critical': 'bg-red-100 text-red-800',
    'warning': 'bg-yellow-100 text-yellow-800',
    'info': 'bg-blue-100 text-blue-800'
  }
  return classMap[level] || 'bg-gray-100 text-gray-800'
}

const getAlertLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    'critical': '严重',
    'warning': '警告',
    'info': '信息'
  }
  return textMap[level] || level
}

const getAlertStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    'pending': 'bg-red-100 text-red-800',
    'processing': 'bg-yellow-100 text-yellow-800',
    'resolved': 'bg-green-100 text-green-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getAlertStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'resolved': '已解决'
  }
  return textMap[status] || status
}

const getNotificationStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    'sent': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'pending': 'bg-yellow-100 text-yellow-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getNotificationStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'sent': '已发送',
    'failed': '发送失败',
    'pending': '待发送'
  }
  return textMap[status] || status
}

const processAlert = (alertId: number) => {
  const alert = alerts.value.find(a => a.id === alertId)
  if (alert) {
    alert.status = 'processing'
    alert.updatedAt = new Date().toISOString().slice(0, 19).replace('T', ' ')
    alert.responseTime = Math.floor((new Date().getTime() - new Date(alert.createdAt).getTime()) / 60000)
    console.log(`开始处理告警: ${alert.title}`)
  }
}

const resolveAlert = (alertId: number) => {
  const alert = alerts.value.find(a => a.id === alertId)
  if (alert) {
    alert.status = 'resolved'
    alert.resolvedAt = new Date().toISOString().slice(0, 19).replace('T', ' ')
    alert.updatedAt = alert.resolvedAt
    console.log(`告警已解决: ${alert.title}`)

    // 更新统计数据
    alertStats.value.resolved++
    alertStats.value.pending = Math.max(0, alertStats.value.pending - 1)
  }
}

const viewAlertDetail = (alertId: number) => {
  const alert = alerts.value.find(a => a.id === alertId)
  if (alert) {
    console.log('查看告警详情:', alert)
    // 这里可以打开告警详情弹窗或跳转到详情页面
  }
}

// 分页方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const resetPagination = () => {
  currentPage.value = 1
}

// 时间格式化方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 监听搜索和过滤条件变化，重置分页
watch([alertSearchQuery, alertStatusFilter, alertTypeFilter, alertLevelFilter], () => {
  resetPagination()
})

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  updateAlertStats()
})

onMounted(() => {
  updateAlertStats()
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-input, .form-select {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  background-color: #f8fafc;
  transform: scale(1.01);
}
</style>
