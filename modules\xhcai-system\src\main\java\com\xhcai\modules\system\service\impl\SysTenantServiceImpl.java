package com.xhcai.modules.system.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.datasource.plugin.TenantContextHolder;
import com.xhcai.modules.system.dto.SysTenantQueryDTO;
import com.xhcai.modules.system.entity.SysTenant;
import com.xhcai.modules.system.mapper.SysTenantMapper;
import com.xhcai.modules.system.service.ISysTenantConfigService;
import com.xhcai.modules.system.service.ISysTenantService;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.vo.SysTenantVO;

/**
 * 租户信息服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysTenantServiceImpl extends ServiceImpl<SysTenantMapper, SysTenant> implements ISysTenantService {

    private static final Logger log = LoggerFactory.getLogger(SysTenantServiceImpl.class);

    @Autowired
    private SysTenantMapper tenantMapper;

    @Autowired
    private ISysTenantConfigService tenantConfigService;

    @Override
    public PageResult<SysTenantVO> selectTenantPage(SysTenantQueryDTO queryDTO) {
        // 只有平台管理员可以查看所有租户
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法查看租户列表");
        }

        Page<SysTenant> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

        // 平台管理员可以直接查询所有租户（租户表不需要租户隔离）
        IPage<SysTenant> tenantPage = tenantMapper.selectTenantPage(
                page,
                queryDTO.getTenantCode(),
                queryDTO.getTenantName(),
                queryDTO.getContactPerson(),
                queryDTO.getContactPhone(),
                queryDTO.getContactEmail(),
                queryDTO.getStatus(),
                queryDTO.getIncludeExpired()
        );

        List<SysTenantVO> tenantVOList = tenantPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<>(tenantVOList, tenantPage.getTotal(), tenantPage.getCurrent(), tenantPage.getSize());
    }

    @Override
    public SysTenant selectByTenantCode(String tenantCode) {
        if (!StringUtils.hasText(tenantCode)) {
            return null;
        }

        // 租户表不需要租户隔离，直接查询
        return tenantMapper.selectByTenantCode(tenantCode);
    }

    @Override
    public SysTenant selectByDomain(String domain) {
        if (!StringUtils.hasText(domain)) {
            return null;
        }

        // 租户表不需要租户隔离，直接查询
        return tenantMapper.selectByDomain(domain);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTenant(SysTenant tenant) {
        // 只有平台管理员可以创建租户
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法创建租户");
        }

        // 参数校验
        validateTenant(tenant, true);

        // 检查租户编码是否已存在
        if (existsTenantCode(tenant.getTenantCode(), null)) {
            throw new BusinessException("租户编码已存在");
        }

        // 检查域名是否已存在
        if (StringUtils.hasText(tenant.getDomain()) && existsDomain(tenant.getDomain(), null)) {
            throw new BusinessException("租户域名已存在");
        }

        // 设置默认值
        if (!StringUtils.hasText(tenant.getStatus())) {
            tenant.setStatus(CommonConstants.TENANT_STATUS_NORMAL);
        }
        if (tenant.getUserLimit() == null) {
            tenant.setUserLimit(100);
        }
        if (tenant.getStorageLimit() == null) {
            tenant.setStorageLimit(1024L);
        }

        // 直接保存租户（租户表本身不需要租户隔离）
        boolean result = save(tenant);

        if (result) {
            // 初始化租户数据
            try {
                initializeTenantData(tenant.getId(), "admin", "123456", tenant.getContactEmail());
            } catch (Exception e) {
                log.error("初始化租户数据失败: {}", e.getMessage(), e);
                // 如果初始化失败，删除已创建的租户
                removeById(tenant.getId());
                throw new BusinessException("创建租户失败：初始化数据异常");
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTenant(SysTenant tenant) {
        // 只有平台管理员可以更新租户
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法更新租户");
        }

        // 参数校验
        validateTenant(tenant, false);

        // 检查租户是否存在
        SysTenant existTenant = getById(tenant.getId());
        if (existTenant == null) {
            throw new BusinessException("租户不存在");
        }

        // 检查租户编码是否已存在（排除自己）
        if (existsTenantCode(tenant.getTenantCode(), tenant.getId())) {
            throw new BusinessException("租户编码已存在");
        }

        // 检查域名是否已存在（排除自己）
        if (StringUtils.hasText(tenant.getDomain()) && existsDomain(tenant.getDomain(), tenant.getId())) {
            throw new BusinessException("租户域名已存在");
        }

        return updateById(tenant);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTenants(List<String> tenantIds) {
        // 只有平台管理员可以删除租户
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法删除租户");
        }

        if (CollectionUtils.isEmpty(tenantIds)) {
            return false;
        }

        // 检查是否包含平台租户
        for (String tenantId : tenantIds) {
            if (tenantId.startsWith(CommonConstants.PLATFORM_TENANT_PREFIX)) {
                throw new BusinessException("不能删除平台租户");
            }
        }

        // 逻辑删除租户
        return removeByIds(tenantIds);
    }

    @Override
    public boolean enableTenant(String tenantId) {
        return updateTenantStatus(tenantId, CommonConstants.TENANT_STATUS_NORMAL);
    }

    @Override
    public boolean disableTenant(String tenantId) {
        return updateTenantStatus(tenantId, CommonConstants.TENANT_STATUS_DISABLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean renewTenant(String tenantId, Integer months) {
        // 只有平台管理员可以续期租户
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法续期租户");
        }

        if (tenantId == null || months == null || months <= 0) {
            throw new BusinessException("参数错误");
        }

        // 租户表不需要租户隔离，直接查询
        SysTenant tenant = getById(tenantId);
        if (tenant == null) {
            throw new BusinessException("租户不存在");
        }

        // 计算新的过期时间
        LocalDateTime newExpireTime;
        if (tenant.getExpireTime() == null || tenant.getExpireTime().isBefore(LocalDateTime.now())) {
            // 如果没有过期时间或已过期，从当前时间开始计算
            newExpireTime = LocalDateTime.now().plusMonths(months);
        } else {
            // 如果还未过期，从原过期时间开始计算
            newExpireTime = tenant.getExpireTime().plusMonths(months);
        }

        tenant.setExpireTime(newExpireTime);
        // 如果租户已过期，重新启用
        if (CommonConstants.TENANT_STATUS_EXPIRED.equals(tenant.getStatus())) {
            tenant.setStatus(CommonConstants.TENANT_STATUS_NORMAL);
        }

        // 租户表不需要租户隔离，直接更新
        return updateById(tenant);
    }

    @Override
    public boolean existsTenantCode(String tenantCode, String excludeId) {
        if (!StringUtils.hasText(tenantCode)) {
            return false;
        }

        // 租户表不需要租户隔离，直接查询
        return tenantMapper.existsTenantCode(tenantCode, excludeId) > 0;
    }

    @Override
    public boolean existsDomain(String domain, String excludeId) {
        if (!StringUtils.hasText(domain)) {
            return false;
        }

        // 租户表不需要租户隔离，直接查询
        return tenantMapper.existsDomain(domain, excludeId) > 0;
    }

    @Override
    public SysTenantVO getTenantStatistics(String tenantId) {
        if (tenantId == null) {
            return null;
        }

        // 租户表不需要租户隔离，直接查询
        SysTenant tenant = getById(tenantId);
        if (tenant == null) {
            return null;
        }

        SysTenantVO tenantVO = convertToVO(tenant);

        // 获取用户数量统计
        try {
            Integer userCount = TenantContextHolder.callWithTenant(tenantId, ()
                    -> tenantMapper.countUsersByTenantId(tenantId)
            );
            tenantVO.setCurrentUserCount(userCount);
        } catch (Exception e) {
            log.warn("获取租户 {} 用户数量统计失败: {}", tenantId, e.getMessage());
            tenantVO.setCurrentUserCount(0);
        }

        return tenantVO;
    }

    /**
     * 参数校验
     */
    private void validateTenant(SysTenant tenant, boolean isCreate) {
        if (tenant == null) {
            throw new BusinessException("租户信息不能为空");
        }

        if (!StringUtils.hasText(tenant.getTenantCode())) {
            throw new BusinessException("租户编码不能为空");
        }

        if (!StringUtils.hasText(tenant.getTenantName())) {
            throw new BusinessException("租户名称不能为空");
        }

        // 其他校验逻辑...
    }

    /**
     * 更新租户状态
     */
    private boolean updateTenantStatus(String tenantId, String status) {
        // 只有平台管理员可以更新租户状态
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法更新租户状态");
        }

        if (tenantId == null) {
            throw new BusinessException("租户ID不能为空");
        }

        // 租户表不需要租户隔离，直接查询和更新
        SysTenant tenant = getById(tenantId);
        if (tenant == null) {
            throw new BusinessException("租户不存在");
        }

        tenant.setStatus(status);
        return updateById(tenant);
    }

    /**
     * 转换为VO
     */
    private SysTenantVO convertToVO(SysTenant tenant) {
        if (tenant == null) {
            return null;
        }

        SysTenantVO vo = new SysTenantVO();
        BeanUtils.copyProperties(tenant, vo);

        // 设置状态名称
        switch (tenant.getStatus()) {
            case CommonConstants.TENANT_STATUS_NORMAL:
                vo.setStatusName("正常");
                break;
            case CommonConstants.TENANT_STATUS_DISABLED:
                vo.setStatusName("停用");
                break;
            case CommonConstants.TENANT_STATUS_EXPIRED:
                vo.setStatusName("过期");
                break;
            default:
                vo.setStatusName("未知");
        }

        // 检查是否过期
        if (tenant.getExpireTime() != null) {
            vo.setIsExpired(tenant.getExpireTime().isBefore(LocalDateTime.now()));
        } else {
            vo.setIsExpired(false);
        }

        return vo;
    }

    @Override
    public List<SysTenantVO> getExpiringTenants(Integer days) {
        final Integer finalDays = (days == null || days <= 0) ? 7 : days; // 默认7天

        // 租户表不需要租户隔离，直接查询
        List<SysTenant> tenants = tenantMapper.selectExpiringTenants(finalDays);

        return tenants.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysTenantVO> getExpiredTenants() {
        // 租户表不需要租户隔离，直接查询
        List<SysTenant> tenants = tenantMapper.selectExpiredTenants();

        return tenants.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int processExpiredTenants() {
        // 只有平台管理员可以处理过期租户
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法处理过期租户");
        }

        // 租户表不需要租户隔离，直接查询和更新
        List<SysTenant> expiredTenants = tenantMapper.selectExpiredTenants();

        if (expiredTenants.isEmpty()) {
            log.info("没有找到需要处理的过期租户");
            return 0;
        }

        int processedCount = 0;
        for (SysTenant tenant : expiredTenants) {
            try {
                tenant.setStatus(CommonConstants.TENANT_STATUS_EXPIRED);
                boolean updated = updateById(tenant);
                if (updated) {
                    processedCount++;
                    log.info("租户 {} ({}) 已设置为过期状态", tenant.getTenantName(), tenant.getTenantCode());
                } else {
                    log.warn("更新租户 {} ({}) 过期状态失败", tenant.getTenantName(), tenant.getTenantCode());
                }
            } catch (Exception e) {
                log.error("处理过期租户 {} ({}) 时发生异常: {}", tenant.getTenantName(), tenant.getTenantCode(), e.getMessage(), e);
            }
        }

        return processedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initializeTenantData(String tenantId, String adminUsername, String adminPassword, String adminEmail) {
        // 只有平台管理员可以初始化租户数据
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，无法初始化租户数据");
        }

        if (tenantId == null) {
            throw new BusinessException("租户ID不能为空");
        }

        try {
            // 1. 为新租户创建默认配置
            copyTenantConfigs(null, tenantId);

            log.info("租户 {} 数据初始化完成", tenantId);
            return true;
        } catch (Exception e) {
            log.error("初始化租户 {} 数据失败: {}", tenantId, e.getMessage(), e);
            throw new BusinessException("初始化租户数据失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyTenantConfigs(String sourceTenantId, String targetTenantId) {
        if (sourceTenantId == null || targetTenantId == null) {
            throw new BusinessException("源租户ID和目标租户ID不能为空");
        }

        try {
            return tenantConfigService.copyConfigsToTenant(sourceTenantId, targetTenantId);
        } catch (Exception e) {
            log.error("复制租户配置失败: {}", e.getMessage(), e);
            throw new BusinessException("复制租户配置失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateTenantStatus(String tenantId) {
        if (tenantId == null) {
            return false;
        }

        // 租户表不需要租户隔离，直接查询
        SysTenant tenant = getById(tenantId);

        if (tenant == null) {
            return false;
        }

        // 检查租户状态
        if (!CommonConstants.TENANT_STATUS_NORMAL.equals(tenant.getStatus())) {
            return false;
        }

        // 检查是否过期
        if (tenant.getExpireTime() != null && tenant.getExpireTime().isBefore(LocalDateTime.now())) {
            return false;
        }

        return true;
    }

    @Override
    public Integer getTenantUserCount(String tenantId) {
        if (tenantId == null) {
            return 0;
        }

        try {
            // 查询指定租户的用户数量，需要在该租户上下文中执行
            return TenantContextHolder.callWithTenant(tenantId, ()
                    -> tenantMapper.countUsersByTenantId(tenantId)
            );
        } catch (Exception e) {
            log.error("查询租户 {} 用户数量失败: {}", tenantId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public boolean checkUserLimit(String tenantId) {
        if (tenantId == null) {
            return false;
        }

        try {
            // 租户表不需要租户隔离，直接查询
            SysTenant tenant = getById(tenantId);

            if (tenant == null) {
                log.warn("租户 {} 不存在，无法检查用户限制", tenantId);
                return false;
            }

            if (tenant.getUserLimit() == null || tenant.getUserLimit() <= 0) {
                // 没有设置用户限制或限制为0，表示不限制
                return false;
            }

            Integer currentUserCount = getTenantUserCount(tenantId);
            boolean isExceeded = currentUserCount >= tenant.getUserLimit();

            if (isExceeded) {
                log.warn("租户 {} 用户数量 {} 已达到或超过限制 {}", tenantId, currentUserCount, tenant.getUserLimit());
            }

            return isExceeded;
        } catch (Exception e) {
            log.error("检查租户 {} 用户限制时发生异常: {}", tenantId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取租户剩余可用用户数量
     */
    public Integer getTenantRemainingUserCount(String tenantId) {
        if (tenantId == null) {
            return null;
        }

        try {
            SysTenant tenant = getById(tenantId);
            if (tenant == null || tenant.getUserLimit() == null || tenant.getUserLimit() <= 0) {
                return null; // 无限制
            }

            Integer currentUserCount = getTenantUserCount(tenantId);
            int remaining = tenant.getUserLimit() - currentUserCount;
            return Math.max(0, remaining);
        } catch (Exception e) {
            log.error("获取租户 {} 剩余用户数量时发生异常: {}", tenantId, e.getMessage(), e);
            return null;
        }
    }
}
