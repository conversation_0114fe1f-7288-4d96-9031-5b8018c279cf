<template>
  <div class="el-dept-tree-selector">
    <!-- 树形选择器模式 -->
    <el-tree-select
      v-model="selectedValue"
      :data="treeData"
      :props="treeProps"
      :multiple="config.multiple"
      :clearable="config.clearable"
      :filterable="config.filterable"
      :placeholder="config.placeholder"
      :size="config.size"
      :disabled="config.disabled"
      :loading="loading"
      :check-strictly="config.checkStrictly"
      :show-checkbox="config.multiple"
      :render-after-expand="false"
      :default-expand-all="config.defaultExpandAll"
      :expand-on-click-node="config.expandOnClickNode"
      :filter-node-method="filterNodeMethod"
      :teleported="config.teleported"
      :popper-class="config.popperClass"
      :fit-input-width="config.fitInputWidth"
      :suffix-icon="config.suffixIcon"
      :tag-type="config.tagType"
      :max-collapse-tags="config.maxCollapseTags"
      :collapse-tags-tooltip="config.collapseTagsTooltip"
      :effect="config.effect"
      node-key="value"
      @change="handleChange"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
      @clear="handleClear"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <template #default="{ node, data }">
        <div class="tree-node-content">
          <el-icon class="node-icon" :color="getNodeIconColor(data)">
            <component :is="getNodeIcon(data)" />
          </el-icon>
          <span class="node-label" :class="{ 'is-disabled': data.disabled }">
            {{ data.label }}
          </span>
          <span v-if="data.deptCode" class="dept-code">
            ({{ data.deptCode }})
          </span>
          <div v-if="showNodeExtra" class="node-extra">
            <span v-if="data.userCount !== undefined" class="user-count">
              {{ data.userCount }}人
            </span>
            <span v-if="data.leaderName" class="leader-name">
              负责人: {{ data.leaderName }}
            </span>
          </div>
        </div>
      </template>
      
      <template #empty>
        <div class="empty-content">
          <el-icon size="48" color="#c0c4cc">
            <FolderOpened />
          </el-icon>
          <p>{{ config.noDataText || '暂无部门数据' }}</p>
        </div>
      </template>
    </el-tree-select>

    <!-- 已选择标签显示（多选模式下） -->
    <div v-if="config.multiple && showSelectedTags && hasSelection" class="selected-tags">
      <div class="tags-header">已选择的部门:</div>
      <div class="tags-content">
        <el-tag
          v-for="option in selectedOptions"
          :key="option.value"
          :type="config.tagType"
          :closable="!config.disabled"
          :size="config.size"
          @close="handleTagClose(option.value)"
        >
          {{ option.label }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElTreeSelect, ElIcon, ElTag } from 'element-plus'
import { 
  Folder, 
  OfficeBuilding, 
  FolderOpened 
} from '@element-plus/icons-vue'
import { DeptAPI } from '@/api/system'
import type { 
  DeptSelectorOption, 
  SelectorConfig, 
  TreeSelectorConfig,
  SelectorEmits 
} from './types'
import type { SysDeptVO } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<TreeSelectorConfig>
  excludeDeptId?: string
  onlyEnabled?: boolean
  showNodeExtra?: boolean
  showSelectedTags?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  showNodeExtra: true,
  showSelectedTags: true
})

const emit = defineEmits<SelectorEmits>()

// 响应式数据
const loading = ref(false)
const treeData = ref<DeptSelectorOption[]>([])
const selectedValue = ref<string | string[]>()
const selectedOptions = ref<DeptSelectorOption[]>([])

// 树形选择器属性配置
const treeProps = {
  children: 'children',
  label: 'label',
  value: 'value',
  disabled: 'disabled',
  isLeaf: (data: DeptSelectorOption) => !data.children || data.children.length === 0
}

// 默认配置
const defaultConfig: TreeSelectorConfig = {
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择部门',
  size: 'default',
  disabled: false,
  checkStrictly: false,
  expandOnClickNode: false,
  defaultExpandAll: false,
  teleported: true,
  fitInputWidth: false,
  tagType: 'info',
  maxCollapseTags: 3,
  collapseTagsTooltip: true,
  effect: 'light',
  noDataText: '暂无部门数据',
  noMatchText: '无匹配数据',
  loadingText: '加载中...'
}

// 合并配置
const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 计算属性
const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedValue.value) && selectedValue.value.length > 0
  }
  return selectedValue.value !== undefined && selectedValue.value !== null && selectedValue.value !== ''
})

// 方法
const loadDeptTree = async () => {
  try {
    loading.value = true
    const response = await DeptAPI.getDeptTree({
      status: props.onlyEnabled ? '0' : undefined
    })
    const data = response.data || []
    treeData.value = transformDeptData(data)
  } catch (error) {
    console.error('加载部门树失败:', error)
    treeData.value = []
  } finally {
    loading.value = false
  }
}

const transformDeptData = (data: SysDeptVO[]): DeptSelectorOption[] => {
  const transform = (depts: SysDeptVO[]): DeptSelectorOption[] => {
    return depts
      .filter(dept => !props.excludeDeptId || dept.id !== props.excludeDeptId)
      .map(dept => ({
        value: dept.id,
        label: dept.deptName,
        disabled: dept.status !== '0',
        deptCode: dept.deptCode,
        parentId: dept.parentId,
        orderNum: dept.orderNum,
        leaderId: dept.leaderId,
        leaderName: dept.leaderName,
        status: dept.status,
        userCount: dept.userCount,
        level: dept.level,
        children: dept.children && dept.children.length > 0 ? transform(dept.children) : undefined
      }))
  }
  return transform(data)
}

const getNodeIcon = (data: DeptSelectorOption) => {
  return data.children && data.children.length > 0 ? Folder : OfficeBuilding
}

const getNodeIconColor = (data: DeptSelectorOption) => {
  if (data.disabled) return '#c0c4cc'
  return data.children && data.children.length > 0 ? '#f39c12' : '#409eff'
}

const filterNodeMethod = (value: string, data: DeptSelectorOption) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase()) ||
         (data.deptCode && data.deptCode.toLowerCase().includes(value.toLowerCase()))
}

const findOptionsByValues = (values: string | string[]): DeptSelectorOption[] => {
  const targetValues = Array.isArray(values) ? values : (values ? [values] : [])
  const result: DeptSelectorOption[] = []

  const findInTree = (options: DeptSelectorOption[]) => {
    for (const option of options) {
      if (targetValues.includes(option.value)) {
        result.push(option)
      }
      if (option.children) {
        findInTree(option.children)
      }
    }
  }

  findInTree(treeData.value)
  console.log('findOptionsByValues result:', result, 'for values:', targetValues, 'treeData:', treeData.value)
  return result
}

// 事件处理
const handleChange = (value: string | string[]) => {
  console.log('ElDeptTreeSelector handleChange:', value)
  selectedValue.value = value
  selectedOptions.value = findOptionsByValues(value)

  emit('update:modelValue', value)
  emit('change', value, selectedOptions.value.length === 1 ? selectedOptions.value[0] : selectedOptions.value)

  if (!config.value.multiple && selectedOptions.value.length === 1) {
    emit('select', value as string, selectedOptions.value[0])
  }
}

const handleVisibleChange = (visible: boolean) => {
  emit('visible-change', visible)
}

const handleRemoveTag = (value: string) => {
  emit('remove-tag', value)
  emit('remove', value)
}

const handleTagClose = (value: string) => {
  if (config.value.multiple && Array.isArray(selectedValue.value)) {
    const newValue = selectedValue.value.filter(v => v !== value)
    handleChange(newValue)
  }
}

const handleClear = () => {
  const clearValue = config.value.multiple ? [] : ''
  selectedValue.value = clearValue
  selectedOptions.value = []
  emit('update:modelValue', clearValue)
  emit('change', clearValue, null)
  emit('clear')
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue || (config.value.multiple ? [] : '')
  selectedOptions.value = findOptionsByValues(selectedValue.value)
}, { immediate: true })

// 组件挂载
onMounted(() => {
  loadDeptTree()
})

// 暴露方法
defineExpose({
  refresh: loadDeptTree,
  clearSelection: handleClear
})
</script>

<style scoped>
.el-dept-tree-selector {
  width: 100%;
}

.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.node-icon {
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  word-break: break-all;
}

.node-label.is-disabled {
  color: var(--el-text-color-disabled);
}

.dept-code {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  flex-shrink: 0;
}

.node-extra {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  flex-shrink: 0;
}

.selected-tags {
  margin-top: 8px;
}

.tags-header {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
}

.empty-content p {
  margin: 8px 0 0 0;
  font-size: 14px;
}
</style>
