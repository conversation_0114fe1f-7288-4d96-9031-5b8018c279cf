module.exports = {
  presets: [
    [
      '@babel/preset-env',
      {
        // 根据browserslist配置自动确定需要的polyfill
        useBuiltIns: 'usage',
        corejs: 3,
        // 保持模块化，让bundler处理
        modules: false,
        // 调试模式，显示使用的polyfill
        debug: process.env.NODE_ENV === 'development'
      }
    ]
  ],
  plugins: [
    // 支持可选链操作符
    '@babel/plugin-proposal-optional-chaining',
    // 支持空值合并操作符
    '@babel/plugin-proposal-nullish-coalescing-operator',
    // 支持类属性
    '@babel/plugin-proposal-class-properties',
    // 支持私有方法
    '@babel/plugin-proposal-private-methods'
  ],
  env: {
    // 开发环境配置
    development: {
      plugins: [
        // 开发时保持模块热更新
        'react-refresh/babel'
      ]
    },
    // 生产环境配置
    production: {
      plugins: [
        // 移除console.log
        ['transform-remove-console', { exclude: ['error', 'warn'] }]
      ]
    },
    // 测试环境配置
    test: {
      presets: [
        [
          '@babel/preset-env',
          {
            targets: { node: 'current' }
          }
        ]
      ]
    }
  }
}
