package com.xhcai.modules.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * AI聊天请求（Dify标准格式）
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Schema(description = "AI聊天请求（Dify标准格式）")
public class AiChatRequest {

    /**
     * 输入参数
     */
    @Schema(description = "输入参数", example = "{}")
    private Map<String, Object> inputs;

    /**
     * 查询内容
     */
    @NotBlank(message = "查询内容不能为空")
    @Schema(description = "查询内容", example = "What are the specs of the iPhone 13 Pro Max?", required = true)
    private String query;

    /**
     * 响应模式
     */
    @JsonProperty("response_mode")
    @Schema(description = "响应模式", example = "streaming", allowableValues = {"blocking", "streaming"})
    private String responseMode = "streaming";

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "")
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 用户标识
     */
    @Schema(description = "用户标识", example = "abc-123")
    private String user;

    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    private List<FileInfo> files;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID", example = "12345678-1234-1234-1234-123456789abc")
    private String appId;

    /**
     * 父消息ID
     */
    @Schema(description = "父消息ID")
    @JsonProperty("parent_message_id")
    private String parentMessageId;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent-123")
    private String agentId;

    /**
     * 文件信息
     */
    @Schema(description = "文件信息")
    public static class FileInfo {
        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "image", allowableValues = {"image", "document", "audio", "video"})
        private String type;

        /**
         * 传输方式
         */
        @JsonProperty("transfer_method")
        @Schema(description = "传输方式", example = "local_file", allowableValues = {"remote_url", "local_file"})
        private String transferMethod;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://cloud.dify.ai/logo/logo-site.png")
        private String url;

        /**
         * 上传文件ID（本地文件时使用）
         */
        @JsonProperty("upload_file_id")
        @Schema(description = "上传文件ID", example = "93243d0f-928a-445e-bf15-1f8db7578d93")
        private String uploadFileId;

        /**
         * 文件名
         */
        @Schema(description = "文件名")
        private String fileName;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小")
        private Long fileSize;

        // Getters and Setters
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getTransferMethod() {
            return transferMethod;
        }

        public void setTransferMethod(String transferMethod) {
            this.transferMethod = transferMethod;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getUploadFileId() {
            return uploadFileId;
        }

        public void setUploadFileId(String uploadFileId) {
            this.uploadFileId = uploadFileId;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        @Override
        public String toString() {
            return "FileInfo{" +
                    "type='" + type + '\'' +
                    ", transferMethod='" + transferMethod + '\'' +
                    ", url='" + url + '\'' +
                    ", uploadFileId='" + uploadFileId + '\'' +
                    ", fileName='" + fileName + '\'' +
                    ", fileSize=" + fileSize +
                    '}';
        }
    }

    // ==================== Getters and Setters ====================

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getResponseMode() {
        return responseMode;
    }

    public void setResponseMode(String responseMode) {
        this.responseMode = responseMode;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public List<FileInfo> getFiles() {
        return files;
    }

    public void setFiles(List<FileInfo> files) {
        this.files = files;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getParentMessageId() {
        return parentMessageId;
    }

    public void setParentMessageId(String parentMessageId) {
        this.parentMessageId = parentMessageId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    // ==================== 辅助方法 ====================

    /**
     * 转换为 Dify Console API 请求格式的 Map
     */
    public Map<String, Object> toDifyConsoleRequestMap() {
        Map<String, Object> requestMap = new java.util.HashMap<>();

        requestMap.put("response_mode", responseMode != null ? responseMode : "streaming");
        requestMap.put("conversation_id", conversationId != null ? conversationId : "");
        requestMap.put("files", files != null ? files : new java.util.ArrayList<>());
        requestMap.put("query", query);
        requestMap.put("inputs", inputs != null ? inputs : new java.util.HashMap<>());
        requestMap.put("parent_message_id", parentMessageId);

        return requestMap;
    }

    /**
     * 转换为 Dify 请求格式的 Map（保持向后兼容）
     */
    public Map<String, Object> toDifyRequestMap() {
        Map<String, Object> requestMap = new java.util.HashMap<>();

        requestMap.put("inputs", inputs != null ? inputs : new java.util.HashMap<>());
        requestMap.put("query", query);
        requestMap.put("response_mode", responseMode != null ? responseMode : "streaming");
        requestMap.put("conversation_id", conversationId != null ? conversationId : "");
        requestMap.put("user", user != null ? user : "default-user");
        requestMap.put("files", files != null ? files : new java.util.ArrayList<>());

        return requestMap;
    }

    /**
     * 确定消息类型
     */
    public String getMessageType() {
        // 如果有文件，根据文件类型确定
        if (files != null && !files.isEmpty()) {
            // 检查是否有图片文件
            boolean hasImage = files.stream()
                    .anyMatch(file -> "image".equals(file.getType()));
            if (hasImage) {
                return "image";
            }

            // 检查是否有其他类型的文件
            boolean hasFile = files.stream()
                    .anyMatch(file -> file.getType() != null && !file.getType().equals("image"));
            if (hasFile) {
                return "file";
            }
        }

        // 默认为文本
        return "text";
    }

    @Override
    public String toString() {
        return "AiChatRequest{" +
                "inputs=" + inputs +
                ", query='" + query + '\'' +
                ", responseMode='" + responseMode + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", user='" + user + '\'' +
                ", files=" + files +
                ", appId='" + appId + '\'' +
                ", parentMessageId='" + parentMessageId + '\'' +
                ", agentId='" + agentId + '\'' +
                '}';
    }
}
