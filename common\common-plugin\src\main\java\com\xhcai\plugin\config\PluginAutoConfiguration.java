package com.xhcai.plugin.config;

import com.xhcai.plugin.core.PluginContextManager;
import com.xhcai.plugin.loader.HotSwapPluginManager;
import com.xhcai.plugin.service.PluginServiceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 插件自动配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(PluginProperties.class)
public class PluginAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public PluginContextManager pluginContextManager() {
        log.info("Creating PluginContextManager bean");
        return new PluginContextManager();
    }
    
    @Bean
    @ConditionalOnMissingBean
    public HotSwapPluginManager hotSwapPluginManager() {
        log.info("Creating HotSwapPluginManager bean");
        return new HotSwapPluginManager();
    }
    
    @Bean
    @ConditionalOnMissingBean
    public PluginServiceManager pluginServiceManager() {
        log.info("Creating PluginServiceManager bean");
        return new PluginServiceManager();
    }
}
