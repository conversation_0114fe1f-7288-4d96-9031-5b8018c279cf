<template>
  <div class="platform-init-container">
    <div class="init-card">
      <div class="init-header">
        <div class="logo">
          <h1>🚀 XHC智能平台</h1>
        </div>
        <h2>平台初始化</h2>
        <p class="subtitle">欢迎使用XHC智能平台，请先创建平台管理员账号</p>

        <!-- 显示连接状态 -->
        <div v-if="connectionError" class="connection-status error">
          <el-alert
            title="后端服务连接失败"
            type="error"
            :description="connectionError"
            show-icon
            :closable="false"
          />
          <p class="retry-hint">请确保后端服务已启动，然后点击下方按钮重试</p>
          <el-button @click="checkInitStatus" :loading="checkingConnection" type="primary" size="small">
            重新检测连接
          </el-button>
        </div>

        <div v-else-if="initStatus" class="connection-status success">
          <el-alert
            title="后端服务连接正常"
            type="success"
            :description="`${initStatus.message}`"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <div class="init-form">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          size="large"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入管理员用户名"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入管理员密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="form.email"
              placeholder="请输入管理员邮箱"
              :prefix-icon="Message"
              clearable
            />
          </el-form-item>

          <el-form-item label="真实姓名" prop="realName">
            <el-input
              v-model="form.realName"
              placeholder="请输入管理员真实姓名（可选）"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="handleSubmit"
              class="submit-btn"
            >
              {{ loading ? '创建中...' : '创建管理员账号' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Message } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { AuthAPI } from '@/api/auth'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)
const checkingConnection = ref(false)
const connectionError = ref('')
const initStatus = ref<any>(null)

// 表单数据
const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  realName: ''
})

// 表单验证规则
const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度必须在3-50个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 100, message: '密码长度必须在6-100个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== form.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 检查平台初始化状态
const checkInitStatus = async () => {
  checkingConnection.value = true
  connectionError.value = ''
  initStatus.value = null

  try {
    const result = await AuthAPI.checkPlatformInitialization()
    initStatus.value = result.data

    if (result.data.initialized) {
      // 如果已经初始化，跳转到登录页面
      ElMessage.info('平台已初始化，正在跳转到登录页面...')
      router.push('/login')
    }
  } catch (error: any) {
    console.error('检查初始化状态失败:', error)
    connectionError.value = error.message || '无法连接到后端服务，请检查服务是否正常启动'
  } finally {
    checkingConnection.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    const result = await AuthAPI.createPlatformAdmin({
      username: form.username,
      password: form.password,
      email: form.email,
      realName: form.realName || undefined
    })

    if (result.data.success) {
      await ElMessageBox.alert(
        '平台管理员账号创建成功！系统已完成初始化，包括平台租户创建、平台管理员角色分配、默认部门、角色、权限等数据。请使用新创建的账号登录系统。',
        '平台初始化完成',
        {
          confirmButtonText: '前往登录',
          type: 'success'
        }
      )

      // 跳转到登录页面
      router.push('/login')
    } else {
      ElMessage.error(result.data.message || '创建失败')
    }
  } catch (error: any) {
    console.error('创建平台管理员失败:', error)
    ElMessage.error(error.message || '创建失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 页面加载时不需要再次检查初始化状态，因为路由守卫已经检查过了
// 只有在用户手动点击重新检测时才调用 checkInitStatus()
onMounted(() => {

  checkInitStatus()

})
</script>

<style scoped>
.platform-init-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.init-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
}

.init-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo h1 {
  color: #667eea;
  margin: 0 0 20px 0;
  font-size: 2.5rem;
  font-weight: bold;
}

.init-header h2 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.subtitle {
  color: #666;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.init-form {
  margin-top: 20px;
}

.submit-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.3);
}

.connection-status {
  margin: 20px 0;
}

.retry-hint {
  margin: 10px 0;
  color: #666;
  font-size: 14px;
}
</style>
