<template>
  <div class="dept-tree-selector" :class="{ 'is-dropdown': isDropdownMode }">
    <!-- 下拉模式 -->
    <DropdownSelector
      v-if="isDropdownMode"
      v-model="selectedValues"
      :placeholder="config.placeholder"
      :disabled="config.disabled" 
      :clearable="config.clearable"
      :size="config.size"
      :show-actions="config.multiple"
      :dropdown-class="'dept-dropdown-panel'"
      :align="align"
      @change="handleDropdownChange"
      @clear="clearSelection"
    >
      <template #display>
        <span v-if="!hasSelection" class="placeholder-text">{{ mergedConfig.placeholder }}</span>
        <div v-else class="selected-display">
          <span v-if="!mergedConfig.multiple" class="single-selected">
            {{ displayText }}
          </span>
          <div v-else class="multiple-selected">
            <span class="selected-count">{{ displayText }}</span>
          </div>
        </div>
      </template>

      <!-- 下拉内容 -->
      <div class="dropdown-dept-selector">
        <DeptTreeSelectorContent
          ref="contentRef"
          v-model="selectedValues"
          :config="mergedConfig"
          :exclude-dept-id="excludeDeptId"
          :only-enabled="onlyEnabled"
          @change="handleContentChange"
          @select="handleSelect"
          @remove="handleRemove"
        />
      </div>
    </DropdownSelector>

    <!-- 嵌入模式 -->
    <div v-else class="embedded-mode">
      <DeptTreeSelectorContent
        v-model="selectedValues"
        :config="mergedConfig"
        :exclude-dept-id="excludeDeptId"
        :only-enabled="onlyEnabled"
        :show-header="true"
        @change="handleContentChange"
        @select="handleSelect"
        @remove="handleRemove"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import DropdownSelector from './DropdownSelector.vue'
import DeptTreeSelectorContent from './DeptTreeSelectorContent.vue'
import type { DeptSelectorOption, SelectorConfig } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  excludeDeptId?: string
  onlyEnabled?: boolean
  mode?: 'dropdown' | 'embedded'
  align?: 'left' | 'right' | 'center'
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], options: DeptSelectorOption[]): void
  (e: 'select', value: string, option: DeptSelectorOption): void
  (e: 'remove', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  mode: 'embedded',
  align: 'left'
})

const emit = defineEmits<Emits>()

// 响应式数据 - 确保每个组件实例都有独立的数据
const selectedValues = ref<string | string[]>(
  Array.isArray(props.modelValue) ? [...props.modelValue] : props.modelValue || (props.config.multiple ? [] : '')
)
const selectedOptions = ref<DeptSelectorOption[]>([]) // 存储选中的选项详细信息
const contentRef = ref<InstanceType<typeof DeptTreeSelectorContent>>()

// 计算属性
const isDropdownMode = computed(() => props.mode === 'dropdown')

const mergedConfig = computed(() => ({
  multiple: false,
  checkStrictly: false,
  showCheckbox: true,
  expandOnClickNode: false,
  defaultExpandAll: false,
  placeholder: '请选择部门',
  ...props.config
}))

const hasSelection = computed(() => {
  return selectedValues.value !== null &&
         selectedValues.value !== undefined &&
         selectedValues.value !== '' &&
         (Array.isArray(selectedValues.value) ? selectedValues.value.length > 0 : true)
})

const selectedCount = computed(() => {
  if (mergedConfig.value.multiple && Array.isArray(selectedValues.value)) {
    return selectedValues.value.length
  }
  return hasSelection.value ? 1 : 0
})

// 计算显示文本
const displayText = computed(() => {
  if (!hasSelection.value) return ''

  if (mergedConfig.value.multiple && Array.isArray(selectedValues.value)) {
    return `已选择 ${selectedValues.value.length} 个部门`
  } else {
    // 单选模式，显示选中部门的名称
    if (selectedOptions.value.length > 0) {
      const option = selectedOptions.value[0]
      return option.label || option.deptName || option.name || String(option.value)
    }
    return String(selectedValues.value)
  }
})

// 方法
const getSelectedLabels = () => {
  if (!hasSelection.value) return []

  // 如果有选中的选项详细信息，使用它们
  if (selectedOptions.value.length > 0) {
    return selectedOptions.value.map(option => option.label || option.deptName || option.name || String(option.value))
  }

  // 否则返回选中的值（作为后备）
  if (mergedConfig.value.multiple && Array.isArray(selectedValues.value)) {
    return selectedValues.value.map(value => String(value))
  } else if (selectedValues.value) {
    return [String(selectedValues.value)]
  }

  return []
}

const clearSelection = () => {
  const clearValue = mergedConfig.value.multiple ? [] : ''
  selectedValues.value = clearValue
  emit('update:modelValue', clearValue)
  emit('change', clearValue, [])
}

// 事件处理
const handleDropdownChange = (value: string | string[]) => {
  selectedValues.value = value
  emit('update:modelValue', value)
}

const handleContentChange = (value: string | string[], options: DeptSelectorOption[]) => {
  selectedValues.value = Array.isArray(value) ? [...value] : value
  selectedOptions.value = options ? [...options] : [] // 存储选中的选项详细信息
  emit('update:modelValue', selectedValues.value)
  emit('change', selectedValues.value, options)
}

const handleSelect = (value: string, option: DeptSelectorOption) => {
  emit('select', value, option)
}

const handleRemove = (value: string) => {
  emit('remove', value)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValues.value = Array.isArray(newValue) ? [...newValue] : newValue || (mergedConfig.value.multiple ? [] : '')
  // 清空选项缓存，让子组件重新获取
  if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
    selectedOptions.value = []
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  clearSelection
})
</script>

<style scoped>
.dept-tree-selector {
  width: 100%;
}

.dept-tree-selector.is-dropdown {
  display: inline-block;
}

.placeholder-text {
  color: #c0c4cc;
}

.selected-display {
  color: #606266;
}

.single-selected {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multiple-selected {
  display: flex;
  align-items: center;
}

.selected-count {
  font-size: 14px;
  color: #606266;
}

.dropdown-dept-selector {
  min-width: 300px;
}

.embedded-mode {
  width: 100%;
}

/* 下拉面板样式 */
:deep(.dept-dropdown-panel) {
  min-width: 320px;
}

:deep(.dept-dropdown-panel .dropdown-content) {
  padding: 0;
}

:deep(.dept-dropdown-panel .dept-tree-selector-content) {
  border: none;
  box-shadow: none;
}

:deep(.dept-dropdown-panel .tree-container) {
  border: none;
  border-radius: 0;
  min-height: 200px;
  max-height: 300px;
}
</style>
