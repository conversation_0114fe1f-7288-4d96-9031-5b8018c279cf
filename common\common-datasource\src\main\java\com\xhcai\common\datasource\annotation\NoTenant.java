package com.xhcai.common.datasource.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标识实体类不需要租户字段的注解
 * 
 * <p>使用此注解的实体类将：</p>
 * <ul>
 *   <li>不会自动创建tenant_id字段（即使继承了BaseEntity）</li>
 *   <li>不会被多租户插件自动添加租户过滤条件</li>
 *   <li>不会在插入时自动填充tenant_id值</li>
 * </ul>
 * 
 * <p>适用场景：</p>
 * <ul>
 *   <li>系统级别的配置表（如字典表、系统参数表）</li>
 *   <li>租户管理相关的表（如租户信息表本身）</li>
 *   <li>全局共享的数据表（如地区代码表、行业分类表）</li>
 *   <li>日志表（需要记录跨租户的操作日志）</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>
 * &#64;Entity
 * &#64;Table(name = "sys_dict")
 * &#64;NoTenant
 * public class SysDict extends BaseEntity {
 *     // 此实体不会包含tenant_id字段
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface NoTenant {
    
    /**
     * 说明不需要租户字段的原因
     * 
     * @return 原因说明
     */
    String reason() default "";
    
    /**
     * 是否完全忽略多租户处理
     * 如果为true，则完全跳过多租户相关的所有处理
     * 如果为false，则只是不创建tenant_id字段，但仍可能需要其他多租户相关处理
     * 
     * @return 是否完全忽略
     */
    boolean ignoreCompletely() default true;
}
