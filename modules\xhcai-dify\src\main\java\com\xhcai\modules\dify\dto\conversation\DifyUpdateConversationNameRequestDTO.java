package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Dify 修改会话名称请求 DTO
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Dify 修改会话名称请求")
public class DifyUpdateConversationNameRequestDTO {

    /**
     * 会话名称
     */
    @JsonProperty("name")
    @Schema(description = "会话名称", example = "询问身份 😊", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "会话名称不能为空")
    @Size(max = 200, message = "会话名称长度不能超过200个字符")
    private String name;
}
