package com.xhcai.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 导入工作流配置请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "导入工作流配置请求")
public class ImportRequestDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001", required = true)
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 配置JSON
     */
    @Schema(description = "配置JSON", required = true)
    @NotBlank(message = "配置JSON不能为空")
    private String configJson;

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }
}
