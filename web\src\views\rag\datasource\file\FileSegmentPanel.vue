<template>
  <div class="segment-panel" :class="{ visible }" @click.self="close">
    <div class="panel-content" :class="{ visible }">
      <!-- 头部 -->
      <div class="panel-header">
        <div class="header-left">
          <div class="file-info">
            <div class="file-icon" :class="getFileTypeClass(file?.type)">
              {{ getFileIcon(file?.type) }}
            </div>
            <div class="file-details">
              <h3 class="file-name">{{ file?.name }}</h3>
              <div class="file-meta">
                <span>{{ formatFileSize(file?.size) }}</span>
                <span>•</span>
                <span>{{ file?.segmentCount || 0 }} 个分段</span>
                <span>•</span>
                <span>{{ formatNumber(file?.charCount || 0) }} 字符</span>
              </div>
            </div>
          </div>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="close" title="关闭">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- 主要内容区域 - 左右分栏 -->
      <div class="main-content">
        <!-- 左侧：文件内容查看器 -->
        <div class="left-panel">
          <div class="file-viewer-container">
            <FileViewer
              :file="file"
              :segments="segments"
              :selectedSegmentId="selectedSegmentId"
              @download="handleDownload"
              @segmentHover="handleSegmentHover"
              @segmentLeave="handleSegmentLeave"
            />
          </div>
        </div>

        <!-- 右侧：分段列表 -->
        <div class="right-panel">
          <div class="panel-title">
            <div>
              <h4>分段列表</h4>
              <span class="segment-count">共 {{ segments.length }} 个分段</span>
            </div>
            <div class="toolbar-right">
              <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input
                  type="text"
                  placeholder="搜索分段内容..."
                  v-model="searchQuery"
                  @input="filterSegments"
                  class="search-input"
                >
              </div>

              <!-- Excel特有的控制 -->
              <div v-if="isExcelFile" class="excel-controls">
                <label class="text-xs text-gray-600">合并行数:</label>
                <select v-model="excelRowsPerSegment" @change="regenerateExcelSegments" class="text-xs px-2 py-1 border rounded">
                  <option value="1">1行/段</option>
                  <option value="2">2行/段</option>
                  <option value="3">3行/段</option>
                  <option value="5">5行/段</option>
                </select>
              </div>

              <button class="btn btn-outline" @click="addSegment">
                <i class="fas fa-plus"></i>
                添加分段
              </button>
              <button class="action-btn" @click="refreshSegments" title="刷新">
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>
          </div>

          <div class="segments-container">
            <div v-if="loading" class="loading-state">
              <div class="loading-spinner"></div>
              <p>正在加载分段...</p>
            </div>

            <div v-else-if="filteredSegments.length === 0" class="empty-state">
              <div class="empty-icon">📄</div>
              <p>暂无分段数据</p>
              <p class="empty-hint">文档还未进行分段处理</p>
            </div>

            <div v-else class="segments-list">
              <div
                v-for="(segment, index) in filteredSegments"
                :key="segment.id"
                class="segment-item"
                :class="{ selected: selectedSegmentId === segment.id }"
                @click="selectSegment(segment)"
              >
                <div class="segment-header">
                  <div class="segment-index"># {{ index + 1 }}</div>
                  <div class="segment-meta">
                    <span class="char-count">{{ segment.content.length }} 字符</span>
                    <span class="segment-score" v-if="segment.score">
                      相似度: {{ (segment.score * 100).toFixed(1) }}%
                    </span>
                  </div>
                  <div class="segment-actions">
                    <button
                      class="action-btn"
                      @click.stop="editSegment(segment)"
                      title="编辑"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button
                      class="action-btn danger"
                      @click.stop="deleteSegment(segment)"
                      title="删除"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
                <div class="segment-content">
                  <p class="content-text">{{ segment.content }}</p>
                  <div class="segment-metadata" v-if="segment.metadata">
                    <div class="metadata-item" v-for="(value, key) in segment.metadata" :key="key">
                      <span class="metadata-key">{{ key }}:</span>
                      <span class="metadata-value">{{ value }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分段详情/编辑区域 -->
          <div v-if="selectedSegment" class="segment-detail" :class="{ collapsed: isDetailCollapsed }">
            <div class="detail-header">
              <div class="header-left">
                <h4>分段详情</h4>
                <button
                  class="collapse-btn"
                  @click="toggleDetailCollapse"
                  :title="isDetailCollapsed ? '展开详情' : '收起详情'"
                >
                  <i :class="isDetailCollapsed ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
                </button>
              </div>
              <div class="header-right">
                <button class="btn btn-primary" @click="saveSegment" v-if="isEditing">
                  保存修改
                </button>
              </div>
            </div>
            <div class="detail-content">
              <div class="form-group">
                <label>分段内容</label>
                <textarea
                  v-model="editingContent"
                  :readonly="!isEditing"
                  class="content-textarea"
                  rows="6"
                ></textarea>
              </div>
              <div class="form-group" v-if="selectedSegment.metadata">
                <label>元数据</label>
                <div class="metadata-list">
                  <div
                    v-for="(value, key) in selectedSegment.metadata"
                    :key="key"
                    class="metadata-row"
                  >
                    <span class="key">{{ key }}</span>
                    <span class="value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import FileViewer from '@/views/rag/viewers/FileViewer.vue'
import { getFileIcon, getFileTypeClass, formatFileSize, formatNumber } from '@/utils/fileUtils'

// Props
interface Props {
  file: any
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const segments = ref<any[]>([])
const loading = ref(false)
const searchQuery = ref('')
const selectedSegmentId = ref<string | null>(null)
const selectedSegment = ref<any>(null)
const isEditing = ref(false)
const editingContent = ref('')
const isDetailCollapsed = ref(false) // 分段详情区域折叠状态
const excelRowsPerSegment = ref(1) // Excel每个分段包含的行数

// 计算属性
const filteredSegments = computed(() => {
  if (!searchQuery.value.trim()) {
    return segments.value
  }

  const query = searchQuery.value.toLowerCase()
  return segments.value.filter(segment =>
    segment.content.toLowerCase().includes(query)
  )
})

const isExcelFile = computed(() => {
  return props.file?.type === 'xlsx' || props.file?.type === 'xls'
})

// 方法
const close = () => {
  emit('close')
}

const loadSegments = async () => {
  if (!props.file) return

  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 根据文件类型生成不同的分段数据
    if (props.file.type === 'xlsx' || props.file.type === 'xls') {
      segments.value = generateExcelSegments()
    } else {
      // 其他文件类型的分段数据
      const segmentCount = props.file.segmentCount || 8
      segments.value = Array.from({ length: segmentCount }, (_, index) => {
        const pageNum = Math.floor(index / 4) + 1 // 每页4个分段
        const positionInPage = index % 4

        return {
          id: `segment_${index + 1}`,
          content: `这是第 ${index + 1} 个分段的内容。${generateMockContent(index)}`,
          metadata: {
            page: pageNum,
            position: positionInPage,
            startRow: index * 2 + 1,
            endRow: index * 2 + 2,
            source: props.file.name,
            timestamp: new Date().toISOString()
          },
          score: Math.random() * 0.3 + 0.7 // 0.7-1.0 的相似度分数
        }
      })
    }
  } catch (error) {
    console.error('加载分段失败:', error)
  } finally {
    loading.value = false
  }
}

const generateMockContent = (index: number) => {
  const contents = [
    '本文档详细描述了产品的核心功能和技术实现方案，包括系统架构设计、数据流程、接口规范等重要内容。',
    '在技术选型方面，我们采用了现代化的微服务架构，确保系统的可扩展性和维护性。',
    '用户界面设计遵循了最新的设计规范，提供了直观、易用的操作体验。',
    '安全性是我们重点考虑的因素，实现了多层次的安全防护机制。',
    '性能优化方面，通过缓存策略和数据库优化，确保系统的高效运行。'
  ]
  return contents[index % contents.length]
}

// 生成Excel文件的分段数据
const generateExcelSegments = () => {
  const excelSegments = []

  // 模拟Excel数据
  const sampleData = {
    headers: ['产品名称', '销售额', '数量', '单价', '日期'],
    rows: [
      ['iPhone 15', '¥50,000', '50', '¥1,000', '2024-01-15'],
      ['MacBook Pro', '¥120,000', '10', '¥12,000', '2024-01-16'],
      ['iPad Air', '¥30,000', '60', '¥500', '2024-01-17'],
      ['Apple Watch', '¥25,000', '100', '¥250', '2024-01-18'],
      ['AirPods Pro', '¥15,000', '75', '¥200', '2024-01-19'],
      ['iPhone 14', '¥40,000', '40', '¥1,000', '2024-01-20'],
      ['MacBook Air', '¥80,000', '8', '¥10,000', '2024-01-21']
    ]
  }

  // 表头作为第一个分段
  const headerContent = sampleData.headers.join(' | ')
  excelSegments.push({
    id: 'excel_header',
    content: `表头: ${headerContent}`,
    metadata: {
      type: 'header',
      startRow: 1,
      endRow: 1,
      source: props.file?.name,
      timestamp: new Date().toISOString()
    },
    score: 1.0
  })

  // 每行数据作为一个分段
  sampleData.rows.forEach((row, index) => {
    const rowContent = sampleData.headers.map((header, colIndex) =>
      `${header}: ${row[colIndex]}`
    ).join(' | ')

    excelSegments.push({
      id: `excel_row_${index + 2}`,
      content: rowContent,
      metadata: {
        type: 'data_row',
        startRow: index + 2,
        endRow: index + 2,
        source: props.file?.name,
        timestamp: new Date().toISOString()
      },
      score: Math.random() * 0.3 + 0.7
    })
  })

  return excelSegments
}

// 重新生成Excel分段（根据行数设置）
const regenerateExcelSegments = () => {
  if (!isExcelFile.value) return

  const excelSegments = []
  const rowsPerSegment = parseInt(excelRowsPerSegment.value.toString())

  // 模拟Excel数据
  const sampleData = {
    headers: ['产品名称', '销售额', '数量', '单价', '日期'],
    rows: [
      ['iPhone 15', '¥50,000', '50', '¥1,000', '2024-01-15'],
      ['MacBook Pro', '¥120,000', '10', '¥12,000', '2024-01-16'],
      ['iPad Air', '¥30,000', '60', '¥500', '2024-01-17'],
      ['Apple Watch', '¥25,000', '100', '¥250', '2024-01-18'],
      ['AirPods Pro', '¥15,000', '75', '¥200', '2024-01-19'],
      ['iPhone 14', '¥40,000', '40', '¥1,000', '2024-01-20'],
      ['MacBook Air', '¥80,000', '8', '¥10,000', '2024-01-21']
    ]
  }

  // 表头作为第一个分段
  const headerContent = sampleData.headers.join(' | ')
  excelSegments.push({
    id: 'excel_header',
    content: `表头: ${headerContent}`,
    metadata: {
      type: 'header',
      startRow: 1,
      endRow: 1,
      source: props.file?.name,
      timestamp: new Date().toISOString()
    },
    score: 1.0
  })

  // 根据设置的行数合并数据行
  for (let i = 0; i < sampleData.rows.length; i += rowsPerSegment) {
    const endIndex = Math.min(i + rowsPerSegment, sampleData.rows.length)
    const segmentRows = sampleData.rows.slice(i, endIndex)

    const segmentContent = segmentRows.map(row =>
      sampleData.headers.map((header, colIndex) =>
        `${header}: ${row[colIndex]}`
      ).join(' | ')
    ).join('\n')

    excelSegments.push({
      id: `excel_rows_${i + 2}_${endIndex + 1}`,
      content: segmentContent,
      metadata: {
        type: 'data_rows',
        startRow: i + 2,
        endRow: endIndex + 1,
        source: props.file?.name,
        timestamp: new Date().toISOString()
      },
      score: Math.random() * 0.3 + 0.7
    })
  }

  segments.value = excelSegments
}

const refreshSegments = () => {
  loadSegments()
}

const filterSegments = () => {
  // 搜索过滤已通过计算属性实现
}

const selectSegment = (segment: any) => {
  selectedSegmentId.value = segment.id
  selectedSegment.value = segment
  editingContent.value = segment.content
  isEditing.value = false

  // 触发FileViewer中的分段定位
  // 通过更新selectedSegmentId来通知FileViewer组件
  console.log('Selected segment:', segment)

  // 如果分段有页面信息，可以滚动到对应页面
  if (segment.metadata && segment.metadata.page) {
    // 这里可以添加滚动到指定页面的逻辑
    console.log('Navigate to page:', segment.metadata.page)
  }
}

const editSegment = (segment: any) => {
  selectSegment(segment)
  isEditing.value = true
}

const saveSegment = () => {
  if (selectedSegment.value) {
    selectedSegment.value.content = editingContent.value
    // 这里应该调用API保存
    isEditing.value = false
    alert('分段内容已保存')
  }
}

const deleteSegment = (segment: any) => {
  if (confirm(`确定要删除这个分段吗？`)) {
    segments.value = segments.value.filter(s => s.id !== segment.id)
    if (selectedSegmentId.value === segment.id) {
      selectedSegmentId.value = null
      selectedSegment.value = null
    }
  }
}

const addSegment = () => {
  const newSegment = {
    id: `segment_${Date.now()}`,
    content: '新增分段内容...',
    metadata: {
      page: 1,
      position: segments.value.length + 1,
      source: props.file?.name,
      timestamp: new Date().toISOString()
    }
  }
  segments.value.push(newSegment)
  selectSegment(newSegment)
  isEditing.value = true
}

// 其他方法
const toggleDetailCollapse = () => {
  isDetailCollapsed.value = !isDetailCollapsed.value
}

const handleDownload = (file: any) => {
  // 处理文件下载
  alert(`下载文件: ${file?.name}`)
}

// 处理分段悬停事件
const handleSegmentHover = (segmentInfo: any) => {
  // 可以在这里显示悬停提示信息
  console.log('Segment hover:', segmentInfo)
}

// 处理分段离开事件
const handleSegmentLeave = () => {
  // 隐藏悬停提示信息
  console.log('Segment leave')
}

// 监听文件变化
watch(() => props.file, (newFile) => {
  if (newFile) {
    loadSegments()
  }
}, { immediate: true })

// 监听面板可见性
watch(() => props.visible, (visible) => {
  if (!visible) {
    selectedSegmentId.value = null
    selectedSegment.value = null
    isEditing.value = false
  }
})
</script>

<style scoped>
.segment-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.segment-panel.visible {
  opacity: 1;
  visibility: visible;
}

.panel-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  transform: scale(0.95);
  opacity: 0;
  transition: all 0.3s ease;
}

.panel-content.visible {
  transform: scale(1);
  opacity: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.file-type-pdf { background: #fef3c7; }
.file-type-doc { background: #dbeafe; }
.file-type-txt { background: #f3f4f6; }
.file-type-md { background: #ecfdf5; }
.file-type-xlsx { background: #fef2f2; }

.file-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.file-meta {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
}

.file-meta span {
  margin: 0 4px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background: white;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.action-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.close-btn {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fecaca;
  padding: 5px 10px;
  border-radius: 5px;
}

.close-btn:hover {
  background: #fecaca;
}

.panel-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.segment-count {
  font-size: 14px;
  color: #64748b;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-box {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 14px;
}

.search-input {
  padding: 8px 12px 8px 36px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline {
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e2e8f0;
  background: #f8fafc;
}

.right-panel {
  width: 700px;
  display: flex;
  flex-direction: column;
  background: white;
}

.panel-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.panel-title h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.segment-count {
  font-size: 12px;
  color: #64748b;
  background: #e2e8f0;
  padding: 2px 8px;
  border-radius: 12px;
}

.file-viewer-container {
  flex: 1;
  overflow: hidden;
}

.segments-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.segments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.segment-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.segment-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.segment-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.segment-index {
  font-weight: 600;
  color: #3b82f6;
  font-size: 14px;
}

.segment-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #64748b;
}

.segment-score {
  color: #059669;
  font-weight: 500;
}

.segment-actions {
  display: flex;
  gap: 4px;
}

.segment-actions .action-btn {
  width: 24px;
  height: 24px;
  font-size: 12px;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #dc2626;
}

.segment-content {
  font-size: 14px;
  line-height: 1.6;
}

.content-text {
  margin: 0 0 12px 0;
  color: #374151;
}

.segment-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.metadata-item {
  font-size: 12px;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 4px;
  color: #64748b;
}

.metadata-key {
  font-weight: 500;
}

.segment-detail {
  border-top: 1px solid #e2e8f0;
  padding: 16px 20px;
  background: #f8fafc;
  max-height: 450px;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.collapse-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 12px;
}

.collapse-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.segment-detail.collapsed .detail-content {
  display: none;
}

.segment-detail.collapsed .detail-header {
  margin-bottom: 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.content-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
}

.content-textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.content-textarea[readonly] {
  background: #f8fafc;
  color: #64748b;
}

.metadata-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metadata-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.metadata-row .key {
  font-weight: 500;
  color: #374151;
}

.metadata-row .value {
  color: #64748b;
}

/* Excel控制样式 */
.excel-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.excel-controls label {
  font-size: 11px;
  color: #64748b;
  white-space: nowrap;
}

.excel-controls select {
  font-size: 11px;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  padding: 2px 4px;
  background: white;
}
</style>
