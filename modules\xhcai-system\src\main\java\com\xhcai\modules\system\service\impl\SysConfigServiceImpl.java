package com.xhcai.modules.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.SysConfigQueryDTO;
import com.xhcai.modules.system.dto.SysConfigDTO;
import com.xhcai.modules.system.entity.SysConfig;
import com.xhcai.modules.system.mapper.SysConfigMapper;
import com.xhcai.modules.system.service.ISysConfigService;
import com.xhcai.modules.system.vo.SysConfigVO;

/**
 * 系统配置服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements ISysConfigService {

    private static final Logger log = LoggerFactory.getLogger(SysConfigServiceImpl.class);

    @Autowired
    private SysConfigMapper configMapper;

    /**
     * 配置缓存
     */
    private final ConcurrentHashMap<String, String> configCache = new ConcurrentHashMap<>();

    @Override
    public PageResult<SysConfigVO> selectConfigPage(SysConfigQueryDTO queryDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        Page<SysConfig> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<SysConfig> configPage = configMapper.selectConfigPage(page,
                queryDTO.getConfigName(),
                queryDTO.getConfigKey(),
                queryDTO.getConfigType(),
                queryDTO.getConfigGroup(),
                queryDTO.getStatus(),
                queryDTO.getIsSystem(),
                tenantId);

        List<SysConfigVO> configVOs = configPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<>(configVOs, configPage.getTotal(), configPage.getCurrent(), configPage.getSize());
    }

    @Override
    public List<SysConfigVO> selectConfigList(SysConfigQueryDTO queryDTO) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        List<SysConfig> configs = configMapper.selectConfigList(
                queryDTO.getConfigName(),
                queryDTO.getConfigKey(),
                queryDTO.getConfigType(),
                queryDTO.getConfigGroup(),
                queryDTO.getStatus(),
                queryDTO.getIsSystem(),
                tenantId);

        return configs.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public SysConfigVO selectConfigById(String configId) {
        if (configId == null) {
            return null;
        }

        SysConfig config = getById(configId);
        if (config == null) {
            return null;
        }

        return convertToVO(config);
    }

    @Override
    public SysConfig selectByConfigKey(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        return configMapper.selectByConfigKey(configKey, tenantId);
    }

    @Override
    public String getConfigValue(String configKey) {
        return getConfigValue(configKey, null);
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        if (!StringUtils.hasText(configKey)) {
            return defaultValue;
        }

        // 先从缓存获取
        String cacheKey = SecurityUtils.getCurrentTenantId() + ":" + configKey;
        String value = configCache.get(cacheKey);
        if (value != null) {
            return value;
        }

        // 从数据库获取
        SysConfig config = selectByConfigKey(configKey);
        if (config != null && CommonConstants.STATUS_NORMAL.equals(config.getStatus())) {
            value = config.getConfigValue();
            // 放入缓存
            configCache.put(cacheKey, value != null ? value : "");
            return value;
        }

        return defaultValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertConfig(SysConfig config) {
        // 参数校验
        validateConfig(config, true);

        // 检查配置键是否已存在
        if (existsConfigKey(config.getConfigKey(), null)) {
            throw new BusinessException("配置键已存在");
        }

        // 设置排序号
        if (config.getSortOrder() == null) {
            String tenantId = SecurityUtils.getCurrentTenantId();
            Integer maxSortOrder = configMapper.selectMaxSortOrder(tenantId);
            config.setSortOrder(maxSortOrder + 1);
        }

        // 设置默认状态
        if (!StringUtils.hasText(config.getStatus())) {
            config.setStatus(CommonConstants.STATUS_NORMAL);
        }

        // 设置默认配置类型
        if (!StringUtils.hasText(config.getConfigType())) {
            config.setConfigType("N"); // 非系统内置
        }

        boolean result = save(config);
        if (result) {
            // 清除缓存
            refreshCache();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfig(SysConfig config) {
        // 参数校验
        validateConfig(config, false);

        // 检查配置是否存在
        SysConfig existConfig = getById(config.getId());
        if (existConfig == null) {
            throw new BusinessException("配置不存在");
        }

        // 检查配置键是否已存在（排除自己）
        if (existsConfigKey(config.getConfigKey(), config.getId())) {
            throw new BusinessException("配置键已存在");
        }

        // 系统内置配置不允许修改配置键和类型
        if (Boolean.TRUE.equals(existConfig.getIsSystem())) {
            config.setConfigKey(existConfig.getConfigKey());
            config.setConfigType(existConfig.getConfigType());
            config.setIsSystem(existConfig.getIsSystem());
        }

        boolean result = updateById(config);
        if (result) {
            // 清除缓存
            refreshCache();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfigs(List<String> configIds) {
        if (CollectionUtils.isEmpty(configIds)) {
            return false;
        }

        // 检查是否包含系统内置配置
        List<SysConfig> configs = listByIds(configIds);
        for (SysConfig config : configs) {
            if (Boolean.TRUE.equals(config.getIsSystem())) {
                throw new BusinessException("系统内置配置不能删除：" + config.getConfigName());
            }
        }

        boolean result = removeByIds(configIds);
        if (result) {
            // 清除缓存
            refreshCache();
        }
        return result;
    }

    @Override
    public boolean existsConfigKey(String configKey, String excludeId) {
        if (!StringUtils.hasText(configKey)) {
            return false;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        Integer count = configMapper.existsConfigKey(configKey, excludeId, tenantId);
        return count > 0;
    }

    @Override
    public List<String> selectConfigGroups() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        return configMapper.selectConfigGroups(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateConfigs(List<SysConfigDTO> configDTOs) {
        if (CollectionUtils.isEmpty(configDTOs)) {
            return false;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();

        for (SysConfigDTO configDTO : configDTOs) {
            if (!StringUtils.hasText(configDTO.getConfigKey())) {
                continue;
            }

            // 查询是否已存在该配置
            SysConfig existingConfig = selectByConfigKey(configDTO.getConfigKey());

            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setConfigValue(configDTO.getConfigValue());
                if (StringUtils.hasText(configDTO.getConfigName())) {
                    existingConfig.setConfigName(configDTO.getConfigName());
                }
                if (StringUtils.hasText(configDTO.getConfigType())) {
                    existingConfig.setConfigType(configDTO.getConfigType());
                }
                if (StringUtils.hasText(configDTO.getConfigGroup())) {
                    existingConfig.setConfigGroup(configDTO.getConfigGroup());
                }
                if (StringUtils.hasText(configDTO.getRemark())) {
                    existingConfig.setRemark(configDTO.getRemark());
                }
                existingConfig.setUpdateBy(currentUserId);
                updateById(existingConfig);
            } else {
                // 创建新配置
                SysConfig newConfig = new SysConfig();
                BeanUtils.copyProperties(configDTO, newConfig);
                newConfig.setTenantId(tenantId);
                newConfig.setCreateBy(currentUserId);
                newConfig.setUpdateBy(currentUserId);
                if (!StringUtils.hasText(newConfig.getStatus())) {
                    newConfig.setStatus("0"); // 默认启用
                }
                save(newConfig);
            }
        }

        // 清除缓存
        refreshCache();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> configIds, String status) {
        if (CollectionUtils.isEmpty(configIds) || !StringUtils.hasText(status)) {
            return false;
        }

        boolean result = lambdaUpdate()
                .in(SysConfig::getId, configIds)
                .set(SysConfig::getStatus, status)
                .update();

        if (result) {
            // 清除缓存
            refreshCache();
        }
        return result;
    }

    @Override
    public void refreshCache() {
        configCache.clear();
        log.info("系统配置缓存已清除");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initSystemConfigs() {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 初始化系统默认配置
        List<SysConfig> defaultConfigs = getDefaultConfigs();

        for (SysConfig config : defaultConfigs) {
            if (selectByConfigKey(config.getConfigKey()) == null) {
                config.setTenantId(tenantId);
                save(config);
            }
        }

        log.info("系统配置初始化完成");
    }

    @Override
    public List<SysConfigVO> exportConfigs(SysConfigQueryDTO queryDTO) {
        return selectConfigList(queryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importConfigs(List<SysConfig> configList) {
        if (CollectionUtils.isEmpty(configList)) {
            return "导入数据为空";
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMsg = new StringBuilder();

        for (SysConfig config : configList) {
            try {
                // 设置默认值
                if (!StringUtils.hasText(config.getStatus())) {
                    config.setStatus(CommonConstants.STATUS_NORMAL);
                }
                if (!StringUtils.hasText(config.getConfigType())) {
                    config.setConfigType("N");
                }

                insertConfig(config);
                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMsg.append("配置[").append(config.getConfigKey()).append("]导入失败：").append(e.getMessage()).append("; ");
                log.error("导入配置失败: {}", e.getMessage(), e);
            }
        }

        return String.format("导入完成，成功：%d，失败：%d。%s", successCount, failCount, errorMsg.toString());
    }

    /**
     * 参数校验
     */
    private void validateConfig(SysConfig config, boolean isInsert) {
        if (config == null) {
            throw new BusinessException("配置信息不能为空");
        }

        if (!StringUtils.hasText(config.getConfigName())) {
            throw new BusinessException("配置名称不能为空");
        }

        if (!StringUtils.hasText(config.getConfigKey())) {
            throw new BusinessException("配置键不能为空");
        }

        if (config.getConfigName().length() > 100) {
            throw new BusinessException("配置名称长度不能超过100个字符");
        }

        if (config.getConfigKey().length() > 100) {
            throw new BusinessException("配置键长度不能超过100个字符");
        }

        if (StringUtils.hasText(config.getConfigValue()) && config.getConfigValue().length() > 500) {
            throw new BusinessException("配置值长度不能超过500个字符");
        }
    }

    /**
     * 转换为VO
     */
    private SysConfigVO convertToVO(SysConfig config) {
        if (config == null) {
            return null;
        }

        SysConfigVO vo = new SysConfigVO();
        BeanUtils.copyProperties(config, vo);

        // 设置状态名称
        if (CommonConstants.STATUS_NORMAL.equals(config.getStatus())) {
            vo.setStatusName("正常");
        } else if ("1".equals(config.getStatus())) {
            vo.setStatusName("停用");
        } else {
            vo.setStatusName("未知");
        }

        // 设置配置类型名称
        if ("Y".equals(config.getConfigType())) {
            vo.setConfigTypeName("系统内置");
        } else if ("N".equals(config.getConfigType())) {
            vo.setConfigTypeName("用户自定义");
        } else {
            vo.setConfigTypeName("未知");
        }

        return vo;
    }

    /**
     * 获取默认系统配置
     */
    private List<SysConfig> getDefaultConfigs() {
        List<SysConfig> configs = new ArrayList<>();

        // 系统名称
        SysConfig systemName = new SysConfig();
        systemName.setConfigName("系统名称");
        systemName.setConfigKey("sys.system.name");
        systemName.setConfigValue("XHCAI智能平台");
        systemName.setConfigType("Y");
        systemName.setConfigGroup("系统配置");
        systemName.setConfigDesc("系统名称配置");
        systemName.setIsSystem(true);
        systemName.setStatus(CommonConstants.STATUS_NORMAL);
        systemName.setSortOrder(1);
        configs.add(systemName);

        // 系统版本
        SysConfig systemVersion = new SysConfig();
        systemVersion.setConfigName("系统版本");
        systemVersion.setConfigKey("sys.system.version");
        systemVersion.setConfigValue("1.0.0");
        systemVersion.setConfigType("Y");
        systemVersion.setConfigGroup("系统配置");
        systemVersion.setConfigDesc("系统版本配置");
        systemVersion.setIsSystem(true);
        systemVersion.setStatus(CommonConstants.STATUS_NORMAL);
        systemVersion.setSortOrder(2);
        configs.add(systemVersion);

        // 默认密码
        SysConfig defaultPassword = new SysConfig();
        defaultPassword.setConfigName("用户默认密码");
        defaultPassword.setConfigKey("sys.user.default.password");
        defaultPassword.setConfigValue("123456");
        defaultPassword.setConfigType("Y");
        defaultPassword.setConfigGroup("用户配置");
        defaultPassword.setConfigDesc("新用户默认密码");
        defaultPassword.setIsSystem(true);
        defaultPassword.setStatus(CommonConstants.STATUS_NORMAL);
        defaultPassword.setSortOrder(3);
        configs.add(defaultPassword);

        return configs;
    }
}
