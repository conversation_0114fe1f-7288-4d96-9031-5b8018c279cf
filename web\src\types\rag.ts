// ==================== 文档相关类型定义 ====================

/**
 * 文档实体
 */
export interface Document {
  id: string
  datasetId: string
  position: number
  dataSourceType: string
  dataSourceInfo?: Record<string, any>
  datasetProcessRuleId?: string
  batchId?: string
  name: string
  fileSize: number
  createdFrom: string
  createdBy?: string
  createdAt: string
  processingStartedAt?: string
  processingCompletedAt?: string
  fileId?: string
  wordCount?: number
  tokens?: number
  documentStatus: string | undefined
  error?: string | null
  enabled: boolean
  disabledAt?: string
  disabledBy?: string
  archived: boolean
  archivedReason?: string
  archivedBy?: string
  archivedAt?: string
  updatedAt?: string
  docType: string
  docMetadata?: Record<string, any>
  docForm?: string
  docLanguage?: string
  hitCount?: number
  displayStatus?: string
  remark?: string
  deleted?: boolean
  uploadTime?: Date
  updatedBy?: string
}

/**
 * 文档查询DTO
 */
export interface DocumentQueryDTO {
  current?: number
  size?: number
  datasetId?: string
  name?: string
  documentStatus?: string
  enabled?: boolean
  startTime?: string
  endTime?: string,
  batch?: string
}

/**
 * 文档VO
 */
export interface DocumentVO extends Document {
  uploading?: boolean
  uploaded?: boolean
  progress?: number
}

/**
 * 批量分段处理请求DTO
 */
export interface BatchSegmentationRequest {
  documentIds: string[]
  docConfigs: Record<string, Record<string, any>>
  segmentConfig?: FileCleanSegmentConfig
}

/**
 * 批量保存分段处理配置信息请求DTO
 */
export interface FileCleanSegmentConfigParams {
  documentIds: string[]
  docConfigs: Record<string, FileCleanSegmentConfig>
  segmentConfig: FileSegmentConfig
  cleaningConfig: FileCleaningConfig
}

// 文件分段配置接口
export interface FileCleanSegmentConfig {
  segmentConfig: FileSegmentConfig
  cleaningConfig: FileCleaningConfig
}

// 文件分段配置接口
export interface FileSegmentConfig {
  type: 'directory' | 'natural' | 'delimiter' | 'constantLength' | 'none'   // 分段类型： 目录切分 | 自然文段切分 | 固定大小切分 | 分隔符切分 | 不切分
  directory: Record<string, any>
  natural: Record<string, any>
  delimiter: Record<string, any>
  constantLength: Record<string, any>
  none: Record<string, any>
}

// 文件清理配置接口
export interface FileCleaningConfig {
  removeEmptyLines: boolean
  removeExtraSpaces: boolean
  removeSpecialChars: boolean
  normalizeText: boolean
  deleteSymbol: boolean
  deleteInlineMedia: boolean
  filterKeywords: string
}

// 向量化配置接口
export interface VectorizationConfig {
  // 索引模式：high_quality（高质量）、economy（经济）
  indexMode: 'high_quality' | 'economy'
  // 嵌入模型（仅高质量模式）
  embeddingModel?: string
  // 向量数据库ID
  vectorDatabase?: string
  // 向量数据库ID（默认选择）
  vectorDatabaseId?: string
  // 文件存储ID（默认选择）
  fileStorageId?: string
  // 检索设置
  retrievalSettings: RetrievalSettings
}

// 检索设置接口
export interface RetrievalSettings {
  // 检索模式：vector（向量检索）、fulltext（全文检索）、hybrid（混合检索）
  retrievalMode: 'vector' | 'fulltext' | 'hybrid'
  // 是否启用Rerank模型
  enableRerank: boolean
  // Rerank模型（启用时）
  rerankModel?: string
  // Top K（向量检索和全文检索）
  topK?: number
  // Score阈值（向量检索）
  scoreThreshold?: number
  // 混合检索权重设置
  hybridWeights: HybridWeights
}

// 混合检索权重接口
export interface HybridWeights {
  // 语义权重
  semanticWeight: number
  // 关键词权重
  keywordWeight: number
}

// 文件分段信息接口
export interface FileSegment {
  id: string
  content: string
  length: number
  index: number
}

// 定义文件类型
export interface UploadedFile extends DocumentVO{
  file?: File
  wordCount?: number
  // 后端文件ID（上传成功后从后端返回）
  documentId?: string
  processProgress?: number
  // 分段配置（如果为空则使用默认配置）
  segmentConfig?: FileSegmentConfig | null
  cleaningConfig?: FileCleaningConfig | null
  // 分段结果
  segments?: FileSegment[]
  segmentCount?: number
  estimatedTime?: number
  updateTime?: number | null
  isModified?: boolean
  [key: string]: any
}