package com.xhcai.modules.agent.vo;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体工作流VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体工作流信息")
public class AgentWorkflowVO {

    private static final long serialVersionUID = 1L;

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "workflow_001")
    private String id;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    private String agentId;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "客服助手")
    private String agentName;

    /**
     * 工作流名称
     */
    @Schema(description = "工作流名称", example = "客服工作流")
    private String name;

    /**
     * 工作流描述
     */
    @Schema(description = "工作流描述", example = "智能客服的工作流程配置")
    private String description;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1")
    private Integer version;

    /**
     * Vue Flow节点数据（JSON格式）
     */
    @Schema(description = "Vue Flow节点数据", example = "[{\"id\":\"node1\",\"type\":\"start\",\"position\":{\"x\":100,\"y\":100},\"data\":{\"label\":\"开始\"}}]")
    private String nodesData;

    /**
     * Vue Flow边数据（JSON格式）
     */
    @Schema(description = "Vue Flow边数据", example = "[{\"id\":\"edge1\",\"source\":\"node1\",\"target\":\"node2\",\"type\":\"default\"}]")
    private String edgesData;

    /**
     * 视口配置（JSON格式）
     */
    @Schema(description = "视口配置", example = "{\"x\":0,\"y\":0,\"zoom\":1}")
    private String viewportConfig;

    /**
     * 节点库配置（JSON格式）
     */
    @Schema(description = "节点库配置", example = "{\"categories\":[{\"name\":\"基础节点\",\"nodes\":[{\"type\":\"start\",\"label\":\"开始节点\"}]}]}")
    private String nodeLibrary;

    /**
     * 全局变量配置（JSON格式）
     */
    @Schema(description = "全局变量配置", example = "{\"var1\":\"value1\",\"var2\":\"value2\"}")
    private String globalVariables;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称", example = "启用")
    private String statusName;

    /**
     * 是否已发布
     */
    @Schema(description = "是否已发布", example = "false")
    private Boolean isPublished;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private LocalDateTime publishedAt;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    private LocalDateTime lastModified;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "node_add")
    private String operationType;

    /**
     * 操作描述
     */
    @Schema(description = "操作描述", example = "添加了开始节点")
    private String operationDesc;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "admin")
    private String createBy;

    /**
     * 更新人
     */
    @Schema(description = "更新人", example = "admin")
    private String updateBy;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "工作流备注信息")
    private String remark;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getNodesData() {
        return nodesData;
    }

    public void setNodesData(String nodesData) {
        this.nodesData = nodesData;
    }

    public String getEdgesData() {
        return edgesData;
    }

    public void setEdgesData(String edgesData) {
        this.edgesData = edgesData;
    }

    public String getViewportConfig() {
        return viewportConfig;
    }

    public void setViewportConfig(String viewportConfig) {
        this.viewportConfig = viewportConfig;
    }

    public String getNodeLibrary() {
        return nodeLibrary;
    }

    public void setNodeLibrary(String nodeLibrary) {
        this.nodeLibrary = nodeLibrary;
    }

    public String getGlobalVariables() {
        return globalVariables;
    }

    public void setGlobalVariables(String globalVariables) {
        this.globalVariables = globalVariables;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public LocalDateTime getLastModified() {
        return lastModified;
    }

    public void setLastModified(LocalDateTime lastModified) {
        this.lastModified = lastModified;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
