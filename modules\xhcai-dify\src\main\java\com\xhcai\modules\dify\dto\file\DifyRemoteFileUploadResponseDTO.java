package com.xhcai.modules.dify.dto.file;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify 远程文件上传响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 远程文件上传响应")
public class DifyRemoteFileUploadResponseDTO {

    /**
     * 文件ID
     */
    @Schema(description = "文件ID")
    private String id;

    /**
     * 文件名
     */
    @Schema(description = "文件名")
    private String name;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）")
    private Long size;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名")
    private String extension;

    /**
     * 文件访问URL
     */
    @Schema(description = "文件访问URL")
    private String url;

    /**
     * MIME 类型
     */
    @JsonProperty("mime_type")
    @Schema(description = "MIME 类型")
    private String mimeType;

    /**
     * 创建者ID
     */
    @JsonProperty("created_by")
    @Schema(description = "创建者ID")
    private String createdBy;

    /**
     * 创建时间（时间戳）
     */
    @JsonProperty("created_at")
    @Schema(description = "创建时间（时间戳）")
    private Long createdAt;
}
