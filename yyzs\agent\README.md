# YYZS Agent Platform

## 项目简介

YYZS Agent Platform 是一个基于 Spring Boot 3.5.3 的独立后端服务，专门用于管理 Elastic Stack 组件的安装、配置、监控和运维。该平台支持 Filebeat、Heartbeat、Metricbeat、Packetbeat、Winlogbeat、Auditbeat、Logstash、Elasticsearch、Kafka 等组件的自动化管理。

## 技术架构

- **框架**: Spring Boot 3.5.3
- **数据库**: PostgreSQL + JPA + MyBatis Plus
- **缓存**: 内存缓存 (Simple Cache)
- **监控**: OSHI (系统监控)
- **文档**: Swagger/OpenAPI 3
- **构建工具**: Maven
- **Java版本**: 21

## 主要功能

### 1. 组件管理
- 组件安装包上传
- 自动安装和配置
- 组件启动、停止、重启
- 组件卸载和清理
- 批量操作支持

### 2. 配置管理
- 配置文件生成和管理
- 配置模板支持
- 配置备份和恢复
- 配置验证

### 3. 监控功能
- 实时性能监控
- 资源使用统计
- 健康状态检查
- 告警通知
- 历史数据管理

### 4. 系统管理
- 系统资源监控
- 日志管理
- 自动启动配置
- 端口管理

## 项目结构

```
yyzs/agent/
├── src/main/java/com/yyzs/agent/
│   ├── YyzsAgentApplication.java          # 主启动类
│   ├── config/                            # 配置类
│   │   ├── ApplicationStartupRunner.java  # 启动初始化
│   │   ├── DatabaseConfig.java           # 数据库配置
│   │   ├── SecurityConfig.java           # 安全配置
│   │   └── SwaggerConfig.java            # API文档配置
│   ├── controller/                        # 控制器层
│   │   ├── ElasticComponentController.java # 组件管理API
│   │   └── ComponentMonitorController.java # 监控API
│   ├── service/                          # 服务层
│   │   ├── ElasticComponentService.java   # 组件服务接口
│   │   ├── ComponentInstallService.java   # 安装服务接口
│   │   ├── ComponentMonitorService.java   # 监控服务接口
│   │   ├── FileStorageService.java        # 文件存储服务接口
│   │   └── impl/                         # 服务实现类
│   ├── entity/                           # 实体类
│   │   ├── BaseEntity.java               # 基础实体
│   │   ├── ElasticComponent.java         # 组件实体
│   │   └── ComponentMonitor.java         # 监控数据实体
│   ├── mapper/                           # 数据访问层
│   │   ├── ElasticComponentMapper.java   # 组件Mapper
│   │   └── ComponentMonitorMapper.java   # 监控Mapper
│   ├── dto/                              # 数据传输对象
│   └── exception/                        # 异常处理
│       └── GlobalExceptionHandler.java   # 全局异常处理器
├── src/main/resources/
│   ├── application.yml                   # 主配置文件
│   └── application-dev.yml              # 开发环境配置
└── pom.xml                              # Maven配置文件
```

## API 接口规范

### 组件管理 API (`/api/components`)

- `POST /api/components/upload` - 上传组件安装包
- `POST /api/components/{componentId}/install` - 安装组件
- `DELETE /api/components/{componentId}/uninstall` - 卸载组件
- `POST /api/components/{componentId}/start` - 启动组件
- `POST /api/components/{componentId}/stop` - 停止组件
- `POST /api/components/{componentId}/restart` - 重启组件
- `GET /api/components` - 获取组件列表
- `GET /api/components/{componentId}` - 获取组件详情
- `GET /api/components/{componentId}/status` - 检查组件状态
- `POST /api/components/batch/start` - 批量启动组件
- `POST /api/components/batch/stop` - 批量停止组件
- `GET /api/components/statistics` - 获取组件统计信息
- `PUT /api/components/{componentId}/config` - 更新组件配置
- `GET /api/components/{componentId}/config` - 获取组件配置
- `GET /api/components/{componentId}/logs` - 获取组件日志

### 监控管理 API (`/api/monitor`)

- `GET /api/monitor/components/{componentId}/latest` - 获取最新监控数据
- `GET /api/monitor/components/{componentId}/history` - 获取历史监控数据
- `GET /api/monitor/components/latest` - 获取所有组件最新监控数据
- `GET /api/monitor/components/unhealthy` - 获取不健康的组件
- `GET /api/monitor/components/{componentId}/health` - 检查组件健康状态
- `GET /api/monitor/components/{componentId}/stats` - 获取组件性能统计
- `GET /api/monitor/system/resources` - 获取系统资源使用情况
- `POST /api/monitor/components/{componentId}/collect` - 手动收集监控数据
- `POST /api/monitor/components/collect-all` - 批量收集监控数据
- `POST /api/monitor/start` - 启动监控
- `POST /api/monitor/stop` - 停止监控
- `GET /api/monitor/status` - 获取监控状态
- `PUT /api/monitor/interval` - 设置监控间隔
- `DELETE /api/monitor/cleanup` - 清理历史监控数据

## 快速开始

### 1. 环境要求

- Java 21+
- PostgreSQL 12+
- Redis 6+
- Maven 3.8+

### 2. 数据库配置

创建数据库：
```sql
CREATE DATABASE yyzs_agent_dev;
```

### 3. 配置文件

修改 `src/main/resources/application-dev.yml` 中的数据库和Redis连接信息。

### 4. 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 5. 访问应用

- 应用地址: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html
- 健康检查: http://localhost:8080/actuator/health

## 支持的组件

- **Filebeat** - 日志文件采集
- **Heartbeat** - 服务可用性监控
- **Metricbeat** - 系统和服务指标采集
- **Packetbeat** - 网络数据包分析
- **Winlogbeat** - Windows事件日志采集
- **Auditbeat** - 审计数据采集
- **Logstash** - 数据处理管道
- **Elasticsearch** - 搜索和分析引擎
- **Kafka** - 分布式流处理平台

## 配置说明

### 应用配置

```yaml
yyzs:
  agent:
    # 组件安装目录
    install-path: /opt/elastic
    # 组件包存储目录
    package-storage-path: /data/packages
    # 监控数据收集间隔（秒）
    monitor-interval: 30
    # 支持的组件类型
    supported-components:
      - filebeat
      - heartbeat
      - metricbeat
      - packetbeat
      - winlogbeat
      - auditbeat
      - logstash
      - elasticsearch
      - kafka
```

## 开发指南

### 添加新组件支持

1. 在 `ComponentInstallServiceImpl` 中添加组件类型判断
2. 实现组件特定的安装、启动、停止逻辑
3. 添加组件配置模板
4. 更新支持的组件列表

### 扩展监控指标

1. 在 `ComponentMonitor` 实体中添加新字段
2. 在 `ComponentMonitorServiceImpl` 中实现数据收集逻辑
3. 更新数据库表结构
4. 添加相应的API接口

## 许可证

Apache License 2.0

## 联系方式

- 邮箱: <EMAIL>
- 网站: https://www.yyzs.com
