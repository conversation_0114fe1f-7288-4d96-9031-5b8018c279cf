<template>
  <div class="node-properties">
    <div class="property-header">
      <div class="header-content">
        <div class="node-info">
          <div class="node-icon">
            <i class="fas" :class="getNodeIcon(node.type)"></i>
          </div>
          <div>
            <div class="node-title">{{ node.data?.label || node.type }}</div>
            <div class="node-id">ID: {{ node.id }}</div>
          </div>
        </div>
        <button
          class="btn btn-icon close-btn"
          @click="closeProperties"
          title="收缩属性面板"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- 选项卡导航 -->
    <div class="tabs-nav">
      <button
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-button"
        :class="{ active: activeTab === tab.key }"
        @click="activeTab = tab.key"
      >
        <i :class="tab.icon"></i>
        {{ tab.label }}
      </button>
    </div>

    <div class="property-content">
      <!-- 属性配置选项卡 -->
      <div v-show="activeTab === 'properties'" class="tab-content">
        <!-- 基础属性 -->
        <div class="property-section">
          <h4 class="section-title">基础属性</h4>

          <div class="form-group">
            <label>节点标签</label>
            <input
              v-model="localData.label"
              type="text"
              class="form-input"
              @input="onUpdate"
            />
          </div>

          <div class="form-group">
            <label>描述</label>
            <textarea
              v-model="localData.description"
              class="form-textarea"
              rows="3"
              @input="onUpdate"
            ></textarea>
          </div>


        </div>

        <!-- 节点配置 -->
        <div class="property-section" v-if="hasConfig">
          <h4 class="section-title">节点配置</h4>

          <!-- 动态配置表单 -->
          <component
            :is="getConfigComponent(node.type)"
            v-if="getConfigComponent(node.type)"
            :config="localData.config"
            @update="onConfigUpdate"
          />

          <!-- 通用配置编辑器 -->
          <div v-else class="config-editor">
            <div
              v-for="(value, key) in localData.config"
              :key="key"
              class="form-group"
            >
              <ParameterSelector
                v-if="typeof value === 'string' || typeof value === 'number'"
                v-model="localData.config[key]"
                :label="formatConfigKey(key)"
                :type="typeof value === 'number' ? 'number' : 'text'"
                :available-parameters="availableParameters"
                :node-id="node.id"
                @input="onUpdate"
              />
              <textarea
                v-else-if="typeof value === 'object'"
                v-model="localData.config[key]"
                class="form-textarea"
                rows="3"
                @input="onUpdate"
              ></textarea>
              <div v-else class="config-value">
                {{ value }}
              </div>
            </div>

            <!-- 添加配置项 -->
            <div class="add-config-section">
              <div class="add-config-form" v-if="showAddConfigForm">
                <div class="form-row">
                  <div class="form-group">
                    <label>配置项名称</label>
                    <input
                      v-model="newConfigKey"
                      type="text"
                      class="form-input"
                      placeholder="请输入配置项名称"
                      @keyup.enter="confirmAddConfig"
                    />
                  </div>
                  <div class="form-group">
                    <label>配置项类型</label>
                    <select v-model="newConfigType" class="form-select">
                      <option value="text">文本</option>
                      <option value="number">数字</option>
                      <option value="boolean">布尔值</option>
                      <option value="object">对象</option>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label>默认值</label>
                  <input
                    v-if="newConfigType === 'text'"
                    v-model="newConfigValue"
                    type="text"
                    class="form-input"
                    placeholder="请输入默认值"
                  />
                  <input
                    v-else-if="newConfigType === 'number'"
                    v-model.number="newConfigValue"
                    type="number"
                    class="form-input"
                    placeholder="请输入数字"
                  />
                  <select
                    v-else-if="newConfigType === 'boolean'"
                    v-model="newConfigValue"
                    class="form-select"
                  >
                    <option :value="true">是</option>
                    <option :value="false">否</option>
                  </select>
                  <textarea
                    v-else-if="newConfigType === 'object'"
                    v-model="newConfigValue"
                    class="form-textarea"
                    rows="3"
                    placeholder="请输入JSON格式的对象"
                  ></textarea>
                </div>
                <div class="form-actions">
                  <button class="btn btn-secondary btn-sm" @click="cancelAddConfig">
                    <i class="fas fa-times"></i>
                    取消
                  </button>
                  <button class="btn btn-primary btn-sm" @click="confirmAddConfig">
                    <i class="fas fa-check"></i>
                    确认添加
                  </button>
                </div>
              </div>

              <button
                v-else
                class="btn btn-secondary btn-sm add-config-btn"
                @click="showAddConfigForm = true"
              >
                <i class="fas fa-plus"></i>
                添加配置项
              </button>
            </div>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="property-section">
          <h4 class="section-title">状态信息</h4>

          <div class="form-group">
            <label>状态</label>
            <select v-model="localData.status" class="form-select" @change="onUpdate">
              <option value="stable">稳定</option>
              <option value="beta">测试</option>
              <option value="alpha">预览</option>
              <option value="deprecated">已弃用</option>
            </select>
          </div>

          <div class="form-group">
            <label>版本</label>
            <input
              v-model="localData.version"
              type="text"
              class="form-input"
              placeholder="例如: 1.0.0"
              @input="onUpdate"
            />
          </div>
        </div>

        <!-- 标签管理 -->
        <div class="property-section">
          <h4 class="section-title">标签管理</h4>

          <div class="tags-editor">
            <div class="tag-list" v-if="localData.tags && localData.tags.length > 0">
              <span v-for="(tag, index) in localData.tags" :key="index" class="tag-item">
                {{ tag }}
                <button @click="removeTag(index)" class="tag-remove">×</button>
              </span>
            </div>
            <div class="tag-input">
              <input
                v-model="newTag"
                type="text"
                class="form-input"
                placeholder="添加标签"
                @keyup.enter="addTag"
              />
              <button @click="addTag" class="btn btn-sm btn-primary">添加</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 参数依赖选项卡 -->
      <div v-show="activeTab === 'parameters'" class="tab-content">
        <div class="property-section">
          <h4 class="section-title">上游节点参数</h4>
          <div v-if="upstreamNodes.length === 0" class="no-upstream">
            <i class="fas fa-info-circle"></i>
            <span>当前节点没有上游节点</span>
          </div>
          <div v-else class="upstream-list">
            <div
              v-for="nodeId in upstreamNodes"
              :key="nodeId"
              class="upstream-node"
            >
              <div class="upstream-header">
                <i class="fas fa-cube"></i>
                <span>{{ getNodeLabel(nodeId) }}</span>
              </div>
              <div class="parameter-list">
                <div
                  v-for="param in getNodeParameters(nodeId)"
                  :key="param.key"
                  class="parameter-item"
                >
                  <div class="parameter-info">
                    <span class="parameter-name">{{ param.label }}</span>
                    <span class="parameter-type">{{ param.type }}</span>
                  </div>
                  <div class="parameter-value">{{ param.value || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="property-section">
          <h4 class="section-title">依赖验证</h4>
          <div class="dependency-validation">
            <div v-if="dependencyValidation.valid" class="validation-success">
              <i class="fas fa-check-circle"></i>
              <span>所有参数依赖都有效</span>
            </div>
            <div v-else class="validation-errors">
              <div
                v-for="error in dependencyValidation.errors"
                :key="error"
                class="validation-error"
              >
                <i class="fas fa-exclamation-triangle"></i>
                <span>{{ error }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 样式设置选项卡 -->
      <div v-show="activeTab === 'style'" class="tab-content">
        <NodeStyleEditor
          :selected-node-type="node.data?.nodeType || node.type"
          :node="node"
          @position-update="onPositionUpdate"
          @style-update="onStyleUpdate"
        />
      </div>


    </div>

    <!-- 操作按钮 -->
    <div class="property-actions">
      <button 
        class="btn btn-secondary"
        @click="resetToDefault"
      >
        <i class="fas fa-undo"></i>
        重置
      </button>
      
      <button 
        class="btn btn-danger"
        @click="deleteNode"
      >
        <i class="fas fa-trash"></i>
        删除
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { Node } from '@vue-flow/core'
import { storeToRefs } from 'pinia'

// 导入配置组件
import DatabaseConfig from './config/DatabaseConfig.vue'
import AIConfig from './config/AIConfig.vue'
import ParameterSelector from './ParameterSelector.vue'
import NodeStyleEditor from './NodeStyleEditor.vue'
import { useNodeParameters } from '../composables/useNodeParameters'

// 导入全局状态管理
import { useWorkflowStore } from '@/stores/workflowStore'

// Props
interface Props {
  node: Node
  nodes?: Node[]
  edges?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  nodes: () => [],
  edges: () => []
})

// Emits
const emit = defineEmits<{
  'update': [nodeId: string, updates: any]
  'delete': [nodeId: string]
  'close': []
}>()

// 使用全局状态管理
const workflowStore = useWorkflowStore()
const { nodes: globalNodes, edges: globalEdges } = storeToRefs(workflowStore)

// 响应式数据
const activeTab = ref('properties')

// 选项卡配置
const tabs = [
  { key: 'properties', label: '配置', icon: 'fas fa-cog' },
  { key: 'parameters', label: '参数', icon: 'fas fa-link' },
  { key: 'style', label: '样式', icon: 'fas fa-palette' }
]

const localData = ref({
  label: '',
  description: '',
  config: {} as Record<string, any>,
  status: 'stable',
  version: '',
  tags: [] as string[]
})

const localPosition = ref({
  x: 0,
  y: 0
})

const localStyle = ref({
  width: 200,
  height: 100,
  backgroundColor: '#ffffff',
  borderColor: '#d1d5db'
})

// 添加配置项相关
const showAddConfigForm = ref(false)
const newConfigKey = ref('')
const newConfigType = ref('text')
const newConfigValue = ref<any>('')

// 标签管理相关
const newTag = ref('')

// 参数依赖管理 - 优先使用全局状态，fallback到props
const nodesRef = computed(() => globalNodes.value.length > 0 ? globalNodes.value : (props.nodes || []))
const edgesRef = computed(() => globalEdges.value.length > 0 ? globalEdges.value : (props.edges || []))
const {
  getUpstreamNodes,
  getAvailableParameters,
  getNodeOutputParameters,
  validateNodeDependencies
} = useNodeParameters(nodesRef, edgesRef)

// 计算属性
const hasConfig = computed(() => {
  return localData.value.config && Object.keys(localData.value.config).length > 0
})

const upstreamNodes = computed(() => {
  return getUpstreamNodes(props.node.id)
})

const availableParameters = computed(() => {
  return getAvailableParameters(props.node.id)
})

const dependencyValidation = computed(() => {
  return validateNodeDependencies(props.node.id)
})

// 方法
const getNodeIcon = (nodeType: string | undefined) => {
  if (!nodeType) return 'fa-cube'

  const iconMap: Record<string, string> = {
    start: 'fa-play-circle',
    end: 'fa-stop-circle',
    condition: 'fa-code-branch',
    mysql: 'fa-database',
    postgresql: 'fa-database',
    oracle: 'fa-database',
    redis: 'fa-layer-group',
    'llm-chat': 'fa-comments',
    'generate-pdf': 'fa-file-pdf',
    'pie-chart': 'fa-chart-pie',
    'http-request': 'fa-globe'
  }
  return iconMap[nodeType] || 'fa-cube'
}

const getConfigComponent = (nodeType: string | undefined) => {
  if (!nodeType) return null

  // 返回对应节点类型的配置组件
  const componentMap: Record<string, any> = {
    mysql: DatabaseConfig,
    postgresql: DatabaseConfig,
    oracle: DatabaseConfig,
    dameng: DatabaseConfig,
    elasticsearch: DatabaseConfig,
    redis: DatabaseConfig,
    'llm-chat': AIConfig,
    'text-embedding': AIConfig,
    'speech-to-text': AIConfig,
    'text-to-speech': AIConfig,
    'image-generation': AIConfig,
    'image-analysis': AIConfig,
    'knowledge-base': AIConfig
  }

  return componentMap[nodeType] || null
}

const formatConfigKey = (key: string) => {
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
}

const onUpdate = () => {
  // 立即更新节点数据，确保实时响应
  const updatedData = { ...props.node.data, ...localData.value }
  emit('update', props.node.id, {
    data: updatedData
  })
}

const onPositionUpdate = () => {
  emit('update', props.node.id, {
    position: localPosition.value
  })
}

const onStyleUpdate = () => {
  emit('update', props.node.id, {
    style: localStyle.value,
    data: {
      ...props.node.data,
      style: localStyle.value
    }
  })
}

const onConfigUpdate = (config: any) => {
  // 立即更新本地配置数据
  localData.value.config = { ...localData.value.config, ...config }
  // 立即触发节点更新
  onUpdate()
}

const confirmAddConfig = () => {
  if (!newConfigKey.value.trim()) {
    alert('请输入配置项名称')
    return
  }

  if (localData.value.config[newConfigKey.value]) {
    alert('配置项已存在')
    return
  }

  let value = newConfigValue.value

  // 根据类型转换值
  if (newConfigType.value === 'number') {
    value = Number(value) || 0
  } else if (newConfigType.value === 'boolean') {
    value = Boolean(value)
  } else if (newConfigType.value === 'object') {
    try {
      value = JSON.parse(value || '{}')
    } catch (e) {
      alert('对象格式不正确，请输入有效的JSON')
      return
    }
  }

  localData.value.config[newConfigKey.value] = value
  onUpdate()
  cancelAddConfig()
}

const cancelAddConfig = () => {
  showAddConfigForm.value = false
  newConfigKey.value = ''
  newConfigType.value = 'text'
  newConfigValue.value = ''
}

const resetToDefault = () => {
  if (confirm('确定要重置为默认配置吗？')) {
    // TODO: 重置为默认配置
    console.log('重置节点配置')
  }
}

const getNodeLabel = (nodeId: string) => {
  const nodes = nodesRef.value
  const node = nodes.find(n => n.id === nodeId)
  return node?.data?.label || node?.type || nodeId
}

const getNodeParameters = (nodeId: string) => {
  return getNodeOutputParameters(nodeId)
}

const deleteNode = () => {
  if (confirm('确定要删除此节点吗？')) {
    emit('delete', props.node.id)
  }
}

const closeProperties = () => {
  emit('close')
}

// 标签管理方法
const addTag = () => {
  if (newTag.value.trim() && localData.value.tags) {
    if (!localData.value.tags.includes(newTag.value.trim())) {
      localData.value.tags.push(newTag.value.trim())
      newTag.value = ''
      onUpdate()
    }
  }
}

const removeTag = (index: number) => {
  if (localData.value.tags) {
    localData.value.tags.splice(index, 1)
    onUpdate()
  }
}

// 初始化数据
const initializeData = () => {
  localData.value = {
    label: props.node.data?.label || '',
    description: props.node.data?.description || '',
    config: props.node.data?.config || {},
    status: props.node.data?.status || 'stable',
    version: props.node.data?.version || '',
    tags: props.node.data?.tags || []
  }
  
  localPosition.value = {
    x: props.node.position.x,
    y: props.node.position.y
  }
  
  // 处理样式，考虑到style可能是函数或对象
  const nodeStyle = props.node.style
  if (nodeStyle && typeof nodeStyle === 'object' && !('call' in nodeStyle)) {
    localStyle.value = {
      width: (nodeStyle as any).width || 200,
      height: (nodeStyle as any).height || 100,
      backgroundColor: (nodeStyle as any).backgroundColor || '#ffffff',
      borderColor: (nodeStyle as any).borderColor || '#d1d5db'
    }
  } else {
    localStyle.value = {
      width: 200,
      height: 100,
      backgroundColor: '#ffffff',
      borderColor: '#d1d5db'
    }
  }
}

// 监听节点变化
watch(() => props.node, () => {
  initializeData()
}, { immediate: true })

// 监听节点数据变化，确保实时同步
watch(() => props.node.data, (newData) => {
  if (newData) {
    // 只更新不会导致循环的字段
    if (newData.label !== localData.value.label) {
      localData.value.label = newData.label || ''
    }
    if (newData.description !== localData.value.description) {
      localData.value.description = newData.description || ''
    }
    if (JSON.stringify(newData.config) !== JSON.stringify(localData.value.config)) {
      localData.value.config = { ...(newData.config || {}) }
    }
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.node-properties {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.property-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.close-btn {
  color: #6b7280;
  background: transparent;
  border: none;
  padding: 4px;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-btn:hover {
  color: #374151;
  background: #f3f4f6;
}

.tabs-nav {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: #374151;
  background: #f3f4f6;
}

.tab-button.active {
  color: #3b82f6;
  background: #ffffff;
  border-bottom-color: #3b82f6;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.node-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-icon i {
  color: #6b7280;
  font-size: 18px;
}

.node-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.node-id {
  font-size: 12px;
  color: #9ca3af;
  font-family: monospace;
}

.property-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.property-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
}

.color-input {
  height: 40px;
  padding: 4px;
}

.position-inputs {
  display: flex;
  gap: 12px;
}

.input-group {
  flex: 1;
}

.input-group label {
  font-size: 12px;
  color: #6b7280;
}

.config-editor {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

.config-value {
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 4px;
  font-size: 13px;
  color: #6b7280;
  font-family: monospace;
}



.property-actions {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  justify-content: flex-end;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-danger:hover {
  background: #fee2e2;
}

/* 参数依赖样式 */
.no-upstream {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 14px;
}

.upstream-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upstream-node {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.upstream-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  color: #374151;
}

.parameter-list {
  padding: 8px 0;
}

.parameter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.parameter-item:last-child {
  border-bottom: none;
}

.parameter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.parameter-name {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
}

.parameter-type {
  font-size: 10px;
  padding: 2px 6px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

.parameter-value {
  font-size: 12px;
  color: #6b7280;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.dependency-validation {
  padding: 16px;
}

.validation-success {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #059669;
  font-size: 14px;
}

.validation-errors {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.validation-error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #dc2626;
  font-size: 13px;
  padding: 8px 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
}

/* 添加配置项样式 */
.add-config-section {
  margin-top: 16px;
}

.add-config-form {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.form-row {
  display: flex;
  gap: 12px;
}

.form-row .form-group {
  flex: 1;
}

.form-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}

.add-config-btn {
  width: 100%;
  justify-content: center;
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  outline: none;
  transition: border-color 0.2s;
}

.form-select:focus {
  border-color: #3b82f6;
}

/* 标签管理样式 */
.tags-editor {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  min-height: 32px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-size: 12px;
  color: #374151;
  font-weight: 500;
}

.tag-remove {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.tag-remove:hover {
  color: #ef4444;
  background: #fef2f2;
}

.tag-input {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag-input .form-input {
  flex: 1;
}
</style>
