-- 文件上传记录表
CREATE TABLE IF NOT EXISTS upload_files (
    id VARCHAR(36) PRIMARY KEY,
    original_filename VARCHAR(255) NOT NULL,
    file_size BIGINT,
    file_extension VARCHAR(10),
    mime_type VARCHAR(100),
    file_hash VARCHAR(64),
    dataset_id VARCHAR(36) NOT NULL,
    batch_id VARCHAR(36),
    document_id VARCHAR(36),
    minio_bucket VARCHAR(100),
    minio_object_name VARCHAR(500),
    minio_url VARCHAR(1000),
    upload_status VARCHAR(20) DEFAULT 'uploading',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    upload_user_id VARCHAR(36),
    operation_type VARCHAR(20) DEFAULT 'upload',
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_message VARCHAR(1000),
    
    -- 基础字段
    tenant_id VARCHAR(36),
    create_by VARCHAR(36),
    create_time TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    update_by <PERSON><PERSON>HA<PERSON>(36),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted SMALLINT DEFAULT 0,
    version INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE upload_files IS '文件上传记录表';

-- 添加列注释
COMMENT ON COLUMN upload_files.id IS '主键ID';
COMMENT ON COLUMN upload_files.original_filename IS '原始文件名';
COMMENT ON COLUMN upload_files.file_size IS '文件大小（字节）';
COMMENT ON COLUMN upload_files.file_extension IS '文件扩展名';
COMMENT ON COLUMN upload_files.mime_type IS 'MIME类型';
COMMENT ON COLUMN upload_files.file_hash IS '文件内容hash值';
COMMENT ON COLUMN upload_files.dataset_id IS '知识库ID';
COMMENT ON COLUMN upload_files.batch_id IS '上传批次ID';
COMMENT ON COLUMN upload_files.document_id IS '关联的文档ID（documents表）';
COMMENT ON COLUMN upload_files.minio_bucket IS 'MinIO存储桶名称';
COMMENT ON COLUMN upload_files.minio_object_name IS 'MinIO对象名称（文件路径）';
COMMENT ON COLUMN upload_files.minio_url IS 'MinIO访问URL';
COMMENT ON COLUMN upload_files.upload_status IS '上传状态：uploading-上传中，uploaded-已上传，failed-失败，deleted-已删除';
COMMENT ON COLUMN upload_files.upload_time IS '上传时间';
COMMENT ON COLUMN upload_files.upload_user_id IS '上传用户ID';
COMMENT ON COLUMN upload_files.operation_type IS '操作类型：upload-上传，delete-删除';
COMMENT ON COLUMN upload_files.operation_time IS '操作时间';
COMMENT ON COLUMN upload_files.error_message IS '错误信息';
COMMENT ON COLUMN upload_files.tenant_id IS '租户ID';
COMMENT ON COLUMN upload_files.create_by IS '创建人';
COMMENT ON COLUMN upload_files.create_time IS '创建时间';
COMMENT ON COLUMN upload_files.update_by IS '更新人';
COMMENT ON COLUMN upload_files.update_time IS '更新时间';
COMMENT ON COLUMN upload_files.deleted IS '删除标记：0-未删除，1-已删除';
COMMENT ON COLUMN upload_files.version IS '版本号';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_upload_files_update_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_upload_files_update_time
    BEFORE UPDATE ON upload_files
    FOR EACH ROW
    EXECUTE FUNCTION update_upload_files_update_time();

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_upload_files_dataset_id ON upload_files(dataset_id);
CREATE INDEX IF NOT EXISTS idx_upload_files_batch_id ON upload_files(batch_id);
CREATE INDEX IF NOT EXISTS idx_upload_files_document_id ON upload_files(document_id);
CREATE INDEX IF NOT EXISTS idx_upload_files_upload_user_id ON upload_files(upload_user_id);
CREATE INDEX IF NOT EXISTS idx_upload_files_upload_status ON upload_files(upload_status);
CREATE INDEX IF NOT EXISTS idx_upload_files_upload_time ON upload_files(upload_time);
CREATE INDEX IF NOT EXISTS idx_upload_files_operation_type ON upload_files(operation_type);
CREATE INDEX IF NOT EXISTS idx_upload_files_tenant_id ON upload_files(tenant_id);
CREATE INDEX IF NOT EXISTS idx_upload_files_deleted ON upload_files(deleted);
CREATE INDEX IF NOT EXISTS idx_upload_files_file_hash ON upload_files(file_hash);
