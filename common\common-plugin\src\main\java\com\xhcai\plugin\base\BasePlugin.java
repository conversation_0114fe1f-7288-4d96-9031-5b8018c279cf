package com.xhcai.plugin.base;

import java.time.LocalDateTime;
import java.util.Map;

import org.pf4j.Plugin;
import org.pf4j.PluginWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xhcai.plugin.core.PluginInfo;
import com.xhcai.plugin.core.PluginStatus;

/**
 * 插件基础类 所有插件都应该继承此类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class BasePlugin extends Plugin {

    protected static final Logger log = LoggerFactory.getLogger(BasePlugin.class);

    /**
     * 插件信息
     */
    protected PluginInfo pluginInfo;

    /**
     * 插件配置
     */
    protected Map<String, Object> configuration;

    public BasePlugin(PluginWrapper wrapper) {
        super(wrapper);
        initializePluginInfo();
    }

    /**
     * 初始化插件信息
     */
    private void initializePluginInfo() {
        this.pluginInfo = PluginInfo.builder()
                .pluginId(wrapper.getPluginId())
                .pluginName(wrapper.getDescriptor().getPluginDescription())
                .version(wrapper.getDescriptor().getVersion())
                .pluginClass(wrapper.getDescriptor().getPluginClass())
                .author(wrapper.getDescriptor().getProvider())
                .description(wrapper.getDescriptor().getPluginDescription())
                .status(PluginStatus.CREATED)
                .pluginPath(wrapper.getPluginPath().toString())
                .createTime(LocalDateTime.now())
                .build();
    }

    @Override
    public void start() {
        log.info("Starting plugin: {} ({})", pluginInfo.getPluginName(), pluginInfo.getPluginId());

        try {
            pluginInfo.setStatus(PluginStatus.STARTING);
            pluginInfo.setUpdateTime(LocalDateTime.now());

            // 执行插件特定的启动逻辑
            doStart();

            pluginInfo.setStatus(PluginStatus.STARTED);
            log.info("Plugin started successfully: {} ({})", pluginInfo.getPluginName(), pluginInfo.getPluginId());

        } catch (Exception e) {
            pluginInfo.setStatus(PluginStatus.FAILED);
            log.error("Failed to start plugin: {} ({})", pluginInfo.getPluginName(), pluginInfo.getPluginId(), e);
            throw new RuntimeException("Failed to start plugin", e);
        }
    }

    @Override
    public void stop() {
        log.info("Stopping plugin: {} ({})", pluginInfo.getPluginName(), pluginInfo.getPluginId());

        try {
            pluginInfo.setStatus(PluginStatus.STOPPING);
            pluginInfo.setUpdateTime(LocalDateTime.now());

            // 执行插件特定的停止逻辑
            doStop();

            pluginInfo.setStatus(PluginStatus.STOPPED);
            log.info("Plugin stopped successfully: {} ({})", pluginInfo.getPluginName(), pluginInfo.getPluginId());

        } catch (Exception e) {
            pluginInfo.setStatus(PluginStatus.FAILED);
            log.error("Failed to stop plugin: {} ({})", pluginInfo.getPluginName(), pluginInfo.getPluginId(), e);
            throw new RuntimeException("Failed to stop plugin", e);
        }
    }

    /**
     * 插件特定的启动逻辑 子类可以重写此方法实现自定义启动逻辑
     */
    protected void doStart() {
        // 默认实现为空
    }

    /**
     * 插件特定的停止逻辑 子类可以重写此方法实现自定义停止逻辑
     */
    protected void doStop() {
        // 默认实现为空
    }

    /**
     * 设置插件配置
     */
    public void setConfiguration(Map<String, Object> configuration) {
        this.configuration = configuration;
        this.pluginInfo.setConfiguration(configuration);

        // 应用配置
        applyConfiguration(configuration);
    }

    /**
     * 应用配置 子类可以重写此方法处理配置变更
     */
    protected void applyConfiguration(Map<String, Object> configuration) {
        // 默认实现为空
    }

    /**
     * 获取插件信息
     */
    public PluginInfo getPluginInfo() {
        return pluginInfo;
    }

    /**
     * 获取插件配置
     */
    public Map<String, Object> getConfiguration() {
        return configuration;
    }

    /**
     * 检查插件健康状态
     */
    public boolean isHealthy() {
        return pluginInfo.getStatus() == PluginStatus.STARTED;
    }
}
