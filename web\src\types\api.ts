/**
 * API相关类型定义
 */

// 基础响应结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success?: boolean
  timestamp?: number
}

// 分页响应结构
export interface PaginatedResponse<T = any> {
  list: T[]
  records?: T[] // 兼容不同的分页响应格式
  pagination: {
    currentPage: number
    pageSize: number
    total: number
  }
}

// MyBatis Plus 分页响应结构
export interface PageResult<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 请求配置
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  skipAuth?: boolean // 是否跳过认证
  skipErrorHandler?: boolean // 是否跳过错误处理
  contentType?: 'json' | 'form' | 'multipart' | 'text' // 内容类型
  formData?: FormData // 表单数据
  onUploadProgress?: (progress: number) => void // 上传进度回调
}

// Token信息
export interface TokenInfo {
  token: string
  access_token: string
  refresh_token: string
  expires_in?: number
  token_type?: string
}

// 错误信息
export interface ApiError {
  code: number
  msg: string
  details?: any
  timestamp?: number
}

// 请求拦截器类型
export type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>

// 响应拦截器类型
export type ResponseInterceptor<T = any> = (response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>

// 错误拦截器类型
export type ErrorInterceptor = (error: ApiError) => Promise<never> | ApiError

// SSE配置
export interface SSEConfig {
  url: string
  headers?: Record<string, string>
  withCredentials?: boolean
  skipAuth?: boolean // 是否跳过认证
  reconnect?: boolean // 是否自动重连
  reconnectInterval?: number // 重连间隔（毫秒）
  maxReconnectAttempts?: number // 最大重连次数
}

// SSE事件类型
export interface SSEEvent {
  type: string
  data: any
  id?: string
  retry?: number
}

// SSE连接状态
export enum SSEConnectionState {
  CONNECTING = 'connecting',
  OPEN = 'open',
  CLOSED = 'closed',
  ERROR = 'error'
}

// SSE事件处理器类型
export type SSEEventHandler = (event: SSEEvent) => void
export type SSEErrorHandler = (error: Event) => void
export type SSEOpenHandler = (event: Event) => void
export type SSECloseHandler = (event: Event) => void
