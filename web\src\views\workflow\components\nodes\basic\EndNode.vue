<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="end-node-content">
        <p class="node-description">{{ nodeConfig.description }}</p>
        <div class="node-config" v-if="data.config && Object.keys(data.config).length > 0">
          <div class="config-item" v-for="(value, key) in data.config" :key="key">
            <span class="config-key">{{ key }}:</span>
            <span class="config-value">{{ value }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from '../BaseNode.vue'
import { getNodeByType } from '../../../config/nodeLibrary'

// Props
interface EndNodeData {
  label?: string
  description?: string
  config?: Record<string, any>
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: EndNodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 获取节点配置
const nodeConfig = computed(() => {
  return getNodeByType('end') || {
    description: '工作流的结束节点，可以有多个结束节点'
  }
})
</script>

<style scoped>
.end-node-content {
  text-align: center;
}

.node-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.node-config {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.config-key {
  font-weight: 500;
}

.config-value {
  font-family: monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}
</style>
