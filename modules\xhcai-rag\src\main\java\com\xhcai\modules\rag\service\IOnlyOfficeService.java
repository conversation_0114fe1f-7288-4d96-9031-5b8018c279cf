package com.xhcai.modules.rag.service;

import java.util.Map;

/**
 * OnlyOffice服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IOnlyOfficeService {

    /**
     * 生成文档预览配置
     *
     * @param documentId 文档ID
     * @param fileName 文件名
     * @param fileUrl 文件URL
     * @param mode 模式：view（预览）、edit（编辑）
     * @return OnlyOffice配置
     */
    Map<String, Object> generateDocumentConfig(String documentId, String fileName, String fileUrl, String mode);

    /**
     * 生成文档预览URL
     *
     * @param documentId 文档ID
     * @return 预览URL
     */
    String generatePreviewUrl(String documentId);

    /**
     * 处理OnlyOffice回调
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    Map<String, Object> handleCallback(Map<String, Object> callbackData);

    /**
     * 检查文档格式是否支持
     *
     * @param format 文件格式
     * @return 是否支持
     */
    boolean isSupportedFormat(String format);

    /**
     * 检查文档格式是否可编辑
     *
     * @param format 文件格式
     * @return 是否可编辑
     */
    boolean isEditableFormat(String format);

    /**
     * 生成JWT令牌
     *
     * @param payload 载荷数据
     * @return JWT令牌
     */
    String generateJwtToken(Map<String, Object> payload);

    /**
     * 验证JWT令牌
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    boolean validateJwtToken(String token);
}
