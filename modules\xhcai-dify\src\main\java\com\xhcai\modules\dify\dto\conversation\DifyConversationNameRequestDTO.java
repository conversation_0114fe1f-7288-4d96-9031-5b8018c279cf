package com.xhcai.modules.dify.dto.conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify 会话名称请求 DTO
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Dify 会话名称请求")
public class DifyConversationNameRequestDTO {

    /**
     * 是否自动生成名称
     */
    @JsonProperty("auto_generate")
    @Schema(description = "是否自动生成名称", example = "true")
    private Boolean autoGenerate;
}
