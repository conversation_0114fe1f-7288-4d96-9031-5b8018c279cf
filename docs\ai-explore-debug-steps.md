# AI探索页面调试步骤

## 当前问题
AI探索页面选择智能体标签页时没有调用`/api/agent/ai-explore`接口。

## 已添加的调试功能

### 1. 页面调试信息
在ModelAgentSelector组件的模板中添加了调试信息显示：
- 显示当前activeTab的值
- 显示allCategories的数量

### 2. 控制台日志
添加了详细的控制台日志：
- 组件挂载时的状态信息
- 标签页点击事件的详细日志
- API调用过程的跟踪

### 3. 强制加载机制
在点击AI探索智能体标签页时，会强制调用API加载数据。

## 测试步骤

### 步骤1：启动服务
```bash
# 启动后端服务
cd admin-api
mvn spring-boot:run

# 启动前端服务
cd web
npm run dev
```

### 步骤2：访问页面
1. 打开浏览器访问 `http://localhost:5173/ai`
2. 打开开发者工具（F12）
3. 切换到Console面板

### 步骤3：观察初始状态
查看页面上的调试信息框（黄色背景），应该显示：
- activeTab的当前值
- allCategories的数量

查看Console日志，应该看到：
```
=== ModelAgentSelector mounted ===
activeTab初始值: ai-explore
allCategories数量: 1
allCategories详情: [...]
dynamicAgents数量: 0
agentsLoading状态: false
```

### 步骤4：测试标签页点击
1. 点击"AI探索智能体"标签页
2. 观察Console日志，应该看到：
```
=== 标签页点击 ===
点击的标签页ID: ai-explore
当前activeTab: ai-explore
设置后的activeTab: ai-explore
点击了AI探索智能体标签页，强制加载数据
loadAiExploreAgents 开始执行
开始调用 /api/agent/ai-explore 接口
```

3. 检查Network面板，应该看到对`/api/agent/ai-explore`的请求

### 步骤5：验证API响应
如果API调用成功，应该看到：
```
API响应: {success: true, data: [...]}
AI探索智能体加载成功，数量: X
loadAiExploreAgents 执行完成
```

## 可能的问题和解决方案

### 问题1：页面上没有显示调试信息
**可能原因**：ModelAgentSelector组件没有被渲染
**解决方案**：
1. 确保页面没有消息（currentMessages.length === 0）
2. 检查WelcomePage组件是否正确渲染

### 问题2：activeTab初始值不是'ai-explore'
**可能原因**：有其他地方在重置activeTab的值
**解决方案**：
1. 检查是否有其他组件在修改activeTab
2. 查看是否有全局状态管理在影响

### 问题3：点击标签页没有日志输出
**可能原因**：点击事件没有绑定到正确的元素
**解决方案**：
1. 检查模板中的@click绑定
2. 确保handleTabClick函数被正确调用

### 问题4：API调用失败
**可能原因**：后端服务问题或网络问题
**解决方案**：
1. 检查后端服务是否正常启动
2. 直接访问API接口测试：`GET http://localhost:8080/api/agent/ai-explore`
3. 检查浏览器Network面板的错误信息

### 问题5：allCategories为空
**可能原因**：dynamicAgentCategory没有正确创建
**解决方案**：
1. 检查modelStore.ts中的dynamicAgentCategory定义
2. 确保allCategories计算属性正确执行

## 清理调试代码
测试完成后，记得移除调试代码：
1. 删除模板中的调试信息框
2. 删除多余的console.log语句
3. 保留核心功能代码

## 预期的最终结果
修复完成后，应该实现：
1. 页面加载时默认选中AI探索智能体标签页
2. 自动调用`/api/agent/ai-explore`接口
3. 正确显示智能体列表
4. 用户可以正常选择智能体进行对话

## 如果问题仍然存在
请提供以下信息：
1. 浏览器Console的完整日志
2. Network面板的请求详情
3. 页面上调试信息框显示的内容
4. 后端服务的日志信息
