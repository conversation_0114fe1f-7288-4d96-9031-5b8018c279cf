package com.xhcai.modules.system.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.modules.system.entity.SysDept;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.mapper.SysDeptMapper;
import com.xhcai.modules.system.mapper.SysUserMapper;
import com.xhcai.modules.system.service.ISystemQueryService;

/**
 * 系统查询服务实现类
 * 用于处理跨模块的关联查询，避免循环依赖
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class SystemQueryServiceImpl implements ISystemQueryService {

    private static final Logger log = LoggerFactory.getLogger(SystemQueryServiceImpl.class);

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Override
    public String getDeptNameById(String deptId) {
        if (!StringUtils.hasText(deptId)) {
            return "未分配部门";
        }

        if ("0".equals(deptId)) {
            return "顶级部门";
        }

        try {
            SysDept dept = deptMapper.selectById(deptId);
            return dept != null ? dept.getDeptName() : "未分配部门";
        } catch (Exception e) {
            log.warn("查询部门名称失败: deptId={}, error={}", deptId, e.getMessage());
            return "未分配部门";
        }
    }

    @Override
    public String getUserNameById(String userId) {
        if (!StringUtils.hasText(userId)) {
            return null;
        }

        try {
            SysUser user = userMapper.selectById(userId);
            if (user != null) {
                return StringUtils.hasText(user.getNickname()) ? user.getNickname() : user.getUsername();
            }
            return null;
        } catch (Exception e) {
            log.warn("查询用户姓名失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    @Override
    public Integer countUsersByDeptId(String deptId) {
        if (!StringUtils.hasText(deptId)) {
            return 0;
        }

        try {
            return deptMapper.countUsersByDeptId(deptId);
        } catch (Exception e) {
            log.warn("统计部门用户数量失败: deptId={}, error={}", deptId, e.getMessage());
            return 0;
        }
    }
}
