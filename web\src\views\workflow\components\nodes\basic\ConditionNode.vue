<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="condition-node-content">
        <div class="condition-display" v-if="data.config?.condition">
          <code>{{ data.config.condition }}</code>
        </div>
        <p class="node-description">{{ nodeConfig.description }}</p>
        <div class="condition-config" v-if="data.config">
          <div class="config-row" v-if="data.config.operator">
            <span class="config-label">操作符:</span>
            <span class="config-value">{{ data.config.operator }}</span>
          </div>
          <div class="config-row" v-if="data.config.value !== undefined">
            <span class="config-label">比较值:</span>
            <span class="config-value">{{ data.config.value }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from '../BaseNode.vue'
import { getNodeByType } from '../../../config/nodeLibrary'

// Props
interface ConditionNodeData {
  label?: string
  description?: string
  config?: {
    condition?: string
    operator?: string
    value?: any
  }
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: ConditionNodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 获取节点配置
const nodeConfig = computed(() => {
  return getNodeByType('condition') || {
    description: '根据条件进行分支判断'
  }
})
</script>

<style scoped>
.condition-node-content {
  text-align: center;
}

.condition-display {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
}

.condition-display code {
  color: white;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  word-break: break-all;
}

.node-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.condition-config {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: left;
}

.config-row {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.config-label {
  font-weight: 500;
}

.config-value {
  font-family: monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}
</style>
