# 系统核心模块配置
system:
  # 系统基础配置
  basic:
    # 系统名称
    name: ${SYSTEM_NAME:XHC AI Plus Platform}
    # 系统版本
    version: ${SYSTEM_VERSION:1.0.0}
    # 系统描述
    description: ${SYSTEM_DESCRIPTION:AI智能体服务平台}
    # 系统作者
    author: ${SYSTEM_AUTHOR:xhcai}
    # 系统官网
    website: ${SYSTEM_WEBSITE:https://github.com/xhcai/xhcai-plus}

  # 用户管理配置
  user:
    # 默认密码
    default-password: ${SYSTEM_USER_DEFAULT_PASSWORD:123456}
    # 密码加密强度
    password-strength: ${SYSTEM_USER_PASSWORD_STRENGTH:10}
    # 用户锁定配置
    lock:
      # 是否启用用户锁定
      enabled: ${SYSTEM_USER_LOCK_ENABLED:true}
      # 最大失败次数
      max-attempts: ${SYSTEM_USER_LOCK_MAX_ATTEMPTS:5}
      # 锁定时间（分钟）
      lock-duration: ${SYSTEM_USER_LOCK_DURATION:30}
    # 用户会话配置
    session:
      # 最大并发会话数
      max-sessions: ${SYSTEM_USER_MAX_SESSIONS:1}
      # 会话超时时间（分钟）
      timeout: ${SYSTEM_USER_SESSION_TIMEOUT:30}

  # 角色权限配置
  role:
    # 默认角色
    default-role: ${SYSTEM_ROLE_DEFAULT:USER}
    # 超级管理员角色
    super-admin-role: ${SYSTEM_ROLE_SUPER_ADMIN:SUPER_ADMIN}
    # 租户管理员角色
    tenant-admin-role: ${SYSTEM_ROLE_TENANT_ADMIN:TENANT_ADMIN}

  # 权限配置
  permission:
    # 权限缓存时间（秒）
    cache-ttl: ${SYSTEM_PERMISSION_CACHE_TTL:3600}
    # 权限检查模式：strict(严格) / loose(宽松)
    check-mode: ${SYSTEM_PERMISSION_CHECK_MODE:strict}

  # 部门配置
  dept:
    # 根部门名称
    root-dept-name: ${SYSTEM_DEPT_ROOT_NAME:根部门}
    # 最大层级深度
    max-depth: ${SYSTEM_DEPT_MAX_DEPTH:10}

  # 租户配置
  tenant:
    # 是否启用多租户
    enabled: ${SYSTEM_TENANT_ENABLED:true}
    # 默认租户名称
    default-tenant-name: ${SYSTEM_TENANT_DEFAULT_NAME:默认租户}
    # 租户隔离模式：schema(模式隔离) / table(表隔离)
    isolation-mode: ${SYSTEM_TENANT_ISOLATION_MODE:table}
    # 租户数据权限
    data-scope:
      # 是否启用数据权限
      enabled: ${SYSTEM_TENANT_DATA_SCOPE_ENABLED:true}
      # 默认数据权限范围
      default-scope: ${SYSTEM_TENANT_DATA_SCOPE_DEFAULT:TENANT}

  # 字典配置
  dict:
    # 字典缓存时间（秒）
    cache-ttl: ${SYSTEM_DICT_CACHE_TTL:7200}
    # 是否启用字典缓存
    cache-enabled: ${SYSTEM_DICT_CACHE_ENABLED:true}

  # 配置管理
  config:
    # 配置缓存时间（秒）
    cache-ttl: ${SYSTEM_CONFIG_CACHE_TTL:1800}
    # 是否启用配置缓存
    cache-enabled: ${SYSTEM_CONFIG_CACHE_ENABLED:true}

  # 审计日志配置
  audit:
    # 是否启用审计日志
    enabled: ${SYSTEM_AUDIT_ENABLED:true}
    # 日志保留天数
    retention-days: ${SYSTEM_AUDIT_RETENTION_DAYS:90}
    # 异步处理
    async: ${SYSTEM_AUDIT_ASYNC:true}

  # 安全配置
  security:
    # JWT配置
    jwt:
      # 密钥
      secret: ${JWT_SECRET:xhcai-plus-jwt-secret-key-2025-very-long-secret-key-for-security}
      # 访问令牌过期时间（秒）
      access-token-expiration: ${JWT_EXPIRATION:86400}
      # 刷新令牌过期时间（秒）
      refresh-token-expiration: ${JWT_REFRESH_EXPIRATION:604800}
      # 发行者
      issuer: ${JWT_ISSUER:xhcai-plus}
    
    # 验证码配置
    captcha:
      # 是否启用验证码
      enabled: ${SYSTEM_CAPTCHA_ENABLED:false}
      # 验证码类型：image(图片) / sms(短信)
      type: ${SYSTEM_CAPTCHA_TYPE:image}
      # 验证码长度
      length: ${SYSTEM_CAPTCHA_LENGTH:4}
      # 验证码过期时间（秒）
      expiration: ${SYSTEM_CAPTCHA_EXPIRATION:300}

  # 初始化配置
  init:
    # 是否启用数据初始化
    enabled: ${SYSTEM_INIT_ENABLED:true}
    # 初始化数据文件路径
    data-path: ${SYSTEM_INIT_DATA_PATH:classpath:init-data/}
    # 是否强制重新初始化
    force-reinit: ${SYSTEM_INIT_FORCE_REINIT:false}

# 日志配置
logging:
  level:
    com.xhcai.modules.system: ${SYSTEM_LOG_LEVEL:DEBUG}
    org.springframework.security: ${SYSTEM_SECURITY_LOG_LEVEL:DEBUG}

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: ${SYSTEM_MANAGEMENT_ENDPOINTS:health,info,metrics,system}
  endpoint:
    system:
      enabled: ${SYSTEM_ENDPOINT_ENABLED:true}
  metrics:
    tags:
      module: system
