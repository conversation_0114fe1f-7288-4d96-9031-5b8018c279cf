package com.xhcai.modules.dify.exception;

/**
 * Dify模块自定义异常
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyException extends RuntimeException {

    private int code;
    private String message;

    public DifyException(String message) {
        super(message);
        this.code = 500;
        this.message = message;
    }

    public DifyException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public DifyException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
        this.message = message;
    }

    public DifyException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
