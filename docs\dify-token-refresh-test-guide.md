# Dify令牌刷新修复测试指南

## 测试目标

验证Dify API调用时的令牌自动刷新功能是否正常工作。

## 测试场景

### 场景1：正常API调用（令牌有效）
- **请求**：`GET /api/dify/agents/list/{platformId}`
- **预期结果**：正常返回数据，无令牌刷新操作

### 场景2：令牌过期自动刷新
- **请求**：`GET /api/dify/agents/list/{platformId}`（使用过期令牌）
- **预期结果**：
  1. 收到401错误
  2. 自动刷新令牌
  3. 使用新令牌重试API调用
  4. 返回正确数据

## 测试步骤

### 1. 准备测试环境

确保以下配置正确：
- Dify平台连接配置
- 用户已登录并有有效的refresh_token
- 数据库中有对应的平台配置记录

### 2. 手动测试步骤

#### 步骤1：正常调用测试
```bash
curl -X GET "http://localhost:8000/api/dify/agents/list/{platformId}?page=1&limit=30" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -H "Content-Type: application/json"
```

#### 步骤2：模拟令牌过期
可以通过以下方式模拟令牌过期：
1. 在数据库中将access_token设置为过期的值
2. 或者等待令牌自然过期

#### 步骤3：观察日志
关注以下关键日志：
```
- INFO: 平台{platformId}令牌已过期，开始刷新令牌
- INFO: 用户{userId}在平台{platformId}的令牌刷新成功
- INFO: 平台{platformId}令牌刷新成功，使用新令牌重试API调用
```

### 3. 验证修复效果

#### 修复前的错误日志（不应再出现）：
```
- WARN: 未获取到用户信息，可能未登录或在响应式上下文中
- ERROR: 平台{platformId}令牌刷新过程失败: 您当前没有登录
```

#### 修复后的正常日志：
```
- DEBUG: 从Reactor Context获取到用户ID: {userId}
- INFO: 使用用户ID {userId}刷新平台{platformId}的令牌
- INFO: 平台{platformId}令牌刷新成功，使用新令牌重试API调用
- DEBUG: 平台{platformId}API调用成功
```

## 测试检查点

### ✅ 成功标准
1. **令牌刷新成功**：日志显示令牌刷新成功
2. **API重试成功**：使用新令牌成功调用API并返回数据
3. **无用户信息获取错误**：不再出现"您当前没有登录"错误
4. **响应正常**：API返回正确的数据结构

### ❌ 失败标准
1. 仍然出现"未获取到用户信息"警告
2. 出现"您当前没有登录"错误
3. 令牌刷新成功但API重试失败
4. 返回空数据或错误响应

## 故障排查

### 如果仍然出现问题：

1. **检查用户认证状态**
   ```bash
   # 检查JWT令牌是否有效
   # 检查用户是否正确登录
   ```

2. **检查平台配置**
   ```sql
   SELECT * FROM third_platform WHERE id = '{platformId}';
   SELECT * FROM third_platform_account WHERE platform_id = '{platformId}' AND user_id = '{userId}';
   ```

3. **检查日志级别**
   确保日志级别设置为DEBUG以查看详细信息：
   ```yaml
   logging:
     level:
       com.xhcai.modules.dify: DEBUG
   ```

4. **检查响应式上下文传递**
   确认Controller层正确传递了用户上下文：
   ```java
   .contextWrite(context -> context.put("userId", userId))
   ```

## 性能考虑

- 令牌刷新操作会增加API调用延迟
- 建议监控令牌刷新频率，避免频繁刷新
- 考虑实现令牌预刷新机制（在令牌即将过期时主动刷新）

## 安全考虑

- 确保refresh_token安全存储
- 监控异常的令牌刷新行为
- 实现令牌刷新失败的告警机制
