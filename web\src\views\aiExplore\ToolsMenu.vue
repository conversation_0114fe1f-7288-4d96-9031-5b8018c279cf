<template>
  <div class="tools-menu-container flex items-center gap-2">
    <!-- 工具菜单（移动端显示全部，PC端显示其余工具） -->
    <div class="tools-menu relative">
      <!-- 触发按钮 -->
      <button
        @click="toggleMenu"
        class="tools-trigger flex items-center gap-1 p-2 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors duration-200"
        :class="{ 'text-purple-600 bg-purple-50': isOpen }"
        :title="isDesktop ? '更多工具' : '工具箱'"
      >
        <el-icon class="text-base flex-shrink-0">
          <Tools />
        </el-icon>
        <!-- PC端显示"更多"文字 -->
        <span v-if="isDesktop" class="hidden lg:inline text-sm font-medium whitespace-nowrap">更多</span>
      </button>

      <!-- 上拉菜单 -->
      <div
        v-if="isOpen"
        class="tools-menu-panel absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
        :class="isDesktop ? 'min-w-40' : 'min-w-48'"
      >
        <div class="py-2">
          <!-- 移动端：显示所有工具 -->
          <template v-if="!isDesktop">
            <!-- 警情分析 -->
            <button
              @click="selectTool('police-analysis')"
              class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
            >
              <el-icon class="text-red-500"><DataAnalysis /></el-icon>
              警情分析
            </button>

            <!-- 公文分析 -->
            <button
              @click="selectTool('document-analysis')"
              class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
            >
              <el-icon class="text-blue-500"><Document /></el-icon>
              公文分析
            </button>

            <!-- 案件研判 -->
            <button
              @click="selectTool('case-analysis')"
              class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
            >
              <el-icon class="text-purple-500"><Cpu /></el-icon>
              案件研判
            </button>
          </template>

          <!-- 法律咨询 -->
          <button
            @click="selectTool('legal-consultation')"
            class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
          >
            <el-icon class="text-green-500"><ChatDotRound /></el-icon>
            法律咨询
          </button>

          <!-- 报告生成 -->
          <button
            @click="selectTool('report-generation')"
            class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
          >
            <el-icon class="text-orange-500"><DocumentCopy /></el-icon>
            报告生成
          </button>

          <!-- 证据分析 -->
          <button
            @click="selectTool('evidence-analysis')"
            class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
          >
            <el-icon class="text-cyan-500"><View /></el-icon>
            证据分析
          </button>
        </div>
      </div>
    </div>

    <!-- PC端：常用工具直接展示 -->
    <div class="hidden md:flex items-center gap-2">
      <!-- 警情分析 -->
      <button
        @click="selectTool('police-analysis')"
        class="tool-button flex items-center gap-1 p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
        title="警情分析"
      >
        <el-icon class="text-base flex-shrink-0">
          <DataAnalysis />
        </el-icon>
        <span class="text-sm font-medium whitespace-nowrap">警情分析</span>
      </button>

      <!-- 公文分析 -->
      <button
        @click="selectTool('document-analysis')"
        class="tool-button flex items-center gap-1 p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
        title="公文分析"
      >
        <el-icon class="text-base flex-shrink-0">
          <Document />
        </el-icon>
        <span class="text-sm font-medium whitespace-nowrap">公文分析</span>
      </button>

      <!-- 案件研判 -->
      <button
        @click="selectTool('case-analysis')"
        class="tool-button flex items-center gap-1 p-2 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors duration-200"
        title="案件研判"
      >
        <el-icon class="text-base flex-shrink-0">
          <Cpu />
        </el-icon>
        <span class="text-sm font-medium whitespace-nowrap">案件研判</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {
  Tools,
  Cpu,
  DocumentCopy,
  View,
  Document,
  DataAnalysis,
  ChatDotRound
} from '@element-plus/icons-vue'
import { useChatInputStore, type ToolType } from '@/stores/chatInputStore'

// 使用全局聊天输入状态
const chatInputStore = useChatInputStore()

// 响应式数据
const isOpen = ref(false)
const isDesktop = ref(window.innerWidth >= 768)

// 方法
const toggleMenu = () => {
  isOpen.value = !isOpen.value
}

const closeMenu = () => {
  isOpen.value = false
}

const selectTool = (tool: ToolType) => {
  chatInputStore.handleToolSelect(tool)
  closeMenu()
}



// 监听窗口大小变化
const handleResize = () => {
  isDesktop.value = window.innerWidth >= 768
  // 如果切换到移动端，关闭菜单
  if (!isDesktop.value) {
    closeMenu()
  }
}

// 点击外部关闭
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.tools-menu-container')) {
    closeMenu()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 工具按钮基础样式 */
.tool-button,
.tools-trigger {
  min-width: auto;
  white-space: nowrap;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid transparent;
  transition: all 0.2s ease-out;
  height: 2.5rem; /* 统一按钮高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-button:hover,
.tools-trigger:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tool-button:active,
.tools-trigger:active {
  transform: translateY(0);
}

/* 确保图标和文字垂直居中 */
.tool-button .el-icon,
.tools-trigger .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 文字样式统一 */
.tool-button span,
.tools-trigger span {
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 工具菜单面板动画 */
.tools-menu-panel {
  animation: slideUp 0.2s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 菜单按钮圆角 */
.tools-menu-panel button:first-child {
  border-radius: 0.5rem 0.5rem 0 0;
}

.tools-menu-panel button:last-child {
  border-radius: 0 0 0.5rem 0.5rem;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .tool-button span,
  .tools-trigger span {
    display: none;
  }

  .tool-button,
  .tools-trigger {
    padding: 0.5rem;
    min-width: 2.5rem;
    width: 2.5rem;
  }
}

/* 中等屏幕优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .tools-menu-container {
    gap: 0.25rem;
  }

  .tool-button,
  .tools-trigger {
    padding: 0.375rem;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .tools-menu-container {
    gap: 0.5rem;
  }
}

/* 工具按钮悬浮效果增强 */
.tool-button {
  position: relative;
  overflow: hidden;
}

.tool-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.tool-button:hover::before {
  left: 100%;
}
</style>
