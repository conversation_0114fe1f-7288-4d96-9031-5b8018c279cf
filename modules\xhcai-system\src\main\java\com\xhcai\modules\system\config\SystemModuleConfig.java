package com.xhcai.modules.system.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 系统模块配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class SystemModuleConfig {

    private static final Logger log = LoggerFactory.getLogger(SystemModuleConfig.class);

    /**
     * 系统模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemApi() {
        return GroupedOpenApi.builder()
                .group("system")
                .displayName("系统管理（所有）")
                .pathsToMatch("/api/system/**")
                .build();
    }

    /**
     * 系统用户模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemUserApi() {
        return GroupedOpenApi.builder()
                .group("system-user")
                .displayName("系统管理（用户）")
                .pathsToMatch("/api/system/user/**")
                .build();
    }

    /**
     * 部门模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemDeptApi() {
        return GroupedOpenApi.builder()
                .group("system-dept")
                .displayName("系统管理（部门）")
                .pathsToMatch("/api/system/dept/**")
                .build();
    }

    /**
     * 系统菜单模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemMenuApi() {
        return GroupedOpenApi.builder()
                .group("system-menu")
                .displayName("系统管理（菜单）")
                .pathsToMatch("/api/system/menu/**")
                .build();
    }

    /**
     * 系统角色模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemRoleApi() {
        return GroupedOpenApi.builder()
                .group("system-role")
                .displayName("系统管理（角色）")
                .pathsToMatch("/api/system/role/**")
                .build();
    }

    /**
     * 系统租户模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemTenantApi() {
        return GroupedOpenApi.builder()
                .group("system-tenant")
                .displayName("系统管理（租户）")
                .pathsToMatch("/api/system/tenant/**")
                .build();
    }

    /**
     * 系统租户配置模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemTenantConfigApi() {
        return GroupedOpenApi.builder()
                .group("system-config")
                .displayName("系统管理（租户配置）")
                .pathsToMatch("/api/system/tenant-config/**")
                .build();
    }

    /**
     * 用户认证API分组 这是核心认证功能，不属于特定模块
     */
    @Bean
    public GroupedOpenApi authApi() {
        return GroupedOpenApi.builder()
                .group("system-auth")
                .displayName("系统管理（用户认证）")
                .pathsToMatch("/api/auth/**")
                .build();
    }

    /**
     * 权限模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemPermissionApi() {
        return GroupedOpenApi.builder()
                .group("system-permission")
                .displayName("系统管理（权限）")
                .pathsToMatch("/api/system/permission/**")
                .build();
    }

    /**
     * 系统配置模块API分组配置
     */
    @Bean
    public GroupedOpenApi systemConfigApi() {
        return GroupedOpenApi.builder()
                .group("system-config")
                .displayName("系统管理（配置）")
                .pathsToMatch("/api/system/config/**")
                .build();
    }

}
