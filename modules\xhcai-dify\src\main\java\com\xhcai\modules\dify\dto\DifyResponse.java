package com.xhcai.modules.dify.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Dify API通用响应结构
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyResponse<T> {

    /**
     * 响应状态码
     */
    private int code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 请求ID
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 是否成功
     */
    private boolean success;

    public DifyResponse() {
    }

    public DifyResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = code == 200;
    }

    public static <T> DifyResponse<T> success(T data) {
        return new DifyResponse<>(200, "Success", data);
    }

    public static <T> DifyResponse<T> error(int code, String message) {
        return new DifyResponse<>(code, message, null);
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
        this.success = code == 200;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
