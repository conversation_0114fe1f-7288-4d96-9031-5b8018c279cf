import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { fileURLToPath, URL } from 'node:url'

// Legacy浏览器构建配置
export default defineConfig({
  plugins: [
    vue(),
    // 专门为旧版浏览器优化的legacy插件配置
    legacy({
      targets: [
        'ie >= 11',
        'chrome >= 60',
        'firefox >= 60',
        'safari >= 10.1',
        'edge >= 15'
      ],
      additionalLegacyPolyfills: [
        'regenerator-runtime/runtime',
        'core-js/stable',
        'whatwg-fetch'
      ],
      renderLegacyChunks: true,
      polyfills: [
        'es.symbol',
        'es.symbol.description',
        'es.symbol.async-iterator',
        'es.symbol.has-instance',
        'es.symbol.is-concat-spreadable',
        'es.symbol.iterator',
        'es.symbol.match',
        'es.symbol.match-all',
        'es.symbol.replace',
        'es.symbol.search',
        'es.symbol.species',
        'es.symbol.split',
        'es.symbol.to-primitive',
        'es.symbol.to-string-tag',
        'es.symbol.unscopables',
        'es.array.concat',
        'es.array.copy-within',
        'es.array.every',
        'es.array.fill',
        'es.array.filter',
        'es.array.find',
        'es.array.find-index',
        'es.array.flat',
        'es.array.flat-map',
        'es.array.for-each',
        'es.array.from',
        'es.array.includes',
        'es.array.index-of',
        'es.array.is-array',
        'es.array.iterator',
        'es.array.join',
        'es.array.last-index-of',
        'es.array.map',
        'es.array.of',
        'es.array.reduce',
        'es.array.reduce-right',
        'es.array.reverse',
        'es.array.slice',
        'es.array.some',
        'es.array.sort',
        'es.array.species',
        'es.array.splice',
        'es.array.unscopables.flat',
        'es.array.unscopables.flat-map',
        'es.array-buffer.constructor',
        'es.array-buffer.is-view',
        'es.array-buffer.slice',
        'es.date.now',
        'es.date.to-iso-string',
        'es.date.to-json',
        'es.date.to-primitive',
        'es.date.to-string',
        'es.function.bind',
        'es.function.name',
        'es.global-this',
        'es.json.stringify',
        'es.json.to-string-tag',
        'es.map',
        'es.math.acosh',
        'es.math.asinh',
        'es.math.atanh',
        'es.math.cbrt',
        'es.math.clz32',
        'es.math.cosh',
        'es.math.expm1',
        'es.math.fround',
        'es.math.hypot',
        'es.math.imul',
        'es.math.log1p',
        'es.math.log10',
        'es.math.log2',
        'es.math.sign',
        'es.math.sinh',
        'es.math.tanh',
        'es.math.to-string-tag',
        'es.math.trunc',
        'es.number.constructor',
        'es.number.epsilon',
        'es.number.is-finite',
        'es.number.is-integer',
        'es.number.is-nan',
        'es.number.is-safe-integer',
        'es.number.max-safe-integer',
        'es.number.min-safe-integer',
        'es.number.parse-float',
        'es.number.parse-int',
        'es.number.to-fixed',
        'es.number.to-precision',
        'es.object.assign',
        'es.object.create',
        'es.object.define-getter',
        'es.object.define-properties',
        'es.object.define-property',
        'es.object.define-setter',
        'es.object.entries',
        'es.object.freeze',
        'es.object.from-entries',
        'es.object.get-own-property-descriptor',
        'es.object.get-own-property-descriptors',
        'es.object.get-own-property-names',
        'es.object.get-prototype-of',
        'es.object.is',
        'es.object.is-extensible',
        'es.object.is-frozen',
        'es.object.is-sealed',
        'es.object.keys',
        'es.object.lookup-getter',
        'es.object.lookup-setter',
        'es.object.prevent-extensions',
        'es.object.seal',
        'es.object.set-prototype-of',
        'es.object.to-string',
        'es.object.values',
        'es.promise',
        'es.promise.all-settled',
        'es.promise.any',
        'es.promise.finally',
        'es.reflect.apply',
        'es.reflect.construct',
        'es.reflect.define-property',
        'es.reflect.delete-property',
        'es.reflect.get',
        'es.reflect.get-own-property-descriptor',
        'es.reflect.get-prototype-of',
        'es.reflect.has',
        'es.reflect.is-extensible',
        'es.reflect.own-keys',
        'es.reflect.prevent-extensions',
        'es.reflect.set',
        'es.reflect.set-prototype-of',
        'es.reflect.to-string-tag',
        'es.regexp.constructor',
        'es.regexp.exec',
        'es.regexp.flags',
        'es.regexp.sticky',
        'es.regexp.test',
        'es.regexp.to-string',
        'es.set',
        'es.string.code-point-at',
        'es.string.ends-with',
        'es.string.from-code-point',
        'es.string.includes',
        'es.string.iterator',
        'es.string.match',
        'es.string.match-all',
        'es.string.pad-end',
        'es.string.pad-start',
        'es.string.raw',
        'es.string.repeat',
        'es.string.replace',
        'es.string.replace-all',
        'es.string.search',
        'es.string.split',
        'es.string.starts-with',
        'es.string.substr',
        'es.string.trim',
        'es.string.trim-end',
        'es.string.trim-start',
        'es.typed-array.float32-array',
        'es.typed-array.float64-array',
        'es.typed-array.int8-array',
        'es.typed-array.int16-array',
        'es.typed-array.int32-array',
        'es.typed-array.uint8-array',
        'es.typed-array.uint8-clamped-array',
        'es.typed-array.uint16-array',
        'es.typed-array.uint32-array',
        'es.typed-array.copy-within',
        'es.typed-array.every',
        'es.typed-array.fill',
        'es.typed-array.filter',
        'es.typed-array.find',
        'es.typed-array.find-index',
        'es.typed-array.for-each',
        'es.typed-array.from',
        'es.typed-array.includes',
        'es.typed-array.index-of',
        'es.typed-array.iterator',
        'es.typed-array.join',
        'es.typed-array.last-index-of',
        'es.typed-array.map',
        'es.typed-array.of',
        'es.typed-array.reduce',
        'es.typed-array.reduce-right',
        'es.typed-array.reverse',
        'es.typed-array.set',
        'es.typed-array.slice',
        'es.typed-array.some',
        'es.typed-array.sort',
        'es.typed-array.subarray',
        'es.typed-array.to-locale-string',
        'es.typed-array.to-string',
        'es.weak-map',
        'es.weak-set',
        'web.dom-collections.for-each',
        'web.dom-collections.iterator',
        'web.immediate',
        'web.queue-microtask',
        'web.url',
        'web.url.to-json',
        'web.url-search-params'
      ],
      modernPolyfills: [
        'es.global-this'
      ]
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 4000,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    // 针对旧版浏览器的构建目标
    target: ['es5', 'chrome60', 'firefox60', 'safari10.1', 'edge15'],
    cssTarget: ['chrome60', 'firefox60', 'safari10.1', 'edge15'],
    rollupOptions: {
      output: {
        manualChunks: {
          // 基础库
          'vendor-core': ['vue', 'vue-router', 'pinia'],
          // UI组件
          'vendor-ui': ['@tiptap/vue-3', '@tiptap/starter-kit'],
          // Polyfills
          'polyfills': ['core-js/stable', 'regenerator-runtime/runtime']
        },
        // 为旧版浏览器优化的文件命名
        assetFileNames: (assetInfo) => {
          if (!assetInfo.name) {
            return 'static/assets/[name]-legacy-[hash][extname]'
          }
          const info = assetInfo.name.split('.')
          let extType = info[info.length - 1]
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'media'
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'img'
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'fonts'
          }
          return `static/${extType}/[name]-legacy-[hash][extname]`
        },
        chunkFileNames: 'static/js/[name]-legacy-[hash].js',
        entryFileNames: 'static/js/[name]-legacy-[hash].js'
      }
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        // 为旧版浏览器保留更多兼容性
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log'],
        // 不要过度优化，保持兼容性
        passes: 1
      },
      mangle: {
        // 保留一些关键的函数名以便调试
        keep_fnames: /^(Vue|Router|Pinia)/
      },
      format: {
        // 保持一定的可读性
        comments: false
      }
    }
  }
})
