/**
 * 组件状态枚举
 */
export enum ComponentStatus {
  INSTALLING = 'INSTALLING',
  INSTALLED = 'INSTALLED',
  RUNNING = 'RUNNING',
  STOPPED = 'STOPPED',
  ERROR = 'ERROR',
  UNINSTALLED = 'UNINSTALLED',
}

/**
 * 组件状态描述映射
 */
export const ComponentStatusLabels: Record<ComponentStatus, string> = {
  [ComponentStatus.INSTALLING]: '安装中',
  [ComponentStatus.INSTALLED]: '已安装',
  [ComponentStatus.RUNNING]: '运行中',
  [ComponentStatus.STOPPED]: '已停止',
  [ComponentStatus.ERROR]: '错误',
  [ComponentStatus.UNINSTALLED]: '未安装',
};

/**
 * 组件状态颜色映射
 */
export const ComponentStatusColors: Record<ComponentStatus, string> = {
  [ComponentStatus.INSTALLING]: 'text-warning-600 bg-warning-50',
  [ComponentStatus.INSTALLED]: 'text-secondary-600 bg-secondary-50',
  [ComponentStatus.RUNNING]: 'text-success-600 bg-success-50',
  [ComponentStatus.STOPPED]: 'text-secondary-600 bg-secondary-100',
  [ComponentStatus.ERROR]: 'text-error-600 bg-error-50',
  [ComponentStatus.UNINSTALLED]: 'text-secondary-400 bg-secondary-50',
};

/**
 * 组件类型
 */
export type ComponentType = 
  | 'filebeat'
  | 'heartbeat'
  | 'metricbeat'
  | 'packetbeat'
  | 'winlogbeat'
  | 'auditbeat'
  | 'logstash'
  | 'elasticsearch'
  | 'kafka';

/**
 * 组件类型描述映射
 */
export const ComponentTypeLabels: Record<ComponentType, string> = {
  filebeat: 'Filebeat',
  heartbeat: 'Heartbeat',
  metricbeat: 'Metricbeat',
  packetbeat: 'Packetbeat',
  winlogbeat: 'Winlogbeat',
  auditbeat: 'Auditbeat',
  logstash: 'Logstash',
  elasticsearch: 'Elasticsearch',
  kafka: 'Kafka',
};

/**
 * Elastic组件接口
 */
export interface ElasticComponent {
  id: string;
  name: string;
  type: ComponentType;
  version: string;
  status: ComponentStatus;
  installPath?: string;
  configPath?: string;
  logPath?: string;
  processId?: number;
  port?: number;
  host: string;
  description?: string;
  packageFileName?: string;
  packageFilePath?: string;
  packageFileSize?: number;
  installTime?: string;
  lastStartTime?: string;
  lastStopTime?: string;
  autoStart: boolean;
  startCommand?: string;
  stopCommand?: string;
  restartCommand?: string;
  statusCommand?: string;
  createTime: string;
  updateTime: string;
  createBy?: string;
  updateBy?: string;
  remark?: string;
}

/**
 * 组件安装请求
 */
export interface ComponentInstallRequest {
  componentId: string;
  config?: Record<string, any>;
  autoStart?: boolean;
  customInstallPath?: string;
  port?: number;
  host?: string;
}

/**
 * 组件上传请求
 */
export interface ComponentUploadRequest {
  file: File;
  componentType: ComponentType;
  version: string;
  description?: string;
}

/**
 * 组件统计信息
 */
export interface ComponentStatistics {
  statusCounts: Array<{
    status: string;
    count: number;
  }>;
  typeCounts: Array<{
    type: string;
    count: number;
  }>;
  totalCount: number;
  runningCount: number;
  installedCount: number;
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult {
  results: Record<string, boolean>;
  totalCount: number;
  successCount: number;
}
