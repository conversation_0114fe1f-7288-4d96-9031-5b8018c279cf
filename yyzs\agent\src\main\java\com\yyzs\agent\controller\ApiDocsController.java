package com.yyzs.agent.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API文档管理控制器
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/docs")
@RequiredArgsConstructor
@Tag(name = "API文档管理", description = "获取可用的API接口文档和示例")
public class ApiDocsController {

    @Operation(summary = "获取API接口列表", description = "获取所有可用的API接口信息")
    @GetMapping("/endpoints")
    public ResponseEntity<Map<String, Object>> getApiEndpoints(
            @Parameter(description = "接口分类") @RequestParam(required = false) String category) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("success", true);
            result.put("data", List.of());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取API接口列表失败", e);
            result.put("success", false);
            result.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "获取API分类列表", description = "获取所有API接口分类")
    @GetMapping("/categories")
    public ResponseEntity<Map<String, Object>> getApiCategories() {
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("success", true);
            result.put("data", List.of());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取API分类列表失败", e);
            result.put("success", false);
            result.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
}
