<template>
  <div class="workflow-editor">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <button
          class="btn btn-icon back-btn"
          @click="goBackToAgents"
          title="返回智能体列表"
        >
          <i class="fas fa-arrow-left"></i>
        </button>
        <div class="agent-info">
          <i class="fas fa-robot"></i>
          <span class="agent-name">{{ agentInfo.name || '智能体编排' }}</span>
          <span class="version">v{{ workflowConfig.version }}</span>
        </div>
      </div>
      
      <div class="toolbar-center">
        <div class="canvas-controls">
          <button 
            class="btn btn-icon" 
            @click="zoomIn" 
            title="放大"
          >
            <i class="fas fa-search-plus"></i>
          </button>
          <span class="zoom-level">{{ Math.round(viewport.zoom * 100) }}%</span>
          <button 
            class="btn btn-icon" 
            @click="zoomOut" 
            title="缩小"
          >
            <i class="fas fa-search-minus"></i>
          </button>
          <button 
            class="btn btn-icon" 
            @click="fitView" 
            title="适应画布"
          >
            <i class="fas fa-expand-arrows-alt"></i>
          </button>
        </div>
      </div>
      
      <div class="toolbar-right">
        <button
          class="btn btn-secondary"
          :class="{ active: panelState.showRunner }"
          @click="toggleRunner"
          :disabled="!hasNodes"
        >
          <i class="fas fa-bug"></i>
          调试
        </button>
        <button
          class="btn btn-secondary"
          @click="clearCanvas"
          :disabled="!hasNodes"
        >
          <i class="fas fa-trash"></i>
          清除
        </button>

        <button
          class="btn btn-secondary"
          @click="showSettings"
        >
          <i class="fas fa-cog"></i>
          设置
        </button>
        <button
          class="btn btn-primary"
          @click="publishWorkflow"
          :disabled="!hasNodes"
        >
          <i class="fas fa-rocket"></i>
          发布
        </button>
      </div>
    </div>



    <!-- 主编辑区域 -->
    <div class="editor-container">
      <!-- 左侧节点库面板 -->
      <div
        class="node-library-panel"
        :class="{ collapsed: !panelState.showNodeLibrary }"
        :style="{ width: panelState.showNodeLibrary ? `${panelState.leftPanelWidth}px` : '50px' }"
      >
        <div class="panel-header">
          <h3 v-if="panelState.showNodeLibrary">节点库</h3>
          <button
            class="btn btn-icon"
            @click="toggleNodeLibrary"
            :title="panelState.showNodeLibrary ? '收缩节点库' : '展开节点库'"
          >
            <i class="fas" :class="panelState.showNodeLibrary ? 'fa-chevron-left' : 'fa-chevron-right'"></i>
          </button>
        </div>

        <div class="panel-content" v-show="panelState.showNodeLibrary">
          <NodeLibrary
            :categories="nodeLibraryCategories"
            @node-drag-start="onNodeDragStart"
            @add-node="addNodeToCanvas"
          />
        </div>

        <!-- 左侧面板拖拽调整器 -->
        <div
          v-if="panelState.showNodeLibrary"
          class="panel-resizer panel-resizer-right"
          @mousedown="startResize('left', $event)"
        ></div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <VueFlow
          ref="vueFlowRef"
          v-model:nodes="nodes"
          v-model:edges="edges"
          v-model:viewport="viewport"
          :default-viewport="{ zoom: 1, x: 0, y: 0 }"
          :min-zoom="0.1"
          :max-zoom="4"
          :snap-to-grid="true"
          :snap-grid="[20, 20]"
          :delete-key-code="['Delete', 'Backspace']"
          @nodes-change="onNodesChange"
          @edges-change="onEdgesChange"
          @connect="onConnect"
          @node-click="onNodeClick"
          @edge-click="onEdgeClick"
          @edge-context-menu="onEdgeContextMenu"
          @pane-click="onPaneClick"
          @drop="onDrop"
          @dragover="onDragOver"
          class="vue-flow"
        >
          <!-- 背景 -->
          <Background 
            pattern-color="#e5e7eb" 
            :gap="20" 
            variant="dots" 
          />
          
          <!-- 小地图 -->
          <MiniMap 
            :node-color="getNodeColor"
            :mask-color="'rgba(255, 255, 255, 0.8)'"
            position="bottom-right"
          />
          
          <!-- 控制面板 -->
          <Controls 
            position="bottom-left"
            :show-zoom="false"
            :show-fit-view="false"
            :show-interactive="false"
          />
          
          <!-- 自定义节点组件 -->
          <template #node-start="nodeProps">
            <StartNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-end="nodeProps">
            <EndNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-condition="nodeProps">
            <ConditionNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-loop="nodeProps">
            <ConditionNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-database="nodeProps">
            <DatabaseNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-ai="nodeProps">
            <AINode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-file="nodeProps">
            <FileNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-render="nodeProps">
            <RenderNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-data="nodeProps">
            <DataNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <template #node-utility="nodeProps">
            <UtilityNode
              v-bind="nodeProps"
              :execution-status="getNodeExecutionStatus(nodeProps.id)"
              :execution-progress="getNodeExecutionProgress(nodeProps.id)"
              :execution-info="getNodeExecutionInfo(nodeProps.id)"
            />
          </template>

          <!-- 自定义连接线组件 -->
          <template #edge-default="edgeProps">
            <AnimatedEdge
              v-bind="edgeProps"
              :is-animated="getEdgeAnimated(edgeProps.id)"
              :is-flowing="getEdgeFlowing(edgeProps.id)"
              :data-packet="getEdgeDataPacket(edgeProps.id)"
            />
          </template>
        </VueFlow>
      </div>

      <!-- 右侧属性面板 -->
      <div
        class="properties-panel"
        :class="{ collapsed: !panelState.showProperties }"
        :style="{ width: panelState.showProperties ? `${panelState.rightPanelWidth}px` : '0' }"
      >
        <!-- 右侧面板拖拽调整器 -->
        <div
          v-if="panelState.showProperties"
          class="panel-resizer panel-resizer-left"
          @mousedown="startResize('right', $event)"
        ></div>

        <div class="panel-content" v-show="panelState.showProperties">
          <NodeProperties
            v-if="selectionState.selectedNode"
            :node="selectionState.selectedNode"
            :nodes="nodes"
            :edges="edges"
            @update="onNodeUpdate"
            @delete="deleteNode"
            @close="onPropertiesClose"
          />

          <EdgeProperties
            v-else-if="selectionState.selectedEdge"
            :edge="selectionState.selectedEdge"
            :nodes="nodes"
            @update="onEdgeUpdate"
            @delete="deleteEdge"
            @insert-node="insertNodeOnEdge"
          />

          <div v-else class="no-selection">
            <i class="fas fa-mouse-pointer"></i>
            <p>选择节点或连接线以配置属性</p>
          </div>
        </div>
      </div>

      <!-- 右侧调试面板 -->
      <div
        v-if="panelState.showRunner"
        class="debug-panel"
        :class="{ collapsed: !panelState.showDebugPanel }"
        :style="{ width: panelState.showDebugPanel ? `${panelState.debugPanelWidth}px` : '50px' }"
      >
        <!-- 调试面板拖拽调整器 -->
        <div
          v-if="panelState.showDebugPanel"
          class="panel-resizer panel-resizer-left"
          @mousedown="startResize('debug', $event)"
        ></div>

        <div class="panel-header">
          <h3 v-if="panelState.showDebugPanel">工作流调试</h3>
          <button
            class="btn btn-icon"
            @click="toggleDebugPanel"
            :title="panelState.showDebugPanel ? '收缩调试面板' : '展开调试面板'"
          >
            <i class="fas" :class="panelState.showDebugPanel ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
          </button>
        </div>

        <div class="panel-content" v-show="panelState.showDebugPanel">
          <!-- 工作流运行器 -->
          <div class="runner-section">
            <WorkflowRunner
              ref="workflowRunnerRef"
              :nodes="nodes"
              :edges="edges"
              :agent-id="agentId"
              :global-variables="workflowConfig.globalVariables"
              :use-remote-execution="false"
              @execution-start="onExecutionStart"
              @execution-complete="onExecutionComplete"
              @execution-error="onExecutionError"
              @execution-pause="onExecutionPause"
              @execution-resume="onExecutionResume"
              @execution-cancel="onExecutionCancel"
              @node-start="onNodeStart"
              @node-complete="onNodeComplete"
              @node-error="onNodeError"
              @progress-update="onProgressUpdate"
              @edge-traverse="onEdgeTraverse"
            />
          </div>

          <!-- 执行日志面板 -->
          <div class="logs-section">
            <ExecutionLogPanel
              :execution-steps="executionState.executionSteps"
              :execution-stats="executionState.executionStats"
              @clear-logs="clearExecutionLogs"
            />
          </div>
        </div>
      </div>


    </div>

    <!-- 设置弹窗 -->
    <SettingsModal 
      v-if="showSettingsModal"
      :workflow-config="workflowConfig"
      @close="showSettingsModal = false"
      @save="onSettingsSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { VueFlow, useVueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { MiniMap } from '@vue-flow/minimap'
import { Controls } from '@vue-flow/controls'
import type { Node, Edge } from '@vue-flow/core'
import { storeToRefs } from 'pinia'

// 导入组件
import NodeLibrary from './components/NodeLibrary.vue'
import NodeProperties from './components/NodeProperties.vue'
import EdgeProperties from './components/EdgeProperties.vue'
import SettingsModal from './components/SettingsModal.vue'
import WorkflowRunner from './components/WorkflowRunner.vue'
import ExecutionLogPanel from './components/ExecutionLogPanel.vue'
import AnimatedEdge from './components/AnimatedEdge.vue'

// 导入自定义节点组件
import StartNode from './components/nodes/StartNode.vue'
import EndNode from './components/nodes/EndNode.vue'
import ConditionNode from './components/nodes/ConditionNode.vue'
import DatabaseNode from './components/nodes/DatabaseNode.vue'
import AINode from './components/nodes/AINode.vue'
import FileNode from './components/nodes/FileNode.vue'
import RenderNode from './components/nodes/RenderNode.vue'
import DataNode from './components/nodes/DataNode.vue'
import UtilityNode from './components/nodes/UtilityNode.vue'

// 导入API和类型
import { getAgentById } from '@/api/agents'
import { NODE_LIBRARY_CONFIG, type NodeCategory, getNodeByType } from './config/nodeLibrary'
import { initializeNodeConfigs, watchNodeConfigChanges } from './composables/useNodeConfig'

// 导入全局状态管理
import { useWorkflowStore } from '@/stores/workflowStore'

// 路由和状态
const route = useRoute()
const router = useRouter()
const agentId = route.params.id as string

// 使用全局状态管理
const workflowStore = useWorkflowStore()
const {
  agentInfo,
  workflowConfig,
  nodes,
  edges,
  viewport,
  selectionState,
  panelState,
  executionState,
  hasNodes
} = storeToRefs(workflowStore)

// Vue Flow实例
const vueFlowRef = ref()
const { fitView: vueFlowFitView, zoomIn: vueFlowZoomIn, zoomOut: vueFlowZoomOut } = useVueFlow()

// 本地状态（不需要全局管理的）
const showSettingsModal = ref(false)

// 工作流运行器引用
const workflowRunnerRef = ref()

// 面板宽度调整状态
const isResizing = ref(false)
const resizeType = ref<'left' | 'right' | 'debug' | null>(null)

const nodeLibraryCategories = ref<NodeCategory[]>(NODE_LIBRARY_CONFIG.categories)

// 方法 - 使用全局状态管理的方法
const {
  toggleNodeLibrary,
  toggleProperties,
  toggleRunner,
  toggleDebugPanel,
  clearSelection,
  selectNode,
  selectEdge,
  addNode,
  updateNode,
  deleteNode,
  addEdge,
  updateEdge,
  deleteEdge,
  clearCanvas,
  updateGlobalVariables,
  updateWorkflowSettings,
  getNodeExecutionStatus,
  getNodeExecutionProgress,
  getNodeExecutionInfo,
  getEdgeAnimated,
  getEdgeFlowing,
  getEdgeDataPacket,
  updateNodeExecutionState,
  updateEdgeAnimationState,
  startExecution,
  completeExecution,
  cancelExecution,
  setPanelWidth,
  validateDataConsistency,
  cleanupInvalidEdges
} = workflowStore

const onPropertiesClose = () => {
  clearSelection()
}

const goBackToAgents = () => {
  router.push('/agents')
}



// 面板宽度调整方法
const startResize = (type: 'left' | 'right' | 'debug', event: MouseEvent) => {
  event.preventDefault()
  isResizing.value = true
  resizeType.value = type

  const startX = event.clientX
  let startWidth: number

  if (type === 'left') {
    startWidth = panelState.value.leftPanelWidth
  } else if (type === 'right') {
    startWidth = panelState.value.rightPanelWidth
  } else {
    startWidth = panelState.value.debugPanelWidth
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.value) return

    const deltaX = e.clientX - startX
    let newWidth: number

    if (type === 'left') {
      newWidth = Math.max(200, Math.min(600, startWidth + deltaX))
      setPanelWidth('left', newWidth)
    } else if (type === 'right') {
      newWidth = Math.max(200, Math.min(600, startWidth - deltaX))
      setPanelWidth('right', newWidth)
    } else {
      newWidth = Math.max(300, Math.min(800, startWidth - deltaX))
      setPanelWidth('debug', newWidth)
    }
  }

  const handleMouseUp = () => {
    isResizing.value = false
    resizeType.value = null
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

const zoomIn = () => {
  vueFlowZoomIn()
}

const zoomOut = () => {
  vueFlowZoomOut()
}

const fitView = () => {
  vueFlowFitView()
}

const showSettings = () => {
  showSettingsModal.value = true
}

const publishWorkflow = async () => {
  try {
    // TODO: 实现发布逻辑
    console.log('发布工作流')
  } catch (error) {
    console.error('发布失败:', error)
  }
}

// 工作流执行事件处理
const onExecutionStart = (context: any) => {
  console.log('工作流开始执行:', context)

  // 使用全局状态管理的开始执行方法
  startExecution()

  // 更新执行统计
  executionState.value.executionStats = {
    status: 'running',
    totalDuration: 0,
    completedNodes: 0,
    errorNodes: 0
  }

  // 找到开始节点，设置为准备状态
  const startNodes = nodes.value.filter(node => node.type === 'start')
  startNodes.forEach(node => {
    updateNodeExecutionState(node.id, 'pending', 0, '准备执行')
  })
}

const onExecutionComplete = (context: any) => {
  console.log('工作流执行完成:', context)

  // 使用全局状态管理的完成执行方法
  completeExecution({
    status: 'completed',
    totalDuration: context.metrics.totalDuration,
    completedNodes: context.metrics.completedNodes,
    errorNodes: context.metrics.errorNodes
  })

  // 清除节点高亮
  clearNodeHighlights()
}

const onExecutionError = (data: any) => {
  console.error('工作流执行错误:', data)
  // 可以在这里显示错误通知
}

const onExecutionPause = (context: any) => {
  console.log('工作流执行暂停:', context)
  executionState.value.isPaused = true
}

const onExecutionResume = (context: any) => {
  console.log('工作流执行恢复:', context)
  executionState.value.isPaused = false
}

const onExecutionCancel = (context: any) => {
  console.log('工作流执行取消:', context)

  // 使用全局状态管理的取消执行方法
  cancelExecution()

  // 清除节点高亮
  clearNodeHighlights()
}

const onNodeStart = (data: any) => {
  console.log('节点开始执行:', data)

  // 添加到执行步骤
  executionState.value.executionSteps.push({
    ...data.step,
    expanded: false
  })

  // 更新节点数据以触发重新渲染
  updateNodeExecutionState(data.node.id, 'running', 0, '执行中...')

  // 高亮显示当前执行的节点
  highlightCurrentNode(data.node.id)
}

const onNodeComplete = (data: any) => {
  console.log('节点执行完成:', data)

  // 更新执行步骤
  const stepIndex = executionState.value.executionSteps.findIndex(step => step.nodeId === data.node.id)
  if (stepIndex !== -1) {
    executionState.value.executionSteps[stepIndex] = {
      ...executionState.value.executionSteps[stepIndex],
      ...data.step
    }
  }

  updateNodeExecutionState(data.node.id, 'completed', 100, '执行完成')
}

const onNodeError = (data: any) => {
  console.error('节点执行错误:', data)

  // 更新执行步骤
  const stepIndex = executionState.value.executionSteps.findIndex(step => step.nodeId === data.node.id)
  if (stepIndex !== -1) {
    executionState.value.executionSteps[stepIndex] = {
      ...executionState.value.executionSteps[stepIndex],
      ...data.step
    }
  }

  updateNodeExecutionState(data.node.id, 'error', 0, '执行失败')
}

const onProgressUpdate = (data: any) => {
  console.log('执行进度更新:', data)
  // 更新当前节点的进度
  if (data.currentNode) {
    const progressInfo = `执行中... ${data.progress}%`
    updateNodeExecutionState(data.currentNode, 'running', data.progress, progressInfo)
  }

  // 更新执行步骤中的进度
  const stepIndex = executionState.value.executionSteps.findIndex(step => step.nodeId === data.currentNode)
  if (stepIndex !== -1) {
    executionState.value.executionSteps[stepIndex].progress = data.progress
  }

  // 更新全局执行进度
  executionState.value.executionProgress = data.progress
}

const onEdgeTraverse = (data: any) => {
  console.log('连接线遍历:', data)
  // 激活连接线动画
  const edgeId = data.edge.id

  // 更新连接线状态
  updateEdgeAnimationState(edgeId, true, data.data)

  // 高亮源节点到目标节点的路径
  highlightExecutionPath(data.sourceNode, data.targetNode)

  // 一段时间后停止动画
  setTimeout(() => {
    updateEdgeAnimationState(edgeId, false, null)
  }, 2000)
}

// 清除执行日志
const clearExecutionLogs = () => {
  executionState.value.executionSteps = []
  executionState.value.executionStats = null
}

// 高亮当前执行节点
const highlightCurrentNode = (nodeId: string) => {
  // 使用Vue Flow的选中状态管理
  const nodeIndex = nodes.value.findIndex(n => n.id === nodeId)
  if (nodeIndex !== -1) {
    // 更新节点数据，添加执行高亮标记
    nodes.value[nodeIndex] = {
      ...nodes.value[nodeIndex],
      data: {
        ...nodes.value[nodeIndex].data,
        isCurrentlyExecuting: true
      }
    }
  }

  // 清除其他节点的执行高亮
  nodes.value.forEach((node, index) => {
    if (node.id !== nodeId && node.data?.isCurrentlyExecuting) {
      nodes.value[index] = {
        ...node,
        data: {
          ...node.data,
          isCurrentlyExecuting: false
        }
      }
    }
  })
}

// 清除所有节点高亮
const clearNodeHighlights = () => {
  nodes.value.forEach((node, index) => {
    if (node.data?.isCurrentlyExecuting) {
      nodes.value[index] = {
        ...node,
        data: {
          ...node.data,
          isCurrentlyExecuting: false
        }
      }
    }
  })
}

// 高亮执行路径
const highlightExecutionPath = (sourceNodeId: string, targetNodeId: string) => {
  console.log(`执行路径: ${sourceNodeId} -> ${targetNodeId}`)

  // 可以在这里添加更复杂的路径高亮逻辑
  // 比如高亮所有相关的连接线和节点

  // 标记源节点为已完成状态（如果还没有标记的话）
  const sourceNode = nodes.value.find(n => n.id === sourceNodeId)
  if (sourceNode && sourceNode.data?.executionStatus === 'running') {
    updateNodeExecutionState(sourceNodeId, 'completed', 100, '执行完成')
  }

  // 标记目标节点为准备执行状态
  const targetNode = nodes.value.find(n => n.id === targetNodeId)
  if (targetNode && targetNode.data?.executionStatus === 'waiting') {
    updateNodeExecutionState(targetNodeId, 'pending', 0, '准备执行')
  }
}

// 节点和边的事件处理
const onNodesChange = (changes: any[]) => {
  // 处理节点变化
  saveWorkflow('nodes_change', '节点变化')
}

const onEdgesChange = (changes: any[]) => {
  // 处理边变化
  saveWorkflow('edges_change', '边变化')
}

const onNodeClick = (event: any) => {
  selectNode(event.node)
}

const onEdgeClick = (event: any) => {
  selectEdge(event.edge)
}

const onEdgeContextMenu = (event: any) => {
  event.preventDefault()

  // 显示连接线右键菜单
  const contextMenu = [
    {
      label: '插入节点',
      action: () => insertNodeOnEdge(event.edge)
    },
    {
      label: '删除连接线',
      action: () => deleteEdge(event.edge.id)
    },
    {
      label: '复制连接线',
      action: () => copyEdge(event.edge)
    }
  ]

  // TODO: 显示上下文菜单
  console.log('连接线右键菜单:', contextMenu)
}

const onPaneClick = () => {
  clearSelection()
}

// 连接处理
const onConnect = (connection: any) => {
  // 验证连接是否有效
  if (!connection.source || !connection.target) {
    console.warn('无效的连接:', connection)
    return
  }

  // 检查是否已存在相同的连接
  const existingEdge = edges.value.find(edge =>
    edge.source === connection.source &&
    edge.target === connection.target &&
    edge.sourceHandle === connection.sourceHandle &&
    edge.targetHandle === connection.targetHandle
  )

  if (existingEdge) {
    console.warn('连接已存在')
    return
  }

  // 创建新的连接线
  const newEdge = {
    id: `edge_${connection.source}_${connection.target}_${Date.now()}`,
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle || 'output',
    targetHandle: connection.targetHandle || 'input',
    type: 'default',
    animated: false,
    markerEnd: 'arrowclosed',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  }

  addEdge(newEdge)
}

// 拖拽处理
const onNodeDragStart = (nodeType: string) => {
  console.log('开始拖拽节点:', nodeType)
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

// 节点类型映射 - 将具体节点类型映射到VueFlow模板类型
const getVueFlowNodeType = (nodeType: string): string => {
  // 基础节点
  if (['start', 'end', 'condition', 'loop'].includes(nodeType)) {
    return nodeType
  }

  // 数据库节点
  if (['mysql', 'postgresql', 'oracle', 'redis'].includes(nodeType)) {
    return 'database'
  }

  // AI节点
  if (['llm-chat', 'text-embedding', 'speech-to-text', 'text-to-speech', 'image-generation', 'image-analysis', 'knowledge-base'].includes(nodeType)) {
    return 'ai'
  }

  // 文件生成节点
  if (['generate-ppt', 'generate-word', 'generate-pdf', 'generate-excel'].includes(nodeType)) {
    return 'file'
  }

  // 文件提取节点
  if (['extract-ppt', 'extract-word', 'extract-pdf'].includes(nodeType)) {
    return 'file'
  }

  // 渲染节点
  if (['pie-chart', 'line-chart', 'bar-chart'].includes(nodeType)) {
    return 'render'
  }

  // 数据处理节点
  if (['data-filter', 'data-transform'].includes(nodeType)) {
    return 'data'
  }

  // 工具节点
  if (['http-request', 'delay'].includes(nodeType)) {
    return 'utility'
  }

  // 默认返回原类型
  return nodeType
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()

  const nodeData = event.dataTransfer?.getData('application/vueflow')
  if (!nodeData) return

  try {
    const nodeTemplate = JSON.parse(nodeData)
    const { screenToFlowCoordinate } = vueFlowRef.value
    const position = screenToFlowCoordinate({
      x: event.clientX,
      y: event.clientY
    })

    // 创建新节点，保持完整的节点配置
    const newNode = {
      id: `${nodeTemplate.type}_${Date.now()}`,
      type: getVueFlowNodeType(nodeTemplate.type), // 使用映射后的类型
      position,
      data: {
        ...nodeTemplate.defaultData,
        label: nodeTemplate.label,
        description: nodeTemplate.description,
        nodeType: nodeTemplate.type, // 保存原始节点类型
        category: nodeTemplate.category,
        icon: nodeTemplate.icon,
        color: nodeTemplate.color,
        gradient: nodeTemplate.gradient,
        labelColor: nodeTemplate.labelColor,
        iconColor: nodeTemplate.iconColor,
        config: nodeTemplate.defaultData?.config || {}
      }
    }

    addNode(newNode)
  } catch (error) {
    console.error('解析节点数据失败:', error)
  }
}

// 添加节点到画布（点击添加）
const addNodeToCanvas = (nodeTemplate: any) => {
  const newNode = {
    id: `${nodeTemplate.type}_${Date.now()}`,
    type: getVueFlowNodeType(nodeTemplate.type), // 使用映射后的类型
    position: { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 },
    data: {
      ...nodeTemplate.defaultData,
      label: nodeTemplate.label,
      description: nodeTemplate.description,
      nodeType: nodeTemplate.type, // 保存原始节点类型
      category: nodeTemplate.category,
      icon: nodeTemplate.icon,
      color: nodeTemplate.color,
      gradient: nodeTemplate.gradient,
      labelColor: nodeTemplate.labelColor,
      iconColor: nodeTemplate.iconColor,
      config: nodeTemplate.defaultData?.config || {}
    }
  }

  nodes.value.push(newNode)
  saveWorkflow('add_node', `添加节点: ${nodeTemplate.label}`)
}

// 连接线操作方法
const insertNodeOnEdge = (edge: any) => {
  // 计算插入位置（连接线中点）
  const sourceNode = nodes.value.find(n => n.id === edge.source)
  const targetNode = nodes.value.find(n => n.id === edge.target)

  if (!sourceNode || !targetNode) return

  const insertPosition = {
    x: (sourceNode.position.x + targetNode.position.x) / 2,
    y: (sourceNode.position.y + targetNode.position.y) / 2
  }

  // 创建新节点
  const newNode = {
    id: `inserted_${Date.now()}`,
    type: 'condition', // 默认插入条件节点
    position: insertPosition,
    data: {
      label: '插入节点',
      description: '在连接线上插入的节点'
    }
  }

  // 删除原连接线
  deleteEdge(edge.id)

  // 添加新节点
  addNode(newNode)

  // 创建新的连接线
  const newEdge1 = {
    id: `edge_${edge.source}_${newNode.id}`,
    source: edge.source,
    target: newNode.id,
    sourceHandle: edge.sourceHandle,
    targetHandle: 'input'
  }

  const newEdge2 = {
    id: `edge_${newNode.id}_${edge.target}`,
    source: newNode.id,
    target: edge.target,
    sourceHandle: 'output',
    targetHandle: edge.targetHandle
  }

  addEdge(newEdge1)
  addEdge(newEdge2)
}

// 这些方法已经在全局状态管理中定义，这里不需要重复定义

const copyEdge = (edge: any) => {
  // TODO: 实现连接线复制功能
  console.log('复制连接线:', edge)
  // 可以将连接线信息复制到剪贴板或临时存储
}

// 节点和边的更新 - 使用全局状态管理的方法
const onNodeUpdate = updateNode
const onEdgeUpdate = updateEdge

// 设置保存
const onSettingsSave = (settings: any) => {
  updateWorkflowSettings(settings)
  showSettingsModal.value = false
}

// 获取节点颜色（用于小地图）
const getNodeColor = (node: Node) => {
  // 首先尝试从节点数据中获取颜色
  if (node.data?.color && node.data.color !== 'blue' && node.data.color !== 'green' && node.data.color !== 'red') {
    return node.data.color
  }

  // 如果有原始节点类型，根据原始类型获取配置
  if (node.data?.nodeType) {
    const nodeConfig = getNodeByType(node.data.nodeType)
    if (nodeConfig) {
      // 从gradient中提取主色调
      if (nodeConfig.gradient) {
        const gradientMatch = nodeConfig.gradient.match(/#[0-9a-fA-F]{6}/)
        if (gradientMatch) {
          return gradientMatch[0]
        }
      }
      // 使用预定义的颜色映射
      const categoryColorMap: Record<string, string> = {
        'basic': '#667eea',
        'database': '#11998e',
        'ai': '#fa709a',
        'file-generator': '#ff9a9e',
        'file-extractor': '#4facfe',
        'render': '#a8edea',
        'data': '#667eea',
        'utility': '#bdc3c7'
      }
      if (nodeConfig.category && categoryColorMap[nodeConfig.category]) {
        return categoryColorMap[nodeConfig.category]
      }
    }
  }

  // 根据映射后的节点类型获取颜色
  const typeColorMap: Record<string, string> = {
    start: '#10b981',
    end: '#ef4444',
    condition: '#f59e0b',
    loop: '#f59e0b',
    database: '#3b82f6',
    ai: '#8b5cf6',
    file: '#06b6d4',
    render: '#ec4899',
    data: '#84cc16',
    utility: '#6b7280'
  }

  return typeColorMap[node.type || 'default'] || '#6b7280'
}

// 保存工作流 - 使用全局状态管理的保存方法
const saveWorkflow = workflowStore.saveWorkflow



// 初始化
const init = async () => {
  try {
    // 加载智能体信息
    const agentResponse = await getAgentById(agentId)
    if (agentResponse.success) {
      agentInfo.value = agentResponse.data
    }

    // 使用全局状态管理的初始化方法
    await workflowStore.initializeWorkflow(agentId)

    // 数据一致性检查和清理
    const validation = validateDataConsistency()
    if (!validation.valid) {
      console.warn('发现数据一致性问题:', validation.errors)
      cleanupInvalidEdges()
    }

    // 使用静态节点库配置
    console.log('使用静态节点库配置:', NODE_LIBRARY_CONFIG.categories)
    // nodeLibraryCategories 已经在初始化时设置为静态配置
  } catch (error) {
    console.error('初始化失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 初始化响应式节点配置
  initializeNodeConfigs()

  // 监听配置变更
  watchNodeConfigChanges((version) => {
    console.log(`节点配置已更新，版本: ${version}`)
    // 这里可以添加额外的处理逻辑，比如强制重新渲染节点
  })

  init()
})

// 定期保存
let saveInterval: number
onMounted(() => {
  saveInterval = window.setInterval(() => {
    if (hasNodes.value) {
      saveWorkflow('auto_save', '自动保存')
    }
  }, 30000) // 每30秒自动保存
})

onUnmounted(() => {
  if (saveInterval) {
    clearInterval(saveInterval)
  }
})
</script>

<style scoped>
.workflow-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left .back-btn {
  color: #6b7280;
  background: transparent;
  border: 1px solid #e5e7eb;
}

.toolbar-left .back-btn:hover {
  color: #374151;
  background: #f3f4f6;
  border-color: #d1d5db;
}

.toolbar-left .agent-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.agent-info i {
  color: #3b82f6;
  font-size: 20px;
}

.agent-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.version {
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.toolbar-center .canvas-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-level {
  min-width: 50px;
  text-align: center;
  font-size: 14px;
  color: #6b7280;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.runner-container {
  padding: 0 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.btn.active {
  background: #2563eb !important;
  color: white;
}

.editor-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.node-library-panel,
.properties-panel,
.debug-panel,
.style-editor-panel {
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  max-width: 800px;
}

.properties-panel,
.debug-panel,
.style-editor-panel {
  border-right: none;
  border-left: 1px solid #e5e7eb;
}

/* 面板调整器 */
.panel-resizer {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
  transition: background-color 0.2s;
  z-index: 10;
}

.panel-resizer:hover {
  background-color: #3b82f6;
}

.panel-resizer-right {
  right: -2px;
}

.panel-resizer-left {
  left: -2px;
}

/* 调整时的视觉反馈 */
.panel-resizer:active {
  background-color: #2563eb;
}

body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

.panel-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

/* 收缩状态下的图标样式 */
.panel-collapsed-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  border-bottom: 1px solid #e5e7eb;
}

.collapsed-toggle {
  color: #6b7280;
  background: transparent;
  border: none;
  padding: 8px;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.collapsed-toggle:hover {
  color: #374151;
  background: #f3f4f6;
}

.collapsed-toggle i {
  font-size: 16px;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.canvas-container {
  flex: 1;
  position: relative;
}

.vue-flow {
  width: 100%;
  height: 100%;
}

/* 确保执行状态指示器显示在最顶层 */
.vue-flow .vue-flow__node {
  position: relative;
}

.vue-flow .vue-flow__node .execution-indicator {
  z-index: 10000 !important;
  position: absolute !important;
  pointer-events: none !important;
}

/* 确保当前执行的节点显示在其他节点之上 */
.vue-flow .vue-flow__node.node-currently-executing {
  z-index: 1001 !important;
}

/* 为执行状态的节点提供更高的层级 */
.vue-flow .vue-flow__node.execution-running,
.vue-flow .vue-flow__node.execution-waiting,
.vue-flow .vue-flow__node.execution-error {
  z-index: 1001 !important;
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
  color: #9ca3af;
}

.no-selection i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  padding: 8px;
  width: 36px;
  height: 36px;
  justify-content: center;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

/* 调试面板特定样式 */
.debug-panel {
  max-width: 800px;
}

.debug-panel .panel-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.debug-panel .runner-section {
  flex-shrink: 0;
  border-bottom: 1px solid #e5e7eb;
}

.debug-panel .logs-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.debug-panel .logs-section .execution-log-panel {
  flex: 1;
  margin-bottom: 0;
  border-radius: 0;
  box-shadow: none;
  border: none;
  display: flex;
  flex-direction: column;
}

.debug-panel .logs-section .execution-log-panel .panel-content {
  flex: 1;
  overflow-y: auto;
}
</style>
