import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { envConfig, logger } from '@/config/env'
import { AuthAPI } from '@/api/auth'

// 导入布局组件
import MainLayout from '@/layouts/MainLayout.vue'
import LoginLayout from '@/layouts/LoginLayout.vue'

// 页面组件使用动态导入实现代码分割
// 只保留必要的静态导入（如登录页面）
import Login from '@/views/Login.vue'
import PlatformInit from '@/views/PlatformInit.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    component: LoginLayout,
    children: [
      {
        path: '',
        name: 'Login',
        component: Login,
        meta: {
          title: '用户登录',
          public: true // 公开路由，无需认证
        }
      }
    ]
  },
  {
    path: '/platform-init',
    name: 'PlatformInit',
    component: PlatformInit,
    meta: {
      title: '平台初始化',
      public: true // 公开路由，无需认证
    }
  },
  {
    path: '/ai',
    name: 'AIExploreGlobal',
    component: () => import('@/views/aiExplore/AIExplore.vue'),
    meta: {
      title: 'AI探索',
      hideFromNav: true,
      requiresAuth: true
    }
  },
  {
    path: '/thinking-test',
    name: 'ThinkingTest',
    component: () => import('@/views/aiExplore/ThinkingTest.vue'),
    meta: {
      title: '思考内容测试',
      hideFromNav: true,
      requiresAuth: false // 测试页面，无需认证
    }
  },
  {
    path: '/conversation-list-test',
    name: 'ConversationListTest',
    component: () => import('@/views/aiExplore/ConversationListTest.vue'),
    meta: {
      title: '对话记录列表测试',
      hideFromNav: true,
      requiresAuth: false // 测试页面，无需认证
    }
  },
  {
    path: '/conversation-messages-test',
    name: 'ConversationMessagesTest',
    component: () => import('@/views/aiExplore/ConversationMessagesTest.vue'),
    meta: {
      title: '会话消息获取测试',
      hideFromNav: true,
      requiresAuth: false // 测试页面，无需认证
    }
  },
  {
    path: '/conversation-sidebar-test',
    name: 'ConversationSidebarTest',
    component: () => import('@/views/aiExplore/ConversationSidebarTest.vue'),
    meta: {
      title: '对话侧边栏测试',
      hideFromNav: true,
      requiresAuth: false // 测试页面，无需认证
    }
  },
  {
    path: '/parameter-config-test',
    name: 'ParameterConfigTest',
    component: () => import('@/views/test/ParameterConfigTest.vue'),
    meta: {
      title: '参数配置组件测试',
      hideFromNav: true,
      requiresAuth: false // 测试页面，无需认证
    }
  },
  {
    path: '/agent-choreography',
    name: 'AgentChoreography',
    component: () => import('@/views/agent/AgentChoreography.vue'),
    meta: {
      title: '智能体编排',
      hideFromNav: true,
      requiresAuth: false // 编排页面，通过token验证
    }
  },
  {
    path: '/',
    component: MainLayout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
        meta: {
          title: '首页',
          icon: '🏠',
          requiresAuth: true
        }
      },
      {
        path: 'business',
        name: 'BusinessProject',
        component: () => import('@/views/business/BusinessProject.vue'),
        meta: {
          title: 'AI场景',
          icon: '💼',
          requiresAuth: true
        }
      },
      {
        path: 'agents',
        name: 'Agents',
        component: () => import('@/views/agent/Agents.vue'),
        meta: {
          title: '智能体',
          icon: '🤖',
          requiresAuth: true
        }
      },
      {
        path: 'agents/:id/workflow',
        name: 'Workflow',
        component: () => import('@/views/workflow/index.vue'),
        meta: {
          title: '智能体编排',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'knowledge',
        name: 'Knowledge',
        component: () => import('@/views/rag/Knowledge.vue'),
        meta: {
          title: '知识库',
          icon: '📖',
          requiresAuth: true
        }
      },
      {
        path: 'knowledge/:id',
        name: 'KnowledgeDetail',
        component: () => import('@/views/rag/KnowledgeDetail.vue'),
        meta: {
          title: '知识库详情',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'knowledge/:id/datasource',
        name: 'DataSourceFlow',
        component: () => import('@/views/rag/datasource/index.vue'),
        meta: {
          title: '数据源工作流',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'knowledge/create',
        name: 'KnowledgeCreate',
        component: () => import('@/views/rag/KnowledgeCreate.vue'),
        meta: {
          title: '创建知识库',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'knowledge/create-flow',
        name: 'KnowledgeCreateFlow',
        component: () => import('@/views/rag/datasource/file/FileCollectFlow.vue'),
        meta: {
          title: '文档处理流程',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'knowledge-graph',
        name: 'KnowledgeGraph',
        component: () => import('@/views/rkg/KnowledgeGraph.vue'),
        meta: {
          title: '知识图谱',
          icon: '🕸️',
          requiresAuth: true
        }
      },
      {
        path: 'knowledge-graph/:id',
        name: 'KnowledgeGraphDetail',
        component: () => import('@/views/rkg/KnowledgeGraphDetail.vue'),
        meta: {
          title: '知识图谱详情',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'ai-explore',
        name: 'AIExplore',
        component: () => import('@/views/aiExplore/AIExplore.vue'),
        meta: {
          title: 'AI探索',
          icon: '🚀',
          requiresAuth: true
        }
      },
      {
        path: 'message-files-test',
        name: 'MessageFilesTest',
        component: () => import('@/views/aiExplore/MessageFilesTest.vue'),
        meta: {
          title: 'Message Files 测试',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'plugins',
        name: 'Plugins',
        component: () => import('@/views/Plugins.vue'),
        meta: {
          title: '插件集',
          icon: '🧩',
          requiresAuth: true
        }
      },
      {
        path: 'monitor',
        name: 'Monitor',
        component: () => import('@/views/monitor/Monitor.vue'),
        meta: {
          title: '监控',
          icon: '📈',
          requiresAuth: true,
          // roles: ['admin', 'monitor']  需要管理员或监控角色
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/settings/Settings.vue'),
        redirect: '/settings/system',
        meta: {
          title: '设置',
          icon: '⚙️',
          requiresAuth: true,
          // permissions: ['system:settings']  需要系统设置权限
        },
        children: [
          {
            path: 'system',
            name: 'SystemSettings',
            component: () => import('@/views/settings/SystemSettings.vue'),
            meta: {
              title: '系统设置',
              requiresAuth: true
            }
          },
          {
            path: 'users',
            name: 'UserManagement',
            component: () => import('@/views/settings/UserManagement.vue'),
            meta: {
              title: '用户管理',
              requiresAuth: true
            }
          },
          {
            path: 'units',
            name: 'UnitManagement',
            component: () => import('@/views/settings/UnitManagement.vue'),
            meta: {
              title: '单位管理',
              requiresAuth: true
            }
          },
          {
            path: 'tenants',
            name: 'TenantManagement',
            component: () => import('@/views/settings/tenant/TenantManagement.vue'),
            meta: {
              title: '租户管理',
              requiresAuth: true
            }
          },
          {
            path: 'dict',
            name: 'DictManagement',
            component: () => import('@/views/settings/DictManagement.vue'),
            meta: {
              title: '字典管理',
              requiresAuth: true
            }
          },
          {
            path: 'vector-database',
            name: 'VectorDatabaseManagement',
            component: () => import('@/views/settings/VectorDatabaseManagement.vue'),
            meta: {
              title: '向量数据库管理',
              requiresAuth: true
            }
          },
          {
            path: 'file-storage',
            name: 'fileStorage',
            component: () => import('@/views/settings/FileStorageManagement.vue'),
            meta: {
              title: '向量数据库管理',
              requiresAuth: true
            }
          },
          {
            path: 'knowledge-config',
            name: 'KnowledgeConfiguration',
            component: () => import('@/views/settings/KnowledgeConfiguration.vue'),
            meta: {
              title: '知识库配置',
              requiresAuth: true
            }
          },
          {
            path: 'knowledge-api',
            name: 'KnowledgeApiManagement',
            component: () => import('@/views/settings/KnowledgeApiManagement.vue'),
            meta: {
              title: '知识库API密钥',
              requiresAuth: true
            }
          },
          {
            path: 'agent-api',
            name: 'AgentApiManagement',
            component: () => import('@/views/settings/AgentApiManagement.vue'),
            meta: {
              title: '智能体API密钥',
              requiresAuth: true
            }
          },
          {
            path: 'model-config',
            name: 'ModelConfiguration',
            component: () => import('@/views/settings/ModelConfiguration.vue'),
            meta: {
              title: '模型配置',
              requiresAuth: true
            }
          },
          {
            path: 'third-platform-agents',
            name: 'ThirdPlatformManagement',
            component: () => import('@/views/settings/ThirdPlatformManagement.vue'),
            meta: {
              title: '第三方智能体',
              requiresAuth: true
            }
          },
          {
            path: 'rabbitmq',
            name: 'RabbitMQManagement',
            component: () => import('@/views/settings/mq/RabbitMQManagement.vue'),
            meta: {
              title: 'RabbitMQ管理',
              requiresAuth: true
            }
          },
          {
            path: 'announcements',
            name: 'AnnouncementManagement',
            component: () => import('@/views/settings/AnnouncementManagement.vue'),
            meta: {
              title: '公告发布',
              requiresAuth: true
            }
          },
          {
            path: 'help-docs',
            name: 'HelpDocManagement',
            component: () => import('@/views/settings/HelpDocManagement.vue'),
            meta: {
              title: '帮助文档配置',
              requiresAuth: true
            }
          }
        ]
      },
      {
        path: 'help',
        name: 'Help',
        component: () => import('@/views/Help.vue'),
        meta: {
          title: '帮助中心',
          icon: '💡',
          hideFromNav: true,
          requiresAuth: true
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/Profile.vue'),
        meta: {
          title: '个人信息',
          hideFromNav: true, // 不在导航菜单中显示
          requiresAuth: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  logger.debug('Route navigation:', { to: to.path, from: _from.path })

  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - ${envConfig.appTitle}`
  }

  // 检查平台初始化状态（除了平台初始化页面本身）
  if (to.path !== '/platform-init') {
    try {
      const initStatus = await AuthAPI.checkPlatformInitialization()
      logger.debug('Platform initialization status:', initStatus.data)

      if (!initStatus.data.initialized) {
        logger.info('Platform not initialized, redirecting to platform init page')
        next('/platform-init')
        return
      }
    } catch (error: any) {
      logger.error('Failed to check platform initialization status:', error)
      // 如果是网络错误或后端服务未启动，也跳转到初始化页面
      // 这样用户可以看到错误信息而不是被困在登录页面
      logger.warn('Backend service may not be available, redirecting to platform init page')
      next('/platform-init')
      return
    }
  }

  // 获取认证状态
  const authStore = useAuthStore()

  // 检查是否为公开路由
  const isPublicRoute = to.meta?.public === true || to.path === envConfig.loginRedirectPath

  // 如果是公开路由，直接通过
  if (isPublicRoute) {
    // 如果已登录且访问登录页，重定向到默认页面
    if (to.path === envConfig.loginRedirectPath && authStore.isLoggedIn) {
      logger.debug('Already logged in, redirecting to default page')
      next(envConfig.defaultRedirectPath)
      return
    }
    next()
    return
  }

  // 检查是否已登录
  if (!authStore.isLoggedIn) {
    logger.warn('Not authenticated, redirecting to login')
    next({
      path: envConfig.loginRedirectPath,
      query: { redirect: to.fullPath } // 保存原始路径用于登录后重定向
    })
    return
  }

  // 检查token是否过期
  if (authStore.isTokenExpired()) {
    logger.warn('Token expired, clearing auth and redirecting to login')
    authStore.clearAuth()
    next({
      path: envConfig.loginRedirectPath,
      query: { redirect: to.fullPath }
    })
    return
  }

  // 检查权限（如果路由定义了权限要求）
  if (to.meta?.permissions && Array.isArray(to.meta.permissions)) {
    const hasPermission = to.meta.permissions.some((permission: string) =>
      authStore.hasPermission(permission)
    )

    if (!hasPermission) {
      logger.warn('Insufficient permissions for route:', to.path)
      next({ name: 'Home' }) // 重定向到首页或403页面
      return
    }
  }

  // 检查角色（如果路由定义了角色要求）
  if (to.meta?.roles && Array.isArray(to.meta.roles)) {
    const hasRole = to.meta.roles.some((role: string) =>
      authStore.hasRole(role)
    )

    if (!hasRole) {
      logger.warn('Insufficient role for route:', to.path)
      next({ name: 'Home' }) // 重定向到首页或403页面
      return
    }
  }

  logger.debug('Route navigation allowed')
  next()
})

export default router
