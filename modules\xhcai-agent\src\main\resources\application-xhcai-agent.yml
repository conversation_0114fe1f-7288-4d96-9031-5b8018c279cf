# 智能体模块配置
agent:
  # 默认配置
  default:
    # 默认模型配置
    model:
      name: ${AGENT_DEFAULT_MODEL:gpt-3.5-turbo}
      temperature: ${AGENT_DEFAULT_TEMPERATURE:0.7}
      max-tokens: ${AGENT_DEFAULT_MAX_TOKENS:2000}
      top-p: 1.0
      frequency-penalty: 0.0
      presence-penalty: 0.0

    # 默认系统提示词
    system-prompt: ${AGENT_DEFAULT_SYSTEM_PROMPT:你是一个有用的AI助手，请根据用户的问题提供准确、有帮助的回答。}

    # 默认对话配置
    conversation:
      max-history: ${AGENT_CONVERSATION_MAX_HISTORY:20}
      timeout: ${AGENT_CONVERSATION_TIMEOUT:30000}
      auto-cleanup: ${AGENT_CONVERSATION_AUTO_CLEANUP:true}
      cleanup-interval: ${AGENT_CONVERSATION_CLEANUP_INTERVAL:3600000} # 1小时

    # 默认工具配置
    tools:
      enabled: ${AGENT_TOOLS_ENABLED:true}
      timeout: ${AGENT_TOOLS_TIMEOUT:10000}
      max-retries: ${AGENT_TOOLS_MAX_RETRIES:3}

  # 限制配置
  limits:
    # 智能体数量限制
    max-agents-per-tenant: ${AGENT_MAX_AGENTS_PER_TENANT:100}
    max-agents-per-user: ${AGENT_MAX_AGENTS_PER_USER:20}

    # 对话限制
    max-conversations-per-user: ${AGENT_MAX_CONVERSATIONS_PER_USER:50}
    max-messages-per-conversation: ${AGENT_MAX_MESSAGES_PER_CONVERSATION:1000}
    max-message-length: ${AGENT_MAX_MESSAGE_LENGTH:10000}

    # Token限制
    max-tokens-per-request: ${AGENT_MAX_TOKENS_PER_REQUEST:4000}
    max-tokens-per-day: ${AGENT_MAX_TOKENS_PER_DAY:100000}

    # 文件限制
    max-file-size: ${AGENT_MAX_FILE_SIZE:10485760} # 10MB
    max-files-per-message: ${AGENT_MAX_FILES_PER_MESSAGE:5}

  # 缓存配置
  cache:
    # 智能体配置缓存
    agent-config:
      enabled: ${AGENT_CACHE_ENABLED:true}
      ttl: ${AGENT_CONFIG_CACHE_TTL:3600} # 1小时
      max-size: ${AGENT_CONFIG_CACHE_MAX_SIZE:1000}

    # 对话历史缓存
    conversation-history:
      enabled: ${AGENT_CACHE_ENABLED:true}
      ttl: ${AGENT_HISTORY_CACHE_TTL:1800} # 30分钟
      max-size: ${AGENT_HISTORY_CACHE_MAX_SIZE:500}

  # 监控配置
  monitoring:
    # 性能监控
    performance:
      enabled: ${AGENT_PERFORMANCE_MONITORING_ENABLED:true}
      slow-query-threshold: ${AGENT_SLOW_QUERY_THRESHOLD:5000} # 5秒

    # 使用统计
    usage:
      enabled: ${AGENT_USAGE_MONITORING_ENABLED:true}
      report-interval: ${AGENT_USAGE_REPORT_INTERVAL:300000} # 5分钟

# Spring AI配置
spring:
  ai:
    # OpenAI配置
    openai:
      api-key: ${OPENAI_API_KEY:}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: ${AGENT_DEFAULT_MODEL:gpt-3.5-turbo}
          temperature: ${AGENT_DEFAULT_TEMPERATURE:0.7}
          max-tokens: ${AGENT_DEFAULT_MAX_TOKENS:2000}

    # Ollama配置
    ollama:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
          model: llama2
          temperature: ${AGENT_DEFAULT_TEMPERATURE:0.7}

# 日志配置
logging:
  level:
    com.xhcai.modules.agent: DEBUG
    org.springframework.ai: DEBUG

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,agent
  endpoint:
    agent:
      enabled: true
  metrics:
    tags:
      module: agent
