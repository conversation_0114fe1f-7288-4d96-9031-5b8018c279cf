package com.xhcai.common.api.dto;

import com.xhcai.common.api.query.DataScopeable;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 数据权限查询DTO基类
 * 提供数据权限查询的通用字段和方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "数据权限查询DTO基类")
public class DataScopeQueryDTO implements DataScopeable, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    /**
     * 数据权限SQL
     */
    @Schema(hidden = true)
    private String dataScope;

    @Override
    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String getDataScope() {
        return dataScope;
    }

    @Override
    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    @Override
    public String toString() {
        return "DataScopeQueryDTO{" +
                "tenantId=" + tenantId +
                ", dataScope='" + dataScope + '\'' +
                '}';
    }
}
