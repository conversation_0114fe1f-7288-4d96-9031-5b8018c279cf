import type { Component } from 'vue'

export interface RunnerWindowProps {
  visible: boolean
  title?: string
  component?: Component | string
  componentProps?: Record<string, any>
  headerInfo?: HeaderInfo
  isMinimized?: boolean
  isMaximized?: boolean
  position?: { x: number; y: number }
  size?: { width: number; height: number }
  resizable?: boolean
  draggable?: boolean
  showControls?: boolean
  defaultFullscreen?: boolean
  enableTaskbar?: boolean
}

export interface HeaderInfo {
  icon?: string
  iconBg?: string
  title?: string
  subtitle?: string
  meta?: Array<{
    label: string
    value: string
    type?: 'badge' | 'text'
    color?: string
  }>
}

export interface RunnerWindowInstance {
  minimize: () => void
  maximize: () => void
  close: () => void
  toggleMaximize: () => void
  updatePosition: (position: { x: number; y: number }) => void
  updateSize: (size: { width: number; height: number }) => void
}

export interface RunnerWindowEmits {
  minimize: []
  maximize: [isMaximized: boolean]
  close: []
  'update:visible': [visible: boolean]
  'update:position': [position: { x: number; y: number }]
  'update:size': [size: { width: number; height: number }]
  'update:isMaximized': [isMaximized: boolean]
  'update:isMinimized': [isMinimized: boolean]
}
