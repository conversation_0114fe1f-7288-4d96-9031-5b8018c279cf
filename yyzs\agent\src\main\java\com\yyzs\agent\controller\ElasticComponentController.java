package com.yyzs.agent.controller;

import com.yyzs.agent.entity.ElasticComponent;
import com.yyzs.agent.service.ElasticComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Elastic组件管理控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/components")
@RequiredArgsConstructor
@Tag(name = "Elastic组件管理", description = "Elastic Stack组件的安装、配置、启停等管理功能")
public class ElasticComponentController {

    private final ElasticComponentService elasticComponentService;

    @Operation(summary = "上传组件安装包", description = "上传Elastic Stack组件的安装包文件")
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadPackage(
            @Parameter(description = "安装包文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "组件类型") @RequestParam("componentType") String componentType,
            @Parameter(description = "组件版本") @RequestParam("version") String version,
            @Parameter(description = "组件描述") @RequestParam(value = "description", required = false) String description) {

        Map<String, Object> result = new HashMap<>();
        try {
            String componentId = elasticComponentService.uploadPackage(file, componentType, version, description);
            Map<String, Object> data = new HashMap<>();
            data.put("componentId", componentId);
            result.put("success", true);
            result.put("data", data);
            result.put("message", "安装包上传成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("上传安装包失败", e);
            result.put("success", false);
            result.put("message", "上传失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "安装组件", description = "根据配置参数安装指定的组件")
    @PostMapping("/{componentId}/install")
    public ResponseEntity<Map<String, Object>> installComponent(
            @Parameter(description = "组件ID") @PathVariable String componentId,
            @Parameter(description = "安装配置参数") @RequestBody(required = false) Map<String, Object> config) {

        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = elasticComponentService.installComponent(componentId, config);
            result.put("success", success);
            result.put("message", success ? "组件安装成功" : "组件安装失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("安装组件失败: " + componentId, e);
            result.put("success", false);
            result.put("message", "安装失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "卸载组件", description = "卸载指定的组件")
    @PostMapping("/{componentId}/uninstall")
    public ResponseEntity<Map<String, Object>> uninstallComponent(
            @Parameter(description = "组件ID") @PathVariable String componentId,
            @Parameter(description = "卸载选项") @RequestBody(required = false) Map<String, Object> uninstallOptions) {

        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = elasticComponentService.uninstallComponent(componentId, uninstallOptions);
            result.put("success", success);
            result.put("message", success ? "组件卸载成功" : "组件卸载失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("卸载组件失败: " + componentId, e);
            result.put("success", false);
            result.put("message", "卸载失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "启动组件", description = "启动指定的组件")
    @PostMapping("/{componentId}/start")
    public ResponseEntity<Map<String, Object>> startComponent(
            @Parameter(description = "组件ID") @PathVariable String componentId) {

        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = elasticComponentService.startComponent(componentId);
            result.put("success", success);
            result.put("message", success ? "组件启动成功" : "组件启动失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("启动组件失败: " + componentId, e);
            result.put("success", false);
            result.put("message", "启动失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "停止组件", description = "停止指定的组件")
    @PostMapping("/{componentId}/stop")
    public ResponseEntity<Map<String, Object>> stopComponent(
            @Parameter(description = "组件ID") @PathVariable String componentId) {

        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = elasticComponentService.stopComponent(componentId);
            result.put("success", success);
            result.put("message", success ? "组件停止成功" : "组件停止失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("停止组件失败: " + componentId, e);
            result.put("success", false);
            result.put("message", "停止失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "重启组件", description = "重启指定的组件")
    @PostMapping("/{componentId}/restart")
    public ResponseEntity<Map<String, Object>> restartComponent(
            @Parameter(description = "组件ID") @PathVariable String componentId) {

        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = elasticComponentService.restartComponent(componentId);
            result.put("success", success);
            result.put("message", success ? "组件重启成功" : "组件重启失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("重启组件失败: " + componentId, e);
            result.put("success", false);
            result.put("message", "重启失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "获取组件列表", description = "获取所有组件的列表")
    @GetMapping
    public ResponseEntity<List<ElasticComponent>> getComponents(
            @Parameter(description = "组件类型过滤") @RequestParam(required = false) String type,
            @Parameter(description = "组件状态过滤") @RequestParam(required = false) String status) {

        try {
            List<ElasticComponent> components;

            if (type != null && !type.isEmpty()) {
                components = elasticComponentService.getComponentsByType(type);
            } else if (status != null && !status.isEmpty()) {
                ElasticComponent.ComponentStatus componentStatus = ElasticComponent.ComponentStatus.valueOf(status.toUpperCase());
                components = elasticComponentService.getComponentsByStatus(componentStatus);
            } else {
                components = elasticComponentService.list();
            }

            return ResponseEntity.ok(components);
        } catch (Exception e) {
            log.error("获取组件列表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "获取组件详情", description = "根据ID获取组件的详细信息")
    @GetMapping("/{componentId}")
    public ResponseEntity<ElasticComponent> getComponent(
            @Parameter(description = "组件ID") @PathVariable String componentId) {

        try {
            ElasticComponent component = elasticComponentService.getById(componentId);
            if (component != null) {
                return ResponseEntity.ok(component);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取组件详情失败: " + componentId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "检查组件状态", description = "检查指定组件的运行状态")
    @GetMapping("/{componentId}/status")
    public ResponseEntity<Map<String, Object>> checkComponentStatus(
            @Parameter(description = "组件ID") @PathVariable String componentId) {

        Map<String, Object> result = new HashMap<>();
        try {
            ElasticComponent.ComponentStatus status = elasticComponentService.checkComponentStatus(componentId);
            result.put("componentId", componentId);
            result.put("status", status.name());
            result.put("statusDescription", status.getDescription());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查组件状态失败: " + componentId, e);
            result.put("error", "检查状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "批量启动组件", description = "批量启动多个组件")
    @PostMapping("/batch/start")
    public ResponseEntity<Map<String, Object>> batchStartComponents(
            @Parameter(description = "组件ID列表") @RequestBody List<String> componentIds) {

        try {
            Map<String, Boolean> results = elasticComponentService.batchStartComponents(componentIds);
            Map<String, Object> response = new HashMap<>();
            response.put("results", results);
            response.put("totalCount", componentIds.size());
            response.put("successCount", results.values().stream().mapToInt(b -> b ? 1 : 0).sum());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量启动组件失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "批量启动失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "批量停止组件", description = "批量停止多个组件")
    @PostMapping("/batch/stop")
    public ResponseEntity<Map<String, Object>> batchStopComponents(
            @Parameter(description = "组件ID列表") @RequestBody List<String> componentIds) {

        try {
            Map<String, Boolean> results = elasticComponentService.batchStopComponents(componentIds);
            Map<String, Object> response = new HashMap<>();
            response.put("results", results);
            response.put("totalCount", componentIds.size());
            response.put("successCount", results.values().stream().mapToInt(b -> b ? 1 : 0).sum());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量停止组件失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "批量停止失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "获取组件统计信息", description = "获取组件的统计信息")
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getComponentStatistics() {
        try {
            Map<String, Object> statistics = elasticComponentService.getComponentStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取组件统计信息失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "更新组件配置", description = "更新指定组件的配置")
    @PutMapping("/{componentId}/config")
    public ResponseEntity<Map<String, Object>> updateComponentConfig(
            @Parameter(description = "组件ID") @PathVariable String componentId,
            @Parameter(description = "配置参数") @RequestBody Map<String, Object> config) {

        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = elasticComponentService.updateComponentConfig(componentId, config);
            result.put("success", success);
            result.put("message", success ? "配置更新成功" : "配置更新失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新组件配置失败: " + componentId, e);
            result.put("success", false);
            result.put("message", "配置更新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    @Operation(summary = "获取组件配置", description = "获取指定组件的配置信息")
    @GetMapping("/{componentId}/config")
    public ResponseEntity<Map<String, Object>> getComponentConfig(
            @Parameter(description = "组件ID") @PathVariable String componentId) {

        try {
            Map<String, Object> config = elasticComponentService.getComponentConfig(componentId);
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            log.error("获取组件配置失败: " + componentId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(summary = "获取组件日志", description = "获取指定组件的日志信息")
    @GetMapping("/{componentId}/logs")
    public ResponseEntity<Map<String, Object>> getComponentLogs(
            @Parameter(description = "组件ID") @PathVariable String componentId,
            @Parameter(description = "日志行数") @RequestParam(defaultValue = "100") int lines) {

        try {
            List<String> logs = elasticComponentService.getComponentLogs(componentId, lines);
            Map<String, Object> result = new HashMap<>();
            result.put("componentId", componentId);
            result.put("lines", lines);
            result.put("logs", logs);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取组件日志失败: " + componentId, e);
            return ResponseEntity.badRequest().build();
        }
    }
}
