package com.xhcai.plugin.service;

import com.xhcai.plugin.config.PluginProperties;
import com.xhcai.plugin.core.PluginContext;
import com.xhcai.plugin.core.PluginContextManager;
import com.xhcai.plugin.core.PluginType;
import com.xhcai.plugin.loader.HotSwapPluginManager;
import com.xhcai.plugin.model.IModelService;
import com.xhcai.plugin.storage.IStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Map;

/**
 * 插件服务管理器
 * 提供统一的插件服务访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@Service
public class PluginServiceManager {
    private static final Logger log = LoggerFactory.getLogger(PluginServiceManager.class);

    @Autowired
    private PluginContextManager contextManager;

    @Autowired
    private HotSwapPluginManager hotSwapManager;

    @Autowired(required = false)
    private PluginProperties pluginProperties;
    
    @PostConstruct
    public void initialize() {
        log.info("Initializing Plugin Service Manager...");

        try {
            // 检查上下文管理器是否可用
            if (contextManager == null) {
                log.error("PluginContextManager is null, cannot initialize Plugin Service Manager");
                return;
            }

            // 初始化热插拔管理器
            Map<PluginType, PluginContext> contexts = contextManager.getAllContexts();
            if (contexts == null || contexts.isEmpty()) {
                log.warn("No plugin contexts available, Plugin Service Manager will have limited functionality");
            } else {
                hotSwapManager.initialize(contexts);
                log.info("Initialized hot swap manager with {} plugin contexts", contexts.size());
            }

            // 初始化插件服务
            initializePluginServices();

            log.info("Plugin Service Manager initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Plugin Service Manager", e);
            // 不抛出异常，允许应用继续启动
        }
    }

    /**
     * 初始化插件服务
     */
    private void initializePluginServices() {
        log.info("Initializing plugin services...");

        try {
            // 初始化存储服务
            initializeStorageServices();

            // 初始化模型服务
            initializeModelServices();

            log.info("Plugin services initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize plugin services", e);
        }
    }

    /**
     * 初始化存储服务
     */
    private void initializeStorageServices() {
        try {
            List<IStorageService> services = getStorageServices();
            if (services.isEmpty()) {
                log.warn("No storage services found to initialize");
                return;
            }

            // 获取存储配置
            Map<String, Object> storageConfig = getStorageConfig();
            if (storageConfig == null) {
                log.warn("No storage configuration found, skipping storage service initialization");
                return;
            }

            // 初始化每个存储服务
            for (IStorageService service : services) {
                try {
                    service.initialize(storageConfig);
                    log.info("Initialized storage service: {} ({})", service.getServiceName(), service.getServiceType());
                } catch (Exception e) {
                    log.error("Failed to initialize storage service: {} ({})", service.getServiceName(), service.getServiceType(), e);
                }
            }

        } catch (Exception e) {
            log.error("Failed to initialize storage services", e);
        }
    }

    /**
     * 初始化模型服务
     */
    private void initializeModelServices() {
        try {
            List<IModelService> services = getModelServices();
            if (services.isEmpty()) {
                log.warn("No model services found to initialize");
                return;
            }

            // 获取模型配置
            Map<String, Object> modelConfig = getModelConfig();
            if (modelConfig == null) {
                log.warn("No model configuration found, skipping model service initialization");
                return;
            }

            // 初始化每个模型服务
            for (IModelService service : services) {
                try {
                    service.initialize(modelConfig);
                    log.info("Initialized model service: {} ({})", service.getServiceName(), service.getServiceType());
                } catch (Exception e) {
                    log.error("Failed to initialize model service: {} ({})", service.getServiceName(), service.getServiceType(), e);
                }
            }

        } catch (Exception e) {
            log.error("Failed to initialize model services", e);
        }
    }

    /**
     * 获取存储配置
     */
    private Map<String, Object> getStorageConfig() {
        if (pluginProperties == null || pluginProperties.getTypes() == null) {
            return null;
        }

        PluginProperties.PluginTypeConfig storageTypeConfig = pluginProperties.getTypes().get("storage");
        if (storageTypeConfig == null) {
            return null;
        }

        return storageTypeConfig.getConfig();
    }

    /**
     * 获取模型配置
     */
    private Map<String, Object> getModelConfig() {
        if (pluginProperties == null || pluginProperties.getTypes() == null) {
            return null;
        }

        PluginProperties.PluginTypeConfig modelTypeConfig = pluginProperties.getTypes().get("model");
        if (modelTypeConfig == null) {
            return null;
        }

        return modelTypeConfig.getConfig();
    }
    
    @PreDestroy
    public void destroy() {
        log.info("Destroying Plugin Service Manager...");
        hotSwapManager.destroy();
        log.info("Plugin Service Manager destroyed");
    }
    
    /**
     * 获取存储服务实例列表
     */
    public List<IStorageService> getStorageServices() {
        try {
            if (contextManager == null) {
                log.warn("PluginContextManager is null, returning empty storage services list");
                return List.of();
            }

            PluginContext context = contextManager.getPluginContext(PluginType.STORAGE);
            if (context == null) {
                log.warn("Storage plugin context is null, returning empty storage services list");
                return List.of();
            }

            return context.getPluginInstances(IStorageService.class);
        } catch (Exception e) {
            log.error("Failed to get storage services", e);
            return List.of();
        }
    }

    /**
     * 获取指定的存储服务实例
     */
    public IStorageService getStorageService(String pluginId) {
        try {
            if (contextManager == null) {
                log.warn("PluginContextManager is null, cannot get storage service: {}", pluginId);
                return null;
            }

            PluginContext context = contextManager.getPluginContext(PluginType.STORAGE);
            if (context == null) {
                log.warn("Storage plugin context is null, cannot get storage service: {}", pluginId);
                return null;
            }

            return context.getPluginInstance(pluginId, IStorageService.class);
        } catch (Exception e) {
            log.error("Failed to get storage service: {}", pluginId, e);
            return null;
        }
    }

    /**
     * 获取默认的存储服务实例
     */
    public IStorageService getDefaultStorageService() {
        try {
            List<IStorageService> services = getStorageServices();
            if (services == null || services.isEmpty()) {
                log.warn("No storage service available");
                throw new RuntimeException("No storage service available");
            }

            IStorageService defaultService = services.get(0);
            log.debug("Using default storage service: {} ({})",
                     defaultService.getServiceName(), defaultService.getServiceType());
            return defaultService;
        } catch (Exception e) {
            log.error("Failed to get default storage service", e);
            throw new RuntimeException("Failed to get default storage service: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据服务类型获取存储服务
     */
    public IStorageService getStorageServiceByType(String serviceType) {
        List<IStorageService> services = getStorageServices();
        return services.stream()
                .filter(service -> serviceType.equals(service.getServiceType()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取模型服务实例列表
     */
    public List<IModelService> getModelServices() {
        PluginContext context = contextManager.getPluginContext(PluginType.MODEL);
        return context.getPluginInstances(IModelService.class);
    }
    
    /**
     * 获取指定的模型服务实例
     */
    public IModelService getModelService(String pluginId) {
        PluginContext context = contextManager.getPluginContext(PluginType.MODEL);
        return context.getPluginInstance(pluginId, IModelService.class);
    }
    
    /**
     * 获取默认的模型服务实例
     */
    public IModelService getDefaultModelService() {
        List<IModelService> services = getModelServices();
        if (services.isEmpty()) {
            throw new RuntimeException("No model service available");
        }
        return services.get(0);
    }
    
    /**
     * 根据服务类型获取模型服务
     */
    public IModelService getModelServiceByType(String serviceType) {
        List<IModelService> services = getModelServices();
        return services.stream()
                .filter(service -> serviceType.equals(service.getServiceType()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取支持指定模型的服务
     */
    public IModelService getModelServiceForModel(String modelName) {
        List<IModelService> services = getModelServices();
        return services.stream()
                .filter(service -> service.isModelAvailable(modelName))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 检查插件服务健康状态
     */
    public boolean isPluginServiceHealthy(PluginType pluginType, String pluginId) {
        try {
            PluginContext context = contextManager.getPluginContext(pluginType);
            
            switch (pluginType) {
                case STORAGE:
                    IStorageService storageService = context.getPluginInstance(pluginId, IStorageService.class);
                    return storageService != null && storageService.isHealthy();
                    
                case MODEL:
                    IModelService modelService = context.getPluginInstance(pluginId, IModelService.class);
                    return modelService != null && modelService.isHealthy();
                    
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("Failed to check plugin service health: {} - {}", pluginType, pluginId, e);
            return false;
        }
    }
    
    /**
     * 重启插件上下文
     */
    public void restartPluginContext(PluginType pluginType) {
        contextManager.restartPluginContext(pluginType);
        
        // 重新初始化热插拔管理器
        Map<PluginType, PluginContext> contexts = contextManager.getAllContexts();
        hotSwapManager.initialize(contexts);
    }
    
    /**
     * 获取插件上下文管理器
     */
    public PluginContextManager getContextManager() {
        return contextManager;
    }
    
    /**
     * 获取热插拔管理器
     */
    public HotSwapPluginManager getHotSwapManager() {
        return hotSwapManager;
    }
}
