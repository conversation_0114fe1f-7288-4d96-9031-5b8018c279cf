package com.xhcai.modules.system.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户登录日志实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_login_log", indexes = {
    @Index(name = "idx_sys_login_log_user_id", columnList = "user_id"),
    @Index(name = "idx_sys_login_log_user_login_time", columnList = "login_time"),
    @Index(name = "idx_sys_login_log_user_login_ip", columnList = "login_ip"),
    @Index(name = "idx_sys_login_log_user_status", columnList = "status")
})
@Schema(description = "用户登录日志")
@TableName("sys_login_log")
public class SysLoginLog extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Column(name = "user_id", length = 36)
    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    @TableField("user_id")
    private String userId;

    /**
     * 用户名
     */
    @Column(name = "username", length = 50)
    @Schema(description = "用户名")
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    @TableField("username")
    private String username;

    /**
     * 登录IP
     */
    @Column(name = "login_ip", length = 50)
    @Schema(description = "登录IP")
    @Size(max = 50, message = "登录IP长度不能超过50个字符")
    @TableField("login_ip")
    private String loginIp;

    /**
     * 登录地点
     */
    @Column(name = "login_location", length = 255)
    @Schema(description = "登录地点")
    @Size(max = 255, message = "登录地点长度不能超过255个字符")
    @TableField("login_location")
    private String loginLocation;

    /**
     * 浏览器类型
     */
    @Column(name = "browser", length = 50)
    @Schema(description = "浏览器类型")
    @Size(max = 50, message = "浏览器类型长度不能超过50个字符")
    @TableField("browser")
    private String browser;

    /**
     * 操作系统
     */
    @Column(name = "os", length = 50)
    @Schema(description = "操作系统")
    @Size(max = 50, message = "操作系统长度不能超过50个字符")
    @TableField("os")
    private String os;

    /**
     * 登录状态：0-成功，1-失败
     */
    @Column(name = "status", length = 1)
    @Schema(description = "登录状态：0-成功，1-失败")
    @TableField("status")
    private String status;

    /**
     * 提示消息
     */
    @Column(name = "msg", length = 255)
    @Schema(description = "提示消息")
    @Size(max = 255, message = "提示消息长度不能超过255个字符")
    @TableField("msg")
    private String msg;

    /**
     * 登录时间
     */
    @Column(name = "login_time")
    @Schema(description = "登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 登出时间
     */
    @Column(name = "logout_time")
    @Schema(description = "登出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("logout_time")
    private LocalDateTime logoutTime;

    /**
     * 会话ID
     */
    @Column(name = "session_id", length = 128)
    @Schema(description = "会话ID")
    @Size(max = 128, message = "会话ID长度不能超过128个字符")
    @TableField("session_id")
    private String sessionId;

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public LocalDateTime getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(LocalDateTime loginTime) {
        this.loginTime = loginTime;
    }

    public LocalDateTime getLogoutTime() {
        return logoutTime;
    }

    public void setLogoutTime(LocalDateTime logoutTime) {
        this.logoutTime = logoutTime;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    @Override
    public String toString() {
        return "SysLoginLog{" +
                "userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", loginIp='" + loginIp + '\'' +
                ", loginLocation='" + loginLocation + '\'' +
                ", browser='" + browser + '\'' +
                ", os='" + os + '\'' +
                ", status='" + status + '\'' +
                ", msg='" + msg + '\'' +
                ", loginTime=" + loginTime +
                ", logoutTime=" + logoutTime +
                ", sessionId='" + sessionId + '\'' +
                '}';
    }
}
