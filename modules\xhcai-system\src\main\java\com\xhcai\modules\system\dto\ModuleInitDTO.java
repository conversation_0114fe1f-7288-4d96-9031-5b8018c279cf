package com.xhcai.modules.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模块初始化信息DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "模块初始化信息")
public class ModuleInitDTO {

    @Schema(description = "模块ID")
    private String moduleId;

    @Schema(description = "模块名称")
    private String moduleName;

    @Schema(description = "模块描述")
    private String description;

    @Schema(description = "模块版本")
    private String version;

    @Schema(description = "模块作者")
    private String author;

    @Schema(description = "初始化器类名")
    private String initializerClass;

    @Schema(description = "是否已初始化")
    private Boolean initialized;

    @Schema(description = "初始化状态：NOT_INITIALIZED-未初始化，INITIALIZING-初始化中，INITIALIZED-已初始化，FAILED-初始化失败")
    private String status;

    @Schema(description = "初始化进度百分比(0-100)")
    private Integer progress;

    @Schema(description = "初始化消息")
    private String message;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "最后初始化时间")
    private LocalDateTime lastInitTime;

    @Schema(description = "初始化功能列表")
    private List<String> features;

    @Schema(description = "API前缀")
    private String apiPrefix;

    @Schema(description = "执行顺序")
    private Integer order;

    @Schema(description = "是否可以手动初始化")
    private Boolean manualInit;

    @Schema(description = "依赖的模块列表")
    private List<String> dependencies;

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getInitializerClass() {
        return initializerClass;
    }

    public void setInitializerClass(String initializerClass) {
        this.initializerClass = initializerClass;
    }

    public Boolean getInitialized() {
        return initialized;
    }

    public void setInitialized(Boolean initialized) {
        this.initialized = initialized;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LocalDateTime getLastInitTime() {
        return lastInitTime;
    }

    public void setLastInitTime(LocalDateTime lastInitTime) {
        this.lastInitTime = lastInitTime;
    }

    public List<String> getFeatures() {
        return features;
    }

    public void setFeatures(List<String> features) {
        this.features = features;
    }

    public String getApiPrefix() {
        return apiPrefix;
    }

    public void setApiPrefix(String apiPrefix) {
        this.apiPrefix = apiPrefix;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Boolean getManualInit() {
        return manualInit;
    }

    public void setManualInit(Boolean manualInit) {
        this.manualInit = manualInit;
    }

    public List<String> getDependencies() {
        return dependencies;
    }

    public void setDependencies(List<String> dependencies) {
        this.dependencies = dependencies;
    }
}
