package com.xhcai.common.api.dto;

import com.xhcai.common.api.query.Keywordable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 分页+关键字搜索查询DTO基类
 * 组合分页查询和关键字搜索功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "分页+关键字搜索查询DTO基类")
public class PageKeywordQueryDTO extends PageQueryDTO implements Keywordable {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索关键字
     */
    @Schema(description = "搜索关键字", example = "用户")
    @Size(max = 100, message = "搜索关键字长度不能超过100个字符")
    private String keyword;

    @Override
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return "PageKeywordQueryDTO{" +
                "current=" + getCurrent() +
                ", size=" + getSize() +
                ", orderBy='" + getOrderBy() + '\'' +
                ", orderDirection='" + getOrderDirection() + '\'' +
                ", keyword='" + keyword + '\'' +
                '}';
    }
}
