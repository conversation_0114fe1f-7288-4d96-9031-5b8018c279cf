package com.xhcai.modules.dify.dto.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Dify 已安装应用响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 已安装应用响应")
public class DifyInstalledAppsResponseDTO {

    /**
     * 已安装应用列表
     */
    @JsonProperty("installed_apps")
    @Schema(description = "已安装应用列表")
    private List<InstalledApp> installedApps;

    /**
     * 已安装应用信息
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "已安装应用信息")
    public static class InstalledApp {
        
        /**
         * 安装应用ID（用于调用聊天接口）
         */
        @Schema(description = "安装应用ID")
        private String id;

        /**
         * 应用信息
         */
        @Schema(description = "应用信息")
        private App app;

        /**
         * 应用所有者租户ID
         */
        @JsonProperty("app_owner_tenant_id")
        @Schema(description = "应用所有者租户ID")
        private String appOwnerTenantId;

        /**
         * 是否置顶
         */
        @JsonProperty("is_pinned")
        @Schema(description = "是否置顶")
        private Boolean isPinned;

        /**
         * 最后使用时间
         */
        @JsonProperty("last_used_at")
        @Schema(description = "最后使用时间")
        private Long lastUsedAt;

        /**
         * 是否可编辑
         */
        @Schema(description = "是否可编辑")
        private Boolean editable;

        /**
         * 是否可卸载
         */
        @Schema(description = "是否可卸载")
        private Boolean uninstallable;


    }

    /**
     * 应用信息
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "应用信息")
    public static class App {
        
        /**
         * 应用ID
         */
        @Schema(description = "应用ID")
        private String id;

        /**
         * 应用名称
         */
        @Schema(description = "应用名称")
        private String name;

        /**
         * 应用模式
         */
        @Schema(description = "应用模式")
        private String mode;

        /**
         * 图标类型
         */
        @JsonProperty("icon_type")
        @Schema(description = "图标类型")
        private String iconType;

        /**
         * 图标
         */
        @Schema(description = "图标")
        private String icon;

        /**
         * 图标背景色
         */
        @JsonProperty("icon_background")
        @Schema(description = "图标背景色")
        private String iconBackground;

        /**
         * 图标URL
         */
        @JsonProperty("icon_url")
        @Schema(description = "图标URL")
        private String iconUrl;

        /**
         * 是否使用图标作为回答图标
         */
        @JsonProperty("use_icon_as_answer_icon")
        @Schema(description = "是否使用图标作为回答图标")
        private Boolean useIconAsAnswerIcon;


    }



    /**
     * 根据应用ID查找已安装应用的ID
     *
     * @param appId 应用ID
     * @return 已安装应用的ID，如果未找到返回null
     */
    public String findInstalledAppIdByAppId(String appId) {
        if (installedApps == null || appId == null) {
            return null;
        }

        return installedApps.stream()
                .filter(installedApp -> installedApp.getApp() != null && 
                        appId.equals(installedApp.getApp().getId()))
                .map(InstalledApp::getId)
                .findFirst()
                .orElse(null);
    }


}
