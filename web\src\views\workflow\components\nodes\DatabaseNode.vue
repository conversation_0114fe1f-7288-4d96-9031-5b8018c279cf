<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="database-node-content">
        <div class="db-info" v-if="data.config?.host">
          <div class="info-item">
            <i class="fas fa-server"></i>
            <span>{{ data.config.host }}:{{ data.config.port }}</span>
          </div>
          <div class="info-item" v-if="data.config.database">
            <i class="fas fa-database"></i>
            <span>{{ data.config.database }}</span>
          </div>
        </div>

        <div class="sql-preview" v-if="data.config?.sql">
          <code>{{ truncateSql(data.config.sql) }}</code>
        </div>

        <p class="node-description">{{ getNodeDescription() }}</p>

        <div class="db-config" v-if="data.config">
          <div class="config-row" v-if="data.config.operation">
            <span class="config-label">操作:</span>
            <span class="config-value">{{ data.config.operation }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">

import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { getNodeByType } from '../../config/nodeLibrary'

// Props
interface DatabaseNodeData {
  label?: string
  description?: string
  nodeType?: string  // 添加节点类型字段
  config?: {
    dbType?: string
    host?: string
    port?: number
    database?: string
    username?: string
    password?: string
    sql?: string
    operation?: string
  }
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: DatabaseNodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 方法
const truncateSql = (sql: string) => {
  if (sql.length > 50) {
    return sql.substring(0, 50) + '...'
  }
  return sql
}

const getNodeDescription = () => {
  // 优先使用 nodeType，如果没有则使用 dbType，最后默认为 mysql
  const nodeType = props.data.nodeType || props.data.config?.dbType || 'mysql'
  const nodeConfig = getNodeByType(nodeType)
  return nodeConfig?.description || '数据库操作节点'
}
</script>

<style scoped>
.database-node-content {
  text-align: left;
}

.db-info {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #374151;
  margin-bottom: 6px;
  padding: 2px 0;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item i {
  width: 14px;
  text-align: center;
  color: #6b7280;
  font-size: 11px;
}

.info-item span {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f9fafb;
  color: #4b5563;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #e5e7eb;
  font-weight: 500;
}

.sql-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 12px;
}

.sql-preview code {
  color: #1e293b;
  font-size: 11px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  word-break: break-all;
  line-height: 1.4;
  font-weight: 400;
}

.node-description {
  font-size: 13px;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.5;
  font-weight: 400;
}

.db-config {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.config-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.config-row:last-child {
  border-bottom: none;
}

.config-label {
  font-weight: 600;
  color: #1f2937;
  flex-shrink: 0;
}

.config-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f9fafb;
  color: #6b7280;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #e5e7eb;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

</style>
