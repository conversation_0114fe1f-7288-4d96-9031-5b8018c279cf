import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// Element Plus 样式 - 组件将通过 unplugin-vue-components 自动导入
import 'element-plus/dist/index.css'

// 导入样式
import './assets/main.css'

// 导入 Font Awesome
import '@fortawesome/fontawesome-free/css/all.css'

// 导入 Vue Flow样式
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css' // 如果你想使用默认主题

// 导入兼容性支持
import './utils/polyfills'
import './utils/corePolyfills'
import { initPolyfills } from './utils/polyfills'
import { initCorePolyfills } from './utils/corePolyfills'
import { initES6Compatibility } from './utils/es6Compat'
import { initBrowserSupport } from './utils/browserSupport'
import { initVue3Compatibility } from './utils/vueCompat'
import { initThirdPlatformCompatibility } from './utils/thirdPlatformCompat'

// 导入API模块以确保拦截器被初始化
import '@/api/index'

// 初始化兼容性支持（按依赖顺序）
initCorePolyfills()
initPolyfills()
initES6Compatibility()
initBrowserSupport()
initVue3Compatibility()
initThirdPlatformCompatibility()

const app = createApp(App)

const pinia = createPinia()
app.use(pinia)
app.use(router)

// Element Plus 组件将通过 unplugin-vue-components 自动导入
// 无需手动注册

// 错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue Error:', err)
  console.error('Component:', instance)
  console.error('Info:', info)
}

// 警告处理
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Vue Warning:', msg)
  console.warn('Component:', instance)
  console.warn('Trace:', trace)
}

// 初始化认证状态
import { useAuthStore } from '@/stores/authStore'
const authStore = useAuthStore()
authStore.initAuth()

app.mount('#app')
