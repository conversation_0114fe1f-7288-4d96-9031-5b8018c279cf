package com.xhcai.common.api.dto;

import com.xhcai.common.api.query.Statusable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

import java.io.Serializable;

/**
 * 状态查询DTO基类
 * 提供状态查询的通用字段和方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "状态查询DTO基类")
public class StatusQueryDTO implements Statusable, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    @Override
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "StatusQueryDTO{" +
                "status='" + status + '\'' +
                '}';
    }
}
