package com.xhcai.modules.dify.dto.agent;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.Map;

/**
 * 智能体对话请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyChatRequestDTO {

    /**
     * 智能体ID
     */
    @NotBlank(message = "智能体ID不能为空")
    @JsonProperty("agent_id")
    private String agentId;

    /**
     * 用户消息
     */
    @NotBlank(message = "用户消息不能为空")
    @Size(max = 4000, message = "用户消息长度不能超过4000个字符")
    private String message;

    /**
     * 会话ID（可选，用于多轮对话）
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 用户ID
     */
    @JsonProperty("user_id")
    private String userId;

    /**
     * 输入变量
     */
    private Map<String, Object> inputs;

    /**
     * 是否流式响应
     */
    private boolean stream = false;

    /**
     * 响应模式：blocking-阻塞, streaming-流式
     */
    @JsonProperty("response_mode")
    private String responseMode = "blocking";

    /**
     * 文件列表（用于文件上传）
     */
    private List<FileInfo> files;

    /**
     * 自动保存对话历史
     */
    @JsonProperty("auto_generate_name")
    private boolean autoGenerateName = true;

    /**
     * 文件信息
     */
    public static class FileInfo {
        private String type;
        @JsonProperty("transfer_method")
        private String transferMethod;
        private String url;
        @JsonProperty("upload_file_id")
        private String uploadFileId;

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getTransferMethod() { return transferMethod; }
        public void setTransferMethod(String transferMethod) { this.transferMethod = transferMethod; }
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public String getUploadFileId() { return uploadFileId; }
        public void setUploadFileId(String uploadFileId) { this.uploadFileId = uploadFileId; }
    }

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public boolean isStream() {
        return stream;
    }

    public void setStream(boolean stream) {
        this.stream = stream;
        this.responseMode = stream ? "streaming" : "blocking";
    }

    public String getResponseMode() {
        return responseMode;
    }

    public void setResponseMode(String responseMode) {
        this.responseMode = responseMode;
        this.stream = "streaming".equals(responseMode);
    }

    public List<FileInfo> getFiles() {
        return files;
    }

    public void setFiles(List<FileInfo> files) {
        this.files = files;
    }

    public boolean isAutoGenerateName() {
        return autoGenerateName;
    }

    public void setAutoGenerateName(boolean autoGenerateName) {
        this.autoGenerateName = autoGenerateName;
    }
}
