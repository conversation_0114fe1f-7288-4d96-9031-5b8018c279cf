<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑租户信息' : '创建新租户'"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="modern-dialog"
    append-to-body
  >
    <template #header>
      <div class="modern-dialog-header">
        <div class="header-content">
          <div class="header-icon">
            <el-icon class="text-2xl text-white">
              <Plus v-if="!isEdit" />
              <Edit v-else />
            </el-icon>
          </div>
          <div class="ml-4">
            <h3 class="dialog-title">
              {{ isEdit ? '编辑租户信息' : '创建新租户' }}
            </h3>
            <p class="dialog-subtitle">
              {{ isEdit ? '修改租户的基本信息和配置' : '填写租户的基本信息和配置' }}
            </p>
          </div>
        </div>
      </div>
    </template>

    <div class="modern-dialog-body">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="0"
        class="modern-tenant-form"
      >
        <!-- 基本信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Key /></el-icon>
            <h4 class="section-title">基本信息</h4>
          </div>
          <div class="section-content">
            <div class="grid grid-cols-2 gap-6">
              <div class="form-field">
                <label class="field-label">
                  <el-icon><Key /></el-icon>
                  租户编码 <span class="required">*</span>
                </label>
                <el-form-item prop="tenantCode">
                  <el-input
                    v-model="formData.tenantCode"
                    placeholder="请输入租户编码"
                    :disabled="isEdit"
                    maxlength="50"
                    show-word-limit
                    size="large"
                    class="modern-input"
                  />
                </el-form-item>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <el-icon><OfficeBuilding /></el-icon>
                  租户名称 <span class="required">*</span>
                </label>
                <el-form-item prop="tenantName">
                  <el-input
                    v-model="formData.tenantName"
                    placeholder="请输入租户名称"
                    maxlength="100"
                    show-word-limit
                    size="large"
                    class="modern-input"
                  />
                </el-form-item>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <el-icon><OfficeBuilding /></el-icon>
                  租户简称
                </label>
                <el-form-item prop="tenantShortName">
                  <el-input
                    v-model="formData.tenantShortName"
                    placeholder="请输入租户简称"
                    maxlength="50"
                    show-word-limit
                    size="large"
                    class="modern-input"
                  />
                </el-form-item>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <el-icon><Setting /></el-icon>
                  租户状态 <span class="required">*</span>
                </label>
                <el-form-item prop="status">
                  <el-select
                    v-model="formData.status"
                    placeholder="请选择租户状态"
                    size="large"
                    class="modern-select w-full"
                  >
                    <el-option
                      v-for="status in statusOptions"
                      :key="status.dictValue"
                      :value="status.dictValue"
                      :label="status.dictLabel"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- 联系信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><User /></el-icon>
            <h4 class="section-title">联系信息</h4>
          </div>
          <div class="section-content">
            <div class="grid grid-cols-2 gap-6">
              <div class="form-field">
                <label class="field-label">
                  <el-icon><User /></el-icon>
                  联系人
                </label>
                <el-form-item prop="contactPerson">
                  <el-input
                    v-model="formData.contactPerson"
                    placeholder="请输入联系人"
                    maxlength="50"
                    show-word-limit
                    size="large"
                    class="modern-input"
                  />
                </el-form-item>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <el-icon><Phone /></el-icon>
                  联系电话
                </label>
                <el-form-item prop="contactPhone">
                  <el-input
                    v-model="formData.contactPhone"
                    placeholder="请输入联系电话"
                    maxlength="20"
                    size="large"
                    class="modern-input"
                  />
                </el-form-item>
              </div>

              <div class="form-field col-span-2">
                <label class="field-label">
                  <el-icon><Message /></el-icon>
                  联系邮箱
                </label>
                <el-form-item prop="contactEmail">
                  <el-input
                    v-model="formData.contactEmail"
                    placeholder="请输入联系邮箱"
                    maxlength="100"
                    size="large"
                    class="modern-input"
                  />
                </el-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Setting /></el-icon>
            <h4 class="section-title">配置信息</h4>
          </div>
          <div class="section-content">
            <div class="grid grid-cols-2 gap-6">
              <div class="form-field">
                <label class="field-label">
                  <el-icon><Key /></el-icon>
                  租户域名
                </label>
                <el-form-item prop="domain">
                  <el-input
                    v-model="formData.domain"
                    placeholder="请输入租户域名"
                    maxlength="100"
                    size="large"
                    class="modern-input"
                  />
                </el-form-item>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <el-icon><Clock /></el-icon>
                  过期时间
                </label>
                <el-form-item prop="expireTime">
                  <el-date-picker
                    v-model="formData.expireTime"
                    type="datetime"
                    placeholder="选择过期时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    size="large"
                    class="modern-input w-full"
                  />
                </el-form-item>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <el-icon><UserFilled /></el-icon>
                  用户限制
                </label>
                <el-form-item prop="userLimit">
                  <el-input-number
                    v-model="formData.userLimit"
                    :min="1"
                    :max="10000"
                    placeholder="用户数量限制"
                    size="large"
                    class="modern-input w-full"
                  />
                </el-form-item>
              </div>

              <div class="form-field">
                <label class="field-label">
                  <el-icon><DataBoard /></el-icon>
                  存储限制(GB)
                </label>
                <el-form-item prop="storageLimit">
                  <el-input-number
                    v-model="formData.storageLimit"
                    :min="1"
                    :max="1000"
                    placeholder="存储空间限制"
                    size="large"
                    class="modern-input w-full"
                  />
                </el-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Document /></el-icon>
            <h4 class="section-title">备注信息</h4>
          </div>
          <div class="section-content">
            <div class="form-field">
              <label class="field-label">
                <el-icon><Document /></el-icon>
                备注说明
              </label>
              <el-form-item prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入备注说明..."
                  maxlength="500"
                  show-word-limit
                  resize="none"
                  class="modern-textarea"
                />
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="modern-dialog-footer">
        <el-button @click="$emit('update:visible', false)" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          size="large"
        >
          {{ submitting ? '保存中...' : (isEdit ? '更新' : '创建') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Edit,
  Key,
  OfficeBuilding,
  User,
  Phone,
  Message,
  Setting,
  Clock,
  UserFilled,
  DataBoard,
  Document
} from '@element-plus/icons-vue'
import type { SysTenantVO } from '@/types/system'
import { useTenantData } from './composables/useTenantData'

// 使用租户数据管理 composable
const {
  statusOptions,
  createTenant,
  updateTenant
} = useTenantData()

// Props
interface Props {
  visible: boolean
  tenant?: SysTenantVO | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  tenant: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': [message: string]
}>()

// 计算属性
const isEdit = computed(() => !!props.tenant)

// 表单引用
const formRef = ref()
const submitting = ref(false)

// 表单数据
const formData = reactive({
  tenantCode: '',
  tenantName: '',
  tenantShortName: '',
  status: '0',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  domain: '',
  expireTime: '',
  userLimit: null,
  storageLimit: null,
  remark: ''
})

// 表单验证规则
const formRules = {
  tenantCode: [
    { required: true, message: '请输入租户编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  tenantName: [
    { required: true, message: '请输入租户名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择租户状态', trigger: 'change' }
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  contactPhone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 方法
const handleSubmit = async () => {
  try {
    submitting.value = true
    await formRef.value?.validate()

    let success = false
    if (isEdit.value) {
      success = await updateTenant(props.tenant!.id, { ...formData })
    } else {
      success = await createTenant({ ...formData })
    }

    if (success) {
      emit('update:visible', false)
      emit('success', isEdit.value ? '租户更新成功' : '租户创建成功')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    tenantCode: '',
    tenantName: '',
    tenantShortName: '',
    status: '0',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    domain: '',
    expireTime: '',
    userLimit: null,
    storageLimit: null,
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 监听tenant变化，更新表单数据
watch(() => props.tenant, (newTenant) => {
  if (newTenant) {
    Object.assign(formData, {
      tenantCode: newTenant.tenantCode || '',
      tenantName: newTenant.tenantName || '',
      tenantShortName: newTenant.tenantShortName || '',
      status: newTenant.status || '0',
      contactPerson: newTenant.contactPerson || '',
      contactPhone: newTenant.contactPhone || '',
      contactEmail: newTenant.contactEmail || '',
      domain: newTenant.domain || '',
      expireTime: newTenant.expireTime || '',
      userLimit: newTenant.userLimit || null,
      storageLimit: newTenant.storageLimit || null,
      remark: newTenant.remark || ''
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 监听visible变化，重置表单
watch(() => props.visible, (newVisible) => {
  if (!newVisible && !props.tenant) {
    resetForm()
  }
})
</script>

<style scoped>
/* 现代化对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__footer) {
  padding: 0;
}

.modern-dialog-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.dialog-subtitle {
  font-size: 14px;
  margin: 4px 0 0 0;
  opacity: 0.9;
  line-height: 1.2;
}

.modern-dialog-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modern-dialog-footer {
  padding: 20px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式 */
.form-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.section-icon {
  color: #3b82f6;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.section-content {
  padding: 20px;
}

.form-field {
  margin-bottom: 20px;
}

.field-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.field-label .el-icon {
  margin-right: 6px;
  color: #6b7280;
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

/* 输入框样式 */
:deep(.modern-input .el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

:deep(.modern-input .el-input__wrapper:hover) {
  border-color: #3b82f6;
}

:deep(.modern-input .el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.modern-select .el-select__wrapper) {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

:deep(.modern-select .el-select__wrapper:hover) {
  border-color: #3b82f6;
}

:deep(.modern-select .el-select__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.modern-textarea .el-textarea__inner) {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

:deep(.modern-textarea .el-textarea__inner:hover) {
  border-color: #3b82f6;
}

:deep(.modern-textarea .el-textarea__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 网格布局 */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.col-span-2 {
  grid-column: span 2;
}

.gap-6 {
  gap: 24px;
}

.w-full {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .col-span-2 {
    grid-column: span 1;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
}
</style>
