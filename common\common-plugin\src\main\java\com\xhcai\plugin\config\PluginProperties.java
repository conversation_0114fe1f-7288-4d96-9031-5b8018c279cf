package com.xhcai.plugin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * 插件配置属性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ConfigurationProperties(prefix = "xhcai.plugin")
public class PluginProperties {
    
    /**
     * 插件根目录
     */
    private String rootPath = "plugins";
    
    /**
     * 是否开发模式
     */
    private boolean devMode = true;
    
    /**
     * 是否启用热插拔
     */
    private boolean hotSwapEnabled = true;
    
    /**
     * 插件扫描间隔（秒）
     */
    private int scanInterval = 10;
    
    /**
     * 插件启动超时时间（秒）
     */
    private int startTimeout = 30;
    
    /**
     * 插件停止超时时间（秒）
     */
    private int stopTimeout = 10;
    
    /**
     * 各插件类型的特定配置
     */
    private Map<String, PluginTypeConfig> types;
    
    /**
     * 插件类型配置
     */
    @Data
    public static class PluginTypeConfig {
        
        /**
         * 是否启用该类型插件
         */
        private boolean enabled = true;
        
        /**
         * 插件目录
         */
        private String directory;
        
        /**
         * 最大插件数量
         */
        private int maxPlugins = 10;
        
        /**
         * 插件特定配置
         */
        private Map<String, Object> config;
    }
}
