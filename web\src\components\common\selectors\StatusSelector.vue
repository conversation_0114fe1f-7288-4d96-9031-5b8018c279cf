<template>
  <div class="status-selector" :class="{ 'is-dropdown': isDropdownMode }">
    <!-- 下拉模式 -->
    <DropdownSelector
      v-if="isDropdownMode" 
      v-model="selectedValue"
      :placeholder="mergedConfig.placeholder"
      :disabled="mergedConfig.disabled"
      :clearable="mergedConfig.clearable"
      :size="mergedConfig.size as 'large' | 'default' | 'small'"
      :dropdown-class="'status-dropdown-panel'"
      :align="align"
      @change="handleDropdownChange"
      @clear="clearSelection"
    >
      <template #display>
        <span v-if="!hasSelection" class="placeholder-text">{{ mergedConfig.placeholder }}</span>
        <div v-else class="selected-display">
          <span class="status-badge" :class="getStatusClass(selectedValue)">
            {{ displayText }}
          </span>
        </div>
      </template>
      
      <!-- 下拉内容 -->
      <div class="dropdown-status-selector">
        <StatusSelectorContent
          v-model="selectedValue"
          :config="mergedConfig"
          :options="statusOptions"
          @change="handleContentChange"
          @select="handleSelect"
        />
      </div>
    </DropdownSelector>

    <!-- 嵌入模式 -->
    <div v-else class="embedded-mode">
      <StatusSelectorContent
        v-model="selectedValue"
        :config="mergedConfig"
        :options="statusOptions"
        :show-header="true"
        @change="handleContentChange"
        @select="handleSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import DropdownSelector from './DropdownSelector.vue'
import StatusSelectorContent from './StatusSelectorContent.vue'

interface StatusOption {
  value: string | number
  label: string
  description?: string
  color?: string
  bgColor?: string
  disabled?: boolean
}

interface SelectorConfig {
  multiple?: boolean
  clearable?: boolean
  placeholder?: string
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  showDescription?: boolean
  showBadge?: boolean
}

interface Props {
  modelValue?: string | number | null
  config?: Partial<SelectorConfig>
  mode?: 'dropdown' | 'embedded'
  options?: StatusOption[]
  preset?: 'basic' | 'agent' | 'user' | 'system' | 'custom'
  align?: 'left' | 'right' | 'center'
}

interface Emits {
  (e: 'update:modelValue', value: string | number | null): void
  (e: 'change', value: string | number | null, option: StatusOption | null): void
  (e: 'select', value: string | number, option: StatusOption): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  config: () => ({}),
  mode: 'embedded',
  options: () => [],
  preset: 'basic',
  align: 'left'
})

const emit = defineEmits<Emits>()

// 响应式数据 - 确保每个组件实例都有独立的数据
const selectedValue = ref<string | number | null>(props.modelValue)
const selectedOption = ref<StatusOption | null>(null)

// 计算属性
const isDropdownMode = computed(() => props.mode === 'dropdown')

const mergedConfig = computed(() => ({
  multiple: false,
  clearable: true,
  placeholder: '请选择状态',
  size: 'default',
  disabled: false,
  showDescription: false,
  showBadge: true,
  ...props.config
}))

// 预设状态选项
const presetOptions = computed(() => {
  switch (props.preset) {
    case 'agent':
      return [
        { value: '', label: '全部状态', color: '#909399', bgColor: '#f4f4f5' },
        { value: 'active', label: '启用', color: '#16a34a', bgColor: '#dcfce7', description: '智能体正常运行' },
        { value: 'inactive', label: '禁用', color: '#dc2626', bgColor: '#fef2f2', description: '智能体已停用' }
      ]
    case 'user':
      return [
        { value: '', label: '全部状态', color: '#909399', bgColor: '#f4f4f5' },
        { value: '0', label: '正常', color: '#16a34a', bgColor: '#dcfce7', description: '用户账号正常' },
        { value: '1', label: '禁用', color: '#dc2626', bgColor: '#fef2f2', description: '用户账号已禁用' },
        { value: '2', label: '锁定', color: '#f59e0b', bgColor: '#fef3c7', description: '用户账号已锁定' }
      ]
    case 'system':
      return [
        { value: '', label: '全部状态', color: '#909399', bgColor: '#f4f4f5' },
        { value: 'online', label: '在线', color: '#16a34a', bgColor: '#dcfce7', description: '系统正常运行' },
        { value: 'offline', label: '离线', color: '#dc2626', bgColor: '#fef2f2', description: '系统离线' },
        { value: 'maintenance', label: '维护中', color: '#f59e0b', bgColor: '#fef3c7', description: '系统维护中' }
      ]
    case 'basic':
    default:
      return [
        { value: '', label: '全部', color: '#909399', bgColor: '#f4f4f5' },
        { value: '1', label: '启用', color: '#16a34a', bgColor: '#dcfce7' },
        { value: '0', label: '禁用', color: '#dc2626', bgColor: '#fef2f2' }
      ]
  }
})

// 最终状态选项
const statusOptions = computed(() => {
  return props.options.length > 0 ? props.options : presetOptions.value
})

const hasSelection = computed(() => {
  return selectedValue.value !== null &&
         selectedValue.value !== undefined &&
         selectedValue.value !== ''
})

// 计算显示文本
const displayText = computed(() => {
  if (!hasSelection.value) return ''
  const option = getStatusOption(selectedValue.value)
  return option ? option.label : String(selectedValue.value || '')
})

// 方法
const getStatusOption = (value: string | number | null): StatusOption | null => {
  if (value === null || value === undefined) return null
  return statusOptions.value.find(option => option.value === value) || null
}

const getStatusLabel = (value: string | number | null): string => {
  const option = getStatusOption(value)
  return option ? option.label : String(value || '')
}

const getStatusClass = (value: string | number | null): string => {
  const option = getStatusOption(value)
  if (!option) return ''
  
  // 根据预设类型返回不同的class
  switch (props.preset) {
    case 'agent':
      if (value === 'active') return 'status-active'
      if (value === 'inactive') return 'status-inactive'
      return 'status-all'
    case 'user':
      if (value === '0') return 'status-normal'
      if (value === '1') return 'status-disabled'
      if (value === '2') return 'status-locked'
      return 'status-all'
    case 'system':
      if (value === 'online') return 'status-online'
      if (value === 'offline') return 'status-offline'
      if (value === 'maintenance') return 'status-maintenance'
      return 'status-all'
    default:
      if (value === '1' || value === 'active') return 'status-enabled'
      if (value === '0' || value === 'inactive') return 'status-disabled'
      return 'status-all'
  }
}

const clearSelection = () => {
  selectedValue.value = null
  emit('update:modelValue', null)
  emit('change', null, null)
}

// 事件处理
const handleDropdownChange = (value: string | number | null) => {
  selectedValue.value = value
  emit('update:modelValue', value)
}

const handleContentChange = (value: string | number | null, option: StatusOption | null) => {
  selectedValue.value = value
  selectedOption.value = option
  emit('update:modelValue', value)
  emit('change', value, option)
}

const handleSelect = (value: string | number, option: StatusOption) => {
  emit('select', value, option)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
  selectedOption.value = getStatusOption(newValue)
}, { immediate: true })

// 暴露方法
defineExpose({
  clearSelection,
  getStatusOption,
  getStatusLabel
})
</script>

<style scoped>
.status-selector {
  width: 100%;
}

.status-selector.is-dropdown {
  display: inline-block;
}

.placeholder-text {
  color: #c0c4cc;
}

.selected-display {
  color: #606266;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 基础状态样式 */
.status-badge.status-all {
  background: #f4f4f5;
  color: #909399;
}

.status-badge.status-enabled,
.status-badge.status-active,
.status-badge.status-normal,
.status-badge.status-online {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.status-disabled,
.status-badge.status-inactive,
.status-badge.status-offline {
  background: #fef2f2;
  color: #dc2626;
}

.status-badge.status-locked,
.status-badge.status-maintenance {
  background: #fef3c7;
  color: #f59e0b;
}

.dropdown-status-selector {
  min-width: 200px;
}

.embedded-mode {
  width: 100%;
}

/* 下拉面板样式 */
:deep(.status-dropdown-panel) {
  min-width: 200px;
}

:deep(.status-dropdown-panel .dropdown-content) {
  padding: 4px;
}

:deep(.status-dropdown-panel .status-selector-content) {
  border: none;
  box-shadow: none;
}
</style>
