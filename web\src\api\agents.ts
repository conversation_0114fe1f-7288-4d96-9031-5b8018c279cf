/**
 * 智能体管理相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse, PaginatedResponse } from '@/types/api'
import { DeptAPI, UserAPI } from './system'
import type { SysDeptQueryDTO, SysUserQueryDTO } from '@/types/system'

// 智能体基本信息 - 后端返回的原始数据结构
export interface AgentRaw {
  id: string
  name: string
  description?: string
  icon?: string
  iconType?: 'emoji' | 'icon'
  mode: 'advanced-chat' | 'workflow' | 'chatflow' | 'agent' | 'text-generation'
  tags?: string[]
  createdAt: string
  updatedAt?: string
  organizeModel?: {
    fullName?: string
    realName?: string
  }
  status?: 'active' | 'inactive' | 'draft'
  isPublic?: boolean
  createdBy?: string
  usageCount?: number
  rating?: number
  config?: AgentConfig
}

// 智能体前端显示数据结构
export interface Agent {
  id: string
  name: string
  description: string
  icon: string
  iconBackground?: string  // 图标背景颜色
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
  sourceType?: string      // 智能体来源类型：platform-本平台，external-外部智能体
  sourceTypeName?: string  // 智能体来源类型名称
}

// 智能体配置
export interface AgentConfig {
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  tools: string[]
  knowledgeBases: string[]
  plugins: string[]
  settings: Record<string, any>
}

// 智能体查询参数 - 匹配后端AgentQueryDTO (继承PageTimeRangeQueryDTO)
export interface AgentQueryParams {
  current?: number    // 后端使用current字段，不是currentPage
  size?: number       // 后端使用size字段，不是pageSize
  name?: string
  type?: string
  status?: string
  isPublic?: boolean
  createBy?: string
  version?: string
  beginTime?: string  // 开始时间，格式：yyyy-MM-dd HH:mm:ss
  endTime?: string    // 结束时间，格式：yyyy-MM-dd HH:mm:ss
  projectId?: string
}

// 第三方智能体VO
export interface ThirdPlatformVO {
  id: string
  name: string
  description?: string
  unitId?: string
  unitName?: string
  connectionUrl?: string
  apiKey?: string
  timeout?: number
  accessScope?: string
  accessScopeText?: string
  authorizedUsers?: string[]
  authorizedUserNames?: string[]
  authorizedUnits?: string[]
  authorizedUnitNames?: string[]
  status?: number
  statusText?: string
  icon?: string
  iconBg?: string
  lastTestTime?: string
  lastTestResult?: number
  lastTestResultText?: string
  lastTestError?: string
  createBy?: string
  createByName?: string
  createTime?: string
  updateBy?: string
  updateByName?: string
  updateTime?: string
  remark?: string
}

// 后端AgentVO对应的前端类型
export interface AgentVO {
  id: string
  name: string
  description?: string
  avatar?: string
  iconBackground?: string
  type: string
  typeName?: string
  modelConfig?: string
  systemPrompt?: string
  toolsConfig?: string
  knowledgeConfig?: string
  conversationConfig?: string
  status?: string
  statusName?: string
  isPublic?: boolean
  sortOrder?: number
  version?: string
  publishedAt?: string
  lastConversationAt?: string
  conversationCount?: number
  tenantId?: string
  remark?: string
  deleted?: boolean
  createTime?: string    // 后端使用createTime字段，不是createdAt
  updateTime?: string    // 后端使用updateTime字段，不是updatedAt
  createBy?: string      // 创建人ID
  updateBy?: string      // 更新人ID
  createByName?: string  // 创建人姓名
  createByUsername?: string  // 创建人用户名
  createByDeptName?: string  // 创建人部门名称
  sourceType?: string    // 智能体来源类型：platform-本平台，external-外部智能体
  sourceTypeName?: string // 智能体来源类型名称
  externalAgentId?: string // 外部智能体ID
  externalAgentName?: string // 外部智能体名称
  externalAgentDetails?: string // 外部智能体详细信息（JSON格式）
  thirdPlatform?: ThirdPlatformVO // 第三方智能体详细信息
  platformId?: string // 第三方平台ID
  appId?: string // 应用ID（从external_agent_id映射而来）
  agentType?: string // 智能体类型
}

// 分页结果类型 - 匹配后端PageResult结构
export interface PageResult<T> {
  records: T[]  // 后端使用records字段，不是list
  total: number
  current: number  // 后端使用current字段，不是currentPage
  size: number     // 后端使用size字段，不是pageSize
  pages: number    // 后端使用pages字段，不是totalPages
  hasPrevious?: boolean
  hasNext?: boolean
  isFirst?: boolean
  isLast?: boolean
}

// 创建智能体请求 - 匹配后端AgentCreateDTO
export interface CreateAgentRequest {
  name: string
  description?: string
  avatar?: string  // 对应后端的avatar字段
  iconBackground?: string  // 图标背景颜色
  type: 'advanced-chat' | 'workflow' | 'chat' | 'agent-chat' | 'completion'  // 对应后端的type字段
  modelConfig?: string  // JSON格式的模型配置
  systemPrompt?: string  // 系统提示词
  toolsConfig?: string  // JSON格式的工具配置
  knowledgeConfig?: string  // JSON格式的知识库配置
  conversationConfig?: string  // JSON格式的对话配置
  isPublic?: boolean
  sortOrder?: number
  version?: string
  sourceType?: string  // 智能体来源类型：platform-本平台，external-外部智能体
  externalAgentId?: string  // 外部智能体ID
  platformId?: string  // 智能体平台ID，用于外部智能体关联
}

// 更新智能体请求
export interface UpdateAgentRequest {
  id: string
  name?: string
  description?: string
  avatar?: string
  iconBackground?: string
  category?: string
  tags?: string[]
  status?: 'active' | 'inactive' | 'draft'
  isPublic?: boolean
  config?: Partial<AgentConfig>
}

// 智能体对话消息
export interface AgentMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: Record<string, any>
  inputs?: Record<string, any>  // 输入参数（JSON格式）
  query?: string               // 查询内容
  externalInfoId?: string      // 外部信息ID
}

// 智能体对话会话
export interface AgentConversation {
  id: string
  agentId: string
  title: string
  messages: AgentMessage[]
  createdAt: string
  updatedAt: string
}

// 外部智能体详细信息
export interface ExternalAgentDetails {
  platform: string           // 平台名称，如 "Dify", "OpenAI", "Claude"
  platformUrl?: string        // 平台URL
  status: 'connected' | 'disconnected' | 'error' | 'syncing'  // 连接状态
  lastSync?: string          // 最后同步时间
  version?: string           // 智能体版本
  model?: string             // 使用的模型
  capabilities?: string[]    // 能力列表
  usage?: {                  // 使用统计
    totalCalls: number
    successRate: number
    avgResponseTime: number
  }
  config?: {                 // 配置信息
    temperature?: number
    maxTokens?: number
    timeout?: number
  }
  error?: string             // 错误信息（如果有）
  // 第三方智能体特有字段
  connectionUrl?: string     // 连接地址
  unitName?: string          // 所属单位名称
  accessScope?: string       // 访问范围文本
  lastTestTime?: string      // 最后测试时间
  lastTestResult?: string    // 最后测试结果文本
  lastTestError?: string     // 最后测试错误信息
  createByName?: string      // 创建人姓名
  updateByName?: string      // 更新人姓名
}

// 智能体对话请求
export interface ChatRequest {
  message: string
  conversationId?: string
  stream?: boolean
  context?: Record<string, any>
}

// 智能体对话响应
export interface ChatResponse {
  message: AgentMessage
  conversationId: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}

/**
 * 智能体管理API类
 */
export class AgentsAPI {
  /**
   * 获取智能体列表
   */
  static async getAgents(params?: AgentQueryParams): Promise<ApiResponse<PageResult<AgentVO>>> {
    return apiClient.post<PageResult<AgentVO>>('/api/agent/page', params)
  }

  /**
   * 获取智能体详情
   */
  static async getAgentById(id: string): Promise<ApiResponse<AgentVO>> {
    return apiClient.get<AgentVO>(`/api/agent/${id}`)
  }

  /**
   * 创建智能体
   */
  static async createAgent(data: CreateAgentRequest): Promise<ApiResponse<string>> {
    return apiClient.post<string>('/api/agent', data)
  }

  /**
   * 创建第三方智能体
   */
  static async createThirdPartyAgent(data: {
    name: string
    mode: string
    avatar?: string
    iconBackground?: string
    description?: string
    platformId: string
  }): Promise<ApiResponse<string>> {
    return apiClient.post<string>('/api/agent/third-party', data)
  }

  /**
   * 更新智能体
   */
  static async updateAgent(data: UpdateAgentRequest): Promise<ApiResponse<Agent>> {
    return apiClient.put<Agent>(`/api/agent`, data)
  }

  /**
   * 删除智能体
   */
  static async deleteAgent(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/agent/${id}`)
  }

  /**
   * 复制智能体
   */
  static async cloneAgent(id: string, name?: string): Promise<ApiResponse<Agent>> {
    return apiClient.post<Agent>(`/api/agents/${id}/clone`, { name })
  }

  /**
   * 发布智能体
   */
  static async publishAgent(id: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/agents/${id}/publish`)
  }

  /**
   * 下架智能体
   */
  static async unpublishAgent(id: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/agents/${id}/unpublish`)
  }

  /**
   * 与智能体对话
   */
  static async chat(agentId: string, data: ChatRequest): Promise<ApiResponse<ChatResponse>> {
    return apiClient.post<ChatResponse>(`/api/agents/${agentId}/chat`, data)
  }

  /**
   * 获取智能体对话历史
   */
  static async getConversations(agentId: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<PaginatedResponse<AgentConversation>>> {
    return apiClient.get<PaginatedResponse<AgentConversation>>(`/api/agents/${agentId}/conversations`, params)
  }

  /**
   * 获取对话详情
   */
  static async getConversation(conversationId: string): Promise<ApiResponse<AgentConversation>> {
    return apiClient.get<AgentConversation>(`/api/conversations/${conversationId}`)
  }

  /**
   * 删除对话
   */
  static async deleteConversation(conversationId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/conversations/${conversationId}`)
  }

  /**
   * 获取智能体分类
   */
  static async getCategories(): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>('/api/agents/categories')
  }

  /**
   * 获取热门标签
   */
  static async getPopularTags(): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>('/api/agents/tags/popular')
  }

  /**
   * 获取智能体统计信息
   */
  static async getAgentStats(id: string): Promise<ApiResponse<{
    usageCount: number
    conversationCount: number
    averageRating: number
    totalMessages: number
    activeUsers: number
  }>> {
    return apiClient.get(`/api/agents/${id}/stats`)
  }

  /**
   * 评价智能体
   */
  static async rateAgent(id: string, rating: number, comment?: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/agents/${id}/rate`, { rating, comment })
  }

  /**
   * 获取智能体评价
   */
  static async getAgentRatings(id: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<PaginatedResponse<{
    id: string
    rating: number
    comment?: string
    userId: string
    userName: string
    createdAt: string
  }>>> {
    return apiClient.get(`/api/agents/${id}/ratings`, params)
  }

  /**
   * 获取未关联项目的智能体列表
   */
  static async getUnlinkedAgents(params?: AgentQueryParams): Promise<ApiResponse<PageResult<AgentVO>>> {
    return apiClient.post<PageResult<AgentVO>>('/api/agent/unlinked', params)
  }

  /**
   * 批量关联智能体到项目
   */
  static async linkAgentsToProject(agentIds: string[], projectId: string): Promise<ApiResponse<boolean>> {
    return apiClient.post<boolean>('/api/agent/link-to-project', {
      agentIds,
      projectId
    })
  }

  /**
   * 获取AI探索智能体列表
   */
  static async getAiExploreAgents(): Promise<ApiResponse<any[]>> {
    return apiClient.get('/api/agent/ai-explore')
  }

  /**
   * 获取Dify直接登录token
   */
  static async getDifyDirectLoginToken(platformId: string): Promise<ApiResponse<{accessToken: string, refreshToken: string}>> {
    return apiClient.post(`/api/dify/auth/direct-login/${platformId}`)
  }
}

/**
 * 第三方智能体管理API
 */
export class ThirdPlatformAPI {
  /**
   * 分页查询第三方智能体列表
   */
  static async getPlatformPage(queryDTO: any): Promise<ApiResponse<PageResult<any>>> {
    return apiClient.get('/api/third-platform/page', queryDTO)
  }

  /**
   * 获取第三方智能体列表（用于关联选择）
   */
  static async getThirdPlatformList(): Promise<ApiResponse<ThirdPlatformVO[]>> {
    return apiClient.get<ThirdPlatformVO[]>('/api/third-platform/list')
  }

  /**
   * 获取指定平台下的智能体列表
   */
  static async getDifyAgentsList(platformId: string): Promise<ApiResponse<{
    page: number
    limit: number
    total: number
    data: any[]
    has_more: boolean
  }>> {
    return apiClient.get(`/api/dify/agents/list/${platformId}`)
  }

  /**
   * 获取已关联的智能体列表
   */
  static async getAssociatedAgents(platformId?: string): Promise<ApiResponse<any[]>> {
    const params = platformId ? { platformId } : {}
    return apiClient.get('/api/agent/associated', params)
  }

  /**
   * 根据ID查询第三方智能体详情
   */
  static async getAgentById(id: string): Promise<ApiResponse<any>> {
    return apiClient.get(`/api/third-platform/${id}`)
  }

  /**
   * 新增第三方智能体
   */
  static async addAgent(agent: any): Promise<ApiResponse<void>> {
    return apiClient.post('/api/third-platform', agent)
  }

  /**
   * 修改第三方智能体
   */
  static async updateAgent(agent: any): Promise<ApiResponse<void>> {
    return apiClient.put('/api/third-platform', agent)
  }

  /**
   * 删除第三方智能体
   */
  static async deleteAgent(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/third-platform/${id}`)
  }

  /**
   * 批量删除第三方智能体
   */
  static async batchDeleteAgents(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete('/api/third-platform/batch', { data: { ids } })
  }

  /**
   * 启用/禁用第三方智能体
   */
  static async toggleStatus(id: string, status: number): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/third-platform/${id}/status`, { status })
  }

  /**
   * 批量启用/禁用第三方智能体
   */
  static async batchToggleStatus(ids: string[], status: number): Promise<ApiResponse<void>> {
    return apiClient.put('/api/third-platform/batch/status', { ids, status })
  }

  /**
   * 测试第三方智能体连接
   */
  static async testConnection(id: string): Promise<ApiResponse<string>> {
    return apiClient.post(`/api/third-platform/${id}/test`)
  }

  /**
   * 查询用户可访问的第三方智能体列表
   */
  static async getAccessibleAgents(userId?: string): Promise<ApiResponse<any[]>> {
    return apiClient.get('/api/third-platform/accessible', userId ? { userId } : {})
  }

  /**
   * 检查用户访问权限
   */
  static async checkUserAccess(agentId: string, userId: string): Promise<ApiResponse<boolean>> {
    return apiClient.get(`/api/third-platform/${agentId}/access/${userId}`)
  }

  /**
   * 获取智能体统计信息
   */
  static async getAgentStatistics(): Promise<ApiResponse<any>> {
    return apiClient.get('/api/third-platform/statistics')
  }

  /**
   * 复制智能体配置
   */
  static async copyAgent(sourceId: string, newName: string): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/third-platform/${sourceId}/copy`, { newName })
  }

  /**
   * 导出智能体配置
   */
  static async exportAgents(ids: string[]): Promise<ApiResponse<any[]>> {
    return apiClient.post('/api/third-platform/export', ids)
  }

  /**
   * 导入智能体配置
   */
  static async importAgents(agents: any[]): Promise<ApiResponse<string>> {
    return apiClient.post('/api/third-platform/import', agents)
  }

  /**
   * 验证智能体配置
   */
  static async validateAgentConfig(agent: any): Promise<ApiResponse<string>> {
    return apiClient.post('/api/third-platform/validate', agent)
  }

  /**
   * 创建第三方智能体
   */
  static async createThirdPartyAgent(data: {
    name: string
    mode: string
    description?: string
    platformId: string
  }): Promise<ApiResponse<string>> {
    return apiClient.post<string>('/api/agent/third-party', data)
  }

  /**
   * 获取部门列表（用于下拉选择）
   */
  static async getDeptList(): Promise<ApiResponse<any[]>> {
    const queryDTO: SysDeptQueryDTO = { status: '0' }
    return DeptAPI.getDeptList(queryDTO)
  }

  /**
   * 获取用户列表（用于下拉选择）
   */
  static async getUserList(): Promise<ApiResponse<any[]>> {
    const queryDTO: SysUserQueryDTO = {
      current: 1,
      size: 100,
      status: '0'
    }
    const response = await UserAPI.getUserPage(queryDTO)
    if (response.success && response.data) {
      return {
        ...response,
        data: response.data.records || []
      }
    }
    return response as any
  }
}

// 导出便捷函数
export const getAgents = AgentsAPI.getAgents
export const getAgentById = AgentsAPI.getAgentById
export const createAgent = AgentsAPI.createAgent
export const updateAgent = AgentsAPI.updateAgent
export const deleteAgent = AgentsAPI.deleteAgent
export const cloneAgent = AgentsAPI.cloneAgent
export const publishAgent = AgentsAPI.publishAgent
export const unpublishAgent = AgentsAPI.unpublishAgent
export const chat = AgentsAPI.chat
export const getConversations = AgentsAPI.getConversations
export const getConversation = AgentsAPI.getConversation
export const deleteConversation = AgentsAPI.deleteConversation
export const getCategories = AgentsAPI.getCategories
export const getPopularTags = AgentsAPI.getPopularTags
export const getAgentStats = AgentsAPI.getAgentStats
export const rateAgent = AgentsAPI.rateAgent
export const getAgentRatings = AgentsAPI.getAgentRatings
export const getAiExploreAgents = AgentsAPI.getAiExploreAgents
export const getAssociatedAgents = ThirdPlatformAPI.getAssociatedAgents

export default {
  AgentsAPI,
  ThirdPlatformAPI: ThirdPlatformAPI
}
