<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件热插拔测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .plugin-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .plugin-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: #f9f9f9;
        }
        .plugin-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .plugin-info {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        .plugin-actions {
            margin-top: 10px;
        }
        .btn {
            padding: 5px 10px;
            margin: 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>插件热插拔测试</h1>
        
        <div class="section">
            <h2>插件列表</h2>
            <button class="btn btn-primary" onclick="refreshPlugins()">刷新插件列表</button>
            <div id="pluginList" class="plugin-list">
                <!-- 插件列表将在这里显示 -->
            </div>
        </div>
        
        <div class="section">
            <h2>插件操作</h2>
            <div class="form-group">
                <label for="pluginId">插件ID:</label>
                <input type="text" id="pluginId" placeholder="例如: minio-storage-plugin">
            </div>
            <div class="form-group">
                <label for="jarPath">JAR文件路径:</label>
                <input type="text" id="jarPath" placeholder="例如: plugins/storage/minio-storage-plugin-1.0.0.jar">
            </div>
            <div class="plugin-actions">
                <button class="btn btn-success" onclick="loadPlugin()">加载插件</button>
                <button class="btn btn-warning" onclick="reloadPlugin()">重载插件</button>
                <button class="btn btn-danger" onclick="unloadPlugin()">卸载插件</button>
                <button class="btn btn-primary" onclick="startPlugin()">启动插件</button>
                <button class="btn btn-warning" onclick="stopPlugin()">停止插件</button>
            </div>
        </div>
        
        <div class="section">
            <h2>操作日志</h2>
            <div id="statusLog"></div>
        </div>
    </div>

    <script>
        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusLog = document.getElementById('statusLog');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            statusLog.insertBefore(statusDiv, statusLog.firstChild);
            
            // 限制日志条数
            while (statusLog.children.length > 10) {
                statusLog.removeChild(statusLog.lastChild);
            }
        }

        // API调用封装
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return data;
            } catch (error) {
                showStatus(`API调用失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 刷新插件列表
        async function refreshPlugins() {
            try {
                showStatus('正在刷新插件列表...', 'info');
                const result = await apiCall('/api/plugins');
                
                if (result.success) {
                    displayPlugins(result.data);
                    showStatus(`插件列表刷新成功，共 ${result.data.length} 个插件`, 'success');
                } else {
                    showStatus(`刷新失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`刷新插件列表失败: ${error.message}`, 'error');
            }
        }

        // 显示插件列表
        function displayPlugins(plugins) {
            const pluginList = document.getElementById('pluginList');
            pluginList.innerHTML = '';
            
            if (plugins.length === 0) {
                pluginList.innerHTML = '<p>暂无插件</p>';
                return;
            }
            
            plugins.forEach(plugin => {
                const pluginCard = document.createElement('div');
                pluginCard.className = 'plugin-card';
                pluginCard.innerHTML = `
                    <h3>${plugin.pluginId}</h3>
                    <div class="plugin-info">描述: ${plugin.description || '无'}</div>
                    <div class="plugin-info">版本: ${plugin.version}</div>
                    <div class="plugin-info">状态: ${plugin.state}</div>
                    <div class="plugin-info">提供者: ${plugin.provider}</div>
                `;
                pluginList.appendChild(pluginCard);
            });
        }

        // 插件操作函数
        async function loadPlugin() {
            const pluginId = document.getElementById('pluginId').value;
            const jarPath = document.getElementById('jarPath').value;
            
            if (!pluginId || !jarPath) {
                showStatus('请填写插件ID和JAR路径', 'error');
                return;
            }
            
            try {
                showStatus(`正在加载插件: ${pluginId}`, 'info');
                const result = await apiCall(`/api/plugins/${pluginId}/load?jarPath=${encodeURIComponent(jarPath)}`, {
                    method: 'POST'
                });
                
                if (result.success) {
                    showStatus(result.data, 'success');
                    refreshPlugins();
                } else {
                    showStatus(result.message, 'error');
                }
            } catch (error) {
                showStatus(`加载插件失败: ${error.message}`, 'error');
            }
        }

        async function unloadPlugin() {
            const pluginId = document.getElementById('pluginId').value;
            if (!pluginId) {
                showStatus('请填写插件ID', 'error');
                return;
            }
            
            try {
                showStatus(`正在卸载插件: ${pluginId}`, 'info');
                const result = await apiCall(`/api/plugins/${pluginId}/unload`, {
                    method: 'POST'
                });
                
                if (result.success) {
                    showStatus(result.data, 'success');
                    refreshPlugins();
                } else {
                    showStatus(result.message, 'error');
                }
            } catch (error) {
                showStatus(`卸载插件失败: ${error.message}`, 'error');
            }
        }

        async function startPlugin() {
            const pluginId = document.getElementById('pluginId').value;
            if (!pluginId) {
                showStatus('请填写插件ID', 'error');
                return;
            }
            
            try {
                showStatus(`正在启动插件: ${pluginId}`, 'info');
                const result = await apiCall(`/api/plugins/${pluginId}/start`, {
                    method: 'POST'
                });
                
                if (result.success) {
                    showStatus(result.data, 'success');
                    refreshPlugins();
                } else {
                    showStatus(result.message, 'error');
                }
            } catch (error) {
                showStatus(`启动插件失败: ${error.message}`, 'error');
            }
        }

        async function stopPlugin() {
            const pluginId = document.getElementById('pluginId').value;
            if (!pluginId) {
                showStatus('请填写插件ID', 'error');
                return;
            }
            
            try {
                showStatus(`正在停止插件: ${pluginId}`, 'info');
                const result = await apiCall(`/api/plugins/${pluginId}/stop`, {
                    method: 'POST'
                });
                
                if (result.success) {
                    showStatus(result.data, 'success');
                    refreshPlugins();
                } else {
                    showStatus(result.message, 'error');
                }
            } catch (error) {
                showStatus(`停止插件失败: ${error.message}`, 'error');
            }
        }

        async function reloadPlugin() {
            const pluginId = document.getElementById('pluginId').value;
            const jarPath = document.getElementById('jarPath').value;
            
            if (!pluginId || !jarPath) {
                showStatus('请填写插件ID和JAR路径', 'error');
                return;
            }
            
            try {
                showStatus(`正在重载插件: ${pluginId}`, 'info');
                const result = await apiCall(`/api/plugins/${pluginId}/reload?jarPath=${encodeURIComponent(jarPath)}`, {
                    method: 'POST'
                });
                
                if (result.success) {
                    showStatus(result.data, 'success');
                    refreshPlugins();
                } else {
                    showStatus(result.message, 'error');
                }
            } catch (error) {
                showStatus(`重载插件失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动刷新插件列表
        window.onload = function() {
            refreshPlugins();
        };
    </script>
</body>
</html>
