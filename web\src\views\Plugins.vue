<template>
  <div class="container overflow-auto">
    <!-- 搜索和过滤区域 -->
    <div class="filter-section">
      <div class="filter-controls">
        <div class="filter-header">
          <h1 class="page-title">
            <i class="fas fa-puzzle-piece"></i>
            插件管理平台
          </h1>
          <div class="flex items-center justify-center gap-5">
            <button
              class="btn btn-primary"
              @click="showCustomPluginModal"
            >
              <i class="fas fa-plus"></i>
              创建自定义插件
            </button>
            <button
              class="btn btn-secondary"
              @click="showCustomPluginModal"
            >
              <i class="fas fa-plus"></i>
              添加MCP服务
            </button>
          </div>
        </div>
        <div class="type-filter-row">
          <div class="type-filters">
            <button
              v-for="type in pluginTypes"
              :key="type.value"
              class="type-filter-btn"
              :class="{ active: type.value === currentFilter }"
              @click="setTypeFilter(type.value)"
            >
              <span>{{ type.name }}</span>
              <span class="type-count">{{ type.count }}</span>
            </button>
          </div>
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input
              type="text"
              v-model="currentSearch"
              placeholder="搜索插件名称或描述..."
              @input="filterPlugins"
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 插件卡片网格 -->
    <div class="plugins-grid">
      <div
        v-if="paginatedPlugins.length === 0"
        style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #6c757d;"
      >
        <i class="fas fa-puzzle-piece" style="font-size: 48px; margin-bottom: 20px; opacity: 0.3;"></i>
        <h3 style="margin-bottom: 10px; font-weight: 500;">暂无插件</h3>
        <p>没有找到符合条件的插件</p>
      </div>

      <div
        v-for="plugin in paginatedPlugins"
        :key="plugin.id"
        class="plugin-card"
        @click="showPluginDetail(plugin)"
      >
        <div class="plugin-header">
          <div class="plugin-icon">
            <i :class="plugin.icon"></i>
          </div>
          <div class="plugin-info">
            <h3>{{ plugin.name }}</h3>
            <span class="plugin-type">{{ plugin.type }}</span>
          </div>
        </div>
        <div class="plugin-description">{{ plugin.description }}</div>
        <div class="plugin-meta">
          <div class="plugin-code">{{ plugin.code }}</div>
          <div class="plugin-version">v{{ plugin.version }}</div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container">
      <Pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="filteredPlugins.length"
        :show-page-size-selector="true"
        :show-page-numbers="true"
        :show-jumper="true"
        @change="handlePaginationChange"
      />
    </div>

    <!-- 插件详情右侧面板 -->
    <div class="plugin-detail-panel" :class="{ show: showDetailPanel }" v-if="selectedPlugin">
      <div class="panel-overlay" @click="closeDetailPanel"></div>
      <div class="panel-content">
        <!-- 面板头部 -->
        <div class="panel-header">
          <div class="plugin-header-info">
            <div class="plugin-icon-large">
              <i :class="selectedPlugin.icon"></i>
            </div>
            <div class="plugin-title-info">
              <h2>{{ selectedPlugin.name }}</h2>
              <div class="plugin-meta-info">
                <span class="plugin-type-badge">{{ selectedPlugin.type }}</span>
                <span class="plugin-version-badge">v{{ selectedPlugin.version }}</span>
              </div>
            </div>
          </div>
          <button class="panel-close-btn" @click="closeDetailPanel">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 面板主体 -->
        <div class="panel-body">
          <!-- 插件基本信息 -->
          <div class="info-section">
            <h3 class="section-title">
              <i class="fas fa-info-circle"></i>
              基本信息
            </h3>
            <div class="info-grid">
              <div class="info-item">
                <label>插件编码</label>
                <span>{{ selectedPlugin.code }}</span>
              </div>
              <div class="info-item">
                <label>插件类型</label>
                <span>{{ selectedPlugin.type }}</span>
              </div>
              <div class="info-item">
                <label>版本号</label>
                <span>v{{ selectedPlugin.version }}</span>
              </div>
              <div class="info-item">
                <label>创建时间</label>
                <span>{{ selectedPlugin.createTime || '2024-01-15' }}</span>
              </div>
            </div>
            <div class="info-description">
              <label>插件描述</label>
              <p>{{ selectedPlugin.description }}</p>
            </div>
          </div>

          <!-- 插件参数列表 -->
          <div class="params-section">
            <h3 class="section-title">
              <i class="fas fa-cogs"></i>
              参数配置
            </h3>
            <div class="params-list" v-if="selectedPlugin.parameters && selectedPlugin.parameters.length > 0">
              <div
                v-for="param in selectedPlugin.parameters"
                :key="param.name"
                class="param-item"
              >
                <div class="param-header">
                  <div class="param-name">
                    {{ param.name }}
                    <span v-if="param.required" class="required-mark">*</span>
                  </div>
                  <div class="param-type">{{ param.type }}</div>
                </div>
                <div class="param-description">{{ param.description }}</div>
                <div class="param-details">
                  <div v-if="param.defaultValue" class="param-default">
                    默认值: {{ param.defaultValue }}
                  </div>
                  <div v-if="param.example" class="param-example">
                    示例: {{ param.example }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="no-params">
              <i class="fas fa-info-circle"></i>
              该插件暂无配置参数
            </div>
          </div>

          <!-- 使用说明 -->
          <div class="usage-section" v-if="selectedPlugin.usage">
            <h3 class="section-title">
              <i class="fas fa-book"></i>
              使用说明
            </h3>
            <div class="usage-content">
              {{ selectedPlugin.usage }}
            </div>
          </div>
        </div>

        <!-- 面板底部 -->
        <div class="panel-footer">
          <button class="btn-panel btn-secondary" @click="closeDetailPanel">
            <i class="fas fa-times"></i>
            关闭
          </button>
          <button class="btn-panel btn-primary" @click="configurePlugin">
            <i class="fas fa-cog"></i>
            配置插件
          </button>
        </div>
      </div>
    </div>

    <!-- 自定义插件创建模态框 -->
    <div class="modal" :class="{ show: showModal }" v-if="showModal">
      <div class="modal-content custom-plugin-modal">
        <div class="modal-header">
          <h3>创建自定义插件</h3>
          <button class="modal-close" @click="hideCustomPluginModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveCustomPlugin">
            <div class="form-row">
              <div class="form-group">
                <label for="pluginName">插件名称 *</label>
                <input
                  type="text"
                  v-model="customPlugin.name"
                  required
                  placeholder="请输入插件名称"
                >
              </div>
              <div class="form-group">
                <label for="pluginIcon">插件图标</label>
                <div class="icon-upload-area">
                  <div class="icon-preview">
                    <i class="fas fa-puzzle-piece"></i>
                  </div>
                  <button type="button" class="btn btn-secondary">
                    <i class="fas fa-upload"></i>
                    上传图标
                  </button>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="pluginDescription">插件描述</label>
              <textarea
                v-model="customPlugin.description"
                rows="3"
                placeholder="请输入插件功能描述"
              ></textarea>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="pluginCode">插件编码 *</label>
                <input
                  type="text"
                  v-model="customPlugin.code"
                  required
                  placeholder="请输入插件唯一编码"
                >
              </div>
              <div class="form-group">
                <label for="pluginVersion">插件版本</label>
                <input
                  type="text"
                  v-model="customPlugin.version"
                  placeholder="1.0.0"
                >
              </div>
            </div>

            <div class="form-group">
              <label for="schemaInfo">Schema信息</label>
              <textarea
                v-model="customPlugin.schemaInfo"
                rows="4"
                placeholder="请输入JSON格式的Schema信息"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="availableTools">可用工具</label>
              <textarea
                v-model="customPlugin.availableTools"
                rows="3"
                placeholder="请输入可用工具列表，每行一个"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="authMethod">鉴权方法</label>
              <select v-model="customPlugin.authMethod">
                <option value="none">无需鉴权</option>
                <option value="api_key">API Key</option>
                <option value="oauth">OAuth 2.0</option>
                <option value="bearer">Bearer Token</option>
                <option value="basic">Basic Auth</option>
              </select>
            </div>

            <div class="form-group">
              <label for="pluginTags">标签</label>
              <div class="tags-input-container">
                <div class="tags-display">
                  <span
                    v-for="tag in customPluginTags"
                    :key="tag"
                    class="tag-item"
                  >
                    {{ tag }}
                    <span class="tag-remove" @click="removeTag(tag)">×</span>
                  </span>
                </div>
                <input
                  type="text"
                  v-model="tagInput"
                  placeholder="输入标签后按回车添加"
                  @keypress="addTag"
                >
              </div>
            </div>

            <div class="form-group">
              <label for="privacyPolicy">隐私协议</label>
              <textarea
                v-model="customPlugin.privacyPolicy"
                rows="3"
                placeholder="请输入隐私协议内容"
              ></textarea>
            </div>

            <div class="form-group">
              <label for="disclaimer">自定义免责声明</label>
              <textarea
                v-model="customPlugin.disclaimer"
                rows="3"
                placeholder="请输入自定义免责声明"
              ></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="hideCustomPluginModal">取消</button>
          <button type="button" class="btn btn-primary" @click="saveCustomPlugin">保存插件</button>
        </div>
      </div>
    </div>



    <!-- 遮罩层 -->
    <div class="overlay" :class="{ show: showModal }" @click="hideCustomPluginModal" v-if="showModal"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useMessage } from '@/composables/useMessage'
import Pagination from '@/components/common/Pagination.vue'

// 定义插件参数类型
interface PluginParameter {
  name: string
  type: string
  required: boolean
  description: string
  example: string
  defaultValue?: any
}

// 定义插件类型
interface Plugin {
  id: number
  name: string
  description: string
  code: string
  type: string
  version: string
  icon: string
  tags: string[]
  usage: string
  parameters: PluginParameter[]
  createTime: string
}

// 插件数据
const pluginsData = ref<Plugin[]>([
  {
    id: 1,
    name: "文档解析器",
    description: "智能解析各种格式的文档，支持PDF、Word、Excel等多种格式，提取关键信息并进行结构化处理。",
    code: "doc_parser_v1",
    type: "工具",
    version: "1.2.0",
    icon: "fas fa-file-alt",
    tags: ["文档", "解析", "AI"],
    usage: "上传文档文件，插件会自动识别格式并提取关键信息，支持批量处理。",
    parameters: [
      {
        name: "file_path",
        type: "string",
        required: true,
        description: "要解析的文档文件路径",
        example: "/path/to/document.pdf"
      },
      {
        name: "output_format",
        type: "string",
        required: false,
        description: "输出格式，支持json、xml、txt",
        defaultValue: "json",
        example: "json"
      },
      {
        name: "extract_images",
        type: "boolean",
        required: false,
        description: "是否提取文档中的图片",
        defaultValue: "false",
        example: "true"
      }
    ],
    createTime: "2024-01-15 10:30:00"
  },
  {
    id: 2,
    name: "GPT-4模型",
    description: "OpenAI最新的大语言模型，具备强大的理解和生成能力，支持多轮对话和复杂推理。",
    code: "gpt4_model",
    type: "模型",
    version: "4.0.1",
    icon: "fas fa-brain",
    tags: ["语言模型", "对话", "推理"],
    usage: "通过API调用GPT-4模型，支持文本生成、对话、翻译、摘要等多种任务。",
    parameters: [
      {
        name: "api_key",
        type: "string",
        required: true,
        description: "OpenAI API密钥",
        example: "sk-xxxxxxxxxxxxxxxx"
      },
      {
        name: "max_tokens",
        type: "integer",
        required: false,
        description: "最大生成token数量",
        defaultValue: "2048",
        example: "4096"
      },
      {
        name: "temperature",
        type: "float",
        required: false,
        description: "生成文本的随机性，0-1之间",
        defaultValue: "0.7",
        example: "0.8"
      },
      {
        name: "system_prompt",
        type: "string",
        required: false,
        description: "系统提示词，定义模型行为",
        defaultValue: "",
        example: "你是一个专业的AI助手"
      }
    ],
    createTime: "2024-01-10 14:20:00"
  },
  {
    id: 3,
    name: "智能路由策略",
    description: "根据用户输入自动选择最适合的处理路径，提高响应效率和准确性。",
    code: "smart_router",
    type: "Agent策略",
    version: "2.1.0",
    icon: "fas fa-route",
    tags: ["路由", "策略", "优化"],
    usage: "高",
    parameters: [],
    createTime: "2024-01-15"
  },
  {
    id: 4,
    name: "Chrome扩展",
    description: "浏览器扩展插件，支持网页内容抓取、自动填表、数据提取等功能。",
    code: "chrome_ext",
    type: "扩展",
    version: "1.5.2",
    icon: "fab fa-chrome",
    tags: ["浏览器", "扩展", "自动化"],
    usage: "中",
    parameters: [],
    createTime: "2024-01-10"
  },
  {
    id: 5,
    name: "办公套件",
    description: "集成多种办公工具的插件集合，包括邮件处理、日程管理、文档编辑等功能。",
    code: "office_suite",
    type: "插件集",
    version: "3.0.0",
    icon: "fas fa-briefcase",
    tags: ["办公", "套件", "集成"],
    usage: "高",
    parameters: [],
    createTime: "2024-01-20"
  },
  {
    id: 6,
    name: "数据库连接器",
    description: "MCP服务，提供与各种数据库的连接能力，支持MySQL、PostgreSQL、MongoDB等。",
    code: "db_connector",
    type: "MCP服务",
    version: "1.8.0",
    icon: "fas fa-database",
    tags: ["数据库", "连接", "MCP"],
    usage: "中",
    parameters: [],
    createTime: "2024-01-12"
  },
  {
    id: 7,
    name: "自定义API工具",
    description: "用户自定义的API调用工具，支持REST和GraphQL接口。",
    code: "custom_api_tool",
    type: "自定义",
    version: "1.0.0",
    icon: "fas fa-code",
    tags: ["API", "自定义", "接口"],
    usage: "低",
    parameters: [],
    createTime: "2024-01-08"
  },
  {
    id: 8,
    name: "数据处理流程",
    description: "自动化数据处理工作流，支持数据清洗、转换、分析等操作。",
    code: "data_workflow",
    type: "工作流",
    version: "2.3.1",
    icon: "fas fa-project-diagram",
    tags: ["数据", "工作流", "自动化"],
    usage: "中",
    parameters: [],
    createTime: "2024-01-18"
  }
])

// 插件类型配置
const pluginTypes = ref([
  { name: "全部类型", value: "", count: 0 },
  { name: "工具", value: "工具", count: 0 },
  { name: "模型", value: "模型", count: 0 },
  { name: "Agent策略", value: "Agent策略", count: 0 },
  { name: "扩展", value: "扩展", count: 0 },
  { name: "插件集", value: "插件集", count: 0 },
  { name: "MCP服务", value: "MCP服务", count: 0 },
  { name: "自定义", value: "自定义", count: 0 },
  { name: "工作流", value: "工作流", count: 0 }
])

// 全局变量
const currentPage = ref(1)
const pageSize = ref(40)
const currentFilter = ref("")
const currentSearch = ref("")
const filteredPlugins = ref<Plugin[]>([...pluginsData.value])
const customPluginTags = ref<string[]>([])

// 模态框相关
const showModal = ref(false)

// 消息提示
const { showSuccess, showError } = useMessage()

// 右侧面板相关
const showDetailPanel = ref(false)
const selectedPlugin = ref<Plugin | null>(null)
const customPlugin = ref({
  name: '',
  description: '',
  code: '',
  version: '1.0.0',
  schemaInfo: '',
  availableTools: '',
  authMethod: 'none',
  privacyPolicy: '',
  disclaimer: ''
})
const tagInput = ref('')



// 计算属性
const paginatedPlugins = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPlugins.value.slice(start, end)
})

// 分页处理方法
const handlePaginationChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
}

// 方法
const updateTypeCounts = () => {
  pluginTypes.value.forEach(type => {
    if (type.value === "") {
      type.count = pluginsData.value.length
    } else {
      type.count = pluginsData.value.filter(plugin => plugin.type === type.value).length
    }
  })
}

const filterPlugins = () => {
  filteredPlugins.value = pluginsData.value.filter(plugin => {
    const matchesType = !currentFilter.value || plugin.type === currentFilter.value
    const matchesSearch = !currentSearch.value ||
      plugin.name.toLowerCase().includes(currentSearch.value.toLowerCase()) ||
      plugin.description.toLowerCase().includes(currentSearch.value.toLowerCase()) ||
      plugin.tags.some(tag => tag.toLowerCase().includes(currentSearch.value.toLowerCase()))

    return matchesType && matchesSearch
  })

  currentPage.value = 1
}

const setTypeFilter = (type: string) => {
  currentFilter.value = type
  filterPlugins()
}



// 模态框相关方法
const showCustomPluginModal = () => {
  showModal.value = true
  document.body.style.overflow = 'hidden'
}

const hideCustomPluginModal = () => {
  showModal.value = false
  document.body.style.overflow = ''
  resetCustomPluginForm()
}

const resetCustomPluginForm = () => {
  customPlugin.value = {
    name: '',
    description: '',
    code: '',
    version: '1.0.0',
    schemaInfo: '',
    availableTools: '',
    authMethod: 'none',
    privacyPolicy: '',
    disclaimer: ''
  }
  customPluginTags.value = []
  tagInput.value = ''
}

// 标签相关方法
const addTag = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault()
    const tag = tagInput.value.trim()
    if (tag && !customPluginTags.value.includes(tag)) {
      customPluginTags.value.push(tag)
      tagInput.value = ''
    }
  }
}

const removeTag = (tag: string) => {
  customPluginTags.value = customPluginTags.value.filter(t => t !== tag)
}

// 保存自定义插件
const saveCustomPlugin = () => {
  if (!customPlugin.value.name || !customPlugin.value.code) {
    showError('请填写插件名称和插件编码')
    return
  }

  if (pluginsData.value.some(plugin => plugin.code === customPlugin.value.code)) {
    showError('插件编码已存在，请使用其他编码')
    return
  }

  const newPlugin = {
    id: Date.now(),
    name: customPlugin.value.name,
    description: customPlugin.value.description || '暂无描述',
    code: customPlugin.value.code,
    type: '自定义',
    version: customPlugin.value.version || '1.0.0',
    icon: 'fas fa-puzzle-piece',
    tags: [...customPluginTags.value],
    usage: '自定义插件使用说明',
    parameters: [],
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
  }

  pluginsData.value.push(newPlugin)
  updateTypeCounts()
  filterPlugins()
  hideCustomPluginModal()
  showSuccess('自定义插件创建成功！')
}

// 右侧面板相关方法
const showPluginDetail = (plugin: Plugin) => {
  selectedPlugin.value = plugin
  showDetailPanel.value = true
  // 添加body类来防止背景滚动
  document.body.classList.add('panel-open')
}

const closeDetailPanel = () => {
  showDetailPanel.value = false
  selectedPlugin.value = null
  // 移除body类恢复滚动
  document.body.classList.remove('panel-open')
}

const configurePlugin = () => {
  if (selectedPlugin.value) {
    alert(`配置插件"${selectedPlugin.value.name}"功能开发中...`)
  }
}



// 插件操作方法
const showPluginDetails = (plugin: any) => {
  const detailsHtml = `
    <div style="padding: 20px; max-width: 500px;">
      <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
        <div style="width: 60px; height: 60px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 28px; color: white; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
          <i class="${plugin.icon}"></i>
        </div>
        <div>
          <h3 style="margin: 0 0 5px 0; color: #2c3e50;">${plugin.name}</h3>
          <span style="background: #f8f9fa; color: #6c757d; padding: 4px 8px; border-radius: 6px; font-size: 12px;">${plugin.type}</span>
        </div>
      </div>
      <div style="margin-bottom: 15px;">
        <strong>描述：</strong><br>
        <p style="margin: 5px 0; color: #6c757d; line-height: 1.5;">${plugin.description}</p>
      </div>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
        <div>
          <strong>插件编码：</strong><br>
          <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-size: 12px;">${plugin.code}</code>
        </div>
        <div>
          <strong>版本：</strong><br>
          <span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 4px; font-size: 12px;">v${plugin.version}</span>
        </div>
      </div>
      ${plugin.tags && plugin.tags.length > 0 ? `
        <div>
          <strong>标签：</strong><br>
          <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
            ${plugin.tags.map((tag: string) => `<span style="background: linear-gradient(135deg, #667eea20, #764ba220); color: #667eea; padding: 3px 7px; border-radius: 10px; font-size: 10px; border: 1px solid #667eea30;">${tag}</span>`).join('')}
          </div>
        </div>
      ` : ''}
    </div>
  `

  showInfoModal('插件详情', detailsHtml)
}

const clonePlugin = (plugin: any) => {
  const clonedPlugin = {
    ...plugin,
    id: Date.now(),
    name: plugin.name + ' (副本)',
    code: plugin.code + '_copy_' + Date.now()
  }

  pluginsData.value.push(clonedPlugin)
  updateTypeCounts()
  filterPlugins()
  showSuccess(`插件 "${plugin.name}" 克隆成功！`)
}

const exportPlugin = (plugin: any) => {
  const exportData = {
    name: plugin.name,
    description: plugin.description,
    code: plugin.code,
    type: plugin.type,
    version: plugin.version,
    tags: plugin.tags || [],
    exportTime: new Date().toISOString()
  }

  const dataStr = JSON.stringify(exportData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `${plugin.code}_config.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  showSuccess(`插件配置已导出：${plugin.code}_config.json`)
}

const deletePlugin = (plugin: any) => {
  if (confirm(`确定要删除插件 "${plugin.name}" 吗？此操作不可撤销。`)) {
    const index = pluginsData.value.findIndex(p => p.id === plugin.id)
    if (index > -1) {
      pluginsData.value.splice(index, 1)
      updateTypeCounts()
      filterPlugins()
      showSuccess(`插件 "${plugin.name}" 已删除`)
    }
  }
}

const editPlugin = (plugin: any) => {
  showInfoModal('编辑插件', `
    <div style="padding: 20px; text-align: center;">
      <i class="fas fa-tools" style="font-size: 48px; color: #667eea; margin-bottom: 20px;"></i>
      <h3 style="margin-bottom: 15px; color: #2c3e50;">编辑功能开发中</h3>
      <p style="color: #6c757d; line-height: 1.5;">
        插件编辑功能正在开发中，敬请期待！<br>
        当前选择的插件：<strong>${plugin.name}</strong>
      </p>
    </div>
  `)
}

// 工具方法

const showInfoModal = (title: string, content: string) => {
  const modal = document.createElement('div')
  modal.className = 'modal show'
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 600px;">
      <div class="modal-header">
        <h3>${title}</h3>
        <button class="modal-close" onclick="this.closest('.modal').remove()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        ${content}
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" onclick="this.closest('.modal').remove()">确定</button>
      </div>
    </div>
  `

  document.body.appendChild(modal)

  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      modal.remove()
    }
  })
}

// 事件监听
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Escape') {
    if (showModal.value) {
      hideCustomPluginModal()
    }
  }
}

// 生命周期
onMounted(() => {
  updateTypeCounts()
  filterPlugins()

  // 添加事件监听
  document.addEventListener('keydown', handleKeyDown)

  // 添加动画样式
  const animationStyles = document.createElement('style')
  animationStyles.textContent = `
    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(100%);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes slideOutRight {
      from {
        opacity: 1;
        transform: translateX(0);
      }
      to {
        opacity: 0;
        transform: translateX(100%);
      }
    }
  `
  document.head.appendChild(animationStyles)
})

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('keydown', handleKeyDown)

  // 恢复body样式
  document.body.style.overflow = ''
})
</script>

<style scoped>
.container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background-attachment: fixed;
  min-height: 100vh;
  color: #333;
  margin: 0;
  width: 100%;
  max-width: none;
}

/* 过滤区域头部 */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
}

.page-title i {
  color: #60a5fa;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background: #e9ecef;
  color: #495057;
}

/* 过滤控件 */
.filter-controls {
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  backdrop-filter: blur(10px);
  margin-bottom: 30px;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 340px;
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 11px 15px 11px 45px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  transition: all 0.3s ease;
  height: 40px;
}

.search-box input:focus {
  outline: none;
  border-color: #93c5fd;
  box-shadow: 0 0 0 2px rgba(147, 197, 253, 0.1);
}

.search-box input::placeholder {
  color: #adb5bd;
}

/* 类型过滤器 */
.type-filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.type-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  width: 100%;
}

.type-filter-btn {
  padding: 5px 16px;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  background: #fff;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  outline: none;
  white-space: nowrap;
}

.type-filter-btn:hover {
  border-color: #93c5fd;
  color: #60a5fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(147, 197, 253, 0.15);
}

.type-filter-btn.active {
  background: #93c5fd;
  border-color: #93c5fd;
  color: white;
  box-shadow: 0 2px 8px rgba(147, 197, 253, 0.25);
}

.type-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
}

.type-filter-btn.active .type-count {
  background: rgba(255, 255, 255, 0.3);
}

.type-filter-btn:not(.active) .type-count {
  background: #f8f9fa;
  color: #6c757d;
}

/* 插件卡片网格 */
.plugins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  align-items: stretch;
  padding: 0 20px;
}

/* 插件卡片样式 */
.plugin-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.plugin-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.plugin-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.plugin-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  background: linear-gradient(135deg, #93c5fd 0%, #a5b4fc 100%);
  flex-shrink: 0;
}

.plugin-icon img {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: cover;
}

.plugin-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
  margin-top: 0;
}

.plugin-type {
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.plugin-description {
  color: #6c757d;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 60px;
}

.plugin-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
  border-top: 1px solid #f1f3f4;
  padding-top: 12px;
  margin-top: auto;
}

.plugin-version {
  background: #dbeafe;
  color: #3b82f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.plugin-code {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #6c757d;
}



/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
}

.modal.show {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 600px;
  margin: 50px auto;
  animation: modalSlideIn 0.3s ease-out;
}

.custom-plugin-modal {
  max-width: 800px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px 20px;
  border-bottom: 1px solid #f1f3f4;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 25px 30px;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 30px 25px;
  border-top: 1px solid #f1f3f4;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #93c5fd;
  box-shadow: 0 0 0 2px rgba(147, 197, 253, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* 图标上传区域 */
.icon-upload-area {
  display: flex;
  align-items: center;
  gap: 15px;
}

.icon-preview {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  background: linear-gradient(135deg, #93c5fd 0%, #a5b4fc 100%);
  border: 2px dashed #e9ecef;
  transition: all 0.3s ease;
}

.icon-preview img {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  object-fit: cover;
}

/* 标签输入容器 */
.tags-input-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 8px;
  background: #fff;
  transition: all 0.3s ease;
}

.tags-input-container:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.tag-item {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #667eea30;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-remove {
  cursor: pointer;
  font-size: 12px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.tag-remove:hover {
  opacity: 1;
}

.tags-input-container input {
  border: none;
  outline: none;
  padding: 4px 0;
  font-size: 14px;
  background: transparent;
}

/* 遮罩层 */
.overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(5px);
}

.overlay.show {
  display: block;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 15px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .filter-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .plugins-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .plugin-card {
    padding: 15px;
  }



  .modal-content {
    margin: 20px auto;
    max-width: 95%;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .icon-upload-area {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px 15px;
  }

  .page-title {
    font-size: 20px;
    gap: 8px;
  }

  .type-filters {
    justify-content: center;
  }

  .type-filter-btn {
    font-size: 12px;
    padding: 6px 10px;
  }

  .plugin-header {
    gap: 10px;
  }

  .plugin-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .plugin-info h3 {
    font-size: 16px;
  }

  .pagination-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .page-number {
    width: 32px;
    height: 32px;
    font-size: 13px;
  }
}

/* 打印样式 */
@media print {
  .filter-section {
    display: none;
  }

  .plugins-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .plugin-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* 右侧面板样式 - 浅蓝色柔和主题 */
.plugin-detail-panel {
  position: fixed;
  top: 70px;
  right: 0;
  width: 100%;
  height: calc(100% - 70px);
  z-index: 1000;
  pointer-events: none;
  transition: all 0.3s ease;
}

.plugin-detail-panel.show {
  pointer-events: auto;
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  backdrop-filter: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.plugin-detail-panel.show .panel-overlay {
  opacity: 1;
}

.panel-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 500px;
  height: 100%;
  background: #fefefe;
  box-shadow: -4px 0 20px rgba(59, 130, 246, 0.15);
  border-left: 1px solid #dbeafe;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.plugin-detail-panel.show .panel-content {
  transform: translateX(0);
}

/* 面板头部 */
.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.plugin-header-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.plugin-icon-large {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.plugin-title-info h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.plugin-meta-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plugin-type-badge {
  background: rgba(59, 130, 246, 0.15);
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.plugin-version-badge {
  background: rgba(34, 197, 94, 0.15);
  color: #166534;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.panel-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.panel-close-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #111827;
  transform: scale(1.05);
}

/* 面板主体 */
.panel-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.info-section,
.params-section,
.usage-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.section-title i {
  color: #3b82f6;
  font-size: 14px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  font-weight: 600;
  color: #4b5563;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

.info-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-description label {
  font-size: 12px;
  font-weight: 600;
  color: #4b5563;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-description p {
  margin: 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
}

/* 参数列表样式 */
.params-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.param-item {
  background: #f8fafc;
  border: 1px solid #e0f2fe;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.param-item:hover {
  border-color: #bae6fd;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.param-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.param-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 4px;
}

.required-mark {
  color: #ef4444;
  font-size: 12px;
}

.param-type {
  background: rgba(59, 130, 246, 0.15);
  color: #1e40af;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

.param-description {
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 8px;
}

.param-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.param-default,
.param-example {
  font-size: 12px;
  color: #6b7280;
  font-family: 'Courier New', monospace;
}

.param-default::before {
  content: "默认: ";
  font-family: inherit;
  color: #4b5563;
}

.param-example::before {
  content: "示例: ";
  font-family: inherit;
  color: #4b5563;
}

.no-params {
  text-align: center;
  padding: 32px 16px;
  color: #6b7280;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.no-params i {
  font-size: 24px;
  color: #9ca3af;
}

/* 使用说明样式 */
.usage-content {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
}

/* 面板底部 */
.panel-footer {
  padding: 20px 24px;
  border-top: 1px solid #dbeafe;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

.btn-panel {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  outline: none;
}

.btn-panel.btn-secondary {
  background: #f0f9ff;
  color: #374151;
  border: 1px solid #bae6fd;
}

.btn-panel.btn-secondary:hover {
  background: #dbeafe;
  color: #111827;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.15);
}

.btn-panel.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.25);
}

.btn-panel.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.35);
}

.btn-panel i {
  font-size: 11px;
}

/* 防止背景滚动 */
body.panel-open {
  overflow: hidden;
}

.pagination-container {
  position: fixed;
  z-index: 999;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding: 0 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-content {
    width: 100%;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .panel-footer {
    flex-direction: column;
    gap: 8px;
  }

  .btn-panel {
    width: 100%;
    justify-content: center;
  }
}
</style>
