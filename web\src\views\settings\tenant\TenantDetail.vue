<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="租户详情信息"
    width="900px"
    class="modern-dialog detail-dialog"
    align-center
    destroy-on-close
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="modern-dialog-header">
        <div class="flex items-center">
          <div class="dialog-icon">
            <el-icon class="text-2xl text-green-500">
              <View />
            </el-icon>
          </div>
          <div class="ml-4">
            <h3 class="dialog-title">
              租户详情信息
            </h3>
            <p class="dialog-subtitle">
              查看租户的完整信息和统计数据
            </p>
          </div>
        </div>
      </div>
    </template>

    <div class="modern-dialog-body" v-if="tenant">
      <!-- 租户概览卡片 -->
      <div class="detail-section overview-section">
        <div class="tenant-overview">
          <div class="tenant-avatar">
            <el-icon class="text-4xl text-blue-500">
              <OfficeBuilding />
            </el-icon>
          </div>
          <div class="tenant-info">
            <h2 class="tenant-name">{{ tenant.tenantName }}</h2>
            <p class="tenant-code">{{ tenant.tenantCode }}</p>
            <div class="tenant-status">
              <el-tag
                :type="tenant.status === '0' ? 'success' : tenant.status === '1' ? 'danger' : 'warning'"
                size="large"
                effect="dark"
              >
                {{ tenant.statusName }}
              </el-tag>
              <span v-if="tenant.isExpired" class="expired-badge">
                <el-icon><Clock /></el-icon>
                已过期
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 基本信息卡片 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon class="section-icon"><Key /></el-icon>
          <h4 class="section-title">基本信息</h4>
        </div>
        <div class="section-content">
          <div class="grid grid-cols-2 gap-6">
            <div class="detail-field">
              <div class="field-label">
                <el-icon><Key /></el-icon>
                租户编码
              </div>
              <div class="field-value">{{ tenant.tenantCode }}</div>
            </div>
            <div class="detail-field">
              <div class="field-label">
                <el-icon><OfficeBuilding /></el-icon>
                租户名称
              </div>
              <div class="field-value">{{ tenant.tenantName }}</div>
            </div>
            <div class="detail-field">
              <div class="field-label">
                <el-icon><OfficeBuilding /></el-icon>
                租户简称
              </div>
              <div class="field-value">{{ tenant.tenantShortName || '-' }}</div>
            </div>
            <div class="detail-field">
              <div class="field-label">
                <el-icon><Key /></el-icon>
                租户域名
              </div>
              <div class="field-value">{{ tenant.domain || '-' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系信息卡片 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon class="section-icon"><User /></el-icon>
          <h4 class="section-title">联系信息</h4>
        </div>
        <div class="section-content">
          <div class="grid grid-cols-2 gap-6">
            <div class="detail-field">
              <div class="field-label">
                <el-icon><User /></el-icon>
                联系人
              </div>
              <div class="field-value">{{ tenant.contactPerson || '-' }}</div>
            </div>
            <div class="detail-field">
              <div class="field-label">
                <el-icon><Phone /></el-icon>
                联系电话
              </div>
              <div class="field-value">{{ tenant.contactPhone || '-' }}</div>
            </div>
            <div class="detail-field col-span-2">
              <div class="field-label">
                <el-icon><Message /></el-icon>
                联系邮箱
              </div>
              <div class="field-value">{{ tenant.contactEmail || '-' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计信息卡片 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon class="section-icon"><Setting /></el-icon>
          <h4 class="section-title">统计信息</h4>
        </div>
        <div class="section-content">
          <div class="grid grid-cols-2 gap-6">
            <div class="detail-field">
              <div class="field-label">
                <el-icon><User /></el-icon>
                用户统计
              </div>
              <div class="field-value">
                <div class="stat-item">
                  <span class="stat-current">{{ tenant.currentUserCount || 0 }}</span>
                  <span class="stat-separator">/</span>
                  <span class="stat-limit">{{ tenant.userLimit || '∞' }}</span>
                </div>
              </div>
            </div>
            <div class="detail-field">
              <div class="field-label">
                <el-icon><Setting /></el-icon>
                存储统计
              </div>
              <div class="field-value">
                <div class="stat-item">
                  <span class="stat-current">{{ tenant.usedStorage || 0 }}MB</span>
                  <span class="stat-separator">/</span>
                  <span class="stat-limit">{{ tenant.storageLimit || '∞' }}MB</span>
                </div>
              </div>
            </div>
            <div class="detail-field">
              <div class="field-label">
                <el-icon><Clock /></el-icon>
                过期时间
              </div>
              <div class="field-value" :class="tenant.isExpired ? 'text-red-600' : ''">
                {{ tenant.expireTime || '永久' }}
              </div>
            </div>
            <div class="detail-field">
              <div class="field-label">
                <el-icon><Calendar /></el-icon>
                创建时间
              </div>
              <div class="field-value">{{ tenant.createTime || '-' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div v-if="tenant.remark" class="detail-section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h4 class="section-title">备注信息</h4>
        </div>
        <div class="section-content">
          <div class="remark-content">
            {{ tenant.remark }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modern-dialog-footer">
        <el-button @click="$emit('update:visible', false)" size="large">
          关闭
        </el-button>
        <el-button 
          type="primary" 
          @click="$emit('edit', tenant)" 
          size="large"
          :icon="Edit"
        >
          编辑租户
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  View,
  Key,
  OfficeBuilding,
  User,
  Phone,
  Message,
  Setting,
  Clock,
  Calendar,
  Document,
  Edit
} from '@element-plus/icons-vue'
import type { SysTenantVO } from '@/types/system'

// Props
interface Props {
  visible: boolean
  tenant?: SysTenantVO | null
}

defineProps<Props>()

// Emits
defineEmits<{
  'update:visible': [value: boolean]
  'edit': [tenant: SysTenantVO]
}>()
</script>

<style scoped>
/* 现代化对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__footer) {
  padding: 0;
}

.modern-dialog-header {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  padding: 24px;
  color: white;
}

.dialog-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
}

.dialog-subtitle {
  font-size: 14px;
  margin: 4px 0 0 0;
  opacity: 0.9;
  line-height: 1.2;
}

.modern-dialog-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.modern-dialog-footer {
  padding: 20px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 概览区域 */
.overview-section {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
}

.tenant-overview {
  display: flex;
  align-items: center;
  padding: 20px;
}

.tenant-avatar {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.tenant-info {
  flex: 1;
}

.tenant-name {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.tenant-code {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
  font-family: 'Monaco', 'Menlo', monospace;
}

.tenant-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.expired-badge {
  background: #fef2f2;
  color: #dc2626;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 详情区域 */
.detail-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.section-icon {
  color: #3b82f6;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.section-content {
  padding: 20px;
}

.detail-field {
  margin-bottom: 16px;
}

.field-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 6px;
}

.field-label .el-icon {
  margin-right: 6px;
  color: #9ca3af;
}

.field-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-current {
  color: #3b82f6;
  font-weight: 700;
}

.stat-separator {
  color: #6b7280;
  margin: 0 8px;
}

.stat-limit {
  color: #6b7280;
}

.remark-content {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
  color: #374151;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 网格布局 */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.col-span-2 {
  grid-column: span 2;
}

.gap-6 {
  gap: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .col-span-2 {
    grid-column: span 1;
  }
  
  .tenant-overview {
    flex-direction: column;
    text-align: center;
  }
  
  .tenant-avatar {
    margin-right: 0;
    margin-bottom: 16px;
  }
  
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
}
</style>
