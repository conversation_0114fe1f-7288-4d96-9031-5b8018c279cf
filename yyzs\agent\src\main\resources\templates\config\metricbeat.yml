# Metricbeat Configuration Template
# ======================== Metricbeat Configuration ===========================

# =========================== Modules configuration ============================
metricbeat.config.modules:
  # Glob pattern for configuration loading
  path: ${MODULES_PATH:${path.config}/modules.d/*.yml}
  
  # Set to true to enable config reloading
  reload.enabled: ${MODULES_RELOAD:true}
  
  # Period on which files under path should be checked for changes
  reload.period: ${MODULES_RELOAD_PERIOD:10s}

# ======================= Elasticsearch template setting =======================
setup.template.settings:
  index.number_of_shards: ${INDEX_SHARDS:1}
  index.codec: ${INDEX_CODEC:best_compression}

# ================================== General ===================================

# The name of the shipper that publishes the network data. It can be used to group
# all the transactions sent by a single shipper in the web interface.
name: ${SHIPPER_NAME:metricbeat}

# The tags of the shipper are included in their own field with each
# transaction published.
tags: [${TAGS:"metricbeat", "metrics"}]

# Optional fields that you can specify to add additional information to the
# output.
fields:
  env: ${ENVIRONMENT:production}
  service: ${SERVICE_NAME:default}

# ================================= Dashboards =================================
# These settings control loading the sample dashboards to the Kibana index. Loading
# the dashboards is disabled by default and can be enabled either by setting the
# options here or by using the `setup` command.
setup.dashboards.enabled: ${DASHBOARDS_ENABLED:false}

# =================================== Kibana ===================================
setup.kibana:
  # Kibana Host
  host: "${KIBANA_HOST:localhost:5601}"

# ================================== Outputs ===================================

# ---------------------------- Elasticsearch Output ----------------------------
output.elasticsearch:
  # Array of hosts to connect to.
  hosts: [${ELASTICSEARCH_HOSTS:"localhost:9200"}]
  
  # Protocol - either `http` (default) or `https`.
  protocol: "${ELASTICSEARCH_PROTOCOL:http}"
  
  # Authentication credentials
  username: "${ELASTICSEARCH_USERNAME:elastic}"
  password: "${ELASTICSEARCH_PASSWORD:changeme}"
  
  # Index name
  index: "${INDEX_NAME:metricbeat-%{+yyyy.MM.dd}}"

# ------------------------------ Logstash Output ----------------------------
#output.logstash:
  # The Logstash hosts
  #hosts: [${LOGSTASH_HOSTS:"localhost:5044"}]

# ================================= Processors =================================
processors:
  - add_host_metadata: ~
  - add_cloud_metadata: ~
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~

# ================================== Logging ===================================
logging.level: ${LOG_LEVEL:info}
logging.to_files: true
logging.files:
  path: ${LOG_PATH:/var/log/metricbeat}
  name: metricbeat
  keepfiles: 7
  permissions: 0644

# ============================= X-Pack Monitoring ==============================
monitoring.enabled: ${MONITORING_ENABLED:false}

# ============================== Instrumentation ===============================
#instrumentation:
    #enabled: false

# ================================ HTTP Endpoint ==============================
# Each beat can expose internal metrics through a HTTP endpoint. For security
# reasons the endpoint is disabled by default. This feature is currently experimental.
# Stats can be access through http://localhost:5066/stats . For pretty JSON output
# append ?pretty to the URL.

# Defines if the HTTP endpoint is enabled.
http.enabled: ${HTTP_ENABLED:false}
http.host: ${HTTP_HOST:localhost}
http.port: ${HTTP_PORT:5066}

# Custom configuration
${CUSTOM_CONFIG:}
