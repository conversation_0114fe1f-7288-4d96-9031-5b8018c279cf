package com.xhcai.modules.system.utils;

import com.xhcai.common.security.service.LoginUser;
import com.xhcai.modules.system.entity.SysRole;
import com.xhcai.modules.system.enums.DataScopeEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 数据权限工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DataScopeUtils {

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    /**
     * 构建数据权限SQL
     *
     * @param user 当前用户
     * @param deptAlias 部门表别名
     * @param userAlias 用户表别名
     * @return 数据权限SQL
     */
    public static String buildDataScopeSql(LoginUser user, String deptAlias, String userAlias) {
        if (user == null) {
            return "";
        }

        // 获取用户角色
        Set<String> roleKeys = user.getRoles();
        if (CollectionUtils.isEmpty(roleKeys)) {
            // 没有角色，只能查看自己的数据
            return buildSelfDataSql(user.getUserId(), userAlias);
        }

        // 超级管理员拥有全部数据权限
        if (roleKeys.contains("admin") || roleKeys.contains("ROLE_ADMIN")) {
            return "";
        }

        StringBuilder sqlString = new StringBuilder();
        
        // 这里需要查询用户的角色数据权限
        // 由于我们没有直接的角色信息，暂时根据角色名称判断
        boolean hasAllDataScope = false;
        boolean hasDeptScope = false;
        boolean hasDeptAndChildScope = false;
        boolean hasSelfScope = false;

        // 根据角色判断数据权限（这里简化处理，实际应该查询数据库）
        for (String roleKey : roleKeys) {
            if (roleKey.contains("admin")) {
                hasAllDataScope = true;
                break;
            } else if (roleKey.contains("manager")) {
                hasDeptAndChildScope = true;
            } else if (roleKey.contains("leader")) {
                hasDeptScope = true;
            } else {
                hasSelfScope = true;
            }
        }

        if (hasAllDataScope) {
            // 全部数据权限
            return "";
        } else if (hasDeptAndChildScope) {
            // 本部门及以下数据权限
            sqlString.append(buildDeptAndChildDataSql(user.getDeptId(), deptAlias));
        } else if (hasDeptScope) {
            // 本部门数据权限
            sqlString.append(buildDeptDataSql(user.getDeptId(), deptAlias));
        } else if (hasSelfScope) {
            // 仅本人数据权限
            sqlString.append(buildSelfDataSql(user.getUserId(), userAlias));
        }

        return sqlString.toString();
    }

    /**
     * 构建基于角色的数据权限SQL
     *
     * @param roles 用户角色列表
     * @param user 当前用户
     * @param deptAlias 部门表别名
     * @param userAlias 用户表别名
     * @return 数据权限SQL
     */
    public static String buildDataScopeSqlByRoles(List<SysRole> roles, LoginUser user, String deptAlias, String userAlias) {
        if (CollectionUtils.isEmpty(roles) || user == null) {
            return buildSelfDataSql(user != null ? user.getUserId() : null, userAlias);
        }

        StringBuilder sqlString = new StringBuilder();
        boolean hasCondition = false;

        for (SysRole role : roles) {
            String dataScope = role.getDataScope();
            if (!StringUtils.hasText(dataScope)) {
                continue;
            }

            DataScopeEnum dataScopeEnum = DataScopeEnum.getByCode(dataScope);
            if (dataScopeEnum == null) {
                continue;
            }

            if (hasCondition) {
                sqlString.append(" OR ");
            }

            switch (dataScopeEnum) {
                case ALL:
                    // 全部数据权限，直接返回空字符串
                    return "";
                case CUSTOM:
                    // 自定数据权限，需要查询角色关联的部门
                    sqlString.append(buildCustomDataSql(role.getId(), deptAlias));
                    break;
                case DEPT:
                    // 本部门数据权限
                    sqlString.append(buildDeptDataSql(user.getDeptId(), deptAlias));
                    break;
                case DEPT_AND_CHILD:
                    // 本部门及以下数据权限
                    sqlString.append(buildDeptAndChildDataSql(user.getDeptId(), deptAlias));
                    break;
                case SELF:
                    // 仅本人数据权限
                    sqlString.append(buildSelfDataSql(user.getUserId(), userAlias));
                    break;
            }
            hasCondition = true;
        }

        if (sqlString.length() > 0) {
            return " AND (" + sqlString.toString() + ")";
        }

        // 默认只能查看自己的数据
        return buildSelfDataSql(user.getUserId(), userAlias);
    }

    /**
     * 构建全部数据权限SQL
     */
    private static String buildAllDataSql() {
        return "";
    }

    /**
     * 构建自定数据权限SQL
     */
    private static String buildCustomDataSql(String roleId, String deptAlias) {
        if (roleId == null) {
            return "1=0";
        }
        
        String alias = StringUtils.hasText(deptAlias) ? deptAlias + "." : "";
        return alias + "dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id = '" + roleId + "')";
    }

    /**
     * 构建本部门数据权限SQL
     */
    private static String buildDeptDataSql(String deptId, String deptAlias) {
        if (deptId == null) {
            return "1=0";
        }
        
        String alias = StringUtils.hasText(deptAlias) ? deptAlias + "." : "";
        return alias + "dept_id = '" + deptId + "' ";
    }

    /**
     * 构建本部门及以下数据权限SQL
     */
    private static String buildDeptAndChildDataSql(String deptId, String deptAlias) {
        if (deptId == null) {
            return "1=0";
        }
        
        String alias = StringUtils.hasText(deptAlias) ? deptAlias + "." : "";
        return alias + "dept_id IN (SELECT id FROM sys_dept WHERE id = " + deptId + 
               " OR FIND_IN_SET(" + deptId + ", ancestors))";
    }

    /**
     * 构建仅本人数据权限SQL
     */
    private static String buildSelfDataSql(String userId, String userAlias) {
        if (userId == null) {
            return "1=0";
        }
        
        String alias = StringUtils.hasText(userAlias) ? userAlias + "." : "";
        return " AND " + alias + "create_by = '" + userId + "'";
    }

    /**
     * 检查是否有数据权限
     */
    public static boolean hasDataScope(LoginUser user, String permission) {
        if (user == null) {
            return false;
        }

        // 超级管理员拥有所有权限
        Set<String> roles = user.getRoles();
        if (!CollectionUtils.isEmpty(roles) && 
            (roles.contains("admin") || roles.contains("ROLE_ADMIN"))) {
            return true;
        }

        // 检查是否有指定权限
        Set<String> permissions = user.getPermissions();
        return !CollectionUtils.isEmpty(permissions) && permissions.contains(permission);
    }
}
