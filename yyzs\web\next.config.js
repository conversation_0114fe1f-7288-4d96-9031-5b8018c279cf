/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // API代理配置
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:8080/api/:path*',
      },
    ];
  },

  // 环境变量配置
  env: {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
    NEXT_PUBLIC_APP_NAME: 'YYZS Agent Platform',
    NEXT_PUBLIC_APP_VERSION: '1.0.0',
  },

  // 图片优化配置
  images: {
    domains: ['localhost'],
    unoptimized: true,
  },

  // 输出配置
  output: 'standalone',
  
  // 实验性功能
  experimental: {
    appDir: true,
  },
};

module.exports = nextConfig;
