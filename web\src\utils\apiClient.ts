/**
 * API客户端基础类
 * 统一处理HTTP请求、响应拦截、错误处理等
 */

import { envConfig, logger } from '@/config/env'
import SSEClient from './sseClient'
import type {
  ApiResponse,
  RequestConfig,
  ApiError,
  RequestInterceptor,
  ResponseInterceptor,
  ErrorInterceptor,
  SSEConfig
} from '@/types/api'

export class ApiClient {
  private baseURL: string
  private timeout: number
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private errorInterceptors: ErrorInterceptor[] = []

  constructor(baseURL?: string, timeout?: number) {
    this.baseURL = baseURL || envConfig.apiBaseUrl
    this.timeout = timeout || envConfig.apiTimeout
  }

  /**
   * 获取基础URL
   */
  getBaseURL(): string {
    return this.baseURL
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor) {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor) {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: ErrorInterceptor) {
    this.errorInterceptors.push(interceptor)
  }

  /**
   * 处理请求配置
   */
  private async processRequestConfig(config: RequestConfig): Promise<RequestConfig> {
    let processedConfig = { ...config }
    
    // 应用请求拦截器
    for (const interceptor of this.requestInterceptors) {
      processedConfig = await interceptor(processedConfig)
    }
    
    return processedConfig
  }

  /**
   * 处理响应数据
   */
  private async processResponse<T>(response: ApiResponse<T>): Promise<ApiResponse<T>> {
    let processedResponse = response
    
    // 应用响应拦截器
    for (const interceptor of this.responseInterceptors) {
      processedResponse = await interceptor(processedResponse)
    }
    
    return processedResponse
  }

  /**
   * 处理错误
   */
  private async processError(error: ApiError): Promise<never> {
    let processedError = error
    
    // 应用错误拦截器
    for (const interceptor of this.errorInterceptors) {
      try {
        processedError = await interceptor(processedError)
      } catch (e) {
        // 如果拦截器抛出错误，继续抛出
        throw e
      }
    }
    
    throw processedError
  }

  /**
   * 构建完整URL
   */
  private buildURL(url: string, params?: Record<string, any>): string {
    const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`

    if (!params || Object.keys(params).length === 0) {
      return fullURL
    }

    const urlParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        urlParams.append(key, String(value))
      }
    })

    const separator = fullURL.includes('?') ? '&' : '?'
    return `${fullURL}${separator}${urlParams.toString()}`
  }

  /**
   * 使用XMLHttpRequest发送带进度回调的请求
   */
  private async requestWithProgress<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()

      // 处理请求体和headers
      const { body, headers } = this.processRequestBody(config)

      // 构建URL
      const url = this.buildURL(config.url, config.method === 'GET' ? config.params : undefined)

      // 设置进度回调
      if (config.onUploadProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100)
            config.onUploadProgress!(progress)
          }
        })
      }

      // 设置完成回调
      xhr.addEventListener('load', async () => {
        try {
          let responseData: ApiResponse<T>
          const contentType = xhr.getResponseHeader('content-type')

          if (contentType && contentType.includes('application/json')) {
            responseData = JSON.parse(xhr.responseText)
          } else {
            // 非JSON响应，构造标准响应格式
            responseData = {
              code: xhr.status,
              message: xhr.status >= 200 && xhr.status < 300 ? 'Success' : 'Request failed',
              data: xhr.responseText as any,
              success: xhr.status >= 200 && xhr.status < 300,
              fail: xhr.status < 200 || xhr.status >= 300,
              timestamp: new Date().toISOString(),
              path: config.url
            }
          }

          if (xhr.status >= 200 && xhr.status < 300) {
            // 应用响应拦截器
            let processedResponse = responseData
            for (const interceptor of this.responseInterceptors) {
              processedResponse = await interceptor(processedResponse)
            }
            resolve(processedResponse)
          } else {
            const error = {
              code: responseData.code || xhr.status,
              msg: responseData.message || 'Request failed',
              details: responseData.data,
              timestamp: Date.now()
            }

            // 应用错误拦截器
            let processedError = error
            for (const interceptor of this.errorInterceptors) {
              try {
                processedError = await interceptor(processedError)
              } catch (e) {
                reject(e)
                return
              }
            }
            reject(new Error(processedError.msg))
          }
        } catch (error) {
          reject(new Error('响应解析失败'))
        }
      })

      // 设置错误回调
      xhr.addEventListener('error', () => {
        reject(new Error('网络错误'))
      })

      // 设置超时回调
      xhr.addEventListener('timeout', () => {
        reject(new Error('请求超时'))
      })

      // 配置请求
      xhr.open(config.method || 'GET', url)
      xhr.timeout = config.timeout || this.timeout

      // 设置请求头
      Object.entries(headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value)
      })

      // 发送请求
      xhr.send(body)
    })
  }

  /**
   * 处理请求体和Content-Type
   */
  private processRequestBody(config: RequestConfig): { body?: BodyInit; headers: Record<string, string> } {
    const headers: Record<string, string> = { ...config.headers }
    let body: BodyInit | undefined

    // 如果有FormData，直接使用
    if (config.formData) {
      body = config.formData
      // FormData会自动设置Content-Type，包含boundary
      // 不要手动设置Content-Type
    } else if (config.data && ['POST', 'PUT', 'PATCH'].includes(config.method || 'GET')) {
      // 根据contentType处理数据
      switch (config.contentType) {
        case 'form':
          // application/x-www-form-urlencoded
          headers['Content-Type'] = 'application/x-www-form-urlencoded'
          if (typeof config.data === 'object') {
            const formData = new URLSearchParams()
            Object.entries(config.data).forEach(([key, value]) => {
              if (value !== null && value !== undefined) {
                formData.append(key, String(value))
              }
            })
            body = formData.toString()
          } else {
            body = String(config.data)
          }
          break

        case 'multipart':
          // multipart/form-data
          const multipartData = new FormData()
          if (typeof config.data === 'object') {
            Object.entries(config.data).forEach(([key, value]) => {
              if (value !== null && value !== undefined) {
                if (value instanceof File || value instanceof Blob) {
                  multipartData.append(key, value)
                } else {
                  multipartData.append(key, String(value))
                }
              }
            })
          }
          body = multipartData
          // FormData会自动设置Content-Type
          break

        case 'text':
          headers['Content-Type'] = 'text/plain'
          body = String(config.data)
          break

        case 'json':
        default:
          headers['Content-Type'] = 'application/json'
          body = JSON.stringify(config.data)
          break
      }
    }

    return { body, headers }
  }

  /**
   * 发送HTTP请求
   */
  async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    try {
      // 处理请求配置
      const processedConfig = await this.processRequestConfig(config)

      logger.debug('API Request:', processedConfig)

      // 如果有上传进度回调，使用XMLHttpRequest
      if (processedConfig.onUploadProgress && ['POST', 'PUT', 'PATCH'].includes(processedConfig.method || 'GET')) {
        return this.requestWithProgress<T>(processedConfig)
      }

      // 处理请求体和headers
      const { body, headers } = this.processRequestBody(processedConfig)

      // 构建请求选项
      const requestOptions: RequestInit = {
        method: processedConfig.method || 'GET',
        headers,
        signal: AbortSignal.timeout(processedConfig.timeout || this.timeout),
        body
      }

      // 构建URL
      const url = this.buildURL(
        processedConfig.url,
        requestOptions.method === 'GET' ? processedConfig.params : undefined
      )

      // 发送请求
      const response = await fetch(url, requestOptions)
      
      // 解析响应
      let responseData: ApiResponse<T>
      const contentType = response.headers.get('content-type')
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json()
      } else {
        // 非JSON响应，构造标准响应格式
        const text = await response.text()
        responseData = {
          code: response.status,
          message: response.statusText,
          data: text as T,
          success: response.ok
        }
      }
      
      logger.debug('API Response:', responseData)
      
      // 检查HTTP状态
      if (!response.ok) {
        const error: ApiError = {
          code: response.status,
          msg: responseData.message || response.statusText,
          details: responseData
        }
        return this.processError(error)
      }

      // 检查业务状态
      if (!responseData.success && responseData.code !== 200) {
        const error: ApiError = {
          code: responseData.code,
          msg: responseData.message,
          details: responseData
        }
        return this.processError(error)
      }
      
      // 处理成功响应
      return this.processResponse(responseData)
      
    } catch (error) {
      logger.error('API Request Error:', error)
      
      // 构造错误对象
      const apiError: ApiError = {
        code: 0,
        msg: error instanceof Error ? error.message : '网络请求失败',
        details: error
      }
      
      return this.processError(apiError)
    }
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, params?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'GET',
      params,
      ...config
    })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      ...config
    })
  }

  /**
   * PATCH请求
   */
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PATCH',
      data,
      ...config
    })
  }

  /**
   * 发送表单数据 (application/x-www-form-urlencoded)
   */
  postForm<T = any>(url: string, data?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      contentType: 'form',
      ...config
    })
  }

  /**
   * 发送多部分表单数据 (multipart/form-data)
   */
  postMultipart<T = any>(url: string, data?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      contentType: 'multipart',
      ...config
    })
  }

  /**
   * 直接发送FormData对象
   */
  postFormData<T = any>(url: string, formData: FormData, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      formData,
      ...config
    })
  }

  /**
   * PUT表单数据 (application/x-www-form-urlencoded)
   */
  putForm<T = any>(url: string, data?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      contentType: 'form',
      ...config
    })
  }

  /**
   * PUT多部分表单数据 (multipart/form-data)
   */
  putMultipart<T = any>(url: string, data?: Record<string, any>, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      contentType: 'multipart',
      ...config
    })
  }

  /**
   * PUT FormData对象
   */
  putFormData<T = any>(url: string, formData: FormData, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      formData,
      ...config
    })
  }

  /**
   * 创建SSE连接
   * 支持token认证
   */
  createSSE(url: string, config?: Partial<SSEConfig>): SSEClient {
    const sseConfig: SSEConfig = {
      url,
      skipAuth: false, // 默认需要认证
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      withCredentials: false,
      ...config
    }

    return new SSEClient(sseConfig)
  }

  /**
   * 创建不需要认证的SSE连接
   */
  createPublicSSE(url: string, config?: Partial<SSEConfig>): SSEClient {
    return this.createSSE(url, {
      ...config,
      skipAuth: true
    })
  }
}

// 创建默认实例
export const apiClient = new ApiClient()

export default apiClient
