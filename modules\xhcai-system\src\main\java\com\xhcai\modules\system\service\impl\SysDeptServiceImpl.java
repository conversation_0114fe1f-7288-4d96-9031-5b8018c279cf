package com.xhcai.modules.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.system.dto.SysDeptQueryDTO;
import com.xhcai.modules.system.entity.SysDept;
import com.xhcai.modules.system.mapper.SysDeptMapper;
import com.xhcai.modules.system.service.ISysDeptService;
import com.xhcai.modules.system.service.ISystemQueryService;
import com.xhcai.modules.system.utils.DictUtils;
import com.xhcai.modules.system.vo.SysDeptVO;

/**
 * 部门信息服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements ISysDeptService {

    private static final Logger log = LoggerFactory.getLogger(SysDeptServiceImpl.class);

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private ISystemQueryService systemQueryService;

    @Override
    public List<SysDeptVO> selectDeptList(SysDeptQueryDTO queryDTO) {
        List<SysDept> depts = deptMapper.selectDeptList(
                queryDTO.getDeptName(),
                queryDTO.getDeptCode(),
                queryDTO.getLeaderId(),
                queryDTO.getStatus(),
                queryDTO.getParentId()
        );

        return depts.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysDeptVO> selectDeptTree(SysDeptQueryDTO queryDTO) {
        List<SysDept> depts = deptMapper.selectDeptTree(queryDTO.getStatus());
        return buildDeptTree(depts);
    }

    @Override
    public SysDeptVO selectDeptById(String deptId) {
        if (deptId == null) {
            return null;
        }

        SysDept dept = getById(deptId);
        if (dept == null) {
            return null;
        }

        SysDeptVO deptVO = convertToVO(dept);

        // 设置父部门名称
        String parentId = dept.getParentId();
        if (parentId == null || parentId.trim().isEmpty()) {
            parentId = CommonConstants.DEPT_TREE_ROOT_ID;
        }
        if (!CommonConstants.DEPT_TREE_ROOT_ID.equals(parentId)) {
            SysDept parentDept = getById(parentId);
            if (parentDept != null) {
                deptVO.setParentName(parentDept.getDeptName());
            }
        }

        // 设置用户数量
        Integer userCount = deptMapper.countUsersByDeptId(deptId);
        deptVO.setUserCount(userCount);

        // 设置是否有子部门
        Integer childrenCount = deptMapper.hasChildren(deptId);
        deptVO.setHasChildren(childrenCount > 0);

        return deptVO;
    }

    @Override
    public SysDept selectByDeptCode(String deptCode) {
        if (!StringUtils.hasText(deptCode)) {
            return null;
        }
        return deptMapper.selectByDeptCode(deptCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDept(SysDept dept) {
        // 参数校验
        validateDept(dept, true);

        // 检查部门编码是否已存在
        if (StringUtils.hasText(dept.getDeptCode()) && existsDeptCode(dept.getDeptCode(), null)) {
            throw new BusinessException("部门编码已存在");
        }

        // 检查同级部门名称是否已存在
        if (existsDeptName(dept.getDeptName(), dept.getParentId(), null)) {
            throw new BusinessException("同级部门名称已存在");
        }

        // 设置祖级列表
        setDeptAncestors(dept);

        // 设置排序号
        if (dept.getOrderNum() == null) {
            Integer maxOrderNum = deptMapper.selectMaxOrderNum(dept.getParentId());
            dept.setOrderNum(maxOrderNum + 1);
        }

        // 设置默认状态
        if (!StringUtils.hasText(dept.getStatus())) {
            dept.setStatus(CommonConstants.STATUS_NORMAL);
        }

        return save(dept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDept(SysDept dept) {
        // 参数校验
        validateDept(dept, false);

        // 检查部门是否存在
        SysDept existDept = getById(dept.getId());
        if (existDept == null) {
            throw new BusinessException("部门不存在");
        }

        // 检查部门编码是否已存在（排除自己）
        if (StringUtils.hasText(dept.getDeptCode()) && existsDeptCode(dept.getDeptCode(), dept.getId())) {
            throw new BusinessException("部门编码已存在");
        }

        // 检查同级部门名称是否已存在（排除自己）
        if (existsDeptName(dept.getDeptName(), dept.getParentId(), dept.getId())) {
            throw new BusinessException("同级部门名称已存在");
        }

        // 如果父部门发生变化，需要更新祖级列表
        if (!existDept.getParentId().equals(dept.getParentId())) {
            // 检查是否将部门移动到自己的子部门下
            if (isChildDept(dept.getId(), dept.getParentId())) {
                throw new BusinessException("不能将部门移动到自己的子部门下");
            }

            // 更新祖级列表
            String oldAncestors = existDept.getAncestors();
            setDeptAncestors(dept);
            String newAncestors = dept.getAncestors();

            // 更新子部门的祖级列表
            deptMapper.updateChildrenAncestors(dept.getId(), newAncestors, oldAncestors);
        }

        return updateById(dept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDepts(List<String> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return false;
        }

        for (String deptId : deptIds) {
            // 检查是否存在子部门
            if (hasChildren(deptId)) {
                SysDept dept = getById(deptId);
                throw new BusinessException("部门【" + dept.getDeptName() + "】存在子部门，不能删除");
            }

            // 检查部门下是否有用户
            if (hasUsers(deptId)) {
                SysDept dept = getById(deptId);
                throw new BusinessException("部门【" + dept.getDeptName() + "】下存在用户，不能删除");
            }
        }

        return removeByIds(deptIds);
    }

    @Override
    public boolean existsDeptCode(String deptCode, String excludeId) {
        if (!StringUtils.hasText(deptCode)) {
            return false;
        }
        return deptMapper.existsDeptCode(deptCode, excludeId) > 0;
    }

    @Override
    public boolean existsDeptName(String deptName, String parentId, String excludeId) {
        if (!StringUtils.hasText(deptName)) {
            return false;
        }
        return deptMapper.existsDeptName(deptName, parentId, excludeId) > 0;
    }

    @Override
    public boolean hasChildren(String deptId) {
        if (deptId == null) {
            return false;
        }
        return deptMapper.hasChildren(deptId) > 0;
    }

    @Override
    public boolean hasUsers(String deptId) {
        if (deptId == null) {
            return false;
        }
        return deptMapper.countUsersByDeptId(deptId) > 0;
    }

    @Override
    public List<SysDeptVO> selectChildrenByParentId(String parentId) {
        if (parentId == null) {
            return new ArrayList<>();
        }

        List<SysDept> children = deptMapper.selectChildrenByParentId(parentId);
        return children.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysDeptVO> selectDeptAndChildren(String deptId) {
        if (deptId == null) {
            return new ArrayList<>();
        }

        List<SysDept> depts = deptMapper.selectDeptAndChildren(deptId);
        return depts.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean moveDept(String deptId, String newParentId) {
        if (deptId == null || newParentId == null) {
            throw new BusinessException("部门ID和新父部门ID不能为空");
        }

        SysDept dept = getById(deptId);
        if (dept == null) {
            throw new BusinessException("部门不存在");
        }

        // 检查是否将部门移动到自己的子部门下
        if (isChildDept(deptId, newParentId)) {
            throw new BusinessException("不能将部门移动到自己的子部门下");
        }

        // 更新父部门
        String oldAncestors = dept.getAncestors();
        dept.setParentId(newParentId);
        setDeptAncestors(dept);
        String newAncestors = dept.getAncestors();

        // 更新部门信息
        boolean result = updateById(dept);

        if (result) {
            // 更新子部门的祖级列表
            deptMapper.updateChildrenAncestors(deptId, newAncestors, oldAncestors);
        }

        return result;
    }

    /**
     * 参数校验
     */
    private void validateDept(SysDept dept, boolean isCreate) {
        if (dept == null) {
            throw new BusinessException("部门信息不能为空");
        }

        if (!StringUtils.hasText(dept.getDeptName())) {
            throw new BusinessException("部门名称不能为空");
        }

        if (dept.getParentId() == null || dept.getParentId().trim().isEmpty()) {
            dept.setParentId(CommonConstants.DEPT_TREE_ROOT_ID);
        }

        // 其他校验逻辑...
    }

    /**
     * 设置部门祖级列表
     */
    private void setDeptAncestors(SysDept dept) {
        String parentId = dept.getParentId();
        if (parentId == null || parentId.trim().isEmpty()) {
            parentId = CommonConstants.DEPT_TREE_ROOT_ID;
            dept.setParentId(parentId);
        }

        if (CommonConstants.DEPT_TREE_ROOT_ID.equals(parentId)) {
            dept.setAncestors("0");
        } else {
            SysDept parentDept = getById(parentId);
            if (parentDept != null) {
                dept.setAncestors(parentDept.getAncestors() + "," + parentId);
            } else {
                dept.setAncestors("0");
            }
        }
    }

    /**
     * 检查是否是子部门
     */
    private boolean isChildDept(String deptId, String parentId) {
        if (deptId == null || parentId == null) {
            return false;
        }

        SysDept parentDept = getById(parentId);
        if (parentDept == null) {
            return false;
        }

        return parentDept.getAncestors().contains(deptId.toString()) || deptId.equals(parentId);
    }

    /**
     * 转换为VO
     */
    private SysDeptVO convertToVO(SysDept dept) {
        if (dept == null) {
            return null;
        }

        SysDeptVO vo = new SysDeptVO();
        BeanUtils.copyProperties(dept, vo);

        // 处理parentId为空字符串的情况
        if (vo.getParentId() == null || vo.getParentId().trim().isEmpty()) {
            vo.setParentId(CommonConstants.DEPT_TREE_ROOT_ID);
        }

        // 设置状态名称 - 使用字典工具类
        vo.setStatusName(DictUtils.getDeptStatusLabel(dept.getStatus()));

        // 设置层级
        if (StringUtils.hasText(dept.getAncestors())) {
            vo.setLevel(dept.getAncestors().split(",").length);
        } else {
            vo.setLevel(1);
        }

        // 设置负责人姓名 - 使用系统查询服务
        if (StringUtils.hasText(dept.getLeaderId())) {
            String leaderName = systemQueryService.getUserNameById(dept.getLeaderId());
            vo.setLeaderName(leaderName);
        }

        // 设置用户数量统计 - 使用系统查询服务
        Integer userCount = systemQueryService.countUsersByDeptId(dept.getId());
        vo.setUserCount(userCount);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableDept(String deptId) {
        return updateDeptStatus(deptId, CommonConstants.STATUS_NORMAL);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableDept(String deptId) {
        return updateDeptStatus(deptId, CommonConstants.STATUS_DISABLE);
    }

    @Override
    public Integer getDeptUserCount(String deptId) {
        if (deptId == null) {
            return 0;
        }
        return deptMapper.countUsersByDeptId(deptId);
    }

    @Override
    public List<SysDeptVO> buildDeptTree(List<SysDept> depts) {
        if (CollectionUtils.isEmpty(depts)) {
            return new ArrayList<>();
        }

        List<SysDeptVO> deptVOs = depts.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return buildTree(deptVOs, CommonConstants.DEPT_TREE_ROOT_ID);
    }

    @Override
    public List<SysDeptVO> buildDeptSelectTree(String excludeDeptId) {
        List<SysDept> allDepts = deptMapper.selectDeptTree(CommonConstants.STATUS_NORMAL);

        if (excludeDeptId != null) {
            // 排除指定部门及其子部门
            allDepts = allDepts.stream()
                    .filter(dept -> !dept.getId().equals(excludeDeptId)
                    && !dept.getAncestors().contains(excludeDeptId))
                    .collect(Collectors.toList());
        }

        return buildDeptTree(allDepts);
    }

    @Override
    public String getDeptPath(String deptId) {
        if (deptId == null) {
            return "";
        }

        SysDept dept = getById(deptId);
        if (dept == null) {
            return "";
        }

        if (StringUtils.hasText(dept.getAncestors()) && !"0".equals(dept.getAncestors())) {
            List<SysDept> ancestorDepts = deptMapper.selectByAncestors(dept.getAncestors());
            StringBuilder path = new StringBuilder();

            for (SysDept ancestorDept : ancestorDepts) {
                if (!CommonConstants.DEPT_TREE_ROOT_ID.equals(ancestorDept.getId())) {
                    path.append(ancestorDept.getDeptName()).append("/");
                }
            }
            path.append(dept.getDeptName());

            return path.toString();
        } else {
            return dept.getDeptName();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> deptIds, String status) {
        if (CollectionUtils.isEmpty(deptIds) || !StringUtils.hasText(status)) {
            return false;
        }

        return lambdaUpdate()
                .in(SysDept::getId, deptIds)
                .set(SysDept::getStatus, status)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncDeptOrder(List<SysDept> depts) {
        if (CollectionUtils.isEmpty(depts)) {
            return false;
        }

        for (SysDept dept : depts) {
            if (dept.getId() != null && dept.getOrderNum() != null) {
                lambdaUpdate()
                        .eq(SysDept::getId, dept.getId())
                        .set(SysDept::getOrderNum, dept.getOrderNum())
                        .update();
            }
        }

        return true;
    }

    /**
     * 更新部门状态
     */
    private boolean updateDeptStatus(String deptId, String status) {
        if (deptId == null || !StringUtils.hasText(status)) {
            throw new BusinessException("部门ID和状态不能为空");
        }

        SysDept dept = getById(deptId);
        if (dept == null) {
            throw new BusinessException("部门不存在");
        }

        dept.setStatus(status);
        return updateById(dept);
    }

    /**
     * 构建树形结构
     */
    private List<SysDeptVO> buildTree(List<SysDeptVO> depts, String parentId) {
        List<SysDeptVO> tree = new ArrayList<>();

        for (SysDeptVO dept : depts) {
            // 处理parentId为空字符串或null的情况
            String deptParentId = dept.getParentId();
            if (deptParentId == null || deptParentId.trim().isEmpty()) {
                deptParentId = CommonConstants.DEPT_TREE_ROOT_ID;
            }

            if (parentId.equals(deptParentId)) {
                List<SysDeptVO> children = buildTree(depts, dept.getId());
                dept.setChildren(children);
                dept.setHasChildren(!children.isEmpty());
                tree.add(dept);
            }
        }

        return tree;
    }
}
