package com.xhcai.modules.dify.service;

import com.xhcai.modules.dify.config.DifyWebClientConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Dify 原始流服务
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Service
public class DifyRawStreamService {

    private static final Logger log = LoggerFactory.getLogger(DifyRawStreamService.class);

    @Autowired
    private DifyWebClientConfig difyWebClientConfig;

    /**
     * 创建 SSE 原始流
     * 
     * @param query 查询内容
     * @param user 用户标识
     * @param conversationId 会话ID
     * @param apiKey API密钥
     * @return SSE发射器
     */
    public SseEmitter createRawSseStream(String query, String user, String conversationId, String apiKey) {
        log.info("创建原始SSE流: query={}, user={}, conversationId={}", query, user, conversationId);
        
        SseEmitter emitter = new SseEmitter(120000L); // 2分钟超时
        
        // 添加连接状态跟踪
        final boolean[] isCompleted = {false};
        
        // 设置超时和完成回调
        emitter.onTimeout(() -> {
            log.info("原始SSE连接超时");
            isCompleted[0] = true;
        });
        
        emitter.onCompletion(() -> {
            log.info("原始SSE连接完成");
            isCompleted[0] = true;
        });
        
        emitter.onError((throwable) -> {
            log.error("原始SSE连接错误", throwable);
            isCompleted[0] = true;
        });
        
        CompletableFuture.runAsync(() -> {
            try {
                // 发送开始事件
                if (!isCompleted[0]) {
                    emitter.send(SseEmitter.event()
                            .data("开始原始SSE流...")
                            .name("start"));
                }

                // 构建请求体
                Map<String, Object> requestBody = difyWebClientConfig.createChatRequestBody(query, user, conversationId);

                // 调用原始 SSE 流
                difyWebClientConfig.callDifyRawSseStream(requestBody, apiKey)
                        .doOnNext(line -> {
                            try {
                                if (!isCompleted[0]) {
                                    log.debug("收到原始Dify数据: {}", line);
                                    // 直接转发原始数据
                                    emitter.send(SseEmitter.event()
                                            .data(line)
                                            .name("raw"));
                                } else {
                                    log.debug("SSE连接已完成，跳过数据发送: {}", line);
                                }
                            } catch (IOException e) {
                                log.error("发送原始SSE数据失败", e);
                                if (!isCompleted[0]) {
                                    isCompleted[0] = true;
                                    emitter.completeWithError(e);
                                }
                            } catch (IllegalStateException e) {
                                log.warn("SSE连接已完成，无法发送数据: {}", e.getMessage());
                                isCompleted[0] = true;
                            }
                        })
                        .doOnComplete(() -> {
                            try {
                                if (!isCompleted[0]) {
                                    log.info("原始SSE流完成");
                                    emitter.send(SseEmitter.event()
                                            .data("原始流式测试完成")
                                            .name("complete"));
                                    isCompleted[0] = true;
                                    emitter.complete();
                                } else {
                                    log.debug("SSE连接已完成，跳过完成事件发送");
                                }
                            } catch (IOException e) {
                                log.error("完成SSE流失败", e);
                                if (!isCompleted[0]) {
                                    isCompleted[0] = true;
                                    emitter.completeWithError(e);
                                }
                            } catch (IllegalStateException e) {
                                log.warn("SSE连接已完成，无法发送完成事件: {}", e.getMessage());
                                isCompleted[0] = true;
                            }
                        })
                        .doOnError(error -> {
                            log.error("原始SSE流处理失败", error);
                            try {
                                if (!isCompleted[0]) {
                                    emitter.send(SseEmitter.event()
                                            .data("错误: " + error.getMessage())
                                            .name("error"));
                                    isCompleted[0] = true;
                                    emitter.complete();
                                } else {
                                    log.debug("SSE连接已完成，跳过错误事件发送");
                                }
                            } catch (IOException e) {
                                if (!isCompleted[0]) {
                                    isCompleted[0] = true;
                                    emitter.completeWithError(error);
                                }
                            } catch (IllegalStateException e) {
                                log.warn("SSE连接已完成，无法发送错误事件: {}", e.getMessage());
                                isCompleted[0] = true;
                            }
                        })
                        .subscribe();

            } catch (Exception e) {
                log.error("原始SSE流启动失败", e);
                if (!isCompleted[0]) {
                    isCompleted[0] = true;
                    emitter.completeWithError(e);
                }
            }
        });

        return emitter;
    }



    /**
     * 获取原始 SSE 数据流（不包装为 SSE）
     * 
     * @param query 查询内容
     * @param user 用户标识
     * @param conversationId 会话ID
     * @param apiKey API密钥
     * @return 原始数据流
     */
    public Flux<String> getRawDataStream(String query, String user, String conversationId, String apiKey) {
        log.info("获取原始数据流: query={}, user={}, conversationId={}", query, user, conversationId);
        
        Map<String, Object> requestBody = difyWebClientConfig.createChatRequestBody(query, user, conversationId);
        return difyWebClientConfig.callDifyRawSseStream(requestBody, apiKey);
    }



    /**
     * 使用请求体 Map 创建 SSE 原始流
     *
     * @param requestBody 请求体 Map
     * @param apiKey API密钥
     * @return SSE发射器
     */
    public SseEmitter createRawSseStreamFromRequestMap(Map<String, Object> requestBody, String apiKey) {
        log.info("使用请求体Map创建原始SSE流: {}", requestBody);
        return createRawSseStreamFromRequestBody(requestBody, apiKey);
    }



    /**
     * 使用请求体 Map 获取原始数据流
     *
     * @param requestBody 请求体 Map
     * @param apiKey API密钥
     * @return 原始数据流
     */
    public Flux<String> getRawDataStreamFromRequestMap(Map<String, Object> requestBody, String apiKey) {
        log.info("使用请求体Map获取原始数据流: {}", requestBody);
        return difyWebClientConfig.callDifyRawSseStream(requestBody, apiKey);
    }

    /**
     * 使用请求体 Map 获取原始数据流（带认证）
     *
     * @param requestBody 请求体 Map
     * @return 原始数据流
     */
    public Flux<String> getRawDataStreamFromRequestMapWithAuth(Map<String, Object> requestBody) {
        log.info("使用请求体Map获取原始数据流（带认证）: {}", requestBody);
        return difyWebClientConfig.callDifyRawSseStreamWithAuth(requestBody);
    }

    /**
     * 使用 Console API 获取原始数据流（带认证）
     *
     * @param appId 应用ID
     * @param requestBody 请求体 Map
     * @return 原始数据流
     */
    public Flux<String> getRawDataStreamFromConsoleApi(String appId, Map<String, Object> requestBody) {
        log.info("使用 Console API 获取原始数据流: appId={}, requestBody={}", appId, requestBody);
        return difyWebClientConfig.callDifyConsoleApiWithAuth(appId, requestBody);
    }



    /**
     * 使用请求体创建 SSE 原始流
     *
     * @param requestBody 请求体
     * @param apiKey API密钥
     * @return SSE发射器
     */
    private SseEmitter createRawSseStreamFromRequestBody(Map<String, Object> requestBody, String apiKey) {
        SseEmitter emitter = new SseEmitter(120000L); // 2分钟超时

        // 添加连接状态跟踪
        final boolean[] isCompleted = {false};

        // 设置超时和完成回调
        emitter.onTimeout(() -> {
            log.info("原始SSE连接超时");
            isCompleted[0] = true;
        });

        emitter.onCompletion(() -> {
            log.info("原始SSE连接完成");
            isCompleted[0] = true;
        });

        emitter.onError((throwable) -> {
            log.error("原始SSE连接错误", throwable);
            isCompleted[0] = true;
        });

        CompletableFuture.runAsync(() -> {
            try {
                // 发送开始事件
                if (!isCompleted[0]) {
                    emitter.send(SseEmitter.event()
                            .data("开始原始SSE流...")
                            .name("start"));
                }

                // 调用原始 SSE 流
                difyWebClientConfig.callDifyRawSseStream(requestBody, apiKey)
                        .doOnNext(line -> {
                            try {
                                if (!isCompleted[0]) {
                                    log.debug("收到原始Dify数据: {}", line);
                                    // 直接转发原始数据
                                    emitter.send(SseEmitter.event()
                                            .data(line)
                                            .name("raw"));
                                } else {
                                    log.debug("SSE连接已完成，跳过数据发送: {}", line);
                                }
                            } catch (IOException e) {
                                log.error("发送原始SSE数据失败", e);
                                if (!isCompleted[0]) {
                                    isCompleted[0] = true;
                                    emitter.completeWithError(e);
                                }
                            } catch (IllegalStateException e) {
                                log.warn("SSE连接已完成，无法发送数据: {}", e.getMessage());
                                isCompleted[0] = true;
                            }
                        })
                        .doOnComplete(() -> {
                            try {
                                if (!isCompleted[0]) {
                                    log.info("原始SSE流完成");
                                    emitter.send(SseEmitter.event()
                                            .data("原始流式测试完成")
                                            .name("complete"));
                                    isCompleted[0] = true;
                                    emitter.complete();
                                } else {
                                    log.debug("SSE连接已完成，跳过完成事件发送");
                                }
                            } catch (IOException e) {
                                log.error("完成SSE流失败", e);
                                if (!isCompleted[0]) {
                                    isCompleted[0] = true;
                                    emitter.completeWithError(e);
                                }
                            } catch (IllegalStateException e) {
                                log.warn("SSE连接已完成，无法发送完成事件: {}", e.getMessage());
                                isCompleted[0] = true;
                            }
                        })
                        .doOnError(error -> {
                            log.error("原始SSE流处理失败", error);
                            try {
                                if (!isCompleted[0]) {
                                    emitter.send(SseEmitter.event()
                                            .data("错误: " + error.getMessage())
                                            .name("error"));
                                    isCompleted[0] = true;
                                    emitter.complete();
                                } else {
                                    log.debug("SSE连接已完成，跳过错误事件发送");
                                }
                            } catch (IOException e) {
                                if (!isCompleted[0]) {
                                    isCompleted[0] = true;
                                    emitter.completeWithError(error);
                                }
                            } catch (IllegalStateException e) {
                                log.warn("SSE连接已完成，无法发送错误事件: {}", e.getMessage());
                                isCompleted[0] = true;
                            }
                        })
                        .subscribe();

            } catch (Exception e) {
                log.error("原始SSE流启动失败", e);
                if (!isCompleted[0]) {
                    isCompleted[0] = true;
                    emitter.completeWithError(e);
                }
            }
        });

        return emitter;
    }
}
