<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="end-node-content">
        <p class="node-description">{{ nodeConfig.description }}</p>
        <div class="node-config" v-if="data.config && Object.keys(data.config).length > 0">
          <div class="config-item" v-for="(value, key) in data.config" :key="key">
            <span class="config-key">{{ key }}:</span>
            <span class="config-value">{{ value }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { getNodeByType } from '../../config/nodeLibrary'

// Props
interface EndNodeData {
  label?: string
  description?: string
  config?: Record<string, any>
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: EndNodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 获取节点配置
const nodeConfig = computed(() => {
  return getNodeByType('end') || {
    description: '工作流的结束节点，可以有多个结束节点'
  }
})
</script>

<style scoped>
.end-node-content {
  text-align: center;
}

.node-description {
  font-size: 13px;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.5;
  font-weight: 400;
}

.node-config {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.config-item:last-child {
  border-bottom: none;
}

.config-key {
  font-weight: 600;
  color: #1f2937;
  flex-shrink: 0;
}

.config-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #fef2f2;
  color: #7f1d1d;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #fecaca;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}
</style>
