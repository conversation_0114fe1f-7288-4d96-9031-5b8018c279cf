#!/bin/bash

# 插件热加载演示脚本
# 演示如何在不重启主程序的情况下更新插件

set -e

echo "=== 插件热加载演示 ==="

BASE_URL="http://localhost:8080"
PLUGIN_DIR="/opt/xhcai-plus/plugins"

# 检查服务是否运行
check_service() {
    echo "检查服务状态..."
    if curl -s "$BASE_URL/actuator/health" > /dev/null; then
        echo "✓ 服务正在运行"
    else
        echo "✗ 服务未运行，请先启动服务"
        exit 1
    fi
}

# 启动热加载监控
start_hotload_monitoring() {
    echo "启动热加载监控..."
    response=$(curl -s -X POST "$BASE_URL/api/plugin-hotload/monitoring/start")
    echo "响应: $response"
}

# 查看当前插件状态
show_plugin_status() {
    echo "当前插件状态:"
    curl -s "$BASE_URL/api/plugin-context/status" | jq '.' || echo "请安装jq工具以格式化JSON输出"
}

# 演示存储插件热加载
demo_storage_plugin_hotload() {
    echo ""
    echo "=== 演示存储插件热加载 ==="
    
    # 显示当前存储插件
    echo "当前存储插件类型:"
    curl -s "$BASE_URL/api/plugin-context/storage/types"
    echo ""
    
    # 模拟插件更新（这里只是演示，实际需要真实的插件文件）
    echo "模拟更新MinIO存储插件..."
    echo "在生产环境中，您只需要："
    echo "1. 将新的插件JAR文件复制到 $PLUGIN_DIR/storage/ 目录"
    echo "2. 系统会自动检测并热加载插件"
    echo ""
    
    # 手动热加载演示
    echo "手动热加载插件演示:"
    response=$(curl -s -X POST "$BASE_URL/api/plugin-hotload/storage/plugins/minio-storage-plugin/reload")
    echo "热加载响应: $response"
    echo ""
}

# 演示模型插件热加载
demo_model_plugin_hotload() {
    echo ""
    echo "=== 演示模型插件热加载 ==="
    
    # 显示当前模型插件
    echo "当前模型插件类型:"
    curl -s "$BASE_URL/api/plugin-context/model/types"
    echo ""
    
    # 批量热加载演示
    echo "批量热加载所有模型插件:"
    response=$(curl -s -X POST "$BASE_URL/api/plugin-hotload/model/reload-all")
    echo "批量热加载响应: $response"
    echo ""
}

# 演示通知插件热加载
demo_notification_plugin_hotload() {
    echo ""
    echo "=== 演示通知插件热加载 ==="
    
    # 显示当前通知插件
    echo "当前通知插件类型:"
    curl -s "$BASE_URL/api/plugin-context/notification/types"
    echo ""
    
    # 手动热加载演示
    echo "手动热加载邮件通知插件:"
    response=$(curl -s -X POST "$BASE_URL/api/plugin-hotload/notification/plugins/email-notification-plugin/reload")
    echo "热加载响应: $response"
    echo ""
}

# 监控热加载状态
monitor_hotload_status() {
    echo ""
    echo "=== 热加载监控状态 ==="
    
    response=$(curl -s "$BASE_URL/api/plugin-hotload/monitoring/status")
    echo "监控状态: $response"
    echo ""
}

# 实际文件热加载演示
demo_file_hotload() {
    echo ""
    echo "=== 实际文件热加载演示 ==="
    
    # 检查插件目录
    if [ -d "$PLUGIN_DIR" ]; then
        echo "插件目录结构:"
        find "$PLUGIN_DIR" -name "*.jar" -type f | head -10
        echo ""
        
        echo "要演示真实的热加载，请执行以下步骤:"
        echo "1. 构建新版本的插件:"
        echo "   cd plugins/storage/minio-storage-plugin"
        echo "   mvn clean package -DskipTests"
        echo ""
        echo "2. 复制新插件到生产目录:"
        echo "   cp target/minio-storage-plugin-1.0.0.jar $PLUGIN_DIR/storage/"
        echo ""
        echo "3. 系统会自动检测并热加载插件（如果启用了监控）"
        echo "   或者手动触发热加载:"
        echo "   curl -X POST $BASE_URL/api/plugin-hotload/storage/plugins/minio-storage-plugin/reload"
        echo ""
    else
        echo "插件目录不存在: $PLUGIN_DIR"
        echo "请确保应用已正确部署"
    fi
}

# 性能测试
performance_test() {
    echo ""
    echo "=== 热加载性能测试 ==="
    
    echo "测试热加载响应时间..."
    start_time=$(date +%s%N)
    
    response=$(curl -s -X POST "$BASE_URL/api/plugin-hotload/storage/plugins/minio-storage-plugin/reload")
    
    end_time=$(date +%s%N)
    duration=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    echo "热加载耗时: ${duration}ms"
    echo "响应: $response"
    echo ""
}

# 主函数
main() {
    echo "开始插件热加载演示..."
    echo "服务地址: $BASE_URL"
    echo "插件目录: $PLUGIN_DIR"
    echo ""
    
    check_service
    start_hotload_monitoring
    show_plugin_status
    
    demo_storage_plugin_hotload
    demo_model_plugin_hotload
    demo_notification_plugin_hotload
    
    monitor_hotload_status
    demo_file_hotload
    performance_test
    
    echo ""
    echo "=== 热加载演示完成 ==="
    echo ""
    echo "关键要点:"
    echo "1. ✅ 插件热加载无需重启主程序"
    echo "2. ✅ 支持自动监控和手动触发两种方式"
    echo "3. ✅ 支持单个插件和批量插件热加载"
    echo "4. ✅ 热加载过程中服务保持可用"
    echo "5. ✅ 提供详细的状态监控和日志记录"
    echo ""
    echo "生产环境使用建议:"
    echo "- 启用自动监控: curl -X POST $BASE_URL/api/plugin-hotload/monitoring/start"
    echo "- 定期检查状态: curl $BASE_URL/api/plugin-hotload/monitoring/status"
    echo "- 更新插件时直接替换文件即可，系统会自动处理"
    echo ""
}

# 执行主函数
main "$@"
