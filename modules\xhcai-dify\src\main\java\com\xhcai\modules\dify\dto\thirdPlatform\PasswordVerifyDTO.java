package com.xhcai.modules.dify.dto.thirdPlatform;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 密码验证DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "密码验证DTO")
public class PasswordVerifyDTO {

    /**
     * 密码
     */
    @Schema(description = "密码", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "密码不能为空")
    @Size(min = 1, max = 50, message = "密码长度必须在1-50个字符之间")
    private String password;

    // Getters and Setters
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "PasswordVerifyDTO{" +
                "password='***'" +
                '}';
    }
}
