<template>
  <!-- 编辑智能体模态框 -->
  <div class="edit-modal" v-show="visible" @click="handleModalClick">
    <div class="edit-modal-content" @click.stop>
      <div class="edit-modal-header">
        <div class="header-left">
          <div class="agent-info" v-if="agent">
            <div class="agent-avatar">
              <i :class="agent.icon"></i>
            </div>
            <div class="agent-details">
              <h3>编辑智能体</h3>
              <span class="agent-type-badge">{{ agent.type }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="handleClose">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="edit-modal-body">
        <div class="edit-form">
          <div class="form-group">
            <label for="agentName">智能体名称</label>
            <input
              id="agentName"
              type="text"
              v-model="editAgentName"
              placeholder="请输入智能体名称"
              class="form-input"
            >
          </div>

          <div class="form-group">
            <label for="agentDescription">智能体描述</label>
            <textarea
              id="agentDescription"
              v-model="editAgentDescription"
              placeholder="请输入智能体描述信息"
              class="form-textarea"
              rows="4"
            ></textarea>
          </div>

          <div class="form-group">
            <label>智能体图标</label>
            <div class="icon-selector">
              <div class="current-icon" @click="openIconSelector">
                <div class="icon-preview" :style="{ background: editAgentIconBackground }">
                  <i :class="editAgentIcon" v-if="editAgentIcon"></i>
                  <i class="fas fa-image" v-else></i>
                </div>
                <span>点击选择图标</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="edit-modal-footer">
        <div class="footer-left">
          <span class="edit-tip">修改后将立即生效</span>
        </div>
        <div class="footer-right">
          <button class="btn-modern btn-secondary" @click="handleClose">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button class="btn-modern btn-primary" @click="handleSave" :disabled="!editAgentName.trim()">
            <i class="fas fa-save"></i>
            保存
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 图标选择器组件 -->
  <IconSelector
    v-model:visible="showIconSelector"
    :selected-icon="editAgentIcon"
    :selected-background="editAgentIconBackground"
    @select="handleIconSelect"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import IconSelector from '../../components/common/IconSelector.vue'
import { updateAgent } from '@/api/agents'
import type { UpdateAgentRequest } from '@/api/agents'

// 定义智能体类型
interface Agent {
  id: string
  name: string
  description: string
  icon: string
  iconBackground?: string  // 图标背景颜色
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
}

// Props
interface Props {
  visible: boolean
  agent: Agent | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  agent: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'save': [data: { name: string; description: string; icon: string; iconBackground: string }]
}>()

// 响应式数据
const editAgentName = ref('')
const editAgentDescription = ref('')
const editAgentIcon = ref('')
const editAgentIconBackground = ref('linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)')
const showIconSelector = ref(false)

// 监听 agent 变化，初始化表单数据
watch(() => props.agent, (newAgent) => {
  if (newAgent) {
    editAgentName.value = newAgent.name
    editAgentDescription.value = newAgent.description || ''
    editAgentIcon.value = newAgent.icon || ''
    editAgentIconBackground.value = newAgent.iconBackground || 'linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)'
  }
}, { immediate: true })

// 方法
const handleModalClick = () => {
  handleClose()
}

const handleClose = () => {
  emit('update:visible', false)
  // 重置表单数据
  editAgentName.value = ''
  editAgentDescription.value = ''
  editAgentIcon.value = ''
  editAgentIconBackground.value = 'linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)'
}

const handleSave = async () => {
  if (!editAgentName.value.trim()) {
    ElMessage.warning('请输入智能体名称')
    return
  }

  if (!props.agent?.id) {
    ElMessage.error('智能体ID不存在')
    return
  }

  try {
    // 构建更新请求数据
    const updateData: UpdateAgentRequest = {
      id: props.agent.id,
      name: editAgentName.value.trim(),
      description: editAgentDescription.value.trim(),
      avatar: editAgentIcon.value,
      iconBackground: editAgentIconBackground.value
    }

    // 调用后端API更新智能体
    await updateAgent(updateData)

    ElMessage.success('更新智能体成功')

    // 调用传递过来的save方法刷新列表
    emit('save', {
      name: editAgentName.value.trim(),
      description: editAgentDescription.value.trim(),
      icon: editAgentIcon.value,
      iconBackground: editAgentIconBackground.value
    })

    handleClose()
  } catch (error) {
    console.error('更新智能体失败:', error)
    ElMessage.error('更新智能体失败，请重试')
  }
}

const openIconSelector = () => {
  showIconSelector.value = true
}

const handleIconSelect = (data: { icon: string; background: string }) => {
  editAgentIcon.value = data.icon
  editAgentIconBackground.value = data.background
}
</script>

<style scoped>
/* 编辑模态框样式 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.edit-modal-content {
  background: #fefefe;
  border-radius: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(147, 197, 253, 0.3);
  animation: slideUp 0.4s ease;
  display: flex;
  flex-direction: column;
}

.edit-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.header-left {
  flex: 1;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1a365d;
  font-size: 18px;
}

.agent-details h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.agent-type-badge {
  display: inline-block;
  padding: 2px 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-top: 2px;
}

.edit-modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.form-input {
  padding: 12px 16px;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fefefe;
  color: #111827;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  padding: 12px 16px;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fefefe;
  color: #111827;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.edit-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #dbeafe;
  background: #f8fafc;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.edit-tip {
  font-size: 13px;
  color: #4b5563;
}

/* 图标选择器 */
.icon-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-icon {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.current-icon:hover {
  border-color: #7fb3d3;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.icon-preview {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  color: #ffffff;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.current-icon span {
  font-size: 14px;
  color: #4a5568;
}



/* 按钮样式 */
.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
  min-width: 80px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.btn-secondary:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-1px);
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #495057;
  transform: scale(1.05);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .edit-modal-header {
    padding: 12px 16px;
  }

  .edit-modal-body {
    padding: 16px;
  }

  .edit-modal-footer {
    padding: 12px 16px;
  }

  .icon-selector-content {
    width: 95%;
    max-height: 90vh;
  }

  .icons-grid {
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    gap: 8px;
  }

  .icon-option {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
}
</style>
