<template>
  <!-- 添加/编辑部门模态框 -->
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
    <div class="bg-white rounded-2xl shadow-2xl p-4 w-full max-w-2xl mx-4 transform transition-all duration-300 scale-100">
      <!-- 模态框头部 -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
            <i class="fas fa-building text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900">
              {{ isEdit ? '编辑部门' : '添加部门' }}
            </h3>
            <p class="text-sm text-gray-500 mt-1">
              {{ isEdit ? '修改部门信息' : '创建新的组织部门' }}
            </p>
          </div>
        </div>
        <button @click="handleClose" class="w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200">
          <i class="fas fa-times text-gray-500"></i>
        </button>
      </div>

      <el-form ref="deptFormRef" :model="formData" :rules="deptRules" @submit.prevent="handleSave" class="space-y-4">
        <!-- 基本信息区域 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
          <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
            基本信息
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="部门名称" prop="deptName" required>
              <el-input
                v-model="formData.deptName"
                placeholder="请输入部门名称"
                :prefix-icon="OfficeBuilding"
                clearable
              />
            </el-form-item>

            <el-form-item label="部门编码" prop="deptCode">
              <el-input
                v-model="formData.deptCode"
                placeholder="请输入部门编码"
                :prefix-icon="DocumentCopy"
                clearable
              />
            </el-form-item>

            <el-form-item label="上级部门" prop="parentId">
              <el-select
                v-model="formData.parentId"
                placeholder="请选择上级部门"
                :prefix-icon="OfficeBuilding"
                style="width: 100%"
                clearable
              >
                <el-option value="" label="顶级部门" />
                <el-option
                  v-for="unit in availableParentUnits"
                  :key="unit.id"
                  :value="unit.id"
                  :label="`${'　'.repeat(unit.level || 0)}${unit.deptName}`"
                  :disabled="unit.id === formData.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="显示顺序" prop="orderNum">
              <el-input-number
                v-model="formData.orderNum"
                :min="0"
                placeholder="请输入显示顺序"
                style="width: 100%"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 负责人信息区域 -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100">
          <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-user-tie text-green-500 mr-2"></i>
            负责人信息
          </h4>
          <el-form-item label="部门负责人" prop="leaderId">
            <el-select
              v-model="formData.leaderId"
              placeholder="请选择负责人"
              :prefix-icon="User"
              style="width: 100%"
              clearable
              filterable
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :value="user.id"
                :label="`${user.nickname || user.username} (${user.username})`"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 其他设置区域 -->
        <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100">
          <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-cog text-purple-500 mr-2"></i>
            其他设置
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                :prefix-icon="Switch"
                style="width: 100%"
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.dictValue"
                  :value="status.dictValue"
                  :label="status.dictLabel"
                />
              </el-select>
            </el-form-item>

            <div class="md:col-span-2">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                  :prefix-icon="ChatDotRound"
                />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-4 pt-6 border-t border-gray-200">
          <el-button
            type="primary"
            @click="handleSave"
            :loading="loading"
            size="large"
            style="flex: 1"
          >
            <i class="fas fa-save mr-2"></i>
            {{ loading ? '保存中...' : '保存部门' }}
          </el-button>
          <el-button
            @click="handleClose"
            size="large"
            style="flex: 1"
          >
            <i class="fas fa-times mr-2"></i>
            取消
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, OfficeBuilding, User, Switch, ChatDotRound } from '@element-plus/icons-vue'
import { DeptAPI } from '@/api/system'
import type {
  SysDeptVO,
  SysDept,
  SysDictDataVO,
  SysUserVO
} from '@/types/system'

// Props
interface Props {
  visible: boolean
  isEdit: boolean
  deptData?: SysDept
  availableParentUnits: SysDeptVO[]
  users: SysUserVO[]
  statusOptions: SysDictDataVO[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
const emit = defineEmits<{
  close: []
  save: [dept: SysDept]
}>()

// 表单数据
const formData = ref<SysDept>({
  deptName: '',
  deptCode: '',
  parentId: '',
  orderNum: 0,
  leaderId: '',
  status: '0'
})

// 表单引用
const deptFormRef = ref()

// 表单验证规则
const deptRules = {
  deptName: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 1, max: 30, message: '部门名称长度在 1 到 30 个字符', trigger: 'blur' }
  ],
  deptCode: [
    { max: 30, message: '部门编码长度不能超过 30 个字符', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_]*$/, message: '部门编码只能包含字母、数字和下划线', trigger: 'blur' }
  ]
}

// 监听部门数据变化
watch(() => props.deptData, (newData) => {
  if (newData) {
    formData.value = { ...newData }
  } else {
    // 重置表单
    formData.value = {
      deptName: '',
      deptCode: '',
      parentId: '',
      orderNum: 0,
      leaderId: '',
      status: '0'
    }
  }
}, { immediate: true })

// 关闭模态框
const handleClose = () => {
  // 重置表单验证
  if (deptFormRef.value) {
    deptFormRef.value.resetFields()
  }
  emit('close')
}

// 保存部门
const handleSave = async () => {
  if (!deptFormRef.value) return

  try {
    // 表单验证
    await deptFormRef.value.validate()
    emit('save', { ...formData.value })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
/* 继承父组件样式 */
</style>
