<template>
  <div class="audio-player">
    <div class="player-container">
      <div class="player-header">
        <div class="audio-info">
          <el-icon class="audio-icon"><HeadphoneIcon /></el-icon>
          <div class="audio-details">
            <div class="audio-title">{{ audio.title || '音频文件' }}</div>
            <div class="audio-duration" v-if="duration">{{ formatTime(duration) }}</div>
          </div>
        </div>
        <div class="player-actions">
          <el-button @click="downloadAudio" size="small" type="primary" text>
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>

      <div class="player-controls">
        <!-- 播放/暂停按钮 -->
        <el-button
          @click="togglePlay"
          :type="isPlaying ? 'warning' : 'primary'"
          circle
          size="large"
          class="play-btn"
        >
          <el-icon size="20">
            <VideoPlay v-if="!isPlaying" />
            <VideoPause v-else />
          </el-icon>
        </el-button>

        <!-- 进度条区域 -->
        <div class="progress-area">
          <div class="time-display">
            <span class="current-time">{{ formatTime(currentTime) }}</span>
            <span class="total-time">{{ formatTime(duration) }}</span>
          </div>
          
          <div class="progress-container" @click="seekTo">
            <div class="progress-track" ref="progressTrack">
              <div 
                class="progress-fill" 
                :style="{ width: progressPercentage + '%' }"
              ></div>
              <div 
                class="progress-thumb" 
                :style="{ left: progressPercentage + '%' }"
                @mousedown="startDrag"
              ></div>
            </div>
          </div>
        </div>

        <!-- 音量控制 -->
        <div class="volume-control">
          <el-button @click="toggleMute" size="small" text>
            <el-icon>
              <Mute v-if="isMuted || volume === 0" />
              <Microphone v-else />
            </el-icon>
          </el-button>
          <div class="volume-slider">
            <el-slider
              v-model="volume"
              :min="0"
              :max="100"
              :show-tooltip="false"
              size="small"
              @input="updateVolume"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的音频元素 -->
    <audio
      ref="audioElement"
      :src="audio.url"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @error="onError"
      preload="metadata"
    ></audio>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, defineProps, defineEmits } from 'vue'
import {
  Microphone as HeadphoneIcon,
  Download,
  VideoPlay,
  VideoPause,
  Microphone,
  Mute
} from '@element-plus/icons-vue'

// 接口定义
interface AudioContent {
  url: string
  title?: string
  duration?: string | number
}

// Props定义
const props = defineProps<{
  audio: AudioContent
}>()

// Emits定义
const emit = defineEmits<{
  download: [audio: AudioContent]
  play: []
  pause: []
  ended: []
}>()

// 响应式数据
const audioElement = ref<HTMLAudioElement>()
const progressTrack = ref<HTMLElement>()

const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(80)
const isMuted = ref(false)
const isDragging = ref(false)

// 计算属性
const progressPercentage = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 格式化时间
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 播放/暂停切换
const togglePlay = () => {
  if (!audioElement.value) return
  
  if (isPlaying.value) {
    audioElement.value.pause()
    isPlaying.value = false
    emit('pause')
  } else {
    audioElement.value.play()
    isPlaying.value = true
    emit('play')
  }
}

// 静音切换
const toggleMute = () => {
  if (!audioElement.value) return
  
  isMuted.value = !isMuted.value
  audioElement.value.muted = isMuted.value
}

// 更新音量
const updateVolume = (value: number) => {
  if (!audioElement.value) return
  
  volume.value = value
  audioElement.value.volume = value / 100
  
  if (value === 0) {
    isMuted.value = true
    audioElement.value.muted = true
  } else if (isMuted.value) {
    isMuted.value = false
    audioElement.value.muted = false
  }
}

// 跳转到指定位置
const seekTo = (event: MouseEvent) => {
  if (!audioElement.value || !progressTrack.value) return
  
  const rect = progressTrack.value.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = clickX / rect.width
  const newTime = percentage * duration.value
  
  audioElement.value.currentTime = newTime
  currentTime.value = newTime
}

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  event.preventDefault()
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value || !progressTrack.value || !audioElement.value) return
    
    const rect = progressTrack.value.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    const newTime = percentage * duration.value
    
    audioElement.value.currentTime = newTime
    currentTime.value = newTime
  }
  
  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 下载音频
const downloadAudio = () => {
  const link = document.createElement('a')
  link.href = props.audio.url
  link.download = props.audio.title || 'audio'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  emit('download', props.audio)
}

// 音频事件处理
const onLoadedMetadata = () => {
  if (audioElement.value) {
    duration.value = audioElement.value.duration
    audioElement.value.volume = volume.value / 100
  }
}

const onTimeUpdate = () => {
  if (audioElement.value && !isDragging.value) {
    currentTime.value = audioElement.value.currentTime
  }
}

const onEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
  emit('ended')
}

const onError = (error: Event) => {
  console.error('音频加载错误:', error)
  isPlaying.value = false
}

onMounted(() => {
  if (audioElement.value) {
    audioElement.value.volume = volume.value / 100
  }
})

onUnmounted(() => {
  if (audioElement.value) {
    audioElement.value.pause()
  }
})
</script>

<style scoped>
.audio-player {
  width: 100%;
  max-width: 500px;
}

.player-container {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.audio-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.audio-icon {
  color: #6b7280;
  font-size: 24px;
}

.audio-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.audio-title {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.audio-duration {
  color: #6b7280;
  font-size: 12px;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
}

.play-btn {
  flex-shrink: 0;
}

.progress-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

.progress-container {
  cursor: pointer;
}

.progress-track {
  position: relative;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 14px;
  height: 14px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: left 0.1s ease;
}

.progress-thumb:hover {
  transform: translate(-50%, -50%) scale(1.2);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.volume-slider {
  width: 80px;
}

.volume-slider :deep(.el-slider__runway) {
  height: 4px;
}

.volume-slider :deep(.el-slider__bar) {
  background: #3b82f6;
}

.volume-slider :deep(.el-slider__button) {
  width: 12px;
  height: 12px;
  border: 2px solid #3b82f6;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .player-controls {
    flex-direction: column;
    gap: 12px;
  }

  .progress-area {
    width: 100%;
  }

  .volume-control {
    width: 100%;
    justify-content: center;
  }

  .volume-slider {
    width: 120px;
  }
}
</style>
