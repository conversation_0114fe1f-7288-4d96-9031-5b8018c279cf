package com.xhcai.modules.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.dto.SysDictDataQueryDTO;
import com.xhcai.modules.system.entity.SysDictData;
import com.xhcai.modules.system.vo.SysDictDataVO;

/**
 * 字典数据服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysDictDataService extends IService<SysDictData> {

    /**
     * 分页查询字典数据列表
     *
     * @param queryDTO 查询条件
     * @return 字典数据分页列表
     */
    PageResult<SysDictDataVO> selectDictDataPage(SysDictDataQueryDTO queryDTO);

    /**
     * 查询字典数据列表
     *
     * @param queryDTO 查询条件
     * @return 字典数据列表
     */
    List<SysDictDataVO> selectDictDataList(SysDictDataQueryDTO queryDTO);

    /**
     * 创建字典数据（如果不存在）
     */
    void createDictDataIfNotExists(String dictType, String dictValue, String dictLabel, Integer dictSort, String remark, String cssClass, String listClass);

    /**
     * 创建字典数据（如果不存在）
     */
    void createDictDataIfNotExists(String dictType, String dictValue, String dictLabel, Integer dictSort, String remark, String cssClass, String listClass, String isSystemDict);

    /**
     * 根据字典数据ID查询字典数据信息
     *
     * @param dictDataId 字典数据ID
     * @return 字典数据信息
     */
    SysDictDataVO selectDictDataById(String dictDataId);

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<SysDictDataVO> selectByDictType(String dictType);

    /**
     * 根据字典类型和值查询字典数据
     *
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典数据
     */
    SysDictData selectByDictTypeAndValue(String dictType, String dictValue);

    /**
     * 根据字典类型和值获取字典标签
     *
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    String getDictLabel(String dictType, String dictValue);

    /**
     * 创建字典数据
     *
     * @param dictData 字典数据信息
     * @return 是否成功
     */
    boolean insertDictData(SysDictData dictData);

    /**
     * 更新字典数据
     *
     * @param dictData 字典数据信息
     * @return 是否成功
     */
    boolean updateDictData(SysDictData dictData);

    /**
     * 删除字典数据
     *
     * @param dictDataIds 字典数据ID列表
     * @return 是否成功
     */
    boolean deleteDictDatas(List<String> dictDataIds);

    /**
     * 检查字典值是否存在
     *
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @param excludeId 排除的字典数据ID
     * @return 是否存在
     */
    boolean existsDictValue(String dictType, String dictValue, String excludeId);

    /**
     * 批量更新字典数据状态
     *
     * @param dictDataIds 字典数据ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> dictDataIds, String status);

    /**
     * 根据字典类型删除字典数据
     *
     * @param dictType 字典类型
     * @return 是否成功
     */
    boolean deleteByDictType(String dictType);

    /**
     * 刷新字典数据缓存
     */
    void refreshCache();

    /**
     * 导出字典数据
     *
     * @param queryDTO 查询条件
     * @return 字典数据列表
     */
    List<SysDictDataVO> exportDictDatas(SysDictDataQueryDTO queryDTO);

    /**
     * 导入字典数据
     *
     * @param dictDataList 字典数据列表
     * @return 导入结果
     */
    String importDictDatas(List<SysDictData> dictDataList);
}
