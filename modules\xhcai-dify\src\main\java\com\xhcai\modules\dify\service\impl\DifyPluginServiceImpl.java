package com.xhcai.modules.dify.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.dto.DifyResponse;
import com.xhcai.modules.dify.dto.plugin.DifyPluginDTO;
import com.xhcai.modules.dify.service.IDifyPluginService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 插件服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class DifyPluginServiceImpl implements IDifyPluginService {

    private static final Logger log = LoggerFactory.getLogger(DifyPluginServiceImpl.class);

    @Autowired
    @Qualifier("difyWebClient")
    private WebClient difyWebClient;

    @Autowired
    private DifyConfig difyConfig;

    @Override
    public Mono<Result<PageResult<DifyPluginDTO>>> getPluginList(int page, int size, String keyword, String category, String type, String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", size);
        if (StringUtils.hasText(keyword)) {
            params.put("keyword", keyword);
        }
        if (StringUtils.hasText(category)) {
            params.put("category", category);
        }
        if (StringUtils.hasText(type)) {
            params.put("type", type);
        }
        if (StringUtils.hasText(status)) {
            params.put("status", status);
        }

        return difyWebClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path("/v1/plugins");
                    params.forEach((key, value) -> uriBuilder.queryParam(key, value));
                    return uriBuilder.build();
                })
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<PageResult<DifyPluginDTO>> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<PageResult<DifyPluginDTO>>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取插件列表失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyPluginDTO>> getPlugin(String pluginId) {
        return difyWebClient.get()
                .uri("/v1/plugins/{pluginId}", pluginId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyPluginDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyPluginDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取插件详情失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyPluginDTO>> installPlugin(String pluginId, Map<String, Object> config) {
        Map<String, Object> request = new HashMap<>();
        request.put("plugin_id", pluginId);
        if (config != null && !config.isEmpty()) {
            request.put("config", config);
        }

        return difyWebClient.post()
                .uri("/v1/plugins/install")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyPluginDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyPluginDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("安装插件失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> uninstallPlugin(String pluginId) {
        return difyWebClient.delete()
                .uri("/v1/plugins/{pluginId}/uninstall", pluginId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Void> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Void>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success();
                    } else {
                        throw new BusinessException("卸载插件失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyPluginDTO>> updatePluginConfig(String pluginId, Map<String, Object> config) {
        return difyWebClient.patch()
                .uri("/v1/plugins/{pluginId}/config", pluginId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(config)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyPluginDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyPluginDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("更新插件配置失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> enablePlugin(String pluginId) {
        Map<String, Object> request = new HashMap<>();
        request.put("status", "active");

        return difyWebClient.patch()
                .uri("/v1/plugins/{pluginId}/status", pluginId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Void> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Void>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success();
                    } else {
                        throw new BusinessException("启用插件失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> disablePlugin(String pluginId) {
        Map<String, Object> request = new HashMap<>();
        request.put("status", "inactive");

        return difyWebClient.patch()
                .uri("/v1/plugins/{pluginId}/status", pluginId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Void> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Void>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success();
                    } else {
                        throw new BusinessException("禁用插件失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> invokePluginTool(String pluginId, String toolName, Map<String, Object> parameters) {
        Map<String, Object> request = new HashMap<>();
        request.put("tool_name", toolName);
        request.put("parameters", parameters);

        return difyWebClient.post()
                .uri("/v1/plugins/{pluginId}/tools/invoke", pluginId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Object> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Object>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("调用插件工具失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> getPluginTools(String pluginId) {
        return difyWebClient.get()
                .uri("/v1/plugins/{pluginId}/tools", pluginId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Object> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Object>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取插件工具列表失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<PageResult<DifyPluginDTO>>> getInstalledPlugins(int page, int size) {
        return difyWebClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/v1/plugins/installed")
                        .queryParam("page", page)
                        .queryParam("limit", size)
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<PageResult<DifyPluginDTO>> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<PageResult<DifyPluginDTO>>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取已安装插件列表失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> getPluginStats(String pluginId) {
        return difyWebClient.get()
                .uri("/v1/plugins/{pluginId}/stats", pluginId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Object> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Object>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取插件统计信息失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> checkPluginUpdate(String pluginId) {
        return difyWebClient.get()
                .uri("/v1/plugins/{pluginId}/check-update", pluginId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Object> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Object>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("检查插件更新失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyPluginDTO>> updatePlugin(String pluginId) {
        return difyWebClient.post()
                .uri("/v1/plugins/{pluginId}/update", pluginId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyPluginDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyPluginDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("更新插件失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<PageResult<Object>>> getPluginLogs(String pluginId, int page, int size) {
        return difyWebClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/v1/plugins/{pluginId}/logs")
                        .queryParam("page", page)
                        .queryParam("limit", size)
                        .build(pluginId))
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<PageResult<Object>> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<PageResult<Object>>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取插件日志失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> testPluginConnection(String pluginId) {
        return difyWebClient.post()
                .uri("/v1/plugins/{pluginId}/test", pluginId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Object> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Object>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("测试插件连接失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    /**
     * 统一错误处理
     */
    private <T> Mono<Result<T>> handleError(Throwable throwable) {
        log.error("Dify Plugin API调用失败", throwable);
        
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            HttpStatus status = (HttpStatus) ex.getStatusCode();
            String message = "API调用失败: " + status.getReasonPhrase();
            
            if (status == HttpStatus.UNAUTHORIZED) {
                message = "API密钥无效或已过期";
            } else if (status == HttpStatus.FORBIDDEN) {
                message = "没有权限访问该资源";
            } else if (status == HttpStatus.NOT_FOUND) {
                message = "请求的资源不存在";
            } else if (status == HttpStatus.TOO_MANY_REQUESTS) {
                message = "请求过于频繁，请稍后重试";
            } else if (status.is5xxServerError()) {
                message = "服务器内部错误，请稍后重试";
            }
            
            return Mono.just(Result.fail(status.value(), message));
        } else if (throwable instanceof BusinessException) {
            BusinessException ex = (BusinessException) throwable;
            return Mono.just(Result.fail(500, ex.getMessage()));
        } else {
            return Mono.just(Result.fail(500, "系统异常: " + throwable.getMessage()));
        }
    }
}
