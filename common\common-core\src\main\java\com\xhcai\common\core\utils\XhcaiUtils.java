package com.xhcai.common.core.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 字符串处理工具类
 * 提供常用的字符串处理功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class XhcaiUtils {

    /**
     * 空字符串
     */
    public static final String EMPTY = "";

    /**
     * 默认分隔符
     */
    public static final String DEFAULT_DELIMITER = ",";

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    /**
     * 手机号正则表达式（中国大陆）
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^1[3-9]\\d{9}$"
    );

    /**
     * 私有构造函数，防止实例化
     */
    private XhcaiUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 判断字符串是否为空或null
     *
     * @param str 字符串
     * @return 是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }

    /**
     * 判断字符串是否不为空且不为null
     *
     * @param str 字符串
     * @return 是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 判断字符串是否为空白（null、空字符串或只包含空白字符）
     *
     * @param str 字符串
     * @return 是否为空白
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 判断字符串是否不为空白
     *
     * @param str 字符串
     * @return 是否不为空白
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 根据指定字符串生成UUID
     * 使用MD5哈希算法确保相同输入产生相同UUID
     *
     * @param input 输入字符串
     * @return 生成的UUID字符串
     */
    public static String generateUuidFromString(String input) {
        if (isEmpty(input)) {
            return UUID.randomUUID().toString();
        }

        try {
            // 使用MD5算法生成哈希值
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将哈希值转换为UUID格式
            long mostSigBits = 0;
            long leastSigBits = 0;

            for (int i = 0; i < 8; i++) {
                mostSigBits = (mostSigBits << 8) | (hashBytes[i] & 0xff);
            }
            for (int i = 8; i < 16; i++) {
                leastSigBits = (leastSigBits << 8) | (hashBytes[i] & 0xff);
            }

            UUID uuid = new UUID(mostSigBits, leastSigBits);
            return uuid.toString();
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5算法不可用，返回随机UUID
            return UUID.randomUUID().toString();
        }
    }

    /**
     * 生成随机UUID
     *
     * @return UUID字符串
     */
    public static String generateRandomUuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成不带连字符的UUID
     *
     * @return 不带连字符的UUID字符串
     */
    public static String generateSimpleUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 根据指定字符串生成不带连字符的UUID
     *
     * @param input 输入字符串
     * @return 不带连字符的UUID字符串
     */
    public static String generateSimpleUuidFromString(String input) {
        return generateUuidFromString(input).replace("-", "");
    }

    /**
     * 字符串首字母大写
     *
     * @param str 字符串
     * @return 首字母大写的字符串
     */
    public static String capitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * 字符串首字母小写
     *
     * @param str 字符串
     * @return 首字母小写的字符串
     */
    public static String uncapitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }

    /**
     * 驼峰命名转下划线命名
     *
     * @param camelCase 驼峰命名字符串
     * @return 下划线命名字符串
     */
    public static String camelToSnake(String camelCase) {
        if (isEmpty(camelCase)) {
            return camelCase;
        }
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 下划线命名转驼峰命名
     *
     * @param snakeCase 下划线命名字符串
     * @return 驼峰命名字符串
     */
    public static String snakeToCamel(String snakeCase) {
        if (isEmpty(snakeCase)) {
            return snakeCase;
        }
        
        StringBuilder result = new StringBuilder();
        String[] parts = snakeCase.split("_");
        
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (i == 0) {
                result.append(part.toLowerCase());
            } else {
                result.append(capitalize(part.toLowerCase()));
            }
        }
        
        return result.toString();
    }

    /**
     * 验证邮箱格式
     *
     * @param email 邮箱地址
     * @return 是否为有效邮箱格式
     */
    public static boolean isValidEmail(String email) {
        return isNotEmpty(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证手机号格式（中国大陆）
     *
     * @param phone 手机号
     * @return 是否为有效手机号格式
     */
    public static boolean isValidPhone(String phone) {
        return isNotEmpty(phone) && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 字符串脱敏处理
     *
     * @param str 原字符串
     * @param start 开始保留的字符数
     * @param end 结束保留的字符数
     * @param mask 脱敏字符
     * @return 脱敏后的字符串
     */
    public static String mask(String str, int start, int end, char mask) {
        if (isEmpty(str) || str.length() <= start + end) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(str, 0, start);
        
        int maskLength = str.length() - start - end;
        for (int i = 0; i < maskLength; i++) {
            sb.append(mask);
        }
        
        sb.append(str.substring(str.length() - end));
        return sb.toString();
    }

    /**
     * 手机号脱敏
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        return mask(phone, 3, 4, '*');
    }

    /**
     * 邮箱脱敏
     *
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (isEmpty(email) || !email.contains("@")) {
            return email;
        }
        
        String[] parts = email.split("@");
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 2) {
            return email;
        }
        
        String maskedUsername = mask(username, 1, 1, '*');
        return maskedUsername + "@" + domain;
    }

    /**
     * 截取字符串，超出长度用省略号表示
     *
     * @param str 原字符串
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    public static String truncate(String str, int maxLength) {
        return truncate(str, maxLength, "...");
    }

    /**
     * 截取字符串，超出长度用指定后缀表示
     *
     * @param str 原字符串
     * @param maxLength 最大长度
     * @param suffix 后缀
     * @return 截取后的字符串
     */
    public static String truncate(String str, int maxLength, String suffix) {
        if (isEmpty(str) || str.length() <= maxLength) {
            return str;
        }
        
        if (suffix == null) {
            suffix = "";
        }
        
        int truncateLength = maxLength - suffix.length();
        if (truncateLength <= 0) {
            return suffix.substring(0, maxLength);
        }
        
        return str.substring(0, truncateLength) + suffix;
    }

    /**
     * 重复字符串
     *
     * @param str 字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    public static String repeat(String str, int count) {
        if (isEmpty(str) || count <= 0) {
            return EMPTY;
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 安全的字符串比较（避免空指针异常）
     *
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 是否相等
     */
    public static boolean equals(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equals(str2);
    }

    /**
     * 安全的字符串比较（忽略大小写）
     *
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 是否相等
     */
    public static boolean equalsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equalsIgnoreCase(str2);
    }
}
