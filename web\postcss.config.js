export default {
  plugins: {
    // TailwindCSS处理
    tailwindcss: {},
    // 自动添加浏览器前缀
    autoprefixer: {
      // 根据browserslist自动确定需要的前缀
      overrideBrowserslist: [
        '> 1%',
        'last 2 versions',
        'not dead',
        'not ie 11'
      ]
    },
    // CSS压缩（生产环境）
    ...(process.env.NODE_ENV === 'production' ? {
      cssnano: {
        preset: ['default', {
          // 保留重要的注释
          discardComments: {
            removeAll: false
          },
          // 标准化显示值
          normalizeDisplayValues: false,
          // 减少CSS calc()
          calc: false
        }]
      }
    } : {}),
    // PostCSS插件用于处理现代CSS特性
    'postcss-preset-env': {
      // 根据browserslist自动确定需要的polyfill
      autoprefixer: false, // 已经单独配置了autoprefixer
      stage: 2, // 使用stage 2的特性
      features: {
        // 启用自定义属性（CSS变量）
        'custom-properties': false, // 保持CSS变量，不转换
        // 启用嵌套规则
        'nesting-rules': true,
        // 启用自定义媒体查询
        'custom-media-queries': true,
        // 启用颜色函数
        'color-function': true
      }
    }
  }
}
