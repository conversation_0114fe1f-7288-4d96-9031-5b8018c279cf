package com.xhcai.modules.system.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysPermission;

/**
 * 权限信息Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermission> {

    /**
     * 分页查询权限列表
     *
     * @param page 分页参数
     * @param permissionCode 权限编码
     * @param permissionName 权限名称
     * @param permissionType 权限类型
     * @param status 状态
     * @return 权限列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_permission "
            + "WHERE deleted = 0 "
            + "<if test='permissionCode != null and permissionCode != \"\"'>"
            + "AND permission_code LIKE CONCAT('%', #{permissionCode}, '%') "
            + "</if>"
            + "<if test='permissionName != null and permissionName != \"\"'>"
            + "AND permission_name LIKE CONCAT('%', #{permissionName}, '%') "
            + "</if>"
            + "<if test='permissionType != null and permissionType != \"\"'>"
            + "AND permission_type = #{permissionType} "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "AND status = #{status} "
            + "</if>"
            + "ORDER BY order_num ASC, create_time DESC"
            + "</script>")
    Page<SysPermission> selectPermissionPage(Page<SysPermission> page,
            @Param("permissionCode") String permissionCode,
            @Param("permissionName") String permissionName,
            @Param("permissionType") String permissionType,
            @Param("status") String status);

    /**
     * 查询权限列表
     *
     * @param permissionCode 权限编码
     * @param permissionName 权限名称
     * @param permissionType 权限类型
     * @param status 状态
     * @return 权限列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_permission "
            + "WHERE deleted = 0 "
            + "<if test='permissionCode != null and permissionCode != \"\"'>"
            + "AND permission_code LIKE CONCAT('%', #{permissionCode}, '%') "
            + "</if>"
            + "<if test='permissionName != null and permissionName != \"\"'>"
            + "AND permission_name LIKE CONCAT('%', #{permissionName}, '%') "
            + "</if>"
            + "<if test='permissionType != null and permissionType != \"\"'>"
            + "AND permission_type = #{permissionType} "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "AND status = #{status} "
            + "</if>"
            + "ORDER BY order_num ASC, create_time DESC"
            + "</script>")
    List<SysPermission> selectPermissionList(@Param("permissionCode") String permissionCode,
            @Param("permissionName") String permissionName,
            @Param("permissionType") String permissionType,
            @Param("status") String status);

    /**
     * 查询权限树
     *
     * @param status 状态
     * @return 权限列表
     */
    @Select("SELECT * FROM sys_permission "
            + "WHERE deleted = 0 "
            + "AND status = COALESCE(#{status}, status) "
            + "ORDER BY order_num ASC")
    List<SysPermission> selectPermissionTree(@Param("status") String status);

    /**
     * 根据权限编码查询权限信息
     *
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    @Select("SELECT * FROM sys_permission WHERE permission_code = #{permissionCode} AND deleted = 0")
    SysPermission selectByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 检查权限编码是否存在
     *
     * @param permissionCode 权限编码
     * @param excludeId 排除的权限ID
     * @return 存在数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_permission "
            + "WHERE permission_code = #{permissionCode} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsPermissionCode(@Param("permissionCode") String permissionCode, @Param("excludeId") String excludeId);

    /**
     * 检查权限名称是否存在（同级权限）
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludeId 排除的权限ID
     * @return 存在数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_permission "
            + "WHERE permission_name = #{permissionName} AND parent_id = #{parentId} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsPermissionName(@Param("permissionName") String permissionName,
            @Param("parentId") String parentId,
            @Param("excludeId") String excludeId);

    /**
     * 检查是否存在子权限
     *
     * @param permissionId 权限ID
     * @return 子权限数量
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE parent_id = #{permissionId} AND deleted = 0")
    Integer hasChildren(@Param("permissionId") String permissionId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p "
            + "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id "
            + "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id "
            + "INNER JOIN sys_role r ON ur.role_id = r.id "
            + "WHERE ur.user_id = #{userId} AND ur.tenant_id = #{tenantId} "
            + "AND p.deleted = 0 AND rp.deleted = 0 AND ur.deleted = 0 "
            + "AND r.status = '0' AND r.deleted = 0 AND p.status = '0' "
            + "ORDER BY p.order_num ASC")
    List<SysPermission> selectPermissionsByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 根据用户ID查询权限编码集合
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 权限编码集合
     */
    @Select("SELECT DISTINCT p.permission_code FROM sys_permission p "
            + "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id "
            + "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id "
            + "INNER JOIN sys_role r ON ur.role_id = r.id "
            + "WHERE ur.user_id = #{userId} AND ur.tenant_id = #{tenantId} "
            + "AND p.deleted = 0 AND rp.deleted = 0 AND ur.deleted = 0 "
            + "AND r.status = '0' AND r.deleted = 0 AND p.status = '0'")
    Set<String> selectPermissionCodesByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Select("SELECT p.* FROM sys_permission p "
            + "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id "
            + "WHERE rp.role_id = #{roleId} AND p.deleted = 0 AND rp.deleted = 0 "
            + "AND p.status = '0' "
            + "ORDER BY p.order_num ASC")
    List<SysPermission> selectPermissionsByRoleId(@Param("roleId") String roleId);

    /**
     * 查询所有可用权限
     *
     * @return 权限列表
     */
    @Select("SELECT * FROM sys_permission "
            + "WHERE status = '0' AND deleted = 0 "
            + "ORDER BY order_num ASC")
    List<SysPermission> selectAllAvailablePermissions();

    /**
     * 根据权限类型查询权限列表
     *
     * @param permissionType 权限类型
     * @return 权限列表
     */
    @Select("SELECT * FROM sys_permission "
            + "WHERE permission_type = #{permissionType} AND deleted = 0 "
            + "ORDER BY order_num ASC")
    List<SysPermission> selectPermissionsByType(@Param("permissionType") String permissionType);

    /**
     * 查询最大排序号
     *
     * @param parentId 父权限ID
     * @return 最大排序号
     */
    @Select("SELECT COALESCE(MAX(order_num), 0) FROM sys_permission "
            + "WHERE parent_id = #{parentId} AND deleted = 0")
    Integer selectMaxOrderNum(@Param("parentId") String parentId);

    /**
     * 根据祖级列表查询权限列表
     *
     * @param ancestors 祖级列表
     * @return 权限列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_permission "
            + "WHERE deleted = 0 AND id IN "
            + "<foreach collection='ancestors' item='id' open='(' separator=',' close=')'>"
            + "#{id}"
            + "</foreach>"
            + "ORDER BY order_num ASC"
            + "</script>")
    List<SysPermission> selectByAncestors(@Param("ancestors") String ancestors);
}
