package com.xhcai.modules.agent.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.xhcai.modules.dify.entity.ThirdPlatform;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体信息")
public class AgentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    private String id;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "客服助手")
    private String name;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述", example = "专业的客服助手，能够回答用户问题")
    private String description;

    /**
     * 智能体头像URL
     */
    @Schema(description = "智能体头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    /**
     * 智能体图标背景颜色
     */
    @Schema(description = "智能体图标背景颜色", example = "linear-gradient(135deg, #667eea 0%, #667eeaaa 100%)")
    private String iconBackground;

    /**
     * 智能体类型
     */
    @Schema(description = "智能体类型", example = "chat")
    private String type;

    /**
     * 智能体类型名称
     */
    @Schema(description = "智能体类型名称", example = "聊天型")
    private String typeName;

    /**
     * 模型配置（JSON格式）
     */
    @Schema(description = "模型配置", example = "{\"model\":\"gpt-3.5-turbo\",\"temperature\":0.7}")
    private String modelConfig;

    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词", example = "你是一个专业的客服助手...")
    private String systemPrompt;

    /**
     * 工具配置（JSON格式）
     */
    @Schema(description = "工具配置", example = "[{\"name\":\"search\",\"enabled\":true}]")
    private String toolsConfig;

    /**
     * 知识库配置（JSON格式）
     */
    @Schema(description = "知识库配置", example = "{\"datasets\":[\"kb_001\",\"kb_002\"]}")
    private String knowledgeConfig;

    /**
     * 对话配置（JSON格式）
     */
    @Schema(description = "对话配置", example = "{\"maxTokens\":2000,\"temperature\":0.7}")
    private String conversationConfig;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称", example = "启用")
    private String statusName;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "false")
    private Boolean isPublic;

    /**
     * 排序号
     */
    @Schema(description = "排序号", example = "1")
    private Integer sortOrder;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1.0.0")
    private String version;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private LocalDateTime publishedAt;

    /**
     * 最后对话时间
     */
    @Schema(description = "最后对话时间")
    private LocalDateTime lastConversationAt;

    /**
     * 对话次数
     */
    @Schema(description = "对话次数", example = "100")
    private Long conversationCount;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID", example = "tenant_001")
    private String tenantId;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "这是一个测试智能体")
    private String remark;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "user_001")
    private String createBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createByName;

    /**
     * 创建人用户名
     */
    @Schema(description = "创建人用户名", example = "admin")
    private String createByUsername;

    /**
     * 创建人部门名称
     */
    @Schema(description = "创建人部门名称", example = "技术部")
    private String createByDeptName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人", example = "admin")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 智能体来源类型：platform-本平台，external-外部智能体
     */
    @Schema(description = "智能体来源类型", example = "platform")
    private String sourceType;

    /**
     * 应用ID（对应external_agent_id字段，用于AI探索页面）
     */
    @Schema(description = "应用ID", example = "app_001")
    private String appId;

    /**
     * 智能体来源类型名称
     */
    @Schema(description = "智能体来源类型名称", example = "本平台")
    private String sourceTypeName;

    /**
     * 外部智能体ID（当sourceType为external时使用）
     */
    @Schema(description = "外部智能体ID", example = "ext_agent_001")
    private String externalAgentId;

    /**
     * 第三方智能体平台ID
     */
    @Schema(description = "第三方智能体平台ID", example = "platform_001")
    private String platformId;

    /**
     * 外部智能体名称
     */
    @Schema(description = "外部智能体名称", example = "ChatGPT智能助手")
    private String externalAgentName;

    /**
     * 外部智能体详细信息（JSON格式） 包含平台信息、连接状态、配置详情等
     */
    @Schema(description = "外部智能体详细信息", example = "{\"platform\":\"Dify\",\"status\":\"connected\",\"lastSync\":\"2024-01-15 10:30:00\"}")
    private String externalAgentDetails;

    /**
     * 第三方智能体信息（通过关联查询获取）
     */
    @Schema(description = "第三方智能体信息")
    private ThirdPlatform thirdPlatform;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getIconBackground() {
        return iconBackground;
    }

    public void setIconBackground(String iconBackground) {
        this.iconBackground = iconBackground;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getModelConfig() {
        return modelConfig;
    }

    public void setModelConfig(String modelConfig) {
        this.modelConfig = modelConfig;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public String getToolsConfig() {
        return toolsConfig;
    }

    public void setToolsConfig(String toolsConfig) {
        this.toolsConfig = toolsConfig;
    }

    public String getKnowledgeConfig() {
        return knowledgeConfig;
    }

    public void setKnowledgeConfig(String knowledgeConfig) {
        this.knowledgeConfig = knowledgeConfig;
    }

    public String getConversationConfig() {
        return conversationConfig;
    }

    public void setConversationConfig(String conversationConfig) {
        this.conversationConfig = conversationConfig;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public LocalDateTime getLastConversationAt() {
        return lastConversationAt;
    }

    public void setLastConversationAt(LocalDateTime lastConversationAt) {
        this.lastConversationAt = lastConversationAt;
    }

    public Long getConversationCount() {
        return conversationCount;
    }

    public void setConversationCount(Long conversationCount) {
        this.conversationCount = conversationCount;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName;
    }

    public String getCreateByUsername() {
        return createByUsername;
    }

    public void setCreateByUsername(String createByUsername) {
        this.createByUsername = createByUsername;
    }

    public String getCreateByDeptName() {
        return createByDeptName;
    }

    public void setCreateByDeptName(String createByDeptName) {
        this.createByDeptName = createByDeptName;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceTypeName() {
        return sourceTypeName;
    }

    public void setSourceTypeName(String sourceTypeName) {
        this.sourceTypeName = sourceTypeName;
    }

    public String getExternalAgentId() {
        return externalAgentId;
    }

    public void setExternalAgentId(String externalAgentId) {
        this.externalAgentId = externalAgentId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getExternalAgentName() {
        return externalAgentName;
    }

    public void setExternalAgentName(String externalAgentName) {
        this.externalAgentName = externalAgentName;
    }

    public String getExternalAgentDetails() {
        return externalAgentDetails;
    }

    public void setExternalAgentDetails(String externalAgentDetails) {
        this.externalAgentDetails = externalAgentDetails;
    }

    public ThirdPlatform getThirdPlatform() {
        return thirdPlatform;
    }

    public void setThirdPlatform(ThirdPlatform thirdPlatform) {
        this.thirdPlatform = thirdPlatform;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

}
