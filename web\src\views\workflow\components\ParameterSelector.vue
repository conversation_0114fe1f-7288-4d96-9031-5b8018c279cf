<template>
  <div class="parameter-selector">
    <div class="selector-header">
      <label class="selector-label">{{ label }}</label>
      <button 
        v-if="showParameterButton"
        class="parameter-button"
        @click="showParameterModal = true"
        :disabled="availableParameters.length === 0"
      >
        <i class="fas fa-link"></i>
        选择参数
      </button>
    </div>

    <!-- 输入框 -->
    <div class="input-container">
      <input
        v-model="inputValue"
        :type="inputType"
        :placeholder="placeholder"
        class="parameter-input"
        :class="{ 'has-reference': isParameterReference }"
        @input="handleInput"
        @blur="handleBlur"
      />
      <div v-if="isParameterReference" class="reference-indicator">
        <i class="fas fa-link"></i>
        <span>{{ referenceInfo?.nodeName }}.{{ referenceInfo?.paramKey }}</span>
      </div>
    </div>

    <!-- 参数选择模态框 -->
    <div v-if="showParameterModal" class="parameter-modal-overlay" @click="closeModal">
      <div class="parameter-modal" @click.stop>
        <div class="modal-header">
          <h3>选择参数</h3>
          <button class="close-button" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="modal-content">
          <div class="search-box">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索参数..."
              class="search-input"
            />
          </div>

          <div class="parameter-list">
            <div v-if="groupedParameters.length === 0" class="no-parameters">
              <i class="fas fa-info-circle"></i>
              <span>没有可用的上游参数</span>
            </div>

            <!-- 按节点分组显示参数 -->
            <div v-for="group in groupedParameters" :key="group.nodeId" class="parameter-group">
              <div class="group-header">
                <div class="group-info">
                  <i class="fas fa-cube"></i>
                  <span class="group-name">{{ group.nodeName }}</span>
                  <span class="group-id">{{ group.nodeId }}</span>
                </div>
                <span class="param-count">{{ group.parameters.length }} 个参数</span>
              </div>

              <div class="group-parameters">
                <div
                  v-for="param in group.parameters"
                  :key="`${param.nodeId}-${param.paramKey}`"
                  class="parameter-item"
                  @click="selectParameter(param)"
                >
                  <div class="parameter-info">
                    <div class="parameter-name">{{ param.label }}</div>
                    <div class="parameter-key">{{ param.paramKey }}</div>
                    <div v-if="param.description" class="parameter-description">
                      {{ param.description }}
                    </div>
                  </div>
                  <div class="parameter-type">{{ param.paramType }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeModal">取消</button>
          <button class="btn btn-primary" @click="clearReference" v-if="isParameterReference">
            清除引用
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ParameterOption } from '../composables/useNodeParameters'

// Props
interface Props {
  modelValue: any
  label: string
  placeholder?: string
  type?: 'text' | 'number' | 'password' | 'email'
  availableParameters?: ParameterOption[]
  showParameterButton?: boolean
  nodeId?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  type: 'text',
  availableParameters: () => [],
  showParameterButton: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'parameter-selected': [parameter: ParameterOption]
}>()

// 响应式数据
const inputValue = ref(props.modelValue)
const showParameterModal = ref(false)
const searchQuery = ref('')

// 计算属性
const inputType = computed(() => {
  if (isParameterReference.value) return 'text'
  return props.type
})

const isParameterReference = computed(() => {
  return typeof inputValue.value === 'string' && 
         inputValue.value.startsWith('{{') && 
         inputValue.value.endsWith('}}')
})

const referenceInfo = computed(() => {
  if (!isParameterReference.value) return null
  
  const match = inputValue.value.match(/^\{\{([^.]+)\.([^}]+)\}\}$/)
  if (match) {
    const param = props.availableParameters.find(p => 
      p.nodeId === match[1] && p.paramKey === match[2]
    )
    return param ? {
      nodeId: match[1],
      paramKey: match[2],
      nodeName: param.nodeName
    } : null
  }
  return null
})

const filteredParameters = computed(() => {
  if (!searchQuery.value) return props.availableParameters

  const query = searchQuery.value.toLowerCase()
  return props.availableParameters.filter(param =>
    param.label.toLowerCase().includes(query) ||
    param.nodeName.toLowerCase().includes(query) ||
    param.paramKey.toLowerCase().includes(query) ||
    (param.description && param.description.toLowerCase().includes(query))
  )
})

const groupedParameters = computed(() => {
  const groups = new Map()

  filteredParameters.value.forEach(param => {
    if (!groups.has(param.nodeId)) {
      groups.set(param.nodeId, {
        nodeId: param.nodeId,
        nodeName: param.nodeName,
        parameters: []
      })
    }
    groups.get(param.nodeId).parameters.push(param)
  })

  return Array.from(groups.values()).sort((a, b) => a.nodeName.localeCompare(b.nodeName))
})

// 方法
const handleInput = () => {
  emit('update:modelValue', inputValue.value)
}

const handleBlur = () => {
  // 验证参数引用的有效性
  if (isParameterReference.value && !referenceInfo.value) {
    // 如果是无效的参数引用，清除它
    inputValue.value = ''
    emit('update:modelValue', inputValue.value)
  }
}

const selectParameter = (param: ParameterOption) => {
  inputValue.value = param.value
  emit('update:modelValue', inputValue.value)
  emit('parameter-selected', param)
  closeModal()
}

const clearReference = () => {
  inputValue.value = ''
  emit('update:modelValue', inputValue.value)
  closeModal()
}

const closeModal = () => {
  showParameterModal.value = false
  searchQuery.value = ''
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
}, { immediate: true })
</script>

<style scoped>
.parameter-selector {
  margin-bottom: 16px;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.selector-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.parameter-button {
  padding: 4px 8px;
  font-size: 11px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.parameter-button:hover:not(:disabled) {
  background: #e5e7eb;
  color: #374151;
}

.parameter-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-container {
  position: relative;
}

.parameter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  transition: border-color 0.2s;
}

.parameter-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.parameter-input.has-reference {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #1e40af;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
}

.reference-indicator {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #eff6ff;
  border: 1px solid #3b82f6;
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 4px 8px;
  font-size: 11px;
  color: #1e40af;
  display: flex;
  align-items: center;
  gap: 4px;
}

.parameter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.parameter-modal {
  background: white;
  border-radius: 12px;
  width: 500px;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-box {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
}

.parameter-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.no-parameters {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 14px;
}

.parameter-group {
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.parameter-group:last-child {
  margin-bottom: 0;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-name {
  color: #1f2937;
  font-size: 14px;
}

.group-id {
  color: #6b7280;
  font-size: 11px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.param-count {
  color: #6b7280;
  font-size: 12px;
  background: #e5e7eb;
  padding: 2px 8px;
  border-radius: 12px;
}

.group-parameters {
  background: #ffffff;
}

.parameter-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f3f4f6;
}

.parameter-item:last-child {
  border-bottom: none;
}

.parameter-item:hover {
  background: #f9fafb;
}

.parameter-info {
  flex: 1;
}

.parameter-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
  margin-bottom: 2px;
}

.parameter-key {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.parameter-description {
  font-size: 11px;
  color: #9ca3af;
  line-height: 1.4;
}

.parameter-type {
  background: #f3f4f6;
  color: #6b7280;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
