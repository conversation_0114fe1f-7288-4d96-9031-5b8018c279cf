<template>
  <div class="agent-runner-manager">
    <!-- 最小化任务栏 -->
    <MinimizedTaskbar
      :running-agents="minimizedAgents"
      @restore="restoreRunner"
      @close="closeSpecificAgent"
    />

    <!-- 智能体运行弹出层 -->
    <AgentRunnerWindow
      :visible="runnerModalVisible"
      :agent-info="currentRunningAgent"
      :is-minimized="isMinimized"
      :is-maximized="isMaximized"
      :position="windowPosition"
      :size="windowSize"
      @minimize="minimizeRunner"
      @close="closeRunnerModal"
      @update:position="updatePosition"
      @update:size="updateSize"
      @update:is-maximized="updateMaximized"
    />
  </div>
</template>

<script setup lang="ts">
import AgentRunnerWindow from './AgentRunnerWindow.vue'
import MinimizedTaskbar from './MinimizedTaskbar.vue'
import { useAgentRunner, type AgentRunnerEvents } from '@/composables/useAgentRunner'

// Props
interface Props {
  events?: AgentRunnerEvents
}

const props = withDefaults(defineProps<Props>(), {
  events: () => ({})
})

// 使用 composable
const {
  runnerModalVisible,
  isMinimized,
  isMaximized,
  windowPosition,
  windowSize,
  currentRunningAgent,
  minimizedAgents,
  minimizeRunner,
  closeRunnerModal,
  restoreRunner,
  closeSpecificAgent,
  updatePosition,
  updateSize,
  updateMaximized,
  openRunnerModal
} = useAgentRunner(props.events)

// 暴露方法给父组件
defineExpose({
  openRunnerModal
})
</script>

<style scoped>
.agent-runner-manager {
  position: relative;
}
</style>
