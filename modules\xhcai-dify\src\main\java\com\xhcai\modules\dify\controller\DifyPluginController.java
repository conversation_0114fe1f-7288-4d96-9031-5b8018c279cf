package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.dify.dto.plugin.DifyPluginDTO;
import com.xhcai.modules.dify.service.IDifyPluginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 插件控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Tag(name = "插件管理", description = "Dify插件服务对接")
@RestController
@RequestMapping("/api/dify/plugins")
public class DifyPluginController {

    @Autowired
    private IDifyPluginService pluginService;

    @Operation(summary = "获取插件列表", description = "分页查询插件列表")
    @GetMapping
    @RequiresPermissions("dify:plugin:list")
    public Mono<Result<PageResult<DifyPluginDTO>>> getPluginList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "分类") @RequestParam(required = false) String category,
            @Parameter(description = "类型") @RequestParam(required = false) String type,
            @Parameter(description = "状态") @RequestParam(required = false) String status) {
        return pluginService.getPluginList(page, size, keyword, category, type, status);
    }

    @Operation(summary = "获取插件详情", description = "根据ID获取插件详细信息")
    @GetMapping("/{pluginId}")
    @RequiresPermissions("dify:plugin:view")
    public Mono<Result<DifyPluginDTO>> getPlugin(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.getPlugin(pluginId);
    }

    @Operation(summary = "安装插件", description = "安装指定的插件")
    @PostMapping("/{pluginId}/install")
    @RequiresPermissions("dify:plugin:install")
    public Mono<Result<DifyPluginDTO>> installPlugin(
            @Parameter(description = "插件ID") @PathVariable String pluginId,
            @Parameter(description = "插件配置") @RequestBody(required = false) Map<String, Object> config) {
        return pluginService.installPlugin(pluginId, config);
    }

    @Operation(summary = "卸载插件", description = "卸载指定的插件")
    @DeleteMapping("/{pluginId}/uninstall")
    @RequiresPermissions("dify:plugin:uninstall")
    public Mono<Result<Object>> uninstallPlugin(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.uninstallPlugin(pluginId);
    }

    @Operation(summary = "更新插件配置", description = "更新插件的配置信息")
    @PutMapping("/{pluginId}/config")
    @RequiresPermissions("dify:plugin:config")
    public Mono<Result<DifyPluginDTO>> updatePluginConfig(
            @Parameter(description = "插件ID") @PathVariable String pluginId,
            @Parameter(description = "插件配置") @RequestBody Map<String, Object> config) {
        return pluginService.updatePluginConfig(pluginId, config);
    }

    @Operation(summary = "启用插件", description = "启用指定的插件")
    @PostMapping("/{pluginId}/enable")
    @RequiresPermissions("dify:plugin:manage")
    public Mono<Result<Object>> enablePlugin(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.enablePlugin(pluginId);
    }

    @Operation(summary = "禁用插件", description = "禁用指定的插件")
    @PostMapping("/{pluginId}/disable")
    @RequiresPermissions("dify:plugin:manage")
    public Mono<Result<Object>> disablePlugin(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.disablePlugin(pluginId);
    }

    @Operation(summary = "调用插件工具", description = "调用插件中的指定工具")
    @PostMapping("/{pluginId}/tools/{toolName}/invoke")
    @RequiresPermissions("dify:plugin:invoke")
    public Mono<Result<Object>> invokePluginTool(
            @Parameter(description = "插件ID") @PathVariable String pluginId,
            @Parameter(description = "工具名称") @PathVariable String toolName,
            @Parameter(description = "工具参数") @RequestBody Map<String, Object> parameters) {
        return pluginService.invokePluginTool(pluginId, toolName, parameters);
    }

    @Operation(summary = "获取插件工具列表", description = "获取插件中所有可用的工具")
    @GetMapping("/{pluginId}/tools")
    @RequiresPermissions("dify:plugin:view")
    public Mono<Result<Object>> getPluginTools(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.getPluginTools(pluginId);
    }

    @Operation(summary = "获取已安装插件列表", description = "获取当前已安装的插件列表")
    @GetMapping("/installed")
    @RequiresPermissions("dify:plugin:list")
    public Mono<Result<PageResult<DifyPluginDTO>>> getInstalledPlugins(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        return pluginService.getInstalledPlugins(page, size);
    }

    @Operation(summary = "获取插件统计信息", description = "获取插件的使用统计信息")
    @GetMapping("/{pluginId}/stats")
    @RequiresPermissions("dify:plugin:view")
    public Mono<Result<Object>> getPluginStats(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.getPluginStats(pluginId);
    }

    @Operation(summary = "检查插件更新", description = "检查插件是否有可用更新")
    @GetMapping("/{pluginId}/check-update")
    @RequiresPermissions("dify:plugin:view")
    public Mono<Result<Object>> checkPluginUpdate(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.checkPluginUpdate(pluginId);
    }

    @Operation(summary = "更新插件", description = "更新插件到最新版本")
    @PostMapping("/{pluginId}/update")
    @RequiresPermissions("dify:plugin:update")
    public Mono<Result<DifyPluginDTO>> updatePlugin(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.updatePlugin(pluginId);
    }

    @Operation(summary = "获取插件日志", description = "获取插件的运行日志")
    @GetMapping("/{pluginId}/logs")
    @RequiresPermissions("dify:plugin:view")
    public Mono<Result<PageResult<Object>>> getPluginLogs(
            @Parameter(description = "插件ID") @PathVariable String pluginId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        return pluginService.getPluginLogs(pluginId, page, size);
    }

    @Operation(summary = "测试插件连接", description = "测试插件的连接状态")
    @PostMapping("/{pluginId}/test")
    @RequiresPermissions("dify:plugin:test")
    public Mono<Result<Object>> testPluginConnection(
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        return pluginService.testPluginConnection(pluginId);
    }
}
