package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.rag.entity.DocumentPermission;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 文档权限Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface DocumentPermissionMapper extends BaseMapper<DocumentPermission> {

    /**
     * 根据文档ID查询权限
     */
    @Select("SELECT * FROM document_permissions "
            + "WHERE document_id = #{documentId} "
            + "AND tenant_id = #{tenantId} "
            + "AND deleted = 0 "
            + "AND enabled = true")
    List<DocumentPermission> selectByDocumentId(@Param("tenantId") String tenantId, @Param("documentId") String documentId);

    /**
     * 根据文档ID列表查询权限
     */
    @Select("<script>"
            + "SELECT * FROM document_permissions "
            + "WHERE tenant_id = #{tenantId} "
            + "AND deleted = 0 "
            + "AND enabled = true "
            + "AND document_id IN "
            + "<foreach collection='documentIds' item='documentId' open='(' separator=',' close=')'>"
            + "#{documentId}"
            + "</foreach>"
            + "</script>")
    List<DocumentPermission> selectByDocumentIds(@Param("tenantId") String tenantId, @Param("documentIds") List<String> documentIds);

    /**
     * 删除文档的所有权限
     */
    @Delete("UPDATE document_permissions SET deleted = 1 "
            + "WHERE document_id = #{documentId} "
            + "AND tenant_id = #{tenantId}")
    int deleteByDocumentId(@Param("tenantId") String tenantId, @Param("documentId") String documentId);

    /**
     * 批量删除文档权限
     */
    @Delete("<script>"
            + "UPDATE document_permissions SET deleted = 1 "
            + "WHERE tenant_id = #{tenantId} "
            + "AND document_id IN "
            + "<foreach collection='documentIds' item='documentId' open='(' separator=',' close=')'>"
            + "#{documentId}"
            + "</foreach>"
            + "</script>")
    int deleteByDocumentIds(@Param("tenantId") String tenantId, @Param("documentIds") List<String> documentIds);

    /**
     * 检查用户是否有文档访问权限
     */
    @Select("<script>"
            + "SELECT COUNT(*) > 0 FROM document_permissions dp "
            + "WHERE dp.document_id = #{documentId} "
            + "AND dp.tenant_id = #{tenantId} "
            + "AND dp.deleted = 0 "
            + "AND dp.enabled = true "
            + "AND ("
            + "  dp.permission_type = 'public' "
            + "  OR (dp.permission_type = 'private' AND dp.target_id = #{userId}) "
            + "  OR (dp.permission_type = 'users' AND dp.target_id = #{userId}) "
            + "  OR (dp.permission_type = 'role' AND dp.target_id IN "
            + "    <foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>"
            + "      #{roleId}"
            + "    </foreach>"
            + "  ) "
            + "  OR (dp.permission_type = 'department' AND dp.target_id IN "
            + "    <foreach collection='departmentIds' item='deptId' open='(' separator=',' close=')'>"
            + "      #{deptId}"
            + "    </foreach>"
            + "  )"
            + ")"
            + "</script>")
    boolean hasDocumentAccess(@Param("tenantId") String tenantId,
            @Param("documentId") String documentId,
            @Param("userId") String userId,
            @Param("roleIds") List<String> roleIds,
            @Param("departmentIds") List<String> departmentIds);
}
