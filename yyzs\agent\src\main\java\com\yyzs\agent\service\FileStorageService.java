package com.yyzs.agent.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;

/**
 * 文件存储服务接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface FileStorageService {

    /**
     * 存储文件
     */
    String storeFile(MultipartFile file, String directory) throws IOException;

    /**
     * 删除文件
     */
    boolean deleteFile(String filePath);

    /**
     * 获取文件路径
     */
    Path getFilePath(String fileName, String directory);

    /**
     * 检查文件是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 获取文件大小
     */
    long getFileSize(String filePath);

    /**
     * 创建目录
     */
    boolean createDirectory(String directory);

    /**
     * 清理目录
     */
    boolean cleanDirectory(String directory);

    /**
     * 获取文件扩展名
     */
    String getFileExtension(String fileName);

    /**
     * 生成唯一文件名
     */
    String generateUniqueFileName(String originalFileName);
}
