package com.xhcai.modules.dify.config;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;

import com.xhcai.common.core.config.YamlPropertySourceFactory;

/**
 * Dify模块自动配置类 负责Dify模块的组件扫描和配置文件加载 注意：Dify模块目前没有Entity和Mapper，只有纯API集成
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@AutoConfiguration
@ComponentScan(basePackages = {
    "com.xhcai.modules.dify"
})
@EntityScan(basePackages = "com.xhcai.modules.dify.entity")
@MapperScan(basePackages = "com.xhcai.modules.dify.mapper")
@ConfigurationPropertiesScan(basePackages = "com.xhcai.modules.dify.config")
@PropertySource(value = "classpath:application-xhcai-dify.yml", factory = YamlPropertySourceFactory.class)
public class DifyAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(DifyAutoConfiguration.class);

    public DifyAutoConfiguration() {
        log.info("=== Dify模块自动配置已启用 ===");
        log.info("组件扫描包: com.xhcai.modules.dify.*");
        log.info("配置文件: application-xhcai-dify.yml");
        log.info("注意: Dify模块为纯API集成，无Entity和Mapper");
    }
}
