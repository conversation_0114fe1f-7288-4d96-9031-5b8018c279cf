
/**
 * 配置组件注册系统
 * 管理所有节点的配置组件
 */

import type { Component } from 'vue'

// 配置组件类型
export type ConfigComponent = Component

// 配置组件注册信息
export interface ConfigRegistration {
  nodeType: string
  category: string
  component: ConfigComponent
  title: string
  description: string
}

/**
 * 配置组件注册表类
 */
class ConfigRegistryClass {
  private configs = new Map<string, ConfigRegistration>()
  private configsByCategory = new Map<string, string[]>()

  /**
   * 注册配置组件
   */
  registerConfig(registration: ConfigRegistration): void {
    // 注册配置组件
    this.configs.set(registration.nodeType, registration)
    
    // 添加到类别映射
    const categoryConfigs = this.configsByCategory.get(registration.category) || []
    if (!categoryConfigs.includes(registration.nodeType)) {
      categoryConfigs.push(registration.nodeType)
      this.configsByCategory.set(registration.category, categoryConfigs)
    }
  }

  /**
   * 批量注册配置组件
   */
  registerConfigs(registrations: ConfigRegistration[]): void {
    registrations.forEach(registration => this.registerConfig(registration))
  }

  /**
   * 获取配置组件注册信息
   */
  getConfig(nodeType: string): ConfigRegistration | undefined {
    return this.configs.get(nodeType)
  }

  /**
   * 获取配置组件
   */
  getConfigComponent(nodeType: string): ConfigComponent | undefined {
    const registration = this.configs.get(nodeType)
    return registration?.component
  }

  /**
   * 获取所有已注册的节点类型
   */
  getAllNodeTypes(): string[] {
    return Array.from(this.configs.keys())
  }

  /**
   * 获取类别下的所有配置组件
   */
  getConfigsByCategory(category: string): string[] {
    return this.configsByCategory.get(category) || []
  }

  /**
   * 检查配置组件是否存在
   */
  hasConfig(nodeType: string): boolean {
    return this.configs.has(nodeType)
  }

  /**
   * 取消注册配置组件
   */
  unregisterConfig(nodeType: string): boolean {
    const registration = this.configs.get(nodeType)
    if (!registration) {
      return false
    }

    // 从配置映射中删除
    this.configs.delete(nodeType)

    // 从类别映射中删除
    const categoryConfigs = this.configsByCategory.get(registration.category)
    if (categoryConfigs) {
      const index = categoryConfigs.indexOf(nodeType)
      if (index > -1) {
        categoryConfigs.splice(index, 1)
      }
    }

    return true
  }

  /**
   * 清空所有注册信息
   */
  clear(): void {
    this.configs.clear()
    this.configsByCategory.clear()
  }

  /**
   * 获取注册统计信息
   */
  getStats(): {
    totalConfigs: number
    configsByCategory: Record<string, number>
  } {
    const configsByCategory: Record<string, number> = {}
    this.configsByCategory.forEach((configs, category) => {
      configsByCategory[category] = configs.length
    })

    return {
      totalConfigs: this.configs.size,
      configsByCategory
    }
  }

  /**
   * 搜索配置组件
   */
  searchConfigs(options: {
    category?: string
    keyword?: string
  }): string[] {
    let results = this.getAllNodeTypes()

    // 按类别过滤
    if (options.category) {
      results = results.filter(nodeType => {
        const registration = this.configs.get(nodeType)
        return registration?.category === options.category
      })
    }

    // 按关键词过滤
    if (options.keyword) {
      const keyword = options.keyword.toLowerCase()
      results = results.filter(nodeType => {
        const registration = this.configs.get(nodeType)
        if (!registration) return false
        
        return (
          registration.title.toLowerCase().includes(keyword) ||
          registration.description.toLowerCase().includes(keyword) ||
          nodeType.toLowerCase().includes(keyword)
        )
      })
    }

    return results
  }
}

// 导出单例实例
export const ConfigRegistry = new ConfigRegistryClass()

// 导出类型
export type { ConfigRegistryClass }

/**
 * 配置组件工厂
 */
export class ConfigFactory {
  /**
   * 创建配置组件实例
   */
  static createConfig(nodeType: string, props: Record<string, any> = {}) {
    const component = ConfigRegistry.getConfigComponent(nodeType)
    if (!component) {
      console.warn(`Config component for node type "${nodeType}" not found`)
      return null
    }

    return {
      component,
      props,
      nodeType
    }
  }

  /**
   * 检查是否可以创建配置组件
   */
  static canCreateConfig(nodeType: string): boolean {
    return ConfigRegistry.hasConfig(nodeType)
  }

  /**
   * 获取可用的配置组件类型
   */
  static getAvailableConfigTypes(): string[] {
    return ConfigRegistry.getAllNodeTypes()
  }
}
