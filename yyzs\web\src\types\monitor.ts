/**
 * 进程状态枚举
 */
export enum ProcessStatus {
  RUNNING = 'RUNNING',
  STOPPED = 'STOPPED',
  ERROR = 'ERROR',
}

/**
 * 进程状态描述映射
 */
export const ProcessStatusLabels: Record<ProcessStatus, string> = {
  [ProcessStatus.RUNNING]: '运行中',
  [ProcessStatus.STOPPED]: '已停止',
  [ProcessStatus.ERROR]: '错误',
};

/**
 * 组件监控数据接口
 */
export interface ComponentMonitor {
  id: string;
  componentId: string;
  cpuUsage?: number;
  memoryUsage?: number;
  memoryUsagePercent?: number;
  diskUsage?: number;
  diskUsagePercent?: number;
  networkIn?: number;
  networkOut?: number;
  processStatus?: ProcessStatus;
  processId?: number;
  threadCount?: number;
  fileDescriptorCount?: number;
  uptime?: number;
  responseTime?: number;
  isHealthy: boolean;
  healthMessage?: string;
  errorMessage?: string;
  createTime: string;
  updateTime: string;
}

/**
 * 系统资源使用情况
 */
export interface SystemResourceUsage {
  cpuUsage: number;
  totalMemory: number;
  usedMemory: number;
  availableMemory: number;
  memoryUsagePercent: number;
  totalDisk: number;
  usedDisk: number;
  availableDisk: number;
  diskUsagePercent: number;
}

/**
 * 组件性能统计
 */
export interface ComponentPerformanceStats {
  avgCpuUsage: number;
  avgMemoryUsage: number;
  maxCpuUsage: number;
  maxMemoryUsage: number;
  dataPoints: number;
}

/**
 * 监控状态
 */
export interface MonitoringStatus {
  active: boolean;
  status: string;
}

/**
 * 告警级别
 */
export enum AlertLevel {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL',
}

/**
 * 告警记录
 */
export interface AlertRecord {
  id: string;
  componentId: string;
  level: AlertLevel;
  message: string;
  timestamp: string;
  resolved: boolean;
  resolvedTime?: string;
}

/**
 * 监控图表数据点
 */
export interface MonitorDataPoint {
  timestamp: string;
  cpuUsage?: number;
  memoryUsage?: number;
  diskUsage?: number;
  networkIn?: number;
  networkOut?: number;
  responseTime?: number;
}

/**
 * 监控时间范围
 */
export interface MonitorTimeRange {
  startTime: string;
  endTime: string;
}

/**
 * 监控配置
 */
export interface MonitorConfig {
  intervalSeconds: number;
  enableAlerts: boolean;
  cpuThreshold: number;
  memoryThreshold: number;
  diskThreshold: number;
  responseTimeThreshold: number;
}
