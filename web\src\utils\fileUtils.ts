/**
 * 文件工具类
 * 统一管理文件相关的处理函数
 */

/**
 * 获取文件类型对应的图标
 * @param type 文件类型
 * @returns 文件图标 emoji
 */
export const getFileIcon = (type: string): string => {
  if (!type) return '📄'
  
  const icons: Record<string, string> = {
    // 文档类型
    pdf: '📄',
    doc: '📝',
    docx: '📝',
    txt: '📃',
    md: '📋',
    rtf: '📝',
    
    // 表格类型
    xlsx: '📊',
    xls: '📊',
    csv: '📈',
    
    // 演示文稿
    ppt: '📽️',
    pptx: '📽️',
    
    // 图片类型
    jpg: '🖼️',
    jpeg: '🖼️',
    png: '🖼️',
    gif: '🎞️',
    bmp: '🖼️',
    svg: '🎨',
    webp: '🖼️',
    ico: '🖼️',
    
    // 音频类型
    mp3: '🎵',
    wav: '🎵',
    flac: '🎵',
    aac: '🎵',
    ogg: '🎵',
    
    // 视频类型
    mp4: '🎬',
    avi: '🎬',
    mov: '🎬',
    wmv: '🎬',
    flv: '🎬',
    mkv: '🎬',
    
    // 压缩文件
    zip: '📦',
    rar: '📦',
    '7z': '📦',
    tar: '📦',
    gz: '📦',
    
    // 代码文件
    js: '📜',
    ts: '📜',
    jsx: '📜',
    tsx: '📜',
    vue: '📜',
    html: '🌐',
    css: '🎨',
    scss: '🎨',
    sass: '🎨',
    less: '🎨',
    json: '📋',
    xml: '📋',
    yaml: '📋',
    yml: '📋',
    
    // 编程语言
    py: '🐍',
    java: '☕',
    cpp: '⚙️',
    c: '⚙️',
    cs: '🔷',
    php: '🐘',
    rb: '💎',
    go: '🐹',
    rs: '🦀',
    swift: '🦉',
    kt: '🎯',
    
    // 数据库
    sql: '🗄️',
    db: '🗄️',
    sqlite: '🗄️',
    
    // 配置文件
    ini: '⚙️',
    conf: '⚙️',
    config: '⚙️',
    env: '⚙️',
    
    // 日志文件
    log: '📝',
    
    // 其他
    exe: '⚙️',
    dmg: '💿',
    iso: '💿',
    apk: '📱',
    ipa: '📱'
  }
  
  return icons[type.toLowerCase()] || '📄'
}

/**
 * 获取文件类型对应的CSS类名
 * @param type 文件类型
 * @returns CSS类名
 */
export const getFileTypeClass = (type: string): string => {
  if (!type) return 'file-type-unknown'
  return `file-type-${type.toLowerCase()}`
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字，超过1000使用K为单位
 * @param num 数字
 * @param decimals 小数位数，默认为1
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (num: number, decimals: number = 1): string => {
  if (!num || num === 0) return '0'
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(decimals) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(decimals) + 'K'
  }
  
  return num.toLocaleString()
}

/**
 * 格式化字符数，超过1000使用K为单位
 * @param num 字符数
 * @param decimals 小数位数，默认为1
 * @returns 格式化后的字符数字符串
 */
export const formatCharCount = (num: number, decimals: number = 1): string => {
  return formatNumber(num, decimals)
}

/**
 * 格式化日期时间
 * @param dateStr 日期字符串
 * @param locale 地区设置，默认为'zh-CN'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (dateStr: string, locale: string = 'zh-CN'): string => {
  if (!dateStr) return ''
  
  try {
    return new Date(dateStr).toLocaleString(locale)
  } catch (error) {
    console.warn('Invalid date string:', dateStr)
    return dateStr
  }
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 文件扩展名（小写）
 */
export const getFileExtension = (filename: string): string => {
  if (!filename) return ''
  
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
    return ''
  }
  
  return filename.slice(lastDotIndex + 1).toLowerCase()
}

/**
 * 根据文件名获取文件类型
 * @param filename 文件名
 * @returns 文件类型
 */
export const getFileTypeFromName = (filename: string): string => {
  return getFileExtension(filename)
}

/**
 * 检查是否为图片文件
 * @param type 文件类型
 * @returns 是否为图片文件
 */
export const isImageFile = (type: string): boolean => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico']
  return imageTypes.includes(type.toLowerCase())
}

/**
 * 检查是否为文档文件
 * @param type 文件类型
 * @returns 是否为文档文件
 */
export const isDocumentFile = (type: string): boolean => {
  const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf']
  return documentTypes.includes(type.toLowerCase())
}

/**
 * 检查是否为表格文件
 * @param type 文件类型
 * @returns 是否为表格文件
 */
export const isSpreadsheetFile = (type: string): boolean => {
  const spreadsheetTypes = ['xlsx', 'xls', 'csv']
  return spreadsheetTypes.includes(type.toLowerCase())
}

/**
 * 检查是否为代码文件
 * @param type 文件类型
 * @returns 是否为代码文件
 */
export const isCodeFile = (type: string): boolean => {
  const codeTypes = [
    'js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'scss', 'sass', 'less',
    'json', 'xml', 'yaml', 'yml', 'py', 'java', 'cpp', 'c', 'cs', 'php',
    'rb', 'go', 'rs', 'swift', 'kt', 'sql', 'ini', 'conf', 'config', 'env'
  ]
  return codeTypes.includes(type.toLowerCase())
}

/**
 * 获取权限文本
 * @param permission 权限类型
 * @returns 权限文本
 */
export const getPermissionText = (permission: string): string => {
  const permissionMap: Record<string, string> = {
    'public': '公开共享',
    'private': '个人私有',
    'role': '角色权限',
    'department': '单位权限',
    'users': '指定用户'
  }
  return permissionMap[permission] || '未知权限'
}

/**
 * 文件信息接口
 */
export interface FileInfo {
  id: string
  name: string
  type: string
  size: number
  segmentCount?: number
  charCount?: number
  uploadTime: string
  path?: string
  permission?: string
}
