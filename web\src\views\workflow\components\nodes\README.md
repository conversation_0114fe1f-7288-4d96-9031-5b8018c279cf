# 节点系统使用指南

## 概述

新的节点系统采用模块化设计，支持动态注册和管理工作流节点。系统包含以下核心组件：

- **NodeRegistry**: 节点注册表，管理所有已注册的节点
- **NodeFactory**: 节点工厂，负责创建节点实例
- **NodeManager**: 节点管理器，提供统一的管理接口
- **AutoRegister**: 自动注册系统，批量注册节点

## 目录结构

```
nodes/
├── basic/              # 基础节点（开始、结束、条件等）
├── database/           # 数据库节点
├── ai/                 # AI工具节点
├── file-generator/     # 文件生成节点
├── file-extractor/     # 文件提取节点
├── render/             # 渲染工具节点
├── data/               # 数据处理节点
├── utility/            # 工具节点
├── registry/           # 注册系统
├── factory/            # 工厂系统
├── manager/            # 管理器
├── examples/           # 使用示例
├── BaseNode.vue        # 基础节点组件
├── index.ts            # 统一入口
└── README.md           # 使用指南
```

## 快速开始

### 1. 初始化节点系统

```typescript
import { initializeNodeSystem } from '@/views/workflow/components/nodes'

// 初始化节点系统
await initializeNodeSystem()
```

### 2. 创建节点

```typescript
import { NodeManager } from '@/views/workflow/components/nodes'

// 创建默认节点
const startNode = NodeManager.createDefaultNode('start', { x: 100, y: 100 })

// 创建自定义配置的节点
const mysqlNode = NodeManager.createNode({
  type: 'mysql',
  data: {
    label: '用户数据库',
    config: {
      host: 'localhost',
      database: 'users',
      sql: 'SELECT * FROM users'
    }
  },
  position: { x: 200, y: 200 }
})
```

### 3. 搜索节点

```typescript
// 按类别搜索
const databaseNodes = NodeManager.searchNodes({
  category: '数据库工具'
})

// 按标签搜索
const aiNodes = NodeManager.searchNodes({
  tags: ['AI']
})

// 关键词搜索
const searchResults = NodeManager.searchNodes({
  keyword: 'mysql'
})
```

## 创建新节点

### 1. 创建节点组件

```vue
<!-- database/PostgreSQLNode.vue -->
<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="postgresql-node-content">
        <!-- 节点内容 -->
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import BaseNode from '../BaseNode.vue'
// ... 组件逻辑
</script>
```

### 2. 定义节点配置

```typescript
const postgresqlConfig: NodeLibraryItem = {
  type: "postgresql",
  label: "PostgreSQL",
  icon: "fa-solid fa-database",
  description: "PostgreSQL数据库操作",
  category: "database",
  color: "blue",
  gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  labelColor: "#ffffff",
  iconColor: "#ffffff",
  status: "stable",
  version: "1.0.0",
  tags: ["数据库", "PostgreSQL"],
  handles: {
    source: [{ id: "output", position: "right" }],
    target: [{ id: "input", position: "left" }]
  },
  defaultData: {
    label: "PostgreSQL",
    config: {
      host: "",
      port: 5432,
      database: "",
      sql: "SELECT * FROM table_name"
    }
  }
}
```

### 3. 注册节点

```typescript
import { NodeManager } from '@/views/workflow/components/nodes'
import PostgreSQLNode from './database/PostgreSQLNode.vue'

// 注册节点
NodeManager.registerNode(
  'postgresql',
  '数据库工具',
  PostgreSQLNode,
  postgresqlConfig
)
```

## 创建新节点类别

### 1. 注册类别

```typescript
NodeManager.registerCategory({
  name: '新类别',
  icon: 'fa-solid fa-star',
  description: '新的节点类别',
  order: 10
})
```

### 2. 在类别下注册节点

```typescript
NodeManager.registerNode(
  'new-node',
  '新类别',
  NewNodeComponent,
  newNodeConfig
)
```

## API 参考

### NodeManager

主要的节点管理接口：

```typescript
// 初始化
await NodeManager.initialize()

// 创建节点
NodeManager.createNode(options)
NodeManager.createDefaultNode(type, position)
NodeManager.cloneNode(sourceNode, position)

// 查询节点
NodeManager.getNodeConfig(type)
NodeManager.getNodeComponent(type)
NodeManager.hasNode(type)
NodeManager.getAllNodeTypes()
NodeManager.getNodesByCategory(category)
NodeManager.searchNodes(options)

// 管理类别
NodeManager.getAllCategories()
NodeManager.getCategory(name)
NodeManager.hasCategory(name)

// 注册管理
NodeManager.registerNode(type, category, component, config)
NodeManager.registerCategory(category)
NodeManager.unregisterNode(type)
NodeManager.unregisterCategory(name)

// 统计信息
NodeManager.getStats()
```

### 搜索选项

```typescript
interface NodeSearchOptions {
  category?: string      // 按类别过滤
  tags?: string[]       // 按标签过滤
  status?: string       // 按状态过滤
  keyword?: string      // 关键词搜索
}
```

### 节点创建选项

```typescript
interface NodeCreateOptions {
  type: string                           // 节点类型
  data?: Record<string, any>            // 节点数据
  position?: { x: number; y: number }   // 节点位置
  id?: string                           // 自定义ID
}
```

## 最佳实践

### 1. 节点组件设计

- 继承 `BaseNode` 组件
- 使用 `template #content` 插槽定义内容
- 保持组件的响应式和类型安全

### 2. 节点配置

- 提供完整的节点元信息
- 定义合理的默认数据
- 使用语义化的标签和描述

### 3. 类别组织

- 按功能逻辑分组节点
- 使用清晰的类别名称和图标
- 设置合理的显示顺序

### 4. 扩展性

- 使用统一的注册接口
- 支持动态加载和卸载
- 保持向后兼容性

## 示例代码

完整的使用示例请参考 `examples/RegisterMySQLNode.ts` 文件。

## 故障排除

### 常见问题

1. **节点未注册**: 确保在使用前调用了 `initialize()` 方法
2. **组件未找到**: 检查组件导入路径和注册信息
3. **类别不存在**: 先注册类别再注册节点
4. **配置错误**: 验证节点配置的完整性和正确性

### 调试方法

```typescript
// 检查系统状态
console.log(NodeManager.getStats())

// 检查节点是否存在
console.log(NodeManager.hasNode('mysql'))

// 获取所有已注册的节点
console.log(NodeManager.getAllNodeTypes())
```
