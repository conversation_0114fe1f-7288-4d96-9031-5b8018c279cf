<template>
  <div class="main-layout h-screen flex flex-col overflow-hidden">
    <!-- 顶部导航栏 -->
    <header class="header bg-white/95 backdrop-blur-xl border-b border-black/10 sticky top-0 z-50">
      <div class="nav-container max-w-none mx-0 px-4 flex items-center justify-between h-[70px]">
        <!-- Logo区域 -->
        <div class="logo-section flex items-center gap-3">
          <div class="logo w-10 h-10 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center text-xl text-blue-600 shadow-sm">
            🤖
          </div>
          <span class="logo-text text-xl font-bold text-gray-700 bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
            AI智能体平台
          </span>
        </div>

        <!-- 导航菜单 -->
        <nav>
          <ul class="nav-menu flex list-none gap-2">
            <li v-for="route in navRoutes" :key="route.name" class="nav-item">
              <router-link
                :to="route.path"
                class="nav-link flex items-center gap-2 px-5 py-3 text-gray-500 font-medium rounded-xl transition-all duration-300 hover:text-blue-500 hover:bg-blue-50 relative overflow-hidden"
                :class="{ 'active': $route.name === route.name }"
                active-class="text-blue-500 bg-blue-50"
              >
                <span
                  class="nav-icon text-lg transition-colors duration-300"
                  :class="{
                    'icon-inactive': $route.name !== route.name,
                    'icon-active': $route.name === route.name
                  }"
                >{{ route.meta?.icon }}</span>
                <span class="nav-text">{{ route.meta?.title }}</span>
              </router-link>
            </li>
          </ul>
        </nav>

        <!-- 用户区域 -->
        <div class="user-section flex items-center gap-4">
          <!-- 用户头像下拉菜单 -->
          <div class="user-dropdown relative">
            <div
              class="user-avatar w-9 h-9 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full flex items-center justify-center cursor-pointer transition-all duration-300 hover:scale-110 hover:from-blue-100 hover:to-indigo-100 border border-blue-200 hover:border-blue-300"
              @click="showUserMenu = !showUserMenu"
              title="用户中心"
            >
              <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
            </div>

            <!-- 下拉菜单 -->
            <div
              v-if="showUserMenu"
              class="user-menu absolute right-0 top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50000 min-w-40"
            >
              <div class="py-2">
                <!-- 个人信息 -->
                <button
                  @click="goToProfile"
                  class="w-full px-4 py-2 text-left text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 flex items-center gap-3 transition-colors duration-200"
                >
                  <span class="text-blue-400">👤</span>
                  个人信息
                </button>

                <!-- 帮助 -->
                <button
                  @click="showHelp"
                  class="w-full px-4 py-2 text-left text-sm text-gray-600 hover:bg-green-50 hover:text-green-600 flex items-center gap-3 transition-colors duration-200"
                >
                  <span class="text-green-400">❓</span>
                  帮助
                </button>

                <!-- 分割线 -->
                <div class="border-t border-gray-100 my-1"></div>

                <!-- 退出 -->
                <button
                  @click="logout"
                  class="w-full px-4 py-2 text-left text-sm text-gray-600 hover:bg-red-50 hover:text-red-500 flex items-center gap-3 transition-colors duration-200"
                >
                  <span class="text-red-400">🚪</span>
                  退出
                </button>
              </div>
            </div>

            <!-- 遮罩层 -->
            <div
              v-if="showUserMenu"
              @click="showUserMenu = false"
              class="fixed inset-0 z-40"
            ></div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content flex-1 flex flex-col max-w-none mx-0 w-full overflow-hidden">
      <RouterView />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

const router = useRouter()
const authStore = useAuthStore()

const showUserMenu = ref(false)

// 导航路由
const navRoutes = computed(() => {
  // 找到主布局路由
  const mainLayoutRoute = router.getRoutes().find(route => route.path === '/')

  if (!mainLayoutRoute || !mainLayoutRoute.children) {
    return []
  }

  // 只返回主布局的子路由
  return mainLayoutRoute.children
    .filter(route => route.meta?.title && route.meta?.icon && route.meta?.hideFromNav !== true)
    .map(route => ({
      name: route.name,
      path: route.path === '' ? '/' : `/${route.path}`, // 处理路径
      meta: route.meta
    }))
})

// 个人信息
const goToProfile = () => {
  showUserMenu.value = false
  router.push('/profile')
}

// 帮助
const showHelp = () => {
  showUserMenu.value = false
  router.push('/help')
}

// 退出登录
const logout = () => {
  showUserMenu.value = false
  authStore.logout()
}
</script>

<style scoped>
/* 用户下拉菜单样式 */
.user-dropdown {
  position: relative;
}

.user-menu {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 导航链接悬停效果 */
.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.08), transparent);
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}

/* 导航图标样式优化 */
.nav-icon {
  transition: color 0.3s ease;
}

/* 未选中状态 - 浅灰色 */
.nav-icon.icon-inactive {
  color: rgb(156 163 175) !important; /* gray-400 浅灰色 */
}

/* 选中状态 - 蓝色 */
.nav-icon.icon-active {
  color: rgb(59 130 246) !important; /* blue-500 蓝色 */
}

/* 悬停状态 - 蓝色 (覆盖类样式) */
.nav-link:hover .nav-icon {
  color: rgb(59 130 246) !important; /* 悬停时蓝色 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 12px;
    height: 60px;
  }

  .nav-menu {
    gap: 4px;
  }

  .nav-link {
    padding: 8px 12px;
    font-size: 14px;
  }

  .nav-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .logo-text {
    display: none;
  }

  .user-section {
    gap: 8px;
  }

  .logout-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
