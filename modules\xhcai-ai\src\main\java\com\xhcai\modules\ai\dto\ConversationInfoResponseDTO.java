package com.xhcai.modules.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.core.utils.TimeUtils;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 会话信息响应DTO
 * 用于返回给前端的会话信息，包含正确的时间格式转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "会话信息响应")
public class ConversationInfoResponseDTO {
    
    @Schema(description = "会话ID")
    private String id;
    
    @Schema(description = "会话名称")
    private String name;
    
    @Schema(description = "会话状态")
    private String status;
    
    @Schema(description = "会话介绍")
    private String introduction;
    
    @Schema(description = "智能体ID")
    private String agentId;
    
    @Schema(description = "创建时间戳（秒）")
    private Long createdAt;
    
    @Schema(description = "更新时间戳（秒）")
    private Long updatedAt;
    
    @Schema(description = "创建时间（北京时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createdTime;
    
    @Schema(description = "更新时间（北京时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime updatedTime;

    /**
     * 从ConversationInfo转换为响应DTO
     */
    public static ConversationInfoResponseDTO fromConversationInfo(ConversationInfo conversationInfo) {
        ConversationInfoResponseDTO dto = new ConversationInfoResponseDTO();
        dto.setId(conversationInfo.getId());
        dto.setName(conversationInfo.getName());
        dto.setStatus(conversationInfo.getStatus());
        dto.setIntroduction(conversationInfo.getIntroduction());
        dto.setAgentId(conversationInfo.getAgentId());
        dto.setCreatedAt(conversationInfo.getCreatedAt());
        dto.setUpdatedAt(conversationInfo.getUpdatedAt());
        
        // 转换时间戳为北京时间
        dto.setCreatedTime(TimeUtils.convertTimestampToBeijingTime(conversationInfo.getCreatedAt()));
        dto.setUpdatedTime(TimeUtils.convertTimestampToBeijingTime(conversationInfo.getUpdatedAt()));
        
        return dto;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}
