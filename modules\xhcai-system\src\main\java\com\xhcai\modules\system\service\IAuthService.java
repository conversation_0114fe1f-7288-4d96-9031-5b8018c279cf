package com.xhcai.modules.system.service;

import com.xhcai.modules.system.dto.LoginDTO;
import com.xhcai.modules.system.vo.LoginVO;

/**
 * 认证服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IAuthService {

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    LoginVO login(LoginDTO loginDTO);

    /**
     * 用户登出
     *
     * @param token 访问令牌
     * @return 是否成功
     */
    boolean logout(String token);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌信息
     */
    LoginVO refreshToken(String refreshToken);

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    LoginVO.UserInfo getCurrentUserInfo();

    /**
     * 验证令牌
     *
     * @param token 访问令牌
     * @return 是否有效
     */
    boolean validateToken(String token);

    /**
     * 生成验证码
     *
     * @return 验证码信息（包含图片和UUID）
     */
    Object generateCaptcha();

    /**
     * 验证验证码
     *
     * @param captcha 验证码
     * @param uuid 验证码UUID
     * @return 是否正确
     */
    boolean validateCaptcha(String captcha, String uuid);
}
