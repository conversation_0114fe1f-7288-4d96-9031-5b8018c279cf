package com.xhcai.plugin.core;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.PluginManager;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 插件上下文
 * 每种类型的插件都有独立的上下文环境，实现真正的插件隔离
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Slf4j
public class PluginContext {
    
    /**
     * 插件类型
     */
    private final PluginType pluginType;
    
    /**
     * 插件管理器
     */
    private final PluginManager pluginManager;
    
    /**
     * Spring 应用上下文
     */
    private ConfigurableApplicationContext applicationContext;
    
    /**
     * 插件信息缓存
     */
    private final Map<String, PluginInfo> pluginInfoCache = new ConcurrentHashMap<>();
    
    /**
     * 插件实例缓存
     */
    private final Map<String, Object> pluginInstanceCache = new ConcurrentHashMap<>();
    
    /**
     * 上下文状态
     */
    private volatile ContextStatus status = ContextStatus.CREATED;
    
    public PluginContext(PluginType pluginType, PluginManager pluginManager) {
        this.pluginType = pluginType;
        this.pluginManager = pluginManager;
    }
    
    /**
     * 启动插件上下文
     */
    public synchronized void start() {
        if (status == ContextStatus.STARTED) {
            log.warn("Plugin context for type {} is already started", pluginType);
            return;
        }
        
        try {
            status = ContextStatus.STARTING;
            log.info("Starting plugin context for type: {}", pluginType);
            
            // 启动插件管理器
            pluginManager.loadPlugins();
            pluginManager.startPlugins();
            
            status = ContextStatus.STARTED;
            log.info("Plugin context for type {} started successfully", pluginType);
            
        } catch (Exception e) {
            status = ContextStatus.FAILED;
            log.error("Failed to start plugin context for type: {}", pluginType, e);
            throw new RuntimeException("Failed to start plugin context", e);
        }
    }
    
    /**
     * 停止插件上下文
     */
    public synchronized void stop() {
        if (status != ContextStatus.STARTED) {
            log.warn("Plugin context for type {} is not started", pluginType);
            return;
        }
        
        try {
            status = ContextStatus.STOPPING;
            log.info("Stopping plugin context for type: {}", pluginType);
            
            // 清理缓存
            pluginInstanceCache.clear();
            pluginInfoCache.clear();
            
            // 停止插件管理器
            pluginManager.stopPlugins();
            pluginManager.unloadPlugins();
            
            // 关闭应用上下文
            if (applicationContext != null) {
                applicationContext.close();
                applicationContext = null;
            }
            
            status = ContextStatus.STOPPED;
            log.info("Plugin context for type {} stopped successfully", pluginType);
            
        } catch (Exception e) {
            status = ContextStatus.FAILED;
            log.error("Failed to stop plugin context for type: {}", pluginType, e);
            throw new RuntimeException("Failed to stop plugin context", e);
        }
    }
    
    /**
     * 获取插件实例
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getPluginInstances(Class<T> serviceClass) {
        if (status != ContextStatus.STARTED) {
            throw new IllegalStateException("Plugin context is not started");
        }
        
        return pluginManager.getExtensions(serviceClass);
    }
    
    /**
     * 获取指定插件的实例
     */
    @SuppressWarnings("unchecked")
    public <T> T getPluginInstance(String pluginId, Class<T> serviceClass) {
        if (status != ContextStatus.STARTED) {
            throw new IllegalStateException("Plugin context is not started");
        }
        
        String cacheKey = pluginId + ":" + serviceClass.getName();
        return (T) pluginInstanceCache.computeIfAbsent(cacheKey, key -> {
            List<T> extensions = pluginManager.getExtensions(serviceClass, pluginId);
            return extensions.isEmpty() ? null : extensions.get(0);
        });
    }
    
    /**
     * 上下文状态枚举
     */
    public enum ContextStatus {
        CREATED, STARTING, STARTED, STOPPING, STOPPED, FAILED
    }
}
