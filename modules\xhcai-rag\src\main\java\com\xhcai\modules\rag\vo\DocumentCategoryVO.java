package com.xhcai.modules.rag.vo;

import java.time.LocalDateTime;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文档分类VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档分类VO")
@Data
public class DocumentCategoryVO {

    /**
     * 分类ID
     */
    @Schema(description = "分类ID", example = "category123")
    private String id;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID", example = "dataset123")
    private String datasetId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "产品文档")
    private String name;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述", example = "产品相关的文档资料")
    private String description;

    /**
     * 父分类ID
     */
    @Schema(description = "父分类ID", example = "parent123")
    private String parentId;

    /**
     * 分类层级
     */
    @Schema(description = "分类层级", example = "1")
    private Integer level;

    /**
     * 排序号
     */
    @Schema(description = "排序号", example = "1")
    private Integer sortOrder;

    /**
     * 文件数量
     */
    @Schema(description = "文件数量", example = "15")
    private Integer fileCount;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

    /**
     * 子分类列表
     */
    @Schema(description = "子分类列表")
    private List<DocumentCategoryVO> children;
}
