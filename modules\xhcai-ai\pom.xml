<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.xhcai</groupId>
        <artifactId>xhcai-plus</artifactId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>xhcai-ai</artifactId>
    <name>XHC AI Module</name>
    <description>AI功能模块 - Spring AI集成，对话管理，模型配置</description>

    <dependencies>
        <!-- Common Modules -->
        <dependency>
            <groupId>com.xhcai</groupId>
            <artifactId>xhcai-agent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhcai</groupId>
            <artifactId>xhcai-rag</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhcai</groupId>
            <artifactId>common-plugin</artifactId>
        </dependency>

        <!-- Spring AI -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
        </dependency>
        
        <!-- Spring AI Vector Store -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-pgvector-store-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
