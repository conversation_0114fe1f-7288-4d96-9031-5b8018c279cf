package com.xhcai.modules.dify.dto.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Dify 建议问题响应 DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Schema(description = "Dify建议问题响应")
public class DifySuggestedQuestionsResponseDTO {

    @Schema(description = "建议问题列表")
    @JsonProperty("data")
    private List<String> data;

    public List<String> getData() {
        return data;
    }

    public void setData(List<String> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "DifySuggestedQuestionsResponseDTO{" +
                "data=" + data +
                '}';
    }
}
