<template>
  <div class="project-card-menu">
    <button @click="$emit('edit')" class="menu-item">
      <i class="fas fa-edit"></i>
      编辑
    </button>
    <button @click="$emit('delete')" class="menu-item danger">
      <i class="fas fa-trash"></i>
      删除
    </button>
    <div class="menu-divider"></div>
    <button @click="$emit('create-agent')" class="menu-item">
      <i class="fas fa-robot"></i>
      新建智能体
    </button>
    <button @click="$emit('create-knowledge')" class="menu-item">
      <i class="fas fa-book"></i>
      新建知识库
    </button>
    <button @click="$emit('create-graph')" class="menu-item">
      <i class="fas fa-project-diagram"></i>
      新建知识图谱
    </button>
    <div class="menu-divider"></div>
    <button @click="$emit('link-agent')" class="menu-item">
      <i class="fas fa-link"></i>
      关联智能体
    </button>
    <button @click="$emit('link-knowledge')" class="menu-item">
      <i class="fas fa-link"></i>
      关联知识库
    </button>
    <button @click="$emit('link-graph')" class="menu-item">
      <i class="fas fa-link"></i>
      关联知识图谱
    </button>
    <div class="menu-divider"></div>
    <button @click="$emit('manage-team')" class="menu-item">
      <i class="fas fa-users"></i>
      团队人员
    </button>
    <button @click="$emit('switch-environment')" class="menu-item">
      <i class="fas fa-exchange-alt"></i>
      切换环境
    </button>
  </div>
</template>

<script setup lang="ts">
// Emits
defineEmits<{
  edit: []
  delete: []
  'create-agent': []
  'create-knowledge': []
  'create-graph': []
  'link-agent': []
  'link-knowledge': []
  'link-graph': []
  'manage-team': []
  'switch-environment': []
}>()
</script>

<style scoped>
.project-card-menu {
  background: white;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  min-width: 160px;
  padding: 8px 0;
}

.menu-item {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  transition: var(--transition);
}

.menu-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.menu-item.danger {
  color: #e74c3c;
}

.menu-item.danger:hover {
  background: rgba(231, 76, 60, 0.1);
}

.menu-divider {
  height: 1px;
  background: var(--border-light);
  margin: 8px 0;
}
</style>
