package com.xhcai.modules.rag.controller;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.rag.dto.DocumentCategoryCreateDTO;
import com.xhcai.modules.rag.dto.DocumentCategoryUpdateDTO;
import com.xhcai.modules.rag.service.IDocumentCategoryService;
import com.xhcai.modules.rag.vo.DocumentCategoryVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档分类控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/categories")
@RequiredArgsConstructor
@Validated
@Tag(name = "文档分类管理", description = "文档分类相关接口")
public class DocumentCategoryController {

    private final IDocumentCategoryService documentCategoryService;

    @GetMapping("/tree")
    @Operation(summary = "获取分类树", description = "获取完整的分类树结构")
    public Result<List<DocumentCategoryVO>> getCategoryTree(
            @Parameter(description = "知识库ID")
            @RequestParam(required = false) String datasetId) {
        List<DocumentCategoryVO> categoryTree = documentCategoryService.getCategoryTree(datasetId);
        return Result.success(categoryTree);
    }

    @GetMapping
    @Operation(summary = "获取分类列表", description = "根据父分类ID获取子分类列表")
    public Result<List<DocumentCategoryVO>> getCategories(
            @Parameter(description = "父分类ID，为空时获取根分类")
            @RequestParam(required = false) String parentId,
            @Parameter(description = "知识库ID")
            @RequestParam(required = false) String datasetId) {
        List<DocumentCategoryVO> categories = documentCategoryService.getCategoriesByParentId(parentId, datasetId);
        return Result.success(categories);
    }

    @GetMapping("/{categoryId}")
    @Operation(summary = "获取分类详情", description = "根据分类ID获取分类详情")
    public Result<DocumentCategoryVO> getCategoryById(
            @Parameter(description = "分类ID")
            @PathVariable String categoryId) {
        DocumentCategoryVO category = documentCategoryService.getCategoryById(categoryId);
        if (category == null) {
            return Result.error("分类不存在");
        }
        return Result.success(category);
    }

    @PostMapping
    @Operation(summary = "创建分类", description = "创建新的文档分类")
    public Result<DocumentCategoryVO> createCategory(
            @Parameter(description = "分类创建信息")
            @Valid @RequestBody DocumentCategoryCreateDTO createDTO) {
        DocumentCategoryVO category = documentCategoryService.createCategory(createDTO);
        return Result.success(category);
    }

    @PutMapping("/{categoryId}")
    @Operation(summary = "更新分类", description = "更新文档分类信息")
    public Result<DocumentCategoryVO> updateCategory(
            @Parameter(description = "分类ID")
            @PathVariable String categoryId,
            @Parameter(description = "分类更新信息")
            @Valid @RequestBody DocumentCategoryUpdateDTO updateDTO) {
        DocumentCategoryVO category = documentCategoryService.updateCategory(categoryId, updateDTO);
        return Result.success(category);
    }

    @DeleteMapping("/{categoryId}")
    @Operation(summary = "删除分类", description = "删除文档分类")
    public Result<Void> deleteCategory(
            @Parameter(description = "分类ID")
            @PathVariable String categoryId) {
        documentCategoryService.deleteCategory(categoryId);
        return Result.success();
    }

    @PostMapping("/{categoryId}/file-count")
    @Operation(summary = "更新分类文件数量", description = "更新分类下的文件数量")
    public Result<Void> updateCategoryFileCount(
            @Parameter(description = "分类ID")
            @PathVariable String categoryId,
            @Parameter(description = "文件数量")
            @RequestParam Integer fileCount) {
        documentCategoryService.updateCategoryFileCount(categoryId, fileCount);
        return Result.success();
    }

    @GetMapping("/check-name")
    @Operation(summary = "检查分类名称", description = "检查分类名称是否已存在")
    public Result<Boolean> checkCategoryName(
            @Parameter(description = "分类名称")
            @RequestParam String name,
            @Parameter(description = "父分类ID")
            @RequestParam(required = false) String parentId,
            @Parameter(description = "排除的分类ID")
            @RequestParam(required = false) String excludeId) {
        boolean exists = documentCategoryService.existsCategoryName(name, parentId, excludeId);
        return Result.success(!exists);
    }
}
