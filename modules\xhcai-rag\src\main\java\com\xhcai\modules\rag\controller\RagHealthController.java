package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * RAG模块健康检查控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "RAG健康检查", description = "RAG模块健康状态检查")
@RestController
@RequestMapping("/api/rag/health")
public class RagHealthController {

    private static final Logger log = LoggerFactory.getLogger(RagHealthController.class);

    @Operation(summary = "健康检查", description = "检查RAG模块是否正常运行")
    @GetMapping("/check")
    public Result<Map<String, Object>> healthCheck() {
        log.info("执行RAG模块健康检查");
        
        Map<String, Object> health = new HashMap<>();
        health.put("module", "xhcai-rag");
        health.put("status", "UP");
        health.put("version", "1.0.0");
        health.put("timestamp", LocalDateTime.now());
        health.put("description", "RAG知识库管理模块运行正常");
        
        // TODO: 添加更详细的健康检查
        // 例如：数据库连接、向量存储、AI服务等
        Map<String, Object> details = new HashMap<>();
        details.put("database", "UP");
        details.put("vectorStore", "UP");
        details.put("aiService", "UP");
        health.put("details", details);
        
        return Result.success(health);
    }

    @Operation(summary = "模块信息", description = "获取RAG模块基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> moduleInfo() {
        log.info("获取RAG模块信息");
        
        Map<String, Object> info = new HashMap<>();
        info.put("moduleId", "xhcai-rag");
        info.put("moduleName", "RAG知识库管理模块");
        info.put("version", "1.0.0");
        info.put("author", "xhcai");
        info.put("description", "RAG知识库管理模块，包括文档上传、向量化、检索增强生成等功能");
        info.put("features", new String[]{
            "document_upload", "text_processing", "vector_embedding", 
            "semantic_search", "knowledge_retrieval"
        });
        info.put("supportedFileTypes", new String[]{
            "pdf", "doc", "docx", "txt", "md", "html", "csv", "xlsx", "pptx"
        });
        info.put("apiPrefix", "/api/rag");
        info.put("loadTime", LocalDateTime.now());
        
        return Result.success(info);
    }
}
