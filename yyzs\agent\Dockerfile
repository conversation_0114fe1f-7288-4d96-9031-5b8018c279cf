# 使用官方OpenJDK 17镜像作为基础镜像
FROM openjdk:17-jdk-slim

# 设置维护者信息
LABEL maintainer="YYZS Team <<EMAIL>>"
LABEL description="YYZS Agent - Elastic Stack Component Management Platform"
LABEL version="1.0.0"

# 设置工作目录
WORKDIR /app

# 创建应用用户（安全最佳实践）
RUN groupadd -r yyzs && useradd -r -g yyzs yyzs

# 安装必要的系统工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    tar \
    gzip \
    procps \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录结构
RUN mkdir -p /app/logs \
    && mkdir -p /app/data \
    && mkdir -p /app/config \
    && mkdir -p /app/temp \
    && mkdir -p /opt/elastic \
    && mkdir -p /var/log/yyzs

# 复制Maven构建的JAR文件
COPY target/yyzs-agent-*.jar /app/yyzs-agent.jar

# 复制配置文件
COPY src/main/resources/application.yml /app/config/
COPY src/main/resources/application-docker.yml /app/config/

# 复制启动脚本
COPY docker/start.sh /app/start.sh
RUN chmod +x /app/start.sh

# 设置文件权限
RUN chown -R yyzs:yyzs /app \
    && chown -R yyzs:yyzs /opt/elastic \
    && chown -R yyzs:yyzs /var/log/yyzs

# 暴露端口
EXPOSE 8080 8081

# 设置环境变量
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport" \
    SPRING_PROFILES_ACTIVE=docker \
    SERVER_PORT=8080 \
    MANAGEMENT_PORT=8081 \
    LOG_LEVEL=INFO \
    DB_HOST=localhost \
    DB_PORT=5432 \
    DB_NAME=yyzs_agent \
    DB_USERNAME=yyzs \
    DB_PASSWORD=yyzs123 \
    REDIS_HOST=localhost \
    REDIS_PORT=6379 \
    ELASTIC_INSTALL_PATH=/opt/elastic

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/actuator/health || exit 1

# 切换到应用用户
USER yyzs

# 设置启动命令
ENTRYPOINT ["/app/start.sh"]
CMD ["server"]
