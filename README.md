# YYZS Agent Platform

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.5.3-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

专业的 Elastic Stack 组件管理平台，提供组件安装、配置、监控和运维的完整解决方案。

## 🚀 功能特性

### 核心功能
- ✅ **组件管理** - 支持 Elasticsearch、Logstash、Beats 系列等组件的完整生命周期管理
- ✅ **一键安装** - 自动化组件安装、配置和启动流程
- ✅ **实时监控** - 组件状态、资源使用、性能指标的实时监控
- ✅ **配置管理** - 可视化配置编辑器，支持 YAML/JSON 格式
- ✅ **日志管理** - 集中化日志查看和管理
- ✅ **告警系统** - 智能告警规则和通知机制
- ✅ **批量操作** - 支持多组件的批量启动、停止、重启操作

### 支持的组件
- **Filebeat** - 日志文件采集
- **Heartbeat** - 服务可用性监控
- **Metricbeat** - 系统和服务指标采集
- **Packetbeat** - 网络数据包分析
- **Winlogbeat** - Windows事件日志采集
- **Auditbeat** - 审计数据采集
- **Logstash** - 数据处理管道
- **Elasticsearch** - 搜索和分析引擎
- **Kafka** - 消息队列

### 技术架构
- **后端**: Spring Boot 3.5.3 + Spring AI + JPA + MyBatis Plus
- **前端**: React 18 + Next.js + TypeScript + Tailwind CSS
- **数据库**: PostgreSQL + Redis
- **容器化**: Docker + Docker Compose
- **监控**: Prometheus + Grafana
- **安全**: JWT + RBAC权限控制

## 📋 系统要求

### 开发环境
- Java 17+
- Node.js 18+
- Maven 3.8+
- Docker & Docker Compose
- PostgreSQL 12+
- Redis 6+

### 生产环境
- CPU: 4核心以上
- 内存: 8GB以上
- 磁盘: 100GB以上可用空间
- 操作系统: Linux (推荐 Ubuntu 20.04+)

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/yyzs-agent-platform.git
cd yyzs-agent-platform
```

### 2. 一键部署（推荐）
```bash
# 开发环境部署
chmod +x deploy.sh
./deploy.sh dev

# 生产环境部署
./deploy.sh prod --clean
```

### 3. 手动部署

#### 后端部署
```bash
cd yyzs/agent
./mvnw clean package
java -jar target/yyzs-agent-1.0.0.jar
```

#### 前端部署
```bash
cd yyzs/web
npm install
npm run build
npm start
```

#### 数据库初始化
```bash
# PostgreSQL
createdb yyzs_agent
psql yyzs_agent < docker/init-db.sql

# Redis
redis-server
```

### 4. 访问应用
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8080
- **管理端点**: http://localhost:8081/actuator
- **API文档**: http://localhost:8080/swagger-ui.html

## 📖 使用指南

### 组件上传
1. 访问前端界面，点击"上传组件"
2. 选择组件安装包（支持 .tar.gz, .zip 格式）
3. 选择组件类型和填写版本信息
4. 点击上传，系统自动处理安装

### 组件管理
1. 在组件列表中查看所有已安装的组件
2. 使用筛选和搜索功能快速定位组件
3. 点击组件名称查看详细信息
4. 使用操作按钮进行启动、停止、重启等操作

### 配置管理
1. 在组件详情页面点击"配置"标签
2. 使用表单模式或文本模式编辑配置
3. 系统自动验证配置格式和内容
4. 保存配置后可选择是否重启组件

### 监控告警
1. 访问监控仪表板查看系统整体状态
2. 在告警管理中创建和管理告警规则
3. 配置通知方式（邮件、钉钉、Webhook）
4. 查看和处理告警记录

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yyzs_agent
DB_USERNAME=yyzs
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password

# 应用配置
SERVER_PORT=8080
MANAGEMENT_PORT=8081
LOG_LEVEL=INFO

# 安全配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRATION=86400
```

### 应用配置文件
主要配置文件位于 `yyzs/agent/src/main/resources/application.yml`，包含：
- 数据源配置
- JPA/MyBatis配置
- 安全配置
- 监控配置
- 业务配置

## 🧪 测试

### 运行单元测试
```bash
cd yyzs/agent
./mvnw test
```

### 运行集成测试
```bash
cd yyzs/agent
./mvnw verify
```

### 前端测试
```bash
cd yyzs/web
npm test
```

## 📊 监控和运维

### 健康检查
- **应用健康**: http://localhost:8081/actuator/health
- **数据库连接**: 通过健康检查端点自动检测
- **Redis连接**: 通过健康检查端点自动检测

### 指标监控
- **Prometheus指标**: http://localhost:8081/actuator/prometheus
- **JVM指标**: 内存、GC、线程等
- **应用指标**: 请求量、响应时间、错误率等
- **业务指标**: 组件数量、状态分布等

### 日志管理
- **应用日志**: `/var/log/yyzs/yyzs-agent.log`
- **访问日志**: 通过Nginx记录
- **组件日志**: 各组件独立的日志文件

## 🔒 安全说明

### 认证授权
- 使用JWT令牌进行身份认证
- 基于RBAC的权限控制
- 支持多租户隔离

### 数据安全
- 数据库连接加密
- 敏感信息加密存储
- API接口访问控制

### 网络安全
- HTTPS支持
- CORS配置
- 防火墙规则建议

## 🚀 部署指南

### Docker部署（推荐）
```bash
# 使用docker-compose部署
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f yyzs-agent
```

### Kubernetes部署
```bash
# 应用Kubernetes配置
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -n yyzs-agent
```

### 传统部署
详细的传统部署步骤请参考 [部署文档](docs/deployment.md)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- Java: 遵循 Google Java Style Guide
- TypeScript: 使用 ESLint + Prettier
- 提交信息: 遵循 Conventional Commits

## 📝 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新历史。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

### 文档
- [用户手册](docs/user-guide.md)
- [开发文档](docs/development.md)
- [API文档](docs/api.md)
- [故障排除](docs/troubleshooting.md)

### 社区
- [GitHub Issues](https://github.com/your-org/yyzs-agent-platform/issues)
- [讨论区](https://github.com/your-org/yyzs-agent-platform/discussions)
- 邮箱: <EMAIL>

### 商业支持
如需商业支持或定制开发，请联系我们：
- 邮箱: <EMAIL>
- 电话: +86-xxx-xxxx-xxxx

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

特别感谢以下开源项目：
- [Spring Boot](https://spring.io/projects/spring-boot)
- [React](https://reactjs.org/)
- [Elastic Stack](https://www.elastic.co/)
- [Docker](https://www.docker.com/)

---

**YYZS Agent Platform** - 让 Elastic Stack 组件管理变得简单高效！
