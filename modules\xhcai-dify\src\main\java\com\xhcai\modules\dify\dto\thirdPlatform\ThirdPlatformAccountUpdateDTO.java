package com.xhcai.modules.dify.dto.thirdPlatform;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 用户第三方智能体账号更新DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "用户第三方智能体账号更新DTO")
public class ThirdPlatformAccountUpdateDTO {

    /**
     * 账号名称
     */
    @Schema(description = "账号名称", example = "我的Dify账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "账号名称不能为空")
    @Size(min = 1, max = 100, message = "账号名称长度必须在1-100个字符之间")
    private String accountName;

    /**
     * API密钥
     */
    @Schema(description = "API密钥", example = "app-***")
    @Size(max = 500, message = "API密钥长度不能超过500个字符")
    private String apiKey;

    /**
     * 密码（可选，不传则不修改）
     */
    @Schema(description = "密码", example = "123456")
    @Size(min = 6, max = 50, message = "密码长度必须在6-50个字符之间")
    private String pwd;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "用于测试的账号")
    @Size(max = 500, message = "备注信息长度不能超过500个字符")
    private String remark;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;

    // Getters and Setters
    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ThirdPlatformAccountUpdateDTO{" +
                "accountName='" + accountName + '\'' +
                ", apiKey='" + (apiKey != null ? "***" : null) + '\'' +
                ", pwd='" + (pwd != null ? "***" : null) + '\'' +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                '}';
    }
}
