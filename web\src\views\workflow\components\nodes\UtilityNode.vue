<template>
  <div class="utility-node" :class="{ selected: selected }">
    <div class="node-header">
      <div class="node-icon">
        <i class="fas" :class="getUtilityIcon(data.config?.utilityType || 'http-request')"></i>
      </div>
      <div class="node-title">{{ data.label || getUtilityName(data.config?.utilityType || 'http-request') }}</div>
    </div>
    
    <div class="node-content">
      <p class="node-description" v-if="data.description">{{ data.description }}</p>
    </div>

    <Handle type="target" :position="Position.Left" id="input" class="node-handle node-handle-input" />
    <Handle type="source" :position="Position.Right" id="output" class="node-handle node-handle-output" />
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'

interface UtilityNodeData {
  label?: string
  description?: string
  config?: {
    utilityType?: string
  }
}

interface Props extends NodeProps {
  data: UtilityNodeData
}

defineProps<Props>()

const getUtilityIcon = (utilityType: string) => {
  const iconMap: Record<string, string> = {
    'http-request': 'fa-globe',
    'email-send': 'fa-envelope',
    'delay': 'fa-clock',
    'variable-set': 'fa-cog',
    'log-output': 'fa-file-alt'
  }
  return iconMap[utilityType] || 'fa-tools'
}

const getUtilityName = (utilityType: string) => {
  const nameMap: Record<string, string> = {
    'http-request': 'HTTP请求',
    'email-send': '发送邮件',
    'delay': '延时等待',
    'variable-set': '设置变量',
    'log-output': '日志输出'
  }
  return nameMap[utilityType] || '工具'
}
</script>

<style scoped>
.utility-node {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  border: 2px solid #6b7280;
  border-radius: 12px;
  min-width: 160px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  position: relative;
}

.utility-node:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.utility-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.node-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.node-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.node-title {
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex: 1;
}

.node-content {
  padding: 0 16px 12px;
}

.node-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  margin: 0;
  line-height: 1.4;
}

.node-handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  background: #6b7280;
  border-radius: 50%;
}

.node-handle-input {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-handle-output {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-handle:hover {
  width: 16px;
  height: 16px;
  border-width: 3px;
}

.node-handle-input:hover {
  left: -8px;
}

.node-handle-output:hover {
  right: -8px;
}
</style>
