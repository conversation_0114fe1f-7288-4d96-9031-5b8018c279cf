# Heartbeat Configuration Template
# ======================== Heartbeat Configuration ===========================

# Define a directory to load monitor definitions from. Definitions take the form
# of individual yaml files.
heartbeat.config.monitors:
  # Directory + glob pattern to search for configuration files
  path: ${MONITORS_PATH:${path.config}/monitors.d/*.yml}
  # If enabled, heartbeat will periodically check the config.monitors path for changes
  reload.enabled: ${MONITORS_RELOAD:false}
  # How often to check for changes
  reload.period: ${MONITORS_RELOAD_PERIOD:5s}

# Configure monitors inline
heartbeat.monitors:
# Monitor a HTTP service
- type: http
  # ID used to uniquely identify this monitor in elasticsearch even if the config changes
  id: ${HTTP_MONITOR_ID:http-monitor}
  
  # Human readable display name for this service in Uptime UI and elsewhere
  name: ${HTTP_MONITOR_NAME:HTTP Monitor}
  
  # List of URLs to query
  urls: [${HTTP_URLS:"http://localhost:80/health"}]
  
  # Configure task schedule using cron-like syntax
  schedule: '${HTTP_SCHEDULE:@every 10s}'
  
  # Total test connection and data exchange timeout
  timeout: ${HTTP_TIMEOUT:16s}
  
  # Optional HTTP configuration
  check.request:
    method: ${HTTP_METHOD:GET}
    headers:
      'User-Agent': '${HTTP_USER_AGENT:heartbeat}'
  
  check.response:
    status: [${HTTP_EXPECTED_STATUS:200}]
    body: '${HTTP_EXPECTED_BODY:}'

# Monitor a TCP service
- type: tcp
  id: ${TCP_MONITOR_ID:tcp-monitor}
  name: ${TCP_MONITOR_NAME:TCP Monitor}
  hosts: [${TCP_HOSTS:"localhost:9200"}]
  schedule: '${TCP_SCHEDULE:@every 10s}'
  timeout: ${TCP_TIMEOUT:16s}

# Monitor an ICMP ping
- type: icmp
  id: ${ICMP_MONITOR_ID:icmp-monitor}
  name: ${ICMP_MONITOR_NAME:ICMP Monitor}
  hosts: [${ICMP_HOSTS:"localhost"}]
  schedule: '${ICMP_SCHEDULE:@every 10s}'
  timeout: ${ICMP_TIMEOUT:16s}

# ================================== General ===================================

# The name of the shipper that publishes the network data.
name: ${SHIPPER_NAME:heartbeat}

# The tags of the shipper are included in their own field with each
# transaction published.
tags: [${TAGS:"heartbeat", "uptime"}]

# Optional fields that you can specify to add additional information to the
# output.
fields:
  env: ${ENVIRONMENT:production}
  service: ${SERVICE_NAME:default}

# ================================= Dashboards =================================
setup.dashboards.enabled: ${DASHBOARDS_ENABLED:false}

# =================================== Kibana ===================================
setup.kibana:
  host: "${KIBANA_HOST:localhost:5601}"

# ================================== Outputs ===================================

# ---------------------------- Elasticsearch Output ----------------------------
output.elasticsearch:
  hosts: [${ELASTICSEARCH_HOSTS:"localhost:9200"}]
  protocol: "${ELASTICSEARCH_PROTOCOL:http}"
  username: "${ELASTICSEARCH_USERNAME:elastic}"
  password: "${ELASTICSEARCH_PASSWORD:changeme}"
  index: "${INDEX_NAME:heartbeat-%{+yyyy.MM.dd}}"

# ------------------------------ Logstash Output ----------------------------
#output.logstash:
  #hosts: [${LOGSTASH_HOSTS:"localhost:5044"}]

# ================================= Processors =================================
processors:
  - add_observer_metadata:
      # Optional, but recommended geo settings for the location Heartbeat is running in
      geo:
        # Token describing this location
        name: ${GEO_NAME:us-east-1a}
        # Lat, Lon "
        #location: "37.926868, -78.024902"
        # Named region
        region_name: ${GEO_REGION:us-east-1}
        # ISO country code
        country_iso_code: ${GEO_COUNTRY:US}
        # Human readable region and country
        continent_name: ${GEO_CONTINENT:North America}
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

# ================================== Logging ===================================
logging.level: ${LOG_LEVEL:info}
logging.to_files: true
logging.files:
  path: ${LOG_PATH:/var/log/heartbeat}
  name: heartbeat
  keepfiles: 7
  permissions: 0644

# ============================= X-Pack Monitoring ==============================
monitoring.enabled: ${MONITORING_ENABLED:false}

# ================================ HTTP Endpoint ==============================
http.enabled: ${HTTP_ENABLED:false}
http.host: ${HTTP_HOST:localhost}
http.port: ${HTTP_PORT:5067}

# Custom configuration
${CUSTOM_CONFIG:}
