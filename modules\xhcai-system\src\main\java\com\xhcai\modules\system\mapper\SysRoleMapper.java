package com.xhcai.modules.system.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysRole;

/**
 * 角色信息Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleCode 角色编码
     * @param roleName 角色名称
     * @param status 状态
     * @param dataScope 数据范围
     * @return 角色列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_role "
            + "WHERE deleted = 0 "
            + "<if test='roleCode != null and roleCode != \"\"'>"
            + "AND role_code LIKE CONCAT('%', #{roleCode}, '%') "
            + "</if>"
            + "<if test='roleName != null and roleName != \"\"'>"
            + "AND role_name LIKE CONCAT('%', #{roleName}, '%') "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "AND status = #{status} "
            + "</if>"
            + "<if test='dataScope != null and dataScope != \"\"'>"
            + "AND data_scope = #{dataScope} "
            + "</if>"
            + "ORDER BY role_sort ASC, create_time DESC"
            + "</script>")
    Page<SysRole> selectRolePage(Page<SysRole> page,
            @Param("roleCode") String roleCode,
            @Param("roleName") String roleName,
            @Param("status") String status,
            @Param("dataScope") String dataScope);

    /**
     * 查询角色列表
     *
     * @param roleCode 角色编码
     * @param roleName 角色名称
     * @param status 状态
     * @param dataScope 数据范围
     * @return 角色列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_role "
            + "WHERE deleted = 0 "
            + "<if test='roleCode != null and roleCode != \"\"'>"
            + "AND role_code LIKE CONCAT('%', #{roleCode}, '%') "
            + "</if>"
            + "<if test='roleName != null and roleName != \"\"'>"
            + "AND role_name LIKE CONCAT('%', #{roleName}, '%') "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "AND status = #{status} "
            + "</if>"
            + "<if test='dataScope != null and dataScope != \"\"'>"
            + "AND data_scope = #{dataScope} "
            + "</if>"
            + "ORDER BY role_sort ASC, create_time DESC"
            + "</script>")
    List<SysRole> selectRoleList(@Param("roleCode") String roleCode,
            @Param("roleName") String roleName,
            @Param("status") String status,
            @Param("dataScope") String dataScope);

    /**
     * 根据角色编码查询角色信息
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode} AND deleted = 0")
    SysRole selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 存在数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_role "
            + "WHERE role_code = #{roleCode} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsRoleCode(@Param("roleCode") String roleCode, @Param("excludeId") String excludeId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 存在数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_role "
            + "WHERE role_name = #{roleName} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    Integer existsRoleName(@Param("roleName") String roleName, @Param("excludeId") String excludeId);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM sys_role r "
            + "INNER JOIN sys_user_role ur ON r.id = ur.role_id "
            + "WHERE ur.user_id = #{userId} AND r.deleted = 0 AND ur.deleted = 0 "
            + "ORDER BY r.role_sort ASC")
    List<SysRole> selectRolesByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询角色编码集合
     *
     * @param userId 用户ID
     * @return 角色编码集合
     */
    @Select("SELECT r.role_code FROM sys_role r "
            + "INNER JOIN sys_user_role ur ON r.id = ur.role_id "
            + "WHERE ur.user_id = #{userId} AND r.deleted = 0 AND ur.deleted = 0 "
            + "AND r.status = '0'")
    Set<String> selectRoleCodesByUserId(@Param("userId") String userId);

    /**
     * 查询角色已分配的权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @Select("SELECT permission_id FROM sys_role_permission "
            + "WHERE role_id = #{roleId} AND deleted = 0")
    List<String> selectRolePermissionIds(@Param("roleId") String roleId);

    /**
     * 检查角色是否被用户使用
     *
     * @param roleId 角色ID
     * @return 使用数量
     */
    @Select("SELECT COUNT(*) FROM sys_user_role "
            + "WHERE role_id = #{roleId} AND deleted = 0")
    Integer countUsersByRoleId(@Param("roleId") String roleId);

    /**
     * 查询所有可用角色
     *
     * @return 角色列表
     */
    @Select("SELECT * FROM sys_role "
            + "WHERE status = '0' AND deleted = 0 "
            + "ORDER BY role_sort ASC")
    List<SysRole> selectAllAvailableRoles();

    /**
     * 根据数据范围查询角色列表
     *
     * @param dataScope 数据范围
     * @return 角色列表
     */
    @Select("SELECT * FROM sys_role "
            + "WHERE data_scope = #{dataScope} AND deleted = 0 "
            + "ORDER BY role_sort ASC")
    List<SysRole> selectRolesByDataScope(@Param("dataScope") String dataScope);

    /**
     * 查询最大排序号
     *
     * @return 最大排序号
     */
    @Select("SELECT COALESCE(MAX(role_sort), 0) FROM sys_role WHERE deleted = 0")
    Integer selectMaxRoleSort();
}
