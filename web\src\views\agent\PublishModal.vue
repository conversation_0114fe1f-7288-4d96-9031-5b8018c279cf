<template>
  <!-- 发布模态框 -->
  <div class="publish-modal" v-show="visible" @click="closeModal">
    <div class="publish-modal-content" @click.stop>
      <!-- 模态框头部 -->
      <div class="publish-modal-header">
        <div class="header-left">
          <div class="agent-info" v-if="agent">
            <div class="agent-avatar">
              <i :class="agent.icon"></i>
            </div>
            <div class="agent-details">
              <h3>{{ agent.name }}</h3>
              <span class="agent-type-badge">{{ agent.type }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <h2>发布智能体</h2>
          <button class="close-btn" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- 模态框主体 -->
      <div class="publish-modal-body">
        <!-- 左侧：发布目的 -->
        <div class="publish-left-panel">
          <div class="panel-header">
            <h4>
              <i class="fas fa-bullhorn"></i>
              发布目的
            </h4>
            <p class="panel-desc">选择智能体的发布目标平台</p>
          </div>
          <div class="publish-targets-grid">
            <div
              v-for="target in publishTargets"
              :key="target.value"
              :class="['target-card', { active: selectedPublishTarget === target.value }]"
              @click="selectTarget(target.value)"
            >
              <div class="target-icon">
                <i :class="target.icon"></i>
              </div>
              <div class="target-info">
                <h5>{{ target.label }}</h5>
                <p>{{ target.description }}</p>
              </div>
              <div class="target-check">
                <i class="fas fa-check"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：权限配置 -->
        <div class="publish-right-panel">
          <div class="panel-header">
            <h4>
              <i class="fas fa-shield-alt"></i>
              可见权限
            </h4>
            <p class="panel-desc">{{ getPermissionDescription() }}</p>
          </div>

          <div class="visibility-config">
            <!-- 权限选项 -->
            <div class="visibility-options-modern" v-if="showVisibilityConfig">
              <div
                v-for="visibility in visibilityOptions"
                :key="visibility.value"
                :class="['visibility-card', { active: selectedVisibility === visibility.value }]"
                @click="selectVisibility(visibility.value)"
              >
                <div class="visibility-icon">
                  <i :class="visibility.icon"></i>
                </div>
                <div class="visibility-info">
                  <h6>{{ visibility.label }}</h6>
                  <p>{{ visibility.description }}</p>
                </div>
                <div class="visibility-check">
                  <i class="fas fa-check"></i>
                </div>
              </div>
            </div>

            <!-- 默认公开权限显示 -->
            <div class="default-public-config" v-if="showDefaultPublicConfig">
              <div class="public-notice">
                <div class="notice-icon">
                  <i class="fas fa-globe"></i>
                </div>
                <div class="notice-content">
                  <h6>默认公开权限</h6>
                  <p>{{ getDefaultPublicMessage() }}</p>
                </div>
              </div>
            </div>

            <!-- 部分单位可见配置 -->
            <div class="config-section" v-if="selectedVisibility === 'partial-unit'">
              <h5>选择可见单位</h5>
              <div class="selected-items">
                <div v-for="unit in selectedUnits" :key="unit" class="selected-item">
                  <i class="fas fa-building"></i>
                  {{ unit }}
                  <button class="remove-item" @click="removeUnit(unit)">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <div class="search-section">
                <div class="search-input">
                  <i class="fas fa-search"></i>
                  <input
                    type="text"
                    v-model="unitSearchQuery"
                    placeholder="搜索单位..."
                    @input="searchUnits"
                  >
                </div>
                <div class="search-results" v-if="filteredUnits.length > 0">
                  <div
                    v-for="unit in filteredUnits"
                    :key="unit"
                    class="search-result-item"
                    @click="addUnit(unit)"
                  >
                    <i class="fas fa-building"></i>
                    {{ unit }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 部分人可见配置 -->
            <div class="config-section" v-if="selectedVisibility === 'partial-user'">
              <h5>选择可见用户</h5>
              <div class="selected-items">
                <div v-for="user in selectedUsers" :key="user.id" class="selected-item">
                  <i class="fas fa-user"></i>
                  <div class="user-info">
                    <span class="user-name">{{ user.name }}</span>
                    <span class="user-details">{{ user.unit }} - {{ user.role }}</span>
                  </div>
                  <button class="remove-item" @click="removeUser(user.id)">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <div class="search-section">
                <div class="search-input">
                  <i class="fas fa-search"></i>
                  <input
                    type="text"
                    v-model="userSearchQuery"
                    placeholder="搜索用户..."
                    @input="searchUsers"
                  >
                </div>
                <div class="search-results" v-if="filteredUsers.length > 0">
                  <div
                    v-for="user in filteredUsers"
                    :key="user.id"
                    class="search-result-item"
                    @click="addUser(user)"
                  >
                    <i class="fas fa-user"></i>
                    <div class="user-info">
                      <span class="user-name">{{ user.name }}</span>
                      <span class="user-details">{{ user.unit }} - {{ user.role }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="publish-modal-footer">
        <div class="footer-left">
          <div class="publish-summary" v-if="selectedPublishTarget">
            <span class="summary-text">
              将发布到 <strong>{{ getTargetLabel(selectedPublishTarget) }}</strong>
              <span v-if="showVisibilityConfig && selectedVisibility">
                ，权限：<strong>{{ getVisibilityLabel(selectedVisibility) }}</strong>
              </span>
            </span>
          </div>
        </div>
        <div class="footer-right">
          <button class="btn-modern btn-secondary" @click="closeModal">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button class="btn-modern btn-primary" @click="confirmPublish" :disabled="!canPublish">
            <i class="fas fa-rocket"></i>
            立即发布
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 定义用户类型
interface User {
  id: number
  name: string
  unit: string
  role: string
  email: string
}

// 定义智能体类型
interface Agent {
  id: string | number
  name: string
  description?: string
  icon: string
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
}

// Props
const props = defineProps<{
  visible: boolean
  agent: Agent | null
}>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'publish': [data: any]
}>()

// 响应式数据
const selectedPublishTarget = ref('')
const selectedVisibility = ref('personal')
const selectedUsers = ref<User[]>([])
const selectedUnits = ref<string[]>([])
const userSearchQuery = ref('')
const unitSearchQuery = ref('')
const filteredUsers = ref<User[]>([])
const filteredUnits = ref<string[]>([])

// 发布目标配置
const publishTargets = ref([
  {
    value: 'home',
    label: '首页',
    icon: 'fas fa-home',
    description: '发布到平台首页，提升智能体曝光度'
  },
  {
    value: 'alert-pc',
    label: '警信PC端',
    icon: 'fas fa-desktop',
    description: '发布到警信系统PC端，供桌面用户使用'
  },
  {
    value: 'alert-mobile',
    label: '警信移动端',
    icon: 'fas fa-mobile-alt',
    description: '发布到警信系统移动端，供手机用户使用'
  },
  {
    value: 'home-battle',
    label: '首页比武榜',
    icon: 'fas fa-trophy',
    description: '发布到首页比武榜，展示智能体实力'
  },
  {
    value: 'home-gold',
    label: '首页金榜',
    icon: 'fas fa-medal',
    description: '发布到首页金榜，彰显智能体价值'
  }
])

// 可见权限配置
const visibilityOptions = ref([
  {
    value: 'personal',
    label: '只个人可见',
    icon: 'fas fa-user',
    description: '仅创建者本人可以查看和使用'
  },
  {
    value: 'unit',
    label: '本单位可见',
    icon: 'fas fa-building',
    description: '本单位内的所有成员都可以查看和使用'
  },
  {
    value: 'partial-unit',
    label: '部分单位可见',
    icon: 'fas fa-users',
    description: '选择特定单位的成员可以查看和使用'
  },
  {
    value: 'partial-user',
    label: '部分人可见',
    icon: 'fas fa-user-friends',
    description: '选择特定用户可以查看和使用'
  },
  {
    value: 'public',
    label: '公开',
    icon: 'fas fa-globe',
    description: '所有用户都可以查看和使用'
  }
])

// 单位列表
const allUnits = ref<string[]>([
  '技术部', '产品部', '运营部', '研发中心', '客服部',
  '市场部', '电商部', '数据部', '办公室', '知识部',
  '项目部', '财务部', '人事部', '仓储部', '测试部',
  '设计部', '法务部', '采购部', '质量部', '安全部',
  '销售部', '培训部', '企划部', '审计部', '监察部',
  '信息部', '网络部', '维护部', '支持部', '咨询部',
  '策划部', '宣传部', '公关部', '商务部', '合作部'
])

// 模拟用户数据
const allUsers = ref<User[]>([
  // 技术部
  { id: 1, name: '张三', unit: '技术部', role: '技术总监', email: '<EMAIL>' },
  { id: 5, name: '孙七', unit: '技术部', role: '高级工程师', email: '<EMAIL>' },
  { id: 8, name: '刘十', unit: '技术部', role: '前端工程师', email: '<EMAIL>' },
  { id: 15, name: '马强', unit: '技术部', role: '后端工程师', email: '<EMAIL>' },
  { id: 16, name: '林雪', unit: '技术部', role: '测试工程师', email: '<EMAIL>' },

  // 产品部
  { id: 2, name: '李四', unit: '产品部', role: '产品经理', email: '<EMAIL>' },
  { id: 6, name: '周八', unit: '产品部', role: '产品总监', email: '<EMAIL>' },
  { id: 9, name: '黄十一', unit: '产品部', role: '产品专员', email: '<EMAIL>' },
  { id: 17, name: '王丽', unit: '产品部', role: '用户体验师', email: '<EMAIL>' },
  { id: 18, name: '陈浩', unit: '产品部', role: '产品分析师', email: '<EMAIL>' },

  // 研发中心
  { id: 3, name: '王五', unit: '研发中心', role: '研发总监', email: '<EMAIL>' },
  { id: 10, name: '吴十二', unit: '研发中心', role: '架构师', email: '<EMAIL>' },
  { id: 19, name: '李明', unit: '研发中心', role: '算法工程师', email: '<EMAIL>' },
  { id: 20, name: '张伟', unit: '研发中心', role: 'AI工程师', email: '<EMAIL>' },
  { id: 21, name: '刘芳', unit: '研发中心', role: '数据科学家', email: '<EMAIL>' },

  // 运营部
  { id: 4, name: '赵六', unit: '运营部', role: '运营总监', email: '<EMAIL>' },
  { id: 7, name: '陈九', unit: '运营部', role: '运营专员', email: '<EMAIL>' },
  { id: 22, name: '杨洋', unit: '运营部', role: '内容运营', email: '<EMAIL>' },
  { id: 23, name: '徐静', unit: '运营部', role: '用户运营', email: '<EMAIL>' },
  { id: 24, name: '郑华', unit: '运营部', role: '活动策划', email: '<EMAIL>' },

  // 市场部
  { id: 25, name: '孙磊', unit: '市场部', role: '市场总监', email: '<EMAIL>' },
  { id: 26, name: '周杰', unit: '市场部', role: '市场专员', email: '<EMAIL>' },
  { id: 27, name: '吴琳', unit: '市场部', role: '品牌经理', email: '<EMAIL>' },
  { id: 28, name: '王芳', unit: '市场部', role: '推广专员', email: '<EMAIL>' },
  { id: 29, name: '李涛', unit: '市场部', role: '渠道经理', email: '<EMAIL>' },

  // 客服部
  { id: 30, name: '张敏', unit: '客服部', role: '客服主管', email: '<EMAIL>' },
  { id: 31, name: '刘强', unit: '客服部', role: '客服专员', email: '<EMAIL>' },
  { id: 32, name: '陈静', unit: '客服部', role: '售后专员', email: '<EMAIL>' },
  { id: 33, name: '赵敏', unit: '客服部', role: '客服培训师', email: '<EMAIL>' },

  // 财务部
  { id: 34, name: '王丽华', unit: '财务部', role: '财务总监', email: '<EMAIL>' },
  { id: 35, name: '李财', unit: '财务部', role: '会计师', email: '<EMAIL>' },
  { id: 36, name: '张会计', unit: '财务部', role: '出纳', email: '<EMAIL>' },
  { id: 37, name: '刘审计', unit: '财务部', role: '审计专员', email: '<EMAIL>' },

  // 人事部
  { id: 38, name: '人事王', unit: '人事部', role: '人事总监', email: '<EMAIL>' },
  { id: 39, name: '招聘李', unit: '人事部', role: '招聘专员', email: '<EMAIL>' },
  { id: 40, name: '培训张', unit: '人事部', role: '培训专员', email: '<EMAIL>' },
  { id: 41, name: '薪酬刘', unit: '人事部', role: '薪酬专员', email: '<EMAIL>' }
])

// 计算属性
// 是否显示可见权限配置
const showVisibilityConfig = computed(() => {
  return ['alert-pc', 'alert-mobile', 'home'].includes(selectedPublishTarget.value)
})

// 是否显示默认公开配置
const showDefaultPublicConfig = computed(() => {
  return ['home-battle', 'home-gold'].includes(selectedPublishTarget.value)
})

// 是否可以发布
const canPublish = computed(() => {
  if (!selectedPublishTarget.value) return false
  if (showVisibilityConfig.value && !selectedVisibility.value) return false
  if (selectedVisibility.value === 'partial-unit' && selectedUnits.value.length === 0) return false
  if (selectedVisibility.value === 'partial-user' && selectedUsers.value.length === 0) return false
  return true
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.agent) {
    // 重置状态
    selectedPublishTarget.value = publishTargets.value[0]?.value || ''
    // 根据默认目标设置权限
    if (['home-battle', 'home-gold'].includes(selectedPublishTarget.value)) {
      selectedVisibility.value = 'public'
    } else {
      selectedVisibility.value = 'personal'
    }
    selectedUsers.value = []
    selectedUnits.value = []
    userSearchQuery.value = ''
    unitSearchQuery.value = ''
    filteredUsers.value = []
    filteredUnits.value = []
  }
})

// 方法
const closeModal = () => {
  emit('update:visible', false)
}

const selectTarget = (targetValue: string) => {
  selectedPublishTarget.value = targetValue
  // 根据目标设置默认权限
  if (['home-battle', 'home-gold'].includes(targetValue)) {
    selectedVisibility.value = 'public'
  } else {
    selectedVisibility.value = 'personal'
  }
  selectedUsers.value = []
  selectedUnits.value = []
}

const selectVisibility = (visibilityValue: string) => {
  selectedVisibility.value = visibilityValue
  // 清空之前的选择
  selectedUsers.value = []
  selectedUnits.value = []
}

// 用户搜索和管理
const searchUsers = () => {
  const query = userSearchQuery.value.toLowerCase().trim()
  if (!query) {
    filteredUsers.value = []
    return
  }

  filteredUsers.value = allUsers.value.filter(user =>
    !selectedUsers.value.find(selected => selected.id === user.id) &&
    (user.name.toLowerCase().includes(query) ||
     user.unit.toLowerCase().includes(query) ||
     user.role.toLowerCase().includes(query) ||
     user.email.toLowerCase().includes(query))
  ).slice(0, 10) // 限制显示10个结果
}

const addUser = (user: User) => {
  if (!selectedUsers.value.find(selected => selected.id === user.id)) {
    selectedUsers.value.push(user)
  }
  userSearchQuery.value = ''
  filteredUsers.value = []
}

const removeUser = (userId: number) => {
  selectedUsers.value = selectedUsers.value.filter(user => user.id !== userId)
}

// 单位搜索和管理
const searchUnits = () => {
  const query = unitSearchQuery.value.toLowerCase().trim()
  if (!query) {
    filteredUnits.value = []
    return
  }

  filteredUnits.value = allUnits.value.filter(unit =>
    !selectedUnits.value.includes(unit) &&
    unit.toLowerCase().includes(query)
  ).slice(0, 10)
}

const addUnit = (unit: string) => {
  if (!selectedUnits.value.includes(unit)) {
    selectedUnits.value.push(unit)
  }
  unitSearchQuery.value = ''
  filteredUnits.value = []
}

const removeUnit = (unit: string) => {
  selectedUnits.value = selectedUnits.value.filter(u => u !== unit)
}

// 获取目标标签
const getTargetLabel = (targetValue: string) => {
  const target = publishTargets.value.find(t => t.value === targetValue)
  return target?.label || ''
}

// 获取权限标签
const getVisibilityLabel = (visibilityValue: string) => {
  const visibility = visibilityOptions.value.find(v => v.value === visibilityValue)
  return visibility?.label || ''
}

// 获取权限描述
const getPermissionDescription = () => {
  if (!selectedPublishTarget.value) {
    return '请先选择发布目的'
  }

  const target = publishTargets.value.find(t => t.value === selectedPublishTarget.value)
  if (['home-battle', 'home-gold'].includes(selectedPublishTarget.value)) {
    return `${target?.label}将自动设置为公开权限`
  } else {
    return `配置${target?.label}的访问权限`
  }
}

// 获取默认公开消息
const getDefaultPublicMessage = () => {
  if (selectedPublishTarget.value === 'home-battle') {
    return '发布到首页比武榜将自动设置为公开权限，所有用户都可以查看和参与比武。'
  } else if (selectedPublishTarget.value === 'home-gold') {
    return '发布到首页金榜将自动设置为公开权限，展示智能体的优秀表现。'
  }
  return '该发布目的将自动设置为公开权限。'
}

// 确认发布
const confirmPublish = () => {
  if (!props.agent || !canPublish.value) return

  const agent = props.agent
  const target = publishTargets.value.find(t => t.value === selectedPublishTarget.value)

  const publishData = {
    agent,
    target: selectedPublishTarget.value,
    targetLabel: target?.label,
    visibility: selectedVisibility.value,
    visibilityLabel: getVisibilityLabel(selectedVisibility.value),
    selectedUsers: selectedUsers.value,
    selectedUnits: selectedUnits.value
  }

  emit('publish', publishData)
  closeModal()
}
</script>

<style scoped>
/* 浅蓝色系主题发布模态框样式 */
.publish-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.publish-modal-content {
  background: #fefefe;
  border-radius: 20px;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(147, 197, 253, 0.3);
  animation: slideUp 0.4s ease;
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模态框头部 - 浅蓝色系背景，黑色字体 */
.publish-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  color: #1f2937;
}

.header-left .agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #a7c2ed 0%, #7797f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.agent-details h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.agent-type-badge {
  display: inline-block;
  padding: 2px 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  margin-top: 2px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #111827;
  transform: scale(1.05);
}

/* 模态框主体 */
.publish-modal-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧面板 - 浅蓝色系 */
.publish-left-panel {
  width: 400px;
  background: #f8fafc;
  border-right: 1px solid #dbeafe;
  padding: 24px;
  overflow-y: auto;
}

.panel-header {
  margin-bottom: 20px;
}

.panel-header h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-header h4 i {
  color: #3b82f6;
}

.panel-desc {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.publish-targets-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.target-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.target-card:hover {
  border-color: #93c5fd;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.target-card.active {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
}

.target-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #a7c2ed 0%, #7797f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.target-info {
  flex: 1;
}

.target-info h5 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.target-info p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.target-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.2s ease;
}

.target-card.active .target-check {
  opacity: 1;
  transform: scale(1);
}

/* 右侧面板 - 浅蓝色系 */
.publish-right-panel {
  flex: 1;
  background: white;
  padding: 24px;
  overflow-y: auto;
}

.visibility-config {
  margin-top: 16px;
}

.visibility-options-modern {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.visibility-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.visibility-card:hover {
  border-color: #93c5fd;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.visibility-card.active {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
}

.visibility-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: linear-gradient(135deg, #a7c2ed 0%, #7797f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  margin-right: 12px;
  flex-shrink: 0;
}

.visibility-info {
  flex: 1;
}

.visibility-info h6 {
  margin: 0 0 4px 0;
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
}

.visibility-info p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.visibility-check {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.2s ease;
}

.visibility-card.active .visibility-check {
  opacity: 1;
  transform: scale(1);
}

/* 默认公开配置 */
.default-public-config {
  margin-bottom: 24px;
}

.public-notice {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 12px;
  gap: 12px;
}

.notice-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: #f59e0b;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.notice-content h6 {
  margin: 0 0 4px 0;
  font-size: 15px;
  font-weight: 600;
  color: #92400e;
}

.notice-content p {
  margin: 0;
  font-size: 13px;
  color: #a16207;
  line-height: 1.4;
}

/* 配置区域 */
.config-section {
  margin-bottom: 24px;
}

.config-section h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  min-height: 40px;
  padding: 12px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.selected-item i {
  font-size: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 600;
}

.user-details {
  font-size: 11px;
  opacity: 0.9;
}

.remove-item {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.search-section {
  position: relative;
}

.search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input i {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  font-size: 14px;
  z-index: 1;
}

.search-input input {
  width: 100%;
  padding: 12px 12px 12px 36px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.search-input input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
  margin-top: 4px;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background: #f8fafc;
}

.search-result-item i {
  color: #6b7280;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

/* 模态框底部 - 浅蓝色系 */
.publish-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #dbeafe;
  background: #f8fafc;
}

.footer-left {
  flex: 1;
}

.publish-summary {
  font-size: 14px;
  color: #374151;
}

.summary-text {
  line-height: 1.5;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  min-width: 100px;
  justify-content: center;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-modern i {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .publish-modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }

  .publish-modal-body {
    flex-direction: column;
  }

  .publish-left-panel {
    width: 100%;
    max-height: 300px;
  }

  .publish-right-panel {
    border-top: 1px solid #e2e8f0;
    border-right: none;
  }
}

@media (max-width: 768px) {
  .publish-modal {
    padding: 10px;
  }

  .publish-modal-header {
    padding: 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-right h2 {
    font-size: 20px;
  }

  .publish-modal-footer {
    padding: 20px;
    flex-direction: column;
    gap: 16px;
  }

  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .btn-modern {
    flex: 1;
    min-width: auto;
  }

  .publish-targets-grid {
    gap: 8px;
  }

  .target-card {
    padding: 12px;
  }

  .target-info h5 {
    font-size: 14px;
  }

  .target-info p {
    font-size: 12px;
  }

  .visibility-options-modern {
    gap: 8px;
  }

  .visibility-card {
    padding: 12px;
  }

  .visibility-info h6 {
    font-size: 14px;
  }

  .visibility-info p {
    font-size: 12px;
  }
}
</style>
