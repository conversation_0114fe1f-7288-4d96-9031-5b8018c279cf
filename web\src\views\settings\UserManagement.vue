
<template>
  <div class="user-management bg-white rounded-lg shadow-sm p-6">
    <div class="users-header flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900">用户列表</h3>
      <button @click="showAddUserModal = true" class="btn-primary">
        <span class="mr-2">➕</span>
        添加用户
      </button>
    </div>

    <!-- 筛选区域 -->
    <div class="filters-section bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100 mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-800 flex items-center">
          <span class="mr-2">🔍</span>
          筛选条件
        </h4>
        <button @click="clearFilters" class="btn-secondary text-sm px-3 py-1">
          <span class="mr-1">🔄</span>
          清空筛选
        </button>
      </div>

      <el-form :model="filters" :inline="true" class="search-form">
        <el-form-item label="用户名">
          <el-input
            v-model="filters.username"
            placeholder="输入用户名..."
            clearable
            :prefix-icon="User"
            size="small"
            style="width: 180px"
          />
        </el-form-item>

        <el-form-item label="所属部门">
          <div style="width: 180px">
            <DeptTreeSelector
              v-model="filters.deptId"
              mode="dropdown"
              :config="{
                multiple: false,
                placeholder: '全部部门',
                size: 'small',
                clearable: true,
                filterable: true,
                checkStrictly: true,
                showCheckbox: false
              }"
              @change="handleDeptFilterChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="用户状态">
          <div style="width: 120px">
            <StatusSelector
              v-model="filters.status"
              mode="dropdown"
              preset="user"
              align="left"
              :config="{
                placeholder: '全部状态',
                size: 'small',
                clearable: true,
                showDescription: false
              }"
              @change="handleStatusFilterChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="性别">
          <div style="width: 120px">
            <StatusSelector
              v-model="filters.gender"
              mode="dropdown"
              preset="custom"
              align="left"
              :options="genderStatusOptions"
              :config="{
                placeholder: '全部性别',
                size: 'small',
                clearable: true,
                showDescription: false
              }"
              @change="handleGenderFilterChange"
            />
          </div>
        </el-form-item>
      </el-form>

      <!-- 搜索按钮 -->
      <div class="flex items-center gap-3 mt-4">
        <el-button type="primary" @click="searchUsers" :loading="loading" size="small" :icon="Search">
          {{ loading ? '搜索中...' : '搜索' }}
        </el-button>
        <el-button @click="clearFilters" size="small" :icon="Refresh">
          重置
        </el-button>
        <el-button type="success" @click="showAddUserModal = true" size="small" :icon="Plus">
          添加用户
        </el-button>
      </div>

      <!-- 筛选结果统计 -->
      <div class="mt-4 flex items-center justify-between text-sm text-gray-600">
        <div>
          共找到 <span class="font-semibold text-blue-600">{{ usersPagination.total }}</span> 个用户
          <span v-if="hasActiveFilters" class="ml-2 text-orange-600">（已筛选）</span>
        </div>
        <div v-if="hasActiveFilters" class="flex items-center gap-2">
          <span class="text-xs">活跃筛选:</span>
          <span v-if="filters.username" class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
            用户名: {{ filters.username }}
          </span>
          <span v-if="filters.deptId" class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
            部门: {{ depts.find(d => d.id === filters.deptId)?.deptName }}
          </span>
          <span v-if="filters.status" class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
            状态: {{ statusOptions.find(s => s.dictValue === filters.status)?.dictLabel }}
          </span>
          <span v-if="filters.gender" class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">
            性别: {{ genderOptions.find(g => g.dictValue === filters.gender)?.dictLabel }}
          </span>
        </div>
      </div>
    </div>

    <el-table
      :data="paginatedUsers"
      v-loading="loading"
      stripe
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      :row-style="{ height: '60px' }"
      empty-text="暂无数据"
    >
      <el-table-column prop="username" label="用户名" width="180" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="font-medium">{{ row.username }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.nickname || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="phone" label="手机号" width="130" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.phone" class="flex items-center">
            <el-icon class="mr-1 text-green-500">
              <Phone />
            </el-icon>
            <span>{{ row.phone }}</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.email" class="flex items-center">
            <el-icon class="mr-1 text-blue-500">
              <Message />
            </el-icon>
            <span>{{ row.email }}</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="deptName" label="所属部门" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="getDeptDisplayName(row)" class="flex items-center">
            <el-icon class="mr-1 text-green-500">
              <OfficeBuilding />
            </el-icon>
            <span>{{ getDeptDisplayName(row) }}</span>
          </div>
          <el-tag v-else type="warning" size="small">
            <el-icon class="mr-1">
              <Warning />
            </el-icon>
            未分配部门
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="genderName" label="性别" width="80" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.genderName" :type="row.gender === '1' ? 'primary' : 'success'" size="small">
            {{ row.genderName }}
          </el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="userType" label="用户类型" width="100" align="center">
        <template #default="{ row }">
          <el-tag
            v-if="row.userType"
            :type="row.userType === 'platform' ? 'danger' : row.userType === 'tenant' ? 'warning' : 'info'"
            size="small"
          >
            {{ row.userType === 'platform' ? '平台' : row.userType === 'tenant' ? '租户' : '普通' }}
          </el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="loginIp" label="最后登录IP" width="130" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.loginIp" class="flex items-center">
            <el-icon class="mr-1 text-purple-500">
              <Monitor />
            </el-icon>
            <span>{{ row.loginIp }}</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="loginTime" label="最后登录时间" width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.loginTime" class="flex items-center">
            <el-icon class="mr-1 text-orange-500">
              <Clock />
            </el-icon>
            <span>{{ row.loginTime }}</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag
            :type="row.status === '0' ? 'success' : row.status === '1' ? 'danger' : 'warning'"
            size="small"
          >
            {{ row.statusName || '未知' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="250" align="center" fixed="right">
        <template #default="{ row }">
          <div class="flex gap-1 justify-center">
            <el-button
              type="info"
              size="small"
              @click="viewUser(row)"
              :icon="View"
            >
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="authorizeUser(row)"
              :icon="Key"
            >
              授权
            </el-button>
            <el-dropdown @command="handleUserCommand" trigger="click">
              <el-button type="info" size="small" :icon="MoreFilled">
                更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`editUser:${row.id}`" :icon="Edit">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="`reset:${row.id}`" :icon="Refresh">
                    重置密码
                  </el-dropdown-item>
                  <el-dropdown-item :command="`delete:${row.id}`" :icon="Delete" divided>
                    删除用户
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 用户管理分页 -->
    <div class="flex justify-center mt-4">
      <el-pagination
        v-model:current-page="usersPagination.currentPage"
        v-model:page-size="usersPagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="usersPagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>

    <!-- 用户编辑/添加模态框 -->
    <div v-if="showAddUserModal || showEditUserModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
      <div class="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-3xl mx-4 transform transition-all duration-300 scale-100 max-h-[90vh] overflow-y-auto">
        <!-- 模态框头部 -->
        <div class="flex items-center justify-between mb-8">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <i class="fas fa-user text-white text-lg"></i>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-900">
                {{ showAddUserModal ? '添加用户' : '编辑用户' }}
              </h3>
              <p class="text-sm text-gray-500 mt-1">
                {{ showAddUserModal ? '创建新的系统用户' : '修改用户信息' }}
              </p>
            </div>
          </div>
          <button @click="closeUserModal" class="w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200">
            <i class="fas fa-times text-gray-500"></i>
          </button>
        </div>

        <el-form ref="userFormRef" :model="currentUser" :rules="userRules" @submit.prevent="saveUser" class="space-y-6">
          <!-- 基本信息区域 -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
            <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <el-icon class="text-blue-500 mr-2"><User /></el-icon>
              基本信息
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <el-form-item label="用户名" prop="username" required>
                <el-input
                  v-model="currentUser.username"
                  placeholder="请输入用户名"
                  :prefix-icon="User"
                  clearable
                />
              </el-form-item>

              <el-form-item label="昵称" prop="nickname">
                <el-input
                  v-model="currentUser.nickname"
                  placeholder="请输入昵称"
                  clearable
                />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="currentUser.email"
                  type="email"
                  placeholder="请输入邮箱地址"
                  :prefix-icon="Message"
                  clearable
                />
              </el-form-item>

              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="currentUser.phone"
                  placeholder="请输入手机号"
                  :prefix-icon="Phone"
                  clearable
                />
              </el-form-item>
            </div>
          </div>

          <!-- 组织信息区域 -->
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
            <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <el-icon class="text-green-500 mr-2"><OfficeBuilding /></el-icon>
              组织信息
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <el-form-item label="所属部门" prop="deptId">
                <el-tree-select
                  v-model="currentUser.deptId"
                  :data="deptTreeData"
                  placeholder="请选择部门"
                  style="width: 100%"
                  clearable
                  check-strictly
                  filterable
                  :render-after-expand="false"
                  :default-expand-all="false"
                  :expand-on-click-node="false"
                  :props="{
                    value: 'value',
                    label: 'label',
                    children: 'children'
                  }"
                  node-key="value"
                />
              </el-form-item>

              <el-form-item label="性别" prop="gender">
                <el-select
                  v-model="currentUser.gender"
                  placeholder="请选择性别"
                  style="width: 100%"
                >
                  <el-option
                    v-for="gender in genderOptions"
                    :key="gender.dictValue"
                    :value="gender.dictValue"
                    :label="gender.dictLabel"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <!-- 账户设置区域 -->
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
            <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <el-icon class="text-purple-500 mr-2"><Setting /></el-icon>
              账户设置
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="currentUser.status"
                  placeholder="请选择状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="status in statusOptions"
                    :key="status.dictValue"
                    :value="status.dictValue"
                    :label="status.dictLabel"
                  />
                </el-select>
              </el-form-item>

              <el-form-item v-if="showAddUserModal" label="密码" prop="password" required>
                <el-input
                  v-model="currentUser.password"
                  type="password"
                  placeholder="请输入密码"
                  show-password
                />
                <template #extra>
                  <div class="text-xs text-gray-500 mt-1">
                    <el-icon class="mr-1"><InfoFilled /></el-icon>
                    密码长度至少6位，建议包含字母、数字和特殊字符
                  </div>
                </template>
              </el-form-item>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-4 pt-6 border-t border-gray-200">
            <el-button
              type="primary"
              @click="saveUser"
              :loading="loading"
              size="large"
              style="flex: 1"
            >
              <el-icon class="mr-2"><Check /></el-icon>
              {{ loading ? '保存中...' : '保存用户' }}
            </el-button>
            <el-button
              @click="closeUserModal"
              size="large"
              style="flex: 1"
            >
              <el-icon class="mr-2"><Close /></el-icon>
              取消
            </el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 用户授权模态框 -->
    <div v-if="showAuthModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">用户授权</h3>
          <button @click="showAuthModal = false" class="text-gray-400 hover:text-gray-600">
            <span class="text-xl">×</span>
          </button>
        </div>

        <div class="space-y-4">
          <div class="user-info p-4 bg-gray-50 rounded-lg">
            <p><strong>用户：</strong>{{ authUser?.username }}</p>
            <p><strong>邮箱：</strong>{{ authUser?.email }}</p>
            <p><strong>部门：</strong>{{ authUser?.deptName }}</p>
          </div>

          <div class="user-role-section">
            <h4 class="font-medium text-gray-800 mb-3">角色设置</h4>
            <div class="space-y-2">
              <label v-for="role in roleOptions" :key="role.value" class="flex items-center">
                <input
                  v-model="authForm.roleIds"
                  :value="role.value"
                  type="checkbox"
                  class="form-checkbox mr-2"
                />
                <span>{{ role.label }}</span>
              </label>
            </div>
          </div>

          <div class="permissions-section">
            <h4 class="font-medium text-gray-800 mb-3">权限设置</h4>
            <div class="space-y-2">
              <label v-for="permission in permissionOptions" :key="permission.value" class="flex items-center">
                <input
                  v-model="authForm.permissions"
                  :value="permission.value"
                  type="checkbox"
                  class="form-checkbox mr-2"
                />
                <span>{{ permission.label }}</span>
              </label>
            </div>
          </div>

          <div class="flex gap-3 pt-4">
            <button @click="saveAuth" class="btn-primary flex-1" :disabled="loading">
              {{ loading ? '保存中...' : '保存授权' }}
            </button>
            <button @click="closeAuthModal" class="btn-secondary flex-1">取消</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户详情侧边栏 -->
    <div v-if="showUserDetailDrawer" class="fixed inset-0 z-50 overflow-hidden">
      <div class="absolute inset-0 bg-black bg-opacity-50" @click="closeUserDetailDrawer"></div>
      <div class="absolute right-0 top-0 h-full w-[480px] bg-white shadow-2xl transform transition-transform duration-300 ease-in-out overflow-y-auto">
        <!-- 头部 -->
        <div class="sticky top-0 z-10 flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <el-icon class="text-white text-xl"><User /></el-icon>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-900">用户详情</h3>
              <p class="text-sm text-gray-500">{{ viewUserDetail?.username }}</p>
            </div>
          </div>
          <button @click="closeUserDetailDrawer" class="w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200">
            <el-icon class="text-gray-500"><Close /></el-icon>
          </button>
        </div>

        <!-- 用户详情内容 -->
        <div class="p-6 space-y-6">
          <!-- 基本信息卡片 -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
            <div class="flex items-center mb-4">
              <el-icon class="text-blue-500 text-lg mr-2"><User /></el-icon>
              <h4 class="text-lg font-semibold text-gray-800">基本信息</h4>
            </div>
            <div class="grid grid-cols-1 gap-4">
              <div class="flex items-center justify-between py-2 border-b border-blue-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">用户ID</span>
                <span class="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">{{ viewUserDetail?.id }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-blue-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">用户名</span>
                <span class="text-sm text-gray-900 font-semibold">{{ viewUserDetail?.username }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-blue-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">昵称</span>
                <span class="text-sm text-gray-900">{{ viewUserDetail?.nickname || '-' }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-blue-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">邮箱</span>
                <span class="text-sm text-gray-900">{{ viewUserDetail?.email || '-' }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-blue-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">手机号</span>
                <span class="text-sm text-gray-900">{{ viewUserDetail?.phone || '-' }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-blue-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">性别</span>
                <el-tag v-if="viewUserDetail?.genderName" :type="viewUserDetail?.gender === '1' ? 'primary' : 'success'" size="small">
                  {{ viewUserDetail?.genderName }}
                </el-tag>
                <span v-else class="text-sm text-gray-400">-</span>
              </div>
            </div>
          </div>

          <!-- 组织信息卡片 -->
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
            <div class="flex items-center mb-4">
              <el-icon class="text-green-500 text-lg mr-2"><OfficeBuilding /></el-icon>
              <h4 class="text-lg font-semibold text-gray-800">组织信息</h4>
            </div>
            <div class="grid grid-cols-1 gap-4">
              <div class="flex items-center justify-between py-2 border-b border-green-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">所属部门</span>
                <div v-if="viewUserDetail && getDeptDisplayName(viewUserDetail)" class="flex items-center">
                  <el-icon class="text-green-500 mr-1"><OfficeBuilding /></el-icon>
                  <span class="text-sm text-gray-900">{{ getDeptDisplayName(viewUserDetail) }}</span>
                </div>
                <el-tag v-else type="warning" size="small">未分配部门</el-tag>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-green-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">用户类型</span>
                <el-tag
                  v-if="viewUserDetail?.userType"
                  :type="viewUserDetail?.userType === 'platform' ? 'danger' : viewUserDetail?.userType === 'tenant' ? 'warning' : 'info'"
                  size="small"
                >
                  {{ viewUserDetail?.userType === 'platform' ? '平台' : viewUserDetail?.userType === 'tenant' ? '租户' : '普通' }}
                </el-tag>
                <span v-else class="text-sm text-gray-400">-</span>
              </div>
            </div>
          </div>

          <!-- 状态信息卡片 -->
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100">
            <div class="flex items-center mb-4">
              <el-icon class="text-purple-500 text-lg mr-2"><Setting /></el-icon>
              <h4 class="text-lg font-semibold text-gray-800">状态信息</h4>
            </div>
            <div class="grid grid-cols-1 gap-4">
              <div class="flex items-center justify-between py-2 border-b border-purple-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">账户状态</span>
                <el-tag
                  :type="viewUserDetail?.status === '0' ? 'success' : viewUserDetail?.status === '1' ? 'danger' : 'warning'"
                  size="small"
                >
                  {{ viewUserDetail?.statusName || '未知' }}
                </el-tag>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-purple-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">最后登录IP</span>
                <div v-if="viewUserDetail?.loginIp" class="flex items-center">
                  <el-icon class="text-purple-500 mr-1"><Monitor /></el-icon>
                  <span class="text-sm text-gray-900 font-mono">{{ viewUserDetail?.loginIp }}</span>
                </div>
                <span v-else class="text-sm text-gray-400">-</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-purple-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">最后登录时间</span>
                <div v-if="viewUserDetail?.loginTime" class="flex items-center">
                  <el-icon class="text-purple-500 mr-1"><Clock /></el-icon>
                  <span class="text-sm text-gray-900">{{ viewUserDetail?.loginTime }}</span>
                </div>
                <span v-else class="text-sm text-gray-400">-</span>
              </div>
            </div>
          </div>

          <!-- 系统信息卡片 -->
          <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100">
            <div class="flex items-center mb-4">
              <el-icon class="text-orange-500 text-lg mr-2"><InfoFilled /></el-icon>
              <h4 class="text-lg font-semibold text-gray-800">系统信息</h4>
            </div>
            <div class="grid grid-cols-1 gap-4">
              <div class="flex items-center justify-between py-2 border-b border-orange-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">创建人</span>
                <span class="text-sm text-gray-900">{{ viewUserDetail?.createBy || '-' }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-orange-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">创建时间</span>
                <span class="text-sm text-gray-900">{{ viewUserDetail?.createTime || '-' }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-orange-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">更新人</span>
                <span class="text-sm text-gray-900">{{ viewUserDetail?.updateBy || '-' }}</span>
              </div>
              <div class="flex items-center justify-between py-2 border-b border-orange-100 last:border-b-0">
                <span class="text-sm font-medium text-gray-600">更新时间</span>
                <span class="text-sm text-gray-900">{{ viewUserDetail?.updateTime || '-' }}</span>
              </div>
              <div v-if="viewUserDetail?.remark" class="flex flex-col py-2">
                <span class="text-sm font-medium text-gray-600 mb-2">备注</span>
                <div class="bg-white rounded-lg p-3 border border-orange-200">
                  <span class="text-sm text-gray-900">{{ viewUserDetail?.remark }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="sticky bottom-0 bg-white border-t border-gray-200 p-6">
          <div class="flex gap-3">
            <el-button type="primary" @click="viewUserDetail && editUser(viewUserDetail)" style="flex: 1">
              <el-icon class="mr-2"><Edit /></el-icon>
              编辑用户
            </el-button>
            <el-button @click="closeUserDetailDrawer" style="flex: 1">
              <el-icon class="mr-2"><Close /></el-icon>
              关闭
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Message, OfficeBuilding, Warning, Edit, Key, Delete, Phone, Monitor, Clock, Refresh, Search, Plus, MoreFilled, Setting, InfoFilled, Check, Close, View } from '@element-plus/icons-vue'
import { UserAPI, DeptAPI } from '@/api/system'
import DictAPI from '@/api/dict'
import { DeptTreeSelector } from '@/components/common/selectors'
import StatusSelector from '@/components/common/selectors/StatusSelector.vue'
import type {
  SysUserVO,
  SysUser,
  SysUserQueryDTO,
  SysDeptVO,
  SysDictDataVO,
  ResetPasswordRequest,
  UserAuthRequest
} from '@/types/system'

// 用户数据
const users = ref<SysUserVO[]>([])

// 部门数据（用于下拉选择）
const depts = ref<SysDeptVO[]>([])

// 字典数据
const dictData = ref<Record<string, SysDictDataVO[]>>({})

// 性别字典
const genderOptions = computed(() => dictData.value['sys_user_gender'] || [])

// 状态字典
const statusOptions = computed(() => dictData.value['sys_user_status'] || [])

// 性别状态选项（转换为StatusSelector格式）
const genderStatusOptions = computed(() => {
  const options = [
    { value: '', label: '全部性别', color: '#909399', bgColor: '#f4f4f5' }
  ]

  genderOptions.value.forEach(gender => {
    options.push({
      value: gender.dictValue,
      label: gender.dictLabel,
      color: gender.dictValue === '1' ? '#3b82f6' : gender.dictValue === '2' ? '#ec4899' : '#6b7280',
      bgColor: gender.dictValue === '1' ? '#dbeafe' : gender.dictValue === '2' ? '#fce7f3' : '#f3f4f6'
    })
  })

  return options
})

// 部门树形数据（用于模态框中的树形选择器）
const deptTreeData = computed(() => {
  // 递归转换部门数据为 el-tree-select 需要的格式
  const convertToTreeSelectFormat = (depts: SysDeptVO[]): any[] => {
    return depts.map(dept => {
      const node: any = {
        value: dept.id,
        label: dept.deptName
      }

      // 如果有子部门，递归处理
      if (dept.children && dept.children.length > 0) {
        node.children = convertToTreeSelectFormat(dept.children)
      }

      return node
    })
  }

  // 如果没有部门数据，返回空数组
  if (!depts.value || depts.value.length === 0) {
    return [
      {
        value: '0',
        label: '顶级部门'
      }
    ]
  }

  // 转换部门数据
  const convertedDepts = convertToTreeSelectFormat(depts.value)

  // 添加顶级选项，并包含所有层级的部门
  const treeData = [
    {
      value: '0',
      label: '顶级部门',
      children: convertedDepts
    }
  ]

  // 可选：在开发环境下打印树形结构用于调试
  if (process.env.NODE_ENV === 'development' && treeData.length > 1) {
    console.log('部门树形结构已构建，包含', treeData[0].children?.length || 0, '个顶级部门')
  }

  return treeData
})

// 筛选条件
const filters = ref<SysUserQueryDTO>({
  username: '',
  deptId: '',
  status: '',
  gender: '',
  current: 1,
  size: 10
})

// 分页数据
const usersPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)

// 模态框状态
const showAddUserModal = ref(false)
const showEditUserModal = ref(false)
const showAuthModal = ref(false)
const showUserDetailDrawer = ref(false)

// 当前编辑的用户
const currentUser = ref<SysUser>({
  username: '',
  nickname: '',
  email: '',
  phone: '',
  deptId: '',
  gender: '0',
  status: '0'
})

// 重置密码表单
const resetPasswordForm = ref<ResetPasswordRequest>({
  userId: '',
  newPassword: ''
})

// 用户授权表单
const authForm = ref<UserAuthRequest>({
  userId: '',
  roleIds: [],
  permissions: []
})

// 检查是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return filters.value.username || filters.value.deptId || filters.value.status || filters.value.gender
})

// 分页用户列表（从后端获取，不需要前端分页计算）
const paginatedUsers = computed(() => users.value)

// 获取部门显示名称
const getDeptDisplayName = (user: SysUserVO) => {
  // 如果有 deptName，直接返回
  if (user.deptName) {
    return user.deptName
  }

  // 如果 deptId 为 "0"，显示为顶级部门
  if (user.deptId === "0") {
    return "顶级部门"
  }

  // 如果有 deptId 但没有 deptName，尝试从部门列表中查找
  if (user.deptId) {
    const dept = depts.value.find(d => d.id === user.deptId)
    if (dept) {
      return dept.deptName
    }
  }

  return null
}

// 权限选项
const permissionOptions = [
  { value: 'platform_access', label: '平台访问权限' },
  { value: 'unit_admin', label: '单位管理权限' },
  { value: 'user_manage', label: '用户管理权限' },
  { value: 'agent_create', label: '智能体创建权限' },
  { value: 'knowledge_manage', label: '知识库管理权限' }
]

// 角色选项
const roleOptions = [
  { value: 'admin', label: '管理员' },
  { value: 'designer', label: '设计师' },
  { value: 'user', label: '普通用户' }
]

// 授权用户
const authUser = ref<SysUserVO | null>(null)

// 查看用户详情
const viewUserDetail = ref<SysUserVO | null>(null)

// 表单引用
const userFormRef = ref()

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 100, message: '密码长度在 6 到 100 个字符', trigger: 'blur' }
  ]
}

/**
 * 初始化数据
 */
const initData = async () => {
  await Promise.all([
    loadUsers(),
    loadDepts(),
    loadDictData()
  ])
}

/**
 * 加载用户列表
 */
const loadUsers = async () => {
  try {
    loading.value = true
    const queryParams = {
      ...filters.value,
      current: usersPagination.value.currentPage,
      size: usersPagination.value.pageSize
    }

    const response = await UserAPI.getUserPage(queryParams)
    if (response.success && response.data) {
      users.value = response.data.records
      usersPagination.value.total = response.data.total
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 加载部门列表
 */
const loadDepts = async () => {
  try {
    const response = await DeptAPI.getDeptTree({})
    if (response.success && response.data) {
      depts.value = response.data
    }
  } catch (error) {
    console.error('加载部门列表失败:', error)
  }
}

/**
 * 加载字典数据
 */
const loadDictData = async () => {
  try {
    const response = await DictAPI.getSystemDicts()
    if (response.success && response.data) {
      dictData.value = response.data
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 清空筛选条件
const clearFilters = () => {
  filters.value = {
    username: '',
    deptId: '',
    status: '',
    gender: '',
    current: 1,
    size: 10
  }
  // 重置分页到第一页
  usersPagination.value.currentPage = 1
  // 重新加载数据
  loadUsers()
}

// 用户管理方法
const viewUser = async (user: SysUserVO) => {
  try {
    const response = await UserAPI.getUserById(user.id)
    if (response.success && response.data) {
      // 将SysUser转换为SysUserVO格式，补充显示信息
      viewUserDetail.value = {
        ...user, // 使用列表中的用户信息（包含deptName等显示字段）
        ...response.data // 覆盖详细信息
      }
      showUserDetailDrawer.value = true
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

const editUser = async (user: SysUserVO) => {
  try {
    const response = await UserAPI.getUserById(user.id)
    if (response.success && response.data) {
      currentUser.value = { ...response.data }
      showEditUserModal.value = true
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
  }
}

const authorizeUser = (user: SysUserVO) => {
  authUser.value = { ...user }
  authForm.value = {
    userId: user.id,
    roleIds: user.roles || [],
    permissions: user.permissions || []
  }
  showAuthModal.value = true
}

const deleteUser = async (userId: string) => {
  if (confirm('确定要删除该用户吗？')) {
    try {
      const response = await UserAPI.deleteUsers([userId])
      if (response.success) {
        await loadUsers()
      }
    } catch (error) {
      console.error('删除用户失败:', error)
    }
  }
}

const closeUserModal = () => {
  showAddUserModal.value = false
  showEditUserModal.value = false
  currentUser.value = {
    username: '',
    nickname: '',
    email: '',
    phone: '',
    deptId: '',
    gender: '0',
    status: '0',
    password: ''
  }
  // 重置表单验证
  if (userFormRef.value) {
    userFormRef.value.resetFields()
  }
}

const saveUser = async () => {
  if (!userFormRef.value) return

  try {
    // 表单验证
    await userFormRef.value.validate()

    loading.value = true

    // 处理空字符串为undefined
    const userData = { ...currentUser.value }
    if (userData.deptId === '') {
      userData.deptId = undefined
    }
    if (userData.email === '') {
      userData.email = undefined
    }
    if (userData.phone === '') {
      userData.phone = undefined
    }
    if (userData.nickname === '') {
      userData.nickname = undefined
    }

    if (showEditUserModal.value) {
      // 编辑用户时，如果密码为空则不传递密码字段
      if (!userData.password) {
        delete userData.password
      }
      const response = await UserAPI.updateUser(userData)
      if (response.success) {
        ElMessage.success('用户更新成功')
        await loadUsers()
        closeUserModal()
      }
    } else {
      // 添加用户
      const response = await UserAPI.addUser(userData)
      if (response.success) {
        ElMessage.success('用户创建成功')
        await loadUsers()
        closeUserModal()
      }
    }
  } catch (error) {
    console.error('保存用户失败:', error)
    if (error instanceof Error) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('保存用户失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理用户操作命令
const handleUserCommand = (command: string) => {
  const [action, userId] = command.split(':')
  const user = users.value.find(u => u.id === userId)

  if (!user) return

  switch (action) {
    case 'reset':
      resetUserPassword(user)
      break
    case 'delete':
      deleteUser(userId)
      break
    case 'editUser':
      editUser(user)
      break
  }
}

// 重置用户密码
const resetUserPassword = async (user: SysUserVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 "${user.username}" 的密码吗？重置后密码将变为默认密码。`,
      '重置密码确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await UserAPI.resetPassword({ userId: user.id, newPassword: '123456' })
    if (response.success) {
      ElMessage.success('密码重置成功，新密码为：123456')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
    }
  }
}

// 权限管理方法
const saveAuth = async () => {
  try {
    const response = await UserAPI.authorizeUser(authForm.value)
    if (response.success) {
      await loadUsers()
      closeAuthModal()
    }
  } catch (error) {
    console.error('用户授权失败:', error)
  }
}

const closeAuthModal = () => {
  showAuthModal.value = false
  authUser.value = null
  authForm.value = {
    userId: '',
    roleIds: [],
    permissions: []
  }
}

// 关闭用户详情窗口
const closeUserDetailDrawer = () => {
  showUserDetailDrawer.value = false
  viewUserDetail.value = null
}

// 搜索用户
const searchUsers = () => {
  usersPagination.value.currentPage = 1
  filters.value.current = 1
  loadUsers()
}

// 筛选器事件处理
const handleDeptFilterChange = (value: string | string[]) => {
  // 部门过滤器变化处理
  console.log('部门过滤器变化:', value)
  filters.value.deptId = Array.isArray(value) ? value[0] || '' : value || ''
}

const handleStatusFilterChange = (value: string | number | null) => {
  // 状态过滤器变化处理
  console.log('状态过滤器变化:', value)
  filters.value.status = value ? String(value) : ''
}

const handleGenderFilterChange = (value: string | number | null) => {
  // 性别过滤器变化处理
  console.log('性别过滤器变化:', value)
  filters.value.gender = value ? String(value) : ''
}

// 分页变化处理
const handlePageChange = (page: number) => {
  usersPagination.value.currentPage = page
  filters.value.current = page
  loadUsers()
}

const handlePageSizeChange = (size: number) => {
  usersPagination.value.pageSize = size
  usersPagination.value.currentPage = 1
  filters.value.size = size
  filters.value.current = 1
  loadUsers()
}

// 组件挂载时初始化数据
onMounted(() => {
  initData()
})
</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');

/* 必填字段标识 */
.form-label.required::after {
  content: ' *';
  color: #ef4444;
  font-weight: bold;
}

/* 筛选区域样式 */
.filters-section {
  position: relative;
  overflow: hidden;
}

.filters-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  opacity: 0.8;
}

.filter-group {
  position: relative;
}

.filter-group .form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 筛选标签样式 */
.filters-section .px-2 {
  transition: all 0.2s ease;
  cursor: default;
}

.filters-section .px-2:hover {
  transform: scale(1.05);
}

/* 清空筛选按钮样式 */
.filters-section .btn-secondary {
  transition: all 0.2s ease;
}

.filters-section .btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 用户表格样式增强 */
.users-table .table-row {
  transition: all 0.2s ease;
}

.el-avatar--circle {
  flex-shrink: 0 !important;
}

.users-table .table-row:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

/* 状态标签样式 */
.table-row .px-2 {
  transition: all 0.2s ease;
}

.table-row:hover .px-2 {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .filters-section .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .filters-section .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filters-section .flex.items-center.justify-between {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .filters-section .flex.items-center.gap-2 {
    flex-wrap: wrap;
    justify-content: center;
  }

  .users-table .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .users-table .table-row > div {
    padding: 0.25rem 0;
  }

  .users-table .table-row > div:first-child {
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filters-section {
  animation: fadeIn 0.3s ease-out;
}
</style>
