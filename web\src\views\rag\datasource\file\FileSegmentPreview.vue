<template>
  <div v-if="isOpen" class="file-segment-preview-overlay" @click="handleOverlayClick">
    <div class="file-segment-preview-container" @click.stop>
      <!-- 头部 -->
      <div class="preview-header">
        <div class="header-left">
          <h3 class="file-title">{{ currentFile?.name || '文件预览' }}</h3>
          <div class="file-info">
            <span class="file-size">{{ formatFileSize(currentFile?.fileSize || 0) }}</span>
            <span class="file-type">{{ (currentFile?.docType || '').toUpperCase() }}</span>
            <span v-if="currentFile?.wordCount" class="word-count">
              {{ currentFile.wordCount }} 字符
            </span>
          </div>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="handleClose" title="关闭">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- 主体内容 -->
      <div class="preview-content">
        <!-- 左侧：文档预览 -->
        <div class="document-preview-panel">
          <div class="panel-header">
            <h4>文档预览</h4>
            <div class="preview-controls">
              <button class="control-btn" @click="toggleFullscreen" title="全屏">
                <i class="fas fa-expand"></i>
              </button>
              <button class="control-btn" @click="downloadFile" title="下载">
                <i class="fas fa-download"></i>
              </button>
            </div>
          </div>
          <div class="document-viewer">
            <!-- OnlyOffice 文档预览区域 -->
            <div v-if="isOfficeDocument" class="onlyoffice-viewer">
              <iframe
                v-if="onlyOfficeUrl"
                :src="onlyOfficeUrl"
                class="onlyoffice-iframe"
                frameborder="0"
                allowfullscreen
              ></iframe>
              <div v-else class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <span>正在加载文档预览...</span>
              </div>
            </div>
            <!-- 其他文件类型使用现有的 FileViewer -->
            <div v-else class="file-viewer-container">
              <FileViewer
                :file="currentFile"
                :segments="segments"
                :selectedSegmentId="selectedSegmentId"
                @segmentHover="handleSegmentHover"
                @segmentLeave="handleSegmentLeave"
              />
            </div>
          </div>
        </div>

        <!-- 右侧：分段列表 -->
        <div class="segments-panel">
          <div class="panel-header">
            <h4>文档分段</h4>
            <div class="segment-stats">
              <span class="segment-count">共 {{ segments.length }} 个分段</span>
            </div>
          </div>
          <div class="segments-list">
            <div v-if="loading" class="loading-state">
              <i class="fas fa-spinner fa-spin"></i>
              <span>正在加载分段数据...</span>
            </div>
            <div v-else-if="segments.length === 0" class="empty-state">
              <i class="fas fa-file-alt"></i>
              <span>暂无分段数据</span>
            </div>
            <div v-else class="segment-items">
              <div
                v-for="(segment, index) in segments"
                :key="segment.id"
                class="segment-item"
                :class="{ 'active': selectedSegmentId === segment.id }"
                @click="selectSegment(segment)"
                @mouseenter="handleSegmentHover(segment)"
                @mouseleave="handleSegmentLeave"
              >
                <div class="segment-header">
                  <span class="segment-index">#{{ index + 1 }}</span>
                  <span v-if="segment.score" class="segment-score">
                    {{ Math.round(segment.score * 100) }}%
                  </span>
                </div>
                <div class="segment-content">
                  {{ segment.content }}
                </div>
                <div v-if="segment.metadata" class="segment-metadata">
                  <div v-for="(value, key) in segment.metadata" :key="key" class="metadata-item">
                    <span class="metadata-key">{{ key }}:</span>
                    <span class="metadata-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import FileViewer from '@/views/rag/viewers/FileViewer.vue'
import { formatFileSize } from '@/utils/fileUtils'
import { RagAPI } from '@/api/rag'

// Props
interface Props {
  isOpen: boolean
  currentFile: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  configUpdated: [fileId: string, config: any]
}>()

// 响应式数据
const loading = ref(false)
const segments = ref<any[]>([])
const selectedSegmentId = ref<string | null>(null)
const onlyOfficeUrl = ref<string>('')

// 计算属性
const isOfficeDocument = computed(() => {
  if (!props.currentFile?.docType) return false
  const officeTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
  return officeTypes.includes(props.currentFile.docType.toLowerCase())
})

// 方法
const handleClose = () => {
  emit('close')
}

const handleOverlayClick = () => {
  handleClose()
}

const selectSegment = (segment: any) => {
  selectedSegmentId.value = segment.id
}

const handleSegmentHover = (segment: any) => {
  // 可以在这里实现分段高亮等功能
  console.log('Segment hover:', segment)
}

const handleSegmentLeave = () => {
  // 取消分段高亮
  console.log('Segment leave')
}

const toggleFullscreen = () => {
  // 实现全屏功能
  console.log('Toggle fullscreen')
}

const downloadFile = () => {
  // 实现文件下载功能
  if (props.currentFile?.minioUrl) {
    window.open(props.currentFile.minioUrl, '_blank')
  }
}

// 加载文档分段数据
const loadSegments = async () => {
  if (!props.currentFile?.documentId) {
    console.warn('No document ID provided')
    return
  }

  loading.value = true
  try {
    // 调用API获取文档分段数据
    const response = await RagAPI.getDocumentSegments(props.currentFile.documentId)
    if (response.success && response.data) {
      segments.value = response.data
    } else {
      // 如果没有分段数据，生成模拟数据用于演示
      segments.value = generateMockSegments()
    }
  } catch (error) {
    console.error('加载分段数据失败:', error)
    // 生成模拟数据
    segments.value = generateMockSegments()
  } finally {
    loading.value = false
  }
}

// 生成模拟分段数据
const generateMockSegments = () => {
  const mockSegments = []
  const segmentCount = Math.floor(Math.random() * 8) + 3 // 3-10个分段

  for (let i = 0; i < segmentCount; i++) {
    mockSegments.push({
      id: `segment_${i + 1}`,
      content: `这是第 ${i + 1} 个分段的内容。${generateMockContent(i)}`,
      metadata: {
        page: Math.floor(i / 3) + 1,
        position: i % 3,
        startRow: i * 2 + 1,
        endRow: i * 2 + 2,
        source: props.currentFile?.name || 'unknown',
        timestamp: new Date().toISOString()
      },
      score: Math.random() * 0.3 + 0.7 // 0.7-1.0 的相似度分数
    })
  }

  return mockSegments
}

// 生成模拟内容
const generateMockContent = (index: number) => {
  const contents = [
    '本文档介绍了系统的基本功能和使用方法，包括用户管理、权限控制、数据处理等核心模块的详细说明。',
    '在实际应用中，用户需要根据具体的业务需求进行相应的配置和调整，以确保系统能够正常运行。',
    '系统支持多种数据格式的导入和导出，包括Excel、CSV、JSON等常见格式，方便用户进行数据迁移。',
    '为了保证数据安全，系统采用了多层次的安全防护机制，包括数据加密、访问控制、审计日志等。',
    '用户界面设计简洁直观，支持响应式布局，能够在不同设备上提供良好的用户体验。',
    '系统提供了丰富的API接口，支持与第三方系统的集成，满足企业级应用的需求。',
    '定期的系统维护和更新是确保系统稳定运行的重要保障，建议用户制定相应的维护计划。',
    '技术支持团队提供7x24小时的服务，确保用户在使用过程中遇到的问题能够及时得到解决。'
  ]

  return contents[index % contents.length]
}

// 加载 OnlyOffice 预览URL
const loadOnlyOfficeUrl = async () => {
  if (!isOfficeDocument.value || !props.currentFile?.documentId) {
    return
  }

  try {
    // 调用API获取OnlyOffice预览URL
    const response = await RagAPI.getDocumentPreviewUrl(props.currentFile.documentId)
    if (response.success && response.data) {
      onlyOfficeUrl.value = response.data
    }
  } catch (error) {
    console.error('加载OnlyOffice预览URL失败:', error)
  }
}

// 监听文件变化
watch(() => props.currentFile, (newFile) => {
  if (newFile && props.isOpen) {
    loadSegments()
    if (isOfficeDocument.value) {
      loadOnlyOfficeUrl()
    }
  }
}, { immediate: true })

// 监听打开状态变化
watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.currentFile) {
    loadSegments()
    if (isOfficeDocument.value) {
      loadOnlyOfficeUrl()
    }
  } else {
    // 清理数据
    segments.value = []
    selectedSegmentId.value = null
    onlyOfficeUrl.value = ''
  }
})

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.isOpen) {
    handleClose()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.file-segment-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.file-segment-preview-container {
  background: white;
  border-radius: 12px;
  width: 95vw;
  height: 90vh;
  max-width: 1400px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* 头部样式 */
.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.header-left {
  flex: 1;
}

.file-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.file-size, .file-type, .word-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 主体内容样式 */
.preview-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.document-preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e5e7eb;
}

.segments-panel {
  width: 400px;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.control-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.segment-stats {
  font-size: 14px;
  color: #6b7280;
}

/* 文档查看器样式 */
.document-viewer {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.onlyoffice-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.onlyoffice-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  gap: 12px;
}

.loading-placeholder i {
  font-size: 24px;
}

.file-viewer-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* 分段列表样式 */
.segments-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  gap: 12px;
}

.loading-state i {
  font-size: 20px;
}

.empty-state i {
  font-size: 24px;
}

.segment-items {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.segment-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.segment-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.segment-item.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.segment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.segment-index {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 4px;
}

.segment-score {
  font-size: 12px;
  font-weight: 600;
  color: #059669;
  background: #d1fae5;
  padding: 2px 8px;
  border-radius: 4px;
}

.segment-content {
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.segment-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.metadata-item {
  font-size: 12px;
  background: #f9fafb;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.metadata-key {
  font-weight: 600;
  color: #6b7280;
}

.metadata-value {
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .segments-panel {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .file-segment-preview-container {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }

  .preview-content {
    flex-direction: column;
  }

  .document-preview-panel {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
    height: 60%;
  }

  .segments-panel {
    width: 100%;
    height: 40%;
  }
}
</style>