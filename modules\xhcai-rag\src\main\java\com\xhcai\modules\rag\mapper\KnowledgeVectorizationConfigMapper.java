package com.xhcai.modules.rag.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig;

/**
 * 知识库向量化配置Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeVectorizationConfigMapper extends BaseMapper<KnowledgeVectorizationConfig> {

    /**
     * 根据租户ID获取配置
     */
    @Results(id = "KnowledgeVectorizationConfigResultMap", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "index_mode", property = "indexMode"),
        @Result(column = "embedding_model", property = "embeddingModel"),
        @Result(column = "retrieval_settings", property = "retrievalSettings",
                typeHandler = com.xhcai.modules.rag.handler.RetrievalSettingsTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "remark", property = "remark"),
        @Result(column = "deleted", property = "deleted"),
        @Result(column = "created_by", property = "createdBy"),
        @Result(column = "created_at", property = "createdAt"),
        @Result(column = "updated_by", property = "updatedBy"),
        @Result(column = "updated_at", property = "updatedAt")
    })
    @Select("SELECT * FROM datasets_vectorization_config WHERE tenant_id = #{tenantId} AND deleted = 0 LIMIT 1")
    KnowledgeVectorizationConfig getByTenantId(String tenantId);
}
