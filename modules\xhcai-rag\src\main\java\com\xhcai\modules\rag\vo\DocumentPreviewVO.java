package com.xhcai.modules.rag.vo;

import com.xhcai.common.api.dto.PageQueryDTO;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "文档预览VO")
@Data
public class DocumentPreviewVO extends PageQueryDTO {
    /**
     * 文档ID
     */
    @Schema(description = "文档ID", example = "document123")
    private String documentId;

    /**
     * 文档类型
     */
    @Schema(description = "文档类型", example = "doc, docx, pdf, txt, md, rtf, html, csv, xlsx")
    private String docType;

    /**
     * 获取预览URL
     */
    @Schema(description = "预览URL", example = "https://example.com/preview.png")
    private String previewUrl;

    /**
     * 清洗配置
     * {
     *     "deleteSymbol": false,
     *     "normalizeText": false,
     *     "filterKeywords": "",
     *     "removeEmptyLines": true,
     *     "deleteInlineMedia": false,
     *     "removeExtraSpaces": true,
     *     "removeSpecialChars": false
     * }
     */
    @Schema(description = "清洗 配置")
    private CleaningConfig cleanConfig;

    /**
     * 分段配置
     * {
     *     "type": "directory",
     *      "constantLength": {
     *         "maxLen": "3",
     *         "delimiter": "\n\n",
     *         "overlapLen": 50
     *     },
     *     "natural": {
     *         "segments": "3"
     *     },
     *     "delimiter": {
     *         "delimiter": "\n\n"
     *     },
     *     "directory": {
     *         "level": "2"
     *     }
     * }
     */
    @Schema(description = "分段 配置")
    private SegmentConfig segmentConfig;
}
