<template>
  <BaseFileViewer :file="file" @download="handleDownload" @refresh="handleRefresh">
    <template #content>
      <div class="word-viewer">
        <div class="word-toolbar">
          <div class="toolbar-left">
            <button class="tool-btn" @click="zoomOut" :disabled="scale <= 0.5">
              <i class="fas fa-search-minus"></i>
            </button>
            <span class="zoom-info">{{ Math.round(scale * 100) }}%</span>
            <button class="tool-btn" @click="zoomIn" :disabled="scale >= 2">
              <i class="fas fa-search-plus"></i>
            </button>
            <button class="tool-btn" @click="resetZoom">
              <i class="fas fa-expand-arrows-alt"></i>
            </button>
          </div>
          <div class="toolbar-center">
            <button class="tool-btn" @click="prevPage" :disabled="currentPage <= 1">
              <i class="fas fa-chevron-left"></i>
            </button>
            <span class="page-info">
              第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
            </span>
            <button class="tool-btn" @click="nextPage" :disabled="currentPage >= totalPages">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
          <div class="toolbar-right">
            <button class="tool-btn" :class="{ active: showOutline }" @click="toggleOutline">
              <i class="fas fa-list"></i>
              大纲
            </button>
            <button class="tool-btn" @click="toggleFullscreen">
              <i class="fas fa-expand"></i>
            </button>
            <button class="tool-btn" @click="handleDownload" title="下载文件">
              <i class="fas fa-download"></i>
            </button>
            <button class="tool-btn" @click="handleRefresh" title="刷新内容">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>
        
        <div class="word-content" ref="wordContainer">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载 Word 文档...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="loadWord">重新加载</button>
          </div>
          
          <div v-else class="word-display">
            <div class="word-container" :style="{ transform: `scale(${scale})` }">
              <div class="word-page" v-for="page in visiblePages" :key="page">
                <div class="page-header">
                  <div class="page-number">第 {{ page }} 页</div>
                </div>
                <div class="page-content">
                  <div class="document-title">{{ file?.name || '文档标题' }}</div>

                  <div class="document-body">
                    <!-- 动态渲染分段内容 -->
                    <div
                      v-for="segment in getPageSegments(page)"
                      :key="segment.id"
                      class="content-section segment-container"
                      :class="{
                        'segment-highlighted': hoveredSegmentId === segment.id,
                        'segment-selected': selectedSegmentId === segment.id
                      }"
                      @mouseenter="handleSegmentHover(segment.id)"
                      @mouseleave="handleSegmentLeave"
                      @click="selectSegment(segment)"
                    >
                      <div class="segment-content">
                        <div class="segment-text" v-html="formatSegmentContent(segment.content)"></div>
                        <div class="segment-meta" v-if="segment.metadata">
                          <span class="segment-id">分段 #{{ getSegmentIndex(segment.id) }}</span>
                          <span class="char-count">{{ segment.content.length }} 字符</span>
                        </div>
                      </div>
                    </div>

                    <!-- 如果没有分段数据，显示默认内容 -->
                    <div v-if="!segments || segments.length === 0" class="default-content">
                      <div class="content-section">
                        <h2>文档概述</h2>
                        <p>这是一个 Word 文档查看器的模拟显示。在实际应用中，这里会显示真实的 Word 文档内容。</p>
                      </div>

                      <div class="content-section">
                        <h3>主要功能</h3>
                        <ul>
                          <li>支持 .doc 和 .docx 格式</li>
                          <li>页面导航和缩放控制</li>
                          <li>文档大纲显示</li>
                          <li>全屏查看模式</li>
                        </ul>
                      </div>

                      <div class="content-section">
                        <h3>技术实现</h3>
                        <p>可以使用以下技术来实现真实的 Word 文档渲染：</p>
                        <ul>
                          <li><strong>mammoth.js</strong> - 将 .docx 转换为 HTML</li>
                          <li><strong>Office Online</strong> - 微软官方在线查看器</li>
                          <li><strong>LibreOffice Online</strong> - 开源文档查看器</li>
                        </ul>
                      </div>
                    </div>

                    <div class="page-break"></div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 大纲侧边栏 -->
            <div class="outline-panel" v-if="showOutline">
              <div class="outline-header">
                <h4>文档大纲</h4>
                <button class="close-outline" @click="toggleOutline">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="outline-content">
                <div class="outline-item" @click="goToSection('overview')">
                  <i class="fas fa-file-alt"></i>
                  文档概述
                </div>
                <div class="outline-item" @click="goToSection('features')">
                  <i class="fas fa-list"></i>
                  主要功能
                </div>
                <div class="outline-item" @click="goToSection('tech')">
                  <i class="fas fa-cog"></i>
                  技术实现
                </div>
                <div class="outline-item" @click="goToSection('table')">
                  <i class="fas fa-table"></i>
                  示例表格
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseFileViewer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import BaseFileViewer from './BaseFileViewer.vue'

// Props
interface Props {
  file: any
  segments?: any[]
  selectedSegmentId?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  download: [file: any]
  segmentHover: [segmentInfo: any]
  segmentLeave: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const currentPage = ref(1)
const totalPages = ref(3)
const scale = ref(1)
const showOutline = ref(false)
const wordContainer = ref<HTMLElement | null>(null)
const hoveredSegmentId = ref<string | null>(null)

// 计算属性
const visiblePages = computed(() => {
  return [currentPage.value]
})

// 方法
const handleDownload = (file: any) => {
  emit('download', file)
}

const handleRefresh = () => {
  loadWord()
}

const loadWord = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 模拟 Word 文档加载
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    // 模拟文档数据
    totalPages.value = 3
    currentPage.value = 1
    
  } catch (err) {
    error.value = '加载 Word 文档失败'
  } finally {
    loading.value = false
  }
}

const zoomIn = () => {
  if (scale.value < 2) {
    scale.value = Math.min(2, scale.value + 0.25)
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(0.5, scale.value - 0.25)
  }
}

const resetZoom = () => {
  scale.value = 1
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const toggleOutline = () => {
  showOutline.value = !showOutline.value
}

const toggleFullscreen = () => {
  if (wordContainer.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      wordContainer.value.requestFullscreen()
    }
  }
}

const goToSection = (section: string) => {
  // 模拟跳转到指定章节
  console.log('跳转到章节:', section)
}

// 生命周期
onMounted(() => {
  loadWord()
})

// 监听选中分段变化
watch(() => props.selectedSegmentId, (newSegmentId) => {
  if (newSegmentId && props.segments) {
    const segment = props.segments.find(seg => seg.id === newSegmentId)
    if (segment && segment.metadata && segment.metadata.page) {
      // 跳转到指定页面
      goToPage(segment.metadata.page)
    }
  }
})

// 跳转到指定页面
const goToPage = (pageNumber: number) => {
  if (pageNumber >= 1 && pageNumber <= totalPages.value) {
    currentPage.value = pageNumber
  }
}

// 获取指定页面的分段
const getPageSegments = (pageNum: number) => {
  if (!props.segments) return []
  return props.segments.filter(segment =>
    segment.metadata && segment.metadata.page === pageNum
  )
}

// 格式化分段内容
const formatSegmentContent = (content: string) => {
  // 简单的文本格式化，将换行转换为段落
  return content
    .split('\n')
    .filter(line => line.trim())
    .map(line => `<p>${line.trim()}</p>`)
    .join('')
}

// 获取分段索引
const getSegmentIndex = (segmentId: string) => {
  if (!props.segments) return 0
  return props.segments.findIndex(seg => seg.id === segmentId) + 1
}

// 选择分段
const selectSegment = (segment: any) => {
  // 这里可以触发事件通知父组件
  console.log('Selected segment:', segment)
}

// 检查分段是否在指定页面
const isSegmentInPage = (segmentId: string, pageNum: number) => {
  if (!props.segments) return false
  const segment = props.segments.find(seg => seg.id === segmentId)
  return segment && segment.metadata && segment.metadata.page === pageNum
}

// 处理分段悬停
const handleSegmentHover = (segmentId: string) => {
  hoveredSegmentId.value = segmentId
  if (!props.segments) return
  const segment = props.segments.find(seg => seg.id === segmentId)
  if (segment) {
    const segmentInfo = {
      id: segment.id,
      charCount: segment.content?.length || 0,
      keywords: extractKeywords(segment.content || ''),
      content: segment.content?.substring(0, 100) + (segment.content?.length > 100 ? '...' : '')
    }
    emit('segmentHover', segmentInfo)
  }
}

// 处理分段离开
const handleSegmentLeave = () => {
  hoveredSegmentId.value = null
  emit('segmentLeave')
}

// 简单的关键词提取
const extractKeywords = (text: string): string[] => {
  const words = text.match(/[\u4e00-\u9fa5]{2,}|[a-zA-Z]{3,}/g) || []
  const wordCount: { [key: string]: number } = {}

  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1
  })

  return Object.entries(wordCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([word]) => word)
}
</script>

<style scoped>
.word-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.word-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 16px;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: white;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  font-size: 12px;
}

.tool-btn:hover:not(:disabled) {
  background: #f1f5f9;
  color: #334155;
}

.tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tool-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.zoom-info,
.page-info {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  white-space: nowrap;
}

.word-content {
  flex: 1;
  overflow: auto;
  background: #f1f5f9;
  position: relative;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.word-display {
  display: flex;
  height: 100%;
  position: relative;
}

.word-container {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 20px;
  transform-origin: top center;
  transition: transform 0.2s ease;
}

.word-page {
  width: 210mm;
  min-height: 297mm;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: hidden;
}

.page-header {
  padding: 10px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  text-align: center;
}

.page-number {
  font-size: 12px;
  color: #64748b;
}

.page-content {
  padding: 40px;
  line-height: 1.6;
  color: #1e293b;
}

.document-title {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
  color: #1e293b;
}

.document-body h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 24px 0 12px 0;
  color: #1e293b;
}

.document-body h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 10px 0;
  color: #374151;
}

.document-body h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 16px 0 8px 0;
  color: #374151;
}

.document-body p {
  margin: 12px 0;
  text-align: justify;
}

.document-body ul {
  margin: 12px 0;
  padding-left: 20px;
}

.document-body li {
  margin: 6px 0;
}

.sample-table {
  margin: 20px 0;
}

.sample-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.sample-table th,
.sample-table td {
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  text-align: left;
}

.sample-table th {
  background: #f8fafc;
  font-weight: 600;
}

.page-break {
  height: 1px;
  background: #e2e8f0;
  margin: 30px 0;
}

.outline-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 250px;
  height: 100%;
  background: white;
  border-left: 1px solid #e2e8f0;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.outline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.outline-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.close-outline {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-outline:hover {
  background: #f1f5f9;
  color: #334155;
}

/* 分段相关样式 */
.content-section {
  padding: 12px;
  margin: 8px 0;
  border: 2px dashed transparent;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.segment-container {
  border: 2px dashed #e2e8f0;
  background: rgba(248, 250, 252, 0.5);
  position: relative;
}

.segment-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed #cbd5e1;
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.segment-container.segment-highlighted {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.segment-container.segment-highlighted::before {
  border-color: #3b82f6;
  opacity: 1;
}

.segment-container.segment-selected {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
  border-width: 3px;
}

.segment-container.segment-selected::before {
  border-color: #10b981;
  border-width: 3px;
  opacity: 1;
}

.segment-content {
  position: relative;
  z-index: 1;
}

.segment-text {
  line-height: 1.6;
  color: #1e293b;
}

.segment-text p {
  margin: 8px 0;
}

.segment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
  font-size: 12px;
  color: #64748b;
}

.segment-id {
  font-weight: 500;
  color: #3b82f6;
}

.char-count {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
}

.default-content .content-section {
  border: none;
  background: transparent;
  cursor: default;
}

.default-content .content-section:hover {
  border: none;
  background: transparent;
  box-shadow: none;
}

.outline-content {
  padding: 16px;
}

.outline-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
}

.outline-item:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.outline-item i {
  font-size: 12px;
  color: #64748b;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
