<template>
  <div class="audit-monitor space-y-6">
    <!-- 审计概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-robot text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">活跃智能体数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ auditStats.activeAgents }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-comments text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总会话数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ auditStats.totalSessions.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-envelope text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总消息数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ auditStats.totalMessages.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-clock text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日活跃会话</p>
            <p class="text-2xl font-semibold text-gray-900">{{ auditStats.todayActiveSessions.toLocaleString() }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体日志审计表格 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">智能体日志审计</h3>
          <div class="flex items-center gap-4">
            <select v-model="auditUnitFilter" class="form-select text-sm">
              <option value="">全部单位</option>
              <option value="技术部">技术部</option>
              <option value="研发部">研发部</option>
              <option value="产品部">产品部</option>
              <option value="运营部">运营部</option>
              <option value="数据部">数据部</option>
            </select>
            <input
              v-model="auditSearchQuery"
              type="text"
              placeholder="搜索智能体..."
              class="form-input text-sm w-64"
            />
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">智能体信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最新请求时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">会话数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">消息数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="agent in paginatedAuditAgents" :key="agent.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center">
                      <i class="fas fa-robot text-white"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ agent.name }}</div>
                    <div class="text-sm text-gray-500">{{ agent.unit }} · {{ agent.creator }}</div>
                    <div class="text-xs text-gray-400">创建时间: {{ formatDate(agent.createdAt) }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDateTime(agent.lastRequestTime) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ agent.sessionCount.toLocaleString() }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ agent.messageCount.toLocaleString() }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  @click="viewAgentSessions(agent.id, agent.name)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  查看会话
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 审计分页 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredAuditAgents.length) }} 条，
            共 {{ filteredAuditAgents.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="text-sm text-gray-700">
              第 {{ currentPage }} / {{ totalPages }} 页
            </span>
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  refreshTrigger: 0
})

// Emits
const emit = defineEmits<{
  openSessionPanel: [agentId: number, agentName: string]
}>()

// 搜索和过滤
const auditSearchQuery = ref('')
const auditUnitFilter = ref('')

// 分页配置
const pageSize = ref(10)
const currentPage = ref(1)

// 审计统计数据
const auditStats = ref({
  activeAgents: 156,
  totalSessions: 25680,
  totalMessages: 89420,
  todayActiveSessions: 1240
})

// 审计数据
const auditAgents = ref([
  {
    id: 1,
    name: '智能客服助手',
    unit: '技术部',
    creator: '张三',
    createdAt: '2024-01-15',
    lastRequestTime: '2024-06-30 14:25:30',
    sessionCount: 1250,
    messageCount: 8920
  },
  {
    id: 2,
    name: '代码生成器',
    unit: '研发部',
    creator: '李四',
    createdAt: '2024-02-10',
    lastRequestTime: '2024-06-30 13:45:15',
    sessionCount: 890,
    messageCount: 5670
  },
  {
    id: 3,
    name: '文档摘要生成',
    unit: '产品部',
    creator: '王五',
    createdAt: '2024-03-05',
    lastRequestTime: '2024-06-30 12:30:45',
    sessionCount: 567,
    messageCount: 3450
  },
  {
    id: 4,
    name: '业务流程助手',
    unit: '运营部',
    creator: '赵六',
    createdAt: '2024-04-12',
    lastRequestTime: '2024-06-30 11:15:20',
    sessionCount: 345,
    messageCount: 2340
  },
  {
    id: 5,
    name: '数据处理工作流',
    unit: '数据部',
    creator: '孙七',
    createdAt: '2024-05-08',
    lastRequestTime: '2024-06-30 10:45:10',
    sessionCount: 234,
    messageCount: 1560
  }
])

// 审计相关计算属性
const filteredAuditAgents = computed(() => {
  let filtered = auditAgents.value

  if (auditUnitFilter.value) {
    filtered = filtered.filter(agent => agent.unit === auditUnitFilter.value)
  }

  if (auditSearchQuery.value) {
    const query = auditSearchQuery.value.toLowerCase()
    filtered = filtered.filter(agent =>
      agent.name.toLowerCase().includes(query) ||
      agent.unit.toLowerCase().includes(query) ||
      agent.creator.toLowerCase().includes(query)
    )
  }

  return filtered
})

const paginatedAuditAgents = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredAuditAgents.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredAuditAgents.value.length / pageSize.value)
})

// 方法
const updateAuditStats = () => {
  // 更新审计统计数据
  auditStats.value.activeAgents = auditAgents.value.length
  auditStats.value.totalSessions = auditAgents.value.reduce((sum, agent) => sum + agent.sessionCount, 0)
  auditStats.value.totalMessages = auditAgents.value.reduce((sum, agent) => sum + agent.messageCount, 0)

  // 模拟今日活跃会话数更新
  auditStats.value.todayActiveSessions = Math.floor(Math.random() * 500) + 800

  // 更新智能体的最新请求时间（模拟实时更新）
  auditAgents.value.forEach(agent => {
    // 随机更新一些智能体的最新请求时间
    if (Math.random() > 0.7) {
      const now = new Date()
      const randomMinutes = Math.floor(Math.random() * 60)
      now.setMinutes(now.getMinutes() - randomMinutes)
      agent.lastRequestTime = now.toISOString().slice(0, 19).replace('T', ' ')
    }
  })
}

// 审计相关方法
const viewAgentSessions = (agentId: number, agentName: string) => {
  emit('openSessionPanel', agentId, agentName)
}

// 分页方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const resetPagination = () => {
  currentPage.value = 1
}

// 时间格式化方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听搜索和过滤条件变化，重置分页
watch([auditSearchQuery, auditUnitFilter], () => {
  resetPagination()
})

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  updateAuditStats()
})

onMounted(() => {
  updateAuditStats()
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-input, .form-select {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  background-color: #f8fafc;
  transform: scale(1.01);
}

/* 渐变背景 */
.from-blue-400 {
  --tw-gradient-from: #60a5fa;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0));
}

.to-blue-600 {
  --tw-gradient-to: #2563eb;
}
</style>