package com.yyzs.agent.service.impl;

import com.yyzs.agent.service.ComponentConfigTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 组件配置模板服务实现类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class ComponentConfigTemplateServiceImpl implements ComponentConfigTemplateService {

    private static final String[] SUPPORTED_COMPONENTS = {
        "elasticsearch", "logstash", "filebeat", "heartbeat", "metricbeat",
        "packetbeat", "winlogbeat", "auditbeat", "kafka"
    };

    private static final Pattern TEMPLATE_VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    @Override
    public String getDefaultConfigTemplate(String componentType) {
        try {
            String templatePath = String.format("templates/config/%s", getConfigFileName(componentType));
            ClassPathResource resource = new ClassPathResource(templatePath);

            if (resource.exists()) {
                return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            } else {
                log.warn("配置模板不存在: {}", templatePath);
                return generateBasicTemplate(componentType);
            }
        } catch (IOException e) {
            log.error("读取配置模板失败: " + componentType, e);
            return generateBasicTemplate(componentType);
        }
    }

    @Override
    public String generateConfigContent(String componentType, Map<String, Object> userConfig) {
        String template = getDefaultConfigTemplate(componentType);
        Map<String, Object> defaultConfig = getDefaultConfigParams(componentType);
        Map<String, Object> finalConfig = mergeConfigs(defaultConfig, userConfig);

        return replaceTemplateVariables(template, finalConfig);
    }

    @Override
    public boolean validateConfig(String componentType, Map<String, Object> config) {
        if (!isComponentTypeSupported(componentType)) {
            return false;
        }

        switch (componentType.toLowerCase()) {
            case "elasticsearch":
                return validateElasticsearchConfig(config);
            case "logstash":
                return validateLogstashConfig(config);
            case "filebeat":
            case "heartbeat":
            case "metricbeat":
            case "packetbeat":
            case "winlogbeat":
            case "auditbeat":
                return validateBeatConfig(config);
            case "kafka":
                return validateKafkaConfig(config);
            default:
                return true;
        }
    }

    @Override
    public Map<String, Object> getConfigSchema(String componentType) {
        Map<String, Object> schema = new HashMap<>();

        switch (componentType.toLowerCase()) {
            case "elasticsearch":
                schema = getElasticsearchConfigSchema();
                break;
            case "logstash":
                schema = getLogstashConfigSchema();
                break;
            case "filebeat":
                schema = getFilebeatConfigSchema();
                break;
            case "heartbeat":
                schema = getHeartbeatConfigSchema();
                break;
            case "metricbeat":
                schema = getMetricbeatConfigSchema();
                break;
            case "kafka":
                schema = getKafkaConfigSchema();
                break;
            default:
                schema = getGenericConfigSchema();
        }

        return schema;
    }

    @Override
    public Map<String, Object> getDefaultConfigParams(String componentType) {
        Map<String, Object> defaults = new HashMap<>();

        switch (componentType.toLowerCase()) {
            case "elasticsearch":
                defaults.put("CLUSTER_NAME", "elasticsearch");
                defaults.put("NODE_NAME", "node-1");
                defaults.put("DATA_PATH", "/var/lib/elasticsearch");
                defaults.put("LOGS_PATH", "/var/log/elasticsearch");
                defaults.put("NETWORK_HOST", "0.0.0.0");
                defaults.put("HTTP_PORT", "9200");
                defaults.put("SECURITY_ENABLED", "false");
                defaults.put("MONITORING_ENABLED", "true");
                defaults.put("HEAP_SIZE", "1g");
                break;

            case "logstash":
                defaults.put("BEATS_PORT", "5044");
                defaults.put("BEATS_HOST", "0.0.0.0");
                defaults.put("ELASTICSEARCH_HOSTS", "\"localhost:9200\"");
                defaults.put("INDEX_PATTERN", "logstash-%{+YYYY.MM.dd}");
                defaults.put("PIPELINE_WORKERS", "2");
                defaults.put("PIPELINE_BATCH_SIZE", "125");
                defaults.put("HTTP_API_HOST", "127.0.0.1");
                defaults.put("HTTP_API_PORT", "9600");
                defaults.put("LOG_LEVEL", "info");
                defaults.put("LOG_PATH", "/var/log/logstash");
                break;

            case "filebeat":
                defaults.put("INPUT_ID", "default-filestream");
                defaults.put("INPUT_ENABLED", "true");
                defaults.put("LOG_PATHS", "/var/log/*.log");
                defaults.put("LOG_TYPE", "application");
                defaults.put("ENVIRONMENT", "production");
                defaults.put("SHIPPER_NAME", "filebeat");
                defaults.put("ELASTICSEARCH_HOSTS", "\"localhost:9200\"");
                defaults.put("ELASTICSEARCH_USERNAME", "elastic");
                defaults.put("ELASTICSEARCH_PASSWORD", "changeme");
                defaults.put("INDEX_NAME", "filebeat-%{+yyyy.MM.dd}");
                defaults.put("LOG_LEVEL", "info");
                break;

            case "heartbeat":
                defaults.put("HTTP_MONITOR_ID", "http-monitor");
                defaults.put("HTTP_MONITOR_NAME", "HTTP Monitor");
                defaults.put("HTTP_URLS", "\"http://localhost:80/health\"");
                defaults.put("HTTP_SCHEDULE", "@every 10s");
                defaults.put("HTTP_TIMEOUT", "16s");
                defaults.put("SHIPPER_NAME", "heartbeat");
                defaults.put("ELASTICSEARCH_HOSTS", "\"localhost:9200\"");
                defaults.put("INDEX_NAME", "heartbeat-%{+yyyy.MM.dd}");
                break;

            case "metricbeat":
                defaults.put("SHIPPER_NAME", "metricbeat");
                defaults.put("ELASTICSEARCH_HOSTS", "\"localhost:9200\"");
                defaults.put("INDEX_NAME", "metricbeat-%{+yyyy.MM.dd}");
                defaults.put("MODULES_RELOAD", "true");
                defaults.put("MODULES_RELOAD_PERIOD", "10s");
                defaults.put("LOG_LEVEL", "info");
                defaults.put("HTTP_ENABLED", "false");
                defaults.put("HTTP_PORT", "5066");
                break;

            case "kafka":
                defaults.put("BROKER_ID", "0");
                defaults.put("LISTENERS", "PLAINTEXT://localhost:9092");
                defaults.put("ADVERTISED_LISTENERS", "PLAINTEXT://localhost:9092");
                defaults.put("NUM_NETWORK_THREADS", "3");
                defaults.put("NUM_IO_THREADS", "8");
                defaults.put("LOG_DIRS", "/var/kafka-logs");
                defaults.put("NUM_PARTITIONS", "1");
                defaults.put("ZOOKEEPER_CONNECT", "localhost:2181");
                defaults.put("LOG_RETENTION_HOURS", "168");
                break;

            default:
                // 通用默认配置
                defaults.put("LOG_LEVEL", "info");
                defaults.put("ENVIRONMENT", "production");
        }

        return defaults;
    }

    @Override
    public Map<String, Object> parseConfigContent(String componentType, String configContent) {
        Map<String, Object> config = new HashMap<>();

        if (!StringUtils.hasText(configContent)) {
            return config;
        }

        String[] lines = configContent.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty() || line.startsWith("#")) {
                continue;
            }

            String[] parts;
            if ("kafka".equals(componentType)) {
                parts = line.split("=", 2);
            } else {
                parts = line.split(":", 2);
            }

            if (parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                config.put(key, value);
            }
        }

        return config;
    }

    @Override
    public Map<String, Object> mergeConfigs(Map<String, Object> defaultConfig, Map<String, Object> userConfig) {
        Map<String, Object> merged = new HashMap<>(defaultConfig);
        if (userConfig != null) {
            merged.putAll(userConfig);
        }
        return merged;
    }

    @Override
    public String[] getSupportedComponentTypes() {
        return SUPPORTED_COMPONENTS.clone();
    }

    @Override
    public boolean isComponentTypeSupported(String componentType) {
        return Arrays.asList(SUPPORTED_COMPONENTS).contains(componentType.toLowerCase());
    }

    @Override
    public String getConfigFileName(String componentType) {
        switch (componentType.toLowerCase()) {
            case "elasticsearch":
                return "elasticsearch.yml";
            case "logstash":
                return "logstash.conf";
            case "kafka":
                return "server.properties";
            case "filebeat":
                return "filebeat.yml";
            case "heartbeat":
                return "heartbeat.yml";
            case "metricbeat":
                return "metricbeat.yml";
            case "packetbeat":
                return "packetbeat.yml";
            case "winlogbeat":
                return "winlogbeat.yml";
            case "auditbeat":
                return "auditbeat.yml";
            default:
                return componentType.toLowerCase() + ".yml";
        }
    }

    @Override
    public String getStartupScriptTemplate(String componentType) {
        // 返回启动脚本模板
        return generateStartupScriptTemplate(componentType);
    }

    @Override
    public String generateStartupScript(String componentType, String installPath, Map<String, Object> config) {
        String template = getStartupScriptTemplate(componentType);
        Map<String, Object> scriptVars = new HashMap<>();
        scriptVars.put("INSTALL_PATH", installPath);
        scriptVars.put("COMPONENT_TYPE", componentType);
        scriptVars.put("CONFIG_FILE", installPath + "/config/" + getConfigFileName(componentType));

        // 添加组件特定的启动参数
        switch (componentType.toLowerCase()) {
            case "elasticsearch":
                scriptVars.put("JAVA_OPTS", config.getOrDefault("JAVA_OPTS", "-Xms1g -Xmx1g"));
                break;
            case "logstash":
                scriptVars.put("LS_JAVA_OPTS", config.getOrDefault("LS_JAVA_OPTS", "-Xms1g -Xmx1g"));
                break;
        }

        return replaceTemplateVariables(template, scriptVars);
    }

    /**
     * 替换模板变量
     */
    private String replaceTemplateVariables(String template, Map<String, Object> variables) {
        if (template == null || variables == null) {
            return template;
        }

        Matcher matcher = TEMPLATE_VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String varExpression = matcher.group(1);
            String replacement = resolveVariable(varExpression, variables);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 解析变量表达式
     */
    private String resolveVariable(String varExpression, Map<String, Object> variables) {
        // 支持默认值语法: ${VAR_NAME:default_value}
        String[] parts = varExpression.split(":", 2);
        String varName = parts[0].trim();
        String defaultValue = parts.length > 1 ? parts[1].trim() : "";

        Object value = variables.get(varName);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 生成基础配置模板
     */
    private String generateBasicTemplate(String componentType) {
        StringBuilder template = new StringBuilder();
        template.append("# ").append(componentType.toUpperCase()).append(" Configuration\n");
        template.append("# Auto-generated basic template\n\n");

        Map<String, Object> defaults = getDefaultConfigParams(componentType);
        for (Map.Entry<String, Object> entry : defaults.entrySet()) {
            template.append("# ").append(entry.getKey()).append("\n");
            template.append(entry.getKey().toLowerCase().replace("_", "."))
                    .append(": ${").append(entry.getKey()).append(":").append(entry.getValue()).append("}\n\n");
        }

        return template.toString();
    }

    /**
     * 验证Elasticsearch配置
     */
    private boolean validateElasticsearchConfig(Map<String, Object> config) {
        // 必需的配置项
        String[] requiredFields = {"CLUSTER_NAME", "NODE_NAME"};
        for (String field : requiredFields) {
            if (!config.containsKey(field) || !StringUtils.hasText(config.get(field).toString())) {
                log.warn("Elasticsearch配置缺少必需字段: {}", field);
                return false;
            }
        }

        // 验证端口号
        if (config.containsKey("HTTP_PORT")) {
            try {
                int port = Integer.parseInt(config.get("HTTP_PORT").toString());
                if (port < 1 || port > 65535) {
                    log.warn("Elasticsearch HTTP端口号无效: {}", port);
                    return false;
                }
            } catch (NumberFormatException e) {
                log.warn("Elasticsearch HTTP端口号格式错误: {}", config.get("HTTP_PORT"));
                return false;
            }
        }

        return true;
    }

    /**
     * 验证Logstash配置
     */
    private boolean validateLogstashConfig(Map<String, Object> config) {
        // Logstash配置相对灵活，主要验证端口
        if (config.containsKey("BEATS_PORT")) {
            try {
                int port = Integer.parseInt(config.get("BEATS_PORT").toString());
                if (port < 1 || port > 65535) {
                    log.warn("Logstash Beats端口号无效: {}", port);
                    return false;
                }
            } catch (NumberFormatException e) {
                log.warn("Logstash Beats端口号格式错误: {}", config.get("BEATS_PORT"));
                return false;
            }
        }

        return true;
    }

    /**
     * 验证Beat配置
     */
    private boolean validateBeatConfig(Map<String, Object> config) {
        // 验证Elasticsearch主机配置
        if (config.containsKey("ELASTICSEARCH_HOSTS")) {
            String hosts = config.get("ELASTICSEARCH_HOSTS").toString();
            if (!StringUtils.hasText(hosts)) {
                log.warn("Beat配置缺少Elasticsearch主机配置");
                return false;
            }
        }

        return true;
    }

    /**
     * 验证Kafka配置
     */
    private boolean validateKafkaConfig(Map<String, Object> config) {
        // 必需的配置项
        String[] requiredFields = {"BROKER_ID", "LISTENERS", "ZOOKEEPER_CONNECT"};
        for (String field : requiredFields) {
            if (!config.containsKey(field) || !StringUtils.hasText(config.get(field).toString())) {
                log.warn("Kafka配置缺少必需字段: {}", field);
                return false;
            }
        }

        // 验证Broker ID
        try {
            Integer.parseInt(config.get("BROKER_ID").toString());
        } catch (NumberFormatException e) {
            log.warn("Kafka Broker ID格式错误: {}", config.get("BROKER_ID"));
            return false;
        }

        return true;
    }

    /**
     * 获取Elasticsearch配置Schema
     */
    private Map<String, Object> getElasticsearchConfigSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("CLUSTER_NAME", createFieldSchema("string", "集群名称", true, "elasticsearch"));
        schema.put("NODE_NAME", createFieldSchema("string", "节点名称", true, "node-1"));
        schema.put("DATA_PATH", createFieldSchema("string", "数据存储路径", false, "/var/lib/elasticsearch"));
        schema.put("LOGS_PATH", createFieldSchema("string", "日志存储路径", false, "/var/log/elasticsearch"));
        schema.put("NETWORK_HOST", createFieldSchema("string", "网络绑定地址", false, "0.0.0.0"));
        schema.put("HTTP_PORT", createFieldSchema("integer", "HTTP端口", false, 9200));
        schema.put("SECURITY_ENABLED", createFieldSchema("boolean", "启用安全功能", false, false));
        schema.put("MONITORING_ENABLED", createFieldSchema("boolean", "启用监控", false, true));
        schema.put("HEAP_SIZE", createFieldSchema("string", "JVM堆内存大小", false, "1g"));
        return schema;
    }

    /**
     * 获取Logstash配置Schema
     */
    private Map<String, Object> getLogstashConfigSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("BEATS_PORT", createFieldSchema("integer", "Beats输入端口", false, 5044));
        schema.put("BEATS_HOST", createFieldSchema("string", "Beats绑定地址", false, "0.0.0.0"));
        schema.put("ELASTICSEARCH_HOSTS", createFieldSchema("string", "Elasticsearch主机列表", false, "localhost:9200"));
        schema.put("INDEX_PATTERN", createFieldSchema("string", "索引模式", false, "logstash-%{+YYYY.MM.dd}"));
        schema.put("PIPELINE_WORKERS", createFieldSchema("integer", "管道工作线程数", false, 2));
        schema.put("PIPELINE_BATCH_SIZE", createFieldSchema("integer", "管道批处理大小", false, 125));
        schema.put("HTTP_API_PORT", createFieldSchema("integer", "HTTP API端口", false, 9600));
        schema.put("LOG_LEVEL", createFieldSchema("string", "日志级别", false, "info"));
        return schema;
    }

    /**
     * 获取Filebeat配置Schema
     */
    private Map<String, Object> getFilebeatConfigSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("LOG_PATHS", createFieldSchema("string", "日志文件路径", true, "/var/log/*.log"));
        schema.put("LOG_TYPE", createFieldSchema("string", "日志类型", false, "application"));
        schema.put("ENVIRONMENT", createFieldSchema("string", "环境标识", false, "production"));
        schema.put("ELASTICSEARCH_HOSTS", createFieldSchema("string", "Elasticsearch主机", false, "localhost:9200"));
        schema.put("INDEX_NAME", createFieldSchema("string", "索引名称", false, "filebeat-%{+yyyy.MM.dd}"));
        schema.put("LOG_LEVEL", createFieldSchema("string", "日志级别", false, "info"));
        return schema;
    }

    /**
     * 获取Heartbeat配置Schema
     */
    private Map<String, Object> getHeartbeatConfigSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("HTTP_URLS", createFieldSchema("string", "HTTP监控URL", true, "http://localhost:80/health"));
        schema.put("HTTP_SCHEDULE", createFieldSchema("string", "监控频率", false, "@every 10s"));
        schema.put("HTTP_TIMEOUT", createFieldSchema("string", "超时时间", false, "16s"));
        schema.put("ELASTICSEARCH_HOSTS", createFieldSchema("string", "Elasticsearch主机", false, "localhost:9200"));
        schema.put("INDEX_NAME", createFieldSchema("string", "索引名称", false, "heartbeat-%{+yyyy.MM.dd}"));
        return schema;
    }

    /**
     * 获取Metricbeat配置Schema
     */
    private Map<String, Object> getMetricbeatConfigSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("ELASTICSEARCH_HOSTS", createFieldSchema("string", "Elasticsearch主机", false, "localhost:9200"));
        schema.put("INDEX_NAME", createFieldSchema("string", "索引名称", false, "metricbeat-%{+yyyy.MM.dd}"));
        schema.put("MODULES_RELOAD", createFieldSchema("boolean", "启用模块重载", false, true));
        schema.put("MODULES_RELOAD_PERIOD", createFieldSchema("string", "模块重载周期", false, "10s"));
        schema.put("LOG_LEVEL", createFieldSchema("string", "日志级别", false, "info"));
        return schema;
    }

    /**
     * 获取Kafka配置Schema
     */
    private Map<String, Object> getKafkaConfigSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("BROKER_ID", createFieldSchema("integer", "Broker ID", true, 0));
        schema.put("LISTENERS", createFieldSchema("string", "监听地址", true, "PLAINTEXT://localhost:9092"));
        schema.put("LOG_DIRS", createFieldSchema("string", "日志目录", false, "/var/kafka-logs"));
        schema.put("NUM_PARTITIONS", createFieldSchema("integer", "默认分区数", false, 1));
        schema.put("ZOOKEEPER_CONNECT", createFieldSchema("string", "Zookeeper连接", true, "localhost:2181"));
        schema.put("LOG_RETENTION_HOURS", createFieldSchema("integer", "日志保留时间(小时)", false, 168));
        return schema;
    }

    /**
     * 获取通用配置Schema
     */
    private Map<String, Object> getGenericConfigSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("LOG_LEVEL", createFieldSchema("string", "日志级别", false, "info"));
        schema.put("ENVIRONMENT", createFieldSchema("string", "环境标识", false, "production"));
        return schema;
    }

    /**
     * 创建字段Schema
     */
    private Map<String, Object> createFieldSchema(String type, String description, boolean required, Object defaultValue) {
        Map<String, Object> field = new HashMap<>();
        field.put("type", type);
        field.put("description", description);
        field.put("required", required);
        field.put("default", defaultValue);
        return field;
    }

    /**
     * 生成启动脚本模板
     */
    private String generateStartupScriptTemplate(String componentType) {
        StringBuilder script = new StringBuilder();
        script.append("#!/bin/bash\n");
        script.append("# Auto-generated startup script for ").append(componentType).append("\n\n");
        script.append("INSTALL_PATH=${INSTALL_PATH}\n");
        script.append("CONFIG_FILE=${CONFIG_FILE}\n");
        script.append("COMPONENT_TYPE=${COMPONENT_TYPE}\n\n");

        script.append("case \"$1\" in\n");
        script.append("  start)\n");
        script.append("    echo \"Starting ").append(componentType).append("...\"\n");

        switch (componentType.toLowerCase()) {
            case "elasticsearch":
                script.append("    cd $INSTALL_PATH\n");
                script.append("    export ES_JAVA_OPTS=\"${JAVA_OPTS:-'-Xms1g -Xmx1g'}\"\n");
                script.append("    ./bin/elasticsearch -d -p elasticsearch.pid\n");
                break;
            case "logstash":
                script.append("    cd $INSTALL_PATH\n");
                script.append("    export LS_JAVA_OPTS=\"${LS_JAVA_OPTS:-'-Xms1g -Xmx1g'}\"\n");
                script.append("    nohup ./bin/logstash -f $CONFIG_FILE > /dev/null 2>&1 &\n");
                script.append("    echo $! > logstash.pid\n");
                break;
            case "kafka":
                script.append("    cd $INSTALL_PATH\n");
                script.append("    nohup ./bin/kafka-server-start.sh $CONFIG_FILE > /dev/null 2>&1 &\n");
                script.append("    echo $! > kafka.pid\n");
                break;
            default:
                // Beats组件
                script.append("    cd $INSTALL_PATH\n");
                script.append("    nohup ./bin/").append(componentType).append(" -c $CONFIG_FILE > /dev/null 2>&1 &\n");
                script.append("    echo $! > ").append(componentType).append(".pid\n");
        }

        script.append("    ;;\n");
        script.append("  stop)\n");
        script.append("    echo \"Stopping ").append(componentType).append("...\"\n");
        script.append("    pkill -f ").append(componentType).append("\n");
        script.append("    ;;\n");
        script.append("  restart)\n");
        script.append("    $0 stop\n");
        script.append("    sleep 2\n");
        script.append("    $0 start\n");
        script.append("    ;;\n");
        script.append("  *)\n");
        script.append("    echo \"Usage: $0 {start|stop|restart}\"\n");
        script.append("    exit 1\n");
        script.append("    ;;\n");
        script.append("esac\n");

        return script.toString();
    }
}
