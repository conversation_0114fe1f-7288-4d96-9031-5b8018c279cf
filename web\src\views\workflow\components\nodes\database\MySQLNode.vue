<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="mysql-node-content">
        <div class="db-info" v-if="data.config?.host">
          <div class="info-item">
            <i class="fas fa-server"></i>
            <span>{{ data.config.host }}:{{ data.config.port || 3306 }}</span>
          </div>
          <div class="info-item" v-if="data.config.database">
            <i class="fas fa-database"></i>
            <span>{{ data.config.database }}</span>
          </div>
        </div>
        
        <div class="sql-preview" v-if="data.config?.sql">
          <code>{{ truncateSql(data.config.sql) }}</code>
        </div>
        
        <p class="node-description">{{ nodeConfig.description }}</p>
        
        <div class="db-config" v-if="data.config">
          <div class="config-row" v-if="data.config.operation">
            <span class="config-label">操作:</span>
            <span class="config-value">{{ data.config.operation }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { NodeProps } from '@vue-flow/core'
import BaseNode from '../BaseNode.vue'
import { getNodeByType } from '../../../config/nodeLibrary'

// Props
interface MySQLNodeData {
  label?: string
  description?: string
  config?: {
    host?: string
    port?: number
    database?: string
    username?: string
    password?: string
    sql?: string
    operation?: string
  }
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: MySQLNodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 获取节点配置
const nodeConfig = computed(() => {
  return getNodeByType('mysql') || {
    description: 'MySQL数据库操作，支持查询、插入、更新、删除'
  }
})

// 方法
const truncateSql = (sql: string) => {
  if (sql.length > 50) {
    return sql.substring(0, 50) + '...'
  }
  return sql
}
</script>

<style scoped>
.mysql-node-content {
  text-align: left;
}

.db-info {
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.info-item i {
  width: 12px;
  text-align: center;
}

.info-item span {
  font-family: monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}

.sql-preview {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 6px;
  margin-bottom: 8px;
}

.sql-preview code {
  color: white;
  font-size: 10px;
  font-family: 'Monaco', 'Menlo', monospace;
  word-break: break-all;
  line-height: 1.3;
}

.node-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.db-config {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.config-row {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.config-label {
  font-weight: 500;
}

.config-value {
  font-family: monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}
</style>
