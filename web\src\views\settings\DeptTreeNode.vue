<template>
  <div class="dept-tree-node">
    <!-- 当前节点 -->
    <div 
      class="dept-node-row group hover:bg-blue-50 transition-all duration-200 border-b border-gray-100 last:border-b-0"
      :class="{ 'bg-blue-25': isExpanded }"
    >
      <div class="grid grid-cols-12 gap-4 px-6 py-4 items-center">
        <!-- 部门名称 -->
        <div class="col-span-4 flex items-center" :style="{ paddingLeft: `${level * 24}px` }">
          <!-- 展开/折叠按钮 -->
          <button
            v-if="dept.hasChildren"
            @click="toggleExpand"
            class="w-6 h-6 mr-3 flex items-center justify-center rounded-full hover:bg-blue-100 transition-colors duration-200"
            :class="{ 'text-blue-600': isExpanded, 'text-gray-400': !isExpanded }"
          >
            <svg 
              class="w-4 h-4 transition-transform duration-200" 
              :class="{ 'rotate-90': isExpanded }"
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
          <div v-else class="w-6 mr-3"></div>

          <!-- 部门图标和名称 -->
          <div class="flex items-center">
            <div class="w-8 h-8 mr-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold text-gray-900">{{ dept.deptName }}</div>
              <div v-if="dept.deptCode" class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full inline-block mt-1">
                {{ dept.deptCode }}
              </div>
            </div>
          </div>
        </div>

        <!-- 上级部门 -->
        <div class="col-span-2 text-sm text-gray-600">
          {{ dept.parentName || '顶级部门' }}
        </div>

        <!-- 负责人 -->
        <div class="col-span-2">
          <div v-if="dept.leaderName" class="flex items-center text-sm">
            <div class="w-6 h-6 mr-2 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <span class="text-gray-900">{{ dept.leaderName }}</span>
          </div>
          <span v-else class="text-sm text-gray-400">未指定</span>
        </div>

        <!-- 人员数量 -->
        <div class="col-span-1 text-center">
          <span 
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            :class="(dept.userCount || 0) > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
          >
            {{ dept.userCount || 0 }}人
          </span>
        </div>

        <!-- 状态 -->
        <div class="col-span-1 text-center">
          <span 
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            :class="dept.status === '0' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
          >
            {{ dept.statusName || '未知' }}
          </span>
        </div>

        <!-- 操作按钮 -->
        <div class="col-span-2 flex justify-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <button
            @click="$emit('edit', dept)"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            编辑
          </button>
          <button
            @click="$emit('show-users', dept)"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
          >
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            用户
          </button>
          <button
            @click="$emit('delete', dept.id)"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
          >
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            删除
          </button>
        </div>
      </div>
    </div>

    <!-- 子节点 -->
    <div v-if="isExpanded && dept.children && dept.children.length > 0" class="dept-children">
      <DeptTreeNode
        v-for="child in dept.children"
        :key="child.id"
        :dept="child"
        :level="level + 1"
        @edit="$emit('edit', $event)"
        @show-users="$emit('show-users', $event)"
        @delete="$emit('delete', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { SysDeptVO } from '@/types/system'

interface Props {
  dept: SysDeptVO
  level: number
}

defineProps<Props>()

defineEmits<{
  edit: [dept: SysDeptVO]
  'show-users': [dept: SysDeptVO]
  delete: [id: string]
}>()

// 展开状态
const isExpanded = ref(true) // 默认展开

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style scoped>
.dept-tree-node {
  position: relative;
}

.dept-node-row {
  position: relative;
}

.dept-node-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.dept-node-row:hover::before {
  background-color: #3b82f6;
}

.bg-blue-25 {
  background-color: rgba(59, 130, 246, 0.025);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dept-node-row .grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .dept-node-row .col-span-4,
  .dept-node-row .col-span-2,
  .dept-node-row .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .dept-node-row .opacity-0 {
    opacity: 1;
  }
}
</style>
