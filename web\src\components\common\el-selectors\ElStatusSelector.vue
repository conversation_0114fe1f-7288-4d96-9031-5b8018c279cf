<template>
  <div class="el-status-selector">
    <!-- 下拉选择模式 -->
    <el-select
      v-if="mode === 'select'"
      v-model="selectedValue"
      :placeholder="config.placeholder"
      :size="config.size"
      :disabled="config.disabled"
      :clearable="config.clearable"
      :multiple="config.multiple"
      :filterable="config.filterable"
      :loading="config.loading"
      :no-data-text="config.noDataText"
      :no-match-text="config.noMatchText"
      :loading-text="config.loadingText"
      :popper-class="config.popperClass"
      :teleported="config.teleported"
      :persistent="config.persistent"
      :automatic-dropdown="config.automaticDropdown"
      :fit-input-width="config.fitInputWidth"
      :suffix-icon="config.suffixIcon"
      :tag-type="config.tagType"
      :max-collapse-tags="config.maxCollapseTags"
      :collapse-tags-tooltip="config.collapseTagsTooltip"
      :effect="config.effect"
      @change="handleChange"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
      @clear="handleClear"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <el-option
        v-for="option in statusOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      >
        <div class="status-option-content">
          <el-tag
            :type="getStatusTagType(option)"
            :color="option.bgColor"
            :effect="config.effect"
            size="small"
          >
            <el-icon v-if="option.icon" class="option-icon">
              <component :is="option.icon" />
            </el-icon>
            <span :style="{ color: option.color }">{{ option.label }}</span>
          </el-tag>
          <span v-if="config.showDescription && option.description" class="option-description">
            {{ option.description }}
          </span>
        </div>
      </el-option>
    </el-select>

    <!-- 单选按钮组模式 -->
    <el-radio-group
      v-else-if="mode === 'radio'"
      v-model="selectedValue"
      :size="config.size"
      :disabled="config.disabled"
      @change="handleChange"
    >
      <el-radio-button
        v-for="option in statusOptions"
        :key="option.value"
        :label="option.value"
        :disabled="option.disabled"
      >
        <div class="radio-option-content">
          <el-icon v-if="option.icon" class="option-icon">
            <component :is="option.icon" />
          </el-icon>
          <span>{{ option.label }}</span>
        </div>
      </el-radio-button>
    </el-radio-group>

    <!-- 多选按钮组模式 -->
    <el-checkbox-group
      v-else-if="mode === 'checkbox'"
      v-model="selectedValue"
      :size="config.size"
      :disabled="config.disabled"
      @change="handleChange"
    >
      <el-checkbox-button
        v-for="option in statusOptions"
        :key="option.value"
        :label="option.value"
        :disabled="option.disabled"
      >
        <div class="checkbox-option-content">
          <el-icon v-if="option.icon" class="option-icon">
            <component :is="option.icon" />
          </el-icon>
          <span>{{ option.label }}</span>
        </div>
      </el-checkbox-button>
    </el-checkbox-group>

    <!-- 卡片模式 -->
    <div v-else-if="mode === 'card'" class="status-cards">
      <div
        v-for="option in statusOptions"
        :key="option.value"
        class="status-card"
        :class="{
          'is-selected': isSelected(option.value),
          'is-disabled': option.disabled,
          'is-multiple': config.multiple
        }"
        @click="handleCardClick(option)"
      >
        <!-- 选择指示器 -->
        <div v-if="config.multiple" class="card-checkbox">
          <el-checkbox
            :model-value="isSelected(option.value)"
            :disabled="option.disabled"
            @click.stop
          />
        </div>
        <div v-else class="card-radio">
          <el-radio
            :model-value="selectedValue"
            :label="option.value"
            :disabled="option.disabled"
            @click.stop
          />
        </div>

        <!-- 状态内容 -->
        <div class="card-content">
          <div class="status-badge" :style="getBadgeStyle(option)">
            <el-icon v-if="option.icon" class="badge-icon">
              <component :is="option.icon" />
            </el-icon>
            <span class="badge-text">{{ option.label }}</span>
          </div>
          
          <div v-if="config.showDescription && option.description" class="card-description">
            {{ option.description }}
          </div>
        </div>
      </div>
    </div>

    <!-- 标签模式 -->
    <div v-else class="status-tags">
      <el-tag
        v-for="option in statusOptions"
        :key="option.value"
        :type="getStatusTagType(option)"
        :color="option.bgColor"
        :effect="isSelected(option.value) ? 'dark' : 'light'"
        :size="config.size"
        :closable="false"
        :disable-transitions="false"
        class="status-tag"
        :class="{
          'is-selected': isSelected(option.value),
          'is-disabled': option.disabled,
          'is-clickable': !option.disabled
        }"
        @click="handleTagClick(option)"
      >
        <el-icon v-if="option.icon" class="tag-icon">
          <component :is="option.icon" />
        </el-icon>
        <span :style="{ color: option.color }">{{ option.label }}</span>
      </el-tag>
    </div>

    <!-- 已选择状态显示 -->
    <div v-if="showSelectedInfo && hasSelection" class="selected-info">
      <el-divider content-position="left">
        <el-icon><InfoFilled /></el-icon>
        <span>已选择状态</span>
      </el-divider>
      
      <div class="selected-status">
        <template v-if="config.multiple && Array.isArray(selectedValue)">
          <el-tag
            v-for="value in selectedValue"
            :key="value"
            :type="getStatusTagType(getOptionByValue(value))"
            :closable="!config.disabled"
            :size="config.size"
            @close="handleStatusRemove(value)"
          >
            {{ getOptionByValue(value)?.label }}
          </el-tag>
        </template>
        <template v-else>
          <el-tag
            :type="getStatusTagType(getOptionByValue(selectedValue))"
            :size="config.size"
          >
            {{ getOptionByValue(selectedValue)?.label }}
          </el-tag>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  ElSelect,
  ElOption,
  ElRadioGroup,
  ElRadioButton,
  ElCheckboxGroup,
  ElCheckboxButton,
  ElCheckbox,
  ElRadio,
  ElTag,
  ElIcon,
  ElDivider
} from 'element-plus'
import {
  InfoFilled,
  CircleCheck,
  CircleClose,
  Warning,
  QuestionFilled,
  Lock,
  Unlock
} from '@element-plus/icons-vue'
import type { 
  StatusSelectorOption, 
  SelectorConfig,
  SelectorEmits 
} from './types'

interface Props {
  modelValue?: string | number | null | (string | number)[]
  config?: Partial<SelectorConfig>
  options?: StatusSelectorOption[]
  preset?: 'basic' | 'agent' | 'user' | 'system' | 'custom'
  mode?: 'select' | 'radio' | 'checkbox' | 'card' | 'tag'
  showSelectedInfo?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  config: () => ({}),
  options: () => [],
  preset: 'basic',
  mode: 'select',
  showSelectedInfo: false
})

const emit = defineEmits<SelectorEmits>()

// 响应式数据
const selectedValue = ref<string | number | null | (string | number)[]>()

// 默认配置
const defaultConfig: SelectorConfig = {
  multiple: false,
  clearable: true,
  placeholder: '请选择状态',
  size: 'default',
  disabled: false,
  filterable: false,
  showDescription: true,
  tagType: 'info',
  effect: 'light',
  noDataText: '无数据',
  noMatchText: '无匹配数据',
  loadingText: '加载中...',
  teleported: true,
  persistent: false,
  automaticDropdown: false,
  fitInputWidth: false,
  maxCollapseTags: 3,
  collapseTagsTooltip: true
}

// 合并配置
const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 预设状态选项
const presetOptions = computed(() => {
  switch (props.preset) {
    case 'agent':
      return [
        { value: '', label: '全部状态', color: '#909399', bgColor: '#f4f4f5', icon: QuestionFilled },
        { value: 'active', label: '启用', color: '#16a34a', bgColor: '#dcfce7', description: '智能体正常运行', icon: CircleCheck },
        { value: 'inactive', label: '禁用', color: '#dc2626', bgColor: '#fef2f2', description: '智能体已停用', icon: CircleClose }
      ]
    case 'user':
      return [
        { value: '', label: '全部状态', color: '#909399', bgColor: '#f4f4f5', icon: QuestionFilled },
        { value: '0', label: '正常', color: '#16a34a', bgColor: '#dcfce7', description: '用户账号正常', icon: CircleCheck },
        { value: '1', label: '禁用', color: '#dc2626', bgColor: '#fef2f2', description: '用户账号已禁用', icon: CircleClose },
        { value: '2', label: '锁定', color: '#f59e0b', bgColor: '#fef3c7', description: '用户账号已锁定', icon: Lock }
      ]
    case 'system':
      return [
        { value: '', label: '全部状态', color: '#909399', bgColor: '#f4f4f5', icon: QuestionFilled },
        { value: 'online', label: '在线', color: '#16a34a', bgColor: '#dcfce7', description: '系统正常运行', icon: CircleCheck },
        { value: 'offline', label: '离线', color: '#dc2626', bgColor: '#fef2f2', description: '系统离线', icon: CircleClose },
        { value: 'maintenance', label: '维护中', color: '#f59e0b', bgColor: '#fef3c7', description: '系统维护中', icon: Warning }
      ]
    case 'basic':
    default:
      return [
        { value: '', label: '全部', color: '#909399', bgColor: '#f4f4f5', icon: QuestionFilled },
        { value: '1', label: '启用', color: '#16a34a', bgColor: '#dcfce7', icon: Unlock },
        { value: '0', label: '禁用', color: '#dc2626', bgColor: '#fef2f2', icon: Lock }
      ]
  }
})

// 最终状态选项
const statusOptions = computed(() => {
  return props.options.length > 0 ? props.options : presetOptions.value
})

// 计算属性
const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedValue.value) && selectedValue.value.length > 0
  }
  return selectedValue.value !== null && selectedValue.value !== undefined && selectedValue.value !== ''
})

// 方法
const getOptionByValue = (value: string | number | null | undefined): StatusSelectorOption | null => {
  if (value === null || value === undefined) return null
  return statusOptions.value.find(option => option.value === value) || null
}

const getStatusTagType = (option?: StatusSelectorOption | null) => {
  if (!option) return 'info'

  // 根据预设类型返回不同的tag类型
  switch (props.preset) {
    case 'agent':
      if (option.value === 'active') return 'success'
      if (option.value === 'inactive') return 'danger'
      return 'info'
    case 'user':
      if (option.value === '0') return 'success'
      if (option.value === '1') return 'danger'
      if (option.value === '2') return 'warning'
      return 'info'
    case 'system':
      if (option.value === 'online') return 'success'
      if (option.value === 'offline') return 'danger'
      if (option.value === 'maintenance') return 'warning'
      return 'info'
    default:
      if (option.value === '1' || option.value === 'active') return 'success'
      if (option.value === '0' || option.value === 'inactive') return 'danger'
      return 'info'
  }
}

const getBadgeStyle = (option: StatusSelectorOption) => {
  return {
    backgroundColor: option.bgColor || '#f4f4f5',
    color: option.color || '#909399'
  }
}

const isSelected = (value: string | number): boolean => {
  if (config.value.multiple && Array.isArray(selectedValue.value)) {
    return selectedValue.value.includes(value)
  }
  return selectedValue.value === value
}

// 事件处理
const handleChange = (value: string | number | null | (string | number)[]) => {
  selectedValue.value = value

  let option: StatusSelectorOption | StatusSelectorOption[] | null = null

  if (config.value.multiple && Array.isArray(value)) {
    option = value.map(v => getOptionByValue(v)).filter(Boolean) as StatusSelectorOption[]
  } else {
    option = getOptionByValue(value as string | number)
  }

  emit('update:modelValue', value)
  emit('change', value, option)

  if (!config.value.multiple && option) {
    emit('select', value as string | number, option as StatusSelectorOption)
  }
}

const handleCardClick = (option: StatusSelectorOption) => {
  if (option.disabled) return

  if (config.value.multiple) {
    const currentValues = Array.isArray(selectedValue.value) ? [...selectedValue.value] : []
    const index = currentValues.indexOf(option.value)

    if (index > -1) {
      currentValues.splice(index, 1)
    } else {
      currentValues.push(option.value)
    }

    handleChange(currentValues)
  } else {
    handleChange(option.value)
  }
}

const handleTagClick = (option: StatusSelectorOption) => {
  if (option.disabled) return

  if (config.value.multiple) {
    handleCardClick(option)
  } else {
    handleChange(option.value)
  }
}

const handleStatusRemove = (value: string | number) => {
  if (config.value.multiple && Array.isArray(selectedValue.value)) {
    const newValues = selectedValue.value.filter(v => v !== value)
    handleChange(newValues)
    emit('remove-tag', value)
  }
}

const handleVisibleChange = (visible: boolean) => {
  emit('visible-change', visible)
}

const handleRemoveTag = (value: string | number) => {
  emit('remove-tag', value)
}

const handleClear = () => {
  const clearValue = config.value.multiple ? [] : null
  selectedValue.value = clearValue
  emit('update:modelValue', clearValue)
  emit('change', clearValue, null)
  emit('clear')
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
}, { immediate: true })

// 暴露方法
defineExpose({
  clearSelection: handleClear,
  getOptionByValue,
  getStatusTagType
})
</script>

<style scoped>
.el-status-selector {
  width: 100%;
}

.status-option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.option-icon {
  margin-right: 4px;
}

.option-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-left: 8px;
}

.radio-option-content,
.checkbox-option-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.status-card {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--el-bg-color);
}

.status-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.status-card.is-selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.status-card.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.card-content {
  margin-top: 8px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  gap: 4px;
}

.card-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 8px;
  line-height: 1.4;
}

.status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-tag.is-clickable {
  cursor: pointer;
  transition: all 0.2s;
}

.status-tag.is-clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-tag.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tag-icon {
  margin-right: 4px;
}

.selected-info {
  margin-top: 16px;
}

.selected-status {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
