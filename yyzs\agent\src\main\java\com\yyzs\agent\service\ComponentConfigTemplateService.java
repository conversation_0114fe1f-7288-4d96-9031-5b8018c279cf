package com.yyzs.agent.service;

import java.util.Map;

/**
 * 组件配置模板服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ComponentConfigTemplateService {

    /**
     * 获取组件默认配置模板
     */
    String getDefaultConfigTemplate(String componentType);

    /**
     * 根据用户配置生成最终配置文件内容
     */
    String generateConfigContent(String componentType, Map<String, Object> userConfig);

    /**
     * 验证组件配置参数
     */
    boolean validateConfig(String componentType, Map<String, Object> config);

    /**
     * 获取组件配置参数说明
     */
    Map<String, Object> getConfigSchema(String componentType);

    /**
     * 获取组件默认配置参数
     */
    Map<String, Object> getDefaultConfigParams(String componentType);

    /**
     * 解析配置文件内容为参数Map
     */
    Map<String, Object> parseConfigContent(String componentType, String configContent);

    /**
     * 合并用户配置和默认配置
     */
    Map<String, Object> mergeConfigs(Map<String, Object> defaultConfig, Map<String, Object> userConfig);

    /**
     * 获取支持的组件类型列表
     */
    String[] getSupportedComponentTypes();

    /**
     * 检查组件类型是否支持
     */
    boolean isComponentTypeSupported(String componentType);

    /**
     * 获取组件配置文件名
     */
    String getConfigFileName(String componentType);

    /**
     * 获取组件启动脚本模板
     */
    String getStartupScriptTemplate(String componentType);

    /**
     * 生成组件启动脚本
     */
    String generateStartupScript(String componentType, String installPath, Map<String, Object> config);

}
