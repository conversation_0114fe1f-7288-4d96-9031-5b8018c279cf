<template>
  <div class="bg-white flex flex-col overflow-y-auto">
    <!-- 页面头部导航 -->
    <div class="border-b border-solid border-gray-100 ">
      <div class="header-content">
        <div class="header-left">
          <button
            @click="goBack"
            class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div class="breadcrumb">
            <span class="breadcrumb-item">知识库管理</span>
            <i class="fas fa-chevron-right"></i>
            <span class="breadcrumb-item current">创建知识库</span>
          </div>
        </div>
        <div class="header-right">
          <button
            class="btn btn-create-header"
            @click="createKnowledgeBase"
            :disabled="!canCreate || creating"
          >
            <i class="fas fa-save" v-if="!creating"></i>
            <i class="fas fa-spinner fa-spin" v-if="creating"></i>
            {{ creating ? '保存中...' : '保存知识库' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 p-5 flex flex-row overflow-y-auto">
      <!-- 表单容器 -->
      <div class="flex flex-col gap-5  pl-4 basis-1/3">
        <!-- 基本信息表单 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-database text-purple-500"></i>
              基本信息
            </h4>
          </div>
          <div class="form-body">
            <div class="form-group">
              <label for="kb-name" class="form-label required">知识库名称</label>
              <input
                id="kb-name"
                type="text"
                v-model="formData.name"
                placeholder="请输入一个有意义的知识库名称，如：产品技术文档库"
                class="form-input"
                maxlength="50"
                @input="validateForm"
              >
              <div class="input-footer">
                <span class="input-hint">建议使用简洁明了的名称，便于团队成员识别</span>
                <span class="char-count">{{ formData.name.length }}/50</span>
              </div>
            </div>

            <div class="form-group">
              <label for="kb-description" class="form-label">知识库描述</label>
              <textarea
                id="kb-description"
                v-model="formData.description"
                placeholder="请详细描述知识库的用途、包含的内容类型等信息..."
                class="form-textarea"
                rows="4"
                maxlength="200"
              ></textarea>
              <div class="input-footer">
                <span class="input-hint">详细的描述有助于团队成员了解知识库的用途</span>
                <span class="char-count">{{ formData.description.length }}/200</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 访问权限区域 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-shield-alt text-purple-500"></i>
              访问权限
              <p class="form-subtitle">选择合适的权限级别，确保知识库的安全性和协作效率</p>
            </h4>
          </div>
          <div class="form-body">
            <div class="form-group">
              <div class="permission-grid">
                <label class="permission-card" :class="{ active: formData.permission === 'personal' }">
                  <input
                    type="radio"
                    v-model="formData.permission"
                    value="personal"
                    @change="handlePermissionChange"
                  >
                  <div class="card-content">
                    <div class="card-icon">
                      <i class="fas fa-user"></i>
                    </div>
                    <div class="card-title">个人私有</div>
                    <div class="card-desc">仅创建者可见</div>
                  </div>
                </label>

                <label class="permission-card" :class="{ active: formData.permission === 'role' }">
                  <input
                    type="radio"
                    v-model="formData.permission"
                    value="role"
                    @change="handlePermissionChange"
                  >
                  <div class="card-content">
                    <div class="card-icon">
                      <i class="fas fa-user-tag"></i>
                    </div>
                    <div class="card-title">选择角色</div>
                    <div class="card-desc">基于角色权限管理</div>
                  </div>
                </label>

                <label class="permission-card" :class="{ active: formData.permission === 'department' }">
                  <input
                    type="radio"
                    v-model="formData.permission"
                    value="department"
                    @change="handlePermissionChange"
                  >
                  <div class="card-content">
                    <div class="card-icon">
                      <i class="fas fa-building"></i>
                    </div>
                    <div class="card-title">选择单位</div>
                    <div class="card-desc">指定部门单位可见</div>
                  </div>
                </label>

                <label class="permission-card" :class="{ active: formData.permission === 'users' }">
                  <input
                    type="radio"
                    v-model="formData.permission"
                    value="users"
                    @change="handlePermissionChange"
                  >
                  <div class="card-content">
                    <div class="card-icon">
                      <i class="fas fa-users"></i>
                    </div>
                    <div class="card-title">选择部分人</div>
                    <div class="card-desc">精确权限控制</div>
                  </div>
                </label>

                <label class="permission-card" :class="{ active: formData.permission === 'public' }">
                  <input
                    type="radio"
                    v-model="formData.permission"
                    value="public"
                    @change="handlePermissionChange"
                  >
                  <div class="card-content">
                    <div class="card-icon">
                      <i class="fas fa-globe"></i>
                    </div>
                    <div class="card-title">公开共享</div>
                    <div class="card-desc">所有人可见</div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 当前选择信息显示区域 -->
        <div v-if="hasSelections" class="selection-summary">
          <div class="summary-header">
            <div class="summary-title">
              <i class="fas fa-check-circle"></i>
              <span>当前权限设置</span>
            </div>
            <div class="summary-count">
              已选择 {{ getTotalSelectionCount() }} 项
            </div>
          </div>

          <!-- 角色选择显示 -->
          <div v-if="formData.permission === 'role' && selectedRoles.length > 0" class="summary-section">
            <div class="summary-label">
              <i class="fas fa-user-tag"></i>
              <span>选择的角色 ({{ selectedRoles.length }})</span>
            </div>
            <div class="summary-grid">
              <div
                v-for="roleId in selectedRoles"
                :key="roleId"
                class="summary-item role-item"
              >
                <button class="item-remove-btn" @click="toggleRole(roleId)">
                  <i class="fas fa-times"></i>
                </button>
                <div class="item-name">{{ getRoleName(roleId) }}</div>
                <div class="item-desc">{{ getRoleDescription(roleId) }}</div>
              </div>
            </div>
          </div>

          <!-- 部门选择显示 -->
          <div v-if="formData.permission === 'department' && selectedDepartments.length > 0" class="summary-section">
            <div class="summary-label">
              <i class="fas fa-building"></i>
              <span>选择的部门 ({{ selectedDepartments.length }})</span>
            </div>
            <div class="summary-grid">
              <div
                v-for="deptId in selectedDepartments"
                :key="deptId"
                class="summary-item dept-item"
              >
                <button class="item-remove-btn" @click="toggleDepartment(deptId)">
                  <i class="fas fa-times"></i>
                </button>
                <div class="item-name">{{ getDepartmentName(deptId) }}</div>
                <div class="item-desc">{{ getDepartmentDescription(deptId) }}</div>
              </div>
            </div>
          </div>

          <!-- 用户选择显示 -->
          <div v-if="formData.permission === 'users' && selectedUsers.length > 0" class="summary-section">
            <div class="summary-label">
              <i class="fas fa-users"></i>
              <span>选择的用户 ({{ selectedUsers.length }}/10)</span>
            </div>
            <div class="summary-grid">
              <div
                v-for="userId in selectedUsers"
                :key="userId"
                class="summary-item user-item"
              >
                <div class="item-avatar">{{ getUserName(userId).charAt(0) }}</div>
                <div class="item-text">
                  <button class="item-remove-btn" @click="toggleUser(userId)">
                    <i class="fas fa-times"></i>
                  </button>
                  <div class="item-name">{{ getUserName(userId) }}</div>
                  <div class="item-desc">{{ getUserDepartment(userId) }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="summary-actions">
            <button class="btn btn-outline" @click="openSelectionPanel">
              <i class="fas fa-edit"></i>
              修改选择
            </button>
            <button class="btn btn-outline-danger" @click="clearCurrentSelection">
              <i class="fas fa-trash"></i>
              清空选择
            </button>
          </div>
        </div>
      </div>

      <!-- 向量化检索相关配置 -->
      <div class="flex-1 flex flex-col gap-4 pl-10 ml-10 border-l border-l-gray-200 border-solid">
        <!-- 索引模式 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-vector-square text-purple-500"></i>
              索引模式
            </h4>
          </div>

          <!-- 索引模式选择 -->
          <div class="config-group">
            <div class="flex flex-row gap-3">
              <label class="radio-option basis-1/2" :style="{backgroundColor: vectorizationConfig.indexMode === 'high_quality' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.indexMode"
                  value="high_quality"
                >
                <span class="radio-text">
                  <strong>高质量</strong><span class="ml-3 text-blue-400 border-blue-400 border-solid border rounded-md px-1 py-0 text-sm">推荐</span>
                </span>
                <div class="option-desc">
                  调用嵌入模型来处理文档以实现更精确的检索，可以帮助大语言模型生成高质量的回答
                </div>
              </label>

              <label class="radio-option basis-1/2" :style="{backgroundColor: vectorizationConfig.indexMode === 'economy' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.indexMode"
                  value="economy"
                >
                <span class="radio-text">
                <strong>经济</strong>
              </span>
                <div class="option-desc">
                  每个数据块使用10个关键词进行检索，不会消耗任何tokens，但会以降低检索准确性为代价
                </div>
              </label>
            </div>
          </div>

          <!-- 嵌入模型选择（仅高质量模式） -->
          <div class="config-group">
            <label class="config-label">嵌入模型</label>
            <select v-model="vectorizationConfig.embeddingModel" class="config-select" :disabled="vectorizationConfig.indexMode === 'economy'">
              <option v-for="model in models['Embeddings']" :key="model.modelId" :value="model.modelId">{{model.name}}</option>
            </select>
          </div>

          <!-- 向量数据库选择 -->
          <div class="config-group">
            <label class="config-label">向量数据库</label>
            <div class="flex items-center space-x-2">
              <select v-model="vectorizationConfig.vectorDatabaseId" class="config-select flex-1">
                <option value="">请选择向量数据库</option>
                <option
                  v-for="db in enabledVectorDatabases"
                  :key="db.id"
                  :value="db.id"
                >
                  {{ db.name }} ({{ db.typeName }})
                </option>
              </select>
              <button
                @click="refreshVectorDatabases"
                class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                title="刷新向量数据库列表"
              >
                🔄
              </button>
            </div>
            <div v-if="vectorizationConfig.vectorDatabaseId" class="mt-2 text-sm text-gray-600">
              <span v-if="selectedVectorDatabase">
                {{ selectedVectorDatabase.icon }} {{ selectedVectorDatabase.description }}
              </span>
            </div>
          </div>

          <!-- 文件存储选择 -->
          <div class="config-group">
            <label class="config-label">文件存储</label>
            <div class="flex items-center space-x-2">
              <select v-model="vectorizationConfig.fileStorageId" class="config-select flex-1">
                <option value="">请选择文件存储</option>
                <option
                  v-for="storage in enabledFileStorages"
                  :key="storage.id"
                  :value="storage.id"
                >
                  {{ storage.name }} ({{ storage.storageTypeName }})
                </option>
              </select>
              <button
                @click="refreshFileStorages"
                class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                title="刷新文件存储列表"
              >
                🔄
              </button>
            </div>
            <div v-if="vectorizationConfig.fileStorageId" class="mt-2 text-sm text-gray-600">
              <span v-if="selectedFileStorage">
                {{ selectedFileStorage.icon }} {{ selectedFileStorage.description }}
              </span>
            </div>
          </div>
        </div>

        <!-- 检索模式 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-vector-square text-purple-500"></i>
              检索模式
            </h4>
          </div>
          <!-- 检索设置 -->
          <div class="config-group">
            <div class="flex flex-row gap-3">
              <label class="radio-option basis-1/3" :style="{backgroundColor: vectorizationConfig.retrievalSettings.retrievalMode === 'vector' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.retrievalSettings.retrievalMode"
                  value="vector"
                >
                <span class="radio-text">
                  <strong>向量检索</strong>
                </span>
                <span class="option-desc">
                  通过生成查询嵌入并查询与其向量表示最相似的文本分段
                </span>
              </label>

              <label class="radio-option basis-1/3" :style="{backgroundColor: vectorizationConfig.retrievalSettings.retrievalMode === 'fulltext' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.retrievalSettings.retrievalMode"
                  value="fulltext"
                >
                <span class="radio-text">
                  <strong>全文检索</strong>
                </span>
                <span class="option-desc">
                  索引文档中的所有词汇，从而允许用户查询任意词汇，并返回包含这些词汇的文本片段
                </span>
              </label>

              <label class="radio-option basis-1/3" :style="{backgroundColor: vectorizationConfig.retrievalSettings.retrievalMode === 'hybrid' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.retrievalSettings.retrievalMode"
                  value="hybrid"
                >
                <span class="radio-text">
                  <strong>混合检索</strong><span class="ml-3 text-blue-400 border-blue-400 border-solid border rounded-md px-1 py-0 text-sm">推荐</span>
                </span>
                <span class="option-desc">
                  同时执行全文检索和向量检索，并应用重排序步骤，从两类查询结果中选择匹配用户问题的最佳结果
                </span>
              </label>
            </div>
          </div>
        </div>

        <!-- 是否启用重排序 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-vector-square text-purple-500"></i>
              是否启用重排序检索
            </h4>
          </div>
          <div class="config-group">
            <!-- Rerank模型配置 -->
            <div class="config-group">
              <label class="config-label">
                <input
                  type="checkbox"
                  v-model="vectorizationConfig.retrievalSettings.enableRerank"
                  class="config-checkbox"
                >
                启用Rerank模型
              </label>
              <div class="config-hint">
                重排序模型将根据候选文档列表与用户问题语义匹配进行重新排序，从而改进语义排序的结果
              </div>
            </div>

            <!-- Rerank模型选择 -->
            <div class="config-group" v-show="vectorizationConfig.retrievalSettings.enableRerank">
              <label class="config-label">Rerank模型</label>
              <select v-model="vectorizationConfig.retrievalSettings.rerankModel" class="config-select" :disabled="!vectorizationConfig.retrievalSettings.enableRerank">
                <option v-for="model in models['Reranker']" :key="model.modelId" :value="model.modelId">{{model.name}}</option>
              </select>
            </div>

            <!-- 混合检索权重设置 -->
            <div class="config-group" v-show="vectorizationConfig.retrievalSettings.retrievalMode === 'hybrid' && !vectorizationConfig.retrievalSettings.enableRerank">
              <label class="config-label">检索权重配置</label>

              <div class="weight-config">
                <div class="weight-item">
                  <div class="flex justify-between items-center">
                    <label class="weight-label">语义权重<span class="text-blue-400 font-bold px-2 py-1">（{{vectorizationConfig.retrievalSettings.hybridWeights.semanticWeight}}）</span></label>
                    <label class="weight-label"><span class="text-blue-400 font-bold px-2 py-1">（{{vectorizationConfig.retrievalSettings.hybridWeights.keywordWeight}}）</span>关键词权重</label>
                  </div>

                  <div class="input-with-slider">
                    <input
                      type="range"
                      v-model="vectorizationConfig.retrievalSettings.hybridWeights.semanticWeight"
                      min="0"
                      max="1"
                      step="0.1"
                      class="config-slider"
                      @input="updateKeywordWeight"
                    >
                  </div>
                </div>
              </div>
              <div class="config-hint">
                控制语义和关键词检索结果的占比，两者权重之和应为1.0
              </div>
            </div>
          </div>
        </div>

        <!-- 检索结果 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-vector-square text-purple-500"></i>
              检索结果
            </h4>
          </div>
          <div class="flex flex-row gap-10">
            <!-- Top K 设置（非Rerank模式） -->
            <div class="config-group basis-1/2" >
              <label class="config-label">Top K</label>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="vectorizationConfig.retrievalSettings.topK"
                  min="1"
                  max="10"
                  step="1"
                  class="config-slider"
                >
                <input
                  type="number"
                  v-model="vectorizationConfig.retrievalSettings.topK"
                  min="1"
                  max="10"
                  class="config-input"
                >
              </div>
              <div class="config-hint">
                用于筛选与用户问题相似度最高的文本片段。系统同时会根据选用模型上下文窗口大小动态调整分段数量。最大可返回10个
              </div>
            </div>

            <!-- Score阈值设置（向量检索模式且非Rerank） -->
            <div class="config-group flex-1">
              <label class="config-label">Score阈值</label>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="vectorizationConfig.retrievalSettings.scoreThreshold"
                  min="0"
                  max="1"
                  step="0.1"
                  class="config-slider"
                >
                <input
                  type="number"
                  v-model="vectorizationConfig.retrievalSettings.scoreThreshold"
                  min="0"
                  max="1"
                  step="0.1"
                  class="config-input"
                >
              </div>
              <div class="config-hint">
                用于设置文本片段筛选的相似度阈值，范围是0到1
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧拉出面板 -->
    <div class="slide-panel-overlay" v-if="showSelectionPanel" @click="closeSelectionPanel"></div>
    <div class="slide-panel" :class="{ 'slide-panel-open': showSelectionPanel }">
      <!-- 角色选择 -->
      <div v-if="formData.permission === 'role'" class="selection-content">
        <div class="selection-header">
          <h3>选择角色</h3>
          <button class="close-btn" @click="closeSelectionPanel">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 已选择的角色列表 -->
        <div v-if="selectedRoles.length > 0" class="selected-items">
          <div class="selected-header">
            <span class="selected-title">已选择角色 ({{ selectedRoles.length }})</span>
            <button class="clear-all-btn" @click="clearAllRoles">清空</button>
          </div>
          <div class="selected-tags">
            <span
              v-for="roleId in selectedRoles"
              :key="roleId"
              class="selected-tag"
            >
              {{ getRoleName(roleId) }}
              <i class="fas fa-times" @click="toggleRole(roleId)"></i>
            </span>
          </div>
        </div>

        <div class="selection-body">
          <div class="search-box">
            <input
              type="text"
              v-model="roleSearchQuery"
              placeholder="搜索角色..."
              class="search-input"
            >
          </div>
          <div class="selection-list">
            <div
              v-for="role in filteredRoles"
              :key="role.id"
              class="selection-item"
              :class="{ selected: selectedRoles.includes(role.id) }"
              @click="toggleRole(role.id)"
            >
              <div class="item-icon">
                <i class="fas fa-user-tag"></i>
              </div>
              <div class="item-info">
                <div class="item-name">{{ role.name }}</div>
                <div class="item-desc">{{ role.description }}</div>
              </div>
              <div class="item-check">
                <i class="fas fa-check" v-if="selectedRoles.includes(role.id)"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 部门选择 -->
      <div v-if="formData.permission === 'department'" class="selection-content">
        <div class="selection-header">
          <h3>选择部门</h3>
          <button class="close-btn" @click="closeSelectionPanel">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 已选择的部门列表 -->
        <div v-if="selectedDepartments.length > 0" class="selected-items">
          <div class="selected-header">
            <span class="selected-title">已选择部门 ({{ selectedDepartments.length }})</span>
            <button class="clear-all-btn" @click="clearAllDepartments">清空</button>
          </div>
          <div class="selected-tags">
            <span
              v-for="deptId in selectedDepartments"
              :key="deptId"
              class="selected-tag"
            >
              {{ getDepartmentName(deptId) }}
              <i class="fas fa-times" @click="toggleDepartment(deptId)"></i>
            </span>
          </div>
        </div>

        <div class="selection-body">
          <div class="search-box">
            <input
              type="text"
              v-model="deptSearchQuery"
              placeholder="搜索部门..."
              class="search-input"
            >
          </div>
          <div class="selection-list">
            <div
              v-for="dept in filteredDepartments"
              :key="dept.id"
              class="selection-item"
              :class="{ selected: selectedDepartments.includes(dept.id) }"
              @click="toggleDepartment(dept.id)"
            >
              <div class="item-icon">
                <i class="fas fa-building"></i>
              </div>
              <div class="item-info">
                <div class="item-name">{{ dept.name }}</div>
                <div class="item-desc">{{ dept.description }}</div>
              </div>
              <div class="item-check">
                <i class="fas fa-check" v-if="selectedDepartments.includes(dept.id)"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户选择 -->
      <div v-if="formData.permission === 'users'" class="selection-content">
        <div class="selection-header">
          <h3>选择用户</h3>
          <button class="close-btn" @click="closeSelectionPanel">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 用户选择限制提示 -->
        <div class="user-limit-notice">
          <div class="notice-content">
            <i class="fas fa-info-circle"></i>
            <span>最多可选择10位用户，超过建议使用角色管理</span>
          </div>
          <div class="user-count" :class="{ warning: selectedUsers.length >= 8, error: selectedUsers.length >= 10 }">
            {{ selectedUsers.length }}/10
          </div>
        </div>

        <!-- 已选择的用户列表 -->
        <div v-if="selectedUsers.length > 0" class="selected-items">
          <div class="selected-header">
            <span class="selected-title">已选择用户 ({{ selectedUsers.length }})</span>
            <button class="clear-all-btn" @click="clearAllUsers">清空</button>
          </div>
          <div class="selected-tags">
            <span
              v-for="userId in selectedUsers"
              :key="userId"
              class="selected-tag user-tag"
            >
              <div class="tag-avatar">{{ getUserName(userId).charAt(0) }}</div>
              <span>{{ getUserName(userId) }}</span>
              <i class="fas fa-times" @click="toggleUser(userId)"></i>
            </span>
          </div>
        </div>

        <div class="selection-body">
          <div class="search-box">
            <input
              type="text"
              v-model="userSearchQuery"
              placeholder="搜索用户..."
              class="search-input"
            >
          </div>
          <div class="selection-list">
            <div
              v-for="user in filteredUsers"
              :key="user.id"
              class="selection-item"
              :class="{
                selected: selectedUsers.includes(user.id),
                disabled: !selectedUsers.includes(user.id) && selectedUsers.length >= 10
              }"
              @click="toggleUser(user.id)"
            >
              <div class="item-avatar">{{ user.name.charAt(0) }}</div>
              <div class="item-info">
                <div class="item-name">{{ user.name }}</div>
                <div class="item-desc">{{ user.department }}</div>
              </div>
              <div class="item-check">
                <i class="fas fa-check" v-if="selectedUsers.includes(user.id)"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { RagAPI, type DatasetCreateDTO } from '@/api/rag'
import { VectorDatabaseAPI, type VectorDatabaseVO } from '@/api/vectorDatabase'
import { FileStorageAPI, type FileStorageConfigVO } from '@/api/fileStorage'
import { KnowledgeConfigAPI } from '@/api/knowledgeConfig'
import type { ApiResponse } from '@/types/api'
import type { VectorizationConfig } from '@/types/rag'
import { ElNotification } from 'element-plus'
import { AiModelAPI, type AiModel } from '@/api/model'
import {v4 as uuidv4} from "uuid";

// 路由
const router = useRouter()
const route = useRoute()

// 定义用户类型
interface User {
  id: number
  name: string
  department: string
}

// 表单数据
const formData = ref({
  name: '',
  description: '',
  permission: 'personal',
  selectedUsers: [] as User[]
})

// 向量化配置
const vectorizationConfig = ref<VectorizationConfig>({
  indexMode: 'high_quality',
  embeddingModel: 'text-embedding-ada-002',
  vectorDatabase: '',
  vectorDatabaseId: '',
  fileStorageId: '',
  retrievalSettings: {
    retrievalMode: 'hybrid',
    enableRerank: false,
    topK: 5,
    scoreThreshold: 0.7,
    rerankModel: 'bge-reranker-large',
    hybridWeights: {
      semanticWeight: 0.7,
      keywordWeight: 0.3
    }
  }
})

// 向量数据库相关
const enabledVectorDatabases = ref<VectorDatabaseVO[]>([])
const selectedVectorDatabase = computed(() => {
  return enabledVectorDatabases.value.find(db => db.id === vectorizationConfig.value.vectorDatabaseId)
})

// 文件存储相关
const enabledFileStorages = ref<FileStorageConfigVO[]>([])
const selectedFileStorage = computed(() => {
  return enabledFileStorages.value.find(storage => storage.id === vectorizationConfig.value.fileStorageId)
})

// 选择面板状态
const showSelectionPanel = ref(false)

// 搜索查询
const userSearchQuery = ref('')
const roleSearchQuery = ref('')
const deptSearchQuery = ref('')

// 选择的项目
const selectedUsers = ref<number[]>([])
const selectedRoles = ref<number[]>([])
const selectedDepartments = ref<number[]>([])

// 创建状态
const creating = ref(false)

// 模拟数据
const mockUsers = ref([
  { id: 1, name: '张三', department: '技术部' },
  { id: 2, name: '李四', department: '产品部' },
  { id: 3, name: '王五', department: '设计部' },
  { id: 4, name: '赵六', department: '运营部' },
  { id: 5, name: '钱七', department: '市场部' }
])

const mockRoles = ref([
  { id: 1, name: '管理员', description: '系统管理员角色，拥有所有权限' },
  { id: 2, name: '编辑者', description: '内容编辑角色，可以编辑和发布内容' },
  { id: 3, name: '审核员', description: '内容审核角色，负责内容审核' },
  { id: 4, name: '查看者', description: '只读角色，只能查看内容' }
])

const mockDepartments = ref([
  { id: 1, name: '技术部', description: '负责产品技术开发和维护' },
  { id: 2, name: '产品部', description: '负责产品规划和需求管理' },
  { id: 3, name: '设计部', description: '负责产品UI/UX设计' },
  { id: 4, name: '运营部', description: '负责产品运营和推广' },
  { id: 5, name: '市场部', description: '负责市场营销和商务合作' }
])

// 计算属性
const canCreate = computed(() => {
  const hasBasicInfo = formData.value.name.trim() && formData.value.permission

  if (!hasBasicInfo) return false

  // 检查权限相关的选择
  switch (formData.value.permission) {
    case 'role':
      return selectedRoles.value.length > 0
    case 'department':
      return selectedDepartments.value.length > 0
    case 'users':
      return selectedUsers.value.length > 0
    case 'personal':
    case 'public':
    default:
      return true
  }
})

// 过滤后的数据
const filteredUsers = computed(() => {
  if (!userSearchQuery.value.trim()) return mockUsers.value
  return mockUsers.value.filter(user =>
    user.name.includes(userSearchQuery.value) ||
    user.department.includes(userSearchQuery.value)
  )
})

const filteredRoles = computed(() => {
  if (!roleSearchQuery.value.trim()) return mockRoles.value
  return mockRoles.value.filter(role =>
    role.name.includes(roleSearchQuery.value) ||
    role.description.includes(roleSearchQuery.value)
  )
})

const filteredDepartments = computed(() => {
  if (!deptSearchQuery.value.trim()) return mockDepartments.value
  return mockDepartments.value.filter(dept =>
    dept.name.includes(deptSearchQuery.value) ||
    dept.description.includes(deptSearchQuery.value)
  )
})

// 是否有选择项
const hasSelections = computed(() => {
  return (formData.value.permission === 'role' && selectedRoles.value.length > 0) ||
         (formData.value.permission === 'department' && selectedDepartments.value.length > 0) ||
         (formData.value.permission === 'users' && selectedUsers.value.length > 0)
})

// 获取总选择数量
const getTotalSelectionCount = () => {
  switch (formData.value.permission) {
    case 'role':
      return selectedRoles.value.length
    case 'department':
      return selectedDepartments.value.length
    case 'users':
      return selectedUsers.value.length
    default:
      return 0
  }
}

// 方法
const goBack = () => {
  // 检查路由参数中是否有来源信息
  const from = route.query.from as string
  const projectId = route.query.projectId as string

  if (from === 'business-project' && projectId) {
    // 从业务项目页面来的，返回到业务项目页面
    router.push('/business-project')
  } else {
    // 默认返回到知识库管理页面
    router.push('/knowledge')
  }
}

const validateForm = () => {
  // 表单验证逻辑
}

// 权限变更处理
const handlePermissionChange = () => {
  const needsSelection = ['role', 'department', 'users'].includes(formData.value.permission)
  showSelectionPanel.value = needsSelection

  // 清空之前的选择
  if (formData.value.permission !== 'users') {
    selectedUsers.value = []
    userSearchQuery.value = ''
  }
  if (formData.value.permission !== 'role') {
    selectedRoles.value = []
    roleSearchQuery.value = ''
  }
  if (formData.value.permission !== 'department') {
    selectedDepartments.value = []
    deptSearchQuery.value = ''
  }

  validateForm()
}

// 关闭选择面板
const closeSelectionPanel = () => {
  showSelectionPanel.value = false
}

// 切换选择项
const toggleUser = (userId: number) => {
  const index = selectedUsers.value.indexOf(userId)
  if (index > -1) {
    selectedUsers.value.splice(index, 1)
  } else {
    // 检查是否超过10人限制
    if (selectedUsers.value.length >= 10) {
      return
    }
    selectedUsers.value.push(userId)
  }
}

const toggleRole = (roleId: number) => {
  const index = selectedRoles.value.indexOf(roleId)
  if (index > -1) {
    selectedRoles.value.splice(index, 1)
  } else {
    selectedRoles.value.push(roleId)
  }
}

const toggleDepartment = (deptId: number) => {
  const index = selectedDepartments.value.indexOf(deptId)
  if (index > -1) {
    selectedDepartments.value.splice(index, 1)
  } else {
    selectedDepartments.value.push(deptId)
  }
}

// 清空选择
const clearAllUsers = () => {
  selectedUsers.value = []
}

const clearAllRoles = () => {
  selectedRoles.value = []
}

const clearAllDepartments = () => {
  selectedDepartments.value = []
}

// 获取名称的辅助方法
const getUserName = (userId: number) => {
  const user = mockUsers.value.find(u => u.id === userId)
  return user ? user.name : ''
}

const getRoleName = (roleId: number) => {
  const role = mockRoles.value.find(r => r.id === roleId)
  return role ? role.name : ''
}

const getDepartmentName = (deptId: number) => {
  const dept = mockDepartments.value.find(d => d.id === deptId)
  return dept ? dept.name : ''
}

const getRoleDescription = (roleId: number) => {
  const role = mockRoles.value.find(r => r.id === roleId)
  return role ? role.description : ''
}

const getDepartmentDescription = (deptId: number) => {
  const dept = mockDepartments.value.find(d => d.id === deptId)
  return dept ? dept.description : ''
}

const getUserDepartment = (userId: number) => {
  const user = mockUsers.value.find(u => u.id === userId)
  return user ? user.department : ''
}

// 打开选择面板
const openSelectionPanel = () => {
  showSelectionPanel.value = true
}

// 清空当前选择
const clearCurrentSelection = () => {
  switch (formData.value.permission) {
    case 'role':
      clearAllRoles()
      break
    case 'department':
      clearAllDepartments()
      break
    case 'users':
      clearAllUsers()
      break
  }
}

// 权重更新方法
const updateKeywordWeight = () => {
  const semanticWeight = vectorizationConfig.value.retrievalSettings.hybridWeights.semanticWeight
  vectorizationConfig.value.retrievalSettings.hybridWeights.keywordWeight = Math.round((1 - semanticWeight) * 10) / 10
}

// 加载默认向量化配置
const loadDefaultVectorizationConfig = async () => {
  try {
    const response = await KnowledgeConfigAPI.getVectorizationConfig()
    if (response.success && response.data) {
      // 使用默认配置覆盖当前配置
      vectorizationConfig.value = {
        ...vectorizationConfig.value,
        indexMode: response.data.indexMode || 'high_quality',
        embeddingModel: response.data.embeddingModel || 'text-embedding-ada-002',
        vectorDatabaseId: response.data.vectorDatabaseId || '',
        fileStorageId: response.data.fileStorageId || '',
        retrievalSettings: {
          ...vectorizationConfig.value.retrievalSettings,
          ...response.data.retrievalSettings
        }
      }
    }
  } catch (error) {
    console.error('加载默认向量化配置失败:', error)
  }
}

// 加载启用的向量数据库列表
const loadEnabledVectorDatabases = async () => {
  try {
    const response = await VectorDatabaseAPI.getEnabledVectorDatabases()
    if (response.success && response.data) {
      enabledVectorDatabases.value = response.data

      // 如果没有选择向量数据库且有默认数据库，自动选择默认数据库
      if (!vectorizationConfig.value.vectorDatabaseId) {
        const defaultDb = enabledVectorDatabases.value.find(db => db.isDefault === 'Y')
        if (defaultDb) {
          vectorizationConfig.value.vectorDatabaseId = defaultDb.id
        }
      }
    }
  } catch (error) {
    console.error('加载向量数据库列表失败:', error)
  }
}

// 加载启用的文件存储列表
const loadEnabledFileStorages = async () => {
  try {
    const response = await FileStorageAPI.getFileStorageConfigList({ status: '0' })
    if (response.success && response.data) {
      enabledFileStorages.value = response.data

      // 如果没有选择文件存储且有默认存储，自动选择默认存储
      if (!vectorizationConfig.value.fileStorageId) {
        const defaultStorage = enabledFileStorages.value.find(storage => storage.isDefault === 'Y')
        if (defaultStorage) {
          vectorizationConfig.value.fileStorageId = defaultStorage.id
        }
      }
    }
  } catch (error) {
    console.error('加载文件存储列表失败:', error)
  }
}

// 刷新向量数据库列表
const refreshVectorDatabases = async () => {
  await loadEnabledVectorDatabases()
}

// 刷新文件存储列表
const refreshFileStorages = async () => {
  await loadEnabledFileStorages()
}

// 加载模型列表
const models = ref<Record<string, AiModel[]>>({})
const loadModels = async () => {
  try {
    const response = await AiModelAPI.getAiModelsByType('Embeddings,Reranker')
    if (response.success && response.data) {
      models.value = response.data.reduce( (acc, item) =>  {
        if (!acc[item.type]) {
          acc[item.type] = []
        }
        acc[item.type].push(item)
        return acc
      }, {} as Record<string, AiModel[]>)
    }
  } catch (error) {
    console.error('加载模型列表失败:', error)
  }
}

const createKnowledgeBase = async () => {
  if (!canCreate.value || creating.value) return

  creating.value = true

  try {
    // 构建创建请求数据
    const createDTO: DatasetCreateDTO = {
      name: formData.value.name,
      description: formData.value.description,
      dataSourceType: 'upload_file',
      vectorizationConfig: vectorizationConfig.value,
      vectorDatabaseId: vectorizationConfig.value.vectorDatabaseId,
      fileStorageId: vectorizationConfig.value.fileStorageId
    }

    // 调用API创建知识库
    const response: ApiResponse<any> = await RagAPI.createDataset(createDTO)

    if (response.success && response.data) {
      // 创建成功，保存权限数据
      await savePermissions(response.data.id)

      // 跳转到文档处理流程
      router.push({
        name: 'KnowledgeCreateFlow',
        query: {
          datasetId: response.data.id,
          name: response.data.name,
          batchId: uuidv4()
        }
      })
    } else {
      ElNotification({
        title: '创建知识库失败',
        message: response.message,
        type: 'error'
      })
    }
  } catch (error) {
    ElNotification({
      title: '创建知识库异常',
      message: "创建异常，请联系管理员",
      type: 'error'
    })
    console.error(error)
  } finally {
    creating.value = false
  }
}

const savePermissions = async (datasetId: string) => {
  try {
    // 根据权限类型保存权限数据
    if (formData.value.permission === 'personal') {
      // 个人权限：只有创建者可见，不需要额外保存权限记录
      console.log('个人权限，无需额外保存权限记录')
    } else if (formData.value.permission === 'partial') {
      // 部分人可见：保存选中的用户权限
      const permissionPromises = formData.value.selectedUsers.map(user => {
        const permissionData = {
          datasetId: datasetId,
          objectType: 'account',
          objectId: user.id.toString(),
          permissionType: '2' // 读写权限
        }
        // 这里应该调用权限保存API，暂时用console.log代替
        console.log('保存用户权限:', permissionData)
        return Promise.resolve(permissionData)
      })

      await Promise.all(permissionPromises)
      console.log('用户权限保存完成')
    } else if (formData.value.permission === 'public') {
      // 公开权限：所有人可见，可以保存一个特殊的权限记录
      const publicPermissionData = {
        datasetId: datasetId,
        objectType: 'public',
        objectId: 'all',
        permissionType: '1' // 只读权限
      }
      console.log('保存公开权限:', publicPermissionData)
    }
  } catch (error) {
    console.error('保存权限失败:', error)
    throw error
  }
}

onMounted(async () => {
  // 加载默认向量化配置
  await loadDefaultVectorizationConfig()
  // 加载模型列表
  await loadModels()
  // 加载向量数据库列表
  await loadEnabledVectorDatabases()
  // 加载文件存储列表
  await loadEnabledFileStorages()
})
</script>

<style scoped>

.header-content {
  max-width: 100vw;
  margin: 0 auto;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 右侧权限区域 */
.form-title i {
  color: #3b82f6;
  font-size: 24px;
}

.form-title h2 {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.form-subtitle {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.6;
  margin: 0;
}

/* 表单组件样式 */
.form-group {
  margin-bottom: 32px;
}

.form-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: ' *';
  color: #ef4444;
}

.form-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
}

.form-textarea {
  width: 100%;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: white;
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
  line-height: 1.6;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea::placeholder {
  color: #9ca3af;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.input-hint {
  font-size: 14px;
  color: #6b7280;
}

.char-count {
  font-size: 14px;
  color: #9ca3af;
  font-weight: 500;
}

/* 通用按钮样式 */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-create-header {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  font-weight: 600;
}

.btn-create-header:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.btn-create-header:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 选择信息显示区域 */
.selection-summary {
  margin-top: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.summary-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.summary-title i {
  color: #10b981;
  font-size: 18px;
}

.summary-count {
  font-size: 14px;
  color: #6b7280;
  background: white;
  padding: 4px 12px;
  border-radius: 20px;
  border: 1px solid #d1d5db;
}

.summary-section {
  margin-bottom: 20px;
}

.summary-section:last-of-type {
  margin-bottom: 0;
}

.summary-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.summary-label i {
  color: #6b7280;
}

/* 简洁宫格布局 */
.summary-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 选择信息显示项目 */
.summary-item {
  position: relative;
  padding: 8px 12px;
  background: #f1f5f9;
  border-radius: 8px;
  transition: all 0.2s ease;
  min-width: 120px;
  max-width: 250px;
}

.summary-item:hover {
  background: #e2e8f0;
}

/* 角色和部门项目样式 */
.role-item,
.dept-item {
  padding-right: 28px; /* 为删除按钮留出空间 */
}

/* 用户项目样式 */
.user-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.item-text {
  position: relative;
  flex: 1;
  padding-right: 20px; /* 为删除按钮留出空间 */
}

/* 删除按钮 */
.item-remove-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 16px;
  height: 16px;
  border: none;
  background: #cbd5e1;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 9px;
  opacity: 0.8;
}

.item-remove-btn:hover {
  background: #ef4444;
  opacity: 1;
}

/* 用户头像 */
.item-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 2px;
}

/* 项目内容 */
.item-name {
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  margin-bottom: 2px;
}

.item-desc {
  font-size: 11px;
  color: #64748b;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

.summary-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.btn-outline {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #1f2937;
}

.btn-outline-danger {
  padding: 8px 16px;
  background: white;
  border: 1px solid #fca5a5;
  border-radius: 6px;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-outline-danger:hover {
  background: #fef2f2;
  border-color: #f87171;
  color: #b91c1c;
}


.step-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.step-content p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* 功能特性 */
.feature-item i {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  color: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.feature-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.feature-info p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* 最佳实践 */
.practice-item i {
  color: #10b981;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-label.required::after {
  content: ' *';
  color: #ef4444;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.char-count {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
  margin-top: 4px;
}

/* 权限选项 - 宫格布局 */
.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.permission-card {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  overflow: hidden;
  padding: 2px;
}

.permission-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: all 0.2s ease;
  min-height: 140px;
  justify-content: center;
}

.permission-card:hover .card-content {
  border-color: #3b82f6;
  background: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.permission-card.active .card-content {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.permission-card.active .card-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.card-icon i {
  font-size: 20px;
  color: #6b7280;
}

.permission-card.active .card-icon i {
  color: white;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

/* 用户选择器 */
.user-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #eff6ff;
  color: #1d4ed8;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #bfdbfe;
}

.user-tag i {
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.user-tag i:hover {
  opacity: 1;
}

/* 拉出面板遮罩 */
.slide-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* 右侧拉出面板 */
.slide-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 480px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.slide-panel.slide-panel-open {
  transform: translateX(0);
}

.selection-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.selection-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.selection-body {
  flex: 1;
  padding: 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-box {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.selection-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selection-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selection-item:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.selection-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

.item-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  flex-shrink: 0;
}

.selection-item.selected .item-icon {
  background: #3b82f6;
  color: white;
}

.item-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-weight: 600;
  flex-shrink: 0;
}

.selection-item.selected .item-avatar {
  background: #3b82f6;
  color: white;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.item-desc {
  font-size: 12px;
  color: #6b7280;
}

.item-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.selection-item.selected .item-check {
  opacity: 1;
}

.selection-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.selection-item.disabled:hover {
  border-color: #e5e7eb;
  background: white;
}

/* 用户限制提示 */
.user-limit-notice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin: 0 24px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #0369a1;
  font-size: 14px;
}

.notice-content i {
  color: #0284c7;
}

.user-count {
  font-size: 14px;
  font-weight: 600;
  color: #0369a1;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #bae6fd;
}

.user-count.warning {
  color: #d97706;
  background: #fef3c7;
  border-color: #fcd34d;
}

.user-count.error {
  color: #dc2626;
  background: #fee2e2;
  border-color: #fca5a5;
}

/* 已选择项目列表 */
.selected-items {
  margin: 0 24px 16px;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.selected-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.clear-all-btn {
  padding: 4px 8px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: #eff6ff;
  color: #1d4ed8;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #bfdbfe;
  transition: all 0.2s ease;
}

.selected-tag:hover {
  background: #dbeafe;
}

.selected-tag i {
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.selected-tag i:hover {
  opacity: 1;
}

.selected-tag.user-tag {
  padding: 4px 8px;
}

.tag-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
}

/* 向量化配置区域样式 */
.config-section {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 12px;
  margin-bottom: 16px;
}

.config-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.config-title i {
  color: #3b82f6;
}

.config-group {
  margin-bottom: 16px;
}

.config-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.input-with-slider {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.config-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.config-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.config-hint {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

.radio-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.radio-option input[type="radio"] {
  margin-right: 8px;
  margin-top: 2px;
}

.radio-text {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #374151;
}

.option-desc {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.config-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.config-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.config-checkbox {
  margin-right: 8px;
  margin-top: 2px;
}

.weight-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.weight-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weight-label {
  font-size: 13px;
  font-weight: 500;
  color: #475569;
}


@media (max-width: 1024px) {
  .header-content {
    padding: 16px 24px;
  }
  .slide-panel {
    width: 400px;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-left {
    width: 100%;
    justify-content: space-between;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .form-group {
    margin-bottom: 24px;
  }

  .back-btn span {
    display: none;
  }

  .permission-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
  }

  .card-content {
    padding: 20px 12px;
    min-height: 90px;
  }

  .card-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-desc {
    font-size: 11px;
  }

  .slide-panel {
    width: 350px;
  }

  .selection-header {
    padding: 20px;
  }

  .selection-body {
    padding: 20px;
  }

  .selection-summary {
    margin-top: 24px;
    padding: 20px;
  }

  .summary-actions {
    flex-direction: column;
    gap: 8px;
  }

  .btn-outline,
  .btn-outline-danger {
    width: 100%;
    justify-content: center;
  }

  .summary-grid {
    gap: 6px;
  }

  .summary-item {
    min-width: 100px;
    max-width: 90px;
    padding: 6px 10px;
  }

  .role-item,
  .dept-item {
    padding-right: 24px;
  }

  .item-text {
    padding-right: 18px;
  }
}

@media (max-width: 480px) {
  .form-title h2 {
    font-size: 20px;
  }

  .form-input,
  .form-textarea {
    padding: 12px;
    font-size: 16px;
  }

  .slide-panel {
    width: 100vw;
  }

  .permission-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .card-content {
    padding: 16px 12px;
    min-height: 100px;
  }

  .selected-items {
    margin: 0 16px 12px;
    padding: 12px;
  }

  .user-limit-notice {
    margin: 0 16px 12px;
    padding: 10px 12px;
  }

  .selected-tags {
    gap: 6px;
  }

  .selected-tag {
    font-size: 11px;
    padding: 4px 6px;
  }

  .selection-summary {
    margin-top: 20px;
    padding: 16px;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 16px;
  }

  .summary-grid {
    gap: 6px;
  }

  .summary-item {
    min-width: 90px;
    max-width: 160px;
    padding: 6px 8px;
  }

  .role-item,
  .dept-item {
    padding-right: 22px;
  }

  .item-text {
    padding-right: 16px;
  }

  .item-avatar {
    width: 18px;
    height: 18px;
    font-size: 9px;
  }

  .item-name {
    font-size: 12px;
  }

  .item-desc {
    font-size: 10px;
  }

  .item-remove-btn {
    width: 14px;
    height: 14px;
    font-size: 8px;
    top: 4px;
    right: 4px;
  }
}

@media (max-width: 480px) {
  .summary-grid {
    gap: 4px;
  }

  .summary-item {
    min-width: 80px;
    max-width: 140px;
    padding: 6px 8px;
  }

  .role-item,
  .dept-item {
    padding-right: 20px;
  }

  .item-text {
    padding-right: 14px;
  }

  .item-avatar {
    width: 16px;
    height: 16px;
    font-size: 8px;
  }

  .item-name {
    font-size: 11px;
  }

  .item-desc {
    font-size: 9px;
  }

  .item-remove-btn {
    width: 12px;
    height: 12px;
    font-size: 7px;
    top: 4px;
    right: 4px;
  }
}
</style>
