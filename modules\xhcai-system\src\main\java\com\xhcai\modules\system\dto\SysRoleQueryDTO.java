package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

/**
 * 角色查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "角色查询DTO")
public class SysRoleQueryDTO extends PageQueryDTO {

    /**
     * 角色编码
     */
    @Schema(description = "角色编码", example = "admin")
    private String roleCode;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称", example = "管理员")
    private String roleName;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    /**
     * 数据范围
     */
    @Schema(description = "数据范围", example = "1", allowableValues = {"1", "2", "3", "4", "5"})
    @Pattern(regexp = "^[12345]$", message = "数据范围值必须为1-5")
    private String dataScope;

    // Getters and Setters
    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    @Override
    public String toString() {
        return "SysRoleQueryDTO{"
                + "roleCode='" + roleCode + '\''
                + ", roleName='" + roleName + '\''
                + ", status='" + status + '\''
                + ", dataScope='" + dataScope + '\''
                + ", current=" + getCurrent()
                + ", size=" + getSize()
                + ", orderBy='" + getOrderBy() + '\''
                + ", orderDirection='" + getOrderDirection() + '\''
                + '}';
    }
}
