/**
 * SSE (Server-Sent Events) 客户端
 * 支持token认证、自动重连、事件处理等功能
 */

import { envConfig, logger } from '@/config/env'
import { useAuthStore } from '@/stores/authStore'
import { SSEConnectionState } from '@/types/api'
import type {
  SSEConfig,
  SSEEvent,
  SSEEventHandler,
  SSEErrorHandler,
  SSEOpenHandler,
  SSECloseHandler
} from '@/types/api'

export class SSEClient {
  private eventSource: EventSource | null = null
  private config: SSEConfig
  private state: SSEConnectionState = SSEConnectionState.CLOSED
  private reconnectAttempts = 0
  private reconnectTimer: number | null = null
  private eventHandlers: Map<string, SSEEventHandler[]> = new Map()
  private errorHandlers: SSEErrorHandler[] = []
  private openHandlers: SSEOpenHandler[] = []
  private closeHandlers: SSECloseHandler[] = []

  constructor(config: SSEConfig) {
    this.config = {
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      withCredentials: false,
      ...config
    }
  }

  /**
   * 建立SSE连接
   */
  connect(): void {
    if (this.eventSource && this.state !== SSEConnectionState.CLOSED) {
      logger.warn('SSE connection already exists')
      return
    }

    try {
      this.state = SSEConnectionState.CONNECTING
      
      // 构建完整URL
      const url = this.buildURL()
      
      // 创建EventSource
      this.eventSource = new EventSource(url, {
        withCredentials: this.config.withCredentials
      })

      // 设置事件监听器
      this.setupEventListeners()

      logger.info('SSE connection initiated:', url)
    } catch (error) {
      logger.error('Failed to create SSE connection:', error)
      this.handleError(error as Event)
    }
  }

  /**
   * 关闭SSE连接
   */
  close(): void {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
    
    this.state = SSEConnectionState.CLOSED
    this.clearReconnectTimer()
    
    // 触发关闭事件
    this.closeHandlers.forEach(handler => {
      try {
        handler(new Event('close'))
      } catch (error) {
        logger.error('Error in close handler:', error)
      }
    })

    logger.info('SSE connection closed')
  }

  /**
   * 获取连接状态
   */
  getState(): SSEConnectionState {
    return this.state
  }

  /**
   * 是否已连接
   */
  isConnected(): boolean {
    return this.state === SSEConnectionState.OPEN
  }

  /**
   * 添加事件处理器
   */
  addEventListener(eventType: string, handler: SSEEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, [])
    }
    this.eventHandlers.get(eventType)!.push(handler)
  }

  /**
   * 移除事件处理器
   */
  removeEventListener(eventType: string, handler: SSEEventHandler): void {
    const handlers = this.eventHandlers.get(eventType)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 添加错误处理器
   */
  onError(handler: SSEErrorHandler): void {
    this.errorHandlers.push(handler)
  }

  /**
   * 添加连接打开处理器
   */
  onOpen(handler: SSEOpenHandler): void {
    this.openHandlers.push(handler)
  }

  /**
   * 添加连接关闭处理器
   */
  onClose(handler: SSECloseHandler): void {
    this.closeHandlers.push(handler)
  }

  /**
   * 构建带认证参数的URL
   */
  private buildURL(): string {
    const baseURL = this.config.url.startsWith('http') 
      ? this.config.url 
      : `${envConfig.apiBaseUrl}${this.config.url}`

    // 如果跳过认证，直接返回URL
    if (this.config.skipAuth) {
      return baseURL
    }

    // 添加认证参数到URL查询字符串中（因为EventSource不支持自定义headers）
    const authStore = useAuthStore()
    if (authStore.isLoggedIn && authStore.tokens) {
      const url = new URL(baseURL)
      
      if (authStore.tokens.token) {
        url.searchParams.set('token', authStore.tokens.token)
      }
      
      return url.toString()
    }

    return baseURL
  }

  /**
   * 设置EventSource事件监听器
   */
  private setupEventListeners(): void {
    if (!this.eventSource) return

    // 连接打开
    this.eventSource.onopen = (event) => {
      this.state = SSEConnectionState.OPEN
      this.reconnectAttempts = 0
      logger.info('SSE connection opened')
      
      this.openHandlers.forEach(handler => {
        try {
          handler(event)
        } catch (error) {
          logger.error('Error in open handler:', error)
        }
      })
    }

    // 接收消息
    this.eventSource.onmessage = (event) => {
      this.handleMessage(event)
    }

    // 连接错误
    this.eventSource.onerror = (event) => {
      this.handleError(event)
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const sseEvent: SSEEvent = {
        type: event.type || 'message',
        data: this.parseEventData(event.data),
        id: event.lastEventId
      }

      logger.debug('SSE message received:', sseEvent)

      // 触发对应类型的事件处理器
      const handlers = this.eventHandlers.get(sseEvent.type) || []
      handlers.forEach(handler => {
        try {
          handler(sseEvent)
        } catch (error) {
          logger.error('Error in event handler:', error)
        }
      })

      // 触发通用消息处理器
      const messageHandlers = this.eventHandlers.get('message') || []
      if (sseEvent.type !== 'message') {
        messageHandlers.forEach(handler => {
          try {
            handler(sseEvent)
          } catch (error) {
            logger.error('Error in message handler:', error)
          }
        })
      }
    } catch (error) {
      logger.error('Error handling SSE message:', error)
    }
  }

  /**
   * 解析事件数据
   */
  private parseEventData(data: string): any {
    if (!data) return null

    try {
      return JSON.parse(data)
    } catch {
      return data
    }
  }

  /**
   * 处理连接错误
   */
  private handleError(event: Event): void {
    this.state = SSEConnectionState.ERROR
    logger.error('SSE connection error:', event)

    // 触发错误处理器
    this.errorHandlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        logger.error('Error in error handler:', error)
      }
    })

    // 尝试重连
    if (this.config.reconnect && this.shouldReconnect()) {
      this.scheduleReconnect()
    }
  }

  /**
   * 是否应该重连
   */
  private shouldReconnect(): boolean {
    return this.reconnectAttempts < (this.config.maxReconnectAttempts || 5)
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.clearReconnectTimer()
    
    const delay = this.config.reconnectInterval || 3000
    logger.info(`Scheduling SSE reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1})`)
    
    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectAttempts++
      this.connect()
    }, delay)
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
}

export default SSEClient
