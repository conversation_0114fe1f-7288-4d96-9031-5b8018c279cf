package com.xhcai.modules.dify.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.vo.ThirdPlatformAccountVO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountCreateDTO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountQueryDTO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountUpdateDTO;
import com.xhcai.modules.dify.entity.ThirdPlatformAccount;

/**
 * 用户第三方智能体账号服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IThirdPlatformAccountService extends IService<ThirdPlatformAccount> {

    /**
     * 分页查询用户第三方智能体账号
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ThirdPlatformAccountVO> selectPageVO(ThirdPlatformAccountQueryDTO query);

    /**
     * 根据ID查询用户第三方智能体账号详情
     *
     * @param id 主键ID
     * @return 账号详情
     */
    ThirdPlatformAccountVO selectVOById(String id);

    /**
     * 创建用户第三方智能体账号
     *
     * @param createDTO 创建参数
     * @return 创建结果
     */
    Result<String> createAccount(ThirdPlatformAccountCreateDTO createDTO);

    /**
     * 更新用户第三方智能体账号
     *
     * @param id 主键ID
     * @param updateDTO 更新参数
     * @return 更新结果
     */
    Result<Void> updateAccount(String id, ThirdPlatformAccountUpdateDTO updateDTO);

    /**
     * 删除用户第三方智能体账号
     *
     * @param id 主键ID
     * @return 删除结果
     */
    Result<Void> deleteAccount(String id);

    /**
     * 测试连接
     *
     * @param id 主键ID
     * @return 测试结果
     */
    Result<String> testConnection(String id);

    /**
     * 查询用户的所有第三方账号（用于统计）
     *
     * @return 账号列表
     */
    List<ThirdPlatformAccountVO> selectByCurrentUser();

    /**
     * 检查用户在指定平台是否已有账号
     *
     * @param platformId 平台ID
     * @return 是否已有账号
     */
    boolean hasAccountInPlatform(String platformId);

    /**
     * 更新账号使用统计
     *
     * @param id 账号ID
     * @param success 是否成功
     * @param responseTime 响应时间（毫秒）
     */
    void updateUsageStats(String id, boolean success, long responseTime);

    /**
     * 验证密码
     *
     * @param id 账号ID
     * @param rawPassword 原始密码
     * @return 验证结果
     */
    boolean verifyPassword(String id, String rawPassword);
}
