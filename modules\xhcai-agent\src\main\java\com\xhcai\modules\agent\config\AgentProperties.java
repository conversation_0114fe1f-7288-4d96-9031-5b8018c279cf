package com.xhcai.modules.agent.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 智能体模块配置属性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = "agent")
public class AgentProperties {

    /**
     * 默认配置
     */
    private Default defaultConfig = new Default();

    /**
     * 限制配置
     */
    private Limits limits = new Limits();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 监控配置
     */
    private Monitoring monitoring = new Monitoring();

    /**
     * 默认配置
     */
    public static class Default {
        /**
         * 模型配置
         */
        private Model model = new Model();

        /**
         * 系统提示词
         */
        private String systemPrompt = "你是一个有用的AI助手，请根据用户的问题提供准确、有帮助的回答。";

        /**
         * 对话配置
         */
        private Conversation conversation = new Conversation();

        /**
         * 工具配置
         */
        private Tools tools = new Tools();

        public static class Model {
            private String name = "gpt-3.5-turbo";
            private Double temperature = 0.7;
            private Integer maxTokens = 2000;
            private Double topP = 1.0;
            private Double frequencyPenalty = 0.0;
            private Double presencePenalty = 0.0;

            // Getters and Setters
            public String getName() { return name; }
            public void setName(String name) { this.name = name; }
            public Double getTemperature() { return temperature; }
            public void setTemperature(Double temperature) { this.temperature = temperature; }
            public Integer getMaxTokens() { return maxTokens; }
            public void setMaxTokens(Integer maxTokens) { this.maxTokens = maxTokens; }
            public Double getTopP() { return topP; }
            public void setTopP(Double topP) { this.topP = topP; }
            public Double getFrequencyPenalty() { return frequencyPenalty; }
            public void setFrequencyPenalty(Double frequencyPenalty) { this.frequencyPenalty = frequencyPenalty; }
            public Double getPresencePenalty() { return presencePenalty; }
            public void setPresencePenalty(Double presencePenalty) { this.presencePenalty = presencePenalty; }
        }

        public static class Conversation {
            private Integer maxHistory = 20;
            private Long timeout = 30000L;
            private Boolean autoCleanup = true;
            private Long cleanupInterval = 3600000L;

            // Getters and Setters
            public Integer getMaxHistory() { return maxHistory; }
            public void setMaxHistory(Integer maxHistory) { this.maxHistory = maxHistory; }
            public Long getTimeout() { return timeout; }
            public void setTimeout(Long timeout) { this.timeout = timeout; }
            public Boolean getAutoCleanup() { return autoCleanup; }
            public void setAutoCleanup(Boolean autoCleanup) { this.autoCleanup = autoCleanup; }
            public Long getCleanupInterval() { return cleanupInterval; }
            public void setCleanupInterval(Long cleanupInterval) { this.cleanupInterval = cleanupInterval; }
        }

        public static class Tools {
            private Boolean enabled = true;
            private Long timeout = 10000L;
            private Integer maxRetries = 3;

            // Getters and Setters
            public Boolean getEnabled() { return enabled; }
            public void setEnabled(Boolean enabled) { this.enabled = enabled; }
            public Long getTimeout() { return timeout; }
            public void setTimeout(Long timeout) { this.timeout = timeout; }
            public Integer getMaxRetries() { return maxRetries; }
            public void setMaxRetries(Integer maxRetries) { this.maxRetries = maxRetries; }
        }

        // Getters and Setters
        public Model getModel() { return model; }
        public void setModel(Model model) { this.model = model; }
        public String getSystemPrompt() { return systemPrompt; }
        public void setSystemPrompt(String systemPrompt) { this.systemPrompt = systemPrompt; }
        public Conversation getConversation() { return conversation; }
        public void setConversation(Conversation conversation) { this.conversation = conversation; }
        public Tools getTools() { return tools; }
        public void setTools(Tools tools) { this.tools = tools; }
    }

    /**
     * 限制配置
     */
    public static class Limits {
        private Integer maxAgentsPerTenant = 100;
        private Integer maxAgentsPerUser = 20;
        private Integer maxConversationsPerUser = 50;
        private Integer maxMessagesPerConversation = 1000;
        private Integer maxMessageLength = 10000;
        private Integer maxTokensPerRequest = 4000;
        private Long maxTokensPerDay = 100000L;
        private Long maxFileSize = 10485760L; // 10MB
        private Integer maxFilesPerMessage = 5;

        // Getters and Setters
        public Integer getMaxAgentsPerTenant() { return maxAgentsPerTenant; }
        public void setMaxAgentsPerTenant(Integer maxAgentsPerTenant) { this.maxAgentsPerTenant = maxAgentsPerTenant; }
        public Integer getMaxAgentsPerUser() { return maxAgentsPerUser; }
        public void setMaxAgentsPerUser(Integer maxAgentsPerUser) { this.maxAgentsPerUser = maxAgentsPerUser; }
        public Integer getMaxConversationsPerUser() { return maxConversationsPerUser; }
        public void setMaxConversationsPerUser(Integer maxConversationsPerUser) { this.maxConversationsPerUser = maxConversationsPerUser; }
        public Integer getMaxMessagesPerConversation() { return maxMessagesPerConversation; }
        public void setMaxMessagesPerConversation(Integer maxMessagesPerConversation) { this.maxMessagesPerConversation = maxMessagesPerConversation; }
        public Integer getMaxMessageLength() { return maxMessageLength; }
        public void setMaxMessageLength(Integer maxMessageLength) { this.maxMessageLength = maxMessageLength; }
        public Integer getMaxTokensPerRequest() { return maxTokensPerRequest; }
        public void setMaxTokensPerRequest(Integer maxTokensPerRequest) { this.maxTokensPerRequest = maxTokensPerRequest; }
        public Long getMaxTokensPerDay() { return maxTokensPerDay; }
        public void setMaxTokensPerDay(Long maxTokensPerDay) { this.maxTokensPerDay = maxTokensPerDay; }
        public Long getMaxFileSize() { return maxFileSize; }
        public void setMaxFileSize(Long maxFileSize) { this.maxFileSize = maxFileSize; }
        public Integer getMaxFilesPerMessage() { return maxFilesPerMessage; }
        public void setMaxFilesPerMessage(Integer maxFilesPerMessage) { this.maxFilesPerMessage = maxFilesPerMessage; }
    }

    /**
     * 缓存配置
     */
    public static class Cache {
        private CacheConfig agentConfig = new CacheConfig(true, 3600L, 1000);
        private CacheConfig conversationHistory = new CacheConfig(true, 1800L, 500);

        public static class CacheConfig {
            private Boolean enabled;
            private Long ttl;
            private Integer maxSize;

            public CacheConfig() {}

            public CacheConfig(Boolean enabled, Long ttl, Integer maxSize) {
                this.enabled = enabled;
                this.ttl = ttl;
                this.maxSize = maxSize;
            }

            // Getters and Setters
            public Boolean getEnabled() { return enabled; }
            public void setEnabled(Boolean enabled) { this.enabled = enabled; }
            public Long getTtl() { return ttl; }
            public void setTtl(Long ttl) { this.ttl = ttl; }
            public Integer getMaxSize() { return maxSize; }
            public void setMaxSize(Integer maxSize) { this.maxSize = maxSize; }
        }

        // Getters and Setters
        public CacheConfig getAgentConfig() { return agentConfig; }
        public void setAgentConfig(CacheConfig agentConfig) { this.agentConfig = agentConfig; }
        public CacheConfig getConversationHistory() { return conversationHistory; }
        public void setConversationHistory(CacheConfig conversationHistory) { this.conversationHistory = conversationHistory; }
    }

    /**
     * 监控配置
     */
    public static class Monitoring {
        private Performance performance = new Performance();
        private Usage usage = new Usage();

        public static class Performance {
            private Boolean enabled = true;
            private Long slowQueryThreshold = 5000L;

            // Getters and Setters
            public Boolean getEnabled() { return enabled; }
            public void setEnabled(Boolean enabled) { this.enabled = enabled; }
            public Long getSlowQueryThreshold() { return slowQueryThreshold; }
            public void setSlowQueryThreshold(Long slowQueryThreshold) { this.slowQueryThreshold = slowQueryThreshold; }
        }

        public static class Usage {
            private Boolean enabled = true;
            private Long reportInterval = 300000L;

            // Getters and Setters
            public Boolean getEnabled() { return enabled; }
            public void setEnabled(Boolean enabled) { this.enabled = enabled; }
            public Long getReportInterval() { return reportInterval; }
            public void setReportInterval(Long reportInterval) { this.reportInterval = reportInterval; }
        }

        // Getters and Setters
        public Performance getPerformance() { return performance; }
        public void setPerformance(Performance performance) { this.performance = performance; }
        public Usage getUsage() { return usage; }
        public void setUsage(Usage usage) { this.usage = usage; }
    }

    // Getters and Setters
    public Default getDefaultConfig() { return defaultConfig; }
    public void setDefaultConfig(Default defaultConfig) { this.defaultConfig = defaultConfig; }
    public Limits getLimits() { return limits; }
    public void setLimits(Limits limits) { this.limits = limits; }
    public Cache getCache() { return cache; }
    public void setCache(Cache cache) { this.cache = cache; }
    public Monitoring getMonitoring() { return monitoring; }
    public void setMonitoring(Monitoring monitoring) { this.monitoring = monitoring; }
}
