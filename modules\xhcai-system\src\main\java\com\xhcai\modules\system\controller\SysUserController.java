package com.xhcai.modules.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.annotation.DataScope;
import com.xhcai.modules.system.dto.SysUserQueryDTO;
import com.xhcai.modules.system.dto.UserProfileUpdateDTO;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.service.ISysUserService;
import com.xhcai.modules.system.vo.SysUserVO;
import com.xhcai.modules.system.vo.UserProfileVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "用户管理", description = "用户管理相关接口")
@RestController
@RequestMapping("/api/system/user")
public class SysUserController {

    @Autowired
    private ISysUserService userService;

    /**
     * 分页查询用户列表
     */
    @Operation(summary = "分页查询用户列表")
    @GetMapping("/page")
    @RequiresPermissions("system:user:view")
    @DataScope(deptAlias = "d", userAlias = "u")
    public Result<PageResult<SysUserVO>> page(@Valid SysUserQueryDTO queryDTO) {
        PageResult<SysUserVO> pageResult = userService.selectUserPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询用户详情
     */
    @Operation(summary = "根据ID查询用户详情")
    @GetMapping("/{id}")
    @RequiresPermissions("system:user:view")
    public Result<SysUser> getById(@Parameter(description = "用户ID") @PathVariable String id) {
        SysUser user = userService.getById(id);
        return Result.success(user);
    }

    /**
     * 新增用户
     */
    @Operation(summary = "新增用户")
    @PostMapping
    @RequiresPermissions("system:user:add")
    public Result<Void> add(@Validated @RequestBody SysUser user) {
        boolean success = userService.insertUser(user);
        return Result.status(success);
    }

    /**
     * 修改用户
     */
    @Operation(summary = "修改用户")
    @PutMapping
    @RequiresPermissions("system:user:edit")
    public Result<Void> edit(@Validated @RequestBody SysUser user) {
        boolean success = userService.updateUser(user);
        return Result.status(success);
    }

    /**
     * 删除用户
     */
    @Operation(summary = "删除用户")
    @DeleteMapping("/{ids}")
    @RequiresPermissions("system:user:remove")
    public Result<Void> remove(@Parameter(description = "用户ID列表，多个用逗号分隔") @PathVariable String ids) {
        List<String> userIds = List.of(ids.split(","));
        boolean success = userService.deleteUsers(userIds);
        return Result.status(success);
    }

    /**
     * 重置用户密码
     */
    @Operation(summary = "重置用户密码")
    @PutMapping("/resetPassword")
    @RequiresPermissions("system:user:resetPwd")
    public Result<Void> resetPassword(@RequestBody ResetPasswordRequest request) {
        boolean success = userService.resetPassword(request.getUserId(), request.getNewPassword());
        return Result.status(success);
    }

    /**
     * 修改用户状态
     */
    @Operation(summary = "修改用户状态")
    @PutMapping("/changeStatus")
    @RequiresPermissions("system:user:edit")
    public Result<Void> changeStatus(@RequestBody ChangeStatusRequest request) {
        boolean success = userService.changeStatus(request.getUserId(), request.getStatus());
        return Result.status(success);
    }

    /**
     * 分配用户角色
     */
    @Operation(summary = "分配用户角色")
    @PutMapping("/assignRoles")
    @RequiresPermissions("system:user:edit")
    public Result<Void> assignRoles(@RequestBody AssignRolesRequest request) {
        boolean success = userService.assignRoles(request.getUserId(), request.getRoleIds());
        return Result.status(success);
    }

    /**
     * 检查用户名是否存在
     */
    @Operation(summary = "检查用户名是否存在")
    @GetMapping("/checkUsername")
    @RequiresPermissions("system:user:view")
    public Result<Boolean> checkUsername(@RequestParam String username, @RequestParam(required = false) String userId) {
        boolean exists = userService.existsUsername(username, userId);
        return Result.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @Operation(summary = "检查邮箱是否存在")
    @GetMapping("/checkEmail")
    @RequiresPermissions("system:user:view")
    public Result<Boolean> checkEmail(@RequestParam String email, @RequestParam(required = false) String userId) {
        boolean exists = userService.existsEmail(email, userId);
        return Result.success(exists);
    }

    /**
     * 检查手机号是否存在
     */
    @Operation(summary = "检查手机号是否存在")
    @GetMapping("/checkPhone")
    @RequiresPermissions("system:user:view")
    public Result<Boolean> checkPhone(@RequestParam String phone, @RequestParam(required = false) String userId) {
        boolean exists = userService.existsPhone(phone, userId);
        return Result.success(exists);
    }

    /**
     * 导出用户数据
     */
    @Operation(summary = "导出用户数据")
    @PostMapping("/export")
    @RequiresPermissions("system:user:export")
    public Result<List<SysUserVO>> export(@RequestBody SysUserQueryDTO queryDTO) {
        List<SysUserVO> userList = userService.exportUsers(queryDTO);
        return Result.success(userList);
    }

    /**
     * 获取用户角色列表
     */
    @Operation(summary = "获取用户角色", description = "根据用户ID获取角色列表")
    @GetMapping("/{id}/roles")
    @RequiresPermissions("system:user:view")
    public Result<List<Object>> getUserRoles(
            @Parameter(description = "用户ID", required = true) @PathVariable String id) {
        // 这里需要调用角色服务获取用户角色
        // 暂时返回空列表，等角色服务完善后再实现
        return Result.success(List.of());
    }

    /**
     * 为用户分配角色
     */
    @Operation(summary = "分配用户角色", description = "为用户分配角色")
    @PutMapping("/{id}/roles")
    @RequiresPermissions("system:user:edit")
    public Result<Void> assignUserRoles(
            @Parameter(description = "用户ID", required = true) @PathVariable String id,
            @RequestBody List<String> roleIds) {
        boolean result = userService.assignUserRoles(id, roleIds, SecurityUtils.getCurrentTenantId());
        return result ? Result.success() : Result.fail("分配角色失败");
    }

    /**
     * 获取用户权限列表
     */
    @Operation(summary = "获取用户权限", description = "根据用户ID获取权限列表")
    @GetMapping("/{id}/permissions")
    @RequiresPermissions("system:user:view")
    public Result<Object> getUserPermissions(
            @Parameter(description = "用户ID", required = true) @PathVariable String id, @Parameter(description = "租户ID", required = true) @PathVariable String tenantId) {
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setId(id);
        sysUserVO.setTenantId(tenantId);
        Set<String> permissions = userService.selectUserPermissions(sysUserVO);
        return Result.success(permissions);
    }

    /**
     * 批量导入用户
     */
    @Operation(summary = "批量导入用户", description = "批量导入用户数据")
    @PostMapping("/import")
    @RequiresPermissions("system:user:import")
    public Result<String> importUsers(@RequestBody List<SysUser> userList) {
        String result = userService.importUsers(userList);
        return Result.success(result);
    }

    /**
     * 获取用户统计信息
     */
    @Operation(summary = "获取用户统计", description = "获取用户统计信息")
    @GetMapping("/statistics")
    @RequiresPermissions("system:user:view")
    public Result<Object> getUserStatistics() {
        // 这里应该返回用户统计信息，如总数、在线数、各状态用户数等
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalUsers", 0);
        statistics.put("activeUsers", 0);
        statistics.put("disabledUsers", 0);
        statistics.put("lockedUsers", 0);
        return Result.success(statistics);
    }

    /**
     * 批量修改用户状态
     */
    @Operation(summary = "批量修改状态", description = "批量修改用户状态")
    @PutMapping("/batch-status")
    @RequiresPermissions("system:user:edit")
    public Result<Void> batchUpdateStatus(
            @RequestBody List<String> userIds,
            @Parameter(description = "状态", required = true) @RequestParam String status) {
        boolean result = userService.batchUpdateStatus(userIds, status);
        return result ? Result.success() : Result.fail("批量修改状态失败");
    }

    /**
     * 重置密码请求
     */
    public static class ResetPasswordRequest {

        private String userId;
        private String newPassword;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getNewPassword() {
            return newPassword;
        }

        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }

    /**
     * 修改状态请求
     */
    public static class ChangeStatusRequest {

        private String userId;
        private String status;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }

    /**
     * 分配角色请求
     */
    public static class AssignRolesRequest {

        private String userId;
        private List<String> roleIds;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public List<String> getRoleIds() {
            return roleIds;
        }

        public void setRoleIds(List<String> roleIds) {
            this.roleIds = roleIds;
        }
    }

    // ==================== 个人信息相关接口 ====================
    /**
     * 获取当前用户个人信息
     */
    @Operation(summary = "获取当前用户个人信息", description = "获取当前登录用户的详细个人信息")
    @GetMapping("/profile")
    public Result<UserProfileVO> getCurrentUserProfile() {
        UserProfileVO profile = userService.getCurrentUserProfile();
        return Result.success(profile);
    }

    /**
     * 更新当前用户个人信息
     */
    @Operation(summary = "更新当前用户个人信息", description = "更新当前登录用户的个人信息")
    @PutMapping("/profile")
    public Result<Void> updateCurrentUserProfile(@Valid @RequestBody UserProfileUpdateDTO updateDTO) {
        userService.updateCurrentUserProfile(updateDTO);
        return Result.success("个人信息更新成功");
    }

    /**
     * 修改当前用户密码
     */
    @Operation(summary = "修改当前用户密码", description = "修改当前登录用户的密码")
    @PutMapping("/profile/password")
    public Result<Void> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        userService.changeCurrentUserPassword(request.getOldPassword(), request.getNewPassword());
        return Result.success("密码修改成功");
    }

    /**
     * 修改密码请求参数
     */
    @Schema(description = "修改密码请求参数")
    public static class ChangePasswordRequest {

        @Schema(description = "原密码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "原密码不能为空")
        private String oldPassword;

        @Schema(description = "新密码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "新密码不能为空")
        @Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
        private String newPassword;

        public String getOldPassword() {
            return oldPassword;
        }

        public void setOldPassword(String oldPassword) {
            this.oldPassword = oldPassword;
        }

        public String getNewPassword() {
            return newPassword;
        }

        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
}
