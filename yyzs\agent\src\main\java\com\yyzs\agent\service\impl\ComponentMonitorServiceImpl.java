package com.yyzs.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyzs.agent.entity.ComponentMonitor;
import com.yyzs.agent.entity.ElasticComponent;
import com.yyzs.agent.mapper.ComponentMonitorMapper;
import com.yyzs.agent.service.ComponentMonitorService;
import com.yyzs.agent.service.ElasticComponentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 组件监控服务实现类
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComponentMonitorServiceImpl extends ServiceImpl<ComponentMonitorMapper, ComponentMonitor> 
        implements ComponentMonitorService {

    private final ElasticComponentService elasticComponentService;
    
    @Value("${yyzs.agent.monitor-interval:30}")
    private int monitorInterval;

    private final AtomicBoolean monitoringActive = new AtomicBoolean(false);
    private final SystemInfo systemInfo = new SystemInfo();
    private final HardwareAbstractionLayer hardware = systemInfo.getHardware();
    private final OperatingSystem os = systemInfo.getOperatingSystem();

    @Override
    public ComponentMonitor collectMonitorData(String componentId) {
        try {
            ElasticComponent component = elasticComponentService.getById(componentId);
            if (component == null) {
                log.warn("组件不存在: {}", componentId);
                return null;
            }

            ComponentMonitor monitor = new ComponentMonitor();
            monitor.setComponentId(componentId);
            monitor.setCreateTime(LocalDateTime.now());
            monitor.setUpdateTime(LocalDateTime.now());

            // 获取进程信息
            Long processId = component.getProcessId();
            if (processId != null) {
                OSProcess process = os.getProcess((int) processId.longValue());
                if (process != null) {
                    // CPU使用率
                    double cpuUsage = process.getProcessCpuLoadCumulative() * 100;
                    monitor.setCpuUsage(BigDecimal.valueOf(cpuUsage).setScale(2, RoundingMode.HALF_UP));

                    // 内存使用量
                    long memoryUsage = process.getResidentSetSize() / 1024 / 1024; // MB
                    monitor.setMemoryUsage(memoryUsage);

                    // 内存使用率
                    GlobalMemory memory = hardware.getMemory();
                    double memoryPercent = (double) process.getResidentSetSize() / memory.getTotal() * 100;
                    monitor.setMemoryUsagePercent(BigDecimal.valueOf(memoryPercent).setScale(2, RoundingMode.HALF_UP));

                    // 线程数
                    monitor.setThreadCount(process.getThreadCount());

                    // 文件描述符数量
                    monitor.setFileDescriptorCount(process.getOpenFiles());

                    // 运行时长
                    monitor.setUptime((System.currentTimeMillis() - process.getStartTime()) / 1000);

                    // 进程状态
                    monitor.setProcessStatus(ComponentMonitor.ProcessStatus.RUNNING);
                    monitor.setProcessId(processId);
                    monitor.setIsHealthy(true);
                } else {
                    // 进程不存在
                    monitor.setProcessStatus(ComponentMonitor.ProcessStatus.STOPPED);
                    monitor.setIsHealthy(false);
                    monitor.setHealthMessage("进程不存在");
                }
            } else {
                monitor.setProcessStatus(ComponentMonitor.ProcessStatus.STOPPED);
                monitor.setIsHealthy(false);
                monitor.setHealthMessage("未找到进程ID");
            }

            // 检查端口连通性
            if (component.getPort() != null) {
                long startTime = System.currentTimeMillis();
                boolean portReachable = checkPortReachability(component.getHost(), component.getPort());
                long responseTime = System.currentTimeMillis() - startTime;
                
                monitor.setResponseTime(responseTime);
                if (!portReachable) {
                    monitor.setIsHealthy(false);
                    monitor.setHealthMessage("端口不可达");
                }
            }

            // 获取磁盘使用情况
            if (component.getInstallPath() != null) {
                try {
                    java.nio.file.FileStore fileStore = java.nio.file.Files.getFileStore(
                        java.nio.file.Paths.get(component.getInstallPath()));
                    long totalSpace = fileStore.getTotalSpace();
                    long usableSpace = fileStore.getUsableSpace();
                    long usedSpace = totalSpace - usableSpace;
                    
                    monitor.setDiskUsage(usedSpace / 1024 / 1024); // MB
                    double diskPercent = (double) usedSpace / totalSpace * 100;
                    monitor.setDiskUsagePercent(BigDecimal.valueOf(diskPercent).setScale(2, RoundingMode.HALF_UP));
                } catch (Exception e) {
                    log.warn("获取磁盘使用情况失败: {}", component.getInstallPath(), e);
                }
            }

            // 保存监控数据
            save(monitor);
            return monitor;

        } catch (Exception e) {
            log.error("收集组件监控数据异常: " + componentId, e);
            return null;
        }
    }

    @Override
    public List<ComponentMonitor> collectAllMonitorData() {
        List<ElasticComponent> components = elasticComponentService.getRunningComponents();
        List<ComponentMonitor> monitors = new ArrayList<>();

        for (ElasticComponent component : components) {
            ComponentMonitor monitor = collectMonitorData(component.getId());
            if (monitor != null) {
                monitors.add(monitor);
            }
        }

        return monitors;
    }

    @Override
    public ComponentMonitor getLatestMonitorData(String componentId) {
        return baseMapper.findLatestByComponentId(componentId);
    }

    @Override
    public List<ComponentMonitor> getHistoryMonitorData(String componentId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.findHistoryByComponentId(componentId, startTime, endTime);
    }

    @Override
    public List<ComponentMonitor> getAllLatestMonitorData() {
        return baseMapper.findAllLatestMonitorData();
    }

    @Override
    public List<ComponentMonitor> getUnhealthyComponents() {
        return baseMapper.findUnhealthyComponents();
    }

    @Override
    public boolean checkComponentHealth(String componentId) {
        ComponentMonitor monitor = getLatestMonitorData(componentId);
        return monitor != null && monitor.getIsHealthy();
    }

    @Override
    public Map<String, Object> getComponentPerformanceStats(String componentId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();

        // 获取平均值
        Double avgCpu = baseMapper.getAverageCpuUsage(componentId, startTime, endTime);
        Double avgMemory = baseMapper.getAverageMemoryUsage(componentId, startTime, endTime);

        // 获取最大值
        Double maxCpu = baseMapper.getMaxCpuUsage(componentId, startTime, endTime);
        Double maxMemory = baseMapper.getMaxMemoryUsage(componentId, startTime, endTime);

        stats.put("avgCpuUsage", avgCpu != null ? avgCpu : 0.0);
        stats.put("avgMemoryUsage", avgMemory != null ? avgMemory : 0.0);
        stats.put("maxCpuUsage", maxCpu != null ? maxCpu : 0.0);
        stats.put("maxMemoryUsage", maxMemory != null ? maxMemory : 0.0);

        // 获取数据点数量
        long dataPoints = baseMapper.countByComponentId(componentId);
        stats.put("dataPoints", dataPoints);

        return stats;
    }

    @Override
    public Map<String, Object> getSystemResourceUsage() {
        Map<String, Object> usage = new HashMap<>();

        try {
            // CPU使用率
            CentralProcessor processor = hardware.getProcessor();
            double cpuUsage = processor.getSystemCpuLoad(1000) * 100;
            usage.put("cpuUsage", BigDecimal.valueOf(cpuUsage).setScale(2, RoundingMode.HALF_UP));

            // 内存使用情况
            GlobalMemory memory = hardware.getMemory();
            long totalMemory = memory.getTotal();
            long availableMemory = memory.getAvailable();
            long usedMemory = totalMemory - availableMemory;
            
            usage.put("totalMemory", totalMemory / 1024 / 1024); // MB
            usage.put("usedMemory", usedMemory / 1024 / 1024); // MB
            usage.put("availableMemory", availableMemory / 1024 / 1024); // MB
            usage.put("memoryUsagePercent", BigDecimal.valueOf((double) usedMemory / totalMemory * 100)
                    .setScale(2, RoundingMode.HALF_UP));

            // 磁盘使用情况
            java.nio.file.FileStore rootFileStore = java.nio.file.Files.getFileStore(
                java.nio.file.Paths.get("/"));
            long totalDisk = rootFileStore.getTotalSpace();
            long usableDisk = rootFileStore.getUsableSpace();
            long usedDisk = totalDisk - usableDisk;
            
            usage.put("totalDisk", totalDisk / 1024 / 1024 / 1024); // GB
            usage.put("usedDisk", usedDisk / 1024 / 1024 / 1024); // GB
            usage.put("availableDisk", usableDisk / 1024 / 1024 / 1024); // GB
            usage.put("diskUsagePercent", BigDecimal.valueOf((double) usedDisk / totalDisk * 100)
                    .setScale(2, RoundingMode.HALF_UP));

        } catch (Exception e) {
            log.error("获取系统资源使用情况异常", e);
        }

        return usage;
    }

    @Override
    public int cleanupHistoryData(LocalDateTime beforeTime) {
        return baseMapper.deleteBeforeTime(beforeTime);
    }

    @Override
    public void startMonitoring() {
        monitoringActive.set(true);
        log.info("组件监控已启动，监控间隔: {}秒", monitorInterval);
    }

    @Override
    public void stopMonitoring() {
        monitoringActive.set(false);
        log.info("组件监控已停止");
    }

    @Override
    public boolean isMonitoringActive() {
        return monitoringActive.get();
    }

    @Override
    public void setMonitorInterval(int intervalSeconds) {
        this.monitorInterval = intervalSeconds;
        log.info("监控间隔已更新为: {}秒", intervalSeconds);
    }

    @Override
    public void sendAlert(String componentId, String message, String level) {
        // TODO: 实现告警通知功能（邮件、短信、钉钉等）
        log.warn("组件告警 [{}] - 组件ID: {}, 消息: {}", level, componentId, message);
    }

    @Override
    public List<Map<String, Object>> getAlertHistory(String componentId) {
        // TODO: 实现告警历史查询
        return new ArrayList<>();
    }

    /**
     * 定时监控任务
     */
    @Scheduled(fixedDelayString = "${yyzs.agent.monitor-interval:30}000")
    public void scheduledMonitoring() {
        if (!monitoringActive.get()) {
            return;
        }

        try {
            log.debug("开始执行定时监控任务");
            List<ComponentMonitor> monitors = collectAllMonitorData();
            
            // 检查告警条件
            for (ComponentMonitor monitor : monitors) {
                checkAlertConditions(monitor);
            }
            
            log.debug("定时监控任务完成，收集了{}个组件的监控数据", monitors.size());
        } catch (Exception e) {
            log.error("定时监控任务执行异常", e);
        }
    }

    /**
     * 检查端口可达性
     */
    private boolean checkPortReachability(String host, Integer port) {
        try (java.net.Socket socket = new java.net.Socket()) {
            socket.connect(new java.net.InetSocketAddress(host, port), 5000);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查告警条件
     */
    private void checkAlertConditions(ComponentMonitor monitor) {
        // CPU使用率告警
        if (monitor.getCpuUsage() != null && monitor.getCpuUsage().doubleValue() > 80) {
            sendAlert(monitor.getComponentId(), 
                String.format("CPU使用率过高: %.2f%%", monitor.getCpuUsage()), "WARNING");
        }

        // 内存使用率告警
        if (monitor.getMemoryUsagePercent() != null && monitor.getMemoryUsagePercent().doubleValue() > 80) {
            sendAlert(monitor.getComponentId(), 
                String.format("内存使用率过高: %.2f%%", monitor.getMemoryUsagePercent()), "WARNING");
        }

        // 磁盘使用率告警
        if (monitor.getDiskUsagePercent() != null && monitor.getDiskUsagePercent().doubleValue() > 90) {
            sendAlert(monitor.getComponentId(), 
                String.format("磁盘使用率过高: %.2f%%", monitor.getDiskUsagePercent()), "WARNING");
        }

        // 健康状态告警
        if (!monitor.getIsHealthy()) {
            sendAlert(monitor.getComponentId(), 
                "组件健康检查失败: " + monitor.getHealthMessage(), "ERROR");
        }
    }
}
