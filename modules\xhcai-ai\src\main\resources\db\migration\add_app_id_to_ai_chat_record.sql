-- 为ai_chat_record表添加app_id字段
-- 用于存储Dify智能体应用ID，支持会话记录与智能体的关联

-- 检查并添加app_id字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE ai_chat_record ADD COLUMN app_id VARCHAR(100) COMMENT "应用ID（Dify智能体ID）"',
        'SELECT "app_id字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ai_chat_record' 
        AND COLUMN_NAME = 'app_id'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为app_id字段添加索引（提高查询性能）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'CREATE INDEX idx_ai_chat_record_app_id ON ai_chat_record(app_id)',
        'SELECT "app_id字段索引已存在" as message'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'ai_chat_record' 
        AND INDEX_NAME = 'idx_ai_chat_record_app_id'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为session_id和app_id组合添加复合索引（提高会话查询性能）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'CREATE INDEX idx_ai_chat_record_session_app ON ai_chat_record(session_id, app_id)',
        'SELECT "session_id和app_id复合索引已存在" as message'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'ai_chat_record' 
        AND INDEX_NAME = 'idx_ai_chat_record_session_app'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
