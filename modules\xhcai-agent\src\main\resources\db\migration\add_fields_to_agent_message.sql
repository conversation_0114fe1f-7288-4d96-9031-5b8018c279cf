-- 为agent_message表添加inputs、query、external_info_id字段
-- 用于增强智能体消息的功能和扩展性

-- 检查并添加inputs字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent_message ADD COLUMN inputs TEXT COMMENT "输入参数（JSON格式）"',
        'SELECT "inputs字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent_message' 
        AND COLUMN_NAME = 'inputs'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加query字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent_message ADD COLUMN query TEXT COMMENT "查询内容"',
        'SELECT "query字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent_message' 
        AND COLUMN_NAME = 'query'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加external_info_id字段
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE agent_message ADD COLUMN external_info_id VARCHAR(36) COMMENT "外部信息ID"',
        'SELECT "external_info_id字段已存在" as message'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'agent_message' 
        AND COLUMN_NAME = 'external_info_id'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为external_info_id字段添加索引（提高查询性能）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'CREATE INDEX idx_agent_message_external_info_id ON agent_message(external_info_id)',
        'SELECT "external_info_id字段索引已存在" as message'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'agent_message' 
        AND INDEX_NAME = 'idx_agent_message_external_info_id'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为conversation_id和external_info_id组合添加复合索引（优化查询性能）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'CREATE INDEX idx_agent_message_conv_external ON agent_message(conversation_id, external_info_id)',
        'SELECT "conversation_id和external_info_id复合索引已存在" as message'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'agent_message' 
        AND INDEX_NAME = 'idx_agent_message_conv_external'
        AND TABLE_SCHEMA = DATABASE()
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构变更结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'agent_message' 
    AND TABLE_SCHEMA = DATABASE()
    AND COLUMN_NAME IN ('inputs', 'query', 'external_info_id')
ORDER BY ORDINAL_POSITION;

-- 显示相关索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'agent_message' 
    AND TABLE_SCHEMA = DATABASE()
    AND (INDEX_NAME LIKE '%external%' OR COLUMN_NAME = 'external_info_id')
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 显示字段添加成功信息
SELECT 'agent_message表新字段添加完成' as result;
