package com.xhcai.common.api.dto;

import java.io.Serializable;

import com.xhcai.common.api.query.Pageable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * 分页查询DTO基类 统一所有模块的分页参数规范
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "分页查询DTO基类")
public class PageQueryDTO implements Pageable, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码，从1开始
     */
    @Schema(description = "当前页码", example = "1", minimum = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小，默认10条，最大100条
     */
    @Schema(description = "每页大小", example = "10", minimum = "1", maximum = "100")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime")
    private String orderBy;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String orderDirection = "desc";

    /**
     * 是否查询总数，默认true 当不需要总数时可设置为false提高查询性能
     */
    @Schema(description = "是否查询总数", example = "true")
    private Boolean searchCount = true;

    // Getters and Setters
    public Long getCurrent() {
        return current;
    }

    // 兼容pageNum参数
    public Long getPageNum() {return current;}

    public void setCurrent(Long current) {
        this.current = current;
    }

    // 兼容pageNum参数
    public void setPageNum(Long pageNum) {this.current = pageNum;}

    public Long getSize() {
        return size;
    }

    // 兼容pageSize参数
    public Long getPageSize() {return size;}

    public void setSize(Long size) {
        this.size = size;
    }

    // 兼容pageSize参数
    public void setPageSize(Long pageSize) {this.size = pageSize;}

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderDirection() {
        return orderDirection;
    }

    public void setOrderDirection(String orderDirection) {
        this.orderDirection = orderDirection;
    }

    public Boolean getSearchCount() {
        return searchCount;
    }

    public void setSearchCount(Boolean searchCount) {
        this.searchCount = searchCount;
    }

    /**
     * 获取偏移量，用于数据库查询
     *
     * @return 偏移量
     */
    public Long getOffset() {
        return (current - 1) * size;
    }

    /**
     * 获取限制数量，用于数据库查询
     *
     * @return 限制数量
     */
    public Long getLimit() {
        return size;
    }

    /**
     * 是否为升序排序
     *
     * @return true-升序，false-降序
     */
    public Boolean isAsc() {
        return "asc".equalsIgnoreCase(orderDirection);
    }

    /**
     * 是否为降序排序
     *
     * @return true-降序，false-升序
     */
    public Boolean isDesc() {
        return "desc".equalsIgnoreCase(orderDirection);
    }

    @Override
    public String toString() {
        return "PageQueryDTO{"
                + "current=" + current
                + ", size=" + size
                + ", orderBy='" + orderBy + '\''
                + ", orderDirection='" + orderDirection + '\''
                + ", searchCount=" + searchCount
                + '}';
    }
}
