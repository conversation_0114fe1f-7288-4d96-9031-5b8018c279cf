package com.yyzs.agent;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * YYZS Agent Platform 主应用程序
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
public class YyzsAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(YyzsAgentApplication.class, args);
    }
}
