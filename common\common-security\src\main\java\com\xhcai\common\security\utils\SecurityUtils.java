package com.xhcai.common.security.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.enums.ResultCode;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.service.LoginUser;

import java.util.Set;

/**
 * 安全工具类 - 用于获取当前登录用户信息
 *
 * 提供统一的用户信息获取、权限检查、租户管理等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SecurityUtils {

    private static final Logger log = LoggerFactory.getLogger(SecurityUtils.class);

    /**
     * 获取当前登录用户
     *
     * @return 当前登录用户，未登录返回null
     */
    public static LoginUser getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() != null && authentication.getPrincipal() instanceof LoginUser) {

                LoginUser currentUser = (LoginUser) authentication.getPrincipal();
                if (currentUser == null) {
                    throw new BusinessException(ResultCode.UNAUTHORIZED.getCode(), "您当前没有登录");
                }
                return currentUser;
            }
        } catch (Exception e) {
            log.debug("获取当前用户失败: {}", e.getMessage());
            throw new BusinessException(ResultCode.UNAUTHORIZED.getCode(), "您当前没有登录");
        }
        throw new BusinessException(ResultCode.UNAUTHORIZED.getCode(), "您当前没有登录");
    }

    /**
     * 获取当前登录用户（不抛出异常）
     *
     * @return 当前登录用户，未登录返回null
     */
    public static LoginUser getCurrentUserSafely() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() != null && authentication.getPrincipal() instanceof LoginUser) {
                return (LoginUser) authentication.getPrincipal();
            }
        } catch (Exception e) {
            log.debug("获取当前用户失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前用户ID
     *
     * @return 当前用户ID，未登录返回null
     */
    public static String getCurrentUserId() {
        LoginUser currentUser = getCurrentUser();
        String userId = currentUser.getUserId();
        if (userId == null || userId.trim().isEmpty()) {
            throw new BusinessException(ResultCode.UNAUTHORIZED.getCode(), "没有用户ID");
        }
        return userId;
    }

    /**
     * 获取当前用户名
     *
     * @return 当前用户名，未登录返回null
     */
    public static String getCurrentUsername() {
        LoginUser currentUser = getCurrentUser();
        return currentUser.getUsername();
    }

    /**
     * 获取当前租户ID
     *
     * @return 当前租户ID，未登录返回null
     */
    public static String getCurrentTenantId() {
        LoginUser currentUser = getCurrentUserSafely();
        if (currentUser == null) {
            return null;
        }
        String tenantId = currentUser.getTenantId();
        if (tenantId == null || tenantId.trim().isEmpty()) {
            return null;
        }
        return tenantId;
    }

    /**
     * 检查当前用户是否拥有指定权限
     *
     * @param permission 权限标识
     * @return 是否拥有权限
     */
    public static boolean hasPermission(String permission) {
        LoginUser currentUser = getCurrentUser();
        return currentUser.hasPermission(permission);
    }

    /**
     * 检查当前用户是否拥有指定角色
     *
     * @param role 角色标识
     * @return 是否拥有角色
     */
    public static boolean hasRole(String role) {
        LoginUser currentUser = getCurrentUser();
        return currentUser.hasRole(role);
    }

    /**
     * 获取当前用户拥有的角色ID
     *
     * @return 角色ID集合
     */
    public static Set<String> getCurrentUserRoleIds() {
        LoginUser currentUser = getCurrentUser();
        return currentUser.getRoles();
    }

    /**
     * 获取当前用户的部门ID
     *
     * @return 部门ID
     */
    public static String getCurrentUserDeptId() {
        LoginUser currentUser = getCurrentUser();
        return currentUser.getDeptId();
    }

    /**
     * 检查是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isAuthenticated() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return authentication != null && authentication.isAuthenticated()
                    && !"anonymousUser".equals(authentication.getPrincipal());
        } catch (Exception e) {
            log.debug("检查登录状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查当前用户是否是平台管理员 平台管理员通过用户类型USER_TYPE_PLATFORM来识别
     *
     * @return 是否是平台管理员
     */
    public static boolean isPlatformAdmin() {
        LoginUser currentUser = getCurrentUserSafely();
        if (currentUser == null) {
            return false;
        }

        // 检查用户类型是否为平台管理员
        return CommonConstants.USER_TYPE_PLATFORM.equals(currentUser.getUserType());
    }

    /**
     * 检查当前用户是否是租户管理员
     *
     * @return 是否是租户管理员
     */
    public static boolean isTenantAdmin() {
        LoginUser currentUser = getCurrentUser();

        // 平台管理员也被认为是租户管理员
        if (isPlatformAdmin()) {
            return true;
        }

        // 检查用户类型是否为租户管理员
        return CommonConstants.USER_TYPE_TENANT.equals(currentUser.getUserType());
    }

    /**
     * 检查当前用户是否可以访问指定租户的数据
     *
     * @param targetTenantId 目标租户ID
     * @return 是否可以访问
     */
    public static boolean canAccessTenant(String targetTenantId) {
        if (targetTenantId == null) {
            return false;
        }

        LoginUser currentUser = getCurrentUser();

        // 平台管理员可以访问所有租户
        if (isPlatformAdmin()) {
            return true;
        }

        // 普通用户只能访问自己的租户
        return targetTenantId.equals(currentUser.getTenantId());
    }

    /**
     * 获取当前用户的角色级别 0: 平台管理员 1: 租户管理员 2: 普通用户
     *
     * @return 角色级别
     */
    public static int getUserLevel() {
        if (isPlatformAdmin()) {
            return 0;
        }
        if (isTenantAdmin()) {
            return 1;
        }
        return 2;
    }

    /**
     * 检查当前用户是否可以管理指定用户
     *
     * @param targetUserId 目标用户ID
     * @param targetTenantId 目标用户的租户ID
     * @return 是否可以管理
     */
    public static boolean canManageUser(String targetUserId, String targetTenantId) {
        if (targetUserId == null || targetTenantId == null) {
            return false;
        }

        LoginUser currentUser = getCurrentUser();

        // 不能管理自己
        if (targetUserId.equals(currentUser.getUserId())) {
            return false;
        }

        // 平台管理员可以管理所有用户
        if (isPlatformAdmin()) {
            return true;
        }

        // 租户管理员只能管理同租户的用户
        return isTenantAdmin() && targetTenantId.equals(currentUser.getTenantId());
    }

    /**
     * 检查当前用户是否拥有任意一个指定权限
     *
     * @param permissions 权限标识数组
     * @return 是否拥有任意一个权限
     */
    public static boolean hasAnyPermission(String... permissions) {
        if (permissions == null) {
            return false;
        }

        for (String permission : permissions) {
            if (hasPermission(permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户是否拥有所有指定权限
     *
     * @param permissions 权限标识数组
     * @return 是否拥有所有权限
     */
    public static boolean hasAllPermissions(String... permissions) {
        if (permissions == null) {
            return true;
        }

        for (String permission : permissions) {
            if (!hasPermission(permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查当前用户是否拥有任意一个指定角色
     *
     * @param roles 角色标识数组
     * @return 是否拥有任意一个角色
     */
    public static boolean hasAnyRole(String... roles) {
        if (roles == null) {
            return false;
        }

        for (String role : roles) {
            if (hasRole(role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户是否拥有所有指定角色
     *
     * @param roles 角色标识数组
     * @return 是否拥有所有角色
     */
    public static boolean hasAllRoles(String... roles) {
        if (roles == null) {
            return true;
        }

        for (String role : roles) {
            if (!hasRole(role)) {
                return false;
            }
        }
        return true;
    }
}
