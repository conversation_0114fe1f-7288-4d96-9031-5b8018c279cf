package com.yyzs.agent.service.impl;

import com.yyzs.agent.entity.ElasticComponent;
import com.yyzs.agent.service.ComponentInstallService;
import com.yyzs.agent.service.ComponentConfigTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.exec.*;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 组件安装服务实现类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComponentInstallServiceImpl implements ComponentInstallService {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ComponentInstallServiceImpl.class);

    private final ComponentConfigTemplateService configTemplateService;

    @Value("${yyzs.agent.install-path:/opt/elastic}")
    private String installBasePath;

    private static final int COMMAND_TIMEOUT = 300; // 5分钟超时

    @Override
    public boolean installComponent(ElasticComponent component, Map<String, Object> config) {
        try {
            log.info("开始安装组件: {}", component.getName());

            // 1. 验证安装环境
            if (!validateInstallEnvironment(component)) {
                log.error("安装环境验证失败: {}", component.getName());
                return false;
            }

            // 2. 创建安装目录
            String installPath = installBasePath + "/" + component.getName();
            Path installDir = Paths.get(installPath);
            if (!Files.exists(installDir)) {
                Files.createDirectories(installDir);
            }
            component.setInstallPath(installPath);

            // 3. 解压安装包
            if (!extractPackage(component)) {
                log.error("解压安装包失败: {}", component.getName());
                return false;
            }

            // 4. 生成配置文件
            if (!generateComponentConfig(component, config)) {
                log.error("生成配置文件失败: {}", component.getName());
                return false;
            }

            // 5. 设置权限
            if (!setComponentPermissions(component)) {
                log.error("设置权限失败: {}", component.getName());
                return false;
            }

            // 6. 创建启动脚本
            if (!createStartupScript(component)) {
                log.error("创建启动脚本失败: {}", component.getName());
                return false;
            }

            log.info("组件安装成功: {}", component.getName());
            return true;

        } catch (Exception e) {
            log.error("安装组件异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean uninstallComponent(ElasticComponent component) {
        try {
            log.info("开始卸载组件: {}", component.getName());

            // 1. 停止组件
            if (component.getStatus() == ElasticComponent.ComponentStatus.RUNNING) {
                stopComponent(component);
            }

            // 2. 清理安装文件
            if (!cleanupInstallFiles(component)) {
                log.error("清理安装文件失败: {}", component.getName());
                return false;
            }

            log.info("组件卸载成功: {}", component.getName());
            return true;

        } catch (Exception e) {
            log.error("卸载组件异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean startComponent(ElasticComponent component) {
        try {
            log.info("启动组件: {}", component.getName());

            String startCommand = buildStartCommand(component);
            if (startCommand == null) {
                log.error("构建启动命令失败: {}", component.getName());
                return false;
            }

            // 执行启动命令
            CommandLine cmdLine = CommandLine.parse(startCommand);
            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0);

            // 设置工作目录
            executor.setWorkingDirectory(new File(component.getInstallPath()));

            // 异步执行
            DefaultExecuteResultHandler resultHandler = new DefaultExecuteResultHandler();
            executor.execute(cmdLine, resultHandler);

            // 等待一段时间检查启动状态
            Thread.sleep(5000);

            // 获取进程ID
            Long processId = getComponentProcessId(component);
            if (processId != null) {
                component.setProcessId(processId);
                log.info("组件启动成功: {}, PID: {}", component.getName(), processId);
                return true;
            } else {
                log.error("组件启动失败，未找到进程: {}", component.getName());
                return false;
            }

        } catch (Exception e) {
            log.error("启动组件异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean stopComponent(ElasticComponent component) {
        try {
            log.info("停止组件: {}", component.getName());

            // 优先使用优雅停止
            String stopCommand = buildStopCommand(component);
            if (stopCommand != null) {
                CommandLine cmdLine = CommandLine.parse(stopCommand);
                DefaultExecutor executor = new DefaultExecutor();
                executor.setExitValue(0);

                ExecuteWatchdog watchdog = new ExecuteWatchdog(30000); // 30秒超时
                executor.setWatchdog(watchdog);

                try {
                    executor.execute(cmdLine);
                    log.info("组件优雅停止成功: {}", component.getName());
                    return true;
                } catch (Exception e) {
                    log.warn("优雅停止失败，尝试强制停止: {}", component.getName());
                }
            }

            // 强制停止进程
            if (component.getProcessId() != null) {
                return killComponentProcess(component);
            }

            return true;

        } catch (Exception e) {
            log.error("停止组件异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public ElasticComponent.ComponentStatus checkComponentStatus(ElasticComponent component) {
        try {
            // 检查进程是否存在
            Long processId = getComponentProcessId(component);
            if (processId == null) {
                return ElasticComponent.ComponentStatus.STOPPED;
            }

            // 检查端口是否监听（如果有端口配置）
            if (component.getPort() != null) {
                if (!isPortListening(component.getPort())) {
                    return ElasticComponent.ComponentStatus.ERROR;
                }
            }

            return ElasticComponent.ComponentStatus.RUNNING;

        } catch (Exception e) {
            log.error("检查组件状态异常: " + component.getName(), e);
            return ElasticComponent.ComponentStatus.ERROR;
        }
    }

    @Override
    public boolean updateComponentConfig(ElasticComponent component, Map<String, Object> config) {
        try {
            // 备份当前配置
            String backupPath = backupComponentConfig(component);
            if (backupPath == null) {
                log.error("备份配置失败: {}", component.getName());
                return false;
            }

            // 生成新配置
            if (!generateComponentConfig(component, config)) {
                log.error("生成新配置失败: {}", component.getName());
                // 恢复备份
                restoreComponentConfig(component, backupPath);
                return false;
            }

            log.info("更新组件配置成功: {}", component.getName());
            return true;

        } catch (Exception e) {
            log.error("更新组件配置异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getComponentConfig(ElasticComponent component) {
        try {
            String configPath = component.getConfigPath();
            if (configPath == null || !Files.exists(Paths.get(configPath))) {
                return new HashMap<>();
            }

            // 读取配置文件内容
            String content = Files.readString(Paths.get(configPath));

            // 使用配置模板服务解析配置
            return configTemplateService.parseConfigContent(component.getType(), content);

        } catch (Exception e) {
            log.error("获取组件配置异常: " + component.getName(), e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean validateComponentConfig(String componentType, Map<String, Object> config) {
        try {
            // 使用配置模板服务验证配置
            return configTemplateService.validateConfig(componentType, config);
        } catch (Exception e) {
            log.error("验证组件配置异常: " + componentType, e);
            return false;
        }
    }

    @Override
    public List<String> getComponentLogs(ElasticComponent component, int lines) {
        try {
            String logPath = component.getLogPath();
            if (logPath == null || !Files.exists(Paths.get(logPath))) {
                return new ArrayList<>();
            }

            // 使用tail命令获取最后几行日志
            String command = String.format("tail -n %d %s", lines, logPath);
            CommandLine cmdLine = CommandLine.parse(command);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream);

            DefaultExecutor executor = new DefaultExecutor();
            executor.setStreamHandler(streamHandler);
            executor.setExitValue(0);

            executor.execute(cmdLine);

            String output = outputStream.toString();
            return Arrays.asList(output.split("\n"));

        } catch (Exception e) {
            log.error("获取组件日志异常: " + component.getName(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean clearComponentLogs(ElasticComponent component) {
        try {
            String logPath = component.getLogPath();
            if (logPath == null || !Files.exists(Paths.get(logPath))) {
                return true;
            }

            // 清空日志文件
            Files.write(Paths.get(logPath), new byte[0]);
            log.info("清理组件日志成功: {}", component.getName());
            return true;

        } catch (Exception e) {
            log.error("清理组件日志异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public String backupComponentConfig(ElasticComponent component) {
        try {
            String configPath = component.getConfigPath();
            if (configPath == null || !Files.exists(Paths.get(configPath))) {
                return null;
            }

            String backupPath = configPath + ".backup." + System.currentTimeMillis();
            Files.copy(Paths.get(configPath), Paths.get(backupPath));

            log.info("备份组件配置成功: {} -> {}", configPath, backupPath);
            return backupPath;

        } catch (Exception e) {
            log.error("备份组件配置异常: " + component.getName(), e);
            return null;
        }
    }

    @Override
    public boolean restoreComponentConfig(ElasticComponent component, String backupPath) {
        try {
            if (backupPath == null || !Files.exists(Paths.get(backupPath))) {
                log.error("备份文件不存在: {}", backupPath);
                return false;
            }

            String configPath = component.getConfigPath();
            Files.copy(Paths.get(backupPath), Paths.get(configPath), StandardCopyOption.REPLACE_EXISTING);

            log.info("恢复组件配置成功: {} <- {}", configPath, backupPath);
            return true;

        } catch (Exception e) {
            log.error("恢复组件配置异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean generateComponentConfig(ElasticComponent component, Map<String, Object> config) {
        try {
            // 生成配置文件路径
            String configPath = component.getInstallPath() + "/config/" + configTemplateService.getConfigFileName(component.getType());
            component.setConfigPath(configPath);

            // 确保配置目录存在
            Path configDir = Paths.get(configPath).getParent();
            if (!Files.exists(configDir)) {
                Files.createDirectories(configDir);
            }

            // 使用配置模板服务生成配置文件内容
            String configContent = configTemplateService.generateConfigContent(component.getType(), config);

            // 写入配置文件
            Files.writeString(Paths.get(configPath), configContent);

            log.info("生成组件配置文件成功: {}", configPath);
            return true;

        } catch (Exception e) {
            log.error("生成组件配置异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean extractPackage(ElasticComponent component) {
        try {
            String packagePath = component.getPackageFilePath();
            String installPath = component.getInstallPath();

            if (!Files.exists(Paths.get(packagePath))) {
                log.error("安装包不存在: {}", packagePath);
                return false;
            }

            // 根据文件扩展名选择解压方式
            if (packagePath.endsWith(".tar.gz") || packagePath.endsWith(".tgz")) {
                return extractTarGz(packagePath, installPath);
            } else if (packagePath.endsWith(".zip")) {
                return extractZip(packagePath, installPath);
            } else {
                log.error("不支持的安装包格式: {}", packagePath);
                return false;
            }

        } catch (Exception e) {
            log.error("解压安装包异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean setComponentPermissions(ElasticComponent component) {
        try {
            String installPath = component.getInstallPath();

            // 设置可执行权限
            String command = String.format("chmod -R 755 %s", installPath);
            CommandLine cmdLine = CommandLine.parse(command);

            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0);
            executor.execute(cmdLine);

            log.info("设置组件权限成功: {}", component.getName());
            return true;

        } catch (Exception e) {
            log.error("设置组件权限异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean createStartupScript(ElasticComponent component) {
        try {
            String scriptPath = component.getInstallPath() + "/bin/start.sh";

            // 使用配置模板服务生成启动脚本
            Map<String, Object> config = new HashMap<>();
            config.put("COMPONENT_NAME", component.getName());
            String scriptContent = configTemplateService.generateStartupScript(
                    component.getType(), component.getInstallPath(), config);

            // 写入脚本文件
            Files.writeString(Paths.get(scriptPath), scriptContent);

            // 设置可执行权限
            String chmodCommand = String.format("chmod +x %s", scriptPath);
            CommandLine cmdLine = CommandLine.parse(chmodCommand);
            DefaultExecutor executor = new DefaultExecutor();
            executor.execute(cmdLine);

            component.setStartCommand(scriptPath + " start");
            component.setStopCommand(scriptPath + " stop");
            component.setRestartCommand(scriptPath + " restart");

            log.info("创建启动脚本成功: {}", scriptPath);
            return true;

        } catch (Exception e) {
            log.error("创建启动脚本异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public Long getComponentProcessId(ElasticComponent component) {
        try {
            String command = String.format("pgrep -f %s", component.getName());
            CommandLine cmdLine = CommandLine.parse(command);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream);

            DefaultExecutor executor = new DefaultExecutor();
            executor.setStreamHandler(streamHandler);
            executor.setExitValues(new int[]{0, 1}); // pgrep返回1表示未找到进程

            int exitValue = executor.execute(cmdLine);
            if (exitValue == 0) {
                String output = outputStream.toString().trim();
                if (!output.isEmpty()) {
                    return Long.parseLong(output.split("\n")[0]);
                }
            }
            return null;

        } catch (Exception e) {
            log.error("获取组件进程ID异常: " + component.getName(), e);
            return null;
        }
    }

    @Override
    public boolean killComponentProcess(ElasticComponent component) {
        try {
            Long processId = component.getProcessId();
            if (processId == null) {
                processId = getComponentProcessId(component);
            }

            if (processId == null) {
                log.warn("未找到组件进程: {}", component.getName());
                return true;
            }

            // 先尝试优雅停止
            String command = String.format("kill %d", processId);
            CommandLine cmdLine = CommandLine.parse(command);
            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValues(new int[]{0, 1});

            int exitValue = executor.execute(cmdLine);
            if (exitValue == 0) {
                // 等待进程结束
                Thread.sleep(5000);

                // 检查进程是否还存在
                if (getComponentProcessId(component) == null) {
                    log.info("组件进程优雅停止成功: {}", component.getName());
                    return true;
                }
            }

            // 强制停止
            command = String.format("kill -9 %d", processId);
            cmdLine = CommandLine.parse(command);
            executor.execute(cmdLine);

            log.info("组件进程强制停止成功: {}", component.getName());
            return true;

        } catch (Exception e) {
            log.error("停止组件进程异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean isComponentPortAvailable(ElasticComponent component, Integer port) {
        return !isPortListening(port);
    }

    @Override
    public boolean validateInstallEnvironment(ElasticComponent component) {
        try {
            // 检查Java环境（对于需要Java的组件）
            if (requiresJava(component.getType())) {
                if (!checkJavaInstallation()) {
                    log.error("Java环境检查失败，组件需要Java运行环境: {}", component.getName());
                    return false;
                }
            }

            // 检查磁盘空间
            Path installPath = Paths.get(component.getInstallPath());
            if (!Files.exists(installPath.getParent())) {
                Files.createDirectories(installPath.getParent());
            }

            long freeSpace = Files.getFileStore(installPath.getParent()).getUsableSpace();
            long requiredSpace = component.getPackageFileSize() * 3; // 预留3倍空间

            if (freeSpace < requiredSpace) {
                log.error("磁盘空间不足，需要: {}MB, 可用: {}MB",
                        requiredSpace / 1024 / 1024, freeSpace / 1024 / 1024);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("验证安装环境异常: " + component.getName(), e);
            return false;
        }
    }

    @Override
    public boolean cleanupInstallFiles(ElasticComponent component) {
        try {
            String installPath = component.getInstallPath();
            if (installPath != null && Files.exists(Paths.get(installPath))) {
                FileUtils.deleteDirectory(new File(installPath));
                log.info("清理安装文件成功: {}", installPath);
            }
            return true;

        } catch (Exception e) {
            log.error("清理安装文件异常: " + component.getName(), e);
            return false;
        }
    }

    /**
     * 构建启动命令
     */
    private String buildStartCommand(ElasticComponent component) {
        String installPath = component.getInstallPath();
        String componentType = component.getType().toLowerCase();

        switch (componentType) {
            case "elasticsearch":
                return installPath + "/bin/elasticsearch -d";
            case "logstash":
                return installPath + "/bin/logstash -f " + component.getConfigPath() + " &";
            case "filebeat":
            case "heartbeat":
            case "metricbeat":
            case "packetbeat":
            case "winlogbeat":
            case "auditbeat":
                return installPath + "/bin/" + componentType + " -c " + component.getConfigPath() + " &";
            case "kafka":
                return installPath + "/bin/kafka-server-start.sh " + component.getConfigPath() + " &";
            default:
                log.warn("未知组件类型，无法构建启动命令: {}", componentType);
                return null;
        }
    }

    /**
     * 构建停止命令
     */
    private String buildStopCommand(ElasticComponent component) {
        String installPath = component.getInstallPath();
        String componentType = component.getType().toLowerCase();

        switch (componentType) {
            case "elasticsearch":
                return "pkill -f elasticsearch";
            case "logstash":
                return "pkill -f logstash";
            case "filebeat":
            case "heartbeat":
            case "metricbeat":
            case "packetbeat":
            case "winlogbeat":
            case "auditbeat":
                return "pkill -f " + componentType;
            case "kafka":
                return installPath + "/bin/kafka-server-stop.sh";
            default:
                return null;
        }
    }

    /**
     * 检查端口是否正在监听
     */
    private boolean isPortListening(Integer port) {
        try {
            String command = String.format("netstat -tuln | grep :%d", port);
            CommandLine cmdLine = CommandLine.parse(command);

            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValues(new int[]{0, 1});

            int exitValue = executor.execute(cmdLine);
            return exitValue == 0;

        } catch (Exception e) {
            log.error("检查端口监听状态异常: " + port, e);
            return false;
        }
    }

    /**
     * 检查组件是否需要Java环境
     */
    private boolean requiresJava(String componentType) {
        return Arrays.asList("elasticsearch", "logstash", "kafka").contains(componentType.toLowerCase());
    }

    /**
     * 检查Java安装
     */
    private boolean checkJavaInstallation() {
        try {
            CommandLine cmdLine = CommandLine.parse("java -version");
            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValues(new int[]{0});

            executor.execute(cmdLine);
            return true;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解压tar.gz文件
     */
    private boolean extractTarGz(String packagePath, String installPath) {
        try {
            String command = String.format("tar -xzf %s -C %s --strip-components=1", packagePath, installPath);
            CommandLine cmdLine = CommandLine.parse(command);

            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0);

            ExecuteWatchdog watchdog = new ExecuteWatchdog(COMMAND_TIMEOUT * 1000);
            executor.setWatchdog(watchdog);

            executor.execute(cmdLine);
            return true;

        } catch (Exception e) {
            log.error("解压tar.gz文件失败: " + packagePath, e);
            return false;
        }
    }

    /**
     * 解压zip文件
     */
    private boolean extractZip(String packagePath, String installPath) {
        try {
            String command = String.format("unzip -q %s -d %s", packagePath, installPath);
            CommandLine cmdLine = CommandLine.parse(command);

            DefaultExecutor executor = new DefaultExecutor();
            executor.setExitValue(0);

            ExecuteWatchdog watchdog = new ExecuteWatchdog(COMMAND_TIMEOUT * 1000);
            executor.setWatchdog(watchdog);

            executor.execute(cmdLine);
            return true;

        } catch (Exception e) {
            log.error("解压zip文件失败: " + packagePath, e);
            return false;
        }
    }

    public Map<String, Object> getDefaultConfigTemplate(String componentType) {
        // 使用配置模板服务获取默认配置
        return configTemplateService.getDefaultConfigParams(componentType);
    }
}
