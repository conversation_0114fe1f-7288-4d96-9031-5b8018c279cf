package com.xhcai.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.system.entity.SysTenantConfig;

import java.util.List;
import java.util.Map;

/**
 * 租户配置服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysTenantConfigService extends IService<SysTenantConfig> {

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @return 配置值
     */
    String getConfigValue(String configKey, String tenantId);

    /**
     * 根据配置键获取配置值（当前租户）
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 设置配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean setConfigValue(String configKey, String configValue, String tenantId);

    /**
     * 设置配置值（当前租户）
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否成功
     */
    boolean setConfigValue(String configKey, String configValue);

    /**
     * 获取租户所有配置
     *
     * @param tenantId 租户ID
     * @return 配置Map
     */
    Map<String, String> getTenantConfigs(String tenantId);

    /**
     * 获取当前租户所有配置
     *
     * @return 配置Map
     */
    Map<String, String> getCurrentTenantConfigs();

    /**
     * 批量设置配置
     *
     * @param configs 配置Map
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean batchSetConfigs(Map<String, String> configs, String tenantId);

    /**
     * 批量设置配置（当前租户）
     *
     * @param configs 配置Map
     * @return 是否成功
     */
    boolean batchSetConfigs(Map<String, String> configs);

    /**
     * 根据配置类型获取配置列表
     *
     * @param configType 配置类型
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<SysTenantConfig> getConfigsByType(String configType, String tenantId);

    /**
     * 获取系统配置列表
     *
     * @param tenantId 租户ID
     * @return 系统配置列表
     */
    List<SysTenantConfig> getSystemConfigs(String tenantId);

    /**
     * 获取用户配置列表
     *
     * @param tenantId 租户ID
     * @return 用户配置列表
     */
    List<SysTenantConfig> getUserConfigs(String tenantId);

    /**
     * 创建配置
     *
     * @param config 配置信息
     * @return 是否成功
     */
    boolean createConfig(SysTenantConfig config);

    /**
     * 更新配置
     *
     * @param config 配置信息
     * @return 是否成功
     */
    boolean updateConfig(SysTenantConfig config);

    /**
     * 删除配置
     *
     * @param configIds 配置ID列表
     * @return 是否成功
     */
    boolean deleteConfigs(List<String> configIds);

    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @param excludeId 排除的配置ID
     * @return 是否存在
     */
    boolean existsConfigKey(String configKey, String tenantId, String excludeId);

    /**
     * 复制配置到指定租户
     *
     * @param sourceTenantId 源租户ID
     * @param targetTenantId 目标租户ID
     * @return 是否成功
     */
    boolean copyConfigsToTenant(String sourceTenantId, String targetTenantId);

    /**
     * 重置租户配置为默认值
     *
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean resetTenantConfigs(String tenantId);

    /**
     * 获取配置的默认值
     *
     * @param configKey 配置键
     * @return 默认值
     */
    String getDefaultConfigValue(String configKey);

    /**
     * 刷新配置缓存
     *
     * @param tenantId 租户ID
     */
    void refreshConfigCache(String tenantId);

    /**
     * 清除配置缓存
     *
     * @param tenantId 租户ID
     */
    void clearConfigCache(String tenantId);
}
