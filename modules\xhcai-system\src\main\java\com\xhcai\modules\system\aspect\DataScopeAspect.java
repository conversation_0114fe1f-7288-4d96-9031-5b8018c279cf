package com.xhcai.modules.system.aspect;

import com.xhcai.common.security.service.LoginUser;
import com.xhcai.modules.system.annotation.DataScope;
import com.xhcai.modules.system.entity.SysRole;
import com.xhcai.modules.system.service.ISysRoleService;
import com.xhcai.modules.system.utils.DataScopeUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据权限处理切面
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Aspect
@Component
public class DataScopeAspect {

    private static final Logger log = LoggerFactory.getLogger(DataScopeAspect.class);

    @Autowired
    private ISysRoleService roleService;

    /**
     * 数据权限处理
     */
    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) {
        try {
            clearDataScope(point);
            handleDataScope(point, controllerDataScope);
        } catch (Exception e) {
            log.error("数据权限处理异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理数据权限
     */
    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope) {
        // 获取当前用户
        LoginUser currentUser = SecurityUtils.getCurrentUser();
        if (currentUser == null) {
            return;
        }

        // 如果是平台管理员，则不过滤数据
        if (SecurityUtils.isPlatformAdmin()) {
            return;
        }

        // 获取用户角色
        List<SysRole> roles = roleService.selectRoleEntitiesByUserId(currentUser.getUserId());

        // 构建数据权限SQL
        String dataScopeSql = DataScopeUtils.buildDataScopeSqlByRoles(
                roles,
                currentUser,
                controllerDataScope.deptAlias(),
                controllerDataScope.userAlias()
        );

        if (dataScopeSql != null && !dataScopeSql.isEmpty()) {
            // 将数据权限SQL设置到参数中
            setDataScopeToParams(joinPoint.getArgs(), dataScopeSql);
        }
    }

    /**
     * 设置数据权限SQL到参数中
     */
    private void setDataScopeToParams(Object[] args, String dataScopeSql) {
        if (args == null || args.length == 0) {
            return;
        }

        // 遍历所有参数，找到有dataScope字段的参数对象
        for (Object param : args) {
            if (param != null) {
                try {
                    // 通过反射设置dataScope属性
                    java.lang.reflect.Field field = param.getClass().getDeclaredField(DataScopeUtils.DATA_SCOPE);
                    field.setAccessible(true);
                    field.set(param, dataScopeSql);
                    log.debug("成功设置数据权限SQL到参数对象: {}", param.getClass().getSimpleName());
                    break; // 找到第一个有dataScope字段的参数即可
                } catch (NoSuchFieldException e) {
                    // 如果参数对象没有dataScope字段，继续查找下一个参数
                    log.debug("参数对象{}没有dataScope字段", param.getClass().getSimpleName());
                } catch (Exception e) {
                    log.warn("设置数据权限SQL失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 清除数据权限参数
     */
    private void clearDataScope(final JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return;
        }

        // 遍历所有参数，清除dataScope字段
        for (Object param : args) {
            if (param != null) {
                try {
                    java.lang.reflect.Field field = param.getClass().getDeclaredField(DataScopeUtils.DATA_SCOPE);
                    field.setAccessible(true);
                    field.set(param, "");
                } catch (NoSuchFieldException e) {
                    // 如果参数对象没有dataScope字段，继续查找下一个参数
                } catch (Exception e) {
                    // 忽略其他异常
                }
            }
        }
    }
}
