package com.xhcai.modules.rag.service;

import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.DocumentSegment;
import com.xhcai.modules.rag.enums.DocumentStatus;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文档处理服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDocumentProcessingService {

    /**
     * 解析文档内容
     *
     * @param file 文件
     * @return 解析后的文本内容
     */
    String parseDocument(MultipartFile file);

    /**
     * 解析文档内容（从文件路径）
     *
     * @param filePath 文件路径
     * @return 解析后的文本内容
     */
    String parseDocument(String filePath);

    /**
     * 文档分段
     *
     * @param content 文档内容
     * @param chunkSize 分段大小
     * @param chunkOverlap 分段重叠
     * @return 分段列表
     */
    List<String> splitDocument(String content, int chunkSize, int chunkOverlap);

    /**
     * 处理文档（解析 + 分段）
     *
     * @param document 文档实体
     * @param file 文件
     * @return 文档分段列表
     */
    List<DocumentSegment> processDocument(Document document, MultipartFile file);

    /**
     * 异步处理文档
     *
     * @param documentId 文档ID
     * @param documentStatus 文档状态
     */
    void processDocumentAsync(String documentId, DocumentStatus documentStatus);

    /**
     * 清理文档内容
     *
     * @param content 原始内容
     * @return 清理后的内容
     */
    String cleanContent(String content);

    /**
     * 提取文档元数据
     *
     * @param file 文件
     * @return 元数据信息
     */
    Object extractMetadata(MultipartFile file);

    /**
     * 检测文档语言
     *
     * @param content 文档内容
     * @return 语言代码
     */
    String detectLanguage(String content);

    /**
     * 计算文档统计信息
     *
     * @param content 文档内容
     * @return 统计信息（字符数、词数、段落数等）
     */
    Object calculateStats(String content);

    /**
     * 验证文档格式
     *
     * @param file 文件
     * @return 是否支持的格式
     */
    boolean isSupportedFormat(MultipartFile file);

    /**
     * 获取文档处理进度
     *
     * @param documentId 文档ID
     * @return 处理进度信息
     */
    Object getProcessingProgress(String documentId);

    /**
     * 取消文档处理
     *
     * @param documentId 文档ID
     * @return 是否取消成功
     */
    boolean cancelProcessing(String documentId);
}
