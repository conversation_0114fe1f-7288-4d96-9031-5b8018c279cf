package com.xhcai.modules.rag.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.FileStorageConfigCreateDTO;
import com.xhcai.modules.rag.dto.FileStorageConfigQueryDTO;
import com.xhcai.modules.rag.dto.FileStorageConfigUpdateDTO;
import com.xhcai.modules.rag.service.IFileStorageConfigService;
import com.xhcai.modules.rag.vo.FileStorageConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 文件存储配置控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/rag/file-storage")
@Tag(name = "文件存储配置管理", description = "文件存储配置管理相关接口")
public class FileStorageController {

    @Autowired
    private IFileStorageConfigService fileStorageConfigService;

    /**
     * 分页查询文件存储配置列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询文件存储配置列表")
    @RequiresPermissions("rag:file-storage:list")
    public Result<PageResult<FileStorageConfigVO>> getFileStorageConfigPage(
            @Parameter(description = "查询参数") FileStorageConfigQueryDTO queryDTO) {
        PageResult<FileStorageConfigVO> result = fileStorageConfigService.selectFileStorageConfigPage(queryDTO);
        return Result.success(result);
    }

    /**
     * 查询文件存储配置列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询文件存储配置列表")
    @RequiresPermissions("rag:file-storage:list")
    public Result<List<FileStorageConfigVO>> getFileStorageConfigList(
            @Parameter(description = "查询参数") FileStorageConfigQueryDTO queryDTO) {
        List<FileStorageConfigVO> result = fileStorageConfigService.selectFileStorageConfigList(queryDTO);
        return Result.success(result);
    }

    /**
     * 根据ID查询文件存储配置详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询文件存储配置详情")
    @RequiresPermissions("rag:file-storage:query")
    public Result<FileStorageConfigVO> getFileStorageConfigById(
            @Parameter(description = "配置ID") @PathVariable String id) {
        FileStorageConfigVO result = fileStorageConfigService.selectFileStorageConfigById(id);
        return Result.success(result);
    }

    /**
     * 创建文件存储配置
     */
    @PostMapping
    @Operation(summary = "创建文件存储配置")
    @RequiresPermissions("rag:file-storage:add")
    public Result<Void> createFileStorageConfig(
            @Parameter(description = "创建参数") @Valid @RequestBody FileStorageConfigCreateDTO createDTO) {
        boolean success = fileStorageConfigService.createFileStorageConfig(createDTO);
        return success ? Result.success() : Result.error("创建失败");
    }

    /**
     * 更新文件存储配置
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新文件存储配置")
    @RequiresPermissions("rag:file-storage:edit")
    public Result<Void> updateFileStorageConfig(
            @Parameter(description = "配置ID") @PathVariable String id,
            @Parameter(description = "更新参数") @Valid @RequestBody FileStorageConfigUpdateDTO updateDTO) {
        boolean success = fileStorageConfigService.updateFileStorageConfig(id, updateDTO);
        return success ? Result.success() : Result.error("更新失败");
    }

    /**
     * 删除文件存储配置
     */
    @DeleteMapping
    @Operation(summary = "删除文件存储配置")
    @RequiresPermissions("rag:file-storage:remove")
    public Result<Void> deleteFileStorageConfigs(
            @Parameter(description = "配置ID列表") @RequestBody List<String> ids) {
        boolean success = fileStorageConfigService.deleteFileStorageConfigs(ids);
        return success ? Result.success() : Result.error("删除失败");
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/status")
    @Operation(summary = "批量更新状态")
    @RequiresPermissions("rag:file-storage:edit")
    public Result<Void> batchUpdateStatus(
            @Parameter(description = "更新参数") @RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> ids = (List<String>) params.get("ids");
        String status = (String) params.get("status");
        
        boolean success = fileStorageConfigService.batchUpdateStatus(ids, status);
        return success ? Result.success() : Result.error("更新状态失败");
    }

    /**
     * 设置默认存储配置
     */
    @PutMapping("/{id}/default")
    @Operation(summary = "设置默认存储配置")
    @RequiresPermissions("rag:file-storage:edit")
    public Result<Void> setDefaultConfig(
            @Parameter(description = "配置ID") @PathVariable String id) {
        boolean success = fileStorageConfigService.setDefaultConfig(id);
        return success ? Result.success() : Result.error("设置默认配置失败");
    }

    /**
     * 获取默认存储配置
     */
    @GetMapping("/default")
    @Operation(summary = "获取默认存储配置")
    @RequiresPermissions("rag:file-storage:query")
    public Result<FileStorageConfigVO> getDefaultConfig() {
        FileStorageConfigVO result = fileStorageConfigService.getDefaultConfig();
        return Result.success(result);
    }

    /**
     * 根据存储类型获取配置列表
     */
    @GetMapping("/type/{storageType}")
    @Operation(summary = "根据存储类型获取配置列表")
    @RequiresPermissions("rag:file-storage:list")
    public Result<List<FileStorageConfigVO>> getConfigsByType(
            @Parameter(description = "存储类型") @PathVariable String storageType) {
        List<FileStorageConfigVO> result = fileStorageConfigService.getConfigsByType(storageType);
        return Result.success(result);
    }

    /**
     * 测试存储连接
     */
    @PostMapping("/{id}/test")
    @Operation(summary = "测试存储连接")
    @RequiresPermissions("rag:file-storage:test")
    public Result<Map<String, Object>> testConnection(
            @Parameter(description = "配置ID") @PathVariable String id) {
        boolean success = fileStorageConfigService.testConnection(id);
        Map<String, Object> result = Map.of(
                "success", success,
                "message", success ? "连接测试成功" : "连接测试失败"
        );
        return Result.success(result);
    }

    /**
     * 测试存储连接（根据配置）
     */
    @PostMapping("/test")
    @Operation(summary = "测试存储连接（根据配置）")
    @RequiresPermissions("rag:file-storage:test")
    public Result<Map<String, Object>> testConnectionByConfig(
            @Parameter(description = "存储配置") @Valid @RequestBody FileStorageConfigCreateDTO config) {
        boolean success = fileStorageConfigService.testConnection(config);
        Map<String, Object> result = Map.of(
                "success", success,
                "message", success ? "连接测试成功" : "连接测试失败"
        );
        return Result.success(result);
    }
}
