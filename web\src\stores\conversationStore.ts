import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 接口定义
export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  contentType?: 'text' | 'markdown' | 'html' | 'audio' | 'video' | 'file' | 'chart' | 'image' | 'flowchart'
  timestamp: Date
  files?: File[] | any[]
  images?: string[]
  agentFiles?: any[] // 智能体上传的文件
  message_files?: any[] // 从API返回的消息文件
  audioContent?: {
    url: string
    title?: string
    duration?: number
  }
  videoContent?: {
    url: string
    title?: string
    duration?: number
    poster?: string
  }
  chartData?: {
    type: string
    data: any
    options?: any
  }
  chartType?: 'bar' | 'line' | 'pie' | 'scatter'
  flowchartData?: {
    type: 'mermaid' | 'drawio' | 'custom'
    data: string | object
  }
  isTyping?: boolean
  streaming?: boolean
  // 后端返回的实际消息ID
  backendMessageId?: string
  // 当前节点信息（用于显示流式处理的节点状态）
  currentNode?: {
    title: string
    nodeType: string
    isActive: boolean
  } | null
}

export interface Conversation {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  archived?: boolean
  // 后端返回的实际对话ID
  backendConversationId?: string
}

export const useConversationStore = defineStore('conversation', () => {
  // 状态
  const conversations = ref<Conversation[]>([])
  const currentConversationId = ref<string | null>(null)
  
  // 计算属性
  const currentConversation = computed(() => {
    return conversations.value.find(c => c.id === currentConversationId.value) || null
  })
  
  const currentMessages = computed(() => {
    return currentConversation.value?.messages || []
  })
  
  const activeConversations = computed(() => {
    return conversations.value.filter(conv => !conv.archived)
  })
  
  const archivedConversations = computed(() => {
    return conversations.value.filter(conv => conv.archived)
  })
  
  // 按时间分组对话的通用函数
  const groupConversationsByTime = (conversationList: Conversation[]) => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

    const groups = [
      { title: '今天', conversations: [] as Conversation[] },
      { title: '昨天', conversations: [] as Conversation[] },
      { title: '7天内', conversations: [] as Conversation[] },
      { title: '30天内', conversations: [] as Conversation[] }
    ]

    const monthGroups: { [key: string]: Conversation[] } = {}

    conversationList.forEach(conv => {
      const convDate = new Date(conv.updatedAt)
      const convDateOnly = new Date(convDate.getFullYear(), convDate.getMonth(), convDate.getDate())

      if (convDateOnly.getTime() === today.getTime()) {
        groups[0].conversations.push(conv)
      } else if (convDateOnly.getTime() === yesterday.getTime()) {
        groups[1].conversations.push(conv)
      } else if (convDate >= sevenDaysAgo) {
        groups[2].conversations.push(conv)
      } else if (convDate >= thirtyDaysAgo) {
        groups[3].conversations.push(conv)
      } else {
        // 超过30天的按月分组
        const monthKey = convDate.getFullYear() + '年' + (convDate.getMonth() + 1) + '月'
        if (!monthGroups[monthKey]) {
          monthGroups[monthKey] = []
        }
        monthGroups[monthKey].push(conv)
      }
    })

    // 添加月份分组
    Object.keys(monthGroups).sort().reverse().forEach(monthKey => {
      groups.push({
        title: monthKey,
        conversations: monthGroups[monthKey].sort((a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        )
      })
    })

    // 过滤掉空的分组
    return groups.filter(group => group.conversations.length > 0)
  }

  // 按时间分组的活跃对话记录
  const activeGroupedConversations = computed(() => {
    return groupConversationsByTime(activeConversations.value)
  })

  // 按时间分组的归档对话记录
  const archivedGroupedConversations = computed(() => {
    return groupConversationsByTime(archivedConversations.value)
  })

  // 兼容性：保持原有的 groupedConversations（默认为活跃对话）
  const groupedConversations = computed(() => {
    return activeGroupedConversations.value
  })
  
  // 工具函数
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }
  
  // Actions
  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: generateId(),
      title: '新对话',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    conversations.value.unshift(newConversation)
    currentConversationId.value = newConversation.id
    saveConversations()
    
    console.log('新对话已创建:', {
      id: newConversation.id,
      currentConversationId: currentConversationId.value,
      conversationsCount: conversations.value.length
    })
    
    return newConversation
  }
  
  const selectConversation = (conversationId: string) => {
    currentConversationId.value = conversationId
  }

  // 强制清空聊天窗口（用于智能体切换）
  const clearChatWindow = () => {
    console.log('强制清空聊天窗口')

    // 先清空当前对话ID，强制触发响应式更新
    currentConversationId.value = null

    // 清空所有对话（确保彻底清空）
    conversations.value = []

    // 立即创建新的空对话
    const newConversation: Conversation = {
      id: generateId(),
      title: '新对话',
      messages: [], // 确保消息数组为空
      createdAt: new Date(),
      updatedAt: new Date()
    }

    conversations.value = [newConversation]
    currentConversationId.value = newConversation.id
    saveConversations()

    console.log('聊天窗口已强制清空，当前消息数量:', currentMessages.value.length)
    console.log('新对话详情:', newConversation)
  }

  const addMessage = (message: Omit<Message, 'id' | 'timestamp'> & { timestamp?: Date }) => {
    if (!currentConversation.value) {
      createNewConversation()
    }

    const newMessage: Message = {
      ...message,
      id: generateId(),
      timestamp: message.timestamp || new Date()
    }
    
    currentConversation.value!.messages.push(newMessage)
    
    // 更新对话标题（如果是第一条消息）
    if (currentConversation.value!.messages.length === 1 && currentConversation.value!.title === '新对话') {
      currentConversation.value!.title = message.content.substring(0, 20) + (message.content.length > 20 ? '...' : '')
    }
    
    // 更新对话时间
    currentConversation.value!.updatedAt = new Date()
    saveConversations()
    
    console.log('消息已添加:', {
      messageId: newMessage.id,
      conversationId: currentConversationId.value,
      messagesCount: currentConversation.value!.messages.length
    })
    
    return newMessage
  }
  
  const updateMessage = (messageId: string, updates: Partial<Message>) => {
    if (!currentConversation.value) return null

    const messageIndex = currentConversation.value.messages.findIndex(m => m.id === messageId)
    if (messageIndex === -1) return null

    Object.assign(currentConversation.value.messages[messageIndex], updates)
    currentConversation.value.updatedAt = new Date()
    saveConversations()

    return currentConversation.value.messages[messageIndex]
  }

  // 获取当前对话的最后一条AI回复消息ID（用作parent_message_id）
  const getLastMessageId = () => {
    if (!currentConversation.value || currentConversation.value.messages.length === 0) {
      return null
    }

    // 从后往前查找最后一条AI回复消息，排除正在流式输出的消息
    const messages = currentConversation.value.messages
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i]
      if (message.role === 'assistant' && !message.streaming) {
        // 只返回已完成的AI回复消息ID，优先返回后端消息ID
        console.log('找到上一条已完成的AI回复:', {
          localId: message.id,
          backendId: message.backendMessageId,
          content: message.content.substring(0, 50) + '...'
        })
        return message.backendMessageId || message.id
      }
    }

    console.log('没有找到已完成的AI回复消息')
    return null
  }

  // 获取当前对话的后端对话ID
  const getBackendConversationId = () => {
    if (!currentConversation.value) {
      console.log('getBackendConversationId: 没有当前对话')
      return null
    }
    const backendId = currentConversation.value.backendConversationId
    console.log('getBackendConversationId:', {
      currentConversationId: currentConversation.value.id,
      backendConversationId: backendId || '(null)',
      hasBackendId: !!backendId
    })
    return backendId || null
  }

  // 检查当前对话是否有消息历史
  const hasConversationHistory = () => {
    return currentConversation.value && currentConversation.value.messages.length > 0
  }

  // 更新对话的后端ID
  const updateBackendConversationId = (conversationId: string, backendConversationId: string) => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.backendConversationId = backendConversationId
      saveConversations()
      console.log('更新对话后端ID:', { conversationId, backendConversationId })
    }
  }

  // 更新消息的后端ID
  const updateBackendMessageId = (messageId: string, backendMessageId: string) => {
    if (!currentConversation.value) return

    const message = currentConversation.value.messages.find(m => m.id === messageId)
    if (message) {
      message.backendMessageId = backendMessageId
      saveConversations()
      console.log('更新消息后端ID:', { messageId, backendMessageId })
    }
  }
  
  const deleteConversation = (conversationId: string) => {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index > -1) {
      conversations.value.splice(index, 1)
      
      // 如果删除的是当前对话，切换到第一个对话或清空
      if (currentConversationId.value === conversationId) {
        currentConversationId.value = conversations.value.length > 0 ? conversations.value[0].id : null
      }
      
      saveConversations()
    }
  }
  
  const archiveConversation = (conversationId: string) => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.archived = true
      conversation.updatedAt = new Date()
      saveConversations()
    }
  }
  
  const updateConversationTitle = (conversationId: string, title: string) => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.title = title
      conversation.updatedAt = new Date()
      saveConversations()
    }
  }
  
  // 本地存储
  const saveConversations = () => {
    try {
      localStorage.setItem('ai-conversations', JSON.stringify(conversations.value))
    } catch (error) {
      console.error('保存对话失败:', error)
    }
  }
  
  const loadConversations = () => {
    try {
      const saved = localStorage.getItem('ai-conversations')
      if (saved) {
        const parsed = JSON.parse(saved)
        // 转换日期字符串为Date对象
        conversations.value = parsed.map((conv: any) => ({
          ...conv,
          createdAt: new Date(conv.createdAt),
          updatedAt: new Date(conv.updatedAt),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }))

        // 如果加载了对话但没有当前选中的对话，则自动选中第一条活跃对话
        if (conversations.value.length > 0 && !currentConversationId.value) {
          const firstActiveConversation = conversations.value.find(conv => !conv.archived)
          if (firstActiveConversation) {
            currentConversationId.value = firstActiveConversation.id
            console.log('自动选中第一条对话:', firstActiveConversation.id)
          }
        }
      }
    } catch (error) {
      console.error('加载对话失败:', error)
    }
  }
  
  // 初始化
  loadConversations()
  
  return {
    // 状态
    conversations,
    currentConversationId,
    
    // 计算属性
    currentConversation,
    currentMessages,
    activeConversations,
    archivedConversations,
    groupedConversations,
    activeGroupedConversations,
    archivedGroupedConversations,
    
    // Actions
    createNewConversation,
    clearChatWindow,
    selectConversation,
    addMessage,
    updateMessage,
    deleteConversation,
    archiveConversation,
    updateConversationTitle,
    getLastMessageId,
    getBackendConversationId,
    hasConversationHistory,
    updateBackendConversationId,
    updateBackendMessageId,
    saveConversations,
    loadConversations
  }
})
