/**
 * 系统管理相关类型定义
 */

// 分页查询基础类型
export interface PageQueryDTO {
  current?: number
  size?: number
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
}

// 分页结果类型
export interface PageResult<T> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

// 用户查询DTO
export interface SysUserQueryDTO extends PageQueryDTO {
  username?: string
  nickname?: string
  email?: string
  phone?: string
  status?: string
  deptId?: string
  gender?: string
  beginTime?: string
  endTime?: string
}

// 用户信息VO
export interface SysUserVO {
  id: string
  username?: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  gender?: string
  genderName?: string
  birthday?: string
  deptId?: string
  deptName?: string
  status: string
  statusName?: string
  userType?: string
  userTypeName?: string
  loginIp?: string
  loginTime?: string
  roles?: string[]
  permissions?: string[]
  tenantId?: string
  remark?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  roleIds?: string[]
  roleNames?: string[]
}

// 用户实体
export interface SysUser {
  id?: string
  username: string
  password?: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  gender?: string
  birthday?: string
  deptId?: string
  status?: string
  remark?: string
}

// 部门查询DTO
export interface SysDeptQueryDTO extends PageQueryDTO {
  deptName?: string
  deptCode?: string
  leaderId?: string
  status?: string
  parentId?: string
  includeChildren?: boolean
}

// 部门信息VO
export interface SysDeptVO {
  id: string
  parentId?: string
  ancestors?: string
  deptName: string
  deptCode?: string
  orderNum?: number
  leaderId?: string
  leaderName?: string
  status: string
  statusName?: string
  children?: SysDeptVO[]
  parentName?: string
  level?: number
  hasChildren?: boolean
  userCount?: number
  createTime?: string
  updateTime?: string
}

// 部门实体
export interface SysDept {
  id?: string
  parentId?: string
  ancestors?: string
  deptName: string
  deptCode?: string
  orderNum?: number
  leaderId?: string
  status?: string
  remark?: string
}

// 字典类型VO
export interface SysDictTypeVO {
  id: string
  dictName: string
  dictType: string
  status: string
  isSystemDict?: string
  tenantId?: string
  remark?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
}

// 字典数据VO
export interface SysDictDataVO {
  id: string
  dictSort?: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass?: string
  listClass?: string
  isDefault?: string
  status: string
  isSystemDict?: string
  tenantId?: string
  remark?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
}

// 字典类型查询DTO
export interface DictTypeQueryDTO {
  dictName?: string
  dictType?: string
  status?: string
  pageNum?: number
  pageSize?: number
}

// 字典数据查询DTO
export interface DictDataQueryDTO {
  dictType?: string
  dictLabel?: string
  status?: string
  pageNum?: number
  pageSize?: number
}

// 字典类型创建/更新DTO
export interface DictTypeDTO {
  id?: string
  dictName: string
  dictType: string
  status: string
  remark?: string
}

// 字典数据创建/更新DTO
export interface DictDataDTO {
  id?: string
  dictSort?: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass?: string
  listClass?: string
  isDefault?: string
  status: string
  remark?: string
}

// 重置密码请求
export interface ResetPasswordRequest {
  userId: string
  newPassword: string
}

// 用户授权请求
export interface UserAuthRequest {
  userId: string
  roleIds: string[]
  permissions: string[]
}

// 角色查询DTO
export interface SysRoleQueryDTO extends PageQueryDTO {
  roleCode?: string
  roleName?: string
  status?: string
  dataScope?: string
}

// 角色信息VO
export interface SysRoleVO {
  id: string
  roleCode: string
  roleKey?: string
  roleName: string
  roleSort?: number
  dataScope?: string
  dataScopeName?: string
  menuCheckStrictly?: boolean
  deptCheckStrictly?: boolean
  status: string
  statusName?: string
  userCount?: number
  permissionIds?: string[]
  createTime?: string
  updateTime?: string
  remark?: string
}

// 角色实体
export interface SysRole {
  id?: string
  roleCode: string
  roleName: string
  roleSort?: number
  dataScope?: string
  menuCheckStrictly?: boolean
  deptCheckStrictly?: boolean
  status?: string
  remark?: string
}

// 权限查询DTO
export interface SysPermissionQueryDTO extends PageQueryDTO {
  permissionCode?: string
  permissionName?: string
  permissionType?: string
  parentId?: string
  status?: string
  visible?: boolean
}

// 权限信息VO
export interface SysPermissionVO {
  id: string
  permissionCode: string
  permissionName: string
  permissionType: string
  parentId?: string
  permissionPath?: string
  icon?: string
  component?: string
  orderNum?: number
  isFrame?: boolean
  isCache?: boolean
  visible?: boolean
  status: string
  statusName?: string
  children?: SysPermissionVO[]
  parentName?: string
  level?: number
  hasChildren?: boolean
  createTime?: string
  updateTime?: string
  remark?: string
}

// 权限实体
export interface SysPermission {
  id?: string
  permissionCode: string
  permissionName: string
  permissionType: string
  parentId?: string
  permissionPath?: string
  icon?: string
  component?: string
  orderNum?: number
  isFrame?: boolean
  isCache?: boolean
  visible?: boolean
  status?: string
  remark?: string
}

// 通用选择器选项接口
export interface SelectorOption {
  value: string
  label: string
  disabled?: boolean
  children?: SelectorOption[]
  [key: string]: any
}

// 选择器配置接口
export interface SelectorConfig {
  multiple?: boolean
  clearable?: boolean
  filterable?: boolean
  placeholder?: string
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  loading?: boolean
  checkStrictly?: boolean
  showCheckbox?: boolean
  expandOnClickNode?: boolean
  defaultExpandAll?: boolean
  filterNodeMethod?: (value: string, data: any) => boolean
}

// 部门选择器专用接口
export interface DeptSelectorOption extends SelectorOption {
  deptCode?: string
  parentId?: string
  orderNum?: number
  leaderId?: string
  leaderName?: string
  status: string
  userCount?: number
  level?: number
}

// 用户选择器专用接口
export interface UserSelectorOption extends SelectorOption {
  username: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  deptId?: string
  deptName?: string
  status: string
  roles?: string[]
}

// 角色选择器专用接口
export interface RoleSelectorOption extends SelectorOption {
  roleCode: string
  roleSort?: number
  dataScope?: string
  status: string
  userCount?: number
  permissionIds?: string[]
}

// 权限选择器专用接口
export interface PermissionSelectorOption extends SelectorOption {
  permissionCode: string
  permissionType: string
  parentId?: string
  permissionPath?: string
  icon?: string
  orderNum?: number
  status: string
  level?: number
}

// 租户查询DTO
export interface SysTenantQueryDTO extends PageQueryDTO {
  tenantCode?: string
  tenantName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  status?: string
  includeExpired?: boolean
}

// 租户信息VO
export interface SysTenantVO {
  id: string
  tenantCode: string
  tenantName: string
  tenantShortName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  address?: string
  logo?: string
  domain?: string
  status: string
  statusName?: string
  expireTime?: string
  userLimit?: number
  currentUserCount?: number
  storageLimit?: number
  usedStorage?: number
  description?: string
  isExpired?: boolean
  createTime?: string
  updateTime?: string
}

// 租户实体
export interface SysTenant {
  id?: string
  tenantCode: string
  tenantName: string
  tenantShortName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  address?: string
  logo?: string
  domain?: string
  status?: string
  expireTime?: string
  userLimit?: number
  storageLimit?: number
  description?: string
}

// ==================== 系统配置相关接口 ====================

/**
 * 系统配置实体
 */
export interface SysConfig {
  id?: string
  configName: string
  configKey: string
  configValue: string
  configType: string
  configGroup?: string
  remark?: string
  status?: string
  tenantId?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  deleted?: number
}

/**
 * 系统配置DTO
 */
export interface SysConfigDTO {
  id?: string
  configName: string
  configKey: string
  configValue?: string
  configType?: string
  configGroup?: string
  remark?: string
  status?: string
}

/**
 * 系统配置VO
 */
export interface SysConfigVO extends SysConfig {
  createByName?: string
  updateByName?: string
}

/**
 * 系统配置查询DTO
 */
export interface SysConfigQueryDTO extends PageTimeRangeQueryDTO {
  configName?: string
  configKey?: string
  configType?: string
  configGroup?: string
  status?: string
}

/**
 * 系统设置保存DTO
 */
export interface SystemSettingsDTO {
  logo?: string
  title?: string
  subtitle?: string
  description?: string
  language?: string
  timezone?: string
}
