package com.xhcai.modules.dify.service;

import com.xhcai.modules.dify.dto.auth.DifyLoginResponseDTO;
import com.xhcai.modules.dify.dto.auth.DifyRefreshTokenResponseDTO;
import reactor.core.publisher.Mono;

/**
 * Dify 认证服务接口
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
public interface IDifyAuthService {

    /**
     * 获取有效的访问令牌
     * 如果 Redis 中没有令牌或令牌无效，会自动登录获取新令牌
     *
     * @return 访问令牌的 Mono
     */
    Mono<String> getValidAccessToken();

    /**
     * 同步获取有效的访问令牌（用于非响应式上下文）
     * 如果 Redis 中没有令牌或令牌无效，会自动登录获取新令牌
     *
     * @return 访问令牌
     */
    String getValidAccessTokenSync();

    /**
     * 登录 Dify 获取令牌
     *
     * @return 登录响应的 Mono
     */
    Mono<DifyLoginResponseDTO> login();

    /**
     * 刷新访问令牌
     *
     * @return 刷新令牌响应的 Mono
     */
    Mono<DifyRefreshTokenResponseDTO> refreshToken();

    /**
     * 清除 Redis 中的令牌
     *
     * @return 完成信号的 Mono
     */
    Mono<Void> clearTokens();

    /**
     * 检查访问令牌是否有效
     *
     * @param accessToken 访问令牌
     * @return 是否有效的 Mono
     */
    Mono<Boolean> isAccessTokenValid(String accessToken);

    /**
     * 处理 401 错误（令牌过期）
     * 尝试刷新令牌，如果刷新失败则重新登录
     *
     * @return 新的访问令牌的 Mono
     */
    Mono<String> handleUnauthorized();

    /**
     * 同步处理 401 错误（令牌过期）
     * 尝试刷新令牌，如果刷新失败则重新登录
     *
     * @return 新的访问令牌
     */
    String handleUnauthorizedSync();

    /**
     * 处理 600 错误（需要重新登录）
     * 清除旧令牌并重新登录
     *
     * @return 新的访问令牌的 Mono
     */
    Mono<String> handleRelogin();

    /**
     * 同步处理 600 错误（需要重新登录）
     * 清除旧令牌并重新登录
     *
     * @return 新的访问令牌
     */
    String handleReloginSync();
}
