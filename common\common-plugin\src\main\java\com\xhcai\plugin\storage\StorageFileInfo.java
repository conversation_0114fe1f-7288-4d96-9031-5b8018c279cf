package com.xhcai.plugin.storage;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 存储文件信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StorageFileInfo {
    
    /**
     * 存储桶名称
     */
    private String bucketName;
    
    /**
     * 对象名称
     */
    private String objectName;
    
    /**
     * 文件大小（字节）
     */
    private Long size;
    
    /**
     * 文件类型
     */
    private String contentType;
    
    /**
     * ETag
     */
    private String etag;
    
    /**
     * 最后修改时间
     */
    private LocalDateTime lastModified;
    
    /**
     * 文件元数据
     */
    private Map<String, String> metadata;
    
    /**
     * 文件访问URL
     */
    private String url;
    
    /**
     * 是否为目录
     */
    private Boolean isDirectory;
    
    /**
     * 存储类型
     */
    private String storageClass;
}
