<template>
  <div class="conversation-messages-test p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">会话消息获取测试</h1>
      
      <!-- 测试控制区域 -->
      <div class="bg-white rounded-lg shadow p-4 mb-6">
        <h2 class="text-lg font-semibold mb-4">测试控制</h2>
        
        <div class="flex flex-col gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              智能体 AppId:
            </label>
            <input
              v-model="testAppId"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入智能体的AppId"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              对话 ConversationId:
            </label>
            <input
              v-model="testConversationId"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入对话的ConversationId"
            />
          </div>
          
          <div class="flex gap-2">
            <button
              @click="fetchMessages"
              :disabled="loading || !testAppId || !testConversationId"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {{ loading ? '获取中...' : '获取会话消息' }}
            </button>
            
            <button
              @click="clearResults"
              class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              清空结果
            </button>
          </div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <h3 class="text-red-800 font-semibold mb-2">错误信息:</h3>
        <p class="text-red-700">{{ error }}</p>
      </div>
      
      <!-- 结果显示区域 -->
      <div v-if="messages.length > 0" class="bg-white rounded-lg shadow p-4">
        <h2 class="text-lg font-semibold mb-4">
          会话消息列表 (共 {{ messages.length }} 条)
        </h2>
        
        <div class="space-y-4">
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="border border-gray-200 rounded-lg p-4"
            :class="{
              'bg-blue-50': message.role === 'user',
              'bg-green-50': message.role === 'assistant'
            }"
          >
            <div class="flex justify-between items-start mb-2">
              <div class="flex items-center gap-2">
                <span class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="{
                        'bg-blue-100 text-blue-800': message.role === 'user',
                        'bg-green-100 text-green-800': message.role === 'assistant'
                      }">
                  {{ message.role === 'user' ? '用户' : 'AI助手' }}
                </span>
                <span class="text-xs text-gray-500">
                  {{ formatTimestamp(message.timestamp) }}
                </span>
              </div>
            </div>
            
            <div class="text-sm text-gray-800">
              <div v-if="message.contentType === 'markdown'" class="prose prose-sm max-w-none">
                <div v-html="renderMarkdown(message.content)"></div>
              </div>
              <div v-else class="whitespace-pre-wrap">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading && !error" class="bg-gray-50 rounded-lg p-8 text-center">
        <p class="text-gray-600">暂无消息，请输入AppId和ConversationId并点击获取按钮</p>
      </div>
      
      <!-- 原始数据展示 -->
      <div v-if="rawData" class="mt-6 bg-gray-100 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-2">原始响应数据:</h3>
        <pre class="text-sm text-gray-700 overflow-auto max-h-96">{{ JSON.stringify(rawData, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { exploreApi, type ConversationMessagesResponse } from '@/api/explore'
import { marked } from 'marked'

// 响应式数据
const testAppId = ref('3778735c-cc1a-41e3-988f-7a108e3eafb0') // 默认使用通用助手的AppId
const testConversationId = ref('') // 需要用户输入
const loading = ref(false)
const error = ref('')
const messages = ref<any[]>([])
const rawData = ref<any>(null)

// 获取会话消息
const fetchMessages = async () => {
  if (!testAppId.value.trim() || !testConversationId.value.trim()) {
    error.value = '请输入AppId和ConversationId'
    return
  }
  
  loading.value = true
  error.value = ''
  messages.value = []
  rawData.value = null
  
  try {
    console.log('开始获取会话消息，AppId:', testAppId.value, 'ConversationId:', testConversationId.value)
    const result = await exploreApi.getConversationMessages(testAppId.value, testConversationId.value, {
      limit: 50
    })
    
    console.log('获取会话消息成功:', result)
    rawData.value = result
    
    // 转换消息格式
    const convertedMessages = convertBackendMessagesToLocal(result.data)
    messages.value = convertedMessages
    
  } catch (err: any) {
    console.error('获取会话消息失败:', err)
    error.value = err.message || '获取会话消息失败'
  } finally {
    loading.value = false
  }
}

// 转换后端消息格式为前端格式
const convertBackendMessagesToLocal = (backendMessages: any[]) => {
  const messages: any[] = []

  backendMessages.forEach(msg => {
    // 优先使用createdTime字段，如果没有则使用created_at
    const messageTime = msg.createdTime ? new Date(msg.createdTime) : new Date(msg.created_at * 1000)

    // 添加用户消息
    if (msg.query) {
      const userMessage: any = {
        role: 'user',
        content: msg.query,
        contentType: 'text',
        timestamp: messageTime
      }

      // 添加message_files字段（如果存在）
      if (msg.message_files && msg.message_files.length > 0) {
        userMessage.message_files = msg.message_files
      }

      messages.push(userMessage)
    }

    // 添加AI回复消息
    if (msg.answer) {
      // 保留完整的回复内容，包括 <think> 标签，让MarkdownRenderer处理
      let content = msg.answer

      messages.push({
        role: 'assistant',
        content: content.trim(),
        contentType: 'markdown',
        timestamp: messageTime
      })
    }
  })
  
  // 按时间戳排序
  return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
}

// 清空结果
const clearResults = () => {
  messages.value = []
  error.value = ''
  rawData.value = null
}

// 格式化时间戳
const formatTimestamp = (timestamp: Date) => {
  if (!timestamp) return '未知'
  
  return timestamp.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 渲染Markdown
const renderMarkdown = (content: string) => {
  try {
    return marked(content)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return content
  }
}
</script>

<style scoped>
.conversation-messages-test {
  min-height: 100vh;
  background: linear-gradient(to bottom right, #f8fafc, #e2e8f0);
}

.prose {
  color: inherit;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: inherit;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose ul, .prose ol {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
</style>
