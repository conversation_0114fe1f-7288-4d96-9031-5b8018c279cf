package com.xhcai.modules.agent.vo;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体工作流版本VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体工作流版本信息")
public class AgentWorkflowVersionVO {

    private static final long serialVersionUID = 1L;

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "workflow_001")
    private String id;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    private String agentId;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1")
    private Integer version;

    /**
     * 工作流名称
     */
    @Schema(description = "工作流名称", example = "客服工作流")
    private String name;

    /**
     * 工作流描述
     */
    @Schema(description = "工作流描述", example = "智能客服的工作流程配置")
    private String description;

    /**
     * 是否已发布
     */
    @Schema(description = "是否已发布", example = "false")
    private Boolean isPublished;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private LocalDateTime publishedAt;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    private LocalDateTime lastModified;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "node_add")
    private String operationType;

    /**
     * 操作描述
     */
    @Schema(description = "操作描述", example = "添加了开始节点")
    private String operationDesc;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "admin")
    private String createBy;

    /**
     * 是否为当前版本
     */
    @Schema(description = "是否为当前版本", example = "true")
    private Boolean isCurrent;

    /**
     * 节点数量
     */
    @Schema(description = "节点数量", example = "5")
    private Integer nodeCount;

    /**
     * 连接数量
     */
    @Schema(description = "连接数量", example = "4")
    private Integer connectionCount;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public LocalDateTime getLastModified() {
        return lastModified;
    }

    public void setLastModified(LocalDateTime lastModified) {
        this.lastModified = lastModified;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Boolean getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Boolean isCurrent) {
        this.isCurrent = isCurrent;
    }

    public Integer getNodeCount() {
        return nodeCount;
    }

    public void setNodeCount(Integer nodeCount) {
        this.nodeCount = nodeCount;
    }

    public Integer getConnectionCount() {
        return connectionCount;
    }

    public void setConnectionCount(Integer connectionCount) {
        this.connectionCount = connectionCount;
    }
}
