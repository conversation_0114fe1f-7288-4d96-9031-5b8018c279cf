package com.xhcai.common.api.query;

/**
 * 分页查询接口
 * 定义分页查询的基本方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface Pageable {

    /**
     * 获取当前页码
     * 
     * @return 当前页码，从1开始
     */
    Long getCurrent();

    /**
     * 获取每页大小
     * 
     * @return 每页大小
     */
    Long getSize();

    /**
     * 获取排序字段
     * 
     * @return 排序字段
     */
    String getOrderBy();

    /**
     * 获取排序方向
     * 
     * @return 排序方向：asc-升序，desc-降序
     */
    String getOrderDirection();

    /**
     * 是否查询总数
     * 
     * @return true-查询总数，false-不查询总数
     */
    Boolean getSearchCount();

    /**
     * 获取偏移量，用于数据库查询
     * 
     * @return 偏移量
     */
    default Long getOffset() {
        return (getCurrent() - 1) * getSize();
    }

    /**
     * 获取限制数量，用于数据库查询
     * 
     * @return 限制数量
     */
    default Long getLimit() {
        return getSize();
    }

    /**
     * 是否为升序排序
     * 
     * @return true-升序，false-降序
     */
    default Boolean isAsc() {
        return "asc".equalsIgnoreCase(getOrderDirection());
    }

    /**
     * 是否为降序排序
     * 
     * @return true-降序，false-升序
     */
    default Boolean isDesc() {
        return "desc".equalsIgnoreCase(getOrderDirection());
    }
}
