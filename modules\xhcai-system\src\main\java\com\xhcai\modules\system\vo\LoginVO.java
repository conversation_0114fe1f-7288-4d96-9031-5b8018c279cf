package com.xhcai.modules.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 登录响应VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "登录响应VO")
public class LoginVO {

    /**
     * 访问令牌
     */
    @Schema(description = "访问令牌")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @Schema(description = "刷新令牌")
    private String refreshToken;

    /**
     * 令牌类型
     */
    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    /**
     * 过期时间（秒）
     */
    @Schema(description = "过期时间（秒）", example = "86400")
    private Long expiresIn;

    /**
     * 用户信息
     */
    @Schema(description = "用户信息")
    private UserInfo userInfo;

    /**
     * 用户权限
     */
    @Schema(description = "用户权限")
    private Set<String> permissions;

    /**
     * 用户角色
     */
    @Schema(description = "用户角色")
    private Set<String> roles;

    /**
     * 用户菜单
     */
    @Schema(description = "用户菜单")
    private List<SysPermissionVO> menus;

    /**
     * 用户信息内部类
     */
    @Schema(description = "用户信息")
    public static class UserInfo {
        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private String userId;

        /**
         * 用户名
         */
        @Schema(description = "用户名", example = "admin")
        private String username;

        /**
         * 昵称
         */
        @Schema(description = "昵称", example = "管理员")
        private String nickname;

        /**
         * 邮箱
         */
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;

        /**
         * 手机号
         */
        @Schema(description = "手机号", example = "13800138000")
        private String phone;

        /**
         * 头像
         */
        @Schema(description = "头像")
        private String avatar;

        /**
         * 性别
         */
        @Schema(description = "性别", example = "1")
        private String gender;

        /**
         * 部门ID
         */
        @Schema(description = "部门ID", example = "1")
        private String deptId;

        /**
         * 部门名称
         */
        @Schema(description = "部门名称", example = "技术部")
        private String deptName;

        /**
         * 租户ID
         */
        @Schema(description = "租户ID", example = "1")
        private String tenantId;

        /**
         * 状态
         */
        @Schema(description = "状态", example = "0")
        private String status;

        /**
         * 最后登录IP
         */
        @Schema(description = "最后登录IP")
        private String loginIp;

        /**
         * 最后登录时间
         */
        @Schema(description = "最后登录时间")
        private LocalDateTime loginTime;

        // Getters and Setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getGender() {
            return gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public String getDeptId() {
            return deptId;
        }

        public void setDeptId(String deptId) {
            this.deptId = deptId;
        }

        public String getDeptName() {
            return deptName;
        }

        public void setDeptName(String deptName) {
            this.deptName = deptName;
        }

        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getLoginIp() {
            return loginIp;
        }

        public void setLoginIp(String loginIp) {
            this.loginIp = loginIp;
        }

        public LocalDateTime getLoginTime() {
            return loginTime;
        }

        public void setLoginTime(LocalDateTime loginTime) {
            this.loginTime = loginTime;
        }
    }

    // Getters and Setters
    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    public Set<String> getRoles() {
        return roles;
    }

    public void setRoles(Set<String> roles) {
        this.roles = roles;
    }

    public List<SysPermissionVO> getMenus() {
        return menus;
    }

    public void setMenus(List<SysPermissionVO> menus) {
        this.menus = menus;
    }
}
