'use client';

import { useState, useEffect } from 'react';
import { 
  Activity, 
  Server, 
  AlertTriangle, 
  CheckCircle, 
  Cpu, 
  MemoryStick, 
  HardDrive,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { ComponentMonitor, SystemResourceUsage } from '@/types/monitor';
import { ElasticComponent } from '@/types/component';
import MonitorAPI from '@/api/monitor';
import ComponentsAPI from '@/api/components';
import toast from 'react-hot-toast';

export default function MonitorDashboard() {
  const [components, setComponents] = useState<ElasticComponent[]>([]);
  const [monitorData, setMonitorData] = useState<ComponentMonitor[]>([]);
  const [systemResources, setSystemResources] = useState<SystemResourceUsage | null>(null);
  const [unhealthyComponents, setUnhealthyComponents] = useState<ComponentMonitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
    // 设置自动刷新
    const interval = setInterval(loadDashboardData, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      if (!loading) setRefreshing(true);

      // 并行加载数据
      const [componentsRes, monitorRes, systemRes, unhealthyRes] = await Promise.all([
        ComponentsAPI.getComponents(),
        MonitorAPI.getAllLatestMonitorData(),
        MonitorAPI.getSystemResourceUsage(),
        MonitorAPI.getUnhealthyComponents(),
      ]);

      if (componentsRes.success && componentsRes.data) {
        setComponents(componentsRes.data);
      }

      if (monitorRes.success && monitorRes.data) {
        setMonitorData(monitorRes.data);
      }

      if (systemRes.success && systemRes.data) {
        setSystemResources(systemRes.data);
      }

      if (unhealthyRes.success && unhealthyRes.data) {
        setUnhealthyComponents(unhealthyRes.data);
      }
    } catch (error: any) {
      console.error('加载监控数据失败:', error);
      toast.error('加载监控数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getHealthyCount = () => {
    return monitorData.filter(m => m.isHealthy).length;
  };

  const getRunningCount = () => {
    return components.filter(c => c.status === 'RUNNING').length;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">加载监控数据...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">监控仪表板</h2>
          <p className="text-gray-600">实时监控系统和组件运行状态</p>
        </div>
        <button
          onClick={loadDashboardData}
          disabled={refreshing}
          className="btn-outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          刷新
        </button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Components */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Server className="h-8 w-8 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总组件数</p>
                <p className="text-2xl font-semibold text-gray-900">{components.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Running Components */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="h-8 w-8 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">运行中</p>
                <p className="text-2xl font-semibold text-gray-900">{getRunningCount()}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Healthy Components */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">健康组件</p>
                <p className="text-2xl font-semibold text-gray-900">{getHealthyCount()}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Unhealthy Components */}
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-error-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">异常组件</p>
                <p className="text-2xl font-semibold text-gray-900">{unhealthyComponents.length}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* System Resources */}
      {systemResources && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">系统资源使用情况</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* CPU Usage */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Cpu className="h-5 w-5 text-primary-600" />
                    <span className="font-medium text-gray-700">CPU</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {systemResources.cpuUsage.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      systemResources.cpuUsage > 80 
                        ? 'bg-error-600' 
                        : systemResources.cpuUsage > 60 
                        ? 'bg-warning-600' 
                        : 'bg-primary-600'
                    }`}
                    style={{ width: `${systemResources.cpuUsage}%` }}
                  ></div>
                </div>
              </div>

              {/* Memory Usage */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MemoryStick className="h-5 w-5 text-success-600" />
                    <span className="font-medium text-gray-700">内存</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {formatBytes(systemResources.usedMemory)} / {formatBytes(systemResources.totalMemory)}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      systemResources.memoryUsagePercent > 80 
                        ? 'bg-error-600' 
                        : systemResources.memoryUsagePercent > 60 
                        ? 'bg-warning-600' 
                        : 'bg-success-600'
                    }`}
                    style={{ width: `${systemResources.memoryUsagePercent}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500">
                  {systemResources.memoryUsagePercent.toFixed(1)}% 已使用
                </div>
              </div>

              {/* Disk Usage */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <HardDrive className="h-5 w-5 text-warning-600" />
                    <span className="font-medium text-gray-700">磁盘</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {formatBytes(systemResources.usedDisk)} / {formatBytes(systemResources.totalDisk)}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      systemResources.diskUsagePercent > 80 
                        ? 'bg-error-600' 
                        : systemResources.diskUsagePercent > 60 
                        ? 'bg-warning-600' 
                        : 'bg-warning-600'
                    }`}
                    style={{ width: `${systemResources.diskUsagePercent}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500">
                  {systemResources.diskUsagePercent.toFixed(1)}% 已使用
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Component Status Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Component Health Status */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">组件健康状态</h3>
          </div>
          <div className="card-body">
            {monitorData.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">暂无监控数据</p>
              </div>
            ) : (
              <div className="space-y-4">
                {monitorData.slice(0, 5).map((monitor) => {
                  const component = components.find(c => c.id === monitor.componentId);
                  if (!component) return null;

                  return (
                    <div key={monitor.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {monitor.isHealthy ? (
                          <CheckCircle className="h-5 w-5 text-success-600" />
                        ) : (
                          <AlertTriangle className="h-5 w-5 text-error-600" />
                        )}
                        <div>
                          <p className="font-medium text-gray-900">{component.name}</p>
                          <p className="text-sm text-gray-500">{component.type}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          {monitor.cpuUsage !== undefined && (
                            <div className="flex items-center space-x-1">
                              <Cpu className="h-3 w-3" />
                              <span>{monitor.cpuUsage.toFixed(1)}%</span>
                            </div>
                          )}
                          {monitor.memoryUsagePercent !== undefined && (
                            <div className="flex items-center space-x-1">
                              <MemoryStick className="h-3 w-3" />
                              <span>{monitor.memoryUsagePercent.toFixed(1)}%</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Unhealthy Components */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">异常组件</h3>
          </div>
          <div className="card-body">
            {unhealthyComponents.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-success-400 mx-auto mb-4" />
                <p className="text-success-600 font-medium">所有组件运行正常</p>
              </div>
            ) : (
              <div className="space-y-4">
                {unhealthyComponents.map((monitor) => {
                  const component = components.find(c => c.id === monitor.componentId);
                  if (!component) return null;

                  return (
                    <div key={monitor.id} className="flex items-center justify-between p-3 bg-error-50 border border-error-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className="h-5 w-5 text-error-600" />
                        <div>
                          <p className="font-medium text-gray-900">{component.name}</p>
                          <p className="text-sm text-error-600">
                            {monitor.healthMessage || monitor.errorMessage || '组件异常'}
                          </p>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(monitor.updateTime).toLocaleTimeString()}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Performance Chart */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">系统性能趋势</h3>
        </div>
        <div className="card-body">
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={generateMockChartData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="cpu" 
                  stackId="1" 
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.6}
                  name="CPU使用率 (%)"
                />
                <Area 
                  type="monotone" 
                  dataKey="memory" 
                  stackId="2" 
                  stroke="#10B981" 
                  fill="#10B981" 
                  fillOpacity={0.6}
                  name="内存使用率 (%)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Last Update Info */}
      <div className="text-center text-sm text-gray-500">
        最后更新: {new Date().toLocaleString()}
      </div>
    </div>
  );
}

// Mock data generator for chart
function generateMockChartData() {
  const data = [];
  const now = new Date();
  
  for (let i = 23; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60 * 60 * 1000);
    data.push({
      time: time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      cpu: Math.random() * 30 + 20,
      memory: Math.random() * 40 + 30,
    });
  }
  
  return data;
}
