<template>
  <div class="base-file-viewer">
    <div class="viewer-content">
      <slot name="content">
        <div class="default-content">
          <div class="loading-state" v-if="loading">
            <div class="loading-spinner"></div>
            <p>正在加载文件内容...</p>
          </div>
          <div class="error-state" v-else-if="error">
            <div class="error-icon">⚠️</div>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="refreshContent">重试</button>
            <button class="btn btn-primary" @click="downloadFile">下载文件</button>
          </div>
          <div class="empty-state" v-else-if="!content">
            <div class="empty-icon">📄</div>
            <p>暂无内容</p>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Props
interface Props {
  file: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  download: [file: any]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const content = ref('')

// 方法
const downloadFile = () => {
  emit('download', props.file)
}

const refreshContent = () => {
  emit('refresh')
  loadContent()
}

const loadContent = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 这里应该根据文件类型调用不同的API
    await new Promise(resolve => setTimeout(resolve, 1000))
    content.value = '文件内容加载完成'
  } catch (err) {
    error.value = '加载文件内容失败'
  } finally {
    loading.value = false
  }
}

// 其他方法

// 生命周期
onMounted(() => {
  loadContent()
})
</script>

<style scoped>
.base-file-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.viewer-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.default-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
