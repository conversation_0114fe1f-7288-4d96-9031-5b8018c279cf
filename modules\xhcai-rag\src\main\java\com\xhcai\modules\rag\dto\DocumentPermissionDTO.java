package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 文档权限设置DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档权限设置DTO")
@Data
public class DocumentPermissionDTO {

    /**
     * 文档ID列表（批量设置时使用）
     */
    @Schema(description = "文档ID列表", example = "[\"doc1\", \"doc2\"]")
    @NotEmpty(message = "文档ID列表不能为空")
    private List<String> documentIds;

    /**
     * 权限类型
     */
    @Schema(description = "权限类型", example = "public", allowableValues = {"public", "private", "role", "department", "users"})
    @NotBlank(message = "权限类型不能为空")
    private String permissionType;

    /**
     * 角色ID列表
     */
    @Schema(description = "角色ID列表", example = "[\"role1\", \"role2\"]")
    private List<String> roleIds;

    /**
     * 部门ID列表
     */
    @Schema(description = "部门ID列表", example = "[\"dept1\", \"dept2\"]")
    private List<String> departmentIds;

    /**
     * 用户ID列表
     */
    @Schema(description = "用户ID列表", example = "[\"user1\", \"user2\"]")
    private List<String> userIds;

    /**
     * 权限级别
     */
    @Schema(description = "权限级别", example = "read", allowableValues = {"read", "write", "admin"})
    private String permissionLevel = "read";
}
