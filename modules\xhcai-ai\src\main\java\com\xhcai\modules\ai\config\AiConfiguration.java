package com.xhcai.modules.ai.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * AI配置类 解决多个EmbeddingModel Bean冲突问题 通过排除Ollama自动配置，只使用OpenAI作为主要的EmbeddingModel
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class AiConfiguration {

    private static final Logger log = LoggerFactory.getLogger(AiConfiguration.class);

    /**
     * 主要的EmbeddingModel Bean 使用OpenAI作为主要的EmbeddingModel
     */
    @Bean
    @Primary
    @ConditionalOnBean(OpenAiEmbeddingModel.class)
    public EmbeddingModel embeddingModel(@Qualifier("openAiEmbeddingModel") OpenAiEmbeddingModel openAiEmbeddingModel) {
        log.info("配置OpenAI作为主要的EmbeddingModel");
        return openAiEmbeddingModel;
    }

    /**
     * AI模块API分组配置
     */
    @Bean
    public GroupedOpenApi aiApi() {
        return GroupedOpenApi.builder()
                .group("ai")
                .displayName("AI功能")
                .pathsToMatch("/api/ai/**")
                .build();
    }
}
