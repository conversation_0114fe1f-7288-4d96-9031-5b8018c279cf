package com.xhcai.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 业务项目创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "业务项目创建DTO")
public class BusinessProjectCreateDTO {

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 100, message = "项目名称长度不能超过100个字符")
    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    /**
     * 项目描述
     */
    @Size(max = 500, message = "项目描述长度不能超过500个字符")
    @Schema(description = "项目描述")
    private String description;

    /**
     * 项目负责人ID
     */
    @NotBlank(message = "项目负责人不能为空")
    @Schema(description = "项目负责人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownerId;

    /**
     * 应用环境
     */
    @NotBlank(message = "应用环境不能为空")
    @Pattern(regexp = "^(production|test|development)$", message = "应用环境只能是production、test或development")
    @Schema(description = "应用环境：production-生产环境，test-测试环境，development-开发环境", requiredMode = Schema.RequiredMode.REQUIRED)
    private String environment;

    /**
     * 项目图标
     */
    @Size(max = 100, message = "项目图标长度不能超过100个字符")
    @Schema(description = "项目图标")
    private String icon;

    /**
     * 图标颜色
     */
    @Size(max = 200, message = "图标颜色长度不能超过200个字符")
    @Schema(description = "图标颜色")
    private String iconColor;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;

    /**
     * 团队成员列表
     */
    @Schema(description = "团队成员列表")
    private List<TeamMemberDTO> teamMembers;

    /**
     * 团队成员DTO
     */
    @Schema(description = "团队成员DTO")
    public static class TeamMemberDTO {
        /**
         * 用户ID
         */
        @NotBlank(message = "用户ID不能为空")
        @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
        private String userId;

        /**
         * 成员角色
         */
        @Size(max = 50, message = "成员角色长度不能超过50个字符")
        @Schema(description = "成员角色")
        private String role;

        // Getters and Setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconColor() {
        return iconColor;
    }

    public void setIconColor(String iconColor) {
        this.iconColor = iconColor;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<TeamMemberDTO> getTeamMembers() {
        return teamMembers;
    }

    public void setTeamMembers(List<TeamMemberDTO> teamMembers) {
        this.teamMembers = teamMembers;
    }
}
