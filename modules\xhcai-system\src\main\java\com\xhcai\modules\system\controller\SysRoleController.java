package com.xhcai.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.modules.system.annotation.DataScope;
import com.xhcai.modules.system.dto.SysRoleQueryDTO;
import com.xhcai.modules.system.entity.SysRole;
import com.xhcai.modules.system.service.ISysRoleService;
import com.xhcai.modules.system.vo.SysRoleVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 角色管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "角色管理", description = "角色管理相关接口")
@RestController
@RequestMapping("/api/system/role")
@RequiresTenantAdmin(message = "角色管理需要租户管理员权限")
public class SysRoleController {

    @Autowired
    private ISysRoleService roleService;

    /**
     * 分页查询角色列表
     */
    @Operation(summary = "分页查询角色列表", description = "根据条件分页查询角色信息")
    @GetMapping("/page")
    @RequiresPermissions("system:role:view")
    @DataScope(deptAlias = "d", userAlias = "u")
    public Result<PageResult<SysRoleVO>> page(@Valid SysRoleQueryDTO queryDTO) {
        PageResult<SysRoleVO> pageResult = roleService.selectRolePage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询角色列表
     */
    @Operation(summary = "查询角色列表", description = "根据条件查询角色列表")
    @GetMapping("/list")
    @RequiresPermissions("system:role:view")
    @DataScope(deptAlias = "d", userAlias = "u")
    public Result<List<SysRoleVO>> list(@Valid SysRoleQueryDTO queryDTO) {
        List<SysRoleVO> roleList = roleService.selectRoleList(queryDTO);
        return Result.success(roleList);
    }

    /**
     * 根据ID查询角色详情
     */
    @Operation(summary = "查询角色详情", description = "根据角色ID查询详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("system:role:view")
    public Result<SysRoleVO> getById(
            @Parameter(description = "角色ID", required = true) @PathVariable String id) {
        SysRoleVO roleVO = roleService.selectRoleById(id);
        return Result.success(roleVO);
    }

    /**
     * 创建角色
     */
    @Operation(summary = "创建角色", description = "创建新的角色")
    @PostMapping
    @RequiresPermissions("system:role:add")
    public Result<Void> create(@Valid @RequestBody SysRole role) {
        boolean result = roleService.insertRole(role);
        return result ? Result.success() : Result.fail("创建角色失败");
    }

    /**
     * 更新角色信息
     */
    @Operation(summary = "更新角色", description = "更新角色信息")
    @PutMapping
    @RequiresPermissions("system:role:edit")
    public Result<Void> update(@Valid @RequestBody SysRole role) {
        boolean result = roleService.updateRole(role);
        return result ? Result.success() : Result.fail("更新角色失败");
    }

    /**
     * 删除角色
     */
    @Operation(summary = "删除角色", description = "批量删除角色")
    @DeleteMapping
    @RequiresPermissions("system:role:remove")
    public Result<Void> delete(@RequestBody List<String> roleIds) {
        boolean result = roleService.deleteRoles(roleIds);
        return result ? Result.success() : Result.fail("删除角色失败");
    }

    /**
     * 启用角色
     */
    @Operation(summary = "启用角色", description = "启用指定角色")
    @PutMapping("/{id}/enable")
    @RequiresPermissions("system:role:edit")
    public Result<Void> enable(
            @Parameter(description = "角色ID", required = true) @PathVariable String id) {
        boolean result = roleService.enableRole(id);
        return result ? Result.success() : Result.fail("启用角色失败");
    }

    /**
     * 停用角色
     */
    @Operation(summary = "停用角色", description = "停用指定角色")
    @PutMapping("/{id}/disable")
    @RequiresPermissions("system:role:edit")
    public Result<Void> disable(
            @Parameter(description = "角色ID", required = true) @PathVariable String id) {
        boolean result = roleService.disableRole(id);
        return result ? Result.success() : Result.fail("停用角色失败");
    }

    /**
     * 获取所有可用角色
     */
    @Operation(summary = "获取可用角色", description = "获取所有可用角色列表，用于下拉选择")
    @GetMapping("/available")
    @RequiresPermissions("system:role:view")
    public Result<List<SysRoleVO>> getAvailableRoles() {
        List<SysRoleVO> roles = roleService.selectAllAvailableRoles();
        return Result.success(roles);
    }

    /**
     * 根据数据范围查询角色
     */
    @Operation(summary = "根据数据范围查询角色", description = "根据数据范围查询角色列表")
    @GetMapping("/data-scope/{dataScope}")
    @RequiresPermissions("system:role:view")
    public Result<List<SysRoleVO>> getRolesByDataScope(
            @Parameter(description = "数据范围", required = true) @PathVariable String dataScope) {
        List<SysRoleVO> roles = roleService.selectRolesByDataScope(dataScope);
        return Result.success(roles);
    }

    /**
     * 为角色分配权限
     */
    @Operation(summary = "分配权限", description = "为角色分配权限")
    @PutMapping("/{id}/permissions")
    @RequiresPermissions("system:role:edit")
    public Result<Void> assignPermissions(
            @Parameter(description = "角色ID", required = true) @PathVariable String id,
            @RequestBody List<String> permissionIds) {
        boolean result = roleService.assignPermissions(id, permissionIds);
        return result ? Result.success() : Result.fail("分配权限失败");
    }

    /**
     * 查询角色已分配的权限
     */
    @Operation(summary = "查询角色权限", description = "查询角色已分配的权限ID列表")
    @GetMapping("/{id}/permissions")
    @RequiresPermissions("system:role:view")
    public Result<List<String>> getRolePermissions(
            @Parameter(description = "角色ID", required = true) @PathVariable String id) {
        List<String> permissionIds = roleService.selectRolePermissionIds(id);
        return Result.success(permissionIds);
    }

    /**
     * 复制角色权限
     */
    @Operation(summary = "复制角色权限", description = "将源角色的权限复制到目标角色")
    @PostMapping("/copy-permissions")
    @RequiresPermissions("system:role:edit")
    public Result<Void> copyRolePermissions(
            @Parameter(description = "源角色ID", required = true) @RequestParam String sourceRoleId,
            @Parameter(description = "目标角色ID", required = true) @RequestParam String targetRoleId) {
        boolean result = roleService.copyRolePermissions(sourceRoleId, targetRoleId);
        return result ? Result.success() : Result.fail("复制角色权限失败");
    }

    /**
     * 批量更新角色状态
     */
    @Operation(summary = "批量更新状态", description = "批量更新角色状态")
    @PutMapping("/batch-status")
    @RequiresPermissions("system:role:edit")
    public Result<Void> batchUpdateStatus(
            @RequestBody List<String> roleIds,
            @Parameter(description = "状态", required = true) @RequestParam String status) {
        boolean result = roleService.batchUpdateStatus(roleIds, status);
        return result ? Result.success() : Result.fail("批量更新状态失败");
    }

    /**
     * 检查角色编码是否存在
     */
    @Operation(summary = "检查角色编码", description = "检查角色编码是否已存在")
    @GetMapping("/check-code")
    @RequiresPermissions("system:role:view")
    public Result<Boolean> checkRoleCode(
            @Parameter(description = "角色编码", required = true) @RequestParam("roleCode") String roleCode,
            @Parameter(description = "排除的角色ID") @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = roleService.existsRoleCode(roleCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查角色名称是否存在
     */
    @Operation(summary = "检查角色名称", description = "检查角色名称是否已存在")
    @GetMapping("/check-name")
    @RequiresPermissions("system:role:view")
    public Result<Boolean> checkRoleName(
            @Parameter(description = "角色名称", required = true) @RequestParam("roleName") String roleName,
            @Parameter(description = "排除的角色ID") @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = roleService.existsRoleName(roleName, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查角色是否被用户使用
     */
    @Operation(summary = "检查角色使用情况", description = "检查角色是否被用户使用")
    @GetMapping("/{id}/used")
    @RequiresPermissions("system:role:view")
    public Result<Boolean> checkRoleUsed(
            @Parameter(description = "角色ID", required = true) @PathVariable String id) {
        boolean used = roleService.isRoleUsedByUsers(id);
        return Result.success(used);
    }
}
