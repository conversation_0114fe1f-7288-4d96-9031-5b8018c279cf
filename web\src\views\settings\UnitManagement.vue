<template>
  <div class="unit-management bg-white rounded-lg shadow-sm p-6">
    <div class="units-header flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-800">组织架构</h3>
      <div class="flex items-center gap-4">
        <!-- 搜索框 -->
        <div class="search-box">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索单位名称..."
            class="form-input w-64"
          />
        </div>

        <button @click="showAddUnitModal = true" class="btn-primary">
          <span class="mr-2">➕</span>
          添加单位
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
      <span class="text-gray-600">加载中...</span>
    </div>

    <!-- 空状态 -->
    <div v-else-if="filteredUnits.length === 0" class="text-center py-16 text-gray-500">
      <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
      </div>
      <p class="text-lg font-medium">暂无部门数据</p>
      <p class="text-sm text-gray-400 mt-1">请添加部门信息</p>
    </div>

    <!-- 自定义部门树形组件 -->
    <div v-else class="dept-tree-container">
      <!-- 表头 -->
      <div class="dept-tree-header bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-t-lg px-6 py-4">
        <div class="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
          <div class="col-span-4">部门名称</div>
          <div class="col-span-2">上级部门</div>
          <div class="col-span-2">负责人</div>
          <div class="col-span-1 text-center">人员</div>
          <div class="col-span-1 text-center">状态</div>
          <div class="col-span-2 text-center">操作</div>
        </div>
      </div>

      <!-- 树形内容 -->
      <div class="dept-tree-content border-l border-r border-b border-gray-200 rounded-b-lg bg-white">
        <DeptTreeNode
          v-for="dept in filteredUnits"
          :key="dept.id"
          :dept="dept"
          :level="0"
          @edit="editDept"
          @show-users="showDeptUsers"
          @delete="(id: string) => handleDeptCommand(`delete:${id}`)"
        />
      </div>
    </div>

    <!-- 部门管理分页（搜索时显示） -->
    <div v-if="searchQuery" class="mt-4">
      <div class="text-sm text-gray-600 mb-2">
        共找到 {{ filteredUnits.length }} 个部门
      </div>
    </div>

    <!-- 添加/编辑部门模态框 -->
    <DeptModal
      :visible="showAddUnitModal || showEditUnitModal"
      :is-edit="showEditUnitModal"
      :dept-data="currentDept"
      :available-parent-units="availableParentUnits"
      :users="users"
      :status-options="statusOptions"
      :loading="loading"
      @close="closeDeptModal"
      @save="saveDept"
    />

    <!-- 部门用户管理侧边栏 -->
    <DeptUsersDrawer
      :visible="showDeptUsersDrawer"
      :current-dept="currentDeptForUsers"
      :dept-users="deptUsers"
      :available-users="availableUsersForDept"
      :loading="loading"
      @close="closeDeptUsersDrawer"
      @load-dept-users="loadDeptUsers"
      @refresh-users="loadUsers"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DeptAPI, UserAPI } from '@/api/system'
import DictAPI from '@/api/dict'
import DeptTreeNode from './DeptTreeNode.vue'
import { DeptModal, DeptUsersDrawer } from './unit'
import type {
  SysDeptVO,
  SysDept,
  SysDictDataVO,
  SysUserVO
} from '@/types/system'

// 搜索查询
const searchQuery = ref('')

// 部门数据
const depts = ref<SysDeptVO[]>([])

// 用户数据
const users = ref<SysUserVO[]>([])

// 字典数据
const dictData = ref<Record<string, SysDictDataVO[]>>({})

// 部门状态字典
const statusOptions = computed(() => dictData.value['sys_dept_status'] || [])

// 加载状态
const loading = ref(false)

// 模态框状态
const showAddUnitModal = ref(false)
const showEditUnitModal = ref(false)

// 部门用户管理相关状态
const showDeptUsersDrawer = ref(false)
const currentDeptForUsers = ref<SysDeptVO | null>(null)
const deptUsers = ref<SysUserVO[]>([])

// 当前编辑的部门
const currentDept = ref<SysDept>({
  deptName: '',
  deptCode: '',
  parentId: '',
  orderNum: 0,
  leaderId: '',
  status: '0'
})

/**
 * 初始化数据
 */
const initData = async () => {
  await Promise.all([
    loadDepts(),
    loadUsers(),
    loadDictData()
  ])
}

/**
 * 加载部门列表
 */
const loadDepts = async () => {
  try {
    loading.value = true
    const response = await DeptAPI.getDeptTree({})
    if (response.success && response.data) {
      depts.value = response.data
    }
  } catch (error) {
    console.error('加载部门列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 加载用户列表
 */
const loadUsers = async () => {
  try {
    const response = await UserAPI.getUserPage({
      current: 1,
      size: 20, // 获取所有用户用于下拉选择
      status: '0' // 只获取正常状态的用户
    })
    if (response.success && response.data) {
      users.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

/**
 * 加载字典数据
 */
const loadDictData = async () => {
  try {
    const response = await DictAPI.getDeptStatusDict()
    if (response.success && response.data) {
      dictData.value['sys_dept_status'] = response.data
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}



// 过滤后的部门列表
const filteredUnits = computed(() => {
  if (!searchQuery.value) {
    // 非搜索状态：直接返回树形数据
    return depts.value
  }

  // 搜索状态：扁平化显示匹配结果
  const query = searchQuery.value.toLowerCase()

  // 递归获取所有节点（扁平化）
  const flattenNodes = (nodes: SysDeptVO[]): SysDeptVO[] => {
    const result: SysDeptVO[] = []

    for (const node of nodes) {
      // 添加当前节点（移除children以显示为扁平列表）
      result.push({
        ...node,
        children: [],
        hasChildren: false
      })

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        result.push(...flattenNodes(node.children))
      }
    }

    return result
  }

  const allNodes = flattenNodes(depts.value)

  // 过滤匹配的节点
  const filteredNodes = allNodes.filter(node =>
    node.deptName.toLowerCase().includes(query) ||
    node.leaderName?.toLowerCase().includes(query) ||
    node.deptCode?.toLowerCase().includes(query)
  )

  return filteredNodes
})

// 可选的上级部门（排除自己和子级部门）
const availableParentUnits = computed(() => {
  // 递归获取所有部门（扁平化）
  const flattenDepts = (depts: SysDeptVO[], level = 0): SysDeptVO[] => {
    const result: SysDeptVO[] = []

    for (const dept of depts) {
      // 添加当前部门，并设置层级信息用于显示缩进
      result.push({ ...dept, level })

      // 递归处理子部门
      if (dept.children && dept.children.length > 0) {
        result.push(...flattenDepts(dept.children, level + 1))
      }
    }

    return result
  }

  const allDepts = flattenDepts(depts.value)

  return allDepts.filter(unit => {
    // 排除自己
    if (currentDept.value.id && unit.id === currentDept.value.id) {
      return false
    }

    // 如果是编辑模式，还需要排除自己的所有子级部门
    if (currentDept.value.id) {
      // 检查是否是当前部门的子级部门
      const isChildDept = (deptId: string, parentId: string): boolean => {
        const findInTree = (nodes: SysDeptVO[]): boolean => {
          for (const node of nodes) {
            if (node.id === parentId) {
              // 找到父部门，检查其子部门中是否包含目标部门
              const checkChildren = (children: SysDeptVO[]): boolean => {
                for (const child of children) {
                  if (child.id === deptId) {
                    return true
                  }
                  if (child.children && child.children.length > 0) {
                    if (checkChildren(child.children)) {
                      return true
                    }
                  }
                }
                return false
              }

              return node.children ? checkChildren(node.children) : false
            }

            if (node.children && node.children.length > 0) {
              if (findInTree(node.children)) {
                return true
              }
            }
          }
          return false
        }

        return findInTree(depts.value)
      }

      if (isChildDept(unit.id, currentDept.value.id)) {
        return false
      }
    }

    return true
  })
})

// 可添加到部门的用户列表（排除已在部门的用户）
const availableUsersForDept = computed(() => {
  const deptUserIds = new Set(deptUsers.value.map(u => u.id))
  return users.value.filter(user => !deptUserIds.has(user.id))
})

// 部门管理方法
const editDept = async (dept: SysDeptVO) => {
  try {
    const response = await DeptAPI.getDeptById(dept.id)
    if (response.success && response.data) {
      currentDept.value = { ...response.data }
      showEditUnitModal.value = true
    }
  } catch (error) {
    console.error('获取部门详情失败:', error)
  }
}

const deleteDept = async (deptId: string) => {
  if (confirm('确定要删除该部门吗？')) {
    try {
      const response = await DeptAPI.deleteDepts([deptId])
      if (response.success) {
        await loadDepts()
      }
    } catch (error) {
      console.error('删除部门失败:', error)
    }
  }
}

const closeDeptModal = () => {
  showAddUnitModal.value = false
  showEditUnitModal.value = false
  currentDept.value = {
    deptName: '',
    deptCode: '',
    parentId: '',
    orderNum: 0,
    leaderId: '',
    status: '0'
  }
}

const saveDept = async (deptData: SysDept) => {
  try {
    loading.value = true

    // 设置祖级列表
    if (deptData.parentId) {
      const parentDept = depts.value.find(d => d.id === deptData.parentId)
      if (parentDept) {
        deptData.ancestors = parentDept.ancestors
          ? `${parentDept.ancestors},${parentDept.id}`
          : parentDept.id
      }
    } else {
      deptData.ancestors = ''
    }

    let response
    if (showEditUnitModal.value) {
      // 编辑部门
      response = await DeptAPI.updateDept(deptData)
    } else {
      // 添加部门
      response = await DeptAPI.addDept(deptData)
    }

    if (response.success) {
      ElMessage.success(showEditUnitModal.value ? '部门更新成功' : '部门创建成功')
      await loadDepts()
      closeDeptModal()
    }
  } catch (error) {
    console.error('保存部门失败:', error)
    if (error instanceof Error) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('保存部门失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理部门操作命令
const handleDeptCommand = (command: string) => {
  const [action, deptId] = command.split(':')

  switch (action) {
    case 'delete':
      deleteDept(deptId)
      break
  }
}

// 显示部门用户管理
const showDeptUsers = async (dept: SysDeptVO) => {
  currentDeptForUsers.value = dept
  await loadDeptUsers(dept.id)
  showDeptUsersDrawer.value = true
}

// 关闭部门用户管理窗口
const closeDeptUsersDrawer = () => {
  showDeptUsersDrawer.value = false
  currentDeptForUsers.value = null
  deptUsers.value = []
}

// 加载部门用户列表
const loadDeptUsers = async (deptId: string) => {
  try {
    const response = await UserAPI.getUserPage({
      current: 1,
      size: 100,
      deptId: deptId
    })
    if (response.success && response.data) {
      deptUsers.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载部门用户失败:', error)
  }
}



// 组件挂载时初始化数据
onMounted(() => {
  initData()
})


</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');

/* 必填字段标识 */
.form-label.required::after {
  content: ' *';
  color: #ef4444;
  font-weight: bold;
}

.search-box input {
  transition: all 0.3s ease;
}

.search-box input:focus {
  transform: scale(1.02);
}

mark {
  background-color: #fef08a;
  padding: 0 2px;
  border-radius: 2px;
}

/* 现代化树形结构样式 */
.tree-row {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.75rem;
  margin: 0.25rem 0;
}

.tree-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

/* 现代化折叠/展开按钮样式 */
.tree-row button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tree-row button:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.tree-row button:active {
  transform: scale(0.95);
}

/* 层级连接线动画效果 */
.tree-row .w-px {
  transition: all 0.3s ease;
  position: relative;
}

.tree-row:hover .w-px {
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
  opacity: 0.8;
}

.tree-row .w-px::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.3));
  transform: translateX(-50%) scaleY(0);
  transition: transform 0.3s ease;
}

.tree-row:hover .w-px::before {
  transform: translateX(-50%) scaleY(1);
}

/* 水平连接线样式 */
.tree-row .h-px {
  transition: all 0.3s ease;
  position: relative;
}

.tree-row:hover .h-px {
  background: linear-gradient(to right, #3b82f6, #1d4ed8);
  opacity: 0.8;
}

/* 单位图标样式 */
.tree-row .fa-building {
  transition: all 0.3s ease;
}

.tree-row:hover .fa-building {
  color: #1d4ed8;
  transform: scale(1.1);
}

/* 子单位数量标签样式 */
.tree-row .bg-blue-100 {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tree-row .bg-blue-100::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.tree-row:hover .bg-blue-100 {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.tree-row:hover .bg-blue-100::before {
  left: 100%;
}

/* 单位名称样式 */
.tree-row .font-semibold {
  transition: all 0.3s ease;
}

.tree-row:hover .font-semibold {
  color: #1e40af;
}

/* 层级路径样式 */
.tree-row .text-gray-500 {
  transition: all 0.3s ease;
}

.tree-row:hover .text-gray-500 {
  color: #3b82f6;
}

/* 树形操作按钮样式 */
.btn-secondary {
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .tree-row > div {
    padding: 0.25rem 0;
  }

  .tree-row > div:first-child {
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .tree-row > div:not(:first-child)::before {
    content: attr(data-label) ': ';
    font-weight: 500;
    color: #6b7280;
  }
}
</style>
