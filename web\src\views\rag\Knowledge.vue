<template>
  <div class="container overflow-auto bg-gradient-secondary flex-1">
    <div class="header">
      <div class="header-top">
        <h1 class="header-title">知识库管理</h1>
        <div class="header-actions">
          <button class="btn btn-primary" @click="createKnowledgeBase">
            <span>+</span> 创建知识库
          </button>
          <button class="btn btn-secondary" @click="importExternal">
            <span>📥</span> 外部知识库
          </button>
        </div>
      </div>

      <div class="filters">
        <div class="search-box">
          <span class="search-icon" v-if="!isSearching">🔍</span>
          <span class="search-icon searching" v-else>⏳</span>
          <input
            type="text"
            class="search-input"
            placeholder="搜索知识库..."
            v-model="searchQuery"
            @input="debouncedSearch"
          >
        </div>

        <div class="filter-group">
          <select class="filter-select" v-model="selectedUnit" @change="filterByUnit">
            <option value="">全部单位</option>
            <option value="技术部">技术部</option>
            <option value="产品部">产品部</option>
            <option value="市场部">市场部</option>
            <option value="人事部">人事部</option>
            <option value="财务部">财务部</option>
            <option value="客服部">客服部</option>
            <option value="法务部">法务部</option>
            <option value="数据部">数据部</option>
            <option value="项目部">项目部</option>
            <option value="安全部">安全部</option>
            <option value="采购部">采购部</option>
            <option value="设计部">设计部</option>
            <option value="运维部">运维部</option>
            <option value="质量部">质量部</option>
            <option value="销售部">销售部</option>
          </select>

          <select class="filter-select" v-model="selectedTag" @change="filterByTag">
            <option value="">全部标签</option>
            <option value="AI">AI</option>
            <option value="技术文档">技术文档</option>
            <option value="产品规范">产品规范</option>
            <option value="培训资料">培训资料</option>
            <option value="法律法规">法律法规</option>
            <option value="行业报告">行业报告</option>
          </select>
        </div>
      </div>
    </div>

    <div class="knowledge-grid">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载知识库...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="knowledgeBases?.length === 0" class="empty-container">
        <div class="empty-icon">📚</div>
        <p>暂无知识库数据</p>
        <p class="empty-tip">请尝试调整筛选条件或创建新的知识库</p>
      </div>

      <!-- 知识库卡片 -->
      <div
        v-else
        v-for="kb in knowledgeBases"
        :key="kb.id"
        class="knowledge-card"
        @click="enterKnowledgeBase(kb.id, kb.name)"
        @contextmenu.prevent="showContextMenu($event, kb)"
      >
        <div class="card-menu">
          <button class="menu-btn" @click.stop="toggleMenu($event, kb.id)">⋮</button>
          <div class="menu-dropdown" :id="`menu-${kb.id}`" :class="{ show: activeMenu === kb.id }">
            <div class="menu-item" @click.stop="handleCornerMenuClick($event, 'edit', kb)">
              <i class="fas fa-edit"></i>
              编辑
            </div>
            <div class="menu-item" @click.stop="handleCornerMenuClick($event, 'settings', kb)">
              <i class="fas fa-cog"></i>
              设置
            </div>
            <div class="menu-item" @click.stop="handleCornerMenuClick($event, 'api-key', kb)">
              <i class="fas fa-key"></i>
              申请API密钥
            </div>
            <div class="menu-item danger" @click.stop="openDeleteConfirm($event, kb)">
              <i class="fas fa-trash"></i>
              删除
            </div>
          </div>
        </div>

        <div class="card-header">
          <div class="card-icon" :style="{ background: kb.iconBg || `linear-gradient(135deg, ${getRandomColor()} 0%, ${getRandomColor()}aa 100%)` }">
            <i :class="kb.icon"></i>
          </div>
          <div class="card-info">
            <div class="card-title">{{ kb.name }}</div>
            <div class="card-description" :class="{ empty: !kb.description }">
              {{ kb.description || '知识库描述信息，请点右键或点右上角菜单中的编辑按钮，添加描述信息' }}
            </div>
          </div>
        </div>

        <div class="card-meta">
          <div class="meta-item">
            <span>🏢</span>
            <span>{{ kb.createUser.deptName || '未知部门' }}</span>
          </div>
          <div class="meta-item">
            <span>👤</span>
            <span>{{ kb.createUser.nickname || '未知用户' }}</span>
          </div>
          <div class="meta-item">
            <span>📅</span>
            <span>创建: {{ formatDate(kb.createTime) }}</span>
          </div>
          <div class="meta-item">
            <span>🔄</span>
            <span>更新: {{ formatDate(kb.updateTime || kb.createTime) }}</span>
          </div>
        </div>

        <div class="card-stats">
          <div class="stat-item">
            <div class="stat-value">{{ kb.docCount }}</div>
            <div class="stat-label">文档数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ kb.totalWordCountToK}}</div>
            <div class="stat-label">字符数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ kb.appCount }}</div>
            <div class="stat-label">关联智能体数</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分页控件 -->
  <div class="pagination-container">
    <Pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="totalCount"
      :show-page-size-selector="true"
      :show-page-numbers="true"
      :show-jumper="true"
      @change="handlePaginationChange"
    />
  </div>

  <!-- 右键菜单 -->
  <div
    class="context-menu"
    v-show="contextMenuVisible"
    :style="contextMenuStyle"
    ref="contextMenu"
  >
    <div class="menu-item" @click="handleContextMenuClick('edit')">
      <i class="fas fa-edit"></i>
      编辑
    </div>
    <div class="menu-item" @click="handleContextMenuClick('settings')">
      <i class="fas fa-cog"></i>
      设置
    </div>
    <div class="menu-item" @click="handleContextMenuClick('api-key')">
      <i class="fas fa-key"></i>
      申请API密钥
    </div>
    <div class="menu-item danger" @click="handleContextMenuClick('delete')">
      <i class="fas fa-trash"></i>
      删除
    </div>
  </div>

  <!-- 编辑知识库模态框 -->
  <EditDatasetModal
    v-model:visible="editModalVisible"
    :dataset="currentEditingKB"
    @save="handleDatasetEditSave"
  />

  <!-- 删除确认模态框 -->
  <div class="delete-confirm-modal" v-show="deleteConfirmVisible" @click="closeDeleteConfirm">
    <div class="delete-confirm-content" @click.stop>
      <div class="delete-confirm-header">
        <div class="warning-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3>确认删除</h3>
      </div>

      <div class="delete-confirm-body">
        <p class="delete-message">
          您确定要删除知识库 <strong>"{{ currentDeleteKB?.name }}"</strong> 吗？
        </p>
        <div class="delete-warning">
          <i class="fas fa-info-circle"></i>
          <span>此操作不可撤销，删除后将无法恢复该知识库的所有数据和文档。</span>
        </div>
      </div>

      <div class="delete-confirm-footer">
        <button class="btn-modern btn-secondary" @click="closeDeleteConfirm">
          <i class="fas fa-times"></i>
          取消
        </button>
        <button class="btn-modern btn-danger" @click="confirmDelete">
          <i class="fas fa-trash"></i>
          确认删除
        </button>
      </div>
    </div>
  </div>

  <!-- API密钥申请模态框 -->
  <div class="api-key-modal" v-show="apiKeyModalVisible" @click="closeApiKeyModal">
    <div class="api-key-modal-content" @click.stop>
      <div class="api-key-modal-header">
        <div class="header-left">
          <div class="kb-info" v-if="currentApiKeyKB">
            <div class="kb-icon">
              {{ currentApiKeyKB.icon }}
            </div>
            <div class="kb-details">
              <h3>申请API密钥</h3>
              <span class="kb-name">{{ currentApiKeyKB.name }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <button class="close-btn" @click="closeApiKeyModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="api-key-modal-body">
        <div class="api-key-tabs">
          <button
            class="api-key-tab"
            :class="{ active: activeApiKeyTab === 'form' }"
            @click="activeApiKeyTab = 'form'"
          >
            申请表单
          </button>
          <button
            class="api-key-tab"
            :class="{ active: activeApiKeyTab === 'list' }"
            @click="activeApiKeyTab = 'list'"
          >
            已申请密钥
          </button>
        </div>

        <!-- 申请表单 -->
        <div class="api-key-form-section" v-show="activeApiKeyTab === 'form'">
          <div class="form-grid">
            <div class="form-group">
              <label>申请单位</label>
              <input type="text" v-model="apiKeyForm.applicantUnit" placeholder="请输入申请单位" class="form-input">
            </div>
            <div class="form-group">
              <label>申请人姓名</label>
              <input type="text" v-model="apiKeyForm.applicantName" placeholder="请输入申请人姓名" class="form-input">
            </div>
            <div class="form-group">
              <label>申请人手机号</label>
              <input type="tel" v-model="apiKeyForm.applicantPhone" placeholder="请输入申请人手机号" class="form-input">
            </div>
            <div class="form-group">
              <label>责任单位</label>
              <input type="text" v-model="apiKeyForm.responsibleUnit" placeholder="请输入责任单位" class="form-input">
            </div>
            <div class="form-group">
              <label>责任人姓名</label>
              <input type="text" v-model="apiKeyForm.responsibleName" placeholder="请输入责任人姓名" class="form-input">
            </div>
            <div class="form-group">
              <label>责任人手机号</label>
              <input type="tel" v-model="apiKeyForm.responsiblePhone" placeholder="请输入责任人手机号" class="form-input">
            </div>
            <div class="form-group">
              <label>访问频率</label>
              <select v-model="apiKeyForm.accessFrequency" class="form-input">
                <option value="">请选择访问频率</option>
                <option value="low">低频（每分钟10次以下）</option>
                <option value="medium">中频（每分钟10-50次）</option>
                <option value="high">高频（每分钟50次以上）</option>
              </select>
            </div>
            <div class="form-group">
              <label>访问次数</label>
              <input type="number" v-model="apiKeyForm.accessCount" placeholder="请输入预计访问次数" class="form-input">
            </div>
          </div>
        </div>

        <!-- 已申请密钥列表 -->
        <div class="api-key-list-section" v-show="activeApiKeyTab === 'list'">
          <div class="api-key-list">
            <div v-if="currentKBApiKeys.length === 0" class="empty-state">
              <i class="fas fa-key"></i>
              <p>暂无已申请的API密钥</p>
            </div>
            <div v-else class="key-items">
              <div v-for="(key, index) in currentKBApiKeys" :key="index" class="key-item">
                <div class="key-info">
                  <div class="key-header">
                    <span class="key-id">密钥ID: {{ key.id }}</span>
                    <span class="key-status" :class="key.status">{{ key.statusText }}</span>
                  </div>
                  <div class="key-details">
                    <p><strong>申请单位:</strong> {{ key.applicantUnit }}</p>
                    <p><strong>申请人:</strong> {{ key.applicantName }} ({{ key.applicantPhone }})</p>
                    <p><strong>责任单位:</strong> {{ key.responsibleUnit }}</p>
                    <p><strong>责任人:</strong> {{ key.responsibleName }} ({{ key.responsiblePhone }})</p>
                    <p><strong>访问频率:</strong> {{ key.accessFrequency }}</p>
                    <p><strong>访问次数:</strong> {{ key.accessCount }}</p>
                    <p><strong>申请时间:</strong> {{ key.createTime }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="api-key-modal-footer">
        <div class="footer-left">
          <span class="api-key-tip">提交后将进入审核流程，请耐心等待</span>
        </div>
        <div class="footer-right">
          <button class="btn-modern btn-secondary" @click="closeApiKeyModal">
            <i class="fas fa-times"></i>
            取消
          </button>
          <button class="btn-modern btn-primary" @click="submitApiKeyApplication" :disabled="!isApiKeyFormValid">
            <i class="fas fa-paper-plane"></i>
            提交申请
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 遮罩层 -->
  <div class="overlay" v-show="contextMenuVisible || editModalVisible || deleteConfirmVisible || apiKeyModalVisible" @click="hideContextMenu"></div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { RagAPI, type DatasetQueryDTO, type DatasetVO } from '@/api/rag'
import Pagination from '@/components/common/Pagination.vue'
import EditDatasetModal from './components/EditDatasetModal.vue'

// 响应式数据
const searchQuery = ref('')
const selectedUnit = ref('')
const selectedTag = ref('')
const currentPage = ref(1)
const pageSize = ref(40)
const activeMenu = ref<string | null>(null)
const loading = ref(false)
const totalCount = ref(0)
const totalPages = ref(0)
const allFilteredData = ref([]) // 用于标签过滤时存储所有过滤后的数据
const searchTimer = ref<number | null>(null) // 防抖定时器
const isSearching = ref(false) // 搜索状态

// 右键菜单和编辑相关
const contextMenuVisible = ref(false)
const editModalVisible = ref(false)
const deleteConfirmVisible = ref(false)
const contextMenuStyle = ref({})
const currentContextKB = ref<DatasetVO | null>(null)
const currentEditingKB = ref<DatasetVO | null>(null)
const currentDeleteKB = ref<DatasetVO | null>(null)
const editKBName = ref('')
const editKBDescription = ref('')
const contextMenu = ref<HTMLElement | null>(null)

// API密钥申请相关
const apiKeyModalVisible = ref(false)
const currentApiKeyKB = ref<DatasetVO | null>(null)
const activeApiKeyTab = ref('form')
const apiKeyForm = ref({
  applicantUnit: '',
  applicantName: '',
  applicantPhone: '',
  responsibleUnit: '',
  responsibleName: '',
  responsiblePhone: '',
  accessFrequency: '',
  accessCount: ''
})
const apiKeyList = ref([
  {
    id: 'KB_API_001',
    kbId: 1,
    kbName: '企业知识库',
    applicantUnit: '北京信息技术公司',
    applicantName: '李明',
    applicantPhone: '13900139001',
    responsibleUnit: '信息部',
    responsibleName: '张华',
    responsiblePhone: '13900139002',
    accessFrequency: 'high',
    accessCount: '8000',
    status: 'approved',
    statusText: '已通过',
    createTime: '2024-01-12 11:20:00',
    approveTime: '2024-01-13 15:30:00'
  },
  {
    id: 'KB_API_002',
    kbId: 2,
    kbName: '法律条文库',
    applicantUnit: '上海律师事务所',
    applicantName: '王律师',
    applicantPhone: '13900139003',
    responsibleUnit: '法务部',
    responsibleName: '陈主任',
    responsiblePhone: '13900139004',
    accessFrequency: 'medium',
    accessCount: '3000',
    status: 'pending',
    statusText: '审核中',
    createTime: '2024-01-25 14:10:00'
  },
  {
    id: 'KB_API_003',
    kbId: 3,
    kbName: '医学文献库',
    applicantUnit: '深圳医疗研究院',
    applicantName: '刘医生',
    applicantPhone: '13900139005',
    responsibleUnit: '研究部',
    responsibleName: '黄教授',
    responsiblePhone: '13900139006',
    accessFrequency: 'low',
    accessCount: '1200',
    status: 'rejected',
    statusText: '已拒绝',
    createTime: '2024-01-20 09:30:00',
    rejectTime: '2024-01-21 16:45:00',
    rejectReason: '申请用途不明确，需要补充详细说明'
  },
  {
    id: 'KB_API_004',
    kbId: 4,
    kbName: '技术文档库',
    applicantUnit: '广州软件公司',
    applicantName: '程序员小王',
    applicantPhone: '13900139007',
    responsibleUnit: '开发部',
    responsibleName: '技术总监',
    responsiblePhone: '13900139008',
    accessFrequency: 'high',
    accessCount: '15000',
    status: 'approved',
    statusText: '已通过',
    createTime: '2024-01-08 10:15:00',
    approveTime: '2024-01-09 13:20:00'
  },
  {
    id: 'KB_API_005',
    kbId: 5,
    kbName: '产品手册库',
    applicantUnit: '杭州制造企业',
    applicantName: '产品经理',
    applicantPhone: '13900139009',
    responsibleUnit: '产品部',
    responsibleName: '部门经理',
    responsiblePhone: '13900139010',
    accessFrequency: 'medium',
    accessCount: '4500',
    status: 'pending',
    statusText: '审核中',
    createTime: '2024-01-28 16:40:00'
  },
  {
    id: 'KB_API_006',
    kbId: 1,
    kbName: '企业知识库',
    applicantUnit: '成都科技创新公司',
    applicantName: '数据分析师',
    applicantPhone: '13900139011',
    responsibleUnit: '数据部',
    responsibleName: '数据主管',
    responsiblePhone: '13900139012',
    accessFrequency: 'low',
    accessCount: '800',
    status: 'approved',
    statusText: '已通过',
    createTime: '2024-01-05 08:50:00',
    approveTime: '2024-01-06 14:15:00'
  }
])

// 知识库数据（从API获取）
const knowledgeBases = ref<DatasetVO[]>()

// 分页处理方法
const handlePaginationChange = async (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
  await loadKnowledgeBases()
}

// 统一的数据加载方法
const loadKnowledgeBases = async () => {
  loading.value = true
  try {
    const queryDTO: DatasetQueryDTO = {
      current: currentPage.value,
      size: pageSize.value,
      name: searchQuery.value || undefined, // 搜索关键词
      dataSourceType: selectedUnit.value || undefined, // 数据源类型过滤
      deptId: mapUnitToDeptId(selectedUnit.value), // 部门ID过滤
      tags: selectedTag.value ? [selectedTag.value] : undefined, // 标签过滤（转换为数组）
    }

    const response = await RagAPI.getDatasetPage(queryDTO)

    if (response.success) {
      // 将API返回的DatasetVO数据映射到现有的知识库格式
      knowledgeBases.value = response.data.records.map(dataset => ({
        ...dataset,
        unit: dataset.createByDeptName || mapDataSourceTypeToUnit(dataset.dataSourceType || ''),
        owner: dataset.createByName || dataset.createByUsername || dataset.createdBy || '未知',
        createTime: dataset.createTime || dataset.createdAt || '',
        updateTime: dataset.updatedAt || dataset.createTime || dataset.createdAt || '',
        docCount: dataset.documentCount || 0,
        totalWordCountToK: dataset.totalWordCount ? (dataset.totalWordCount < 1000 ? (dataset.totalWordCount + '') : (Math.floor((dataset.totalWordCount || 0) / 1000) + 'K')) : 0,
        appCount: 0, // API暂不提供此字段
      }))

      totalCount.value = response.data.total
      totalPages.value = response.data.pages
    } else {
      console.error('加载知识库失败:', response.message)
    }
  } catch (error) {
    console.error('加载知识库失败:', error)
  } finally {
    loading.value = false
  }
}

// 辅助方法：简单哈希函数
const simpleHash = (str: string) => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

// 辅助方法：将数据源类型映射到单位
const mapDataSourceTypeToUnit = (dataSourceType: string) => {
  const mapping: Record<string, string> = {
    'upload_file': '技术部',
    'web_crawl': '市场部',
    'api_import': '数据部',
    'database': '技术部'
  }
  return mapping[dataSourceType] || '未知部门'
}

// 辅助方法：将单位名称映射到部门ID
const mapUnitToDeptId = (unitName: string) => {
  if (!unitName) return undefined

  const mapping: Record<string, string> = {
    '技术部': 'dept_001',
    '产品部': 'dept_002',
    '市场部': 'dept_003',
    '人事部': 'dept_004',
    '财务部': 'dept_005',
    '客服部': 'dept_006',
    '法务部': 'dept_007',
    '数据部': 'dept_008',
    '项目部': 'dept_009',
    '安全部': 'dept_010',
    '采购部': 'dept_011',
    '设计部': 'dept_012',
    '运维部': 'dept_013',
    '质量部': 'dept_014',
    '销售部': 'dept_015'
  }
  return mapping[unitName]
}

// 辅助方法：格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 防抖搜索函数
const debouncedSearch = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  // 如果搜索框为空，立即执行搜索
  if (!searchQuery.value.trim()) {
    searchKnowledgeBases()
    return
  }

  isSearching.value = true

  searchTimer.value = setTimeout(async () => {
    try {
      currentPage.value = 1
      if (selectedTag.value) {
        await loadKnowledgeBasesWithTagFilter()
      } else {
        await loadKnowledgeBases()
      }
    } finally {
      isSearching.value = false
    }
  }, 500) // 500毫秒防抖
}

// 方法
const searchKnowledgeBases = async () => {
  currentPage.value = 1
  await loadKnowledgeBases()
}

const filterByUnit = async () => {
  currentPage.value = 1
  await loadKnowledgeBases()
}

const filterByTag = async () => {
  currentPage.value = 1
  allFilteredData.value = [] // 清空之前的过滤数据
  // 现在API支持标签过滤，直接使用统一的加载方法
  await loadKnowledgeBases()
}





const toggleMenu = (event: Event, id: string) => {
  event.stopPropagation()
  activeMenu.value = activeMenu.value === id ? null : id
}

const closeAllMenus = () => {
  activeMenu.value = null
}

// 路由实例
const router = useRouter()

// 功能按钮事件
const createKnowledgeBase = () => {
  // 路由到创建知识库页面
  router.push('/knowledge/create')
}

const importExternal = () => {
  alert('外部知识库功能 - 设计中')
}



const enterKnowledgeBase = (id: string, name: string) => {
  router.push({path: `/knowledge/${id}`, query: {name: name}})
}

const editKnowledgeBase = (event: Event, id: string) => {
  event.stopPropagation()
  closeAllMenus()
  alert(`编辑知识库 ${id} - 设计中`)
}

// 右上角菜单点击处理
const handleCornerMenuClick = (event: Event, action: string, kb: any) => {
  event.stopPropagation()
  closeAllMenus()

  if (action === 'edit') {
    openEditModal(kb)
  } else if (action === 'settings') {
    alert(`设置知识库"${kb.name}"功能开发中...`)
  } else if (action === 'api-key') {
    openApiKeyModal(kb)
  }
}

const deleteKnowledgeBase = (event: Event, id: number) => {
  event.stopPropagation()
  closeAllMenus()
  const kb = knowledgeBases.value.find(k => k.id === id)
  if (kb) {
    openDeleteConfirm(event, kb)
  }
}

// 右键菜单相关方法
const showContextMenu = (event: MouseEvent, kb: any) => {
  event.preventDefault()
  currentContextKB.value = kb

  const x = event.clientX
  const y = event.clientY

  contextMenuStyle.value = {
    position: 'fixed',
    left: `${x}px`,
    top: `${y}px`,
    zIndex: 1000
  }

  contextMenuVisible.value = true

  // 下一帧调整位置，避免菜单超出屏幕
  nextTick(() => {
    if (contextMenu.value) {
      const rect = contextMenu.value.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      let adjustedX = x
      let adjustedY = y

      if (x + rect.width > viewportWidth) {
        adjustedX = viewportWidth - rect.width - 10
      }

      if (y + rect.height > viewportHeight) {
        adjustedY = viewportHeight - rect.height - 10
      }

      contextMenuStyle.value = {
        position: 'fixed',
        left: `${adjustedX}px`,
        top: `${adjustedY}px`,
        zIndex: 1000
      }
    }
  })
}

const hideContextMenu = () => {
  contextMenuVisible.value = false
  currentContextKB.value = null
}

const handleContextMenuClick = (action: string) => {
  if (!currentContextKB.value) return

  const kb = currentContextKB.value

  if (action === 'edit') {
    openEditModal(kb)
  } else if (action === 'settings') {
    alert(`设置知识库"${kb.name}"功能开发中...`)
  } else if (action === 'api-key') {
    openApiKeyModal(kb)
  } else if (action === 'delete') {
    openDeleteConfirm(null, kb)
  }

  hideContextMenu()
}

// 编辑相关方法
const openEditModal = (kb: any) => {
  currentEditingKB.value = kb
  editKBName.value = kb.name
  editKBDescription.value = kb.description || ''
  editModalVisible.value = true
}

const closeEditModal = () => {
  editModalVisible.value = false
  currentEditingKB.value = null
  editKBName.value = ''
  editKBDescription.value = ''
}

// 处理知识库编辑保存
const handleDatasetEditSave = async (updatedDataset: any) => {
  try {
    // 重新加载知识库列表以获取最新数据
    await loadKnowledgeBases()

    // 更新本地数据（可选，因为已经重新加载了）
    if (knowledgeBases.value) {
      const originalKB = knowledgeBases.value.find(k => k.id === updatedDataset.id)
      if (originalKB) {
        Object.assign(originalKB, updatedDataset)
      }
    }

    console.log(`知识库"${updatedDataset.name}"信息已更新！`)
  } catch (error) {
    console.error('刷新知识库列表失败:', error)
  }
}

// API密钥申请相关方法
const openApiKeyModal = (kb: any) => {
  currentApiKeyKB.value = kb
  activeApiKeyTab.value = 'form'
  // 重置表单
  apiKeyForm.value = {
    applicantUnit: '',
    applicantName: '',
    applicantPhone: '',
    responsibleUnit: '',
    responsibleName: '',
    responsiblePhone: '',
    accessFrequency: '',
    accessCount: ''
  }
  apiKeyModalVisible.value = true
}

const closeApiKeyModal = () => {
  apiKeyModalVisible.value = false
  currentApiKeyKB.value = null
}

const isApiKeyFormValid = computed(() => {
  const form = apiKeyForm.value
  return form.applicantUnit && form.applicantName && form.applicantPhone &&
         form.responsibleUnit && form.responsibleName && form.responsiblePhone &&
         form.accessFrequency && form.accessCount
})

// 当前知识库的API密钥列表
const currentKBApiKeys = computed(() => {
  if (!currentApiKeyKB.value) return []
  return apiKeyList.value.filter(key => key.kbId === currentApiKeyKB.value!.id)
})

const submitApiKeyApplication = () => {
  if (!isApiKeyFormValid.value || !currentApiKeyKB.value) return

  const newApiKey = {
    id: `KB_API_${Date.now()}`,
    kbId: currentApiKeyKB.value.id,
    kbName: currentApiKeyKB.value.name,
    ...apiKeyForm.value,
    status: 'pending',
    statusText: '审核中',
    createTime: new Date().toLocaleString()
  }

  apiKeyList.value.push(newApiKey)

  alert(`API密钥申请已提交！\n申请ID: ${newApiKey.id}\n请耐心等待审核结果。`)
  closeApiKeyModal()
}

// 删除确认相关方法
const openDeleteConfirm = (event: Event | null, kb: any) => {
  if (event) {
    event.stopPropagation()
  }
  closeAllMenus()
  currentDeleteKB.value = kb
  deleteConfirmVisible.value = true
}

const closeDeleteConfirm = () => {
  deleteConfirmVisible.value = false
  currentDeleteKB.value = null
}

const confirmDelete = () => {
  if (!currentDeleteKB.value) return

  const kbName = currentDeleteKB.value.name
  const kbId = currentDeleteKB.value.id

  // 从数据中删除知识库
  knowledgeBases.value = knowledgeBases.value.filter(kb => kb.id !== kbId)

  // 关闭确认框
  closeDeleteConfirm()

  // 显示成功消息
  alert(`知识库"${kbName}"已成功删除`)
}

const getRandomColor = () => {
  const colors = ['#667eea', '#f093fb', '#4facfe', '#43e97b', '#fa709a', '#ffecd2']
  return colors[Math.floor(Math.random() * colors.length)]
}

onMounted(async () => {
  // 初始化加载数据
  await loadKnowledgeBases()

  // 点击页面其他地方关闭菜单
  document.addEventListener('click', (e) => {
    if (!(e.target as Element)?.closest('.card-menu')) {
      closeAllMenus()
    }
    if (!(e.target as Element)?.closest('.context-menu')) {
      hideContextMenu()
    }
  })
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
})
</script>

<style scoped>
.container {
  width: 100%;
  max-width: none;
  margin: 0;
}

.header {
  background: rgba(255, 255, 255, 0.9);
  padding: 20px 25px;
  margin-bottom: 25px;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-secondary:hover {
  background: #d5dbdb;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
  transform: translateY(-1px);
}

.filters {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 300px;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #95a5a6;
  transition: all 0.3s ease;
}

.search-icon.searching {
  color: #3498db;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1.1);
  }
}

.filter-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-select {
  padding: 10px 16px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  padding: 0 20px;
}

/* 加载状态样式 */
.loading-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #7f8c8d;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-container p {
  margin: 8px 0;
  font-size: 16px;
}

.empty-tip {
  font-size: 14px;
  color: #95a5a6;
}

.knowledge-card {
  background: white;
  border-radius: 6px;
  padding: 24px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.knowledge-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  word-break: break-word;
}

.card-description {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-description.empty {
  color: #bdc3c7;
  font-style: italic;
}

.card-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  font-size: 13px;
  color: #7f8c8d;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.card-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ecf0f1;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.stat-label {
  font-size: 12px;
  color: #95a5a6;
  margin-top: 2px;
}

.card-menu {
  position: absolute;
  top: 16px;
  right: 16px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.knowledge-card:hover .card-menu {
  opacity: 1;
}

.menu-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255,255,255,0.9);
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  padding: 8px 0;
  min-width: 160px;
  z-index: 10;
  display: none;
  white-space: nowrap;
}

.menu-dropdown.show {
  display: block;
}

.menu-item {
  padding: 10px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #2c3e50;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-item.danger {
  color: #e74c3c;
}



/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e8f0;
  padding: 8px 0;
  min-width: 120px;
  z-index: 1000;
}

.context-menu .menu-item {
  padding: 10px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-menu .menu-item:hover {
  background: #f3f4f6;
  color: #111827;
}

.context-menu .menu-item.danger {
  color: #dc2626;
}

.context-menu .menu-item.danger:hover {
  background: #fef2f2;
  color: #dc2626;
}

.context-menu .menu-item i {
  font-size: 12px;
  width: 16px;
  text-align: center;
}

/* 编辑模态框样式 - 浅蓝色柔和主题 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.edit-modal-content {
  background: #fefefe;
  border-radius: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(147, 197, 253, 0.3);
  animation: slideUp 0.4s ease;
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.edit-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.kb-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kb-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.kb-details h3 {
  margin: 0 0 2px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.kb-unit {
  font-size: 12px;
  color: #4b5563;
  font-weight: normal;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.close-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #111827;
  transform: scale(1.05);
}

.edit-modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.form-input {
  padding: 12px 16px;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fefefe;
  color: #111827;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  padding: 12px 16px;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fefefe;
  color: #111827;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.edit-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #dbeafe;
  background: #f8fafc;
}

.footer-left {
  flex: 1;
}

.edit-tip {
  font-size: 13px;
  color: #4b5563;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 浅蓝色系按钮样式 */
.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern.btn-secondary {
  background: #f0f9ff;
  color: #374151;
  border: 1px solid #bae6fd;
}

.btn-modern.btn-secondary:hover {
  background: #dbeafe;
  color: #111827;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.15);
}

.btn-modern.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 3px 10px rgba(59, 130, 246, 0.25);
}

.btn-modern.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.35);
}

.btn-modern.btn-primary:disabled {
  background: #bae6fd;
  color: #60a5fa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-modern.btn-primary:disabled::before {
  display: none;
}

.btn-modern i {
  font-size: 11px;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 删除确认模态框样式 */
.delete-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  backdrop-filter: blur(4px);
}

.delete-confirm-content {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  width: 90%;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.delete-confirm-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f1f5f9;
}

.warning-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: #d97706;
  font-size: 24px;
}

.delete-confirm-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.delete-confirm-body {
  padding: 20px 24px;
}

.delete-message {
  font-size: 16px;
  color: #374151;
  margin: 0 0 16px;
  text-align: center;
  line-height: 1.5;
}

.delete-message strong {
  color: #1f2937;
  font-weight: 600;
}

.delete-warning {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 16px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  font-size: 14px;
  color: #92400e;
}

.delete-warning i {
  color: #d97706;
  margin-top: 2px;
  flex-shrink: 0;
}

.delete-confirm-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-modern {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 100px;
  justify-content: center;
}

.btn-modern.btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-modern.btn-secondary:hover {
  background: #f1f5f9;
  color: #475569;
  border-color: #cbd5e1;
}

.btn-modern.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.btn-modern.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .delete-confirm-content {
    max-width: 95%;
    margin: 20px;
  }

  .delete-confirm-header {
    padding: 20px 20px 12px;
  }

  .warning-icon {
    width: 56px;
    height: 56px;
    font-size: 20px;
    margin-bottom: 12px;
  }

  .delete-confirm-header h3 {
    font-size: 18px;
  }

  .delete-confirm-body {
    padding: 16px 20px;
  }

  .delete-message {
    font-size: 15px;
  }

  .delete-confirm-footer {
    padding: 12px 20px 20px;
    flex-direction: column;
  }

  .btn-modern {
    width: 100%;
  }
}

/* API密钥申请模态框样式 */
.api-key-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
  z-index: 10000;
}

.api-key-modal-content {
  background: #fefefe;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(147, 197, 253, 0.3);
  animation: slideUp 0.4s ease;
  display: flex;
  flex-direction: column;
}

.api-key-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.kb-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kb-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #1a365d;
}

.kb-details h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.kb-name {
  font-size: 12px;
  color: #4b5563;
  font-weight: normal;
}

.api-key-modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.api-key-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.api-key-tab {
  padding: 8px 16px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  font-weight: 500;
}

.api-key-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.api-key-modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #dbeafe;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.api-key-tip {
  font-size: 12px;
  color: #4b5563;
  font-weight: normal;
}

.api-key-list {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.3;
}

.key-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.key-id {
  font-weight: 600;
  color: #1e40af;
}

.key-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.key-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.key-status.approved {
  background: #d1fae5;
  color: #065f46;
}

.key-status.rejected {
  background: #fee2e2;
  color: #991b1b;
}

.key-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #4b5563;
}

.pagination-container {
  position: fixed;
  z-index: 999;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding: 0 20px;
}
</style>
