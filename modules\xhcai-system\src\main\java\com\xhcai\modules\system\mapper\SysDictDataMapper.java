package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysDictData;

/**
 * 字典数据Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysDictDataMapper extends BaseMapper<SysDictData> {

    /**
     * 分页查询字典数据列表
     *
     * @param page 分页参数
     * @param dictType 字典类型
     * @param dictLabel 字典标签
     * @param status 状态
     * @return 字典数据分页列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_dict_data "
            + "WHERE deleted = 0 "
            + "<if test='dictType != null and dictType != \"\"'>"
            + "  AND dict_type = #{dictType} "
            + "</if>"
            + "<if test='dictLabel != null and dictLabel != \"\"'>"
            + "  AND dict_label LIKE CONCAT('%', #{dictLabel}, '%') "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "  AND status = #{status} "
            + "</if>"
            + "ORDER BY dict_sort ASC, create_time DESC"
            + "</script>")
    Page<SysDictData> selectDictDataPage(Page<SysDictData> page,
            @Param("dictType") String dictType,
            @Param("dictLabel") String dictLabel,
            @Param("status") String status);

    /**
     * 查询字典数据列表
     *
     * @param dictType 字典类型
     * @param dictLabel 字典标签
     * @param status 状态
     * @return 字典数据列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_dict_data "
            + "WHERE deleted = 0 "
            + "<if test='dictType != null and dictType != \"\"'>"
            + "  AND dict_type = #{dictType} "
            + "</if>"
            + "<if test='dictLabel != null and dictLabel != \"\"'>"
            + "  AND dict_label LIKE CONCAT('%', #{dictLabel}, '%') "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "  AND status = #{status} "
            + "</if>"
            + "ORDER BY dict_sort ASC, create_time DESC"
            + "</script>")
    List<SysDictData> selectDictDataList(@Param("dictType") String dictType,
            @Param("dictLabel") String dictLabel,
            @Param("status") String status);

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    @Select("SELECT * FROM sys_dict_data WHERE dict_type = #{dictType} AND status = '0' AND deleted = 0 ORDER BY dict_sort ASC")
    List<SysDictData> selectByDictType(@Param("dictType") String dictType);

    /**
     * 根据字典类型和值查询字典数据
     *
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典数据
     */
    @Select("SELECT * FROM sys_dict_data WHERE dict_type = #{dictType} AND dict_value = #{dictValue} AND status = '0' AND deleted = 0 LIMIT 1")
    SysDictData selectByDictTypeAndValue(@Param("dictType") String dictType, @Param("dictValue") String dictValue);

    /**
     * 检查字典值是否存在
     *
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @param excludeId 排除的ID
     * @return 数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_dict_data "
            + "WHERE dict_type = #{dictType} AND dict_value = #{dictValue} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "  AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    int checkDictValueExists(@Param("dictType") String dictType,
            @Param("dictValue") String dictValue,
            @Param("excludeId") String excludeId);

    /**
     * 获取字典类型下的最大排序号
     *
     * @param dictType 字典类型
     * @return 最大排序号
     */
    @Select("SELECT COALESCE(MAX(dict_sort), 0) FROM sys_dict_data WHERE dict_type = #{dictType} AND deleted = 0")
    Integer selectMaxSortByDictType(@Param("dictType") String dictType);

    /**
     * 根据字典类型删除字典数据
     *
     * @param dictType 字典类型
     * @return 影响行数
     */
    @Update("UPDATE sys_dict_data SET deleted = 1 WHERE dict_type = #{dictType}")
    int deleteByDictType(@Param("dictType") String dictType);
}
