server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: yyzs-agent
  profiles:
    active: dev

  # 动态数据源配置
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组，默认值即为 master
      primary: master
      # 严格模式，默认false. 设置为true后在未匹配到指定数据源时候会抛出异常
      strict: false
      # 数据源配置
      datasource:
        # 主数据源（系统核心数据）
        master:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${DB_HOST:**************}:${DB_PORT:5432}/${DB_NAME:dify}?currentSchema=${DB_SCHEMA:yyzs}
          username: ${DB_USERNAME:postgres}
          password: ${DB_PASSWORD:XHC12345}
          # Druid连接池配置
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            # 监控配置
            web-stat-filter:
              enabled: ${DRUID_WEB_STAT_FILTER_ENABLED:true}
              url-pattern: ${DRUID_WEB_STAT_FILTER_URL_PATTERN:/*}
              exclusions: ${DRUID_WEB_STAT_FILTER_EXCLUSIONS:"*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"}
            stat-view-servlet:
              enabled: ${DRUID_STAT_VIEW_SERVLET_ENABLED:true}
              url-pattern: ${DRUID_STAT_VIEW_SERVLET_URL_PATTERN:/druid/*}
              reset-enable: ${DRUID_STAT_VIEW_SERVLET_RESET_ENABLE:false}
              login-username: ${DRUID_STAT_VIEW_SERVLET_USERNAME:admin}
              login-password: ${DRUID_STAT_VIEW_SERVLET_PASSWORD:admin123}
            filter:
              stat:
                enabled: true
                log-slow-sql: true
                slow-sql-millis: 2000
              wall:
                enabled: true
                config:
                  multi-statement-allow: true

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    open-in-view: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

  # Jackson配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 缓存配置
  cache:
    type: simple

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.yyzs.agent.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.yyzs.agent: debug
    org.springframework.security: debug
    org.springframework.web: debug
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/yyzs-agent.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: YYZS Agent Platform API
    description: Elastic Stack组件管理平台API文档
    version: 1.0.0
    contact:
      name: YYZS Team
      email: <EMAIL>

# 应用自定义配置
yyzs:
  agent:
    # 组件安装目录
    install-path: /opt/elastic
    # 组件包存储目录
    package-storage-path: /data/packages
    # 监控数据收集间隔（秒）
    monitor-interval: 30
    # 支持的组件类型
    supported-components:
      - filebeat
      - heartbeat
      - metricbeat
      - packetbeat
      - winlogbeat
      - auditbeat
      - logstash
      - elasticsearch
      - kafka
    # 默认配置模板路径
    config-templates-path: classpath:templates/config
