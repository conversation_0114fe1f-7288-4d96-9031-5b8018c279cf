package com.xhcai.modules.dify.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.enums.ResultCode;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.dto.chat.DifyChatRequestDTO;
import com.xhcai.modules.dify.dto.chat.DifyChatResponseDTO;
import com.xhcai.modules.dify.service.IDifyChatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Dify聊天服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class DifyChatServiceImpl implements IDifyChatService {

    private static final Logger log = LoggerFactory.getLogger(DifyChatServiceImpl.class);

    /**
     * Dify API Key - 直接写死
     */
    private static final String DIFY_API_KEY = "app-HZWa3wyLD5KZVwbksobWe9nk";

    @Autowired
    private DifyConfig difyConfig;

    @Autowired
    private WebClient difyWebClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public DifyChatResponseDTO chat(DifyChatRequestDTO requestDTO) {
        log.info("发送Dify聊天请求: {}", requestDTO);
        log.info("Dify配置 - baseUrl: {}, apiKey: {}", difyConfig.getBaseUrl(),
                 DIFY_API_KEY.substring(0, Math.min(10, DIFY_API_KEY.length())) + "...");

        // 设置为阻塞模式
        requestDTO.setResponseMode("streaming");

        try {
            log.info("发送请求到: {}/v1/chat-messages", difyConfig.getBaseUrl());

            // 确保 files 字段存在（根据 curl 示例，即使为空也需要包含）
            if (requestDTO.getFiles() == null) {
                requestDTO.setFiles(new java.util.ArrayList<>());
            }

            // 确保 inputs 字段存在
            if (requestDTO.getInputs() == null) {
                requestDTO.setInputs(new HashMap<>());
            }

            log.info("请求体: {}", requestDTO);
            log.info("使用写死的API Key: {}", DIFY_API_KEY.substring(0, Math.min(10, DIFY_API_KEY.length())) + "...");

            return difyWebClient.post()
                    .uri("/v1/chat-messages")
                    .header("Authorization", "Bearer " + DIFY_API_KEY)
                    .header("Content-Type", "application/json")
                    .header("Accept", "application/json")
                    .bodyValue(requestDTO)
                    .retrieve()
                    .onStatus(status -> status.is4xxClientError(), response -> {
                        log.error("Dify API 4xx错误: status={}, headers={}", response.statusCode(), response.headers());
                        return response.bodyToMono(String.class)
                                .map(body -> {
                                    log.error("错误响应体: {}", body);
                                    return new RuntimeException("Dify API 4xx错误: " + response.statusCode() + ", body: " + body);
                                });
                    })
                    .onStatus(status -> status.is5xxServerError(), response -> {
                        log.error("Dify API 5xx错误: status={}", response.statusCode());
                        return response.bodyToMono(String.class)
                                .map(body -> {
                                    log.error("错误响应体: {}", body);
                                    return new RuntimeException("Dify API 5xx错误: " + response.statusCode() + ", body: " + body);
                                });
                    })
                    .bodyToMono(DifyChatResponseDTO.class)
                    .timeout(Duration.ofSeconds(difyConfig.getReadTimeout() / 1000))
                    .block();

        } catch (Exception e) {
            log.error("Dify聊天请求失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify聊天服务调用失败: " + e.getMessage());
        }
    }

    @Override
    public Flux<DifyChatResponseDTO> streamChat(DifyChatRequestDTO requestDTO) {
        log.info("发送Dify流式聊天请求: {}", requestDTO);

        // 设置为流式模式
        requestDTO.setResponseMode("streaming");

        // 确保 files 字段存在
        if (requestDTO.getFiles() == null) {
            requestDTO.setFiles(new java.util.ArrayList<>());
        }

        // 确保 inputs 字段存在
        if (requestDTO.getInputs() == null) {
            requestDTO.setInputs(new HashMap<>());
        }

        log.info("流式请求体: {}", requestDTO);
        log.info("流式聊天使用写死的API Key: {}", DIFY_API_KEY.substring(0, Math.min(10, DIFY_API_KEY.length())) + "...");

        try {
            return difyWebClient.post()
                    .uri("/v1/chat-messages")
                    .header("Authorization", "Bearer " + DIFY_API_KEY)
                    .header("Content-Type", "application/json")
                    .header("Accept", "text/event-stream")
                    .bodyValue(requestDTO)
                    .retrieve()
                    .onStatus(status -> status.is4xxClientError(), response -> {
                        log.error("Dify流式API 4xx错误: status={}", response.statusCode());
                        return response.bodyToMono(String.class)
                                .map(body -> {
                                    log.error("流式错误响应体: {}", body);
                                    return new RuntimeException("Dify流式API 4xx错误: " + response.statusCode() + ", body: " + body);
                                });
                    })
                    .onStatus(status -> status.is5xxServerError(), response -> {
                        log.error("Dify流式API 5xx错误: status={}", response.statusCode());
                        return response.bodyToMono(String.class)
                                .map(body -> {
                                    log.error("流式错误响应体: {}", body);
                                    return new RuntimeException("Dify流式API 5xx错误: " + response.statusCode() + ", body: " + body);
                                });
                    })
                    .bodyToFlux(String.class)
                    .doOnNext(line -> log.info("收到SSE原始数据: [{}]", line)) // 改为 info 级别，用方括号显示边界
                    .filter(line -> StringUtils.hasText(line))
                    .doOnNext(line -> log.info("非空数据: [{}]", line))
                    // 更宽松的过滤条件：包含 "data:" 即可
                    .filter(line -> line.contains("data:"))
                    .doOnNext(line -> log.info("包含data的数据: [{}]", line))
                    .map(line -> {
                        // 更宽松的数据提取
                        if (line.startsWith("data: ")) {
                            return line.substring(6).trim();
                        } else if (line.startsWith("data:")) {
                            return line.substring(5).trim();
                        } else {
                            // 如果包含 data: 但不是开头，尝试提取
                            int index = line.indexOf("data:");
                            if (index >= 0) {
                                return line.substring(index + 5).trim();
                            }
                            return line.trim();
                        }
                    })
                    .doOnNext(data -> log.info("提取的数据: [{}]", data))
                    .filter(data -> !data.equals("[DONE]") && !data.isEmpty()) // 过滤结束标记和空数据
                    .doOnNext(data -> log.info("准备解析的数据: [{}]", data))
                    .map(data -> {
                        // 先尝试解析，如果失败就创建简单响应
                        DifyChatResponseDTO response = parseEventData(data);
                        if (response == null) {
                            // 解析失败，创建一个包含原始数据的简单响应
                            response = new DifyChatResponseDTO();
                            response.setEvent("raw_data");
                            response.setAnswer(data);
                            log.info("解析失败，创建原始数据响应: {}", data);
                        }
                        return response;
                    })
                    .doOnNext(response -> log.info("最终响应: {}", response))
                    .filter(response -> response != null)
                    .timeout(Duration.ofSeconds(120)) // 增加超时时间到2分钟
                    .doOnError(error -> log.error("流式聊天处理出错", error))
                    .onErrorResume(error -> {
                        log.error("流式聊天错误恢复", error);
                        return Flux.empty(); // 返回空流而不是错误
                    });

        } catch (Exception e) {
            log.error("Dify流式聊天请求失败", e);
            return Flux.error(new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify流式聊天服务调用失败: " + e.getMessage()));
        }
    }

    @Override
    public SseEmitter streamChatSse(DifyChatRequestDTO requestDTO) {
        log.info("发送Dify SSE流式聊天请求: {}", requestDTO);

        SseEmitter emitter = new SseEmitter(Duration.ofSeconds(difyConfig.getReadTimeout() / 1000).toMillis());

        // 添加连接状态跟踪
        final boolean[] isCompleted = {false};

        // 设置超时和完成回调
        emitter.onTimeout(() -> {
            log.info("SSE流式聊天连接超时");
            isCompleted[0] = true;
        });

        emitter.onCompletion(() -> {
            log.info("SSE流式聊天连接完成");
            isCompleted[0] = true;
        });

        emitter.onError((throwable) -> {
            log.error("SSE流式聊天连接错误", throwable);
            isCompleted[0] = true;
        });

        CompletableFuture.runAsync(() -> {
            try {
                StringBuilder fullResponse = new StringBuilder();
                final int[] messageCount = {0}; // 计数器

                log.info("开始SSE流式聊天处理，请求: {}", requestDTO);

                streamChat(requestDTO)
                        .doOnNext(response -> {
                            messageCount[0]++;
                            log.info("收到第{}个响应: {}", messageCount[0], response);
                            try {
                                // 检查连接状态
                                if (isCompleted[0]) {
                                    log.debug("SSE连接已完成，跳过响应处理");
                                    return;
                                }
                                log.info("SSE处理响应: {}", response);

                                // 处理不同类型的 Dify 事件
                                String eventType = response.getEvent();
                                log.info("处理事件类型: {}, 响应: {}", eventType, response);

                                switch (eventType) {
                                    case "workflow_started":
                                        // 工作流开始事件
                                        log.info("工作流开始: task_id={}, workflow_run_id={}",
                                                response.getTaskId(), response.getWorkflowRunId());
                                        emitter.send(SseEmitter.event()
                                                .data("工作流开始处理...")
                                                .name("workflow_started"));
                                        break;

                                    case "node_started":
                                        // 节点开始事件 - 发送节点信息给客户端用于调试
                                        log.info("节点开始: task_id={}, node_id={}, node_type={}",
                                                response.getTaskId(), extractNodeId(response), extractNodeType(response));
                                        emitter.send(SseEmitter.event()
                                                .data("节点处理中: " + extractNodeTitle(response))
                                                .name("node_started"));
                                        break;

                                    case "node_finished":
                                        // 节点完成事件 - 发送节点完成信息
                                        String nodeStatus = extractNodeStatus(response);
                                        log.info("节点完成: task_id={}, node_id={}, status={}",
                                                response.getTaskId(), extractNodeId(response), nodeStatus);
                                        emitter.send(SseEmitter.event()
                                                .data("节点完成: " + extractNodeTitle(response) + " (" + nodeStatus + ")")
                                                .name("node_finished"));
                                        break;

                                    case "message":
                                        // LLM 返回文本块事件 - 主要的消息内容
                                        if (response.getAnswer() != null && !response.getAnswer().isEmpty()) {
                                            log.info("发送消息块: task_id={}, message_id={}, content_length={}",
                                                    response.getTaskId(), response.getMessageId(), response.getAnswer().length());
                                            if (safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                                    .data(response.getAnswer())
                                                    .name("message"))) {
                                                fullResponse.append(response.getAnswer());
                                            }
                                        }
                                        break;

                                    case "message_file":
                                        // 文件事件 - 有新文件需要展示
                                        log.info("收到文件事件: conversation_id={}, file_type={}, file_url={}",
                                                response.getConversationId(), extractFileType(response), extractFileUrl(response));
                                        emitter.send(SseEmitter.event()
                                                .data(objectMapper.writeValueAsString(response))
                                                .name("message_file"));
                                        break;

                                    case "message_end":
                                        // 消息结束事件 - 流式返回结束
                                        log.info("消息结束: task_id={}, message_id={}, conversation_id={}",
                                                response.getTaskId(), response.getMessageId(), response.getConversationId());

                                        // 发送元数据信息
                                        if (response.getMetadata() != null) {
                                            safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                                    .data(objectMapper.writeValueAsString(response.getMetadata()))
                                                    .name("metadata"));
                                        }

                                        // 发送使用情况统计
                                        if (response.getUsage() != null) {
                                            safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                                    .data(objectMapper.writeValueAsString(response.getUsage()))
                                                    .name("usage"));
                                        }

                                        // 发送引用资源信息
                                        Object retrieverResources = extractRetrieverResources(response);
                                        if (retrieverResources != null) {
                                            safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                                    .data(objectMapper.writeValueAsString(retrieverResources))
                                                    .name("retriever_resources"));
                                        }

                                        // 发送消息结束标记
                                        safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                                .data("消息完成")
                                                .name("message_end"));

                                        // 发送完成标记并结束连接
                                        safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                                .data("[DONE]")
                                                .name("done"));

                                        safeCompleteSse(emitter, isCompleted);
                                        log.info("SSE连接已完成，总响应长度: {}", fullResponse.length());
                                        return; // 结束处理

                                    case "tts_message":
                                        // TTS 音频流事件
                                        log.info("收到TTS音频块: task_id={}, message_id={}, audio_length={}",
                                                response.getTaskId(), response.getMessageId(),
                                                extractAudioLength(response));
                                        emitter.send(SseEmitter.event()
                                                .data(objectMapper.writeValueAsString(response))
                                                .name("tts_message"));
                                        break;

                                    case "tts_message_end":
                                        // TTS 音频流结束事件
                                        log.info("TTS音频流结束: task_id={}, message_id={}",
                                                response.getTaskId(), response.getMessageId());
                                        emitter.send(SseEmitter.event()
                                                .data("TTS音频流结束")
                                                .name("tts_message_end"));
                                        break;

                                    case "message_replace":
                                        // 消息内容替换事件 - 内容审查替换
                                        log.warn("消息内容被替换: task_id={}, message_id={}, replacement={}",
                                                response.getTaskId(), response.getMessageId(), response.getAnswer());
                                        emitter.send(SseEmitter.event()
                                                .data(response.getAnswer())
                                                .name("message_replace"));
                                        // 替换之前累积的响应内容
                                        fullResponse.setLength(0);
                                        fullResponse.append(response.getAnswer());
                                        break;

                                    case "workflow_finished":
                                        // 工作流执行结束事件
                                        String workflowStatus = extractWorkflowStatus(response);
                                        log.info("工作流完成: task_id={}, workflow_run_id={}, status={}, elapsed_time={}s",
                                                response.getTaskId(), response.getWorkflowRunId(),
                                                workflowStatus, extractElapsedTime(response));

                                        // 发送工作流输出结果
                                        Object workflowOutputs = extractWorkflowOutputs(response);
                                        if (workflowOutputs != null) {
                                            emitter.send(SseEmitter.event()
                                                    .data(objectMapper.writeValueAsString(workflowOutputs))
                                                    .name("workflow_outputs"));
                                        }

                                        emitter.send(SseEmitter.event()
                                                .data("工作流处理完成: " + workflowStatus)
                                                .name("workflow_finished"));
                                        break;

                                    case "error":
                                        // 流式输出异常事件
                                        log.error("收到错误事件: task_id={}, message_id={}, status={}, code={}, message={}",
                                                response.getTaskId(), response.getMessageId(),
                                                response.getCode(), response.getCode(), response.getError());

                                        // 发送详细错误信息
                                        Map<String, Object> errorInfo = new HashMap<>();
                                        errorInfo.put("task_id", response.getTaskId());
                                        errorInfo.put("message_id", response.getMessageId());
                                        errorInfo.put("status", response.getCode());
                                        errorInfo.put("code", response.getCode());
                                        errorInfo.put("message", response.getError());

                                        safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                                .data(objectMapper.writeValueAsString(errorInfo))
                                                .name("error"));
                                        safeCompleteWithError(emitter, isCompleted, new RuntimeException(response.getError()));
                                        return;

                                    case "ping":
                                        // 保持连接存活的 ping 事件
                                        log.debug("收到ping事件，连接保持存活");
                                        // ping 事件通常不需要发送给客户端
                                        break;

                                    case "raw_data":
                                        // 原始数据事件（解析失败时创建的）
                                        log.info("发送原始数据: {}", response.getAnswer());
                                        emitter.send(SseEmitter.event()
                                                .data(response.getAnswer())
                                                .name("raw_data"));
                                        fullResponse.append(response.getAnswer());
                                        break;

                                    default:
                                        // 其他未知事件类型
                                        log.info("收到未知事件类型: {}, 数据: {}", eventType, response);
                                        if (response.getAnswer() != null && !response.getAnswer().isEmpty()) {
                                            // 如果有 answer 内容，直接发送
                                            emitter.send(SseEmitter.event()
                                                    .data(response.getAnswer())
                                                    .name(eventType != null ? eventType : "unknown"));
                                            fullResponse.append(response.getAnswer());
                                        } else {
                                            // 否则发送完整的响应对象
                                            emitter.send(SseEmitter.event()
                                                    .data(objectMapper.writeValueAsString(response))
                                                    .name(eventType != null ? eventType : "unknown"));
                                        }
                                        break;
                                }
                            } catch (IOException e) {
                                log.error("发送SSE数据失败", e);
                                safeCompleteWithError(emitter, isCompleted, e);
                            }
                        })
                        .doOnComplete(() -> {
                            log.info("流式聊天数据流完成");
                            safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                    .data("[DONE]")
                                    .name("done"));
                            safeCompleteSse(emitter, isCompleted);
                        })
                        .doOnError(error -> {
                            log.error("SSE流式聊天处理失败", error);
                            // 发送错误信息给客户端
                            safeSendSseEvent(emitter, isCompleted, SseEmitter.event()
                                    .data("Error: " + error.getMessage())
                                    .name("error"));
                            safeCompleteSse(emitter, isCompleted); // 正常完成而不是错误完成
                        })
                        .subscribe();

            } catch (Exception e) {
                log.error("SSE流式聊天启动失败", e);
                safeCompleteWithError(emitter, isCompleted, e);
            }
        });

        return emitter;
    }

    /**
     * SSE 流式聊天 - 调试版本（简化处理）
     */
    @Override
    public SseEmitter streamChatSseDebug(DifyChatRequestDTO requestDTO) {
        log.info("开始调试版SSE流式聊天: {}", requestDTO);

        SseEmitter emitter = new SseEmitter(120000L); // 2分钟超时

        CompletableFuture.runAsync(() -> {
            try {
                // 发送开始事件
                emitter.send(SseEmitter.event()
                        .data("开始调试版流式聊天...")
                        .name("start"));

                // 直接调用流式聊天，简化处理
                streamChat(requestDTO)
                        .doOnNext(response -> {
                            try {
                                log.info("调试版收到响应: event={}, answer={}",
                                        response.getEvent(),
                                        response.getAnswer() != null ? response.getAnswer().substring(0, Math.min(50, response.getAnswer().length())) + "..." : "null");

                                // 简单处理：只要有 answer 就发送
                                if (response.getAnswer() != null && !response.getAnswer().isEmpty()) {
                                    emitter.send(SseEmitter.event()
                                            .data(response.getAnswer())
                                            .name("message"));
                                } else {
                                    // 发送事件信息
                                    emitter.send(SseEmitter.event()
                                            .data("事件: " + response.getEvent())
                                            .name("event"));
                                }
                            } catch (IOException e) {
                                log.error("调试版发送SSE数据失败", e);
                                emitter.completeWithError(e);
                            }
                        })
                        .doOnComplete(() -> {
                            try {
                                emitter.send(SseEmitter.event()
                                        .data("调试版流式聊天完成")
                                        .name("complete"));
                                emitter.complete();
                            } catch (IOException e) {
                                log.error("调试版完成SSE流失败", e);
                                emitter.completeWithError(e);
                            }
                        })
                        .doOnError(error -> {
                            log.error("调试版流式聊天处理失败", error);
                            try {
                                emitter.send(SseEmitter.event()
                                        .data("错误: " + error.getMessage())
                                        .name("error"));
                                emitter.complete();
                            } catch (IOException e) {
                                emitter.completeWithError(error);
                            }
                        })
                        .subscribe();

            } catch (Exception e) {
                log.error("调试版流式聊天启动失败", e);
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    @Override
    public Object getConversationHistory(String conversationId, String user) {
        // TODO: 实现获取会话历史
        return null;
    }

    @Override
    public boolean deleteConversation(String conversationId, String user) {
        // TODO: 实现删除会话
        return false;
    }

    @Override
    public boolean renameConversation(String conversationId, String name, String user) {
        // TODO: 实现重命名会话
        return false;
    }

    @Override
    public Object getConversations(String user, Integer page, Integer limit) {
        // TODO: 实现获取会话列表
        return null;
    }

    @Override
    public boolean stopGeneration(String taskId, String user) {
        // TODO: 实现停止生成
        return false;
    }

    @Override
    public Object getAppParameters(String user) {
        // TODO: 实现获取应用参数
        return null;
    }

    @Override
    public Object uploadFile(Object file, String user) {
        // TODO: 实现文件上传
        return null;
    }

    @Override
    public Object audioToText(Object file, String user) {
        // TODO: 实现音频转文字
        return null;
    }

    @Override
    public Object textToAudio(String text, String user, Boolean streaming) {
        // TODO: 实现文字转音频
        return null;
    }

    /**
     * 从 workflow_finished 事件中提取答案
     */
    private String extractAnswerFromWorkflowFinished(DifyChatResponseDTO response) {
        try {
            // 尝试从 data.outputs.answer 提取答案
            if (response.getData() != null) {
                Object data = response.getData();
                if (data instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> dataMap = (Map<String, Object>) data;
                    Object outputs = dataMap.get("outputs");
                    if (outputs instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> outputsMap = (Map<String, Object>) outputs;
                        Object answer = outputsMap.get("answer");
                        if (answer != null) {
                            return answer.toString();
                        }
                    }
                }
            }

            // 如果没有找到，尝试从 answer 字段提取
            if (response.getAnswer() != null) {
                return response.getAnswer();
            }

            log.warn("无法从workflow_finished事件中提取答案: {}", response);
            return null;
        } catch (Exception e) {
            log.error("提取workflow_finished答案时出错", e);
            return null;
        }
    }

    /**
     * 解析事件数据
     */
    private DifyChatResponseDTO parseEventData(String data) {
        try {
            log.info("尝试解析JSON数据: [{}]", data);

            // 如果数据为空或者是特殊标记，返回 null
            if (data == null || data.trim().isEmpty()) {
                log.info("数据为空，返回null");
                return null;
            }

            // 尝试解析为完整的 DifyChatResponseDTO
            DifyChatResponseDTO response = objectMapper.readValue(data, DifyChatResponseDTO.class);
            log.info("✅ 成功解析JSON: event={}, answer={}", response.getEvent(),
                    response.getAnswer() != null ? response.getAnswer().substring(0, Math.min(50, response.getAnswer().length())) + "..." : "null");
            return response;

        } catch (JsonProcessingException e) {
            log.warn("❌ JSON解析失败: [{}], 错误: {}", data, e.getMessage());
            // 解析失败时返回 null，让上层处理
            return null;
        } catch (Exception e) {
            log.error("❌ 解析过程中出现异常: [{}]", data, e);
            return null;
        }
    }

    // ==================== 辅助方法：从不同事件中提取特定字段 ====================

    /**
     * 从 node 事件中提取 node_id
     */
    private String extractNodeId(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object nodeId = dataMap.get("node_id");
                return nodeId != null ? nodeId.toString() : null;
            }
        } catch (Exception e) {
            log.warn("提取node_id失败", e);
        }
        return null;
    }

    /**
     * 从 node 事件中提取 node_type
     */
    private String extractNodeType(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object nodeType = dataMap.get("node_type");
                return nodeType != null ? nodeType.toString() : null;
            }
        } catch (Exception e) {
            log.warn("提取node_type失败", e);
        }
        return null;
    }

    /**
     * 从 node 事件中提取 title
     */
    private String extractNodeTitle(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object title = dataMap.get("title");
                return title != null ? title.toString() : "未知节点";
            }
        } catch (Exception e) {
            log.warn("提取node title失败", e);
        }
        return "未知节点";
    }

    /**
     * 从 node_finished 事件中提取 status
     */
    private String extractNodeStatus(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object status = dataMap.get("status");
                return status != null ? status.toString() : "unknown";
            }
        } catch (Exception e) {
            log.warn("提取node status失败", e);
        }
        return "unknown";
    }

    /**
     * 从 workflow_finished 事件中提取 status
     */
    private String extractWorkflowStatus(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object status = dataMap.get("status");
                return status != null ? status.toString() : "unknown";
            }
        } catch (Exception e) {
            log.warn("提取workflow status失败", e);
        }
        return "unknown";
    }

    /**
     * 从 workflow_finished 事件中提取 outputs
     */
    private Object extractWorkflowOutputs(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                return dataMap.get("outputs");
            }
        } catch (Exception e) {
            log.warn("提取workflow outputs失败", e);
        }
        return null;
    }

    /**
     * 从事件中提取 elapsed_time
     */
    private Double extractElapsedTime(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object elapsedTime = dataMap.get("elapsed_time");
                if (elapsedTime instanceof Number) {
                    return ((Number) elapsedTime).doubleValue();
                }
            }
        } catch (Exception e) {
            log.warn("提取elapsed_time失败", e);
        }
        return null;
    }

    /**
     * 从 message_file 事件中提取文件类型
     */
    private String extractFileType(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object type = dataMap.get("type");
                return type != null ? type.toString() : null;
            }
        } catch (Exception e) {
            log.warn("提取file type失败", e);
        }
        return null;
    }

    /**
     * 从 message_file 事件中提取文件URL
     */
    private String extractFileUrl(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object url = dataMap.get("url");
                return url != null ? url.toString() : null;
            }
        } catch (Exception e) {
            log.warn("提取file url失败", e);
        }
        return null;
    }

    /**
     * 从 tts_message 事件中提取音频长度
     */
    private Integer extractAudioLength(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                Object audio = dataMap.get("audio");
                return audio != null ? audio.toString().length() : 0;
            }
        } catch (Exception e) {
            log.warn("提取audio length失败", e);
        }
        return 0;
    }

    /**
     * 从 message_end 事件中提取 retriever_resources
     */
    private Object extractRetrieverResources(DifyChatResponseDTO response) {
        try {
            if (response.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) response.getData();
                return dataMap.get("retriever_resources");
            }
        } catch (Exception e) {
            log.warn("提取retriever_resources失败", e);
        }
        return null;
    }

    /**
     * 安全地发送 SSE 事件，避免向已完成的连接发送数据
     */
    private boolean safeSendSseEvent(SseEmitter emitter, boolean[] isCompleted, SseEmitter.SseEventBuilder eventBuilder) {
        try {
            if (!isCompleted[0]) {
                emitter.send(eventBuilder);
                return true;
            } else {
                log.debug("SSE连接已完成，跳过事件发送");
                return false;
            }
        } catch (IOException e) {
            log.error("发送SSE事件失败", e);
            if (!isCompleted[0]) {
                isCompleted[0] = true;
                try {
                    emitter.completeWithError(e);
                } catch (Exception ex) {
                    log.error("完成SSE连接失败", ex);
                }
            }
            return false;
        } catch (IllegalStateException e) {
            log.warn("SSE连接已完成，无法发送事件: {}", e.getMessage());
            isCompleted[0] = true;
            return false;
        }
    }

    /**
     * 安全地完成 SSE 连接
     */
    private void safeCompleteSse(SseEmitter emitter, boolean[] isCompleted) {
        try {
            if (!isCompleted[0]) {
                isCompleted[0] = true;
                emitter.complete();
                log.info("SSE连接已安全完成");
            } else {
                log.debug("SSE连接已经完成，跳过重复完成操作");
            }
        } catch (IllegalStateException e) {
            log.warn("SSE连接已完成: {}", e.getMessage());
            isCompleted[0] = true;
        } catch (Exception e) {
            log.error("完成SSE连接时出错", e);
            isCompleted[0] = true;
        }
    }

    /**
     * 安全地以错误完成 SSE 连接
     */
    private void safeCompleteWithError(SseEmitter emitter, boolean[] isCompleted, Throwable error) {
        try {
            if (!isCompleted[0]) {
                isCompleted[0] = true;
                emitter.completeWithError(error);
                log.info("SSE连接已安全地以错误完成");
            } else {
                log.debug("SSE连接已经完成，跳过错误完成操作");
            }
        } catch (IllegalStateException e) {
            log.warn("SSE连接已完成: {}", e.getMessage());
            isCompleted[0] = true;
        } catch (Exception e) {
            log.error("以错误完成SSE连接时出错", e);
            isCompleted[0] = true;
        }
    }
}
