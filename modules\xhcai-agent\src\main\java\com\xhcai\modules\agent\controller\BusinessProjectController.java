package com.xhcai.modules.agent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.agent.dto.BusinessProjectCreateDTO;
import com.xhcai.modules.agent.dto.BusinessProjectQueryDTO;
import com.xhcai.modules.agent.dto.BusinessProjectUpdateDTO;
import com.xhcai.modules.agent.entity.BusinessProject;
import com.xhcai.modules.agent.service.IBusinessProjectService;
import com.xhcai.modules.agent.vo.BusinessProjectVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 业务项目控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/agent/business-projects")
@Tag(name = "业务项目管理", description = "业务项目（AI场景）管理相关接口")
public class BusinessProjectController {

    @Autowired
    private IBusinessProjectService businessProjectService;

    /**
     * 分页查询业务项目列表
     */
    @GetMapping
    @Operation(summary = "分页查询业务项目列表", description = "根据条件分页查询业务项目列表")
    @RequiresPermissions("agent:business-project:list")
    public Result<IPage<BusinessProjectVO>> getProjectPage(@Valid BusinessProjectQueryDTO queryDTO) {
        IPage<BusinessProjectVO> page = businessProjectService.getProjectPage(queryDTO);
        return Result.success(page);
    }

    /**
     * 根据ID查询业务项目详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询业务项目详情", description = "根据ID查询业务项目详情")
    @RequiresPermissions("agent:business-project:detail")
    public Result<BusinessProjectVO> getProjectById(
            @Parameter(description = "项目ID", required = true) @PathVariable String id) {
        BusinessProjectVO project = businessProjectService.getProjectById(id);
        return Result.success(project);
    }

    /**
     * 创建业务项目
     */
    @PostMapping
    @Operation(summary = "创建业务项目", description = "创建新的业务项目")
    @RequiresPermissions("agent:business-project:create")
    public Result<BusinessProjectVO> createProject(@Valid @RequestBody BusinessProjectCreateDTO createDTO) {
        BusinessProjectVO project = businessProjectService.createProject(createDTO);
        return Result.success(project);
    }

    /**
     * 更新业务项目
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新业务项目", description = "根据ID更新业务项目信息")
    @RequiresPermissions("agent:business-project:update")
    public Result<BusinessProjectVO> updateProject(
            @Parameter(description = "项目ID", required = true) @PathVariable String id,
            @Valid @RequestBody BusinessProjectUpdateDTO updateDTO) {
        BusinessProjectVO project = businessProjectService.updateProject(id, updateDTO);
        return Result.success(project);
    }

    /**
     * 删除业务项目
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除业务项目", description = "根据ID删除业务项目")
    @RequiresPermissions("agent:business-project:delete")
    public Result<Void> deleteProject(
            @Parameter(description = "项目ID", required = true) @PathVariable String id) {
        businessProjectService.deleteProject(id);
        return Result.success();
    }

    /**
     * 批量删除业务项目
     */
    @DeleteMapping
    @Operation(summary = "批量删除业务项目", description = "根据ID列表批量删除业务项目")
    @RequiresPermissions("agent:business-project:delete")
    public Result<Void> deleteProjects(@RequestBody List<String> ids) {
        businessProjectService.deleteProjects(ids);
        return Result.success();
    }

    /**
     * 更新项目团队
     */
    @PutMapping("/{id}/team")
    @Operation(summary = "更新项目团队", description = "更新项目的团队成员")
    @RequiresPermissions("agent:business-project:team")
    public Result<BusinessProjectVO> updateProjectTeam(
            @Parameter(description = "项目ID", required = true) @PathVariable String id,
            @RequestBody Map<String, List<BusinessProjectCreateDTO.TeamMemberDTO>> request) {
        List<BusinessProjectCreateDTO.TeamMemberDTO> teamMembers = request.get("teamMembers");
        BusinessProjectVO project = businessProjectService.updateProjectTeam(id, teamMembers);
        return Result.success(project);
    }

    /**
     * 添加团队成员
     */
    @PostMapping("/team-members")
    @Operation(summary = "添加团队成员", description = "向项目添加团队成员")
    @RequiresPermissions("agent:business-project:team")
    public Result<Void> addTeamMember(@RequestBody Map<String, String> request) {
        String projectId = request.get("projectId");
        String userId = request.get("userId");
        String role = request.get("role");
        businessProjectService.addTeamMember(projectId, userId, role);
        return Result.success();
    }

    /**
     * 移除团队成员
     */
    @DeleteMapping("/{projectId}/team-members/{userId}")
    @Operation(summary = "移除团队成员", description = "从项目中移除团队成员")
    @RequiresPermissions("agent:business-project:team")
    public Result<Void> removeTeamMember(
            @Parameter(description = "项目ID", required = true) @PathVariable String projectId,
            @Parameter(description = "用户ID", required = true) @PathVariable String userId) {
        businessProjectService.removeTeamMember(projectId, userId);
        return Result.success();
    }

    /**
     * 更新团队成员角色
     */
    @PutMapping("/{projectId}/team-members/{userId}")
    @Operation(summary = "更新团队成员角色", description = "更新团队成员的角色")
    @RequiresPermissions("agent:business-project:team")
    public Result<Void> updateTeamMemberRole(
            @Parameter(description = "项目ID", required = true) @PathVariable String projectId,
            @Parameter(description = "用户ID", required = true) @PathVariable String userId,
            @RequestBody Map<String, String> request) {
        String role = request.get("role");
        businessProjectService.updateTeamMemberRole(projectId, userId, role);
        return Result.success();
    }

    /**
     * 切换项目环境
     */
    @PutMapping("/{id}/environment")
    @Operation(summary = "切换项目环境", description = "切换项目的运行环境")
    @RequiresPermissions("agent:business-project:environment")
    public Result<BusinessProjectVO> switchEnvironment(
            @Parameter(description = "项目ID", required = true) @PathVariable String id,
            @RequestBody Map<String, String> request) {
        String environment = request.get("environment");
        BusinessProjectVO project = businessProjectService.switchEnvironment(id, environment);
        return Result.success(project);
    }

    /**
     * 获取项目统计信息
     */
    @GetMapping("/{id}/stats")
    @Operation(summary = "获取项目统计信息", description = "获取项目的统计信息")
    @RequiresPermissions("agent:business-project:stats")
    public Result<BusinessProject> getProjectStats(
            @Parameter(description = "项目ID", required = true) @PathVariable String id) {
        BusinessProject stats = businessProjectService.getProjectStats(id);
        return Result.success(stats);
    }
}
