package com.xhcai.modules.rag.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig;
import com.xhcai.modules.rag.mapper.KnowledgeSegmentConfigMapper;
import com.xhcai.modules.rag.mapper.KnowledgeVectorizationConfigMapper;
import com.xhcai.modules.rag.service.KnowledgeConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 知识库配置服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS("rag")
public class KnowledgeConfigServiceImpl implements KnowledgeConfigService {

    private final KnowledgeSegmentConfigMapper segmentConfigMapper;
    private final KnowledgeVectorizationConfigMapper vectorizationConfigMapper;

    @Override
    public KnowledgeSegmentConfig getSegmentConfig() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        KnowledgeSegmentConfig config = segmentConfigMapper.getByTenantId(tenantId);
        
        if (config == null) {
            // 返回默认配置
            config = new KnowledgeSegmentConfig();
        }
        
        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowledgeSegmentConfig saveSegmentConfig(KnowledgeSegmentConfig config) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 查找现有配置
        KnowledgeSegmentConfig existingConfig = segmentConfigMapper.getByTenantId(tenantId);
        
        if (existingConfig != null) {
            // 更新现有配置
            existingConfig.setSegmentConfig(config.getSegmentConfig());
            existingConfig.setCleaningConfig(config.getCleaningConfig());
            segmentConfigMapper.updateById(existingConfig);
            return existingConfig;
        } else {
            // 创建新配置
            segmentConfigMapper.insert(config);
            return config;
        }
    }

    @Override
    public KnowledgeVectorizationConfig getVectorizationConfig() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        KnowledgeVectorizationConfig config = vectorizationConfigMapper.getByTenantId(tenantId);
        
        if (config == null) {
            // 返回默认配置
            config = createDefaultVectorizationConfig();
        }
        
        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowledgeVectorizationConfig saveVectorizationConfig(KnowledgeVectorizationConfig config) {
        String tenantId = SecurityUtils.getCurrentTenantId();

        // 查找现有配置
        KnowledgeVectorizationConfig existingConfig = vectorizationConfigMapper.getByTenantId(tenantId);
        
        if (existingConfig != null) {
            // 更新现有配置
            existingConfig.setIndexMode(config.getIndexMode());
            existingConfig.setEmbeddingModel(config.getEmbeddingModel());
            existingConfig.setVectorDatabaseId(config.getVectorDatabaseId());
            existingConfig.setFileStorageId(config.getFileStorageId());
            existingConfig.setRetrievalSettings(config.getRetrievalSettings());
            vectorizationConfigMapper.updateById(existingConfig);
            return existingConfig;
        } else {
            // 创建新配置
            vectorizationConfigMapper.insert(config);
            return config;
        }
    }

    /**
     * 创建默认向量化配置
     */
    private KnowledgeVectorizationConfig createDefaultVectorizationConfig() {
        KnowledgeVectorizationConfig config = new KnowledgeVectorizationConfig();
        config.setIndexMode("high_quality");
        config.setEmbeddingModel("text-embedding-ada-002");
        
        // 检索设置
        KnowledgeVectorizationConfig.RetrievalSettings retrievalSettings = new KnowledgeVectorizationConfig.RetrievalSettings();
        retrievalSettings.setRetrievalMode("hybrid");
        retrievalSettings.setEnableRerank(false);
        retrievalSettings.setRerankModel("bge-reranker-large");
        retrievalSettings.setTopK(5);
        retrievalSettings.setScoreThreshold(new BigDecimal("0.7"));
        
        // 混合检索权重
        KnowledgeVectorizationConfig.HybridWeights hybridWeights = new KnowledgeVectorizationConfig.HybridWeights();
        hybridWeights.setSemanticWeight(new BigDecimal("0.7"));
        hybridWeights.setKeywordWeight(new BigDecimal("0.3"));
        retrievalSettings.setHybridWeights(hybridWeights);
        
        config.setRetrievalSettings(retrievalSettings);
        
        return config;
    }
}
