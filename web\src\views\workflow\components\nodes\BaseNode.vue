<template>
  <div
    :class="[
      'base-node',
      `node-${nodeData.category || 'default'}`,
      `execution-${executionStatus}`,
      {
        'node-selected': selected,
        'node-executing': isExecuting,
        'node-completed': executionStatus === 'completed',
        'node-error': executionStatus === 'error',
        'node-currently-executing': isCurrentlyExecuting
      }
    ]"
    :style="nodeStyle"
  >
    <!-- 执行状态指示器 -->
    <div
      v-if="executionStatus !== 'idle'"
      class="execution-indicator"
      :class="`indicator-${executionStatus}`"
    >
      <div class="indicator-icon">
        <i v-if="executionStatus === 'waiting'" class="fas fa-clock"></i>
        <i v-else-if="executionStatus === 'running'" class="fas fa-spinner fa-spin"></i>
        <i v-else-if="executionStatus === 'completed'" class="fas fa-check"></i>
        <i v-else-if="executionStatus === 'error'" class="fas fa-exclamation-triangle"></i>
        <i v-else-if="executionStatus === 'skipped'" class="fas fa-forward"></i>
      </div>
    </div>

    <!-- 进度条 -->
    <div
      v-if="showProgress && executionProgress > 0"
      class="progress-bar"
    >
      <div
        class="progress-fill"
        :style="{ width: `${executionProgress}%` }"
      ></div>
    </div>

    <!-- 节点头部 -->
    <div class="node-header" :style="headerStyle">
      <div class="node-icon" :style="iconStyle">
        <i :class="nodeConfig.icon || 'fa-solid fa-cube'"></i>
      </div>
      <div class="node-info">
        <div class="node-title" :style="titleStyle">
          {{ nodeData.label || nodeConfig.label }}
        </div>
        <div class="node-subtitle" v-if="subtitle">
          {{ subtitle }}
        </div>
        <div class="execution-info" v-if="executionInfo">
          <span class="execution-text">{{ executionInfo }}</span>
        </div>
      </div>
      <div class="node-status" v-if="nodeConfig.status">
        <span
          class="status-badge"
          :class="`status-${nodeConfig.status}`"
        >
          {{ getStatusText(nodeConfig.status) }}
        </span>
      </div>
    </div>

    <!-- 节点内容 -->
    <div class="node-content" v-if="hasContent" :style="contentStyle">
      <slot name="content">
        <div class="default-content">
          <p class="node-description">{{ nodeConfig.description }}</p>
        </div>
      </slot>
    </div>

    <!-- 节点底部 -->
    <div class="node-footer" v-if="hasFooter">
      <slot name="footer">
        <div class="node-version" v-if="nodeConfig.version">
          v{{ nodeConfig.version }}
        </div>
      </slot>
    </div>

    <!-- 连接点 -->
    <template v-if="nodeConfig.handles">
      <!-- 输入连接点 -->
      <Handle
        v-for="handle in nodeConfig.handles.target || []"
        :key="`target-${handle.id}`"
        :id="handle.id"
        type="target"
        :position="handle.position"
        :style="getHandleStyle(handle.position)"
        class="node-handle node-handle-target"
        @mousedown.stop
        @dragstart.stop
      >
        <span v-if="(handle as any).label" class="handle-label">{{ (handle as any).label }}</span>
      </Handle>

      <!-- 输出连接点 -->
      <Handle
        v-for="handle in nodeConfig.handles.source || []"
        :key="`source-${handle.id}`"
        :id="handle.id"
        type="source"
        :position="handle.position"
        :style="getHandleStyle(handle.position)"
        class="node-handle node-handle-source"
        @mousedown.stop
        @dragstart.stop
      >
        <span v-if="(handle as any).label" class="handle-label">{{ (handle as any).label }}</span>
      </Handle>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import { getNodeByType } from '../../config/nodeLibrary'
import { useNodeConfig } from '../../composables/useNodeConfig'

// 执行状态类型定义
type ExecutionStatus = 'idle' | 'waiting' | 'running' | 'completed' | 'error' | 'skipped'

// Props
interface BaseNodeProps extends NodeProps {
  selected: boolean
  executionStatus?: ExecutionStatus
  executionProgress?: number
  executionInfo?: string
}

const props = withDefaults(defineProps<BaseNodeProps>(), {
  selected: false,
  executionStatus: 'idle',
  executionProgress: 0,
  executionInfo: ''
})

// 获取节点配置（响应式）
const nodeType = computed(() => props.data?.nodeType || props.type)

const nodeConfig = computed(() => {
  // 使用响应式配置管理器
  const reactiveConfig = useNodeConfig(nodeType.value)
  const config = reactiveConfig.value || getNodeByType(nodeType.value)

  if (config) {
    return config
  }

  // 如果没有找到配置，返回默认配置
  return {
    type: props.type,
    label: props.data?.label || props.type,
    icon: props.data?.icon || 'fa-solid fa-cube',
    description: props.data?.description || '未知节点类型',
    category: props.data?.category || 'unknown',
    color: props.data?.color || 'gray',
    gradient: props.data?.gradient || 'linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%)',
    labelColor: props.data?.labelColor || '#ffffff',
    iconColor: props.data?.iconColor || '#ffffff',
    status: 'stable',
    version: '1.0.0',
    tags: [],
    handles: {
      source: [{ id: 'output', position: Position.Right }],
      target: [{ id: 'input', position: Position.Left }]
    },
    defaultData: {
      label: props.data?.label || props.type,
      config: {}
    }
  }
})

// 节点数据
const nodeData = computed(() => props.data || {})

// 计算样式
const nodeStyle = computed(() => ({
  background: '#ffffff',
  borderColor: props.selected ? '#3b82f6' : '#e5e7eb',
  boxShadow: props.selected ? '0 0 0 2px #3b82f6' : '0 2px 8px rgba(0, 0, 0, 0.1)'
}))

const headerStyle = computed(() => ({
  background: nodeConfig.value.gradient || nodeConfig.value.color || '#f3f4f6'
}))

const iconStyle = computed(() => ({
  color: nodeConfig.value.iconColor || '#6b7280'
}))

const titleStyle = computed(() => ({
  color: nodeConfig.value.labelColor || '#1f2937'
}))

const contentStyle = computed(() => {
  // 从节点的data中获取样式信息
  const dataStyle = props.data?.style || {}

  return {
    background: dataStyle.backgroundColor || '#ffffff'
  }
})

// 执行状态相关计算属性
const isExecuting = computed(() => {
  return props.executionStatus === 'running'
})

const showProgress = computed(() => {
  return props.executionStatus === 'running' && props.executionProgress > 0
})

const executionStatus = computed(() => {
  return props.executionStatus || 'idle'
})

const executionProgress = computed(() => {
  return props.executionProgress || 0
})

const executionInfo = computed(() => {
  if (!props.executionInfo) return ''

  switch (props.executionStatus) {
    case 'waiting':
      return '等待执行...'
    case 'running':
      return props.executionInfo || `执行中... ${props.executionProgress}%`
    case 'completed':
      return '执行完成'
    case 'error':
      return '执行失败'
    case 'skipped':
      return '已跳过'
    default:
      return props.executionInfo
  }
})

const isCurrentlyExecuting = computed(() => {
  return nodeData.value?.isCurrentlyExecuting || false
})



// 计算属性
const subtitle = computed(() => {
  // 子组件可以重写这个计算属性来显示自定义副标题
  return ''
})

const hasContent = computed(() => {
  // 子组件可以重写这个计算属性来控制是否显示内容区域
  return true
})

const hasFooter = computed(() => {
  // 子组件可以重写这个计算属性来控制是否显示底部区域
  return false
})

// 方法
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'stable': '稳定',
    'beta': '测试',
    'alpha': '预览',
    'deprecated': '已弃用'
  }
  return statusMap[status] || status
}

const getHandleStyle = (position: Position) => {
  const baseStyle = {
    width: '12px',
    height: '12px',
    border: '2px solid #ffffff',
    background: nodeConfig.value.color || '#3b82f6',
    borderRadius: '50%',
    cursor: 'crosshair'
  }

  switch (position) {
    case Position.Left:
      return { ...baseStyle, left: '-6px', top: '50%', transform: 'translateY(-50%)' }
    case Position.Right:
      return { ...baseStyle, right: '-6px', top: '50%', transform: 'translateY(-50%)' }
    case Position.Top:
      return { ...baseStyle, top: '-6px', left: '50%', transform: 'translateX(-50%)' }
    case Position.Bottom:
      return { ...baseStyle, bottom: '-6px', left: '50%', transform: 'translateX(-50%)' }
    default:
      return baseStyle
  }
}

// 暴露给子组件的方法和数据
defineExpose({
  nodeConfig,
  nodeData,
  getStatusText,
  getHandleStyle
})
</script>

<style scoped>
.base-node {
  min-width: 180px;
  min-height: 60px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible !important;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.base-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  border-color: #d1d5db;
}

.node-selected {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 执行状态样式 */
.execution-idle {
  /* 默认状态 - 无特殊样式 */
}

.execution-waiting {
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: pulse-waiting 2s infinite;
}

.execution-running {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  animation: pulse-running 1.5s infinite;
}

.execution-completed {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.execution-error {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  animation: shake 0.5s ease-in-out;
}

.execution-skipped {
  border-color: #6b7280;
  box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
  opacity: 0.7;
}

/* 当前执行节点高亮 */
.node-currently-executing {
  border-color: #f59e0b !important;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.4) !important;
  animation: current-executing-pulse 1.5s infinite;
  z-index: 1001;
  position: relative;
}

.node-currently-executing::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #f59e0b;
  border-radius: 16px;
  animation: current-executing-border 2s infinite;
  pointer-events: none;
}

/* 执行状态指示器 */
.execution-indicator {
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 999999 !important;
  font-size: 12px !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  pointer-events: none !important;
}

.indicator-waiting {
  background: #f59e0b;
}

.indicator-running {
  background: #3b82f6;
}

.indicator-completed {
  background: #10b981;
}

.indicator-error {
  background: #ef4444;
}

.indicator-skipped {
  background: #6b7280;
}



/* 进度条 */
.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 1.5s infinite;
}

/* 执行信息 */
.execution-info {
  margin-top: 2px;
}

.execution-text {
  font-size: 11px;
  color: #6b7280;
  font-style: italic;
}

/* 动画效果 */
@keyframes pulse-waiting {
  0%, 100% {
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.4);
  }
}

@keyframes pulse-running {
  0%, 100% {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.5);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 当前执行节点动画 */
@keyframes current-executing-pulse {
  0%, 100% {
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.4);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(245, 158, 11, 0.6);
  }
}

@keyframes current-executing-border {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.node-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 12px 12px 0 0;
  gap: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.node-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
}

.node-info {
  flex: 1;
  min-width: 0;
}

.node-title {
  font-size: 15px;
  font-weight: 600;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-subtitle {
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.3;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-status {
  flex-shrink: 0;
  z-index: 15;
  position: relative;
}

.status-badge {
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-stable {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-beta {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.status-alpha {
  background: #fce7f3;
  color: #be185d;
  border: 1px solid #fbcfe8;
}

.status-deprecated {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

.node-content {
  padding: 12px 16px;
  background: #ffffff;
  color: #374151;
}

.default-content {
  text-align: center;
}

.node-description {
  font-size: 13px;
  color: #374151;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

.node-footer {
  padding: 8px 16px;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
  text-align: center;
  border-top: 1px solid #f3f4f6;
}

.node-version {
  font-size: 11px;
  color: #9ca3af;
  font-weight: 500;
}

.node-handle {
  border-radius: 50%;
  position: absolute;
  z-index: 30;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: crosshair;
  pointer-events: auto;
}

.node-handle:hover {
  opacity: 1;
  transform: scale(1.4);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
  z-index: 35;
}

.base-node:hover .node-handle {
  opacity: 0.8;
  transform: scale(1);
}

.base-node:hover .node-handle:hover {
  opacity: 1;
  transform: scale(1.4);
}

.handle-label {
  position: absolute;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  pointer-events: none;
  color: #374151;
  background: #ffffff;
  padding: 4px 8px;
  border-radius: 6px;
  transform: translateY(-100%);
  margin-top: -6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.node-handle-target .handle-label {
  left: -50%;
  transform: translateX(-50%) translateY(-100%);
}

.node-handle-source .handle-label {
  right: -50%;
  transform: translateX(50%) translateY(-100%);
}
</style>
