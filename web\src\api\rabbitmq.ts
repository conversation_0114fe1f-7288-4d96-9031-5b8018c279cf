/**
 * RabbitMQ管理相关API
 * 基于后端xhcai-rag模块的RabbitMQ插件API接口
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'

// ==================== 类型定义 ====================

/**
 * 连接状态
 */
export interface ConnectionStatus {
  active: number
  total: number
  healthy: boolean
}

/**
 * 队列统计
 */
export interface QueueStats {
  total: number
  active: number
  messages: number
}

/**
 * 消息统计
 */
export interface MessageStats {
  pending: number
  processed: number
  failed: number
  rate: number
}

/**
 * 交换机统计
 */
export interface ExchangeStats {
  total: number
  active: number
}

/**
 * 连接信息
 */
export interface ConnectionInfo {
  id: string
  name: string
  host: string
  port: number
  vhost: string
  user: string
  status: 'connected' | 'disconnected' | 'error'
  channels: number
  lastActivity: string
  createTime: string
}

/**
 * 队列信息
 */
export interface QueueInfo {
  name: string
  vhost: string
  durable: boolean
  autoDelete: boolean
  exclusive: boolean
  messages: number
  consumers: number
  memory: number
  status: 'running' | 'idle' | 'error'
  node: string
  arguments?: Record<string, any>
}

/**
 * 交换机信息
 */
export interface ExchangeInfo {
  name: string
  vhost: string
  type: 'direct' | 'topic' | 'fanout' | 'headers'
  durable: boolean
  autoDelete: boolean
  internal: boolean
  arguments?: Record<string, any>
}

/**
 * 消息信息
 */
export interface MessageInfo {
  id: string
  messageId: string
  messageType: string
  payload: any
  properties: Record<string, any>
  status: 'sent' | 'delivered' | 'failed' | 'expired'
  queue: string
  exchange: string
  routingKey: string
  priority: number
  retryCount: number
  createTime: string
  deliveryTime?: string
  errorMessage?: string
}

/**
 * 绑定信息
 */
export interface BindingInfo {
  source: string
  destination: string
  destinationType: 'queue' | 'exchange'
  routingKey: string
  arguments?: Record<string, any>
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
  timestamp: string
  service: string
  status: 'healthy' | 'unhealthy' | 'error'
  connectionHealthy: boolean
  queues: Record<string, any>
  error?: string
}

/**
 * 测试消息请求
 */
export interface TestMessageRequest {
  messageType: string
  content: string
  tenantId?: string
  userId?: string
}

/**
 * 文档处理任务请求
 */
export interface DocumentTaskRequest {
  documentId: string
  tenantId: string
  userId: string
}

/**
 * 向量化任务请求
 */
export interface EmbeddingTaskRequest {
  segmentId: string
  tenantId: string
  userId: string
}

/**
 * 通知消息请求
 */
export interface NotificationRequest {
  title: string
  content: string
  type?: string
  tenantId: string
  userId: string
}

// ==================== API类定义 ====================

/**
 * RabbitMQ管理API类
 */
export class RabbitMQAPI {
  
  // ==================== 健康检查和状态API ====================

  /**
   * RabbitMQ健康检查
   */
  static async healthCheck(): Promise<ApiResponse<HealthCheckResult>> {
    return apiClient.get<HealthCheckResult>('/api/rag/rabbitmq/health')
  }

  /**
   * 获取连接状态
   */
  static async getConnectionStatus(): Promise<ApiResponse<ConnectionStatus>> {
    return apiClient.get<ConnectionStatus>('/api/rag/rabbitmq/status/connections')
  }

  /**
   * 获取队列统计
   */
  static async getQueueStats(): Promise<ApiResponse<QueueStats>> {
    return apiClient.get<QueueStats>('/api/rag/rabbitmq/status/queues')
  }

  /**
   * 获取消息统计
   */
  static async getMessageStats(): Promise<ApiResponse<MessageStats>> {
    return apiClient.get<MessageStats>('/api/rag/rabbitmq/status/messages')
  }

  /**
   * 获取交换机统计
   */
  static async getExchangeStats(): Promise<ApiResponse<ExchangeStats>> {
    return apiClient.get<ExchangeStats>('/api/rag/rabbitmq/status/exchanges')
  }

  // ==================== 连接管理API ====================

  /**
   * 获取连接列表
   */
  static async getConnections(): Promise<ApiResponse<ConnectionInfo[]>> {
    return apiClient.get<ConnectionInfo[]>('/api/rag/rabbitmq/connections')
  }

  /**
   * 获取连接详情
   */
  static async getConnectionDetail(connectionId: string): Promise<ApiResponse<ConnectionInfo>> {
    return apiClient.get<ConnectionInfo>(`/api/rag/rabbitmq/connections/${connectionId}`)
  }

  /**
   * 关闭连接
   */
  static async closeConnection(connectionId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/rabbitmq/connections/${connectionId}`)
  }

  // ==================== 队列管理API ====================

  /**
   * 获取队列列表
   */
  static async getQueues(): Promise<ApiResponse<QueueInfo[]>> {
    return apiClient.get<QueueInfo[]>('/api/rag/rabbitmq/queues')
  }

  /**
   * 获取队列详情
   */
  static async getQueueDetail(queueName: string): Promise<ApiResponse<QueueInfo>> {
    return apiClient.get<QueueInfo>(`/api/rag/rabbitmq/queues/${encodeURIComponent(queueName)}`)
  }

  /**
   * 创建队列
   */
  static async createQueue(queueInfo: Partial<QueueInfo>): Promise<ApiResponse<QueueInfo>> {
    return apiClient.post<QueueInfo>('/api/rag/rabbitmq/queues', queueInfo)
  }

  /**
   * 删除队列
   */
  static async deleteQueue(queueName: string, ifUnused?: boolean, ifEmpty?: boolean): Promise<ApiResponse<void>> {
    const params = new URLSearchParams()
    if (ifUnused !== undefined) params.append('ifUnused', String(ifUnused))
    if (ifEmpty !== undefined) params.append('ifEmpty', String(ifEmpty))
    
    return apiClient.delete<void>(`/api/rag/rabbitmq/queues/${encodeURIComponent(queueName)}?${params}`)
  }

  /**
   * 清空队列
   */
  static async purgeQueue(queueName: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/rabbitmq/queues/${encodeURIComponent(queueName)}/purge`)
  }

  // ==================== 交换机管理API ====================

  /**
   * 获取交换机列表
   */
  static async getExchanges(): Promise<ApiResponse<ExchangeInfo[]>> {
    return apiClient.get<ExchangeInfo[]>('/api/rag/rabbitmq/exchanges')
  }

  /**
   * 获取交换机详情
   */
  static async getExchangeDetail(exchangeName: string): Promise<ApiResponse<ExchangeInfo>> {
    return apiClient.get<ExchangeInfo>(`/api/rag/rabbitmq/exchanges/${encodeURIComponent(exchangeName)}`)
  }

  /**
   * 创建交换机
   */
  static async createExchange(exchangeInfo: Partial<ExchangeInfo>): Promise<ApiResponse<ExchangeInfo>> {
    return apiClient.post<ExchangeInfo>('/api/rag/rabbitmq/exchanges', exchangeInfo)
  }

  /**
   * 删除交换机
   */
  static async deleteExchange(exchangeName: string, ifUnused?: boolean): Promise<ApiResponse<void>> {
    const params = ifUnused !== undefined ? `?ifUnused=${ifUnused}` : ''
    return apiClient.delete<void>(`/api/rag/rabbitmq/exchanges/${encodeURIComponent(exchangeName)}${params}`)
  }

  // ==================== 绑定管理API ====================

  /**
   * 获取绑定列表
   */
  static async getBindings(): Promise<ApiResponse<BindingInfo[]>> {
    return apiClient.get<BindingInfo[]>('/api/rag/rabbitmq/bindings')
  }

  /**
   * 创建绑定
   */
  static async createBinding(bindingInfo: BindingInfo): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/rabbitmq/bindings', bindingInfo)
  }

  /**
   * 删除绑定
   */
  static async deleteBinding(source: string, destination: string, routingKey: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>('/api/rag/rabbitmq/bindings', {
      source,
      destination,
      routingKey
    })
  }

  // ==================== 消息管理API ====================

  /**
   * 获取消息列表
   */
  static async getMessages(queueName?: string, limit?: number): Promise<ApiResponse<MessageInfo[]>> {
    const params = new URLSearchParams()
    if (queueName) params.append('queue', queueName)
    if (limit) params.append('limit', String(limit))
    
    return apiClient.get<MessageInfo[]>(`/api/rag/rabbitmq/messages?${params}`)
  }

  /**
   * 发送测试消息
   */
  static async sendTestMessage(request: TestMessageRequest): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/rabbitmq/test/send', request)
  }

  /**
   * 发送文档处理任务
   */
  static async sendDocumentTask(request: DocumentTaskRequest): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/rabbitmq/task/document', request)
  }

  /**
   * 发送向量化处理任务
   */
  static async sendEmbeddingTask(request: EmbeddingTaskRequest): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/rabbitmq/task/embedding', request)
  }

  /**
   * 发送通知消息
   */
  static async sendNotification(request: NotificationRequest): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/rabbitmq/notification', request)
  }

  // ==================== 监控和统计API ====================

  /**
   * 获取队列信息
   */
  static async getQueuesInfo(): Promise<ApiResponse<Record<string, any>>> {
    return apiClient.get<Record<string, any>>('/api/rag/rabbitmq/queues/info')
  }

  /**
   * 获取性能指标
   */
  static async getPerformanceMetrics(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/rabbitmq/metrics/performance')
  }

  /**
   * 获取消息流量统计
   */
  static async getMessageFlowStats(timeRange?: string): Promise<ApiResponse<any>> {
    const params = timeRange ? `?timeRange=${timeRange}` : ''
    return apiClient.get<any>(`/api/rag/rabbitmq/metrics/flow${params}`)
  }

  /**
   * 获取错误统计
   */
  static async getErrorStats(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/rabbitmq/metrics/errors')
  }
}

export default RabbitMQAPI
