package com.xhcai.plugin.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 模型请求
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelRequest {
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 消息列表
     */
    private List<ModelMessage> messages;
    
    /**
     * 单个提示词（用于简单场景）
     */
    private String prompt;
    
    /**
     * 最大生成长度
     */
    private Integer maxTokens;
    
    /**
     * 温度参数（0-2）
     */
    private Double temperature;
    
    /**
     * Top-p 参数（0-1）
     */
    private Double topP;
    
    /**
     * Top-k 参数
     */
    private Integer topK;
    
    /**
     * 频率惩罚（-2.0 到 2.0）
     */
    private Double frequencyPenalty;
    
    /**
     * 存在惩罚（-2.0 到 2.0）
     */
    private Double presencePenalty;
    
    /**
     * 停止词列表
     */
    private List<String> stop;
    
    /**
     * 是否流式输出
     */
    private Boolean stream;
    
    /**
     * 用户ID（用于追踪）
     */
    private String userId;
    
    /**
     * 会话ID（用于上下文管理）
     */
    private String sessionId;
    
    /**
     * 系统提示词
     */
    private String systemPrompt;
    
    /**
     * 函数定义（用于函数调用）
     */
    private List<ModelFunction> functions;
    
    /**
     * 函数调用模式
     */
    private String functionCall;
    
    /**
     * 额外参数
     */
    private Map<String, Object> extraParams;
    
    /**
     * 请求超时时间（毫秒）
     */
    private Long timeoutMs;
}
