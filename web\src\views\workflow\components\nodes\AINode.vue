<template>
  <BaseNode v-bind="$props" :selected="selected">
    <template #content>
      <div class="ai-node-content">
        <div class="ai-info" v-if="data.config?.model">
          <div class="info-item">
            <i class="fas fa-microchip"></i>
            <span>{{ data.config.model }}</span>
          </div>
          <div class="info-item" v-if="data.config?.temperature">
            <i class="fas fa-thermometer-half"></i>
            <span>温度: {{ data.config.temperature }}</span>
          </div>
        </div>

        <div class="prompt-preview" v-if="data.config?.prompt">
          <code>{{ truncatePrompt(data.config.prompt) }}</code>
        </div>

        <p class="node-description">{{ getNodeDescription() }}</p>

        <div class="ai-config" v-if="data.config">
          <div class="config-row" v-if="data.config.maxTokens">
            <span class="config-label">最大令牌:</span>
            <span class="config-value">{{ data.config.maxTokens }}</span>
          </div>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import type { NodeProps } from '@vue-flow/core'
import BaseNode from './BaseNode.vue'
import { getNodeByType } from '../../config/nodeLibrary'

// Props
interface AINodeData {
  label?: string
  description?: string
  nodeType?: string  // 添加节点类型字段
  config?: {
    aiType?: string
    model?: string
    prompt?: string
    temperature?: number
    maxTokens?: number
    systemMessage?: string
  }
}

interface Props extends Omit<NodeProps, 'selected'> {
  data: AINodeData
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

// 方法
const truncatePrompt = (prompt: string) => {
  if (prompt.length > 60) {
    return prompt.substring(0, 60) + '...'
  }
  return prompt
}

const getNodeDescription = () => {
  // 优先使用 nodeType，如果没有则使用 aiType，最后默认为 llm-chat
  const nodeType = props.data.nodeType || props.data.config?.aiType || 'llm-chat'
  const nodeConfig = getNodeByType(nodeType)
  return nodeConfig?.description || 'AI智能处理节点'
}
</script>

<style scoped>
.ai-node-content {
  text-align: left;
}

.ai-info {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #374151;
  margin-bottom: 6px;
  padding: 2px 0;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item i {
  width: 14px;
  text-align: center;
  color: #8b5cf6;
  font-size: 11px;
}

.info-item span {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f3e8ff;
  color: #581c87;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #e9d5ff;
  font-weight: 500;
}

.prompt-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 12px;
}

.prompt-preview code {
  color: #1e293b;
  font-size: 11px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  word-break: break-all;
  line-height: 1.4;
  font-weight: 400;
}

.node-description {
  font-size: 13px;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.5;
  font-weight: 400;
}

.ai-config {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.config-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

.config-row:last-child {
  border-bottom: none;
}

.config-label {
  font-weight: 600;
  color: #1f2937;
  flex-shrink: 0;
}

.config-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f3e8ff;
  color: #7c3aed;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #e9d5ff;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}


</style>
