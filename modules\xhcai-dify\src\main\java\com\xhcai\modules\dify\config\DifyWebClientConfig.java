package com.xhcai.modules.dify.config;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.modules.dify.entity.ThirdPlatform;
import com.xhcai.modules.dify.mapper.ThirdPlatformMapper;
import io.netty.channel.ChannelOption;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyAppsListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationNameRequestDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationNameResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyUpdateConversationNameRequestDTO;
import com.xhcai.modules.dify.dto.chat.DifyStopWorkflowResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyMessageListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyMessageDTO;
import com.xhcai.modules.dify.dto.file.DifyFileUploadResponseDTO;
import com.xhcai.modules.dify.dto.file.DifyRemoteFileUploadRequestDTO;
import com.xhcai.modules.dify.dto.file.DifyRemoteFileUploadResponseDTO;
import com.xhcai.modules.dify.service.IDifyAuthService;
import com.xhcai.modules.dify.service.IDifyMultiPlatformAuthService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.netty.http.client.HttpClient;
import org.springframework.util.StringUtils;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.common.security.service.LoginUser;

import javax.net.ssl.SSLException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Dify WebClient配置
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Configuration
public class DifyWebClientConfig {

    private static final Logger log = LoggerFactory.getLogger(DifyWebClientConfig.class);

    @Autowired
    private DifyConfig difyConfig;

    @Autowired(required = false)
    @Lazy
    private IDifyAuthService difyAuthService;

    @Autowired(required = false)
    @Lazy
    private IDifyMultiPlatformAuthService difyMultiPlatformAuthService;

    @Autowired
    private WebClient.Builder webClientBuilder;

    @Autowired
    private ObjectMapper objectMapper;

    @Bean("difyWebClient")
    public WebClient difyWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, difyConfig.getConnectTimeout())
                .responseTimeout(Duration.ofMillis(difyConfig.getReadTimeout()))
                .doOnConnected(conn -> 
                    conn.addHandlerLast(new ReadTimeoutHandler(difyConfig.getReadTimeout(), TimeUnit.MILLISECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(difyConfig.getReadTimeout(), TimeUnit.MILLISECONDS))
                );

        // SSL配置 - 只有在启用SSL时才配置SSL上下文
        if (difyConfig.isSslEnabled()) {
            try {
                SslContext sslContext = SslContextBuilder
                        .forClient()
                        .trustManager(InsecureTrustManagerFactory.INSTANCE)
                        .build();
                httpClient = httpClient.secure(sslContextSpec -> sslContextSpec.sslContext(sslContext));
            } catch (SSLException e) {
                throw new RuntimeException("Failed to create SSL context", e);
            }
        }

        log.info("创建 Dify WebClient，Base URL: {}", difyConfig.getBaseUrl());

        return WebClient.builder()
                .baseUrl(difyConfig.getBaseUrl())
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }



    /**
     * 调用 Dify 原始 SSE 流接口
     *
     * @param requestBody 请求体
     * @param apiKey API密钥
     * @return 原始 SSE 数据流
     */
    public Flux<String> callDifyRawSseStream(Map<String, Object> requestBody, String apiKey) {
        log.info("调用 Dify 原始 SSE 流接口: {}", requestBody);

        return difyWebClient()
                .post()
                .uri("/v1/chat-messages")
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .header("Accept", "text/event-stream")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class)
                .doOnNext(line -> log.debug("收到原始 Dify SSE 数据: {}", line))
                .doOnError(error -> log.error("Dify 原始 SSE 流调用失败", error))
                .doOnComplete(() -> log.info("Dify 原始 SSE 流调用完成"));
    }



    /**
     * 创建标准的 Dify 聊天请求体
     *
     * @param query 查询内容
     * @param user 用户标识
     * @param conversationId 会话ID（可选）
     * @return 请求体
     */
    public Map<String, Object> createChatRequestBody(String query, String user, String conversationId) {
        Map<String, Object> requestBody = new java.util.HashMap<>();
        requestBody.put("inputs", new java.util.HashMap<>());
        requestBody.put("query", query);
        requestBody.put("response_mode", "streaming");
        requestBody.put("conversation_id", conversationId != null ? conversationId : "");
        requestBody.put("user", user != null ? user : "default-user");
        requestBody.put("files", new java.util.ArrayList<>());

        log.debug("创建 Dify 聊天请求体: {}", requestBody);
        return requestBody;
    }

    /**
     * 调用 Dify 原始 SSE 流接口（带认证）
     *
     * @param requestBody 请求体
     * @return 原始 SSE 数据流
     */
    public Flux<String> callDifyRawSseStreamWithAuth(Map<String, Object> requestBody) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，回退到无认证调用");
            return Flux.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        return callDifyApiWithAuthReactive("/v1/chat-messages", requestBody)
                .flatMapMany(responseSpec -> responseSpec.bodyToFlux(String.class))
                .doOnNext(line -> log.debug("收到原始 Dify SSE 数据: {}", line))
                .doOnError(error -> log.error("Dify 原始 SSE 流调用失败", error))
                .doOnComplete(() -> log.info("Dify 原始 SSE 流调用完成"));
    }

    /**
     * 调用 Dify Console API 聊天接口（带认证）
     *
     * @param appId 应用ID
     * @param requestBody 请求体
     * @return 原始 SSE 数据流
     */
    public Flux<String> callDifyConsoleApiWithAuth(String appId, Map<String, Object> requestBody) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Flux.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        String uri = "/console/api/installed-apps/" + appId + "/chat-messages";
        log.info("调用 Dify Console API: {}", uri);

        return callDifyApiWithAuthReactive(uri, requestBody)
                .flatMapMany(responseSpec -> responseSpec.bodyToFlux(String.class))
                .doOnNext(line -> log.debug("收到 Dify Console API SSE 数据: {}", line))
                .doOnError(error -> log.error("Dify Console API 调用失败", error))
                .doOnComplete(() -> log.info("Dify Console API 调用完成"));
    }

    /**
     * 调用需要认证的 Dify API
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @return WebClient 响应规范
     */
    private WebClient.ResponseSpec callDifyApiWithAuth(String uri, Map<String, Object> requestBody) {
        return callDifyApiWithAuth(uri, requestBody, 0);
    }

    /**
     * 调用需要认证的 Dify API（带重试机制）
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @return WebClient 响应规范
     */
    private WebClient.ResponseSpec callDifyApiWithAuth(String uri, Map<String, Object> requestBody, int retryCount) {
        if (retryCount > 2) {
            throw new RuntimeException("API 调用重试次数超限");
        }

        // 同步获取访问令牌
        String accessToken;
        try {
            accessToken = difyAuthService.getValidAccessTokenSync();
        } catch (Exception e) {
            log.error("获取访问令牌失败", e);
            throw new RuntimeException("获取访问令牌失败: " + e.getMessage());
        }

        log.debug("使用访问令牌调用 Dify API: {}", uri);

        try {
            return difyWebClient()
                    .post()
                    .uri(uri)
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .header("Accept", "text/event-stream")
                    .bodyValue(requestBody)
                    .retrieve()
                    .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                        log.warn("收到 401 错误，访问令牌可能过期");
                        return Mono.error(new WebClientResponseException(401, "Unauthorized", null, null, null));
                    })
                    .onStatus(status -> status.value() == 600, response -> {
                        log.warn("收到 600 错误，需要重新登录");
                        return Mono.error(new WebClientResponseException(600, "Need Relogin", null, null, null));
                    });

        } catch (WebClientResponseException e) {
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                log.warn("处理 401 错误：刷新令牌并重试");
                // 注意：这里仍然使用同步方法，因为这个方法本身是同步的
                // 如果在响应式上下文中出现问题，应该使用响应式版本的方法
                String newAccessToken = difyAuthService.handleUnauthorizedSync();
                return callDifyApiWithAuthToken(uri, requestBody, newAccessToken);

            } else if (e.getStatusCode().value() == 600) {
                log.warn("处理 600 错误：重新登录并重试");
                String newAccessToken = difyAuthService.handleReloginSync();
                return callDifyApiWithAuthToken(uri, requestBody, newAccessToken);

            } else {
                throw e;
            }
        }
    }

    /**
     * 使用指定的访问令牌调用 Dify API
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @param accessToken 访问令牌
     * @return WebClient 响应规范
     */
    private WebClient.ResponseSpec callDifyApiWithAuthToken(String uri, Map<String, Object> requestBody, String accessToken) {
        log.debug("使用新的访问令牌重新调用 Dify API: {}", uri);

        return difyWebClient()
                .post()
                .uri(uri)
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .header("Accept", "text/event-stream")
                .bodyValue(requestBody)
                .retrieve();
    }

    /**
     * 响应式调用需要认证的 Dify API
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @return WebClient 响应规范的 Mono
     */
    public Mono<WebClient.ResponseSpec> callDifyApiWithAuthReactive(String uri, Map<String, Object> requestBody) {
        return callDifyApiWithAuthReactive(uri, requestBody, 0);
    }

    /**
     * 响应式调用需要认证的 Dify API（带重试机制）
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @return WebClient 响应规范的 Mono
     */
    private Mono<WebClient.ResponseSpec> callDifyApiWithAuthReactive(String uri, Map<String, Object> requestBody, int retryCount) {
        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        // 响应式获取访问令牌
        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用 Dify API: {}", uri);

                    WebClient.ResponseSpec responseSpec = difyWebClient()
                            .post()
                            .uri(uri)
                            .header("Authorization", "Bearer " + accessToken)
                            .header("Content-Type", "application/json")
                            .header("Accept", "text/event-stream")
                            .bodyValue(requestBody)
                            .retrieve()
                            .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                                log.warn("收到 401 错误，访问令牌可能过期");
                                return Mono.error(new WebClientResponseException(401, "Unauthorized", null, null, null));
                            })
                            .onStatus(status -> status.value() == 600, response -> {
                                log.warn("收到 600 错误，需要重新登录");
                                return Mono.error(new WebClientResponseException(600, "Need Relogin", null, null, null));
                            });

                    return Mono.just(responseSpec);
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> callDifyApiWithAuthReactive(uri, requestBody, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> callDifyApiWithAuthReactive(uri, requestBody, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取已安装应用列表（带认证）
     *
     * @return 已安装应用响应
     */
    public Mono<DifyInstalledAppsResponseDTO> getInstalledAppsWithAuth() {
        return getInstalledAppsWithAuthInternal(0);
    }

    /**
     * 获取已安装应用列表（带认证）
     *
     * @param appId 应用ID（用于查询特定应用）
     * @return 已安装应用响应
     */
    public Mono<DifyInstalledAppsResponseDTO> getInstalledAppsWithAuth(String appId) {
        return getInstalledAppsWithAuth(appId, 0);
    }

    /**
     * 获取已安装应用列表（带认证，支持重试）
     *
     * @param retryCount 重试次数
     * @return 已安装应用响应
     */
    private Mono<DifyInstalledAppsResponseDTO> getInstalledAppsWithAuthInternal(int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/installed-apps";
        log.info("获取已安装应用列表 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedGetRequestReactive(uri, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyInstalledAppsResponseDTO.class))
                .doOnNext(response -> log.debug("收到已安装应用响应: {} 个应用", response.getInstalledApps() != null ? response.getInstalledApps().size() : 0))
                .doOnError(error -> log.error("获取已安装应用列表失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> getInstalledAppsWithAuthInternal(retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> getInstalledAppsWithAuthInternal(retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取已安装应用列表（带认证，支持重试）
     *
     * @param appId 应用ID
     * @param retryCount 重试次数
     * @return 已安装应用响应
     */
//    private Mono<DifyInstalledAppsResponseDTO> getInstalledAppsWithAuth(String appId, int retryCount) {
//        if (difyAuthService == null) {
//            log.warn("认证服务未启用，无法调用 Console API");
//            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
//        }
//
//        if (retryCount > 2) {
//            return Mono.error(new RuntimeException("API 调用重试次数超限"));
//        }
//
//        String uri = "/console/api/installed-apps?app_id=" + appId;
//        log.info("调用 Dify Console API 获取已安装应用 (重试次数: {}): {}", retryCount, uri);
//
//        return makeAuthenticatedGetRequestReactive(uri, retryCount)
////                .flatMap(responseSpec -> {
////                    log.debug("开始解析已安装应用响应体");
////                    return responseSpec.bodyToMono(DifyInstalledAppsResponseDTO.class)
////                            .doOnNext(response -> log.debug("成功解析已安装应用响应: {}", response))
////                            .doOnError(parseError -> log.error("解析已安装应用响应体失败", parseError));
////                })
//                .flatMap(responseSpec -> {
//                    log.debug("开始解析已安装应用响应体");
//                    return responseSpec.bodyToMono(String.class)
//                            .doOnNext(rawResponse -> log.info("收到原始响应体: {}", rawResponse))
//                            .flatMap(rawResponse -> {
//                                try {
//                                    DifyInstalledAppsResponseDTO response = objectMapper.readValue(rawResponse, DifyInstalledAppsResponseDTO.class);
//                                    log.debug("成功解析已安装应用响应: {}", response);
//                                    return Mono.just(response);
//                                } catch (Exception e) {
//                                    log.error("解析已安装应用响应体失败", e);
//                                    return Mono.error(e);
//                                }
//                            });
//                })
//                .doOnNext(response -> log.debug("收到已安装应用响应: {}", response))
//                .doOnError(error -> log.error("获取已安装应用失败，错误类型: {}, 错误信息: {}", error.getClass().getSimpleName(), error.getMessage(), error))
//                .onErrorResume(WebClientResponseException.class, e -> {
//                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
//                        log.warn("处理 401 错误：刷新令牌并重试");
//                        return difyAuthService.handleUnauthorized()
//                                .flatMap(newAccessToken -> getInstalledAppsWithAuth(appId, retryCount + 1));
//
//                    } else if (e.getStatusCode().value() == 600) {
//                        log.warn("处理 600 错误：重新登录并重试");
//                        return difyAuthService.handleRelogin()
//                                .flatMap(newAccessToken -> getInstalledAppsWithAuth(appId, retryCount + 1));
//
//                    } else {
//                        return Mono.error(e);
//                    }
//                });
//    }

    private Mono<DifyInstalledAppsResponseDTO> getInstalledAppsWithAuth(String appId, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/installed-apps?app_id=" + appId;
        log.info("调用 Dify Console API 获取已安装应用 (重试次数: {}): {}", retryCount, uri);

        try {
            log.info("开始调用 makeAuthenticatedGetRequestReactive");
            Mono<WebClient.ResponseSpec> responseMono = makeAuthenticatedGetRequestReactive(uri, retryCount);
            log.info("makeAuthenticatedGetRequestReactive 调用完成，返回的 Mono: {}", responseMono != null ? "非null" : "null");

            if (responseMono == null) {
                log.error("makeAuthenticatedGetRequestReactive 返回了 null");
                return Mono.error(new RuntimeException("makeAuthenticatedGetRequestReactive 返回了 null"));
            }

            return responseMono
                    .doOnSubscribe(subscription -> log.info("开始订阅 makeAuthenticatedGetRequestReactive 的结果"))
                    .doOnNext(responseSpec -> {
                        log.info("makeAuthenticatedGetRequestReactive 返回成功，responseSpec: {}", responseSpec != null ? "非null" : "null");
                        // 添加HTTP状态码检查
                        if (responseSpec != null) {
                            log.info("准备获取响应体和状态码");
                        }
                    })
                    .doOnError(error -> log.error("makeAuthenticatedGetRequestReactive 返回错误: {}", error.getMessage(), error))
                    .flatMap(responseSpec -> {
                        log.debug("开始解析已安装应用响应体");

                        // 先检查状态码
                        return responseSpec.toEntity(String.class)
                                .doOnNext(responseEntity -> {
                                    log.info("HTTP状态码: {}", responseEntity.getStatusCode());
                                    log.info("响应头: {}", responseEntity.getHeaders());
                                    log.info("收到原始响应体: {}", responseEntity.getBody());
                                })
                                .map(responseEntity -> responseEntity.getBody())
                                .doOnError(bodyError -> log.error("获取响应体失败: {}", bodyError.getMessage(), bodyError))
                                .flatMap(rawResponse -> {
                                    if (rawResponse == null || rawResponse.trim().isEmpty()) {
                                        log.error("Dify API 返回空响应体，可能是认证失败或API不可用");
                                        return Mono.error(new RuntimeException("Dify API 返回空响应体，请检查认证状态和API可用性"));
                                    }

                                    try {
                                        DifyInstalledAppsResponseDTO response = objectMapper.readValue(rawResponse, DifyInstalledAppsResponseDTO.class);
                                        log.debug("成功解析已安装应用响应: {}", response);
                                        return Mono.just(response);
                                    } catch (Exception e) {
                                        log.error("解析已安装应用响应体失败，原始响应: {}", rawResponse, e);
                                        return Mono.error(new RuntimeException("解析Dify API响应失败: " + e.getMessage(), e));
                                    }
                                });
                    })
                    .doOnNext(response -> log.debug("收到已安装应用响应: {}", response))
                    .doOnError(error -> log.error("获取已安装应用失败，错误类型: {}, 错误信息: {}", error.getClass().getSimpleName(), error.getMessage(), error))
                    .onErrorResume(WebClientResponseException.class, e -> {
                        if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                            log.warn("处理 401 错误：刷新令牌并重试");
                            return difyAuthService.handleUnauthorized()
                                    .flatMap(newAccessToken -> getInstalledAppsWithAuth(appId, retryCount + 1));

                        } else if (e.getStatusCode().value() == 600) {
                            log.warn("处理 600 错误：重新登录并重试");
                            return difyAuthService.handleRelogin()
                                    .flatMap(newAccessToken -> getInstalledAppsWithAuth(appId, retryCount + 1));

                        } else {
                            return Mono.error(e);
                        }
                    });
        } catch (Exception e) {
            log.error("调用 makeAuthenticatedGetRequestReactive 时发生异常", e);
            return Mono.error(e);
        }
    }

    /**
     * 获取智能体会话列表（带认证）
     *
     * @param installedAppId 已安装应用ID
     * @param limit 限制数量，默认100
     * @param pinned 是否只获取置顶会话，默认false
     * @return 会话列表响应
     */
    public Mono<DifyConversationListResponseDTO> getConversationsWithAuth(String installedAppId, Integer limit, Boolean pinned) {
        return getConversationsWithAuth(installedAppId, limit, pinned, 0);
    }

    /**
     * 获取智能体会话列表（带认证，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param limit 限制数量，默认100
     * @param pinned 是否只获取置顶会话，默认false
     * @param retryCount 重试次数
     * @return 会话列表响应
     */
    private Mono<DifyConversationListResponseDTO> getConversationsWithAuth(String installedAppId, Integer limit, Boolean pinned, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        // 构建查询参数
        StringBuilder uriBuilder = new StringBuilder("/console/api/installed-apps/")
                .append(installedAppId)
                .append("/conversations");

        // 添加查询参数
        boolean hasParams = false;
        if (limit != null) {
            uriBuilder.append(hasParams ? "&" : "?").append("limit=").append(limit);
            hasParams = true;
        }
        if (pinned != null) {
            uriBuilder.append(hasParams ? "&" : "?").append("pinned=").append(pinned);
        }

        String uri = uriBuilder.toString();
        log.info("调用 Dify Console API 获取会话列表 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedGetRequestReactive(uri, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyConversationListResponseDTO.class))
                .doOnNext(response -> log.debug("收到会话列表响应: {}", response))
                .doOnError(error -> log.error("获取会话列表失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> getConversationsWithAuth(installedAppId, limit, pinned, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> getConversationsWithAuth(installedAppId, limit, pinned, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取智能体会话列表（带认证，使用默认参数）
     *
     * @param installedAppId 已安装应用ID
     * @return 会话列表响应
     */
    public Mono<DifyConversationListResponseDTO> getConversationsWithAuth(String installedAppId) {
        return getConversationsWithAuth(installedAppId, 100, false);
    }

    /**
     * 获取会话名称（带认证）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param autoGenerate 是否自动生成名称
     * @return 会话名称响应
     */
    public Mono<DifyConversationNameResponseDTO> getConversationNameWithAuth(String installedAppId, String conversationId, Boolean autoGenerate) {
        return getConversationNameWithAuth(installedAppId, conversationId, autoGenerate, 0);
    }

    /**
     * 获取会话名称（带认证，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param autoGenerate 是否自动生成名称
     * @param retryCount 重试次数
     * @return 会话名称响应
     */
    private Mono<DifyConversationNameResponseDTO> getConversationNameWithAuth(String installedAppId, String conversationId, Boolean autoGenerate, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/installed-apps/" + installedAppId + "/conversations/" + conversationId + "/name";
        log.info("调用 Dify Console API 获取会话名称 (重试次数: {}): {}", retryCount, uri);

        // 创建请求体
        DifyConversationNameRequestDTO requestBody = new DifyConversationNameRequestDTO(autoGenerate != null ? autoGenerate : Boolean.TRUE);

        return makeAuthenticatedPostRequestForJson(uri, requestBody, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyConversationNameResponseDTO.class))
                .doOnNext(response -> log.debug("收到会话名称响应: {}", response))
                .doOnError(error -> log.error("获取会话名称失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> getConversationNameWithAuth(installedAppId, conversationId, autoGenerate, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> getConversationNameWithAuth(installedAppId, conversationId, autoGenerate, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取会话名称（带认证，使用默认自动生成）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @return 会话名称响应
     */
    public Mono<DifyConversationNameResponseDTO> getConversationNameWithAuth(String installedAppId, String conversationId) {
        return getConversationNameWithAuth(installedAppId, conversationId, true);
    }

    /**
     * 修改会话名称（带认证）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param name 新的会话名称
     * @return 会话名称响应
     */
    public Mono<DifyConversationNameResponseDTO> updateConversationNameWithAuth(String installedAppId, String conversationId, String name) {
        return updateConversationNameWithAuth(installedAppId, conversationId, name, 0);
    }

    /**
     * 修改会话名称（带认证，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param name 新的会话名称
     * @param retryCount 重试次数
     * @return 会话名称响应
     */
    private Mono<DifyConversationNameResponseDTO> updateConversationNameWithAuth(String installedAppId, String conversationId, String name, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/installed-apps/" + installedAppId + "/conversations/" + conversationId + "/name";
        log.info("调用 Dify Console API 修改会话名称 (重试次数: {}): {}", retryCount, uri);

        // 创建请求体
        DifyUpdateConversationNameRequestDTO requestBody = new DifyUpdateConversationNameRequestDTO(name);

        return makeAuthenticatedPostRequestForJson(uri, requestBody, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyConversationNameResponseDTO.class))
                .doOnNext(response -> log.debug("收到修改会话名称响应: {}", response))
                .doOnError(error -> log.error("修改会话名称失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> updateConversationNameWithAuth(installedAppId, conversationId, name, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> updateConversationNameWithAuth(installedAppId, conversationId, name, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 删除会话记录（带认证）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @return 删除结果
     */
    public Mono<Boolean> deleteConversationWithAuth(String installedAppId, String conversationId) {
        return deleteConversationWithAuth(installedAppId, conversationId, 0);
    }

    /**
     * 删除会话记录（带认证，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param retryCount 重试次数
     * @return 删除结果
     */
    private Mono<Boolean> deleteConversationWithAuth(String installedAppId, String conversationId, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/installed-apps/" + installedAppId + "/conversations/" + conversationId;
        log.info("调用 Dify Console API 删除会话记录 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedDeleteRequest(uri, retryCount)
                .flatMap(responseSpec -> {
                    // 对于DELETE请求，通常返回204 No Content或200 OK
                    // 我们使用toBodilessEntity()来处理可能没有响应体的情况
                    return responseSpec.toBodilessEntity()
                            .doOnNext(response -> {
                                log.debug("收到删除会话记录响应: status={}", response.getStatusCode());
                                // 检查状态码是否表示成功
                                if (response.getStatusCode().is2xxSuccessful()) {
                                    log.debug("删除会话记录成功，状态码: {}", response.getStatusCode());
                                } else {
                                    log.warn("删除会话记录返回非成功状态码: {}", response.getStatusCode());
                                }
                            })
                            .map(response -> response.getStatusCode().is2xxSuccessful()); // 根据状态码判断是否成功
                })
                .doOnError(error -> log.error("删除会话记录失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> deleteConversationWithAuth(installedAppId, conversationId, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> deleteConversationWithAuth(installedAppId, conversationId, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 停止工作流（带认证）
     *
     * @param installedAppId 已安装应用ID
     * @param taskId 任务ID
     * @return 停止结果
     */
    public Mono<DifyStopWorkflowResponseDTO> stopWorkflowWithAuth(String installedAppId, String taskId) {
        return stopWorkflowWithAuth(installedAppId, taskId, 0);
    }

    /**
     * 停止工作流（带认证，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param taskId 任务ID
     * @param retryCount 重试次数
     * @return 停止结果
     */
    private Mono<DifyStopWorkflowResponseDTO> stopWorkflowWithAuth(String installedAppId, String taskId, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/installed-apps/" + installedAppId + "/chat-messages/" + taskId + "/stop";
        log.info("调用 Dify Console API 停止工作流 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedPostRequestWithoutBody(uri, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyStopWorkflowResponseDTO.class))
                .doOnNext(response -> log.debug("收到停止工作流响应: {}", response))
                .doOnError(error -> log.error("停止工作流失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> stopWorkflowWithAuth(installedAppId, taskId, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> stopWorkflowWithAuth(installedAppId, taskId, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 发送认证的DELETE请求
     *
     * @param uri 接口路径
     * @param retryCount 重试次数
     * @return WebClient 响应规范
     */
    private Mono<WebClient.ResponseSpec> makeAuthenticatedDeleteRequest(String uri, int retryCount) {
        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用 Dify DELETE API (重试次数: {}): {}", retryCount, uri);

                    WebClient.ResponseSpec responseSpec = difyWebClient()
                            .delete()
                            .uri(uri)
                            .header("Authorization", "Bearer " + accessToken)
                            .header("Content-Type", "application/json")
                            .header("Accept", "application/json")
                            .retrieve()
                            .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                                log.warn("收到 401 错误，检查是否为令牌过期");
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("401 错误响应体: {}", body);
                                            // 检查是否为 Dify 特定的令牌过期错误格式
                                            if (isDifyTokenExpiredError(body)) {
                                                return Mono.error(new WebClientResponseException(401, "Token Expired", null, body.getBytes(), null));
                                            } else {
                                                return Mono.error(new WebClientResponseException(401, "Unauthorized", null, body.getBytes(), null));
                                            }
                                        });
                            })
                            .onStatus(status -> status.is4xxClientError() && !status.equals(HttpStatus.UNAUTHORIZED), response -> {
                                log.warn("收到客户端错误: {}", response.statusCode());
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("客户端错误响应体: {}", body);
                                            return Mono.error(new WebClientResponseException(response.statusCode().value(), "Client Error", null, body.getBytes(), null));
                                        });
                            })
                            .onStatus(status -> status.is5xxServerError(), response -> {
                                log.warn("收到服务器错误: {}", response.statusCode());
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("服务器错误响应体: {}", body);
                                            return Mono.error(new WebClientResponseException(response.statusCode().value(), "Server Error", null, body.getBytes(), null));
                                        });
                            });

                    return Mono.just(responseSpec);
                });
    }

    /**
     * 发送认证的POST请求（无请求体）
     *
     * @param uri 接口路径
     * @param retryCount 重试次数
     * @return WebClient 响应规范
     */
    private Mono<WebClient.ResponseSpec> makeAuthenticatedPostRequestWithoutBody(String uri, int retryCount) {
        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用 Dify POST API (无请求体, 重试次数: {}): {}", retryCount, uri);

                    WebClient.ResponseSpec responseSpec = difyWebClient()
                            .post()
                            .uri(uri)
                            .header("Authorization", "Bearer " + accessToken)
                            .header("Content-Type", "application/json")
                            .header("Accept", "application/json")
                            .retrieve()
                            .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                                log.warn("收到 401 错误，检查是否为令牌过期");
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("401 错误响应体: {}", body);
                                            // 检查是否为 Dify 特定的令牌过期错误格式
                                            if (isDifyTokenExpiredError(body)) {
                                                return Mono.error(new WebClientResponseException(401, "Token Expired", null, body.getBytes(), null));
                                            } else {
                                                return Mono.error(new WebClientResponseException(401, "Unauthorized", null, body.getBytes(), null));
                                            }
                                        });
                            })
                            .onStatus(status -> status.is4xxClientError() && !status.equals(HttpStatus.UNAUTHORIZED), response -> {
                                log.warn("收到客户端错误: {}", response.statusCode());
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("客户端错误响应体: {}", body);
                                            return Mono.error(new WebClientResponseException(response.statusCode().value(), "Client Error", null, body.getBytes(), null));
                                        });
                            })
                            .onStatus(status -> status.is5xxServerError(), response -> {
                                log.warn("收到服务器错误: {}", response.statusCode());
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("服务器错误响应体: {}", body);
                                            return Mono.error(new WebClientResponseException(response.statusCode().value(), "Server Error", null, body.getBytes(), null));
                                        });
                            });

                    return Mono.just(responseSpec);
                });
    }

    /**
     * 发送认证的POST请求（用于JSON响应）
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @return WebClient 响应规范
     */
    private Mono<WebClient.ResponseSpec> makeAuthenticatedPostRequestForJson(String uri, Object requestBody) {
        return makeAuthenticatedPostRequestForJson(uri, requestBody, 0);
    }

    /**
     * 发送认证的POST请求（用于JSON响应，带重试机制）
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @return WebClient 响应规范
     */
    private Mono<WebClient.ResponseSpec> makeAuthenticatedPostRequestForJson(String uri, Object requestBody, int retryCount) {
        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用 Dify POST API (JSON响应, 重试次数: {}): {}", retryCount, uri);

                    WebClient.ResponseSpec responseSpec = difyWebClient()
                            .post()
                            .uri(uri)
                            .header("Authorization", "Bearer " + accessToken)
                            .header("Content-Type", "application/json")
                            .header("Accept", "application/json")
                            .bodyValue(requestBody)
                            .retrieve()
                            .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                                log.warn("收到 401 错误，检查是否为令牌过期");
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("401 错误响应体: {}", body);
                                            // 检查是否为 Dify 特定的令牌过期错误格式
                                            if (isDifyTokenExpiredError(body)) {
                                                return Mono.error(new WebClientResponseException(401, "Token Expired", null, body.getBytes(), null));
                                            } else {
                                                return Mono.error(new WebClientResponseException(401, "Unauthorized", null, body.getBytes(), null));
                                            }
                                        });
                            });

                    return Mono.just(responseSpec);
                });
    }

    /**
     * 使用已安装应用ID调用聊天接口（带认证）
     *
     * @param installedAppId 已安装应用ID
     * @param requestBody 请求体
     * @return 原始 SSE 数据流
     */
    public Flux<String> callDifyChatWithInstalledAppId(String installedAppId, Map<String, Object> requestBody) {
        return callDifyChatWithInstalledAppId(installedAppId, requestBody, 0);
    }

    /**
     * 使用已安装应用ID调用聊天接口（带认证和重试）
     *
     * @param installedAppId 已安装应用ID
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @return 原始 SSE 数据流
     */
    public Flux<String> callDifyChatWithInstalledAppId(String installedAppId, Map<String, Object> requestBody, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Flux.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            log.error("重试次数超过限制，放弃聊天请求: {}", installedAppId);
            return Flux.error(new RuntimeException("重试次数超过限制，认证失败"));
        }

        String uri = "/console/api/installed-apps/" + installedAppId + "/chat-messages";
        log.info("调用 Dify Console API 聊天接口 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedPostRequest(uri, requestBody, retryCount)
                .bodyToFlux(String.class)
                .doOnNext(line -> log.debug("收到 Dify 聊天 SSE 数据: {}", line))
                .doOnError(error -> log.error("Dify 聊天接口调用失败", error))
                .doOnComplete(() -> log.info("Dify 聊天接口调用完成"));
    }

    /**
     * 发起带认证的POST请求（支持令牌自动刷新和重试）
     *
     * @param uri 接口路径
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @return WebClient 响应规范
     */
    private WebClient.ResponseSpec makeAuthenticatedPostRequest(String uri, Map<String, Object> requestBody, int retryCount) {
        if (difyAuthService == null) {
            throw new RuntimeException("认证服务未启用");
        }

        if (retryCount > 2) {
            log.error("重试次数超过限制，放弃请求: {}", uri);
            throw new RuntimeException("重试次数超过限制，认证失败");
        }

        // 先尝试获取有效的访问令牌
        String accessToken;
        try {
            accessToken = difyAuthService.getValidAccessTokenSync();
        } catch (Exception e) {
            log.error("获取访问令牌失败", e);
            throw new RuntimeException("获取访问令牌失败: " + e.getMessage());
        }

        log.debug("使用访问令牌调用 Dify POST API (重试次数: {}): {}", retryCount, uri);

        return difyWebClient()
                .post()
                .uri(uri)
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .header("Accept", "text/event-stream")
                .bodyValue(requestBody)
                .retrieve()
                .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                    log.warn("收到 401 错误，检查是否为令牌过期");
                    return response.bodyToMono(String.class)
                            .map(body -> {
                                log.debug("401 错误响应体: {}", body);
                                // 检查是否为 Dify 特定的令牌过期错误格式
                                if (isDifyTokenExpiredError(body)) {
                                    return new WebClientResponseException(401, "Token Expired", null, body.getBytes(), null);
                                } else {
                                    return new WebClientResponseException(401, "Unauthorized", null, body.getBytes(), null);
                                }
                            });
                });
    }

    /**
     * 调用 Dify API（GET 请求，带认证）
     *
     * @param uri 接口路径
     * @return WebClient 响应规范
     */
    private WebClient.ResponseSpec callDifyGetApiWithAuth(String uri) {
        if (difyAuthService == null) {
            throw new RuntimeException("认证服务未启用");
        }

        String accessToken = difyAuthService.getValidAccessTokenSync();
        log.debug("使用访问令牌调用 Dify GET API: {}", uri);
        return callDifyGetApiWithAuthToken(uri, accessToken);
    }

    private WebClient.ResponseSpec callDifyGetApiWithAuthToken(String uri, String accessToken) {
        try {
            return difyWebClient()
                    .get()
                    .uri(uri)
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .retrieve()
                    .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                        log.warn("收到 401 错误，访问令牌可能过期");
                        return Mono.error(new WebClientResponseException(401, "Unauthorized", null, null, null));
                    })
                    .onStatus(status -> status.value() == 600, response -> {
                        log.warn("收到 600 错误，需要重新登录");
                        return Mono.error(new WebClientResponseException(600, "Need Relogin", null, null, null));
                    });

        } catch (WebClientResponseException e) {
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                log.warn("处理 401 错误：刷新令牌并重试");
                throw new RuntimeException("需要在响应式链中处理 401 错误");

            } else if (e.getStatusCode().value() == 600) {
                log.warn("处理 600 错误：重新登录并重试");
                throw new RuntimeException("需要在响应式链中处理 600 错误");

            } else {
                throw e;
            }
        }
    }



    /**
     * 响应式发起带认证的GET请求（支持令牌自动刷新）
     *
     * @param uri 接口路径
     * @return WebClient 响应规范的 Mono
     */
    private Mono<WebClient.ResponseSpec> makeAuthenticatedGetRequestReactive(String uri) {
        return makeAuthenticatedGetRequestReactive(uri, 0);
    }

    /**
     * 响应式发起带认证的GET请求（支持令牌自动刷新和重试）
     *
     * @param uri 接口路径
     * @param retryCount 重试次数
     * @return WebClient 响应规范的 Mono
     */
    private Mono<WebClient.ResponseSpec> makeAuthenticatedGetRequestReactive(String uri, int retryCount) {
        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        log.info("开始获取访问令牌 (重试次数: {})", retryCount);

        // 响应式获取访问令牌
        return difyAuthService.getValidAccessToken()
                .doOnSubscribe(subscription -> log.info("开始订阅 getValidAccessToken"))
                .doOnNext(accessToken -> log.info("获取到访问令牌: {}...", accessToken != null ? accessToken.substring(0, Math.min(20, accessToken.length())) : "null"))
                .doOnError(error -> log.error("获取访问令牌失败: {}", error.getMessage(), error))
                .doFinally(signalType -> log.info("getValidAccessToken 完成，信号类型: {}", signalType))
                .flatMap(accessToken -> {
                    if (accessToken == null || accessToken.trim().isEmpty()) {
                        log.error("获取到的访问令牌为空");
                        return Mono.error(new RuntimeException("访问令牌为空"));
                    }

                    log.info("使用访问令牌调用 Dify GET API (重试次数: {}): {}", retryCount, uri);
                    log.info("完整请求URL: {}{}", difyConfig.getBaseUrl(), uri);
                    log.info("请求头 - Authorization: Bearer {}...", accessToken.substring(0, Math.min(20, accessToken.length())));

                    WebClient.ResponseSpec responseSpec = difyWebClient()
                            .get()
                            .uri(uri)
                            .header("Authorization", "Bearer " + accessToken)
                            .header("Content-Type", "application/json")
                            .retrieve()
                            .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                                log.warn("收到 401 错误，检查是否为令牌过期");
                                return response.bodyToMono(String.class)
                                        .flatMap(body -> {
                                            log.debug("401 错误响应体: {}", body);
                                            // 检查是否为 Dify 特定的令牌过期错误格式
                                            if (isDifyTokenExpiredError(body)) {
                                                return Mono.error(new WebClientResponseException(401, "Token Expired", null, body.getBytes(), null));
                                            } else {
                                                return Mono.error(new WebClientResponseException(401, "Unauthorized", null, body.getBytes(), null));
                                            }
                                        });
                            })
                            .onStatus(status -> status.is4xxClientError(), response -> {
                                log.warn("收到 4xx 客户端错误: {}", response.statusCode());
                                return response.bodyToMono(String.class)
                                        .doOnNext(body -> log.warn("4xx 错误响应体: {}", body))
                                        .map(body -> new WebClientResponseException(response.statusCode().value(), "Client Error", null, body.getBytes(), null));
                            })
                            .onStatus(status -> status.is5xxServerError(), response -> {
                                log.error("收到 5xx 服务器错误: {}", response.statusCode());
                                return response.bodyToMono(String.class)
                                        .doOnNext(body -> log.error("5xx 错误响应体: {}", body))
                                        .map(body -> new WebClientResponseException(response.statusCode().value(), "Server Error", null, body.getBytes(), null));
                            });

                    log.info("创建了 ResponseSpec，准备返回");
                    return Mono.just(responseSpec);
                })
                .switchIfEmpty(Mono.defer(() -> {
                    log.error("getValidAccessToken 返回了空的 Mono");
                    return Mono.error(new RuntimeException("获取Dify访问令牌失败：返回空结果"));
                }))
                .onErrorMap(e -> {
                    // 如果错误信息已经包含Dify相关信息，直接返回
                    if (e.getMessage().contains("Dify")) {
                        return e;
                    }
                    // 否则添加Dify前缀
                    return new RuntimeException("Dify API调用失败: " + e.getMessage(), e);
                });
    }

    /**
     * 发起带认证的GET请求（支持令牌自动刷新）
     *
     * @param uri 接口路径
     * @return WebClient 响应规范
     */
    private WebClient.ResponseSpec makeAuthenticatedGetRequest(String uri) {
        return makeAuthenticatedGetRequest(uri, 0);
    }

    /**
     * 发起带认证的GET请求（支持令牌自动刷新和重试）
     *
     * @param uri 接口路径
     * @param retryCount 重试次数
     * @return WebClient 响应规范
     */
    private WebClient.ResponseSpec makeAuthenticatedGetRequest(String uri, int retryCount) {
        if (difyAuthService == null) {
            throw new RuntimeException("认证服务未启用");
        }

        if (retryCount > 2) {
            log.error("重试次数超过限制，放弃请求: {}", uri);
            throw new RuntimeException("重试次数超过限制，认证失败");
        }

        // 先尝试获取有效的访问令牌
        String accessToken;
        try {
            accessToken = difyAuthService.getValidAccessTokenSync();
        } catch (Exception e) {
            log.error("获取访问令牌失败", e);
            throw new RuntimeException("获取访问令牌失败: " + e.getMessage());
        }

        log.debug("使用访问令牌调用 Dify GET API (重试次数: {}): {}", retryCount, uri);

        return difyWebClient()
                .get()
                .uri(uri)
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .retrieve()
                .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                    log.warn("收到 401 错误，检查是否为令牌过期");
                    return response.bodyToMono(String.class)
                            .map(body -> {
                                log.debug("401 错误响应体: {}", body);
                                // 检查是否为 Dify 特定的令牌过期错误格式
                                if (isDifyTokenExpiredError(body)) {
                                    return new WebClientResponseException(401, "Token Expired", null, body.getBytes(), null);
                                } else {
                                    return new WebClientResponseException(401, "Unauthorized", null, body.getBytes(), null);
                                }
                            });
                });
    }

    /**
     * 检查是否为 Dify 特定的令牌过期错误
     *
     * @param responseBody 响应体
     * @return 是否为令牌过期错误
     */
    private boolean isDifyTokenExpiredError(String responseBody) {
        try {
            // 检查响应体是否包含 Dify 特定的令牌过期标识
            return responseBody != null &&
                   (responseBody.contains("\"code\": \"unauthorized\"") ||
                    responseBody.contains("\"code\":\"unauthorized\"")) &&
                   (responseBody.contains("Token has expired") ||
                    responseBody.contains("token has expired") ||
                    responseBody.contains("令牌已过期"));
        } catch (Exception e) {
            log.debug("解析错误响应体失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取会话消息列表（带认证）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param limit 限制数量，默认20
     * @param lastId 最后一个消息ID（用于分页）
     * @return 消息列表
     */
    public Mono<List<DifyMessageDTO>> getConversationMessagesWithAuth(String installedAppId, String conversationId, Integer limit, String lastId) {
        return getConversationMessagesWithAuth(installedAppId, conversationId, limit, lastId, 0);
    }

    /**
     * 获取会话消息列表（带认证，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param limit 限制数量，默认20
     * @param lastId 最后一个消息ID（用于分页）
     * @param retryCount 重试次数
     * @return 消息列表
     */
    private Mono<List<DifyMessageDTO>> getConversationMessagesWithAuth(String installedAppId, String conversationId, Integer limit, String lastId, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        // 构建查询参数
        StringBuilder uriBuilder = new StringBuilder("/console/api/installed-apps/")
                .append(installedAppId)
                .append("/messages");

        // 添加查询参数
        boolean hasParams = false;
        if (conversationId != null && !conversationId.trim().isEmpty()) {
            uriBuilder.append("?conversation_id=").append(conversationId);
            hasParams = true;
        }

        if (limit != null && limit > 0) {
            uriBuilder.append(hasParams ? "&" : "?").append("limit=").append(limit);
            hasParams = true;
        }

        if (lastId != null && !lastId.trim().isEmpty()) {
            uriBuilder.append(hasParams ? "&" : "?").append("last_id=").append(lastId);
        }

        String uri = uriBuilder.toString();
        log.info("获取会话消息列表 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedGetRequestReactive(uri, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyMessageListResponseDTO.class))
                .map(response -> response.getData() != null ? response.getData() : new ArrayList<DifyMessageDTO>())
                .doOnNext(messages -> log.debug("收到消息列表响应: {} 条消息", messages != null ? messages.size() : 0))
                .doOnError(error -> log.error("获取消息列表失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> getConversationMessagesWithAuth(installedAppId, conversationId, limit, lastId, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> getConversationMessagesWithAuth(installedAppId, conversationId, limit, lastId, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取会话消息列表（带认证，使用默认参数）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @return 消息列表
     */
    public Mono<List<DifyMessageDTO>> getConversationMessagesWithAuth(String installedAppId, String conversationId) {
        return getConversationMessagesWithAuth(installedAppId, conversationId, 20, null);
    }

    /**
     * 获取会话消息列表（带认证，返回原始响应）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param limit 限制数量，默认20
     * @param lastId 最后一个消息ID（用于分页）
     * @return 原始消息列表响应
     */
    public Mono<DifyMessageListResponseDTO> getConversationMessagesRawWithAuth(String installedAppId, String conversationId, Integer limit, String lastId) {
        return getConversationMessagesRawWithAuth(installedAppId, conversationId, limit, lastId, 0);
    }

    /**
     * 获取会话消息列表（带认证，返回原始响应，支持重试）
     *
     * @param installedAppId 已安装应用ID
     * @param conversationId 会话ID
     * @param limit 限制数量，默认20
     * @param lastId 最后一个消息ID（用于分页）
     * @param retryCount 重试次数
     * @return 原始消息列表响应
     */
    private Mono<DifyMessageListResponseDTO> getConversationMessagesRawWithAuth(String installedAppId, String conversationId, Integer limit, String lastId, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        // 构建查询参数
        StringBuilder uriBuilder = new StringBuilder("/console/api/installed-apps/")
                .append(installedAppId)
                .append("/messages");

        // 添加查询参数
        boolean hasParams = false;
        if (conversationId != null && !conversationId.trim().isEmpty()) {
            uriBuilder.append("?conversation_id=").append(conversationId);
            hasParams = true;
        }

        if (limit != null && limit > 0) {
            uriBuilder.append(hasParams ? "&" : "?").append("limit=").append(limit);
            hasParams = true;
        }

        if (lastId != null && !lastId.trim().isEmpty()) {
            uriBuilder.append(hasParams ? "&" : "?").append("last_id=").append(lastId);
        }

        String uri = uriBuilder.toString();
        log.info("获取会话消息列表原始响应 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedGetRequestReactive(uri, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyMessageListResponseDTO.class))
                .doOnNext(response -> log.debug("收到原始消息列表响应: {} 条消息", response.getData() != null ? response.getData().size() : 0))
                .doOnError(error -> log.error("获取原始消息列表失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> getConversationMessagesRawWithAuth(installedAppId, conversationId, limit, lastId, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> getConversationMessagesRawWithAuth(installedAppId, conversationId, limit, lastId, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取智能体列表（带认证）
     *
     * @param page 页码，默认1
     * @param limit 每页数量，默认30
     * @param name 智能体名称过滤（可选）
     * @param isCreatedByMe 是否只获取我创建的智能体，默认true
     * @return 智能体列表响应
     */
    public Mono<DifyAppsListResponseDTO> getAppsListWithAuth(Integer page, Integer limit, String name, Boolean isCreatedByMe) {
        return getAppsListWithAuth(page, limit, name, isCreatedByMe, 0);
    }

    /**
     * 获取指定平台的智能体列表（带认证）
     *
     * @param platformId 平台ID
     * @param page 页码，默认1
     * @param limit 每页数量，默认30
     * @param name 智能体名称过滤（可选）
     * @param isCreatedByMe 是否只获取我创建的智能体，默认true
     * @return 智能体列表响应
     */
    public Mono<DifyAppsListResponseDTO> getAppsListWithAuth(String platformId, Integer page, Integer limit, String name, Boolean isCreatedByMe) {
        return getAppsListWithAuth(platformId, page, limit, name, isCreatedByMe, 0);
    }

    /**
     * 获取智能体列表（带认证，支持重试）
     *
     * @param page 页码，默认1
     * @param limit 每页数量，默认30
     * @param name 智能体名称过滤（可选）
     * @param isCreatedByMe 是否只获取我创建的智能体，默认true
     * @param retryCount 重试次数
     * @return 智能体列表响应
     */
    private Mono<DifyAppsListResponseDTO> getAppsListWithAuth(Integer page, Integer limit, String name, Boolean isCreatedByMe, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        // 构建查询参数
        StringBuilder uriBuilder = new StringBuilder("/console/api/apps");
        uriBuilder.append("?page=").append(page != null ? page : 1);
        uriBuilder.append("&limit=").append(limit != null ? limit : 30);

        if (name != null && !name.trim().isEmpty()) {
            uriBuilder.append("&name=").append(name.trim());
        } else {
            uriBuilder.append("&name=");
        }

        uriBuilder.append("&is_created_by_me=").append(isCreatedByMe != null ? isCreatedByMe : true);

        String uri = uriBuilder.toString();
        log.info("获取智能体列表 (重试次数: {}): {}", retryCount, uri);

        return makeAuthenticatedGetRequestReactive(uri, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyAppsListResponseDTO.class))
                .doOnNext(response -> log.debug("收到智能体列表响应: {} 个智能体", response.getData() != null ? response.getData().size() : 0))
                .doOnError(error -> log.error("获取智能体列表失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> getAppsListWithAuth(page, limit, name, isCreatedByMe, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> getAppsListWithAuth(page, limit, name, isCreatedByMe, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取指定平台的智能体列表（带认证，支持重试）
     *
     * @param platformId 平台ID
     * @param page 页码，默认1
     * @param limit 每页数量，默认30
     * @param name 智能体名称过滤（可选）
     * @param isCreatedByMe 是否只获取我创建的智能体，默认true
     * @param retryCount 重试次数
     * @return 智能体列表响应
     */
    private Mono<DifyAppsListResponseDTO> getAppsListWithAuth(String platformId, Integer page, Integer limit, String name, Boolean isCreatedByMe, int retryCount) {
        if (difyMultiPlatformAuthService == null) {
            log.warn("多平台认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("多平台认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        // 构建查询参数
        StringBuilder uriBuilder = new StringBuilder("/console/api/apps");
        uriBuilder.append("?page=").append(page != null ? page : 1);
        uriBuilder.append("&limit=").append(limit != null ? limit : 30);

        if (name != null && !name.trim().isEmpty()) {
            uriBuilder.append("&name=").append(name.trim());
        } else {
            uriBuilder.append("&name=");
        }

        uriBuilder.append("&is_created_by_me=").append(isCreatedByMe != null ? isCreatedByMe : true);

        String uri = uriBuilder.toString();
        log.info("获取平台{}的智能体列表 (重试次数: {}): {}", platformId, retryCount, uri);

        return makeAuthenticatedGetRequestWithRetryForPlatform(platformId, uri, retryCount, DifyAppsListResponseDTO.class)
                .doOnNext(response -> log.debug("收到平台{}的智能体列表响应: {} 个智能体", platformId, response.getData() != null ? response.getData().size() : 0))
                .doOnError(error -> log.error("获取平台{}的智能体列表失败", platformId, error));
    }

    /**
     * 获取智能体列表（带认证，使用默认参数）
     *
     * @return 智能体列表响应
     */
    public Mono<DifyAppsListResponseDTO> getAppsListWithAuth() {
        return getAppsListWithAuth(1, 30, null, true);
    }

    /**
     * 获取指定平台的智能体列表（带认证，使用默认参数）
     *
     * @param platformId 平台ID
     * @return 智能体列表响应
     */
    public Mono<DifyAppsListResponseDTO> getAppsListWithAuth(String platformId) {
        return getAppsListWithAuth(platformId, 1, 30, null, true);
    }

    /**
     * 测试Dify API连接性
     */
    public Mono<String> testDifyConnection() {
        log.info("测试Dify API连接性");

        return difyWebClient()
                .get()
                .uri("/health")  // 尝试访问健康检查端点
                .retrieve()
                .bodyToMono(String.class)
                .doOnNext(response -> log.info("Dify健康检查响应: {}", response))
                .onErrorResume(error -> {
                    log.warn("Dify健康检查失败，尝试访问根路径: {}", error.getMessage());
                    return difyWebClient()
                            .get()
                            .uri("/")
                            .retrieve()
                            .bodyToMono(String.class)
                            .doOnNext(response -> log.info("Dify根路径响应: {}", response))
                            .onErrorResume(rootError -> {
                                log.error("Dify根路径也无法访问: {}", rootError.getMessage());
                                return Mono.just("连接失败: " + rootError.getMessage());
                            });
                });
    }

    /**
     * 上传文件到 Dify（带认证）
     *
     * @param fileContent 文件内容
     * @param fileName 文件名
     * @param contentType 内容类型
     * @return 文件上传响应
     */
    public Mono<DifyFileUploadResponseDTO> uploadFileWithAuth(byte[] fileContent, String fileName, String contentType) {
        return uploadFileWithAuth(fileContent, fileName, contentType, 0);
    }

    /**
     * 上传文件到 Dify（带认证，支持重试）
     *
     * @param fileContent 文件内容
     * @param fileName 文件名
     * @param contentType 内容类型
     * @param retryCount 重试次数
     * @return 文件上传响应
     */
    private Mono<DifyFileUploadResponseDTO> uploadFileWithAuth(byte[] fileContent, String fileName, String contentType, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/files/upload";
        log.info("上传文件到 Dify: fileName={}, contentType={}, size={}", fileName, contentType, fileContent.length);

        return makeAuthenticatedFileUploadRequest(uri, fileContent, fileName, contentType, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyFileUploadResponseDTO.class))
                .doOnNext(response -> log.debug("收到文件上传响应: {}", response))
                .doOnError(error -> log.error("文件上传失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> uploadFileWithAuth(fileContent, fileName, contentType, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> uploadFileWithAuth(fileContent, fileName, contentType, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 发送认证的文件上传请求
     *
     * @param uri 接口路径
     * @param fileContent 文件内容
     * @param fileName 文件名
     * @param contentType 内容类型
     * @param retryCount 重试次数
     * @return WebClient 响应规范
     */
    private Mono<WebClient.ResponseSpec> makeAuthenticatedFileUploadRequest(String uri, byte[] fileContent, String fileName, String contentType, int retryCount) {
        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用 Dify 文件上传 API (重试次数: {}): {}", retryCount, uri);

                    // 创建 MultiValueMap 用于 multipart/form-data
                    MultiValueMap<String, HttpEntity<?>> parts = new LinkedMultiValueMap<>();

                    // 创建文件部分
                    HttpHeaders fileHeaders = new HttpHeaders();
                    fileHeaders.setContentType(MediaType.parseMediaType(contentType));
                    fileHeaders.setContentDispositionFormData("file", fileName);
                    HttpEntity<byte[]> fileEntity = new HttpEntity<>(fileContent, fileHeaders);
                    parts.add("file", fileEntity);

                    return Mono.just(difyWebClient()
                            .post()
                            .uri(uri)
                            .header("Authorization", "Bearer " + accessToken)
                            .contentType(MediaType.MULTIPART_FORM_DATA)
                            .bodyValue(parts)
                            .retrieve()
                            .onStatus(HttpStatus.UNAUTHORIZED::equals, response -> {
                                log.warn("收到 401 错误，检查是否为令牌过期");
                                return response.bodyToMono(String.class)
                                        .map(body -> {
                                            log.debug("401 错误响应体: {}", body);
                                            return new WebClientResponseException(401, "Unauthorized", null, body.getBytes(), null);
                                        });
                            })
                            .onStatus(status -> status.value() == 600, response -> {
                                log.warn("收到 600 错误，需要重新登录");
                                return Mono.error(new WebClientResponseException(600, "Need Relogin", null, null, null));
                            }));
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> makeAuthenticatedFileUploadRequest(uri, fileContent, fileName, contentType, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> makeAuthenticatedFileUploadRequest(uri, fileContent, fileName, contentType, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 上传远程文件到 Dify（带认证）
     *
     * @param fileUrl 远程文件URL
     * @return 远程文件上传响应
     */
    public Mono<DifyRemoteFileUploadResponseDTO> uploadRemoteFileWithAuth(String fileUrl) {
        return uploadRemoteFileWithAuth(fileUrl, 0);
    }

    /**
     * 上传远程文件到 Dify（带认证，支持重试）
     *
     * @param fileUrl 远程文件URL
     * @param retryCount 重试次数
     * @return 远程文件上传响应
     */
    private Mono<DifyRemoteFileUploadResponseDTO> uploadRemoteFileWithAuth(String fileUrl, int retryCount) {
        if (difyAuthService == null) {
            log.warn("认证服务未启用，无法调用 Console API");
            return Mono.error(new RuntimeException("认证服务未启用，无法调用需要认证的接口"));
        }

        if (retryCount > 2) {
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        String uri = "/console/api/remote-files/upload";
        log.info("上传远程文件到 Dify: fileUrl={}", fileUrl);

        DifyRemoteFileUploadRequestDTO requestDTO = new DifyRemoteFileUploadRequestDTO(fileUrl);

        return makeAuthenticatedPostRequestForJson(uri, requestDTO, retryCount)
                .flatMap(responseSpec -> responseSpec.bodyToMono(DifyRemoteFileUploadResponseDTO.class))
                .doOnNext(response -> log.debug("收到远程文件上传响应: {}", response))
                .doOnError(error -> log.error("远程文件上传失败", error))
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("处理 401 错误：刷新令牌并重试");
                        return difyAuthService.handleUnauthorized()
                                .flatMap(newAccessToken -> uploadRemoteFileWithAuth(fileUrl, retryCount + 1));

                    } else if (e.getStatusCode().value() == 600) {
                        log.warn("处理 600 错误：重新登录并重试");
                        return difyAuthService.handleRelogin()
                                .flatMap(newAccessToken -> uploadRemoteFileWithAuth(fileUrl, retryCount + 1));

                    } else {
                        return Mono.error(e);
                    }
                });
    }

    /**
     * 获取当前用户ID并添加到Reactor Context中
     * 优先从Reactor Context获取，如果没有则从SecurityContext获取
     *
     * @return 包含用户ID的Mono
     */
    private Mono<String> getCurrentUserIdForContext() {
        return Mono.deferContextual(contextView -> {
            // 首先尝试从Reactor Context获取用户ID
            String userId = contextView.getOrDefault("userId", null);

            if (StringUtils.hasText(userId)) {
                log.debug("从Reactor Context获取到用户ID: {}", userId);
                return Mono.just(userId);
            }

            // 如果Context中没有，尝试从SecurityContext获取
            return Mono.fromCallable(() -> {
                try {
                    log.debug("尝试从SecurityContext获取当前用户信息");
                    LoginUser currentUser = SecurityUtils.getCurrentUserSafely();
                    if (currentUser != null) {
                        log.debug("获取到用户信息: {}", currentUser.getUsername());
                        if (StringUtils.hasText(currentUser.getUserId())) {
                            log.debug("获取到用户ID: {}", currentUser.getUserId());
                            return currentUser.getUserId();
                        } else {
                            log.warn("用户ID为空");
                        }
                    } else {
                        log.warn("未获取到用户信息，可能未登录或在响应式上下文中");
                    }
                } catch (Exception e) {
                    log.error("获取当前用户ID失败: {}", e.getMessage(), e);
                }
                return null;
            }).subscribeOn(Schedulers.boundedElastic());
        });
    }

    /**
     * 响应式发起带认证的GET请求（支持指定平台）
     * 参考DifyAuthServiceImpl的实现方式，直接处理401错误并进行令牌刷新
     *
     * @param platformId 平台ID
     * @param uri 接口路径
     * @param retryCount 重试次数
     * @param responseType 响应类型
     * @return 响应体的 Mono
     */
    private <T> Mono<T> makeAuthenticatedGetRequestWithRetryForPlatform(String platformId, String uri, int retryCount, Class<T> responseType) {
        if (retryCount > 2) {
            log.error("平台{}的API调用重试次数超限: {}", platformId, retryCount);
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        log.info("开始调用平台{}的API (重试次数: {}): {}", platformId, retryCount, uri);

        // 获取访问令牌并发起请求
        return difyMultiPlatformAuthService.getCurrentUserValidAccessToken(platformId)
                .flatMap(accessToken -> {
                    if (!StringUtils.hasText(accessToken)) {
                        return Mono.error(new RuntimeException("访问令牌为空"));
                    }

                    // 获取平台配置并发起请求
                    return Mono.fromCallable(() -> thirdPlatformMapper.selectById(platformId))
                            .subscribeOn(Schedulers.boundedElastic())
                            .flatMap(platform -> {
                                if (platform == null) {
                                    return Mono.error(new RuntimeException("平台配置不存在: " + platformId));
                                }

                                log.debug("使用平台{}的访问令牌调用API: {}{}", platformId, platform.getConnectionUrl(), uri);

                                WebClient webClient = webClientBuilder.baseUrl(platform.getConnectionUrl()).build();
                                return webClient
                                        .get()
                                        .uri(uri)
                                        .header("Authorization", "Bearer " + accessToken)
                                        .header("Content-Type", "application/json")
                                        .retrieve()
                                        .bodyToMono(responseType);
                            });
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("平台{}收到401错误，尝试刷新令牌 (重试次数: {})", platformId, retryCount);

                        // 检查是否为令牌过期错误
                        String responseBody = e.getResponseBodyAsString();
                        log.debug("平台{}的401错误响应体: {}", platformId, responseBody);
                        if (isDifyTokenExpiredError(responseBody)) {
                            log.info("平台{}令牌已过期，开始刷新令牌", platformId);
                            return getCurrentUserIdForContext()
                                    .doOnNext(userId -> log.debug("获取到用户ID: {}", userId))
                                    .doOnError(error -> log.error("获取用户ID失败", error))
                                    .flatMap(userId -> {
                                        if (StringUtils.hasText(userId)) {
                                            log.info("使用用户ID {}刷新平台{}的令牌", userId, platformId);
                                            return difyMultiPlatformAuthService.handleCurrentUserUnauthorized(platformId)
                                                    .contextWrite(context -> context.put("userId", userId))
                                                    .doOnNext(newToken -> log.info("平台{}令牌刷新成功，新令牌: {}", platformId, newToken != null ? "已获取" : "为空"))
                                                    .doOnError(refreshError -> log.error("平台{}令牌刷新失败", platformId, refreshError));
                                        } else {
                                            log.error("无法获取用户ID，无法刷新平台{}的令牌", platformId);
                                            return Mono.error(new RuntimeException("用户未登录或无法获取用户信息，无法刷新令牌"));
                                        }
                                    })
                                    .flatMap(newToken -> {
                                        if (StringUtils.hasText(newToken)) {
                                            log.info("平台{}令牌刷新成功，使用新令牌重试API调用", platformId);
                                            // 直接使用新令牌发起请求，避免重新获取用户信息
                                            return callDifyApiWithAuthTokenForPlatform(platformId, uri, newToken, responseType);
                                        } else {
                                            log.error("平台{}令牌刷新失败：新令牌为空", platformId);
                                            return Mono.error(new RuntimeException("令牌刷新失败：新令牌为空"));
                                        }
                                    })
                                    .onErrorResume(refreshError -> {
                                        log.error("平台{}令牌刷新过程失败: {}", platformId, refreshError.getMessage(), refreshError);
                                        return Mono.error(new RuntimeException("调用dify接口令牌过期，无法刷新令牌: " + refreshError.getMessage(), refreshError));
                                    });
                        } else {
                            log.error("平台{}未授权访问，非令牌过期错误，响应体: {}", platformId, responseBody);
                            return Mono.error(e);
                        }
                    } else {
                        log.error("平台{}API调用失败，状态码: {}", platformId, e.getStatusCode());
                        return Mono.error(e);
                    }
                });
    }

    @Autowired
    private ThirdPlatformMapper thirdPlatformMapper;

    /**
     * 使用指定令牌直接调用Dify API（用于重试场景）
     *
     * @param platformId 平台ID
     * @param uri API路径
     * @param accessToken 访问令牌
     * @param responseType 响应类型
     * @return API响应
     */
    private <T> Mono<T> callDifyApiWithAuthTokenForPlatform(String platformId, String uri, String accessToken, Class<T> responseType) {
        log.debug("使用指定令牌调用平台{}的API: {}", platformId, uri);

        // 获取平台配置
        ThirdPlatform platform = thirdPlatformMapper.selectById(platformId);
        if (platform == null) {
            return Mono.error(new RuntimeException("平台配置不存在: " + platformId));
        }

        String baseUrl = platform.getConnectionUrl();
        if (!StringUtils.hasText(baseUrl)) {
            return Mono.error(new RuntimeException("平台" + platformId + "的连接URL未配置"));
        }

        // 构建完整URL
        String fullUrl = baseUrl + uri;

        // 创建WebClient并发起请求
        WebClient webClient = webClientBuilder.baseUrl(baseUrl).build();

        return webClient.get()
                .uri(uri)
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .retrieve()
                .bodyToMono(responseType)
                .doOnNext(response -> log.debug("平台{}API调用成功", platformId))
                .doOnError(error -> log.error("平台{}API调用失败", platformId, error));
    }

    /**
     * 响应式发起带认证的POST请求（支持指定平台）
     *
     * @param platformId 平台ID
     * @param uri 接口路径
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @return 响应体的 Mono
     */
    public <T> Mono<T> makeAuthenticatedPostRequestForPlatform(String platformId, String uri, Object requestBody, Class<T> responseType) {
        return makeAuthenticatedPostRequestWithRetryForPlatform(platformId, uri, requestBody, 0, responseType);
    }

    /**
     * 响应式发起带认证的POST请求（支持指定平台，带重试机制）
     *
     * @param platformId 平台ID
     * @param uri 接口路径
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @param responseType 响应类型
     * @return 响应体的 Mono
     */
    private <T> Mono<T> makeAuthenticatedPostRequestWithRetryForPlatform(String platformId, String uri, Object requestBody, int retryCount, Class<T> responseType) {
        if (retryCount > 2) {
            log.error("平台{}的POST API调用重试次数超限: {}", platformId, retryCount);
            return Mono.error(new RuntimeException("API 调用重试次数超限"));
        }

        log.info("开始调用平台{}的POST API (重试次数: {}): {}", platformId, retryCount, uri);

        // 获取访问令牌并发起请求
        return difyMultiPlatformAuthService.getCurrentUserValidAccessToken(platformId)
                .flatMap(accessToken -> {
                    if (!StringUtils.hasText(accessToken)) {
                        return Mono.error(new RuntimeException("访问令牌为空"));
                    }

                    // 获取平台配置并发起请求
                    return Mono.fromCallable(() -> thirdPlatformMapper.selectById(platformId))
                            .subscribeOn(Schedulers.boundedElastic())
                            .flatMap(platform -> {
                                if (platform == null) {
                                    return Mono.error(new RuntimeException("平台配置不存在: " + platformId));
                                }

                                log.debug("使用平台{}的访问令牌调用POST API: {}{}", platformId, platform.getConnectionUrl(), uri);

                                WebClient webClient = webClientBuilder.baseUrl(platform.getConnectionUrl()).build();
                                return webClient
                                        .post()
                                        .uri(uri)
                                        .header("Authorization", "Bearer " + accessToken)
                                        .header("Content-Type", "application/json")
                                        .bodyValue(requestBody)
                                        .retrieve()
                                        .bodyToMono(responseType);
                            });
                })
                .onErrorResume(WebClientResponseException.class, e -> {
                    if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                        log.warn("平台{}收到401错误，尝试刷新令牌 (重试次数: {})", platformId, retryCount);

                        // 检查是否为令牌过期错误
                        String responseBody = e.getResponseBodyAsString();
                        log.debug("平台{}的401错误响应体: {}", platformId, responseBody);
                        if (isDifyTokenExpiredError(responseBody)) {
                            log.info("平台{}令牌已过期，开始刷新令牌", platformId);
                            return getCurrentUserIdForContext()
                                    .doOnNext(userId -> log.debug("获取到用户ID: {}", userId))
                                    .doOnError(error -> log.error("获取用户ID失败", error))
                                    .flatMap(userId -> {
                                        if (StringUtils.hasText(userId)) {
                                            log.info("使用用户ID {}刷新平台{}的令牌", userId, platformId);
                                            return difyMultiPlatformAuthService.handleCurrentUserUnauthorized(platformId)
                                                    .contextWrite(context -> context.put("userId", userId))
                                                    .doOnNext(newToken -> log.info("平台{}令牌刷新成功，新令牌: {}", platformId, newToken != null ? "已获取" : "为空"))
                                                    .doOnError(refreshError -> log.error("平台{}令牌刷新失败", platformId, refreshError));
                                        } else {
                                            log.error("无法获取用户ID，无法刷新平台{}的令牌", platformId);
                                            return Mono.error(new RuntimeException("用户未登录或无法获取用户信息，无法刷新令牌"));
                                        }
                                    })
                                    .flatMap(newToken -> {
                                        if (StringUtils.hasText(newToken)) {
                                            log.info("平台{}令牌刷新成功，使用新令牌重试POST API调用", platformId);
                                            // 直接使用新令牌发起请求，避免重新获取用户信息
                                            return callDifyPostApiWithAuthTokenForPlatform(platformId, uri, requestBody, newToken, responseType);
                                        } else {
                                            log.error("平台{}令牌刷新失败：新令牌为空", platformId);
                                            return Mono.error(new RuntimeException("令牌刷新失败：新令牌为空"));
                                        }
                                    })
                                    .doOnError(error -> log.error("平台{}令牌刷新过程中发生错误", platformId, error));
                        } else {
                            log.warn("平台{}收到401错误，但不是令牌过期错误，直接返回错误", platformId);
                            return Mono.error(e);
                        }
                    } else {
                        log.error("平台{}API调用失败，状态码: {}", platformId, e.getStatusCode());
                        return Mono.error(e);
                    }
                })
                .doOnNext(response -> log.debug("平台{}POST API调用成功", platformId))
                .doOnError(error -> log.error("平台{}POST API调用最终失败", platformId, error));
    }

    /**
     * 使用指定令牌直接调用Dify POST API（用于重试场景）
     *
     * @param platformId 平台ID
     * @param uri API路径
     * @param requestBody 请求体
     * @param accessToken 访问令牌
     * @param responseType 响应类型
     * @return API响应
     */
    private <T> Mono<T> callDifyPostApiWithAuthTokenForPlatform(String platformId, String uri, Object requestBody, String accessToken, Class<T> responseType) {
        log.debug("使用指定令牌调用平台{}的POST API: {}", platformId, uri);

        // 获取平台配置
        ThirdPlatform platform = thirdPlatformMapper.selectById(platformId);
        if (platform == null) {
            return Mono.error(new RuntimeException("平台配置不存在: " + platformId));
        }

        String baseUrl = platform.getConnectionUrl();
        if (!StringUtils.hasText(baseUrl)) {
            return Mono.error(new RuntimeException("平台" + platformId + "的连接URL未配置"));
        }

        // 创建WebClient并发起请求
        WebClient webClient = webClientBuilder.baseUrl(baseUrl).build();

        return webClient.post()
                .uri(uri)
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(responseType)
                .doOnNext(response -> log.debug("平台{}POST API调用成功", platformId))
                .doOnError(error -> log.error("平台{}POST API调用失败", platformId, error));
    }

}
