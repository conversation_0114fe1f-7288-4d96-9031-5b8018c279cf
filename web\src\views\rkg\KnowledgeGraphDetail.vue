<template>
  <div class="knowledge-graph-detail h-screen bg-gray-50 flex flex-col">
    <!-- 顶部工具栏 -->
    <div class="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <button
          @click="goBack"
          class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <div>
          <h1 class="text-xl font-semibold text-gray-900">{{ graphData.name }}</h1>
          <p class="text-sm text-gray-500">{{ graphData.nodeCount }} 个节点，{{ graphData.edgeCount }} 个关系</p>
        </div>
      </div>
      
      <div class="flex items-center gap-3">
        <!-- 布局选择 -->
        <select
          v-model="selectedLayout"
          @change="applyLayout"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="force">力导向布局</option>
          <option value="circular">环形布局</option>
          <option value="hierarchical">层次布局</option>
          <option value="grid">网格布局</option>
        </select>

        <!-- 关联深度控制 -->
        <div class="flex items-center gap-2">
          <label class="text-sm text-gray-600">关联深度:</label>
          <select
            v-model="relationDepth"
            @change="updateRelationDepth"
            class="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="1">1层</option>
            <option value="2">2层</option>
            <option value="3">3层</option>
            <option value="all">全部</option>
          </select>
        </div>
        
        <!-- 搜索 -->
        <div class="relative">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索节点..."
            class="pl-8 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 w-48"
          >
          <svg class="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
        
        <!-- 工具按钮 -->
        <button
          @click="toggleSidebar"
          class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="属性面板"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
        
        <button
          @click="fitView"
          class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="适应视图"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
          </svg>
        </button>

        <button
          @click="resetView"
          class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="重置视图"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex relative">
      <!-- 知识图谱可视化 -->
      <div class="flex-1 relative flex flex-col">
        <!-- 过滤提示组件 -->
        <div class="filter-tips-wrapper">
          <GraphFilterTips
            :selected-node-types="selectedNodeTypes"
            :selected-edge-types="selectedEdgeTypes"
            :relation-depth="relationDepth"
            :selected-layout="selectedLayout"
            @remove-node-type="handleRemoveNodeType"
            @remove-edge-type="handleRemoveEdgeType"
            @clear-all="handleClearAllFilters"
          />
        </div>

        <!-- 图谱画布 -->
        <div class="flex-1 relative">
          <VueFlow
            ref="vueFlowRef"
            v-model="elements"
            :default-viewport="{ zoom: 0.8 }"
            :min-zoom="0.1"
            :max-zoom="4"
            @nodes-change="onNodesChange"
            @edges-change="onEdgesChange"
            @node-click="onNodeClick"
            @edge-click="onEdgeClick"
            class="knowledge-graph-flow"
          >
            <Background color="#f1f5f9" :gap="20" />
            <Controls />
            <MiniMap />

            <!-- 自定义节点 -->
            <template #node-entity="{ data }">
              <EntityNode :data="data" :selected="selectedNode?.id === data.id" />
            </template>

            <template #node-concept="{ data }">
              <ConceptNode :data="data" :selected="selectedNode?.id === data.id" />
            </template>
          </VueFlow>
        </div>

        <!-- 图例 -->
        <div class="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
          <h3 class="text-sm font-medium text-gray-900 mb-3">图例</h3>
          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
              <span class="text-xs text-gray-600">实体节点</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 bg-green-500 rounded-full"></div>
              <span class="text-xs text-gray-600">概念节点</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-1 bg-gray-400"></div>
              <span class="text-xs text-gray-600">关系连接</span>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div
          class="absolute top-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 cursor-pointer hover:shadow-xl transition-shadow"
          @click="toggleStatisticsPanel"
        >
          <div class="grid grid-cols-2 gap-4 text-center">
            <div>
              <div class="text-lg font-semibold text-blue-600">{{ graphData.nodeCount }}</div>
              <div class="text-xs text-gray-500">节点</div>
            </div>
            <div>
              <div class="text-lg font-semibold text-green-600">{{ graphData.edgeCount }}</div>
              <div class="text-xs text-gray-500">关系</div>
            </div>
          </div>
          <div class="text-xs text-gray-400 mt-2 text-center">点击查看详情</div>
        </div>

        <!-- 统计信息弹出层 -->
        <div
          v-if="showStatisticsPanel"
          class="absolute top-4 left-4 bg-white rounded-lg shadow-2xl border border-gray-200 p-6 z-50 w-80 max-h-full overflow-y-auto"
        >
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">图谱统计信息</h3>
            <button
              @click="showStatisticsPanel = false"
              class="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <GraphStatisticsPanel
            :nodes="allElements.filter(el => isNodeElement(el)) as Node[]"
            :edges="allElements.filter(el => isEdgeElement(el)) as Edge[]"
            @node-type-select="handleNodeTypeSelect"
            @edge-type-select="handleEdgeTypeSelect"
            @reset-filter="handleResetFilter"
          />
        </div>
      </div>

      <!-- 侧边栏 -->
      <div
        v-if="showSidebar"
        class="w-80 bg-white border-l border-gray-200 overflow-y-auto"
      >
        <div class="p-6">
          <!-- 选中节点信息 -->
          <div v-if="selectedNode" class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">节点信息</h3>
            <NodeDetails :node="selectedNode" />
          </div>

          <!-- 选中边信息 -->
          <div v-if="selectedEdge" class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">关系信息</h3>
            <EdgeDetails :edge="selectedEdge" />
          </div>

          <!-- 图谱统计 -->
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">图谱统计</h3>
            <GraphStatistics :data="graphData" />
          </div>

          <!-- 节点类型分布 -->
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">节点类型</h3>
            <NodeTypeDistribution :distribution="nodeTypeDistribution" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { MiniMap } from '@vue-flow/minimap'
import { Controls } from '@vue-flow/controls'
import type { Node, Edge } from '@vue-flow/core'

// 导入自定义组件
import EntityNode from './components/graph/EntityNode.vue'
import ConceptNode from './components/graph/ConceptNode.vue'
import NodeDetails from './components/graph/NodeDetails.vue'
import EdgeDetails from './components/graph/EdgeDetails.vue'
import GraphStatistics from './components/graph/GraphStatistics.vue'
import NodeTypeDistribution from './components/graph/NodeTypeDistribution.vue'
import GraphStatisticsPanel from './components/graph/GraphStatisticsPanel.vue'
import GraphFilterTips from './components/graph/GraphFilterTips.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const elements = ref<(Node | Edge)[]>([])
const allElements = ref<(Node | Edge)[]>([]) // 存储所有元素
const selectedNode = ref<Node | null>(null)
const selectedEdge = ref<Edge | null>(null)
const showSidebar = ref(true)
const searchQuery = ref('')
const selectedLayout = ref('force')
const relationDepth = ref<string>('2')
const hoveredNodeId = ref<string | null>(null)
const vueFlowRef = ref<any>(null)
const showStatisticsPanel = ref(false)
const selectedNodeTypes = ref<string[]>([])
const selectedEdgeTypes = ref<string[]>([])
const isFiltered = ref(false)

// 图谱数据
const graphData = reactive({
  id: '',
  name: '',
  nodeCount: 0,
  edgeCount: 0,
  description: ''
})

// 节点类型分布
const nodeTypeDistribution = ref([
  { type: '实体', count: 45, color: '#3b82f6' },
  { type: '概念', count: 23, color: '#10b981' },
  { type: '属性', count: 12, color: '#f59e0b' }
])

// 生命周期
onMounted(() => {
  loadGraphData()
})

// 新增方法
const toggleStatisticsPanel = () => {
  showStatisticsPanel.value = !showStatisticsPanel.value
}

const handleNodeTypeSelect = (nodeTypes: string[]) => {
  selectedNodeTypes.value = nodeTypes
  applyTypeFilter()
}

const handleEdgeTypeSelect = (edgeTypes: string[]) => {
  selectedEdgeTypes.value = edgeTypes
  applyTypeFilter()
}

const handleResetFilter = () => {
  selectedNodeTypes.value = []
  selectedEdgeTypes.value = []
  isFiltered.value = false
  // 重新应用过滤条件（主要是关联深度控制）
  applyTypeFilter()
}

const applyTypeFilter = () => {
  if (selectedNodeTypes.value.length === 0 && selectedEdgeTypes.value.length === 0) {
    // 如果没有任何过滤条件，应用关联深度控制
    applyRelationDepthFilter([...allElements.value])
    isFiltered.value = false
    return
  }

  isFiltered.value = true

  // 如果只选择了边类型，优先显示与选中边连接的节点
  if (selectedNodeTypes.value.length === 0 && selectedEdgeTypes.value.length > 0) {
    // 先过滤出符合条件的边
    const filteredEdges = allElements.value.filter(el => {
      if (!isEdgeElement(el)) return false
      const edge = el as Edge
      return selectedEdgeTypes.value.includes(edge.data?.relationship || edge.label || '')
    }) as Edge[]

    // 获取与这些边连接的所有节点ID
    const connectedNodeIds = new Set<string>()
    filteredEdges.forEach(edge => {
      connectedNodeIds.add(edge.source)
      connectedNodeIds.add(edge.target)
    })

    // 过滤出连接的节点
    const connectedNodes = allElements.value.filter(el => {
      if (isEdgeElement(el)) return false
      return connectedNodeIds.has(el.id)
    }) as Node[]

    // 应用关联深度控制
    applyRelationDepthFilter([...connectedNodes, ...filteredEdges])
    return
  }

  // 如果选择了节点类型，先过滤节点
  let filteredNodes = allElements.value.filter(el => {
    if (isEdgeElement(el)) return false
    const node = el as Node
    return selectedNodeTypes.value.length === 0 || selectedNodeTypes.value.includes(node.data?.type || '')
  })

  const filteredNodeIds = new Set(filteredNodes.map(node => node.id))

  // 过滤边：只显示连接已选择节点的边，并且符合边类型条件
  const filteredEdges = allElements.value.filter(el => {
    if (!isEdgeElement(el)) return false
    const edge = el as Edge

    // 只显示连接已选择节点的边
    const sourceSelected = filteredNodeIds.has(edge.source)
    const targetSelected = filteredNodeIds.has(edge.target)

    if (!sourceSelected || !targetSelected) return false

    // 如果选择了边类型，还要检查边类型
    if (selectedEdgeTypes.value.length > 0) {
      return selectedEdgeTypes.value.includes(edge.data?.relationship || edge.label || '')
    }

    return true
  })

  // 如果同时选择了节点类型和边类型，需要进一步过滤节点
  if (selectedNodeTypes.value.length > 0 && selectedEdgeTypes.value.length > 0) {
    // 获取符合边类型条件的边连接的节点
    const edgeConnectedNodeIds = new Set<string>()
    const typedFilteredEdges = filteredEdges as Edge[]
    typedFilteredEdges.forEach(edge => {
      edgeConnectedNodeIds.add(edge.source)
      edgeConnectedNodeIds.add(edge.target)
    })

    // 只保留既符合节点类型又与选中边类型连接的节点
    filteredNodes = filteredNodes.filter(node => edgeConnectedNodeIds.has(node.id))
  }

  // 应用关联深度控制
  applyRelationDepthFilter([...filteredNodes, ...filteredEdges])
}

const applyRelationDepthFilter = (elementsToFilter: (Node | Edge)[]) => {
  if (relationDepth.value === 'all') {
    elements.value = elementsToFilter
    return
  }

  const depth = parseInt(relationDepth.value)
  const finalNodes = new Set<string>()
  const finalEdges = new Set<string>()

  const nodes = elementsToFilter.filter(el => !isEdgeElement(el)) as Node[]
  const edges = elementsToFilter.filter(el => isEdgeElement(el)) as Edge[]

  nodes.forEach(node => {
    const relatedData = getRelatedNodesAndEdges(node.id, depth, nodes, edges)
    relatedData.nodeIds.forEach(id => finalNodes.add(id))
    relatedData.edgeIds.forEach(id => finalEdges.add(id))
  })

  elements.value = [
    ...nodes.filter(node => finalNodes.has(node.id)),
    ...edges.filter(edge => finalEdges.has(edge.id))
  ]
}

const getRelatedNodesAndEdges = (startNodeId: string, depth: number, nodes: any[], edges: any[]) => {
  const relatedNodeIds = new Set<string>()
  const relatedEdgeIds = new Set<string>()

  relatedNodeIds.add(startNodeId)

  const findRelated = (nodeId: string, currentDepth: number) => {
    if (currentDepth >= depth) return

    edges.forEach(edge => {
      if (edge.source === nodeId || edge.target === nodeId) {
        relatedEdgeIds.add(edge.id)
        const connectedNodeId = edge.source === nodeId ? edge.target : edge.source

        if (!relatedNodeIds.has(connectedNodeId)) {
          relatedNodeIds.add(connectedNodeId)
          findRelated(connectedNodeId, currentDepth + 1)
        }
      }
    })
  }

  findRelated(startNodeId, 0)

  return { nodeIds: relatedNodeIds, edgeIds: relatedEdgeIds }
}

// 事件处理
const loadGraphData = async () => {
  const graphId = route.params.id as string

  // 根据图谱ID加载不同的数据
  const graphConfigs = {
    'kg-1': {
      name: '案件关系知识图谱',
      nodeCount: 856,
      edgeCount: 2134,
      description: '涵盖案件当事人、证据、时间线、法律条文等复杂关系网络'
    },
    'kg-2': {
      name: '人员关系知识图谱',
      nodeCount: 1245,
      edgeCount: 3567,
      description: '构建人员身份、社会关系、行为轨迹、通信记录等多维度关系网络'
    },
    'kg-3': {
      name: '车辆图知识图谱',
      nodeCount: 678,
      edgeCount: 1456,
      description: '整合车辆信息、行驶轨迹、违章记录、保险理赔等数据'
    },
    'kg-4': {
      name: '金融风控知识图谱',
      nodeCount: 423,
      edgeCount: 1234,
      description: '基于交易行为、资金流向、风险事件等构建的金融风险识别网络'
    },
    'kg-5': {
      name: '企业关联知识图谱',
      nodeCount: 567,
      edgeCount: 1678,
      description: '企业股权结构、高管关系、业务往来、投资关系等企业间复杂关联关系'
    },
    'kg-6': {
      name: '网络安全威胁图谱',
      nodeCount: 789,
      edgeCount: 2345,
      description: '网络攻击链、恶意软件家族、攻击者画像、漏洞利用等网络安全威胁情报'
    }
  }

  const config = graphConfigs[graphId as keyof typeof graphConfigs] || graphConfigs['kg-1']

  // 模拟加载图谱数据
  graphData.id = graphId
  graphData.name = config.name
  graphData.nodeCount = config.nodeCount
  graphData.edgeCount = config.edgeCount
  graphData.description = config.description

  // 生成示例节点和边
  generateSampleGraph(graphId)
}

const generateSampleGraph = (graphId: string) => {
  const nodes: Node[] = []
  const edges: Edge[] = []

  // 根据不同图谱类型生成不同的节点和边
  switch (graphId) {
    case 'kg-1': // 案件关系知识图谱
      generateCaseGraph(nodes, edges)
      break
    case 'kg-2': // 人员关系知识图谱
      generatePersonGraph(nodes, edges)
      break
    case 'kg-3': // 车辆图知识图谱
      generateVehicleGraph(nodes, edges)
      break
    case 'kg-4': // 金融风控知识图谱
      generateFinanceGraph(nodes, edges)
      break
    case 'kg-5': // 企业关联知识图谱
      generateCompanyGraph(nodes, edges)
      break
    case 'kg-6': // 网络安全威胁图谱
      generateSecurityGraph(nodes, edges)
      break
    default:
      generateCaseGraph(nodes, edges)
  }

  allElements.value = [...nodes, ...edges]
  elements.value = [...nodes, ...edges]

  // 延迟执行居中显示，确保DOM已更新
  setTimeout(() => {
    fitView()
  }, 100)
}

// 生成案件关系图谱
const generateCaseGraph = (nodes: Node[], edges: Edge[]) => {
  const caseEntities = [
    { id: 'case-1', label: '刑事案件-2024001', type: 'case', x: 400, y: 100 },
    { id: 'suspect-1', label: '嫌疑人-张某', type: 'person', x: 200, y: 200 },
    { id: 'suspect-2', label: '嫌疑人-李某', type: 'person', x: 600, y: 200 },
    { id: 'victim-1', label: '受害人-王某', type: 'person', x: 100, y: 300 },
    { id: 'witness-1', label: '证人-赵某', type: 'person', x: 300, y: 350 },
    { id: 'witness-2', label: '证人-钱某', type: 'person', x: 500, y: 350 },
    { id: 'evidence-1', label: '物证-作案工具', type: 'evidence', x: 150, y: 450 },
    { id: 'evidence-2', label: '书证-银行记录', type: 'evidence', x: 350, y: 450 },
    { id: 'evidence-3', label: '视频证据', type: 'evidence', x: 550, y: 450 },
    { id: 'location-1', label: '案发地点-某商场', type: 'location', x: 700, y: 300 },
    { id: 'time-1', label: '案发时间-2024/01/15', type: 'time', x: 800, y: 200 },
    { id: 'law-1', label: '刑法第264条', type: 'law', x: 750, y: 100 }
  ]

  // 添加更多节点以达到50+的要求
  for (let i = 0; i < 40; i++) {
    caseEntities.push({
      id: `related-${i}`,
      label: `相关信息-${i + 1}`,
      type: 'info',
      x: Math.random() * 800 + 100,
      y: Math.random() * 400 + 500
    })
  }

  caseEntities.forEach(entity => {
    nodes.push({
      id: entity.id,
      type: 'entity',
      position: { x: entity.x, y: entity.y },
      data: {
        label: entity.label,
        type: entity.type,
        properties: { category: entity.type, status: 'active' }
      }
    })
  })

  // 生成关系边
  const relationships = [
    { source: 'case-1', target: 'suspect-1', label: '涉嫌' },
    { source: 'case-1', target: 'suspect-2', label: '涉嫌' },
    { source: 'case-1', target: 'victim-1', label: '受害' },
    { source: 'suspect-1', target: 'suspect-2', label: '共犯' },
    { source: 'suspect-1', target: 'evidence-1', label: '使用' },
    { source: 'suspect-2', target: 'evidence-2', label: '留下' },
    { source: 'witness-1', target: 'suspect-1', label: '目击' },
    { source: 'witness-2', target: 'location-1', label: '在场' },
    { source: 'case-1', target: 'location-1', label: '发生于' },
    { source: 'case-1', target: 'time-1', label: '发生于' },
    { source: 'case-1', target: 'law-1', label: '适用' }
  ]

  // 添加更多关系以达到50+的要求
  for (let i = 0; i < 45; i++) {
    const sourceIndex = Math.floor(Math.random() * caseEntities.length)
    const targetIndex = Math.floor(Math.random() * caseEntities.length)
    if (sourceIndex !== targetIndex) {
      relationships.push({
        source: caseEntities[sourceIndex].id,
        target: caseEntities[targetIndex].id,
        label: '关联'
      })
    }
  }

  relationships.forEach((rel, index) => {
    edges.push({
      id: `edge-${index}`,
      source: rel.source,
      target: rel.target,
      type: 'smoothstep',
      label: rel.label,
      data: { relationship: rel.label.toLowerCase(), weight: Math.random() * 0.5 + 0.5 }
    })
  })
}

// 生成人员关系图谱
const generatePersonGraph = (nodes: Node[], edges: Edge[]) => {
  const personEntities = [
    { id: 'person-1', label: '张三', type: 'person', x: 400, y: 100 },
    { id: 'person-2', label: '李四', type: 'person', x: 200, y: 200 },
    { id: 'person-3', label: '王五', type: 'person', x: 600, y: 200 },
    { id: 'company-1', label: '某科技公司', type: 'company', x: 300, y: 300 },
    { id: 'phone-1', label: '138****1234', type: 'phone', x: 100, y: 350 },
    { id: 'address-1', label: '北京市朝阳区', type: 'address', x: 500, y: 350 },
    { id: 'vehicle-1', label: '京A12345', type: 'vehicle', x: 700, y: 250 },
    { id: 'bank-1', label: '工商银行账户', type: 'account', x: 150, y: 450 }
  ]

  // 添加更多节点
  for (let i = 0; i < 45; i++) {
    personEntities.push({
      id: `contact-${i}`,
      label: `联系人-${i + 1}`,
      type: 'contact',
      x: Math.random() * 800 + 100,
      y: Math.random() * 400 + 500
    })
  }

  personEntities.forEach(entity => {
    nodes.push({
      id: entity.id,
      type: 'entity',
      position: { x: entity.x, y: entity.y },
      data: {
        label: entity.label,
        type: entity.type,
        properties: { category: entity.type, status: 'active' }
      }
    })
  })

  // 生成关系
  const relationships = [
    { source: 'person-1', target: 'person-2', label: '朋友' },
    { source: 'person-1', target: 'company-1', label: '就职' },
    { source: 'person-1', target: 'phone-1', label: '拥有' },
    { source: 'person-2', target: 'person-3', label: '同事' },
    { source: 'person-1', target: 'address-1', label: '居住' },
    { source: 'person-1', target: 'vehicle-1', label: '拥有' },
    { source: 'person-1', target: 'bank-1', label: '开户' }
  ]

  // 添加更多关系
  for (let i = 0; i < 50; i++) {
    const sourceIndex = Math.floor(Math.random() * personEntities.length)
    const targetIndex = Math.floor(Math.random() * personEntities.length)
    if (sourceIndex !== targetIndex) {
      relationships.push({
        source: personEntities[sourceIndex].id,
        target: personEntities[targetIndex].id,
        label: '关联'
      })
    }
  }

  relationships.forEach((rel, index) => {
    edges.push({
      id: `edge-${index}`,
      source: rel.source,
      target: rel.target,
      type: 'smoothstep',
      label: rel.label,
      data: { relationship: rel.label.toLowerCase(), weight: Math.random() * 0.5 + 0.5 }
    })
  })
}

// 生成车辆图知识图谱
const generateVehicleGraph = (nodes: Node[], edges: Edge[]) => {
  const vehicleEntities = [
    { id: 'vehicle-1', label: '京A12345', type: 'vehicle', x: 400, y: 100 },
    { id: 'owner-1', label: '车主-张某', type: 'person', x: 200, y: 200 },
    { id: 'insurance-1', label: '平安保险', type: 'insurance', x: 600, y: 200 },
    { id: 'violation-1', label: '违章记录-超速', type: 'violation', x: 300, y: 300 },
    { id: 'maintenance-1', label: '保养记录', type: 'maintenance', x: 500, y: 300 },
    { id: 'route-1', label: '行驶路线-A', type: 'route', x: 100, y: 400 }
  ]

  // 添加更多节点
  for (let i = 0; i < 47; i++) {
    vehicleEntities.push({
      id: `traffic-${i}`,
      label: `交通记录-${i + 1}`,
      type: 'traffic',
      x: Math.random() * 800 + 100,
      y: Math.random() * 400 + 450
    })
  }

  vehicleEntities.forEach(entity => {
    nodes.push({
      id: entity.id,
      type: 'entity',
      position: { x: entity.x, y: entity.y },
      data: {
        label: entity.label,
        type: entity.type,
        properties: { category: entity.type, status: 'active' }
      }
    })
  })

  // 生成关系
  const relationships = [
    { source: 'vehicle-1', target: 'owner-1', label: '所有' },
    { source: 'vehicle-1', target: 'insurance-1', label: '投保' },
    { source: 'vehicle-1', target: 'violation-1', label: '违章' },
    { source: 'vehicle-1', target: 'maintenance-1', label: '保养' },
    { source: 'vehicle-1', target: 'route-1', label: '行驶' }
  ]

  // 添加更多关系
  for (let i = 0; i < 50; i++) {
    const sourceIndex = Math.floor(Math.random() * vehicleEntities.length)
    const targetIndex = Math.floor(Math.random() * vehicleEntities.length)
    if (sourceIndex !== targetIndex) {
      relationships.push({
        source: vehicleEntities[sourceIndex].id,
        target: vehicleEntities[targetIndex].id,
        label: '关联'
      })
    }
  }

  relationships.forEach((rel, index) => {
    edges.push({
      id: `edge-${index}`,
      source: rel.source,
      target: rel.target,
      type: 'smoothstep',
      label: rel.label,
      data: { relationship: rel.label.toLowerCase(), weight: Math.random() * 0.5 + 0.5 }
    })
  })
}

// 生成金融风控知识图谱
const generateFinanceGraph = (nodes: Node[], edges: Edge[]) => {
  const financeEntities = [
    { id: 'account-1', label: '银行账户-001', type: 'account', x: 400, y: 100 },
    { id: 'person-1', label: '账户持有人', type: 'person', x: 200, y: 200 },
    { id: 'transaction-1', label: '大额交易', type: 'transaction', x: 600, y: 200 },
    { id: 'risk-1', label: '风险事件', type: 'risk', x: 300, y: 300 },
    { id: 'merchant-1', label: '商户-A', type: 'merchant', x: 500, y: 300 }
  ]

  // 添加更多节点
  for (let i = 0; i < 48; i++) {
    financeEntities.push({
      id: `finance-${i}`,
      label: `金融记录-${i + 1}`,
      type: 'record',
      x: Math.random() * 800 + 100,
      y: Math.random() * 400 + 400
    })
  }

  financeEntities.forEach(entity => {
    nodes.push({
      id: entity.id,
      type: 'entity',
      position: { x: entity.x, y: entity.y },
      data: {
        label: entity.label,
        type: entity.type,
        properties: { category: entity.type, status: 'active' }
      }
    })
  })

  // 生成关系
  const relationships = [
    { source: 'account-1', target: 'person-1', label: '持有' },
    { source: 'account-1', target: 'transaction-1', label: '发生' },
    { source: 'transaction-1', target: 'risk-1', label: '触发' },
    { source: 'transaction-1', target: 'merchant-1', label: '支付' }
  ]

  // 添加更多关系
  for (let i = 0; i < 50; i++) {
    const sourceIndex = Math.floor(Math.random() * financeEntities.length)
    const targetIndex = Math.floor(Math.random() * financeEntities.length)
    if (sourceIndex !== targetIndex) {
      relationships.push({
        source: financeEntities[sourceIndex].id,
        target: financeEntities[targetIndex].id,
        label: '关联'
      })
    }
  }

  relationships.forEach((rel, index) => {
    edges.push({
      id: `edge-${index}`,
      source: rel.source,
      target: rel.target,
      type: 'smoothstep',
      label: rel.label,
      data: { relationship: rel.label.toLowerCase(), weight: Math.random() * 0.5 + 0.5 }
    })
  })
}

// 生成企业关联知识图谱
const generateCompanyGraph = (nodes: Node[], edges: Edge[]) => {
  const companyEntities = [
    { id: 'company-1', label: '母公司-A', type: 'company', x: 400, y: 100 },
    { id: 'company-2', label: '子公司-B', type: 'company', x: 200, y: 200 },
    { id: 'company-3', label: '关联公司-C', type: 'company', x: 600, y: 200 },
    { id: 'executive-1', label: '董事长-张某', type: 'executive', x: 300, y: 300 },
    { id: 'investment-1', label: '投资项目-X', type: 'investment', x: 500, y: 300 }
  ]

  // 添加更多节点
  for (let i = 0; i < 48; i++) {
    companyEntities.push({
      id: `business-${i}`,
      label: `业务关系-${i + 1}`,
      type: 'business',
      x: Math.random() * 800 + 100,
      y: Math.random() * 400 + 400
    })
  }

  companyEntities.forEach(entity => {
    nodes.push({
      id: entity.id,
      type: 'entity',
      position: { x: entity.x, y: entity.y },
      data: {
        label: entity.label,
        type: entity.type,
        properties: { category: entity.type, status: 'active' }
      }
    })
  })

  // 生成关系
  const relationships = [
    { source: 'company-1', target: 'company-2', label: '控股' },
    { source: 'company-1', target: 'company-3', label: '关联' },
    { source: 'executive-1', target: 'company-1', label: '管理' },
    { source: 'company-1', target: 'investment-1', label: '投资' }
  ]

  // 添加更多关系
  for (let i = 0; i < 50; i++) {
    const sourceIndex = Math.floor(Math.random() * companyEntities.length)
    const targetIndex = Math.floor(Math.random() * companyEntities.length)
    if (sourceIndex !== targetIndex) {
      relationships.push({
        source: companyEntities[sourceIndex].id,
        target: companyEntities[targetIndex].id,
        label: '关联'
      })
    }
  }

  relationships.forEach((rel, index) => {
    edges.push({
      id: `edge-${index}`,
      source: rel.source,
      target: rel.target,
      type: 'smoothstep',
      label: rel.label,
      data: { relationship: rel.label.toLowerCase(), weight: Math.random() * 0.5 + 0.5 }
    })
  })
}

// 生成网络安全威胁图谱
const generateSecurityGraph = (nodes: Node[], edges: Edge[]) => {
  const securityEntities = [
    { id: 'malware-1', label: '恶意软件-Trojan.A', type: 'malware', x: 400, y: 100 },
    { id: 'attacker-1', label: '攻击者-APT组织', type: 'attacker', x: 200, y: 200 },
    { id: 'vulnerability-1', label: '漏洞-CVE-2024-001', type: 'vulnerability', x: 600, y: 200 },
    { id: 'target-1', label: '攻击目标-服务器', type: 'target', x: 300, y: 300 },
    { id: 'technique-1', label: '攻击技术-钓鱼', type: 'technique', x: 500, y: 300 }
  ]

  // 添加更多节点
  for (let i = 0; i < 48; i++) {
    securityEntities.push({
      id: `threat-${i}`,
      label: `威胁情报-${i + 1}`,
      type: 'threat',
      x: Math.random() * 800 + 100,
      y: Math.random() * 400 + 400
    })
  }

  securityEntities.forEach(entity => {
    nodes.push({
      id: entity.id,
      type: 'entity',
      position: { x: entity.x, y: entity.y },
      data: {
        label: entity.label,
        type: entity.type,
        properties: { category: entity.type, status: 'active' }
      }
    })
  })

  // 生成关系
  const relationships = [
    { source: 'attacker-1', target: 'malware-1', label: '使用' },
    { source: 'malware-1', target: 'vulnerability-1', label: '利用' },
    { source: 'vulnerability-1', target: 'target-1', label: '攻击' },
    { source: 'attacker-1', target: 'technique-1', label: '采用' },
    { source: 'technique-1', target: 'target-1', label: '针对' }
  ]

  // 添加更多关系
  for (let i = 0; i < 50; i++) {
    const sourceIndex = Math.floor(Math.random() * securityEntities.length)
    const targetIndex = Math.floor(Math.random() * securityEntities.length)
    if (sourceIndex !== targetIndex) {
      relationships.push({
        source: securityEntities[sourceIndex].id,
        target: securityEntities[targetIndex].id,
        label: '关联'
      })
    }
  }

  relationships.forEach((rel, index) => {
    edges.push({
      id: `edge-${index}`,
      source: rel.source,
      target: rel.target,
      type: 'smoothstep',
      label: rel.label,
      data: { relationship: rel.label.toLowerCase(), weight: Math.random() * 0.5 + 0.5 }
    })
  })
}

const goBack = () => {
  router.back()
}

const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

const fitView = () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.fitView({ padding: 0.2, duration: 800 })
  }
}

const resetView = () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.setViewport({ x: 0, y: 0, zoom: 0.8 }, { duration: 800 })
  }
}

const applyLayout = () => {
  console.log('Apply layout:', selectedLayout.value)

  if (!allElements.value.length) return

  const nodes = allElements.value.filter(el => el.type !== undefined) as Node[]
  const edges = allElements.value.filter(el => el.type === undefined) as Edge[]

  let updatedNodes: Node[] = []

  switch (selectedLayout.value) {
    case 'force':
      updatedNodes = applyForceLayout(nodes, edges)
      break
    case 'circular':
      updatedNodes = applyCircularLayout(nodes)
      break
    case 'hierarchical':
      updatedNodes = applyHierarchicalLayout(nodes, edges)
      break
    case 'grid':
      updatedNodes = applyGridLayout(nodes)
      break
    default:
      updatedNodes = nodes
  }

  // 更新节点位置
  allElements.value = [...updatedNodes, ...edges]

  // 重新应用过滤条件，保持当前的过滤状态
  applyTypeFilter()

  // 适应视图
  setTimeout(() => {
    if (vueFlowRef.value) {
      vueFlowRef.value.fitView({ padding: 0.2, duration: 800 })
    }
  }, 100)
}

const updateRelationDepth = () => {
  if (hoveredNodeId.value) {
    // 如果有悬停节点，重新应用过滤
    applyNodeHoverFilter(hoveredNodeId.value)
  } else {
    // 如果没有悬停节点，重新应用类型过滤（包含关联深度控制）
    applyTypeFilter()
  }
}

// GraphFilterTips 组件事件处理
const handleRemoveNodeType = (nodeType: string) => {
  const index = selectedNodeTypes.value.indexOf(nodeType)
  if (index > -1) {
    selectedNodeTypes.value.splice(index, 1)
    applyTypeFilter()
  }
}

const handleRemoveEdgeType = (edgeType: string) => {
  const index = selectedEdgeTypes.value.indexOf(edgeType)
  if (index > -1) {
    selectedEdgeTypes.value.splice(index, 1)
    applyTypeFilter()
  }
}

const handleClearAllFilters = () => {
  selectedNodeTypes.value = []
  selectedEdgeTypes.value = []
  relationDepth.value = 'all'
  selectedLayout.value = 'force'
  isFiltered.value = false
  applyTypeFilter()
}

const onNodesChange = (changes: any[]) => {
  console.log('Nodes changed:', changes)
}

const onEdgesChange = (changes: any[]) => {
  console.log('Edges changed:', changes)
}

const onNodeClick = (event: any) => {
  selectedNode.value = event.node
  selectedEdge.value = null
  // 自动显示右侧信息面板
  showSidebar.value = true
}

const onEdgeClick = (event: any) => {
  selectedEdge.value = event.edge
  selectedNode.value = null
  // 自动显示右侧信息面板
  showSidebar.value = true
}

// 应用节点悬停过滤效果
const applyNodeHoverFilter = (nodeId: string) => {
  const depth = relationDepth.value === 'all' ? Infinity : parseInt(relationDepth.value)
  const relatedNodeIds = new Set<string>()
  const relatedEdgeIds = new Set<string>()

  // 添加目标节点
  relatedNodeIds.add(nodeId)

  // 递归查找相关节点
  const findRelatedNodes = (currentNodeId: string, currentDepth: number) => {
    if (currentDepth >= depth) return

    // 查找所有与当前节点相连的边
    const allEdges = allElements.value.filter(el => isEdgeElement(el)) as Edge[]
    const connectedEdges = allEdges.filter(edge =>
      edge.source === currentNodeId || edge.target === currentNodeId
    )

    connectedEdges.forEach(edge => {
      relatedEdgeIds.add(edge.id)
      const connectedNodeId = edge.source === currentNodeId ? edge.target : edge.source

      if (!relatedNodeIds.has(connectedNodeId)) {
        relatedNodeIds.add(connectedNodeId)
        findRelatedNodes(connectedNodeId, currentDepth + 1)
      }
    })
  }

  findRelatedNodes(nodeId, 0)

  // 只过滤显示相关的节点和边，不修改原始对象
  const filteredElements = allElements.value.filter(element => {
    if (isEdgeElement(element)) {
      return relatedEdgeIds.has(element.id)
    } else {
      return relatedNodeIds.has(element.id)
    }
  })

  elements.value = filteredElements
}

// 辅助函数：判断是否为边元素
const isEdgeElement = (element: any): boolean => {
  return element.source !== undefined && element.target !== undefined
}

// 辅助函数：判断是否为节点元素
const isNodeElement = (element: any): boolean => {
  return element.position !== undefined && element.data !== undefined
}

// 布局算法实现
const applyForceLayout = (nodes: Node[], edges: Edge[]): Node[] => {
  // 力导向布局算法
  const centerX = 400
  const centerY = 300
  const iterations = 100
  const repulsionStrength = 1000
  const attractionStrength = 0.1
  const damping = 0.9

  // 初始化节点位置（如果没有位置）
  const layoutNodes = nodes.map(node => ({
    ...node,
    position: node.position || {
      x: centerX + (Math.random() - 0.5) * 200,
      y: centerY + (Math.random() - 0.5) * 200
    },
    velocity: { x: 0, y: 0 }
  }))

  // 构建邻接关系
  const adjacency = new Map<string, string[]>()
  edges.forEach(edge => {
    if (!adjacency.has(edge.source)) adjacency.set(edge.source, [])
    if (!adjacency.has(edge.target)) adjacency.set(edge.target, [])
    adjacency.get(edge.source)?.push(edge.target)
    adjacency.get(edge.target)?.push(edge.source)
  })

  // 迭代计算
  for (let iter = 0; iter < iterations; iter++) {
    layoutNodes.forEach(node => {
      let fx = 0, fy = 0

      // 排斥力
      layoutNodes.forEach(other => {
        if (node.id !== other.id) {
          const dx = node.position.x - other.position.x
          const dy = node.position.y - other.position.y
          const distance = Math.sqrt(dx * dx + dy * dy) || 1
          const force = repulsionStrength / (distance * distance)
          fx += (dx / distance) * force
          fy += (dy / distance) * force
        }
      })

      // 吸引力（连接的节点）
      const neighbors = adjacency.get(node.id) || []
      neighbors.forEach(neighborId => {
        const neighbor = layoutNodes.find(n => n.id === neighborId)
        if (neighbor) {
          const dx = neighbor.position.x - node.position.x
          const dy = neighbor.position.y - node.position.y
          fx += dx * attractionStrength
          fy += dy * attractionStrength
        }
      })

      // 更新速度和位置
      node.velocity.x = (node.velocity.x + fx) * damping
      node.velocity.y = (node.velocity.y + fy) * damping
      node.position.x += node.velocity.x
      node.position.y += node.velocity.y
    })
  }

  return layoutNodes.map(({ velocity, ...node }) => node)
}

const applyCircularLayout = (nodes: Node[]): Node[] => {
  // 环形布局算法
  const centerX = 400
  const centerY = 300
  const radius = Math.min(200, Math.max(100, nodes.length * 8))

  return nodes.map((node, index) => {
    const angle = (2 * Math.PI * index) / nodes.length
    return {
      ...node,
      position: {
        x: centerX + radius * Math.cos(angle),
        y: centerY + radius * Math.sin(angle)
      }
    }
  })
}

const applyHierarchicalLayout = (nodes: Node[], edges: Edge[]): Node[] => {
  // 层次布局算法
  const levels = new Map<string, number>()
  const visited = new Set<string>()
  const adjacency = new Map<string, string[]>()

  // 构建邻接表
  edges.forEach(edge => {
    if (!adjacency.has(edge.source)) adjacency.set(edge.source, [])
    adjacency.get(edge.source)?.push(edge.target)
  })

  // 找到根节点（入度为0的节点）
  const inDegree = new Map<string, number>()
  nodes.forEach(node => inDegree.set(node.id, 0))
  edges.forEach(edge => {
    inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1)
  })

  const roots = nodes.filter(node => (inDegree.get(node.id) || 0) === 0)

  // BFS分层
  const queue = roots.map(root => ({ id: root.id, level: 0 }))
  roots.forEach(root => {
    levels.set(root.id, 0)
    visited.add(root.id)
  })

  while (queue.length > 0) {
    const { id, level } = queue.shift()!
    const neighbors = adjacency.get(id) || []

    neighbors.forEach(neighborId => {
      if (!visited.has(neighborId)) {
        levels.set(neighborId, level + 1)
        visited.add(neighborId)
        queue.push({ id: neighborId, level: level + 1 })
      }
    })
  }

  // 未访问的节点放在最后一层
  nodes.forEach(node => {
    if (!visited.has(node.id)) {
      levels.set(node.id, Math.max(...Array.from(levels.values())) + 1)
    }
  })

  // 按层排列
  const levelGroups = new Map<number, string[]>()
  levels.forEach((level, nodeId) => {
    if (!levelGroups.has(level)) levelGroups.set(level, [])
    levelGroups.get(level)?.push(nodeId)
  })

  const startY = 100
  const levelHeight = 150
  const startX = 100

  return nodes.map(node => {
    const level = levels.get(node.id) || 0
    const levelNodes = levelGroups.get(level) || []
    const indexInLevel = levelNodes.indexOf(node.id)
    const levelWidth = Math.max(600, levelNodes.length * 120)

    return {
      ...node,
      position: {
        x: startX + (indexInLevel + 0.5) * (levelWidth / levelNodes.length),
        y: startY + level * levelHeight
      }
    }
  })
}

const applyGridLayout = (nodes: Node[]): Node[] => {
  // 网格布局算法
  const cols = Math.ceil(Math.sqrt(nodes.length))
  const cellWidth = 150
  const cellHeight = 120
  const startX = 100
  const startY = 100

  return nodes.map((node, index) => {
    const row = Math.floor(index / cols)
    const col = index % cols

    return {
      ...node,
      position: {
        x: startX + col * cellWidth,
        y: startY + row * cellHeight
      }
    }
  })
}
</script>

<style>
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
@import '@vue-flow/controls/dist/style.css';
@import '@vue-flow/minimap/dist/style.css';

.knowledge-graph-flow {
  background-color: #f8fafc;
}

.vue-flow__node {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.vue-flow__edge-path {
  stroke: #6b7280;
  stroke-width: 2;
}

.vue-flow__edge-text {
  font-size: 12px;
  fill: #374151;
}

/* 基础过渡动画 */
.vue-flow__node,
.vue-flow__edge {
  transition: opacity 0.2s ease;
}

.filter-tips-wrapper {
  position: absolute;
  top: -1px;
  right: -1px;
  z-index: 9999;
}
</style>
