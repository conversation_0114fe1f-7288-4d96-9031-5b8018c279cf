package com.xhcai.common.datasource.plugin;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.datasource.utils.TenantUtils;

import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.schema.Column;

/**
 * 多租户处理器 自动为SQL添加租户ID过滤条件
 *
 * <p>
 * 支持@NoTenant注解，通过TenantUtils统一管理租户隔离规则</p>
 * <p>
 * 租户隔离规则：</p>
 * <ul>
 * <li>通过TenantUtils.getIgnoreTables()获取忽略表列表</li>
 * <li>通过@NoTenant注解标识的实体对应的表会被忽略</li>
 * <li>多租户功能强制启用，不能禁用</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class MultiTenantLineHandler implements TenantLineHandler {

    private static final Logger log = LoggerFactory.getLogger(MultiTenantLineHandler.class);

    // 注意：不需要租户隔离的表名列表已移至 TenantUtils.getIgnoreTables()
    // 多租户功能强制启用，不能禁用
    @Override
    public Expression getTenantId() {
        // 获取当前租户ID
        String tenantId = getCurrentTenantId();
        log.debug("当前租户ID: {}", tenantId);

        // 如果租户ID为空，返回一个特殊值
        // ignoreTable方法会处理这种情况
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.debug("租户ID为空，使用特殊标识");
            return new StringValue("__NO_TENANT__");
        }

        // 返回 StringValue 以确保与数据库中的 VARCHAR 类型匹配
        return new StringValue(tenantId);
    }

    @Override
    public String getTenantIdColumn() {
        return "tenant_id";
    }

    @Override
    public boolean ignoreTable(String tableName) {
        // 首先检查是否有有效的租户ID
        String tenantId = getCurrentTenantId();
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.debug("租户ID为空，跳过表 {} 的多租户过滤", tableName);
            return true; // 忽略多租户过滤
        }

        // 使用TenantUtils统一检查是否需要租户隔离
        boolean needsIsolation = TenantUtils.needsTenantIsolation(tableName);

        if (!needsIsolation) {
            log.debug("表 {} 不需要租户隔离", tableName);
        }

        return !needsIsolation;
    }

    @Override
    public boolean ignoreInsert(List<Column> columns, String tenantIdColumn) {
        // 检查是否已经存在租户ID字段（通过MyMetaObjectHandler自动填充或手动指定）
        boolean hasTenantIdColumn = columns.stream()
                .anyMatch(column -> column.getColumnName().equals(tenantIdColumn));

        if (hasTenantIdColumn) {
            log.debug("SQL中已包含租户ID字段，跳过多租户插件自动添加");
            return true;
        }

        // 获取当前租户ID
        String tenantId = getCurrentTenantId();

        // 如果没有租户ID，跳过多租户处理
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.debug("租户ID为空，跳过多租户插件自动添加");
            return true;
        }

        // 所有用户（包括平台管理员）在有有效租户ID时都需要添加租户字段
        // 这确保了数据的正确归属和多租户隔离
        log.debug("需要自动添加租户ID字段: {}, 当前用户是否为平台管理员: {}", tenantId, isPlatformAdmin());
        return false;
    }

    /**
     * 获取当前租户ID
     */
    private String getCurrentTenantId() {
        try {
            // 优先检查租户上下文（用于平台管理员指定租户操作）
            String contextTenantId = getTenantFromContext();
            if (contextTenantId != null) {
                log.debug("从租户上下文获取租户ID: {}", contextTenantId);
                return contextTenantId;
            }

            // 通过反射调用SecurityUtils.getCurrentTenantId()
            Class<?> securityUtilsClass = Class.forName("com.xhcai.common.security.utils.SecurityUtils");
            String tenantId = (String) securityUtilsClass.getMethod("getCurrentTenantId").invoke(null);

            // 如果获取不到租户ID，返回null（表示不进行租户过滤）
            if (tenantId == null) {
                log.debug("未获取到租户ID，不进行租户过滤");
            }

            return tenantId;
        } catch (Exception e) {
            log.debug("获取当前租户ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查当前用户是否是平台管理员
     */
    private boolean isPlatformAdmin() {
        try {
            Class<?> securityUtilsClass = Class.forName("com.xhcai.common.security.utils.SecurityUtils");
            return (Boolean) securityUtilsClass.getMethod("isPlatformAdmin").invoke(null);
        } catch (Exception e) {
            log.debug("检查平台管理员状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从上下文中获取租户ID 用于平台管理员指定要操作的租户
     */
    private String getTenantFromContext() {
        try {
            // 从ThreadLocal或其他上下文中获取租户ID
            return TenantContextHolder.getTenantId();
        } catch (Exception e) {
            log.debug("从上下文获取租户ID失败: {}", e.getMessage());
            return null;
        }
    }
}
