package com.xhcai.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 回滚请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "回滚工作流版本请求")
public class RollbackRequestDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001", required = true)
    @NotBlank(message = "智能体ID不能为空")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 目标版本号
     */
    @Schema(description = "目标版本号", example = "1", required = true)
    @NotNull(message = "版本号不能为空")
    @Min(value = 1, message = "版本号必须大于0")
    private Integer version;

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}
