/**
 * ES6+ 兼容性处理工具
 * 提供现代JavaScript特性的兼容性封装
 */

/**
 * 安全的可选链操作符实现
 */
export function safeGet<T>(obj: any, path: string, defaultValue?: T): T | undefined {
  try {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj) ?? defaultValue
  } catch {
    return defaultValue
  }
}

/**
 * 安全的空值合并操作符实现
 */
export function nullishCoalescing<T>(value: T | null | undefined, fallback: T): T {
  return value !== null && value !== undefined ? value : fallback
}

/**
 * 模拟async/await的Promise链式调用
 */
export function asyncWrapper<T>(promiseFunction: () => Promise<T>): Promise<T> {
  return promiseFunction().catch(error => {
    console.error('Async operation failed:', error)
    throw error
  })
}

/**
 * 安全的解构赋值
 */
export function safeDestructure<T extends Record<string, any>>(
  obj: T | null | undefined,
  keys: (keyof T)[],
  defaults: Partial<T> = {}
): Partial<T> {
  if (!obj) return defaults

  const result: Partial<T> = {}
  keys.forEach(key => {
    result[key] = obj[key] !== undefined ? obj[key] : defaults[key]
  })
  return result
}

/**
 * 模板字符串的兼容性实现
 */
export function templateString(template: string, values: Record<string, any>): string {
  return template.replace(/\$\{([^}]+)\}/g, (match, key) => {
    return values[key] !== undefined ? String(values[key]) : match
  })
}

/**
 * 箭头函数的兼容性封装
 */
export function createArrowFunction<T extends any[], R>(
  fn: (...args: T) => R,
  thisArg?: any
): (...args: T) => R {
  return function(this: any, ...args: T): R {
    return fn.apply(thisArg || this, args)
  }
}

/**
 * Set的兼容性实现
 */
export class CompatSet<T> {
  private items: T[] = []

  constructor(iterable?: Iterable<T>) {
    if (iterable) {
      for (const item of iterable) {
        this.add(item)
      }
    }
  }

  add(value: T): this {
    if (!this.has(value)) {
      this.items.push(value)
    }
    return this
  }

  has(value: T): boolean {
    return this.items.indexOf(value) !== -1
  }

  delete(value: T): boolean {
    const index = this.items.indexOf(value)
    if (index !== -1) {
      this.items.splice(index, 1)
      return true
    }
    return false
  }

  clear(): void {
    this.items = []
  }

  get size(): number {
    return this.items.length
  }

  forEach(callback: (value: T, value2: T, set: CompatSet<T>) => void): void {
    this.items.forEach(item => callback(item, item, this))
  }

  values(): T[] {
    return [...this.items]
  }

  keys(): T[] {
    return this.values()
  }

  entries(): [T, T][] {
    return this.items.map(item => [item, item] as [T, T])
  }
}

/**
 * Map的兼容性实现
 */
export class CompatMap<K, V> {
  private keys: K[] = []
  private values: V[] = []

  constructor(iterable?: Iterable<[K, V]>) {
    if (iterable) {
      for (const [key, value] of iterable) {
        this.set(key, value)
      }
    }
  }

  set(key: K, value: V): this {
    const index = this.keys.indexOf(key)
    if (index !== -1) {
      this.values[index] = value
    } else {
      this.keys.push(key)
      this.values.push(value)
    }
    return this
  }

  get(key: K): V | undefined {
    const index = this.keys.indexOf(key)
    return index !== -1 ? this.values[index] : undefined
  }

  has(key: K): boolean {
    return this.keys.indexOf(key) !== -1
  }

  delete(key: K): boolean {
    const index = this.keys.indexOf(key)
    if (index !== -1) {
      this.keys.splice(index, 1)
      this.values.splice(index, 1)
      return true
    }
    return false
  }

  clear(): void {
    this.keys = []
    this.values = []
  }

  get size(): number {
    return this.keys.length
  }

  forEach(callback: (value: V, key: K, map: CompatMap<K, V>) => void): void {
    this.keys.forEach((key, index) => {
      callback(this.values[index], key, this)
    })
  }

  getKeys(): K[] {
    return [...this.keys]
  }

  getValues(): V[] {
    return [...this.values]
  }

  entries(): [K, V][] {
    return this.keys.map((key, index) => [key, this.values[index]] as [K, V])
  }
}

/**
 * 检测并返回适当的Set实现
 */
export function getSetImplementation<T>(): typeof Set | typeof CompatSet {
  return typeof Set !== 'undefined' ? Set : CompatSet as any
}

/**
 * 检测并返回适当的Map实现
 */
export function getMapImplementation<K, V>(): typeof Map | typeof CompatMap {
  return typeof Map !== 'undefined' ? Map : CompatMap as any
}

/**
 * 安全的JSON解析
 */
export function safeJSONParse<T>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json)
  } catch {
    return defaultValue
  }
}

/**
 * 安全的JSON字符串化
 */
export function safeJSONStringify(obj: any, defaultValue: string = '{}'): string {
  try {
    return JSON.stringify(obj)
  } catch {
    return defaultValue
  }
}

/**
 * 防抖函数的兼容性实现
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: number | undefined

  return function(this: any, ...args: Parameters<T>) {
    const context = this
    const later = function() {
      timeout = undefined
      if (!immediate) func.apply(context, args)
    }

    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = window.setTimeout(later, wait)

    if (callNow) func.apply(context, args)
  }
}

/**
 * 节流函数的兼容性实现
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return function(this: any, ...args: Parameters<T>) {
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 初始化ES6兼容性支持
 */
export function initES6Compatibility(): void {
  // 检测原生支持情况
  const hasNativeSet = typeof Set !== 'undefined'
  const hasNativeMap = typeof Map !== 'undefined'
  const hasNativePromise = typeof Promise !== 'undefined'

  if (!hasNativeSet) {
    (window as any).Set = CompatSet
  }

  if (!hasNativeMap) {
    (window as any).Map = CompatMap
  }

  console.log('ES6 compatibility initialized:', {
    nativeSet: hasNativeSet,
    nativeMap: hasNativeMap,
    nativePromise: hasNativePromise
  })
}
