package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysDict;

/**
 * 字典类型Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysDictMapper extends BaseMapper<SysDict> {

    /**
     * 分页查询字典类型列表
     *
     * @param page 分页参数
     * @param dictName 字典名称
     * @param dictType 字典类型
     * @param status 状态
     * @return 字典类型分页列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_dict "
            + "WHERE deleted = 0 "
            + "<if test='dictName != null and dictName != \"\"'>"
            + "  AND dict_name LIKE CONCAT('%', #{dictName}, '%') "
            + "</if>"
            + "<if test='dictType != null and dictType != \"\"'>"
            + "  AND dict_type LIKE CONCAT('%', #{dictType}, '%') "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "  AND status = #{status} "
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    Page<SysDict> selectDictPage(Page<SysDict> page,
            @Param("dictName") String dictName,
            @Param("dictType") String dictType,
            @Param("status") String status);

    /**
     * 查询字典类型列表
     *
     * @param dictName 字典名称
     * @param dictType 字典类型
     * @param status 状态
     * @return 字典类型列表
     */
    @Select("<script>"
            + "SELECT * FROM sys_dict "
            + "WHERE deleted = 0 "
            + "<if test='dictName != null and dictName != \"\"'>"
            + "  AND dict_name LIKE CONCAT('%', #{dictName}, '%') "
            + "</if>"
            + "<if test='dictType != null and dictType != \"\"'>"
            + "  AND dict_type LIKE CONCAT('%', #{dictType}, '%') "
            + "</if>"
            + "<if test='status != null and status != \"\"'>"
            + "  AND status = #{status} "
            + "</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<SysDict> selectDictList(@Param("dictName") String dictName,
            @Param("dictType") String dictType,
            @Param("status") String status);

    /**
     * 根据字典类型查询字典信息
     *
     * @param dictType 字典类型
     * @return 字典信息
     */
    @Select("SELECT * FROM sys_dict WHERE dict_type = #{dictType} AND deleted = 0 LIMIT 1")
    SysDict selectByDictType(@Param("dictType") String dictType);

    /**
     * 检查字典类型是否存在
     *
     * @param dictType 字典类型
     * @param excludeId 排除的ID
     * @return 数量
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM sys_dict "
            + "WHERE dict_type = #{dictType} AND deleted = 0 "
            + "<if test='excludeId != null'>"
            + "  AND id != #{excludeId} "
            + "</if>"
            + "</script>")
    int checkDictTypeExists(@Param("dictType") String dictType, @Param("excludeId") String excludeId);

    /**
     * 获取所有字典类型
     *
     * @return 字典类型列表
     */
    @Select("SELECT DISTINCT dict_type FROM sys_dict WHERE status = '0' AND deleted = 0 ORDER BY dict_type")
    List<String> selectAllDictTypes();
}
