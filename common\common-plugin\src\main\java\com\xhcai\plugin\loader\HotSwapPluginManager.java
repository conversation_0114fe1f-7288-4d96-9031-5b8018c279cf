package com.xhcai.plugin.loader;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.xhcai.plugin.core.PluginContext;
import com.xhcai.plugin.core.PluginInfo;
import com.xhcai.plugin.core.PluginType;

/**
 * 热插拔插件管理器 支持插件的热插拔和自动监控
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class HotSwapPluginManager {

    private static final Logger log = LoggerFactory.getLogger(HotSwapPluginManager.class);

    private final Map<PluginType, PluginLoader> pluginLoaders = new ConcurrentHashMap<>();
    private final Map<PluginType, WatchService> watchServices = new ConcurrentHashMap<>();
    private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(4);

    /**
     * 初始化热插拔管理器
     */
    public void initialize(Map<PluginType, PluginContext> contexts) {
        log.info("Initializing HotSwap Plugin Manager...");

        for (Map.Entry<PluginType, PluginContext> entry : contexts.entrySet()) {
            PluginType pluginType = entry.getKey();
            PluginContext context = entry.getValue();

            // 创建插件加载器
            PluginLoader loader = new PluginLoader(context.getPluginManager(), pluginType);
            pluginLoaders.put(pluginType, loader);

            // 启动文件监控
            startFileWatcher(pluginType, context);
        }

        log.info("HotSwap Plugin Manager initialized successfully");
    }

    /**
     * 启动文件监控
     */
    private void startFileWatcher(PluginType pluginType, PluginContext context) {
        try {
            Path pluginDir = Paths.get("plugins", pluginType.getCode());
            if (!Files.exists(pluginDir)) {
                Files.createDirectories(pluginDir);
            }

            WatchService watchService = FileSystems.getDefault().newWatchService();
            pluginDir.register(watchService,
                    StandardWatchEventKinds.ENTRY_CREATE,
                    StandardWatchEventKinds.ENTRY_MODIFY,
                    StandardWatchEventKinds.ENTRY_DELETE);

            watchServices.put(pluginType, watchService);

            // 启动监控线程
            executorService.submit(() -> watchPluginDirectory(pluginType, watchService, pluginDir));

            log.info("Started file watcher for plugin type: {} at path: {}", pluginType, pluginDir);

        } catch (IOException e) {
            log.error("Failed to start file watcher for plugin type: {}", pluginType, e);
        }
    }

    /**
     * 监控插件目录
     */
    private void watchPluginDirectory(PluginType pluginType, WatchService watchService, Path pluginDir) {
        log.info("Watching plugin directory: {} for type: {}", pluginDir, pluginType);

        try {
            while (!Thread.currentThread().isInterrupted()) {
                WatchKey key = watchService.take();

                for (WatchEvent<?> event : key.pollEvents()) {
                    WatchEvent.Kind<?> kind = event.kind();
                    Path fileName = (Path) event.context();
                    Path fullPath = pluginDir.resolve(fileName);

                    log.debug("File event: {} - {}", kind, fullPath);

                    // 只处理 JAR 文件
                    if (!fileName.toString().endsWith(".jar")) {
                        continue;
                    }

                    // 延迟处理，避免文件正在写入时处理
                    executorService.schedule(() -> {
                        handleFileEvent(pluginType, kind, fullPath);
                    }, 2, TimeUnit.SECONDS);
                }

                if (!key.reset()) {
                    log.warn("Watch key is no longer valid for plugin type: {}", pluginType);
                    break;
                }
            }
        } catch (InterruptedException e) {
            log.info("Plugin directory watcher interrupted for type: {}", pluginType);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("Error in plugin directory watcher for type: {}", pluginType, e);
        }
    }

    /**
     * 处理文件事件
     */
    private void handleFileEvent(PluginType pluginType, WatchEvent.Kind<?> kind, Path filePath) {
        try {
            PluginLoader loader = pluginLoaders.get(pluginType);
            if (loader == null) {
                log.warn("No plugin loader found for type: {}", pluginType);
                return;
            }

            String fileName = filePath.getFileName().toString();
            String pluginId = fileName.substring(0, fileName.lastIndexOf('.'));

            if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
                log.info("New plugin detected: {} for type: {}", fileName, pluginType);
                loader.loadPlugin(filePath);

            } else if (kind == StandardWatchEventKinds.ENTRY_MODIFY) {
                log.info("Plugin modified: {} for type: {}", fileName, pluginType);
                if (loader.isPluginLoaded(pluginId)) {
                    loader.upgradePlugin(pluginId, filePath);
                } else {
                    loader.loadPlugin(filePath);
                }

            } else if (kind == StandardWatchEventKinds.ENTRY_DELETE) {
                log.info("Plugin deleted: {} for type: {}", fileName, pluginType);
                if (loader.isPluginLoaded(pluginId)) {
                    loader.unloadPlugin(pluginId);
                }
            }

        } catch (Exception e) {
            log.error("Failed to handle file event: {} - {}", kind, filePath, e);
        }
    }

    /**
     * 手动加载插件
     */
    public String loadPlugin(PluginType pluginType, Path pluginPath) {
        PluginLoader loader = pluginLoaders.get(pluginType);
        if (loader == null) {
            throw new IllegalArgumentException("No plugin loader found for type: " + pluginType);
        }
        return loader.loadPlugin(pluginPath);
    }

    /**
     * 手动卸载插件
     */
    public boolean unloadPlugin(PluginType pluginType, String pluginId) {
        PluginLoader loader = pluginLoaders.get(pluginType);
        if (loader == null) {
            throw new IllegalArgumentException("No plugin loader found for type: " + pluginType);
        }
        return loader.unloadPlugin(pluginId);
    }

    /**
     * 手动重新加载插件
     */
    public boolean reloadPlugin(PluginType pluginType, String pluginId) {
        PluginLoader loader = pluginLoaders.get(pluginType);
        if (loader == null) {
            throw new IllegalArgumentException("No plugin loader found for type: " + pluginType);
        }
        return loader.reloadPlugin(pluginId);
    }

    /**
     * 获取已加载的插件列表
     */
    public List<PluginInfo> getLoadedPlugins(PluginType pluginType) {
        PluginLoader loader = pluginLoaders.get(pluginType);
        if (loader == null) {
            throw new IllegalArgumentException("No plugin loader found for type: " + pluginType);
        }
        return loader.getLoadedPlugins();
    }

    /**
     * 销毁热插拔管理器
     */
    public void destroy() {
        log.info("Destroying HotSwap Plugin Manager...");

        // 关闭文件监控
        watchServices.values().forEach(watchService -> {
            try {
                watchService.close();
            } catch (IOException e) {
                log.error("Failed to close watch service", e);
            }
        });

        // 关闭线程池
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("HotSwap Plugin Manager destroyed");
    }
}
