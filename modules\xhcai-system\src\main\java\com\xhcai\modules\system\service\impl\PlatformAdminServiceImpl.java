package com.xhcai.modules.system.service.impl;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.datasource.plugin.TenantContextHolder;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.SysUserQueryDTO;
import com.xhcai.modules.system.entity.SysDept;
import com.xhcai.modules.system.entity.SysTenant;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.service.IPlatformAdminService;
import com.xhcai.modules.system.service.ISysDeptService;
import com.xhcai.modules.system.service.ISysTenantService;
import com.xhcai.modules.system.service.ISysUserService;
import com.xhcai.modules.system.utils.DictUtils;
import com.xhcai.modules.system.vo.SysUserVO;

/**
 * 平台管理员服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class PlatformAdminServiceImpl implements IPlatformAdminService {

    private static final Logger log = LoggerFactory.getLogger(PlatformAdminServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysTenantService tenantService;

    @Autowired
    private ISysDeptService deptService;

    @Override
    public PageResult<SysUserVO> selectCrossTenantUserPage(SysUserQueryDTO queryDTO, String tenantId) {
        // 检查平台管理员权限
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        if (tenantId != null) {
            // 查询指定租户的用户
            return TenantContextHolder.callWithTenant(tenantId, ()
                    -> userService.selectUserPage(queryDTO)
            );
        } else {
            // 查询所有租户的用户（平台管理员可以不指定租户上下文）
            return userService.selectUserPage(queryDTO);
        }
    }

    @Override
    public SysUserVO getCrossTenantUserDetail(String userId, String tenantId) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        if (userId == null || tenantId == null) {
            throw new BusinessException("用户ID和租户ID不能为空");
        }

        return TenantContextHolder.callWithTenant(tenantId, () -> {
            SysUser user = userService.getById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            return convertToUserVO(user);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCrossTenantUser(SysUser user, String targetTenantId) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        if (user == null || targetTenantId == null) {
            throw new BusinessException("用户信息和目标租户ID不能为空");
        }

        // 验证目标租户状态
        if (!tenantService.validateTenantStatus(targetTenantId)) {
            throw new BusinessException("目标租户状态异常，无法创建用户");
        }

        // 检查租户用户数量限制
        if (tenantService.checkUserLimit(targetTenantId)) {
            throw new BusinessException("目标租户用户数量已达上限");
        }

        // 设置租户ID
        user.setTenantId(targetTenantId);

        return TenantContextHolder.callWithTenant(targetTenantId, ()
                -> userService.insertUser(user)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCrossTenantUser(SysUser user, String targetTenantId) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        if (user == null || user.getId() == null || targetTenantId == null) {
            throw new BusinessException("用户信息和目标租户ID不能为空");
        }

        // 验证目标租户状态
        if (!tenantService.validateTenantStatus(targetTenantId)) {
            throw new BusinessException("目标租户状态异常，无法更新用户");
        }

        return TenantContextHolder.callWithTenant(targetTenantId, ()
                -> userService.updateUser(user)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCrossTenantUsers(List<String> userIds, String tenantId) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        if (userIds == null || userIds.isEmpty() || tenantId == null) {
            throw new BusinessException("用户ID列表和租户ID不能为空");
        }

        return TenantContextHolder.callWithTenant(tenantId, ()
                -> userService.deleteUsers(userIds)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetCrossTenantUserPassword(String userId, String tenantId, String newPassword) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        if (userId == null || tenantId == null) {
            throw new BusinessException("用户ID和租户ID不能为空");
        }

        return TenantContextHolder.callWithTenant(tenantId, ()
                -> userService.resetPassword(userId, newPassword)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleCrossTenantUserStatus(String userId, String tenantId, boolean enabled) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        if (userId == null || tenantId == null) {
            throw new BusinessException("用户ID和租户ID不能为空");
        }

        return TenantContextHolder.callWithTenant(tenantId, () -> {
            SysUser user = userService.getById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            user.setStatus(enabled ? CommonConstants.STATUS_NORMAL : CommonConstants.STATUS_LOCK);
            return userService.updateUser(user);
        });
    }

    @Override
    public Map<String, Object> getSystemStatistics() {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> statistics = new HashMap<>();

        try {
            // 租户统计（租户表不需要租户隔离）
            long totalTenants = tenantService.count();
            statistics.put("totalTenants", totalTenants);

            // 用户统计（平台管理员可以查看所有用户）
            long totalUsers = userService.count();
            statistics.put("totalUsers", totalUsers);

            // 活跃租户统计
            List<Map<String, Object>> tenantStats = getTenantStatistics();
            long activeTenants = tenantStats.stream()
                    .mapToLong(stat -> (Long) stat.getOrDefault("userCount", 0L))
                    .count();
            statistics.put("activeTenants", activeTenants);

            // 系统运行时间
            statistics.put("systemUptime", getSystemUptime());

            // 当前时间
            statistics.put("currentTime", LocalDateTime.now());

            String currentUsername = SecurityUtils.getCurrentUsername();
            log.info("平台管理员 {} 查询系统统计信息", currentUsername != null ? currentUsername : "unknown");

        } catch (Exception e) {
            log.error("获取系统统计信息失败: {}", e.getMessage(), e);
            throw new BusinessException("获取系统统计信息失败");
        }

        return statistics;
    }

    @Override
    public List<Map<String, Object>> getTenantStatistics() {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        // TODO: 实现租户统计逻辑
        return List.of();
    }

    @Override
    public Map<String, Object> getUserStatistics(String tenantId) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> statistics = new HashMap<>();

        if (tenantId != null) {
            // 指定租户的用户统计
            Integer userCount = tenantService.getTenantUserCount(tenantId);
            statistics.put("userCount", userCount);
            statistics.put("tenantId", tenantId);
        } else {
            // 所有租户的用户统计（平台管理员可以查看所有用户）
            long totalUsers = userService.count();
            statistics.put("totalUsers", totalUsers);
        }

        return statistics;
    }

    @Override
    public Map<String, Object> getSystemPerformance() {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> performance = new HashMap<>();

        // JVM内存信息
        Runtime runtime = Runtime.getRuntime();
        performance.put("totalMemory", runtime.totalMemory());
        performance.put("freeMemory", runtime.freeMemory());
        performance.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        performance.put("maxMemory", runtime.maxMemory());

        // CPU核心数
        performance.put("availableProcessors", runtime.availableProcessors());

        // 系统属性
        performance.put("javaVersion", System.getProperty("java.version"));
        performance.put("osName", System.getProperty("os.name"));
        performance.put("osVersion", System.getProperty("os.version"));

        return performance;
    }

    /**
     * 转换为用户VO
     */
    private SysUserVO convertToUserVO(SysUser user) {
        if (user == null) {
            return null;
        }

        SysUserVO userVO = new SysUserVO();
        BeanUtils.copyProperties(user, userVO);

        // 设置性别描述 - 使用字典工具类
        if (StringUtils.hasText(user.getGender())) {
            userVO.setGenderName(DictUtils.getUserGenderLabel(user.getGender()));
        }

        // 设置状态描述 - 使用字典工具类
        if (StringUtils.hasText(user.getStatus())) {
            userVO.setStatusName(DictUtils.getUserStatusLabel(user.getStatus()));
        }

        // 设置部门名称
        if (user.getDeptId() != null) {
            try {
                SysDept dept = deptService.getById(user.getDeptId());
                if (dept != null) {
                    userVO.setDeptName(dept.getDeptName());
                }
            } catch (Exception e) {
                log.warn("获取部门信息失败，部门ID: {}, 错误: {}", user.getDeptId(), e.getMessage());
            }
        }

        return userVO;
    }

    @Override
    public Map<String, Object> getLogStatistics(Integer days) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> statistics = new HashMap<>();
        // TODO: 实现日志统计逻辑
        statistics.put("days", days != null ? days : 7);
        statistics.put("totalLogs", 0);
        statistics.put("errorLogs", 0);
        statistics.put("warnLogs", 0);

        return statistics;
    }

    @Override
    public Map<String, Object> cleanupSystemData(String dataType, Integer days) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("dataType", dataType);
        result.put("days", days);
        result.put("cleanedCount", 0);
        result.put("success", true);

        log.info("平台管理员 {} 执行数据清理: 类型={}, 天数={}",
                SecurityUtils.getCurrentUsername(), dataType, days);

        return result;
    }

    @Override
    public Map<String, Object> executeMaintenanceTask(String taskType) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("taskType", taskType);
        result.put("startTime", LocalDateTime.now());
        result.put("success", true);

        String currentUsername = SecurityUtils.getCurrentUsername();
        log.info("平台管理员 {} 执行维护任务: {}", currentUsername != null ? currentUsername : "unknown", taskType);

        return result;
    }

    @Override
    public Map<String, Object> getSystemConfig() {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> config = new HashMap<>();
        config.put("systemName", "XHC智能平台");
        config.put("version", "1.0.0");
        config.put("environment", "production");

        return config;
    }

    @Override
    public boolean updateSystemConfig(Map<String, Object> configs) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        log.info("平台管理员 {} 更新系统配置", SecurityUtils.getCurrentUsername());
        return true;
    }

    @Override
    public List<Map<String, Object>> getTenantConfigOverview() {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        // TODO: 实现租户配置概览逻辑
        return List.of();
    }

    @Override
    public boolean batchUpdateTenantConfigs(List<String> tenantIds, Map<String, String> configs) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        log.info("平台管理员 {} 批量更新租户配置: 租户数={}, 配置数={}",
                SecurityUtils.getCurrentUsername(), tenantIds.size(), configs.size());
        return true;
    }

    @Override
    public Map<String, Object> getSystemHealthCheck() {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("database", getDatabaseStatus());
        health.put("cache", getCacheStatus());
        health.put("checkTime", LocalDateTime.now());

        return health;
    }

    @Override
    public Map<String, Object> getDatabaseStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("connectionCount", 10);
        status.put("activeConnections", 5);

        return status;
    }

    @Override
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("hitRate", 0.85);
        status.put("size", 1000);

        return status;
    }

    @Override
    public boolean clearCache(String cacheType) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        String currentUsername = SecurityUtils.getCurrentUsername();
        log.info("平台管理员 {} 清理缓存: {}", currentUsername != null ? currentUsername : "unknown", cacheType);
        return true;
    }

    @Override
    public List<Map<String, Object>> getSystemErrorLogs(Integer hours, String level) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        // TODO: 实现错误日志查询逻辑
        return List.of();
    }

    @Override
    public String exportSystemData(String dataType, String tenantId) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        log.info("平台管理员 {} 导出系统数据: 类型={}, 租户={}",
                SecurityUtils.getCurrentUsername(), dataType, tenantId);

        return "/exports/system_data_" + System.currentTimeMillis() + ".xlsx";
    }

    @Override
    public Map<String, Object> importSystemData(String dataType, String filePath, String tenantId) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("importedCount", 0);

        log.info("平台管理员 {} 导入系统数据: 类型={}, 文件={}, 租户={}",
                SecurityUtils.getCurrentUsername(), dataType, filePath, tenantId);

        return result;
    }

    @Override
    public boolean sendSystemNotification(String title, String content, List<String> tenantIds, List<String> userIds) {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        log.info("平台管理员 {} 发送系统通知: 标题={}", SecurityUtils.getCurrentUsername(), title);
        return true;
    }

    @Override
    public Map<String, Object> getSystemVersion() {
        Map<String, Object> version = new HashMap<>();
        version.put("version", "1.0.0");
        version.put("buildTime", "2024-01-01 00:00:00");
        version.put("gitCommit", "abc123");

        return version;
    }

    @Override
    public Map<String, Object> checkSystemUpdate() {
        if (!SecurityUtils.isPlatformAdmin()) {
            throw new BusinessException("权限不足，需要平台管理员权限");
        }

        Map<String, Object> update = new HashMap<>();
        update.put("hasUpdate", false);
        update.put("currentVersion", "1.0.0");
        update.put("latestVersion", "1.0.0");

        return update;
    }

    @Override
    public Map<String, Object> checkPlatformInitialization() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始检查平台初始化状态，查找用户类型为平台管理员的用户");

            // 直接通过用户类型字段查询平台管理员
            boolean hasAdmin = isPlatformAdminExists();

            // 获取平台管理员数量
            long adminCount = 0;
            if (hasAdmin) {
                adminCount = userService.lambdaQuery()
                        .eq(SysUser::getUserType, CommonConstants.USER_TYPE_PLATFORM)
                        .count();
            }

            result.put("initialized", hasAdmin);
            result.put("hasPlatformAdmin", hasAdmin);
            result.put("adminCount", adminCount);
            result.put("message", hasAdmin ? "平台已初始化" : "平台未初始化，需要创建平台管理员");

            log.info("平台初始化状态检查完成: 已初始化={}, 管理员数量={}", hasAdmin, adminCount);

        } catch (Exception e) {
            log.error("检查平台初始化状态失败: {}", e.getMessage(), e);
            result.put("initialized", false);
            result.put("hasPlatformAdmin", false);
            result.put("adminCount", 0);
            result.put("error", "检查平台初始化状态失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> createPlatformAdmin(String username, String password, String email, String realName) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查平台是否已初始化（检查是否已存在平台管理员）
            if (isPlatformAdminExists()) {
                result.put("success", false);
                result.put("message", "平台已初始化，不能重复创建管理员");
                return result;
            }

            // 1. 创建平台租户
            SysTenant platformTenant = createPlatformTenant(username, email);
            String platformTenantId = platformTenant.getId();

            // 2. 在平台租户上下文中创建管理员用户
            SysUser createdAdmin = TenantContextHolder.callWithTenant(platformTenantId, () -> {
                // 检查用户名是否已存在
                long existingCount = userService.lambdaQuery()
                        .eq(SysUser::getUsername, username)
                        .count();

                if (existingCount > 0) {
                    throw new BusinessException("用户名已存在");
                }

                // 创建平台管理员用户
                SysUser admin = new SysUser();
                admin.setUsername(username);
                admin.setPassword(new BCryptPasswordEncoder().encode(password));
                admin.setEmail(email);
                admin.setNickname(realName); // 使用nickname字段存储真实姓名
                admin.setTenantId(platformTenantId);
                admin.setStatus(CommonConstants.STATUS_NORMAL);
                admin.setUserType(CommonConstants.USER_TYPE_PLATFORM); // 设置用户类型为平台管理员
                admin.setDeptId(CommonConstants.DEPT_TREE_ROOT_ID); // 平台管理员不属于任何部门

                // 保存用户并返回用户对象
                boolean saveSuccess = userService.save(admin);
                if (saveSuccess) {
                    return admin; // 直接返回创建的用户对象
                } else {
                    throw new BusinessException("保存平台管理员失败");
                }
            });

            if (createdAdmin != null) {
                result.put("success", true);
                result.put("message", "平台管理员创建成功");
                result.put("adminId", createdAdmin.getId());
                result.put("username", createdAdmin.getUsername());
                result.put("email", createdAdmin.getEmail());
                log.info("平台管理员创建成功: 用户名={}, 邮箱={}, 用户类型={}",
                        username, email, createdAdmin.getUserType());
            } else {
                result.put("success", false);
                result.put("message", "平台管理员创建失败");
            }

        } catch (Exception e) {
            log.error("创建平台管理员失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "创建平台管理员失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 检查平台管理员是否已存在 通过用户类型字段判断
     */
    private boolean isPlatformAdminExists() {
        try {
            // 查询所有租户中是否存在用户类型为platform的用户
            long count = userService.lambdaQuery()
                    .eq(SysUser::getUserType, CommonConstants.USER_TYPE_PLATFORM)
                    .count();

            log.info("检查平台管理员是否存在: 找到 {} 个平台管理员用户", count);
            return count > 0;
        } catch (Exception e) {
            log.error("检查平台管理员存在性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建平台租户
     */
    private SysTenant createPlatformTenant(String adminUsername, String adminEmail) {
        // 生成平台租户编码
        String tenantCode = CommonConstants.PLATFORM_TENANT_PREFIX + System.currentTimeMillis();

        SysTenant platformTenant = new SysTenant();
        platformTenant.setTenantCode(tenantCode);
        platformTenant.setTenantName("平台管理租户");
        platformTenant.setTenantShortName("平台租户");
        platformTenant.setContactPerson(adminUsername);
        platformTenant.setContactEmail(adminEmail);
        platformTenant.setStatus(CommonConstants.TENANT_STATUS_NORMAL);
        platformTenant.setUserLimit(1000); // 平台租户用户数量限制较高
        platformTenant.setStorageLimit(10240L); // 平台租户存储限制较高
        platformTenant.setDescription("平台管理员所属的租户");

        // 直接保存，不通过租户上下文（因为租户表本身不需要租户隔离）
        boolean saveSuccess = tenantService.save(platformTenant);
        if (!saveSuccess) {
            throw new BusinessException("创建平台租户失败");
        }

        return platformTenant;
    }

    /**
     * 获取系统运行时间
     */
    private long getSystemUptime() {
        return System.currentTimeMillis();
    }
}
