package com.xhcai.modules.rag.enums;

/**
 * 数据源类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum DataSourceType {
    
    /**
     * 文件上传
     */
    UPLOAD_FILE("upload_file", "文件上传"),
    
    /**
     * 网页抓取
     */
    WEB_CRAWL("web_crawl", "网页抓取"),
    
    /**
     * API接口
     */
    API("api", "API接口"),
    
    /**
     * 数据库
     */
    DATABASE("database", "数据库"),
    
    /**
     * 文本输入
     */
    TEXT_INPUT("text_input", "文本输入"),
    
    /**
     * 第三方集成
     */
    THIRD_PARTY("third_party", "第三方集成");

    private final String code;
    private final String description;

    DataSourceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 数据源类型代码
     * @return 数据源类型枚举
     */
    public static DataSourceType fromCode(String code) {
        for (DataSourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的数据源类型代码: " + code);
    }
}
