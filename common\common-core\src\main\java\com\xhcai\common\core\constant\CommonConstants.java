package com.xhcai.common.core.constant;

/**
 * 通用常量
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CommonConstants {

    /**
     * 成功标记
     */
    Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    Integer FAIL = 500;

    /**
     * 编码
     */
    String UTF8 = "UTF-8";

    /**
     * JSON 资源
     */
    String CONTENT_TYPE = "application/json; charset=utf-8";

    /**
     * 前端工程名
     */
    String FRONT_END_PROJECT = "xhcai-ui";

    /**
     * 后端工程名
     */
    String BACK_END_PROJECT = "xhcai-plus";

    /**
     * 验证码前缀
     */
    String DEFAULT_CODE_KEY = "DEFAULT_CODE_KEY:";

    /**
     * 菜单树根节点
     */
    String MENU_TREE_ROOT_ID = "0";

    /**
     * 部门树根节点
     */
    String DEPT_TREE_ROOT_ID = "0";

    /**
     * 权限树根节点
     */
    String PERMISSION_TREE_ROOT_ID = "0";

    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "123456";

    /**
     * 普通用户角色编码
     */
    String ROLE_USER = "ROLE_USER";

    /**
     * 用户类型 - 平台管理员
     */
    String USER_TYPE_PLATFORM = "platform";

    /**
     * 用户类型 - 租户管理员
     */
    String USER_TYPE_TENANT = "tenant";

    /**
     * 用户类型 - 普通人员
     */
    String USER_TYPE_NORMAL = "normal";

    /**
     * 删除标记
     */
    String STATUS_DEL = "1";

    /**
     * 正常标记
     */
    String STATUS_NORMAL = "0";

    /**
     * 停用标记
     */
    String STATUS_DISABLE = "1";

    /**
     * 锁定标记
     */
    String STATUS_LOCK = "9";

    /**
     * 菜单类型
     */
    String MENU_TYPE_MENU = "0";

    /**
     * 按钮类型
     */
    String MENU_TYPE_BUTTON = "1";

    /**
     * 目录类型
     */
    String MENU_TYPE_CATALOG = "2";

    /**
     * 模块启用状态
     */
    String MODULE_STATUS_ENABLED = "1";

    /**
     * 模块禁用状态
     */
    String MODULE_STATUS_DISABLED = "0";

    /**
     * 平台租户标识（用于标识平台管理员所属的租户，不再使用固定的"0"）
     * 注意：平台管理员通过用户类型USER_TYPE_PLATFORM来识别，而不是通过租户ID
     */
    String PLATFORM_TENANT_PREFIX = "PLATFORM_";

    /**
     * 租户状态 - 正常
     */
    String TENANT_STATUS_NORMAL = "0";

    /**
     * 租户状态 - 停用
     */
    String TENANT_STATUS_DISABLED = "1";

    /**
     * 租户状态 - 过期
     */
    String TENANT_STATUS_EXPIRED = "2";

    /**
     * 日期格式化
     */
    String NORM_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式化
     */
    String NORM_DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 时间格式化
     */
    String NORM_TIME_PATTERN = "HH:mm:ss";

    /**
     * 请求头中的租户ID
     */
    String TENANT_ID_HEADER = "Tenant-Id";

    /**
     * 请求头中的用户ID
     */
    String USER_ID_HEADER = "User-Id";

    /**
     * 请求头中的用户名
     */
    String USER_NAME_HEADER = "User-Name";

    /**
     * 请求头中的角色信息
     */
    String ROLE_HEADER = "Role";

    /**
     * 权限标识前缀
     */
    String PERMISSION_PREFIX = "PERM:";

    /**
     * 角色标识前缀
     */
    String ROLE_PREFIX = "ROLE:";

    /**
     * 缓存前缀
     */
    String CACHE_PREFIX = "xhcai:";

    /**
     * 用户缓存前缀
     */
    String USER_CACHE_PREFIX = CACHE_PREFIX + "user:";

    /**
     * 权限缓存前缀
     */
    String PERMISSION_CACHE_PREFIX = CACHE_PREFIX + "permission:";

    /**
     * 角色缓存前缀
     */
    String ROLE_CACHE_PREFIX = CACHE_PREFIX + "role:";

    /**
     * 模块缓存前缀
     */
    String MODULE_CACHE_PREFIX = CACHE_PREFIX + "module:";

    /**
     * AI对话缓存前缀
     */
    String AI_CHAT_CACHE_PREFIX = CACHE_PREFIX + "ai:chat:";

    /**
     * 文件上传路径前缀
     */
    String FILE_UPLOAD_PREFIX = "/upload/";

    /**
     * 临时文件路径前缀
     */
    String TEMP_FILE_PREFIX = "/temp/";

    /**
     * 系统配置缓存前缀
     */
    String CONFIG_CACHE_PREFIX = CACHE_PREFIX + "config:";

    /**
     * 字典缓存前缀
     */
    String DICT_CACHE_PREFIX = CACHE_PREFIX + "dict:";

    /**
     * 操作日志类型 - 登录
     */
    String LOG_TYPE_LOGIN = "LOGIN";

    /**
     * 操作日志类型 - 操作
     */
    String LOG_TYPE_OPERATION = "OPERATION";

    /**
     * 操作日志类型 - 异常
     */
    String LOG_TYPE_EXCEPTION = "EXCEPTION";

    /**
     * 模块事件类型 - 启用
     */
    String MODULE_EVENT_ENABLE = "MODULE_ENABLE";

    /**
     * 模块事件类型 - 禁用
     */
    String MODULE_EVENT_DISABLE = "MODULE_DISABLE";

    /**
     * 模块事件类型 - 安装
     */
    String MODULE_EVENT_INSTALL = "MODULE_INSTALL";

    /**
     * 模块事件类型 - 卸载
     */
    String MODULE_EVENT_UNINSTALL = "MODULE_UNINSTALL";
}
