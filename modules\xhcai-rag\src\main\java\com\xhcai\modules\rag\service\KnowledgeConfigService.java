package com.xhcai.modules.rag.service;

import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig;

/**
 * 知识库配置服务接口
 *
 * <AUTHOR>
 */
public interface KnowledgeConfigService {

    /**
     * 获取文件分段配置
     */
    KnowledgeSegmentConfig getSegmentConfig();

    /**
     * 保存文件分段配置
     */
    KnowledgeSegmentConfig saveSegmentConfig(KnowledgeSegmentConfig config);

    /**
     * 获取向量化配置
     */
    KnowledgeVectorizationConfig getVectorizationConfig();

    /**
     * 保存向量化配置
     */
    KnowledgeVectorizationConfig saveVectorizationConfig(KnowledgeVectorizationConfig config);
}
