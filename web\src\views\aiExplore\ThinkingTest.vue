<template>
  <div class="thinking-test-page">
    <div class="container mx-auto p-6">
      <h1 class="text-2xl font-bold mb-6">思考内容显示功能测试</h1>
      
      <!-- 测试控制区域 -->
      <div class="test-controls mb-6 p-4 bg-gray-100 rounded-lg">
        <h2 class="text-lg font-semibold mb-4">测试控制</h2>
        
        <div class="flex gap-4 mb-4">
          <button
            @click="startStreamingTest"
            :disabled="isStreaming"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {{ isStreaming ? '流式输出中...' : '开始流式测试' }}
          </button>

          <button
            @click="startMarkdownTest"
            :disabled="isStreaming"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            测试Markdown格式
          </button>

          <button
            @click="resetTest"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            重置测试
          </button>
        </div>
        
        <div class="text-sm text-gray-600">
          <p>测试目标：</p>
          <ul class="list-disc list-inside ml-4">
            <li>&lt;think&gt;&lt;/think&gt; 标签不在对话框中显示</li>
            <li>思考内容流式输出到"思考中"块</li>
            <li>思考中时内容块自动展开</li>
            <li>思考结束后内容块自动合上</li>
            <li>正文内容正常显示</li>
          </ul>
        </div>
      </div>
      
      <!-- 消息显示区域 -->
      <div class="message-area">
        <div class="message-item mb-4 p-4 bg-white rounded-lg shadow">
          <div class="message-header mb-2">
            <span class="text-sm text-gray-500">AI回复</span>
            <span class="ml-2 text-xs text-blue-500">{{ isStreaming ? '流式输出中' : '完成' }}</span>
          </div>
          
          <!-- 使用MarkdownRenderer渲染内容 -->
          <MarkdownRenderer
            :content="currentContent"
            :streaming="isStreaming"
          />
        </div>
      </div>
      
      <!-- 调试信息 -->
      <div class="debug-info mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 class="text-lg font-semibold mb-2">调试信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>当前内容长度:</strong> {{ currentContent.length }}字符
          </div>
          <div>
            <strong>是否在思考中:</strong> {{ isThinkingStreaming ? '是' : '否' }}
          </div>
          <div>
            <strong>思考内容长度:</strong> {{ thinkingContent.length }}字符
          </div>
          <div>
            <strong>主内容长度:</strong> {{ mainContent.length }}字符
          </div>
        </div>
        
        <div class="mt-4">
          <details class="cursor-pointer">
            <summary class="font-semibold">原始内容</summary>
            <pre class="mt-2 p-2 bg-white rounded text-xs overflow-auto">{{ currentContent }}</pre>
          </details>
        </div>
        
        <div class="mt-4">
          <details class="cursor-pointer">
            <summary class="font-semibold">解析后的思考内容</summary>
            <pre class="mt-2 p-2 bg-white rounded text-xs overflow-auto">{{ thinkingContent }}</pre>
          </details>
        </div>
        
        <div class="mt-4">
          <details class="cursor-pointer">
            <summary class="font-semibold">解析后的主内容</summary>
            <pre class="mt-2 p-2 bg-white rounded text-xs overflow-auto">{{ mainContent }}</pre>
          </details>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import MarkdownRenderer from '@/views/aiExplore/renderers/MarkdownRenderer.vue'

// 响应式数据
const currentContent = ref('')
const isStreaming = ref(false)

// 测试数据 - 包含思考内容的示例
const testContent = `<think>
用户问我能做什么，这是一个很常见的问题。我需要简要介绍我的主要功能和能力。我可以帮助用户进行对话、回答问题、协助写作、分析问题等。我应该用友好和专业的语气来回答。

让我组织一下回答的结构：
1. 首先说明我是AI助手
2. 然后列出主要功能分类
3. 每个分类下面列出具体能力
4. 最后询问用户需要什么帮助

这样的结构比较清晰，用户容易理解。我还要使用一些emoji来让回答更生动一些。
</think>

我是一个AI助手，可以帮助您完成多种任务：

## 🚀 主要功能

### 💬 对话交流
- **回答问题**：各种领域的知识问答
- **提供建议**：基于分析给出合理建议
- **友好对话**：进行自然流畅的交流

### ✍️ 写作协助
- **内容创作**：帮助撰写文章、邮件、报告
- **文本优化**：改进表达方式和语言风格
- **写作指导**：提供结构建议和写作技巧

### 📊 分析思考
- **问题分析**：深入分析复杂问题的各个方面
- **多角度思考**：从不同维度提供见解
- **逻辑推理**：运用逻辑思维进行推理判断

### 🔍 信息处理
- **内容总结**：提取关键信息并归纳要点
- **概念解释**：用通俗易懂的方式解释复杂概念
- **学习辅导**：提供学习方法和知识指导

### 💻 技术支持
- **代码编写**：多种编程语言的代码示例
- **技术解答**：解决技术问题和疑难
- **最佳实践**：分享行业最佳实践经验

## 📝 示例场景

> **用户**：我需要写一份项目报告，但不知道怎么开始。
>
> **我的帮助**：我可以帮您：
> 1. 制定报告大纲和结构
> 2. 提供各部分的写作要点
> 3. 优化语言表达和逻辑流程

## 🎯 使用建议

1. **明确需求**：详细描述您的具体需要
2. **提供背景**：说明相关的背景信息
3. **互动交流**：随时提出疑问和反馈

---

**有什么我可以帮助您的吗？** 请随时告诉我您的需求！`

// 复杂Markdown测试数据
const complexMarkdownContent = `<think>
用户想要测试Markdown格式的渲染效果。我需要提供一个包含各种Markdown元素的示例，包括：
1. 各级标题
2. 列表（有序和无序）
3. 代码块和行内代码
4. 表格
5. 引用块
6. 链接和图片
7. 强调文本（粗体、斜体）
8. 分割线

这样可以全面测试Markdown渲染器的功能。我要确保格式正确，内容有意义。
</think>

# Markdown格式测试文档

这是一个**全面的Markdown格式测试**，包含了各种常用的Markdown元素。

## 1. 文本格式

### 强调文本
- **粗体文本**：使用双星号包围
- *斜体文本*：使用单星号包围
- ***粗斜体***：使用三个星号
- ~~删除线~~：使用双波浪号
- \`行内代码\`：使用反引号

### 引用块
> 这是一个引用块的示例。
>
> 引用块可以包含多行内容，非常适合展示重要信息或引用他人的话语。

## 2. 列表示例

### 无序列表
- 第一项
- 第二项
  - 嵌套项目 1
  - 嵌套项目 2
- 第三项

### 有序列表
1. 首先做这个
2. 然后做那个
3. 最后完成这个
   1. 子步骤 A
   2. 子步骤 B

## 3. 代码示例

### 行内代码
使用 \`console.log()\` 函数可以在控制台输出信息。

### 代码块
\`\`\`javascript
// JavaScript 示例
function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to our platform, \${name}!\`;
}

// 调用函数
const message = greetUser("Alice");
console.log(message);
\`\`\`

\`\`\`python
# Python 示例
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

# 计算前10个斐波那契数
for i in range(10):
    print(f"F({i}) = {calculate_fibonacci(i)}")
\`\`\`

## 4. 表格示例

| 功能 | 描述 | 状态 |
|------|------|------|
| 文本渲染 | 支持各种文本格式 | ✅ 完成 |
| 代码高亮 | 语法高亮显示 | ✅ 完成 |
| 表格支持 | 表格格式化显示 | ✅ 完成 |
| 链接处理 | 自动识别和处理链接 | 🔄 进行中 |

## 5. 链接和图片

### 链接示例
- [GitHub](https://github.com)
- [Vue.js官网](https://vuejs.org)
- [Markdown指南](https://www.markdownguide.org)

### 图片示例
![Markdown Logo](https://markdown-here.com/img/icon256.png)

## 6. 分割线

---

## 7. 任务列表

- [x] 实现基础Markdown渲染
- [x] 添加代码高亮功能
- [x] 支持表格格式
- [ ] 添加数学公式支持
- [ ] 实现图表渲染
- [ ] 优化移动端显示

## 8. 嵌套内容示例

### 复杂嵌套结构
1. **第一大类**
   - 子项目 A
     > 这是一个嵌套在列表中的引用块
   - 子项目 B
     \`\`\`bash
     # 嵌套的代码块
     npm install markdown-renderer
     npm run build
     \`\`\`

2. **第二大类**
   - 功能列表：
     - [x] 基础功能
     - [x] 高级功能
     - [ ] 扩展功能

---

## 总结

这个测试文档展示了Markdown渲染器的各种功能，包括：

1. ✨ **文本格式化**：粗体、斜体、删除线等
2. 📝 **结构化内容**：标题、列表、引用块
3. 💻 **代码展示**：行内代码和代码块
4. 📊 **数据表格**：结构化数据展示
5. 🔗 **链接处理**：自动链接识别
6. 🎯 **任务管理**：任务列表支持

> **测试完成！** 如果所有元素都能正确渲染，说明Markdown渲染器工作正常。`

// 解析函数
const extractStreamingThinkingContent = (content: string) => {
  const openTagIndex = content.lastIndexOf('<think>')
  const closeTagIndex = content.lastIndexOf('</think>')
  
  if (openTagIndex > closeTagIndex) {
    const thinkingStart = openTagIndex + 7 // '<think>'.length
    return content.substring(thinkingStart)
  }
  
  return ''
}

const cleanContentFromThinkingTags = (content: string) => {
  let cleaned = content.replace(/<think>[\s\S]*?<\/think>/g, '')
  
  const openTagIndex = cleaned.lastIndexOf('<think>')
  const closeTagIndex = cleaned.lastIndexOf('</think>')
  
  if (openTagIndex > closeTagIndex) {
    cleaned = cleaned.substring(0, openTagIndex)
  }
  
  return cleaned.trim()
}

const checkThinkingStreaming = (content: string) => {
  const openTags = (content.match(/<think>/g) || []).length
  const closeTags = (content.match(/<\/think>/g) || []).length
  return openTags > closeTags
}

const parseThinkingContent = (content: string) => {
  const thinkRegex = /<think>([\s\S]*?)<\/think>/g
  let match
  let thinking = ''
  let main = content
  
  while ((match = thinkRegex.exec(content)) !== null) {
    thinking += match[1]
  }
  
  main = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
  
  return { thinking, main }
}

// 计算属性
const isThinkingStreaming = computed(() => {
  return isStreaming.value && checkThinkingStreaming(currentContent.value)
})

const thinkingContent = computed(() => {
  if (!currentContent.value) return ''
  
  if (isThinkingStreaming.value) {
    return extractStreamingThinkingContent(currentContent.value)
  } else {
    const { thinking } = parseThinkingContent(currentContent.value)
    return thinking
  }
})

const mainContent = computed(() => {
  if (!currentContent.value) return ''
  
  if (isThinkingStreaming.value) {
    return cleanContentFromThinkingTags(currentContent.value)
  } else {
    const { main } = parseThinkingContent(currentContent.value)
    return main
  }
})

// 方法
const startStreamingTest = async () => {
  if (isStreaming.value) return

  isStreaming.value = true
  currentContent.value = ''

  // 模拟流式输出
  const content = testContent
  let index = 0

  const streamInterval = setInterval(() => {
    if (index < content.length) {
      currentContent.value += content[index]
      index++
    } else {
      clearInterval(streamInterval)
      isStreaming.value = false
    }
  }, 50) // 每50ms输出一个字符
}

const startMarkdownTest = async () => {
  if (isStreaming.value) return

  isStreaming.value = true
  currentContent.value = ''

  // 模拟复杂Markdown内容的流式输出
  const content = complexMarkdownContent
  let index = 0

  const streamInterval = setInterval(() => {
    if (index < content.length) {
      currentContent.value += content[index]
      index++
    } else {
      clearInterval(streamInterval)
      isStreaming.value = false
    }
  }, 30) // 稍快一些的输出速度
}

const resetTest = () => {
  currentContent.value = ''
  isStreaming.value = false
}
</script>

<style scoped>
.thinking-test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
}

.message-item {
  border: 1px solid #e5e7eb;
}

.debug-info {
  font-family: 'Courier New', monospace;
}

details summary {
  padding: 8px;
  background-color: #f3f4f6;
  border-radius: 4px;
  margin-bottom: 8px;
}

details[open] summary {
  margin-bottom: 8px;
}

pre {
  max-height: 200px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
