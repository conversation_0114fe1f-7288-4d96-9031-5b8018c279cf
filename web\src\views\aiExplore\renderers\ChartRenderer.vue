<template>
  <div class="chart-renderer">
    <div class="chart-container">
      <div class="chart-header">
        <div class="chart-info">
          <el-icon class="chart-icon"><DataAnalysis /></el-icon>
          <div class="chart-details">
            <div class="chart-title">{{ chartTitle }}</div>
            <div class="chart-type">{{ typeLabel }}</div>
          </div>
        </div>
        <div class="chart-actions">
          <el-button @click="exportChart" size="small" type="primary" text>
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="toggleFullscreen" size="small" type="primary" text>
            <el-icon><FullScreen /></el-icon>
            全屏
          </el-button>
        </div>
      </div>

      <div class="chart-content" ref="chartContainer">
        <canvas ref="chartCanvas"></canvas>
      </div>

      <!-- 图表说明 -->
      <div v-if="data.description" class="chart-description">
        <p>{{ data.description }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, defineProps, defineEmits, nextTick } from 'vue'
import { DataAnalysis, Download, FullScreen } from '@element-plus/icons-vue'
import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  BarController,
  LineController,
  PieController,
  ScatterController
} from 'chart.js'

// 注册Chart.js组件
Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  BarController,
  LineController,
  PieController,
  ScatterController
)

// 接口定义
interface ChartData {
  title?: string
  description?: string
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
    fill?: boolean
  }[]
  options?: any
}

// Props定义
const props = defineProps<{
  data: ChartData
  type: 'bar' | 'line' | 'pie' | 'scatter'
}>()

// Emits定义
const emit = defineEmits<{
  export: [type: string]
  fullscreen: [isFullscreen: boolean]
}>()

// 响应式数据
const chartCanvas = ref<HTMLCanvasElement>()
const chartContainer = ref<HTMLElement>()
const chartInstance = ref<Chart | null>(null)
const isFullscreen = ref(false)

// 计算属性
const chartTitle = computed(() => {
  return props.data.title || '图表'
})

const typeLabel = computed(() => {
  const labels = {
    bar: '柱状图',
    line: '折线图',
    pie: '饼图',
    scatter: '散点图'
  }
  return labels[props.type] || '图表'
})

// 获取图表配置
const getChartConfig = () => {
  const baseConfig = {
    type: props.type,
    data: {
      labels: props.data.labels,
      datasets: props.data.datasets.map(dataset => ({
        ...dataset,
        backgroundColor: dataset.backgroundColor || getDefaultColors(props.type),
        borderColor: dataset.borderColor || getDefaultBorderColors(props.type),
        borderWidth: dataset.borderWidth || 1
      }))
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: !!props.data.title,
          text: props.data.title
        },
        legend: {
          display: true,
          position: 'top' as const
        },
        tooltip: {
          mode: 'index' as const,
          intersect: false
        }
      },
      scales: props.type !== 'pie' ? {
        x: {
          display: true,
          title: {
            display: false
          }
        },
        y: {
          display: true,
          title: {
            display: false
          },
          beginAtZero: true
        }
      } : undefined,
      ...props.data.options
    }
  }

  return baseConfig
}

// 获取默认颜色
const getDefaultColors = (type: string) => {
  const colors = [
    'rgba(59, 130, 246, 0.8)',
    'rgba(16, 185, 129, 0.8)',
    'rgba(245, 158, 11, 0.8)',
    'rgba(239, 68, 68, 0.8)',
    'rgba(139, 92, 246, 0.8)',
    'rgba(236, 72, 153, 0.8)',
    'rgba(6, 182, 212, 0.8)',
    'rgba(34, 197, 94, 0.8)'
  ]
  
  if (type === 'pie') {
    return colors
  }
  
  return colors[0]
}

// 获取默认边框颜色
const getDefaultBorderColors = (type: string) => {
  const colors = [
    'rgba(59, 130, 246, 1)',
    'rgba(16, 185, 129, 1)',
    'rgba(245, 158, 11, 1)',
    'rgba(239, 68, 68, 1)',
    'rgba(139, 92, 246, 1)',
    'rgba(236, 72, 153, 1)',
    'rgba(6, 182, 212, 1)',
    'rgba(34, 197, 94, 1)'
  ]
  
  if (type === 'pie') {
    return colors
  }
  
  return colors[0]
}

// 创建图表
const createChart = () => {
  if (!chartCanvas.value) return
  
  // 销毁现有图表
  if (chartInstance.value) {
    chartInstance.value.destroy()
  }
  
  const config = getChartConfig()
  chartInstance.value = new Chart(chartCanvas.value, config)
}

// 导出图表
const exportChart = () => {
  if (!chartInstance.value) return
  
  const url = chartInstance.value.toBase64Image()
  const link = document.createElement('a')
  link.download = `${chartTitle.value}.png`
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  emit('export', 'png')
}

// 全屏切换
const toggleFullscreen = () => {
  if (!chartContainer.value) return
  
  if (!isFullscreen.value) {
    if (chartContainer.value.requestFullscreen) {
      chartContainer.value.requestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 全屏状态监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  emit('fullscreen', isFullscreen.value)
  
  // 全屏状态改变时重新调整图表大小
  nextTick(() => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  })
}

// 窗口大小改变时重新调整图表
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    createChart()
  })
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.destroy()
  }
  
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-renderer {
  width: 100%;
}

.chart-container {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-icon {
  color: #6b7280;
  font-size: 24px;
}

.chart-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.chart-title {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.chart-type {
  color: #6b7280;
  font-size: 12px;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-content {
  position: relative;
  height: 400px;
  padding: 16px;
}

.chart-content canvas {
  width: 100% !important;
  height: 100% !important;
}

.chart-description {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.chart-description p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

/* 全屏样式 */
.chart-container:fullscreen {
  border-radius: 0;
}

.chart-container:fullscreen .chart-content {
  height: calc(100vh - 120px);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .chart-content {
    height: 300px;
    padding: 12px;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chart-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
