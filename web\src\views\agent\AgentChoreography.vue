<template>
  <div class="choreography-container">
    <!-- 顶层遮盖层，遮盖Dify顶部菜单 -->
    <div class="dify-header-overlay">
      <div class="overlay-content">
        <div class="agent-info">
          <i class="fas fa-robot"></i>
          <span class="agent-name">{{ agentName }}</span>
          <span class="status-text">智能体编排模式</span>
        </div>
        <div class="overlay-actions">
          <button class="btn-overlay" @click="sendTokenToIframe" title="重新发送Token">
            <i class="fas fa-key"></i>
          </button>
          <button class="btn-overlay" @click="toggleDebugPanel" title="调试信息">
            <i class="fas fa-bug"></i>
          </button>
          <button class="btn-overlay" @click="refreshPage" title="刷新页面">
            <i class="fas fa-refresh"></i>
          </button>
          <button class="btn-overlay" @click="openInNewTab" title="在新标签页中打开Dify">
            <i class="fas fa-external-link-alt"></i>
          </button>
          <button class="btn-overlay" @click="goBack" title="返回智能体管理">
            <i class="fas fa-arrow-left"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Dify页面iframe -->
    <iframe
      ref="difyIframe"
      :src="targetUrl"
      class="dify-iframe"
      frameborder="0"
      @load="onIframeLoad"
    ></iframe>

    <!-- 加载状态 -->
    <div v-if="loading || tokenLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p v-if="tokenLoading">正在获取登录凭证...</p>
        <p v-else>正在加载智能体编排页面...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error || tokenError" class="error-overlay">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>{{ tokenError ? '获取凭证失败' : '加载失败' }}</h3>
        <p>{{ error || tokenError }}</p>
        <div class="error-actions">
          <button class="btn-retry" @click="retryLoad">重试</button>
          <button class="btn-close-error" @click="closePage">关闭</button>
        </div>
      </div>
    </div>

    <!-- 调试面板 -->
    <div v-if="showDebugPanel" class="debug-panel">
      <div class="debug-content">
        <div class="debug-header">
          <h3>调试信息</h3>
          <button class="btn-close-debug" @click="toggleDebugPanel">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="debug-section">
          <h4>URL参数</h4>
          <div class="debug-info">
            <div><strong>目标URL:</strong> {{ targetUrl }}</div>
            <div><strong>智能体名称:</strong> {{ agentName }}</div>
            <div><strong>AccessToken:</strong> {{ accessToken ? accessToken.substring(0, 30) + '...' : '未设置' }}</div>
            <div><strong>RefreshToken:</strong> {{ refreshToken ? refreshToken.substring(0, 30) + '...' : '未设置' }}</div>
          </div>
        </div>

        <div class="debug-section">
          <h4>本地存储状态</h4>
          <div class="debug-info">
            <div v-for="(value, key) in localStorageStatus" :key="key">
              <strong>{{ key }}:</strong>
              <span :class="value ? 'text-green-600' : 'text-red-600'">
                {{ value ? (typeof value === 'string' ? value.substring(0, 30) + '...' : '已设置') : '未设置' }}
              </span>
            </div>
          </div>
        </div>

        <div class="debug-section">
          <h4>操作</h4>
          <div class="debug-actions">
            <button class="btn-debug" @click="sendTokenToIframe">发送Token到iframe</button>
            <button class="btn-debug" @click="refreshLocalStorage">重新设置本地存储</button>
            <button class="btn-debug" @click="clearLocalStorage">清除本地存储</button>
            <button class="btn-debug" @click="checkLocalStorage">检查本地存储</button>
            <button class="btn-debug" @click="injectTokenReceiverScript">重新注入脚本</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { AgentsAPI } from '@/api/agents'

const route = useRoute()
const difyIframe = ref<HTMLIFrameElement | null>(null)
const loading = ref(true)
const error = ref('')
const showDebugPanel = ref(false)
const localStorageStatus = ref<Record<string, string | boolean>>({})

// 从URL参数获取数据
const targetUrl = ref(decodeURIComponent(route.query.targetUrl as string || ''))
const platformId = ref(decodeURIComponent(route.query.platformId as string || ''))
const agentId = ref(decodeURIComponent(route.query.agentId as string || ''))
const agentType = ref(decodeURIComponent(route.query.agentType as string || ''))
const agentName = ref(decodeURIComponent(route.query.agentName as string || '智能体'))

// Token数据（从API获取）
const accessToken = ref('')
const refreshToken = ref('')
const tokenLoading = ref(false)
const tokenError = ref('')

// 获取Dify登录token
const fetchDifyTokens = async () => {
  if (!platformId.value) {
    tokenError.value = '缺少平台ID，无法获取登录凭证'
    console.error('获取token失败: 缺少平台ID')
    return false
  }

  tokenLoading.value = true
  tokenError.value = ''

  try {
    console.log('开始获取Dify登录token，平台ID:', platformId.value)

    const response = await AgentsAPI.getDifyDirectLoginToken(platformId.value)

    if (!response.success || !response.data) {
      tokenError.value = '获取登录凭证失败：' + (response.message || '未知错误')
      console.error('获取token失败:', response.message)
      return false
    }

    accessToken.value = response.data.accessToken
    refreshToken.value = response.data.refreshToken

    console.log('获取Dify登录token成功:', {
      accessToken: accessToken.value.substring(0, 20) + '...',
      refreshToken: refreshToken.value.substring(0, 20) + '...'
    })

    return true

  } catch (error: any) {
    tokenError.value = '获取登录凭证异常：' + (error.message || '未知错误')
    console.error('获取token异常:', error)
    return false
  } finally {
    tokenLoading.value = false
  }
}

// 设置本地存储
const setupLocalStorage = () => {
  try {
    if (!accessToken.value || !refreshToken.value) {
      console.warn('缺少必要的token，无法设置本地存储')
      return
    }

    // 设置Dify控制台token - 只保留基础的两个键名
    localStorage.setItem('console_token', accessToken.value)
    localStorage.setItem('refresh_token', refreshToken.value)

    console.log('已设置Dify本地存储:', {
      console_token: accessToken.value.substring(0, 20) + '...',
      refresh_token: refreshToken.value.substring(0, 20) + '...'
    })

    // 验证存储是否成功
    const storedConsoleToken = localStorage.getItem('console_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')

    if (storedConsoleToken && storedRefreshToken) {
      console.log('本地存储验证成功')
    } else {
      console.warn('本地存储验证失败:', {
        console_token: !!storedConsoleToken,
        refresh_token: !!storedRefreshToken
      })
    }

  } catch (err) {
    console.error('设置本地存储失败:', err)
    error.value = '设置登录凭证失败'
  }
}

// iframe加载完成
const onIframeLoad = () => {
  loading.value = false
  console.log('Dify页面加载完成')

  // 尝试注入token接收脚本
  injectTokenReceiverScript()

  // 延迟发送token信息，确保脚本已加载
  setTimeout(() => {
    sendTokenToIframe()
  }, 500)
}

// 注入token接收脚本到iframe
const injectTokenReceiverScript = () => {
  try {
    if (!difyIframe.value || !difyIframe.value.contentDocument) {
      console.warn('无法访问iframe内容，可能存在跨域限制')
      return
    }

    const iframeDoc = difyIframe.value.contentDocument
    const script = iframeDoc.createElement('script')

    // 从public目录加载脚本内容
    fetch('/dify-token-receiver.js')
      .then(response => response.text())
      .then(scriptContent => {
        script.textContent = scriptContent
        iframeDoc.head.appendChild(script)
        console.log('已注入token接收脚本到iframe')
      })
      .catch(error => {
        console.warn('加载token接收脚本失败，使用内联脚本:', error)

        // 备用方案：直接内联脚本内容
        script.textContent = `
          console.log('[Dify Token Receiver] 内联脚本已加载');

          function handleTokenMessage(event) {
            if (event.data && event.data.type === 'SET_DIFY_TOKENS') {
              console.log('[Dify Token Receiver] 收到token消息');

              const tokens = event.data.data;
              if (tokens && tokens.console_token && tokens.refresh_token) {
                // 只设置基础的两个token
                localStorage.setItem('console_token', tokens.console_token);
                localStorage.setItem('refresh_token', tokens.refresh_token);

                console.log('[Dify Token Receiver] Token已设置到localStorage');

                // 发送确认消息
                if (window.parent) {
                  window.parent.postMessage({
                    type: 'DIFY_TOKENS_SET',
                    source: 'dify-page',
                    timestamp: Date.now()
                  }, '*');
                }
              }
            }
          }

          window.addEventListener('message', handleTokenMessage);

          // 发送页面准备就绪消息
          if (window.parent) {
            window.parent.postMessage({
              type: 'DIFY_PAGE_READY',
              source: 'dify-page',
              timestamp: Date.now()
            }, '*');
          }
        `

        iframeDoc.head.appendChild(script)
        console.log('已注入内联token接收脚本到iframe')
      })

  } catch (error) {
    console.error('注入token接收脚本失败:', error)
    console.log('将使用postMessage直接通信')
  }
}

// 向iframe发送token
const sendTokenToIframe = () => {
  if (!difyIframe.value || !difyIframe.value.contentWindow) {
    console.warn('iframe未准备好，无法发送token')
    return
  }

  const tokenData = {
    type: 'SET_DIFY_TOKENS',
    source: 'xhcai-choreography',
    timestamp: Date.now(),
    data: {
      console_token: accessToken.value,
      refresh_token: refreshToken.value
    }
  }

  try {
    // 发送消息到iframe - 使用通配符，因为可能有端口或路径差异
    difyIframe.value.contentWindow.postMessage(tokenData, '*')
    console.log('已向iframe发送token信息:', {
      type: tokenData.type,
      source: tokenData.source,
      timestamp: tokenData.timestamp,
      tokenCount: Object.keys(tokenData.data).length - 1, // 减去origin字段
      accessTokenPreview: accessToken.value.substring(0, 20) + '...'
    })

    // 多次发送确保成功
    const sendIntervals = [500, 1000, 2000, 3000, 5000]
    sendIntervals.forEach((delay, index) => {
      setTimeout(() => {
        if (difyIframe.value?.contentWindow) {
          difyIframe.value.contentWindow.postMessage(tokenData, '*')
          console.log(`第${index + 2}次发送token信息 (延迟${delay}ms)`)
        }
      }, delay)
    })

  } catch (error) {
    console.error('发送token到iframe失败:', error)
  }
}

// 监听iframe的回复消息
const handleIframeMessage = (event: MessageEvent) => {
  // 安全检查：确保消息来自预期的源
  if (!event.origin.includes('localhost:3001')) {
    return
  }

  console.log('收到iframe消息:', event.data)

  if (event.data.type === 'DIFY_TOKENS_SET') {
    console.log('iframe确认token设置成功:', event.data)
    // 可以在这里更新UI状态
  } else if (event.data.type === 'DIFY_TOKENS_ERROR') {
    console.error('iframe设置token失败:', event.data)
  } else if (event.data.type === 'DIFY_PAGE_READY') {
    console.log('Dify页面准备就绪，重新发送token')
    // 页面准备就绪时重新发送token
    setTimeout(() => sendTokenToIframe(), 100)
  }
}

// 刷新页面
const refreshPage = () => {
  if (difyIframe.value) {
    loading.value = true
    difyIframe.value.src = difyIframe.value.src
  }
}

// 在新标签页中打开
const openInNewTab = () => {
  window.open(targetUrl.value, '_blank')
}

// 关闭页面
const closePage = () => {
  window.close()
}

// 返回智能体管理页面
const goBack = () => {
  // 尝试返回上一页
  if (window.history.length > 1) {
    window.history.back()
  } else {
    // 如果没有历史记录，跳转到智能体管理页面
    window.location.href = '/agents'
  }
}

// 重试加载
const retryLoad = async () => {
  error.value = ''
  tokenError.value = ''
  loading.value = true

  // 重新获取token
  const tokenSuccess = await fetchDifyTokens()

  if (!tokenSuccess) {
    error.value = tokenError.value || '获取登录凭证失败'
    loading.value = false
    return
  }

  // 重新设置本地存储
  setupLocalStorage()

  // 重新加载iframe
  if (difyIframe.value) {
    difyIframe.value.src = targetUrl.value
  }
}

// 切换调试面板
const toggleDebugPanel = () => {
  showDebugPanel.value = !showDebugPanel.value
  if (showDebugPanel.value) {
    checkLocalStorage()
  }
}

// 检查本地存储状态
const checkLocalStorage = () => {
  const keys = ['console_token', 'refresh_token']

  const status: Record<string, string | boolean> = {}
  keys.forEach(key => {
    const value = localStorage.getItem(key)
    status[key] = value || false
  })

  localStorageStatus.value = status

  console.log('本地存储检查结果:', {
    console_token: !!localStorage.getItem('console_token'),
    refresh_token: !!localStorage.getItem('refresh_token')
  })
}

// 重新设置本地存储
const refreshLocalStorage = () => {
  setupLocalStorage()
  checkLocalStorage()
}

// 清除本地存储
const clearLocalStorage = () => {
  localStorage.removeItem('console_token')
  localStorage.removeItem('refresh_token')

  console.log('已清除本地存储')
  checkLocalStorage()
}

// 监听iframe错误
const handleIframeError = () => {
  loading.value = false
  error.value = '无法加载Dify页面，请检查网络连接或联系管理员'
}

onMounted(async () => {
  console.log('智能体编排页面初始化:', {
    targetUrl: targetUrl.value,
    platformId: platformId.value,
    agentId: agentId.value,
    agentType: agentType.value,
    agentName: agentName.value
  })

  // 设置页面标题
  document.title = `智能体编排 - ${agentName.value}`

  // 验证必要参数
  if (!targetUrl.value || !platformId.value) {
    error.value = '缺少必要的参数（targetUrl或platformId），无法加载页面'
    loading.value = false
    return
  }

  // 添加消息监听器
  window.addEventListener('message', handleIframeMessage)
  console.log('已添加postMessage监听器')

  // 获取Dify登录token
  const tokenSuccess = await fetchDifyTokens()

  if (!tokenSuccess) {
    error.value = tokenError.value || '获取登录凭证失败'
    loading.value = false
    return
  }

  // 设置本地存储（作为备用方案）
  setupLocalStorage()

  // 延迟验证本地存储设置
  setTimeout(() => {
    checkLocalStorage()

    // 验证关键token是否设置成功
    const consoleToken = localStorage.getItem('console_token')
    const refreshTokenStored = localStorage.getItem('refresh_token')

    if (!consoleToken || !refreshTokenStored) {
      console.warn('关键token设置可能失败:', {
        console_token: !!consoleToken,
        refresh_token: !!refreshTokenStored
      })

      // 尝试重新设置
      console.log('尝试重新设置本地存储...')
      setupLocalStorage()
    } else {
      console.log('本地存储设置验证成功')
    }
  }, 100)

  // 监听iframe错误
  if (difyIframe.value) {
    difyIframe.value.addEventListener('error', handleIframeError)
  }
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('message', handleIframeMessage)

  if (difyIframe.value) {
    difyIframe.value.removeEventListener('error', handleIframeError)
  }

  console.log('编排页面已清理事件监听器')
})
</script>

<style scoped>
.choreography-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶层遮盖层 - 遮盖Dify顶部菜单 */
.dify-header-overlay {
  position: relative;
  width: 100%;
  min-height: 56px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid var(--color-divider-regular, #e5e7eb);
  z-index: 30;
  display: flex;
  flex-direction: column;
  flex-basis: auto;
  flex-grow: 0;
  flex-shrink: 0;
}

.overlay-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 56px;
  color: white;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-info i {
  font-size: 20px;
  color: #ffd700;
}

.agent-name {
  font-size: 16px;
  font-weight: 600;
}

.status-text {
  font-size: 12px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

.overlay-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-overlay {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-overlay:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-close {
  background: rgba(220, 38, 38, 0.8);
}

.btn-close:hover {
  background: rgba(220, 38, 38, 1);
}

/* Dify页面iframe */
.dify-iframe {
  width: 100%;
  flex: 1;
  border: none;
  background: white;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 40;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 40;
}

.error-content {
  text-align: center;
  color: #666;
  max-width: 400px;
  padding: 40px;
}

.error-content i {
  font-size: 48px;
  color: #f56565;
  margin-bottom: 16px;
}

.error-content h3 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 12px;
}

.error-content p {
  margin-bottom: 24px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn-retry,
.btn-close-error {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-retry {
  background: #667eea;
  color: white;
}

.btn-retry:hover {
  background: #5a67d8;
}

.btn-close-error {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-close-error:hover {
  background: #cbd5e0;
}

/* 调试面板 */
.debug-panel {
  position: absolute;
  top: 56px;
  right: 0;
  width: 400px;
  height: calc(100% - 56px);
  background: white;
  border-left: 1px solid #e5e7eb;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 50;
  overflow-y: auto;
}

.debug-content {
  padding: 16px;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.debug-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.btn-close-debug {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.btn-close-debug:hover {
  background: #f3f4f6;
  color: #374151;
}

.debug-section {
  margin-bottom: 16px;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.debug-info {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.5;
}

.debug-info > div {
  margin-bottom: 4px;
}

.debug-info > div:last-child {
  margin-bottom: 0;
}

.debug-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.btn-debug {
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-debug:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.text-green-600 {
  color: #059669;
}

.text-red-600 {
  color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overlay-content {
    padding: 0 12px;
  }

  .agent-info {
    gap: 8px;
  }

  .agent-name {
    font-size: 14px;
  }

  .status-text {
    display: none;
  }

  .btn-overlay {
    width: 32px;
    height: 32px;
  }

  .debug-panel {
    width: 100%;
    right: 0;
  }
}
</style>
