<template>
  <BaseConfig
    node-type="mysql"
    v-model="localConfig"
    title="MySQL数据库配置"
    @validate="handleValidate"
    @reset="handleReset"
    @change="handleChange"
  >
    <template #content>
      <div class="mysql-config">
        <div class="config-description">
          <p>配置MySQL数据库连接信息和SQL操作。</p>
        </div>

        <div class="config-form">
          <!-- 连接信息 -->
          <div class="config-section">
            <h4 class="section-title">连接信息</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="mysql-host" class="form-label">主机地址 *</label>
                <input
                  id="mysql-host"
                  v-model="localConfig.host"
                  type="text"
                  placeholder="localhost"
                  class="form-input"
                  required
                  @input="updateConfig"
                />
              </div>
              
              <div class="form-group">
                <label for="mysql-port" class="form-label">端口</label>
                <input
                  id="mysql-port"
                  v-model.number="localConfig.port"
                  type="number"
                  placeholder="3306"
                  class="form-input"
                  min="1"
                  max="65535"
                  @input="updateConfig"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="mysql-database" class="form-label">数据库名 *</label>
                <input
                  id="mysql-database"
                  v-model="localConfig.database"
                  type="text"
                  placeholder="请输入数据库名"
                  class="form-input"
                  required
                  @input="updateConfig"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="mysql-username" class="form-label">用户名</label>
                <input
                  id="mysql-username"
                  v-model="localConfig.username"
                  type="text"
                  placeholder="请输入用户名"
                  class="form-input"
                  @input="updateConfig"
                />
              </div>
              
              <div class="form-group">
                <label for="mysql-password" class="form-label">密码</label>
                <input
                  id="mysql-password"
                  v-model="localConfig.password"
                  type="password"
                  placeholder="请输入密码"
                  class="form-input"
                  @input="updateConfig"
                />
              </div>
            </div>
          </div>

          <!-- SQL操作 -->
          <div class="config-section">
            <h4 class="section-title">SQL操作</h4>
            
            <div class="form-group">
              <label for="mysql-operation" class="form-label">操作类型</label>
              <select
                id="mysql-operation"
                v-model="localConfig.operation"
                class="form-select"
                @change="updateConfig"
              >
                <option value="select">查询 (SELECT)</option>
                <option value="insert">插入 (INSERT)</option>
                <option value="update">更新 (UPDATE)</option>
                <option value="delete">删除 (DELETE)</option>
                <option value="custom">自定义</option>
              </select>
            </div>

            <div class="form-group">
              <label for="mysql-sql" class="form-label">SQL语句 *</label>
              <div class="sql-editor">
                <textarea
                  id="mysql-sql"
                  v-model="localConfig.sql"
                  placeholder="请输入SQL语句"
                  class="form-textarea sql-textarea"
                  rows="6"
                  required
                  @input="updateConfig"
                ></textarea>
                <div class="sql-actions">
                  <button @click="formatSQL" class="btn-format" title="格式化SQL">
                    <i class="fa-solid fa-code"></i>
                    格式化
                  </button>
                  <button @click="validateSQL" class="btn-validate" title="验证SQL">
                    <i class="fa-solid fa-check"></i>
                    验证
                  </button>
                </div>
              </div>
            </div>

            <!-- SQL模板 -->
            <div class="form-group">
              <label class="form-label">SQL模板</label>
              <div class="sql-templates">
                <button
                  v-for="template in sqlTemplates"
                  :key="template.name"
                  @click="applySQLTemplate(template)"
                  class="btn-template"
                  :title="template.description"
                >
                  {{ template.name }}
                </button>
              </div>
            </div>
          </div>

          <!-- 高级选项 -->
          <div class="config-section">
            <h4 class="section-title">高级选项</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="mysql-timeout" class="form-label">超时时间 (秒)</label>
                <input
                  id="mysql-timeout"
                  v-model.number="localConfig.timeout"
                  type="number"
                  placeholder="30"
                  class="form-input"
                  min="1"
                  max="300"
                  @input="updateConfig"
                />
              </div>
              
              <div class="form-group">
                <label for="mysql-charset" class="form-label">字符集</label>
                <select
                  id="mysql-charset"
                  v-model="localConfig.charset"
                  class="form-select"
                  @change="updateConfig"
                >
                  <option value="utf8mb4">utf8mb4</option>
                  <option value="utf8">utf8</option>
                  <option value="latin1">latin1</option>
                  <option value="gbk">gbk</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  v-model="localConfig.enableSSL"
                  @change="updateConfig"
                />
                启用SSL连接
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  v-model="localConfig.enableTransaction"
                  @change="updateConfig"
                />
                启用事务
              </label>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseConfig>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import BaseConfig from '../BaseConfig.vue'

// Props
interface MySQLConfigProps {
  modelValue: {
    host?: string
    port?: number
    database?: string
    username?: string
    password?: string
    operation?: string
    sql?: string
    timeout?: number
    charset?: string
    enableSSL?: boolean
    enableTransaction?: boolean
  }
}

const props = defineProps<MySQLConfigProps>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: MySQLConfigProps['modelValue']]
  'validate': [isValid: boolean, errors: string[]]
}>()

// 响应式数据
const localConfig = ref({
  host: 'localhost',
  port: 3306,
  database: '',
  username: '',
  password: '',
  operation: 'select',
  sql: 'SELECT * FROM table_name',
  timeout: 30,
  charset: 'utf8mb4',
  enableSSL: false,
  enableTransaction: false,
  ...props.modelValue
})

// SQL模板
const sqlTemplates = [
  {
    name: '查询所有',
    sql: 'SELECT * FROM table_name',
    description: '查询表中所有数据'
  },
  {
    name: '条件查询',
    sql: 'SELECT * FROM table_name WHERE column_name = ?',
    description: '根据条件查询数据'
  },
  {
    name: '插入数据',
    sql: 'INSERT INTO table_name (column1, column2) VALUES (?, ?)',
    description: '插入新数据'
  },
  {
    name: '更新数据',
    sql: 'UPDATE table_name SET column1 = ? WHERE id = ?',
    description: '更新现有数据'
  },
  {
    name: '删除数据',
    sql: 'DELETE FROM table_name WHERE id = ?',
    description: '删除指定数据'
  }
]

// 方法
const updateConfig = () => {
  emit('update:modelValue', { ...localConfig.value })
}

const formatSQL = () => {
  // 简单的SQL格式化
  let sql = localConfig.value.sql || ''
  sql = sql.replace(/\s+/g, ' ').trim()
  sql = sql.replace(/\b(SELECT|FROM|WHERE|JOIN|ORDER BY|GROUP BY|HAVING|INSERT|UPDATE|DELETE|VALUES)\b/gi, '\n$1')
  sql = sql.replace(/,/g, ',\n  ')
  localConfig.value.sql = sql.trim()
  updateConfig()
}

const validateSQL = () => {
  const sql = localConfig.value.sql?.trim()
  if (!sql) {
    alert('SQL语句不能为空')
    return false
  }
  
  // 简单的SQL验证
  const keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER']
  const hasKeyword = keywords.some(keyword => 
    sql.toUpperCase().includes(keyword)
  )
  
  if (!hasKeyword) {
    alert('SQL语句格式不正确')
    return false
  }
  
  alert('SQL语句验证通过')
  return true
}

const applySQLTemplate = (template: typeof sqlTemplates[0]) => {
  localConfig.value.sql = template.sql
  updateConfig()
}

const handleValidate = (isValid: boolean, errors: string[]) => {
  emit('validate', isValid, errors)
}

const handleReset = () => {
  localConfig.value = {
    host: 'localhost',
    port: 3306,
    database: '',
    username: '',
    password: '',
    operation: 'select',
    sql: 'SELECT * FROM table_name',
    timeout: 30,
    charset: 'utf8mb4',
    enableSSL: false,
    enableTransaction: false
  }
  updateConfig()
}

const handleChange = (key: string, value: any) => {
  console.log(`MySQL config changed: ${key} = ${value}`)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  localConfig.value = { ...localConfig.value, ...newValue }
}, { deep: true })
</script>

<style scoped>
.mysql-config {
  min-height: 400px;
}

.config-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.config-section:last-child {
  border-bottom: none;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-row .form-group {
  flex: 1;
}

.sql-editor {
  position: relative;
}

.sql-textarea {
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.sql-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.btn-format,
.btn-validate {
  padding: 6px 12px;
  border: 1px solid #6c757d;
  background: #ffffff;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
}

.btn-format:hover,
.btn-validate:hover {
  background: #6c757d;
  color: #ffffff;
}

.sql-templates {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.btn-template {
  padding: 6px 12px;
  border: 1px solid #007bff;
  background: #ffffff;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.btn-template:hover {
  background: #007bff;
  color: #ffffff;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}
</style>
