<template>
  <div class="agent-monitor space-y-6">
    <!-- 智能体概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-robot text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总智能体数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ agentStats.total }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-chart-line text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日请求总量</p>
            <p class="text-2xl font-semibold text-gray-900">{{ agentStats.todayRequests.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-comments text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日消息总量</p>
            <p class="text-2xl font-semibold text-gray-900">{{ agentStats.todayMessages.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-heart text-red-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日点赞总量</p>
            <p class="text-2xl font-semibold text-gray-900">{{ agentStats.todayLikes.toLocaleString() }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体监控表格 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">智能体监控详情</h3>
          <div class="flex items-center gap-4">
            <select v-model="agentTypeFilter" class="form-select text-sm">
              <option value="">全部类型</option>
              <option value="聊天助手">聊天助手</option>
              <option value="Agent">Agent</option>
              <option value="文本生成">文本生成</option>
              <option value="ChatFlow">ChatFlow</option>
              <option value="工作流">工作流</option>
            </select>
            <input
              v-model="agentSearchQuery"
              type="text"
              placeholder="搜索智能体..."
              class="form-input text-sm w-64"
            />
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">智能体信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">请求量</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">消息量</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点赞量</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模型性能</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="agent in paginatedAgents" :key="agent.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center">
                      <i class="fas fa-robot text-white"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ agent.name }}</div>
                    <div class="text-sm text-gray-500">{{ agent.unit }} · {{ agent.creator }}</div>
                    <div class="text-xs text-gray-400">
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                        {{ agent.type }}
                      </span>
                      {{ formatDate(agent.createdAt) }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">总量: {{ agent.totalRequests.toLocaleString() }}</div>
                <div class="text-sm text-gray-500">今日: {{ agent.todayRequests.toLocaleString() }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="['ChatFlow', '聊天助手', 'Agent'].includes(agent.type)">
                  <div class="text-sm text-gray-900">总量: {{ agent.totalMessages?.toLocaleString() || 0 }}</div>
                  <div class="text-sm text-gray-500">今日: {{ agent.todayMessages?.toLocaleString() || 0 }}</div>
                </div>
                <div v-else class="text-sm text-gray-400">-</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="['ChatFlow', '聊天助手', 'Agent'].includes(agent.type)">
                  <div class="text-sm text-gray-900">总量: {{ agent.totalLikes?.toLocaleString() || 0 }}</div>
                  <div class="text-sm text-gray-500">今日: {{ agent.todayLikes?.toLocaleString() || 0 }}</div>
                </div>
                <div v-else class="text-sm text-gray-400">-</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">响应: {{ agent.avgResponseTime }}ms</div>
                <div class="text-sm text-gray-500">速率: {{ agent.avgTokenSpeed }} token/s</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(agent.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusText(agent.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 智能体分页 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredAgents.length) }} 条，
            共 {{ filteredAgents.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="text-sm text-gray-700">
              第 {{ currentPage }} / {{ totalPages }} 页
            </span>
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  refreshTrigger: 0
})

// 搜索和过滤
const agentSearchQuery = ref('')
const agentTypeFilter = ref('')

// 分页配置
const pageSize = ref(10)
const currentPage = ref(1)

// 智能体统计数据
const agentStats = ref({
  total: 156,
  todayRequests: 12450,
  todayMessages: 8920,
  todayLikes: 1230
})

// 智能体监控数据
const agents = ref([
  {
    id: 1,
    name: '智能客服助手',
    unit: '技术部',
    creator: '张三',
    type: '聊天助手',
    createdAt: '2024-01-15',
    updatedAt: '2024-06-20',
    totalRequests: 15420,
    todayRequests: 234,
    totalMessages: 8920,
    todayMessages: 156,
    totalLikes: 1230,
    todayLikes: 23,
    avgResponseTime: 120,
    avgTokenSpeed: 85,
    status: 'healthy'
  },
  {
    id: 2,
    name: '代码生成器',
    unit: '研发部',
    creator: '李四',
    type: 'Agent',
    createdAt: '2024-02-10',
    updatedAt: '2024-06-18',
    totalRequests: 8920,
    todayRequests: 145,
    totalMessages: 5670,
    todayMessages: 89,
    totalLikes: 890,
    todayLikes: 15,
    avgResponseTime: 180,
    avgTokenSpeed: 92,
    status: 'healthy'
  },
  {
    id: 3,
    name: '文档摘要生成',
    unit: '产品部',
    creator: '王五',
    type: '文本生成',
    createdAt: '2024-03-05',
    updatedAt: '2024-06-15',
    totalRequests: 5670,
    todayRequests: 89,
    avgResponseTime: 95,
    avgTokenSpeed: 110,
    status: 'healthy'
  },
  {
    id: 4,
    name: '业务流程助手',
    unit: '运营部',
    creator: '赵六',
    type: 'ChatFlow',
    createdAt: '2024-04-12',
    updatedAt: '2024-06-22',
    totalRequests: 3450,
    todayRequests: 67,
    totalMessages: 2340,
    todayMessages: 45,
    totalLikes: 456,
    todayLikes: 8,
    avgResponseTime: 210,
    avgTokenSpeed: 78,
    status: 'warning'
  },
  {
    id: 5,
    name: '数据处理工作流',
    unit: '数据部',
    creator: '孙七',
    type: '工作流',
    createdAt: '2024-05-08',
    updatedAt: '2024-06-19',
    totalRequests: 2340,
    todayRequests: 34,
    avgResponseTime: 340,
    avgTokenSpeed: 65,
    status: 'error'
  }
])

// 计算属性：过滤后的数据
const filteredAgents = computed(() => {
  let filtered = agents.value

  if (agentTypeFilter.value) {
    filtered = filtered.filter(agent => agent.type === agentTypeFilter.value)
  }

  if (agentSearchQuery.value) {
    const query = agentSearchQuery.value.toLowerCase()
    filtered = filtered.filter(agent =>
      agent.name.toLowerCase().includes(query) ||
      agent.unit.toLowerCase().includes(query) ||
      agent.creator.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 分页后的数据
const paginatedAgents = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredAgents.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredAgents.value.length / pageSize.value)
})

// 方法
const updateAgentStats = () => {
  agents.value.forEach(agent => {
    agent.todayRequests = Math.floor(Math.random() * 300) + 50
    if (['ChatFlow', '聊天助手', 'Agent'].includes(agent.type)) {
      agent.todayMessages = Math.floor(Math.random() * 200) + 30
      agent.todayLikes = Math.floor(Math.random() * 50) + 5
    }
    agent.avgResponseTime = Math.floor(Math.random() * 400) + 80
    agent.avgTokenSpeed = Math.floor(Math.random() * 60) + 60

    // 更新状态
    if (agent.avgResponseTime > 300) {
      agent.status = 'warning'
    } else if (agent.avgResponseTime > 400) {
      agent.status = 'error'
    } else {
      agent.status = 'healthy'
    }
  })

  // 更新统计数据
  agentStats.value.todayRequests = agents.value.reduce((sum, agent) => sum + agent.todayRequests, 0)
  agentStats.value.todayMessages = agents.value.reduce((sum, agent) => sum + (agent.todayMessages || 0), 0)
  agentStats.value.todayLikes = agents.value.reduce((sum, agent) => sum + (agent.todayLikes || 0), 0)
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return statusMap[status] || status
}

// 分页方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const resetPagination = () => {
  currentPage.value = 1
}

// 时间格式化方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 监听搜索和过滤条件变化，重置分页
watch([agentSearchQuery, agentTypeFilter], () => {
  resetPagination()
})

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  updateAgentStats()
})

onMounted(() => {
  updateAgentStats()
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-input, .form-select {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  background-color: #f8fafc;
  transform: scale(1.01);
}

/* 渐变背景 */
.from-blue-400 {
  --tw-gradient-from: #60a5fa;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0));
}

.to-blue-600 {
  --tw-gradient-to: #2563eb;
}
</style>
