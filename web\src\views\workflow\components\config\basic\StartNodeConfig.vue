<template>
  <BaseConfig
    node-type="start"
    v-model="localConfig"
    title="开始节点配置"
    @validate="handleValidate"
    @reset="handleReset"
    @change="handleChange"
  >
    <template #content>
      <div class="start-config">
        <div class="config-description">
          <p>开始节点是工作流的入口点，每个工作流必须有且仅有一个开始节点。</p>
        </div>

        <div class="config-form">
          <!-- 节点标签 -->
          <div class="form-group">
            <label for="start-label" class="form-label">节点标签</label>
            <input
              id="start-label"
              v-model="localConfig.label"
              type="text"
              placeholder="请输入节点显示名称"
              class="form-input"
              @input="updateConfig"
            />
          </div>

          <!-- 节点描述 -->
          <div class="form-group">
            <label for="start-description" class="form-label">节点描述</label>
            <textarea
              id="start-description"
              v-model="localConfig.description"
              placeholder="请输入节点描述信息"
              class="form-textarea"
              rows="3"
              @input="updateConfig"
            ></textarea>
          </div>

          <!-- 初始数据 -->
          <div class="form-group">
            <label class="form-label">初始数据</label>
            <div class="initial-data-config">
              <div class="data-item" v-for="(value, key) in localConfig.initialData" :key="key">
                <input
                  v-model="key"
                  type="text"
                  placeholder="数据键"
                  class="form-input data-key"
                  @input="updateInitialData"
                />
                <input
                  v-model="localConfig.initialData[key]"
                  type="text"
                  placeholder="数据值"
                  class="form-input data-value"
                  @input="updateConfig"
                />
                <button @click="removeDataItem(key)" class="btn-remove" title="删除">
                  <i class="fa-solid fa-times"></i>
                </button>
              </div>
              <button @click="addDataItem" class="btn-add">
                <i class="fa-solid fa-plus"></i>
                添加数据项
              </button>
            </div>
          </div>

          <!-- 触发条件 -->
          <div class="form-group">
            <label class="form-label">触发条件</label>
            <div class="trigger-config">
              <div class="form-row">
                <select v-model="localConfig.triggerType" class="form-select" @change="updateConfig">
                  <option value="manual">手动触发</option>
                  <option value="schedule">定时触发</option>
                  <option value="event">事件触发</option>
                  <option value="webhook">Webhook触发</option>
                </select>
              </div>
              
              <!-- 定时触发配置 -->
              <div v-if="localConfig.triggerType === 'schedule'" class="schedule-config">
                <input
                  v-model="localConfig.scheduleExpression"
                  type="text"
                  placeholder="Cron表达式，如：0 0 9 * * ?"
                  class="form-input"
                  @input="updateConfig"
                />
                <small class="form-help">使用Cron表达式定义执行时间</small>
              </div>

              <!-- 事件触发配置 -->
              <div v-if="localConfig.triggerType === 'event'" class="event-config">
                <input
                  v-model="localConfig.eventName"
                  type="text"
                  placeholder="事件名称"
                  class="form-input"
                  @input="updateConfig"
                />
              </div>

              <!-- Webhook触发配置 -->
              <div v-if="localConfig.triggerType === 'webhook'" class="webhook-config">
                <input
                  v-model="localConfig.webhookPath"
                  type="text"
                  placeholder="Webhook路径"
                  class="form-input"
                  @input="updateConfig"
                />
                <div class="webhook-methods">
                  <label class="checkbox-label" v-for="method in ['GET', 'POST', 'PUT']" :key="method">
                    <input
                      type="checkbox"
                      :value="method"
                      :checked="localConfig.allowedMethods?.includes(method)"
                      @change="updateAllowedMethods(method, $event)"
                    >
                    {{ method }}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseConfig>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import BaseConfig from '../BaseConfig.vue'

// Props
interface StartNodeConfigProps {
  modelValue: {
    label?: string
    description?: string
    initialData?: Record<string, any>
    triggerType?: 'manual' | 'schedule' | 'event' | 'webhook'
    scheduleExpression?: string
    eventName?: string
    webhookPath?: string
    allowedMethods?: string[]
  }
}

const props = defineProps<StartNodeConfigProps>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: StartNodeConfigProps['modelValue']]
  'validate': [isValid: boolean, errors: string[]]
}>()

// 响应式数据
const localConfig = ref({
  label: '开始',
  description: '',
  initialData: {},
  triggerType: 'manual' as const,
  scheduleExpression: '',
  eventName: '',
  webhookPath: '',
  allowedMethods: ['POST'],
  ...props.modelValue
})

// 方法
const updateConfig = () => {
  emit('update:modelValue', { ...localConfig.value })
}

const addDataItem = () => {
  const key = `data_${Object.keys(localConfig.value.initialData).length + 1}`
  localConfig.value.initialData[key] = ''
  updateConfig()
}

const removeDataItem = (key: string) => {
  delete localConfig.value.initialData[key]
  updateConfig()
}

const updateInitialData = () => {
  // 重新构建初始数据对象以确保响应性
  const newData: Record<string, any> = {}
  Object.entries(localConfig.value.initialData).forEach(([key, value]) => {
    if (key.trim()) {
      newData[key.trim()] = value
    }
  })
  localConfig.value.initialData = newData
  updateConfig()
}

const handleValidate = (isValid: boolean, errors: string[]) => {
  emit('validate', isValid, errors)
}

const handleReset = () => {
  localConfig.value = {
    label: '开始',
    description: '',
    initialData: {},
    triggerType: 'manual',
    scheduleExpression: '',
    eventName: '',
    webhookPath: '',
    allowedMethods: ['POST']
  }
  updateConfig()
}

const handleChange = (key: string, value: any) => {
  console.log(`Start node config changed: ${key} = ${value}`)
}

const updateAllowedMethods = (method: string, event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.checked) {
    if (!localConfig.value.allowedMethods?.includes(method)) {
      localConfig.value.allowedMethods = [...(localConfig.value.allowedMethods || []), method]
    }
  } else {
    localConfig.value.allowedMethods = localConfig.value.allowedMethods?.filter(m => m !== method) || []
  }
  updateConfig()
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  localConfig.value = { ...localConfig.value, ...newValue }
}, { deep: true })
</script>

<style scoped>
.start-config {
  min-height: 200px;
}

.config-description p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.form-textarea {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.initial-data-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.data-key {
  flex: 1;
}

.data-value {
  flex: 2;
}

.btn-remove {
  padding: 6px 8px;
  border: 1px solid #dc3545;
  background: #ffffff;
  color: #dc3545;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-remove:hover {
  background: #dc3545;
  color: #ffffff;
}

.btn-add {
  padding: 8px 12px;
  border: 1px solid #28a745;
  background: #ffffff;
  color: #28a745;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  align-self: flex-start;
}

.btn-add:hover {
  background: #28a745;
  color: #ffffff;
}

.trigger-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-row {
  display: flex;
  gap: 12px;
}

.form-select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background: #ffffff;
  cursor: pointer;
}

.form-help {
  color: #6c757d;
  font-size: 12px;
  margin-top: 4px;
}

.webhook-methods {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}
</style>
