package com.xhcai.modules.system.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.utils.ApplicationContextHolder;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.SysUserQueryDTO;
import com.xhcai.modules.system.dto.UserProfileUpdateDTO;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.entity.SysUserRole;
import com.xhcai.modules.system.mapper.SysUserMapper;
import com.xhcai.modules.system.mapper.SysUserRoleMapper;
import com.xhcai.modules.system.service.ISysUserService;
import com.xhcai.modules.system.service.ISystemQueryService;
import com.xhcai.modules.system.utils.DictUtils;
import com.xhcai.modules.system.vo.SysUserVO;
import com.xhcai.modules.system.vo.UserProfileVO;

/**
 * 用户信息服务实现类 使用主数据源处理用户相关数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private ISystemQueryService systemQueryService;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public PageResult<SysUserVO> selectUserPage(SysUserQueryDTO queryDTO) {
        Page<SysUser> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<SysUser> userPage = userMapper.selectUserPage(
                page,
                queryDTO.getUsername(),
                queryDTO.getNickname(),
                queryDTO.getEmail(),
                queryDTO.getPhone(),
                queryDTO.getStatus(),
                queryDTO.getDeptId(),
                queryDTO.getTenantId(),
                queryDTO.getDataScope()
        );

        List<SysUserVO> userVOList = userPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<>(userVOList, userPage.getTotal(), userPage.getCurrent(), userPage.getSize());
    }

    @Override
    public SysUser selectByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        return userMapper.selectByUsername(username);
    }

    @Override
    public SysUser selectByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return null;
        }
        return userMapper.selectByEmail(email);
    }

    @Override
    public SysUser selectByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return null;
        }
        return userMapper.selectByPhone(phone);
    }

    @Override
    public Set<String> selectUserPermissions(SysUserVO sysUserVO) {
        if (sysUserVO == null) {
            return new HashSet<>();
        }
        return userMapper.selectUserPermissions(sysUserVO.getId(), sysUserVO.getTenantId());
    }

    @Override
    public Set<String> selectUserRoles(SysUserVO sysUserVO) {
        if (sysUserVO == null) {
            return new HashSet<>();
        }
        return userMapper.selectUserRoles(sysUserVO.getId(), sysUserVO.getTenantId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertUser(SysUser user) {
        // 参数校验
        validateUser(user, true);

        // 检查用户名是否已存在
        if (existsUsername(user.getUsername(), null)) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && existsEmail(user.getEmail(), null)) {
            throw new BusinessException("邮箱已存在");
        }

        // 检查手机号是否已存在
        if (StringUtils.hasText(user.getPhone()) && existsPhone(user.getPhone(), null)) {
            throw new BusinessException("手机号已存在");
        }

        // 设置默认值
        if (!StringUtils.hasText(user.getPassword())) {
            user.setPassword(CommonConstants.DEFAULT_PASSWORD);
        }

        // 密码加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // 设置默认状态
        if (!StringUtils.hasText(user.getStatus())) {
            user.setStatus(CommonConstants.STATUS_NORMAL);
        }

        // 设置租户ID
        user.setTenantId(SecurityUtils.getCurrentTenantId());

        return save(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUser user) {
        if (user.getId() == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 获取原用户信息
        SysUser existUser = getById(user.getId());
        if (existUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 参数校验
        validateUser(user, false);

        // 检查用户名是否已存在（排除自己）
        if (StringUtils.hasText(user.getUsername()) && existsUsername(user.getUsername(), user.getId())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在（排除自己）
        if (StringUtils.hasText(user.getEmail()) && existsEmail(user.getEmail(), user.getId())) {
            throw new BusinessException("邮箱已存在");
        }

        // 检查手机号是否已存在（排除自己）
        if (StringUtils.hasText(user.getPhone()) && existsPhone(user.getPhone(), user.getId())) {
            throw new BusinessException("手机号已存在");
        }

        // 如果修改了密码，需要重新加密
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        } else {
            // 不修改密码时，保持原密码
            user.setPassword(existUser.getPassword());
        }

        boolean result = updateById(user);

        // 如果更新成功，清除用户缓存以确保下次获取最新信息
        if (result) {
            clearUserCache(user.getId());
            log.debug("用户信息更新成功，已清除缓存: userId={}", user.getId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUsers(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }

        String tenantId = SecurityUtils.getCurrentTenantId();

        // 删除用户角色关联
        for (String userId : userIds) {
            userRoleMapper.deleteByUserId(userId, tenantId);
        }

        // 逻辑删除用户
        return removeByIds(userIds);
    }

    @Override
    public boolean existsUsername(String username, String userId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        String tenantId = SecurityUtils.getCurrentTenantId();
        return userMapper.existsUsername(username, userId, tenantId);
    }

    @Override
    public boolean existsEmail(String email, String userId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        String tenantId = SecurityUtils.getCurrentTenantId();
        return userMapper.existsEmail(email, userId, tenantId);
    }

    @Override
    public boolean existsPhone(String phone, String userId) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        String tenantId = SecurityUtils.getCurrentTenantId();
        return userMapper.existsPhone(phone, userId, tenantId);
    }

    @Override
    public void updateLoginInfo(String userId, String loginIp) {
        if (userId == null) {
            return;
        }
        String loginTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern(CommonConstants.NORM_DATETIME_PATTERN));
        userMapper.updateLoginInfo(userId, loginIp, loginTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(String userId, String newPassword) {
        if (userId == null || !StringUtils.hasText(newPassword)) {
            throw new BusinessException("参数不能为空");
        }

        SysUser user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        String encodedPassword = passwordEncoder.encode(newPassword);
        userMapper.resetPassword(userId, encodedPassword);
        return true;
    }

    /**
     * 参数校验
     */
    private void validateUser(SysUser user, boolean isInsert) {
        if (user == null) {
            throw new BusinessException("用户信息不能为空");
        }

        if (isInsert && !StringUtils.hasText(user.getUsername())) {
            throw new BusinessException("用户名不能为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(String userId, String oldPassword, String newPassword) {
        if (userId == null || !StringUtils.hasText(oldPassword) || !StringUtils.hasText(newPassword)) {
            throw new BusinessException("参数不能为空");
        }

        SysUser user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("原密码错误");
        }

        String encodedPassword = passwordEncoder.encode(newPassword);
        userMapper.resetPassword(userId, encodedPassword);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(String userId, String status) {
        if (userId == null || !StringUtils.hasText(status)) {
            throw new BusinessException("参数不能为空");
        }

        SysUser user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        user.setStatus(status);
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(String userId, List<String> roleIds) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        String tenantId = SecurityUtils.getCurrentTenantId();
        String currentUserId = SecurityUtils.getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        // 删除原有角色关联
        userRoleMapper.deleteByUserId(userId, tenantId);

        // 添加新的角色关联
        if (!CollectionUtils.isEmpty(roleIds)) {
            // 使用MyBatis Plus的insert方法，会自动触发MetaObjectHandler
            for (String roleId : roleIds) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRoleMapper.insert(userRole);
            }
        }

        return true;
    }

    @Override
    public List<SysUser> selectUsersByRoleId(String roleId) {
        if (roleId == null) {
            return new ArrayList<>();
        }
        String tenantId = SecurityUtils.getCurrentTenantId();
        return userMapper.selectUsersByRoleId(roleId, tenantId);
    }

    @Override
    public List<SysUser> selectUsersByDeptId(String deptId) {
        if (deptId == null) {
            return new ArrayList<>();
        }
        String tenantId = SecurityUtils.getCurrentTenantId();
        return userMapper.selectUsersByDeptId(deptId, tenantId);
    }

    @Override
    public String importUsers(List<SysUser> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return "导入数据为空";
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMsg = new StringBuilder();

        for (SysUser user : userList) {
            try {
                // 设置默认值
                if (!StringUtils.hasText(user.getPassword())) {
                    user.setPassword(CommonConstants.DEFAULT_PASSWORD);
                }
                if (!StringUtils.hasText(user.getStatus())) {
                    user.setStatus(CommonConstants.STATUS_NORMAL);
                }

                insertUser(user);
                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMsg.append("用户[").append(user.getUsername()).append("]导入失败：").append(e.getMessage()).append("; ");
                log.error("导入用户失败: {}", e.getMessage(), e);
            }
        }

        return String.format("导入完成，成功%d条，失败%d条。%s", successCount, failCount, errorMsg.toString());
    }

    @Override
    public List<SysUserVO> exportUsers(SysUserQueryDTO queryDTO) {
        // 设置大页面查询所有数据
        Page<SysUser> page = new Page<>(1, 10000);
        IPage<SysUser> userPage = userMapper.selectUserPage(
                page,
                queryDTO.getUsername(),
                queryDTO.getNickname(),
                queryDTO.getEmail(),
                queryDTO.getPhone(),
                queryDTO.getStatus(),
                queryDTO.getDeptId(),
                queryDTO.getTenantId(),
                queryDTO.getDataScope()
        );

        return userPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignUserRoles(String userId, List<String> roleIds, String tenantId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 先删除用户原有角色
        userRoleMapper.deleteByUserId(userId, tenantId);

        // 分配新角色
        if (!CollectionUtils.isEmpty(roleIds)) {
            // 使用MyBatis Plus的insert方法，会自动触发MetaObjectHandler
            for (String roleId : roleIds) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRoleMapper.insert(userRole);
            }
            return true;
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> userIds, String status) {
        if (CollectionUtils.isEmpty(userIds) || !StringUtils.hasText(status)) {
            return false;
        }

        return lambdaUpdate()
                .in(SysUser::getId, userIds)
                .set(SysUser::getStatus, status)
                .update();
    }

    /**
     * 转换为VO对象
     */
    private SysUserVO convertToVO(SysUser user) {
        if (user == null) {
            return null;
        }

        SysUserVO userVO = new SysUserVO();
        BeanUtils.copyProperties(user, userVO);

        // 设置性别描述 - 使用字典工具类
        if (StringUtils.hasText(user.getGender())) {
            userVO.setGenderName(DictUtils.getUserGenderLabel(user.getGender()));
        }

        // 设置状态描述 - 使用字典工具类
        if (StringUtils.hasText(user.getStatus())) {
            userVO.setStatusName(DictUtils.getUserStatusLabel(user.getStatus()));
        }

        // 设置部门名称 - 使用系统查询服务
        userVO.setDeptName(systemQueryService.getDeptNameById(user.getDeptId()));

        return userVO;
    }

    /**
     * 清除用户缓存
     *
     * @param userId 用户ID
     */
    private void clearUserCache(String userId) {
        try {
            Class<?> userCacheServiceClass = Class.forName("com.xhcai.common.security.service.UserCacheService");
            Object userCacheService = ApplicationContextHolder.getBean(userCacheServiceClass);
            userCacheServiceClass.getMethod("clearUserCache", String.class).invoke(userCacheService, userId);
            log.debug("用户缓存已清除: userId={}", userId);
        } catch (Exception e) {
            log.warn("清除用户缓存失败: userId={}, error={}", userId, e.getMessage());
        }
    }

    // ==================== 个人信息相关方法实现 ====================
    @Override
    public UserProfileVO getCurrentUserProfile() {
        String currentUserId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(currentUserId)) {
            throw new BusinessException("用户未登录");
        }

        SysUser user = getById(currentUserId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        UserProfileVO profile = new UserProfileVO();
        BeanUtils.copyProperties(user, profile);

        // 设置性别文本
        if (StringUtils.hasText(user.getGender())) {
            switch (user.getGender()) {
                case "1":
                    profile.setGenderText("男");
                    break;
                case "2":
                    profile.setGenderText("女");
                    break;
                default:
                    profile.setGenderText("未知");
                    break;
            }
        } else {
            profile.setGenderText("未知");
        }

        // 设置状态文本
        if (StringUtils.hasText(user.getStatus())) {
            switch (user.getStatus()) {
                case "0":
                    profile.setStatusText("正常");
                    break;
                case "1":
                    profile.setStatusText("停用");
                    break;
                case "9":
                    profile.setStatusText("删除");
                    break;
                default:
                    profile.setStatusText("未知");
                    break;
            }
        } else {
            profile.setStatusText("未知");
        }

        // 设置用户类型文本
        if (StringUtils.hasText(user.getUserType())) {
            switch (user.getUserType()) {
                case "platform":
                    profile.setUserTypeText("平台管理员");
                    break;
                case "tenant":
                    profile.setUserTypeText("租户管理员");
                    break;
                case "normal":
                    profile.setUserTypeText("普通用户");
                    break;
                default:
                    profile.setUserTypeText("未知");
                    break;
            }
        } else {
            profile.setUserTypeText("普通用户");
        }

        // 获取部门名称
        if (StringUtils.hasText(user.getDeptId())) {
            try {
                // 这里应该调用部门服务获取部门名称
                // profile.setDeptName(deptService.getDeptNameById(user.getDeptId()));
                profile.setDeptName("技术部"); // 临时设置
            } catch (Exception e) {
                log.warn("获取部门名称失败: deptId={}", user.getDeptId(), e);
                profile.setDeptName("未知部门");
            }
        }

        // 获取租户名称
        if (StringUtils.hasText(user.getTenantId())) {
            try {
                // 这里应该调用租户服务获取租户名称
                // profile.setTenantName(tenantService.getTenantNameById(user.getTenantId()));
                profile.setTenantName("默认租户"); // 临时设置
            } catch (Exception e) {
                log.warn("获取租户名称失败: tenantId={}", user.getTenantId(), e);
                profile.setTenantName("未知租户");
            }
        }

        // 获取用户角色和权限（暂时设置为空，后续可以实现）
        profile.setRoles(new ArrayList<>());
        profile.setPermissions(new ArrayList<>());

        return profile;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCurrentUserProfile(UserProfileUpdateDTO updateDTO) {
        String currentUserId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(currentUserId)) {
            throw new BusinessException("用户未登录");
        }

        SysUser user = getById(currentUserId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查邮箱是否已被其他用户使用
        if (StringUtils.hasText(updateDTO.getEmail()) && !updateDTO.getEmail().equals(user.getEmail())) {
            long count = lambdaQuery()
                    .eq(SysUser::getEmail, updateDTO.getEmail())
                    .ne(SysUser::getId, currentUserId)
                    .count();
            if (count > 0) {
                throw new BusinessException("邮箱已被其他用户使用");
            }
        }

        // 检查手机号是否已被其他用户使用
        if (StringUtils.hasText(updateDTO.getPhone()) && !updateDTO.getPhone().equals(user.getPhone())) {
            long count = lambdaQuery()
                    .eq(SysUser::getPhone, updateDTO.getPhone())
                    .ne(SysUser::getId, currentUserId)
                    .count();
            if (count > 0) {
                throw new BusinessException("手机号已被其他用户使用");
            }
        }

        // 更新用户信息
        BeanUtils.copyProperties(updateDTO, user);
        user.setUpdateTime(LocalDateTime.now());

        boolean success = updateById(user);
        if (!success) {
            throw new BusinessException("更新个人信息失败");
        }

        // 清除用户缓存
        clearUserCache(currentUserId);

        log.info("用户[{}]更新个人信息成功", currentUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeCurrentUserPassword(String oldPassword, String newPassword) {
        String currentUserId = SecurityUtils.getCurrentUserId();
        if (!StringUtils.hasText(currentUserId)) {
            throw new BusinessException("用户未登录");
        }

        SysUser user = getById(currentUserId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证原密码
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("原密码不正确");
        }

        // 检查新密码是否与原密码相同
        if (passwordEncoder.matches(newPassword, user.getPassword())) {
            throw new BusinessException("新密码不能与原密码相同");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());

        boolean success = updateById(user);
        if (!success) {
            throw new BusinessException("修改密码失败");
        }

        // 清除用户缓存
        clearUserCache(currentUserId);

        log.info("用户[{}]修改密码成功", currentUserId);
    }
}
