package com.xhcai.common.datasource.entity;

import com.baomidou.mybatisplus.annotation.TableField;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;

/**
 * 基础实体类 包含通用的审计字段（包括租户字段）
 *
 * <p>
 * 此基类继承自 {@link BaseEntity}，包含所有公共的审计字段，并额外添加了租户字段。</p>
 * <p>
 * 此基类适用于需要多租户隔离的实体类。如果实体不需要租户隔离，请：</p>
 *
 * <p>
 * 包含的审计字段：</p>
 * <ul>
 * <li>继承自 BaseEntity 的字段：id, remark, deleted, create_by, create_time,
 * update_by, update_time</li>
 * <li>tenant_id - 租户ID（多租户隔离字段）</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see BaseEntity 抽象基础实体类
 * @see com.xhcai.common.datasource.annotation.NoTenant NoTenant注解
 */
@MappedSuperclass
public abstract class BaseWithTenantIDEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    // ========== 租户字段（在表中显示在最后） ==========
    /**
     * 租户ID 审计字段 - 多租户隔离
     */
    @Column(name = "tenant_id", length = 36)
    @TableField(value = "tenant_id")
    private String tenantId;

    // Getters and Setters for tenant field only
    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "BaseEntity{"
                + "tenantId='" + tenantId + '\''
                + ", " + super.toString()
                + '}';
    }
}
