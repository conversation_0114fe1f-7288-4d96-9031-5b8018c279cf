package com.yyzs.agent.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 组件监控数据实体
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "component_monitor")
@TableName("component_monitor")
public class ComponentMonitor extends BaseEntity {

    /**
     * 组件ID
     */
    @Column(name = "component_id", nullable = false, length = 32)
    private String componentId;

    /**
     * CPU使用率（百分比）
     */
    @Column(name = "cpu_usage", precision = 5, scale = 2)
    private BigDecimal cpuUsage;

    /**
     * 内存使用量（MB）
     */
    @Column(name = "memory_usage")
    private Long memoryUsage;

    /**
     * 内存使用率（百分比）
     */
    @Column(name = "memory_usage_percent", precision = 5, scale = 2)
    private BigDecimal memoryUsagePercent;

    /**
     * 磁盘使用量（MB）
     */
    @Column(name = "disk_usage")
    private Long diskUsage;

    /**
     * 磁盘使用率（百分比）
     */
    @Column(name = "disk_usage_percent", precision = 5, scale = 2)
    private BigDecimal diskUsagePercent;

    /**
     * 网络入流量（KB/s）
     */
    @Column(name = "network_in")
    private Long networkIn;

    /**
     * 网络出流量（KB/s）
     */
    @Column(name = "network_out")
    private Long networkOut;

    /**
     * 进程状态（RUNNING, STOPPED, ERROR）
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "process_status", length = 20)
    private ProcessStatus processStatus;

    /**
     * 进程ID
     */
    @Column(name = "process_id")
    private Long processId;

    /**
     * 线程数
     */
    @Column(name = "thread_count")
    private Integer threadCount;

    /**
     * 文件描述符数量
     */
    @Column(name = "file_descriptor_count")
    private Long fileDescriptorCount;

    /**
     * 运行时长（秒）
     */
    @Column(name = "uptime")
    private Long uptime;

    /**
     * 响应时间（毫秒）
     */
    @Column(name = "response_time")
    private Long responseTime;

    /**
     * 是否健康
     */
    @Column(name = "is_healthy", nullable = false)
    private Boolean isHealthy = true;

    /**
     * 健康检查消息
     */
    @Column(name = "health_message", length = 1000)
    private String healthMessage;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 2000)
    private String errorMessage;

    /**
     * 进程状态枚举
     */
    public enum ProcessStatus {
        RUNNING("运行中"),
        STOPPED("已停止"),
        ERROR("错误");

        private final String description;

        ProcessStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
