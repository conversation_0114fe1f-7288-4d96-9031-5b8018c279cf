<template>
  <div class="ai-explore flex bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50"
       :class="{
         'sidebar-open': showConversationList && !isDesktop,
         'h-full': isInLayout,
         'h-screen': !isInLayout
       }">
    <div class="flex-1 flex overflow-hidden">
      <!-- 对话记录侧边栏 -->
      <ConversationSidebar
        ref="conversationSidebarRef"
        v-if="showConversationList"
        :app-id="currentAppId"
        @hide-sidebar="showConversationList = false"
        @conversation-selected="handleConversationSelected"
      />


      <!-- 主聊天区域 -->
      <div class="chat-main flex-1 flex flex-col">
        <!-- 顶部简化工具栏 -->
        <div class="top-bar px-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <button
                v-if="!showConversationList"
                @click="showConversationList = true"
                class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 flex items-center justify-center"
                title="显示对话记录"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                </svg>
              </button>
              <!-- 模型选择器 - 在模态框中隐藏 -->
              <div v-if="!props.isInModal" class="min-w-0">
                <ModelSelector
                  v-model="selectedModel"
                  @change="handleModelChange"
                />
              </div>
            </div>

            <div class="flex items-center gap-2">
              <!-- 新建对话按钮 -->
              <button
                @click="createNewConversation"
                class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 flex items-center gap-1"
                title="新建对话"
              >
                <el-icon class="text-base">
                  <Plus />
                </el-icon>
                <span class="text-sm font-medium hidden md:inline">新建对话</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 消息列表区域 -->
        <div class="messages-container flex-1 overflow-y-auto"
             data-messages-container
             :class="{
               'pb-0': currentMessages.length === 0,
               'pb-32': currentMessages.length > 0
             }"
             style="width: 100%; max-width: 100%;">
          <!-- 空状态：显示欢迎信息和内容展示区域 -->
          <WelcomePage
            v-if="currentMessages.length === 0"
            @model-select="handleModelSelect"
            @agent-select="handleAgentSelect"
            @quick-start="handleQuickStart"
            :agent-info="agentInfo"
          />

          <!-- 消息列表组件 -->
          <MessageList
            v-else
            :messages="currentMessages"
            :is-sending="chatInputStore.isSending"
            :is-streaming="isStreaming"
            :thinking-text="thinkingText"
            :thinking-progress="thinkingProgress"
            :thinking-stage="thinkingStage"
            :thinking-steps="thinkingSteps"
            :format-time="formatTime"
            :get-message-data="getMessageData"
            @link-click="handleLinkClick"
            @image-click="handleImageClick"
            @file-download="handleFileDownload"
          />
        </div>



        <!-- 参数配置组件 - 固定在输入框上方 -->
        <div
          v-if="showParameterConfig"
          class="parameter-config-container fixed right-0 z-[98] transition-all duration-200 ease-out"
          :style="{
            left: showConversationList ? '320px' : '0px',
            bottom: showSuggestedQuestions ? '200px' : '120px'
          }"
        >
          <div class="px-4 pb-2">
            <ParameterConfig
              :parameters="userInputFormParameters"
              :values="parameterValues"
              @update:values="handleParameterValuesUpdate"
              @collapse="handleParameterConfigCollapse"
              @validation-change="handleParameterValidationChange"
            />
          </div>
        </div>

        <!-- 建议问题组件 - 固定在输入框上方 -->
        <div
          v-if="showSuggestedQuestions"
          class="suggested-questions-container fixed right-0 z-[99] transition-all duration-200 ease-out"
          :style="{
            left: showConversationList ? '320px' : '0px',
            bottom: '120px'
          }"
        >
          <div class="px-4 pb-2">
            <SuggestedQuestions
              :questions="suggestedQuestions"
              :disabled="chatInputStore.isSending || isStreaming || loadingSuggestedQuestions"
              @question-click="handleSuggestedQuestionClick"
            />
          </div>
        </div>

        <!-- 输入区域 - 固定在底部，只在有消息时显示 -->
        <ChatInputArea
          ref="chatInputAreaRef"
          :container-class="`fixed bottom-0 right-0 shadow-lg transition-all duration-200 ease-out z-[100]`"
          :style="{
            left: showConversationList ? '320px' : '0px'
          }"
          :hide-tools-menu="props.isInModal"
          :can-stop-workflow="canStopWorkflow"
          :can-expand-parameter-config="!showParameterConfig && userInputFormParameters.length > 0"
          @stop-workflow="handleStopWorkflow"
          @expand-parameter-config="handleParameterConfigExpand"
        />
      </div>
    </div>






  </div>
</template>

<style scoped>
/* 思考动画相关样式 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.animate-shimmer {
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

/* 流式输出时的光标效果 */
.streaming-cursor::after {
  content: '|';
  animation: blink 1s infinite;
  color: #3b82f6;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 用户区域样式 */
.user-area {
  z-index: 30;
}

/* 模型智能体选择器区域样式 */
.model-agent-selector-section {
  height: 100%;
  min-height: 500px;
  padding: 0;
}

.model-agent-selector-section .model-agent-selector {
  height: 100%;
  padding: 1rem;
}

/* 移动端用户区域优化 */
@media (max-width: 768px) {
  .user-area {
    padding: 12px 16px;
  }

  .user-area .flex {
    gap: 8px;
  }

  .user-area .w-8 {
    width: 32px;
    height: 32px;
  }

  .user-area .w-9 {
    width: 36px;
    height: 36px;
  }

  .user-area .text-sm {
    font-size: 13px;
  }

  .user-area .text-xs {
    font-size: 11px;
  }
}
</style>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import ModelSelector from '@/views/aiExplore/ModelSelector.vue'
import ChatInputArea from '@/views/aiExplore/ChatInputArea.vue'
import WelcomePage from '@/views/aiExplore/WelcomePage.vue'
import { exploreApi } from '@/api/explore'

// 定义props
interface Props {
  agentInfo?: any
  isInModal?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  agentInfo: null,
  isInModal: false
})
import MessageList from '@/views/aiExplore/MessageList.vue'
import ConversationSidebar from '@/views/aiExplore/ConversationSidebar.vue'
import SuggestedQuestions from '@/components/SuggestedQuestions.vue'
import ParameterConfig from '@/components/ParameterConfig.vue'

import { StreamContentParser, mockStreamData } from '@/utils/streamContentParser'
import { RendererSampleGenerator, ContentMatcher } from '@/utils/rendererSamples'
import type { RendererType } from '@/types/renderer'
import { useModelStore } from '@/stores/modelStore'
import { useConversationStore } from '@/stores/conversationStore'
import { useRendererStore } from '@/stores/rendererStore'

import { useChatInputStore } from '@/stores/chatInputStore'
import { storeToRefs } from 'pinia'

// 导入类型定义
import type { Message } from '@/stores/conversationStore'

// 文件和图片类型定义现在在全局store中



// 使用共享数据存储
const {
  selectedModelId,
  selectedModelInfo,
  setSelectedModel
} = useModelStore()

// 使用对话存储
const conversationStore = useConversationStore()

// 使用渲染器存储
const rendererStore = useRendererStore()

// 使用全局聊天输入存储
const chatInputStore = useChatInputStore()

// 使用storeToRefs来保持响应性
const { conversations, currentConversationId, currentMessages } = storeToRefs(conversationStore)

// 从渲染器store获取状态
const { selectedRenderer } = storeToRefs(rendererStore)

// 方法可以直接解构
const { createNewConversation, selectConversation, addMessage, updateMessage, updateConversationTitle, getLastMessageId, getBackendConversationId, hasConversationHistory, updateBackendConversationId, updateBackendMessageId } = conversationStore
// handleRendererSelect 现在通过 chatInputHandlers 统一管理

// 响应式数据
const selectedModel = selectedModelId // 使用共享的selectedModelId
const showConversationList = ref(false) // 默认隐藏，后面会根据屏幕大小调整
const conversationSidebarRef = ref<any>(null) // ConversationSidebar组件引用
const chatInputAreaRef = ref<any>(null) // ChatInputArea组件引用

// 检测屏幕大小
const isDesktop = ref(window.innerWidth >= 768)
// isSending现在由全局chatInputStore管理
const isStreaming = ref(false)
// 工作流相关状态
const currentTaskId = ref<string | null>(null)
const canStopWorkflow = ref(false)
// 当前节点状态
const currentNodeInfo = ref<{
  title: string
  nodeType: string
  isActive: boolean
} | null>(null)





// AI思考动态效果相关状态
const thinkingText = ref('AI正在思考中')
const thinkingProgress = ref(0)
const thinkingStage = ref('准备中...')
const thinkingSteps = ref([
  { name: '理解', active: false, completed: false },
  { name: '分析', active: false, completed: false },
  { name: '生成', active: false, completed: false },
  { name: '准备', active: false, completed: false }
])

// 思考文本列表
const thinkingTexts = [
  'AI正在思考中',
  '正在分析您的问题',
  '正在搜索相关信息',
  '正在组织回答',
  '正在优化回复内容'
]

// 建议问题相关状态
const suggestedQuestions = ref<string[]>([])
const showSuggestedQuestions = ref(false)
const chatParameters = ref<any>(null)
const loadingSuggestedQuestions = ref(false)

// 参数配置相关状态
const showParameterConfig = ref(false)
const userInputFormParameters = ref<any[]>([])
const parameterValues = ref<Record<string, any>>({})
const parameterValidation = ref({ isValid: true, errors: [] as string[] })



// 注册全局聊天输入事件处理器
chatInputStore.registerEventHandlers({
  onSendMessage: async (messageData: any) => {
    console.log('全局handleSendMessage 被调用，消息数据:', messageData)
    console.log('chatInputStore.canSend:', chatInputStore.canSend)
    console.log('isSending.value:', chatInputStore.isSending)

    // 注意：这里不需要再检查canSend，因为handleSendMessage已经检查过了
    // 这个事件处理器只有在canSend为true时才会被调用

    // 确保有选中的模型或智能体，如果没有则尝试自动设置
    if (!selectedModelInfo.value) {
      console.warn('请先选择模型或智能体，当前selectedModelInfo为空')
      // 尝试自动设置默认模型
      if (selectedModelId.value) {
        console.log('尝试自动设置默认模型:', selectedModelId.value)
        setSelectedModel(selectedModelId.value)
        // 设置后再次检查
        if (!selectedModelInfo.value) {
          console.warn('自动设置默认模型失败，请手动选择模型或智能体')
          return
        }
      } else {
        console.warn('没有默认模型ID，请先选择一个模型或智能体')
        return
      }
    }

    // 检查参数配置验证
    if (showParameterConfig.value && !parameterValidation.value.isValid) {
      console.warn('参数配置验证失败:', parameterValidation.value.errors)
      ElMessage.warning(`请完善参数配置：${parameterValidation.value.errors.join('、')}`)
      return
    }

    // 确保有当前对话
    if (!currentConversationId.value) {
      console.log('没有当前对话，创建新对话')
      createNewConversation()
    }

    // 获取智能体上传的文件
    const agentFiles = chatInputAreaRef.value?.getAgentUploadedFiles() || []

    // 添加用户消息
    const userMessage = addMessage({
      role: 'user',
      content: messageData.content,
      contentType: 'text', // 用户输入默认为文本类型
      files: messageData.files || [],
      images: messageData.images || [],
      agentFiles: agentFiles // 添加智能体文件
    })

    console.log('用户消息已添加:', {
      messageId: userMessage.id,
      conversationId: currentConversationId.value,
      messagesCount: currentMessages.value.length
    })

    // 滚动到底部
    scrollToBottom()

    // 根据选择的类型决定使用哪种回复方式
    if (selectedModelInfo.value?.type === 'agent') {
      console.log('选择了智能体，使用流式聊天接口')
      await handleAgentStreamChat(messageData.content)
    } else {
      console.log('选择了模型，使用模拟AI回复')
      await simulateAIResponse()
    }
  },
  onFileUpload: (file: File) => {
    console.log('文件上传:', file.name)
    // 文件上传逻辑现在由全局store处理
  },
  onImageUpload: (file: File) => {
    console.log('图片上传:', file.name)
    // 图片上传逻辑现在由全局store处理
  },
  onVoiceRecordingStart: async () => {
    await startRecording()
  },
  onVoiceRecordingStop: () => {
    stopRecording()
  },
  onVoiceModeToggle: () => {
    chatInputStore.isVoiceMode = !chatInputStore.isVoiceMode
    console.log('语音模式:', chatInputStore.isVoiceMode ? '开启' : '关闭')
  }
})





// DOM引用 (保留用于其他用途)
const messageInput = ref<HTMLTextAreaElement | null>(null)

// 语音录制相关
let mediaRecorder: MediaRecorder | null = null
let recordingTimer: number | null = null

// canSend逻辑现在完全由全局chatInputStore管理

// 检测是否在 MainLayout 中
const isInLayout = computed(() => {
  // 如果是在模态框中，则认为是在布局中
  if (props.isInModal) return true
  // 通过路由名称判断是否在 MainLayout 中
  return window.location.pathname !== '/ai'
})

// 获取当前选择的智能体的appId
const currentAppId = computed(() => {
  // 只有选择了智能体时，才返回appId
  if (selectedModelInfo.value?.type === 'agent' && selectedModelInfo.value.appId) {
    console.log('当前选择的是智能体，appId:', selectedModelInfo.value.appId)
    return selectedModelInfo.value.appId
  }
  // 选择模型时，不关联appId，对话记录设置为空
  if (selectedModelInfo.value?.type === 'model') {
    console.log('当前选择的是模型，不关联appId')
    return undefined
  }
  // 没有选择任何模型或智能体时
  console.log('没有选择模型或智能体')
  return undefined
})









// 格式化时间 - 智能时间显示
const formatTime = (date: Date) => {
  const now = new Date()
  const messageDate = new Date(date)

  // 获取时间部分 HH:mm
  const timeStr = messageDate.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })

  // 获取今天的开始时间（00:00:00）
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  // 获取本周一的开始时间
  const currentWeekStart = new Date(today)
  const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay() // 周日为7，周一为1
  currentWeekStart.setDate(today.getDate() - dayOfWeek + 1)

  // 获取上周一的开始时间
  const lastWeekStart = new Date(currentWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000)
  const lastWeekEnd = new Date(currentWeekStart.getTime() - 1)

  // 获取消息日期的开始时间
  const messageDateStart = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate())

  // 判断是今天
  if (messageDateStart.getTime() === today.getTime()) {
    return `今天 ${timeStr}`
  }

  // 判断是昨天
  if (messageDateStart.getTime() === yesterday.getTime()) {
    return `昨天 ${timeStr}`
  }

  // 判断是本周
  if (messageDateStart >= currentWeekStart && messageDateStart < today) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const dayIndex = messageDate.getDay()
    return `本${weekdays[dayIndex]} ${timeStr}`
  }

  // 判断是上周
  if (messageDateStart >= lastWeekStart && messageDateStart <= lastWeekEnd) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const dayIndex = messageDate.getDay()
    return `上${weekdays[dayIndex]} ${timeStr}`
  }

  // 其他时间显示完整日期
  const year = messageDate.getFullYear()
  const month = String(messageDate.getMonth() + 1).padStart(2, '0')
  const day = String(messageDate.getDate()).padStart(2, '0')

  return `${year}年${month}月${day}日 ${timeStr}`
}

// 格式化图片数据为组件所需格式
const formatImagesForComponent = (images?: string[]) => {
  if (!images || images.length === 0) return undefined

  return images.map((url, index) => ({
    url,
    alt: `图片${index + 1}`,
    caption: undefined
  }))
}

// 格式化文件数据为组件所需格式
const formatFilesForComponent = (files?: File[] | any[]) => {
  if (!files || files.length === 0) return undefined

  return files.map(file => {
    try {
      // 如果是File对象，创建URL
      if (file instanceof File) {
        return {
          name: file.name,
          url: URL.createObjectURL(file),
          type: file.type,
          size: file.size
        }
      }
      // 如果是已经解析过的文件数据对象，直接返回
      else if (file && typeof file === 'object' && file.name && file.url) {
        return {
          name: file.name,
          url: file.url,
        type: file.type || 'application/octet-stream',
        size: file.size || 0
      }
    }
    // 其他情况，返回默认值
    else {
      return {
        name: 'Unknown File',
        url: '#',
        type: 'application/octet-stream',
        size: 0
      }
    }
    } catch (error) {
      console.warn('格式化文件数据时出错:', error, file)
      return {
        name: 'Error File',
        url: '#',
        type: 'application/octet-stream',
        size: 0
      }
    }
  })
}

// 获取消息数据 - 优化版本，避免每次渲染都重新计算
const getMessageData = (message: Message) => {
  try {
    const data = {
      images: formatImagesForComponent(message.images),
      audioContent: message.audioContent,
      videoContent: message.videoContent,
      files: formatFilesForComponent(message.files),
      chartData: message.chartData,
      chartType: message.chartType,
      flowchartData: message.flowchartData
    }

    // 移除图表消息数据的控制台输出
    // if (message.contentType === 'chart' && import.meta.env.DEV) {
    //   console.log('图表消息数据:', {
    //     contentType: message.contentType,
    //     chartData: message.chartData,
    //     chartType: message.chartType,
    //     content: message.content.substring(0, 200) + '...'
    //   })
    // }

    return data
  } catch (error) {
    console.warn('获取消息数据时出错:', error)
    return {
      images: undefined,
      audioContent: message.audioContent,
      videoContent: message.videoContent,
      files: undefined,
      chartData: message.chartData,
      chartType: message.chartType,
      flowchartData: message.flowchartData
    }
  }
}

// 处理图片点击
const handleImageClick = (image: any, index: number) => {
  console.log('图片点击:', image, index)
  // 这里可以实现图片预览功能
}

// 处理文件下载
const handleFileDownload = (file: any) => {
  console.log('文件下载:', file)
  // 这里可以实现文件下载功能
  const link = document.createElement('a')
  link.href = file.url
  link.download = file.name
  link.click()
}

// 处理链接点击
const handleLinkClick = (url: string) => {
  // 在新窗口打开链接
  window.open(url, '_blank', 'noopener,noreferrer')
}

// 处理模型变化
const handleModelChange = async (item: any) => {
  selectedModelInfo.value = item
  console.log('选择了:', item.type === 'model' ? '模型' : '智能体', item.name)

  // 根据选择的类型调用相应的处理方法
  if (item.type === 'agent') {
    console.log('🔧 handleModelChange: 检测到智能体选择，调用 handleAgentSelect')
    await handleAgentSelect(item)
  } else {
    console.log('🔧 handleModelChange: 检测到模型选择，调用 handleModelSelect')
    handleModelSelect(item)
  }
}

// ChatInputArea 相关的事件处理现在通过 chatInputHandlers 统一管理

// 渲染器相关状态已移至全局 rendererStore

// 处理模型选择
const handleModelSelect = (model: any) => {
  setSelectedModel(model.id, { ...model, type: 'model' })
  console.log('选择模型:', model.name)

  // 选择新模型时，创建新对话（清空对话历史）
  console.log('模型切换，创建新对话')
  createNewConversation()

  // 清空建议问题和参数配置（模型不支持建议问题和参数配置）
  showSuggestedQuestions.value = false
  suggestedQuestions.value = []
  chatParameters.value = null
  showParameterConfig.value = false
  userInputFormParameters.value = []
  parameterValues.value = {}

  // 聚焦到输入框
  nextTick(() => {
    messageInput.value?.focus()
  })
}

// 处理智能体选择
const handleAgentSelect = async (agent: any) => {
  setSelectedModel(agent.id, { ...agent, type: 'agent' })
  console.log('选择智能体:', agent.name, 'appId:', agent.appId)

  // 选择新智能体时，创建新对话（清空对话历史）
  console.log('智能体切换，创建新对话')
  createNewConversation()

  // 清空建议问题和参数配置
  showSuggestedQuestions.value = false
  suggestedQuestions.value = []
  chatParameters.value = null
  showParameterConfig.value = false
  userInputFormParameters.value = []
  parameterValues.value = {}

  // 获取智能体的参数配置
  if (agent.appId) {
    console.log('🔧 handleAgentSelect: 准备调用 loadChatParameters，appId:', agent.appId)
    await loadChatParameters(agent.appId)
    console.log('🔧 handleAgentSelect: loadChatParameters 调用完成')
  } else {
    console.log('⚠️ handleAgentSelect: agent.appId 为空，跳过参数配置加载')
  }

  // 等待一下让ConversationSidebar更新
  await nextTick()

  // ConversationSidebar 会自动获取对话记录并选择第一条对话
  // 不需要手动调用 loadLatestConversation，避免重复操作
  console.log('等待ConversationSidebar自动加载和选择对话记录')

  // 聚焦到输入框
  nextTick(() => {
    messageInput.value?.focus()
  })
}

// 刷新对话记录
const refreshConversationList = async () => {
  if (conversationSidebarRef.value && currentAppId.value) {
    console.log('刷新对话记录列表')
    try {
      await conversationSidebarRef.value.refreshConversations()
      console.log('对话记录刷新成功')
    } catch (error) {
      console.error('刷新对话记录失败:', error)
    }
  } else {
    console.log('跳过刷新对话记录:', {
      hasRef: !!conversationSidebarRef.value,
      hasAppId: !!currentAppId.value
    })
  }
}

// 加载智能体的最新对话记录
const loadLatestConversation = async (appId: string) => {
  if (!appId) {
    console.log('没有appId，跳过加载最新对话')
    return
  }

  try {
    console.log('开始加载智能体最新对话记录，appId:', appId)

    // 获取对话记录列表
    const conversations = await exploreApi.getConversations(appId, {
      limit: 1, // 只获取最新的一条对话
      pinned: false
    })

    console.log('获取到对话记录:', conversations)

    if (conversations && conversations.length > 0) {
      const latestConversation = conversations[0]
      console.log('找到最新对话:', latestConversation)

      // 加载这个对话的消息
      await handleConversationSelected(latestConversation.id)
    } else {
      console.log('该智能体没有对话记录，保持空白状态')
    }
  } catch (error) {
    console.error('加载最新对话失败:', error)
    // 失败时不影响用户操作，保持当前状态
  }
}

// 处理快速开始
const handleQuickStart = (example: any) => {
  console.log('快速开始:', example)

  // 如果有内容，直接发送消息
  if (example.content) {
    // 确保有当前对话
    if (!currentConversationId.value) {
      console.log('没有当前对话，创建新对话')
      createNewConversation()
    }

    // 添加用户消息
    addMessage({
      role: 'user',
      content: example.content,
      contentType: 'text'
    })

    // 模拟AI回复
    setTimeout(() => {
      addMessage({
        role: 'assistant',
        content: `收到您的消息："${example.content}"，我正在为您处理...`,
        contentType: 'text'
      })
    }, 500)
  }
}

// handleSendMessageFromSelector函数已移除
// 现在消息发送完全由全局事件处理器统一处理



// 获取智能体对话记录列表
const getConversationsList = async (appId: string) => {
  try {
    console.log('获取对话记录列表，appId:', appId)
    const conversations = await exploreApi.getConversations(appId, {
      limit: 100,
      pinned: false
    })
    console.log('获取到的对话记录:', conversations)
    return conversations
  } catch (error) {
    console.error('获取对话记录失败:', error)
    throw error
  }
}

// 转换后端消息格式为前端格式
const convertBackendMessagesToLocal = (backendMessages: any[]) => {
  const messages: any[] = []

  backendMessages.forEach(msg => {
    // 优先使用createdTime字段，如果没有则使用created_at
    const messageTime = msg.createdTime ? new Date(msg.createdTime) : new Date(msg.created_at * 1000)

    // 添加用户消息
    if (msg.query) {
      const userMessage: any = {
        role: 'user',
        content: msg.query,
        contentType: 'text',
        timestamp: messageTime,
        backendMessageId: msg.id // 保存后端消息ID
      }

      // 添加message_files字段（如果存在）
      if (msg.message_files && msg.message_files.length > 0) {
        userMessage.message_files = msg.message_files
      }

      messages.push(userMessage)
    }

    // 添加AI回复消息
    if (msg.answer) {
      // 保留完整的回复内容，包括 <think> 标签，让MarkdownRenderer处理
      let content = msg.answer

      messages.push({
        role: 'assistant',
        content: content.trim(),
        contentType: 'markdown',
        timestamp: messageTime,
        backendMessageId: msg.id // 保存后端消息ID
      })
    }
  })

  // 按时间戳排序
  return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
}

// 语音模式切换现在通过 chatInputHandlers 统一管理



// 处理对话选择事件（从 ConversationSidebar 传递过来）
const handleConversationSelected = async (conversationId: string) => {
  console.log('AIExplore: 接收到conversation-selected事件:', {
    conversationId: conversationId,
    currentAppId: currentAppId.value,
    timestamp: new Date().toISOString(),
    isNewConversation: conversationId === '' || conversationId.startsWith('new-')
  })

  // 如果 conversationId 为空字符串或以new-开头，表示是新对话，不需要调用接口
  if (conversationId === '' || conversationId.startsWith('new-')) {
    console.log('选中的是新对话，创建空的聊天窗口，不调用获取消息接口')
    createNewConversation() // 创建新的空对话
    return
  }

  if (!currentAppId.value) {
    console.error('没有当前智能体ID')
    return
  }

  try {
    // 显示加载状态
    chatInputStore.isSending = true

    // 创建新对话并立即保存后端对话ID
    const newConversation = createNewConversation()
    updateBackendConversationId(newConversation.id, conversationId)
    console.log('创建新对话并保存后端对话ID:', {
      backendConversationId: conversationId,
      localConversationId: newConversation.id
    })

    // 验证后端对话ID是否正确保存
    const savedBackendId = getBackendConversationId()
    console.log('验证保存的后端对话ID:', {
      expected: conversationId,
      actual: savedBackendId,
      isCorrect: savedBackendId === conversationId
    })

    // 获取会话消息
    console.log('开始获取会话消息，appId:', currentAppId.value, 'conversationId:', conversationId)
    const messagesResponse = await exploreApi.getConversationMessages(currentAppId.value, conversationId, {
      limit: 50 // 获取最近50条消息
    })

    console.log('获取会话消息成功:', messagesResponse)

    // 转换消息格式并添加到当前对话
    if (messagesResponse.data && messagesResponse.data.length > 0) {
      // 转换后端消息格式为前端格式
      const convertedMessages = convertBackendMessagesToLocal(messagesResponse.data)

      // 更新对话标题（使用第一条用户消息）
      const firstUserMessage = convertedMessages.find(msg => msg.role === 'user')
      if (firstUserMessage) {
        const title = firstUserMessage.content.substring(0, 30) + (firstUserMessage.content.length > 30 ? '...' : '')
        updateConversationTitle(newConversation.id, title)
      }

      // 将消息添加到当前对话，并保存后端消息ID
      convertedMessages.forEach(message => {
        const messageData: any = {
          role: message.role,
          content: message.content,
          contentType: message.contentType || 'markdown',
          timestamp: message.timestamp // 使用后端返回的时间戳
        }

        // 如果消息包含message_files，添加到消息数据中
        if (message.message_files && message.message_files.length > 0) {
          messageData.message_files = message.message_files
          console.log('添加message_files到消息:', message.message_files)
        }

        const addedMessage = addMessage(messageData)

        // 保存后端消息ID
        if (message.backendMessageId && addedMessage) {
          updateBackendMessageId(addedMessage.id, message.backendMessageId)
          console.log('保存后端消息ID:', message.backendMessageId, '到本地消息:', addedMessage.id)
        }
      })

      console.log('会话消息已加载到聊天界面')
    } else {
      // 如果没有消息数据，保持空对话但仍然关联后端对话ID
      console.log('该对话没有消息数据，保持空对话')
    }

  } catch (error) {
    console.error('获取会话消息失败:', error)
    // 出错时也创建一个新的空对话
    createNewConversation()
  } finally {
    // 隐藏加载状态
    chatInputStore.isSending = false

    // 选择对话后自动滚动到底部
    nextTick(() => {
      setTimeout(() => {
        scrollToBottom(true) // 使用强制滚动确保立即到底部
      }, 100) // 增加延迟确保消息列表已更新
    })
  }
}





// 文件、图片、录音相关的处理现在通过 chatInputHandlers 统一管理

// 检查麦克风权限
const checkMicrophonePermission = async () => {
  try {
    // 检查浏览器是否支持 MediaDevices API
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('BROWSER_NOT_SUPPORTED')
    }

    // 检查是否为 HTTPS 环境（本地开发除外）
    if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
      throw new Error('HTTPS_REQUIRED')
    }

    // 检查麦克风权限状态
    if (navigator.permissions) {
      const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName })
      if (permission.state === 'denied') {
        throw new Error('PERMISSION_DENIED')
      }
    }

    return true
  } catch (error) {
    throw error
  }
}

// 开始录音
const startRecording = async () => {
  try {
    // 先检查权限和环境
    await checkMicrophonePermission()

    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    })

    mediaRecorder = new MediaRecorder(stream)
    const audioChunks: Blob[] = []

    mediaRecorder.ondataavailable = (event) => {
      audioChunks.push(event.data)
    }

    mediaRecorder.onstop = () => {
      const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
      // 保存录音数据
      chatInputStore.recordedAudio = audioBlob

      // 停止所有音频轨道
      stream.getTracks().forEach(track => track.stop())
    }

    mediaRecorder.start()
    chatInputStore.isRecording = true
    chatInputStore.recordingTime = 0

    // 开始计时
    recordingTimer = window.setInterval(() => {
      chatInputStore.recordingTime++
    }, 1000)

  } catch (error: any) {
    console.error('录音失败:', error)
    handleRecordingError(error)
  }
}

// 处理录音错误
const handleRecordingError = (error: any) => {
  let errorMessage = '录音失败，请重试'

  if (error.name === 'NotAllowedError' || error.message === 'PERMISSION_DENIED') {
    errorMessage = `麦克风权限被拒绝，请按以下步骤操作：

1. 点击地址栏左侧的锁形图标
2. 将"麦克风"权限设置为"允许"
3. 刷新页面后重试

或在浏览器设置中允许此网站访问麦克风。`
  } else if (error.name === 'NotFoundError') {
    errorMessage = '未找到麦克风设备，请检查您的麦克风是否正常连接。'
  } else if (error.name === 'NotReadableError') {
    errorMessage = '麦克风设备被占用，请关闭其他使用麦克风的程序后重试。'
  } else if (error.message === 'BROWSER_NOT_SUPPORTED') {
    errorMessage = '您的浏览器不支持录音功能，请使用 Chrome、Firefox、Safari 等现代浏览器。'
  } else if (error.message === 'HTTPS_REQUIRED') {
    errorMessage = '录音功能需要在 HTTPS 环境下使用，请确保网站使用安全连接。'
  }

  alert(errorMessage)
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder && chatInputStore.isRecording) {
    mediaRecorder.stop()
    chatInputStore.isRecording = false
    chatInputStore.recordingDuration = chatInputStore.recordingTime

    if (recordingTimer) {
      clearInterval(recordingTimer)
      recordingTimer = null
    }
  }
}



// 启动思考动画
const startThinkingAnimation = () => {
  thinkingProgress.value = 0
  thinkingStage.value = '准备中...'
  thinkingText.value = thinkingTexts[0]

  // 重置步骤状态
  thinkingSteps.value.forEach(step => {
    step.active = false
    step.completed = false
  })
}

// 模拟思考过程 - 快速思考
const simulateThinkingProcess = async () => {
  const stages = [
    { text: '正在理解您的问题', stage: '理解问题中...', stepIndex: 0, duration: 200 },
    { text: '正在分析相关信息', stage: '分析信息中...', stepIndex: 1, duration: 250 },
    { text: '正在生成回答', stage: '生成回答中...', stepIndex: 2, duration: 200 },
    { text: '准备开始回复', stage: '准备回复中...', stepIndex: 3, duration: 150 }
  ]

  for (let i = 0; i < stages.length; i++) {
    const stage = stages[i]

    // 更新当前步骤
    thinkingSteps.value[stage.stepIndex].active = true
    thinkingText.value = stage.text
    thinkingStage.value = stage.stage

    // 动画进度条
    const startProgress = (i / stages.length) * 100
    const endProgress = ((i + 1) / stages.length) * 100

    await animateProgress(startProgress, endProgress, stage.duration)

    // 完成当前步骤
    thinkingSteps.value[stage.stepIndex].active = false
    thinkingSteps.value[stage.stepIndex].completed = true
  }

  // 思考完成，准备开始流式输出
  thinkingText.value = '开始回复'
  thinkingStage.value = '思考完成'
  thinkingProgress.value = 100
}

// 动画进度条
const animateProgress = (start: number, end: number, duration: number): Promise<void> => {
  return new Promise(resolve => {
    const startTime = Date.now()
    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      thinkingProgress.value = start + (end - start) * progress

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        resolve()
      }
    }
    animate()
  })
}



// 处理智能体流式聊天
const handleAgentStreamChat = async (query: string) => {
  console.log('handleAgentStreamChat 开始执行，查询:', query)

  // 启动思考动画
  startThinkingAnimation()

  // 模拟思考过程
  await simulateThinkingProcess()

  // 思考完成，开始流式输出
  isStreaming.value = true

  // 在创建AI消息之前，先获取conversation_id和parent_message_id
  const hasHistory = hasConversationHistory()
  const backendConversationId = getBackendConversationId()

  // 判断当前是否为新对话（选中的对话记录ID为空或以new-开头）
  const isNewConversation = !backendConversationId || backendConversationId === '' || backendConversationId.startsWith('new-')

  // 根据是否为新对话设置参数
  let conversationId = ''
  let parentMessageId = null

  if (isNewConversation) {
    // 新对话：conversation_id和parent_message_id都为空
    conversationId = ''
    parentMessageId = null
  } else {
    // 已存在的对话：使用后端对话ID和最后一条消息ID
    conversationId = backendConversationId
    parentMessageId = hasHistory ? getLastMessageId() : null
  }

  console.log('流式聊天参数检查:', {
    hasHistory,
    backendConversationId,
    isNewConversation,
    conversationId: conversationId || '(空字符串)',
    parentMessageId: parentMessageId || '(null)',
    currentConversationId: currentConversationId.value,
    messagesCount: currentMessages.value.length,
    currentAppId: currentAppId.value,
    selectedModelType: selectedModelInfo.value?.type,
    willUseConversationId: conversationId ? `使用对话记录ID: ${conversationId}` : '新对话（无conversation_id）'
  })

  // 创建AI消息（用于流式输出）- 智能体回复使用markdown格式，但不解析图表
  const aiMessage = addMessage({
    role: 'assistant',
    content: '',
    contentType: 'markdown', // 使用markdown渲染器支持格式化显示
    streaming: true,
    currentNode: null // 初始化节点信息
  })

  console.log('AI消息已添加，开始流式聊天')
  scrollToBottom()

  // 用于累积流式内容的变量
  let accumulatedContent = ''

  // 存储是否为新对话的状态，供回调函数使用
  const isNewConversationForCallback = isNewConversation

  try {

    // 获取智能体上传的文件
    const agentFiles = chatInputAreaRef.value?.getAgentUploadedFiles() || []

    // 过滤文件数据，只保留API需要的四个参数
    const apiFiles = agentFiles.map(file => ({
      type: file.type,
      transfer_method: file.transfer_method,
      url: file.url,
      upload_file_id: file.upload_file_id
    }))

    // 处理参数值，转换文件参数格式
    const processedInputs = processParameterValues(parameterValues.value || {})

    // 构建API请求参数
    const apiParams = {
      response_mode: 'streaming',
      conversation_id: conversationId,
      files: apiFiles,
      query: query,
      inputs: processedInputs,
      parent_message_id: parentMessageId,
      appId: currentAppId.value || '',
      agentId: selectedModelInfo.value?.id || ''
    }

    console.log('流式聊天API请求参数:', {
      ...apiParams,
      conversation_id: apiParams.conversation_id || '(空字符串)',
      parent_message_id: apiParams.parent_message_id || '(null)',
      appId: apiParams.appId || '(空字符串)',
      agentId: apiParams.agentId || '(空字符串)'
    })

    // 调用流式聊天API
    await exploreApi.streamChat(apiParams, {
      onMessage: (data: any) => {
        accumulatedContent = handleStreamMessage(aiMessage, data, accumulatedContent)
      },
      onError: (error: any) => {
        console.error('流式聊天错误:', error)
        updateMessage(aiMessage.id, {
          content: '抱歉，发生了错误，请重试。',
          streaming: false
        })
        isStreaming.value = false
      },
      onComplete: async () => {
        updateMessage(aiMessage.id, { streaming: false })
        isStreaming.value = false
        console.log('流式聊天完成')

        // 清空智能体上传的文件
        chatInputAreaRef.value?.clearAgentUploadedFiles()

        // 如果是新对话，处理新对话的后续逻辑
        if (isNewConversationForCallback) {
          console.log('新对话流式聊天完成，处理新对话逻辑')
          await handleNewConversationComplete()
        } else {
          // 已存在的对话，也不刷新对话记录列表，避免不必要的API调用
          console.log('已存在对话流式聊天完成，跳过刷新对话记录列表')
        }
      }
    })
  } catch (error) {
    console.error('流式聊天请求失败:', error)
    updateMessage(aiMessage.id, {
      content: '抱歉，发生了错误，请重试。',
      streaming: false
    })
    isStreaming.value = false
  }
}

// 处理流式消息
const handleStreamMessage = (aiMessage: any, data: any, accumulatedContent: string): string => {
  try {
    // 移除流式数据的控制台输出，避免打印样例数据
    // if (import.meta.env.DEV) {
    //   console.log('收到流式数据:', data)
    // }

    if (data.event === 'workflow_started') {
      console.log('工作流开始，会话ID:', data.conversation_id, '任务ID:', data.task_id, '数据:', data)

      // 保存后端返回的对话ID
      if (data.conversation_id && currentConversationId.value) {
        updateBackendConversationId(currentConversationId.value, data.conversation_id)
        console.log('保存后端对话ID:', data.conversation_id, '到本地对话:', currentConversationId.value)
      }

      // 保存任务ID并启用停止工作流功能
      if (data.task_id) {
        currentTaskId.value = data.task_id
        canStopWorkflow.value = true
        console.log('保存任务ID:', data.task_id, '启用停止工作流功能')
      }
    } else if (data.event === 'node_started') {
      const nodeTitle = data.data?.title || data.data?.node_type || '未知节点'
      const nodeType = data.data?.node_type || 'unknown'
      console.log('节点开始:', nodeTitle)

      // 更新当前节点信息
      currentNodeInfo.value = {
        title: nodeTitle,
        nodeType: nodeType,
        isActive: true
      }

      // 更新AI消息的节点状态
      updateMessage(aiMessage.id, {
        currentNode: currentNodeInfo.value
      })

    } else if (data.event === 'node_finished') {
      const nodeTitle = data.data?.title || data.data?.node_type || '未知节点'
      console.log('节点完成:', nodeTitle)

      // 标记节点为非活跃状态
      if (currentNodeInfo.value) {
        currentNodeInfo.value.isActive = false
        updateMessage(aiMessage.id, {
          currentNode: currentNodeInfo.value
        })
      }
    } else if (data.event === 'message') {
      if (data.answer) {
        accumulatedContent += data.answer
        // 智能体回复：强制使用markdown格式，清除可能的图表数据
        updateMessage(aiMessage.id, {
          content: accumulatedContent,
          contentType: 'markdown',
          // 确保清除图表相关数据
          chartData: undefined,
          chartType: undefined,
          flowchartData: undefined
        })

        // 移除智能体消息更新的控制台输出
        // if (import.meta.env.DEV && accumulatedContent.length % 100 === 0) {
        //   console.log('智能体消息更新，当前长度:', accumulatedContent.length)
        // }

        nextTick(() => {
          scrollToBottom()
        })
      }
    } else if (data.event === 'workflow_finished') {
      updateMessage(aiMessage.id, { streaming: false })
      isStreaming.value = false
      // 重置工作流状态
      currentTaskId.value = null
      canStopWorkflow.value = false
      currentNodeInfo.value = null
      console.log('工作流完成，重置工作流状态')

      // 清除消息的节点状态
      updateMessage(aiMessage.id, {
        currentNode: null
      })

    } else if (data.event === 'message_end') {
      updateMessage(aiMessage.id, { streaming: false })
      isStreaming.value = false
      // 重置工作流状态
      currentTaskId.value = null
      canStopWorkflow.value = false
      currentNodeInfo.value = null
      console.log('消息结束，消息ID:', data.message_id, '元数据:', data.metadata)

      // 清除消息的节点状态
      updateMessage(aiMessage.id, {
        currentNode: null
      })
      // 在消息完全生成后保存AI回复消息的ID（更可靠）
      if (data.message_id) {
        updateBackendMessageId(aiMessage.id, data.message_id)
        console.log('保存AI回复消息ID:', data.message_id)

        // 检查是否需要获取建议问题
        checkAndLoadSuggestedQuestions(data.message_id)
      }
      // 注释：取消刷新对话记录列表，改为在onComplete回调中根据对话类型智能处理
      // refreshConversationList()
    }
  } catch (error) {
    console.error('解析流式数据失败:', error)
  }

  return accumulatedContent
}

// 处理停止工作流
const handleStopWorkflow = async () => {
  if (!currentTaskId.value || !currentAppId.value) {
    console.log('没有当前任务ID或appId，无法停止工作流')
    return
  }

  try {
    console.log('停止工作流:', {
      appId: currentAppId.value,
      taskId: currentTaskId.value
    })

    await exploreApi.stopWorkflow(currentAppId.value, currentTaskId.value)
    console.log('工作流停止成功')

    // 重置状态
    currentTaskId.value = null
    canStopWorkflow.value = false
    isStreaming.value = false

    // 显示成功提示
    ElMessage.success('工作流已停止')

  } catch (error) {
    console.error('停止工作流失败:', error)
    ElMessage.error('停止工作流失败，请重试')
  }
}

// 处理新对话完成后的逻辑
const handleNewConversationComplete = async () => {
  try {
    // 获取当前对话的后端ID
    const backendConversationId = getBackendConversationId()

    if (!backendConversationId || !currentAppId.value) {
      console.log('没有后端对话ID或appId，跳过新对话完成处理')
      return
    }

    console.log('处理新对话完成逻辑，后端对话ID:', backendConversationId)

    // 调用获取对话名称的接口
    try {
      // 获取当前智能体ID
      const currentAgentId = selectedModelInfo.value?.type === 'agent' ? selectedModelInfo.value.id : undefined
      console.log('获取对话名称，参数:', {
        appId: currentAppId.value,
        conversationId: backendConversationId,
        agentId: currentAgentId
      })

      const conversationNameResponse = await exploreApi.getConversationName(currentAppId.value, backendConversationId, currentAgentId)
      console.log('获取对话名称成功:', conversationNameResponse)

      // 更新对话标题
      if (conversationNameResponse.name && currentConversationId.value) {
        updateConversationTitle(currentConversationId.value, conversationNameResponse.name)
        console.log('更新对话标题为:', conversationNameResponse.name)
      }

      // 更新新对话记录的ID和名称，不需要刷新整个列表
      if (conversationSidebarRef.value) {
        console.log('更新新对话记录的ID和名称')

        // 获取对话侧边栏中当前选中的对话ID
        // 我们需要调用对话侧边栏的方法来获取当前选中的ID
        const currentSelectedId = conversationSidebarRef.value.getCurrentSelectedId()

        console.log('获取到当前选中的对话ID:', currentSelectedId)

        // 直接更新新对话记录的信息
        conversationSidebarRef.value.updateNewConversationInfo(
          currentSelectedId,
          backendConversationId,
          conversationNameResponse.name
        )

        console.log('新对话记录信息已更新:', {
          oldId: currentSelectedId,
          newId: backendConversationId,
          newName: conversationNameResponse.name
        })
      }

    } catch (error) {
      console.error('获取对话名称失败:', error)
      // 即使获取名称失败，也不影响对话的正常使用
    }

  } catch (error) {
    console.error('处理新对话完成逻辑失败:', error)
  }
}

// 模拟AI回复
const simulateAIResponse = async () => {
  console.log('simulateAIResponse 开始执行')
  console.log('当前消息数量:', currentMessages.value.length)

  // isSending现在由全局chatInputStore管理
  isStreaming.value = false

  // 启动思考动画
  startThinkingAnimation()

  // 模拟思考过程
  await simulateThinkingProcess()

  // 思考完成，开始流式输出
  isStreaming.value = true

  // 创建AI消息（用于流式输出）
  const aiMessage = addMessage({
    role: 'assistant',
    content: '',
    contentType: 'markdown', // 使用markdown渲染器
    streaming: true
  })

  console.log('AI消息已添加，当前消息数量:', currentMessages.value.length)
  console.log('当前对话ID:', currentConversationId.value)
  scrollToBottom()

  // 获取最后一条用户消息的内容，用于智能匹配渲染器
  const lastUserMessage = currentMessages.value
    .filter(msg => msg.role === 'user')
    .pop()

  const userInput = lastUserMessage?.content || ''
  console.log('用户输入内容:', userInput)

  // 使用智能内容匹配器自动选择渲染器类型
  const matchedRenderer = ContentMatcher.matchRenderer(userInput)
  console.log('智能匹配到的渲染器类型:', matchedRenderer)

  // 根据智能匹配的渲染器类型获取对应的样例数据
  let selectedResponse: string

  if (matchedRenderer && matchedRenderer !== 'text') {
    // 使用智能匹配到的渲染器样例数据
    const sample = RendererSampleGenerator.getSample(matchedRenderer)
    selectedResponse = sample.content
    // 移除渲染器样例数据选择的控制台输出
    // console.log(`使用智能匹配的${matchedRenderer}渲染器样例数据`)
  } else if (selectedRenderer.value && selectedRenderer.value !== 'text') {
    // 如果智能匹配为text但手动选中了特定的渲染器，使用手动选择的渲染器
    const sample = RendererSampleGenerator.getSample(selectedRenderer.value as RendererType)
    selectedResponse = sample.content
    // 移除手动选择渲染器样例数据的控制台输出
    // console.log(`使用手动选择的${selectedRenderer.value}渲染器的样例数据`)
  } else {
    // 如果智能匹配为text且没有手动选择特定渲染器，随机选择回复内容
    const responseOptions = [
      mockStreamData.plainText,        // PlainTextRenderer
      mockStreamData.markdownText,     // MarkdownRenderer
      mockStreamData.chartText,        // ChartRenderer
      mockStreamData.imageText,        // ImageGallery
      mockStreamData.audioText,        // AudioPlayer
      mockStreamData.videoText,        // VideoPlayer
      mockStreamData.fileText,         // FileRenderer
      mockStreamData.flowchartText,    // FlowchartRenderer
      mockStreamData.htmlText,         // HTML内容
      mockStreamData.mixedContent      // 复合内容（多种渲染器）
    ]
    selectedResponse = responseOptions[Math.floor(Math.random() * responseOptions.length)]
    // 移除随机选择回复内容的控制台输出
    // console.log('使用随机选择的回复内容')
  }

  // 开始智能流式输出
  await simulateIntelligentStream(aiMessage, selectedResponse)

  // 保存对话
  conversationStore.saveConversations()

  // isSending现在由全局chatInputStore管理
  isStreaming.value = false
}

// 智能流式输出函数 - 按token速度输出（30token/s）
const simulateIntelligentStream = async (message: Message, fullContent: string) => {
  message.streaming = true
  message.content = ''

  // 使用解析器来识别特殊内容
  const parser = new StreamContentParser()

  // 将内容按token分组进行输出
  const tokens = tokenizeForStreaming(fullContent)

  // 30 tokens/s = 1000ms / 30 = 33.33ms per token
  const tokenDelay = 1000 / 30

  await performStreamOutput(message, tokens, tokenDelay, parser)
}

// 智能体专用流式输出函数 - 不解析图表等特殊内容
const simulateAgentStream = async (message: Message, fullContent: string) => {
  message.streaming = true
  message.content = ''

  // 使用解析器，但禁用图表等特殊内容解析
  const parser = new StreamContentParser()
  parser.setOptions({
    enableChartParsing: false,
    enableFlowchartParsing: false,
    enableAudioParsing: false,
    enableVideoParsing: false,
    enableFileParsing: false
  })

  // 将内容按token分组进行输出
  const tokens = tokenizeForStreaming(fullContent)

  // 30 tokens/s = 1000ms / 30 = 33.33ms per token
  const tokenDelay = 1000 / 30

  await performStreamOutput(message, tokens, tokenDelay, parser)
}

// 通用流式输出执行函数
const performStreamOutput = async (message: Message, tokens: string[], tokenDelay: number, parser: StreamContentParser) => {
  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i]

    // 更新显示内容 - 使用store的updateMessage方法
    const newContent = message.content + token
    updateMessage(message.id, { content: newContent })

    // 移除流式输出更新的控制台输出
    // if (import.meta.env.DEV && i % 10 === 0) {
    //   console.log('流式输出更新:', message.content.length, '字符, contentType:', message.contentType)
    // }

    try {
      // 使用解析器处理当前token
      const parsedData = parser.addChunk(token)

      // 检查是否识别到了特殊内容类型
      const hasSpecialContent = parsedData.contentType !== 'text' ||
                               parsedData.chartData ||
                               (parsedData.images && parsedData.images.length > 0) ||
                               parsedData.audioContent ||
                               parsedData.videoContent ||
                               (parsedData.files && parsedData.files.length > 0) ||
                               parsedData.flowchartData

      if (hasSpecialContent) {
        // 识别到特殊内容，更新消息数据
        const validContentTypes = ['text', 'markdown', 'html', 'audio', 'video', 'file', 'chart', 'image', 'flowchart']
        const newContentType = validContentTypes.includes(parsedData.contentType)
          ? parsedData.contentType
          : 'text'

        const updates: Partial<Message> = {
          contentType: newContentType
        }

        // 只有在检测到完整的特殊内容时才设置相应的属性
        if (parsedData.chartData && parsedData.isComplete) {
          updates.chartData = parsedData.chartData
          updates.chartType = parsedData.chartType as any
        }

        if (parsedData.images && parsedData.images.length > 0) {
          updates.images = parsedData.images.map(img => img.url)
        }

        if (parsedData.audioContent) {
          updates.audioContent = parsedData.audioContent
        }

        if (parsedData.videoContent) {
          updates.videoContent = parsedData.videoContent
          // 移除视频内容检测的控制台输出
          // console.log('检测到视频内容:', parsedData.videoContent)
        }

        if (parsedData.files && parsedData.files.length > 0) {
          updates.files = parsedData.files
        }

        if (parsedData.flowchartData) {
          updates.flowchartData = parsedData.flowchartData
        }

        updateMessage(message.id, updates)
      } else {
        // 未识别到特殊内容，保持为文本类型
        updateMessage(message.id, { contentType: 'text' })
      }
    } catch (error) {
      // 如果解析器出错，确保使用默认的文本类型
      console.warn('解析器处理token时出错:', error)
      updateMessage(message.id, { contentType: 'text' })
    }

    // 固定延迟，确保30token/s的速度
    await new Promise(resolve => setTimeout(resolve, tokenDelay))

    // 每隔几个token滚动一次，提高性能
    if (i % 2 === 0) {
      scrollToBottom()
    }

    // 强制触发Vue的响应式更新
    await nextTick()
  }

  // 最后滚动一次确保到底部
  scrollToBottom()

  // 结束流式输出
  updateMessage(message.id, { streaming: false })
  parser.reset()
}



// 按token分词用于流式输出 - 模拟真实的GPT token分割
const tokenizeForStreaming = (content: string): string[] => {
  const tokens: string[] = []
  let currentToken = ''

  for (let i = 0; i < content.length; i++) {
    const char = content[i]

    // 中文字符，每个字符作为一个token
    if (/[\u4e00-\u9fa5]/.test(char)) {
      if (currentToken) {
        tokens.push(currentToken)
        currentToken = ''
      }
      tokens.push(char)
    }
    // 换行符，作为独立token
    else if (char === '\n') {
      if (currentToken) {
        tokens.push(currentToken)
        currentToken = ''
      }
      tokens.push(char)
    }
    // 重要标点符号，作为独立token
    else if (/[。！？，、；：.!?,;:]/.test(char)) {
      if (currentToken) {
        tokens.push(currentToken)
        currentToken = ''
      }
      tokens.push(char)
    }
    // 空格，结束当前token
    else if (char === ' ') {
      if (currentToken) {
        tokens.push(currentToken)
        currentToken = ''
      }
      tokens.push(char)
    }
    // 其他字符累积成token
    else {
      currentToken += char

      // 英文单词，每3-4个字符分割一次（模拟subword tokenization）
      if (/^[a-zA-Z]+$/.test(currentToken) && currentToken.length >= 4) {
        tokens.push(currentToken)
        currentToken = ''
      }
      // 数字，每2-3个数字分割一次
      else if (/^[0-9]+$/.test(currentToken) && currentToken.length >= 3) {
        tokens.push(currentToken)
        currentToken = ''
      }
      // 特殊符号组合，每2个字符分割一次
      else if (/^[`#*_\[\](){}]+$/.test(currentToken) && currentToken.length >= 2) {
        tokens.push(currentToken)
        currentToken = ''
      }
    }
  }

  // 添加最后的token
  if (currentToken) {
    tokens.push(currentToken)
  }

  return tokens
}

// 滚动到底部 - 优化版本
const scrollToBottom = (force = false) => {
  nextTick(() => {
    // 尝试多个可能的容器选择器
    const selectors = [
      '.messages-container',
      '.chat-messages',
      '.messages-list',
      '[data-messages-container]'
    ]

    let container: Element | null = null
    for (const selector of selectors) {
      container = document.querySelector(selector)
      if (container) break
    }

    if (container) {
      // 如果是强制滚动（如页面加载时），使用立即滚动
      if (force) {
        container.scrollTop = container.scrollHeight
      } else {
        // 平滑滚动到底部
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        })
      }
    }
  })
}



// 监听窗口大小变化
const handleResize = () => {
  const newIsDesktop = window.innerWidth >= 768

  // 如果从桌面端切换到移动端，自动隐藏侧边栏
  if (isDesktop.value && !newIsDesktop && showConversationList.value) {
    showConversationList.value = false
  }
  // 如果从移动端切换到桌面端，自动显示侧边栏
  else if (!isDesktop.value && newIsDesktop && !showConversationList.value) {
    showConversationList.value = true
  }

  isDesktop.value = newIsDesktop
}

// 初始化侧边栏显示状态
const initSidebarVisibility = () => {
  // PC端默认显示侧边栏，移动端默认隐藏
  showConversationList.value = isDesktop.value
}

// 获取聊天参数配置
const getChatParameters = async (appId: string) => {
  try {
    console.log('获取聊天参数配置，appId:', appId)
    const response = await exploreApi.getChatParameters(appId)
    if (response.success && response.data) {
      chatParameters.value = response.data
      console.log('聊天参数配置:', chatParameters.value)
      return response.data
    }
  } catch (error) {
    console.error('获取聊天参数配置失败:', error)
  }
  return null
}

// 获取建议问题
const getSuggestedQuestions = async (appId: string, messageId: string) => {
  if (!appId || !messageId) {
    console.log('appId或messageId为空，跳过获取建议问题')
    return
  }

  try {
    loadingSuggestedQuestions.value = true
    console.log('获取建议问题，appId:', appId, 'messageId:', messageId)

    const response = await exploreApi.getSuggestedQuestions(appId, messageId)
    if (response.success && response.data && response.data.data) {
      suggestedQuestions.value = response.data.data
      showSuggestedQuestions.value = suggestedQuestions.value.length > 0
      console.log('获取到建议问题:', suggestedQuestions.value)
    } else {
      console.log('没有获取到建议问题')
      suggestedQuestions.value = []
      showSuggestedQuestions.value = false
    }
  } catch (error) {
    console.error('获取建议问题失败:', error)
    suggestedQuestions.value = []
    showSuggestedQuestions.value = false
  } finally {
    loadingSuggestedQuestions.value = false
  }
}

// 检查并加载建议问题
const checkAndLoadSuggestedQuestions = async (messageId: string) => {
  if (!currentAppId.value || !messageId) {
    console.log('缺少appId或messageId，跳过建议问题检查')
    return
  }

  try {
    // 先获取聊天参数配置（如果还没有获取过）
    if (!chatParameters.value) {
      await getChatParameters(currentAppId.value)
    }

    // 检查是否启用了回答后建议问题功能
    const suggestedQuestionsAfterAnswer = chatParameters.value?.suggested_questions_after_answer
    if (suggestedQuestionsAfterAnswer?.enabled === true) {
      console.log('启用了回答后建议问题功能，开始获取建议问题')
      await getSuggestedQuestions(currentAppId.value, messageId)
    } else {
      console.log('未启用回答后建议问题功能')
      showSuggestedQuestions.value = false
      suggestedQuestions.value = []
    }
  } catch (error) {
    console.error('检查建议问题配置失败:', error)
  }
}

// 处理建议问题点击
const handleSuggestedQuestionClick = (question: string) => {
  console.log('点击建议问题:', question)

  // 隐藏建议问题
  showSuggestedQuestions.value = false
  suggestedQuestions.value = []

  // 设置输入框内容并发送消息
  chatInputStore.inputMessage = question
  chatInputStore.handleSendMessage()
}

// 处理参数值更新
const handleParameterValuesUpdate = (values: Record<string, any>) => {
  parameterValues.value = values
  console.log('参数值已更新:', values)
}

// 处理参数配置收起
const handleParameterConfigCollapse = () => {
  console.log('🔧 收起参数配置')
  showParameterConfig.value = false
}

// 处理参数配置展开
const handleParameterConfigExpand = () => {
  console.log('🔧 展开参数配置')
  showParameterConfig.value = true
}

// 处理参数验证变化
const handleParameterValidationChange = (isValid: boolean, errors: string[]) => {
  parameterValidation.value = { isValid, errors }
  console.log('🔧 参数验证状态:', isValid, errors)
}

// 处理参数值，转换文件参数为API需要的格式
const processParameterValues = (values: Record<string, any>): Record<string, any> => {
  const processed: Record<string, any> = {}

  for (const [key, value] of Object.entries(values)) {
    if (value && typeof value === 'object') {
      if (Array.isArray(value)) {
        // 批量文件 (file-list)
        processed[key] = value.map(file => ({
          type: file.type || 'document',
          transfer_method: file.transfer_method || 'local_file',
          url: file.url || '',
          upload_file_id: file.upload_file_id
        }))
      } else if (value.upload_file_id) {
        // 单个文件 (file)
        processed[key] = {
          type: value.type || 'document',
          transfer_method: value.transfer_method || 'local_file',
          url: value.url || '',
          upload_file_id: value.upload_file_id
        }
      } else {
        // 其他对象类型参数
        processed[key] = value
      }
    } else {
      // 普通参数（字符串、数字等）
      processed[key] = value
    }
  }

  console.log('🔧 参数值转换:', values, '->', processed)
  return processed
}

// 加载聊天参数配置
const loadChatParameters = async (appId: string) => {
  try {
    console.log('🔧 开始加载聊天参数配置，appId:', appId)

    // 调用真实的API
    const response = await exploreApi.getChatParameters(appId)
    console.log('📡 API响应:', response)

    if (response.success && response.data) {
      chatParameters.value = response.data
      console.log('✅ 聊天参数配置:', chatParameters.value)

      // 检查是否有用户输入表单配置
      if (response.data.user_input_form && response.data.user_input_form.length > 0) {
        userInputFormParameters.value = response.data.user_input_form
        showParameterConfig.value = true

        // 初始化参数默认值
        const defaultValues: Record<string, any> = {}
        response.data.user_input_form.forEach((param: any) => {
          if (param.select && param.select.options && param.select.options.length > 0) {
            // 下拉框默认选择第一个选项
            defaultValues[param.select.variable] = param.select.options[0]
          } else if (param.variable) {
            // 直接结构的文本输入框
            defaultValues[param.variable] = ''
          } else if (param['text-input']) {
            // 包装结构的文本输入框
            defaultValues[param['text-input'].variable] = ''
          } else if (param['paragraph']) {
            // 段落文本框
            defaultValues[param['paragraph'].variable] = ''
          } else if (param['number']) {
            // 数字输入框
            defaultValues[param['number'].variable] = 0
          } else if (param['file']) {
            // 单个文件上传
            defaultValues[param['file'].variable] = null
          } else if (param['file-list']) {
            // 批量文件上传
            defaultValues[param['file-list'].variable] = []
          }
        })
        parameterValues.value = defaultValues

        console.log('✅ 显示真实参数配置，参数数量:', userInputFormParameters.value.length)
        console.log('✅ 默认参数值:', defaultValues)
      } else {
        console.log('ℹ️ 该智能体没有用户输入表单配置，隐藏参数配置')
        showParameterConfig.value = false
        userInputFormParameters.value = []
        parameterValues.value = {}
      }
    } else {
      console.log('⚠️ API响应无效，隐藏参数配置')
      showParameterConfig.value = false
      userInputFormParameters.value = []
      parameterValues.value = {}
    }
  } catch (error) {
    console.error('❌ 加载聊天参数配置失败:', error)
    console.log('❌ API调用失败，隐藏参数配置')
    // API失败时隐藏参数配置
    showParameterConfig.value = false
    userInputFormParameters.value = []
    parameterValues.value = {}
  }
}

// 生命周期
onMounted(() => {
  // 初始化侧边栏状态
  initSidebarVisibility()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)

  // 初始化默认选中的模型信息
  if (!selectedModelInfo.value && selectedModelId.value) {
    // 如果有默认的modelId但没有modelInfo，则设置默认模型信息
    setSelectedModel(selectedModelId.value)
    console.log('初始化默认模型:', selectedModelId.value)
  }

  // 如果没有对话，创建一个新的
  if (conversations.value.length === 0) {
    createNewConversation()
  } else if (!currentConversationId.value) {
    // 如果有对话但没有选中任何对话，则选中第一条活跃对话
    const activeConvs = conversations.value.filter(c => !c.archived)
    if (activeConvs.length > 0) {
      selectConversation(activeConvs[0].id)
      console.log('自动选中第一条对话:', activeConvs[0].id)
    }
  }

  // 页面加载完成后滚动到底部
  nextTick(() => {
    setTimeout(() => {
      scrollToBottom(true) // 使用强制滚动
    }, 200) // 增加延迟确保DOM完全渲染
  })
})

onUnmounted(() => {
  // 清理录音相关资源
  if (recordingTimer) {
    clearInterval(recordingTimer)
  }

  if (mediaRecorder && chatInputStore.isRecording) {
    stopRecording()
  }

  // 清理窗口监听器
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 固定底部输入区域样式 */
.input-area {
  transition: height 0.2s ease-out;
  min-height: 60px;
}

.input-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 隐藏 el-input 的边框 */
.borderless-input :deep(.el-textarea__inner) {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  background: transparent !important;
  resize: none !important;
}

.borderless-input :deep(.el-textarea__inner):focus {
  border: none !important;
  box-shadow: none !important;
}

/* 字数统计样式调整 */
.borderless-input :deep(.el-input__count) {
  background: transparent !important;
  right: 0 !important;
  bottom: -2px !important;
}

.input-wrapper {
  min-height: 60px;
  padding: 16px;
  flex: 1;
}

.integrated-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.integrated-input-container textarea {
  width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  transition: all 0.2s ease-out;
}

.integrated-input-container textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.integrated-input-container textarea:focus + .absolute,
.integrated-input-container:focus-within .absolute {
  z-index: 10;
}

/* 整合输入框样式 */
.integrated-input-container textarea {
  transition: height 0.2s ease-out, border-color 0.2s ease, box-shadow 0.2s ease;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
  overflow-y: auto;
}

.integrated-input-container textarea::-webkit-scrollbar {
  width: 4px;
}

.integrated-input-container textarea::-webkit-scrollbar-track {
  background: transparent;
}

.integrated-input-container textarea::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 2px;
}

.integrated-input-container textarea::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* 功能按钮样式 */
.integrated-input-container .upload-btn,
.integrated-input-container .voice-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
  border-radius: 8px;
}

.integrated-input-container .send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
  border-radius: 8px;
  font-weight: 500;
}

/* 确保按钮不会被textarea遮盖 */
.integrated-input-container .absolute {
  z-index: 10;
}

/* 字符计数样式 */
.character-count {
  transition: color 0.2s ease;
}



/* 打字效果 */
.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #9CA3AF;
  display: inline-block;
  animation: typing 1.4s infinite ease-in-out both;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 录音指示器 */
.recording-dot {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 消息容器样式 */
.messages-container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.messages-wrapper {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.message-item {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.message-content {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  box-sizing: border-box;
}



/* 智能体卡片滚动条样式 */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 卡片悬停效果增强 */
.group:hover .transform {
  transform: translateX(4px);
}

/* 思考动画相关样式 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.95;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.animate-shimmer {
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

/* 思考步骤指示器样式 */
.thinking-steps {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.thinking-steps::-webkit-scrollbar {
  display: none;
}

.step-indicator {
  white-space: nowrap;
  flex-shrink: 0;
}

/* 进度条增强样式 */
.progress-section .bg-gradient-to-r {
  background-image: linear-gradient(
    90deg,
    #3b82f6 0%,
    #8b5cf6 25%,
    #06b6d4 50%,
    #8b5cf6 75%,
    #3b82f6 100%
  );
}

/* 响应式设计 */
@media (max-width: 1280px) {
  .max-w-7xl {
    max-width: 1024px;
  }
}

@media (max-width: 1024px) {
  .grid.xl\\:grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .max-w-7xl {
    max-width: 768px;
  }
}

@media (max-width: 768px) {
  .floating-input-container {
    margin: 0 16px;
    width: calc(100vw - 32px) !important;
  }

  .conversation-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 320px;
    z-index: 50;
    transform: translateX(0);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }

  /* 移动端遮罩层 */
  .ai-explore::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .ai-explore.sidebar-open::before {
    opacity: 1;
    visibility: visible;
  }



  .rounded-2xl {
    border-radius: 1rem;
  }

  .p-6 {
    padding: 1rem;
  }
}

/* 对话记录选中状态样式 */
.conversation-item {
  position: relative;
  overflow: hidden;
}

/* 选中状态的微妙动画效果 */
.conversation-item.selected {
  transform: translateX(2px);
}

/* 选中指示器动画 */
.conversation-item .selected-indicator {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 4px;
    opacity: 1;
  }
}

/* 对话操作下拉菜单样式 */
.conversation-item :deep(.el-dropdown) {
  display: flex;
  align-items: center;
}

.conversation-item :deep(.el-dropdown-menu__item) {
  padding: 3px 16px;
  font-size: 14px;
}

.conversation-item :deep(.el-dropdown-menu__item.text-red-600) {
  color: #dc2626;
}

.conversation-item :deep(.el-dropdown-menu__item.text-red-600:hover) {
  background-color: #fef2f2;
  color: #dc2626;
}

@media (max-width: 640px) {
  .floating-input-container {
    margin: 0 8px;
    width: calc(100vw - 16px) !important;
    border-radius: 16px 16px 0 0;
  }

  .conversation-sidebar {
    width: 300px;
  }
}

/* 用户下拉菜单样式 */
.el-dropdown-menu__item.text-red-600 {
  color: #dc2626 !important;
}

.el-dropdown-menu__item.text-red-600:hover {
  background-color: #fef2f2 !important;
  color: #dc2626 !important;
}

/* 对话列表增强动画效果 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.4s ease-out forwards;
  opacity: 0;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out forwards;
  opacity: 0;
}

/* 切换状态动画 */
.archive-transition-enter-active,
.archive-transition-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.archive-transition-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.archive-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

/* 状态指示器动画 */
.status-indicator {
  position: relative;
  overflow: hidden;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.status-indicator:hover::before {
  left: 100%;
}

/* 对话项悬浮效果增强 */
.conversation-item {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.conversation-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 0;
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
  border-radius: 0 2px 2px 0;
  transition: height 0.3s ease;
}

.conversation-item:hover::before {
  height: 60%;
}

.conversation-item.bg-gradient-to-r::before {
  height: 80%;
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
}

/* 响应式动画优化 */
@media (max-width: 768px) {
  .animate-fade-in,
  .animate-slide-in {
    animation-duration: 0.2s;
  }

  .conversation-item::before {
    width: 2px;
  }
}

/* 切换按钮悬浮效果 */
.group:hover .group-hover\\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\\:translate-x-0_5 {
  transform: translateX(0.125rem);
}
</style>
