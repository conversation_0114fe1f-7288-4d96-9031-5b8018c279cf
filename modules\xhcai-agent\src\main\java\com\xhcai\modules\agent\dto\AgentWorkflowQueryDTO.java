package com.xhcai.modules.agent.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 智能体工作流查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "智能体工作流查询条件")
public class AgentWorkflowQueryDTO extends PageTimeRangeQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    @Size(max = 36, message = "智能体ID长度不能超过36个字符")
    private String agentId;

    /**
     * 工作流名称（模糊查询）
     */
    @Schema(description = "工作流名称", example = "客服工作流")
    @Size(max = 100, message = "工作流名称长度不能超过100个字符")
    private String name;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1")
    private Integer version;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    /**
     * 是否已发布
     */
    @Schema(description = "是否已发布", example = "false")
    private Boolean isPublished;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "node_add")
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    private String operationType;

    /**
     * 是否查询最新版本
     */
    @Schema(description = "是否查询最新版本", example = "true")
    private Boolean latestVersion;

    // Getters and Setters
    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public Boolean getLatestVersion() {
        return latestVersion;
    }

    public void setLatestVersion(Boolean latestVersion) {
        this.latestVersion = latestVersion;
    }
}
