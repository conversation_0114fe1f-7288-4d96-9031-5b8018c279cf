package com.xhcai.common.security.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.DelegatingSecurityContextRepository;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.web.cors.CorsConfigurationSource;

import com.xhcai.common.security.filter.JwtAuthenticationFilter;

/**
 * Spring Security配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private static final Logger log = LoggerFactory.getLogger(SecurityConfig.class);

    @Autowired
    private CorsConfigurationSource corsConfigurationSource;

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    /**
     * 开发环境安全配置 - 启用认证但放宽部分限制
     */
    @Bean
    @Profile("dev")
    @Order(1)
    public SecurityFilterChain devSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF（因为使用JWT）
                .csrf(AbstractHttpConfigurer::disable)
                // 启用CORS
                .cors(cors -> cors.configurationSource(corsConfigurationSource))
                // 配置授权规则
                .authorizeHttpRequests(auth -> auth
                // 公开接口 - 开发环境下完全开放 Swagger
                .requestMatchers(
                        "/api/auth/**",
                        "/api/public/**",
                        "/api/platform/init/**",
                        "/api/dify/test/**",  // Dify测试接口
                        "/actuator/**",
                        "/v3/api-docs/**",
                        "/swagger-ui/**",
                        "/swagger-ui.html",
                        "/swagger-resources/**",
                        "/doc.html",
                        "/webjars/**",
                        "/favicon.ico",
                        "/error",
                        "/login",
                        "/css/**",
                        "/js/**",
                        "/images/**"
                ).permitAll()
                // 其他请求需要认证
                .anyRequest().authenticated()
                )
                // 无状态会话（使用JWT）
                .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                // 禁用默认登录页面
                .formLogin(AbstractHttpConfigurer::disable)
                .httpBasic(AbstractHttpConfigurer::disable)
                // 配置SecurityContext存储库以支持异步请求
                .securityContext(securityContext -> securityContext
                .securityContextRepository(createSecurityContextRepository())
                )
                // 添加JWT认证过滤器（已支持SSE认证）
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * 生产环境安全配置 - 启用完整的安全控制
     */
    @Bean
    @Profile("prod")
    @Order(1)
    public SecurityFilterChain prodSecurityFilterChain(HttpSecurity http) throws Exception {
        log.info("=== 配置生产环境安全过滤器链 ===");

        http
                // 禁用CSRF（因为使用JWT）
                .csrf(AbstractHttpConfigurer::disable)
                // 启用CORS
                .cors(cors -> cors.configurationSource(corsConfigurationSource))
                // 配置授权规则
                .authorizeHttpRequests(auth -> auth
                // 公开接口
                .requestMatchers(
                        "/api/auth/**",
                        "/api/public/**",
                        "/api/platform/init/**",
                        "/api/dify/test/**",  // Dify测试接口
                        "/actuator/**",
                        "/v3/api-docs/**",
                        "/swagger-ui/**",
                        "/swagger-ui.html",
                        "/swagger-resources/**",
                        "/doc.html",
                        "/webjars/**",
                        "/favicon.ico",
                        "/error",
                        "/login",
                        "/css/**",
                        "/js/**",
                        "/images/**"
                ).permitAll()
                // 其他请求需要认证
                .anyRequest().authenticated()
                )
                // 无状态会话（使用JWT）
                .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                // 禁用默认登录页面
                .formLogin(AbstractHttpConfigurer::disable)
                .httpBasic(AbstractHttpConfigurer::disable)
                // 配置SecurityContext存储库以支持异步请求
                .securityContext(securityContext -> securityContext
                .securityContextRepository(createSecurityContextRepository())
                )
                // 添加JWT认证过滤器（已支持SSE认证）
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        log.info("=== 生产环境安全过滤器链配置完成 ===");
        return http.build();
    }

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 创建SecurityContext存储库 支持异步请求的SecurityContext传递
     */
    private SecurityContextRepository createSecurityContextRepository() {
        return new DelegatingSecurityContextRepository(
                new RequestAttributeSecurityContextRepository(),
                new HttpSessionSecurityContextRepository()
        );
    }
}
