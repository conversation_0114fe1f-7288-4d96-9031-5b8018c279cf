<template>
  <div class="edit-dataset-modal" v-show="visible" @click="handleModalClick">
    <div class="modal-content" @click.stop>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <div class="header-left">
          <div class="dataset-info" v-if="dataset">
            <div class="dataset-avatar" :style="{ backgroundColor: dataset.iconBg || '#3b82f6' }">
              <i :class="dataset.icon || '📚'"></i>
            </div>
            <div class="dataset-details">
              <h3>编辑知识库</h3>
              <span class="dataset-unit">{{ dataset.unit || '默认单位' }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <el-button 
            :icon="Close"
            @click="handleClose"
            class="close-btn"
          />
        </div>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-body">
        <el-form 
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          label-position="left"
        >
          <el-form-item label="知识库名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入知识库名称"
              maxlength="100"
              show-word-limit
              clearable
            />
          </el-form-item>

          <el-form-item label="知识库描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              placeholder="请输入知识库描述信息"
              :rows="4"
              maxlength="500"
              show-word-limit
              resize="none"
            />
          </el-form-item>

          <el-form-item label="知识库图标" prop="icon">
            <div class="icon-selector-wrapper">
              <div class="current-icon" :style="{ backgroundColor: formData.iconBg }">
                <i :class="formData.icon"></i>
              </div>
              <el-button 
                type="primary" 
                plain 
                @click="showIconSelector = true"
                :icon="Edit"
              >
                更换图标
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="访问权限" prop="isPublic">
            <el-radio-group v-model="formData.isPublic">
              <el-radio :value="false">私有</el-radio>
              <el-radio :value="true">公开</el-radio>
            </el-radio-group>
            <div class="form-tip">
              <el-icon><InfoFilled /></el-icon>
              <span>私有知识库仅创建者可见，公开知识库团队成员均可访问</span>
            </div>
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="formData.status"
              :active-value="'1'"
              :inactive-value="'0'"
              active-text="启用"
              inactive-text="禁用"
              active-color="#67c23a"
              inactive-color="#f56c6c"
            />
            <div class="form-tip">
              <el-icon><InfoFilled /></el-icon>
              <span>禁用后知识库将无法被检索和使用</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <div class="footer-left">
          <div class="edit-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>修改后将立即生效</span>
          </div>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleSave"
            :loading="saving"
            :disabled="!isFormValid"
          >
            保存
          </el-button>
        </div>
      </div>
    </div>

    <!-- 图标选择器 -->
    <IconSelector
      v-model:visible="showIconSelector"
      @select="handleIconSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElRadioGroup,
  ElRadio,
  ElSwitch,
  ElIcon,
  ElMessage,
  type FormInstance,
  type FormRules
} from 'element-plus'
import {
  Close,
  Edit,
  InfoFilled
} from '@element-plus/icons-vue'
import IconSelector from '@/components/common/IconSelector.vue'
import { RagAPI, type UpdateDatasetRequest } from '@/api/rag'

// 知识库数据类型
interface Dataset {
  id: string
  name: string
  description?: string
  icon?: string
  iconBg?: string
  unit?: string
  isPublic?: boolean
  status?: string
  [key: string]: any
}

// Props
interface Props {
  visible: boolean
  dataset: Dataset | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  dataset: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'save': [dataset: Dataset]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const saving = ref(false)
const showIconSelector = ref(false)

// 表单数据
const formData = ref({
  name: '',
  description: '',
  icon: '📚',
  iconBg: '#3b82f6',
  isPublic: false,
  status: '1'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 1, max: 100, message: '知识库名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '知识库描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const isFormValid = computed(() => {
  return formData.value.name.trim().length > 0
})

// 监听数据集变化，初始化表单
watch(() => props.dataset, (newDataset) => {
  if (newDataset) {
    formData.value = {
      name: newDataset.name || '',
      description: newDataset.description || '',
      icon: newDataset.icon || '📚',
      iconBg: newDataset.iconBg || '#3b82f6',
      isPublic: newDataset.isPublic || false,
      status: newDataset.status || '1'
    }
  }
}, { immediate: true })

// 方法
const handleModalClick = () => {
  handleClose()
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    icon: '📚',
    iconBg: '#3b82f6',
    isPublic: false,
    status: '1'
  }
  showIconSelector.value = false
  saving.value = false
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleIconSelect = (data: { icon: string; background: string }) => {
  formData.value.icon = data.icon
  formData.value.iconBg = data.background
  showIconSelector.value = false
}

const handleSave = async () => {
  if (!props.dataset?.id) {
    ElMessage.error('知识库ID不存在')
    return
  }

  // 表单验证
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return

  try {
    saving.value = true

    // 构建更新请求数据
    const updateData: UpdateDatasetRequest = {
      name: formData.value.name.trim(),
      description: formData.value.description.trim(),
      icon: formData.value.icon,
      iconBg: formData.value.iconBg,
      isPublic: formData.value.isPublic,
      status: formData.value.status
    }

    // 调用后端API更新知识库
    await RagAPI.updateDataset(props.dataset.id, updateData)
    
    ElMessage.success('更新知识库成功')
    
    // 触发保存事件，传递更新后的数据
    const updatedDataset: Dataset = {
      ...props.dataset,
      ...updateData
    }
    
    emit('save', updatedDataset)
    handleClose()
  } catch (error) {
    console.error('更新知识库失败:', error)
    ElMessage.error('更新知识库失败，请重试')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.edit-dataset-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.header-left {
  flex: 1;
}

.dataset-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dataset-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  font-weight: 500;
}

.dataset-details h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.dataset-unit {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background: var(--el-fill-color-light);
  padding: 2px 8px;
  border-radius: 4px;
}

.close-btn {
  padding: 8px;
  border: none;
  background: none;
  color: var(--el-text-color-secondary);
}

.close-btn:hover {
  color: var(--el-text-color-primary);
  background: var(--el-fill-color-light);
}

.modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.icon-selector-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  font-weight: 500;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid var(--el-border-color-light);
  background: var(--el-bg-color-page);
}

.footer-left {
  flex: 1;
}

.edit-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.footer-right {
  display: flex;
  gap: 12px;
}
</style>
