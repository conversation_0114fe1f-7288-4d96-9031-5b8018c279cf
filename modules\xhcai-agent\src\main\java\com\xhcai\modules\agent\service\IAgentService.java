package com.xhcai.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.agent.dto.AgentCreateDTO;
import com.xhcai.modules.agent.dto.AgentQueryDTO;
import com.xhcai.modules.agent.dto.AgentUpdateDTO;
import com.xhcai.modules.agent.dto.ThirdPartyAgentCreateDTO;
import com.xhcai.modules.agent.entity.Agent;
import com.xhcai.modules.agent.mapper.AgentMapper.AgentStatsVO;
import com.xhcai.modules.agent.vo.AgentVO;

import java.util.List;

/**
 * 智能体服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IAgentService extends IService<Agent> {

    /**
     * 分页查询智能体列表
     *
     * @param queryDTO 查询条件
     * @return 智能体列表
     */
    PageResult<AgentVO> getAgentPage(AgentQueryDTO queryDTO);

    /**
     * 根据ID查询智能体详情
     *
     * @param id 智能体ID
     * @return 智能体详情
     */
    AgentVO getAgentById(String id);

    /**
     * 创建智能体
     *
     * @param createDTO 创建信息
     * @return 智能体ID
     */
    String createAgent(AgentCreateDTO createDTO);

    /**
     * 更新智能体
     *
     * @param updateDTO 更新信息
     * @return 是否成功
     */
    boolean updateAgent(AgentUpdateDTO updateDTO);

    /**
     * 删除智能体
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean deleteAgent(String id);

    /**
     * 批量删除智能体
     *
     * @param ids 智能体ID列表
     * @return 是否成功
     */
    boolean batchDeleteAgents(List<String> ids);

    /**
     * 启用智能体
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean enableAgent(String id);

    /**
     * 禁用智能体
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean disableAgent(String id);

    /**
     * 批量更新智能体状态
     *
     * @param ids 智能体ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> ids, String status);

    /**
     * 复制智能体
     *
     * @param id 智能体ID
     * @param name 新名称
     * @return 新智能体ID
     */
    String copyAgent(String id, String name);

    /**
     * 发布智能体
     *
     * @param id 智能体ID
     * @param version 版本号
     * @return 是否成功
     */
    boolean publishAgent(String id, String version);

    /**
     * 设置智能体公开状态
     *
     * @param id 智能体ID
     * @param isPublic 是否公开
     * @return 是否成功
     */
    boolean setAgentPublic(String id, boolean isPublic);

    /**
     * 查询用户的智能体列表
     *
     * @param userId 用户ID
     * @return 智能体列表
     */
    List<AgentVO> getUserAgents(String userId);

    /**
     * 查询公开的智能体列表
     *
     * @return 智能体列表
     */
    List<AgentVO> getPublicAgents();

    /**
     * 检查智能体名称是否存在
     *
     * @param name 智能体名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkNameExists(String name, String excludeId);

    /**
     * 更新智能体对话统计信息
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean updateConversationStats(String id);

    /**
     * 获取智能体统计信息
     *
     * @return 统计信息
     */
    AgentStatsVO getAgentStats();

    /**
     * 获取已关联的智能体列表
     *
     * @param platformId 平台ID（可选）
     * @return 已关联的智能体列表
     */
    List<AgentVO> getAssociatedAgents(String platformId);

    /**
     * 获取AI探索智能体列表
     * 只查询type为advanced-chat的智能体
     *
     * @return AI探索智能体列表
     */
    List<AgentVO> getAiExploreAgents();

    /**
     * 验证智能体配置
     *
     * @param agent 智能体信息
     * @return 验证结果
     */
    boolean validateAgentConfig(Agent agent);

    /**
     * 导出智能体配置
     *
     * @param id 智能体ID
     * @return 配置JSON
     */
    String exportAgentConfig(String id);

    /**
     * 导入智能体配置
     *
     * @param configJson 配置JSON
     * @param name 智能体名称
     * @return 智能体ID
     */
    String importAgentConfig(String configJson, String name);

    /**
     * 批量关联智能体到项目
     *
     * @param agentIds 智能体ID列表
     * @param projectId 项目ID
     * @return 是否成功
     */
    boolean linkAgentsToProject(List<String> agentIds, String projectId);

    /**
     * 获取未关联项目的智能体列表
     *
     * @param queryDTO 查询条件
     * @return 智能体列表
     */
    PageResult<AgentVO> getUnlinkedAgents(AgentQueryDTO queryDTO);

    /**
     * 创建第三方智能体
     *
     * @param createDTO 创建请求
     * @return 智能体ID
     */
    String createThirdPartyAgent(ThirdPartyAgentCreateDTO createDTO);
}
