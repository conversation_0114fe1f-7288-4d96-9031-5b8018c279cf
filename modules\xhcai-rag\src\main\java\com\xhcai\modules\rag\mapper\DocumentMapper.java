package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.vo.DocumentVO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * 文档Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface DocumentMapper extends BaseMapper<Document> {

    /**
     * 根据知识库ID分页查询文档列表
     *
     * @param page 分页参数
     * @param datasetId 知识库ID
     * @param name 文档名称（模糊查询）
     * @param documentStatus 索引状态
     * @param enabled 是否启用
     * @return 文档分页列表
     */
    @Select("<script>"
            + "SELECT ds.dict_label as document_status_desc, ds.list_class as document_status_bg_color, dc.name as category_name, d.dict_label as doc_type_name, d.css_class as doc_icon, d.list_class as doc_style ,dt.* FROM documents dt "
            + "left join document_categories dc on dt.category_id = dc.id "
            + "left join sys_dict_data d on d.dict_type in ('documents', 'images', 'audios', 'videos','code') and d.dict_value  = dt.doc_type "
            + "left join sys_dict_data ds on ds.dict_type = 'document_status' and ds.dict_value  = dt.document_status "
            + "WHERE dt.deleted = 0 "
            + "<if test='datasetId != null and datasetId != \"\"'>"
            + "  AND dt.dataset_id = #{datasetId} "
            + "</if>"
            + "<if test='name != null and name != \"\"'>"
            + "  AND dt.name LIKE CONCAT('%', #{name}, '%') "
            + "</if>"
            + "<if test='documentStatus != null and documentStatus != \"\"'>"
            + "  AND dt.document_status = #{documentStatus} "
            + "</if>"
            + "<if test='batch != null'>"
            + "  AND dt.batch = #{batch} "
            + "</if>"
            + "<if test='enabled != null'>"
            + "  AND dt.enabled = #{enabled} "
            + "</if>"
            + "ORDER BY dt.create_time DESC"
            + "</script>")
    @Results({
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    IPage<DocumentVO> selectPageByDataset(Page<Document> page,
                                          @Param("datasetId") String datasetId,
                                          @Param("name") String name,
                                          @Param("documentStatus") String documentStatus,
                                          @Param("enabled") Boolean enabled,
                                          @Param("batch") String batch);

    /**
     * 根据文档ID查询文档信息
     *
     * @param documentId 文档ID
     * @return 文档信息
     */
    @Select("SELECT * FROM documents WHERE id = #{documentId} AND deleted = 0")
    @Results({
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    Document selectByDocumentId(@Param("documentId") String documentId);

    /**
     * 根据知识库ID查询文档列表
     *
     * @param datasetId 知识库ID
     * @return 文档列表
     */
    @Select("SELECT * FROM documents WHERE dataset_id = #{datasetId} AND deleted = 0 ORDER BY position ASC")
    @Results({
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    List<DocumentVO> selectByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 根据知识库ID统计文档数量
     *
     * @param datasetId 知识库ID
     * @return 文档数量
     */
    @Select("SELECT COUNT(*) FROM documents WHERE dataset_id = #{datasetId} AND deleted = 0")
    Long countByDatasetId(@Param("datasetId") String datasetId);

    /**
     * 根据知识库ID和状态统计文档数量
     *
     * @param datasetId 知识库ID
     * @param documentStatus 索引状态
     * @return 文档数量
     */
    @Select("SELECT COUNT(*) FROM documents WHERE dataset_id = #{datasetId} AND document_status = #{documentStatus} AND deleted = 0")
    Long countByDatasetIdAndStatus(@Param("datasetId") String datasetId, @Param("documentStatus") String documentStatus);

    /**
     * 根据批次号查询文档列表
     *
     * @param batch 批次号
     * @return 文档列表
     */
    @Select("SELECT * FROM documents WHERE batch = #{batch} AND deleted = 0 ORDER BY position ASC")
    @Results({
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    List<DocumentVO> selectByBatch(@Param("batch") String batch);

    /**
     * 查询等待处理的文档列表
     *
     * @param limit 限制数量
     * @return 文档列表
     */
    @Select("SELECT * FROM documents WHERE document_status = 'index_waiting' AND deleted = 0 ORDER BY create_time ASC LIMIT #{limit}")
    @Results({
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    List<Document> selectWaitingDocuments(@Param("limit") Integer limit);

    /**
     * 查询处理中的文档列表
     *
     * @return 文档列表
     */
    @Select("SELECT * FROM documents WHERE document_status IN ('uploading', 'processing', 'parsing', 'cleaning', 'splitting', 'indexing') AND deleted = 0")
    @Results({
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    List<Document> selectProcessingDocuments();

    /**
     * 更新文档索引状态
     *
     * @param id 文档ID
     * @param documentStatus 索引状态
     * @return 更新行数
     */
    @Update("UPDATE documents SET document_status = #{documentStatus}, update_time = NOW() WHERE id = #{id}")
    int updateDocumentStatus(@Param("id") String id, @Param("documentStatus") String documentStatus);

    /**
     * 更新文档处理进度
     *
     * @param id 文档ID
     * @param documentStatus 索引状态
     * @param wordCount 字符数
     * @param tokens token数
     * @return 更新行数
     */
    @Update("UPDATE documents SET document_status = #{documentStatus}, word_count = #{wordCount}, tokens = #{tokens}, update_time = NOW() WHERE id = #{id}")
    int updateProcessingProgress(@Param("id") String id,
            @Param("documentStatus") String documentStatus,
            @Param("wordCount") Integer wordCount,
            @Param("tokens") Integer tokens);

    /**
     * 更新文档错误信息
     *
     * @param id 文档ID
     * @param error 错误信息
     * @return 更新行数
     */
    @Update("UPDATE documents SET document_status = #{documentStatus}, error = #{error}, stopped_at = NOW(), update_time = NOW() WHERE id = #{id}")
    int updateError(@Param("id") String id, @Param("documentStatus") String documentStatus, @Param("error") String error);

    /**
     * 暂停文档处理
     *
     * @param id 文档ID
     * @param pausedBy 暂停人ID
     * @return 更新行数
     */
    @Update("UPDATE documents SET is_paused = true, paused_by = #{pausedBy}, paused_at = NOW(), update_time = NOW() WHERE id = #{id}")
    int pauseDocument(@Param("id") String id, @Param("pausedBy") String pausedBy);

    /**
     * 恢复文档处理
     *
     * @param id 文档ID
     * @return 更新行数
     */
    @Update("UPDATE documents SET is_paused = false, paused_by = NULL, paused_at = NULL, update_time = NOW() WHERE id = #{id}")
    int resumeDocument(@Param("id") String id);

    /**
     * 更新文档分段开始时间
     *
     * @param documentId 文档ID
     * @return 更新行数
     */
    @Update("UPDATE documents SET processing_started_at = NOW(), update_time = NOW() WHERE id = #{documentId}")
    int updateProcessingStartedAt(@Param("documentId") String documentId);

    /**
     * 更新文档分段完成时间
     *
     * @param documentId 文档ID
     * @return 更新行数
     */
    @Update("UPDATE documents SET splitting_completed_at = NOW(), update_time = NOW() WHERE id = #{documentId}")
    int updateSplittingCompletedAt(@Param("documentId") String documentId);

    /**
     * 启用/禁用文档
     *
     * @param id 文档ID
     * @param enabled 是否启用
     * @param operatorId 操作人ID
     * @return 更新行数
     */
    @Update("<script>"
            + "UPDATE documents SET enabled = #{enabled}, update_time = NOW() "
            + "<if test='!enabled'>"
            + ", disabled_by = #{operatorId}, disabled_at = NOW() "
            + "</if>"
            + "<if test='enabled'>"
            + ", disabled_by = NULL, disabled_at = NULL "
            + "</if>"
            + "WHERE id = #{id}"
            + "</script>")
    int updateEnabled(@Param("id") String id, @Param("enabled") Boolean enabled, @Param("operatorId") String operatorId);

    /**
     * 归档文档
     *
     * @param id 文档ID
     * @param archivedReason 归档原因
     * @param archivedBy 归档人ID
     * @return 更新行数
     */
    @Update("UPDATE documents SET archived = true, archived_reason = #{archivedReason}, archived_by = #{archivedBy}, archived_at = NOW(), update_time = NOW() WHERE id = #{id}")
    int archiveDocument(@Param("id") String id, @Param("archivedReason") String archivedReason, @Param("archivedBy") String archivedBy);

    /**
     * 统计知识库文档的总字符数和token数
     *
     * @param datasetId 知识库ID
     * @return 统计结果 [wordCount, tokens]
     */
    @Select("SELECT COALESCE(SUM(word_count), 0) as word_count, COALESCE(SUM(tokens), 0) as tokens FROM documents WHERE dataset_id = #{datasetId} AND deleted = 0")
    List<Long> selectStatsByDatasetId(@Param("datasetId") String datasetId);

    @Select("select case when max(position) is null then 0 else max(position) end position from documents d where d.dataset_id = #{datasetId}")
    int selectMaxPosition(@Param("datasetId") String datasetId);

    /**
     * 根据分类ID查询文档列表
     *
     * @param categoryId 分类ID
     * @return 文档列表
     */
    @Select("SELECT * FROM documents WHERE category_id = #{categoryId} AND deleted = 0 ORDER BY create_time DESC")
    @Results({
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = JdbcType.OTHER)
    })
    List<DocumentVO> selectByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 根据分类ID统计文档数量
     *
     * @param categoryId 分类ID
     * @return 文档数量
     */
    @Select("SELECT COUNT(*) FROM documents WHERE category_id = #{categoryId} AND deleted = 0")
    Long countByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 更新文档分类
     *
     * @param id 文档ID
     * @param categoryId 分类ID
     * @return 更新行数
     */
    @Update("UPDATE documents SET category_id = #{categoryId}, update_time = NOW() WHERE id = #{id}")
    int updateCategory(@Param("id") String id, @Param("categoryId") String categoryId);

    /**
     * 批量更新文档分类
     *
     * @param documentIds 文档ID列表
     * @param categoryId 分类ID
     * @return 更新行数
     */
    @Update("<script>"
            + "UPDATE documents SET category_id = #{categoryId}, update_time = NOW() "
            + "WHERE id IN "
            + "<foreach collection='documentIds' item='documentId' open='(' separator=',' close=')'>"
            + "#{documentId}"
            + "</foreach>"
            + "</script>")
    int batchUpdateCategory(@Param("documentIds") List<String> documentIds, @Param("categoryId") String categoryId);
}
