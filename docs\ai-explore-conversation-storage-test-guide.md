# AI探索页面和会话存储功能测试指南

## 概述

本文档提供了AI探索页面智能体标签页调用逻辑和Dify智能体会话存储功能的详细测试指南。

## 功能说明

### 1. AI探索页面智能体标签页调用逻辑

- **按需加载**：只有在选择"AI探索智能体"标签页时才调用`/api/agent/ai-explore`接口
- **避免重复调用**：已加载的数据会被缓存，避免重复API调用
- **多重触发机制**：支持标签页切换、组件挂载、直接点击等多种触发方式

### 2. Dify智能体会话存储功能

- **会话记录存储**：将Dify智能体的会话记录存储到`ai_chat_record`表
- **Agent系统集成**：自动同步到`agent_conversation`和`agent_message`表
- **完整数据链路**：支持用户消息、AI回复、token统计等完整信息存储

## 测试步骤

### 前端功能测试

#### 1. 测试AI探索页面智能体标签页调用逻辑

1. **打开AI探索页面**
   - 访问AI探索页面
   - 观察初始状态，智能体列表应该为空或显示加载状态

2. **测试标签页切换触发**
   - 点击"AI探索智能体"标签页
   - 观察是否调用`/api/agent/ai-explore`接口（通过浏览器开发者工具Network面板）
   - 确认智能体列表正确显示

3. **测试缓存机制**
   - 切换到其他标签页（如"AI模型"）
   - 再次切换回"AI探索智能体"标签页
   - 确认不会重复调用API，数据从缓存加载

4. **测试直接访问**
   - 刷新页面
   - 直接点击智能体选择器
   - 确认能正确触发数据加载

#### 2. 测试Dify智能体会话功能

1. **选择智能体**
   - 在AI探索智能体列表中选择一个智能体
   - 确认智能体信息正确显示

2. **发送测试消息**
   - 在聊天输入框中输入测试消息："你好，这是一个测试消息"
   - 点击发送按钮
   - 观察流式响应是否正常

3. **检查会话记录**
   - 打开浏览器开发者工具，查看Network面板
   - 确认调用了`/api/ai/chat/test/stream`接口
   - 观察SSE流式响应数据

### 后端数据验证

#### 1. 检查AI聊天记录表

```sql
-- 查询最新的AI聊天记录
SELECT 
    id,
    session_id,
    user_id,
    app_id,
    user_message,
    ai_response,
    message_type,
    model_name,
    status,
    cost_time,
    create_time
FROM ai_chat_record 
WHERE app_id IS NOT NULL 
ORDER BY create_time DESC 
LIMIT 10;
```

#### 2. 检查Agent会话表

```sql
-- 查询对应的Agent会话记录
SELECT 
    ac.id,
    ac.agent_id,
    ac.session_id,
    ac.title,
    ac.status,
    ac.message_count,
    ac.started_at,
    ac.last_activity_at,
    a.name as agent_name,
    a.external_agent_id
FROM agent_conversation ac
LEFT JOIN agent a ON ac.agent_id = a.id
WHERE ac.session_id IN (
    SELECT DISTINCT session_id 
    FROM ai_chat_record 
    WHERE app_id IS NOT NULL
)
ORDER BY ac.create_time DESC
LIMIT 10;
```

#### 3. 检查Agent消息表

```sql
-- 查询对应的Agent消息记录
SELECT 
    am.id,
    am.conversation_id,
    am.message_type,
    am.content,
    am.sequence_number,
    am.status,
    am.sent_at,
    am.processing_time,
    am.tokens
FROM agent_message am
WHERE am.conversation_id IN (
    SELECT ac.id 
    FROM agent_conversation ac
    WHERE ac.session_id IN (
        SELECT DISTINCT session_id 
        FROM ai_chat_record 
        WHERE app_id IS NOT NULL
    )
)
ORDER BY am.create_time DESC
LIMIT 20;
```

### API接口测试

#### 1. 使用PowerShell脚本测试

```powershell
# 运行测试脚本
.\test-ai-explore-and-conversation-storage.ps1
```

#### 2. 使用curl命令测试

```bash
# 测试AI探索智能体列表接口
curl -X GET "http://localhost:8080/api/agent/ai-explore" \
     -H "Content-Type: application/json"

# 测试流式聊天接口
curl -X POST "http://localhost:8080/api/ai/chat/test/stream" \
     -H "Content-Type: application/json" \
     -d '{
       "inputs": {},
       "query": "你好，这是一个测试消息",
       "response_mode": "streaming",
       "conversation_id": "",
       "user": "test-user",
       "files": [],
       "appId": "your-app-id-here"
     }'

# 测试聊天记录查询接口
curl -X GET "http://localhost:8080/api/ai/chat/records?current=1&size=10" \
     -H "Content-Type: application/json"
```

## 预期结果

### 1. AI探索页面功能

- ✅ 只在选择智能体标签页时调用API
- ✅ 智能体列表正确显示
- ✅ 缓存机制正常工作
- ✅ 加载状态正确显示

### 2. 会话存储功能

- ✅ AI聊天记录正确保存到`ai_chat_record`表
- ✅ 自动创建对应的Agent会话记录
- ✅ 用户消息和AI回复都正确保存
- ✅ Token统计和处理时间正确记录

### 3. 数据一致性

- ✅ `ai_chat_record.app_id`与`agent.external_agent_id`正确关联
- ✅ `ai_chat_record.session_id`与`agent_conversation.session_id`一致
- ✅ 消息序号和时间戳正确设置

## 故障排除

### 常见问题

1. **智能体列表为空**
   - 检查`/api/agent/ai-explore`接口是否正常
   - 确认数据库中有type为'advanced-chat'的智能体记录

2. **流式聊天无响应**
   - 检查Dify配置是否正确
   - 确认智能体的appId是否有效

3. **会话记录未保存**
   - 检查数据库连接是否正常
   - 确认事务是否正确提交

4. **Agent系统同步失败**
   - 检查`ConversationIntegrationService`是否正确注入
   - 确认Agent表中有对应的记录

### 日志检查

查看应用日志中的关键信息：

```
# AI探索智能体加载
[INFO] 加载AI探索智能体数据

# 流式聊天处理
[INFO] 处理Dify标准格式流式聊天请求: query=xxx, appId=xxx, conversationId=xxx
[INFO] 开始调用 Dify Console API: appId=xxx

# 会话记录保存
[INFO] 聊天记录已保存: sessionId=xxx, status=1, costTime=xxxms

# Agent系统同步
[INFO] 创建新的Agent会话: conversationId=xxx, agentId=xxx, sessionId=xxx
[INFO] 保存用户消息到Agent系统: messageId=xxx, conversationId=xxx
[INFO] 同步AI聊天记录到Agent系统完成: chatRecordId=xxx, conversationId=xxx
```

## 总结

通过以上测试步骤，可以全面验证AI探索页面和会话存储功能的正确性。确保：

1. 前端智能体标签页按需加载机制正常工作
2. Dify智能体流式聊天功能正常
3. 会话记录正确存储到数据库
4. AI聊天记录与Agent系统正确集成
5. 数据一致性和完整性得到保证
