<template>
  <div class="base-config">
    <!-- 配置头部 -->
    <div class="config-header" v-if="showHeader">
      <div class="config-title">
        <i :class="nodeConfig?.icon || 'fa-solid fa-cog'" class="config-icon"></i>
        <span>{{ title || nodeConfig?.label || '节点配置' }}</span>
      </div>
      <div class="config-actions" v-if="showActions">
        <slot name="actions">
          <button @click="resetConfig" class="btn-reset" title="重置配置">
            <i class="fa-solid fa-undo"></i>
          </button>
          <button @click="validateConfig" class="btn-validate" title="验证配置">
            <i class="fa-solid fa-check"></i>
          </button>
        </slot>
      </div>
    </div>

    <!-- 配置内容 -->
    <div class="config-content">
      <slot name="content">
        <div class="default-config">
          <p class="config-description">{{ nodeConfig?.description }}</p>
          <div class="config-form">
            <!-- 默认表单字段 -->
            <div class="form-group" v-for="(value, key) in modelValue" :key="key">
              <label :for="`config-${key}`" class="form-label">
                {{ formatLabel(key) }}
              </label>
              <input
                :id="`config-${key}`"
                v-model="localConfig[key]"
                :type="getInputType(key, value)"
                :placeholder="getPlaceholder(key)"
                class="form-input"
                @input="updateConfig"
              />
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 配置底部 -->
    <div class="config-footer" v-if="showFooter">
      <slot name="footer">
        <div class="config-status">
          <span class="status-indicator" :class="validationStatus.class">
            <i :class="validationStatus.icon"></i>
            {{ validationStatus.text }}
          </span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { getNodeByType } from '../../config/nodeLibrary'

// Props
interface BaseConfigProps {
  nodeType: string
  modelValue: Record<string, any>
  title?: string
  showHeader?: boolean
  showActions?: boolean
  showFooter?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<BaseConfigProps>(), {
  showHeader: true,
  showActions: true,
  showFooter: true,
  readonly: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  'validate': [isValid: boolean, errors: string[]]
  'reset': []
  'change': [key: string, value: any]
}>()

// 响应式数据
const localConfig = ref<Record<string, any>>({ ...props.modelValue })
const validationErrors = ref<string[]>([])

// 计算属性
const nodeConfig = computed(() => {
  return getNodeByType(props.nodeType)
})

const validationStatus = computed(() => {
  if (validationErrors.value.length === 0) {
    return {
      class: 'status-valid',
      icon: 'fa-solid fa-check-circle',
      text: '配置有效'
    }
  } else {
    return {
      class: 'status-invalid',
      icon: 'fa-solid fa-exclamation-circle',
      text: `${validationErrors.value.length} 个错误`
    }
  }
})

// 方法
const updateConfig = () => {
  if (props.readonly) return
  
  emit('update:modelValue', { ...localConfig.value })
  validateConfig()
}

const resetConfig = () => {
  if (props.readonly) return
  
  const defaultConfig = nodeConfig.value?.defaultData?.config || {}
  localConfig.value = { ...defaultConfig }
  updateConfig()
  emit('reset')
}

const validateConfig = () => {
  const errors: string[] = []
  
  // 基础验证逻辑
  Object.entries(localConfig.value).forEach(([key, value]) => {
    if (isRequired(key) && (!value || value === '')) {
      errors.push(`${formatLabel(key)} 是必填项`)
    }
  })
  
  // 子组件可以重写这个方法来添加自定义验证
  const customErrors = performCustomValidation()
  errors.push(...customErrors)
  
  validationErrors.value = errors
  const isValid = errors.length === 0
  
  emit('validate', isValid, errors)
  return isValid
}

const performCustomValidation = (): string[] => {
  // 子组件可以重写这个方法
  return []
}

const formatLabel = (key: string): string => {
  // 将驼峰命名转换为可读标签
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
}

const getInputType = (key: string, value: any): string => {
  if (typeof value === 'number') return 'number'
  if (typeof value === 'boolean') return 'checkbox'
  if (key.toLowerCase().includes('password')) return 'password'
  if (key.toLowerCase().includes('email')) return 'email'
  if (key.toLowerCase().includes('url')) return 'url'
  return 'text'
}

const getPlaceholder = (key: string): string => {
  const placeholders: Record<string, string> = {
    host: '请输入主机地址',
    port: '请输入端口号',
    database: '请输入数据库名',
    username: '请输入用户名',
    password: '请输入密码',
    sql: '请输入SQL语句',
    prompt: '请输入提示词',
    model: '请选择模型',
    temperature: '请输入温度值 (0-1)',
    maxTokens: '请输入最大令牌数'
  }
  return placeholders[key] || `请输入${formatLabel(key)}`
}

const isRequired = (key: string): boolean => {
  // 定义必填字段
  const requiredFields = ['host', 'database', 'sql', 'prompt', 'model']
  return requiredFields.includes(key)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  localConfig.value = { ...newValue }
}, { deep: true })

watch(() => localConfig.value, (newValue, oldValue) => {
  // 检测具体变化的字段
  Object.keys(newValue).forEach(key => {
    if (newValue[key] !== oldValue?.[key]) {
      emit('change', key, newValue[key])
    }
  })
}, { deep: true })

// 生命周期
onMounted(() => {
  validateConfig()
})

// 暴露给父组件的方法
defineExpose({
  validate: validateConfig,
  reset: resetConfig,
  getErrors: () => validationErrors.value,
  isValid: () => validationErrors.value.length === 0
})
</script>

<style scoped>
.base-config {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.config-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #495057;
}

.config-icon {
  color: #6c757d;
}

.config-actions {
  display: flex;
  gap: 8px;
}

.btn-reset,
.btn-validate {
  padding: 6px 8px;
  border: 1px solid #dee2e6;
  background: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-reset:hover,
.btn-validate:hover {
  background: #e9ecef;
}

.config-content {
  padding: 20px;
}

.config-description {
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 16px;
  line-height: 1.5;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.config-footer {
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.config-status {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-valid {
  color: #28a745;
}

.status-invalid {
  color: #dc3545;
}
</style>
