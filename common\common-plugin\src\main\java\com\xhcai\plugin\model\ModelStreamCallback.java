package com.xhcai.plugin.model;

/**
 * 模型流式回调接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ModelStreamCallback {
    
    /**
     * 接收流式数据
     * 
     * @param chunk 数据块
     */
    void onData(String chunk);
    
    /**
     * 流式完成
     * 
     * @param response 完整响应
     */
    void onComplete(ModelResponse response);
    
    /**
     * 流式错误
     * 
     * @param error 错误信息
     */
    void onError(Throwable error);
}
