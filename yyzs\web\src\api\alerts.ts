import { AlertRule, AlertRecord } from '@/types/monitor';
import { ApiResponse } from '@/types/api';
import { request } from '@/utils/request';

class AlertsAPI {
  private baseUrl = '/api/alerts';

  /**
   * 获取告警规则列表
   */
  async getAlertRules(params?: {
    componentId?: string;
    enabled?: boolean;
    severity?: string;
  }): Promise<ApiResponse<AlertRule[]>> {
    return request.get(`${this.baseUrl}/rules`, { params });
  }

  /**
   * 获取告警规则详情
   */
  async getAlertRule(id: string): Promise<ApiResponse<AlertRule>> {
    return request.get(`${this.baseUrl}/rules/${id}`);
  }

  /**
   * 创建告警规则
   */
  async createAlertRule(data: Omit<AlertRule, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<AlertRule>> {
    return request.post(`${this.baseUrl}/rules`, data);
  }

  /**
   * 更新告警规则
   */
  async updateAlertRule(id: string, data: Partial<AlertRule>): Promise<ApiResponse<AlertRule>> {
    return request.put(`${this.baseUrl}/rules/${id}`, data);
  }

  /**
   * 删除告警规则
   */
  async deleteAlertRule(id: string): Promise<ApiResponse<void>> {
    return request.delete(`${this.baseUrl}/rules/${id}`);
  }

  /**
   * 启用/禁用告警规则
   */
  async toggleAlertRule(id: string, enabled: boolean): Promise<ApiResponse<void>> {
    return request.patch(`${this.baseUrl}/rules/${id}/toggle`, { enabled });
  }

  /**
   * 获取告警记录列表
   */
  async getAlertRecords(params?: {
    ruleId?: string;
    componentId?: string;
    status?: string;
    severity?: string;
    startTime?: string;
    endTime?: string;
    page?: number;
    size?: number;
  }): Promise<ApiResponse<{
    records: AlertRecord[];
    total: number;
    page: number;
    size: number;
  }>> {
    return request.get(`${this.baseUrl}/records`, { params });
  }

  /**
   * 获取告警记录详情
   */
  async getAlertRecord(id: string): Promise<ApiResponse<AlertRecord>> {
    return request.get(`${this.baseUrl}/records/${id}`);
  }

  /**
   * 确认告警
   */
  async acknowledgeAlert(id: string, message?: string): Promise<ApiResponse<void>> {
    return request.post(`${this.baseUrl}/records/${id}/acknowledge`, { message });
  }

  /**
   * 批量确认告警
   */
  async batchAcknowledgeAlerts(ids: string[], message?: string): Promise<ApiResponse<{
    successCount: number;
    totalCount: number;
  }>> {
    return request.post(`${this.baseUrl}/records/batch/acknowledge`, { ids, message });
  }

  /**
   * 解决告警
   */
  async resolveAlert(id: string, message?: string): Promise<ApiResponse<void>> {
    return request.post(`${this.baseUrl}/records/${id}/resolve`, { message });
  }

  /**
   * 获取活跃告警统计
   */
  async getActiveAlertsStats(): Promise<ApiResponse<{
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
  }>> {
    return request.get(`${this.baseUrl}/stats/active`);
  }

  /**
   * 获取告警趋势数据
   */
  async getAlertTrends(params?: {
    startTime?: string;
    endTime?: string;
    interval?: 'hour' | 'day' | 'week';
  }): Promise<ApiResponse<Array<{
    timestamp: string;
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
  }>>> {
    return request.get(`${this.baseUrl}/trends`, { params });
  }

  /**
   * 测试告警规则
   */
  async testAlertRule(data: Omit<AlertRule, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<{
    triggered: boolean;
    currentValue?: number;
    message?: string;
  }>> {
    return request.post(`${this.baseUrl}/rules/test`, data);
  }

  /**
   * 获取告警通知配置
   */
  async getNotificationConfig(): Promise<ApiResponse<{
    email: {
      enabled: boolean;
      smtp: {
        host: string;
        port: number;
        username: string;
        ssl: boolean;
      };
      recipients: string[];
    };
    webhook: {
      enabled: boolean;
      url: string;
      headers?: Record<string, string>;
    };
    dingtalk: {
      enabled: boolean;
      webhook: string;
      secret?: string;
    };
  }>> {
    return request.get(`${this.baseUrl}/notification/config`);
  }

  /**
   * 更新告警通知配置
   */
  async updateNotificationConfig(config: any): Promise<ApiResponse<void>> {
    return request.put(`${this.baseUrl}/notification/config`, config);
  }

  /**
   * 测试告警通知
   */
  async testNotification(type: 'email' | 'webhook' | 'dingtalk', config?: any): Promise<ApiResponse<{
    success: boolean;
    message?: string;
  }>> {
    return request.post(`${this.baseUrl}/notification/test`, { type, config });
  }

  /**
   * 获取告警模板
   */
  async getAlertTemplates(): Promise<ApiResponse<Array<{
    id: string;
    name: string;
    description: string;
    componentType?: string;
    rules: Omit<AlertRule, 'id' | 'createTime' | 'updateTime'>[];
  }>>> {
    return request.get(`${this.baseUrl}/templates`);
  }

  /**
   * 应用告警模板
   */
  async applyAlertTemplate(templateId: string, componentIds: string[]): Promise<ApiResponse<{
    successCount: number;
    totalCount: number;
    createdRules: string[];
  }>> {
    return request.post(`${this.baseUrl}/templates/${templateId}/apply`, { componentIds });
  }

  /**
   * 导出告警规则
   */
  async exportAlertRules(ruleIds?: string[]): Promise<ApiResponse<Blob>> {
    return request.post(`${this.baseUrl}/rules/export`, { ruleIds }, {
      responseType: 'blob'
    });
  }

  /**
   * 导入告警规则
   */
  async importAlertRules(file: File): Promise<ApiResponse<{
    successCount: number;
    totalCount: number;
    errors?: string[];
  }>> {
    const formData = new FormData();
    formData.append('file', file);
    
    return request.post(`${this.baseUrl}/rules/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    });
  }

  /**
   * 获取告警规则执行历史
   */
  async getAlertRuleHistory(ruleId: string, params?: {
    startTime?: string;
    endTime?: string;
    page?: number;
    size?: number;
  }): Promise<ApiResponse<{
    history: Array<{
      timestamp: string;
      triggered: boolean;
      currentValue: number;
      threshold: number;
      message?: string;
    }>;
    total: number;
  }>> {
    return request.get(`${this.baseUrl}/rules/${ruleId}/history`, { params });
  }
}

export default new AlertsAPI();
