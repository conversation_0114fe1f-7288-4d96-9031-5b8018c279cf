package com.xhcai.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 智能体消息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent_message")
@Schema(description = "智能体消息")
@TableName("agent_message")
public class AgentMessage extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 对话ID
     */
    @Column(name = "conversation_id", length = 36)
    @Schema(description = "对话ID", example = "conv_001")
    @NotBlank(message = "对话ID不能为空")
    @Size(max = 36, message = "对话ID长度不能超过36个字符")
    @TableField("conversation_id")
    private String conversationId;

    /**
     * 消息类型
     */
    @Column(name = "message_type", length = 20)
    @Schema(description = "消息类型", example = "user", allowableValues = {"user", "assistant", "system", "tool"})
    @NotBlank(message = "消息类型不能为空")
    @Pattern(regexp = "^(user|assistant|system|tool)$", message = "消息类型必须为user、assistant、system或tool")
    @TableField("message_type")
    private String messageType;

    /**
     * 消息内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    @Schema(description = "消息内容", example = "你好，我想了解一下产品信息")
    @NotBlank(message = "消息内容不能为空")
    @TableField("content")
    private String content;

    /**
     * 消息序号
     */
    @Column(name = "sequence_number")
    @Schema(description = "消息序号", example = "1")
    @TableField("sequence_number")
    private Integer sequenceNumber;

    /**
     * 父消息ID
     */
    @Column(name = "parent_message_id", length = 36)
    @Schema(description = "父消息ID", example = "msg_001")
    @Size(max = 36, message = "父消息ID长度不能超过36个字符")
    @TableField("parent_message_id")
    private String parentMessageId;

    /**
     * 消息状态
     */
    @Column(name = "status", length = 20)
    @Schema(description = "消息状态", example = "sent", allowableValues = {"sending", "sent", "delivered", "failed"})
    @Pattern(regexp = "^(sending|sent|delivered|failed)$", message = "消息状态必须为sending、sent、delivered或failed")
    @TableField("status")
    private String status;

    /**
     * 发送时间
     */
    @Column(name = "sent_at")
    @Schema(description = "发送时间")
    @TableField("sent_at")
    private LocalDateTime sentAt;

    /**
     * 接收时间
     */
    @Column(name = "received_at")
    @Schema(description = "接收时间")
    @TableField("received_at")
    private LocalDateTime receivedAt;

    /**
     * 处理时间（毫秒）
     */
    @Column(name = "processing_time")
    @Schema(description = "处理时间（毫秒）", example = "1500")
    @TableField("processing_time")
    private Long processingTime;

    /**
     * token消耗
     */
    @Column(name = "tokens")
    @Schema(description = "token消耗", example = "150")
    @TableField("tokens")
    private Integer tokens;

    /**
     * 输入token消耗
     */
    @Column(name = "input_tokens")
    @Schema(description = "输入token消耗", example = "80")
    @TableField("input_tokens")
    private Integer inputTokens;

    /**
     * 输出token消耗
     */
    @Column(name = "output_tokens")
    @Schema(description = "输出token消耗", example = "70")
    @TableField("output_tokens")
    private Integer outputTokens;

    /**
     * 费用（分）
     */
    @Column(name = "cost")
    @Schema(description = "费用（分）", example = "5")
    @TableField("cost")
    private Long cost;

    /**
     * 工具调用信息（JSON格式）
     */
    @Column(name = "tool_calls", columnDefinition = "TEXT")
    @Schema(description = "工具调用信息", example = "[{\"name\":\"search\",\"args\":{\"query\":\"产品信息\"}}]")
    @TableField("tool_calls")
    private String toolCalls;

    /**
     * 工具调用结果（JSON格式）
     */
    @Column(name = "tool_results", columnDefinition = "TEXT")
    @Schema(description = "工具调用结果", example = "[{\"name\":\"search\",\"result\":\"找到相关产品信息...\"}]")
    @TableField("tool_results")
    private String toolResults;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    @Schema(description = "错误信息", example = "网络连接超时")
    @Size(max = 1000, message = "错误信息长度不能超过1000个字符")
    @TableField("error_message")
    private String errorMessage;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "metadata", columnDefinition = "TEXT")
    @Schema(description = "扩展信息", example = "{\"model\":\"gpt-3.5-turbo\",\"temperature\":0.7}")
    @TableField("metadata")
    private String metadata;

    /**
     * 输入参数（JSON格式）
     */
    @Column(name = "inputs", columnDefinition = "TEXT")
    @Schema(description = "输入参数", example = "{\"temperature\":0.7,\"max_tokens\":1000}")
    @TableField("inputs")
    private String inputs;

    /**
     * 查询内容
     */
    @Column(name = "query", columnDefinition = "TEXT")
    @Schema(description = "查询内容", example = "用户的具体问题或查询内容")
    @TableField("query")
    private String query;

    /**
     * 外部信息ID
     */
    @Column(name = "external_info_id", length = 36)
    @Schema(description = "外部信息ID", example = "ext_info_001")
    @Size(max = 36, message = "外部信息ID长度不能超过36个字符")
    @TableField("external_info_id")
    private String externalInfoId;

    // Getters and Setters
    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(Integer sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public String getParentMessageId() {
        return parentMessageId;
    }

    public void setParentMessageId(String parentMessageId) {
        this.parentMessageId = parentMessageId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getSentAt() {
        return sentAt;
    }

    public void setSentAt(LocalDateTime sentAt) {
        this.sentAt = sentAt;
    }

    public LocalDateTime getReceivedAt() {
        return receivedAt;
    }

    public void setReceivedAt(LocalDateTime receivedAt) {
        this.receivedAt = receivedAt;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public Integer getTokens() {
        return tokens;
    }

    public void setTokens(Integer tokens) {
        this.tokens = tokens;
    }

    public Integer getInputTokens() {
        return inputTokens;
    }

    public void setInputTokens(Integer inputTokens) {
        this.inputTokens = inputTokens;
    }

    public Integer getOutputTokens() {
        return outputTokens;
    }

    public void setOutputTokens(Integer outputTokens) {
        this.outputTokens = outputTokens;
    }

    public Long getCost() {
        return cost;
    }

    public void setCost(Long cost) {
        this.cost = cost;
    }

    public String getToolCalls() {
        return toolCalls;
    }

    public void setToolCalls(String toolCalls) {
        this.toolCalls = toolCalls;
    }

    public String getToolResults() {
        return toolResults;
    }

    public void setToolResults(String toolResults) {
        this.toolResults = toolResults;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getInputs() {
        return inputs;
    }

    public void setInputs(String inputs) {
        this.inputs = inputs;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getExternalInfoId() {
        return externalInfoId;
    }

    public void setExternalInfoId(String externalInfoId) {
        this.externalInfoId = externalInfoId;
    }
}
