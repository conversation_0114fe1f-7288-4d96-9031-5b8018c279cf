package com.xhcai.modules.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务项目VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "业务项目VO")
public class BusinessProjectVO {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String id;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String name;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述")
    private String description;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID")
    private String ownerId;

    /**
     * 应用环境
     */
    @Schema(description = "应用环境：production-生产环境，test-测试环境，development-开发环境")
    private String environment;

    /**
     * 项目状态
     */
    @Schema(description = "项目状态：active-运行中，inactive-已停止，maintenance-维护中")
    private String status;

    /**
     * 项目图标
     */
    @Schema(description = "项目图标")
    private String icon;

    /**
     * 图标颜色
     */
    @Schema(description = "图标颜色")
    private String iconColor;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 项目负责人信息
     */
    @Schema(description = "项目负责人信息")
    private Object owner;

    /**
     * 团队成员列表
     */
    @Schema(description = "团队成员列表")
    private List<ProjectTeamMemberVO> teamMembers;

    /**
     * 智能体数量
     */
    @Schema(description = "智能体数量")
    private Integer agentCount;

    /**
     * 知识库数量
     */
    @Schema(description = "知识库数量")
    private Integer knowledgeCount;

    /**
     * 知识图谱数量
     */
    @Schema(description = "知识图谱数量")
    private Integer graphCount;

    /**
     * 运行中的智能体数量
     */
    @Schema(description = "运行中的智能体数量")
    private Integer agentRunningCount;

    /**
     * 已构建的知识库数量
     */
    @Schema(description = "已构建的知识库数量")
    private Integer knowledgeBuiltCount;

    /**
     * 知识图谱关系数量
     */
    @Schema(description = "知识图谱关系数量")
    private Integer graphRelationCount;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconColor() {
        return iconColor;
    }

    public void setIconColor(String iconColor) {
        this.iconColor = iconColor;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Object getOwner() {
        return owner;
    }

    public void setOwner(Object owner) {
        this.owner = owner;
    }

    public List<ProjectTeamMemberVO> getTeamMembers() {
        return teamMembers;
    }

    public void setTeamMembers(List<ProjectTeamMemberVO> teamMembers) {
        this.teamMembers = teamMembers;
    }

    public Integer getAgentCount() {
        return agentCount;
    }

    public void setAgentCount(Integer agentCount) {
        this.agentCount = agentCount;
    }

    public Integer getKnowledgeCount() {
        return knowledgeCount;
    }

    public void setKnowledgeCount(Integer knowledgeCount) {
        this.knowledgeCount = knowledgeCount;
    }

    public Integer getGraphCount() {
        return graphCount;
    }

    public void setGraphCount(Integer graphCount) {
        this.graphCount = graphCount;
    }

    public Integer getAgentRunningCount() {
        return agentRunningCount;
    }

    public void setAgentRunningCount(Integer agentRunningCount) {
        this.agentRunningCount = agentRunningCount;
    }

    public Integer getKnowledgeBuiltCount() {
        return knowledgeBuiltCount;
    }

    public void setKnowledgeBuiltCount(Integer knowledgeBuiltCount) {
        this.knowledgeBuiltCount = knowledgeBuiltCount;
    }

    public Integer getGraphRelationCount() {
        return graphRelationCount;
    }

    public void setGraphRelationCount(Integer graphRelationCount) {
        this.graphRelationCount = graphRelationCount;
    }
}
