package com.xhcai.modules.dify.dto.file;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify 文件上传响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 文件上传响应")
public class DifyFileUploadResponseDTO {

    /**
     * 文件ID
     */
    @Schema(description = "文件ID")
    private String id;

    /**
     * 文件名
     */
    @Schema(description = "文件名")
    private String name;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）")
    private Long size;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名")
    private String extension;

    /**
     * MIME 类型
     */
    @JsonProperty("mime_type")
    @Schema(description = "MIME 类型")
    private String mimeType;

    /**
     * 创建者ID
     */
    @JsonProperty("created_by")
    @Schema(description = "创建者ID")
    private String createdBy;

    /**
     * 创建时间（时间戳）
     */
    @JsonProperty("created_at")
    @Schema(description = "创建时间（时间戳）")
    private Long createdAt;

    /**
     * 预览URL
     */
    @JsonProperty("preview_url")
    @Schema(description = "预览URL")
    private String previewUrl;
}
