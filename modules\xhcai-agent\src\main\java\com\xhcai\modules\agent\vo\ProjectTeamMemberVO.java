package com.xhcai.modules.agent.vo;

import java.time.LocalDateTime;

import com.xhcai.modules.system.vo.SysUserVO;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 项目团队成员VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "项目团队成员VO")
public class ProjectTeamMemberVO {

    /**
     * 成员ID
     */
    @Schema(description = "成员ID")
    private String id;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 成员角色
     */
    @Schema(description = "成员角色")
    private String role;

    /**
     * 加入时间
     */
    @Schema(description = "加入时间")
    private LocalDateTime joinTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 关联的用户信息
     */
    @Schema(description = "关联的用户信息")
    private SysUserVO user;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public LocalDateTime getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(LocalDateTime joinTime) {
        this.joinTime = joinTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public SysUserVO getUser() {
        return user;
    }

    public void setUser(SysUserVO user) {
        this.user = user;
    }
}
