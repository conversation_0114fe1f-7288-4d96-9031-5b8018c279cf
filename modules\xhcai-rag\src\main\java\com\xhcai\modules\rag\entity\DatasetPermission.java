package com.xhcai.modules.rag.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseEntity;
import com.xhcai.modules.rag.enums.PermissionType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.LocalDateTime;

/**
 * 知识库权限实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "dataset_permission")
@TableName("dataset_permission")
public class DatasetPermission extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @Column(name = "dataset_id", nullable = false, length = 255)
    @TableField("dataset_id")
    private String datasetId;

    /**
     * 类型 rule:角色 dept:部门 account:用户
     */
    @Column(name = "object_type", nullable = false, length = 255)
    @TableField("object_type")
    private String objectType;

    /**
     * 角色ID,部门ID,用户ID
     */
    @Column(name = "object_id", nullable = false, length = 255)
    @TableField("object_id")
    private String objectId;

    /**
     * 权限类型(1-只读,2-读写,3-管理)
     */
    @Column(name = "permission_type", nullable = false, length = 255)
    @TableField("permission_type")
    private String permissionType;

    // ==================== Getters and Setters ====================

    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }

    public PermissionType getPermissionTypeEnum() {
        return permissionType != null ? PermissionType.fromCode(permissionType) : null;
    }

    public void setPermissionTypeEnum(PermissionType permissionType) {
        this.permissionType = permissionType != null ? permissionType.getCode() : null;
    }

    @Override
    public String toString() {
        return "DatasetPermission{" +
                "id='" + getId() + '\'' +
                ", datasetId='" + datasetId + '\'' +
                ", objectType='" + objectType + '\'' +
                ", objectId='" + objectId + '\'' +
                ", permissionType='" + permissionType + '\'' +
                ", createBy='" + getCreateBy() + '\'' +
                ", createTime=" + getCreateTime() +
                ", updatedTime=" + getUpdateTime() +
                '}';
    }
}
