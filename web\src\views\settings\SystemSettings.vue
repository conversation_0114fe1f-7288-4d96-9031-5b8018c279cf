<template>
  <div class="system-settings bg-white rounded-lg shadow-sm overflow-hidden">
    <!-- 页面标题 -->
    <div class="settings-header flex items-center justify-between mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-2xl font-bold text-gray-900 flex items-center">
            <span class="mr-3 text-3xl">⚙️</span>
            系统设置
          </h3>
          <p class="text-sm text-gray-600 mt-2">配置系统基本信息、外观和显示设置</p>
        </div>
      </div>
    </div>

    <!-- 统一设置表单 -->
    <div class="settings-content p-8">
      <div class="unified-settings-form bg-gradient-to-br from-white to-gray-50 p-8 rounded-2xl border border-gray-200 shadow-sm">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Logo设置区域 -->
          <div class="logo-section">
            <h5 class="font-semibold text-gray-800 mb-4 flex items-center">
              <span class="mr-2 text-lg">🖼️</span>
              系统Logo
            </h5>
            <div class="logo-upload-area text-center">
              <div
                @click="openLogoUpload"
                class="w-32 h-32 mx-auto bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-dashed border-blue-300 rounded-2xl flex items-center justify-center cursor-pointer hover:border-blue-500 hover:bg-gradient-to-br hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 group relative overflow-hidden"
                title="点击上传Logo"
              >
                <img v-if="systemSettings.logo" :src="systemSettings.logo" alt="系统Logo" class="w-28 h-28 object-contain rounded-xl" />
                <div v-else class="text-center">
                  <div class="text-blue-400 text-4xl mb-2">🏢</div>
                  <p class="text-xs text-gray-500">点击上传</p>
                </div>

                <!-- 悬停遮罩 -->
                <div class="absolute inset-0 bg-black bg-opacity-60 rounded-2xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <div class="text-white text-center">
                    <div class="text-2xl mb-1">📷</div>
                    <div class="text-xs font-medium">更换Logo</div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <p class="text-xs text-gray-500">建议尺寸: 200×200px</p>
                <p class="text-xs text-gray-500">支持: PNG, JPG</p>
              </div>

              <input
                type="file"
                ref="logoInput"
                @change="handleLogoUpload"
                accept="image/*"
                class="hidden"
              />
            </div>
          </div>

          <!-- 系统信息设置区域 -->
          <div class="system-info-section lg:col-span-2">
            <h5 class="font-semibold text-gray-800 mb-6 flex items-center">
              <span class="mr-2 text-lg">📝</span>
              系统信息
            </h5>
            <div class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                  <label class="form-label text-sm font-medium text-gray-700 mb-2 block">系统名称</label>
                  <input
                    v-model="systemSettings.title"
                    type="text"
                    class="form-input w-full"
                    placeholder="请输入系统名称"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label text-sm font-medium text-gray-700 mb-2 block">系统副标题</label>
                  <input
                    v-model="systemSettings.subtitle"
                    type="text"
                    class="form-input w-full"
                    placeholder="请输入系统副标题（可选）"
                  />
                </div>
              </div>
              <div class="form-group">
                <label class="form-label text-sm font-medium text-gray-700 mb-2 block">系统描述</label>
                <textarea
                  v-model="systemSettings.description"
                  class="form-input w-full resize-none"
                  rows="4"
                  placeholder="请输入系统描述信息，向用户介绍系统的主要功能和特色"
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- 显示设置区域 -->
        <div class="display-settings-section mt-8 pt-8 border-t border-gray-200">
          <h5 class="font-semibold text-gray-800 mb-6 flex items-center">
            <span class="mr-2 text-lg">🌐</span>
            显示设置
          </h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 语言设置 -->
            <div class="form-group">
              <label class="form-label text-sm font-medium text-gray-700 mb-2 block flex items-center">
                <span class="mr-2">🗣️</span>
                系统语言
              </label>
              <select v-model="systemSettings.language" class="form-input w-full">
                <option value="zh-CN">🇨🇳 简体中文</option>
                <option value="zh-TW">🇹🇼 繁体中文</option>
                <option value="en-US">🇺🇸 English</option>
              </select>
              <p class="text-xs text-gray-500 mt-1">选择系统界面显示语言</p>
            </div>

            <!-- 时区设置 -->
            <div class="form-group">
              <label class="form-label text-sm font-medium text-gray-700 mb-2 block flex items-center">
                <span class="mr-2">🕐</span>
                时区设置
              </label>
              <select v-model="systemSettings.timezone" class="form-input w-full">
                <option value="Asia/Shanghai">🇨🇳 北京时间 (UTC+8)</option>
                <option value="Asia/Tokyo">🇯🇵 东京时间 (UTC+9)</option>
                <option value="America/New_York">🇺🇸 纽约时间 (UTC-5)</option>
                <option value="Europe/London">🇬🇧 伦敦时间 (UTC+0)</option>
              </select>
              <p class="text-xs text-gray-500 mt-1">选择系统默认时区</p>
            </div>
          </div>
        </div>

        <!-- 统一操作按钮区域 -->
        <div class="form-actions mt-10 pt-6 border-t border-gray-200 flex flex-col sm:flex-row justify-center gap-4">
          <button
            @click="saveAllSettings"
            :disabled="saving || loading"
            class="btn-primary px-10 py-4 text-base font-semibold"
            :class="{ 'opacity-50 cursor-not-allowed': saving || loading }"
          >
            <span class="mr-2">{{ saving ? '⏳' : '💾' }}</span>
            {{ saving ? '保存中...' : '保存所有设置' }}
          </button>
          <button
            @click="resetToDefaults"
            :disabled="saving || loading"
            class="btn-secondary px-8 py-4 text-base font-semibold"
            :class="{ 'opacity-50 cursor-not-allowed': saving || loading }"
          >
            <span class="mr-2">🔄</span>
            恢复默认设置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ConfigAPI } from '@/api/system'
import type { SystemSettingsDTO } from '@/types/system'
import { ElNotification } from 'element-plus'

// 系统设置数据
const systemSettings = ref<SystemSettingsDTO>({
  logo: '',
  title: 'AI智能体平台',
  subtitle: '智能对话，高效协作',
  description: '基于人工智能技术的智能体管理平台，提供智能对话、知识管理、协作办公等功能。',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai'
})

// 加载状态
const loading = ref(false)
const saving = ref(false)

// 加载系统设置
const loadSystemSettings = async () => {
  try {
    loading.value = true
    const response = await ConfigAPI.getSystemSettings()

    if (response.success && response.message) {
      // 合并加载的设置到当前设置中，保留默认值
      Object.assign(systemSettings.value, response.message)
    }
  } catch (error) {
    console.error('加载系统设置失败:', error)
    // 加载失败时使用默认设置，不显示错误提示
  } finally {
    loading.value = false
  }
}

// 系统设置方法
const openLogoUpload = () => {
  const logoInput = document.querySelector('input[type="file"]') as HTMLInputElement
  if (logoInput) {
    logoInput.click()
  }
}

const handleLogoUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      ElNotification({
        title: '请选择图片文件',
        message: "请选择图片文件",
        type: 'info'
      })
      return
    }

    // 验证文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      ElNotification({
        title: '图片文件大小不能超过2MB',
        message: "图片文件大小不能超过2MB",
        type: 'info'
      })
      return
    }

    const reader = new FileReader()
    reader.onload = async (e) => {
      const newLogo = e.target?.result as string
      systemSettings.value.logo = newLogo

      // 自动保存Logo设置
      try {
        const logoSettings: SystemSettingsDTO = { logo: newLogo }
        const response = await ConfigAPI.saveSystemSettings(logoSettings)

        if (response.success) {
          ElNotification({
            title: 'Logo保存成功',
            message: "Logo保存成功",
            type: 'info'
          })
        } else {
          ElNotification({
            title: '保存Logo失败',
            message: "保存Logo失败",
            type: 'error'
          })
          // 恢复原来的Logo
          systemSettings.value.logo = ''
        }
      } catch (error) {
        ElNotification({
          title: '保存Logo失败',
          message: "保存Logo失败",
          type: 'error'
        })
        // 恢复原来的Logo
        systemSettings.value.logo = ''
      }
    }
    reader.readAsDataURL(file)
  }

  // 清空input值，允许重复选择同一文件
  ;(event.target as HTMLInputElement).value = ''
}

const saveAllSettings = async () => {
  if (saving.value) return

  try {
    saving.value = true
    const response = await ConfigAPI.saveSystemSettings(systemSettings.value)

    if (response.success) {
      ElNotification({
        title: '系统设置保存成功',
        message: "系统设置保存成功",
        type: 'info'
      })
    } else {
      ElNotification({
        title: '保存失败',
        message: "保存失败",
        type: 'error'
      })
    }
  } catch (error) {
    ElNotification({
      title: '保存系统设置失败',
      message: "保存失败，请检查网络连接后重试",
      type: 'error'
    })
  } finally {
    saving.value = false
  }
}

const resetToDefaults = () => {
  if (confirm('确定要恢复默认设置吗？此操作不可撤销。')) {
    systemSettings.value = {
      logo: '',
      title: 'AI智能体平台',
      subtitle: '智能对话，高效协作',
      description: '基于人工智能技术的智能体管理平台，提供智能对话、知识管理、协作办公等功能。',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai'
    }
    ElNotification({
      title: '已恢复默认设置',
      message: "已恢复默认设置",
      type: 'error'
    })
  }
}

// 生命周期
onMounted(() => {
  loadSystemSettings()
})
</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');

.settings-grid {
  gap: 2rem;
}

.setting-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.setting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.setting-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.setting-card:hover::before {
  opacity: 1;
}

.current-logo {
  display: inline-block;
}

.current-logo img {
  border-radius: 8px;
}

.current-logo .w-24 {
  position: relative;
  overflow: hidden;
}

.current-logo .w-24:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  color: white;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: white;
  border: 2px solid #e5e7eb;
  color: #374151;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .flex.items-center.gap-6 {
    flex-direction: column;
    align-items: stretch;
  }

  .settings-cards {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
