<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-project-diagram"></i>
          {{ mode === 'create' ? '创建AI场景' : '编辑AI场景' }}
        </h2>
        <button class="modal-close" @click="handleClose">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">AI场景名称 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="formData.name"
                  class="form-input"
                  placeholder="请输入AI场景名称"
                  required
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">AI场景描述</label>
                <textarea
                  v-model="formData.description"
                  class="form-textarea"
                  placeholder="请输入AI场景描述"
                  rows="3"
                ></textarea>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group half">
                <label class="form-label">图标</label>
                <div class="icon-selector">
                  <div 
                    class="icon-preview" 
                    :style="{ background: formData.iconColor }"
                    @click="showIconSelector = true"
                  >
                    <i :class="formData.icon || 'fas fa-project-diagram'"></i>
                  </div>
                  <button 
                    type="button" 
                    class="btn-select-icon"
                    @click="showIconSelector = true"
                  >
                    选择图标
                  </button>
                </div>
              </div>
              <div class="form-group half">
                <label class="form-label">应用环境 <span class="required">*</span></label>
                <select v-model="formData.environment" class="form-select" required>
                  <option value="">请选择环境</option>
                  <option value="production">生产环境</option>
                  <option value="test">测试环境</option>
                  <option value="development">开发环境</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 负责人信息 -->
          <div class="form-section">
            <h3 class="section-title">负责人信息</h3>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">负责人 <span class="required">*</span></label>
                <ElUserByDeptSelector
                  key="ower"
                  v-model="formData.ownerId"
                  :config="ownerSelectorConfig"
                  @change="handleOwnerChange"
                />
              </div>
            </div>
          </div>

          <!-- 团队人员 -->
          <div class="form-section">
            <h3 class="section-title">团队人员</h3>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">团队成员</label>
                <ElUserByDeptSelector
                  key="team"
                  v-model="selectedTeamMemberIds"
                  :config="teamMemberSelectorConfig"
                  @change="handleTeamMemberChange"
                />
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" @click="handleClose">
          取消
        </button>
        <button type="button" class="btn btn-primary" @click="handleSubmit" :disabled="!isFormValid">
          {{ mode === 'create' ? '创建' : '保存' }}
        </button>
      </div>
    </div>

    <!-- 图标选择器 -->
    <IconSelector
      :visible="showIconSelector"
      :selected-icon="formData.icon"
      :selected-background="formData.iconColor"
      @update:visible="showIconSelector = $event"
      @select="handleIconSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import IconSelector from '@/components/common/IconSelector.vue'
import { ElUserByDeptSelector } from '@/components/common/el-selectors'
import { businessProjectApi } from '@/api/business'
import type { BusinessProject, SysUser, CreateBusinessProjectRequest } from '@/api/business'
import type { UserSelectorOption, SelectorConfig } from '@/types/system'

// 表单数据类型
interface FormData {
  name: string
  description: string
  ownerId: string
  environment: 'production' | 'test' | 'development' | ''
  icon?: string
  iconColor?: string
  remark?: string
  teamMembers: Array<{
    userId: string
    role: string
    user?: SysUser
  }>
}

// Props
const props = defineProps<{
  visible: boolean
  project?: BusinessProject | null
  mode: 'create' | 'edit'
}>()

// Emits
const emit = defineEmits<{
  close: []
  save: [data: CreateBusinessProjectRequest]
}>()

// 响应式数据
const formData = ref<FormData>({
  name: '',
  description: '',
  ownerId: '',
  environment: '',
  icon: 'fas fa-project-diagram',
  iconColor: 'linear-gradient(135deg, #667eea, #764ba2)',
  remark: '',
  teamMembers: []
})

const selectedOwner = ref<SysUser | null>(null)
const selectedTeamMemberIds = ref<string[]>([])
const showIconSelector = ref(false)

// 选择器配置
const ownerSelectorConfig = ref<SelectorConfig>({
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择项目负责人',
  size: 'default'
})

const teamMemberSelectorConfig = ref<SelectorConfig>({
  multiple: true,
  clearable: true,
  filterable: true,
  placeholder: '请选择团队成员',
  size: 'default'
})

// 计算属性
const isFormValid = computed(() => {
  return formData.value.name.trim() !== '' &&
         formData.value.environment !== '' &&
         selectedOwner.value !== null
})

// 方法
const handleOwnerChange = (value: string | string[], option: any) => {
  if (value && !Array.isArray(value) && option) {
    selectedOwner.value = option as unknown as SysUser
    formData.value.ownerId = value
  } else {
    selectedOwner.value = null
    formData.value.ownerId = ''
  }
}

const handleTeamMemberChange = (value: string | string[], options: any) => {
  if (Array.isArray(value) && Array.isArray(options)) {
    // 更新选中的用户ID列表
    selectedTeamMemberIds.value = value

    // 更新团队成员列表，保留已有的角色信息
    const newTeamMembers = options.map((user: any) => {
      const existingMember = formData.value.teamMembers.find(member => member.userId === user.value)
      return {
        userId: user.value,
        role: existingMember?.role || '',
        user: user as unknown as SysUser
      }
    })

    formData.value.teamMembers = newTeamMembers
  } else {
    selectedTeamMemberIds.value = []
    formData.value.teamMembers = []
  }
}

const removeTeamMember = (userId: string) => {
  // 从团队成员列表中移除
  formData.value.teamMembers = formData.value.teamMembers.filter(member => member.userId !== userId)
  // 从选中的ID列表中移除
  selectedTeamMemberIds.value = selectedTeamMemberIds.value.filter(id => id !== userId)
}

const handleIconSelect = (data: { icon: string; background: string }) => {
  formData.value.icon = data.icon
  formData.value.iconColor = data.background
  showIconSelector.value = false
}

const handleSubmit = () => {
  if (!isFormValid.value) return

  const submitData: CreateBusinessProjectRequest = {
    name: formData.value.name,
    description: formData.value.description,
    ownerId: formData.value.ownerId,
    environment: formData.value.environment as 'production' | 'test' | 'development',
    icon: formData.value.icon,
    iconColor: formData.value.iconColor,
    remark: formData.value.remark,
    teamMembers: formData.value.teamMembers.map(member => ({
      userId: member.userId,
      role: member.role
    }))
  }

  emit('save', submitData)
}

const handleClose = () => {
  emit('close')
}

const handleOverlayClick = () => {
  handleClose()
}

// 移除不需要的方法

// 监听器
watch(() => props.project, (newProject) => {
  if (newProject && props.mode === 'edit') {
    formData.value = {
      name: newProject.name,
      description: newProject.description || '',
      ownerId: newProject.ownerId,
      environment: newProject.environment,
      icon: newProject.icon,
      iconColor: newProject.iconColor,
      remark: newProject.remark || '',
      teamMembers: (newProject.teamMembers || []).map(member => ({
        userId: member.userId,
        role: member.role,
        user: member.user
      }))
    }
    selectedOwner.value = newProject.owner as SysUser
    selectedTeamMemberIds.value = (newProject.teamMembers || []).map(member => member.userId)
  } else if (props.mode === 'create') {
    // 重置表单
    formData.value = {
      name: '',
      description: '',
      ownerId: '',
      environment: '',
      icon: 'fas fa-project-diagram',
      iconColor: 'linear-gradient(135deg, #667eea, #764ba2)',
      remark: '',
      teamMembers: []
    }
    selectedOwner.value = null
    selectedTeamMemberIds.value = []
  }
}, { immediate: true })
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  width: 80vw;
  height: 80vh;
  min-width: 600px;
  min-height: 500px;
  max-width: 1200px;
  max-height: 900px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-container {
    width: 95vw;
    height: 90vh;
    min-width: 320px;
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .modal-container {
    width: 100vw;
    height: 100vh;
    min-width: 100vw;
    min-height: 100vh;
    border-radius: 0;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-light);
  background: var(--background-white);
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title i {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px 32px;
  border-top: 1px solid var(--border-light);
  background: var(--background-white);
}

/* 表单样式 */
.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--border-light);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  flex: 1;
}

.form-group.half {
  flex: 0 0 calc(50% - 10px);
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.required {
  color: #e74c3c;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  font-size: 14px;
  background: white;
  color: var(--text-primary);
  transition: var(--transition);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 图标选择器 */
.icon-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-preview {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.icon-preview:hover {
  transform: scale(1.05);
}

.icon-preview i {
  font-size: 24px;
  color: white;
}

.btn-select-icon {
  padding: 8px 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--border-radius);
  background: white;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
}

.btn-select-icon:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 用户选择器 */
.user-selector {
  position: relative;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: var(--transition);
}

.user-option:hover {
  background: rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
}

.user-email {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.selected-user {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: var(--border-radius);
  margin-top: 8px;
}

.selected-members {
  margin-top: 12px;
}

.selected-members .selected-user {
  margin-top: 8px;
}

.selected-members .selected-user:first-child {
  margin-top: 0;
}

.role-input {
  width: 100px;
  padding: 6px 8px;
  border: 1px solid var(--border-light);
  border-radius: 4px;
  font-size: 12px;
}

.btn-remove {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
}

.btn-remove:hover {
  background: rgba(231, 76, 60, 0.2);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-light);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: white;
  color: var(--text-primary);
  border: 2px solid var(--border-light);
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}
</style>
