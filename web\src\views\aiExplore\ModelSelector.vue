<template>
  <div class="model-selector">
    <!-- 触发按钮 -->
    <div
      @click="toggleDropdown"
      class="selector-trigger flex items-center gap-1.5 px-2 py-1.5 bg-transparent rounded-md cursor-pointer hover:bg-gray-50 transition-colors text-sm whitespace-nowrap"
      :class="{ 'bg-gray-50': isOpen }"
    >
      <div class="w-1.5 h-1.5 rounded-full flex-shrink-0" :class="selectedItem.type === 'model' ? 'bg-blue-500' : 'bg-green-500'"></div>
      <span class="font-medium text-gray-700">{{ displayText }}</span>
      <el-icon class="text-gray-400 transition-transform flex-shrink-0" :class="{ 'rotate-180': isOpen }">
        <ArrowDown />
      </el-icon>
    </div>



    <!-- 下拉面板 -->
    <div
      v-if="isOpen"
      class="dropdown-panel absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 overflow-hidden"
      :style="{
        minWidth: '360px',
        maxWidth: '480px',
        maxHeight: getMaxHeight
      }"
    >
      <!-- 标签页切换 -->
      <div class="flex border-b border-gray-100">
        <button
          @click="activeTab = 'models'"
          class="flex-1 px-3 py-2 text-xs font-medium transition-colors"
          :class="activeTab === 'models'
            ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'"
        >
          <div class="flex items-center justify-center gap-1.5">
            <el-icon class="text-sm"><Monitor /></el-icon>
            模型列表
          </div>
        </button>
        <button
          @click="switchToAgentsTab"
          class="flex-1 px-3 py-2 text-xs font-medium transition-colors"
          :class="activeTab === 'agents'
            ? 'text-green-600 border-b-2 border-green-600 bg-green-50'
            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'"
        >
          <div class="flex items-center justify-center gap-1.5">
            <el-icon class="text-sm"><UserFilled /></el-icon>
            智能体
          </div>
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="content-area overflow-y-auto" :style="{ maxHeight: getContentHeight }">
        <!-- 模型列表 -->
        <div v-if="activeTab === 'models'" class="p-3">
          <div class="space-y-2">
            <div
              v-for="model in models"
              :key="model.id"
              @click="selectItem(model)"
              class="model-item p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:shadow-sm"
              :class="{ 'bg-blue-50 border border-blue-200 shadow-sm': selectedItem.id === model.id }"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <h4 class="font-semibold text-gray-800 text-sm">{{ model.name }}</h4>
                    <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">{{ model.version }}</span>
                  </div>
                  <p class="text-xs text-gray-600 mb-2 line-clamp-2 leading-relaxed">{{ model.description }}</p>
                  <div class="flex flex-wrap gap-1.5 text-xs">
                    <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                      {{ model.contextLength }}
                    </span>
                    <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                      {{ model.speed }}
                    </span>
                    <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                      {{ model.cost }}
                    </span>
                  </div>
                </div>
                <div v-if="selectedItem.id === model.id" class="text-blue-500 ml-2">
                  <el-icon size="16"><Check /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 智能体列表 -->
        <div v-if="activeTab === 'agents'" class="p-3">
          <!-- 搜索框 -->
          <div class="mb-3">
            <div class="relative">
              <el-input
                v-model="searchQuery"
                placeholder="搜索智能体..."
                size="small"
                clearable
                class="search-input"
              >
                <template #prefix>
                  <el-icon class="text-gray-400"><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <!-- 智能体数量提示 -->
          <div v-if="!agentsLoading && allAgents.length > 0" class="mb-3 px-2 py-1 bg-gray-50 rounded-lg">
            <span class="text-xs text-gray-500">
              <span v-if="searchQuery.trim()">
                找到 {{ filteredAgents.length }} / {{ allAgents.length }} 个智能体
              </span>
              <span v-else>
                共 {{ allAgents.length }} 个智能体
              </span>
              <span v-if="needsScroll" class="ml-2 text-blue-500">• 可滚动</span>
            </span>
          </div>

          <!-- 加载状态 -->
          <div v-if="agentsLoading" class="text-center py-8">
            <div class="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
              <el-icon class="text-gray-400 animate-spin" size="20"><Loading /></el-icon>
            </div>
            <p class="text-sm text-gray-500 mb-1">正在加载智能体...</p>
            <p class="text-xs text-gray-400">请稍候</p>
          </div>

          <!-- 空状态 -->
          <div v-else-if="allAgents.length === 0" class="text-center py-8">
            <div class="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
              <el-icon class="text-gray-400" size="20"><UserFilled /></el-icon>
            </div>
            <p class="text-sm text-gray-500 mb-1">暂无智能体</p>
            <p class="text-xs text-gray-400">请联系管理员添加智能体</p>
          </div>

          <!-- 搜索无结果 -->
          <div v-else-if="!agentsLoading && allAgents.length > 0 && filteredAgents.length === 0" class="text-center py-8">
            <div class="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
              <el-icon class="text-gray-400" size="20"><Search /></el-icon>
            </div>
            <p class="text-sm text-gray-500 mb-1">未找到匹配的智能体</p>
            <p class="text-xs text-gray-400">尝试使用其他关键词搜索</p>
          </div>

          <!-- 智能体列表 -->
          <div v-else-if="!agentsLoading && filteredAgents.length > 0" class="space-y-2">
            <div
              v-for="agent in filteredAgents"
              :key="agent.id"
              @click="selectItem(agent)"
              class="agent-item p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:shadow-sm"
              :class="{ 'bg-green-50 border border-green-200 shadow-sm': selectedItem.id === agent.id }"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <h4 class="font-semibold text-gray-800 text-sm">{{ agent.name }}</h4>
                    <span class="px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-full font-medium">智能体</span>
                  </div>
                  <p class="text-xs text-gray-600 mb-2 line-clamp-2 leading-relaxed">{{ agent.description }}</p>
                  <div class="flex items-center gap-3 text-xs text-gray-500">
                    <span class="flex items-center gap-1">
                      <el-icon class="text-xs"><House /></el-icon>
                      {{ agent.unit }}
                    </span>
                    <span class="flex items-center gap-1">
                      <el-icon class="text-xs"><User /></el-icon>
                      {{ agent.designer }}
                    </span>
                  </div>
                </div>
                <div v-if="selectedItem.id === agent.id" class="text-green-500 ml-2">
                  <el-icon size="16"><Check /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div
      v-if="isOpen"
      @click="closeDropdown"
      class="fixed inset-0 z-40"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ArrowDown, Monitor, UserFilled, Check, House, User, Search, Loading } from '@element-plus/icons-vue'
import { useModelStore, type ModelItem, type AgentItem, type SelectableItem } from '@/stores/modelStore'

// 使用共享数据存储
const {
  models,
  allAgents,
  allItems,
  selectedModelId,
  selectedModelInfo,
  setSelectedModel,
  loadAiExploreAgents,
  agentsLoading
} = useModelStore()

// Props
const props = defineProps<{
  modelValue: string
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [item: SelectableItem]
}>()

// 响应式数据
const isOpen = ref(false)
const activeTab = ref<'models' | 'agents'>('models')
const searchQuery = ref('')





// 过滤后的智能体列表
const filteredAgents = computed(() => {
  if (!searchQuery.value.trim()) {
    return allAgents.value
  }

  const query = searchQuery.value.toLowerCase().trim()
  return allAgents.value.filter(agent =>
    agent.name.toLowerCase().includes(query) ||
    agent.description.toLowerCase().includes(query) ||
    agent.unit.toLowerCase().includes(query) ||
    (agent.designer && agent.designer.toLowerCase().includes(query))
  )
})

// 当前选中项
const selectedItem = computed(() => {
  return allItems.value.find(item => item.id === props.modelValue) || models.value[0]
})

// 显示文本
const displayText = computed(() => {
  const item = selectedItem.value
  if (item.type === 'model') {
    return `当前模型：${item.name}`
  } else {
    return `当前智能体：${item.name}`
  }
})

// 动态计算最大高度
const getMaxHeight = computed(() => {
  const tabHeight = 48 // 标签页高度
  const itemHeight = 88 // 每个项目的实际高度（包含padding和margin）
  const containerPadding = 24 // 容器内边距
  const maxViewportHeight = Math.min(windowHeight.value * 0.7, 500) // 最大高度限制
  // PC端最小高度：确保至少显示4个项目
  const minItemsToShow = 4
  const minHeight = tabHeight + (minItemsToShow * itemHeight) + containerPadding + 40 // 40为搜索框和提示区域

  if (activeTab.value === 'models') {
    // 模型列表：根据实际数量计算
    const modelsCount = models.value.length
    const contentHeight = modelsCount * itemHeight + containerPadding
    const totalHeight = tabHeight + contentHeight

    return Math.max(Math.min(totalHeight, maxViewportHeight), minHeight) + 'px'
  } else {
    // 智能体列表：根据过滤后的数量动态调整
    const agentsCount = filteredAgents.value.length

    if (agentsCount === 0) {
      // 空状态：固定高度
      return Math.max(tabHeight + 160, minHeight) + 'px'
    } else {
      // 有智能体：计算实际需要的高度
      const searchHeight = 40 // 搜索框高度
      const headerHeight = 32 // 数量提示区域高度
      const contentHeight = agentsCount * itemHeight + containerPadding + searchHeight + headerHeight
      const totalHeight = tabHeight + contentHeight

      // 如果项目较少，显示全部；如果较多，限制高度并滚动
      if (agentsCount <= 4) {
        return Math.max(Math.min(totalHeight, maxViewportHeight), minHeight) + 'px'
      } else {
        // 超过4个时，显示4.5个项目的高度（暗示可滚动）
        const limitedContentHeight = 4.5 * itemHeight + containerPadding + searchHeight + headerHeight
        const limitedTotalHeight = tabHeight + limitedContentHeight
        return Math.max(Math.min(limitedTotalHeight, maxViewportHeight), minHeight) + 'px'
      }
    }
  }
})

// 内容区域高度
const getContentHeight = computed(() => {
  const maxHeight = parseInt(getMaxHeight.value)
  const tabHeight = 48 // 与上面保持一致
  return (maxHeight - tabHeight) + 'px'
})

// 是否需要滚动
const needsScroll = computed(() => {
  if (activeTab.value === 'models') {
    return models.value.length > 5
  } else {
    return filteredAgents.value.length > 4
  }
})

// 方法
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
  // 关闭时清空搜索
  searchQuery.value = ''
}

const selectItem = (item: SelectableItem) => {
  setSelectedModel(item.id, item)
  emit('update:modelValue', item.id)
  emit('change', item)
  closeDropdown()
}

// 切换到智能体标签页
const switchToAgentsTab = async () => {
  activeTab.value = 'agents'
  // 如果智能体数据为空且不在加载中，则加载数据
  if (allAgents.value.length === 0 && !agentsLoading.value) {
    console.log('切换到智能体标签页，开始加载智能体数据')
    await loadAiExploreAgents()
  }
}





// 点击外部关闭
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.model-selector')) {
    closeDropdown()
  }
}

// 窗口大小变化时重新计算高度
const windowHeight = ref(window.innerHeight)

const handleResize = () => {
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.model-selector {
  position: relative;
  display: inline-block;
  min-width: fit-content;
}

.dropdown-panel {
  min-width: 360px;
  max-width: 480px;
  transition: height 0.2s ease-out, max-height 0.2s ease-out;
}

.content-area {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
  transition: height 0.2s ease-out;
}

.content-area::-webkit-scrollbar {
  width: 6px;
}

.content-area::-webkit-scrollbar-track {
  background: transparent;
}

.content-area::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* 标签页高度调整 */
.dropdown-panel .flex.border-b {
  min-height: 48px;
}

/* 搜索框样式 */
.search-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.search-input :deep(.el-input__wrapper):hover {
  border-color: #d1d5db;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.model-item,
.agent-item {
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.model-item:hover,
.agent-item:hover {
  border-color: #e5e7eb;
  transform: translateY(-1px);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .dropdown-panel {
    min-width: 320px;
    max-width: 90vw;
    left: -50px;
    right: auto;
  }
}

@media (max-width: 480px) {
  .dropdown-panel {
    min-width: 300px;
    left: -80px;
  }

  .model-item,
  .agent-item {
    padding: 12px;
  }
}


</style>
