package com.xhcai.modules.agent.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.agent.init.AgentPermissionInitializer;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 智能体模块初始化控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "智能体模块初始化", description = "智能体模块初始化相关接口")
@RestController
@RequestMapping("/api/agent/init")
public class AgentInitController {

    private static final Logger log = LoggerFactory.getLogger(AgentInitController.class);

    @Autowired
    private AgentPermissionInitializer agentPermissionInitializer;

    @Operation(summary = "检查智能体模块初始化状态", description = "检查智能体模块是否已初始化")
    @GetMapping("/status")
    @RequiresPermissions("agent:init:status")
    public Result<Map<String, Object>> checkInitStatus() {
        log.info("检查智能体模块初始化状态");

        Map<String, Object> status = new HashMap<>();
        status.put("moduleId", "agent");
        status.put("moduleName", "智能体模块");
        boolean initialized = checkAgentInitialized();
        status.put("initialized", initialized);
        status.put("status", initialized ? "INITIALIZED" : "NOT_INITIALIZED");
        status.put("progress", initialized ? 100 : 0);
        status.put("message", initialized ? "智能体模块已初始化" : "智能体模块未初始化");

        return Result.success(status);
    }

    @Operation(summary = "初始化智能体模块", description = "执行智能体模块初始化")
    @PostMapping("/execute")
    @RequiresPermissions("agent:init:execute")
    public Result<Map<String, Object>> executeInit(
            @Parameter(description = "是否强制重新初始化") @RequestParam(defaultValue = "false") Boolean forceReinit) {
        // 从当前登录用户获取租户ID
        String tenantId = com.xhcai.common.security.utils.SecurityUtils.getCurrentTenantId();
        log.info("执行智能体模块初始化: tenantId={}, forceReinit={}", tenantId, forceReinit);

        Map<String, Object> result = new HashMap<>();
        result.put("moduleId", "agent");
        result.put("moduleName", "智能体模块");
        result.put("startTime", System.currentTimeMillis());

        try {
            // 检查是否已初始化
            if (checkAgentInitialized() && !forceReinit) {
                result.put("status", "SKIPPED");
                result.put("message", "智能体模块已初始化，跳过");
                result.put("progress", 100);
                return Result.success(result);
            }

            // 执行初始化
            result.put("progress", 30);
            agentPermissionInitializer.initializeAll();

            result.put("progress", 70);

            result.put("status", "SUCCESS");
            result.put("message", "智能体模块初始化成功");
            result.put("progress", 100);

        } catch (Exception e) {
            log.error("智能体模块初始化失败", e);
            result.put("status", "FAILED");
            result.put("message", "智能体模块初始化失败: " + e.getMessage());
            result.put("progress", 0);
        } finally {
            result.put("endTime", System.currentTimeMillis());
            long duration = (Long) result.get("endTime") - (Long) result.get("startTime");
            result.put("duration", duration);
        }

        return Result.success(result);
    }

    @Operation(summary = "获取智能体模块信息", description = "获取智能体模块的基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> getModuleInfo() {
        log.info("获取智能体模块信息");

        Map<String, Object> info = new HashMap<>();
        info.put("moduleId", "agent");
        info.put("moduleName", "智能体模块");
        info.put("version", "1.0.0");
        info.put("author", "xhcai");
        info.put("description", "智能体管理模块，提供智能体创建、配置、运行等功能");
        info.put("features", new String[]{
            "智能体创建", "智能体配置", "智能体运行",
            "智能体模板", "智能体权限管理", "智能体监控"
        });
        info.put("apiPrefix", "/api/agent");
        info.put("order", 150);

        return Result.success(info);
    }

    /**
     * 检查智能体模块是否已初始化
     */
    private boolean checkAgentInitialized() {
        try {
            // 这里可以通过查询数据库中的权限数据来判断是否已初始化
            // 暂时返回false，表示未初始化
            return false;
        } catch (Exception e) {
            log.debug("检查智能体初始化状态失败", e);
            return false;
        }
    }
}
