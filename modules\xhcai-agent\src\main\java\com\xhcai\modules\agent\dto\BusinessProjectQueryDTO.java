package com.xhcai.modules.agent.dto;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

/**
 * 业务项目查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "业务项目查询DTO")
public class BusinessProjectQueryDTO extends PageTimeRangeQueryDTO {

    /**
     * 项目名称（模糊查询）
     */
    @Schema(description = "项目名称（模糊查询）")
    private String name;

    /**
     * 项目描述（模糊查询）
     */
    @Schema(description = "项目描述（模糊查询）")
    private String description;

    /**
     * 应用环境
     */
    @Pattern(regexp = "^(production|test|development)$", message = "应用环境只能是production、test或development")
    @Schema(description = "应用环境：production-生产环境，test-测试环境，development-开发环境")
    private String environment;

    /**
     * 项目状态
     */
    @Pattern(regexp = "^(active|inactive|maintenance)$", message = "项目状态只能是active、inactive或maintenance")
    @Schema(description = "项目状态：active-运行中，inactive-已停止，maintenance-维护中")
    private String status;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID")
    private String ownerId;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }
}
