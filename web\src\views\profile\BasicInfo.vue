<template>
  <div class="basic-info">
    <div class="section-header">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">基本信息</h2>
      <p class="text-gray-600">管理您的个人资料信息</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <i class="fas fa-spinner fa-spin text-3xl text-blue-500 mb-4"></i>
        <p class="text-gray-600">加载用户信息中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="!currentUser" class="flex items-center justify-center py-12">
      <div class="text-center">
        <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-4"></i>
        <p class="text-gray-600 mb-4">加载用户信息失败</p>
        <button @click="loadUserProfile" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
          重新加载
        </button>
      </div>
    </div>

    <!-- 用户信息内容 -->
    <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
      <!-- 左侧头像和基本信息 -->
      <div class="space-y-6">
        <!-- 头像和基本信息 -->
        <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6 text-center">
          <div class="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-3xl font-bold mx-auto mb-4 shadow-lg">
            {{ currentUser?.nickname?.charAt(0)?.toUpperCase() || currentUser?.username?.charAt(0)?.toUpperCase() || 'U' }}
          </div>
          <h3 class="text-lg font-semibold text-gray-800">{{ currentUser?.nickname || currentUser?.username || '加载中...' }}</h3>
          <p class="text-sm text-gray-600 mt-1">{{ currentUser?.email || '-' }}</p>
          <div class="mt-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              {{ currentUser?.userTypeText || '-' }}
            </span>
          </div>
          <button class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm font-medium">
            更换头像
          </button>
        </div>
      </div>

      <!-- 右侧详细信息 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 基本信息表单 -->
        <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center gap-2">
              <el-icon class="text-blue-500"><User /></el-icon>
              个人资料
            </h3>
            <button
              @click="toggleEdit"
              class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm font-medium"
            >
              {{ isEditing ? '保存' : '编辑' }}
            </button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">昵称</label>
              <input
                v-if="isEditing"
                v-model="editForm.nickname"
                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入昵称"
              />
              <div v-else class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ currentUser?.nickname || '-' }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">邮箱</label>
              <input
                v-if="isEditing"
                v-model="editForm.email"
                type="email"
                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入邮箱"
              />
              <div v-else class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ currentUser?.email || '-' }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">手机号</label>
              <input
                v-if="isEditing"
                v-model="editForm.phone"
                type="tel"
                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入手机号"
              />
              <div v-else class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ currentUser?.phone || '未设置' }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">性别</label>
              <select
                v-if="isEditing"
                v-model="editForm.gender"
                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">请选择性别</option>
                <option value="0">未知</option>
                <option value="1">男</option>
                <option value="2">女</option>
              </select>
              <div v-else class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ currentUser?.genderText || '未设置' }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">用户名</label>
              <div class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800 font-mono">
                {{ currentUser?.username || '-' }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">部门</label>
              <div class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ currentUser?.deptName || '未设置' }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">用户类型</label>
              <div class="bg-gray-50 rounded-lg px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ currentUser?.userTypeText || '-' }}
                </span>
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">生日</label>
              <input
                v-if="isEditing"
                v-model="editForm.birthday"
                type="date"
                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div v-else class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ currentUser?.birthday || '未设置' }}
              </div>
            </div>

            <div class="space-y-2 md:col-span-2">
              <label class="text-sm font-medium text-gray-600">个人简介</label>
              <textarea
                v-if="isEditing"
                v-model="editForm.bio"
                rows="3"
                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="请输入个人简介"
              ></textarea>
              <div v-else class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800 min-h-[80px]">
                {{ currentUser?.bio || '暂无个人简介' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 账户信息 -->
        <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
          <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
            <el-icon class="text-green-500"><InfoFilled /></el-icon>
            账户信息
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">注册时间</label>
              <div class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ formatDate(currentUser?.createTime) }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">最后登录</label>
              <div class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ formatDate(currentUser?.lastLoginTime) }}
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">账户状态</label>
              <div class="bg-gray-50 rounded-lg px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {{ currentUser?.status === 'active' ? '正常' : '禁用' }}
                </span>
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">租户</label>
              <div class="bg-gray-50 rounded-lg px-4 py-3 text-gray-800">
                {{ currentUser?.tenantName || '默认租户' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { User, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import {
  getCurrentUserProfile,
  updateCurrentUserProfile,
  changeCurrentUserPassword,
  type UserProfileVO,
  type UserProfileUpdateDTO,
  type ChangePasswordDTO
} from '@/api/system/user'

// 当前用户信息
const currentUser = ref<UserProfileVO | null>(null)
const loading = ref(false)

// 编辑状态
const isEditing = ref(false)

// 编辑表单数据
const editForm = reactive<UserProfileUpdateDTO>({
  nickname: '',
  email: '',
  phone: '',
  avatar: '',
  gender: '',
  birthday: '',
  bio: ''
})

// 初始化数据
onMounted(() => {
  loadUserProfile()
})

// 加载用户信息
const loadUserProfile = async () => {
  try {
    loading.value = true
    const response = await getCurrentUserProfile()
    if (response.success && response.data) {
      currentUser.value = response.data
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElMessage.error('加载用户信息失败')
  } finally {
    loading.value = false
  }
}

const toggleEdit = async () => {
  if (isEditing.value) {
    // 保存逻辑
    try {
      loading.value = true
      const response = await updateCurrentUserProfile(editForm)
      if (response.success) {
        ElMessage.success('个人信息更新成功')
        await loadUserProfile() // 重新加载用户信息
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      ElMessage.error('更新用户信息失败')
    } finally {
      loading.value = false
    }
  } else {
    // 进入编辑模式，重置表单
    if (currentUser.value) {
      editForm.nickname = currentUser.value.nickname || ''
      editForm.email = currentUser.value.email || ''
      editForm.phone = currentUser.value.phone || ''
      editForm.avatar = currentUser.value.avatar || ''
      editForm.gender = currentUser.value.gender || ''
      editForm.birthday = currentUser.value.birthday || ''
    }
  }
  isEditing.value = !isEditing.value
}

// 删除不需要的方法，因为我们现在使用后端返回的文本

const formatDate = (date?: Date) => {
  if (!date) return '未知'
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.section-header {
  margin-bottom: 1.5rem;
}
</style>
