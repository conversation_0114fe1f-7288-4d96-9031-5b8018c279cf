<template>
  <div
    class="base-node"
    :class="[
      `node-${type}`,
      { selected: selected, error: hasError }
    ]"
    :style="nodeStyle"
  >
    <!-- 节点头部 -->
    <div class="node-header" :style="headerStyle">
      <div class="node-icon">
        <i :class="icon" :style="{ color: iconColor }"></i>
      </div>
      <div class="node-title">
        <span class="node-label">{{ label }}</span>
        <span v-if="status" class="node-status" :class="`status-${status}`">
          {{ statusText }}
        </span>
      </div>
      <div class="node-actions">
        <button
          v-if="configurable"
          class="action-btn"
          @click="$emit('configure')"
          title="配置"
        >
          <i class="fas fa-cog"></i>
        </button>
        <button
          class="action-btn"
          @click="$emit('delete')"
          title="删除"
        >
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>

    <!-- 节点内容 -->
    <div v-if="$slots.default" class="node-content">
      <slot></slot>
    </div>

    <!-- 错误信息 -->
    <div v-if="hasError" class="node-error">
      <i class="fas fa-exclamation-triangle"></i>
      <span>{{ errorMessage }}</span>
    </div>

    <!-- 连接点 -->
    <Handle
      v-for="handle in targetHandles"
      :key="`target-${handle.id}`"
      :id="handle.id"
      type="target"
      :position="handle.position"
      :style="handleStyle"
    />
    <Handle
      v-for="handle in sourceHandles"
      :key="`source-${handle.id}`"
      :id="handle.id"
      type="source"
      :position="handle.position"
      :style="handleStyle"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'

// Position 类型定义
type PositionType = 'left' | 'right' | 'top' | 'bottom'

// Props
interface Props {
  type: string
  label: string
  icon: string
  color?: string
  gradient?: string
  labelColor?: string
  iconColor?: string
  selected?: boolean
  configurable?: boolean
  status?: 'idle' | 'running' | 'success' | 'error'
  errorMessage?: string
  sourceHandles?: Array<{ id: string; position: PositionType }>
  targetHandles?: Array<{ id: string; position: PositionType }>
}

const props = withDefaults(defineProps<Props>(), {
  color: '#64748b',
  gradient: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
  labelColor: '#1e293b',
  iconColor: '#64748b',
  selected: false,
  configurable: true,
  status: 'idle',
  errorMessage: '',
  sourceHandles: () => [],
  targetHandles: () => []
})

// Emits
defineEmits<{
  'configure': []
  'delete': []
}>()

// 计算属性
const hasError = computed(() => props.status === 'error' || !!props.errorMessage)

const nodeStyle = computed(() => ({
  background: props.gradient,
  borderColor: props.selected ? props.color : '#e2e8f0'
}))

const headerStyle = computed(() => ({
  color: props.labelColor
}))

const handleStyle = computed(() => ({
  background: props.color,
  border: `2px solid ${props.color}`
}))

const statusText = computed(() => {
  switch (props.status) {
    case 'running':
      return '运行中'
    case 'success':
      return '成功'
    case 'error':
      return '错误'
    default:
      return '就绪'
  }
})
</script>

<style scoped>
.base-node {
  min-width: 200px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
}

.base-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.base-node.selected {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.base-node.error {
  border-color: #ef4444;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.node-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.node-title {
  flex: 1;
  min-width: 0;
}

.node-label {
  font-weight: 500;
  font-size: 14px;
  display: block;
}

.node-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 2px;
  display: inline-block;
}

.status-idle {
  background: #f1f5f9;
  color: #64748b;
}

.status-running {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-success {
  background: #dcfce7;
  color: #166534;
}

.status-error {
  background: #fee2e2;
  color: #dc2626;
}

.node-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.node-content {
  padding: 12px;
}

.node-error {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #fee2e2;
  color: #dc2626;
  font-size: 12px;
  border-top: 1px solid #fecaca;
}

/* Handle 样式 */
:deep(.vue-flow__handle) {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid;
  background: white;
}

:deep(.vue-flow__handle-connecting) {
  background: #3b82f6;
}

:deep(.vue-flow__handle-valid) {
  background: #10b981;
}
</style>
