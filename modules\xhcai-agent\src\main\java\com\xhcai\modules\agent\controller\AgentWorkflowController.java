package com.xhcai.modules.agent.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.modules.agent.dto.AgentWorkflowCreateDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowQueryDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowUpdateDTO;
import com.xhcai.modules.agent.dto.ImportRequestDTO;
import com.xhcai.modules.agent.dto.RealtimeSaveDTO;
import com.xhcai.modules.agent.dto.RollbackRequestDTO;
import com.xhcai.modules.agent.service.IAgentWorkflowService;
import com.xhcai.modules.agent.vo.AgentWorkflowVO;
import com.xhcai.modules.agent.vo.AgentWorkflowVersionVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 智能体工作流控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "智能体工作流管理", description = "智能体工作流的创建、编辑、版本管理等功能")
@RestController
@RequestMapping("/api/agent/workflow")
public class AgentWorkflowController {

    @Autowired
    private IAgentWorkflowService agentWorkflowService;

    /**
     * 分页查询工作流列表
     */
    @Operation(summary = "分页查询工作流列表", description = "根据条件分页查询智能体工作流列表")
    @PostMapping("/page")
    @RequiresPermissions("agent:workflow:list")
    public Result<IPage<AgentWorkflowVO>> getWorkflowPage(@Valid @RequestBody AgentWorkflowQueryDTO queryDTO) {
        IPage<AgentWorkflowVO> page = agentWorkflowService.getWorkflowPage(queryDTO);
        return Result.success(page);
    }

    /**
     * 根据ID查询工作流详情
     */
    @Operation(summary = "查询工作流详情", description = "根据ID查询智能体工作流详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("agent:workflow:detail")
    public Result<AgentWorkflowVO> getWorkflowById(
            @Parameter(description = "工作流ID", required = true) @PathVariable String id) {
        AgentWorkflowVO workflow = agentWorkflowService.getWorkflowById(id);
        return Result.success(workflow);
    }

    /**
     * 根据智能体ID查询最新版本工作流
     */
    @Operation(summary = "查询最新版本工作流", description = "根据智能体ID查询最新版本的工作流配置")
    @GetMapping("/latest/{agentId}")
    @RequiresPermissions("agent:workflow:detail")
    public Result<AgentWorkflowVO> getLatestWorkflowByAgentId(
            @Parameter(description = "智能体ID", required = true) @PathVariable String agentId) {
        AgentWorkflowVO workflow = agentWorkflowService.getLatestWorkflowByAgentId(agentId);
        return Result.success(workflow);
    }

    /**
     * 创建工作流
     */
    @Operation(summary = "创建工作流", description = "创建新的智能体工作流")
    @PostMapping
    @RequiresPermissions("agent:workflow:create")
    public Result<String> createWorkflow(@Valid @RequestBody AgentWorkflowCreateDTO createDTO) {
        String workflowId = agentWorkflowService.createWorkflow(createDTO);
        return Result.success(workflowId);
    }

    /**
     * 更新工作流
     */
    @Operation(summary = "更新工作流", description = "更新智能体工作流配置")
    @PutMapping
    @RequiresPermissions("agent:workflow:update")
    public Result<Boolean> updateWorkflow(@Valid @RequestBody AgentWorkflowUpdateDTO updateDTO) {
        boolean result = agentWorkflowService.updateWorkflow(updateDTO);
        return Result.success(result);
    }

    /**
     * 实时保存工作流配置
     */
    @Operation(summary = "实时保存工作流", description = "实时保存工作流配置，支持增量更新")
    @PostMapping("/save-realtime")
    @RequiresPermissions("agent:workflow:update")
    public Result<String> saveWorkflowRealtime(@Valid @RequestBody RealtimeSaveDTO realtimeSaveDTO) {
        String result = agentWorkflowService.realtimeSave(realtimeSaveDTO);
        return Result.success(result);
    }

    /**
     * 删除工作流
     */
    @Operation(summary = "删除工作流", description = "删除指定的智能体工作流")
    @DeleteMapping("/{id}")
    @RequiresPermissions("agent:workflow:delete")
    public Result<Boolean> deleteWorkflow(
            @Parameter(description = "工作流ID", required = true) @PathVariable String id) {
        boolean result = agentWorkflowService.deleteWorkflow(id);
        return Result.success(result);
    }

    /**
     * 批量删除工作流
     */
    @Operation(summary = "批量删除工作流", description = "批量删除智能体工作流")
    @DeleteMapping("/batch")
    @RequiresPermissions("agent:workflow:delete")
    public Result<Boolean> deleteWorkflowBatch(@RequestBody List<String> ids) {
        boolean result = agentWorkflowService.deleteWorkflowBatch(ids);
        return Result.success(result);
    }

    /**
     * 发布工作流
     */
    @Operation(summary = "发布工作流", description = "发布智能体工作流，使其生效")
    @PutMapping("/{id}/publish")
    @RequiresPermissions("agent:workflow:publish")
    public Result<Boolean> publishWorkflow(
            @Parameter(description = "工作流ID", required = true) @PathVariable String id) {
        boolean result = agentWorkflowService.publishWorkflow(id);
        return Result.success(result);
    }

    /**
     * 取消发布工作流
     */
    @Operation(summary = "取消发布工作流", description = "取消发布智能体工作流")
    @PutMapping("/{id}/unpublish")
    @RequiresPermissions("agent:workflow:publish")
    public Result<Boolean> unpublishWorkflow(
            @Parameter(description = "工作流ID", required = true) @PathVariable String id) {
        boolean result = agentWorkflowService.unpublishWorkflow(id);
        return Result.success(result);
    }

    /**
     * 查询工作流版本历史
     */
    @Operation(summary = "查询版本历史", description = "查询智能体工作流的版本历史记录")
    @GetMapping("/versions/{agentId}")
    @RequiresPermissions("agent:workflow:detail")
    public Result<List<AgentWorkflowVersionVO>> getVersionHistory(
            @Parameter(description = "智能体ID", required = true) @PathVariable String agentId) {
        List<AgentWorkflowVersionVO> versions = agentWorkflowService.getVersionHistory(agentId);
        return Result.success(versions);
    }

    /**
     * 回滚到指定版本
     */
    @Operation(summary = "回滚版本", description = "回滚工作流到指定版本")
    @PostMapping("/rollback")
    @RequiresPermissions("agent:workflow:update")
    public Result<Boolean> rollbackToVersion(@Valid @RequestBody RollbackRequestDTO rollbackRequest) {
        boolean result = agentWorkflowService.rollbackToVersion(
                rollbackRequest.getAgentId(),
                rollbackRequest.getVersion());
        return Result.success(result);
    }

    /**
     * 复制工作流到新版本
     */
    @Operation(summary = "复制到新版本", description = "复制现有工作流到新版本")
    @PostMapping("/copy/{sourceId}")
    @RequiresPermissions("agent:workflow:create")
    public Result<String> copyToNewVersion(
            @Parameter(description = "源工作流ID", required = true) @PathVariable String sourceId,
            @Parameter(description = "操作描述") @RequestParam(required = false) String operationDesc) {
        String newWorkflowId = agentWorkflowService.copyToNewVersion(sourceId, operationDesc);
        return Result.success(newWorkflowId);
    }

    /**
     * 导出工作流配置
     */
    @Operation(summary = "导出工作流配置", description = "导出工作流配置为JSON格式")
    @GetMapping("/{id}/export")
    @RequiresPermissions("agent:workflow:detail")
    public Result<String> exportWorkflowConfig(
            @Parameter(description = "工作流ID", required = true) @PathVariable String id) {
        String configJson = agentWorkflowService.exportWorkflowConfig(id);
        return Result.success(configJson);
    }

    /**
     * 导入工作流配置
     */
    @Operation(summary = "导入工作流配置", description = "从JSON配置导入工作流")
    @PostMapping("/import")
    @RequiresPermissions("agent:workflow:create")
    public Result<String> importWorkflowConfig(@Valid @RequestBody ImportRequestDTO importRequest) {
        String workflowId = agentWorkflowService.importWorkflowConfig(
                importRequest.getAgentId(),
                importRequest.getConfigJson());
        return Result.success(workflowId);
    }

    /**
     * 获取Vue Flow节点库配置
     */
    @Operation(summary = "获取节点库配置", description = "获取可用的节点类型和配置模板")
    @GetMapping("/node-library")
    @RequiresPermissions("agent:workflow:detail")
    @RequiresTenantAdmin(allowPlatformAdmin = true)
    public Result<String> getNodeLibrary() {
        String nodeLibrary = agentWorkflowService.getDefaultNodeLibrary();
        return Result.success("获取节点库配置成功", nodeLibrary);
    }

    /**
     * 获取工作流统计信息
     */
    @Operation(summary = "获取统计信息", description = "获取智能体工作流的统计信息")
    @GetMapping("/stats/{agentId}")
    @RequiresPermissions("agent:workflow:detail")
    public Result<WorkflowStatsVO> getWorkflowStats(
            @Parameter(description = "智能体ID", required = true) @PathVariable String agentId) {

        WorkflowStatsVO stats = new WorkflowStatsVO();
        stats.setTotalCount(agentWorkflowService.countByAgentId(agentId));
        stats.setVersionCount(agentWorkflowService.countVersionsByAgentId(agentId));
        stats.setMaxVersion(agentWorkflowService.getMaxVersionByAgentId(agentId));

        return Result.success(stats);
    }

    /**
     * 工作流统计信息VO
     */
    public static class WorkflowStatsVO {

        private Long totalCount;
        private Long versionCount;
        private Integer maxVersion;

        // Getters and Setters
        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public Long getVersionCount() {
            return versionCount;
        }

        public void setVersionCount(Long versionCount) {
            this.versionCount = versionCount;
        }

        public Integer getMaxVersion() {
            return maxVersion;
        }

        public void setMaxVersion(Integer maxVersion) {
            this.maxVersion = maxVersion;
        }
    }
}
