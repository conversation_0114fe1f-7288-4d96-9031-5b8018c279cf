package com.xhcai.plugin.core;

/**
 * 插件类型枚举
 * 定义系统支持的插件类型，每种类型有独立的上下文环境
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PluginType {
    
    /**
     * 存储插件 - 支持 MinIO、FTP、OSS 等存储服务
     */
    STORAGE("storage", "存储插件", "com.xhcai.plugin.storage.IStorageService"),
    
    /**
     * 模型插件 - 支持 OpenAI、Claude、本地模型等
     */
    MODEL("model", "模型插件", "com.xhcai.plugin.model.IModelService"),
    
    /**
     * 通知插件 - 支持邮件、短信、微信等通知服务
     */
    NOTIFICATION("notification", "通知插件", "com.xhcai.plugin.notification.INotificationService"),
    
    /**
     * 数据源插件 - 支持不同数据库、API 数据源
     */
    DATASOURCE("datasource", "数据源插件", "com.xhcai.plugin.datasource.IDatasourceService");
    
    private final String code;
    private final String name;
    private final String serviceInterface;
    
    PluginType(String code, String name, String serviceInterface) {
        this.code = code;
        this.name = name;
        this.serviceInterface = serviceInterface;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getServiceInterface() {
        return serviceInterface;
    }
    
    /**
     * 根据代码获取插件类型
     */
    public static PluginType fromCode(String code) {
        for (PluginType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown plugin type code: " + code);
    }
}
