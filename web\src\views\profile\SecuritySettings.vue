<template>
  <div class="security-settings">
    <div class="section-header">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">安全设置</h2>
      <p class="text-gray-600">管理您的账户安全和隐私设置</p>
    </div>

    <div class="space-y-6 mt-6">
      <!-- 密码设置 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
          <el-icon class="text-blue-500"><Lock /></el-icon>
          密码设置
        </h3>

        <div class="space-y-4">
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">修改密码</h4>
              <p class="text-sm text-gray-600">定期更新密码以保护账户安全</p>
              <p class="text-xs text-gray-500 mt-1">上次修改：2024-01-15</p>
            </div>
            <button 
              @click="showChangePassword = true"
              class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm"
            >
              修改密码
            </button>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">密码强度</h4>
              <p class="text-sm text-gray-600">当前密码强度评估</p>
            </div>
            <div class="flex items-center gap-2">
              <div class="flex gap-1">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
              </div>
              <span class="text-sm font-medium text-green-600">强</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 两步验证 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
          <el-icon class="text-green-500"><Guide /></el-icon>
          两步验证
        </h3>

        <div class="space-y-4">
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">短信验证</h4>
              <p class="text-sm text-gray-600">通过手机短信接收验证码</p>
              <p class="text-xs text-gray-500 mt-1">绑定手机：138****8000</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-green-600 font-medium">已启用</span>
              <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 transition-colors">
                管理
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">邮箱验证</h4>
              <p class="text-sm text-gray-600">通过邮箱接收验证码</p>
              <p class="text-xs text-gray-500 mt-1">绑定邮箱：zhang***@example.com</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-500 font-medium">未启用</span>
              <button class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors">
                启用
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">身份验证器</h4>
              <p class="text-sm text-gray-600">使用身份验证器应用生成验证码</p>
              <p class="text-xs text-gray-500 mt-1">支持 Google Authenticator、Microsoft Authenticator 等</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-500 font-medium">未启用</span>
              <button class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors">
                启用
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 登录安全 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
          <el-icon class="text-orange-500"><Key /></el-icon>
          登录安全
        </h3>

        <div class="space-y-4">
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">登录设备管理</h4>
              <p class="text-sm text-gray-600">查看和管理已登录的设备</p>
            </div>
            <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm">
              查看设备
            </button>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">异常登录提醒</h4>
              <p class="text-sm text-gray-600">检测到异常登录时发送提醒</p>
            </div>
            <el-switch v-model="securitySettings.abnormalLoginAlert" />
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">自动登出</h4>
              <p class="text-sm text-gray-600">长时间无操作时自动登出</p>
            </div>
            <select v-model="securitySettings.autoLogoutTime" class="px-3 py-1 border border-gray-300 rounded text-sm">
              <option value="30">30分钟</option>
              <option value="60">1小时</option>
              <option value="120">2小时</option>
              <option value="240">4小时</option>
              <option value="0">永不</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 隐私设置 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
          <el-icon class="text-purple-500"><View /></el-icon>
          隐私设置
        </h3>

        <div class="space-y-4">
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">个人信息可见性</h4>
              <p class="text-sm text-gray-600">控制其他用户可以看到的个人信息</p>
            </div>
            <select v-model="securitySettings.profileVisibility" class="px-3 py-1 border border-gray-300 rounded text-sm">
              <option value="public">公开</option>
              <option value="friends">仅好友</option>
              <option value="private">私密</option>
            </select>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">活动记录</h4>
              <p class="text-sm text-gray-600">是否记录和显示活动历史</p>
            </div>
            <el-switch v-model="securitySettings.activityLogging" />
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-800">数据导出</h4>
              <p class="text-sm text-gray-600">导出您的个人数据</p>
            </div>
            <button class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors duration-200 text-sm">
              导出数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改密码弹窗 -->
    <el-dialog v-model="showChangePassword" title="修改密码" width="500px">
      <el-form :model="passwordForm" label-width="100px">
        <el-form-item label="当前密码">
          <el-input v-model="passwordForm.currentPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showChangePassword = false">取消</el-button>
          <el-button type="primary" @click="changePassword">确认修改</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Lock, Guide, Key, View } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 安全设置
const securitySettings = reactive({
  abnormalLoginAlert: true,
  autoLogoutTime: '60',
  profileVisibility: 'friends',
  activityLogging: true
})

// 修改密码相关
const showChangePassword = ref(false)
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const changePassword = () => {
  if (!passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }
  
  // 这里添加修改密码的逻辑
  console.log('修改密码:', passwordForm)
  ElMessage.success('密码修改成功')
  showChangePassword.value = false
  
  // 重置表单
  passwordForm.currentPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
}
</script>

<style scoped>
.section-header {
  margin-bottom: 1.5rem;
}
</style>
