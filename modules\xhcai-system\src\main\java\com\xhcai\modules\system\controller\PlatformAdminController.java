package com.xhcai.modules.system.controller;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.CrossTenantAccess;
import com.xhcai.common.security.annotation.RequiresPlatformAdmin;
import com.xhcai.modules.system.dto.SysUserQueryDTO;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.service.IPlatformAdminService;
import com.xhcai.modules.system.vo.SysUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 平台管理员控制器 提供跨租户的管理功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "平台管理员", description = "平台管理员专用接口")
@RestController
@RequestMapping("/api/platform/admin")
@RequiresPlatformAdmin(message = "平台管理功能需要平台管理员权限")
public class PlatformAdminController {

    @Autowired
    private IPlatformAdminService platformAdminService;

    // ========== 跨租户用户管理 ==========
    /**
     * 跨租户分页查询用户列表
     */
    @Operation(summary = "跨租户查询用户", description = "查询指定租户或所有租户的用户列表")
    @GetMapping("/users")
    @CrossTenantAccess
    public Result<PageResult<SysUserVO>> getCrossTenantUsers(
            @Valid SysUserQueryDTO queryDTO,
            @Parameter(description = "租户ID，null表示查询所有租户") @RequestParam(required = false) String tenantId) {
        PageResult<SysUserVO> pageResult = platformAdminService.selectCrossTenantUserPage(queryDTO, tenantId);
        return Result.success(pageResult);
    }

    /**
     * 跨租户查询用户详情
     */
    @Operation(summary = "跨租户用户详情", description = "查询指定租户的用户详细信息")
    @GetMapping("/users/{userId}")
    @CrossTenantAccess
    public Result<SysUserVO> getCrossTenantUserDetail(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId,
            @Parameter(description = "租户ID", required = true) @RequestParam String tenantId) {
        SysUserVO userVO = platformAdminService.getCrossTenantUserDetail(userId, tenantId);
        return Result.success(userVO);
    }

    /**
     * 跨租户创建用户
     */
    @Operation(summary = "跨租户创建用户", description = "在指定租户中创建用户")
    @PostMapping("/users")
    @CrossTenantAccess
    public Result<Void> createCrossTenantUser(
            @Valid @RequestBody SysUser user,
            @Parameter(description = "目标租户ID", required = true) @RequestParam String targetTenantId) {
        boolean result = platformAdminService.createCrossTenantUser(user, targetTenantId);
        return result ? Result.success() : Result.fail("创建用户失败");
    }

    /**
     * 跨租户更新用户
     */
    @Operation(summary = "跨租户更新用户", description = "更新指定租户的用户信息")
    @PutMapping("/users")
    @CrossTenantAccess
    public Result<Void> updateCrossTenantUser(
            @Valid @RequestBody SysUser user,
            @Parameter(description = "目标租户ID", required = true) @RequestParam String targetTenantId) {
        boolean result = platformAdminService.updateCrossTenantUser(user, targetTenantId);
        return result ? Result.success() : Result.fail("更新用户失败");
    }

    /**
     * 跨租户删除用户
     */
    @Operation(summary = "跨租户删除用户", description = "删除指定租户的用户")
    @DeleteMapping("/users")
    @CrossTenantAccess
    public Result<Void> deleteCrossTenantUsers(
            @RequestBody List<String> userIds,
            @Parameter(description = "租户ID", required = true) @RequestParam String tenantId) {
        boolean result = platformAdminService.deleteCrossTenantUsers(userIds, tenantId);
        return result ? Result.success() : Result.fail("删除用户失败");
    }

    /**
     * 跨租户重置用户密码
     */
    @Operation(summary = "跨租户重置密码", description = "重置指定租户用户的密码")
    @PostMapping("/users/{userId}/reset-password")
    @CrossTenantAccess
    public Result<Void> resetCrossTenantUserPassword(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId,
            @Parameter(description = "租户ID", required = true) @RequestParam String tenantId,
            @Parameter(description = "新密码") @RequestParam(defaultValue = "123456") String newPassword) {
        boolean result = platformAdminService.resetCrossTenantUserPassword(userId, tenantId, newPassword);
        return result ? Result.success() : Result.fail("重置密码失败");
    }

    /**
     * 跨租户启用/禁用用户
     */
    @Operation(summary = "跨租户切换用户状态", description = "启用或禁用指定租户的用户")
    @PostMapping("/users/{userId}/toggle-status")
    @CrossTenantAccess
    public Result<Void> toggleCrossTenantUserStatus(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId,
            @Parameter(description = "租户ID", required = true) @RequestParam String tenantId,
            @Parameter(description = "是否启用", required = true) @RequestParam boolean enabled) {
        boolean result = platformAdminService.toggleCrossTenantUserStatus(userId, tenantId, enabled);
        return result ? Result.success() : Result.fail("切换用户状态失败");
    }

    // ========== 系统统计和监控 ==========
    /**
     * 获取系统统计信息
     */
    @Operation(summary = "系统统计信息", description = "获取系统整体统计信息")
    @GetMapping("/statistics/system")
    public Result<Map<String, Object>> getSystemStatistics() {
        Map<String, Object> statistics = platformAdminService.getSystemStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取租户统计信息
     */
    @Operation(summary = "租户统计信息", description = "获取所有租户的统计信息")
    @GetMapping("/statistics/tenants")
    public Result<List<Map<String, Object>>> getTenantStatistics() {
        List<Map<String, Object>> statistics = platformAdminService.getTenantStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取用户统计信息
     */
    @Operation(summary = "用户统计信息", description = "获取用户统计信息")
    @GetMapping("/statistics/users")
    public Result<Map<String, Object>> getUserStatistics(
            @Parameter(description = "租户ID，null表示所有租户") @RequestParam(required = false) String tenantId) {
        Map<String, Object> statistics = platformAdminService.getUserStatistics(tenantId);
        return Result.success(statistics);
    }

    /**
     * 获取系统性能监控信息
     */
    @Operation(summary = "系统性能监控", description = "获取系统性能监控信息")
    @GetMapping("/monitoring/performance")
    public Result<Map<String, Object>> getSystemPerformance() {
        Map<String, Object> performance = platformAdminService.getSystemPerformance();
        return Result.success(performance);
    }

    /**
     * 获取系统日志统计
     */
    @Operation(summary = "日志统计", description = "获取系统日志统计信息")
    @GetMapping("/monitoring/logs")
    public Result<Map<String, Object>> getLogStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> statistics = platformAdminService.getLogStatistics(days);
        return Result.success(statistics);
    }

    /**
     * 获取系统健康检查结果
     */
    @Operation(summary = "系统健康检查", description = "获取系统健康检查结果")
    @GetMapping("/monitoring/health")
    public Result<Map<String, Object>> getSystemHealthCheck() {
        Map<String, Object> health = platformAdminService.getSystemHealthCheck();
        return Result.success(health);
    }

    /**
     * 获取数据库状态
     */
    @Operation(summary = "数据库状态", description = "获取数据库连接状态")
    @GetMapping("/monitoring/database")
    public Result<Map<String, Object>> getDatabaseStatus() {
        Map<String, Object> status = platformAdminService.getDatabaseStatus();
        return Result.success(status);
    }

    /**
     * 获取缓存状态
     */
    @Operation(summary = "缓存状态", description = "获取缓存状态信息")
    @GetMapping("/monitoring/cache")
    public Result<Map<String, Object>> getCacheStatus() {
        Map<String, Object> status = platformAdminService.getCacheStatus();
        return Result.success(status);
    }

    // ========== 系统管理操作 ==========
    /**
     * 清理系统数据
     */
    @Operation(summary = "清理系统数据", description = "清理指定类型的系统数据")
    @PostMapping("/maintenance/cleanup")
    public Result<Map<String, Object>> cleanupSystemData(
            @Parameter(description = "数据类型", required = true) @RequestParam String dataType,
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> result = platformAdminService.cleanupSystemData(dataType, days);
        return Result.success(result);
    }

    /**
     * 执行系统维护任务
     */
    @Operation(summary = "执行维护任务", description = "执行指定的系统维护任务")
    @PostMapping("/maintenance/execute")
    public Result<Map<String, Object>> executeMaintenanceTask(
            @Parameter(description = "任务类型", required = true) @RequestParam String taskType) {
        Map<String, Object> result = platformAdminService.executeMaintenanceTask(taskType);
        return Result.success(result);
    }

    /**
     * 清理缓存
     */
    @Operation(summary = "清理缓存", description = "清理指定类型的缓存")
    @PostMapping("/maintenance/clear-cache")
    public Result<Void> clearCache(
            @Parameter(description = "缓存类型", required = true) @RequestParam String cacheType) {
        boolean result = platformAdminService.clearCache(cacheType);
        return result ? Result.success() : Result.fail("清理缓存失败");
    }

    // ========== 系统配置管理 ==========
    /**
     * 获取系统配置
     */
    @Operation(summary = "获取系统配置", description = "获取系统配置信息")
    @GetMapping("/config/system")
    public Result<Map<String, Object>> getSystemConfig() {
        Map<String, Object> config = platformAdminService.getSystemConfig();
        return Result.success(config);
    }

    /**
     * 更新系统配置
     */
    @Operation(summary = "更新系统配置", description = "更新系统配置信息")
    @PostMapping("/config/system")
    public Result<Void> updateSystemConfig(@RequestBody Map<String, Object> configs) {
        boolean result = platformAdminService.updateSystemConfig(configs);
        return result ? Result.success() : Result.fail("更新系统配置失败");
    }

    /**
     * 获取租户配置概览
     */
    @Operation(summary = "租户配置概览", description = "获取所有租户的配置概览")
    @GetMapping("/config/tenants")
    public Result<List<Map<String, Object>>> getTenantConfigOverview() {
        List<Map<String, Object>> overview = platformAdminService.getTenantConfigOverview();
        return Result.success(overview);
    }

    /**
     * 批量更新租户配置
     */
    @Operation(summary = "批量更新租户配置", description = "批量更新多个租户的配置")
    @PostMapping("/config/tenants/batch")
    public Result<Void> batchUpdateTenantConfigs(
            @RequestBody List<String> tenantIds,
            @RequestBody Map<String, String> configs) {
        boolean result = platformAdminService.batchUpdateTenantConfigs(tenantIds, configs);
        return result ? Result.success() : Result.fail("批量更新租户配置失败");
    }

    // ========== 数据导入导出 ==========
    /**
     * 导出系统数据
     */
    @Operation(summary = "导出系统数据", description = "导出指定类型的系统数据")
    @PostMapping("/data/export")
    public Result<String> exportSystemData(
            @Parameter(description = "数据类型", required = true) @RequestParam String dataType,
            @Parameter(description = "租户ID，null表示所有租户") @RequestParam(required = false) String tenantId) {
        String filePath = platformAdminService.exportSystemData(dataType, tenantId);
        return Result.success(filePath);
    }

    /**
     * 导入系统数据
     */
    @Operation(summary = "导入系统数据", description = "导入系统数据")
    @PostMapping("/data/import")
    public Result<Map<String, Object>> importSystemData(
            @Parameter(description = "数据类型", required = true) @RequestParam String dataType,
            @Parameter(description = "文件路径", required = true) @RequestParam String filePath,
            @Parameter(description = "目标租户ID") @RequestParam(required = false) String tenantId) {
        Map<String, Object> result = platformAdminService.importSystemData(dataType, filePath, tenantId);
        return Result.success(result);
    }

    // ========== 系统通知 ==========
    /**
     * 发送系统通知
     */
    @Operation(summary = "发送系统通知", description = "向指定租户或用户发送系统通知")
    @PostMapping("/notification/send")
    public Result<Void> sendSystemNotification(
            @Parameter(description = "通知标题", required = true) @RequestParam String title,
            @Parameter(description = "通知内容", required = true) @RequestParam String content,
            @Parameter(description = "目标租户ID列表") @RequestParam(required = false) List<String> tenantIds,
            @Parameter(description = "目标用户ID列表") @RequestParam(required = false) List<String> userIds) {
        boolean result = platformAdminService.sendSystemNotification(title, content, tenantIds, userIds);
        return result ? Result.success() : Result.fail("发送系统通知失败");
    }

    // ========== 系统信息 ==========
    /**
     * 获取系统版本信息
     */
    @Operation(summary = "系统版本信息", description = "获取系统版本信息")
    @GetMapping("/info/version")
    public Result<Map<String, Object>> getSystemVersion() {
        Map<String, Object> version = platformAdminService.getSystemVersion();
        return Result.success(version);
    }

    /**
     * 检查系统更新
     */
    @Operation(summary = "检查系统更新", description = "检查是否有系统更新")
    @GetMapping("/info/check-update")
    public Result<Map<String, Object>> checkSystemUpdate() {
        Map<String, Object> update = platformAdminService.checkSystemUpdate();
        return Result.success(update);
    }

    /**
     * 获取系统错误日志
     */
    @Operation(summary = "系统错误日志", description = "获取系统错误日志")
    @GetMapping("/logs/errors")
    public Result<List<Map<String, Object>>> getSystemErrorLogs(
            @Parameter(description = "最近小时数") @RequestParam(defaultValue = "24") Integer hours,
            @Parameter(description = "日志级别") @RequestParam(defaultValue = "ERROR") String level) {
        List<Map<String, Object>> logs = platformAdminService.getSystemErrorLogs(hours, level);
        return Result.success(logs);
    }
}
