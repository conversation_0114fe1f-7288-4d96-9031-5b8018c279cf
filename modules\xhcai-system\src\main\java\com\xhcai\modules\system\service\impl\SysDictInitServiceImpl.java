package com.xhcai.modules.system.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.modules.system.entity.SysDictData;
import com.xhcai.modules.system.service.ISysDictDataService;
import com.xhcai.modules.system.service.ISysDictInitService;
import com.xhcai.modules.system.service.ISysDictService;

/**
 * 字典初始化服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysDictInitServiceImpl implements ISysDictInitService {

    private static final Logger log = LoggerFactory.getLogger(SysDictInitServiceImpl.class);

    @Autowired
    private ISysDictService dictService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Transactional(rollbackFor = Exception.class)
    public void initSystemDicts() {
        log.info("开始初始化系统字典数据...");

        try {
            // 再初始化租户特定的字典数据（在租户上下文中）
            initTenantDictData();

            log.info("系统字典数据初始化完成");
        } catch (Exception e) {
            log.error("系统字典数据初始化失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    /**
     * 初始化租户特定的字典数据（需要在租户上下文中执行）
     */
    private void initTenantDictData() {
        log.info("开始初始化租户字典数据...");

        // 初始化各种字典数据
        initUserGenderDictData();
        initUserStatusDictData();
        initEnvDictData();
        initRoleStatusDictData();
        initDeptStatusDictData();
        initPermissionStatusDictData();
        initConfigStatusDictData();
        initDataScopeDictData();
        initYesNoDictData();

        log.info("租户字典数据初始化完成");
    }

    /**
     * 初始化用户性别字典数据
     */
    private void initUserGenderDictData() {
        String dictType = "sys_user_gender";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("用户性别字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "未知", "0", "default", "");
        createDictData(dictType, 2, "男", "1", "primary", "");
        createDictData(dictType, 3, "女", "2", "danger", "");

        log.info("用户性别字典数据初始化完成");
    }

    /**
     * 初始化用户状态字典数据
     */
    private void initUserStatusDictData() {
        String dictType = "sys_user_status";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("用户状态字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "正常", "0", "success", "Y");
        createDictData(dictType, 2, "停用", "1", "danger", "N");
        createDictData(dictType, 3, "锁定", "9", "warning", "N");

        log.info("用户状态字典数据初始化完成");
    }

    /**
     * 初始化运行环境字典数据
     */
    private void initEnvDictData() {
        String dictType = "sys_environment";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("运行环境字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "生产环境", "prod", "success", "Y");
        createDictData(dictType, 2, "测试环境", "uat", "danger", "N");
        createDictData(dictType, 3, "开发环境", "dev", "warning", "N");

        log.info("运行环境字典数据初始化完成");
    }

    /**
     * 初始化角色状态字典数据
     */
    private void initRoleStatusDictData() {
        String dictType = "sys_role_status";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("角色状态字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "正常", "0", "success", "Y");
        createDictData(dictType, 2, "停用", "1", "danger", "N");

        log.info("角色状态字典数据初始化完成");
    }

    /**
     * 初始化部门状态字典数据
     */
    private void initDeptStatusDictData() {
        String dictType = "sys_dept_status";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("部门状态字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "正常", "0", "success", "Y");
        createDictData(dictType, 2, "停用", "1", "danger", "N");

        log.info("部门状态字典数据初始化完成");
    }

    /**
     * 初始化权限状态字典数据
     */
    private void initPermissionStatusDictData() {
        String dictType = "sys_permission_status";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("权限状态字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "正常", "0", "success", "Y");
        createDictData(dictType, 2, "停用", "1", "danger", "N");

        log.info("权限状态字典数据初始化完成");
    }

    /**
     * 初始化配置状态字典数据
     */
    private void initConfigStatusDictData() {
        String dictType = "sys_config_status";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("配置状态字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "正常", "0", "success", "Y");
        createDictData(dictType, 2, "停用", "1", "danger", "N");

        log.info("配置状态字典数据初始化完成");
    }

    /**
     * 初始化数据范围字典数据
     */
    private void initDataScopeDictData() {
        String dictType = "sys_data_scope";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("数据范围字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "全部数据权限", "1", "primary", "N");
        createDictData(dictType, 2, "自定数据权限", "2", "info", "N");
        createDictData(dictType, 3, "部门数据权限", "3", "warning", "N");
        createDictData(dictType, 4, "部门及以下数据权限", "4", "success", "N");
        createDictData(dictType, 5, "仅本人数据权限", "5", "danger", "Y");

        log.info("数据范围字典数据初始化完成");
    }

    /**
     * 初始化是否字典数据
     */
    private void initYesNoDictData() {
        String dictType = "sys_yes_no";

        // 检查字典数据是否已存在
        if (dictDataService.selectByDictType(dictType) != null
                && !dictDataService.selectByDictType(dictType).isEmpty()) {
            log.debug("是否字典数据已存在，跳过初始化");
            return;
        }

        // 创建字典数据
        createDictData(dictType, 1, "是", "Y", "success", "N");
        createDictData(dictType, 2, "否", "N", "danger", "Y");

        log.info("是否字典数据初始化完成");
    }

    /**
     * 创建字典数据
     */
    private void createDictData(String dictType, int sort, String label, String value, String listClass, String isDefault) {
        SysDictData dictData = new SysDictData();
        dictData.setDictSort(sort);
        dictData.setDictLabel(label);
        dictData.setDictValue(value);
        dictData.setDictType(dictType);
        dictData.setListClass(listClass);
        dictData.setIsDefault(isDefault);
        dictData.setStatus(CommonConstants.STATUS_NORMAL);
        dictDataService.insertDictData(dictData);
    }
}
