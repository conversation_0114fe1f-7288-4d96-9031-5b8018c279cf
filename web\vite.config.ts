import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    // 设置基础路径，如果部署在子目录下需要修改
    // 例如：部署在 /app/ 子目录下，则设置为 '/app/'
    base: mode === 'production' ? '/' : '/',

    plugins: [
      vue({
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    optimizeDeps: {
      include: [
        'element-plus',
        '@element-plus/icons-vue'
      ]
    },
    build: {
      target: 'es2015',
      outDir: 'dist',
      sourcemap: mode !== 'production',
      // 提高 chunk 大小警告限制到 1000kb
      chunkSizeWarningLimit: 1000,
      // 生产环境优化
      minify: mode === 'production' ? 'terser' : 'esbuild',
      terserOptions: mode === 'production' ? {
        compress: {
          drop_console: true, // 移除 console.log
          drop_debugger: true, // 移除 debugger
          pure_funcs: ['console.log', 'console.info', 'console.debug'] // 移除指定函数调用
        }
      } : undefined,
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          // 手动分块配置 - 只包含确实存在的依赖
          manualChunks: {
            // Vue 核心库
            'vue-vendor': ['vue', 'vue-router', 'pinia'],

            // Element Plus UI 库
            'element-plus': ['element-plus', '@element-plus/icons-vue'],

            // 富文本编辑器
            'editor': [
              '@tiptap/vue-3',
              '@tiptap/starter-kit',
              '@tiptap/extension-code-block-lowlight',
              '@tiptap/extension-color',
              '@tiptap/extension-font-family',
              '@tiptap/extension-text-align',
              '@tiptap/extension-text-style'
            ],

            // Vue Flow 工作流相关
            'vue-flow': [
              '@vue-flow/core',
              '@vue-flow/background',
              '@vue-flow/controls',
              '@vue-flow/minimap',
              '@vue-flow/node-resizer'
            ],

            // 图表库
            'charts': ['chart.js']
          }
        }
      }
    },
    server: {
      port: 4000,
      host: '0.0.0.0',
      cors: true,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || '/',
          changeOrigin: true,
          secure: false
        }
      }
    }
  }
})
