<template>
  <div class="help-docs-management h-full">
    <div class="flex gap-6 h-full min-h-0">
      <!-- 左侧文档列表 -->
      <div class="w-1/6 bg-white rounded-lg shadow-sm p-4 flex flex-col min-h-0">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">帮助文档列表</h3>
          <button @click="showAddDocModal = true" class="btn-primary text-sm">
            <i class="fas fa-plus mr-2"></i>
            新增文档
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="mb-4">
          <input
            v-model="docSearchQuery"
            type="text"
            placeholder="搜索文档标题..."
            class="form-input text-sm"
          />
        </div>

        <!-- 文档列表 -->
        <div class="space-y-2 flex-1 overflow-y-auto min-h-0">
          <div
            v-for="doc in filteredHelpDocs"
            :key="doc.id"
            class="doc-item p-3 rounded-lg border border-blue-100 hover:border-blue-300 transition-colors cursor-pointer"
            :class="{ 'bg-blue-50 border-blue-400': selectedDocId === doc.id }"
            @click="selectDocument(doc)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <h4 class="font-medium text-gray-900 text-sm">{{ doc.title }}</h4>
                <p class="text-xs text-gray-500 mt-1">
                  {{ formatDate(doc.updatedAt) }}
                  <span v-if="doc.status === 'archived'" class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-600 rounded text-xs">已归档</span>
                </p>
              </div>
              <div class="relative">
                <button
                  @click.stop="toggleDocMenu(doc.id)"
                  class="p-1 text-gray-400 hover:text-gray-600 rounded"
                >
                  <i class="fas fa-ellipsis-v"></i>
                </button>
                <!-- 下拉菜单 -->
                <div
                  v-if="showDocMenu === doc.id"
                  class="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-24"
                >
                  <button
                    @click="editDocTitle(doc)"
                    class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-t-lg"
                  >
                    <i class="fas fa-edit mr-2"></i>编辑
                  </button>
                  <button
                    @click="duplicateDoc(doc)"
                    class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <i class="fas fa-copy mr-2"></i>复制
                  </button>
                  <button
                    @click="archiveDoc(doc)"
                    class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <i class="fas fa-archive mr-2"></i>归档
                  </button>
                  <button
                    @click="deleteDoc(doc.id)"
                    class="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-b-lg"
                  >
                    <i class="fas fa-trash mr-2"></i>删除
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredHelpDocs.length === 0" class="text-center py-8">
          <div class="text-gray-400 text-4xl mb-4">
            <i class="fas fa-file-alt"></i>
          </div>
          <p class="text-gray-500">暂无帮助文档</p>
          <button @click="showAddDocModal = true" class="btn-primary text-sm mt-4">
            创建第一个文档
          </button>
        </div>
      </div>

      <!-- 右侧编辑器 -->
      <div class="flex-1 bg-white rounded-lg shadow-sm flex flex-col min-h-0">
        <div v-if="selectedDoc" class="h-full flex flex-col min-h-0">
          <!-- 编辑器头部 -->
          <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">{{ selectedDoc.title }}</h3>
                <p class="text-sm text-gray-600 mt-1">
                  最后更新: {{ formatDateTime(selectedDoc.updatedAt) }}
                  <span v-if="hasUnsavedChanges" class="ml-2 text-orange-600">• 有未保存的更改</span>
                </p>
              </div>
              <div class="flex items-center gap-3">
                <button
                  @click="previewDoc"
                  class="btn-secondary text-sm"
                >
                  <i class="fas fa-eye mr-2"></i>预览
                </button>
                <button
                  @click="saveDocument"
                  class="btn-primary text-sm"
                  :disabled="!hasUnsavedChanges"
                >
                  <i class="fas fa-save mr-2"></i>保存
                </button>
              </div>
            </div>
          </div>

          <!-- 富文本编辑器 -->
          <div class="flex-1 p-6 min-h-0 flex flex-col">
            <RichTextEditor
              v-model="selectedDoc.content"
              @input="markAsChanged"
              @file-imported="handleDocumentImported"
              placeholder="开始编写帮助文档内容..."
              class="flex-1 min-h-0"
            />
          </div>
        </div>

        <!-- 未选择文档状态 -->
        <div v-else class="h-full flex items-center justify-center">
          <div class="text-center">
            <div class="text-gray-400 text-6xl mb-4">
              <i class="fas fa-file-alt"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">选择一个文档开始编辑</h3>
            <p class="text-gray-500 mb-6">从左侧列表中选择文档，或创建新的帮助文档</p>
            <button @click="showAddDocModal = true" class="btn-primary">
              <i class="fas fa-plus mr-2"></i>创建新文档
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑文档标题模态框 -->
    <div v-if="showAddDocModal || showEditDocModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">
            {{ showAddDocModal ? '新增帮助文档' : '编辑文档标题' }}
          </h3>
          <button @click="closeDocModal" class="text-gray-400 hover:text-gray-600">
            <span class="text-xl">×</span>
          </button>
        </div>

        <form @submit.prevent="saveDocTitle" class="space-y-4">
          <div class="form-group">
            <label class="form-label">文档标题</label>
            <input v-model="currentDocTitle" type="text" class="form-input" placeholder="输入文档标题" required />
          </div>
          <div v-if="showAddDocModal" class="form-group">
            <label class="form-label">文档分类</label>
            <select v-model="currentDocCategory" class="form-input">
              <option value="getting-started">快速入门</option>
              <option value="user-guide">用户指南</option>
              <option value="api-docs">API文档</option>
              <option value="troubleshooting">故障排除</option>
              <option value="faq">常见问题</option>
              <option value="other">其他</option>
            </select>
          </div>

          <div class="flex gap-3 pt-4">
            <button type="submit" class="btn-primary flex-1">保存</button>
            <button type="button" @click="closeDocModal" class="btn-secondary flex-1">取消</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 文档预览模态框 -->
    <div v-if="showPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg w-full max-w-4xl h-5/6 flex flex-col">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">文档预览 - {{ selectedDoc?.title }}</h3>
          <button @click="showPreviewModal = false" class="text-gray-400 hover:text-gray-600">
            <span class="text-xl">×</span>
          </button>
        </div>
        <div class="flex-1 overflow-y-auto p-6">
          <div class="prose max-w-none" v-html="selectedDoc?.content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import RichTextEditor from '@/components/RichTextEditor.vue'

// 帮助文档相关数据
const helpDocs = ref([
  {
    id: 1,
    title: '快速入门指南',
    content: '<h1>快速入门指南</h1><p>欢迎使用AI智能体平台！本指南将帮助您快速上手。</p><h2>第一步：登录系统</h2><p>使用您的账号和密码登录系统...</p>',
    category: 'getting-started',
    status: 'active',
    createdAt: '2024-01-15 10:00:00',
    updatedAt: '2024-01-20 14:30:00'
  },
  {
    id: 2,
    title: '智能体创建教程',
    content: '<h1>智能体创建教程</h1><p>本教程将详细介绍如何创建和配置智能体。</p><h2>创建步骤</h2><ol><li>进入智能体页面</li><li>点击创建按钮</li><li>填写基本信息</li></ol>',
    category: 'user-guide',
    status: 'active',
    createdAt: '2024-01-16 09:00:00',
    updatedAt: '2024-01-22 16:45:00'
  },
  {
    id: 3,
    title: 'API接口文档',
    content: '<h1>API接口文档</h1><p>本文档介绍平台提供的API接口。</p><h2>认证方式</h2><p>使用API Key进行认证...</p>',
    category: 'api-docs',
    status: 'active',
    createdAt: '2024-01-18 11:00:00',
    updatedAt: '2024-01-25 10:15:00'
  }
])

// 定义文档类型
interface HelpDoc {
  id: number
  title: string
  content: string
  category: string
  updatedAt: string
  createdAt: string
}

const selectedDocId = ref<number | null>(null)
const selectedDoc = ref<HelpDoc | null>(null)
const docSearchQuery = ref('')
const showDocMenu = ref<number | null>(null)
const hasUnsavedChanges = ref(false)

// 文档模态框状态
const showAddDocModal = ref(false)
const showEditDocModal = ref(false)
const showPreviewModal = ref(false)
const currentDocTitle = ref('')
const currentDocCategory = ref('getting-started')
const editingDocId = ref(null)

// 帮助文档计算属性
const filteredHelpDocs = computed(() => {
  let filtered = helpDocs.value.filter(doc => doc.status !== 'deleted')

  if (docSearchQuery.value) {
    const query = docSearchQuery.value.toLowerCase()
    filtered = filtered.filter(doc =>
      doc.title.toLowerCase().includes(query)
    )
  }

  return filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
})

// 帮助文档管理方法
const selectDocument = (doc: any) => {
  if (hasUnsavedChanges.value) {
    if (!confirm('当前文档有未保存的更改，确定要切换文档吗？')) {
      return
    }
  }

  selectedDocId.value = doc.id
  selectedDoc.value = { ...doc }
  hasUnsavedChanges.value = false
  showDocMenu.value = null
}

const toggleDocMenu = (docId: number) => {
  showDocMenu.value = showDocMenu.value === docId ? null : docId
}

const editDocTitle = (doc: any) => {
  currentDocTitle.value = doc.title
  editingDocId.value = doc.id
  showEditDocModal.value = true
  showDocMenu.value = null
}

const duplicateDoc = (doc: any) => {
  const newDoc = {
    id: Date.now(),
    title: `${doc.title} - 副本`,
    content: doc.content,
    category: doc.category,
    status: 'active',
    createdAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
    updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' ')
  }

  helpDocs.value.push(newDoc)
  showDocMenu.value = null
  alert('文档复制成功')
}

const archiveDoc = (doc: any) => {
  if (confirm('确定要归档这个文档吗？')) {
    const index = helpDocs.value.findIndex(d => d.id === doc.id)
    if (index !== -1) {
      helpDocs.value[index].status = 'archived'
      helpDocs.value[index].updatedAt = new Date().toISOString().slice(0, 19).replace('T', ' ')
    }
    showDocMenu.value = null
    alert('文档已归档')
  }
}

const deleteDoc = (docId: number) => {
  if (confirm('确定要删除这个文档吗？此操作不可恢复。')) {
    helpDocs.value = helpDocs.value.filter(doc => doc.id !== docId)

    if (selectedDocId.value === docId) {
      selectedDocId.value = null
      selectedDoc.value = null
      hasUnsavedChanges.value = false
    }

    showDocMenu.value = null
    alert('文档已删除')
  }
}

const closeDocModal = () => {
  showAddDocModal.value = false
  showEditDocModal.value = false
  currentDocTitle.value = ''
  currentDocCategory.value = 'getting-started'
  editingDocId.value = null
}

const saveDocTitle = () => {
  if (!currentDocTitle.value.trim()) {
    alert('请输入文档标题')
    return
  }

  if (showAddDocModal.value) {
    // 新增文档
    const newDoc = {
      id: Date.now(),
      title: currentDocTitle.value.trim(),
      content: '',
      category: currentDocCategory.value,
      status: 'active',
      createdAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' ')
    }

    helpDocs.value.push(newDoc)
    selectDocument(newDoc)
    alert('文档创建成功')
  } else {
    // 编辑标题
    const index = helpDocs.value.findIndex(doc => doc.id === editingDocId.value)
    if (index !== -1) {
      helpDocs.value[index].title = currentDocTitle.value.trim()
      helpDocs.value[index].updatedAt = new Date().toISOString().slice(0, 19).replace('T', ' ')

      if (selectedDoc.value && selectedDoc.value.id === editingDocId.value) {
        selectedDoc.value.title = currentDocTitle.value.trim()
      }
    }
    alert('标题修改成功')
  }

  closeDocModal()
}

const markAsChanged = () => {
  hasUnsavedChanges.value = true
}

const saveDocument = () => {
  if (!selectedDoc.value) return

  const currentDoc = selectedDoc.value
  const index = helpDocs.value.findIndex(doc => doc.id === currentDoc.id)
  if (index !== -1) {
    helpDocs.value[index].content = currentDoc.content
    helpDocs.value[index].updatedAt = new Date().toISOString().slice(0, 19).replace('T', ' ')
    hasUnsavedChanges.value = false
    alert('文档保存成功')
  }
}

const previewDoc = () => {
  if (!selectedDoc.value) return
  showPreviewModal.value = true
}

const handleDocumentImported = (data: { content: string, files: string[] }) => {
  console.log('文档导入成功:', data.files)
  markAsChanged()
  
  // 显示导入成功提示
  const notification = document.createElement('div')
  notification.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50'
  
  const fileList = data.files.length > 1
    ? `${data.files.length} 个文档`
    : `"${data.files[0]}"`
  
  notification.innerHTML = `
    <div class="flex items-center">
      <i class="fas fa-check-circle mr-2"></i>
      <span>${fileList} 导入成功</span>
    </div>
  `
  
  document.body.appendChild(notification)
  
  // 3秒后自动移除提示
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification)
    }
  }, 3000)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');

/* 响应式布局优化 */
@media (max-width: 1024px) {
  .help-docs-management .flex {
    flex-direction: column;
  }

  .help-docs-management .w-1\/6 {
    width: 100%;
    max-height: 300px;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .help-docs-management .w-1\/6 {
    padding: 1rem;
    max-height: 250px;
  }

  .help-docs-management .flex-1 {
    min-height: 400px;
  }
}

/* 左侧文档列表优化 */
.help-docs-management .w-1\/6 h3 {
  font-size: 1rem;
  margin-bottom: 1rem;
}

.help-docs-management .w-1\/6 .btn-primary {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
}

.help-docs-management .w-1\/6 .form-input {
  font-size: 0.75rem;
  padding: 0.5rem;
}

.help-docs-management .w-1\/6 .doc-item {
  padding: 0.75rem;
}

.help-docs-management .w-1\/6 .doc-item h4 {
  font-size: 0.75rem;
  line-height: 1.2;
}

.help-docs-management .w-1\/6 .doc-item p {
  font-size: 0.625rem;
  margin-top: 0.25rem;
}
</style>
