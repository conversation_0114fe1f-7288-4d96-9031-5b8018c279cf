package com.xhcai.modules.rag.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * AI模型创建DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "AI模型创建DTO")
public class AiModelCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基本信息 ==========
    /**
     * 模型名称
     */
    @Schema(description = "模型名称", example = "GPT-4", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模型名称不能为空")
    @Size(min = 1, max = 100, message = "模型名称长度必须在1-100个字符之间")
    private String name;

    /**
     * 模型标识
     */
    @Schema(description = "模型标识", example = "gpt-4-0613", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模型标识不能为空")
    @Size(min = 1, max = 100, message = "模型标识长度必须在1-100个字符之间")
    private String modelId;

    /**
     * 模型提供商
     */
    @Schema(description = "模型提供商", example = "OpenAI", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模型提供商不能为空")
    @Size(min = 1, max = 50, message = "模型提供商长度必须在1-50个字符之间")
    private String provider;

    /**
     * 模型类型
     */
    @Schema(description = "模型类型", example = "对话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模型类型不能为空")
    @Size(min = 1, max = 50, message = "模型类型长度必须在1-50个字符之间")
    private String type;

    /**
     * 推理平台
     */
    @Schema(description = "推理平台", example = "OpenAI")
    @Size(max = 50, message = "推理平台长度不能超过50个字符")
    private String platform;

    /**
     * 模型版本
     */
    @Schema(description = "模型版本", example = "v1.0")
    @Size(max = 50, message = "模型版本长度不能超过50个字符")
    private String version;

    /**
     * 模型描述
     */
    @Schema(description = "模型描述")
    @Size(max = 500, message = "模型描述长度不能超过500个字符")
    private String description;

    /**
     * 状态：0-停用，1-启用
     */
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status = "1";

    // ========== 接口配置 ==========
    /**
     * API端点
     */
    @Schema(description = "API端点", example = "https://api.openai.com/v1")
    @Size(max = 200, message = "API端点长度不能超过200个字符")
    private String apiEndpoint;

    /**
     * API密钥
     */
    @Schema(description = "API密钥")
    @Size(max = 500, message = "API密钥长度不能超过500个字符")
    private String apiKey;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID", example = "org-...")
    @Size(max = 100, message = "组织ID长度不能超过100个字符")
    private String organizationId;

    /**
     * 请求超时时间（秒）
     */
    @Schema(description = "请求超时时间（秒）", example = "30")
    @Min(value = 1, message = "超时时间必须大于0")
    @Max(value = 300, message = "超时时间不能超过300秒")
    private Integer timeout = 30;

    // ========== 模型参数 ==========
    /**
     * 最大Token数
     */
    @Schema(description = "最大Token数", example = "4096")
    @Min(value = 1, message = "最大Token数必须大于0")
    private Integer maxTokens = 4096;

    /**
     * 温度值
     */
    @Schema(description = "温度值", example = "0.7")
    @DecimalMin(value = "0.0", message = "温度值不能小于0")
    @DecimalMax(value = "2.0", message = "温度值不能大于2")
    private BigDecimal temperature = new BigDecimal("0.7");

    /**
     * Top P值
     */
    @Schema(description = "Top P值", example = "1.0")
    @DecimalMin(value = "0.0", message = "Top P值不能小于0")
    @DecimalMax(value = "1.0", message = "Top P值不能大于1")
    private BigDecimal topP = new BigDecimal("1.0");

    /**
     * 频率惩罚
     */
    @Schema(description = "频率惩罚", example = "0.0")
    @DecimalMin(value = "-2.0", message = "频率惩罚不能小于-2")
    @DecimalMax(value = "2.0", message = "频率惩罚不能大于2")
    private BigDecimal frequencyPenalty = new BigDecimal("0.0");

    /**
     * 存在惩罚
     */
    @Schema(description = "存在惩罚", example = "0.0")
    @DecimalMin(value = "-2.0", message = "存在惩罚不能小于-2")
    @DecimalMax(value = "2.0", message = "存在惩罚不能大于2")
    private BigDecimal presencePenalty = new BigDecimal("0.0");

    /**
     * 停止序列
     */
    @Schema(description = "停止序列", example = "\\n,\\n\\n")
    @Size(max = 200, message = "停止序列长度不能超过200个字符")
    private String stopSequences;

    // ========== 费用配置 ==========
    /**
     * 输入价格（$/1K tokens）
     */
    @Schema(description = "输入价格（$/1K tokens）", example = "0.03")
    @DecimalMin(value = "0.0", message = "输入价格不能小于0")
    private BigDecimal inputPrice = new BigDecimal("0.0");

    /**
     * 输出价格（$/1K tokens）
     */
    @Schema(description = "输出价格（$/1K tokens）", example = "0.06")
    @DecimalMin(value = "0.0", message = "输出价格不能小于0")
    private BigDecimal outputPrice = new BigDecimal("0.0");

    /**
     * 每分钟请求限制
     */
    @Schema(description = "每分钟请求限制", example = "3500")
    @Min(value = 1, message = "每分钟请求限制必须大于0")
    private Integer rpmLimit = 3500;

    /**
     * 每分钟Token限制
     */
    @Schema(description = "每分钟Token限制", example = "90000")
    @Min(value = 1, message = "每分钟Token限制必须大于0")
    private Integer tpmLimit = 90000;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    // ========== Getter and Setter ==========
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApiEndpoint() {
        return apiEndpoint;
    }

    public void setApiEndpoint(String apiEndpoint) {
        this.apiEndpoint = apiEndpoint;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public BigDecimal getTopP() {
        return topP;
    }

    public void setTopP(BigDecimal topP) {
        this.topP = topP;
    }

    public BigDecimal getFrequencyPenalty() {
        return frequencyPenalty;
    }

    public void setFrequencyPenalty(BigDecimal frequencyPenalty) {
        this.frequencyPenalty = frequencyPenalty;
    }

    public BigDecimal getPresencePenalty() {
        return presencePenalty;
    }

    public void setPresencePenalty(BigDecimal presencePenalty) {
        this.presencePenalty = presencePenalty;
    }

    public String getStopSequences() {
        return stopSequences;
    }

    public void setStopSequences(String stopSequences) {
        this.stopSequences = stopSequences;
    }

    public BigDecimal getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(BigDecimal inputPrice) {
        this.inputPrice = inputPrice;
    }

    public BigDecimal getOutputPrice() {
        return outputPrice;
    }

    public void setOutputPrice(BigDecimal outputPrice) {
        this.outputPrice = outputPrice;
    }

    public Integer getRpmLimit() {
        return rpmLimit;
    }

    public void setRpmLimit(Integer rpmLimit) {
        this.rpmLimit = rpmLimit;
    }

    public Integer getTpmLimit() {
        return tpmLimit;
    }

    public void setTpmLimit(Integer tpmLimit) {
        this.tpmLimit = tpmLimit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "AiModelCreateDTO{"
                + "name='" + name + '\''
                + ", modelId='" + modelId + '\''
                + ", provider='" + provider + '\''
                + ", type='" + type + '\''
                + ", version='" + version + '\''
                + ", description='" + description + '\''
                + ", status='" + status + '\''
                + ", apiEndpoint='" + apiEndpoint + '\''
                + ", organizationId='" + organizationId + '\''
                + ", timeout=" + timeout
                + ", maxTokens=" + maxTokens
                + ", temperature=" + temperature
                + ", topP=" + topP
                + ", frequencyPenalty=" + frequencyPenalty
                + ", presencePenalty=" + presencePenalty
                + ", stopSequences='" + stopSequences + '\''
                + ", inputPrice=" + inputPrice
                + ", outputPrice=" + outputPrice
                + ", rpmLimit=" + rpmLimit
                + ", tpmLimit=" + tpmLimit
                + ", remark='" + remark + '\''
                + '}';
    }
}
