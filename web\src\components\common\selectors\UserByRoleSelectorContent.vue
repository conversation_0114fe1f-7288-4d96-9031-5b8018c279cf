<template>
  <div class="user-by-role-selector-content">
    <!-- 选择器头部 -->
    <div v-if="showHeader" class="selector-header flex items-center justify-between mb-3">
      <div class="flex items-center gap-2">
        <span class="text-sm font-medium text-gray-700">按角色选择用户</span>
        <span v-if="hasSelection" class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
          已选择 {{ selectedCount }} 个用户
        </span>
      </div>
      <div class="flex items-center gap-2">
        <el-button 
          v-if="config.multiple" 
          @click="selectAllUsers" 
          size="small" 
          type="primary" 
          plain
          :disabled="loading || !currentRoleUsers.length"
        >
          全选当前角色
        </el-button>
        <el-button 
          @click="clearSelection" 
          size="small" 
          :disabled="!hasSelection"
        >
          清空
        </el-button>
      </div>
    </div>

    <div class="selector-content grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- 左侧：角色列表 -->
      <div class="role-panel">
        <div class="panel-header flex items-center justify-between mb-2">
          <h4 class="text-sm font-medium text-gray-700">选择角色</h4>
          <span v-if="selectedRole" class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
            {{ selectedRole.label }}
          </span>
        </div>
        
        <!-- 角色搜索 -->
        <div class="search-box mb-3">
          <el-input
            v-model="roleFilterText"
            placeholder="搜索角色名称、编码..."
            :prefix-icon="Search"
            clearable
            size="small"
            :disabled="roleLoading"
          />
        </div>

        <!-- 角色列表 -->
        <div class="role-list border border-gray-200 rounded-lg bg-white min-h-[300px] max-h-[400px] overflow-auto">
          <div v-if="roleLoading" class="loading-state text-center py-8">
            <div class="loading-spinner">
              <i class="el-icon-loading animate-spin"></i>
            </div>
            <p class="text-gray-500 text-sm mt-2">加载角色中...</p>
          </div>

          <div v-else-if="!filteredRoles.length" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-user-solid text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">
              {{ roleFilterText ? '未找到匹配的角色' : '暂无角色数据' }}
            </p>
          </div>

          <div v-else class="role-items p-2">
            <div
              v-for="role in filteredRoles"
              :key="role.value"
              class="role-item flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
              :class="{
                'bg-blue-50 border-blue-200': selectedRoleId === role.value,
                'opacity-50 cursor-not-allowed': role.disabled
              }"
              @click="handleRoleClick(role)"
            >
              <div class="role-icon mr-3">
                <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                  <i class="el-icon-user-solid text-white text-sm"></i>
                </div>
              </div>
              
              <div class="role-info flex-1 min-w-0">
                <div class="role-name text-sm font-medium text-gray-900 truncate">
                  {{ role.label }}
                </div>
                <div class="role-details text-xs text-gray-500 truncate">
                  {{ role.roleCode }}
                  <span v-if="role.userCount !== undefined" class="ml-2">
                    {{ role.userCount }} 人
                  </span>
                </div>
              </div>
              
              <div class="role-status">
                <el-tag
                  :type="role.status === '0' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ role.status === '0' ? '正常' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：用户列表 -->
      <div class="user-panel">
        <div class="panel-header flex items-center justify-between mb-2">
          <h4 class="text-sm font-medium text-gray-700">
            选择用户
            <span v-if="selectedRole" class="text-xs text-gray-500">
              ({{ selectedRole.label }})
            </span>
          </h4>
          <span v-if="currentRoleUsers.length" class="text-xs text-gray-500">
            共 {{ currentRoleUsers.length }} 人
          </span>
        </div>

        <!-- 用户搜索 -->
        <div class="search-box mb-3">
          <el-input
            v-model="userFilterText"
            placeholder="搜索用户姓名、用户名..."
            :prefix-icon="Search"
            clearable
            size="small"
            :disabled="!selectedRoleId || userLoading"
          />
        </div>

        <!-- 用户列表 -->
        <div class="user-list border border-gray-200 rounded-lg bg-white min-h-[300px] max-h-[400px] overflow-auto">
          <div v-if="!selectedRoleId" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-user text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">请先选择角色</p>
          </div>

          <div v-else-if="userLoading" class="loading-state text-center py-8">
            <div class="loading-spinner">
              <i class="el-icon-loading animate-spin"></i>
            </div>
            <p class="text-gray-500 text-sm mt-2">加载用户中...</p>
          </div>

          <div v-else-if="!filteredUsers.length" class="empty-state text-center py-8">
            <div class="text-gray-400 mb-2">
              <i class="el-icon-user text-4xl"></i>
            </div>
            <p class="text-gray-500 text-sm">
              {{ userFilterText ? '未找到匹配的用户' : '该角色暂无用户' }}
            </p>
          </div>

          <div v-else class="user-items p-2">
            <div
              v-for="user in filteredUsers"
              :key="user.value"
              class="user-item flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
              :class="{
                'bg-blue-50 border-blue-200': isUserSelected(user.value),
                'opacity-50 cursor-not-allowed': user.disabled
              }"
              @click="handleUserClick(user)"
            >
              <el-checkbox
                v-if="config.multiple"
                :model-value="isUserSelected(user.value)"
                :disabled="user.disabled"
                @change="(checked) => handleUserCheck(user, !!checked)"
                class="mr-3"
              />
              
              <div class="user-avatar mr-3">
                <el-avatar :size="32" :src="user.avatar">
                  <span class="text-sm">{{ user.nickname?.[0] || user.username[0] }}</span>
                </el-avatar>
              </div>
              
              <div class="user-info flex-1 min-w-0">
                <div class="user-name text-sm font-medium text-gray-900 truncate">
                  {{ user.nickname || user.username }}
                </div>
                <div class="user-details text-xs text-gray-500 truncate">
                  {{ user.username }}
                  <span v-if="user.deptName" class="ml-2">{{ user.deptName }}</span>
                </div>
              </div>
              
              <div class="user-status">
                <el-tag
                  :type="user.status === '0' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ user.status === '0' ? '正常' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 已选择的用户标签 -->
    <div v-if="hasSelection && config.multiple && showSelectedTags" class="selected-tags mt-4">
      <div class="text-xs text-gray-600 mb-2">已选择的用户:</div>
      <div class="flex flex-wrap gap-1">
        <el-tag
          v-for="user in getSelectedUserOptions()"
          :key="user.value"
          :closable="!config.disabled"
          size="small"
          @close="removeUserSelection(user.value)"
        >
          <div class="flex items-center">
            <el-avatar :size="16" :src="user.avatar" class="mr-1">
              <span class="text-xs">{{ user.nickname?.[0] || user.username[0] }}</span>
            </el-avatar>
            {{ user.nickname || user.username }}
          </div>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElInput, ElButton, ElTag, ElCheckbox, ElAvatar } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { UserAPI, RoleAPI } from '@/api/system'
import type { UserSelectorOption, RoleSelectorOption, SelectorConfig } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  onlyEnabled?: boolean
  excludeUserIds?: string[]
  showHeader?: boolean
  showSelectedTags?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], options: UserSelectorOption[]): void
  (e: 'select', value: string, option: UserSelectorOption): void
  (e: 'remove', value: string): void
  (e: 'roleChange', roleId: string, role: RoleSelectorOption): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  excludeUserIds: () => [],
  showHeader: false,
  showSelectedTags: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const selectedRoleId = ref<string>('')
const selectedRole = ref<RoleSelectorOption | null>(null)
const selectedUserIds = ref<string | string[]>(props.modelValue || (props.config.multiple ? [] : ''))
const allRoles = ref<RoleSelectorOption[]>([])
const currentRoleUsers = ref<UserSelectorOption[]>([])
const roleFilterText = ref('')
const userFilterText = ref('')
const roleLoading = ref(false)
const userLoading = ref(false)

// 配置
const defaultConfig: SelectorConfig = {
  multiple: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择用户',
  size: 'default',
  disabled: false
}

const config = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 计算属性
const filteredRoles = computed(() => {
  if (!roleFilterText.value) return allRoles.value
  
  const filterText = roleFilterText.value.toLowerCase()
  return allRoles.value.filter(role => 
    role.label.toLowerCase().includes(filterText) ||
    role.roleCode.toLowerCase().includes(filterText)
  )
})

const filteredUsers = computed(() => {
  if (!userFilterText.value) return currentRoleUsers.value
  
  const filterText = userFilterText.value.toLowerCase()
  return currentRoleUsers.value.filter(user => 
    user.label.toLowerCase().includes(filterText) ||
    user.username.toLowerCase().includes(filterText) ||
    (user.email && user.email.toLowerCase().includes(filterText))
  )
})

const hasSelection = computed(() => {
  if (config.value.multiple) {
    return Array.isArray(selectedUserIds.value) && selectedUserIds.value.length > 0
  }
  return selectedUserIds.value !== '' && selectedUserIds.value !== null && selectedUserIds.value !== undefined
})

const selectedCount = computed(() => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.length
  }
  return hasSelection.value ? 1 : 0
})

const loading = computed(() => roleLoading.value || userLoading.value)

// 方法
const loadRoles = async () => {
  try {
    roleLoading.value = true
    const response = await RoleAPI.getRoleList({
      status: props.onlyEnabled ? '0' : undefined
    })
    
    const roles = response.data || []
    allRoles.value = roles.map(role => ({
      value: role.id,
      label: role.roleName,
      roleCode: role.roleCode,
      status: role.status,
      userCount: role.userCount,
      disabled: role.status !== '0'
    }))
  } catch (error) {
    console.error('加载角色失败:', error)
    allRoles.value = []
  } finally {
    roleLoading.value = false
  }
}

const loadRoleUsers = async (roleId: string) => {
  if (!roleId) {
    currentRoleUsers.value = []
    return
  }

  try {
    userLoading.value = true
    // 这里需要根据角色ID查询用户，暂时使用所有用户
    const response = await UserAPI.getUserPage({
      status: props.onlyEnabled ? '0' : undefined,
      current: 1,
      size: 100
    })
    
    const users = response.data?.records || []
    currentRoleUsers.value = users
      .filter(user => !props.excludeUserIds.includes(user.id))
      .map(user => ({
        value: user.id,
        label: user.nickname || user.username,
        disabled: user.status !== '0',
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        deptId: user.deptId,
        deptName: user.deptName,
        status: user.status,
        roles: user.roles
      }))
  } catch (error) {
    console.error('加载角色用户失败:', error)
    currentRoleUsers.value = []
  } finally {
    userLoading.value = false
  }
}

const isUserSelected = (userId: string): boolean => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    return selectedUserIds.value.includes(userId)
  }
  return selectedUserIds.value === userId
}

const handleRoleClick = (role: RoleSelectorOption) => {
  if (role.disabled) return
  
  selectedRoleId.value = role.value
  selectedRole.value = role
  loadRoleUsers(role.value)
  emit('roleChange', role.value, role)
}

const handleUserClick = (user: UserSelectorOption) => {
  if (user.disabled) return
  
  if (config.value.multiple) {
    handleUserCheck(user, !isUserSelected(user.value))
  } else {
    selectedUserIds.value = user.value
    emit('update:modelValue', user.value)
    emit('change', user.value, [user])
    emit('select', user.value, user)
  }
}

const handleUserCheck = (user: UserSelectorOption, checked: boolean) => {
  if (!config.value.multiple || user.disabled) return
  
  let newValues: string[]
  if (Array.isArray(selectedUserIds.value)) {
    newValues = [...selectedUserIds.value]
  } else {
    newValues = []
  }
  
  if (checked) {
    if (!newValues.includes(user.value)) {
      newValues.push(user.value)
    }
  } else {
    const index = newValues.indexOf(user.value)
    if (index > -1) {
      newValues.splice(index, 1)
    }
  }
  
  selectedUserIds.value = newValues
  emit('update:modelValue', newValues)
  
  const selectedOptions = getSelectedUserOptions()
  emit('change', newValues, selectedOptions)
  
  if (checked) {
    emit('select', user.value, user)
  } else {
    emit('remove', user.value)
  }
}

const selectAllUsers = () => {
  if (!config.value.multiple) return
  
  const enabledUsers = filteredUsers.value.filter(user => !user.disabled)
  const allUserIds = enabledUsers.map(user => user.value)
  
  selectedUserIds.value = allUserIds
  emit('update:modelValue', allUserIds)
  emit('change', allUserIds, enabledUsers)
}

const clearSelection = () => {
  selectedUserIds.value = config.value.multiple ? [] : ''
  emit('update:modelValue', selectedUserIds.value)
  emit('change', selectedUserIds.value, [])
}

const removeUserSelection = (userId: string) => {
  if (config.value.multiple && Array.isArray(selectedUserIds.value)) {
    const newValues = selectedUserIds.value.filter(id => id !== userId)
    selectedUserIds.value = newValues
    emit('update:modelValue', newValues)
    
    const selectedOptions = getSelectedUserOptions()
    emit('change', newValues, selectedOptions)
    emit('remove', userId)
  }
}

const getSelectedUserOptions = (): UserSelectorOption[] => {
  const selectedIds = config.value.multiple && Array.isArray(selectedUserIds.value) 
    ? selectedUserIds.value 
    : selectedUserIds.value ? [selectedUserIds.value as string] : []
  
  return currentRoleUsers.value.filter(user => selectedIds.includes(user.value))
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedUserIds.value = newValue || (config.value.multiple ? [] : '')
}, { immediate: true })

// 组件挂载
onMounted(async () => {
  selectedUserIds.value = props.modelValue || (config.value.multiple ? [] : '')
  await loadRoles()
})

// 暴露方法
defineExpose({
  clearSelection,
  selectAllUsers,
  getSelectedUserOptions,
  refresh: () => {
    loadRoles()
    if (selectedRoleId.value) {
      loadRoleUsers(selectedRoleId.value)
    }
  }
})
</script>

<style scoped>
.user-by-role-selector-content {
  width: 100%;
}

.panel-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.role-item,
.user-item {
  border: 1px solid transparent;
}

.role-item:hover,
.user-item:hover {
  border-color: #e5e7eb;
}

.role-item.selected,
.user-item.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.selected-tags {
  max-height: 120px;
  overflow-y: auto;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  font-size: 24px;
  color: #409eff;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
