package com.xhcai.modules.ai.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.enums.ResultCode;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.common.datasource.plugin.TenantContextHolder;
import com.xhcai.modules.agent.service.IChatService;
import com.xhcai.modules.ai.dto.ConversationInfo;
import com.xhcai.modules.ai.dto.DifyStreamMetadata;
import org.springframework.util.StringUtils;
import com.xhcai.modules.ai.dto.AiChatQueryDTO;
import com.xhcai.modules.ai.dto.AiChatRequest;
import com.xhcai.modules.ai.dto.AiChatRequestDTO;
import com.xhcai.modules.ai.entity.AiChatRecord;
import com.xhcai.modules.ai.dto.FileUploadResponseDTO;
import com.xhcai.modules.ai.entity.AiFileRecord;
import com.xhcai.modules.ai.mapper.AiFileRecordMapper;
import com.xhcai.modules.ai.mapper.AiChatRecordMapper;
import com.xhcai.modules.ai.service.IAiChatService;
import com.xhcai.modules.ai.service.IConversationIntegrationService;
import com.xhcai.modules.ai.vo.AiChatRecordVO;
import com.xhcai.modules.ai.vo.AiChatResponseVO;
import com.xhcai.modules.dify.dto.chat.DifyChatRequestDTO;
import com.xhcai.modules.dify.dto.chat.DifyChatResponseDTO;
import com.xhcai.modules.dify.dto.chat.DifyStopWorkflowResponseDTO;

import com.xhcai.modules.dify.service.IDifyChatService;
import com.xhcai.modules.dify.service.IDifyAppService;
import com.xhcai.modules.dify.config.DifyWebClientConfig;

import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationDTO;
import com.xhcai.modules.dify.dto.file.DifyFileUploadResponseDTO;
import com.xhcai.modules.dify.dto.file.DifyRemoteFileUploadResponseDTO;
import com.xhcai.modules.rag.plugins.minio.MinioStorageService;

import com.xhcai.modules.dify.dto.conversation.DifyMessageDTO;
import com.xhcai.modules.dify.dto.conversation.DifyMessageListResponseDTO;
import com.xhcai.modules.dify.dto.conversation.DifyConversationNameResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


import java.io.ByteArrayInputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * AI聊天服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class AiChatServiceImpl extends ServiceImpl<AiChatRecordMapper, AiChatRecord> implements IAiChatService {

    private static final Logger log = LoggerFactory.getLogger(AiChatServiceImpl.class);

    @Autowired
    private AiChatRecordMapper chatRecordMapper;

    @Autowired(required = false)
    private ChatClient chatClient;

    @Autowired(required = false)
    private IDifyChatService difyChatService;



    @Autowired(required = false)
    private DifyWebClientConfig difyWebClientConfig;

    @Autowired
    IChatService iChatService;

//    @Autowired(required = false)
//    private DifyConfig difyConfig;

    @Autowired
    private IConversationIntegrationService conversationIntegrationService;

    @Autowired(required = false)
    private IDifyAppService difyAppService;

    @Autowired
    private AiFileRecordMapper aiFileRecordMapper;

    @Autowired(required = false)
    private MinioStorageService minioStorageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiChatResponseVO chat(AiChatRequestDTO requestDTO) {
        log.info("处理聊天请求: sessionId={}, message={}", requestDTO.getSessionId(), requestDTO.getMessage());

        // 优先使用 Dify 服务
        if (difyChatService != null) {
            return chatWithDify(requestDTO);
        }

        // 回退到原有的实现
        return chatWithSpringAI(requestDTO);
    }

    /**
     * 使用 Dify 进行聊天
     */
    private AiChatResponseVO chatWithDify(AiChatRequestDTO requestDTO) {
        log.info("使用Dify进行聊天: sessionId={}, message={}", requestDTO.getSessionId(), requestDTO.getMessage());

        long startTime = System.currentTimeMillis();

        // 创建聊天记录
        AiChatRecord chatRecord = new AiChatRecord();
        chatRecord.setSessionId(requestDTO.getSessionId());
        chatRecord.setUserId(SecurityUtils.getCurrentUserId());
        chatRecord.setUserMessage(requestDTO.getMessage());
        chatRecord.setMessageType(StringUtils.hasText(requestDTO.getMessageType()) ? requestDTO.getMessageType() : "text");
        chatRecord.setModelName("dify");
        chatRecord.setStatus("0"); // 处理中
        chatRecord.setCreateTime(LocalDateTime.now());

        try {
            // 构建 Dify 请求
            DifyChatRequestDTO difyChatRequest = new DifyChatRequestDTO();
            difyChatRequest.setQuery(requestDTO.getMessage());
            difyChatRequest.setUser(SecurityUtils.getCurrentUserId());
            difyChatRequest.setConversationId(requestDTO.getSessionId());
            difyChatRequest.setResponseMode("blocking");

            // 调用 Dify 聊天服务
            DifyChatResponseDTO difyChatResponse = difyChatService.chat(difyChatRequest);

            // 更新记录
            chatRecord.setAiResponse(difyChatResponse.getAnswer());
            chatRecord.setStatus("1"); // 成功
            chatRecord.setCostTime(System.currentTimeMillis() - startTime);

            // 保存记录
            save(chatRecord);

            // 构建响应
            AiChatResponseVO response = new AiChatResponseVO();
            BeanUtils.copyProperties(chatRecord, response);
            response.setRecordId(chatRecord.getId());

            return response;

        } catch (Exception e) {
            log.error("Dify聊天处理失败", e);

            // 更新错误信息
            chatRecord.setStatus("0"); // 失败
            chatRecord.setErrorMsg(e.getMessage());
            chatRecord.setCostTime(System.currentTimeMillis() - startTime);

            // 保存记录
            save(chatRecord);

            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify聊天处理失败: " + e.getMessage());
        }
    }

    /**
     * 使用 Spring AI 进行聊天（回退方案）
     */
    private AiChatResponseVO chatWithSpringAI(AiChatRequestDTO requestDTO) {
        log.info("使用Spring AI进行聊天: sessionId={}, message={}", requestDTO.getSessionId(), requestDTO.getMessage());

        long startTime = System.currentTimeMillis();

        // 创建聊天记录
        AiChatRecord chatRecord = new AiChatRecord();
        chatRecord.setSessionId(requestDTO.getSessionId());
        chatRecord.setUserId(SecurityUtils.getCurrentUserId());
        chatRecord.setUserMessage(requestDTO.getMessage());
        chatRecord.setMessageType(StringUtils.hasText(requestDTO.getMessageType()) ? requestDTO.getMessageType() : "text");
        chatRecord.setModelName(StringUtils.hasText(requestDTO.getModelName()) ? requestDTO.getModelName() : "gpt-3.5-turbo");
        chatRecord.setStatus("0"); // 处理中
        chatRecord.setCreateTime(LocalDateTime.now());

        try {
            // 调用AI服务
            String aiResponse = callAiService(requestDTO.getMessage(), requestDTO.getModelName());

            // 更新记录
            chatRecord.setAiResponse(aiResponse);
            chatRecord.setStatus("1"); // 成功
            chatRecord.setCostTime(System.currentTimeMillis() - startTime);

            // 保存记录
            save(chatRecord);

            // 构建响应
            AiChatResponseVO response = new AiChatResponseVO();
            BeanUtils.copyProperties(chatRecord, response);
            response.setRecordId(chatRecord.getId());

            return response;

        } catch (Exception e) {
            log.error("AI聊天处理失败", e);

            // 更新错误信息
            chatRecord.setStatus("0"); // 失败
            chatRecord.setErrorMsg(e.getMessage());
            chatRecord.setCostTime(System.currentTimeMillis() - startTime);

            // 保存记录
            save(chatRecord);

            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "AI聊天处理失败: " + e.getMessage());
        }
    }

    @Override
    public SseEmitter streamChat(AiChatRequestDTO requestDTO) {
        log.info("处理流式聊天请求: sessionId={}", requestDTO.getSessionId());

        // 优先尝试使用 Dify 服务
        if (difyChatService != null) {
            try {
                return streamChatWithDify(requestDTO);
            } catch (Exception e) {
                log.warn("Dify流式聊天失败，回退到Spring AI: {}", e.getMessage());
            }
        }

        // 最后回退到 Spring AI
        return streamChatWithSpringAI(requestDTO);
    }



    /**
     * 使用 Dify 进行流式聊天（标准方式）
     */
    private SseEmitter streamChatWithDify(AiChatRequestDTO requestDTO) {
        log.info("使用Dify进行流式聊天: sessionId={}", requestDTO.getSessionId());

        long startTime = System.currentTimeMillis();

        try {
            // 创建或获取Agent会话
            ConversationInfo conversationInfo = new ConversationInfo();
            conversationInfo.setId(requestDTO.getSessionId());
            String conversationId = conversationIntegrationService.createOrGetAgentConversation(conversationInfo);


            // 构建 Dify 请求
            DifyChatRequestDTO difyChatRequest = new DifyChatRequestDTO();
            difyChatRequest.setQuery(requestDTO.getMessage());
            difyChatRequest.setUser(SecurityUtils.getCurrentUserId());
            difyChatRequest.setConversationId(requestDTO.getSessionId());
            difyChatRequest.setResponseMode("streaming");

            // 调用 Dify 流式聊天服务
            SseEmitter emitter = difyChatService.streamChatSse(difyChatRequest);

            // 用于收集完整的AI回复内容和元数据
            StringBuilder aiResponseBuilder = new StringBuilder();
            DifyStreamMetadata streamMetadata = new DifyStreamMetadata();

            // 创建一个包装的SseEmitter来拦截数据并收集AI回复
            SseEmitter wrappedEmitter = new SseEmitter(emitter.getTimeout());

            // 拦截原始emitter的数据发送
            emitter.onCompletion(() -> {
                try {
                    long costTime = System.currentTimeMillis() - startTime;
                    String aiResponse = aiResponseBuilder.toString();
                    updateChatMessageWithDifyResponse( aiResponse, costTime, null, streamMetadata);
                    log.info("Dify流式聊天完成: sessionId={}, conversationId={}", requestDTO.getSessionId(), conversationId);
                    wrappedEmitter.complete();
                } catch (Exception e) {
                    log.error("更新AI回复消息失败", e);
                    wrappedEmitter.completeWithError(e);
                }
            });

            emitter.onError((error) -> {
                try {
                    long costTime = System.currentTimeMillis() - startTime;
                    updateChatMessageWithDifyResponse("处理失败", costTime, error.getMessage(), streamMetadata);
                    log.error("Dify流式聊天失败: sessionId={}, conversationId={}", requestDTO.getSessionId(), conversationId, error);
                    wrappedEmitter.completeWithError(error);
                } catch (Exception e) {
                    log.error("更新错误消息失败", e);
                    wrappedEmitter.completeWithError(error);
                }
            });

            // 注意：这里需要实际的流式数据拦截逻辑，但由于SseEmitter的限制，
            // 我们可能需要在DifyChatService中添加回调机制来收集AI回复内容

            return wrappedEmitter;

        } catch (Exception e) {
            log.error("Dify流式聊天处理失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify流式聊天处理失败: " + e.getMessage());
        }
    }

    /**
     * 使用 Spring AI 进行流式聊天（回退方案）
     */
    private SseEmitter streamChatWithSpringAI(AiChatRequestDTO requestDTO) {
        log.info("使用Spring AI进行流式聊天: sessionId={}", requestDTO.getSessionId());

        long startTime = System.currentTimeMillis();
        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        try {
            // 创建或获取Agent会话
            ConversationInfo conversationInfo = new ConversationInfo();
            conversationInfo.setId(requestDTO.getSessionId());
            String conversationId = conversationIntegrationService.createOrGetAgentConversation(conversationInfo);

            // 创建聊天记录，先保存用户提问
            String messageId = createChatMessageRecord(conversationId, requestDTO);

            // 获取当前租户ID，用于异步线程
            String currentTenantId = SecurityUtils.getCurrentTenantId();

            CompletableFuture.runAsync(() -> {
                try {
                    // 模拟流式响应
                    String response = callAiService(requestDTO.getMessage(), requestDTO.getModelName());

                    // 分段发送响应
                    String[] words = response.split(" ");
                    StringBuilder fullResponse = new StringBuilder();
                    for (String word : words) {
                        emitter.send(word + " ");
                        fullResponse.append(word).append(" ");
                        Thread.sleep(100); // 模拟延迟
                    }

                    // 更新聊天记录的AI回复内容
                    long costTime = System.currentTimeMillis() - startTime;
                    updateChatMessageWithResponse(messageId, fullResponse.toString().trim(), costTime, null);

                    emitter.complete();

                } catch (Exception e) {
                    log.error("流式聊天处理失败", e);
                    // 更新聊天记录的错误信息
                    long costTime = System.currentTimeMillis() - startTime;
                    updateChatMessageWithResponse(messageId, "处理失败", costTime, e.getMessage());
                    emitter.completeWithError(e);
                }
            });

        } catch (Exception e) {
            log.error("Spring AI流式聊天初始化失败", e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    @Override
    public List<AiChatRecordVO> getChatHistory(String sessionId, String userId) {
        String currentUserId = userId != null ? userId : SecurityUtils.getCurrentUserId();
        return chatRecordMapper.selectChatHistory(sessionId, currentUserId);
    }

    @Override
    public PageResult<AiChatRecordVO> selectChatRecordPage(AiChatQueryDTO queryDTO) {
        Page<AiChatRecordVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<AiChatRecordVO> pageResult = chatRecordMapper.selectChatRecordPage(page, queryDTO);
        return PageResult.of(pageResult.getRecords(), pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearChatHistory(String sessionId, String userId) {
        String currentUserId = userId != null ? userId : SecurityUtils.getCurrentUserId();

        LambdaQueryWrapper<AiChatRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiChatRecord::getSessionId, sessionId)
                .eq(AiChatRecord::getUserId, currentUserId);

        return remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteChatRecords(List<String> recordIds) {
        return removeByIds(recordIds);
    }

    @Override
    public String generateSessionId(String userId) {
        return "session_" + UUID.randomUUID().toString().replace("-", "");
    }

    @Override
    public List<String> getUserSessions(String userId) {
        String currentUserId = userId != null ? userId : SecurityUtils.getCurrentUserId();
        return chatRecordMapper.selectUserSessions(currentUserId);
    }

    @Override
    public AiChatResponseVO regenerateResponse(String recordId) {
        AiChatRecord record = getById(recordId);
        if (record == null) {
            throw new BusinessException(ResultCode.NOT_FOUND.getCode(), "聊天记录不存在");
        }

        // 重新生成响应
        AiChatRequestDTO requestDTO = new AiChatRequestDTO();
        requestDTO.setSessionId(record.getSessionId());
        requestDTO.setMessage(record.getUserMessage());
        requestDTO.setModelName(record.getModelName());
        requestDTO.setMessageType(record.getMessageType());

        return chat(requestDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rateResponse(String recordId, Integer rating, String feedback) {
        AiChatRecord record = getById(recordId);
        if (record == null) {
            return false;
        }

        // 这里可以添加评分字段到实体类中
        // record.setRating(rating);
        // record.setFeedback(feedback);
        return updateById(record);
    }

    @Override
    public ChatStatistics getChatStatistics(String userId) {
        String currentUserId = userId != null ? userId : SecurityUtils.getCurrentUserId();
        AiChatRecordMapper.ChatStatisticsResult result = chatRecordMapper.selectChatStatistics(currentUserId);

        ChatStatistics statistics = new ChatStatistics();
        if (result != null) {
            statistics.setTotalChats(result.getTotalChats());
            statistics.setTotalTokens(result.getTotalTokens());
            statistics.setTotalSessions(result.getTotalSessions());
            statistics.setAvgResponseTime(result.getAvgResponseTime());
        }

        return statistics;
    }

    /**
     * 调用AI服务
     */
    private String callAiService(String message, String modelName) {
        if (chatClient == null) {
            // 如果没有配置AI客户端，返回模拟响应
            log.warn("AI客户端未配置，返回模拟响应");
            return "这是一个模拟的AI响应，针对您的问题：" + message;
        }

        try {
            String response = chatClient.prompt()
                    .user(message)
                    .call()
                    .content();

            return response;
        } catch (Exception e) {
            log.error("调用AI服务失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "AI服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 从原始 SSE 数据中提取有用的内容
     *
     * @param rawData 原始 SSE 数据行
     * @return 提取的内容，如果没有有用内容则返回 null
     */
    private String extractContentFromRawData(String rawData) {
        try {
            // 跳过非数据行
            if (rawData == null || rawData.trim().isEmpty() ||
                !rawData.contains("data:") || rawData.contains("data: [DONE]")) {
                return null;
            }

            // 提取 data: 后面的 JSON 内容
            String jsonData = null;
            if (rawData.startsWith("data: ")) {
                jsonData = rawData.substring(6).trim();
            } else if (rawData.contains("data:")) {
                int index = rawData.indexOf("data:");
                jsonData = rawData.substring(index + 5).trim();
            }

            if (jsonData == null || jsonData.isEmpty()) {
                return null;
            }

            // 尝试解析 JSON 并提取 answer 字段
            try {
                // 简单的 JSON 解析，查找 "answer" 字段
                if (jsonData.contains("\"answer\"")) {
                    int answerStart = jsonData.indexOf("\"answer\":");
                    if (answerStart != -1) {
                        answerStart = jsonData.indexOf("\"", answerStart + 9);
                        if (answerStart != -1) {
                            int answerEnd = jsonData.indexOf("\"", answerStart + 1);
                            if (answerEnd != -1) {
                                String answer = jsonData.substring(answerStart + 1, answerEnd);
                                // 处理转义字符
                                answer = answer.replace("\\n", "\n")
                                              .replace("\\t", "\t")
                                              .replace("\\\"", "\"")
                                              .replace("\\\\", "\\");
                                return answer;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.debug("解析JSON失败，返回原始数据: {}", e.getMessage());
            }

            // 如果无法解析 JSON，返回原始数据（去掉 data: 前缀）
            return jsonData;

        } catch (Exception e) {
            log.debug("提取内容失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 确定消息类型
     */
    private String determineMessageType(AiChatRequestDTO requestDTO) {
        // 如果有明确的 messageType，使用它
        if (StringUtils.hasText(requestDTO.getMessageType())) {
            return requestDTO.getMessageType();
        }

        // 如果有文件，根据文件类型确定
        if (requestDTO.getFiles() != null && !requestDTO.getFiles().isEmpty()) {
            // 检查是否有图片文件
            boolean hasImage = requestDTO.getFiles().stream()
                    .anyMatch(file -> "image".equals(file.getType()));
            if (hasImage) {
                return "image";
            }

            // 检查是否有其他类型的文件
            boolean hasFile = requestDTO.getFiles().stream()
                    .anyMatch(file -> file.getType() != null && !file.getType().equals("image"));
            if (hasFile) {
                return "file";
            }
        }

        // 默认为文本
        return "text";
    }

    /**
     * 构建 Dify 请求体
     */
    private Map<String, Object> buildDifyRequestBody(AiChatRequestDTO requestDTO) {
        Map<String, Object> requestBody = new HashMap<>();

        // 基本字段
        requestBody.put("inputs", requestDTO.getInputs() != null ? requestDTO.getInputs() : new HashMap<>());
        requestBody.put("query", requestDTO.getActualQuery());
        requestBody.put("response_mode", requestDTO.getResponseMode() != null ? requestDTO.getResponseMode() : "streaming");
        requestBody.put("conversation_id", requestDTO.getActualConversationId() != null ? requestDTO.getActualConversationId() : "");
        requestBody.put("user", requestDTO.getActualUser() != null ? requestDTO.getActualUser() : SecurityUtils.getCurrentUserId());
        requestBody.put("files", requestDTO.getFiles() != null ? requestDTO.getFiles() : new ArrayList<>());

        log.debug("构建的 Dify 请求体: {}", requestBody);
        return requestBody;
    }

    @Override
    public SseEmitter streamChat(AiChatRequest request) {
        log.info("处理Dify标准格式流式聊天请求: query={}, appId={}, conversationId={}",
                request.getQuery(), request.getAppId(), request.getConversationId());

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

//        if (difyConfig == null) {
//            log.error("DifyConfig 未配置，无法获取应用ID");
//            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify配置未找到");
//        }

        // 获取应用ID，必须通过路径参数传递
        String appId = request.getAppId();
        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        // 创建或获取Agent会话
        long startTime = System.currentTimeMillis();


        // 创建SSE发射器
        SseEmitter emitter = new SseEmitter(0L); // 无超时限制

        // 获取当前租户ID，用于异步线程
        String currentTenantId = SecurityUtils.getCurrentTenantId();

        // 异步处理流式响应
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始调用 Dify Console API: appId={}", appId);

                // 第一步：获取已安装应用列表
                difyWebClientConfig.getInstalledAppsWithAuth(appId)
                        .doOnNext(installedAppsResponse -> {
                            log.debug("收到已安装应用响应: {}", installedAppsResponse);

                            // 查找对应的已安装应用ID
                            String installedAppId = installedAppsResponse.findInstalledAppIdByAppId(appId);
                            if (installedAppId == null) {
                                log.error("未找到应用ID {} 对应的已安装应用", appId);
                                emitter.completeWithError(new RuntimeException("未找到对应的已安装应用"));
                                return;
                            }

                            log.info("找到已安装应用ID: {}", installedAppId);

                            // 第二步：使用已安装应用ID调用聊天接口
                            Map<String, Object> requestBody = request.toDifyConsoleRequestMap();
                            log.debug("Dify Console API 请求体: {}", requestBody);

                            // 用于收集AI回复内容和元数据
                            StringBuilder aiResponseBuilder = new StringBuilder();
                            DifyStreamMetadata streamMetadata = new DifyStreamMetadata();
                            streamMetadata.setQuery(request.getQuery());
                            streamMetadata.setAgentId(request.getAgentId());
                            if(StringUtils.hasText(request.getParentMessageId())){
                                streamMetadata.setParentMessageId(request.getParentMessageId());
                            }

                            difyWebClientConfig.callDifyChatWithInstalledAppId(installedAppId, requestBody)
                                    .doOnNext(data -> {
                                        try {

                                            // 解析并收集Dify流式响应数据
                                            parseDifyStreamData(data, aiResponseBuilder, streamMetadata);

                                            // 原样转发数据
                                            emitter.send(SseEmitter.event().data(data));
                                        } catch (Exception e) {
                                            log.error("发送SSE数据失败", e);
                                            emitter.completeWithError(e);
                                        }
                                    })
                                    .doOnError(error -> {
                                        log.error("Dify Console API 聊天调用失败", error);
                                        // 更新聊天记录的错误信息
                                        long costTime = System.currentTimeMillis() - startTime;
                                        updateChatMessageWithDifyResponse( "处理失败", costTime, error.getMessage(), streamMetadata, currentTenantId);
                                        emitter.completeWithError(error);
                                    })
                                    .doOnComplete(() -> {
                                        log.info("Dify Console API 聊天调用完成");
                                        // 更新聊天记录的AI回复内容和Dify元数据
                                        String aiResponse = aiResponseBuilder.toString();
                                        long costTime = System.currentTimeMillis() - startTime;
                                        updateChatMessageWithDifyResponse(aiResponse, costTime, null, streamMetadata, currentTenantId);
                                        emitter.complete();
                                    })
                                    .subscribe();
                        })
                        .doOnError(error -> {
                            log.error("获取已安装应用失败", error);
                            // 更新聊天记录的错误信息
                            long costTime = System.currentTimeMillis() - startTime;
                            DifyStreamMetadata errorMetadata = new DifyStreamMetadata();
                            updateChatMessageWithDifyResponse("处理失败", costTime, error.getMessage(), errorMetadata, currentTenantId);
                            emitter.completeWithError(error);
                        })
                        .subscribe();

            } catch (Exception e) {
                log.error("调用 Dify Console API 异常", e);
                emitter.completeWithError(e);
            }
        });

        // 设置超时和错误处理
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时");
            emitter.complete();
        });

        emitter.onError((error) -> {
            log.error("SSE连接错误", error);
        });

        emitter.onCompletion(() -> {
            log.info("SSE连接完成");
        });

        return emitter;
    }

    @Override
    public List<ConversationInfo> getConversations(String appId, Integer limit, Boolean pinned) {
        log.info("获取智能体会话列表: appId={}, limit={}, pinned={}", appId, limit, pinned);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法获取会话列表");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        try {
            // 第一步：获取已安装应用列表，找到对应的已安装应用ID
            log.info("开始获取已安装应用列表，appId: {}", appId);

            DifyInstalledAppsResponseDTO installedAppsResponse;
            try {
                installedAppsResponse = difyWebClientConfig.getInstalledAppsWithAuth(appId)
                        .block(Duration.ofSeconds(30)); // 设置30秒超时

                log.info("获取已安装应用列表API调用完成，结果: {}", installedAppsResponse != null ? "成功" : "null");
            } catch (Exception apiException) {
                log.error("调用Dify API获取已安装应用列表时发生异常", apiException);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "调用Dify API获取已安装应用列表失败: " + apiException.getMessage());
            }
//            try {
//                log.info("准备调用 difyWebClientConfig.getInstalledAppsWithAuth");
//                Mono<DifyInstalledAppsResponseDTO> responseMono = difyWebClientConfig.getInstalledAppsWithAuth(appId);
//                log.info("getInstalledAppsWithAuth 返回的 Mono: {}", responseMono != null ? "非null" : "null");
//
//                installedAppsResponse = responseMono
//                        .doOnSubscribe(subscription -> log.info("开始订阅获取已安装应用的结果"))
//                        .doOnNext(response -> log.info("成功获取到已安装应用响应: {}", response != null ? "非null" : "null"))
//                        .doOnError(error -> log.error("获取已安装应用时发生错误: {}", error.getMessage(), error))
//                        .doOnCancel(() -> log.warn("获取已安装应用的订阅被取消"))
//                        .doFinally(signalType -> log.info("获取已安装应用完成，信号类型: {}", signalType))
//                        .block(Duration.ofSeconds(30)); // 设置30秒超时
//
//                log.info("获取已安装应用列表API调用完成，结果: {}", installedAppsResponse != null ? "成功" : "null");
//            } catch (Exception apiException) {
//                log.error("调用Dify API获取已安装应用列表时发生异常", apiException);
//                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "调用Dify API获取已安装应用列表失败: " + apiException.getMessage());
//            }

            if (installedAppsResponse == null) {
                log.error("获取已安装应用列表失败：API返回null");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            log.info("成功获取已安装应用列表，应用数量: {}",
                installedAppsResponse.getInstalledApps() != null ? installedAppsResponse.getInstalledApps().size() : 0);

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.findInstalledAppIdByAppId(appId);
            if (installedAppId == null) {
                log.error("未找到应用ID {} 对应的已安装应用", appId);
                throw new BusinessException(ResultCode.NOT_FOUND.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {}", installedAppId);

            // 第二步：使用已安装应用ID获取会话列表
            DifyConversationListResponseDTO conversationResponse = difyWebClientConfig
                    .getConversationsWithAuth(installedAppId, limit, pinned)
                    .block(); // 同步调用

            if (conversationResponse == null || conversationResponse.getData() == null) {
                log.warn("获取会话列表为空");
                return new ArrayList<>();
            }

            // 转换为ConversationInfo列表
            List<ConversationInfo> conversations = new ArrayList<>();
            for (DifyConversationDTO difyConversation : conversationResponse.getData()) {
                ConversationInfo conversation = new ConversationInfo();
                conversation.setId(difyConversation.getId());
                conversation.setName(difyConversation.getName());
                conversation.setStatus(difyConversation.getStatus());
                conversation.setIntroduction(difyConversation.getIntroduction());
                conversation.setCreatedAt(difyConversation.getCreatedAt());
                conversation.setUpdatedAt(difyConversation.getUpdatedAt());
                conversations.add(conversation);
            }

            log.info("成功获取会话列表，共 {} 个会话", conversations.size());
            return conversations;

        } catch (Exception e) {
            log.error("获取会话列表失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取会话列表失败: " + e.getMessage());
        }
    }

    @Override
    public ConversationInfo getConversationName(String appId, String conversationId, String agentId, Boolean autoGenerate) {
        log.info("获取会话名称: appId={}, conversationId={}, agentId={}, autoGenerate={}", appId, conversationId, agentId, autoGenerate);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法获取会话名称");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        if (!StringUtils.hasText(conversationId)) {
            log.error("会话ID未提供，无法获取会话名称");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "会话ID不能为空");
        }

        try {
            // 第一步：获取已安装应用列表，找到对应的已安装应用ID
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig.getInstalledAppsWithAuth(appId)
                    .block(); // 这里使用同步调用，因为我们需要结果来进行下一步

            if (installedAppsResponse == null) {
                log.error("获取已安装应用列表失败");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.findInstalledAppIdByAppId(appId);
            if (installedAppId == null) {
                log.error("未找到应用ID {} 对应的已安装应用", appId);
                throw new BusinessException(ResultCode.NOT_FOUND.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {}", installedAppId);

            // 第二步：使用已安装应用ID获取会话名称
            DifyConversationNameResponseDTO nameResponse = difyWebClientConfig
                    .getConversationNameWithAuth(installedAppId, conversationId, autoGenerate)
                    .block(); // 同步调用

            if (nameResponse == null) {
                log.error("获取会话名称失败");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取会话名称失败");
            }

            // 转换为ConversationInfo
            ConversationInfo conversation = new ConversationInfo();
            conversation.setId(nameResponse.getId());
            conversation.setName(nameResponse.getName());
            conversation.setStatus(nameResponse.getStatus());
            conversation.setIntroduction(nameResponse.getIntroduction());
            conversation.setAgentId(agentId);
            // 保持原始的秒级时间戳，不转换为毫秒级
            conversation.setCreatedAt(nameResponse.getCreatedAt());
            conversation.setUpdatedAt(nameResponse.getUpdatedAt());

            log.info("成功获取会话名称: {}", conversation.getName());

            // 如果提供了agentId，则在agent_conversation表中插入会话记录
            if (StringUtils.hasText(agentId)) {
                try {
                    // 创建或获取Agent会话记录
                    String agentConversationId = conversationIntegrationService.updateAgentConversation(conversation);
                    if (StringUtils.hasText(agentConversationId)) {
                        log.info("成功创建Agent会话记录: agentId={}, conversationId={}, agentConversationId={}",
                                agentId, conversationId, agentConversationId);
                    }
                } catch (Exception e) {
                    log.warn("创建Agent会话记录失败，但不影响主流程: agentId={}, conversationId={}, error={}",
                            agentId, conversationId, e.getMessage());
                }
            }

            return conversation;

        } catch (Exception e) {
            log.error("获取会话名称失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取会话名称失败: " + e.getMessage());
        }
    }

    @Override
    public ConversationInfo updateConversationName(String appId, String conversationId, String name) {
        log.info("修改会话名称: appId={}, conversationId={}, name={}", appId, conversationId, name);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法修改会话名称");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        if (!StringUtils.hasText(conversationId)) {
            log.error("会话ID未提供，无法修改会话名称");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "会话ID不能为空");
        }

        if (!StringUtils.hasText(name)) {
            log.error("会话名称未提供，无法修改会话名称");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "会话名称不能为空");
        }

        try {
            // 第一步：根据appId获取已安装应用ID
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig
                    .getInstalledAppsWithAuth()
                    .block(); // 同步调用

            if (installedAppsResponse == null || installedAppsResponse.getInstalledApps() == null) {
                log.error("获取已安装应用列表失败");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.getInstalledApps().stream()
                    .filter(app -> app.getApp() != null && appId.equals(app.getApp().getId()))
                    .map(DifyInstalledAppsResponseDTO.InstalledApp::getId)
                    .findFirst()
                    .orElse(null);

            if (installedAppId == null) {
                log.error("未找到对应的已安装应用ID: {}", appId);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {}", installedAppId);

            // 第二步：使用已安装应用ID修改会话名称
            DifyConversationNameResponseDTO nameResponse = difyWebClientConfig
                    .updateConversationNameWithAuth(installedAppId, conversationId, name)
                    .block(); // 同步调用

            if (nameResponse == null) {
                log.error("修改会话名称失败");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "修改会话名称失败");
            }

            // 转换为ConversationInfo
            ConversationInfo conversation = new ConversationInfo();
            conversation.setId(nameResponse.getId());
            conversation.setName(nameResponse.getName());
            conversation.setStatus(nameResponse.getStatus());
            conversation.setIntroduction(nameResponse.getIntroduction());
            // 将秒级时间戳转换为毫秒级时间戳
            conversation.setCreatedAt(nameResponse.getCreatedAt() != null ?
                nameResponse.getCreatedAt() * 1000L : null);
            conversation.setUpdatedAt(nameResponse.getUpdatedAt() != null ?
                nameResponse.getUpdatedAt() * 1000L : null);

            log.info("成功修改会话名称: {}", conversation.getName());
            return conversation;

        } catch (Exception e) {
            log.error("修改会话名称失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "修改会话名称失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteConversation(String appId, String conversationId) {
        log.info("删除会话记录: appId={}, conversationId={}", appId, conversationId);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法删除会话记录");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        if (!StringUtils.hasText(conversationId)) {
            log.error("会话ID未提供，无法删除会话记录");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "会话ID不能为空");
        }

        try {
            // 第一步：根据appId获取已安装应用ID
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig
                    .getInstalledAppsWithAuth()
                    .block(); // 同步调用

            if (installedAppsResponse == null || installedAppsResponse.getInstalledApps() == null) {
                log.error("获取已安装应用列表失败");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.getInstalledApps().stream()
                    .filter(app -> app.getApp() != null && appId.equals(app.getApp().getId()))
                    .map(DifyInstalledAppsResponseDTO.InstalledApp::getId)
                    .findFirst()
                    .orElse(null);

            if (installedAppId == null) {
                log.error("未找到对应的已安装应用ID: {}", appId);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {}", installedAppId);

            // 第二步：使用已安装应用ID删除会话记录
            log.info("开始调用Dify API删除会话记录: installedAppId={}, conversationId={}", installedAppId, conversationId);

            Boolean deleteResult;
            try {
                deleteResult = difyWebClientConfig
                        .deleteConversationWithAuth(installedAppId, conversationId)
                        .block(); // 同步调用

                log.info("Dify API删除会话记录调用完成: deleteResult={}", deleteResult);
            } catch (Exception apiException) {
                log.error("调用Dify API删除会话记录时发生异常", apiException);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "调用Dify API删除会话记录失败: " + apiException.getMessage());
            }

            if (deleteResult == null || !deleteResult) {
                log.error("删除会话记录失败: deleteResult={}", deleteResult);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "删除会话记录失败");
            }

            log.info("成功删除会话记录: conversationId={}", conversationId);
            return true;

        } catch (Exception e) {
            log.error("删除会话记录失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "删除会话记录失败: " + e.getMessage());
        }
    }

    @Override
    public boolean stopWorkflow(String appId, String taskId) {
        log.info("停止工作流: appId={}, taskId={}", appId, taskId);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法停止工作流");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        if (!StringUtils.hasText(taskId)) {
            log.error("任务ID未提供，无法停止工作流");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "任务ID不能为空");
        }

        try {
            // 第一步：根据appId获取已安装应用ID
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig
                    .getInstalledAppsWithAuth()
                    .block(); // 同步调用

            if (installedAppsResponse == null || installedAppsResponse.getInstalledApps() == null) {
                log.error("获取已安装应用列表失败");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取已安装应用列表失败");
            }

            // 查找对应的已安装应用ID
            String installedAppId = installedAppsResponse.getInstalledApps().stream()
                    .filter(app -> app.getApp() != null && appId.equals(app.getApp().getId()))
                    .map(DifyInstalledAppsResponseDTO.InstalledApp::getId)
                    .findFirst()
                    .orElse(null);

            if (installedAppId == null) {
                log.error("未找到对应的已安装应用ID: {}", appId);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {}", installedAppId);

            // 第二步：使用已安装应用ID停止工作流
            log.info("开始调用Dify API停止工作流: installedAppId={}, taskId={}", installedAppId, taskId);

            DifyStopWorkflowResponseDTO stopResult;
            try {
                stopResult = difyWebClientConfig
                        .stopWorkflowWithAuth(installedAppId, taskId)
                        .block(); // 同步调用

                log.info("Dify API停止工作流调用完成: stopResult={}", stopResult);
            } catch (Exception apiException) {
                log.error("调用Dify API停止工作流时发生异常", apiException);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "调用Dify API停止工作流失败: " + apiException.getMessage());
            }

            if (stopResult == null || !"success".equals(stopResult.getResult())) {
                log.error("停止工作流失败: stopResult={}", stopResult);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "停止工作流失败");
            }

            log.info("成功停止工作流: taskId={}", taskId);
            return true;

        } catch (Exception e) {
            log.error("停止工作流失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "停止工作流失败: " + e.getMessage());
        }
    }

    @Override
    public List<IAiChatService.ConversationMessage> getConversationMessages(String appId, String conversationId, Integer limit, String lastId) {
        log.info("获取会话消息列表: appId={}, conversationId={}, limit={}, lastId={}", appId, conversationId, limit, lastId);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法获取消息列表");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        if (!StringUtils.hasText(conversationId)) {
            log.error("会话ID未提供，无法获取消息列表");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "会话ID不能为空");
        }

        try {
            // 获取已安装应用ID
            String installedAppId = getInstalledAppId(appId);

            if (installedAppId == null) {
                log.error("未找到对应的已安装应用: appId={}", appId);
                throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {} 对应 appId: {}", installedAppId, appId);

            // 调用Dify API获取消息列表
            log.info("准备调用Dify API: installedAppId={}, conversationId={}, limit={}, lastId={}",
                    installedAppId, conversationId, limit, lastId);

            List<DifyMessageDTO> difyMessages = difyWebClientConfig.getConversationMessagesWithAuth(
                    installedAppId, conversationId, limit, lastId).block();

            log.info("Dify API返回结果: difyMessages={}", difyMessages != null ? difyMessages.size() : "null");

            if (difyMessages == null || difyMessages.isEmpty()) {
                log.warn("获取消息列表为空: difyMessages={}", difyMessages);
                return new ArrayList<>();
            }

            // 转换为ConversationMessage列表
            List<IAiChatService.ConversationMessage> messages = new ArrayList<>();
            for (DifyMessageDTO difyMessage : difyMessages) {
                // 添加用户消息
                if (StringUtils.hasText(difyMessage.getQuery())) {
                    IAiChatService.ConversationMessage userMessage = new IAiChatService.ConversationMessage();
                    userMessage.setId(difyMessage.getId() + "_user");
                    userMessage.setConversationId(difyMessage.getConversationId());
                    userMessage.setQuery(difyMessage.getQuery());
                    userMessage.setContent(difyMessage.getQuery());
                    userMessage.setRole("user");
                    userMessage.setCreatedAt(difyMessage.getCreatedAt());
                    messages.add(userMessage);
                }

                // 添加助手回复
                if (StringUtils.hasText(difyMessage.getAnswer())) {
                    IAiChatService.ConversationMessage assistantMessage = new IAiChatService.ConversationMessage();
                    assistantMessage.setId(difyMessage.getId() + "_assistant");
                    assistantMessage.setConversationId(difyMessage.getConversationId());
                    assistantMessage.setAnswer(difyMessage.getAnswer());
                    assistantMessage.setContent(difyMessage.getAnswer());
                    assistantMessage.setRole("assistant");
                    assistantMessage.setCreatedAt(difyMessage.getCreatedAt());
                    messages.add(assistantMessage);
                }
            }

            log.info("成功获取消息列表，共 {} 条消息", messages.size());
            return messages;

        } catch (Exception e) {
            log.error("获取消息列表失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取消息列表失败: " + e.getMessage());
        }
    }

    @Override
    public Object getConversationMessagesRaw(String appId, String conversationId, Integer limit, String lastId) {
        log.info("获取会话消息列表（原样返回）: appId={}, conversationId={}, limit={}, lastId={}", appId, conversationId, limit, lastId);

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify Console API");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID未提供，无法获取消息列表");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        if (!StringUtils.hasText(conversationId)) {
            log.error("会话ID未提供，无法获取消息列表");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "会话ID不能为空");
        }

        try {
            // 获取已安装应用ID
            String installedAppId = getInstalledAppId(appId);

            if (installedAppId == null) {
                log.error("未找到对应的已安装应用: appId={}", appId);
                throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {} 对应 appId: {}", installedAppId, appId);

            // 调用Dify API获取原始响应
            log.info("准备调用Dify API获取原始响应: installedAppId={}, conversationId={}, limit={}, lastId={}",
                    installedAppId, conversationId, limit, lastId);

            DifyMessageListResponseDTO difyResponse = difyWebClientConfig.getConversationMessagesRawWithAuth(
                    installedAppId, conversationId, limit, lastId).block();

            log.info("Dify API返回原始结果: {}", difyResponse != null ? "有数据" : "null");

            if (difyResponse == null) {
                log.warn("获取消息列表为空");
                // 返回空的Dify格式响应
                DifyMessageListResponseDTO emptyResponse = new DifyMessageListResponseDTO();
                emptyResponse.setData(new ArrayList<>());
                emptyResponse.setLimit(limit != null ? limit : 20);
                emptyResponse.setHasMore(false);
                emptyResponse.setTotal(0);
                return emptyResponse;
            }

            log.info("成功获取原始消息列表，共 {} 条消息",
                    difyResponse.getData() != null ? difyResponse.getData().size() : 0);
            return difyResponse;

        } catch (Exception e) {
            log.error("获取原始消息列表失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 发送Mock流式响应
     */
    private void sendMockStreamResponse(SseEmitter emitter, String query) {
        try {
            // 模拟流式响应
            String[] mockResponses = {
                "这是一个测试响应，",
                "针对您的问题：" + query + "。",
                "由于当前处于测试模式，",
                "无法连接到真实的Dify服务，",
                "所以返回这个模拟响应。",
                "请检查网络连接和Dify服务配置。"
            };

            for (int i = 0; i < mockResponses.length; i++) {
                Thread.sleep(500); // 模拟延迟

                // 构造SSE数据格式
                String sseData = String.format("data: {\"event\":\"message\",\"message_id\":\"mock-%d\",\"conversation_id\":\"mock-conversation\",\"answer\":\"%s\"}\n\n",
                    i, mockResponses[i]);

                emitter.send(SseEmitter.event()
                    .data(sseData)
                    .name("message"));

                log.debug("发送Mock SSE数据: {}", sseData);
            }

            // 发送结束标记
            emitter.send(SseEmitter.event()
                .data("data: [DONE]\n\n")
                .name("done"));

            emitter.complete();
            log.info("Mock流式响应发送完成");

        } catch (Exception e) {
            log.error("发送Mock流式响应失败", e);
            emitter.completeWithError(e);
        }
    }

    /**
     * 根据应用ID获取已安装应用ID
     *
     * @param appId 应用ID
     * @return 已安装应用ID
     */
    private String getInstalledAppId(String appId) {
        try {
            // 获取所有已安装应用列表
            DifyInstalledAppsResponseDTO installedAppsResponse = difyWebClientConfig.getInstalledAppsWithAuth(appId).block();

            if (installedAppsResponse == null || installedAppsResponse.getInstalledApps() == null) {
                log.error("获取已安装应用列表失败");
                return null;
            }

            log.debug("获取到 {} 个已安装应用", installedAppsResponse.getInstalledApps().size());

            // 打印所有已安装应用的信息用于调试
            for (DifyInstalledAppsResponseDTO.InstalledApp app : installedAppsResponse.getInstalledApps()) {
                log.debug("已安装应用: installedAppId={}, appId={}, appName={}",
                    app.getId(),
                    app.getApp() != null ? app.getApp().getId() : "null",
                    app.getApp() != null ? app.getApp().getName() : "null");
            }

            // 根据appId找到对应的已安装应用ID
            for (DifyInstalledAppsResponseDTO.InstalledApp app : installedAppsResponse.getInstalledApps()) {
                if (app.getApp() != null && appId.equals(app.getApp().getId())) {
                    log.info("找到匹配的已安装应用: installedAppId={}, appId={}", app.getId(), appId);
                    return app.getId();
                }
            }

            log.warn("未找到应用ID {} 对应的已安装应用", appId);
            return null;

        } catch (Exception e) {
            log.error("获取已安装应用ID失败", e);
            return null;
        }
    }

    @Override
    public Object getAppParameters(String appId) {
        log.info("获取应用会话参数: appId={}", appId);

        if (!StringUtils.hasText(appId)) {
            log.error("应用ID不能为空");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "应用ID不能为空");
        }

        // 检查必要的依赖
        if (difyAppService == null) {
            log.error("DifyAppService 未配置，无法调用 Dify 应用参数接口");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify应用服务未配置");
        }

        try {
            // 第一步：根据appId获取已安装应用ID
            String installedAppId = getInstalledAppId(appId);
            if (installedAppId == null) {
                log.error("未找到对应的已安装应用ID: {}", appId);
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "未找到对应的已安装应用");
            }

            log.info("找到已安装应用ID: {}", installedAppId);

            // 第二步：调用 Dify 应用参数接口
            return difyAppService.getAppParameters(installedAppId)
                    .map(result -> {
                        if (result.isSuccess()) {
                            return result.getData();
                        } else {
                            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(),
                                "获取应用会话参数失败: " + result.getMessage());
                        }
                    })
                    .block(); // 同步调用

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取应用会话参数失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "获取应用会话参数失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadResponseDTO uploadFile(byte[] fileContent, String fileName, String contentType) {
        log.info("上传文件: fileName={}, contentType={}, size={}", fileName, contentType, fileContent.length);

        if (fileContent == null || fileContent.length == 0) {
            log.error("文件内容不能为空");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "文件内容不能为空");
        }

        if (!StringUtils.hasText(fileName)) {
            log.error("文件名不能为空");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "文件名不能为空");
        }

        if (!StringUtils.hasText(contentType)) {
            log.error("文件类型不能为空");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "文件类型不能为空");
        }

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify 文件上传接口");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        String currentUserId = SecurityUtils.getCurrentUserId();
        AiFileRecord fileRecord = new AiFileRecord();

        try {
            // 1. 先上传到 Dify 平台
            DifyFileUploadResponseDTO difyResponse = difyWebClientConfig
                    .uploadFileWithAuth(fileContent, fileName, contentType)
                    .block(); // 同步调用

            if (difyResponse == null) {
                log.error("文件上传到Dify失败，响应为空");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "文件上传到Dify失败");
            }

            log.info("文件上传到Dify成功: fileId={}, fileName={}, size={}",
                difyResponse.getId(), difyResponse.getName(), difyResponse.getSize());

            // 2. 同时上传到 MinIO
            String minioUrl = null;
            String minioBucket = "ai-files";
            String minioObjectName = null;

            try {
                if (minioStorageService != null) {
                    // 生成MinIO对象名称：年/月/日/时间戳_原文件名
                    LocalDateTime now = LocalDateTime.now();
                    String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                    minioObjectName = datePath + "/" + System.currentTimeMillis() + "_" + fileName;

                    // 上传到MinIO
                    minioUrl = minioStorageService.uploadFile(
                        minioBucket,
                        minioObjectName,
                        new ByteArrayInputStream(fileContent),
                        contentType,
                        null // metadata
                    );

                    log.info("文件上传到MinIO成功: bucket={}, objectName={}, url={}",
                        minioBucket, minioObjectName, minioUrl);
                } else {
                    log.warn("MinioStorageService不可用，跳过MinIO上传");
                }
            } catch (Exception e) {
                log.error("文件上传到MinIO失败，但Dify上传成功，继续处理", e);
                // MinIO上传失败不影响整体流程
                minioUrl = null;
                minioBucket = null;
                minioObjectName = null;
            }

            // 3. 保存文件记录到数据库
            fileRecord.setId(UUID.randomUUID().toString().replace("-", ""));
            fileRecord.setOriginalFilename(fileName);
            fileRecord.setFileSize((long) fileContent.length);
            fileRecord.setMimeType(contentType);
            fileRecord.setDifyFileId(difyResponse.getId());
            fileRecord.setDifyPreviewUrl(difyResponse.getPreviewUrl());
            fileRecord.setUploadTime(LocalDateTime.now());
            fileRecord.setUploadUserId(currentUserId);
            fileRecord.setStatus("uploaded");

            // 设置文件扩展名
            if (fileName.contains(".")) {
                String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
                fileRecord.setFileExtension(extension);
            }

            // 设置MinIO相关信息
            if (minioUrl != null) {
                fileRecord.setMinioBucket(minioBucket);
                fileRecord.setMinioObjectName(minioObjectName);
                fileRecord.setMinioUrl(minioUrl);
            }

            aiFileRecordMapper.insert(fileRecord);
            log.info("文件记录保存成功: recordId={}", fileRecord.getId());

            // 4. 构建响应DTO
            FileUploadResponseDTO responseDTO = new FileUploadResponseDTO();
            responseDTO.setId(fileRecord.getId());
            responseDTO.setOriginalFilename(fileRecord.getOriginalFilename());
            responseDTO.setFileSize(fileRecord.getFileSize());
            responseDTO.setFileExtension(fileRecord.getFileExtension());
            responseDTO.setMimeType(fileRecord.getMimeType());
            responseDTO.setDifyFileId(fileRecord.getDifyFileId());
            responseDTO.setDifyPreviewUrl(fileRecord.getDifyPreviewUrl());
            responseDTO.setMinioBucket(fileRecord.getMinioBucket());
            responseDTO.setMinioObjectName(fileRecord.getMinioObjectName());
            responseDTO.setMinioUrl(fileRecord.getMinioUrl());
            responseDTO.setStatus(fileRecord.getStatus());
            responseDTO.setUploadTime(fileRecord.getUploadTime());

            return responseDTO;

        } catch (BusinessException e) {
            // 更新文件记录状态为失败
            if (fileRecord.getId() != null) {
                fileRecord.setStatus("failed");
                fileRecord.setErrorMessage(e.getMessage());
                aiFileRecordMapper.updateById(fileRecord);
            }
            throw e;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            // 更新文件记录状态为失败
            if (fileRecord.getId() != null) {
                fileRecord.setStatus("failed");
                fileRecord.setErrorMessage(e.getMessage());
                aiFileRecordMapper.updateById(fileRecord);
            }
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public Object uploadRemoteFile(String fileUrl) {
        log.info("上传远程文件: fileUrl={}", fileUrl);

        if (!StringUtils.hasText(fileUrl)) {
            log.error("文件URL不能为空");
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "文件URL不能为空");
        }

        // 验证URL格式
        if (!fileUrl.startsWith("http://") && !fileUrl.startsWith("https://")) {
            log.error("文件URL格式不正确: {}", fileUrl);
            throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "文件URL格式不正确，必须以http://或https://开头");
        }

        // 检查必要的依赖
        if (difyWebClientConfig == null) {
            log.error("DifyWebClientConfig 未配置，无法调用 Dify 远程文件上传接口");
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "Dify服务未配置");
        }

        try {
            // 调用 Dify 远程文件上传接口
            DifyRemoteFileUploadResponseDTO uploadResponse = difyWebClientConfig
                    .uploadRemoteFileWithAuth(fileUrl)
                    .block(); // 同步调用

            if (uploadResponse == null) {
                log.error("远程文件上传失败，响应为空");
                throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "远程文件上传失败");
            }

            log.info("远程文件上传成功: fileId={}, fileName={}, size={}",
                uploadResponse.getId(), uploadResponse.getName(), uploadResponse.getSize());

            return uploadResponse;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("远程文件上传失败", e);
            throw new BusinessException(ResultCode.AI_SERVICE_UNAVAILABLE.getCode(), "远程文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 创建聊天记录
     */
    private AiChatRecord createChatRecord(AiChatRequest request, long startTime) {
        AiChatRecord chatRecord = new AiChatRecord();
        chatRecord.setSessionId(request.getConversationId() != null ? request.getConversationId() : "");
        chatRecord.setUserId(SecurityUtils.getCurrentUserId());
        chatRecord.setInput(request.getInputs().toString());
        chatRecord.setUserMessage(request.getQuery());
        chatRecord.setMessageType(request.getMessageType());
        chatRecord.setModelName("dify");
        chatRecord.setStatus("0"); // 处理中
        chatRecord.setCreateTime(LocalDateTime.now());

        // 设置应用ID
        if (StringUtils.hasText(request.getAppId())) {
            chatRecord.setAppId(request.getAppId());
        }

        return chatRecord;
    }

    /**
     * 保存聊天记录
     */
    private void saveChatRecord(AiChatRecord chatRecord, long startTime, String errorMessage) {
        try {
            if (errorMessage != null) {
                chatRecord.setStatus("0"); // 失败
                chatRecord.setErrorMsg(errorMessage);
            } else {
                chatRecord.setStatus("1"); // 成功
            }

            chatRecord.setCostTime(System.currentTimeMillis() - startTime);
            save(chatRecord);

            log.info("聊天记录已保存: sessionId={}, status={}, costTime={}ms",
                    chatRecord.getSessionId(), chatRecord.getStatus(), chatRecord.getCostTime());

            // 同步到Agent会话系统
            if ("1".equals(chatRecord.getStatus()) && StringUtils.hasText(chatRecord.getAppId())) {
                try {
                    conversationIntegrationService.syncChatRecordToAgent(chatRecord);
                } catch (Exception e) {
                    log.error("同步聊天记录到Agent系统失败", e);
                }
            }
        } catch (Exception e) {
            log.error("保存聊天记录失败", e);
        }
    }

    /**
     * 保存聊天记录（带租户ID参数）
     * 用于异步调用时传递租户ID
     */
    private void saveChatRecord(AiChatRecord chatRecord, long startTime, String errorMessage, String tenantId) {
        try {
            if (errorMessage != null) {
                chatRecord.setStatus("0"); // 失败
                chatRecord.setErrorMsg(errorMessage);
            } else {
                chatRecord.setStatus("1"); // 成功
            }

            chatRecord.setCostTime(System.currentTimeMillis() - startTime);
            save(chatRecord);

            log.info("聊天记录已保存: sessionId={}, status={}, costTime={}ms",
                    chatRecord.getSessionId(), chatRecord.getStatus(), chatRecord.getCostTime());

            // 同步到Agent会话系统
            if ("1".equals(chatRecord.getStatus()) && StringUtils.hasText(chatRecord.getAppId())) {
                try {
                    // 在异步线程中设置租户上下文
                    if (StringUtils.hasText(tenantId)) {
                        TenantContextHolder.setTenantId(tenantId);
                        try {
                            conversationIntegrationService.syncChatRecordToAgent(chatRecord);
                        } finally {
                            // 清理租户上下文
                            TenantContextHolder.clear();
                        }
                    } else {
                        conversationIntegrationService.syncChatRecordToAgent(chatRecord);
                    }
                } catch (Exception e) {
                    log.error("同步聊天记录到Agent系统失败", e);
                }
            }
        } catch (Exception e) {
            log.error("保存聊天记录失败", e);
        }
    }

    /**
     * 保存用户消息到agent_message表
     */
    private String saveUserMessageToAgent(String conversationId, AiChatRequestDTO requestDTO) {
        try {
            String tenantId = SecurityUtils.getCurrentTenantId();

            // 获取下一个序号
            Integer nextSequence = conversationIntegrationService.getNextSequenceNumber(conversationId);

            // 创建用户消息
            String messageId = UUID.randomUUID().toString().replace("-", "");
            conversationIntegrationService.saveUserMessage(conversationId, requestDTO.getMessage(),
                    nextSequence, messageId);

            log.info("保存用户消息到Agent系统: messageId={}, conversationId={}", messageId, conversationId);
            return messageId;

        } catch (Exception e) {
            log.error("保存用户消息到Agent系统失败", e);
            return null;
        }
    }

    /**
     * 保存AI回复消息到agent_message表
     */
    private void saveAssistantMessageToAgent(String conversationId, String aiResponse, long costTime, String parentMessageId) {
        try {
            conversationIntegrationService.saveAssistantMessage(conversationId, aiResponse, null);
            log.info("保存AI回复消息到Agent系统: conversationId={}, costTime={}ms", conversationId, costTime);
        } catch (Exception e) {
            log.error("保存AI回复消息到Agent系统失败", e);
        }
    }

    /**
     * 保存带错误信息的AI回复消息到agent_message表
     */
    private void saveAssistantMessageToAgentWithError(String conversationId, String errorMessage, long costTime, String parentMessageId) {
        try {
            String tenantId = SecurityUtils.getCurrentTenantId();

            // 获取下一个序号
            Integer nextSequence = conversationIntegrationService.getNextSequenceNumber(conversationId);

            // 创建错误消息
            String messageId = UUID.randomUUID().toString().replace("-", "");
            conversationIntegrationService.saveErrorMessage(conversationId, errorMessage,
                    nextSequence, messageId, parentMessageId, costTime);

            log.info("保存错误消息到Agent系统: messageId={}, conversationId={}", messageId, conversationId);
        } catch (Exception e) {
            log.error("保存错误消息到Agent系统失败", e);
        }
    }

    /**
     * 从AiChatRequest保存用户消息到agent_message表
     */
    private String saveUserMessageToAgentFromRequest(String conversationId, AiChatRequest request) {
        try {
            String tenantId = SecurityUtils.getCurrentTenantId();

            // 获取下一个序号
            Integer nextSequence = conversationIntegrationService.getNextSequenceNumber(conversationId);

            // 创建用户消息
            String messageId = UUID.randomUUID().toString().replace("-", "");
            conversationIntegrationService.saveUserMessage(conversationId, request.getQuery(),
                    nextSequence, messageId);

            log.info("保存用户消息到Agent系统: messageId={}, conversationId={}", messageId, conversationId);
            return messageId;

        } catch (Exception e) {
            log.error("保存用户消息到Agent系统失败", e);
            return null;
        }
    }

    /**
     * 创建聊天消息记录（问答合并模式）
     * 用户提问存到query字段，等待AI回复后更新content字段
     */
    private String createChatMessageRecord(String conversationId, AiChatRequestDTO requestDTO) {
        try {
            String tenantId = SecurityUtils.getCurrentTenantId();

            // 获取下一个序号
            Integer nextSequence = conversationIntegrationService.getNextSequenceNumber(conversationId);

            // 创建消息记录
            String messageId = UUID.randomUUID().toString().replace("-", "");

            // 调用新的方法创建问答合并记录
            conversationIntegrationService.createChatMessageRecord(conversationId, requestDTO.getMessage(),
                    requestDTO, nextSequence, messageId);

            log.info("创建聊天消息记录: messageId={}, conversationId={}", messageId, conversationId);
            return messageId;

        } catch (Exception e) {
            log.error("创建聊天消息记录失败", e);
            return null;
        }
    }

    /**
     * 更新聊天消息记录的AI回复内容
     */
    private void updateChatMessageWithResponse(String messageId, String aiResponse, long costTime, String errorMessage) {
        try {
            conversationIntegrationService.updateChatMessageWithResponse(messageId, aiResponse, costTime, errorMessage);
            log.info("更新聊天消息记录: messageId={}, costTime={}ms", messageId, costTime);
        } catch (Exception e) {
            log.error("更新聊天消息记录失败", e);
        }
    }

    /**
     * 更新聊天消息记录的AI回复内容（包含Dify元数据）
     */
    private void updateChatMessageWithDifyResponse(String aiResponse, long costTime,
                                                  String errorMessage, DifyStreamMetadata metadata) {
        try {

            conversationIntegrationService.updateChatMessageWithDifyResponse(aiResponse, costTime, errorMessage, metadata);
            log.info("更新Dify聊天消息记录: messageId={}, difyMessageId={}, costTime={}ms",
                    metadata.getMessageId(), metadata != null ? metadata.getMessageId() : null, costTime);
        } catch (Exception e) {
            log.error("更新Dify聊天消息记录失败", e);
        }
    }

    /**
     * 更新聊天消息记录的AI回复内容（包含Dify元数据，带租户ID）
     * 用于异步调用时传递租户ID
     */
    private void updateChatMessageWithDifyResponse(String aiResponse, long costTime,
                                                  String errorMessage, DifyStreamMetadata metadata, String tenantId) {
        try {
            // 在异步线程中设置租户上下文
            if (StringUtils.hasText(tenantId)) {
                TenantContextHolder.setTenantId(tenantId);
                try {
                    conversationIntegrationService.updateChatMessageWithDifyResponse(aiResponse, costTime, errorMessage, metadata);
                } finally {
                    // 清理租户上下文
                    TenantContextHolder.clear();
                }
            } else {
                conversationIntegrationService.updateChatMessageWithDifyResponse(aiResponse, costTime, errorMessage, metadata);
            }
            log.info("更新Dify聊天消息记录: messageId={}, difyMessageId={}, costTime={}ms",
                    metadata.getMessageId(), metadata != null ? metadata.getMessageId() : null, costTime);
        } catch (Exception e) {
            log.error("更新Dify聊天消息记录失败", e);
        }
    }

    /**
     * 从AiChatRequest创建聊天消息记录（问答合并模式）
     */
    private String createChatMessageRecordFromRequest(String conversationId, AiChatRequest request) {
        try {
            String tenantId = SecurityUtils.getCurrentTenantId();

            // 获取下一个序号
            Integer nextSequence = conversationIntegrationService.getNextSequenceNumber(conversationId);

            // 创建消息记录
            String messageId = UUID.randomUUID().toString().replace("-", "");

            // 调用新的方法创建问答合并记录
            conversationIntegrationService.createChatMessageRecord(conversationId, request.getQuery(),
                    request, nextSequence, messageId);

            log.info("创建聊天消息记录: messageId={}, conversationId={}", messageId, conversationId);
            return messageId;

        } catch (Exception e) {
            log.error("创建聊天消息记录失败", e);
            return null;
        }
    }



    /**
     * 解析Dify流式响应数据
     * 提取AI回复内容和元数据信息
     */
    private void parseDifyStreamData(String data, StringBuilder aiResponseBuilder, DifyStreamMetadata metadata) {
        try {
            if (data == null || data.trim().isEmpty()) {
                return;
            }

            String jsonData;
            // 处理SSE格式的数据（以"data: "开头）
            if (data.startsWith("data: ")) {
                jsonData = data.substring(6).trim();
                if (jsonData.isEmpty() || "[DONE]".equals(jsonData)) {
                    return;
                }
            } else {
                // 直接处理JSON格式的数据
                jsonData = data.trim();
                if (jsonData.isEmpty()) {
                    return;
                }
            }

            // 解析JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonData);

            // 提取事件类型
            String event = jsonNode.has("event") ? jsonNode.get("event").asText() : null;
            if (event != null) {
                metadata.setEvent(event);
            }

            // 提取消息ID
            if (jsonNode.has("message_id")) {
                String messageId = jsonNode.get("message_id").asText();
                if (StringUtils.hasText(messageId)) {
                    metadata.setMessageId(messageId);
                }
            }

            // 提取对话ID
            if (jsonNode.has("conversation_id")) {
                String conversationId = jsonNode.get("conversation_id").asText();
                if (StringUtils.hasText(conversationId)) {
                    metadata.setConversationId(conversationId);
                }
            }

            // 提取任务ID
            if (jsonNode.has("task_id")) {
                String taskId = jsonNode.get("task_id").asText();
                if (StringUtils.hasText(taskId)) {
                    metadata.setTaskId(taskId);
                }
            }

            // 提取创建时间
            if (jsonNode.has("created_at")) {
                Long createdAt = jsonNode.get("created_at").asLong();
                metadata.setCreatedAt(createdAt);
            }

            // 根据事件类型处理不同的数据结构
            if ("message".equals(event)) {
                // 处理消息事件，提取答案内容
                if (jsonNode.has("answer")) {
                    String answer = jsonNode.get("answer").asText();
                    if (StringUtils.hasText(answer)) {
                        aiResponseBuilder.append(answer);
                        metadata.setAnswer(answer);
                    }
                }
            } else if ("node_finished".equals(event)) {
                // 处理节点完成事件，从data字段中提取信息
                if (jsonNode.has("data")) {
                    JsonNode dataNode = jsonNode.get("data");

                    // 提取节点信息
                    if (dataNode.has("node_id")) {
                        String nodeId = dataNode.get("node_id").asText();
                        log.debug("节点完成: nodeId={}, nodeType={}, status={}",
                                nodeId,
                                dataNode.has("node_type") ? dataNode.get("node_type").asText() : "unknown",
                                dataNode.has("status") ? dataNode.get("status").asText() : "unknown");
                    }

                    // 如果节点有输出内容，也可以提取
                    if (dataNode.has("outputs") && !dataNode.get("outputs").isNull()) {
                        JsonNode outputsNode = dataNode.get("outputs");
                        // 这里可以根据具体的输出格式进行处理
                        log.debug("节点输出: {}", outputsNode.toString());
                    }
                }
            }

            // 提取来源变量选择器（在消息事件中）
            if (jsonNode.has("from_variable_selector")) {
                JsonNode selectorNode = jsonNode.get("from_variable_selector");
                if (selectorNode.isArray()) {
                    metadata.setFromVariableSelector(selectorNode.toString());
                }
            }

            // 提取token统计信息
            if (jsonNode.has("metadata")) {
                JsonNode metadataNode = jsonNode.get("metadata");
                if (metadataNode.has("usage")) {
                    JsonNode usageNode = metadataNode.get("usage");

                    if (usageNode.has("prompt_tokens")) {
                        Integer promptTokens = usageNode.get("prompt_tokens").asInt();
                        metadata.setPromptTokens(promptTokens);
                    }

                    if (usageNode.has("completion_tokens")) {
                        Integer completionTokens = usageNode.get("completion_tokens").asInt();
                        metadata.setCompletionTokens(completionTokens);
                    }

                    if (usageNode.has("total_tokens")) {
                        Integer totalTokens = usageNode.get("total_tokens").asInt();
                        metadata.setTotalTokens(totalTokens);
                    }
                }
            }

            log.debug("解析Dify流式数据: event={}, messageId={}, conversationId={}, taskId={}, answer={}, fromVariableSelector={}",
                    event, metadata.getMessageId(), metadata.getConversationId(), metadata.getTaskId(),
                    metadata.getAnswer(), metadata.getFromVariableSelector());

        } catch (Exception e) {
            log.warn("解析Dify流式响应数据失败: {}", e.getMessage());
        }
    }

}
