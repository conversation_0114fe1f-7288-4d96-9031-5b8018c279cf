<template>
  <div class="file-storage-management">
    <!-- 页面头部 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">文件存储管理</h2>
          <p class="text-gray-600 mt-1">管理和配置文件存储连接</p>
        </div>
        <button
          @click="showAddForm = true"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center"
        >
          <span class="mr-2">+</span>添加存储配置
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters mb-6 bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            @input="handleSearch"
            type="text"
            placeholder="搜索存储配置名称..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div class="w-48">
          <select
            v-model="statusFilter"
            @change="handleSearch"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部状态</option>
            <option value="0">启用</option>
            <option value="1">停用</option>
          </select>
        </div>
        <div class="w-48">
          <select
            v-model="typeFilter"
            @change="handleSearch"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部类型</option>
            <option v-for="type in storageTypes" :key="type.dictValue" :value="type.dictValue">
              {{ type.dictLabel }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- 存储配置列表 -->
    <div class="storage-list bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">存储配置列表</h3>
      </div>

      <div class="p-4">
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="text-gray-500">加载中...</div>
        </div>

        <div v-else-if="storageConfigs.length === 0" class="flex items-center justify-center py-12">
          <div class="text-center text-gray-500">
            <div class="text-4xl mb-4">💾</div>
            <div>暂无存储配置</div>
          </div>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="config in storageConfigs"
            :key="config.id"
            class="storage-card p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all"
            :class="{ 'border-blue-300 bg-blue-50': config.isDefault === 'Y' }"
          >
            <!-- 存储类型图标和名称 -->
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <div
                  class="w-10 h-10 rounded-lg flex items-center justify-center text-xl"
                  :style="{ backgroundColor: config.iconColor || '#f3f4f6' }"
                >
                  {{ config.icon || '💾' }}
                </div>
                <div>
                  <div class="font-semibold text-gray-900 flex items-center">
                    {{ config.name }}
                    <span v-if="config.isDefault === 'Y'" class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      默认
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">{{ config.storageTypeName }}</div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  class="px-2 py-1 text-xs rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': config.status === '0',
                    'bg-red-100 text-red-800': config.status === '1'
                  }"
                >
                  {{ config.status === '0' ? '启用' : '停用' }}
                </span>
              </div>
            </div>

            <!-- 连接信息 -->
            <div class="mb-3 space-y-1">
              <div v-if="config.host" class="text-sm text-gray-600">
                <span class="font-medium">主机:</span> {{ config.host }}{{ config.port ? ':' + config.port : '' }}
              </div>
              <div v-if="config.bucketName" class="text-sm text-gray-600">
                <span class="font-medium">存储桶:</span> {{ config.bucketName }}
              </div>
              <div v-if="config.description" class="text-sm text-gray-500">{{ config.description }}</div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center justify-between pt-3 border-t border-gray-100">
              <div class="flex space-x-2">
                <button
                  @click="testConnection(config)"
                  class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                >
                  测试连接
                </button>
                <button
                  v-if="config.isDefault !== 'Y'"
                  @click="setDefault(config)"
                  class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                >
                  设为默认
                </button>
              </div>
              <div class="flex space-x-1">
                <button
                  @click="editConfig(config)"
                  class="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                  title="编辑"
                >
                  ✏️
                </button>
                <button
                  @click="toggleStatus(config)"
                  class="p-1 text-gray-400 hover:text-yellow-500 transition-colors"
                  :title="config.status === '0' ? '停用' : '启用'"
                >
                  {{ config.status === '0' ? '⏸️' : '▶️' }}
                </button>
                <button
                  @click="deleteConfig(config)"
                  class="p-1 text-gray-400 hover:text-red-500 transition-colors"
                  title="删除"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑存储配置表单弹窗 -->
    <div v-if="showAddForm || showEditForm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ showAddForm ? '添加存储配置' : '编辑存储配置' }}
          </h3>
        </div>

        <div class="p-6">
          <form @submit.prevent="submitForm" class="space-y-4">
            <!-- 基本信息 -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">配置名称 *</label>
                <input
                  v-model="formData.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入配置名称"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">存储类型 *</label>
                <select
                  v-model="formData.storageType"
                  required
                  @change="handleTypeChange"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择类型</option>
                  <option v-for="type in storageTypes" :key="type.dictValue" :value="type.dictValue">
                    {{ type.dictLabel }}
                  </option>
                </select>
              </div>
            </div>

            <!-- 连接信息 -->
            <div v-if="needsConnection" class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">主机地址 *</label>
                <input
                  v-model="formData.host"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="*************"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">端口</label>
                <input
                  v-model="formData.port"
                  type="number"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="9000"
                />
              </div>
            </div>

            <!-- 认证信息 -->
            <div v-if="needsAuth" class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">访问密钥ID</label>
                <input
                  v-model="formData.accessKey"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Access Key"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">访问密钥Secret</label>
                <input
                  v-model="formData.secretKey"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Secret Key"
                />
              </div>
            </div>

            <!-- 存储桶/路径 -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formData.storageType === 'local' ? '存储路径' : '存储桶名称' }}
                </label>
                <input
                  v-model="formData.bucketName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  :placeholder="formData.storageType === 'local' ? '/data/storage' : 'bucket-name'"
                />
              </div>
              <div v-if="needsRegion">
                <label class="block text-sm font-medium text-gray-700 mb-1">区域</label>
                <input
                  v-model="formData.region"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="us-east-1"
                />
              </div>
            </div>

            <!-- SSL和自定义域名 -->
            <div v-if="needsConnection" class="grid grid-cols-2 gap-4">
              <div class="flex items-center">
                <input
                  v-model="sslEnabled"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label class="ml-2 text-sm text-gray-700">启用SSL/TLS</label>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">自定义域名</label>
                <input
                  v-model="formData.customDomain"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="files.example.com"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
              <textarea
                v-model="formData.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入描述信息"
              ></textarea>
            </div>

            <div class="flex items-center">
              <input
                v-model="isDefaultChecked"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label class="ml-2 text-sm text-gray-700">设为默认存储</label>
            </div>
          </form>
        </div>

        <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="closeForm"
            type="button"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            @click="submitForm"
            type="button"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            :disabled="submitting"
          >
            {{ submitting ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DictAPI } from '@/api/dict'
import { FileStorageAPI } from '@/api/fileStorage'
import type { SysDictDataVO } from '@/types/system'
import type { FileStorageConfigVO, FileStorageConfigCreateDTO, FileStorageConfigQueryDTO } from '@/api/fileStorage'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddForm = ref(false)
const showEditForm = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')

// 存储类型字典
const storageTypes = ref<SysDictDataVO[]>([])

// 存储配置列表
const storageConfigs = ref<FileStorageConfigVO[]>([])

// 表单数据
const formData = reactive({
  name: '',
  storageType: '',
  host: '',
  port: null as number | null,
  accessKey: '',
  secretKey: '',
  bucketName: '',
  region: '',
  sslEnabled: 'N',
  customDomain: '',
  description: '',
  isDefault: 'N',
  status: '0'
})

// 计算属性
const needsConnection = computed(() => {
  return ['minio', 'ftp', 'obs'].includes(formData.storageType)
})

const needsAuth = computed(() => {
  return ['minio', 'ftp', 'obs'].includes(formData.storageType)
})

const needsRegion = computed(() => {
  return ['obs'].includes(formData.storageType)
})

const sslEnabled = computed({
  get: () => formData.sslEnabled === 'Y',
  set: (value: boolean) => {
    formData.sslEnabled = value ? 'Y' : 'N'
  }
})

const isDefaultChecked = computed({
  get: () => formData.isDefault === 'Y',
  set: (value: boolean) => {
    formData.isDefault = value ? 'Y' : 'N'
  }
})

// 方法
const loadStorageTypes = async () => {
  try {
    const response = await DictAPI.getDictDataByType('file_storage_type')
    if (response.success && response.data) {
      storageTypes.value = response.data
    }
  } catch (error) {
    console.error('加载存储类型失败:', error)
    ElMessage.error('加载存储类型失败')
  }
}

const loadStorageConfigs = async () => {
  loading.value = true
  try {
    const queryParams: FileStorageConfigQueryDTO = {
      name: searchQuery.value,
      storageType: typeFilter.value,
      status: statusFilter.value
    }

    const response = await FileStorageAPI.getFileStorageConfigList(queryParams)
    if (response.success && response.data) {
      storageConfigs.value = response.data
    } else {
      ElMessage.error('加载存储配置列表失败')
    }
  } catch (error) {
    console.error('加载存储配置失败:', error)
    ElMessage.error('加载存储配置失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  loadStorageConfigs()
}

const handleTypeChange = () => {
  // 根据类型设置默认端口
  const typePortMap: Record<string, number> = {
    'minio': 9000,
    'ftp': 21,
    'obs': 443
  }

  if (formData.storageType && typePortMap[formData.storageType]) {
    formData.port = typePortMap[formData.storageType]
  }
}

let currentEditingId = ''

const editConfig = (config: FileStorageConfigVO) => {
  currentEditingId = config.id
  Object.assign(formData, {
    name: config.name,
    storageType: config.storageType,
    host: config.host,
    port: config.port,
    accessKey: config.accessKey,
    bucketName: config.bucketName,
    region: config.region,
    sslEnabled: config.sslEnabled,
    customDomain: config.customDomain,
    description: config.description,
    isDefault: config.isDefault,
    status: config.status
  })
  showEditForm.value = true
}

const deleteConfig = async (config: FileStorageConfigVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除存储配置 "${config.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await FileStorageAPI.deleteFileStorageConfigs([config.id])
    if (response.success) {
      ElMessage.success('删除成功')
      loadStorageConfigs()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const toggleStatus = async (config: FileStorageConfigVO) => {
  try {
    const action = config.status === '0' ? '停用' : '启用'
    const newStatus = config.status === '0' ? '1' : '0'

    await ElMessageBox.confirm(
      `确定要${action}存储配置 "${config.name}" 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await FileStorageAPI.batchUpdateStatus([config.id], newStatus)
    if (response.success) {
      config.status = newStatus
      ElMessage.success(`${action}成功`)
    } else {
      ElMessage.error(`${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const setDefault = async (config: FileStorageConfigVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 "${config.name}" 设为默认存储配置吗？`,
      '确认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await FileStorageAPI.setDefaultConfig(config.id)
    if (response.success) {
      ElMessage.success('设置默认配置成功')
      loadStorageConfigs()
    } else {
      ElMessage.error('设置默认配置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('设置默认配置失败')
    }
  }
}

const testConnection = async (config: FileStorageConfigVO) => {
  try {
    ElMessage.info('正在测试连接...')
    const response = await FileStorageAPI.testConnectionById(config.id)
    if (response.success && response.data.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(response.data.message || '连接测试失败')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error('连接测试失败')
  }
}

const submitForm = async () => {
  submitting.value = true
  try {
    let response
    if (showAddForm.value) {
      response = await FileStorageAPI.createFileStorageConfig(formData)
    } else {
      if (!currentEditingId) {
        ElMessage.error('无法获取编辑的存储配置ID')
        return
      }
      response = await FileStorageAPI.updateFileStorageConfig(currentEditingId, formData)
    }

    if (response.success) {
      ElMessage.success(showAddForm.value ? '添加成功' : '更新成功')
      closeForm()
      loadStorageConfigs()
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    submitting.value = false
  }
}

const closeForm = () => {
  showAddForm.value = false
  showEditForm.value = false
  currentEditingId = ''
  // 重置表单
  Object.assign(formData, {
    name: '',
    storageType: '',
    host: '',
    port: null,
    accessKey: '',
    secretKey: '',
    bucketName: '',
    region: '',
    sslEnabled: 'N',
    customDomain: '',
    description: '',
    isDefault: 'N',
    status: '0'
  })
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadStorageTypes()
  await loadStorageConfigs()
})
</script>

<style scoped>
.file-storage-management {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.storage-card {
  transition: all 0.2s ease-in-out;
}

.storage-card:hover {
  transform: translateY(-2px);
}
</style>
