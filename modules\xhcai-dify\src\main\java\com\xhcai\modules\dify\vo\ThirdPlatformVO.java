package com.xhcai.modules.dify.vo;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 第三方智能体VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "第三方智能体VO")
public class ThirdPlatformVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID", example = "1")
    private String id;

    /**
     * 智能体平台ID（字典数据ID）
     */
    @Schema(description = "智能体平台ID", example = "1")
    private String platformId;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "ChatGPT智能助手")
    private String name;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述", example = "OpenAI GPT-4 智能对话助手")
    private String description;

    /**
     * 所属单位ID
     */
    @Schema(description = "所属单位ID", example = "1")
    private String unitId;

    /**
     * 所属单位名称
     */
    @Schema(description = "所属单位名称", example = "技术部")
    private String unitName;

    /**
     * 连接地址
     */
    @Schema(description = "连接地址", example = "https://api.openai.com/v1/chat/completions")
    private String connectionUrl;

    /**
     * API密钥（脱敏显示）
     */
    @Schema(description = "API密钥（脱敏显示）", example = "sk-***")
    private String apiKey;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", example = "30")
    private Integer timeout;

    /**
     * 访问范围
     */
    @Schema(description = "访问范围", example = "public")
    private String accessScope;

    /**
     * 访问范围文本
     */
    @Schema(description = "访问范围文本", example = "公开")
    private String accessScopeText;

    /**
     * 授权用户ID列表（JSON字符串，用于数据库映射）
     */
    @Schema(description = "授权用户ID列表（JSON字符串）", hidden = true)
    private String authorizedUsersJson;

    /**
     * 授权单位ID列表（JSON字符串，用于数据库映射）
     */
    @Schema(description = "授权单位ID列表（JSON字符串）", hidden = true)
    private String authorizedUnitsJson;

    /**
     * 授权用户ID列表
     */
    @Schema(description = "授权用户ID列表")
    private List<String> authorizedUsers;

    /**
     * 授权用户名称列表
     */
    @Schema(description = "授权用户名称列表")
    private List<String> authorizedUserNames;

    /**
     * 授权单位ID列表
     */
    @Schema(description = "授权单位ID列表")
    private List<String> authorizedUnits;

    /**
     * 授权单位名称列表
     */
    @Schema(description = "授权单位名称列表")
    private List<String> authorizedUnitNames;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 状态文本
     */
    @Schema(description = "状态文本", example = "启用")
    private String statusText;

    /**
     * 智能体图标
     */
    @Schema(description = "智能体图标", example = "🤖")
    private String icon;

    /**
     * 图标背景色
     */
    @Schema(description = "图标背景色", example = "#3b82f6")
    private String iconBg;

    /**
     * 最后连接测试时间
     */
    @Schema(description = "最后连接测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastTestTime;

    /**
     * 最后连接测试结果：0-失败，1-成功
     */
    @Schema(description = "最后连接测试结果：0-失败，1-成功", example = "1")
    private Integer lastTestResult;

    /**
     * 最后连接测试结果文本
     */
    @Schema(description = "最后连接测试结果文本", example = "成功")
    private String lastTestResultText;

    /**
     * 最后连接测试错误信息
     */
    @Schema(description = "最后连接测试错误信息")
    private String lastTestError;

    /**
     * 创建者
     */
    @Schema(description = "创建者", example = "admin")
    private String createBy;

    /**
     * 创建者姓名
     */
    @Schema(description = "创建者姓名", example = "管理员")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者", example = "admin")
    private String updateBy;

    /**
     * 更新者姓名
     */
    @Schema(description = "更新者姓名", example = "管理员")
    private String updateByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getConnectionUrl() {
        return connectionUrl;
    }

    public void setConnectionUrl(String connectionUrl) {
        this.connectionUrl = connectionUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getAccessScope() {
        return accessScope;
    }

    public void setAccessScope(String accessScope) {
        this.accessScope = accessScope;
    }

    public String getAccessScopeText() {
        return accessScopeText;
    }

    public void setAccessScopeText(String accessScopeText) {
        this.accessScopeText = accessScopeText;
    }

    public String getAuthorizedUsersJson() {
        return authorizedUsersJson;
    }

    public void setAuthorizedUsersJson(String authorizedUsersJson) {
        this.authorizedUsersJson = authorizedUsersJson;
    }

    public String getAuthorizedUnitsJson() {
        return authorizedUnitsJson;
    }

    public void setAuthorizedUnitsJson(String authorizedUnitsJson) {
        this.authorizedUnitsJson = authorizedUnitsJson;
    }

    public List<String> getAuthorizedUsers() {
        return authorizedUsers;
    }

    public void setAuthorizedUsers(List<String> authorizedUsers) {
        this.authorizedUsers = authorizedUsers;
    }

    public List<String> getAuthorizedUserNames() {
        return authorizedUserNames;
    }

    public void setAuthorizedUserNames(List<String> authorizedUserNames) {
        this.authorizedUserNames = authorizedUserNames;
    }

    public List<String> getAuthorizedUnits() {
        return authorizedUnits;
    }

    public void setAuthorizedUnits(List<String> authorizedUnits) {
        this.authorizedUnits = authorizedUnits;
    }

    public List<String> getAuthorizedUnitNames() {
        return authorizedUnitNames;
    }

    public void setAuthorizedUnitNames(List<String> authorizedUnitNames) {
        this.authorizedUnitNames = authorizedUnitNames;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconBg() {
        return iconBg;
    }

    public void setIconBg(String iconBg) {
        this.iconBg = iconBg;
    }

    public LocalDateTime getLastTestTime() {
        return lastTestTime;
    }

    public void setLastTestTime(LocalDateTime lastTestTime) {
        this.lastTestTime = lastTestTime;
    }

    public Integer getLastTestResult() {
        return lastTestResult;
    }

    public void setLastTestResult(Integer lastTestResult) {
        this.lastTestResult = lastTestResult;
    }

    public String getLastTestResultText() {
        return lastTestResultText;
    }

    public void setLastTestResultText(String lastTestResultText) {
        this.lastTestResultText = lastTestResultText;
    }

    public String getLastTestError() {
        return lastTestError;
    }

    public void setLastTestError(String lastTestError) {
        this.lastTestError = lastTestError;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdateByName() {
        return updateByName;
    }

    public void setUpdateByName(String updateByName) {
        this.updateByName = updateByName;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
