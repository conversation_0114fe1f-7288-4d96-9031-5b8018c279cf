<template>
  <div class="flow-canvas">
    <!-- Vue Flow 画布组件 -->
    <VueFlow
      :nodes="props.nodes"
      :edges="props.edges"
      :default-viewport="props.viewport"
      :min-zoom="0.2"
      :max-zoom="4"
      :snap-to-grid="true"
      :snap-grid="[15, 15]"
      @connect="onConnect"
      @pane-ready="onPaneReady"
      @drop="onDrop"
      @dragover="onDragOver"
      @node-click="onNodeClick"
    >
      <!-- 自定义节点组件 -->
      <!-- 数据源节点 -->
      <template #node-ftp="nodeProps">
        <DataSourceNode v-bind="nodeProps" />
      </template>
      <template #node-mysql="nodeProps">
        <DataSourceNode v-bind="nodeProps" />
      </template>
      <template #node-elasticsearch="nodeProps">
        <DataSourceNode v-bind="nodeProps" />
      </template>
      <template #node-minio="nodeProps">
        <DataSourceNode v-bind="nodeProps" />
      </template>
      <template #node-postgresql="nodeProps">
        <DataSourceNode v-bind="nodeProps" />
      </template>
      <template #node-oracle="nodeProps">
        <DataSourceNode v-bind="nodeProps" />
      </template>
      <template #node-local-file="nodeProps">
        <DataSourceNode v-bind="nodeProps" />
      </template>

      <!-- 处理节点 -->
      <template #node-segment="nodeProps">
        <SegmentNode v-bind="nodeProps" />
      </template>
      <template #node-vectorize="nodeProps">
        <VectorizeNode v-bind="nodeProps" />
      </template>

      <!-- 输出节点 -->
      <template #node-weaviate="nodeProps">
        <OutputNode v-bind="nodeProps" />
      </template>
      <template #node-pinecone="nodeProps">
        <OutputNode v-bind="nodeProps" />
      </template>
      <template #node-milvus="nodeProps">
        <OutputNode v-bind="nodeProps" />
      </template>
      <template #node-chroma="nodeProps">
        <OutputNode v-bind="nodeProps" />
      </template>
      <template #node-qdrant="nodeProps">
        <OutputNode v-bind="nodeProps" />
      </template>

      <!-- 控制面板 -->
      <Controls />
      
      <!-- 小地图 -->
      <MiniMap />
      
      <!-- 背景 -->
      <Background pattern="dots" :gap="15" />
    </VueFlow>
  </div>
</template>

<script setup lang="ts">
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { MiniMap } from '@vue-flow/minimap'
import { Controls } from '@vue-flow/controls'
import type { Node, Edge, Connection } from '@vue-flow/core'

import DataSourceNode from '../nodes/DataSourceNode.vue'
import SegmentNode from '../nodes/SegmentNode.vue'
import VectorizeNode from '../nodes/VectorizeNode.vue'
import OutputNode from '../nodes/OutputNode.vue'

// 视口配置类型
interface ViewportConfig {
  x: number
  y: number
  zoom: number
}

// Props
interface Props {
  nodes: Node[]
  edges: Edge[]
  viewport?: ViewportConfig
}

const props = withDefaults(defineProps<Props>(), {
  viewport: () => ({ x: 0, y: 0, zoom: 1 })
})

// Emits
const emit = defineEmits<{
  'connect': [connection: Connection]
  'pane-ready': [instance: any]
  'drop': [event: DragEvent]
  'dragover': [event: DragEvent]
  'node-click': [event: any]
}>()

// 事件处理
const onConnect = (connection: Connection) => {
  emit('connect', connection)
}

const onPaneReady = (instance: any) => {
  emit('pane-ready', instance)
}

const onDrop = (event: DragEvent) => {
  emit('drop', event)
}

const onDragOver = (event: DragEvent) => {
  emit('dragover', event)
}

const onNodeClick = (event: any) => {
  emit('node-click', event)
}
</script>

<style scoped>
.flow-canvas {
  width: 100%;
  height: 100%;
  background: #f8fafc;
}

/* Vue Flow 样式覆盖 */
:deep(.vue-flow__node) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.vue-flow__edge) {
  stroke: #64748b;
  stroke-width: 2;
}

:deep(.vue-flow__edge.selected) {
  stroke: #3b82f6;
}

:deep(.vue-flow__controls) {
  bottom: 20px;
  left: 20px;
}

:deep(.vue-flow__minimap) {
  bottom: 20px;
  right: 20px;
}
</style>
