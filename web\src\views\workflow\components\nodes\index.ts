/**
 * 节点系统统一入口
 * 导出所有节点相关的功能和类型
 */

// 导入类型定义和实例
import { NodeManager } from './manager/NodeManager'
import type { NodeSearchOptions } from './manager/NodeManager'
import type { NodeCreateOptions } from './factory/NodeFactory'

// 核心管理器
export { NodeManager }
export type { NodeManagerClass, NodeSearchOptions } from './manager/NodeManager'

// 注册系统
export { NodeRegistry } from './registry/NodeRegistry'
export type { 
  NodeComponent, 
  NodeRegistration, 
  NodeCategoryRegistration 
} from './registry/NodeRegistry'

// 工厂系统
export { NodeFactory } from './factory/NodeFactory'
export type { 
  NodeCreateOptions, 
  NodeCreateResult, 
  NodeFactoryClass 
} from './factory/NodeFactory'

// 自动注册
export { 
  autoRegisterNodes, 
  registerSingleNode,
  getRegistrationStats,
  isNodeRegistered,
  getRegisteredNodeTypes,
  getNodeTypesByCategory
} from './registry/AutoRegister'

// 基础节点组件
export { default as BaseNode } from './BaseNode.vue'

// 基础节点类型
export { default as StartNode } from './basic/StartNode.vue'
export { default as EndNode } from './basic/EndNode.vue'
export { default as ConditionNode } from './basic/ConditionNode.vue'

// 节点库配置
export * from '../../config/nodeLibrary'

/**
 * 初始化节点系统
 * 这是使用节点系统的推荐入口点
 */
export async function initializeNodeSystem() {
  try {
    await NodeManager.initialize()
    console.log('Node system initialized successfully')
    return true
  } catch (error) {
    console.error('Failed to initialize node system:', error)
    return false
  }
}

/**
 * 获取节点系统状态
 */
export function getNodeSystemStatus() {
  return {
    initialized: NodeManager.isInitialized(),
    stats: NodeManager.getStats()
  }
}

/**
 * 创建节点的便捷方法
 */
export function createNode(type: string, options?: Partial<NodeCreateOptions>) {
  return NodeManager.createNode({
    type,
    ...options
  })
}

/**
 * 搜索节点的便捷方法
 */
export function searchNodes(options: NodeSearchOptions) {
  return NodeManager.searchNodes(options)
}

/**
 * 获取所有可用节点类型的便捷方法
 */
export function getAvailableNodeTypes() {
  return NodeManager.getAllNodeTypes()
}

/**
 * 获取所有节点类别的便捷方法
 */
export function getAvailableCategories() {
  return NodeManager.getAllCategories()
}

/**
 * 检查节点类型是否可用的便捷方法
 */
export function isNodeTypeAvailable(type: string) {
  return NodeManager.hasNode(type)
}

/**
 * 获取节点配置的便捷方法
 */
export function getNodeTypeConfig(type: string) {
  return NodeManager.getNodeConfig(type)
}

/**
 * 获取节点组件的便捷方法
 */
export function getNodeTypeComponent(type: string) {
  return NodeManager.getNodeComponent(type)
}
