package com.xhcai.modules.dify.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.dto.auth.DifyLoginRequestDTO;
import com.xhcai.modules.dify.dto.auth.DifyLoginResponseDTO;
import com.xhcai.modules.dify.dto.auth.DifyLoginWrapperDTO;
import com.xhcai.modules.dify.dto.auth.DifyRefreshTokenRequestDTO;
import com.xhcai.modules.dify.dto.auth.DifyRefreshTokenResponseDTO;
import com.xhcai.modules.dify.service.IDifyAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.net.ConnectException;
import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;

/**
 * Dify 认证服务实现
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Service
public class DifyAuthServiceImpl implements IDifyAuthService {

    private static final Logger log = LoggerFactory.getLogger(DifyAuthServiceImpl.class);

    private static final String REDIS_ACCESS_TOKEN_KEY = "dify:access_token";
    private static final String REDIS_REFRESH_TOKEN_KEY = "dify:refresh_token";
    private static final long TOKEN_EXPIRE_TIME = 24 * 60 * 60; // 24小时，单位：秒

    @Autowired
    private DifyConfig difyConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WebClient.Builder webClientBuilder;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 获取有效的访问令牌
     * 如果 Redis 中没有令牌或令牌无效，会自动登录获取新令牌
     *
     * @return 访问令牌的 Mono
     */
    @Override
    public Mono<String> getValidAccessToken() {
        log.debug("获取有效的访问令牌");

        return Mono.fromCallable(() -> {
                    log.debug("开始从 Redis 获取访问令牌");
                    String accessToken = stringRedisTemplate.opsForValue().get(REDIS_ACCESS_TOKEN_KEY);
                    log.debug("从 Redis 获取到的访问令牌: {}", accessToken);
                    return Optional.ofNullable(accessToken); // 使用Optional包装结果
                })
                .doOnNext(opt -> log.debug("Redis查询结果存在性: {}", opt.isPresent()))
                .flatMap(opt -> opt.map(Mono::just).orElseGet(() -> {
                    log.info("Redis中没有访问令牌，开始登录获取新令牌");
                    return login()
                            .doOnSubscribe(subscription -> log.info("开始订阅登录请求"))
                            .doOnNext(loginResponse -> log.info("登录响应接收: {}", loginResponse != null))
                            .switchIfEmpty(Mono.error(new RuntimeException("Dify登录失败：空响应")))
                            .map(loginResponse -> {
                                if (loginResponse == null || loginResponse.getAccessToken() == null) {
                                    throw new RuntimeException("无效的登录响应");
                                }
                                String token = loginResponse.getAccessToken();
                                log.info("获取到新访问令牌: {}...", abbreviate(token));
                                return token;
                            });
                }))
                .doOnNext(token -> log.debug("最终令牌: {}...", abbreviate(token)))
                .doOnError(e -> log.error("令牌获取失败", e))
                .doFinally(signal -> log.debug("流程终止: {}", signal));
    }

    private String abbreviate(String token) {
        return token != null ? token.substring(0, Math.min(10, token.length())) : "null";
    }


    @Override
    public String getValidAccessTokenSync() {
        log.debug("同步获取有效的访问令牌");

        // 从 Redis 获取访问令牌
        String accessToken = stringRedisTemplate.opsForValue().get(REDIS_ACCESS_TOKEN_KEY);

        if (StringUtils.hasText(accessToken)) {
            log.debug("从 Redis 获取到访问令牌");
            return accessToken;
        }

        log.info("Redis 中没有访问令牌，开始登录获取新令牌");
        DifyLoginResponseDTO loginResponse = login().block();
        return loginResponse.getAccessToken();
    }

    @Override
    public Mono<DifyLoginResponseDTO> login() {
        log.info("开始登录 Dify");

        // 如果启用测试模式，返回Mock响应
        if (difyConfig.isTestMode()) {
            log.info("测试模式已启用，返回Mock登录响应");
            DifyLoginResponseDTO mockResponse = new DifyLoginResponseDTO();
            mockResponse.setAccessToken("mock-access-token-" + System.currentTimeMillis());
            mockResponse.setRefreshToken("mock-refresh-token-" + System.currentTimeMillis());

            // 保存Mock令牌到Redis
            stringRedisTemplate.opsForValue().set(REDIS_ACCESS_TOKEN_KEY, mockResponse.getAccessToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
            stringRedisTemplate.opsForValue().set(REDIS_REFRESH_TOKEN_KEY, mockResponse.getRefreshToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);

            return Mono.just(mockResponse);
        }

        // 构建登录请求
        DifyLoginRequestDTO loginRequest = new DifyLoginRequestDTO();
        loginRequest.setEmail(difyConfig.getEmail());
        loginRequest.setPassword(difyConfig.getPassword());
        loginRequest.setLanguage(difyConfig.getLanguage());
        loginRequest.setRememberMe(true);

        log.debug("发送登录请求: {}", loginRequest);

        // 发送登录请求
        WebClient webClient = webClientBuilder.baseUrl(difyConfig.getBaseUrl()).build();

        log.info("发送Dify登录请求到: {}/console/api/login", difyConfig.getBaseUrl());
        log.info("登录请求详情: email={}, language={}, rememberMe={}",
                loginRequest.getEmail(), loginRequest.getLanguage(), loginRequest.getRememberMe());
        log.info("登录请求完整JSON: {}", loginRequest);

        log.info("开始发送HTTP登录请求");
        return webClient.post()
                .uri("/console/api/login")
                .header("Content-Type", "application/json")
                .bodyValue(loginRequest)
                .retrieve()
                .onStatus(status -> status.is4xxClientError(), response -> {
                    log.error("Dify登录请求客户端错误: status={}, headers={}", response.statusCode(), response.headers().asHttpHeaders());
                    return response.bodyToMono(String.class)
                            .doOnNext(body -> log.error("4xx错误响应体: {}", body))
                            .flatMap(body -> Mono.error(new RuntimeException("登录失败，客户端错误: " + response.statusCode() + ", " + body)));
                })
                .onStatus(status -> status.is5xxServerError(), response -> {
                    log.error("Dify登录请求服务器错误: status={}, headers={}", response.statusCode(), response.headers().asHttpHeaders());
                    return response.bodyToMono(String.class)
                            .doOnNext(body -> log.error("5xx错误响应体: {}", body))
                            .flatMap(body -> Mono.error(new RuntimeException("登录失败，服务器错误: " + response.statusCode() + ", " + body)));
                })
                .onStatus(status -> status.is2xxSuccessful(), response -> {
                    log.info("Dify登录请求成功: status={}, headers={}", response.statusCode(), response.headers().asHttpHeaders());
                    return Mono.empty();
                })
                .bodyToMono(String.class)
                .doOnSubscribe(subscription -> log.info("开始订阅登录响应"))
                .doOnNext(rawResponse -> {
                    log.info("收到Dify登录原始响应: {}", rawResponse);
                    if (rawResponse == null || rawResponse.trim().isEmpty()) {
                        log.error("Dify登录响应为空");
                    }
                })
                .flatMap(rawResponse -> {
                    if (rawResponse == null || rawResponse.trim().isEmpty()) {
                        log.error("Dify登录响应为空，无法解析");
                        return Mono.error(new RuntimeException("Dify登录响应为空"));
                    }
                    try {
                        // 尝试解析JSON
                        ObjectMapper objectMapper = new ObjectMapper();
                        DifyLoginWrapperDTO wrapperResponse = objectMapper.readValue(rawResponse, DifyLoginWrapperDTO.class);
                        log.info("成功解析Dify登录响应: {}", wrapperResponse);
                        return Mono.just(wrapperResponse);
                    } catch (Exception e) {
                        log.error("解析Dify登录响应失败: {}", e.getMessage(), e);
                        return Mono.error(new RuntimeException("解析Dify登录响应失败: " + e.getMessage(), e));
                    }
                })
                .doOnTerminate(() -> log.info("HTTP登录请求完成"))
                .timeout(Duration.ofSeconds(30))  // 增加超时时间
                .doOnError(TimeoutException.class, e -> log.error("登录请求超时: {}", e.getMessage()))
                .doOnError(ConnectException.class, e -> log.error("无法连接到Dify服务器: {}", e.getMessage()))
                .doOnError(e -> log.error("登录请求发生错误: {}", e.getMessage(), e))
                .retry(2)  // 重试2次
                .onErrorResume(e -> {
                    log.error("Dify登录失败，已重试2次，最终错误: {}", e.getMessage());
                    return Mono.error(new RuntimeException("Dify登录失败: " + e.getMessage(), e));
                })
                .switchIfEmpty(Mono.defer(() -> {
                    log.error("Dify登录响应为空");
                    return Mono.error(new RuntimeException("Dify登录响应为空"));
                }))
                .flatMap(wrapperResponse -> {
                    if (wrapperResponse != null && wrapperResponse.isSuccess()) {
                        log.info("登录成功，保存令牌到 Redis");

                        // 转换为标准响应格式
                        DifyLoginResponseDTO response = wrapperResponse.toLoginResponse();

                        if (response.getAccessToken() == null || response.getAccessToken().trim().isEmpty()) {
                            log.error("登录响应中访问令牌为空");
                            return Mono.error(new RuntimeException("登录响应中访问令牌为空"));
                        }

                        return Mono.fromRunnable(() -> {
                            // 保存令牌到 Redis
                            stringRedisTemplate.opsForValue().set(REDIS_ACCESS_TOKEN_KEY, response.getAccessToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                            if (response.getRefreshToken() != null) {
                                stringRedisTemplate.opsForValue().set(REDIS_REFRESH_TOKEN_KEY, response.getRefreshToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                            }
                            log.info("令牌已保存到Redis，访问令牌: {}...", response.getAccessToken().substring(0, Math.min(20, response.getAccessToken().length())));
                        }).thenReturn(response);
                    } else {
                        String errorMsg = wrapperResponse != null ?
                            "登录失败：result=" + wrapperResponse.getResult() + ", data=" + wrapperResponse.getData() :
                            "登录失败：响应为空";
                        log.error(errorMsg);
                        return Mono.error(new RuntimeException(errorMsg));
                    }
                })
                .onErrorMap(WebClientResponseException.class, e -> {
                    log.error("登录请求失败: status={}, body={}", e.getStatusCode(), e.getResponseBodyAsString());
                    return new RuntimeException("登录失败: " + e.getMessage(), e);
                })
                .onErrorMap(Exception.class, e -> {
                    if (!(e instanceof RuntimeException)) {
                        log.error("登录过程中发生异常", e);
                        return new RuntimeException("登录失败: " + e.getMessage(), e);
                    }
                    return e;
                });
    }

    @Override
    public Mono<DifyRefreshTokenResponseDTO> refreshToken() {
        log.info("开始刷新访问令牌");

        return Mono.fromCallable(() -> {
            // 从 Redis 获取刷新令牌
            String refreshToken = stringRedisTemplate.opsForValue().get(REDIS_REFRESH_TOKEN_KEY);

            log.debug("从 Redis 获取到刷新令牌: {}", refreshToken);

            if (!StringUtils.hasText(refreshToken)) {
                log.warn("Redis 中没有刷新令牌，需要重新登录");
                throw new RuntimeException("没有刷新令牌，需要重新登录");
            }

            return refreshToken;
        })
        .flatMap(refreshToken -> {
            // 构建刷新令牌请求
            DifyRefreshTokenRequestDTO refreshRequest = new DifyRefreshTokenRequestDTO(refreshToken);

            log.debug("发送刷新令牌请求: {}", refreshRequest);

            // 发送刷新令牌请求
            WebClient webClient = webClientBuilder.baseUrl(difyConfig.getBaseUrl()).build();

            return webClient.post()
                    .uri("/console/api/refresh-token")
                    .header("Content-Type", "application/json")
                    .bodyValue(refreshRequest)
                    .retrieve()
                    .bodyToMono(DifyRefreshTokenResponseDTO.class)
                    .timeout(Duration.ofSeconds(30));
        })
        .flatMap(response -> {
            if (response != null) {
                if (response.isSuccess()) {
                    log.info("刷新令牌成功，更新 Redis 中的令牌");

                    // 获取令牌数据
                    DifyRefreshTokenResponseDTO.TokenData tokenData = response.getTokenData();
                    if (tokenData != null) {
                        return Mono.fromRunnable(() -> {
                            // 更新 Redis 中的令牌
                            stringRedisTemplate.opsForValue().set(REDIS_ACCESS_TOKEN_KEY, tokenData.getAccessToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                            stringRedisTemplate.opsForValue().set(REDIS_REFRESH_TOKEN_KEY, tokenData.getRefreshToken(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                            log.debug("刷新令牌响应: {}", response);
                        }).thenReturn(response);
                    } else {
                        return Mono.error(new RuntimeException("刷新令牌失败：无法解析令牌数据"));
                    }
                } else {
                    // 刷新失败，获取错误消息
                    String errorMessage = response.getErrorMessage();
                    log.warn("刷新令牌失败: result={}, error={}", response.getResult(), errorMessage);
                    return Mono.error(new RuntimeException("刷新令牌失败: " + (errorMessage != null ? errorMessage : "未知错误")));
                }
            } else {
                return Mono.error(new RuntimeException("刷新令牌失败：响应为空"));
            }
        })
        .onErrorMap(WebClientResponseException.class, e -> {
            log.error("刷新令牌请求失败: status={}, body={}", e.getStatusCode(), e.getResponseBodyAsString());
            return new RuntimeException("刷新令牌失败: " + e.getMessage(), e);
        })
        .onErrorMap(Exception.class, e -> {
            if (!(e instanceof RuntimeException)) {
                log.error("刷新令牌过程中发生异常", e);
                return new RuntimeException("刷新令牌失败: " + e.getMessage(), e);
            }
            return e;
        });
    }

    @Override
    public Mono<Void> clearTokens() {
        log.info("清除 Redis 中的令牌");
        return Mono.fromRunnable(() -> {
            stringRedisTemplate.delete(REDIS_ACCESS_TOKEN_KEY);
            stringRedisTemplate.delete(REDIS_REFRESH_TOKEN_KEY);
        });
    }

    @Override
    public Mono<Boolean> isAccessTokenValid(String accessToken) {
        // 这里可以实现令牌验证逻辑
        // 简单实现：检查令牌是否存在且不为空
        return Mono.just(StringUtils.hasText(accessToken));
    }

    @Override
    public Mono<String> handleUnauthorized() {
        log.warn("处理 401 错误：访问令牌过期，尝试刷新令牌");

        return refreshToken()
                .flatMap(refreshResponse -> {
                    if (refreshResponse.isSuccess()) {
                        DifyRefreshTokenResponseDTO.TokenData tokenData = refreshResponse.getTokenData();
                        if (tokenData != null) {
                            log.info("刷新令牌成功，返回新的访问令牌");
                            return Mono.just(tokenData.getAccessToken());
                        }
                    }
                    // 刷新失败
                    return Mono.error(new RuntimeException("刷新令牌失败"));
                })
                .onErrorResume(e -> {
                    log.warn("刷新令牌失败，尝试重新登录: {}", e.getMessage());

                    // 清除无效的刷新令牌
                    return Mono.fromRunnable(() -> stringRedisTemplate.delete(REDIS_REFRESH_TOKEN_KEY))
                            .then(handleRelogin());
                });
    }

    @Override
    public String handleUnauthorizedSync() {
        log.warn("同步处理 401 错误：访问令牌过期，尝试刷新令牌");

        try {
            DifyRefreshTokenResponseDTO refreshResponse = refreshToken().block();
            if (refreshResponse.isSuccess()) {
                DifyRefreshTokenResponseDTO.TokenData tokenData = refreshResponse.getTokenData();
                if (tokenData != null) {
                    log.info("刷新令牌成功，返回新的访问令牌");
                    return tokenData.getAccessToken();
                }
            }

            // 刷新失败，抛出异常进入 catch 块
            throw new RuntimeException("刷新令牌失败");

        } catch (Exception e) {
            log.warn("刷新令牌失败，尝试重新登录: {}", e.getMessage());

            // 清除无效的刷新令牌
            stringRedisTemplate.delete(REDIS_REFRESH_TOKEN_KEY);

            return handleReloginSync();
        }
    }

    @Override
    public Mono<String> handleRelogin() {
        log.warn("处理 600 错误或刷新令牌失败：清除旧令牌并重新登录");

        // 清除旧令牌并重新登录
        return clearTokens()
                .then(login())
                .map(DifyLoginResponseDTO::getAccessToken);
    }

    @Override
    public String handleReloginSync() {
        log.warn("同步处理 600 错误或刷新令牌失败：清除旧令牌并重新登录");

        // 清除旧令牌
        clearTokens().block();

        // 重新登录
        DifyLoginResponseDTO loginResponse = login().block();
        return loginResponse.getAccessToken();
    }
}
