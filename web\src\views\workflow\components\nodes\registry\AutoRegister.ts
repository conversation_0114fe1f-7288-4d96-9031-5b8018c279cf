/**
 * 节点自动注册系统
 * 自动扫描和注册所有节点组件
 */

import { NodeRegistry } from './NodeRegistry'
import { NODE_LIBRARY_CONFIG } from '../../../config/nodeLibrary'

// 基础节点组件
import StartNode from '../basic/StartNode.vue'
import EndNode from '../basic/EndNode.vue'
import ConditionNode from '../basic/ConditionNode.vue'

/**
 * 注册基础节点类别
 */
function registerBasicCategory() {
  NodeRegistry.registerCategory({
    name: '基础节点',
    icon: 'fa-solid fa-cogs',
    description: '工作流的基础控制节点',
    order: 1
  })
}

/**
 * 注册数据库节点类别
 */
function registerDatabaseCategory() {
  NodeRegistry.registerCategory({
    name: '数据库工具',
    icon: 'fa-solid fa-database',
    description: '各种数据库操作工具',
    order: 2
  })
}

/**
 * 注册AI节点类别
 */
function registerAICategory() {
  NodeRegistry.registerCategory({
    name: 'AI工具',
    icon: 'fa-solid fa-brain',
    description: '各种AI模型和智能处理工具',
    order: 3
  })
}

/**
 * 注册文件生成节点类别
 */
function registerFileGeneratorCategory() {
  NodeRegistry.registerCategory({
    name: '文件生成工具',
    icon: 'fa-solid fa-file-plus',
    description: '生成各种格式的文件',
    order: 4
  })
}

/**
 * 注册文件提取节点类别
 */
function registerFileExtractorCategory() {
  NodeRegistry.registerCategory({
    name: '文件提取工具',
    icon: 'fa-solid fa-file-export',
    description: '从各种格式的文件中提取数据',
    order: 5
  })
}

/**
 * 注册渲染节点类别
 */
function registerRenderCategory() {
  NodeRegistry.registerCategory({
    name: '渲染工具',
    icon: 'fa-solid fa-palette',
    description: '数据可视化和渲染工具',
    order: 6
  })
}

/**
 * 注册数据节点类别
 */
function registerDataCategory() {
  NodeRegistry.registerCategory({
    name: '数据工具',
    icon: 'fa-solid fa-chart-bar',
    description: '数据处理和转换工具',
    order: 7
  })
}

/**
 * 注册工具节点类别
 */
function registerUtilityCategory() {
  NodeRegistry.registerCategory({
    name: '其它工具',
    icon: 'fa-solid fa-tools',
    description: '其他实用工具',
    order: 8
  })
}

/**
 * 注册基础节点
 */
function registerBasicNodes() {
  // 注册开始节点
  const startConfig = NODE_LIBRARY_CONFIG.categories
    .find(cat => cat.name === '基础节点')?.nodes
    .find(node => node.type === 'start')
  
  if (startConfig) {
    NodeRegistry.registerNode({
      type: 'start',
      category: '基础节点',
      component: StartNode,
      config: startConfig
    })
  }

  // 注册结束节点
  const endConfig = NODE_LIBRARY_CONFIG.categories
    .find(cat => cat.name === '基础节点')?.nodes
    .find(node => node.type === 'end')
  
  if (endConfig) {
    NodeRegistry.registerNode({
      type: 'end',
      category: '基础节点',
      component: EndNode,
      config: endConfig
    })
  }

  // 注册条件节点
  const conditionConfig = NODE_LIBRARY_CONFIG.categories
    .find(cat => cat.name === '基础节点')?.nodes
    .find(node => node.type === 'condition')
  
  if (conditionConfig) {
    NodeRegistry.registerNode({
      type: 'condition',
      category: '基础节点',
      component: ConditionNode,
      config: conditionConfig
    })
  }
}

/**
 * 动态导入和注册节点组件
 */
async function registerNodesByCategory(categoryName: string, categoryPath: string) {
  try {
    // 这里可以根据需要动态导入组件
    // 由于Vite的限制，我们需要明确指定要导入的组件
    
    switch (categoryName) {
      case '数据库工具':
        // 动态导入数据库节点组件
        await registerDatabaseNodes()
        break
      case 'AI工具':
        // 动态导入AI节点组件
        await registerAINodes()
        break
      case '文件生成工具':
        // 动态导入文件生成节点组件
        await registerFileGeneratorNodes()
        break
      case '文件提取工具':
        // 动态导入文件提取节点组件
        await registerFileExtractorNodes()
        break
      case '渲染工具':
        // 动态导入渲染节点组件
        await registerRenderNodes()
        break
      case '数据工具':
        // 动态导入数据节点组件
        await registerDataNodes()
        break
      case '其它工具':
        // 动态导入工具节点组件
        await registerUtilityNodes()
        break
    }
  } catch (error) {
    console.error(`Failed to register nodes for category ${categoryName}:`, error)
  }
}

/**
 * 注册数据库节点（占位符，实际实现时需要导入具体组件）
 */
async function registerDatabaseNodes() {
  // TODO: 动态导入数据库节点组件
  console.log('Registering database nodes...')
}

/**
 * 注册AI节点（占位符，实际实现时需要导入具体组件）
 */
async function registerAINodes() {
  // TODO: 动态导入AI节点组件
  console.log('Registering AI nodes...')
}

/**
 * 注册文件生成节点（占位符，实际实现时需要导入具体组件）
 */
async function registerFileGeneratorNodes() {
  // TODO: 动态导入文件生成节点组件
  console.log('Registering file generator nodes...')
}

/**
 * 注册文件提取节点（占位符，实际实现时需要导入具体组件）
 */
async function registerFileExtractorNodes() {
  // TODO: 动态导入文件提取节点组件
  console.log('Registering file extractor nodes...')
}

/**
 * 注册渲染节点（占位符，实际实现时需要导入具体组件）
 */
async function registerRenderNodes() {
  // TODO: 动态导入渲染节点组件
  console.log('Registering render nodes...')
}

/**
 * 注册数据节点（占位符，实际实现时需要导入具体组件）
 */
async function registerDataNodes() {
  // TODO: 动态导入数据节点组件
  console.log('Registering data nodes...')
}

/**
 * 注册工具节点（占位符，实际实现时需要导入具体组件）
 */
async function registerUtilityNodes() {
  // TODO: 动态导入工具节点组件
  console.log('Registering utility nodes...')
}

/**
 * 自动注册所有节点
 */
export async function autoRegisterNodes() {
  try {
    console.log('Starting auto-registration of nodes...')

    // 1. 注册所有类别
    registerBasicCategory()
    registerDatabaseCategory()
    registerAICategory()
    registerFileGeneratorCategory()
    registerFileExtractorCategory()
    registerRenderCategory()
    registerDataCategory()
    registerUtilityCategory()

    // 2. 注册基础节点
    registerBasicNodes()

    // 3. 注册其他类别的节点
    const categories = NODE_LIBRARY_CONFIG.categories
    for (const category of categories) {
      if (category.name !== '基础节点') {
        await registerNodesByCategory(category.name, category.name)
      }
    }

    console.log('Auto-registration completed successfully')
    console.log('Registry stats:', NodeRegistry.getStats())
  } catch (error) {
    console.error('Failed to auto-register nodes:', error)
    throw error
  }
}

/**
 * 手动注册单个节点
 */
export function registerSingleNode(type: string, category: string, component: any, config: any) {
  try {
    NodeRegistry.registerNode({
      type,
      category,
      component,
      config
    })
    console.log(`Successfully registered node: ${type}`)
  } catch (error) {
    console.error(`Failed to register node ${type}:`, error)
    throw error
  }
}

/**
 * 获取已注册的节点统计信息
 */
export function getRegistrationStats() {
  return NodeRegistry.getStats()
}

/**
 * 检查节点是否已注册
 */
export function isNodeRegistered(type: string): boolean {
  return NodeRegistry.hasNode(type)
}

/**
 * 获取所有已注册的节点类型
 */
export function getRegisteredNodeTypes(): string[] {
  return NodeRegistry.getAllNodeTypes()
}

/**
 * 获取指定类别的节点类型
 */
export function getNodeTypesByCategory(category: string): string[] {
  return NodeRegistry.getNodesByCategory(category)
}
