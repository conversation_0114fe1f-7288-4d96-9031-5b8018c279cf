<script setup lang="ts">
import {ref, watch, onMounted, computed} from 'vue'
import type { UploadedFile } from '@/types/rag'
import { formatDate, formatFileSize } from '@/utils/fileUtils'
import { useRagConfigStore } from '@/stores/ragConfigStore'
import FileSegmentPreview from '@/views/rag/datasource/file/FileSegmentPreview.vue'
import { RagAPI } from '@/api/rag'
import { ElMessage, ElMessageBox } from 'element-plus'

// 统一状态管理
const configStore = useRagConfigStore()
const showFilePreview = ref(false)
const currentPreviewFile = ref<any>(null)
// 文件列表分页显示
const currentPage = ref(1)
// 分页控制
const goToPage = (page: number) => {
  if (page >= 1 && page <= configStore.getTotalPages) {
    currentPage.value = page
  }
}

const nextPage = () => {
  if (currentPage.value < configStore.getTotalPages) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const closeFilePreview = () => {
  showFilePreview.value = false
  currentPreviewFile.value = null
}

// 文件相关方法
const openFilePreview = async (file: UploadedFile) => {
  currentPreviewFile.value = file
  showFilePreview.value = true
}

// 分段配置
const toSegmentSetting = async (file: UploadedFile) => {
  // 跳转到分段配置页面
  await configStore.setActiveFile(file.id)
}
const onFileConfigUpdated = (fileId: string, config: any) => {
  // 通过事件总线更新文件特定配置
  // eventBus.handleConfigChange(config, 'TextProcessStep', fileId)
}

// 监听文件数量变化，自动调整分页
watch(() => configStore.uploadedFiles.length, () => {
  // 如果当前页超出了总页数，重置到第一页
  if (currentPage.value > configStore.getTotalPages && configStore.getTotalPages > 0) {
    currentPage.value = 1
  }
})

const clearAllFiles = () => {
  // 清空所有文件
  // configStore.clearAllFiles()
}

// 处理文件移除
const handleRemoveFile = async (file: UploadedFile) => {
  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要移除文件 "${file.name}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 判断文件是否已存在于 documents 表中
    if (file.documentId && file.uploaded) {
      // 文件已上传到 documents 表，需要删除数据库记录和 MinIO 中的文件
      try {
        const response = await RagAPI.deleteDocument(file.documentId)
        if (response.success) {
          ElMessage.success('文件删除成功')

          // 记录删除操作到 upload_files 表
          await recordFileDeleteOperation(file, '用户主动删除')

          // 从本地缓存中移除
          configStore.removeFile(file.id)
        } else {
          ElMessage.error('删除文件失败：' + response.message)
        }
      } catch (error) {
        console.error('删除文件失败:', error)
        ElMessage.error('删除文件失败')
      }
    } else {
      // 文件未上传到 documents 表，只需从缓存中移除
      configStore.removeFile(file.id)
      ElMessage.success('文件移除成功')
    }
  } catch (error) {
    // 用户取消删除
    if (error !== 'cancel') {
      console.error('移除文件时发生错误:', error)
    }
  }
}

// 记录文件删除操作
const recordFileDeleteOperation = async (file: UploadedFile, reason: string) => {
  try {
    // 调用API记录删除操作
    await RagAPI.recordFileDeleteOperation({
      originalFilename: file.name,
      fileSize: file.fileSize,
      fileExtension: file.docType,
      mimeType: file.mimeType || '',
      fileHash: file.fileHash || '',
      datasetId: configStore.datasetId,
      batchId: configStore.batchId,
      documentId: file.documentId,
      minioUrl: file.minioUrl || '',
      deleteReason: reason
    })
  } catch (error) {
    console.error('记录删除操作失败:', error)
    // 不影响主流程，只记录日志
  }
}

</script>

<template>
  <div>
    <!-- 文件列表 -->
    <div class="file-list">
      <div class="file-list-header">
        <div class="summary-info">
          <i class="fas fa-file"></i>
          <span class="pl-2">已选择 {{ configStore.uploadedFiles.length }} 个文件</span>
          <span class="total-size">({{ formatFileSize(configStore.getTotalSize) }})</span>
        </div>
        <!-- 分页控制 -->
        <div v-if="configStore.getTotalPages > 1" class="pagination-controls">
          <button
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="prevPage"
          >
            <i class="fas fa-chevron-left"></i>
            上一页
          </button>

          <div class="page-numbers">
            <button
              v-for="page in Math.min(5, configStore.getTotalPages)"
              :key="page"
              class="page-number"
              :class="{ active: page === currentPage }"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
            <span v-if="configStore.getTotalPages > 5">...</span>
            <button
              v-if="configStore.getTotalPages > 5 && currentPage < configStore.getTotalPages - 2"
              class="page-number"
              @click="goToPage(configStore.getTotalPages)"
            >
              {{ configStore.getTotalPages }}
            </button>
          </div>

          <button
            class="pagination-btn"
            :disabled="currentPage === configStore.getTotalPages"
            @click="nextPage"
          >
            下一页
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        <div class="header-actions">
          <!-- 上传统计 -->
          <div class="upload-stats-inline">
            <div class="stats-item-inline">
              <span class="stats-label">已上传:</span>
              <span class="stats-value">{{ configStore.uploadedCount }}</span>
            </div>
            <div class="stats-item-inline pending">
              <span class="stats-label">待上传:</span>
              <span class="stats-value">{{ configStore.pendingFiles.length }}</span>
            </div>
            <div class="stats-item-inline uploading">
              <span class="stats-label">上传中:</span>
              <span class="stats-value">{{ configStore.uploadingCount }}</span>
            </div>
            <div class="stats-item-inline error">
              <span class="stats-label">失败:</span>
              <span class="stats-value">{{ configStore.uploadStats.failed }}</span>
            </div>
          </div>
          <button class="clear-all-btn" @click="clearAllFiles">
            <i class="fas fa-trash"></i>
            清空所有
          </button>
        </div>
      </div>

      <div class="file-items">
        <div
          v-for="file in configStore.uploadedFiles"
          :key="file.id"
          class="file-item"
          :class="{ 'upload-error': file.error }"
        >
          <div class="file-icon">
            {{configStore.getFileTypeIcon(file)}}
          </div>

          <div class="file-info">
            <div class="file-name" :title="file.name">
              {{ file.name }}
              <span v-if="file.isModified" class="modified-badge">
                <i class="fas fa-edit"></i>
                修改文件
              </span>
            </div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
              <span class="file-type">{{ file.docType.toUpperCase() }}</span>
              <span class="word-count">
                <i class="fas fa-file-text"></i>
                {{ file.wordCount }} 字符
              </span>
              <span v-if="file.uploadTime" class="upload-time">
                {{ formatDate(file.uploadTime) }}
              </span>
              <button
                  class="action-btn preview-btn"
                  @click="toSegmentSetting(file)"
                  title="分段配置"
              >
                <span>
                  {{file.segmentConfig && file.segmentConfig[file.segmentConfig.type] ? file.segmentConfig[file.segmentConfig.type].name : '未设置'}}
                </span>
              </button>
            </div>
            <div v-if="file.error" class="file-error">
              <i class="fas fa-exclamation-triangle"></i>
              {{ file.error }}
            </div>
          </div>

          <div class="file-status">
            <div v-if="file.uploading" class="upload-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: file.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ file.progress }}%</span>
            </div>
            <span v-if="file.documentStatus" class="py-1 px-2 text-sm rounded-md" :style="{background: configStore.getDocumentStatusClass(file.documentStatus)}">
                {{ configStore.getDocumentStatusText(file.documentStatus) }}
            </span>
          </div>

          <div class="file-actions">
            <button
              class="action-btn bg-gray-100"
              title="预览"
              @click="openFilePreview(file)"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button
              class="action-btn remove-btn"
              @click="handleRemoveFile(file)"
              title="移除"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件分段预览组件 -->
    <FileSegmentPreview
      :is-open="showFilePreview"
      :current-file="currentPreviewFile"
      @close="closeFilePreview"
      @config-updated="onFileConfigUpdated"
    />
  </div>
</template>

<style scoped>
/* 文件列表 */
.file-list {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.file-list-header {
  display: flex;
  height: 44px;
  align-items: center;
  justify-content: space-between;
  padding: 5px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-stats-inline {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stats-item-inline {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.stats-item-inline .stats-label {
  font-size: 12px;
  color: #6b7280;
}

.stats-item-inline .stats-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.progress-details span {
  padding: 2px 6px;
  background: #f9fafb;
  border-radius: 3px;
}

/* 统计项状态样式 */
.stats-item-inline.error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.stats-item-inline.pending {
  background: #fef3c7;
  border-color: #fbbf24;
  color: #d97706;
}

.stats-item-inline.uploading {
  background: #dbeafe;
  border-color: #93c5fd;
  color: #2563eb;
}


/* 分页控制样式 */
.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f9fafb;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-number {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  color: #374151;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.page-number.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.file-list-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.clear-all-btn {
  padding: 6px 12px;
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-all-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.file-items {
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.file-item:hover {
  background: #f9fafb;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item.upload-error {
  background: #fef2f2;
  border-color: #fecaca;
}

.file-icon {
  font-size: 24px;
  color: #6b7280;
  width: 32px;
  text-align: center;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modified-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: #fef3c7;
  color: #d97706;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid #fbbf24;
  flex-shrink: 0;
}

.modified-badge i {
  font-size: 10px;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.file-meta span {
  padding: 2px 6px;
  background: #f3f4f6;
  border-radius: 3px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.word-count {
  background: #d1fae5 !important;
  color: #059669 !important;
}

.file-error {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

.file-status {
  min-width: 120px;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar {
  width: 100px;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}


.file-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  height: 28px;
  padding: 0 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.preview-btn {
  background: #eff6ff;
  color: #3b82f6;
}

.preview-btn:hover:not(:disabled) {
  background: #dbeafe;
}

.preview-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.remove-btn {
  background: #fef2f2;
  color: #dc2626;
}

.remove-btn:hover {
  background: #fee2e2;
}


.preview-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.preview-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-stats-inline {
    justify-content: center;
  }

  .file-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .file-actions {
    justify-content: center;
  }
}
</style>