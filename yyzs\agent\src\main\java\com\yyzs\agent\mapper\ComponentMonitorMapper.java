package com.yyzs.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yyzs.agent.entity.ComponentMonitor;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 组件监控数据Mapper接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface ComponentMonitorMapper extends BaseMapper<ComponentMonitor> {

    /**
     * 根据组件ID查询最新监控数据
     */
    @Select("SELECT * FROM component_monitor WHERE component_id = #{componentId} AND deleted = 0 ORDER BY create_time DESC LIMIT 1")
    ComponentMonitor findLatestByComponentId(@Param("componentId") String componentId);

    /**
     * 根据组件ID查询监控历史数据
     */
    @Select("SELECT * FROM component_monitor WHERE component_id = #{componentId} AND deleted = 0 " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} ORDER BY create_time DESC")
    List<ComponentMonitor> findHistoryByComponentId(@Param("componentId") String componentId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询所有组件的最新监控数据
     */
    @Select("SELECT cm.* FROM component_monitor cm " +
            "INNER JOIN (" +
            "    SELECT component_id, MAX(create_time) as max_time " +
            "    FROM component_monitor " +
            "    WHERE deleted = 0 " +
            "    GROUP BY component_id" +
            ") latest ON cm.component_id = latest.component_id AND cm.create_time = latest.max_time " +
            "WHERE cm.deleted = 0")
    List<ComponentMonitor> findAllLatestMonitorData();

    /**
     * 查询不健康的组件监控数据
     */
    @Select("SELECT cm.* FROM component_monitor cm " +
            "INNER JOIN (" +
            "    SELECT component_id, MAX(create_time) as max_time " +
            "    FROM component_monitor " +
            "    WHERE deleted = 0 " +
            "    GROUP BY component_id" +
            ") latest ON cm.component_id = latest.component_id AND cm.create_time = latest.max_time " +
            "WHERE cm.deleted = 0 AND cm.is_healthy = false")
    List<ComponentMonitor> findUnhealthyComponents();

    /**
     * 删除指定时间之前的监控数据
     */
    @Delete("DELETE FROM component_monitor WHERE create_time < #{beforeTime}")
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 批量插入监控数据
     */
    @Insert("<script>" +
            "INSERT INTO component_monitor (" +
            "id, component_id, cpu_usage, memory_usage, memory_usage_percent, " +
            "disk_usage, disk_usage_percent, network_in, network_out, " +
            "process_status, process_id, thread_count, file_descriptor_count, " +
            "uptime, response_time, is_healthy, health_message, error_message, " +
            "create_time, update_time, create_by, update_by, deleted" +
            ") VALUES " +
            "<foreach collection='monitors' item='monitor' separator=','>" +
            "(" +
            "#{monitor.id}, #{monitor.componentId}, #{monitor.cpuUsage}, " +
            "#{monitor.memoryUsage}, #{monitor.memoryUsagePercent}, " +
            "#{monitor.diskUsage}, #{monitor.diskUsagePercent}, " +
            "#{monitor.networkIn}, #{monitor.networkOut}, " +
            "#{monitor.processStatus}, #{monitor.processId}, " +
            "#{monitor.threadCount}, #{monitor.fileDescriptorCount}, " +
            "#{monitor.uptime}, #{monitor.responseTime}, " +
            "#{monitor.isHealthy}, #{monitor.healthMessage}, #{monitor.errorMessage}, " +
            "#{monitor.createTime}, #{monitor.updateTime}, " +
            "#{monitor.createBy}, #{monitor.updateBy}, #{monitor.deleted}" +
            ")" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("monitors") List<ComponentMonitor> monitors);

    /**
     * 统计组件监控数据数量
     */
    @Select("SELECT COUNT(*) FROM component_monitor WHERE component_id = #{componentId} AND deleted = 0")
    long countByComponentId(@Param("componentId") String componentId);

    /**
     * 查询组件平均CPU使用率
     */
    @Select("SELECT AVG(cpu_usage) FROM component_monitor " +
            "WHERE component_id = #{componentId} AND deleted = 0 " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Double getAverageCpuUsage(@Param("componentId") String componentId,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查询组件平均内存使用率
     */
    @Select("SELECT AVG(memory_usage_percent) FROM component_monitor " +
            "WHERE component_id = #{componentId} AND deleted = 0 " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Double getAverageMemoryUsage(@Param("componentId") String componentId,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询组件最大CPU使用率
     */
    @Select("SELECT MAX(cpu_usage) FROM component_monitor " +
            "WHERE component_id = #{componentId} AND deleted = 0 " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Double getMaxCpuUsage(@Param("componentId") String componentId,
                         @Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询组件最大内存使用率
     */
    @Select("SELECT MAX(memory_usage_percent) FROM component_monitor " +
            "WHERE component_id = #{componentId} AND deleted = 0 " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Double getMaxMemoryUsage(@Param("componentId") String componentId,
                            @Param("startTime") LocalDateTime startTime,
                            @Param("endTime") LocalDateTime endTime);
}
