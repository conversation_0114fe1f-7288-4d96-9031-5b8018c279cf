<template>
  <BaseNode
    :type="data.type"
    :label="data.label"
    :icon="nodeConfig.icon"
    :color="nodeConfig.color"
    :gradient="nodeConfig.gradient"
    :label-color="nodeConfig.labelColor"
    :icon-color="nodeConfig.iconColor"
    :selected="selected"
    :status="data.status"
    :error-message="data.errorMessage"
    :source-handles="nodeConfig.handles.source"
    :target-handles="nodeConfig.handles.target"
    @configure="handleConfigure"
    @delete="handleDelete"
  >
    <!-- 向量化节点特定内容 -->
    <div class="vectorize-content">
      <div class="config-summary">
        <div class="config-item">
          <i class="fas fa-brain"></i>
          <span>模型: {{ getModelDisplay() }}</span>
        </div>
        <div class="config-item">
          <i class="fas fa-layer-group"></i>
          <span>维度: {{ data.config.dimensions || 1536 }}</span>
        </div>
        <div class="config-item">
          <i class="fas fa-boxes"></i>
          <span>批次大小: {{ data.config.batchSize || 100 }}</span>
        </div>
      </div>
      
      <div v-if="data.stats" class="stats">
        <div class="stat-item">
          <span class="stat-label">输入分段</span>
          <span class="stat-value">{{ data.stats.inputSegments || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">已向量化</span>
          <span class="stat-value">{{ data.stats.vectorizedCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">处理进度</span>
          <span class="stat-value">{{ data.stats.progress || 0 }}%</span>
        </div>
      </div>

      <!-- 进度条 -->
      <div v-if="data.status === 'running' && data.stats?.progress" class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: `${data.stats.progress}%` }"
        ></div>
      </div>

      <!-- 性能指标 -->
      <div v-if="data.performance" class="performance">
        <div class="perf-item">
          <i class="fas fa-clock"></i>
          <span>{{ data.performance.avgTime }}ms/段</span>
        </div>
        <div class="perf-item">
          <i class="fas fa-tachometer-alt"></i>
          <span>{{ data.performance.throughput }}/s</span>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseNode from './BaseNode.vue'
import { NODE_LIBRARY_CONFIG } from '../config/nodeLibrary'
import type { NodeProps } from '@vue-flow/core'

// Props
interface VectorizeNodeData {
  type: string
  label: string
  config: {
    model?: string
    dimensions?: number
    batchSize?: number
  }
  status?: 'idle' | 'running' | 'success' | 'error'
  errorMessage?: string
  stats?: {
    inputSegments: number
    vectorizedCount: number
    progress: number
  }
  performance?: {
    avgTime: number
    throughput: number
  }
}

const props = defineProps<NodeProps<VectorizeNodeData>>()

// Emits
const emit = defineEmits<{
  'configure': [nodeId: string]
  'delete': [nodeId: string]
}>()

// 计算属性
const nodeConfig = computed(() => {
  for (const category of NODE_LIBRARY_CONFIG.categories) {
    const node = category.nodes.find(n => n.type === props.data.type)
    if (node) return node
  }
  // 默认配置
  return {
    icon: 'fas fa-vector-square',
    color: '#f59e0b',
    gradient: 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',
    labelColor: '#92400e',
    iconColor: '#f59e0b',
    handles: {
      source: [{ id: 'output', position: 'right' }],
      target: [{ id: 'input', position: 'left' }]
    }
  }
})

// 方法
const handleConfigure = () => {
  emit('configure', props.id)
}

const handleDelete = () => {
  emit('delete', props.id)
}

const getModelDisplay = () => {
  const model = props.data.config.model || 'text-embedding-ada-002'
  // 简化模型名称显示
  if (model.includes('ada-002')) return 'Ada-002'
  if (model.includes('text-embedding-3-small')) return 'Embedding-3-Small'
  if (model.includes('text-embedding-3-large')) return 'Embedding-3-Large'
  return model.length > 20 ? model.substring(0, 20) + '...' : model
}
</script>

<style scoped>
.vectorize-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-summary {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.config-item i {
  width: 14px;
  text-align: center;
}

.stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}

.progress-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #d97706);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.performance {
  display: flex;
  gap: 16px;
  padding: 6px 8px;
  background: #fffbeb;
  border-radius: 4px;
  border: 1px solid #fef3c7;
}

.perf-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #92400e;
}

.perf-item i {
  width: 12px;
  text-align: center;
}
</style>
