spring:
  # JPA配置 - 开发环境自动建表
  jpa:
    hibernate:
      ddl-auto: update  # 自动更新表结构
      naming:
        physical-strategy: com.xhcai.common.datasource.config.CustomPhysicalNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  # Security配置 - 开发环境禁用默认安全配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration

  # 动态数据源配置
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组，默认值即为 master
      primary: master
      # 严格模式，默认false. 设置为true后在未匹配到指定数据源时候会抛出异常
      strict: false
      # 数据源配置
      datasource:
        # 主数据源（系统核心数据）
        master:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${DB_HOST:**************}:${DB_PORT:5432}/${DB_NAME:dify}?currentSchema=${DB_SCHEMA:xhcai_plus}
          username: ${DB_USERNAME:postgres}
          password: ${DB_PASSWORD:XHC12345}
          # Druid连接池配置
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            # 监控配置
            web-stat-filter:
              enabled: ${DRUID_WEB_STAT_FILTER_ENABLED:true}
              url-pattern: ${DRUID_WEB_STAT_FILTER_URL_PATTERN:/*}
              exclusions: ${DRUID_WEB_STAT_FILTER_EXCLUSIONS:"*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"}
            stat-view-servlet:
              enabled: ${DRUID_STAT_VIEW_SERVLET_ENABLED:true}
              url-pattern: ${DRUID_STAT_VIEW_SERVLET_URL_PATTERN:/druid/*}
              reset-enable: ${DRUID_STAT_VIEW_SERVLET_RESET_ENABLE:false}
              login-username: ${DRUID_STAT_VIEW_SERVLET_USERNAME:admin}
              login-password: ${DRUID_STAT_VIEW_SERVLET_PASSWORD:admin123}
            filter:
              stat:
                enabled: true
                log-slow-sql: true
                slow-sql-millis: 2000
              wall:
                enabled: true
                config:
                  multi-statement-allow: true

        # AI数据源（AI相关数据）
        ai:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${AI_DB_HOST:**************}:${AI_DB_PORT:5432}/${AI_DB_NAME:dify}?currentSchema=${AI_DB_SCHEMA:xhcai_ai}
          username: ${AI_DB_USERNAME:postgres}
          password: ${AI_DB_PASSWORD:XHC12345}
          druid:
            initial-size: 3
            min-idle: 3
            max-active: 15
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            
        # dify数据源（dify相关数据）
        dify:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${DIFY_DB_HOST:**************}:${DIFY_DB_PORT:5432}/${DIFY_DB_NAME:dify}?currentSchema=${DIFY_DB_SCHEMA:public}
          username: ${DIFY_DB_USERNAME:postgres}
          password: ${DIFY_DB_PASSWORD:XHC12345}
          druid:
            initial-size: 3
            min-idle: 3
            max-active: 15
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20

        # 日志数据源（操作日志、系统日志等）
        log:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${LOG_DB_HOST:**************}:${LOG_DB_PORT:5432}/${LOG_DB_NAME:dify}?currentSchema=${LOG_DB_SCHEMA:xhcai_log}
          username: ${LOG_DB_USERNAME:postgres}
          password: ${LOG_DB_PASSWORD:XHC12345}
          druid:
            initial-size: 2
            min-idle: 2
            max-active: 10
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:**************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:XHC12345}
      database: ${REDIS_DATABASE:10}
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms

  # AI配置
  ai:
    # 嵌入模型提供商选择：openai 或 ollama
    embedding:
      provider: ${AI_EMBEDDING_PROVIDER:openai}

    openai:
      api-key: ${OPENAI_API_KEY:sk-test-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
          max-tokens: 4096
      embedding:
        options:
          model: text-embedding-ada-002

    ollama:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
          model: llama2
          temperature: 0.7
      embedding:
        options:
          model: nomic-embed-text

    # PgVector配置
    vectorstore:
      pgvector:
        index-type: HNSW
        distance-type: COSINE_DISTANCE
        dimensions: 1536  # OpenAI text-embedding-ada-002的维度

# Dify平台配置 - 开发环境
dify:
  base-url: ${DIFY_BASE_URL:http://**************}
  api-key: ${DIFY_API_KEY:app-HZWa3wyLD5KZVwbksobWe9nk}
  app-id: ${DIFY_APP_ID:}
  email: ${DIFY_EMAIL:<EMAIL>}
  password: ${DIFY_PASSWORD:XH12345@}
  language: ${DIFY_LANGUAGE:zh-Hans}
  connect-timeout: ${DIFY_CONNECT_TIMEOUT:10000}
  read-timeout: ${DIFY_READ_TIMEOUT:30000}
  ssl-enabled: ${DIFY_SSL_ENABLED:false}
  retry-count: ${DIFY_RETRY_COUNT:3}
  retry-interval: ${DIFY_RETRY_INTERVAL:1000}
  test-mode: ${DIFY_TEST_MODE:false}

  # 开发工具配置
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
      exclude: WEB-INF/**
    livereload:
      enabled: true
      port: 35729

# 日志配置
logging:
  level:
    root: info
    com.xhcai: debug
    com.xhcai.modules.dify: debug
    org.springframework.security: debug
    org.springframework.ai: debug
    org.springframework.web: debug
    org.springframework.web.reactive.function.client: debug
  file:
    name: ${LOG_FILE_PATH:logs/xhcai-plus-dev.log}
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

# 跨域配置
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:"*"}
  allowed-methods: ${CORS_ALLOWED_METHODS:"*"}
  allowed-headers: ${CORS_ALLOWED_HEADERS:"*"}
  allow-credentials: ${CORS_ALLOW_CREDENTIALS:false}
  max-age: ${CORS_MAX_AGE:3600}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        # 暴露所有监控端点
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      # 显示详细健康信息
      show-details: always
      # 显示组件信息
      show-components: always
  health:
    # 启用数据源健康检查
    datasource:
      enabled: true

# Swagger配置（开发环境启用）
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    # 禁用Try it out按钮的认证要求
    disable-swagger-default-url: true
    # 配置默认展开级别
    doc-expansion: none
    # 显示请求持续时间
    display-request-duration: true
    # 操作排序
    operations-sorter: alpha
    # 标签排序
    tags-sorter: alpha
  # 显示actuator端点
  show-actuator: true
  # 默认生成服务器URL
  default-produces-media-type: application/json
  default-consumes-media-type: application/json

# MyBatis Plus 配置
mybatis-plus:
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.xhcai.modules.*.entity
  # mapper xml 文件扫描
  mapper-locations: classpath*:mapper/**/*.xml
  # 类型处理器扫描
  type-handlers-package: com.xhcai.common.datasource.handler
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: false
    # 调用 setters 时，是否触发延迟加载对象的加载
    call-setters-on-nulls: true
    # JDBC类型为null时的处理
    jdbc-type-for-null: 'null'
    # 解决Spring Boot 3.x兼容性问题
    lazy-loading-enabled: false
    aggressive-lazy-loading: false

xhcai:
  plugin:
    # 插件根目录
    root-path: D:/ideaProject/xhcai-plus/admin-api/plugins
    # 是否开发模式
    dev-mode: true
    # 是否启用热插拔
    hot-swap-enabled: true
    # 插件扫描间隔（秒）
    scan-interval: 10
    # 插件启动超时时间（秒）
    start-timeout: 30
    # 插件停止超时时间（秒）
    stop-timeout: 10

    # 各插件类型的特定配置
    types:
      storage:
        enabled: true
        directory: storage
        max-plugins: 5
        config:
          # 存储类型
          type: ${XHCAI_STORAGE_TYPE:minio}
          # MinIO 配置示例
          minio:
            endpoint: ${XHCAI_STORAGE_MINIO_ENDPOINT:http://**************:20000}
            accessKey: ${XHCAI_STORAGE_MINIO_ACCESS_KEY:xh_minio}
            secretKey: ${XHCAI_STORAGE_MINIO_SECRET_KEY:Xinghuo_20250724}
            bucket: ${XHCAI_STORAGE_MINIO_BUCKETNAME:xhcai-plus}

      model:
        enabled: true
        directory: model
        max-plugins: 10
        config:
          # OpenAI 配置示例
          openai:
            api-key: ${OPENAI_API_KEY:your-api-key-here}
            base-url: https://api.openai.com/v1

      notification:
        enabled: true
        directory: notification
        max-plugins: 5
        config:
          # 邮件配置示例
          email:
            smtp-host: smtp.gmail.com
            smtp-port: 587
            username: ${EMAIL_USERNAME:}
            password: ${EMAIL_PASSWORD:}

      datasource:
        enabled: true
        directory: datasource
        max-plugins: 5
        config:
          # 数据源配置示例
          mysql:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: ********************************
            username: ${DB_USERNAME:root}
            password: ${DB_PASSWORD:password}

      queue:
        enabled: true
        directory: queue
        max-plugins: 5
        config:
          # 队列类型
          type: ${XHCAI_QUEUE_TYPE:rabbitmq}
          # RabbitMQ 配置
          host: ${XHCAI_QUEUE_RABBITMQ_HOST:**************}
          port: ${XHCAI_QUEUE_RABBITMQ_PORT:20002}
          username: ${XHCAI_QUEUE_RABBITMQ_USERNAME:xh_admin}
          password: ${XHCAI_QUEUE_RABBITMQ_PASSWORD:Xh_20250724}
          virtualHost: ${XHCAI_QUEUE_RABBITMQ_VIRTUAL_HOST:xhc_aiplus}
          connectionTimeout: ${XHCAI_QUEUE_RABBITMQ_CONNECTION_TIMEOUT:30000}
          requestedHeartbeat: ${XHCAI_QUEUE_RABBITMQ_HEARTBEAT:60}
          publisherConfirms: ${XHCAI_QUEUE_RABBITMQ_PUBLISHER_CONFIRMS:true}
          publisherReturns: ${XHCAI_QUEUE_RABBITMQ_PUBLISHER_RETURNS:true}
          mandatory: ${XHCAI_QUEUE_RABBITMQ_MANDATORY:true}
          acknowledgeMode: ${XHCAI_QUEUE_RABBITMQ_ACKNOWLEDGE_MODE:manual}
          concurrency: ${XHCAI_QUEUE_RABBITMQ_CONCURRENCY:1}
          maxConcurrency: ${XHCAI_QUEUE_RABBITMQ_MAX_CONCURRENCY:5}
          prefetchCount: ${XHCAI_QUEUE_RABBITMQ_PREFETCH_COUNT:1}
