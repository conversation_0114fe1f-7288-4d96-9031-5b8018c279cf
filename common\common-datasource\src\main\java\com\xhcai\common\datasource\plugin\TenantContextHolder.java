package com.xhcai.common.datasource.plugin;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 租户上下文持有者 用于在平台管理员操作时临时指定租户ID
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TenantContextHolder {

    private static final Logger log = LoggerFactory.getLogger(TenantContextHolder.class);

    private static final ThreadLocal<String> TENANT_ID_HOLDER = new ThreadLocal<>();

    /**
     * 设置当前线程的租户ID
     *
     * @param tenantId 租户ID
     */
    public static void setTenantId(String tenantId) {
        TENANT_ID_HOLDER.set(tenantId);
        log.debug("设置线程租户ID: {}", tenantId);
    }

    /**
     * 获取当前线程的租户ID
     *
     * @return 租户ID
     */
    public static String getTenantId() {
        return TENANT_ID_HOLDER.get();
    }

    /**
     * 清除当前线程的租户ID
     */
    public static void clear() {
        String tenantId = TENANT_ID_HOLDER.get();
        TENANT_ID_HOLDER.remove();
        log.debug("清除线程租户ID: {}", tenantId);
    }

    /**
     * 在指定租户上下文中执行操作
     *
     * @param tenantId 租户ID
     * @param runnable 要执行的操作
     */
    public static void runWithTenant(String tenantId, Runnable runnable) {
        String originalTenantId = getTenantId();
        try {
            setTenantId(tenantId);
            runnable.run();
        } finally {
            if (originalTenantId != null) {
                setTenantId(originalTenantId);
            } else {
                clear();
            }
        }
    }

    /**
     * 在指定租户上下文中执行操作并返回结果
     *
     * @param tenantId 租户ID
     * @param supplier 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T callWithTenant(String tenantId, java.util.function.Supplier<T> supplier) {
        String originalTenantId = getTenantId();
        try {
            setTenantId(tenantId);
            return supplier.get();
        } finally {
            if (originalTenantId != null) {
                setTenantId(originalTenantId);
            } else {
                clear();
            }
        }
    }
}
