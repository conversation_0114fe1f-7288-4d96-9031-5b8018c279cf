package com.xhcai.modules.dify.mapper;

import java.util.List;

import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformAccountQueryDTO;
import com.xhcai.modules.dify.entity.ThirdPlatformAccount;
import com.xhcai.modules.dify.vo.ThirdPlatformAccountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 用户第三方智能体账号Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface ThirdPlatformAccountMapper extends BaseMapper<ThirdPlatformAccount> {

    /**
     * 分页查询用户第三方智能体账号
     *
     * @param page 分页参数
     * @param query 查询条件
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 分页结果
     */
    @Select({
        "<script>",
        "SELECT ",
        "    a.id,",
        "    a.user_id,",
        "    a.platform_id,",
        "    d.dict_label as platform_name,",
        "    d.dict_value as platform_icon,",
        "    d.remark as platform_icon_bg,",
        "    d.remark as platform_description,",
        "    a.account_name,",
        "    CONCAT(LEFT(a.api_key, 8), '...', RIGHT(a.api_key, 8)) as api_key,",
        "    CASE WHEN a.pwd IS NOT NULL AND a.pwd != '' THEN true ELSE false END as has_pwd,",
        "    a.remark,",
        "    a.status,",
        "    CASE a.status WHEN 1 THEN '启用' ELSE '禁用' END as status_text,",
        "    a.last_test_time,",
        "    a.last_test_result,",
        "    CASE a.last_test_result WHEN 1 THEN '成功' WHEN 0 THEN '失败' ELSE '未测试' END as last_test_result_text,",
        "    a.last_test_error,",
        "    COALESCE(a.total_calls, 0) as total_calls,",
        "    COALESCE(a.success_calls, 0) as success_calls,",
        "    CASE WHEN COALESCE(a.total_calls, 0) > 0 THEN ROUND(COALESCE(a.success_calls, 0) * 100.0 / a.total_calls, 2) ELSE 0 END as success_rate,",
        "    a.avg_response_time,",
        "    a.last_used_time,",
        "    a.create_time,",
        "    a.update_time",
        "FROM third_platform_account a",
        "LEFT JOIN sys_dict_data d ON d.dict_type = 'third_platform' AND d.dict_value = a.platform_id AND d.deleted = 0",
        "WHERE a.deleted = 0",
        "    AND a.user_id = #{userId}",
        "    AND a.tenant_id = #{tenantId}",
        "    <if test='query.platformId != null and query.platformId != \"\"'>",
        "        AND a.platform_id = #{query.platformId}",
        "    </if>",
        "    <if test='query.accountName != null and query.accountName != \"\"'>",
        "        AND a.account_name LIKE CONCAT('%', #{query.accountName}, '%')",
        "    </if>",
        "    <if test='query.status != null'>",
        "        AND a.status = #{query.status}",
        "    </if>",
        "    <if test='query.lastTestResult != null'>",
        "        AND a.last_test_result = #{query.lastTestResult}",
        "    </if>",
        "    <if test='query.beginTime != null'>",
        "        AND a.create_time >= #{query.beginTime}",
        "    </if>",
        "    <if test='query.endTime != null'>",
        "        AND a.create_time &lt;= #{query.endTime}",
        "    </if>",
        "ORDER BY a.create_time DESC",
        "</script>"
    })
    IPage<ThirdPlatformAccountVO> selectPageVO(Page<ThirdPlatformAccountVO> page,
                                               @Param("query") ThirdPlatformAccountQueryDTO query,
                                               @Param("userId") String userId,
                                               @Param("tenantId") String tenantId);

    /**
     * 根据ID查询用户第三方智能体账号详情
     *
     * @param id 主键ID
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 账号详情
     */
    @Select({
        "SELECT ",
        "    a.id,",
        "    a.user_id,",
        "    a.platform_id,",
        "    d.dict_label as platform_name,",
        "    d.dict_value as platform_icon,",
        "    d.remark as platform_icon_bg,",
        "    d.remark as platform_description,",
        "    a.account_name,",
        "    a.api_key,",
        "    CASE WHEN a.pwd IS NOT NULL AND a.pwd != '' THEN true ELSE false END as has_pwd,",
        "    a.remark,",
        "    a.status,",
        "    CASE a.status WHEN 1 THEN '启用' ELSE '禁用' END as status_text,",
        "    a.last_test_time,",
        "    a.last_test_result,",
        "    CASE a.last_test_result WHEN 1 THEN '成功' WHEN 0 THEN '失败' ELSE '未测试' END as last_test_result_text,",
        "    a.last_test_error,",
        "    COALESCE(a.total_calls, 0) as total_calls,",
        "    COALESCE(a.success_calls, 0) as success_calls,",
        "    CASE WHEN COALESCE(a.total_calls, 0) > 0 THEN ROUND(COALESCE(a.success_calls, 0) * 100.0 / a.total_calls, 2) ELSE 0 END as success_rate,",
        "    a.avg_response_time,",
        "    a.last_used_time,",
        "    a.create_time,",
        "    a.update_time",
        "FROM third_platform_account a",
        "LEFT JOIN sys_dict_data d ON d.dict_type = 'third_platform' AND d.dict_value = a.platform_id AND d.deleted = 0",
        "WHERE a.deleted = 0",
        "    AND a.id = #{id}",
        "    AND a.user_id = #{userId}",
        "    AND a.tenant_id = #{tenantId}"
    })
    ThirdPlatformAccountVO selectVOById(@Param("id") String id,
                                        @Param("userId") String userId,
                                        @Param("tenantId") String tenantId);

    /**
     * 查询用户在指定平台的账号数量
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @param tenantId 租户ID
     * @return 账号数量
     */
    @Select({
        "SELECT COUNT(*) FROM third_platform_account",
        "WHERE deleted = 0",
        "    AND user_id = #{userId}",
        "    AND platform_id = #{platformId}",
        "    AND tenant_id = #{tenantId}"
    })
    int countByUserAndPlatform(@Param("userId") String userId,
            @Param("platformId") String platformId,
            @Param("tenantId") String tenantId);

    /**
     * 查询用户的所有第三方账号（用于统计）
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 账号列表
     */
    @Select({
        "SELECT ",
        "    a.id,",
        "    a.platform_id,",
        "    d.dict_label as platform_name,",
        "    a.account_name,",
        "    CASE WHEN a.pwd IS NOT NULL AND a.pwd != '' THEN true ELSE false END as has_pwd,",
        "    a.status,",
        "    a.last_test_result,",
        "    COALESCE(a.total_calls, 0) as total_calls,",
        "    COALESCE(a.success_calls, 0) as success_calls,",
        "    a.last_used_time",
        "FROM third_platform_account a",
        "LEFT JOIN sys_dict_data d ON d.dict_type = 'third_platform' AND d.dict_value = a.platform_id AND d.deleted = 0",
        "WHERE a.deleted = 0",
        "    AND a.user_id = #{userId}",
        "    AND a.tenant_id = #{tenantId}",
        "    AND a.status = 1",
        "ORDER BY a.last_used_time DESC"
    })
    List<ThirdPlatformAccountVO> selectByUserId(@Param("userId") String userId,
                                                @Param("tenantId") String tenantId);

    /**
     * 根据用户ID和平台ID查询账号实体
     *
     * @param userId 用户ID
     * @param platformId 平台ID
     * @param tenantId 租户ID
     * @return 账号实体
     */
    @Select({
        "SELECT * FROM third_platform_account",
        "WHERE deleted = 0",
        "    AND user_id = #{userId}",
        "    AND platform_id = #{platformId}",
        "    AND tenant_id = #{tenantId}",
        "    AND status = 1"
    })
    ThirdPlatformAccount selectByUserAndPlatform(@Param("userId") String userId,
                                                 @Param("platformId") String platformId,
                                                 @Param("tenantId") String tenantId);
}
