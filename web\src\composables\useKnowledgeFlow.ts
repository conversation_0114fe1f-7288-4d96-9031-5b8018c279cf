/**
 * 知识库创建流程统一状态管理 Composable
 * 提供便捷的方法来管理知识库创建流程的状态
 */

import { computed } from 'vue'
import { useRagConfigStore } from '@/stores/ragConfigStore'
import type { UploadedFile } from '@/types/rag'

export function useKnowledgeFlow() {
  const configStore = useRagConfigStore()

  // 计算属性
  const canProceedToNext = computed(() => {
    if (configStore.currentStep === 1) {
      return configStore.uploadedFiles.length > 0
    }
    if (configStore.currentStep === 2) {
      return configStore.processResult !== null
    }
    return false
  })

  const isFirstStep = computed(() => configStore.currentStep === 1)
  const isLastStep = computed(() => configStore.currentStep === 3)

  const nextStep = () => {
    if (configStore.currentStep < 3) {
      configStore.setCurrentStep(configStore.currentStep + 1)
    }
  }

  const previousStep = () => {
    if (configStore.currentStep > 1) {
      configStore.setCurrentStep(configStore.currentStep - 1)
    }
  }

  const addFiles = (files: UploadedFile[]) => {
    configStore.addUploadedFiles(files)
  }

  const updateFile = (fileId: string, updates: Partial<UploadedFile>) => {
    configStore.updateFileStatus(fileId, updates)
  }

  const removeFile = (fileId: string) => {
    configStore.removeFile(fileId)
  }

  const clearFiles = () => {
    configStore.clearAllFiles()
  }

  const setProcessResult = (result: any) => {
    configStore.setProcessResult(result)
  }

  const resetFlow = () => {
    configStore.resetKnowledgeFlow()
  }

  return {
    // 状态
    datasetId: computed(() => configStore.datasetId),
    datasetName: computed(() => configStore.datasetName),
    uploadedFiles: computed(() => configStore.uploadedFiles),
    currentStep: computed(() => configStore.currentStep),
    processResult: computed(() => configStore.processResult),
    
    // 计算属性
    canProceedToNext,
    isFirstStep,
    isLastStep,
    uploadedCount: computed(() => configStore.uploadedCount),
    uploadingCount: computed(() => configStore.uploadingCount),
    pendingFiles: computed(() => configStore.pendingFiles),
    allFilesProcessed: computed(() => configStore.allFilesProcessed),
    hasUploadingFiles: computed(() => configStore.hasUploadingFiles),
    
    // 方法
    nextStep,
    previousStep,
    addFiles,
    updateFile,
    removeFile,
    clearFiles,
    setProcessResult,
    resetFlow
  }
}
