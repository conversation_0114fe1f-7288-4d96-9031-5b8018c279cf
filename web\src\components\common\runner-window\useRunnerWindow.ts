import { ref, reactive, nextTick, type Ref } from 'vue'
import type { Component } from 'vue'
import type { RunnerWindowProps, HeaderInfo } from './types'

export interface RunnerWindowState {
  visible: boolean
  isMinimized: boolean
  isMaximized: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
}

export interface OpenRunnerWindowOptions {
  title?: string
  component?: Component | string
  componentProps?: Record<string, any>
  headerInfo?: HeaderInfo
  size?: { width: number; height: number }
  position?: { x: number; y: number }
  resizable?: boolean
  draggable?: boolean
  showControls?: boolean
  defaultFullscreen?: boolean
  enableTaskbar?: boolean
}

// 定义返回类型
export interface UseRunnerWindowReturn {
  windowState: RunnerWindowState
  windowConfig: Partial<RunnerWindowProps>
  minimizedWindows: Ref<Array<{
    id: string
    title: string
    icon?: string
    headerInfo?: HeaderInfo
  }>>
  openWindow: (options?: OpenRunnerWindowOptions) => Promise<void>
  closeWindow: () => void
  minimizeWindow: () => void
  restoreWindow: (windowId?: string) => void
  toggleMaximize: () => void
  updatePosition: (position: { x: number; y: number }) => void
  updateSize: (size: { width: number; height: number }) => void
  updateComponent: (component: Component | string, props?: Record<string, any>) => void
  updateHeaderInfo: (headerInfo: HeaderInfo) => void
  isWindowOpen: () => boolean
  getWindowProps: () => RunnerWindowProps
}

export default function useRunnerWindow(): UseRunnerWindowReturn {
  // 窗口状态管理
  const windowState = reactive<RunnerWindowState>({
    visible: false,
    isMinimized: false,
    isMaximized: false,
    position: { x: 100, y: 80 },
    size: { width: 1000, height: 700 }
  })

  // 窗口配置
  const windowConfig = reactive<Partial<RunnerWindowProps>>({
    title: '运行窗口',
    component: undefined,
    componentProps: {},
    headerInfo: undefined,
    resizable: true,
    draggable: true,
    showControls: true,
    defaultFullscreen: false,
    enableTaskbar: true
  })

  // 最小化窗口列表（用于任务栏显示）
  const minimizedWindows = ref<Array<{
    id: string
    title: string
    icon?: string
    headerInfo?: HeaderInfo
  }>>([])

  /**
   * 打开运行窗口
   */
  const openWindow = async (options: OpenRunnerWindowOptions = {}) => {
    // 更新配置
    Object.assign(windowConfig, options)

    // 检查是否需要全屏显示
    if (options.defaultFullscreen) {
      // 设置全屏大小和位置
      windowState.position = { x: 0, y: 0 }
      windowState.size = {
        width: window.innerWidth,
        height: window.innerHeight
      }
      windowState.isMaximized = true
    } else {
      // 设置默认位置（避免重叠）
      if (!options.position) {
        windowState.position = {
          x: 100 + Math.random() * 200,
          y: 80 + Math.random() * 100
        }
      } else {
        windowState.position = options.position
      }

      // 设置默认大小
      if (options.size) {
        windowState.size = options.size
      }

      windowState.isMaximized = false
    }

    // 显示窗口
    windowState.visible = true
    windowState.isMinimized = false

    await nextTick()
  }

  /**
   * 关闭运行窗口
   */
  const closeWindow = () => {
    windowState.visible = false
    windowState.isMinimized = false
    windowState.isMaximized = false
    
    // 清理配置
    windowConfig.component = undefined
    windowConfig.componentProps = {}
    windowConfig.headerInfo = undefined
  }

  /**
   * 最小化窗口
   */
  const minimizeWindow = () => {
    // 只有启用任务栏时才允许最小化
    if (!windowConfig.enableTaskbar) return

    windowState.isMinimized = true

    // 添加到最小化列表
    const windowId = Date.now().toString()
    minimizedWindows.value.push({
      id: windowId,
      title: windowConfig.title || '运行窗口',
      icon: windowConfig.headerInfo?.icon,
      headerInfo: windowConfig.headerInfo
    })
  }

  /**
   * 从最小化状态恢复窗口
   */
  const restoreWindow = (windowId?: string) => {
    windowState.isMinimized = false
    
    // 从最小化列表中移除
    if (windowId) {
      const index = minimizedWindows.value.findIndex(w => w.id === windowId)
      if (index > -1) {
        minimizedWindows.value.splice(index, 1)
      }
    }
  }

  /**
   * 最大化/还原窗口
   */
  const toggleMaximize = () => {
    windowState.isMaximized = !windowState.isMaximized
  }

  /**
   * 更新窗口位置
   */
  const updatePosition = (position: { x: number; y: number }) => {
    windowState.position = position
  }

  /**
   * 更新窗口大小
   */
  const updateSize = (size: { width: number; height: number }) => {
    windowState.size = size
  }

  /**
   * 更新组件
   */
  const updateComponent = (component: Component | string, props?: Record<string, any>) => {
    windowConfig.component = component
    if (props) {
      windowConfig.componentProps = props
    }
  }

  /**
   * 更新头部信息
   */
  const updateHeaderInfo = (headerInfo: HeaderInfo) => {
    windowConfig.headerInfo = headerInfo
  }

  /**
   * 检查窗口是否打开
   */
  const isWindowOpen = () => {
    return windowState.visible && !windowState.isMinimized
  }

  /**
   * 获取窗口完整配置
   */
  const getWindowProps = (): RunnerWindowProps => {
    return {
      visible: windowState.visible,
      isMinimized: windowState.isMinimized,
      isMaximized: windowState.isMaximized,
      position: windowState.position,
      size: windowState.size,
      ...windowConfig
    } as RunnerWindowProps
  }

  return {
    // 状态
    windowState,
    windowConfig,
    minimizedWindows,
    
    // 方法
    openWindow,
    closeWindow,
    minimizeWindow,
    restoreWindow,
    toggleMaximize,
    updatePosition,
    updateSize,
    updateComponent,
    updateHeaderInfo,
    isWindowOpen,
    getWindowProps
  }
}
