package com.xhcai.modules.dify.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.dto.DifyResponse;
import com.xhcai.modules.dify.dto.knowledge.DifyDocumentDTO;
import com.xhcai.modules.dify.dto.knowledge.DifyKnowledgeBaseDTO;
import com.xhcai.modules.dify.service.IDifyKnowledgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知识库服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class DifyKnowledgeServiceImpl implements IDifyKnowledgeService {

    private static final Logger log = LoggerFactory.getLogger(DifyKnowledgeServiceImpl.class);

    @Autowired
    @Qualifier("difyWebClient")
    private WebClient difyWebClient;

    @Autowired
    private DifyConfig difyConfig;

    @Override
    public Mono<Result<DifyKnowledgeBaseDTO>> createKnowledgeBase(DifyKnowledgeBaseDTO difyKnowledgeBaseDTO) {
        return difyWebClient.post()
                .uri("/v1/datasets")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(difyKnowledgeBaseDTO)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyKnowledgeBaseDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyKnowledgeBaseDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("创建知识库失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyKnowledgeBaseDTO>> updateKnowledgeBase(String knowledgeId, DifyKnowledgeBaseDTO difyKnowledgeBaseDTO) {
        return difyWebClient.patch()
                .uri("/v1/datasets/{datasetId}", knowledgeId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(difyKnowledgeBaseDTO)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyKnowledgeBaseDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyKnowledgeBaseDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("更新知识库失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> deleteKnowledgeBase(String knowledgeId) {
        return difyWebClient.delete()
                .uri("/v1/datasets/{datasetId}", knowledgeId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Void> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Void>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success();
                    } else {
                        throw new BusinessException("删除知识库失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyKnowledgeBaseDTO>> getKnowledgeBase(String knowledgeId) {
        return difyWebClient.get()
                .uri("/v1/datasets/{datasetId}", knowledgeId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyKnowledgeBaseDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyKnowledgeBaseDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取知识库失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<PageResult<DifyKnowledgeBaseDTO>>> getKnowledgeBaseList(int page, int size, String keyword, String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", size);
        if (StringUtils.hasText(keyword)) {
            params.put("keyword", keyword);
        }
        if (StringUtils.hasText(status)) {
            params.put("status", status);
        }

        return difyWebClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path("/v1/datasets");
                    params.forEach((key, value) -> uriBuilder.queryParam(key, value));
                    return uriBuilder.build();
                })
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<PageResult<DifyKnowledgeBaseDTO>> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<PageResult<DifyKnowledgeBaseDTO>>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取知识库列表失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyDocumentDTO>> updateDocument(String knowledgeId, String documentId, DifyDocumentDTO difyDocumentDTO) {
        return difyWebClient.patch()
                .uri("/v1/datasets/{datasetId}/documents/{documentId}", knowledgeId, documentId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(difyDocumentDTO)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyDocumentDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyDocumentDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("更新文档失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> deleteDocument(String knowledgeId, String documentId) {
        return difyWebClient.delete()
                .uri("/v1/datasets/{datasetId}/documents/{documentId}", knowledgeId, documentId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Void> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Void>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success();
                    } else {
                        throw new BusinessException("删除文档失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyDocumentDTO>> getDocument(String knowledgeId, String documentId) {
        return difyWebClient.get()
                .uri("/v1/datasets/{datasetId}/documents/{documentId}", knowledgeId, documentId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<DifyDocumentDTO> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<DifyDocumentDTO>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取文档失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<PageResult<DifyDocumentDTO>>> getDocumentList(String knowledgeId, int page, int size, String keyword, String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", size);
        if (StringUtils.hasText(keyword)) {
            params.put("keyword", keyword);
        }
        if (StringUtils.hasText(status)) {
            params.put("status", status);
        }

        return difyWebClient.get()
                .uri(uriBuilder -> {
                    uriBuilder.path("/v1/datasets/{datasetId}/documents").build(knowledgeId);
                    params.forEach((key, value) -> uriBuilder.queryParam(key, value));
                    return uriBuilder.build();
                })
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<PageResult<DifyDocumentDTO>> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<PageResult<DifyDocumentDTO>>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取文档列表失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> reprocessDocument(String knowledgeId, String documentId) {
        return difyWebClient.patch()
                .uri("/v1/datasets/{datasetId}/documents/{documentId}/processing", knowledgeId, documentId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Void> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Void>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success();
                    } else {
                        throw new BusinessException("重新处理文档失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> getDocumentProcessStatus(String knowledgeId, String documentId) {
        return difyWebClient.get()
                .uri("/v1/datasets/{datasetId}/documents/{documentId}/indexing-status", knowledgeId, documentId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Object> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Object>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取文档处理状态失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<List<Object>>> searchKnowledge(String knowledgeId, String query, Integer topK, Double scoreThreshold) {
        Map<String, Object> request = new HashMap<>();
        request.put("query", query);
        if (topK != null) {
            request.put("top_k", topK);
        }
        if (scoreThreshold != null) {
            request.put("score_threshold", scoreThreshold);
        }

        return difyWebClient.post()
                .uri("/v1/datasets/{datasetId}/retrieve", knowledgeId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<List<Object>> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<List<Object>>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("知识库检索失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> batchDeleteDocuments(String knowledgeId, List<String> documentIds) {
        Map<String, Object> request = new HashMap<>();
        request.put("document_ids", documentIds);

        return difyWebClient.delete()
                .uri("/v1/datasets/{datasetId}/documents", knowledgeId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Void> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Void>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success();
                    } else {
                        throw new BusinessException("批量删除文档失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<Object>> getKnowledgeStats(String knowledgeId) {
        return difyWebClient.get()
                .uri("/v1/datasets/{datasetId}/stats", knowledgeId)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    DifyResponse<Object> difyResponse = JSON.parseObject(response, new TypeReference<DifyResponse<Object>>() {});
                    if (difyResponse.isSuccess()) {
                        return Result.success(difyResponse.getData());
                    } else {
                        throw new BusinessException("获取知识库统计信息失败: " + difyResponse.getMessage());
                    }
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    /**
     * 统一错误处理
     */
    private <T> Mono<Result<T>> handleError(Throwable throwable) {
        log.error("Dify Knowledge API调用失败", throwable);
        
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            HttpStatus status = (HttpStatus) ex.getStatusCode();
            String message = "API调用失败: " + status.getReasonPhrase();
            
            if (status == HttpStatus.UNAUTHORIZED) {
                message = "API密钥无效或已过期";
            } else if (status == HttpStatus.FORBIDDEN) {
                message = "没有权限访问该资源";
            } else if (status == HttpStatus.NOT_FOUND) {
                message = "请求的资源不存在";
            } else if (status == HttpStatus.TOO_MANY_REQUESTS) {
                message = "请求过于频繁，请稍后重试";
            } else if (status.is5xxServerError()) {
                message = "服务器内部错误，请稍后重试";
            }
            
            return Mono.just(Result.fail(status.value(), message));
        } else if (throwable instanceof BusinessException) {
            BusinessException ex = (BusinessException) throwable;
            return Mono.just(Result.fail(500, ex.getMessage()));
        } else {
            return Mono.just(Result.fail(500, "系统异常: " + throwable.getMessage()));
        }
    }
}
