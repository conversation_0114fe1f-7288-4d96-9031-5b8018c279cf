package com.xhcai.modules.rag.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.rag.entity.UploadFile;

import java.util.List;

/**
 * 文件上传记录服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IUploadFileService extends IService<UploadFile> {

    /**
     * 创建上传文件记录
     *
     * @param uploadFile 上传文件记录
     * @return 是否成功
     */
    boolean createUploadFile(UploadFile uploadFile);

    /**
     * 根据数据集ID查询上传文件记录
     *
     * @param datasetId 数据集ID
     * @return 上传文件记录列表
     */
    List<UploadFile> getByDatasetId(String datasetId);

    /**
     * 根据批次ID查询上传文件记录
     *
     * @param batchId 批次ID
     * @return 上传文件记录列表
     */
    List<UploadFile> getByBatchId(String batchId);

    /**
     * 根据文档ID查询上传文件记录
     *
     * @param documentId 文档ID
     * @return 上传文件记录
     */
    UploadFile getByDocumentId(String documentId);

    /**
     * 根据文件hash查询上传文件记录
     *
     * @param fileHash 文件hash
     * @param datasetId 数据集ID
     * @return 上传文件记录
     */
    UploadFile getByFileHash(String fileHash, String datasetId);

    /**
     * 分页查询上传文件记录
     *
     * @param page 分页参数
     * @param datasetId 数据集ID
     * @param uploadStatus 上传状态
     * @param operationType 操作类型
     * @return 分页结果
     */
    IPage<UploadFile> getPageByCondition(Page<UploadFile> page, String datasetId, String uploadStatus, String operationType);

    /**
     * 统计数据集的上传文件数量
     *
     * @param datasetId 数据集ID
     * @return 文件数量
     */
    Long countByDatasetId(String datasetId);

    /**
     * 统计数据集的上传文件数量（按状态）
     *
     * @param datasetId 数据集ID
     * @param uploadStatus 上传状态
     * @return 文件数量
     */
    Long countByDatasetIdAndStatus(String datasetId, String uploadStatus);

    /**
     * 根据用户ID查询上传文件记录
     *
     * @param uploadUserId 上传用户ID
     * @return 上传文件记录列表
     */
    List<UploadFile> getByUploadUserId(String uploadUserId);

    /**
     * 更新文档ID
     *
     * @param id 记录ID
     * @param documentId 文档ID
     * @return 是否成功
     */
    boolean updateDocumentId(String id, String documentId);

    /**
     * 更新上传状态
     *
     * @param id 记录ID
     * @param uploadStatus 上传状态
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    boolean updateUploadStatus(String id, String uploadStatus, String errorMessage);

    /**
     * 软删除文件记录
     *
     * @param id 记录ID
     * @return 是否成功
     */
    boolean softDeleteUploadFile(String id);

    /**
     * 批量软删除文件记录
     *
     * @param ids 记录ID列表
     * @return 是否成功
     */
    boolean batchSoftDeleteUploadFiles(List<String> ids);

    /**
     * 记录文件删除操作
     *
     * @param originalUploadFile 原始上传记录
     * @param deleteReason 删除原因
     * @return 是否成功
     */
    boolean recordFileDeleteOperation(UploadFile originalUploadFile, String deleteReason);

    /**
     * 检查文件是否已存在（基于hash）
     *
     * @param fileHash 文件hash
     * @param datasetId 数据集ID
     * @return 是否存在
     */
    boolean isFileExists(String fileHash, String datasetId);

    /**
     * 批量创建上传文件记录
     *
     * @param uploadFiles 上传文件记录列表
     * @return 是否成功
     */
    boolean batchCreateUploadFiles(List<UploadFile> uploadFiles);
}
