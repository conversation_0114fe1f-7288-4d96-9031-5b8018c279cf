/**
 * API统一导出文件
 * 提供统一的API客户端和模块化接口
 */

import apiClient from '@/utils/apiClient'
import { setupInterceptors } from '@/utils/apiInterceptors'

// 导出API模块
export { default as AuthAPI } from './auth'
export { default as AgentsAPI } from './agents'
export { default as PluginsAPI } from './plugins'
export { default as DictAPI } from './dict'
export { default as RagAPI } from './rag'
export { default as AiModelAPI } from './model'
export { ModuleAPI } from './module'

// 导出系统管理API（包含UserAPI、DeptAPI和TenantAPI）
export { UserAPI, DeptAPI, TenantAPI } from './system'

// 导出类型
export type * from './auth'
export type * from './agents'
export type * from './plugins'
export type * from './system'
export type * from './model'
export type * from './module'

// 导出API客户端
export { apiClient }

// 配置拦截器
setupInterceptors(apiClient)

/**
 * 创建新的API客户端实例
 */
export const createApiClient = (baseURL?: string, timeout?: number) => {
  const client = new (apiClient.constructor as any)(baseURL, timeout)
  setupInterceptors(client)
  return client
}

export default {
  apiClient,
  createApiClient
}
