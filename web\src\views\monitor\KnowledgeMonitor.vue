<template>
  <div class="knowledge-monitor space-y-6">
    <!-- 知识库概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-database text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总知识库数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ knowledgeStats.total }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-search text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日调用总次数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ knowledgeStats.todayCalls.toLocaleString() }}</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-percentage text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均召回率</p>
            <p class="text-2xl font-semibold text-gray-900">{{ knowledgeStats.avgRecallRate }}%</p>
          </div>
        </div>
      </div>
      <div class="stat-card bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-check-circle text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">召回成功率</p>
            <p class="text-2xl font-semibold text-gray-900">{{ knowledgeStats.successRate }}%</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 知识库监控表格 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">知识库监控详情</h3>
          <div class="flex items-center gap-4">
            <input
              v-model="knowledgeSearchQuery"
              type="text"
              placeholder="搜索知识库..."
              class="form-input text-sm w-64"
            />
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">知识库信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">调用次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">召回率</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">召回状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成功率</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="kb in paginatedKnowledgeBases" :key="kb.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-green-400 to-green-600 flex items-center justify-center">
                      <i class="fas fa-database text-white"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ kb.name }}</div>
                    <div class="text-sm text-gray-500">{{ kb.unit }} · {{ kb.creator }}</div>
                    <div class="text-xs text-gray-400">
                      文档: {{ kb.documentCount }} | 字符: {{ kb.characterCount }}K | 应用: {{ kb.associatedApps }}
                    </div>
                    <div class="text-xs text-gray-400">{{ formatDate(kb.createdAt) }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">总量: {{ kb.totalCalls.toLocaleString() }}</div>
                <div class="text-sm text-gray-500">今日: {{ kb.todayCalls.toLocaleString() }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">平均: {{ kb.avgRecallRate }}%</div>
                <div class="text-sm text-gray-500">今日: {{ kb.todayRecallRate }}%</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div :class="getRecallStatusClass(kb.recallStatus)" class="w-2 h-2 rounded-full mr-2"></div>
                  <span class="text-sm text-gray-900">{{ kb.recallStatus }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ kb.successRate }}%</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(kb.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusText(kb.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 知识库分页 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredKnowledgeBases.length) }} 条，
            共 {{ filteredKnowledgeBases.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="text-sm text-gray-700">
              第 {{ currentPage }} / {{ totalPages }} 页
            </span>
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  refreshTrigger: 0
})

// 搜索和过滤
const knowledgeSearchQuery = ref('')

// 分页配置
const pageSize = ref(10)
const currentPage = ref(1)

// 知识库统计数据
const knowledgeStats = ref({
  total: 89,
  todayCalls: 5670,
  avgRecallRate: 87.5,
  successRate: 94.2
})

// 知识库监控数据
const knowledgeBases = ref([
  {
    id: 1,
    name: '产品技术文档库',
    unit: '技术部',
    creator: '张三',
    documentCount: 1250,
    characterCount: 2340,
    associatedApps: 15,
    createdAt: '2024-01-10',
    updatedAt: '2024-06-20',
    totalCalls: 8920,
    todayCalls: 156,
    avgRecallRate: 89.5,
    todayRecallRate: 91.2,
    recallStatus: '正常',
    successRate: 95.8,
    status: 'healthy'
  },
  {
    id: 2,
    name: '客户服务知识库',
    unit: '客服部',
    creator: '李四',
    documentCount: 890,
    characterCount: 1560,
    associatedApps: 8,
    createdAt: '2024-02-15',
    updatedAt: '2024-06-18',
    totalCalls: 5670,
    todayCalls: 89,
    avgRecallRate: 85.2,
    todayRecallRate: 87.8,
    recallStatus: '正常',
    successRate: 92.4,
    status: 'healthy'
  },
  {
    id: 3,
    name: '法律条文数据库',
    unit: '法务部',
    creator: '王五',
    documentCount: 2340,
    characterCount: 4560,
    associatedApps: 3,
    createdAt: '2024-03-20',
    updatedAt: '2024-06-15',
    totalCalls: 3450,
    todayCalls: 45,
    avgRecallRate: 92.1,
    todayRecallRate: 89.5,
    recallStatus: '异常',
    successRate: 88.9,
    status: 'warning'
  }
])

// 计算属性：过滤后的数据
const filteredKnowledgeBases = computed(() => {
  let filtered = knowledgeBases.value

  if (knowledgeSearchQuery.value) {
    const query = knowledgeSearchQuery.value.toLowerCase()
    filtered = filtered.filter(kb =>
      kb.name.toLowerCase().includes(query) ||
      kb.unit.toLowerCase().includes(query) ||
      kb.creator.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 分页后的数据
const paginatedKnowledgeBases = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredKnowledgeBases.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredKnowledgeBases.value.length / pageSize.value)
})

// 方法
const updateKnowledgeStats = () => {
  knowledgeBases.value.forEach(kb => {
    kb.todayCalls = Math.floor(Math.random() * 200) + 20
    kb.todayRecallRate = Math.floor(Math.random() * 20) + 80
    kb.successRate = Math.floor(Math.random() * 15) + 85

    // 更新召回状态
    if (kb.todayRecallRate < 85) {
      kb.recallStatus = '异常'
      kb.status = 'warning'
    } else if (kb.todayRecallRate < 80) {
      kb.recallStatus = '严重异常'
      kb.status = 'error'
    } else {
      kb.recallStatus = '正常'
      kb.status = 'healthy'
    }
  })

  // 更新统计数据
  knowledgeStats.value.todayCalls = knowledgeBases.value.reduce((sum, kb) => sum + kb.todayCalls, 0)
  knowledgeStats.value.avgRecallRate = Math.round(
    knowledgeBases.value.reduce((sum, kb) => sum + kb.avgRecallRate, 0) / knowledgeBases.value.length * 10
  ) / 10
  knowledgeStats.value.successRate = Math.round(
    knowledgeBases.value.reduce((sum, kb) => sum + kb.successRate, 0) / knowledgeBases.value.length * 10
  ) / 10
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getRecallStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    '正常': 'bg-green-500',
    '异常': 'bg-yellow-500',
    '严重异常': 'bg-red-500'
  }
  return statusClasses[status] || 'bg-gray-500'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: '正常',
    warning: '警告',
    error: '异常'
  }
  return statusMap[status] || status
}

// 分页方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const resetPagination = () => {
  currentPage.value = 1
}

// 时间格式化方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 监听搜索条件变化，重置分页
watch([knowledgeSearchQuery], () => {
  resetPagination()
})

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  updateKnowledgeStats()
})

onMounted(() => {
  updateKnowledgeStats()
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-input {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  background-color: #f8fafc;
  transform: scale(1.01);
}

/* 渐变背景 */
.from-green-400 {
  --tw-gradient-from: #4ade80;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(74, 222, 128, 0));
}

.to-green-600 {
  --tw-gradient-to: #16a34a;
}
</style>
