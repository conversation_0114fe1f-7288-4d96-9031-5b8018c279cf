<template>
  <BaseFileViewer :file="file" @download="handleDownload" @refresh="handleRefresh">
    <template #content>
      <div class="pdf-viewer">
        <div class="pdf-toolbar">
          <div class="toolbar-left">
            <button class="tool-btn" @click="zoomOut" :disabled="scale <= 0.5">
              <i class="fas fa-search-minus"></i>
            </button>
            <span class="zoom-info">{{ Math.round(scale * 100) }}%</span>
            <button class="tool-btn" @click="zoomIn" :disabled="scale >= 3">
              <i class="fas fa-search-plus"></i>
            </button>
            <button class="tool-btn" @click="resetZoom">
              <i class="fas fa-expand-arrows-alt"></i>
            </button>
          </div>
          <div class="toolbar-center">
            <button class="tool-btn" @click="prevPage" :disabled="currentPage <= 1">
              <i class="fas fa-chevron-left"></i>
            </button>
            <span class="page-info">
              <input 
                type="number" 
                v-model.number="currentPage" 
                @change="goToPage"
                :min="1" 
                :max="totalPages"
                class="page-input"
              > / {{ totalPages }}
            </span>
            <button class="tool-btn" @click="nextPage" :disabled="currentPage >= totalPages">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
          <div class="toolbar-right">
            <button class="tool-btn" @click="toggleFullscreen">
              <i class="fas fa-expand"></i>
            </button>
            <button class="tool-btn" @click="handleDownload" title="下载文件">
              <i class="fas fa-download"></i>
            </button>
            <button class="tool-btn" @click="handleRefresh" title="刷新内容">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>
        
        <div class="pdf-content" ref="pdfContainer">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载 PDF 文件...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="loadPdf">重新加载</button>
          </div>
          
          <div v-else class="pdf-pages">
            <div
              v-for="page in visiblePages"
              :key="page"
              class="pdf-page"
              :style="{ transform: `scale(${scale})` }"
            >
              <div class="page-container">
                <canvas
                  :ref="el => setPageRef(el, page)"
                  class="page-canvas"
                ></canvas>

                <!-- 分段高亮覆盖层 -->
                <div
                  v-if="getPageSegments(page).length > 0"
                  class="segment-overlay"
                >
                  <div
                    v-for="segment in getPageSegments(page)"
                    :key="segment.id"
                    class="segment-highlight"
                    :class="{
                      'selected': selectedSegmentId === segment.id,
                      'hovered': hoveredSegmentId === segment.id
                    }"
                    :style="getSegmentStyle(segment)"
                    @mouseenter="handleSegmentHover(segment)"
                    @mouseleave="handleSegmentLeave"
                    @click="handleSegmentClick(segment)"
                  >
                    <div class="segment-info">
                      <span class="segment-index">#{{ getSegmentIndex(segment.id) }}</span>
                      <span class="segment-chars">{{ segment.content.length }}字符</span>
                    </div>
                    <div class="segment-preview">
                      {{ getSegmentPreview(segment.content) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseFileViewer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import BaseFileViewer from './BaseFileViewer.vue'

// Props
interface Props {
  file: any
  segments?: any[]
  selectedSegmentId?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  download: [file: any]
  segmentHover: [segmentInfo: any]
  segmentLeave: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1)
const pdfContainer = ref<HTMLElement | null>(null)
const pageRefs = ref<Map<number, HTMLCanvasElement>>(new Map())
const hoveredSegmentId = ref<string | null>(null)

// 计算属性
const visiblePages = computed(() => {
  // 简化版本，显示当前页面
  return [currentPage.value]
})

// 方法
const handleDownload = (file: any) => {
  emit('download', file)
}

const handleRefresh = () => {
  loadPdf()
}

const loadPdf = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 模拟 PDF 加载
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 模拟 PDF 数据
    totalPages.value = 10
    currentPage.value = 1
    
    // 渲染页面
    await nextTick()
    renderPages()
    
  } catch (err) {
    error.value = '加载 PDF 文件失败'
  } finally {
    loading.value = false
  }
}

const renderPages = () => {
  visiblePages.value.forEach(pageNum => {
    const canvas = pageRefs.value.get(pageNum)
    if (canvas) {
      const ctx = canvas.getContext('2d')
      if (ctx) {
        // 设置画布大小
        canvas.width = 600
        canvas.height = 800
        
        // 绘制模拟的 PDF 页面
        ctx.fillStyle = '#ffffff'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        // 绘制边框
        ctx.strokeStyle = '#e2e8f0'
        ctx.lineWidth = 1
        ctx.strokeRect(0, 0, canvas.width, canvas.height)
        
        // 绘制文本内容
        ctx.fillStyle = '#1e293b'
        ctx.font = '16px Arial'
        ctx.fillText(`PDF 文件: ${props.file?.name}`, 50, 100)
        ctx.fillText(`第 ${pageNum} 页 / 共 ${totalPages.value} 页`, 50, 130)
        
        // 绘制模拟内容
        ctx.font = '14px Arial'
        ctx.fillStyle = '#64748b'
        const lines = [
          '这是一个模拟的 PDF 查看器。',
          '在实际应用中，这里会显示真实的 PDF 内容。',
          '可以使用 PDF.js 或其他 PDF 渲染库来实现。',
          '',
          '功能特性：',
          '• 页面导航',
          '• 缩放控制',
          '• 全屏查看',
          '• 下载文件'
        ]
        
        lines.forEach((line, index) => {
          ctx.fillText(line, 50, 180 + index * 25)
        })
      }
    }
  })
}

const setPageRef = (el: HTMLCanvasElement | null, page: number) => {
  if (el) {
    pageRefs.value.set(page, el)
  }
}

const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.25)
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(0.5, scale.value - 0.25)
  }
}

const resetZoom = () => {
  scale.value = 1
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    nextTick(() => renderPages())
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    nextTick(() => renderPages())
  }
}

const toggleFullscreen = () => {
  if (pdfContainer.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      pdfContainer.value.requestFullscreen()
    }
  }
}

// 生命周期
onMounted(() => {
  loadPdf()
})

// 监听选中分段变化
watch(() => props.selectedSegmentId, (newSegmentId) => {
  if (newSegmentId && props.segments) {
    const segment = props.segments.find(seg => seg.id === newSegmentId)
    if (segment && segment.metadata && segment.metadata.page) {
      // 跳转到指定页面
      goToPage(segment.metadata.page)
    }
  }
})

// 跳转到指定页面的方法
const goToPage = (pageNumber: number) => {
  if (pageNumber >= 1 && pageNumber <= totalPages.value) {
    currentPage.value = pageNumber
    nextTick(() => renderPages())
  }
}

// 获取指定页面的分段
const getPageSegments = (page: number) => {
  if (!props.segments) return []
  return props.segments.filter(segment =>
    segment.metadata && segment.metadata.page === page
  )
}

// 获取分段在页面上的样式
const getSegmentStyle = (segment: any) => {
  // 根据分段内容长度和位置计算更准确的位置
  const baseTop = 180 // 从文档内容开始的位置
  const lineHeight = 25 // 每行高度
  const position = segment.metadata?.position || 0
  const contentLength = segment.content?.length || 0

  // 根据内容长度估算高度
  const estimatedLines = Math.max(2, Math.ceil(contentLength / 50))
  const height = estimatedLines * lineHeight

  const top = baseTop + position * (height + 20) // 20px 间距

  return {
    position: 'absolute',
    top: `${top}px`,
    left: '50px',
    right: '50px',
    height: `${height}px`,
    border: props.selectedSegmentId === segment.id ? '3px dashed #10b981' : '2px dashed #3b82f6',
    borderRadius: '4px',
    backgroundColor: props.selectedSegmentId === segment.id
      ? 'rgba(16, 185, 129, 0.15)'
      : 'rgba(59, 130, 246, 0.1)',
    pointerEvents: 'all',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    zIndex: props.selectedSegmentId === segment.id ? 10 : 5
  }
}

// 获取分段索引
const getSegmentIndex = (segmentId: string) => {
  if (!props.segments) return 0
  return props.segments.findIndex(seg => seg.id === segmentId) + 1
}

// 获取分段预览文本
const getSegmentPreview = (content: string) => {
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

// 处理分段点击
const handleSegmentClick = (segment: any) => {
  // 触发分段选择事件，通知父组件
  console.log('Segment clicked:', segment)
  // 这里可以添加更多的点击处理逻辑
}

// 处理分段悬停
const handleSegmentHover = (segment: any) => {
  hoveredSegmentId.value = segment.id
  const segmentInfo = {
    id: segment.id,
    charCount: segment.content?.length || 0,
    keywords: extractKeywords(segment.content || ''),
    content: segment.content?.substring(0, 100) + (segment.content?.length > 100 ? '...' : '')
  }
  emit('segmentHover', segmentInfo)
}

// 处理分段离开
const handleSegmentLeave = () => {
  hoveredSegmentId.value = null
  emit('segmentLeave')
}

// 简单的关键词提取
const extractKeywords = (text: string): string[] => {
  const words = text.match(/[\u4e00-\u9fa5]{2,}|[a-zA-Z]{3,}/g) || []
  const wordCount: { [key: string]: number } = {}

  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1
  })

  return Object.entries(wordCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([word]) => word)
}
</script>

<style scoped>
.pdf-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 16px;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: white;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.tool-btn:hover:not(:disabled) {
  background: #f1f5f9;
  color: #334155;
}

.tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-info,
.page-info {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-input {
  width: 50px;
  padding: 4px 6px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
}

.page-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.pdf-content {
  flex: 1;
  overflow: auto;
  background: #f1f5f9;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.pdf-pages {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.pdf-page {
  transform-origin: top center;
  transition: transform 0.2s ease;
}

.page-container {
  position: relative;
  display: inline-block;
}

.page-canvas {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  background: white;
  display: block;
}

.segment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.segment-highlight {
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px;
  font-size: 12px;
  overflow: hidden;
}

.segment-highlight:hover,
.segment-highlight.hovered {
  background-color: rgba(59, 130, 246, 0.2) !important;
  border-color: #2563eb !important;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.segment-highlight.selected {
  background-color: rgba(16, 185, 129, 0.15) !important;
  border-color: #10b981 !important;
  border-width: 3px !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.segment-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-weight: 500;
}

.segment-index {
  color: #3b82f6;
  font-weight: 600;
}

.segment-chars {
  color: #64748b;
  font-size: 10px;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 4px;
  border-radius: 3px;
}

.segment-preview {
  color: #374151;
  line-height: 1.3;
  font-size: 11px;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px;
  border-radius: 3px;
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
