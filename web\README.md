# AI智能体平台 - Web前端

基于Vue3 + Vite + TailwindCSS构建的现代化AI智能体平台前端应用。

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **TypeScript** - JavaScript的超集，提供类型安全
- **Vue Router** - Vue.js官方路由管理器
- **Pinia** - Vue的状态管理库

- **TailwindCSS** - 实用优先的CSS框架
- **ESLint + Prettier** - 代码质量和格式化工具

## 项目结构

```
web/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 资源文件
│   │   └── main.css       # 全局样式
│   ├── components/        # 公共组件
│   ├── layouts/           # 布局组件
│   │   ├── LoginLayout.vue    # 登录布局
│   │   └── MainLayout.vue     # 主布局

│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── stores/            # 状态管理
│   ├── views/             # 页面组件
│   │   ├── Login.vue      # 登录页
│   │   ├── Home.vue       # 首页
│   │   ├── Agents.vue     # 智能体管理
│   │   ├── workflow/      # 智能体编排模块
│   │   │   ├── index.vue  # 主编排页面
│   │   │   └── components/ # 编排组件
│   │   │       ├── NodeLibrary.vue      # 节点库面板
│   │   │       ├── NodeProperties.vue   # 节点属性配置
│   │   │       ├── EdgeProperties.vue   # 连接线属性配置
│   │   │       ├── SettingsModal.vue    # 设置弹窗
│   │   │       ├── DebugModal.vue       # 调试弹窗
│   │   │       └── nodes/               # 自定义节点组件
│   │   │           ├── StartNode.vue    # 开始节点
│   │   │           ├── EndNode.vue      # 结束节点
│   │   │           ├── ConditionNode.vue # 条件节点
│   │   │           ├── DatabaseNode.vue # 数据库节点
│   │   │           ├── AINode.vue       # AI工具节点
│   │   │           ├── FileNode.vue     # 文件操作节点
│   │   │           ├── RenderNode.vue   # 渲染工具节点
│   │   │           ├── DataNode.vue     # 数据处理节点
│   │   │           └── UtilityNode.vue  # 其他工具节点
│   │   ├── Knowledge.vue  # 知识库管理
│   │   ├── Plugins.vue    # 插件管理
│   │   ├── Monitor.vue    # 系统监控
│   │   └── Settings.vue   # 系统设置
│   ├── App.vue            # 根组件
│   └── main.ts            # 应用入口
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── .env.test             # 测试环境配置
├── index.html            # HTML模板
├── package.json          # 项目配置
├── tailwind.config.js    # TailwindCSS配置
├── tsconfig.json         # TypeScript配置
└── vite.config.ts        # Vite配置
```

## 功能特性

### 🎨 现代化UI设计
- 浅色系现代风格主题
- 响应式设计，支持移动端和PC端
- 流畅的动画效果和交互体验
- TailwindCSS实用优先的样式系统



### 🔐 用户认证
- 精美的登录页面设计
- AI风格的背景动画效果
- 简单的登录验证机制

### 📱 模块化页面
- **首页**: 数据概览和快速操作
- **智能体管理**: 创建、编辑和管理AI智能体
- **智能体编排**: 可视化工作流编排系统
  - 基于Vue Flow的拖拽式编排界面
  - 丰富的节点库：基础节点、数据库工具、AI工具、文件工具、渲染工具、数据工具等
  - 实时保存和自动版本管理
  - 调试功能：执行流程监控、变量监控、日志输出、性能指标
  - 节点属性配置和连接线管理
  - 画布功能：缩放、适应视图、清除、设置等
- **知识库管理**: 构建和维护知识体系
- **插件管理**: 扩展平台功能的插件系统
- **系统监控**: 实时监控系统状态和性能
- **系统设置**: 用户管理、部署配置、API密钥等

### 🔧 智能体编排功能详解

#### 节点库系统
- **基础节点**: 开始节点、结束节点、条件判断、循环节点
- **数据库工具**: MySQL、PostgreSQL、Oracle、达梦、Elasticsearch、Redis
- **文件生成工具**: PPT、Word、PDF、Excel、Txt、Markdown生成
- **文件提取工具**: PPT、Word、PDF、Excel、Txt、Markdown提取
- **AI工具**: LLM对话、文本向量化、语音识别、语音合成、图像生成、图像分析、知识库查询
- **渲染工具**: 饼图、折线图、柱状图、音频播放、图片显示、数据表格
- **数据工具**: 数据过滤、数据转换、数据合并、数据排序、数据聚合
- **其他工具**: HTTP请求、发送邮件、延时等待、设置变量、日志输出

#### 编排界面功能
- **拖拽式操作**: 从节点库拖拽节点到画布，直观易用
- **连接线管理**: 点击连接线配置属性，右键插入节点
- **实时保存**: 每次操作自动保存到数据库，保留版本历史
- **调试功能**:
  - 执行流程可视化监控
  - 全局变量和节点变量实时监控
  - 详细的执行日志输出
  - 性能指标统计和图表展示
- **画布控制**: 缩放、平移、适应视图、清除画布等操作

#### 技术实现
- **Vue Flow**: 基于Vue 3的流程图组件库
- **模块化设计**: 统一事件机制和状态管理
- **版本控制**: 每次保存自动创建版本快照
- **热插拔架构**: 支持节点类型的动态扩展

### ⚡ 开发体验
- 热重载开发服务器
- TypeScript类型安全
- ESLint代码质量检查
- Prettier代码格式化
- 模块化组件架构

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
cd web
npm install
```

### 开发模式
```bash
npm run dev
```
访问 http://localhost:3000

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
```

### 代码格式化
```bash
npm run format
```

## 环境配置

项目支持多环境配置，通过不同的`.env`文件管理：

- `.env.development` - 开发环境
- `.env.production` - 生产环境  
- `.env.test` - 测试环境

### 环境变量说明
```bash
# 应用标题
VITE_APP_TITLE=AI智能体平台

# API基础路径
VITE_APP_BASE_API=/api

# 应用基础URL
VITE_APP_BASE_URL=http://localhost:3000

# 后端API地址
VITE_API_BASE_URL=http://localhost:8000
```

## 路由结构

```
/login          # 登录页
/               # 主布局
├── /home       # 首页
├── /agents     # 智能体管理
├── /knowledge  # 知识库管理
├── /plugins    # 插件管理
├── /monitor    # 系统监控
└── /settings   # 系统设置
```

## 组件说明

### 布局组件
- `LoginLayout.vue` - 登录页面布局
- `MainLayout.vue` - 主应用布局，包含导航栏、侧边栏等

### 页面组件
每个页面组件都包含完整的功能实现，包括数据展示、交互逻辑和样式设计。

## 样式系统

项目使用TailwindCSS作为主要样式框架，同时定义了一套设计系统：

### 颜色主题
- 主色调：蓝紫渐变 (#667eea → #764ba2)
- 背景：浅色渐变 (#f5f7fa → #c3cfe2)
- 文字：深灰色系

### 组件样式
- 卡片：白色半透明背景，毛玻璃效果
- 按钮：渐变背景，悬停动效
- 表单：圆角设计，聚焦动效

## 开发规范

### 代码风格
- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API规范
- 使用ESLint和Prettier保证代码质量

### 组件规范
- 使用`<script setup>`语法
- 合理使用响应式API
- 组件命名采用PascalCase

### 样式规范
- 优先使用TailwindCSS工具类
- 自定义样式使用scoped
- 响应式设计优先

## 后续开发

### 待完善功能
1. 用户状态管理 (Pinia Store)
2. API接口集成
3. 错误处理和加载状态
4. 单元测试
5. E2E测试
6. 性能优化

### 扩展建议
1. 添加更多图表组件 (Chart.js/ECharts)
2. 实现拖拽功能
3. 添加主题切换
4. 优化移动端体验
5. 添加PWA支持

## 许可证

MIT License
