package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.config.DifyWebClientConfig;
import com.xhcai.modules.dify.dto.auth.DifyLoginResponseDTO;
import com.xhcai.modules.dify.dto.auth.DifyRefreshTokenResponseDTO;
import com.xhcai.modules.dify.service.IDifyAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Dify 认证管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Tag(name = "Dify认证管理", description = "Dify认证相关接口")
@RestController
@RequestMapping("/api/dify/auth")
public class DifyAuthController {

    private static final Logger log = LoggerFactory.getLogger(DifyAuthController.class);

    @Autowired
    private IDifyAuthService difyAuthService;

    @Autowired
    private DifyWebClientConfig difyWebClientConfig;

    /**
     * 获取当前访问令牌
     */
    @Operation(summary = "获取当前访问令牌")
    @GetMapping("/token")
    public Result<String> getCurrentToken() {
        try {
            String accessToken = difyAuthService.getValidAccessTokenSync();
            // 只返回令牌的前20个字符用于显示
            String displayToken = accessToken.length() > 20 ?
                    accessToken.substring(0, 20) + "..." : accessToken;
            return Result.success(displayToken);
        } catch (Exception e) {
            log.error("获取访问令牌失败", e);
            return Result.fail("获取访问令牌失败: " + e.getMessage());
        }
    }

    /**
     * 手动登录
     */
    @Operation(summary = "手动登录")
    @PostMapping("/login")
    public Result<DifyLoginResponseDTO> login() {
        try {
            DifyLoginResponseDTO response = difyAuthService.login().block();
            log.info("手动登录成功");
            return Result.success(response);
        } catch (Exception e) {
            log.error("手动登录失败", e);
            return Result.fail("登录失败: " + e.getMessage());
        }
    }

    /**
     * 刷新令牌
     */
    @Operation(summary = "刷新令牌")
    @PostMapping("/refresh")
    public Result<DifyRefreshTokenResponseDTO> refreshToken() {
        try {
            DifyRefreshTokenResponseDTO response = difyAuthService.refreshToken().block();
            log.info("刷新令牌成功");
            return Result.success(response);
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return Result.fail("刷新令牌失败: " + e.getMessage());
        }
    }

    /**
     * 清除令牌
     */
    @Operation(summary = "清除令牌")
    @DeleteMapping("/token")
    public Result<String> clearTokens() {
        try {
            difyAuthService.clearTokens().block();
            log.info("清除令牌成功");
            return Result.success("令牌已清除");
        } catch (Exception e) {
            log.error("清除令牌失败", e);
            return Result.fail("清除令牌失败: " + e.getMessage());
        }
    }

    /**
     * 测试认证状态
     */
    @Operation(summary = "测试认证状态")
    @GetMapping("/status")
    public Result<String> getAuthStatus() {
        try {
            String accessToken = difyAuthService.getValidAccessTokenSync();
            boolean isValid = difyAuthService.isAccessTokenValid(accessToken).block();

            String status = isValid ? "认证有效" : "认证无效";
            log.info("认证状态检查: {}", status);
            return Result.success(status);
        } catch (Exception e) {
            log.error("检查认证状态失败", e);
            return Result.fail("检查认证状态失败: " + e.getMessage());
        }
    }

    /**
     * 测试Dify连接
     */
    @Operation(summary = "测试Dify连接")
    @GetMapping("/test-connection")
    public Result<String> testConnection() {
        try {
            String result = difyWebClientConfig.testDifyConnection().block();
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试Dify连接失败", e);
            return Result.fail("测试Dify连接失败: " + e.getMessage());
        }
    }

    /**
     * 模拟处理 401 错误
     */
    @Operation(summary = "模拟处理401错误")
    @PostMapping("/handle-401")
    public Result<String> handleUnauthorized() {
        try {
            String newAccessToken = difyAuthService.handleUnauthorizedSync();
            String displayToken = newAccessToken.length() > 20 ?
                    newAccessToken.substring(0, 20) + "..." : newAccessToken;
            log.info("处理 401 错误成功");
            return Result.success("新令牌: " + displayToken);
        } catch (Exception e) {
            log.error("处理 401 错误失败", e);
            return Result.fail("处理 401 错误失败: " + e.getMessage());
        }
    }

    /**
     * 模拟处理 600 错误
     */
    @Operation(summary = "模拟处理600错误")
    @PostMapping("/handle-600")
    public Result<String> handleRelogin() {
        try {
            String newAccessToken = difyAuthService.handleReloginSync();
            String displayToken = newAccessToken.length() > 20 ?
                    newAccessToken.substring(0, 20) + "..." : newAccessToken;
            log.info("处理 600 错误成功");
            return Result.success("新令牌: " + displayToken);
        } catch (Exception e) {
            log.error("处理 600 错误失败", e);
            return Result.fail("处理 600 错误失败: " + e.getMessage());
        }
    }
}
