package com.yyzs.agent.service;

import com.yyzs.agent.entity.ElasticComponent;

import java.util.List;
import java.util.Map;

/**
 * 组件安装服务接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ComponentInstallService {

    /**
     * 安装组件
     */
    boolean installComponent(ElasticComponent component, Map<String, Object> config);

    /**
     * 卸载组件
     */
    boolean uninstallComponent(ElasticComponent component);

    /**
     * 启动组件
     */
    boolean startComponent(ElasticComponent component);

    /**
     * 停止组件
     */
    boolean stopComponent(ElasticComponent component);

    /**
     * 检查组件状态
     */
    ElasticComponent.ComponentStatus checkComponentStatus(ElasticComponent component);

    /**
     * 更新组件配置
     */
    boolean updateComponentConfig(ElasticComponent component, Map<String, Object> config);

    /**
     * 获取组件配置
     */
    Map<String, Object> getComponentConfig(ElasticComponent component);

    /**
     * 验证组件配置
     */
    boolean validateComponentConfig(String componentType, Map<String, Object> config);

    /**
     * 获取组件日志
     */
    List<String> getComponentLogs(ElasticComponent component, int lines);

    /**
     * 清理组件日志
     */
    boolean clearComponentLogs(ElasticComponent component);

    /**
     * 备份组件配置
     */
    String backupComponentConfig(ElasticComponent component);

    /**
     * 恢复组件配置
     */
    boolean restoreComponentConfig(ElasticComponent component, String backupPath);

    /**
     * 生成组件配置文件
     */
    boolean generateComponentConfig(ElasticComponent component, Map<String, Object> config);

    /**
     * 解压安装包
     */
    boolean extractPackage(ElasticComponent component);

    /**
     * 设置组件权限
     */
    boolean setComponentPermissions(ElasticComponent component);

    /**
     * 创建组件启动脚本
     */
    boolean createStartupScript(ElasticComponent component);

    /**
     * 获取组件进程ID
     */
    Long getComponentProcessId(ElasticComponent component);

    /**
     * 杀死组件进程
     */
    boolean killComponentProcess(ElasticComponent component);

    /**
     * 检查组件端口是否可用
     */
    boolean isComponentPortAvailable(ElasticComponent component, Integer port);

    /**
     * 获取组件默认配置模板
     */
    Map<String, Object> getDefaultConfigTemplate(String componentType);

    /**
     * 验证组件安装环境
     */
    boolean validateInstallEnvironment(ElasticComponent component);

    /**
     * 清理组件安装文件
     */
    boolean cleanupInstallFiles(ElasticComponent component);
}
