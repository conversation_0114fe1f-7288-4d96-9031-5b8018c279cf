# YYZS Agent Platform - 组件安装指南

## 功能概述

YYZS Agent Platform 提供了完整的 Elastic Stack 组件安装和管理功能，支持以下组件：

- **Beats 系列**: Filebeat, Heartbeat, Metricbeat, Packetbeat, Winlogbeat, Auditbeat
- **Elastic Stack**: Logstash, Elasticsearch
- **消息队列**: Kafka

## 安装流程

### 1. 访问安装页面

从主页点击"安装组件"按钮，或直接访问 `/install` 路径。

### 2. 选择组件

在组件列表页面，您可以：
- 浏览所有可用组件
- 使用搜索功能查找特定组件
- 按分类筛选组件（Beats、Elastic Stack、Message Queue）
- 查看组件详细信息和功能特性

### 3. 上传安装包

点击组件卡片进入安装流程：

#### 支持的文件格式
- `.tar.gz`
- `.tgz` 
- `.zip`

#### 文件要求
- 最大文件大小：500MB
- 文件名建议包含版本号（如：elasticsearch-8.11.0.tar.gz）

#### 上传方式
- **拖拽上传**：直接将文件拖拽到上传区域
- **点击选择**：点击上传区域选择文件

### 4. 配置组件

#### 基本配置
- **安装路径**：组件安装目录（必填）
- **端口**：服务监听端口
- **主机地址**：绑定的主机地址
- **自动启动**：安装完成后是否自动启动服务

#### 高级配置
- **JSON 配置**：直接编辑完整的组件配置
- **组件特定配置**：根据不同组件类型提供专门的配置选项

#### 默认配置示例

**Elasticsearch:**
```json
{
  "installPath": "/opt/elastic/elasticsearch",
  "port": 9200,
  "host": "localhost",
  "autoStart": true,
  "cluster": {
    "name": "elasticsearch"
  },
  "node": {
    "name": "node-1"
  },
  "network": {
    "host": "0.0.0.0"
  },
  "discovery": {
    "type": "single-node"
  }
}
```

**Filebeat:**
```json
{
  "installPath": "/opt/elastic/filebeat",
  "port": 5044,
  "host": "localhost",
  "autoStart": true,
  "inputs": [
    {
      "type": "log",
      "enabled": true,
      "paths": ["/var/log/*.log"]
    }
  ],
  "output": {
    "elasticsearch": {
      "hosts": ["localhost:9200"]
    }
  }
}
```

### 5. 安装过程

安装过程包含以下步骤：

1. **解压安装包** - 解压上传的组件安装包
2. **验证文件** - 验证安装包完整性和依赖
3. **安装组件** - 复制文件到目标目录
4. **配置组件** - 生成配置文件和启动脚本
5. **初始化服务** - 注册系统服务和设置权限
6. **验证安装** - 检查安装结果和服务状态

### 6. 安装完成

安装成功后，您可以：
- 返回首页查看组件状态
- 继续安装其他组件
- 在组件管理页面进行后续操作

## 组件管理

安装完成的组件可以进行以下操作：
- **启动/停止/重启**：控制组件运行状态
- **查看日志**：监控组件运行情况
- **修改配置**：更新组件配置参数
- **卸载组件**：完全移除组件

## 故障排除

### 常见问题

1. **上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过限制
   - 检查网络连接状态

2. **安装失败**
   - 检查磁盘空间是否充足
   - 确认安装路径权限
   - 查看错误日志信息

3. **配置错误**
   - 验证 JSON 格式是否正确
   - 检查端口是否被占用
   - 确认路径是否存在

### 日志查看

可以通过以下方式查看详细日志：
- 浏览器开发者工具控制台
- 组件管理页面的日志查看功能
- 服务器端日志文件

## API 接口

### 上传组件
```
POST /api/components/upload
Content-Type: multipart/form-data

参数:
- file: 安装包文件
- componentType: 组件类型
- version: 版本号
- description: 描述信息（可选）
```

### 安装组件
```
POST /api/components/{componentId}/install
Content-Type: application/json

参数:
- config: 组件配置对象
```

### 获取组件列表
```
GET /api/components
参数:
- type: 组件类型（可选）
- status: 组件状态（可选）
```

## 技术支持

如果在使用过程中遇到问题，请：
1. 查看本指南的故障排除部分
2. 检查浏览器控制台错误信息
3. 联系技术支持团队

---

**注意事项：**
- 安装过程中请勿关闭浏览器或刷新页面
- 确保系统有足够的磁盘空间和内存
- 建议在安装前备份重要数据
