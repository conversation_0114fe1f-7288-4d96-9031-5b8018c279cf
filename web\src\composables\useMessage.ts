/**
 * Vue组合式函数 - 消息提示
 * 为Vue组件提供便捷的消息提示功能
 *
 * @example
 * ```typescript
 * // 在Vue组件中使用
 * import { useMessage, type MessageType } from '@/composables/useMessage'
 *
 * const { showSuccess, showError, showMessage } = useMessage()
 *
 * // 显示成功消息
 * showSuccess('操作成功！')
 *
 * // 显示错误消息
 * showError('操作失败！')
 *
 * // 显示自定义消息
 * showMessage({
 *   message: '自定义消息',
 *   type: 'info',
 *   duration: 3000
 * })
 * ```
 */

import { onUnmounted } from 'vue'
import { 
  showMessage as _showMessage,
  showSuccess as _showSuccess,
  showError as _showError,
  showWarning as _showWarning,
  showInfo as _showInfo,
  clearAllMessages,
  type MessageOptions,
  type MessageType
} from '@/utils/message'

/**
 * 消息提示组合式函数
 */
export function useMessage() {
  // 组件卸载时清理所有消息（可选）
  const cleanup = () => {
    // 注意：这里不自动清理所有消息，因为消息可能需要在组件切换时保持显示
    // 如果需要在特定组件卸载时清理，可以手动调用 clearAllMessages()
  }

  onUnmounted(cleanup)

  /**
   * 显示消息
   */
  const showMessage = (options: string | MessageOptions) => {
    _showMessage(options)
  }

  /**
   * 显示成功消息
   */
  const showSuccess = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
    _showSuccess(message, options)
  }

  /**
   * 显示错误消息
   */
  const showError = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
    _showError(message, options)
  }

  /**
   * 显示警告消息
   */
  const showWarning = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
    _showWarning(message, options)
  }

  /**
   * 显示信息消息
   */
  const showInfo = (message: string, options?: Omit<MessageOptions, 'message' | 'type'>) => {
    _showInfo(message, options)
  }

  /**
   * 清除所有消息
   */
  const clearMessages = () => {
    clearAllMessages()
  }

  /**
   * 创建带有默认配置的消息函数
   */
  const createMessageWithDefaults = (defaultOptions: Partial<MessageOptions>) => {
    return (message: string, options?: Partial<MessageOptions>) => {
      showMessage({
        ...defaultOptions,
        ...options,
        message
      })
    }
  }

  return {
    // 基础方法
    showMessage,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    clearMessages,

    // 工具方法
    createMessageWithDefaults
  }
}

/**
 * 全局消息提示函数（不依赖Vue组件）
 * 可以在任何地方使用，包括非Vue环境
 */
export const message = {
  show: _showMessage,
  success: _showSuccess,
  error: _showError,
  warning: _showWarning,
  info: _showInfo,
  clear: clearAllMessages
}

// 重新导出类型，供外部使用
export type { MessageType, MessageOptions } from '@/utils/message'

// 默认导出组合式函数
export default useMessage
