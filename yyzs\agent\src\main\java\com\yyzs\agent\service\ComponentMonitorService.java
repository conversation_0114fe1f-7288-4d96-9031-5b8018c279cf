package com.yyzs.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyzs.agent.entity.ComponentMonitor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 组件监控服务接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ComponentMonitorService extends IService<ComponentMonitor> {

    /**
     * 收集组件监控数据
     */
    ComponentMonitor collectMonitorData(String componentId);

    /**
     * 批量收集所有组件监控数据
     */
    List<ComponentMonitor> collectAllMonitorData();

    /**
     * 获取组件最新监控数据
     */
    ComponentMonitor getLatestMonitorData(String componentId);

    /**
     * 获取组件历史监控数据
     */
    List<ComponentMonitor> getHistoryMonitorData(String componentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取所有组件最新监控数据
     */
    List<ComponentMonitor> getAllLatestMonitorData();

    /**
     * 获取不健康的组件
     */
    List<ComponentMonitor> getUnhealthyComponents();

    /**
     * 检查组件健康状态
     */
    boolean checkComponentHealth(String componentId);

    /**
     * 获取组件性能统计
     */
    Map<String, Object> getComponentPerformanceStats(String componentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取系统资源使用情况
     */
    Map<String, Object> getSystemResourceUsage();

    /**
     * 清理历史监控数据
     */
    int cleanupHistoryData(LocalDateTime beforeTime);

    /**
     * 启动监控任务
     */
    void startMonitoring();

    /**
     * 停止监控任务
     */
    void stopMonitoring();

    /**
     * 获取监控任务状态
     */
    boolean isMonitoringActive();

    /**
     * 设置监控间隔
     */
    void setMonitorInterval(int intervalSeconds);

    /**
     * 发送告警通知
     */
    void sendAlert(String componentId, String message, String level);

    /**
     * 获取组件告警历史
     */
    List<Map<String, Object>> getAlertHistory(String componentId);
}
