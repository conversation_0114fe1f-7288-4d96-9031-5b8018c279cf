package com.xhcai.modules.rag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.rag.entity.FileStorageConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 文件存储配置Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface FileStorageConfigMapper extends BaseMapper<FileStorageConfig> {

    /**
     * 查询文件存储配置列表
     */
    @Select("<script>" +
            "SELECT * FROM file_storage_config " +
            "WHERE deleted = '0' " +
            "<if test='name != null and name != \"\"'>" +
            "AND name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='storageType != null and storageType != \"\"'>" +
            "AND storage_type = #{storageType} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='isDefault != null and isDefault != \"\"'>" +
            "AND is_default = #{isDefault} " +
            "</if>" +
            "ORDER BY is_default DESC, create_time DESC" +
            "</script>")
    List<FileStorageConfig> selectFileStorageConfigList(@Param("name") String name,
                                                        @Param("storageType") String storageType,
                                                        @Param("status") String status,
                                                        @Param("isDefault") String isDefault);

    /**
     * 根据存储类型查询配置
     */
    @Select("SELECT * FROM file_storage_config WHERE storage_type = #{storageType} AND status = '0' AND deleted = '0'")
    List<FileStorageConfig> selectByStorageType(@Param("storageType") String storageType);

    /**
     * 查询默认存储配置
     */
    @Select("SELECT * FROM file_storage_config WHERE is_default = 'Y' AND status = '0' AND deleted = '0' LIMIT 1")
    FileStorageConfig selectDefaultConfig();

    /**
     * 取消所有默认配置
     */
    @Update("UPDATE file_storage_config SET is_default = 'N' WHERE deleted = '0'")
    int clearAllDefaultConfig();

    /**
     * 设置默认配置
     */
    @Update("UPDATE file_storage_config SET is_default = 'Y' WHERE id = #{id} AND deleted = '0'")
    int setDefaultConfig(@Param("id") String id);

    /**
     * 批量更新状态
     */
    @Update("<script>" +
            "UPDATE file_storage_config SET status = #{status} " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "AND deleted = '0'" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status);
}
