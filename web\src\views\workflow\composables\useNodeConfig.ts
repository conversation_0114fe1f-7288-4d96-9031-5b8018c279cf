/**
 * 响应式节点配置管理
 * 解决节点样式配置修改后画布中没有实时生效的问题
 */

import { ref, computed, reactive, watch } from 'vue'
import { NODE_LIBRARY_CONFIG, type NodeLibraryItem } from '../config/nodeLibrary'

// 响应式节点配置存储
const nodeConfigs = reactive<Map<string, NodeLibraryItem>>(new Map())

// 配置变更通知
const configVersion = ref(0)

/**
 * 初始化节点配置
 */
export function initializeNodeConfigs() {
  // 清空现有配置
  nodeConfigs.clear()
  
  // 从静态配置中加载所有节点配置
  NODE_LIBRARY_CONFIG.categories.forEach(category => {
    category.nodes.forEach(node => {
      nodeConfigs.set(node.type, { ...node })
    })
  })
  
  // 触发配置更新
  configVersion.value++
}

/**
 * 获取节点配置（响应式）
 */
export function useNodeConfig(nodeType: string) {
  return computed(() => {
    // 依赖configVersion来确保响应式更新
    configVersion.value
    return nodeConfigs.get(nodeType)
  })
}

/**
 * 更新节点配置
 */
export function updateNodeConfig(nodeType: string, updates: Partial<NodeLibraryItem>) {
  const existingConfig = nodeConfigs.get(nodeType)
  if (existingConfig) {
    // 深度合并配置
    const updatedConfig = {
      ...existingConfig,
      ...updates,
      // 特殊处理嵌套对象
      handles: updates.handles ? { ...existingConfig.handles, ...updates.handles } : existingConfig.handles,
      defaultData: updates.defaultData ? { ...existingConfig.defaultData, ...updates.defaultData } : existingConfig.defaultData
    }
    
    nodeConfigs.set(nodeType, updatedConfig)
    
    // 触发配置更新
    configVersion.value++
    
    console.log(`节点配置已更新: ${nodeType}`, updatedConfig)
  }
}

/**
 * 批量更新节点配置
 */
export function updateMultipleNodeConfigs(updates: Record<string, Partial<NodeLibraryItem>>) {
  Object.entries(updates).forEach(([nodeType, config]) => {
    const existingConfig = nodeConfigs.get(nodeType)
    if (existingConfig) {
      const updatedConfig = {
        ...existingConfig,
        ...config,
        handles: config.handles ? { ...existingConfig.handles, ...config.handles } : existingConfig.handles,
        defaultData: config.defaultData ? { ...existingConfig.defaultData, ...config.defaultData } : existingConfig.defaultData
      }
      nodeConfigs.set(nodeType, updatedConfig)
    }
  })
  
  // 触发配置更新
  configVersion.value++
  
  console.log('批量节点配置已更新', updates)
}

/**
 * 重置节点配置到默认值
 */
export function resetNodeConfig(nodeType: string) {
  // 从原始配置中查找默认配置
  for (const category of NODE_LIBRARY_CONFIG.categories) {
    const originalNode = category.nodes.find(n => n.type === nodeType)
    if (originalNode) {
      nodeConfigs.set(nodeType, { ...originalNode })
      configVersion.value++
      console.log(`节点配置已重置: ${nodeType}`)
      break
    }
  }
}

/**
 * 获取所有节点配置
 */
export function getAllNodeConfigs() {
  return computed(() => {
    configVersion.value // 确保响应式
    return Array.from(nodeConfigs.values())
  })
}

/**
 * 根据分类获取节点配置
 */
export function getNodeConfigsByCategory(categoryName: string) {
  return computed(() => {
    configVersion.value // 确保响应式
    return Array.from(nodeConfigs.values()).filter(node => {
      // 从原始配置中查找分类信息
      for (const category of NODE_LIBRARY_CONFIG.categories) {
        if (category.name === categoryName && category.nodes.some(n => n.type === node.type)) {
          return true
        }
      }
      return false
    })
  })
}

/**
 * 监听配置变更
 */
export function watchNodeConfigChanges(callback: (version: number) => void) {
  return watch(configVersion, callback, { immediate: true })
}

/**
 * 导出配置到JSON
 */
export function exportNodeConfigs() {
  const configs: Record<string, NodeLibraryItem> = {}
  nodeConfigs.forEach((config, type) => {
    configs[type] = config
  })
  return JSON.stringify(configs, null, 2)
}

/**
 * 从JSON导入配置
 */
export function importNodeConfigs(jsonString: string) {
  try {
    const configs = JSON.parse(jsonString) as Record<string, NodeLibraryItem>
    Object.entries(configs).forEach(([type, config]) => {
      nodeConfigs.set(type, config)
    })
    configVersion.value++
    console.log('节点配置导入成功')
    return true
  } catch (error) {
    console.error('节点配置导入失败:', error)
    return false
  }
}

// 自动初始化
initializeNodeConfigs()
