package com.xhcai.modules.ai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.Map;

/**
 * AI聊天请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "AI聊天请求")
public class AiChatRequestDTO {

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "session_123456")
    @Size(max = 100, message = "会话ID长度不能超过100个字符")
    private String sessionId;

    /**
     * 用户消息
     */
    @Schema(description = "用户消息", example = "你好，请介绍一下你自己")
    @NotBlank(message = "用户消息不能为空")
    @Size(max = 10000, message = "用户消息长度不能超过10000个字符")
    private String message;

    /**
     * AI模型名称
     */
    @Schema(description = "AI模型名称", example = "gpt-3.5-turbo")
    @Size(max = 50, message = "AI模型名称长度不能超过50个字符")
    private String modelName;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型", example = "text", allowableValues = {"text", "image", "file"})
    @Pattern(regexp = "^(text|image|file)$", message = "消息类型必须为text、image或file")
    private String messageType = "text";

    /**
     * 是否流式响应
     */
    @Schema(description = "是否流式响应", example = "false")
    private Boolean stream = false;

    /**
     * 温度参数
     */
    @Schema(description = "温度参数", example = "0.7")
    private Double temperature;

    /**
     * 最大token数
     */
    @Schema(description = "最大token数", example = "4096")
    private Integer maxTokens;

    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词")
    @Size(max = 1000, message = "系统提示词长度不能超过1000个字符")
    private String systemPrompt;

    /**
     * 是否包含历史对话
     */
    @Schema(description = "是否包含历史对话", example = "true")
    private Boolean includeHistory = true;

    /**
     * 历史对话数量限制
     */
    @Schema(description = "历史对话数量限制", example = "10")
    private Integer historyLimit = 10;

    // ==================== Dify API 格式字段 ====================

    /**
     * 输入参数（Dify格式）
     */
    @Schema(description = "输入参数", example = "{}")
    private Map<String, Object> inputs;

    /**
     * 查询内容（Dify格式）
     */
    @Schema(description = "查询内容", example = "What are the specs of the iPhone 13 Pro Max?")
    private String query;

    /**
     * 响应模式（Dify格式）
     */
    @Schema(description = "响应模式", example = "streaming", allowableValues = {"blocking", "streaming"})
    private String responseMode = "streaming";

    /**
     * 会话ID（Dify格式）
     */
    @Schema(description = "会话ID", example = "")
    private String conversationId;

    /**
     * 用户标识（Dify格式）
     */
    @Schema(description = "用户标识", example = "abc-123")
    private String user;

    /**
     * 文件列表（Dify格式）
     */
    @Schema(description = "文件列表")
    private List<FileInfo> files;

    /**
     * 文件信息
     */
    @Schema(description = "文件信息")
    public static class FileInfo {
        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "image", allowableValues = {"image", "document", "audio", "video"})
        private String type;

        /**
         * 传输方式
         */
        @Schema(description = "传输方式", example = "remote_url", allowableValues = {"remote_url", "local_file"})
        private String transferMethod;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://cloud.dify.ai/logo/logo-site.png")
        private String url;

        /**
         * 文件ID（本地文件时使用）
         */
        @Schema(description = "文件ID")
        private String fileId;

        /**
         * 文件名
         */
        @Schema(description = "文件名")
        private String fileName;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小")
        private Long fileSize;

        // Getters and Setters
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getTransferMethod() {
            return transferMethod;
        }

        public void setTransferMethod(String transferMethod) {
            this.transferMethod = transferMethod;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        @Override
        public String toString() {
            return "FileInfo{" +
                    "type='" + type + '\'' +
                    ", transferMethod='" + transferMethod + '\'' +
                    ", url='" + url + '\'' +
                    ", fileId='" + fileId + '\'' +
                    ", fileName='" + fileName + '\'' +
                    ", fileSize=" + fileSize +
                    '}';
        }
    }

    // ==================== Getters and Setters ====================
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public Boolean getIncludeHistory() {
        return includeHistory;
    }

    public void setIncludeHistory(Boolean includeHistory) {
        this.includeHistory = includeHistory;
    }

    public Integer getHistoryLimit() {
        return historyLimit;
    }

    public void setHistoryLimit(Integer historyLimit) {
        this.historyLimit = historyLimit;
    }

    // ==================== Dify API 格式字段的 Getters and Setters ====================

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getResponseMode() {
        return responseMode;
    }

    public void setResponseMode(String responseMode) {
        this.responseMode = responseMode;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public List<FileInfo> getFiles() {
        return files;
    }

    public void setFiles(List<FileInfo> files) {
        this.files = files;
    }

    // ==================== 兼容性方法 ====================

    /**
     * 获取实际的查询内容（优先使用 query，回退到 message）
     */
    public String getActualQuery() {
        return query != null ? query : message;
    }

    /**
     * 获取实际的用户标识（优先使用 user，回退到当前用户ID）
     */
    public String getActualUser() {
        return user;
    }

    /**
     * 获取实际的会话ID（优先使用 conversationId，回退到 sessionId）
     */
    public String getActualConversationId() {
        return conversationId != null ? conversationId : sessionId;
    }

    /**
     * 是否为 Dify 格式的请求
     */
    public boolean isDifyFormat() {
        return query != null || inputs != null || user != null || conversationId != null || files != null;
    }

    /**
     * 转换为 Dify 请求格式的 Map
     */
    public Map<String, Object> toDifyRequestMap() {
        Map<String, Object> requestMap = new java.util.HashMap<>();

        // 基本字段
        requestMap.put("inputs", inputs != null ? inputs : new java.util.HashMap<>());
        requestMap.put("query", getActualQuery());
        requestMap.put("response_mode", responseMode != null ? responseMode : "streaming");
        requestMap.put("conversation_id", getActualConversationId() != null ? getActualConversationId() : "");
        requestMap.put("user", getActualUser() != null ? getActualUser() : "default-user");
        requestMap.put("files", files != null ? files : new java.util.ArrayList<>());

        return requestMap;
    }

    @Override
    public String toString() {
        return "AiChatRequestDTO{" +
                "sessionId='" + sessionId + '\'' +
                ", message='" + message + '\'' +
                ", modelName='" + modelName + '\'' +
                ", messageType='" + messageType + '\'' +
                ", stream=" + stream +
                ", temperature=" + temperature +
                ", maxTokens=" + maxTokens +
                ", systemPrompt='" + systemPrompt + '\'' +
                ", includeHistory=" + includeHistory +
                ", historyLimit=" + historyLimit +
                ", inputs=" + inputs +
                ", query='" + query + '\'' +
                ", responseMode='" + responseMode + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", user='" + user + '\'' +
                ", files=" + files +
                '}';
    }
}
