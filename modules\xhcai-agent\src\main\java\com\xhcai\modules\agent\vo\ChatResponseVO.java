package com.xhcai.modules.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天响应VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "聊天响应信息")
public class ChatResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    @Schema(description = "消息ID", example = "msg_001")
    private String messageId;

    /**
     * 对话ID
     */
    @Schema(description = "对话ID", example = "conv_001")
    private String conversationId;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID", example = "agent_001")
    private String agentId;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称", example = "客服助手")
    private String agentName;

    /**
     * 智能体头像
     */
    @Schema(description = "智能体头像", example = "https://example.com/avatar.jpg")
    private String agentAvatar;

    /**
     * 回复内容
     */
    @Schema(description = "回复内容", example = "您好！我是客服助手，很高兴为您服务...")
    private String content;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型", example = "assistant")
    private String messageType;

    /**
     * 消息状态
     */
    @Schema(description = "消息状态", example = "sent")
    private String status;

    /**
     * 是否完成
     */
    @Schema(description = "是否完成", example = "true")
    private Boolean finished;

    /**
     * 处理时间（毫秒）
     */
    @Schema(description = "处理时间（毫秒）", example = "1500")
    private Long processingTime;

    /**
     * token消耗
     */
    @Schema(description = "token消耗", example = "150")
    private Integer tokens;

    /**
     * 输入token消耗
     */
    @Schema(description = "输入token消耗", example = "80")
    private Integer inputTokens;

    /**
     * 输出token消耗
     */
    @Schema(description = "输出token消耗", example = "70")
    private Integer outputTokens;

    /**
     * 费用（分）
     */
    @Schema(description = "费用（分）", example = "5")
    private Long cost;

    /**
     * 工具调用信息
     */
    @Schema(description = "工具调用信息")
    private List<ToolCall> toolCalls;

    /**
     * 建议问题
     */
    @Schema(description = "建议问题")
    private List<String> suggestedQuestions;

    /**
     * 引用文档
     */
    @Schema(description = "引用文档")
    private List<DocumentReference> references;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息", example = "网络连接超时")
    private String errorMessage;

    /**
     * 扩展信息
     */
    @Schema(description = "扩展信息")
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 工具调用信息
     */
    @Schema(description = "工具调用信息")
    public static class ToolCall implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 工具名称
         */
        @Schema(description = "工具名称", example = "search")
        private String name;

        /**
         * 工具参数
         */
        @Schema(description = "工具参数")
        private Map<String, Object> arguments;

        /**
         * 工具结果
         */
        @Schema(description = "工具结果")
        private Object result;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "success")
        private String status;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String error;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Map<String, Object> getArguments() {
            return arguments;
        }

        public void setArguments(Map<String, Object> arguments) {
            this.arguments = arguments;
        }

        public Object getResult() {
            return result;
        }

        public void setResult(Object result) {
            this.result = result;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }
    }

    /**
     * 文档引用信息
     */
    @Schema(description = "文档引用信息")
    public static class DocumentReference implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 文档ID
         */
        @Schema(description = "文档ID", example = "doc_001")
        private String documentId;

        /**
         * 文档名称
         */
        @Schema(description = "文档名称", example = "产品手册.pdf")
        private String documentName;

        /**
         * 文档片段
         */
        @Schema(description = "文档片段", example = "产品具有以下特性...")
        private String snippet;

        /**
         * 相关性评分
         */
        @Schema(description = "相关性评分", example = "0.95")
        private Double score;

        /**
         * 页码
         */
        @Schema(description = "页码", example = "15")
        private Integer pageNumber;

        // Getters and Setters
        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public String getDocumentName() {
            return documentName;
        }

        public void setDocumentName(String documentName) {
            this.documentName = documentName;
        }

        public String getSnippet() {
            return snippet;
        }

        public void setSnippet(String snippet) {
            this.snippet = snippet;
        }

        public Double getScore() {
            return score;
        }

        public void setScore(Double score) {
            this.score = score;
        }

        public Integer getPageNumber() {
            return pageNumber;
        }

        public void setPageNumber(Integer pageNumber) {
            this.pageNumber = pageNumber;
        }
    }

    // Getters and Setters
    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentAvatar() {
        return agentAvatar;
    }

    public void setAgentAvatar(String agentAvatar) {
        this.agentAvatar = agentAvatar;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getFinished() {
        return finished;
    }

    public void setFinished(Boolean finished) {
        this.finished = finished;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public Integer getTokens() {
        return tokens;
    }

    public void setTokens(Integer tokens) {
        this.tokens = tokens;
    }

    public Integer getInputTokens() {
        return inputTokens;
    }

    public void setInputTokens(Integer inputTokens) {
        this.inputTokens = inputTokens;
    }

    public Integer getOutputTokens() {
        return outputTokens;
    }

    public void setOutputTokens(Integer outputTokens) {
        this.outputTokens = outputTokens;
    }

    public Long getCost() {
        return cost;
    }

    public void setCost(Long cost) {
        this.cost = cost;
    }

    public List<ToolCall> getToolCalls() {
        return toolCalls;
    }

    public void setToolCalls(List<ToolCall> toolCalls) {
        this.toolCalls = toolCalls;
    }

    public List<String> getSuggestedQuestions() {
        return suggestedQuestions;
    }

    public void setSuggestedQuestions(List<String> suggestedQuestions) {
        this.suggestedQuestions = suggestedQuestions;
    }

    public List<DocumentReference> getReferences() {
        return references;
    }

    public void setReferences(List<DocumentReference> references) {
        this.references = references;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
