package com.xhcai.modules.dify.dto.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Dify 智能体列表响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@NoArgsConstructor
@Schema(description = "Dify 智能体列表响应")
public class DifyAppsListResponseDTO {

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    private Integer page;

    /**
     * 每页限制数量
     */
    @Schema(description = "每页限制数量", example = "30")
    private Integer limit;

    /**
     * 总数量
     */
    @Schema(description = "总数量", example = "9")
    private Integer total;

    /**
     * 是否有更多数据
     */
    @JsonProperty("has_more")
    @Schema(description = "是否有更多数据")
    private Boolean hasMore;

    /**
     * 智能体数据列表
     */
    @Schema(description = "智能体数据列表")
    private List<DifyAppDTO> data;

    /**
     * 智能体信息
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "智能体信息")
    public static class DifyAppDTO {
        
        /**
         * 智能体ID
         */
        @Schema(description = "智能体ID")
        private String id;

        /**
         * 智能体名称
         */
        @Schema(description = "智能体名称")
        private String name;

        /**
         * 最大活跃请求数
         */
        @JsonProperty("max_active_requests")
        @Schema(description = "最大活跃请求数")
        private Integer maxActiveRequests;

        /**
         * 描述
         */
        @Schema(description = "描述")
        private String description;

        /**
         * 模式
         */
        @Schema(description = "模式", example = "advanced-chat")
        private String mode;

        /**
         * 图标类型
         */
        @JsonProperty("icon_type")
        @Schema(description = "图标类型", example = "emoji")
        private String iconType;

        /**
         * 图标
         */
        @Schema(description = "图标", example = "🤖")
        private String icon;

        /**
         * 图标背景色
         */
        @JsonProperty("icon_background")
        @Schema(description = "图标背景色", example = "#FFEAD5")
        private String iconBackground;

        /**
         * 图标URL
         */
        @JsonProperty("icon_url")
        @Schema(description = "图标URL")
        private String iconUrl;

        /**
         * 模型配置
         */
        @JsonProperty("model_config")
        @Schema(description = "模型配置")
        private Object modelConfig;

        /**
         * 工作流信息
         */
        @Schema(description = "工作流信息")
        private DifyWorkflowDTO workflow;

        /**
         * 是否使用图标作为回答图标
         */
        @JsonProperty("use_icon_as_answer_icon")
        @Schema(description = "是否使用图标作为回答图标")
        private Boolean useIconAsAnswerIcon;

        /**
         * 创建者ID
         */
        @JsonProperty("created_by")
        @Schema(description = "创建者ID")
        private String createdBy;

        /**
         * 创建时间
         */
        @JsonProperty("created_at")
        @Schema(description = "创建时间")
        private Long createdAt;

        /**
         * 更新者ID
         */
        @JsonProperty("updated_by")
        @Schema(description = "更新者ID")
        private String updatedBy;

        /**
         * 更新时间
         */
        @JsonProperty("updated_at")
        @Schema(description = "更新时间")
        private Long updatedAt;

        /**
         * 标签列表
         */
        @Schema(description = "标签列表")
        private List<String> tags;

        /**
         * 访问模式
         */
        @JsonProperty("access_mode")
        @Schema(description = "访问模式")
        private String accessMode;

        /**
         * 创建用户名称
         */
        @JsonProperty("create_user_name")
        @Schema(description = "创建用户名称")
        private String createUserName;

        /**
         * 作者名称
         */
        @JsonProperty("author_name")
        @Schema(description = "作者名称")
        private String authorName;
    }

    /**
     * 工作流信息
     */
    @Data
    @NoArgsConstructor
    @Schema(description = "工作流信息")
    public static class DifyWorkflowDTO {
        
        /**
         * 工作流ID
         */
        @Schema(description = "工作流ID")
        private String id;

        /**
         * 创建者ID
         */
        @JsonProperty("created_by")
        @Schema(description = "创建者ID")
        private String createdBy;

        /**
         * 创建时间
         */
        @JsonProperty("created_at")
        @Schema(description = "创建时间")
        private Long createdAt;

        /**
         * 更新者ID
         */
        @JsonProperty("updated_by")
        @Schema(description = "更新者ID")
        private String updatedBy;

        /**
         * 更新时间
         */
        @JsonProperty("updated_at")
        @Schema(description = "更新时间")
        private Long updatedAt;
    }
}
