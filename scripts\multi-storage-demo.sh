#!/bin/bash

# 多存储插件调用演示脚本
# 演示如何调用指定的存储插件（MinIO和FTP）

set -e

echo "=== 多存储插件调用演示 ==="

BASE_URL="http://localhost:8080"
TEST_FILE="test-file.txt"
BUCKET_NAME="test-bucket"

# 创建测试文件
create_test_file() {
    echo "创建测试文件..."
    echo "这是一个测试文件，用于演示多存储插件调用 - $(date)" > "$TEST_FILE"
    echo "✓ 测试文件创建完成: $TEST_FILE"
}

# 检查服务状态
check_service() {
    echo "检查服务状态..."
    if curl -s "$BASE_URL/actuator/health" > /dev/null; then
        echo "✓ 服务正在运行"
    else
        echo "✗ 服务未运行，请先启动服务"
        exit 1
    fi
}

# 获取可用的存储类型
get_storage_types() {
    echo ""
    echo "=== 获取可用的存储类型 ==="
    
    response=$(curl -s "$BASE_URL/api/storage/types")
    echo "可用存储类型: $response"
    
    # 解析存储类型（简化处理）
    if echo "$response" | grep -q "minio"; then
        echo "✓ MinIO存储插件可用"
        MINIO_AVAILABLE=true
    else
        echo "✗ MinIO存储插件不可用"
        MINIO_AVAILABLE=false
    fi
    
    if echo "$response" | grep -q "ftp"; then
        echo "✓ FTP存储插件可用"
        FTP_AVAILABLE=true
    else
        echo "✗ FTP存储插件不可用"
        FTP_AVAILABLE=false
    fi
}

# 获取和设置默认存储类型
manage_default_storage() {
    echo ""
    echo "=== 管理默认存储类型 ==="
    
    # 获取当前默认存储类型
    default_type=$(curl -s "$BASE_URL/api/storage/default-type")
    echo "当前默认存储类型: $default_type"
    
    # 设置默认存储类型为MinIO
    if [ "$MINIO_AVAILABLE" = true ]; then
        echo "设置默认存储类型为MinIO..."
        response=$(curl -s -X POST "$BASE_URL/api/storage/default-type?storageType=minio")
        echo "设置结果: $response"
    fi
}

# 使用默认存储上传文件
upload_with_default_storage() {
    echo ""
    echo "=== 使用默认存储上传文件 ==="
    
    echo "上传文件到默认存储..."
    response=$(curl -s -X POST \
        -F "bucketName=$BUCKET_NAME" \
        -F "file=@$TEST_FILE" \
        "$BASE_URL/api/storage/upload")
    
    echo "上传结果: $response"
    
    # 提取URL（简化处理）
    if echo "$response" | grep -q "url"; then
        echo "✓ 文件上传成功（默认存储）"
    else
        echo "✗ 文件上传失败（默认存储）"
    fi
}

# 使用指定存储类型上传文件
upload_with_specific_storage() {
    local storage_type=$1
    local available=$2
    
    echo ""
    echo "=== 使用 $storage_type 存储上传文件 ==="
    
    if [ "$available" = false ]; then
        echo "✗ $storage_type 存储不可用，跳过测试"
        return
    fi
    
    echo "上传文件到 $storage_type 存储..."
    response=$(curl -s -X POST \
        -F "bucketName=$BUCKET_NAME" \
        -F "file=@$TEST_FILE" \
        "$BASE_URL/api/storage/upload/$storage_type")
    
    echo "上传结果: $response"
    
    if echo "$response" | grep -q "url"; then
        echo "✓ 文件上传成功（$storage_type 存储）"
        
        # 保存文件信息用于后续测试
        if [ "$storage_type" = "minio" ]; then
            MINIO_OBJECT_NAME="$TEST_FILE"
        elif [ "$storage_type" = "ftp" ]; then
            FTP_OBJECT_NAME="$TEST_FILE"
        fi
    else
        echo "✗ 文件上传失败（$storage_type 存储）"
    fi
}

# 从指定存储下载文件
download_from_specific_storage() {
    local storage_type=$1
    local available=$2
    local object_name=$3
    
    echo ""
    echo "=== 从 $storage_type 存储下载文件 ==="
    
    if [ "$available" = false ] || [ -z "$object_name" ]; then
        echo "✗ $storage_type 存储不可用或文件不存在，跳过测试"
        return
    fi
    
    echo "从 $storage_type 存储下载文件..."
    download_file="downloaded-from-$storage_type-$TEST_FILE"
    
    curl -s "$BASE_URL/api/storage/download/$storage_type?bucketName=$BUCKET_NAME&objectName=$object_name" \
        -o "$download_file"
    
    if [ -f "$download_file" ] && [ -s "$download_file" ]; then
        echo "✓ 文件下载成功（$storage_type 存储）: $download_file"
        echo "文件内容预览:"
        head -n 2 "$download_file"
    else
        echo "✗ 文件下载失败（$storage_type 存储）"
    fi
}

# 获取文件信息
get_file_info() {
    local storage_type=$1
    local available=$2
    local object_name=$3
    
    echo ""
    echo "=== 获取 $storage_type 存储文件信息 ==="
    
    if [ "$available" = false ] || [ -z "$object_name" ]; then
        echo "✗ $storage_type 存储不可用或文件不存在，跳过测试"
        return
    fi
    
    echo "获取 $storage_type 存储文件信息..."
    response=$(curl -s "$BASE_URL/api/storage/$storage_type/info?bucketName=$BUCKET_NAME&objectName=$object_name")
    
    echo "文件信息: $response"
}

# 列出文件
list_files() {
    local storage_type=$1
    local available=$2
    
    echo ""
    echo "=== 列出 $storage_type 存储文件 ==="
    
    if [ "$available" = false ]; then
        echo "✗ $storage_type 存储不可用，跳过测试"
        return
    fi
    
    echo "列出 $storage_type 存储文件..."
    response=$(curl -s "$BASE_URL/api/storage/$storage_type/list?bucketName=$BUCKET_NAME&maxKeys=10")
    
    echo "文件列表: $response"
}

# 性能对比测试
performance_comparison() {
    echo ""
    echo "=== 存储性能对比测试 ==="
    
    echo "执行性能对比测试..."
    response=$(curl -s -X POST \
        -F "bucketName=$BUCKET_NAME" \
        -F "file=@$TEST_FILE" \
        "$BASE_URL/api/storage/performance-test")
    
    echo "性能测试结果:"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# 清理测试文件
cleanup() {
    echo ""
    echo "=== 清理测试文件 ==="
    
    # 删除本地测试文件
    if [ -f "$TEST_FILE" ]; then
        rm "$TEST_FILE"
        echo "✓ 删除本地测试文件: $TEST_FILE"
    fi
    
    # 删除下载的文件
    for file in downloaded-from-*-$TEST_FILE; do
        if [ -f "$file" ]; then
            rm "$file"
            echo "✓ 删除下载文件: $file"
        fi
    done
    
    # 删除远程文件
    if [ "$MINIO_AVAILABLE" = true ] && [ -n "$MINIO_OBJECT_NAME" ]; then
        echo "删除MinIO存储文件..."
        curl -s -X DELETE "$BASE_URL/api/storage/minio?bucketName=$BUCKET_NAME&objectName=$MINIO_OBJECT_NAME"
        echo "✓ MinIO文件删除请求已发送"
    fi
    
    if [ "$FTP_AVAILABLE" = true ] && [ -n "$FTP_OBJECT_NAME" ]; then
        echo "删除FTP存储文件..."
        curl -s -X DELETE "$BASE_URL/api/storage/ftp?bucketName=$BUCKET_NAME&objectName=$FTP_OBJECT_NAME"
        echo "✓ FTP文件删除请求已发送"
    fi
}

# 主函数
main() {
    echo "开始多存储插件调用演示..."
    echo "服务地址: $BASE_URL"
    echo "测试存储桶: $BUCKET_NAME"
    echo ""
    
    # 初始化
    create_test_file
    check_service
    get_storage_types
    manage_default_storage
    
    # 上传测试
    upload_with_default_storage
    upload_with_specific_storage "minio" "$MINIO_AVAILABLE"
    upload_with_specific_storage "ftp" "$FTP_AVAILABLE"
    
    # 下载测试
    download_from_specific_storage "minio" "$MINIO_AVAILABLE" "$MINIO_OBJECT_NAME"
    download_from_specific_storage "ftp" "$FTP_AVAILABLE" "$FTP_OBJECT_NAME"
    
    # 信息查询测试
    get_file_info "minio" "$MINIO_AVAILABLE" "$MINIO_OBJECT_NAME"
    get_file_info "ftp" "$FTP_AVAILABLE" "$FTP_OBJECT_NAME"
    
    # 文件列表测试
    list_files "minio" "$MINIO_AVAILABLE"
    list_files "ftp" "$FTP_AVAILABLE"
    
    # 性能对比测试
    performance_comparison
    
    # 清理
    cleanup
    
    echo ""
    echo "=== 多存储插件调用演示完成 ==="
    echo ""
    echo "关键要点:"
    echo "1. ✅ 支持多个同类型存储插件并存"
    echo "2. ✅ 可以指定使用特定的存储插件"
    echo "3. ✅ 支持设置和切换默认存储插件"
    echo "4. ✅ 提供统一的API接口调用不同插件"
    echo "5. ✅ 支持性能对比和插件选择"
    echo ""
    echo "API调用示例:"
    echo "- 获取存储类型: GET $BASE_URL/api/storage/types"
    echo "- 使用默认存储: POST $BASE_URL/api/storage/upload"
    echo "- 使用MinIO存储: POST $BASE_URL/api/storage/upload/minio"
    echo "- 使用FTP存储: POST $BASE_URL/api/storage/upload/ftp"
    echo "- 从MinIO下载: GET $BASE_URL/api/storage/download/minio"
    echo "- 从FTP下载: GET $BASE_URL/api/storage/download/ftp"
}

# 执行主函数
main "$@"
