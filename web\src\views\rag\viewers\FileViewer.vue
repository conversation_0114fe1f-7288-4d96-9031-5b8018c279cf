<template>
  <div class="file-viewer">
    <component
      :is="viewerComponent"
      :file="file"
      :segments="segments"
      :selectedSegmentId="selectedSegmentId"
      @download="handleDownload"
      @segmentHover="handleSegmentHover"
      @segmentLeave="handleSegmentLeave"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import PdfViewer from './PdfViewer.vue'
import TextViewer from './TextViewer.vue'
import WordViewer from './WordViewer.vue'
import ExcelViewer from './ExcelViewer.vue'
import ImageViewer from './ImageViewer.vue'

// Props
interface Props {
  file: any
  segments?: any[]
  selectedSegmentId?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  download: [file: any]
  close: []
  segmentHover: [segmentInfo: any]
  segmentLeave: []
}>()

// 计算属性
const viewerComponent = computed(() => {
  if (!props.file?.type) return TextViewer // 默认使用 TextViewer

  const fileType = props.file.type.toLowerCase()

  // 根据文件类型返回对应的查看器组件
  switch (fileType) {
    case 'pdf':
      return PdfViewer
    case 'txt':
    case 'md':
    case 'json':
    case 'xml':
    case 'csv':
    case 'log':
    case 'js':
    case 'ts':
    case 'html':
    case 'css':
    case 'py':
    case 'java':
    case 'cpp':
    case 'c':
    case 'php':
    case 'rb':
    case 'go':
    case 'rs':
    case 'sql':
    case 'yaml':
    case 'yml':
    case 'ini':
    case 'conf':
      return TextViewer
    case 'doc':
    case 'docx':
      return WordViewer
    case 'xlsx':
    case 'xls':
      return ExcelViewer
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
    case 'webp':
    case 'ico':
      return ImageViewer
    default:
      return TextViewer // 默认使用 TextViewer
  }
})

// 方法
const handleDownload = (file: any) => {
  emit('download', file)
  // 这里应该实现实际的下载逻辑
  alert(`下载文件: ${file?.name}`)
}

const handleSegmentHover = (segmentInfo: any) => {
  emit('segmentHover', segmentInfo)
}

const handleSegmentLeave = () => {
  emit('segmentLeave')
}


</script>

<style scoped>
.file-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
