// Element Plus 选择器组件类型定义

// 基础选择器配置
export interface SelectorConfig {
  multiple?: boolean
  clearable?: boolean
  placeholder?: string
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  filterable?: boolean
  remote?: boolean
  loading?: boolean
  noDataText?: string
  noMatchText?: string
  loadingText?: string
  popperClass?: string
  teleported?: boolean
  persistent?: boolean
  automaticDropdown?: boolean
  fitInputWidth?: boolean
  suffixIcon?: string
  tagType?: 'success' | 'info' | 'warning' | 'danger'
  maxCollapseTags?: number
  collapseTagsTooltip?: boolean
  effect?: 'dark' | 'light'
  reserveKeyword?: boolean
}

// 部门选择器选项
export interface DeptSelectorOption {
  value: string
  label: string
  deptName?: string
  name?: string
  parentId?: string
  children?: DeptSelectorOption[]
  disabled?: boolean
  level?: number
  path?: string[]
  [key: string]: any
}

// 权限选择器选项
export interface PermissionSelectorOption {
  value: string
  label: string
  permissionName?: string
  name?: string
  type?: 'menu' | 'button' | 'api'
  parentId?: string
  children?: PermissionSelectorOption[]
  disabled?: boolean
  checked?: boolean
  indeterminate?: boolean
  [key: string]: any
}

// 状态选择器选项
export interface StatusSelectorOption {
  value: string | number
  label: string
  description?: string
  color?: string
  bgColor?: string
  disabled?: boolean
  icon?: string
  [key: string]: any
}

// 用户选择器选项
export interface UserSelectorOption {
  value: string
  label: string
  username?: string
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  avatar?: string
  deptId?: string
  deptName?: string
  roleIds?: string[]
  roleNames?: string[]
  status?: string | number
  disabled?: boolean
  [key: string]: any
}

// 角色选择器选项
export interface RoleSelectorOption {
  value: string
  label: string
  roleKey?: string
  roleName?: string
  description?: string
  status?: string | number
  userCount?: number
  disabled?: boolean
  [key: string]: any
}

// 选择器事件
export interface SelectorEmits {
  'update:modelValue': [value: any]
  'change': [value: any, option?: any]
  'select': [value: any, option: any]
  'remove': [value: any]
  'clear': []
  'visible-change': [visible: boolean]
  'remove-tag': [value: any]
  'focus': [event: FocusEvent]
  'blur': [event: FocusEvent]
}

// 树形选择器配置
export interface TreeSelectorConfig extends SelectorConfig {
  checkStrictly?: boolean
  showCheckbox?: boolean
  expandOnClickNode?: boolean
  defaultExpandAll?: boolean
  defaultExpandedKeys?: string[]
  nodeKey?: string
  props?: {
    label?: string
    children?: string
    disabled?: string
    isLeaf?: string
  }
  renderAfterExpand?: boolean
  load?: (node: any, resolve: (data: any[]) => void) => void
  renderContent?: (h: any, context: { node: any; data: any; store: any }) => any
  highlightCurrent?: boolean
  defaultCheckedKeys?: string[]
  currentNodeKey?: string | number
  accordion?: boolean
  indent?: number
  iconClass?: string
  lazy?: boolean
  draggable?: boolean
  allowDrag?: (node: any) => boolean
  allowDrop?: (draggingNode: any, dropNode: any, type: string) => boolean
}
