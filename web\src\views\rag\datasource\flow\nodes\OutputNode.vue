<template>
  <BaseNode
    :type="data.type"
    :label="data.label"
    :icon="nodeConfig.icon"
    :color="nodeConfig.color"
    :gradient="nodeConfig.gradient"
    :label-color="nodeConfig.labelColor"
    :icon-color="nodeConfig.iconColor"
    :selected="selected"
    :status="data.status"
    :error-message="data.errorMessage"
    :source-handles="nodeConfig.handles.source"
    :target-handles="nodeConfig.handles.target"
    @configure="handleConfigure"
    @delete="handleDelete"
  >
    <!-- 输出节点特定内容 -->
    <div class="output-content">
      <div class="config-summary">
        <div v-if="data.config.host" class="config-item">
          <i class="fas fa-server"></i>
          <span>{{ data.config.host }}:{{ data.config.port || getDefaultPort() }}</span>
        </div>
        <div v-if="data.config.className" class="config-item">
          <i class="fas fa-layer-group"></i>
          <span>类: {{ data.config.className }}</span>
        </div>
        <div v-if="data.config.collection" class="config-item">
          <i class="fas fa-folder"></i>
          <span>集合: {{ data.config.collection }}</span>
        </div>
      </div>
      
      <div v-if="data.stats" class="stats">
        <div class="stat-item">
          <span class="stat-label">已存储</span>
          <span class="stat-value">{{ formatNumber(data.stats.storedCount || 0) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">存储大小</span>
          <span class="stat-value">{{ formatSize(data.stats.storageSize || 0) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">处理进度</span>
          <span class="stat-value">{{ data.stats.progress || 0 }}%</span>
        </div>
      </div>

      <!-- 进度条 -->
      <div v-if="data.status === 'running' && data.stats?.progress" class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: `${data.stats.progress}%` }"
        ></div>
      </div>

      <!-- 连接状态 -->
      <div class="connection-status" :class="`status-${connectionStatus}`">
        <i :class="connectionIcon"></i>
        <span>{{ connectionText }}</span>
      </div>
    </div>
  </BaseNode>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseNode from './BaseNode.vue'
import { NODE_LIBRARY_CONFIG } from '../config/nodeLibrary'
import type { NodeProps } from '@vue-flow/core'

// Props
interface OutputNodeData {
  type: string
  label: string
  config: {
    host?: string
    port?: number
    className?: string
    collection?: string
    apiKey?: string
  }
  status?: 'idle' | 'running' | 'success' | 'error'
  errorMessage?: string
  stats?: {
    storedCount: number
    storageSize: number
    progress: number
  }
  connectionStatus?: 'connected' | 'disconnected' | 'connecting' | 'error'
}

const props = defineProps<NodeProps<OutputNodeData>>()

// Emits
const emit = defineEmits<{
  'configure': [nodeId: string]
  'delete': [nodeId: string]
}>()

// 计算属性
const nodeConfig = computed(() => {
  for (const category of NODE_LIBRARY_CONFIG.categories) {
    const node = category.nodes.find(n => n.type === props.data.type)
    if (node) return node
  }
  // 默认配置
  return {
    icon: 'fas fa-database',
    color: '#8b5cf6',
    gradient: 'linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%)',
    labelColor: '#6b21a8',
    iconColor: '#8b5cf6',
    handles: {
      source: [],
      target: [{ id: 'input', position: 'left' }]
    }
  }
})

const connectionStatus = computed(() => {
  return props.data.connectionStatus || 'disconnected'
})

const connectionIcon = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'fas fa-check-circle'
    case 'connecting':
      return 'fas fa-spinner fa-spin'
    case 'error':
      return 'fas fa-exclamation-circle'
    default:
      return 'fas fa-times-circle'
  }
})

const connectionText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中'
    case 'error':
      return '连接错误'
    default:
      return '未连接'
  }
})

// 方法
const handleConfigure = () => {
  emit('configure', props.id)
}

const handleDelete = () => {
  emit('delete', props.id)
}

const getDefaultPort = () => {
  switch (props.data.type) {
    case 'weaviate':
      return 8080
    case 'elasticsearch':
      return 9200
    case 'milvus':
      return 19530
    default:
      return 80
  }
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatSize = (bytes: number) => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return size.toFixed(1) + units[unitIndex]
}
</script>

<style scoped>
.output-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-summary {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.config-item i {
  width: 14px;
  text-align: center;
}

.stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}

.progress-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-connected {
  background: #dcfce7;
  color: #166534;
}

.status-connecting {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-error {
  background: #fee2e2;
  color: #dc2626;
}

.status-disconnected {
  background: #f1f5f9;
  color: #64748b;
}
</style>
