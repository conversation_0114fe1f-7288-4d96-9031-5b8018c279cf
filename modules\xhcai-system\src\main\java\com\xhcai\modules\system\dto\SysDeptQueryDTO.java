package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 部门查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "部门查询DTO")
public class SysDeptQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称", example = "技术部")
    @Size(max = 30, message = "部门名称长度不能超过30个字符")
    private String deptName;

    /**
     * 部门编码
     */
    @Schema(description = "部门编码", example = "TECH")
    @Size(max = 30, message = "部门编码长度不能超过30个字符")
    private String deptCode;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String leaderId;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    private String status;

    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID", example = "1")
    private String parentId;

    /**
     * 是否查询子部门
     */
    @Schema(description = "是否查询子部门", example = "true")
    private Boolean includeChildren;

    /**
     * 数据权限SQL
     */
    @Schema(hidden = true)
    private String dataScope;

    // Getters and Setters
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(String leaderId) {
        this.leaderId = leaderId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Boolean getIncludeChildren() {
        return includeChildren;
    }

    public void setIncludeChildren(Boolean includeChildren) {
        this.includeChildren = includeChildren;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    @Override
    public String toString() {
        return "SysDeptQueryDTO{"
                + "deptName='" + deptName + '\''
                + ", deptCode='" + deptCode + '\''
                + ", leaderId='" + leaderId + '\''
                + ", status='" + status + '\''
                + ", parentId=" + parentId
                + ", includeChildren=" + includeChildren
                + '}';
    }
}
