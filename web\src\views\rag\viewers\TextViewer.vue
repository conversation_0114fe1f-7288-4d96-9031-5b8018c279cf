<template>
  <BaseFileViewer :file="file" @download="handleDownload" @refresh="handleRefresh">
    <template #content>
      <div class="text-viewer">
        <div class="text-toolbar">
          <div class="toolbar-left">
            <button class="tool-btn" @click="decreaseFontSize" :disabled="fontSize <= 12">
              <i class="fas fa-minus"></i>
            </button>
            <span class="font-info">{{ fontSize }}px</span>
            <button class="tool-btn" @click="increaseFontSize" :disabled="fontSize >= 24">
              <i class="fas fa-plus"></i>
            </button>
          </div>
          <div class="toolbar-center">
            <button class="tool-btn" :class="{ active: wordWrap }" @click="toggleWordWrap">
              <i class="fas fa-align-justify"></i>
              自动换行
            </button>
            <button class="tool-btn" :class="{ active: showLineNumbers }" @click="toggleLineNumbers">
              <i class="fas fa-list-ol"></i>
              行号
            </button>
          </div>
          <div class="toolbar-right">
            <div class="search-box">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                placeholder="搜索文本..."
                v-model="searchQuery"
                @input="highlightText"
                class="search-input"
              >
            </div>
            <button class="tool-btn" @click="handleDownload" title="下载文件">
              <i class="fas fa-download"></i>
            </button>
            <button class="tool-btn" @click="handleRefresh" title="刷新内容">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>
        
        <div class="text-content" ref="textContainer">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载文本文件...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="loadText">重新加载</button>
          </div>
          
          <div v-else class="text-display">
            <div class="text-wrapper" :class="{ 'word-wrap': wordWrap }">
              <div
                class="text-content-container"
                :style="{ fontSize: fontSize + 'px' }"
                ref="textContentContainer"
              >
                <div v-if="segments && segments.length > 0" class="segmented-content">
                  <div
                    v-for="(segment, index) in segments"
                    :key="segment.id"
                    :id="`segment-${segment.id}`"
                    class="text-segment"
                    :class="{
                      'selected': selectedSegmentId === segment.id,
                      'highlighted': hoveredSegmentId === segment.id,
                      'editing': editingSegmentId === segment.id
                    }"
                    @mouseenter="handleSegmentMouseEnter(segment, $event)"
                    @mouseleave="handleSegmentMouseLeave"
                  >
                    <div class="segment-header">
                      <span class="segment-number">段落 {{ index + 1 }}</span>
                      <div class="segment-actions">
                        <button
                          @click="startEditSegment(segment)"
                          class="action-btn edit-btn"
                          title="编辑分段"
                        >
                          <i class="fas fa-edit"></i>
                        </button>
                        <button
                          @click="deleteSegment(segment.id)"
                          class="action-btn delete-btn"
                          title="删除分段"
                        >
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>

                    <!-- 编辑模式 -->
                    <div v-if="editingSegmentId === segment.id" class="segment-editor">
                      <textarea
                        v-model="editingContent"
                        class="edit-textarea"
                        @keydown.ctrl.enter="saveSegmentEdit"
                        @keydown.esc="cancelSegmentEdit"
                      ></textarea>
                      <div class="edit-actions">
                        <button @click="saveSegmentEdit" class="btn btn-primary">保存</button>
                        <button @click="cancelSegmentEdit" class="btn btn-secondary">取消</button>
                      </div>
                    </div>

                    <!-- 显示模式 -->
                    <div v-else class="segment-display">
                      <pre
                        class="segment-content"
                        v-html="formatSegmentContent(segment.content, index)"
                      ></pre>

                      <!-- 拖拽调整手柄 -->
                      <div class="resize-handles">
                        <div
                          class="resize-handle resize-top"
                          @mousedown="startResize(segment.id, 'top', $event)"
                        ></div>
                        <div
                          class="resize-handle resize-bottom"
                          @mousedown="startResize(segment.id, 'bottom', $event)"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
                <pre
                  v-else
                  class="text-content-pre"
                  v-html="displayContent"
                ></pre>
              </div>
            </div>

            <!-- 分段工具提示 -->
            <div
              v-if="segmentTooltip.visible"
              class="segment-tooltip"
              :style="{
                left: segmentTooltip.x + 'px',
                top: segmentTooltip.y + 'px'
              }"
            >
              {{ segmentTooltip.content }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseFileViewer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import BaseFileViewer from './BaseFileViewer.vue'

// Props
interface Props {
  file: any
  segments?: any[]
  selectedSegmentId?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  download: [file: any]
  segmentHover: [segmentInfo: any]
  segmentLeave: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const content = ref('')
const fontSize = ref(14)
const wordWrap = ref(true)
const showLineNumbers = ref(true)
const searchQuery = ref('')
const textContainer = ref<HTMLElement | null>(null)
const textContentContainer = ref<HTMLElement | null>(null)
const hoveredSegmentId = ref<string | null>(null)
const editingSegmentId = ref<string | null>(null)
const editingContent = ref('')
const resizing = ref<{
  segmentId: string | null
  direction: 'top' | 'bottom' | null
  startY: number
  originalHeight: number
}>({
  segmentId: null,
  direction: null,
  startY: 0,
  originalHeight: 0
})
const segmentTooltip = ref<{
  visible: boolean
  x: number
  y: number
  content: string
}>({
  visible: false,
  x: 0,
  y: 0,
  content: ''
})

// 计算属性
const displayContent = computed(() => {
  if (!content.value) return ''
  
  let lines = content.value.split('\n')
  
  // 添加行号
  if (showLineNumbers.value) {
    lines = lines.map((line, index) => {
      const lineNumber = (index + 1).toString().padStart(4, ' ')
      return `<span class="line-number">${lineNumber}</span>${escapeHtml(line)}`
    })
  } else {
    lines = lines.map(line => escapeHtml(line))
  }
  
  let result = lines.join('\n')
  
  // 高亮搜索结果
  if (searchQuery.value.trim()) {
    const regex = new RegExp(`(${escapeRegex(searchQuery.value)})`, 'gi')
    result = result.replace(regex, '<mark class="search-highlight">$1</mark>')
  }
  
  return result
})

// 方法
const handleDownload = (file: any) => {
  emit('download', file)
}

const handleRefresh = () => {
  loadText()
}

const loadText = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 模拟文本文件加载
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟文本内容
    content.value = generateMockTextContent()
    
  } catch (err) {
    error.value = '加载文本文件失败'
  } finally {
    loading.value = false
  }
}

const generateMockTextContent = () => {
  const fileType = props.file?.type || 'txt'
  
  if (fileType === 'md') {
    return `# ${props.file?.name || '文档标题'}

## 简介

这是一个 Markdown 文档的示例内容。在实际应用中，这里会显示真实的文件内容。

## 功能特性

- **文本查看**: 支持多种文本格式
- **语法高亮**: 根据文件类型自动高亮
- **搜索功能**: 快速查找文本内容
- **字体调节**: 可调整字体大小
- **行号显示**: 可选择显示或隐藏行号

## 代码示例

\`\`\`javascript
function example() {
  console.log('Hello, World!');
  return true;
}
\`\`\`

## 列表示例

1. 第一项
2. 第二项
3. 第三项

- 无序列表项 1
- 无序列表项 2
- 无序列表项 3

## 表格示例

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 结论

这是文档的结尾部分。`
  } else {
    return `文件名: ${props.file?.name || '未知文件'}
文件类型: ${fileType.toUpperCase()}
文件大小: ${props.file?.size ? Math.round(props.file.size / 1024) + ' KB' : '未知'}

这是一个文本文件查看器的示例内容。
在实际应用中，这里会显示真实的文件内容。

功能特性：
- 支持多种文本格式 (TXT, MD, JSON, XML, CSV 等)
- 语法高亮显示
- 文本搜索和高亮
- 字体大小调节
- 行号显示/隐藏
- 自动换行控制

示例代码：
function loadFile(filename) {
  console.log('Loading file:', filename);
  // 文件加载逻辑
  return fileContent;
}

const data = {
  "name": "示例数据",
  "type": "JSON",
  "content": "这是一个示例"
};

多行文本示例：
第一行内容
第二行内容
第三行内容
...
最后一行内容

这是一个较长的行，用来测试自动换行功能是否正常工作。当启用自动换行时，这行文本应该会在容器边界处自动换行显示。`
  }
}

const increaseFontSize = () => {
  if (fontSize.value < 24) {
    fontSize.value += 2
  }
}

const decreaseFontSize = () => {
  if (fontSize.value > 12) {
    fontSize.value -= 2
  }
}

const toggleWordWrap = () => {
  wordWrap.value = !wordWrap.value
}

const toggleLineNumbers = () => {
  showLineNumbers.value = !showLineNumbers.value
}

const highlightText = () => {
  // 高亮逻辑已在计算属性中实现
}

const escapeHtml = (text: string) => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

const escapeRegex = (text: string) => {
  return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 分段相关方法
const formatSegmentContent = (content: string, index: number) => {
  let lines = content.split('\n')

  // 添加行号
  if (showLineNumbers.value) {
    lines = lines.map((line, lineIndex) => {
      const lineNumber = (lineIndex + 1).toString().padStart(4, ' ')
      return `<span class="line-number">${lineNumber}</span>${escapeHtml(line)}`
    })
  } else {
    lines = lines.map(line => escapeHtml(line))
  }

  let result = lines.join('\n')

  // 高亮搜索结果
  if (searchQuery.value.trim()) {
    const regex = new RegExp(`(${escapeRegex(searchQuery.value)})`, 'gi')
    result = result.replace(regex, '<mark class="search-highlight">$1</mark>')
  }

  return result
}

const handleSegmentMouseEnter = (segment: any, event: MouseEvent) => {
  hoveredSegmentId.value = segment.id

  // 计算字符数和关键词
  const charCount = segment.content.length
  const keywords = extractKeywords(segment.content)

  const segmentInfo = {
    id: segment.id,
    charCount,
    keywords,
    content: segment.content.substring(0, 100) + (segment.content.length > 100 ? '...' : '')
  }

  // 发送悬停事件
  emit('segmentHover', segmentInfo)

  // 显示工具提示
  showSegmentTooltip(event, segmentInfo)
}

const handleSegmentMouseLeave = () => {
  hoveredSegmentId.value = null
  segmentTooltip.value.visible = false
  emit('segmentLeave')
}

const showSegmentTooltip = (event: MouseEvent, segmentInfo: any) => {
  const rect = textContentContainer.value?.getBoundingClientRect()
  if (rect) {
    segmentTooltip.value = {
      visible: true,
      x: event.clientX - rect.left + 10,
      y: event.clientY - rect.top - 10,
      content: `字符数: ${segmentInfo.charCount} | 关键词: ${segmentInfo.keywords.join(', ')}`
    }
  }
}

const extractKeywords = (text: string): string[] => {
  // 简单的关键词提取逻辑
  const words = text.match(/[\u4e00-\u9fa5]{2,}|[a-zA-Z]{3,}/g) || []
  const wordCount: { [key: string]: number } = {}

  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1
  })

  return Object.entries(wordCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([word]) => word)
}

// 滚动到指定分段
const scrollToSegment = (segmentId: string) => {
  const element = document.getElementById(`segment-${segmentId}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// 分段编辑相关方法
const startEditSegment = (segment: any) => {
  editingSegmentId.value = segment.id
  editingContent.value = segment.content
}

const saveSegmentEdit = () => {
  if (editingSegmentId.value && props.segments) {
    const segmentIndex = props.segments.findIndex(seg => seg.id === editingSegmentId.value)
    if (segmentIndex !== -1) {
      // 更新分段内容
      props.segments[segmentIndex].content = editingContent.value
      // 这里应该调用API保存到后端
      console.log('保存分段:', editingSegmentId.value, editingContent.value)
    }
  }
  cancelSegmentEdit()
}

const cancelSegmentEdit = () => {
  editingSegmentId.value = null
  editingContent.value = ''
}

const deleteSegment = (segmentId: string) => {
  if (confirm('确定要删除这个分段吗？')) {
    if (props.segments) {
      const segmentIndex = props.segments.findIndex(seg => seg.id === segmentId)
      if (segmentIndex !== -1) {
        props.segments.splice(segmentIndex, 1)
        // 这里应该调用API删除后端数据
        console.log('删除分段:', segmentId)
      }
    }
  }
}

// 拖拽调整相关方法
const startResize = (segmentId: string, direction: 'top' | 'bottom', event: MouseEvent) => {
  event.preventDefault()

  const segmentElement = document.getElementById(`segment-${segmentId}`)
  if (!segmentElement) return

  resizing.value = {
    segmentId,
    direction,
    startY: event.clientY,
    originalHeight: segmentElement.offsetHeight
  }

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = 'ns-resize'
}

const handleResize = (event: MouseEvent) => {
  if (!resizing.value.segmentId) return

  const deltaY = event.clientY - resizing.value.startY
  const segmentElement = document.getElementById(`segment-${resizing.value.segmentId}`)

  if (segmentElement) {
    let newHeight = resizing.value.originalHeight

    if (resizing.value.direction === 'bottom') {
      newHeight += deltaY
    } else {
      newHeight -= deltaY
    }

    // 限制最小高度
    newHeight = Math.max(newHeight, 50)

    segmentElement.style.height = `${newHeight}px`

    // 调整相邻分段
    adjustAdjacentSegments(resizing.value.segmentId, resizing.value.direction, deltaY)
  }
}

const stopResize = () => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = 'default'

  // 检查并删除空分段
  checkAndRemoveEmptySegments()

  resizing.value = {
    segmentId: null,
    direction: null,
    startY: 0,
    originalHeight: 0
  }
}

const adjustAdjacentSegments = (segmentId: string, direction: 'top' | 'bottom', deltaY: number) => {
  if (!props.segments) return

  const currentIndex = props.segments.findIndex(seg => seg.id === segmentId)
  if (currentIndex === -1) return

  let adjacentIndex = -1

  if (direction === 'top' && currentIndex > 0) {
    adjacentIndex = currentIndex - 1
  } else if (direction === 'bottom' && currentIndex < props.segments.length - 1) {
    adjacentIndex = currentIndex + 1
  }

  if (adjacentIndex !== -1) {
    const adjacentSegment = props.segments[adjacentIndex]
    const adjacentElement = document.getElementById(`segment-${adjacentSegment.id}`)

    if (adjacentElement) {
      const currentHeight = adjacentElement.offsetHeight
      let newHeight = currentHeight

      if (direction === 'top') {
        newHeight += deltaY
      } else {
        newHeight -= deltaY
      }

      newHeight = Math.max(newHeight, 20)
      adjacentElement.style.height = `${newHeight}px`
    }
  }
}

const checkAndRemoveEmptySegments = () => {
  if (!props.segments) return

  const segmentsToRemove: string[] = []

  props.segments.forEach(segment => {
    const element = document.getElementById(`segment-${segment.id}`)
    if (element && element.offsetHeight < 30) {
      segmentsToRemove.push(segment.id)
    }
  })

  segmentsToRemove.forEach(segmentId => {
    const index = props.segments!.findIndex(seg => seg.id === segmentId)
    if (index !== -1) {
      props.segments!.splice(index, 1)
      console.log('自动删除空分段:', segmentId)
    }
  })
}

// 生命周期
onMounted(() => {
  loadText()
})

// 监听选中分段变化
watch(() => props.selectedSegmentId, (newSegmentId) => {
  if (newSegmentId) {
    // 延迟执行以确保DOM已更新
    nextTick(() => {
      scrollToSegment(newSegmentId)
    })
  }
})
</script>

<style scoped>
.text-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.text-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: white;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  font-size: 12px;
}

.tool-btn:hover:not(:disabled) {
  background: #f1f5f9;
  color: #334155;
}

.tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tool-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.font-info {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.search-box {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 12px;
}

.search-input {
  padding: 6px 8px 6px 28px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 12px;
  width: 150px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.text-content {
  flex: 1;
  overflow: auto;
  background: white;
  position: relative;
}

.text-content-container {
  position: relative;
  min-height: 100%;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.text-display {
  height: 100%;
  overflow: auto;
}

.text-wrapper {
  height: 100%;
}

.text-wrapper.word-wrap {
  white-space: pre-wrap;
}

.text-content-pre {
  margin: 0;
  padding: 20px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  line-height: 1.5;
  color: #1e293b;
  background: white;
  border: none;
  outline: none;
  white-space: pre;
  overflow-x: auto;
  min-height: 100%;
}

:deep(.line-number) {
  color: #94a3b8;
  margin-right: 16px;
  user-select: none;
  border-right: 1px solid #e2e8f0;
  padding-right: 8px;
}

:deep(.search-highlight) {
  background: #fef08a;
  color: #92400e;
  padding: 1px 2px;
  border-radius: 2px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

/* 分段相关样式 */
.segmented-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
}

.text-segment {
  position: relative;
  border: 2px dashed transparent;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
  background: rgba(248, 250, 252, 0.5);
}

.text-segment:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.text-segment.selected {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}

.text-segment.highlighted {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e2e8f0;
}

.segment-number {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 12px;
}

.segment-content {
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  line-height: 1.6;
  color: #1e293b;
  background: transparent;
  border: none;
  outline: none;
  white-space: pre-wrap;
  word-break: break-word;
}

.segment-tooltip {
  position: absolute;
  background: #1f2937;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  max-width: 300px;
  word-break: break-all;
}

.segment-tooltip::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #1f2937;
}

/* 分段编辑和拖拽样式 */
.text-segment.editing {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e2e8f0;
}

.segment-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.text-segment:hover .segment-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px 6px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.edit-btn {
  background: #3b82f6;
  color: white;
}

.edit-btn:hover {
  background: #2563eb;
}

.delete-btn {
  background: #ef4444;
  color: white;
}

.delete-btn:hover {
  background: #dc2626;
}

.segment-editor {
  margin-top: 8px;
}

.edit-textarea {
  width: 100%;
  min-height: 100px;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
}

.edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #10b981;
  color: white;
}

.btn-primary:hover {
  background: #059669;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.segment-display {
  position: relative;
}

.resize-handles {
  position: absolute;
  left: 0;
  right: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  left: 0;
  right: 0;
  height: 4px;
  background: transparent;
  cursor: ns-resize;
  pointer-events: all;
  transition: background 0.2s ease;
}

.resize-handle:hover {
  background: rgba(59, 130, 246, 0.3);
}

.resize-top {
  top: -2px;
}

.resize-bottom {
  bottom: -2px;
}

.text-segment:hover .resize-handle {
  background: rgba(59, 130, 246, 0.2);
}
</style>
