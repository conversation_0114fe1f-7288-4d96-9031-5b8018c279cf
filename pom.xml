<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xhcai</groupId>
    <artifactId>xhcai-plus</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>XHC AI Plus Platform</name>
    <description>AI智能体服务平台 - 基于Spring Boot 3.5.3的模块化插件式架构</description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot -->
        <spring-boot.version>3.5.5</spring-boot.version>
        <spring-ai.version>1.0.0-M6</spring-ai.version>
        
        <!-- Database -->
        <postgresql.version>42.7.7</postgresql.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <mybatis-spring.version>3.0.5</mybatis-spring.version>
        <druid.version>1.2.23</druid.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        
        <!-- Security -->
        <jjwt.version>0.12.6</jjwt.version>
        <spring-security.version>6.4.1</spring-security.version>

        <!-- Tools -->
        <hutool.version>5.8.32</hutool.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <mapstruct.version>1.6.2</mapstruct.version>
        <okhttp.version>4.12.0</okhttp.version>
        <lombok.version>1.18.38</lombok.version>
        
        <!-- Documentation -->
        <springdoc.version>2.7.0</springdoc.version>
        
        <!-- Monitoring -->
        <micrometer.version>1.14.1</micrometer.version>

        <!-- Minio -->
        <minio.version>8.5.17</minio.version>

        <!-- Scheduler -->
        <quartz.version>2.5.0</quartz.version>
        
        <!-- Event Bus -->
        <guava.version>33.3.1-jre</guava.version>

        <!-- RabbitMQ -->
        <spring-rabbit.version>3.5.5</spring-rabbit.version>
        
        <!-- Validation -->
        <validation-api.version>3.1.0</validation-api.version>
        
        <!-- Plugin Framework -->
        <pf4j.version>3.13.0</pf4j.version>
        <pf4j-spring.version>0.10.0</pf4j-spring.version>

        <!-- Apache Commons -->
        <commons-lang3.version>3.18.0</commons-lang3.version>
        <commons-fileupload.version>1.6.0</commons-fileupload.version>
        <commons-io.version>2.20.0</commons-io.version>

        <!-- Document Processing -->
        <tika.version>2.9.2</tika.version>
        <pdfbox.version>2.0.29</pdfbox.version>
        <poi.version>5.4.1</poi.version>
        <poi-ooxml-schemas.version>4.1.2</poi-ooxml-schemas.version>

        <!-- Test -->
        <wiremock.version>2.35.2</wiremock.version>

        <!-- 默认跳过JAR签名，只在生产环境启用 -->
        <maven.jarsigner.skip>true</maven.jarsigner.skip>
    </properties>
    <modules>
        <!-- Common Modules -->
        <module>common/common-core</module>
        <module>common/common-security</module>
        <module>common/common-datasource</module>
        <module>common/common-api</module>
        <module>common/common-plugin</module>

        <!-- Business Modules -->
        <module>modules/xhcai-system</module>
        <module>modules/xhcai-ai</module>
        <module>modules/xhcai-dify</module>
        <module>modules/xhcai-agent</module>
        <module>modules/xhcai-rag</module>

        <!-- Main Application -->
        <module>admin-api</module>

        <!-- Plugins (optional, for development convenience) -->
        <!-- <module>plugins</module> -->
    </modules>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring AI BOM -->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Internal Modules -->
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>common-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>common-datasource</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>common-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>common-plugin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>xhcai-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>xhcai-ai</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>xhcai-dify</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>xhcai-agent</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhcai</groupId>
                <artifactId>xhcai-rag</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Database -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mybatis-spring</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <!-- Minio -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- Security -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- Tools -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>


            <!-- Documentation -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- Scheduler -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>

            <!-- Event Bus -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- RabbitMQ -->
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit-test</artifactId>
                <version>${spring-rabbit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>${spring-rabbit.version}</version>
            </dependency>

            <!-- Plugin Framework -->
            <dependency>
                <groupId>org.pf4j</groupId>
                <artifactId>pf4j</artifactId>
                <version>${pf4j.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.pf4j</groupId>
                <artifactId>pf4j-spring</artifactId>
                <version>${pf4j-spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Validation -->
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>

            <!-- Apache Commons -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <!-- Document Processing -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-parsers-standard-package</artifactId>
                <version>${tika.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- Test Dependencies -->
            <dependency>
                <groupId>com.github.tomakehurst</groupId>
                <artifactId>wiremock-jre8</artifactId>
                <version>${wiremock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi-ooxml-schemas.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <parameters>true</parameters>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>

                <!-- Maven Shade Plugin for Java 21 support -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.6.0</version>
                </plugin>

                <!-- Maven JAR Signer Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jarsigner-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>

                <!-- Maven JAR Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.4.2</version>
                </plugin>

                <!-- Maven Resources Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>

            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <!-- 生产环境配置 -->
        <profile>
            <id>prod</id>
            <properties>
                <!-- 生产环境启用JAR签名 -->
                <maven.jarsigner.skip>false</maven.jarsigner.skip>
                <!-- 跳过测试 -->
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2/</url>
        </repository>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
