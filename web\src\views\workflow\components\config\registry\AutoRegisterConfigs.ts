/**
 * 配置组件自动注册系统
 * 自动扫描和注册所有配置组件
 */

import { ConfigRegistry } from './ConfigRegistry'

// 基础配置组件
import StartNodeConfig from '../basic/StartNodeConfig.vue'

// 数据库配置组件
import MySQLConfig from '../database/MySQLConfig.vue'

/**
 * 注册基础节点配置组件
 */
function registerBasicConfigs() {
  // 注册开始节点配置
  ConfigRegistry.registerConfig({
    nodeType: 'start',
    category: '基础节点',
    component: StartNodeConfig,
    title: '开始节点配置',
    description: '配置工作流的开始节点参数'
  })

  // 注册结束节点配置（使用通用配置）
  ConfigRegistry.registerConfig({
    nodeType: 'end',
    category: '基础节点',
    component: StartNodeConfig, // 临时使用StartNodeConfig，后续可创建专门的EndNodeConfig
    title: '结束节点配置',
    description: '配置工作流的结束节点参数'
  })

  // 注册条件节点配置（使用通用配置）
  ConfigRegistry.registerConfig({
    nodeType: 'condition',
    category: '基础节点',
    component: StartNodeConfig, // 临时使用StartNodeConfig，后续可创建专门的ConditionNodeConfig
    title: '条件节点配置',
    description: '配置条件判断节点参数'
  })
}

/**
 * 注册数据库配置组件
 */
function registerDatabaseConfigs() {
  // 注册MySQL配置
  ConfigRegistry.registerConfig({
    nodeType: 'mysql',
    category: '数据库工具',
    component: MySQLConfig,
    title: 'MySQL数据库配置',
    description: '配置MySQL数据库连接和操作参数'
  })

  // 注册PostgreSQL配置（使用MySQL配置作为模板）
  ConfigRegistry.registerConfig({
    nodeType: 'postgresql',
    category: '数据库工具',
    component: MySQLConfig, // 临时使用MySQLConfig，后续可创建专门的PostgreSQLConfig
    title: 'PostgreSQL数据库配置',
    description: '配置PostgreSQL数据库连接和操作参数'
  })

  // 注册Oracle配置（使用MySQL配置作为模板）
  ConfigRegistry.registerConfig({
    nodeType: 'oracle',
    category: '数据库工具',
    component: MySQLConfig, // 临时使用MySQLConfig，后续可创建专门的OracleConfig
    title: 'Oracle数据库配置',
    description: '配置Oracle数据库连接和操作参数'
  })

  // 注册Redis配置（使用MySQL配置作为模板）
  ConfigRegistry.registerConfig({
    nodeType: 'redis',
    category: '数据库工具',
    component: MySQLConfig, // 临时使用MySQLConfig，后续可创建专门的RedisConfig
    title: 'Redis缓存配置',
    description: '配置Redis缓存连接和操作参数'
  })
}

/**
 * 注册AI配置组件（占位符）
 */
function registerAIConfigs() {
  const aiNodeTypes = [
    'llm-chat',
    'text-embedding',
    'speech-to-text',
    'text-to-speech',
    'image-generation',
    'image-analysis',
    'knowledge-base'
  ]

  aiNodeTypes.forEach(nodeType => {
    ConfigRegistry.registerConfig({
      nodeType,
      category: 'AI工具',
      component: StartNodeConfig, // 临时使用通用配置，后续创建专门的AI配置组件
      title: `${nodeType.toUpperCase()}配置`,
      description: `配置${nodeType}节点参数`
    })
  })
}

/**
 * 注册文件生成配置组件（占位符）
 */
function registerFileGeneratorConfigs() {
  const fileGeneratorTypes = [
    'generate-ppt',
    'generate-word',
    'generate-pdf',
    'generate-excel'
  ]

  fileGeneratorTypes.forEach(nodeType => {
    ConfigRegistry.registerConfig({
      nodeType,
      category: '文件生成工具',
      component: StartNodeConfig, // 临时使用通用配置
      title: `${nodeType.replace('generate-', '').toUpperCase()}生成配置`,
      description: `配置${nodeType}节点参数`
    })
  })
}

/**
 * 注册文件提取配置组件（占位符）
 */
function registerFileExtractorConfigs() {
  const fileExtractorTypes = [
    'extract-ppt',
    'extract-word',
    'extract-pdf'
  ]

  fileExtractorTypes.forEach(nodeType => {
    ConfigRegistry.registerConfig({
      nodeType,
      category: '文件提取工具',
      component: StartNodeConfig, // 临时使用通用配置
      title: `${nodeType.replace('extract-', '').toUpperCase()}提取配置`,
      description: `配置${nodeType}节点参数`
    })
  })
}

/**
 * 注册渲染配置组件（占位符）
 */
function registerRenderConfigs() {
  const renderTypes = [
    'pie-chart',
    'line-chart',
    'bar-chart'
  ]

  renderTypes.forEach(nodeType => {
    ConfigRegistry.registerConfig({
      nodeType,
      category: '渲染工具',
      component: StartNodeConfig, // 临时使用通用配置
      title: `${nodeType.replace('-', ' ').toUpperCase()}配置`,
      description: `配置${nodeType}节点参数`
    })
  })
}

/**
 * 注册数据处理配置组件（占位符）
 */
function registerDataConfigs() {
  const dataTypes = [
    'data-filter',
    'data-transform'
  ]

  dataTypes.forEach(nodeType => {
    ConfigRegistry.registerConfig({
      nodeType,
      category: '数据工具',
      component: StartNodeConfig, // 临时使用通用配置
      title: `${nodeType.replace('data-', '').toUpperCase()}配置`,
      description: `配置${nodeType}节点参数`
    })
  })
}

/**
 * 注册工具配置组件（占位符）
 */
function registerUtilityConfigs() {
  const utilityTypes = [
    'http-request',
    'delay'
  ]

  utilityTypes.forEach(nodeType => {
    ConfigRegistry.registerConfig({
      nodeType,
      category: '其它工具',
      component: StartNodeConfig, // 临时使用通用配置
      title: `${nodeType.replace('-', ' ').toUpperCase()}配置`,
      description: `配置${nodeType}节点参数`
    })
  })
}

/**
 * 自动注册所有配置组件
 */
export async function autoRegisterConfigs() {
  try {
    console.log('Starting auto-registration of config components...')

    // 注册各类别的配置组件
    registerBasicConfigs()
    registerDatabaseConfigs()
    registerAIConfigs()
    registerFileGeneratorConfigs()
    registerFileExtractorConfigs()
    registerRenderConfigs()
    registerDataConfigs()
    registerUtilityConfigs()

    console.log('Config components auto-registration completed successfully')
    console.log('Config registry stats:', ConfigRegistry.getStats())
  } catch (error) {
    console.error('Failed to auto-register config components:', error)
    throw error
  }
}

/**
 * 手动注册单个配置组件
 */
export function registerSingleConfig(
  nodeType: string,
  category: string,
  component: any,
  title: string,
  description: string
) {
  try {
    ConfigRegistry.registerConfig({
      nodeType,
      category,
      component,
      title,
      description
    })
    console.log(`Successfully registered config component: ${nodeType}`)
  } catch (error) {
    console.error(`Failed to register config component ${nodeType}:`, error)
    throw error
  }
}

/**
 * 获取已注册的配置组件统计信息
 */
export function getConfigRegistrationStats() {
  return ConfigRegistry.getStats()
}

/**
 * 检查配置组件是否已注册
 */
export function isConfigRegistered(nodeType: string): boolean {
  return ConfigRegistry.hasConfig(nodeType)
}

/**
 * 获取所有已注册的配置组件类型
 */
export function getRegisteredConfigTypes(): string[] {
  return ConfigRegistry.getAllNodeTypes()
}

/**
 * 获取指定类别的配置组件类型
 */
export function getConfigTypesByCategory(category: string): string[] {
  return ConfigRegistry.getConfigsByCategory(category)
}
