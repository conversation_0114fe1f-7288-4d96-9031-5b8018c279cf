/**
 * AI模型配置API接口
 */

import { apiClient } from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'

/**
 * AI模型配置接口定义
 */
export interface AiModel {
  id?: string
  name: string
  modelId: string
  provider: string
  type: string
  platform?: string
  version?: string
  description?: string
  status: string
  // 接口配置
  apiEndpoint?: string
  apiKey?: string
  organizationId?: string
  timeout?: number
  // 模型参数
  maxTokens?: number
  temperature?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stopSequences?: string
  // 费用配置
  inputPrice?: number
  outputPrice?: number
  rpmLimit?: number
  tpmLimit?: number
  // 审计字段
  tenantId?: string
  remark?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  providerDict?: Record<string, any>
  typeDict?: Record<string, any>
  platformDict?: Record<string, any>
}

/**
 * AI模型查询参数
 */
export interface AiModelQueryParams {
  current?: number
  size?: number
  name?: string
  modelId?: string
  provider?: string
  type?: string
  status?: string
  beginTime?: string
  endTime?: string
  orderBy?: string
  orderDirection?: string
}

/**
 * AI模型创建参数
 */
export interface AiModelCreateParams {
  name: string
  modelId: string
  provider: string
  type: string
  platform?: string
  version?: string
  description?: string
  status?: string
  // 接口配置
  apiEndpoint?: string
  apiKey?: string
  organizationId?: string
  timeout?: number
  // 模型参数
  maxTokens?: number
  temperature?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stopSequences?: string
  // 费用配置
  inputPrice?: number
  outputPrice?: number
  rpmLimit?: number
  tpmLimit?: number
  remark?: string
}

/**
 * AI模型更新参数
 */
export interface AiModelUpdateParams extends AiModelCreateParams {
  id: string
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
  hasPrevious: boolean
  hasNext: boolean
  isFirst: boolean
  isLast: boolean
}

/**
 * AI模型配置API
 */
export class AiModelAPI {
  /**
   * 分页查询AI模型列表
   */
  static async getAiModelPage(params: AiModelQueryParams): Promise<ApiResponse<PageResult<AiModel>>> {
    return apiClient.get('/api/rag/model/page', params)
  }

  /**
   * 查询AI模型列表
   */
  static async getAiModelList(params?: AiModelQueryParams): Promise<ApiResponse<AiModel[]>> {
    return apiClient.get('/api/rag/model/list', params)
  }

  /**
   * 根据ID查询AI模型详情
   */
  static async getAiModelById(id: string): Promise<ApiResponse<AiModel>> {
    return apiClient.get(`/api/rag/model/${id}`)
  }

  /**
   * 创建AI模型
   */
  static async createAiModel(data: AiModelCreateParams): Promise<ApiResponse<boolean>> {
    return apiClient.post('/api/rag/model', data)
  }

  /**
   * 更新AI模型
   */
  static async updateAiModel(data: AiModelUpdateParams): Promise<ApiResponse<boolean>> {
    return apiClient.put('/api/rag/model', data)
  }

  /**
   * 删除AI模型
   */
  static async deleteAiModel(id: string): Promise<ApiResponse<boolean>> {
    return apiClient.delete(`/api/rag/model/${id}`)
  }

  /**
   * 批量删除AI模型
   */
  static async batchDeleteAiModels(ids: string[]): Promise<ApiResponse<boolean>> {
    return apiClient.delete('/api/rag/model/batch', { data: ids })
  }

  /**
   * 启用AI模型
   */
  static async enableAiModel(id: string): Promise<ApiResponse<boolean>> {
    return apiClient.put(`/api/rag/model/${id}/enable`)
  }

  /**
   * 停用AI模型
   */
  static async disableAiModel(id: string): Promise<ApiResponse<boolean>> {
    return apiClient.put(`/api/rag/model/${id}/disable`)
  }

  /**
   * 批量更新AI模型状态
   */
  static async batchUpdateStatus(ids: string[], status: string): Promise<ApiResponse<boolean>> {
    return apiClient.put('/api/rag/model/batch/status', { ids, status })
  }

  /**
   * 根据提供商查询模型列表
   */
  static async getAiModelsByProvider(provider: string): Promise<ApiResponse<AiModel[]>> {
    return apiClient.get(`/api/rag/model/provider/${provider}`)
  }

  /**
   * 根据类型查询模型列表
   */
  static async getAiModelsByType(type: string): Promise<ApiResponse<AiModel[]>> {
    return apiClient.get(`/api/rag/model/type/${type}`)
  }

  /**
   * 统计各提供商的模型数量
   */
  static async countByProvider(): Promise<ApiResponse<Array<{ provider: string; count: number }>>> {
    return apiClient.get('/api/rag/model/stats/provider')
  }

  /**
   * 统计各类型的模型数量
   */
  static async countByType(): Promise<ApiResponse<Array<{ type: string; count: number }>>> {
    return apiClient.get('/api/rag/model/stats/type')
  }

  /**
   * 导出AI模型数据
   */
  static async exportAiModels(params?: AiModelQueryParams): Promise<ApiResponse<AiModel[]>> {
    return apiClient.post('/api/rag/model/export', params)
  }

  /**
   * 导入AI模型数据
   */
  static async importAiModels(models: AiModel[]): Promise<ApiResponse<string>> {
    return apiClient.post('/api/rag/model/import', models)
  }

  /**
   * 测试模型连接
   */
  static async testModelConnection(id: string): Promise<ApiResponse<{ success: boolean; message: string; responseTime?: number }>> {
    return apiClient.post(`/api/rag/model/${id}/test`)
  }

  /**
   * 复制模型配置
   */
  static async copyAiModel(id: string, newName: string, newModelId: string): Promise<ApiResponse<boolean>> {
    return apiClient.post(`/api/rag/model/${id}/copy`, { newName, newModelId })
  }
}

export default AiModelAPI
