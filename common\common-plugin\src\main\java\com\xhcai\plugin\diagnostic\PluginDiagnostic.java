package com.xhcai.plugin.diagnostic;

import com.xhcai.plugin.core.PluginContextManager;
import com.xhcai.plugin.core.PluginType;
import com.xhcai.plugin.service.PluginServiceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 插件系统诊断工具
 * 在应用启动时检查插件系统的配置和状态
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class PluginDiagnostic implements CommandLineRunner {
    
    @Value("${xhcai.plugin.root-path:plugins}")
    private String pluginRootPath;
    
    @Value("${xhcai.plugin.dev-mode:true}")
    private boolean devMode;
    
    @Autowired(required = false)
    private PluginContextManager contextManager;
    
    @Autowired(required = false)
    private PluginServiceManager serviceManager;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("=== 插件系统诊断开始 ===");
        
        // 检查配置
        checkConfiguration();
        
        // 检查目录结构
        checkDirectoryStructure();
        
        // 检查组件状态
        checkComponents();
        
        // 检查插件加载状态
        checkPluginLoadStatus();
        
        log.info("=== 插件系统诊断完成 ===");
    }
    
    /**
     * 检查配置
     */
    private void checkConfiguration() {
        log.info("检查插件系统配置...");
        log.info("插件根目录: {}", pluginRootPath);
        log.info("开发模式: {}", devMode);
        
        // 检查路径是否为绝对路径
        Path rootPath = Paths.get(pluginRootPath);
        if (!rootPath.isAbsolute()) {
            log.warn("插件根目录不是绝对路径，可能导致路径解析问题");
        }
    }
    
    /**
     * 检查目录结构
     */
    private void checkDirectoryStructure() {
        log.info("检查插件目录结构...");
        
        Path rootPath = Paths.get(pluginRootPath);
        if (!Files.exists(rootPath)) {
            log.error("插件根目录不存在: {}", rootPath.toAbsolutePath());
            return;
        }
        
        if (!Files.isDirectory(rootPath)) {
            log.error("插件根路径不是目录: {}", rootPath.toAbsolutePath());
            return;
        }
        
        log.info("插件根目录存在: {}", rootPath.toAbsolutePath());
        
        // 检查各插件类型目录
        for (PluginType pluginType : PluginType.values()) {
            Path typePath = rootPath.resolve(pluginType.getCode());
            if (Files.exists(typePath)) {
                log.info("插件类型目录存在: {} -> {}", pluginType.getCode(), typePath.toAbsolutePath());
                
                // 列出目录内容
                try {
                    Files.list(typePath).forEach(path -> {
                        if (Files.isDirectory(path)) {
                            log.info("  发现插件目录: {}", path.getFileName());
                        } else if (path.toString().endsWith(".jar")) {
                            log.info("  发现插件JAR: {}", path.getFileName());
                        }
                    });
                } catch (Exception e) {
                    log.error("无法列出目录内容: {}", typePath, e);
                }
            } else {
                log.warn("插件类型目录不存在: {} -> {}", pluginType.getCode(), typePath.toAbsolutePath());
            }
        }
    }
    
    /**
     * 检查组件状态
     */
    private void checkComponents() {
        log.info("检查插件系统组件状态...");
        
        if (contextManager == null) {
            log.error("PluginContextManager 未注入，插件系统无法工作");
        } else {
            log.info("PluginContextManager 已注入");
        }
        
        if (serviceManager == null) {
            log.error("PluginServiceManager 未注入，插件服务不可用");
        } else {
            log.info("PluginServiceManager 已注入");
        }
    }
    
    /**
     * 检查插件加载状态
     */
    private void checkPluginLoadStatus() {
        log.info("检查插件加载状态...");
        
        if (serviceManager == null) {
            log.error("无法检查插件加载状态：PluginServiceManager 不可用");
            return;
        }
        
        try {
            // 检查存储服务
            var storageServices = serviceManager.getStorageServices();
            log.info("已加载存储服务数量: {}", storageServices.size());
            storageServices.forEach(service -> {
                log.info("  存储服务: {} ({})", service.getServiceName(), service.getServiceType());
            });
            
            // 检查模型服务
            var modelServices = serviceManager.getModelServices();
            log.info("已加载模型服务数量: {}", modelServices.size());
            modelServices.forEach(service -> {
                log.info("  模型服务: {} ({})", service.getServiceName(), service.getServiceType());
            });
            
        } catch (Exception e) {
            log.error("检查插件加载状态时发生错误", e);
        }
    }
}
