package com.xhcai.modules.rag.entity.inner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 清洗配置内部类
 */
@Data
@Schema(description = "清洗配置")
public class CleaningConfig {

    /**
     * 移除空行
     */
    @Schema(description = "移除空行")
    private Boolean removeEmptyLines;

    /**
     * 移除多余空格
     */
    @Schema(description = "移除多余空格")
    private Boolean removeExtraSpaces;

    /**
     * 移除特殊字符
     */
    @Schema(description = "移除特殊字符")
    private Boolean removeSpecialChars;

    /**
     * 文本标准化
     */
    @Schema(description = "文本标准化")
    private Boolean normalizeText;

    /**
     * 删除URL和邮箱
     */
    @Schema(description = "删除URL和邮箱")
    private Boolean deleteSymbol;

    /**
     * 删除内嵌多媒体
     */
    @Schema(description = "删除内嵌多媒体")
    private Boolean deleteInlineMedia;

    /**
     * 过滤关键词
     */
    @Schema(description = "过滤关键词")
    private String filterKeywords;
}