package com.xhcai.modules.rag.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文档分段结果DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentResult {

    /**
     * 分段内容
     */
    private String content;

    /**
     * 字符数
     */
    private Integer wordCount;

    /**
     * token数（估算）
     */
    private Integer tokens;

    /**
     * 关键字列表
     */
    private List<String> keywords;

    /**
     * 分段位置
     */
    private Integer position;

    /**
     * 创建分段结果
     *
     * @param content   内容
     * @param position  位置
     * @param keywords  关键字
     * @return 分段结果
     */
    public static SegmentResult create(String content, Integer position, List<String> keywords) {
        return SegmentResult.builder()
                .content(content)
                .position(position)
                .wordCount(content != null ? content.length() : 0)
                .tokens(estimateTokens(content))
                .keywords(keywords)
                .build();
    }

    /**
     * 估算token数量
     * 简单估算：中文按字符数计算，英文按单词数*1.3计算
     *
     * @param content 内容
     * @return token数量
     */
    private static Integer estimateTokens(String content) {
        if (content == null || content.isEmpty()) {
            return 0;
        }

        // 简单的token估算逻辑
        int chineseChars = 0;
        int englishWords = 0;
        
        String[] words = content.split("\\s+");
        for (String word : words) {
            if (word.matches(".*[\\u4e00-\\u9fa5].*")) {
                // 包含中文字符
                chineseChars += word.length();
            } else {
                // 英文单词
                englishWords++;
            }
        }

        // 中文按字符计算，英文按单词*1.3计算
        return chineseChars + (int) (englishWords * 1.3);
    }
}
