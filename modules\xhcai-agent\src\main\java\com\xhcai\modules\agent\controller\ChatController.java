package com.xhcai.modules.agent.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.agent.dto.ChatRequestDTO;
import com.xhcai.modules.agent.service.IChatService;
import com.xhcai.modules.agent.vo.ChatResponseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * 聊天控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "智能体聊天", description = "智能体对话聊天功能")
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    @Autowired
    private IChatService chatService;

    @Operation(summary = "发送消息", description = "向智能体发送消息并获取回复（阻塞式）")
    @PostMapping("/send")
    @RequiresPermissions("chat:send")
    public Result<ChatResponseVO> sendMessage(@Valid @RequestBody ChatRequestDTO requestDTO) {
        ChatResponseVO response = chatService.sendMessage(requestDTO);
        return Result.success(response);
    }

    @Operation(summary = "发送消息（流式）", description = "向智能体发送消息并获取流式回复")
    @PostMapping(value = "/send/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @RequiresPermissions("chat:send")
    public SseEmitter sendMessageStream(@Valid @RequestBody ChatRequestDTO requestDTO) {
        return chatService.sendMessageStream(requestDTO);
    }

    @Operation(summary = "结束对话", description = "结束指定的对话会话")
    @PutMapping("/conversation/{conversationId}/end")
    @RequiresPermissions("chat:manage")
    public Result<Void> endConversation(@Parameter(description = "对话ID") @PathVariable String conversationId) {
        boolean success = chatService.endConversation(conversationId);
        return success ? Result.success("结束对话成功") : Result.fail("结束对话失败");
    }

    @Operation(summary = "重新生成回复", description = "重新生成指定消息的AI回复")
    @PostMapping("/message/{messageId}/regenerate")
    @RequiresPermissions("chat:send")
    public Result<ChatResponseVO> regenerateResponse(@Parameter(description = "消息ID") @PathVariable String messageId) {
        ChatResponseVO response = chatService.regenerateResponse(messageId);
        return Result.success(response);
    }

    @Operation(summary = "获取对话历史", description = "获取指定对话的历史消息")
    @GetMapping("/conversation/{conversationId}/history")
    @RequiresPermissions("chat:history")
    public Result<List<ChatResponseVO>> getConversationHistory(
            @Parameter(description = "对话ID") @PathVariable String conversationId,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "50") Integer limit) {
        List<ChatResponseVO> history = chatService.getConversationHistory(conversationId, limit);
        return Result.success(history);
    }

    @Operation(summary = "提交用户反馈", description = "对对话质量进行评分和反馈")
    @PostMapping("/conversation/{conversationId}/feedback")
    @RequiresPermissions("chat:feedback")
    public Result<Void> submitFeedback(@Parameter(description = "对话ID") @PathVariable String conversationId,
                                      @RequestBody FeedbackRequest request) {
        boolean success = chatService.submitFeedback(conversationId, request.getRating(), request.getFeedback());
        return success ? Result.success("提交反馈成功") : Result.fail("提交反馈失败");
    }

    @Operation(summary = "获取建议问题", description = "获取智能体的建议问题列表")
    @GetMapping("/agent/{agentId}/suggestions")
    @RequiresPermissions("chat:suggestions")
    public Result<List<String>> getSuggestedQuestions(
            @Parameter(description = "智能体ID") @PathVariable String agentId,
            @Parameter(description = "对话ID") @RequestParam(required = false) String conversationId) {
        List<String> suggestions = chatService.getSuggestedQuestions(agentId, conversationId);
        return Result.success(suggestions);
    }

    @Operation(summary = "检查智能体可用性", description = "检查指定智能体是否可用于聊天")
    @GetMapping("/agent/{agentId}/available")
    @RequiresPermissions("chat:check")
    public Result<Boolean> checkAgentAvailable(@Parameter(description = "智能体ID") @PathVariable String agentId) {
        boolean available = chatService.isAgentAvailable(agentId);
        return Result.success(available);
    }

    @Operation(summary = "获取对话统计", description = "获取指定对话的统计信息")
    @GetMapping("/conversation/{conversationId}/stats")
    @RequiresPermissions("chat:stats")
    public Result<IChatService.ConversationStatsVO> getConversationStats(
            @Parameter(description = "对话ID") @PathVariable String conversationId) {
        IChatService.ConversationStatsVO stats = chatService.getConversationStats(conversationId);
        return Result.success(stats);
    }

    @Operation(summary = "清理过期对话", description = "清理长时间未活动的对话")
    @PostMapping("/conversations/cleanup")
    @RequiresPermissions("chat:admin")
    public Result<Integer> cleanExpiredConversations() {
        int cleanedCount = chatService.cleanExpiredConversations();
        return Result.success("清理过期对话成功", cleanedCount);
    }

    // 内部类定义请求对象
    public static class FeedbackRequest {
        private Integer rating;
        private String feedback;

        public Integer getRating() {
            return rating;
        }

        public void setRating(Integer rating) {
            this.rating = rating;
        }

        public String getFeedback() {
            return feedback;
        }

        public void setFeedback(String feedback) {
            this.feedback = feedback;
        }
    }
}
