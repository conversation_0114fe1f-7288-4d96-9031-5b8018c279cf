import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { TenantAPI } from '@/api/system'
import { DictAPI } from '@/api/dict'
import type { SysTenantVO, SysTenantQueryDTO, SysDictDataVO } from '@/types/system'

// 全局状态实例
let globalTenantState: any = null

/**
 * 租户数据管理 Composable
 * 统一管理租户数据状态、API调用和分页逻辑
 */
export function useTenantData() {
  // 如果已有全局实例，直接返回
  if (globalTenantState) {
    return globalTenantState
  }

  // 响应式数据
  const loading = ref(false)
  const tenants = ref<SysTenantVO[]>([])
  const statusOptions = ref<SysDictDataVO[]>([])

  // 分页数据
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })

  // 筛选条件
  const searchFilters = ref<SysTenantQueryDTO>({
    tenantCode: '',
    tenantName: '',
    contactPerson: '',
    contactPhone: '',
    status: '',
    includeExpired: false,
    current: pagination.currentPage,
    size: pagination.pageSize
  })

  // 计算属性
  const paginatedTenants = computed(() => {
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    return tenants.value.slice(start, end)
  })

  // 加载租户列表
  const loadTenants = async () => {
    try {
      loading.value = true
      const response = await TenantAPI.getTenantPage(searchFilters.value)
      if (response.code === 200 && response.data) {
        tenants.value = response.data.records || []
        pagination.total = response.data.total || 0
      }
    } catch (error) {
      console.error('加载租户列表失败:', error)
      ElMessage.error('加载租户列表失败')
    } finally {
      loading.value = false
    }
  }

  // 加载字典数据
  const loadDictData = async () => {
    try {
      const response = await DictAPI.getDictDataByType('sys_tenant_status')
      if (response.code === 200 && response.data) {
        statusOptions.value = response.data
      }
    } catch (error) {
      console.error('加载字典数据失败:', error)
    }
  }

  // 搜索租户
  const searchTenants = (filters: SysTenantQueryDTO) => {
    searchFilters.value = { ...filters }
    pagination.currentPage = 1
    searchFilters.value.current = 1
    loadTenants()
  }

  // 清空筛选条件
  const clearFilters = () => {
    searchFilters.value = {
      tenantCode: '',
      tenantName: '',
      contactPerson: '',
      contactPhone: '',
      status: '',
      includeExpired: false,
      current: 1,
      size: pagination.pageSize
    }
    pagination.currentPage = 1
    loadTenants()
  }

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.currentPage = 1
    searchFilters.value.size = size
    searchFilters.value.current = 1
    loadTenants()
  }

  const handleCurrentChange = (page: number) => {
    pagination.currentPage = page
    searchFilters.value.current = page
    loadTenants()
  }

  // 创建租户
  const createTenant = async (formData: any) => {
    try {
      await TenantAPI.createTenant(formData)
      ElMessage.success('租户创建成功')
      loadTenants()
      return true
    } catch (error) {
      console.error('创建租户失败:', error)
      ElMessage.error('创建租户失败')
      return false
    }
  }

  // 更新租户
  const updateTenant = async (id: string, formData: any) => {
    try {
      await TenantAPI.updateTenant({ id, ...formData })
      ElMessage.success('租户更新成功')
      loadTenants()
      return true
    } catch (error) {
      console.error('更新租户失败:', error)
      ElMessage.error('更新租户失败')
      return false
    }
  }

  // 删除租户
  const deleteTenant = async (tenantId: string) => {
    try {
      await TenantAPI.deleteTenants([tenantId])
      ElMessage.success('租户删除成功')
      loadTenants()
      return true
    } catch (error) {
      console.error('删除租户失败:', error)
      ElMessage.error('删除租户失败')
      return false
    }
  }

  // 启用租户
  const enableTenant = async (tenantId: string) => {
    try {
      await TenantAPI.enableTenant(tenantId)
      ElMessage.success('租户启用成功')
      loadTenants()
      return true
    } catch (error) {
      console.error('启用租户失败:', error)
      ElMessage.error('启用租户失败')
      return false
    }
  }

  // 禁用租户
  const disableTenant = async (tenantId: string) => {
    try {
      await TenantAPI.disableTenant(tenantId)
      ElMessage.success('租户禁用成功')
      loadTenants()
      return true
    } catch (error) {
      console.error('禁用租户失败:', error)
      ElMessage.error('禁用租户失败')
      return false
    }
  }

  // 初始化数据
  const initData = async () => {
    await Promise.all([loadTenants(), loadDictData()])
  }

  // 创建状态对象
  const state = {
    // 响应式数据
    loading,
    tenants,
    statusOptions,
    pagination,
    searchFilters,
    paginatedTenants,

    // 方法
    loadTenants,
    loadDictData,
    searchTenants,
    clearFilters,
    handleSizeChange,
    handleCurrentChange,
    createTenant,
    updateTenant,
    deleteTenant,
    enableTenant,
    disableTenant,
    initData
  }

  // 保存全局实例
  globalTenantState = state

  return state
}
