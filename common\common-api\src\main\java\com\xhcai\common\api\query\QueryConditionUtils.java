package com.xhcai.common.api.query;

import java.util.List;

/**
 * 查询条件工具类
 * 提供便捷的查询条件构建方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class QueryConditionUtils {

    /**
     * 创建查询条件构建器
     * 
     * @return 查询条件构建器
     */
    public static QueryConditionBuilder builder() {
        return new QueryConditionBuilder();
    }

    /**
     * 构建基础查询条件（分页+时间范围+状态+关键字+数据权限）
     * 
     * @param query 查询对象
     * @param timeField 时间字段名
     * @param statusField 状态字段名
     * @param searchFields 搜索字段列表
     * @return 查询条件构建器
     */
    public static QueryConditionBuilder buildBasicConditions(Object query, String timeField, String statusField, String... searchFields) {
        QueryConditionBuilder builder = new QueryConditionBuilder();
        
        if (query instanceof Pageable) {
            builder.addPageCondition((Pageable) query);
        }
        
        if (query instanceof TimeRangeable) {
            builder.addTimeRangeCondition((TimeRangeable) query, timeField);
        }
        
        if (query instanceof Statusable) {
            builder.addStatusCondition((Statusable) query, statusField);
        }
        
        if (query instanceof Keywordable) {
            builder.addKeywordCondition((Keywordable) query, searchFields);
        }
        
        if (query instanceof DataScopeable) {
            builder.addDataScopeCondition((DataScopeable) query);
        }
        
        return builder;
    }

    /**
     * 构建分页查询条件
     * 
     * @param pageable 分页查询对象
     * @return 查询条件构建器
     */
    public static QueryConditionBuilder buildPageConditions(Pageable pageable) {
        return new QueryConditionBuilder().addPageCondition(pageable);
    }

    /**
     * 构建时间范围查询条件
     * 
     * @param timeRangeable 时间范围查询对象
     * @param timeField 时间字段名
     * @return 查询条件构建器
     */
    public static QueryConditionBuilder buildTimeRangeConditions(TimeRangeable timeRangeable, String timeField) {
        return new QueryConditionBuilder().addTimeRangeCondition(timeRangeable, timeField);
    }

    /**
     * 构建状态查询条件
     * 
     * @param statusable 状态查询对象
     * @param statusField 状态字段名
     * @return 查询条件构建器
     */
    public static QueryConditionBuilder buildStatusConditions(Statusable statusable, String statusField) {
        return new QueryConditionBuilder().addStatusCondition(statusable, statusField);
    }

    /**
     * 构建关键字搜索条件
     * 
     * @param keywordable 关键字查询对象
     * @param searchFields 搜索字段列表
     * @return 查询条件构建器
     */
    public static QueryConditionBuilder buildKeywordConditions(Keywordable keywordable, String... searchFields) {
        return new QueryConditionBuilder().addKeywordCondition(keywordable, searchFields);
    }

    /**
     * 构建数据权限查询条件
     * 
     * @param dataScopeable 数据权限查询对象
     * @return 查询条件构建器
     */
    public static QueryConditionBuilder buildDataScopeConditions(DataScopeable dataScopeable) {
        return new QueryConditionBuilder().addDataScopeCondition(dataScopeable);
    }

    /**
     * 检查字符串是否为空
     * 
     * @param str 字符串
     * @return true-为空，false-不为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否不为空
     * 
     * @param str 字符串
     * @return true-不为空，false-为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 检查列表是否为空
     * 
     * @param list 列表
     * @return true-为空，false-不为空
     */
    public static boolean isEmpty(List<?> list) {
        return list == null || list.isEmpty();
    }

    /**
     * 检查列表是否不为空
     * 
     * @param list 列表
     * @return true-不为空，false-为空
     */
    public static boolean isNotEmpty(List<?> list) {
        return !isEmpty(list);
    }

    /**
     * 为LIKE查询添加通配符
     * 
     * @param keyword 关键字
     * @return 添加通配符的关键字
     */
    public static String addLikeWildcard(String keyword) {
        if (isEmpty(keyword)) {
            return null;
        }
        return "%" + keyword.trim() + "%";
    }

    /**
     * 为前缀匹配添加通配符
     * 
     * @param keyword 关键字
     * @return 添加前缀通配符的关键字
     */
    public static String addPrefixWildcard(String keyword) {
        if (isEmpty(keyword)) {
            return null;
        }
        return keyword.trim() + "%";
    }

    /**
     * 为后缀匹配添加通配符
     * 
     * @param keyword 关键字
     * @return 添加后缀通配符的关键字
     */
    public static String addSuffixWildcard(String keyword) {
        if (isEmpty(keyword)) {
            return null;
        }
        return "%" + keyword.trim();
    }
}
