#!/bin/bash

# YYZS Agent Platform 部署脚本
# 支持开发、测试、生产环境的一键部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="yyzs-agent"
VERSION="1.0.0"
ENVIRONMENT="${1:-dev}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
YYZS Agent Platform 部署脚本

用法: $0 [环境] [选项]

环境:
  dev         开发环境（默认）
  test        测试环境
  prod        生产环境

选项:
  --build-only    仅构建，不部署
  --deploy-only   仅部署，不构建
  --clean         清理旧的容器和镜像
  --logs          查看日志
  --status        查看服务状态
  --stop          停止服务
  --restart       重启服务
  --help          显示此帮助信息

示例:
  $0 dev                    # 开发环境部署
  $0 prod --clean           # 生产环境清理部署
  $0 test --build-only      # 仅构建测试环境镜像
  $0 --logs                 # 查看日志
  $0 --status               # 查看服务状态

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local deps=("docker" "docker-compose" "java" "mvn" "node" "npm")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker未运行，请启动Docker服务"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置${ENVIRONMENT}环境变量..."
    
    case "${ENVIRONMENT}" in
        "dev")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.dev.yml"
            export DB_PASSWORD="yyzs123"
            export REDIS_PASSWORD="yyzs123"
            export JWT_SECRET="yyzs-agent-jwt-secret-dev"
            export LOG_LEVEL="DEBUG"
            ;;
        "test")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.test.yml"
            export DB_PASSWORD="yyzs_test_2024"
            export REDIS_PASSWORD="yyzs_test_2024"
            export JWT_SECRET="yyzs-agent-jwt-secret-test-$(date +%s)"
            export LOG_LEVEL="INFO"
            ;;
        "prod")
            export COMPOSE_FILE="docker-compose.yml:docker-compose.prod.yml"
            # 生产环境密码应该从安全的地方获取
            export DB_PASSWORD="${DB_PASSWORD:-$(openssl rand -base64 32)}"
            export REDIS_PASSWORD="${REDIS_PASSWORD:-$(openssl rand -base64 32)}"
            export JWT_SECRET="${JWT_SECRET:-$(openssl rand -base64 64)}"
            export LOG_LEVEL="WARN"
            ;;
        *)
            log_error "不支持的环境: ${ENVIRONMENT}"
            exit 1
            ;;
    esac
    
    export PROJECT_NAME
    export VERSION
    export ENVIRONMENT
    
    log_info "环境变量设置完成"
}

# 构建后端应用
build_backend() {
    log_info "构建后端应用..."
    
    cd "${SCRIPT_DIR}/yyzs/agent"
    
    # 清理之前的构建
    ./mvnw clean
    
    # 运行测试
    if [[ "${ENVIRONMENT}" != "prod" ]]; then
        log_info "运行单元测试..."
        ./mvnw test
    fi
    
    # 构建应用
    log_info "构建应用包..."
    ./mvnw package -DskipTests
    
    # 检查构建结果
    if [[ ! -f "target/${PROJECT_NAME}-${VERSION}.jar" ]]; then
        log_error "后端应用构建失败"
        exit 1
    fi
    
    log_info "后端应用构建完成"
    cd "${SCRIPT_DIR}"
}

# 构建前端应用
build_frontend() {
    log_info "构建前端应用..."
    
    cd "${SCRIPT_DIR}/yyzs/web"
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm ci
    
    # 构建应用
    log_info "构建前端应用..."
    npm run build
    
    # 检查构建结果
    if [[ ! -d ".next" ]]; then
        log_error "前端应用构建失败"
        exit 1
    fi
    
    log_info "前端应用构建完成"
    cd "${SCRIPT_DIR}"
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -t "${PROJECT_NAME}-backend:${VERSION}" \
        -t "${PROJECT_NAME}-backend:latest" \
        -f yyzs/agent/Dockerfile \
        yyzs/agent/
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -t "${PROJECT_NAME}-frontend:${VERSION}" \
        -t "${PROJECT_NAME}-frontend:latest" \
        -f yyzs/web/Dockerfile \
        yyzs/web/
    
    log_info "Docker镜像构建完成"
}

# 清理旧的容器和镜像
clean_old_resources() {
    log_info "清理旧的容器和镜像..."
    
    # 停止并删除容器
    docker-compose down --remove-orphans || true
    
    # 删除旧的镜像
    docker image prune -f
    
    # 清理未使用的卷（谨慎使用）
    if [[ "${ENVIRONMENT}" == "dev" ]]; then
        docker volume prune -f
    fi
    
    log_info "清理完成"
}

# 部署服务
deploy_services() {
    log_info "部署${ENVIRONMENT}环境服务..."
    
    # 创建必要的目录
    mkdir -p docker/ssl
    mkdir -p docker/grafana/provisioning
    mkdir -p docker/grafana/dashboards
    
    # 生成配置文件
    generate_configs
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services_health
    
    log_info "服务部署完成"
}

# 生成配置文件
generate_configs() {
    log_info "生成配置文件..."
    
    # 生成Nginx配置
    cat > docker/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server yyzs-agent:8080;
    }
    
    upstream frontend {
        server yyzs-web:3000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

    # 生成Prometheus配置
    cat > docker/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'yyzs-agent'
    static_configs:
      - targets: ['yyzs-agent:8081']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
EOF

    log_info "配置文件生成完成"
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    local services=("postgres" "redis" "yyzs-agent")
    local max_attempts=30
    local attempt=1
    
    for service in "${services[@]}"; do
        log_info "检查${service}服务..."
        
        while [[ $attempt -le $max_attempts ]]; do
            if docker-compose ps "${service}" | grep -q "healthy\|Up"; then
                log_info "✓ ${service}服务正常"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                log_error "✗ ${service}服务启动失败"
                docker-compose logs "${service}"
                exit 1
            fi
            
            log_debug "${service}服务启动中... ($attempt/$max_attempts)"
            sleep 5
            ((attempt++))
        done
        
        attempt=1
    done
    
    log_info "所有服务健康检查完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    log_info "服务资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# 查看日志
show_logs() {
    local service="${2:-}"
    
    if [[ -n "${service}" ]]; then
        log_info "查看${service}服务日志:"
        docker-compose logs -f "${service}"
    else
        log_info "查看所有服务日志:"
        docker-compose logs -f
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    log_info "服务已重启"
}

# 主函数
main() {
    local action="${2:-deploy}"
    
    case "${action}" in
        "--help")
            show_help
            exit 0
            ;;
        "--build-only")
            check_dependencies
            setup_environment
            build_backend
            build_frontend
            build_images
            ;;
        "--deploy-only")
            check_dependencies
            setup_environment
            deploy_services
            ;;
        "--clean")
            check_dependencies
            setup_environment
            clean_old_resources
            build_backend
            build_frontend
            build_images
            deploy_services
            ;;
        "--logs")
            show_logs "$@"
            ;;
        "--status")
            show_status
            ;;
        "--stop")
            stop_services
            ;;
        "--restart")
            restart_services
            ;;
        "deploy"|"")
            check_dependencies
            setup_environment
            build_backend
            build_frontend
            build_images
            deploy_services
            ;;
        *)
            log_error "未知选项: ${action}"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log_info "部署脚本被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"

# 部署完成提示
if [[ "${2:-deploy}" == "deploy" || "${2:-}" == "--clean" ]]; then
    log_info "=== 部署完成 ==="
    log_info "应用访问地址:"
    log_info "  前端: http://localhost:3000"
    log_info "  后端API: http://localhost:8080"
    log_info "  管理端点: http://localhost:8081/actuator"
    log_info "  Grafana: http://localhost:3001 (admin/admin123)"
    log_info "  Prometheus: http://localhost:9090"
    log_info ""
    log_info "常用命令:"
    log_info "  查看状态: $0 --status"
    log_info "  查看日志: $0 --logs [服务名]"
    log_info "  重启服务: $0 --restart"
    log_info "  停止服务: $0 --stop"
fi
