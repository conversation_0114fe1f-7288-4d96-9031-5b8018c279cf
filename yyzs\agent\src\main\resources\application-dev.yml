spring:
  # 动态数据源配置
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组，默认值即为 master
      primary: master
      # 严格模式，默认false. 设置为true后在未匹配到指定数据源时候会抛出异常
      strict: false
      # 数据源配置
      datasource:
        # 主数据源（系统核心数据）
        master:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${DB_HOST:**************}:${DB_PORT:5432}/${DB_NAME:dify}?currentSchema=${DB_SCHEMA:yyzs}
          username: ${DB_USERNAME:postgres}
          password: ${DB_PASSWORD:XHC12345}
          # Druid连接池配置
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            # 监控配置
            web-stat-filter:
              enabled: ${DRUID_WEB_STAT_FILTER_ENABLED:true}
              url-pattern: ${DRUID_WEB_STAT_FILTER_URL_PATTERN:/*}
              exclusions: ${DRUID_WEB_STAT_FILTER_EXCLUSIONS:"*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"}
            stat-view-servlet:
              enabled: ${DRUID_STAT_VIEW_SERVLET_ENABLED:true}
              url-pattern: ${DRUID_STAT_VIEW_SERVLET_URL_PATTERN:/druid/*}
              reset-enable: ${DRUID_STAT_VIEW_SERVLET_RESET_ENABLE:false}
              login-username: ${DRUID_STAT_VIEW_SERVLET_USERNAME:admin}
              login-password: ${DRUID_STAT_VIEW_SERVLET_PASSWORD:admin123}
            filter:
              stat:
                enabled: true
                log-slow-sql: true
                slow-sql-millis: 2000
              wall:
                enabled: true
                config:
                  multi-statement-allow: true

  # JPA开发环境配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

# 日志配置
logging:
  level:
    root: info
    com.yyzs.agent: debug
    org.springframework.security: debug
    org.springframework.web: debug
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace

# 开发环境自定义配置
yyzs:
  agent:
    # 开发环境组件安装目录
    install-path: ./dev-data/elastic
    # 开发环境组件包存储目录
    package-storage-path: ./dev-data/packages
    # 开发环境监控数据收集间隔（秒）
    monitor-interval: 10
