<template>
  <div class="external-agent-tooltip">
    <div class="tooltip-header">
      <div class="platform-info">
        <i :class="getPlatformIcon(details.platform)" class="platform-icon"></i>
        <span class="platform-name">{{ details.platform }}</span>
      </div>
      <div class="status-badge" :class="getStatusClass(details.status)">
        <i :class="getStatusIcon(details.status)"></i>
        {{ getStatusText(details.status) }}
      </div>
    </div>

    <div class="tooltip-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item" v-if="details.unitName">
            <span class="label">所属单位:</span>
            <span class="value">{{ details.unitName }}</span>
          </div>
          <div class="info-item" v-if="details.accessScope">
            <span class="label">访问范围:</span>
            <span class="value">{{ details.accessScope }}</span>
          </div>
          <div class="info-item connection-item" v-if="details.connectionUrl">
            <span class="label">连接地址:</span>
            <span class="value connection-url" :title="details.connectionUrl">{{ truncateUrl(details.connectionUrl) }}</span>
          </div>
          <div class="info-item" v-if="details.version">
            <span class="label">版本:</span>
            <span class="value">{{ details.version }}</span>
          </div>
          <div class="info-item" v-if="details.model">
            <span class="label">模型:</span>
            <span class="value">{{ details.model }}</span>
          </div>
          <div class="info-item" v-if="details.lastSync">
            <span class="label">最后同步:</span>
            <span class="value">{{ formatTime(details.lastSync) }}</span>
          </div>
        </div>
      </div>

      <!-- 连接测试信息 -->
      <div class="info-section" v-if="details.lastTestTime || details.lastTestResult || details.lastTestError">
        <h4 class="section-title">连接测试</h4>
        <div class="test-info">
          <div class="test-item" v-if="details.lastTestTime">
            <span class="label">测试时间:</span>
            <span class="value">{{ formatTime(details.lastTestTime) }}</span>
          </div>
          <div class="test-item" v-if="details.lastTestResult">
            <span class="label">测试结果:</span>
            <span class="value" :class="getTestResultClass(details.lastTestResult)">
              <i :class="getTestResultIcon(details.lastTestResult)"></i>
              {{ details.lastTestResult }}
            </span>
          </div>
          <div class="test-item error-item" v-if="details.lastTestError">
            <span class="label">错误信息:</span>
            <span class="value error-text">{{ details.lastTestError }}</span>
          </div>
        </div>
      </div>

      <!-- 管理信息 -->
      <div class="info-section" v-if="details.createByName || details.updateByName">
        <h4 class="section-title">管理信息</h4>
        <div class="info-grid">
          <div class="info-item" v-if="details.createByName">
            <span class="label">创建人:</span>
            <span class="value">{{ details.createByName }}</span>
          </div>
          <div class="info-item" v-if="details.updateByName">
            <span class="label">更新人:</span>
            <span class="value">{{ details.updateByName }}</span>
          </div>
        </div>
      </div>

      <!-- 能力信息 -->
      <div class="info-section" v-if="details.capabilities && details.capabilities.length > 0">
        <h4 class="section-title">支持能力</h4>
        <div class="capabilities">
          <span 
            v-for="capability in details.capabilities" 
            :key="capability" 
            class="capability-tag"
          >
            {{ capability }}
          </span>
        </div>
      </div>

      <!-- 使用统计 -->
      <div class="info-section" v-if="details.usage">
        <h4 class="section-title">使用统计</h4>
        <div class="usage-stats">
          <div class="stat-item">
            <span class="stat-label">总调用次数:</span>
            <span class="stat-value">{{ details.usage.totalCalls }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">成功率:</span>
            <span class="stat-value">{{ (details.usage.successRate * 100).toFixed(1) }}%</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">平均响应时间:</span>
            <span class="stat-value">{{ details.usage.avgResponseTime }}ms</span>
          </div>
        </div>
      </div>

      <!-- 配置信息 -->
      <div class="info-section" v-if="details.config">
        <h4 class="section-title">配置参数</h4>
        <div class="config-grid">
          <div class="config-item" v-if="details.config.temperature !== undefined">
            <span class="label">Temperature:</span>
            <span class="value">{{ details.config.temperature }}</span>
          </div>
          <div class="config-item" v-if="details.config.maxTokens">
            <span class="label">Max Tokens:</span>
            <span class="value">{{ details.config.maxTokens }}</span>
          </div>
          <div class="config-item" v-if="details.config.timeout">
            <span class="label">超时时间:</span>
            <span class="value">{{ details.config.timeout }}s</span>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div class="info-section error-section" v-if="details.error">
        <h4 class="section-title">错误信息</h4>
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          {{ details.error }}
        </div>
      </div>

      <!-- 平台链接 -->
      <div class="info-section" v-if="details.platformUrl">
        <a :href="details.platformUrl" target="_blank" class="platform-link">
          <i class="fas fa-external-link-alt"></i>
          访问平台
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ExternalAgentDetails } from '@/api/agents'

interface Props {
  details: ExternalAgentDetails
}

defineProps<Props>()

// 获取平台图标
const getPlatformIcon = (platform: string): string => {
  const iconMap: Record<string, string> = {
    'Dify': 'fas fa-robot',
    'OpenAI': 'fas fa-brain',
    'Claude': 'fas fa-comments',
    'Coze': 'fas fa-cogs',
    'ChatGPT': 'fas fa-comment-dots'
  }
  return iconMap[platform] || 'fas fa-cube'
}

// 获取状态样式类
const getStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    'connected': 'status-connected',
    'disconnected': 'status-disconnected', 
    'error': 'status-error',
    'syncing': 'status-syncing'
  }
  return classMap[status] || 'status-unknown'
}

// 获取状态图标
const getStatusIcon = (status: string): string => {
  const iconMap: Record<string, string> = {
    'connected': 'fas fa-check-circle',
    'disconnected': 'fas fa-times-circle',
    'error': 'fas fa-exclamation-circle',
    'syncing': 'fas fa-sync-alt fa-spin'
  }
  return iconMap[status] || 'fas fa-question-circle'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    'connected': '已连接',
    'disconnected': '未连接',
    'error': '连接错误',
    'syncing': '同步中'
  }
  return textMap[status] || '未知状态'
}

// 格式化时间
const formatTime = (timeStr: string): string => {
  try {
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  } catch {
    return timeStr
  }
}

// 截断URL显示
const truncateUrl = (url: string): string => {
  if (url.length <= 45) return url

  // 尝试保留协议和域名部分
  try {
    const urlObj = new URL(url)
    const domain = urlObj.hostname
    const protocol = urlObj.protocol

    if (`${protocol}//${domain}`.length <= 35) {
      return `${protocol}//${domain}...`
    }
  } catch {
    // 如果URL解析失败，使用简单截断
  }

  return url.substring(0, 42) + '...'
}

// 获取测试结果样式类
const getTestResultClass = (result: string): string => {
  if (result === '成功') return 'test-success'
  if (result === '失败') return 'test-failed'
  return 'test-unknown'
}

// 获取测试结果图标
const getTestResultIcon = (result: string): string => {
  if (result === '成功') return 'fas fa-check-circle'
  if (result === '失败') return 'fas fa-times-circle'
  return 'fas fa-question-circle'
}
</script>

<style scoped>
.external-agent-tooltip {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 320px;
  max-width: 450px;
  font-size: 14px;
  line-height: 1.4;
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.platform-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.platform-icon {
  font-size: 16px;
  color: #6366f1;
}

.platform-name {
  font-weight: 600;
  color: #1f2937;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-connected {
  background: #dcfce7;
  color: #166534;
}

.status-disconnected {
  background: #fef2f2;
  color: #dc2626;
}

.status-error {
  background: #fef2f2;
  color: #dc2626;
}

.status-syncing {
  background: #dbeafe;
  color: #1d4ed8;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.info-grid, .config-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item, .config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 20px;
}

.label {
  color: #6b7280;
  font-size: 12px;
  white-space: nowrap;
  flex-shrink: 0;
  margin-right: 8px;
}

.value {
  color: #1f2937;
  font-weight: 500;
  font-size: 12px;
  text-align: right;
  word-break: break-all;
  flex: 1;
}

.capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.capability-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #6b7280;
  font-size: 12px;
}

.stat-value {
  color: #1f2937;
  font-weight: 600;
  font-size: 12px;
}

.error-section {
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  background: #fef2f2;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #dc2626;
  font-size: 12px;
}

.platform-link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #6366f1;
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  padding: 6px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.platform-link:hover {
  background: #f8fafc;
  border-color: #6366f1;
}

/* 连接地址样式 */
.connection-item {
  flex-direction: column;
  align-items: flex-start !important;
  gap: 4px;
}

.connection-item .label {
  margin-right: 0;
  margin-bottom: 2px;
}

.connection-item .value {
  text-align: left;
  width: 100%;
}

.connection-url {
  font-family: monospace;
  font-size: 11px;
  color: #6366f1;
  cursor: help;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  word-break: break-all;
  max-width: 100%;
  display: block;
}

/* 测试信息样式 */
.test-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-item.error-item {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.test-success {
  color: #059669;
}

.test-failed {
  color: #dc2626;
}

.test-unknown {
  color: #6b7280;
}

.error-text {
  color: #dc2626;
  font-size: 11px;
  background: #fef2f2;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #fecaca;
  word-break: break-word;
  max-width: 100%;
}
</style>
