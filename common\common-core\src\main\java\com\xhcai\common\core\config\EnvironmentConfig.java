package com.xhcai.common.core.config;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;

/**
 * 环境变量配置加载器 支持从 .env 文件加载环境变量配置
 *
 * 实现 EnvironmentPostProcessor 接口，在应用启动早期阶段加载环境变量， 确保在其他组件初始化前完成配置加载。这是Spring
 * Boot 3.x推荐的现代化方式。
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class EnvironmentConfig implements EnvironmentPostProcessor {

    private static final Logger log = LoggerFactory.getLogger(EnvironmentConfig.class);
    private static final String ENV_FILE_NAME = ".env";
    private static final String ENV_PROPERTY_SOURCE_NAME = "envFile";

    /**
     * 处理环境配置，加载 .env 文件 这个方法会在应用启动早期阶段被Spring Boot自动调用
     */
    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 使用 System.out.println 确保在日志系统初始化前就能看到输出
        System.out.println("🚀 [EnvironmentConfig] Starting environment configuration loading...");
        System.out.println("🚀 [EnvironmentConfig] This should be the FIRST thing you see during startup!");

        log.info("=== XHC AI Plus Environment Configuration Loading ===");

        // 加载 .env 文件
        String loadedEnvFile = loadEnvFile(environment);

        // 验证必需的环境变量
        validateRequiredEnvironmentVariables(environment);

        // 输出加载状态总结
        if (loadedEnvFile != null) {
            log.info("✅ Environment configuration loaded successfully from: {}", loadedEnvFile);
        } else {
            log.info("⚠️  Environment configuration loaded (no .env file found, using system/default values)");
        }
        log.info("=== Environment Configuration Loading Complete ===");
    }

    /**
     * 加载 .env 文件
     *
     * @return 成功加载的 .env 文件路径，如果没有加载则返回 null
     */
    private String loadEnvFile(ConfigurableEnvironment environment) {
        Properties envProperties = new Properties();
        String loadedFilePath = null;

        // 检测当前运行环境
        boolean isDevelopment = isDevelopmentEnvironment(environment);
        log.info("🏷️  Detected environment: {}", isDevelopment ? "Development" : "Production");

        // 根据环境选择不同的搜索策略
        Resource[] envResources = getEnvResourcesByEnvironment(isDevelopment);

        log.info("🔍 Searching for .env file in the following locations (environment-specific):");
        for (int i = 0; i < envResources.length; i++) {
            Resource resource = envResources[i];
            String location = getResourceLocation(resource);
            log.info("  {}. {}", i + 1, location);

            if (resource.exists() && resource.isReadable()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    Properties tempProperties = new Properties();
                    tempProperties.load(inputStream);

                    // 只有当成功加载到属性时才更新主属性对象和路径
                    if (!tempProperties.isEmpty()) {
                        envProperties.putAll(tempProperties);
                        loadedFilePath = location;
                        log.info("✅ Successfully loaded {} properties from .env file: {}", tempProperties.size(), location);
                        break;
                    } else {
                        log.warn("⚠️ .env file is empty at: {}", location);
                    }
                } catch (IOException e) {
                    log.warn("❌ Failed to load .env file from: {} - {}", location, e.getMessage());
                }
            } else {
                log.debug("❌ .env file not found or not readable at: {}", location);
            }
        }

        // 如果加载了 .env 文件，则添加到环境中
        if (!envProperties.isEmpty() && loadedFilePath != null) {
            PropertiesPropertySource propertySource = new PropertiesPropertySource(ENV_PROPERTY_SOURCE_NAME, envProperties);
            // 添加到环境变量源的最后，优先级低于系统环境变量
            environment.getPropertySources().addLast(propertySource);
            log.info("📝 Added {} properties from .env file to environment", envProperties.size());

            // 输出加载的环境变量（隐藏敏感信息）
            logLoadedProperties(envProperties);

            // 验证关键环境变量是否已加载
            verifyKeyEnvironmentVariables(environment);
        } else {
            log.warn("⚠️  No .env file found in any of the searched locations");
            System.out.println("⚠️  [EnvironmentConfig] No .env file found in any of the searched locations");
        }

        System.out.println("✅ [EnvironmentConfig] Environment configuration loading completed!");
        log.info("=== XHC AI Plus Environment Configuration Loading Completed ===");

        return loadedFilePath;
    }

    /**
     * 根据环境获取 .env 文件搜索资源列表
     *
     * @param isDevelopment 是否为开发环境
     * @return 资源数组，按优先级排序
     */
    private Resource[] getEnvResourcesByEnvironment(boolean isDevelopment) {
        if (isDevelopment) {
            // 开发环境：优先搜索 admin-api/src/main/resources 目录
            return new Resource[] {
                    // admin-api/src/main/resources 目录（开发环境优先）
                    new FileSystemResource("admin-api/src/main/resources/" + ENV_FILE_NAME)
            };
        } else {
            // 生产环境：优先搜索 jar 包同级目录
            return new Resource[] {
                    // 1. jar 包同级目录（生产环境优先）
                    new FileSystemResource(ENV_FILE_NAME)
            };
        }
    }

    /**
     * 检测是否为开发环境（基于环境变量）
     *
     * @param environment Spring 环境对象
     * @return 是否为开发环境
     */
    private boolean isDevelopmentEnvironment(ConfigurableEnvironment environment) {
        // 首先检查 Spring profiles
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("dev".equals(profile) || "development".equals(profile)) {
                return true;
            }
            if ("prod".equals(profile) || "production".equals(profile)) {
                return false;
            }
        }

        // 如果没有激活的 profiles，检查默认 profiles
        String[] defaultProfiles = environment.getDefaultProfiles();
        for (String profile : defaultProfiles) {
            if ("dev".equals(profile) || "development".equals(profile)) {
                return true;
            }
            if ("prod".equals(profile) || "production".equals(profile)) {
                return false;
            }
        }

        // 检查环境变量
        String springProfile = environment.getProperty("spring.profiles.active");
        if (springProfile != null) {
            return "dev".equals(springProfile) || "development".equals(springProfile);
        }

        // 检查系统属性
        String systemProfile = System.getProperty("spring.profiles.active");
        if (systemProfile != null) {
            return "dev".equals(systemProfile) || "development".equals(systemProfile);
        }

        // 检查是否存在开发环境特有的目录结构（如 src/main/java）
        boolean hasDevStructure = new FileSystemResource("src/main/java").exists() ||
                new FileSystemResource("admin-api/src/main/java").exists();

        // 默认情况下，如果存在开发目录结构，认为是开发环境
        return hasDevStructure;
    }

    /**
     * 获取资源位置的友好描述
     */
    private String getResourceLocation(Resource resource) {
        try {
            if (resource instanceof FileSystemResource) {
                return ((FileSystemResource) resource).getPath();
            } else if (resource instanceof ClassPathResource) {
                return "classpath:" + ((ClassPathResource) resource).getPath();
            } else {
                return resource.getDescription();
            }
        } catch (Exception e) {
            return resource.getDescription();
        }
    }

    /**
     * 输出加载的环境变量（隐藏敏感信息）
     */
    private void logLoadedProperties(Properties properties) {
        log.info("📋 Loaded environment variables:");

        // 敏感信息关键字
        String[] sensitiveKeys = {"password", "secret", "key", "token", "credential"};

        properties.stringPropertyNames().stream()
                .sorted()
                .forEach(key -> {
                    String value = properties.getProperty(key);
                    boolean isSensitive = false;

                    // 检查是否为敏感信息
                    for (String sensitiveKey : sensitiveKeys) {
                        if (key.toLowerCase().contains(sensitiveKey.toLowerCase())) {
                            isSensitive = true;
                            break;
                        }
                    }

                    if (isSensitive) {
                        log.info("  {} = *** (hidden)", key);
                    } else {
                        log.info("  {} = {}", key, value);
                    }
                });
    }

    /**
     * 验证必需的环境变量
     */
    private void validateRequiredEnvironmentVariables(ConfigurableEnvironment environment) {
        log.info("🔍 Validating required environment variables...");

        String[] requiredVariables = {
                "DB_HOST",
                "DB_PORT",
                "DB_NAME",
                "DB_USERNAME",
                "DB_PASSWORD",
                "REDIS_HOST",
                "REDIS_PORT",
                "JWT_SECRET"
        };

        StringBuilder missingVariables = new StringBuilder();
        StringBuilder presentVariables = new StringBuilder();

        for (String variable : requiredVariables) {
            String value = environment.getProperty(variable);
            if (value == null || value.trim().isEmpty()) {
                if (missingVariables.length() > 0) {
                    missingVariables.append(", ");
                }
                missingVariables.append(variable);
                log.warn("  ❌ {} = (missing)", variable);
            } else {
                if (presentVariables.length() > 0) {
                    presentVariables.append(", ");
                }
                presentVariables.append(variable);

                // 隐藏敏感信息
                if (variable.toLowerCase().contains("password")
                        || variable.toLowerCase().contains("secret")
                        || variable.toLowerCase().contains("key")) {
                    log.info("  ✅ {} = *** (configured)", variable);
                } else {
                    log.info("  ✅ {} = {}", variable, value);
                }
            }
        }

        String activeProfile = environment.getProperty("spring.profiles.active", "dev");
        log.info("🏷️  Active profile: {}", activeProfile);

        if (missingVariables.length() > 0) {
            String message = "Missing required environment variables: " + missingVariables.toString()
                    + ". Please check your .env file or system environment variables.";
            log.error("❌ {}", message);

            // 在开发环境中，只记录警告而不抛出异常
            if (!"dev".equals(activeProfile)) {
                throw new IllegalStateException(message);
            } else {
                log.warn("⚠️  Development mode: continuing with missing environment variables");
            }
        } else {
            log.info("✅ All required environment variables are present: {}", presentVariables.toString());
        }
    }

    /**
     * 创建示例 .env 文件
     */
    public static void createExampleEnvFile() {
        Path envExamplePath = Paths.get(".env.example");
        Path envPath = Paths.get(".env");

        try {
            if (Files.exists(envExamplePath) && !Files.exists(envPath)) {
                Files.copy(envExamplePath, envPath);
                log.info("Created .env file from .env.example");
            }
        } catch (IOException e) {
            log.warn("Failed to create .env file from .env.example", e);
        }
    }

    /**
     * 验证关键环境变量是否已加载
     */
    private void verifyKeyEnvironmentVariables(ConfigurableEnvironment environment) {
        String[] keyVariables = {"DB_HOST", "DB_PORT", "DB_NAME", "DB_USERNAME", "DB_PASSWORD", "JWT_SECRET", "REDIS_HOST", "REDIS_PORT"};

        log.info("🔍 Verifying key environment variables after .env loading:");
        for (String key : keyVariables) {
            String value = environment.getProperty(key);
            if (value != null && !value.trim().isEmpty()) {
                if (key.contains("PASSWORD") || key.contains("SECRET")) {
                    log.info("  ✅ {} = [HIDDEN]", key);
                } else {
                    log.info("  ✅ {} = {}", key, value);
                }
            } else {
                log.warn("  ❌ {} = (not found)", key);
            }
        }
    }

    /**
     * 获取环境变量值，支持默认值
     */
    public static String getEnvValue(String key, String defaultValue) {
        String value = System.getenv(key);
        if (value == null || value.trim().isEmpty()) {
            value = System.getProperty(key, defaultValue);
        }
        return value;
    }

    /**
     * 获取环境变量值，必需参数
     */
    public static String getRequiredEnvValue(String key) {
        String value = getEnvValue(key, null);
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalStateException("Required environment variable '" + key + "' is not set");
        }
        return value;
    }

    /**
     * 检查是否为开发环境（静态方法）
     */
    public static boolean isDevelopmentEnvironment() {
        String profile = getEnvValue("SPRING_PROFILES_ACTIVE", "dev");
        if ("dev".equals(profile) || "development".equals(profile)) {
            return true;
        }
        if ("prod".equals(profile) || "production".equals(profile)) {
            return false;
        }

        // 检查是否存在开发环境特有的目录结构
        boolean hasDevStructure = new FileSystemResource("src/main/java").exists() ||
                new FileSystemResource("admin-api/src/main/java").exists();

        // 默认情况下，如果存在开发目录结构，认为是开发环境
        return hasDevStructure;
    }

    /**
     * 检查是否为生产环境
     */
    public static boolean isProductionEnvironment() {
        String profile = getEnvValue("SPRING_PROFILES_ACTIVE", "dev");
        if ("prod".equals(profile) || "production".equals(profile)) {
            return true;
        }
        if ("dev".equals(profile) || "development".equals(profile)) {
            return false;
        }

        // 如果不是明确的开发环境，且没有开发目录结构，认为是生产环境
        return !isDevelopmentEnvironment();
    }

    /**
     * 检查是否为测试环境
     */
    public static boolean isTestEnvironment() {
        String profile = getEnvValue("SPRING_PROFILES_ACTIVE", "dev");
        return "test".equals(profile);
    }
}
