<template>
  <div
    v-if="visible"
    class="voice-dialog-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
    @click="handleOverlayClick"
  >
    <div
      class="voice-dialog-container bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 relative"
      @click.stop
    >
      <!-- 关闭按钮 -->
      <button
        @click="closeDialog"
        class="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors duration-200"
      >
        <el-icon class="text-gray-500">
          <Close />
        </el-icon>
      </button>

      <!-- 标题 -->
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-2">语音对话</h2>
        <p class="text-gray-600">点击麦克风开始语音对话</p>
      </div>

      <!-- 语音可视化区域 -->
      <div class="voice-visualizer mb-8">
        <div class="flex items-center justify-center">
          <!-- 中央麦克风按钮 -->
          <button
            @click="toggleRecording"
            class="voice-button relative w-24 h-24 rounded-full flex items-center justify-center transition-all duration-300"
            :class="{
              'bg-red-500 hover:bg-red-600 shadow-lg': isRecording,
              'bg-blue-500 hover:bg-blue-600 shadow-md': !isRecording
            }"
          >
            <el-icon class="text-white text-3xl">
              <Microphone v-if="!isRecording" />
              <VideoPause v-else />
            </el-icon>
            
            <!-- 录音时的波纹效果 -->
            <div
              v-if="isRecording"
              class="absolute inset-0 rounded-full border-4 border-red-300 animate-ping"
            ></div>
            <div
              v-if="isRecording"
              class="absolute inset-0 rounded-full border-2 border-red-400 animate-pulse"
            ></div>
          </button>
        </div>

        <!-- 声波可视化 -->
        <div v-if="isRecording" class="voice-waves-container mt-6">
          <div class="flex items-center justify-center gap-1">
            <div
              v-for="i in 20"
              :key="i"
              class="voice-wave-bar bg-blue-500 rounded-full"
              :style="{
                height: `${Math.random() * 40 + 10}px`,
                width: '3px',
                animationDelay: `${i * 0.1}s`
              }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 状态显示 -->
      <div class="text-center mb-6">
        <div v-if="isPlayingWelcome" class="text-green-600 font-medium">
          <el-icon class="mr-1 animate-pulse"><Loading /></el-icon>
          正在播放欢迎语音...
        </div>
        <div v-else-if="isRecording" class="text-red-600 font-medium">
          <el-icon class="mr-1"><Microphone /></el-icon>
          正在录音... {{ recordingTime }}s
        </div>
        <div v-else-if="isProcessing" class="text-blue-600 font-medium">
          <el-icon class="mr-1 animate-spin"><Loading /></el-icon>
          正在处理语音...
        </div>
        <div v-else class="text-gray-500">
          点击麦克风开始录音
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-3">
        <!-- 停止语音播放按钮（仅在播放时显示） -->
        <button
          v-if="isPlayingWelcome"
          @click="stopWelcomeMessage"
          class="flex-1 py-3 px-4 bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors duration-200"
        >
          停止播放
        </button>

        <!-- 清除录音按钮 -->
        <button
          v-if="!isPlayingWelcome"
          @click="clearRecording"
          :disabled="!hasRecording"
          class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          清除录音
        </button>

        <!-- 发送语音按钮 -->
        <button
          v-if="!isPlayingWelcome"
          @click="sendVoiceInput"
          :disabled="!hasRecording || isProcessing"
          class="flex-1 py-3 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          发送语音
        </button>
      </div>

      <!-- 提示信息 -->
      <div class="mt-4 text-xs text-gray-500 text-center space-y-2">
        <div>支持中文、英文等多种语言识别</div>
        <div>
          <button
            @click="checkPermissions"
            class="text-blue-500 hover:text-blue-600 underline"
          >
            检查麦克风权限
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, nextTick } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Close, Microphone, VideoPause, Loading } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'voice-input': [text: string]
  'close': []
}>()

// 状态管理
const isRecording = ref(false)
const isProcessing = ref(false)
const recordingTime = ref(0)
const hasRecording = ref(false)
const recordingTimer = ref<number | null>(null)
const isPlayingWelcome = ref(false)

// 媒体录音相关
let mediaRecorder: MediaRecorder | null = null
let audioChunks: Blob[] = []

// 语音合成相关
let currentUtterance: SpeechSynthesisUtterance | null = null

// 播放欢迎语音
const playWelcomeMessage = () => {
  if (!window.speechSynthesis) {
    console.warn('浏览器不支持语音合成')
    return
  }

  const welcomeText = "现在我们可以开始通过语音对话交流了，请问您有什么要咨询的吗？"

  // 停止之前的语音
  window.speechSynthesis.cancel()

  const utterance = new SpeechSynthesisUtterance(welcomeText)

  // 设置语音参数
  utterance.lang = 'zh-CN' // 中文
  utterance.rate = 0.9 // 语速稍慢一点
  utterance.pitch = 1.1 // 音调稍高一点
  utterance.volume = 0.8 // 音量

  // 语音事件监听
  utterance.onstart = () => {
    isPlayingWelcome.value = true
    console.log('开始播放欢迎语音')
  }

  utterance.onend = () => {
    isPlayingWelcome.value = false
    console.log('欢迎语音播放完成')
  }

  utterance.onerror = (event) => {
    isPlayingWelcome.value = false
    console.error('语音播放错误:', event.error)
  }

  currentUtterance = utterance

  // 播放语音
  window.speechSynthesis.speak(utterance)
}

// 停止语音播放
const stopWelcomeMessage = () => {
  if (window.speechSynthesis) {
    window.speechSynthesis.cancel()
    isPlayingWelcome.value = false
  }
}

// 开始/停止录音
const toggleRecording = async () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    await startRecording()
  }
}

// 检查麦克风权限
const checkMicrophonePermission = async () => {
  try {
    // 检查浏览器是否支持 MediaDevices API
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('BROWSER_NOT_SUPPORTED')
    }

    // 检查是否为 HTTPS 环境（本地开发除外）
    if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
      throw new Error('HTTPS_REQUIRED')
    }

    // 检查麦克风权限状态
    if (navigator.permissions) {
      const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName })
      if (permission.state === 'denied') {
        throw new Error('PERMISSION_DENIED')
      }
    }

    return true
  } catch (error) {
    throw error
  }
}

// 开始录音
const startRecording = async () => {
  try {
    // 先检查权限和环境
    await checkMicrophonePermission()

    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    })

    mediaRecorder = new MediaRecorder(stream)
    audioChunks = []

    mediaRecorder.ondataavailable = (event) => {
      audioChunks.push(event.data)
    }

    mediaRecorder.onstop = () => {
      const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
      hasRecording.value = true
      console.log('录音完成', audioBlob)
    }

    mediaRecorder.start()
    isRecording.value = true
    recordingTime.value = 0

    // 开始计时
    recordingTimer.value = window.setInterval(() => {
      recordingTime.value++
    }, 1000)

  } catch (error: any) {
    console.error('录音失败:', error)
    handleRecordingError(error)
  }
}

// 处理录音错误
const handleRecordingError = (error: any) => {
  let errorMessage = '录音失败，请重试'
  let errorTitle = '录音错误'

  if (error.name === 'NotAllowedError' || error.message === 'PERMISSION_DENIED') {
    errorTitle = '麦克风权限被拒绝'
    errorMessage = `请按以下步骤开启麦克风权限：

1. 点击地址栏左侧的锁形图标
2. 将"麦克风"权限设置为"允许"
3. 刷新页面后重试

或者在浏览器设置中允许此网站访问麦克风。`
  } else if (error.name === 'NotFoundError') {
    errorTitle = '未找到麦克风设备'
    errorMessage = '请检查您的麦克风设备是否正常连接，或尝试重新插拔麦克风。'
  } else if (error.name === 'NotReadableError') {
    errorTitle = '麦克风设备被占用'
    errorMessage = '麦克风可能正在被其他应用程序使用，请关闭其他使用麦克风的程序后重试。'
  } else if (error.message === 'BROWSER_NOT_SUPPORTED') {
    errorTitle = '浏览器不支持'
    errorMessage = '您的浏览器不支持录音功能，请使用 Chrome、Firefox、Safari 等现代浏览器。'
  } else if (error.message === 'HTTPS_REQUIRED') {
    errorTitle = '需要安全连接'
    errorMessage = '录音功能需要在 HTTPS 环境下使用，请确保网站使用安全连接。'
  }

  // 使用 Element Plus 的消息框显示错误
  ElMessageBox.alert(errorMessage, errorTitle, {
    confirmButtonText: '我知道了',
    type: 'warning',
    dangerouslyUseHTMLString: true
  })
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder && isRecording.value) {
    mediaRecorder.stop()
    mediaRecorder.stream.getTracks().forEach(track => track.stop())
    isRecording.value = false
    
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }
  }
}

// 清除录音
const clearRecording = () => {
  hasRecording.value = false
  audioChunks = []
  recordingTime.value = 0
}

// 发送语音输入
const sendVoiceInput = async () => {
  if (!hasRecording.value) return

  isProcessing.value = true
  
  try {
    // 模拟语音识别处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟识别结果
    const mockText = "这是语音识别的结果文本"
    emit('voice-input', mockText)
    
    // 关闭弹窗
    closeDialog()
  } catch (error) {
    console.error('语音处理失败:', error)
    alert('语音处理失败，请重试')
  } finally {
    isProcessing.value = false
  }
}

// 关闭弹窗
const closeDialog = () => {
  stopRecording()
  clearRecording()
  stopWelcomeMessage()
  emit('update:visible', false)
  emit('close')
}

// 点击遮罩层关闭
const handleOverlayClick = () => {
  closeDialog()
}

// 检查权限状态
const checkPermissions = async () => {
  try {
    await checkMicrophonePermission()
    ElMessageBox.alert(
      '麦克风权限检查通过！您可以正常使用语音录入功能。',
      '权限检查结果',
      {
        confirmButtonText: '好的',
        type: 'success'
      }
    )
  } catch (error: any) {
    handleRecordingError(error)
  }
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时，延迟播放欢迎语音
    nextTick(() => {
      setTimeout(() => {
        playWelcomeMessage()
      }, 500) // 延迟500ms播放，让弹窗动画完成
    })
  } else {
    // 弹窗关闭时，停止所有音频
    stopRecording()
    clearRecording()
    stopWelcomeMessage()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  stopRecording()
  stopWelcomeMessage()
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
  }
})
</script>

<style scoped>
.voice-dialog-overlay {
  backdrop-filter: blur(4px);
}

.voice-dialog-container {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.voice-button {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.voice-button:hover {
  transform: scale(1.05);
}

.voice-wave-bar {
  animation: waveAnimation 1s ease-in-out infinite;
}

@keyframes waveAnimation {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(2);
  }
}

/* 录音波纹效果 */
@keyframes ping {
  75%, 100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>
