package com.xhcai.modules.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.constant.CommonConstants;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.core.utils.XhcaiUtils;
import com.xhcai.modules.system.dto.SysDictQueryDTO;
import com.xhcai.modules.system.entity.SysDict;
import com.xhcai.modules.system.mapper.SysDictMapper;
import com.xhcai.modules.system.service.ISysDictDataService;
import com.xhcai.modules.system.service.ISysDictService;
import com.xhcai.modules.system.vo.SysDictVO;

/**
 * 字典类型服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master") // 使用主数据源
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements ISysDictService {

    private static final Logger log = LoggerFactory.getLogger(SysDictServiceImpl.class);

    @Autowired
    private SysDictMapper dictMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Override
    public PageResult<SysDictVO> selectDictPage(SysDictQueryDTO queryDTO) {
        Page<SysDict> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

        // 使用数据库分页查询
        Page<SysDict> dictPage = dictMapper.selectDictPage(
                page,
                queryDTO.getDictName(),
                queryDTO.getDictType(),
                queryDTO.getStatus()
        );

        List<SysDictVO> dictVOs = dictPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return PageResult.of(dictVOs, dictPage.getTotal(), dictPage.getCurrent(), dictPage.getSize());
    }

    @Override
    public List<SysDictVO> selectDictList(SysDictQueryDTO queryDTO) {
        List<SysDict> dicts = dictMapper.selectDictList(
                queryDTO.getDictName(),
                queryDTO.getDictType(),
                queryDTO.getStatus()
        );

        return dicts.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public SysDictVO selectDictById(String dictId) {
        if (dictId == null) {
            return null;
        }

        SysDict dict = getById(dictId);
        return convertToVO(dict);
    }

    @Override
    public SysDict selectByDictType(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            return null;
        }
        return dictMapper.selectByDictType(dictType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDict(SysDict dict) {
        // 参数校验
        validateDict(dict, true);

        // 检查字典类型是否已存在
        if (existsDictType(dict.getDictType(), null)) {
            throw new BusinessException("字典类型已存在");
        }

        // 判断字典类型的ID是否存在，如果不存在，则按照规则生成： uuid(dictType)
        if (!StringUtils.hasText(dict.getId())) {
            dict.setId(XhcaiUtils.generateSimpleUuidFromString(dict.getDictType()));
        }

        // 设置默认状态
        if (!StringUtils.hasText(dict.getStatus())) {
            dict.setStatus(CommonConstants.STATUS_NORMAL);
        }

        boolean result = saveOrUpdate(dict);
        if (result) {
            log.info("创建字典类型成功: {}", dict.getDictType());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDict(SysDict dict) {
        // 参数校验
        validateDict(dict, false);

        // 检查字典类型是否已存在
        if (StringUtils.hasText(dict.getDictType()) && existsDictType(dict.getDictType(), dict.getId())) {
            throw new BusinessException("字典类型已存在");
        }

        boolean result = updateById(dict);
        if (result) {
            log.info("更新字典类型成功: {}", dict.getDictType());
            // 刷新缓存
            refreshCache();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDicts(List<String> dictIds) {
        if (CollectionUtils.isEmpty(dictIds)) {
            throw new BusinessException("删除的字典ID不能为空");
        }

        // 删除字典类型及其关联的字典数据
        for (String dictId : dictIds) {
            SysDict dict = getById(dictId);
            if (dict != null) {
                // 删除关联的字典数据
                dictDataService.deleteByDictType(dict.getDictType());
                // 删除字典类型
                removeById(dictId);
                log.info("删除字典类型成功: {}", dict.getDictType());
            }
        }

        // 刷新缓存
        refreshCache();
        return true;
    }

    /**
     * 创建字典类型（如果不存在）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDictTypeIfNotExists(String dictType, String dictName, String remark) {
        createDictTypeIfNotExists(dictType, dictName, remark, "N");
    }

    /**
     * 创建字典类型（如果不存在）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDictTypeIfNotExists(String dictType, String dictName, String remark, String isSystemDict) {
        SysDict existingDict = selectByDictType(dictType);
        if (existingDict == null) {
            SysDict dict = new SysDict();
            dict.setDictName(dictName);
            dict.setDictType(dictType);
            dict.setStatus("0");
            dict.setRemark(remark);
            dict.setIsSystemDict(isSystemDict);
            insertDict(dict);
            log.info("创建字典类型: {} - {} (系统字典: {})", dictType, dictName, isSystemDict);
        } else {
            log.debug("字典类型已存在: {} - {}", dictType, dictName);
        }
    }

    @Override
    public boolean existsDictType(String dictType, String excludeId) {
        if (!StringUtils.hasText(dictType)) {
            return false;
        }
        return dictMapper.checkDictTypeExists(dictType, excludeId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> dictIds, String status) {
        if (CollectionUtils.isEmpty(dictIds) || !StringUtils.hasText(status)) {
            throw new BusinessException("参数不能为空");
        }

        for (String dictId : dictIds) {
            SysDict dict = new SysDict();
            dict.setId(dictId);
            dict.setStatus(status);
            updateById(dict);
        }

        // 刷新缓存
        refreshCache();
        return true;
    }

    @Override
    public List<String> selectAllDictTypes() {
        return dictMapper.selectAllDictTypes();
    }

    @Override
    public void refreshCache() {
        // 刷新字典数据缓存
        dictDataService.refreshCache();
        log.info("字典缓存刷新完成");
    }

    @Override
    public List<SysDictVO> exportDicts(SysDictQueryDTO queryDTO) {
        return selectDictList(queryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importDicts(List<SysDict> dictList) {
        if (CollectionUtils.isEmpty(dictList)) {
            throw new BusinessException("导入数据不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder failureMsg = new StringBuilder();

        for (SysDict dict : dictList) {
            try {
                if (!existsDictType(dict.getDictType(), null)) {
                    insertDict(dict);
                    successCount++;
                } else {
                    failCount++;
                    failureMsg.append("字典类型 ").append(dict.getDictType()).append(" 已存在; ");
                }
            } catch (Exception e) {
                failCount++;
                failureMsg.append("字典类型 ").append(dict.getDictType()).append(" 导入失败: ").append(e.getMessage()).append("; ");
            }
        }

        return String.format("导入完成，成功 %d 条，失败 %d 条。%s", successCount, failCount, failureMsg.toString());
    }

    /**
     * 参数校验
     */
    private void validateDict(SysDict dict, boolean isInsert) {
        if (dict == null) {
            throw new BusinessException("字典信息不能为空");
        }

        if (!StringUtils.hasText(dict.getDictName())) {
            throw new BusinessException("字典名称不能为空");
        }

        if (!StringUtils.hasText(dict.getDictType())) {
            throw new BusinessException("字典类型不能为空");
        }

        if (StringUtils.hasText(dict.getStatus()) && !CommonConstants.STATUS_NORMAL.equals(dict.getStatus())
                && !CommonConstants.STATUS_DISABLE.equals(dict.getStatus())) {
            throw new BusinessException("状态值必须为0或1");
        }
    }

    /**
     * 转换为VO对象
     */
    private SysDictVO convertToVO(SysDict dict) {
        if (dict == null) {
            return null;
        }

        SysDictVO dictVO = new SysDictVO();
        BeanUtils.copyProperties(dict, dictVO);
        return dictVO;
    }
}
