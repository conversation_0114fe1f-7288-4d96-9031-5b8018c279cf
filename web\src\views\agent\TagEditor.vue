<template>
  <!-- 标签编辑模态框 -->
  <div class="modal" v-show="visible" @click="handleModalClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>编辑标签</h3>
        <button class="modal-close" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="current-tags">
          <label>当前标签</label>
          <div class="tags-list">
            <div v-for="tag in currentTags" :key="tag" class="tag-item">
              {{ tag }}
              <button class="tag-remove" @click="removeTag(tag)">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="add-tag-section">
          <label>添加新标签</label>
          <div class="add-tag-input">
            <input
              type="text"
              v-model="newTagInput"
              placeholder="输入新标签名称"
              @keypress.enter="addNewTag"
            >
            <button class="btn btn-primary" @click="addNewTag">
              <i class="fas fa-plus"></i>
              添加
            </button>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="closeModal">取消</button>
        <button class="btn btn-primary" @click="saveChanges">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 定义组件属性
interface Props {
  visible: boolean
  tags: string[]
}

// 定义事件
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'update:tags', value: string[]): void
  (e: 'save', value: string[]): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  tags: () => []
})

const emit = defineEmits<Emits>()

// 响应式数据
const currentTags = ref<string[]>([])
const newTagInput = ref('')

// 监听props变化，同步数据
watch(() => props.tags, (newTags) => {
  currentTags.value = [...newTags]
}, { immediate: true })

watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 模态框打开时重置数据
    currentTags.value = [...props.tags]
    newTagInput.value = ''
  }
})

// 方法
const handleModalClick = () => {
  closeModal()
}

const closeModal = () => {
  emit('update:visible', false)
  emit('close')
}

const addNewTag = () => {
  const tagName = newTagInput.value.trim()
  if (!tagName) return

  if (currentTags.value.includes(tagName)) {
    alert('标签已存在')
    return
  }

  currentTags.value.push(tagName)
  newTagInput.value = ''
}

const removeTag = (tagName: string) => {
  currentTags.value = currentTags.value.filter(tag => tag !== tagName)
}

const saveChanges = () => {
  emit('update:tags', [...currentTags.value])
  emit('save', [...currentTags.value])
  closeModal()
}
</script>

<style scoped>
/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 25px 30px 20px;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 25px 30px;
}

.modal-footer {
  padding: 20px 30px 25px;
  border-top: 1px solid #f1f3f4;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.current-tags {
  margin-bottom: 25px;
}

.current-tags label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 12px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  color: #667eea;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #667eea30;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-remove {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 10px;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tag-remove:hover {
  background: #667eea;
  color: white;
}

.add-tag-section label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 12px;
}

.add-tag-input {
  display: flex;
  gap: 10px;
}

.add-tag-input input {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.add-tag-input input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.btn-secondary:hover {
  background: #e9ecef;
  color: #495057;
}
</style>
