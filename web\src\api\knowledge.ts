/**
 * 知识库管理相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse, PageResult, PaginatedResponse } from '@/types/api'

// 知识库基本信息
export interface KnowledgeBase {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  status: 'active' | 'inactive' | 'processing'
  isPublic: boolean
  createdBy: string
  createdAt: string
  updatedAt: string
  documentCount: number
  totalSize: number
  config: KnowledgeBaseConfig
}

// 知识库配置
export interface KnowledgeBaseConfig {
  chunkSize: number
  chunkOverlap: number
  embeddingModel: string
  indexType: string
  searchSettings: {
    similarity: number
    maxResults: number
  }
  settings: Record<string, any>
}

// 知识库文档
export interface KnowledgeDocument {
  id: string
  datasetId: string
  name: string
  type: 'text' | 'pdf' | 'word' | 'markdown' | 'url'
  size: number
  status: 'uploaded' | 'processing' | 'processed' | 'failed'
  content?: string
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
  chunks?: DocumentChunk[]
  categoryId?: string
  categoryName?: string
  permission?: string
}

// 文档块
export interface DocumentChunk {
  id: string
  documentId: string
  content: string
  embedding?: number[]
  metadata: Record<string, any>
  position: number
}

// 知识库查询参数
export interface KnowledgeQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  tags?: string[]
  status?: 'active' | 'inactive' | 'processing'
  isPublic?: boolean
  createdBy?: string
  sortBy?: 'name' | 'createdAt' | 'documentCount' | 'totalSize'
  sortOrder?: 'asc' | 'desc'
}

// 创建知识库请求
export interface CreateKnowledgeBaseRequest {
  name: string
  description: string
  category: string
  tags: string[]
  isPublic: boolean
  config: KnowledgeBaseConfig
}

// 更新知识库请求
export interface UpdateKnowledgeBaseRequest {
  name?: string
  description?: string
  category?: string
  tags?: string[]
  status?: 'active' | 'inactive'
  isPublic?: boolean
  config?: Partial<KnowledgeBaseConfig>
}

// 文档上传请求
export interface UploadDocumentRequest {
  name: string
  type: 'text' | 'pdf' | 'word' | 'markdown' | 'url'
  content?: string
  file?: File
  url?: string
  metadata?: Record<string, any>
}

// 知识库搜索请求
export interface SearchKnowledgeRequest {
  query: string
  datasetIds?: string[]
  limit?: number
  similarity?: number
  filters?: Record<string, any>
}

// 知识库搜索结果
export interface SearchResult {
  id: string
  content: string
  score: number
  metadata: Record<string, any>
  document: {
    id: string
    name: string
    type: string
  }
  knowledgeBase: {
    id: string
    name: string
  }
}

// 文档分类
export interface DocumentCategory {
  id: string
  datasetId: string
  name: string
  description?: string
  parentId?: string
  level: number
  sortOrder: number
  fileCount: number
  enabled: boolean
  createdAt: string
  updatedAt: string
  children?: DocumentCategory[]
}

// 创建分类请求
export interface CreateCategoryRequest {
  datasetId: string
  name: string
  description?: string
  parentId?: string
  sortOrder?: number
}

// 更新分类请求
export interface UpdateCategoryRequest {
  name: string
  description?: string
  sortOrder?: number
  enabled?: boolean
}

// 文档权限设置请求
export interface DocumentPermissionRequest {
  documentIds: string[]
  permissionType: 'public' | 'private' | 'role' | 'department' | 'users'
  roleIds?: string[]
  departmentIds?: string[]
  userIds?: string[]
  permissionLevel?: string
}

/**
 * 知识库管理API类
 */
export class KnowledgeAPI {
  /**
   * 获取知识库列表
   */
  static async getKnowledgeBases(params?: KnowledgeQueryParams): Promise<ApiResponse<PaginatedResponse<KnowledgeBase>>> {
    return apiClient.get<PaginatedResponse<KnowledgeBase>>('/api/knowledge-bases', params)
  }

  /**
   * 获取知识库详情
   */
  static async getKnowledgeBaseById(id: string): Promise<ApiResponse<KnowledgeBase>> {
    return apiClient.get<KnowledgeBase>(`/api/knowledge-bases/${id}`)
  }

  /**
   * 创建知识库
   */
  static async createKnowledgeBase(data: CreateKnowledgeBaseRequest): Promise<ApiResponse<KnowledgeBase>> {
    return apiClient.post<KnowledgeBase>('/api/knowledge-bases', data)
  }

  /**
   * 更新知识库
   */
  static async updateKnowledgeBase(id: string, data: UpdateKnowledgeBaseRequest): Promise<ApiResponse<KnowledgeBase>> {
    return apiClient.put<KnowledgeBase>(`/api/knowledge-bases/${id}`, data)
  }

  /**
   * 删除知识库
   */
  static async deleteKnowledgeBase(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/knowledge-bases/${id}`)
  }

  /**
   * 获取知识库文档列表
   */
  static async getDocuments(datasetId: string, params?: {
    page?: number
    pageSize?: number
    keyword?: string
    type?: string
    status?: string
    categoryId?: string
  }): Promise<ApiResponse<PageResult<KnowledgeDocument>>> {
    return apiClient.get<PageResult<KnowledgeDocument>>(`/api/rag/documents/page`, {
      ...params,
      datasetId: datasetId
    })
  }

  /**
   * 获取文档详情
   */
  static async getDocumentById(documentId: string): Promise<ApiResponse<KnowledgeDocument>> {
    return apiClient.get<KnowledgeDocument>(`/api/documents/${documentId}`)
  }

  /**
   * 上传文档
   */
  static async uploadDocument(datasetId: string, data: UploadDocumentRequest): Promise<ApiResponse<KnowledgeDocument>> {
    const formData = new FormData()
    formData.append('name', data.name)
    formData.append('type', data.type)

    if (data.file) {
      formData.append('file', data.file)
    }
    if (data.content) {
      formData.append('content', data.content)
    }
    if (data.url) {
      formData.append('url', data.url)
    }
    if (data.metadata) {
      formData.append('metadata', JSON.stringify(data.metadata))
    }

    // 直接使用postFormData方法，避免手动设置Content-Type
    return apiClient.postFormData<KnowledgeDocument>(`/api/knowledge-bases/${datasetId}/documents`, formData)
  }

  /**
   * 重新处理文档
   */
  static async reprocessDocument(documentId: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/documents/${documentId}/reprocess`)
  }

  /**
   * 搜索知识库
   */
  static async searchKnowledge(data: SearchKnowledgeRequest): Promise<ApiResponse<SearchResult[]>> {
    return apiClient.post<SearchResult[]>('/api/knowledge/search', data)
  }

  /**
   * 获取文档块列表
   */
  static async getDocumentChunks(documentId: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<PaginatedResponse<DocumentChunk>>> {
    return apiClient.get<PaginatedResponse<DocumentChunk>>(`/api/documents/${documentId}/chunks`, params)
  }

  /**
   * 获取知识库分类（旧接口，保持兼容性）
   */
  static async getKnowledgeBaseCategories(): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>('/api/knowledge-bases/categories')
  }

  /**
   * 获取知识库统计信息
   */
  static async getKnowledgeBaseStats(id: string): Promise<ApiResponse<{
    documentCount: number
    totalSize: number
    chunkCount: number
    searchCount: number
    lastUpdated: string
  }>> {
    return apiClient.get(`/api/knowledge-bases/${id}/stats`)
  }

  /**
   * 导出知识库
   */
  static async exportKnowledgeBase(id: string, format: 'json' | 'csv' | 'txt'): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post<{ downloadUrl: string }>(`/api/knowledge-bases/${id}/export`, { format })
  }

  /**
   * 导入知识库
   */
  static async importKnowledgeBase(file: File): Promise<ApiResponse<KnowledgeBase>> {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post<KnowledgeBase>('/api/knowledge-bases/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // ==================== 文档分类管理 ====================

  /**
   * 获取分类树
   */
  static async getCategoryTree(datasetId?: string): Promise<ApiResponse<DocumentCategory[]>> {
    const params = datasetId ? { datasetId } : {}
    return apiClient.get<DocumentCategory[]>('/api/rag/categories/tree', params)
  }

  /**
   * 获取分类列表
   */
  static async getCategories(parentId?: string, datasetId?: string): Promise<ApiResponse<DocumentCategory[]>> {
    const params: any = {}
    if (parentId) params.parentId = parentId
    if (datasetId) params.datasetId = datasetId
    return apiClient.get<DocumentCategory[]>('/api/rag/categories', params)
  }

  /**
   * 获取分类详情
   */
  static async getCategoryById(categoryId: string): Promise<ApiResponse<DocumentCategory>> {
    return apiClient.get<DocumentCategory>(`/api/rag/categories/${categoryId}`)
  }

  /**
   * 创建分类
   */
  static async createCategory(data: CreateCategoryRequest): Promise<ApiResponse<DocumentCategory>> {
    return apiClient.post<DocumentCategory>('/api/rag/categories', data)
  }

  /**
   * 更新分类
   */
  static async updateCategory(categoryId: string, data: UpdateCategoryRequest): Promise<ApiResponse<DocumentCategory>> {
    return apiClient.put<DocumentCategory>(`/api/rag/categories/${categoryId}`, data)
  }

  /**
   * 删除分类
   */
  static async deleteCategory(categoryId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/categories/${categoryId}`)
  }

  /**
   * 检查分类名称是否可用
   */
  static async checkCategoryName(name: string, parentId?: string, excludeId?: string): Promise<ApiResponse<boolean>> {
    return apiClient.get<boolean>('/api/rag/categories/check-name', { name, parentId, excludeId })
  }

  // ==================== 文档操作 ====================

  /**
   * 下载文档
   */
  static async downloadDocument(documentId: string): Promise<ApiResponse<string>> {
    return apiClient.get<string>(`/api/rag/documents/${documentId}/download`)
  }

  /**
   * 批量下载文档
   */
  static async batchDownloadDocuments(documentIds: string[]): Promise<ApiResponse<string>> {
    return apiClient.post<string>('/api/rag/documents/batch/download', documentIds)
  }

  /**
   * 删除文档
   */
  static async deleteDocument(documentId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/documents/${documentId}`)
  }

  /**
   * 批量删除文档
   */
  static async batchDeleteDocuments(documentIds: string[]): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/documents/batch/delete', documentIds)
  }

  /**
   * 更新文档分类
   */
  static async updateDocumentCategory(documentId: string, categoryId?: string): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`/api/rag/documents/${documentId}/category?categoryId=${categoryId || ''}`)
  }

  /**
   * 批量更新文档分类
   */
  static async batchUpdateDocumentCategory(documentIds: string[], categoryId?: string): Promise<ApiResponse<void>> {
    const params = new URLSearchParams()
    documentIds.forEach(id => params.append('documentIds', id))
    if (categoryId) {
      params.append('categoryId', categoryId)
    }
    return apiClient.post<void>(`/api/rag/documents/batch/category?${params.toString()}`)
  }

  /**
   * 根据分类查询文档
   */
  static async getDocumentsByCategory(categoryId: string): Promise<ApiResponse<KnowledgeDocument[]>> {
    return apiClient.get<KnowledgeDocument[]>(`/api/rag/documents/category/${categoryId}`)
  }

  // ==================== 文档权限管理 ====================

  /**
   * 设置文档权限
   */
  static async setDocumentPermission(data: DocumentPermissionRequest): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/documents/permissions', data)
  }

  /**
   * 获取文档权限
   */
  static async getDocumentPermissions(documentId: string): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>(`/api/rag/documents/${documentId}/permissions`)
  }
}

export default KnowledgeAPI
