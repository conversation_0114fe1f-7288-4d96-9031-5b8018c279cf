<template>
  <div
    v-if="visible"
    class="fixed inset-0 bg-transparent flex items-center justify-center z-[9999]"
    @click="handleClose"
    style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100vw; height: 100vh;"
  >
    <div
      class="bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-2xl mx-4 relative z-[10000] overflow-hidden"
      @click.stop
      style="min-width: 480px; max-height: 90vh;"
    >
      <!-- 头部区域 -->
      <div class="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 px-6 py-5 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
              {{ (user.name || user.username || '用户').charAt(0).toUpperCase() }}
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-800">个人信息</h2>
              <p class="text-sm text-gray-500 mt-1">查看和管理您的个人资料</p>
            </div>
          </div>
          <button
            @click="handleClose"
            class="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
            title="关闭"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="p-6 overflow-y-auto" style="max-height: calc(90vh - 200px);">
        <div class="space-y-6">
          <!-- 基本信息卡片 -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <el-icon class="text-blue-500"><User /></el-icon>
              基本信息
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-600">姓名</label>
                <div class="bg-white/80 rounded-lg px-4 py-3 text-gray-800 shadow-sm">
                  {{ user.name || user.username || '未设置' }}
                </div>
              </div>
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-600">邮箱</label>
                <div class="bg-white/80 rounded-lg px-4 py-3 text-gray-800 shadow-sm">
                  {{ user.email || '未设置' }}
                </div>
              </div>
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-600">用户ID</label>
                <div class="bg-white/80 rounded-lg px-4 py-3 text-gray-800 font-mono shadow-sm">
                  {{ user.id }}
                </div>
              </div>
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-600">角色</label>
                <div class="bg-white/80 rounded-lg px-4 py-3 shadow-sm">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ getRoleText(user.role) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 账户统计卡片 -->
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <el-icon class="text-green-500"><DataAnalysis /></el-icon>
              使用统计
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ stats.conversations }}</div>
                <div class="text-sm text-gray-600 mt-1">对话数量</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ stats.messages }}</div>
                <div class="text-sm text-gray-600 mt-1">消息总数</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ stats.agents }}</div>
                <div class="text-sm text-gray-600 mt-1">使用智能体</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-orange-600">{{ stats.knowledgeBases }}</div>
                <div class="text-sm text-gray-600 mt-1">知识库</div>
              </div>
            </div>
          </div>

          <!-- 最近活动卡片 -->
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <el-icon class="text-purple-500"><Clock /></el-icon>
              最近活动
            </h3>
            <div class="space-y-3">
              <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-100">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm" :class="getActivityIconClass(activity.type)">
                  {{ getActivityIcon(activity.type) }}
                </div>
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-800">{{ activity.title }}</div>
                  <div class="text-xs text-gray-500">{{ formatTime(activity.time) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div class="bg-gray-50 px-6 py-4 border-t border-gray-100 flex justify-end gap-3">
        <button
          @click="handleClose"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 font-medium"
        >
          关闭
        </button>
        <button
          @click="handleEdit"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium"
        >
          编辑资料
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Close, User, DataAnalysis, Clock } from '@element-plus/icons-vue'
import type { UserInfo } from '@/stores/authStore'

interface Activity {
  id: string
  type: 'conversation' | 'agent' | 'knowledge' | 'login'
  title: string
  time: Date
}

interface Props {
  visible: boolean
  user: any // 使用 any 类型以兼容 readonly 包装的 UserInfo
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'edit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 模拟统计数据
const stats = ref({
  conversations: 42,
  messages: 1286,
  agents: 8,
  knowledgeBases: 5
})

// 模拟最近活动
const recentActivities = ref<Activity[]>([
  {
    id: '1',
    type: 'conversation',
    title: '开始了新的AI对话',
    time: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前
  },
  {
    id: '2',
    type: 'agent',
    title: '使用了代码生成助手',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前
  },
  {
    id: '3',
    type: 'knowledge',
    title: '查询了技术文档知识库',
    time: new Date(Date.now() - 1000 * 60 * 60 * 24) // 1天前
  },
  {
    id: '4',
    type: 'login',
    title: '登录系统',
    time: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2) // 2天前
  }
])

const handleClose = () => {
  emit('update:visible', false)
}

const handleEdit = () => {
  emit('edit')
  // 这里可以打开编辑弹窗或跳转到编辑页面
  console.log('编辑个人资料')
}

const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    'admin': '管理员',
    'user': '普通用户',
    'vip': 'VIP用户'
  }
  return roleMap[role || 'user'] || '普通用户'
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'conversation': '💬',
    'agent': '🤖',
    'knowledge': '📖',
    'login': '🔐'
  }
  return iconMap[type] || '📝'
}

const getActivityIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    'conversation': 'bg-blue-100 text-blue-600',
    'agent': 'bg-green-100 text-green-600',
    'knowledge': 'bg-purple-100 text-purple-600',
    'login': 'bg-orange-100 text-orange-600'
  }
  return classMap[type] || 'bg-gray-100 text-gray-600'
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}
</script>

<style scoped>
/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
