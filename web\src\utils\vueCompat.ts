/**
 * Vue3 兼容性处理工具
 * 确保Vue3特性在目标浏览器中正常工作
 */

import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import type { Ref, ComputedRef, WatchStopHandle } from 'vue'

/**
 * 安全的响应式数据创建
 */
export function safeRef<T>(value: T): Ref<T> {
  try {
    return ref(value) as Ref<T>
  } catch (error) {
    console.warn('Failed to create ref, falling back to plain object:', error)
    return { value } as Ref<T>
  }
}

/**
 * 安全的响应式对象创建
 */
export function safeReactive<T extends object>(obj: T): T {
  try {
    return reactive(obj) as T
  } catch (error) {
    console.warn('Failed to create reactive object, falling back to plain object:', error)
    return obj
  }
}

/**
 * 安全的计算属性创建
 */
export function safeComputed<T>(getter: () => T): ComputedRef<T> {
  try {
    return computed(getter)
  } catch (error) {
    console.warn('Failed to create computed property, falling back to getter function:', error)
    // 创建一个模拟的计算属性
    let cachedValue: T
    let isValid = false
    
    return {
      get value(): T {
        if (!isValid) {
          cachedValue = getter()
          isValid = true
        }
        return cachedValue
      }
    } as ComputedRef<T>
  }
}

/**
 * 安全的监听器创建
 */
export function safeWatch<T>(
  source: () => T,
  callback: (newValue: T, oldValue: T) => void,
  options?: { immediate?: boolean; deep?: boolean }
): WatchStopHandle {
  try {
    // Vue 3 的 watch 函数当 source 是函数时，回调接收的是函数的返回值
    return watch(
      source,
      callback as any, // 使用类型断言来解决类型不匹配问题
      options
    )
  } catch (error) {
    console.warn('Failed to create watcher, falling back to manual polling:', error)

    let oldValue = source()
    const interval = setInterval(() => {
      const newValue = source()
      if (newValue !== oldValue) {
        callback(newValue, oldValue)
        oldValue = newValue
      }
    }, 100)

    return () => clearInterval(interval)
  }
}

/**
 * 安全的生命周期钩子
 */
export function safeOnMounted(callback: () => void): void {
  try {
    onMounted(callback)
  } catch (error) {
    console.warn('Failed to register onMounted hook, executing immediately:', error)
    // 如果不在组件上下文中，立即执行
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', callback)
    } else {
      callback()
    }
  }
}

/**
 * 安全的卸载钩子
 */
export function safeOnUnmounted(callback: () => void): void {
  try {
    onUnmounted(callback)
  } catch (error) {
    console.warn('Failed to register onUnmounted hook, registering beforeunload:', error)
    window.addEventListener('beforeunload', callback)
  }
}

/**
 * 安全的nextTick
 */
export function safeNextTick(callback?: () => void): Promise<void> {
  try {
    return nextTick(callback)
  } catch (error) {
    console.warn('Failed to use nextTick, falling back to setTimeout:', error)
    return new Promise(resolve => {
      setTimeout(() => {
        if (callback) callback()
        resolve()
      }, 0)
    })
  }
}

/**
 * Teleport组件的兼容性处理
 */
export function createTeleportFallback(to: string, content: HTMLElement): void {
  const target = document.querySelector(to)
  if (target) {
    target.appendChild(content)
  } else {
    console.warn(`Teleport target "${to}" not found, appending to body`)
    document.body.appendChild(content)
  }
}

/**
 * Suspense组件的兼容性处理
 */
export function createSuspenseFallback<T>(
  asyncComponent: () => Promise<T>,
  fallback: HTMLElement,
  onError?: (error: Error) => void
): Promise<T> {
  // 显示加载状态
  if (fallback.parentNode) {
    fallback.style.display = 'block'
  }

  return asyncComponent()
    .then(result => {
      // 隐藏加载状态
      if (fallback.parentNode) {
        fallback.style.display = 'none'
      }
      return result
    })
    .catch(error => {
      // 隐藏加载状态并处理错误
      if (fallback.parentNode) {
        fallback.style.display = 'none'
      }
      if (onError) {
        onError(error)
      } else {
        console.error('Async component failed to load:', error)
      }
      throw error
    })
}

/**
 * Fragment的兼容性处理
 */
export function createFragmentWrapper(children: HTMLElement[]): DocumentFragment {
  const fragment = document.createDocumentFragment()
  children.forEach(child => fragment.appendChild(child))
  return fragment
}

/**
 * 自定义指令的兼容性处理
 */
export interface DirectiveBinding {
  value: any
  oldValue: any
  arg?: string
  modifiers: Record<string, boolean>
}

export function createDirectiveFallback(
  el: HTMLElement,
  binding: DirectiveBinding,
  vnode?: any
): void {
  // 简单的指令实现fallback
  if (binding.arg === 'show') {
    el.style.display = binding.value ? '' : 'none'
  } else if (binding.arg === 'if') {
    if (binding.value) {
      if (el.style.display === 'none') {
        el.style.display = ''
      }
    } else {
      el.style.display = 'none'
    }
  }
}

/**
 * 组合式API的兼容性检查
 */
export function checkCompositionAPISupport(): boolean {
  try {
    // 尝试使用基本的组合式API
    const testRef = ref(0)
    const testReactive = reactive({ count: 0 })
    const testComputed = computed(() => testRef.value + testReactive.count)
    
    return typeof testRef.value === 'number' && 
           typeof testReactive.count === 'number' && 
           typeof testComputed.value === 'number'
  } catch {
    return false
  }
}

/**
 * 多根节点组件的兼容性处理
 */
export function createMultiRootWrapper(elements: HTMLElement[]): HTMLElement {
  const wrapper = document.createElement('div')
  wrapper.style.display = 'contents' // 使wrapper不影响布局
  elements.forEach(el => wrapper.appendChild(el))
  return wrapper
}

/**
 * 全局属性的兼容性处理
 */
export function setupGlobalProperties(app: any, properties: Record<string, any>): void {
  try {
    // Vue 3方式
    if (app.config && app.config.globalProperties) {
      Object.assign(app.config.globalProperties, properties)
    } else {
      // 降级处理
      Object.assign(window, properties)
    }
  } catch (error) {
    console.warn('Failed to setup global properties:', error)
    Object.assign(window, properties)
  }
}

/**
 * 插件系统的兼容性处理
 */
export function safeUsePlugin(app: any, plugin: any, options?: any): void {
  try {
    if (app.use && typeof app.use === 'function') {
      app.use(plugin, options)
    } else {
      // 手动安装插件
      if (plugin.install && typeof plugin.install === 'function') {
        plugin.install(app, options)
      }
    }
  } catch (error) {
    console.warn('Failed to install plugin:', error)
  }
}

/**
 * 事件系统的兼容性处理
 */
export function createEventBus(): {
  on: (event: string, callback: Function) => void
  off: (event: string, callback: Function) => void
  emit: (event: string, ...args: any[]) => void
} {
  const events: Record<string, Function[]> = {}

  return {
    on(event: string, callback: Function) {
      if (!events[event]) {
        events[event] = []
      }
      events[event].push(callback)
    },

    off(event: string, callback: Function) {
      if (events[event]) {
        const index = events[event].indexOf(callback)
        if (index > -1) {
          events[event].splice(index, 1)
        }
      }
    },

    emit(event: string, ...args: any[]) {
      if (events[event]) {
        events[event].forEach(callback => {
          try {
            callback(...args)
          } catch (error) {
            console.error('Event callback error:', error)
          }
        })
      }
    }
  }
}

/**
 * 初始化Vue3兼容性支持
 */
export function initVue3Compatibility(): void {
  const hasCompositionAPI = checkCompositionAPISupport()
  
  console.log('Vue3 compatibility initialized:', {
    compositionAPI: hasCompositionAPI
  })

  // 如果不支持组合式API，可以在这里添加额外的polyfill
  if (!hasCompositionAPI) {
    console.warn('Composition API not fully supported, some features may not work correctly')
  }
}
