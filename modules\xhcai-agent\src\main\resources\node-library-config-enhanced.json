{"categories": [{"name": "基础节点", "icon": "settings", "description": "工作流的基础控制节点", "nodes": [{"type": "start", "label": "开始节点", "icon": "play-circle", "description": "工作流的起始节点，每个工作流必须有一个开始节点", "category": "basic", "maxInstances": 1, "color": "blue", "gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "labelColor": "#ffffff", "iconColor": "#ffffff", "status": "stable", "version": "1.0.0", "tags": ["基础", "必需"], "handles": {"source": [{"id": "output", "position": "right"}], "target": []}, "defaultData": {"label": "开始", "config": {}}}, {"type": "end", "label": "结束节点", "icon": "stop-circle", "description": "工作流的结束节点，可以有多个结束节点", "category": "basic", "color": "blue", "gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "labelColor": "#ffffff", "iconColor": "#ffffff", "status": "stable", "version": "1.0.0", "tags": ["基础", "结束"], "handles": {"source": [], "target": [{"id": "input", "position": "left"}]}, "defaultData": {"label": "结束", "config": {}}}, {"type": "condition", "label": "条件判断", "icon": "git-branch", "description": "根据条件进行分支判断", "category": "basic", "color": "blue", "gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "labelColor": "#ffffff", "iconColor": "#ffffff", "status": "stable", "version": "1.0.0", "tags": ["基础", "逻辑"], "handles": {"source": [{"id": "true", "position": "right", "label": "是"}, {"id": "false", "position": "bottom", "label": "否"}], "target": [{"id": "input", "position": "left"}]}, "defaultData": {"label": "条件判断", "config": {"condition": "", "operator": "equals", "value": ""}}}]}, {"name": "数据库工具", "icon": "database", "description": "各种数据库操作工具", "nodes": [{"type": "mysql", "label": "MySQL", "icon": "database", "description": "MySQL数据库操作，支持查询、插入、更新、删除", "category": "database", "color": "green", "gradient": "linear-gradient(135deg, #11998e 0%, #38ef7d 100%)", "labelColor": "#ffffff", "iconColor": "#ffffff", "status": "stable", "version": "1.0.0", "tags": ["数据库", "MySQL"], "handles": {"source": [{"id": "output", "position": "right"}], "target": [{"id": "input", "position": "left"}]}, "defaultData": {"label": "MySQL", "config": {"host": "", "port": 3306, "database": "", "username": "", "password": "", "sql": "SELECT * FROM table_name", "operation": "select"}}}, {"type": "postgresql", "label": "PostgreSQL", "icon": "database", "description": "PostgreSQL数据库操作", "category": "database", "color": "green", "gradient": "linear-gradient(135deg, #11998e 0%, #38ef7d 100%)", "labelColor": "#ffffff", "iconColor": "#ffffff", "status": "stable", "version": "1.0.0", "tags": ["数据库", "PostgreSQL"], "handles": {"source": [{"id": "output", "position": "right"}], "target": [{"id": "input", "position": "left"}]}, "defaultData": {"label": "PostgreSQL", "config": {"host": "", "port": 5432, "database": "", "username": "", "password": "", "sql": "SELECT * FROM table_name", "operation": "select"}}}]}, {"name": "AI工具", "icon": "cpu", "description": "各种AI模型和智能处理工具", "nodes": [{"type": "llm-chat", "label": "LLM对话", "icon": "message-circle", "description": "使用大语言模型进行对话和文本生成", "category": "ai", "color": "red", "gradient": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "labelColor": "#ffffff", "iconColor": "#ffffff", "status": "stable", "version": "1.0.0", "tags": ["AI", "对话"], "handles": {"source": [{"id": "output", "position": "right"}], "target": [{"id": "input", "position": "left"}]}, "defaultData": {"label": "LLM对话", "config": {"model": "gpt-3.5-turbo", "prompt": "", "temperature": 0.7, "maxTokens": 1000, "systemMessage": ""}}}, {"type": "text-embedding", "label": "文本向量化", "icon": "layers", "description": "将文本转换为向量表示", "category": "ai", "color": "red", "gradient": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "labelColor": "#ffffff", "iconColor": "#ffffff", "status": "stable", "version": "1.0.0", "tags": ["AI", "向量化"], "handles": {"source": [{"id": "output", "position": "right"}], "target": [{"id": "input", "position": "left"}]}, "defaultData": {"label": "文本向量化", "config": {"model": "text-embedding-ada-002", "text": "", "dimensions": 1536}}}]}]}