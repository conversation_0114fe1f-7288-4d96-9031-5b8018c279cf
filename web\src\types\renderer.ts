/**
 * 渲染器类型定义
 * 统一管理所有渲染器相关的类型定义
 */

// 渲染器类型枚举
export type RendererType = 
  | 'text'      // 纯文本渲染器
  | 'markdown'  // Markdown渲染器
  | 'image'     // 图片渲染器
  | 'audio'     // 音频渲染器
  | 'video'     // 视频渲染器
  | 'chart'     // 图表渲染器
  | 'flowchart' // 流程图渲染器
  | 'file'      // 文件渲染器
  | 'html'      // HTML渲染器
  | 'mixed'     // 混合内容渲染器

// 渲染器配置接口
export interface RendererConfig {
  type: RendererType
  name: string
  description: string
  icon?: any
}

// 渲染器样例数据接口
export interface RendererSample {
  type: RendererType
  content: string
  description: string
}

// 内容类型定义（与渲染器类型对应）
export type ContentType = RendererType

// 渲染器选择事件类型
export interface RendererSelectEvent {
  renderer: RendererType
  timestamp: number
}

// 渲染器状态接口
export interface RendererState {
  selectedRenderer: RendererType
  availableRenderers: RendererType[]
  isLoading: boolean
}
