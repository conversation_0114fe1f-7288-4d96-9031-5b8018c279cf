<template>
  <div class="markdown-renderer">
    <!-- 思考内容块 -->
    <ThinkingBlock
      v-if="thinkingContent"
      :content="thinkingContent"
      :streaming="streaming && isThinkingStreaming"
      :completed="!streaming || !isThinkingStreaming"
    />

    <!-- 主要内容 -->
    <div
      v-if="mainContent && streaming"
      class="streaming-content"
      v-html="renderedMainContent"
      @click="handleClick"
    ></div>
    <div
      v-else-if="mainContent"
      class="static-content"
      v-html="renderedMainContent"
      @click="handleClick"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import ThinkingBlock from '@/views/aiExplore/ThinkingBlock.vue'

// Props定义
const props = defineProps<{
  content: string
  streaming?: boolean
}>()

// Emits定义
const emit = defineEmits<{
  linkClick: [url: string]
}>()

// 配置marked
const configureMarked = () => {
  marked.setOptions({
    breaks: true,
    gfm: true
  })

  // 自定义渲染器
  const renderer = new marked.Renderer()
  
  // 链接渲染
  renderer.link = (href, title, text) => {
    const titleAttr = title ? ` title="${title}"` : ''
    return `<a href="${href}"${titleAttr} class="markdown-link" data-url="${href}">${text}</a>`
  }

  // 代码块渲染
  renderer.code = (code, language) => {
    const lang = language || 'text'
    // 确保代码内容正确转义HTML特殊字符
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')

    return `
      <div class="code-block">
        <div class="code-header">
          <span class="language">${lang}</span>
          <button class="copy-btn" data-code="${encodeURIComponent(code)}">复制</button>
        </div>
        <pre><code class="language-${lang}">${escapedCode}</code></pre>
      </div>
    `
  }

  // 表格渲染
  renderer.table = (header, body) => {
    return `
      <div class="table-wrapper">
        <table class="markdown-table">
          <thead>${header}</thead>
          <tbody>${body}</tbody>
        </table>
      </div>
    `
  }

  marked.use({ renderer })
}

// 响应式数据
const renderedMainContent = ref('')
const thinkingContent = ref('')
const mainContent = ref('')
const isThinkingStreaming = ref(false)

// 解析思考内容和主要内容
const parseThinkingContent = (content: string) => {
  const thinkRegex = /<think>([\s\S]*?)<\/think>/g
  let match
  let thinking = ''
  let main = content

  // 提取思考内容
  while ((match = thinkRegex.exec(content)) !== null) {
    thinking += match[1]
  }

  // 移除思考标签，保留主要内容
  main = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim()

  return { thinking, main }
}

// 检查是否在思考标签内流式输出
const checkThinkingStreaming = (content: string) => {
  const openTags = (content.match(/<think>/g) || []).length
  const closeTags = (content.match(/<\/think>/g) || []).length
  return openTags > closeTags
}

// 提取流式思考内容（处理未完成的标签）
const extractStreamingThinkingContent = (content: string) => {
  // 检查是否有未完成的 <think> 标签
  const openTagIndex = content.lastIndexOf('<think>')
  const closeTagIndex = content.lastIndexOf('</think>')

  if (openTagIndex > closeTagIndex) {
    // 有未完成的 <think> 标签，提取其中的内容
    const thinkingStart = openTagIndex + 7 // '<think>'.length
    return content.substring(thinkingStart)
  }

  return ''
}

// 清理内容中的思考标签（包括未完成的标签）
const cleanContentFromThinkingTags = (content: string) => {
  // 移除完整的思考标签
  let cleaned = content.replace(/<think>[\s\S]*?<\/think>/g, '')

  // 移除未完成的 <think> 标签及其后面的内容
  const openTagIndex = cleaned.lastIndexOf('<think>')
  const closeTagIndex = cleaned.lastIndexOf('</think>')

  if (openTagIndex > closeTagIndex) {
    // 有未完成的 <think> 标签，移除它及其后面的内容
    cleaned = cleaned.substring(0, openTagIndex)
  }

  return cleaned.trim()
}

// 渲染内容的异步函数
const renderContent = async () => {
  if (!props.content) {
    renderedMainContent.value = ''
    thinkingContent.value = ''
    mainContent.value = ''
    return
  }

  try {
    // 检查是否在思考中流式输出
    isThinkingStreaming.value = props.streaming && checkThinkingStreaming(props.content)

    if (isThinkingStreaming.value) {
      // 正在思考中，提取流式思考内容
      const streamingThinking = extractStreamingThinkingContent(props.content)
      thinkingContent.value = streamingThinking

      // 清理主要内容中的思考标签
      const cleanedMain = cleanContentFromThinkingTags(props.content)
      mainContent.value = cleanedMain

      // 渲染清理后的主要内容
      if (cleanedMain) {
        let contentToRender = handleIncompleteMarkdown(cleanedMain)
        const html = await marked.parse(contentToRender)
        let sanitizedHtml = DOMPurify.sanitize(html, {
          ALLOWED_TAGS: [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p', 'br', 'strong', 'em', 'u', 's', 'del',
            'ul', 'ol', 'li',
            'blockquote', 'pre', 'code',
            'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'a', 'img',
            'div', 'span', 'button'
          ],
          ALLOWED_ATTR: [
            'href', 'title', 'alt', 'src',
            'class', 'data-url', 'data-code',
            'colspan', 'rowspan'
          ]
        })
        renderedMainContent.value = sanitizedHtml
      } else {
        renderedMainContent.value = ''
      }
    } else {
      // 思考完成或没有思考内容，正常解析
      const { thinking, main } = parseThinkingContent(props.content)

      // 更新思考内容
      thinkingContent.value = thinking
      mainContent.value = main

      // 渲染主要内容
      if (main) {
        let contentToRender = main

        if (props.streaming) {
          // 对于流式输出，确保不完整的Markdown不会破坏渲染
          contentToRender = handleIncompleteMarkdown(main)
          // 在流式输出时添加光标
          contentToRender += '<span class="streaming-cursor">|</span>'
        }

        const html = await marked.parse(contentToRender)
        let sanitizedHtml = DOMPurify.sanitize(html, {
          ALLOWED_TAGS: [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p', 'br', 'strong', 'em', 'u', 's', 'del',
            'ul', 'ol', 'li',
            'blockquote', 'pre', 'code',
            'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'a', 'img',
            'div', 'span', 'button'
          ],
          ALLOWED_ATTR: [
            'href', 'title', 'alt', 'src',
            'class', 'data-url', 'data-code',
            'colspan', 'rowspan'
          ]
        })

        renderedMainContent.value = sanitizedHtml
      } else {
        renderedMainContent.value = ''
      }
    }
  } catch (error) {
    console.error('Markdown渲染错误:', error)
    // 如果渲染失败，至少显示原始内容和光标
    let fallbackContent = mainContent.value
    if (props.streaming && !isThinkingStreaming.value) {
      fallbackContent += '<span class="streaming-cursor">|</span>'
    }
    renderedMainContent.value = fallbackContent
  }
}

// 监听内容变化
watch(() => props.content, renderContent, { immediate: true })
watch(() => props.streaming, renderContent)

// 处理不完整的Markdown内容
const handleIncompleteMarkdown = (content: string): string => {
  // 检查是否有未闭合的代码块
  const codeBlockMatches = content.match(/```/g)
  if (codeBlockMatches && codeBlockMatches.length % 2 === 1) {
    // 如果有未闭合的代码块，暂时闭合它
    return content + '\n```'
  }

  // 检查是否有未完成的表格
  const lines = content.split('\n')
  const lastLine = lines[lines.length - 1]
  if (lastLine && lastLine.includes('|') && !lastLine.trim().endsWith('|')) {
    // 如果表格行未完成，暂时补全
    return content + ' |'
  }

  return content
}

// 处理点击事件
const handleClick = (event: Event) => {
  const target = event.target as HTMLElement
  
  // 处理链接点击
  if (target.classList.contains('markdown-link')) {
    event.preventDefault()
    const url = target.getAttribute('data-url')
    if (url) {
      emit('linkClick', url)
    }
  }
  
  // 处理代码复制
  if (target.classList.contains('copy-btn')) {
    const code = decodeURIComponent(target.getAttribute('data-code') || '')
    navigator.clipboard.writeText(code).then(() => {
      target.textContent = '已复制'
      setTimeout(() => {
        target.textContent = '复制'
      }, 2000)
    })
  }
}

onMounted(() => {
  configureMarked()
})
</script>

<style scoped>
.markdown-renderer {
  width: 100%;
  line-height: 1.6;
}

.markdown-renderer :deep(h1),
.markdown-renderer :deep(h2),
.markdown-renderer :deep(h3),
.markdown-renderer :deep(h4),
.markdown-renderer :deep(h5),
.markdown-renderer :deep(h6) {
  margin: 1em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-renderer :deep(h1) { font-size: 1.5em; }
.markdown-renderer :deep(h2) { font-size: 1.3em; }
.markdown-renderer :deep(h3) { font-size: 1.1em; }

.markdown-renderer :deep(p) {
  margin: 0.5em 0;
}

.markdown-renderer :deep(ul),
.markdown-renderer :deep(ol) {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-renderer :deep(li) {
  margin: 0.2em 0;
}

.markdown-renderer :deep(blockquote) {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #e5e7eb;
  background-color: #f9fafb;
  font-style: italic;
}

.markdown-renderer :deep(.code-block) {
  margin: 1em 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
}

.markdown-renderer :deep(.code-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em 1em;
  background-color: #e2e8f0;
  border-bottom: 1px solid #cbd5e1;
}

.markdown-renderer :deep(.language) {
  font-size: 0.8em;
  color: #64748b;
  font-weight: 500;
}

.markdown-renderer :deep(.copy-btn) {
  font-size: 0.8em;
  padding: 0.2em 0.5em;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.markdown-renderer :deep(.copy-btn:hover) {
  background-color: #2563eb;
}

.markdown-renderer :deep(pre) {
  margin: 0;
  padding: 1em;
  overflow-x: auto;
  background-color: #f8fafc;
}

.markdown-renderer :deep(code) {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-renderer :deep(p code) {
  background-color: #f1f5f9;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.85em;
}

.markdown-renderer :deep(.table-wrapper) {
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-renderer :deep(.markdown-table) {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.markdown-renderer :deep(.markdown-table th),
.markdown-renderer :deep(.markdown-table td) {
  padding: 0.5em 1em;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.markdown-renderer :deep(.markdown-table th) {
  background-color: #f9fafb;
  font-weight: 600;
}

.markdown-renderer :deep(.markdown-table tr:last-child td) {
  border-bottom: none;
}

.markdown-renderer :deep(.markdown-link) {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s;
}

.markdown-renderer :deep(.markdown-link:hover) {
  color: #2563eb;
}

.markdown-renderer :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 0.5em 0;
}

/* 流式输出动画 */
.streaming-content {
  position: relative;
}

.streaming-content::after {
  content: '|';
  animation: blink 1s infinite;
  color: #3b82f6;
}

.markdown-renderer :deep(.streaming-cursor) {
  color: #3b82f6;
  font-weight: bold;
  animation: blink 1s infinite;
  margin-left: 1px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
