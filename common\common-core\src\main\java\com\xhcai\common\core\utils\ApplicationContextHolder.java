package com.xhcai.common.core.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * Spring应用上下文持有者
 * 用于在非Spring管理的类中获取Spring上下文和Bean
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ApplicationContextHolder implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(ApplicationContextHolder.class);

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        ApplicationContextHolder.applicationContext = context;
        log.debug("ApplicationContext已设置");
    }

    /**
     * 获取ApplicationContext
     *
     * @return ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 获取Environment
     *
     * @return Environment
     */
    public static Environment getEnvironment() {
        if (applicationContext != null) {
            return applicationContext.getEnvironment();
        }
        return null;
    }

    /**
     * 根据名称获取Bean
     *
     * @param name Bean名称
     * @return Bean实例
     */
    public static Object getBean(String name) {
        if (applicationContext != null) {
            return applicationContext.getBean(name);
        }
        return null;
    }

    /**
     * 根据类型获取Bean
     *
     * @param clazz Bean类型
     * @param <T> 泛型类型
     * @return Bean实例
     */
    public static <T> T getBean(Class<T> clazz) {
        if (applicationContext != null) {
            return applicationContext.getBean(clazz);
        }
        return null;
    }

    /**
     * 根据名称和类型获取Bean
     *
     * @param name Bean名称
     * @param clazz Bean类型
     * @param <T> 泛型类型
     * @return Bean实例
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        if (applicationContext != null) {
            return applicationContext.getBean(name, clazz);
        }
        return null;
    }

    /**
     * 获取配置属性值
     *
     * @param key 配置键
     * @return 配置值
     */
    public static String getProperty(String key) {
        Environment env = getEnvironment();
        if (env != null) {
            return env.getProperty(key);
        }
        return null;
    }

    /**
     * 获取配置属性值，带默认值
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getProperty(String key, String defaultValue) {
        Environment env = getEnvironment();
        if (env != null) {
            return env.getProperty(key, defaultValue);
        }
        return defaultValue;
    }

    /**
     * 检查ApplicationContext是否已初始化
     *
     * @return true-已初始化，false-未初始化
     */
    public static boolean isInitialized() {
        return applicationContext != null;
    }
}
