package com.xhcai.modules.dify.dto.agent;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 智能体DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyAgentDTO {

    /**
     * 智能体ID
     */
    private String id;

    /**
     * 智能体名称
     */
    @NotBlank(message = "智能体名称不能为空")
    @Size(max = 100, message = "智能体名称长度不能超过100个字符")
    private String name;

    /**
     * 智能体描述
     */
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    private String description;

    /**
     * 智能体图标
     */
    private String icon;

    /**
     * 智能体状态：active-活跃, inactive-非活跃
     */
    private String status;

    /**
     * 智能体类型：chat-对话型, completion-补全型
     */
    private String type;

    /**
     * 模型配置
     */
    @JsonProperty("model_config")
    private ModelConfig modelConfig;

    /**
     * 提示词配置
     */
    @JsonProperty("prompt_config")
    private PromptConfig promptConfig;

    /**
     * 工具配置
     */
    @JsonProperty("tool_configs")
    private List<ToolConfig> toolConfigs;

    /**
     * 知识库配置
     */
    @JsonProperty("knowledge_configs")
    private List<KnowledgeConfig> knowledgeConfigs;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    @JsonProperty("created_by")
    private String createdBy;

    /**
     * 扩展属性
     */
    private Map<String, Object> metadata;

    // 内部类定义
    public static class ModelConfig {
        private String provider;
        private String model;
        private Double temperature;
        @JsonProperty("max_tokens")
        private Integer maxTokens;
        @JsonProperty("top_p")
        private Double topP;
        @JsonProperty("frequency_penalty")
        private Double frequencyPenalty;
        @JsonProperty("presence_penalty")
        private Double presencePenalty;

        // Getters and Setters
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public Double getTemperature() { return temperature; }
        public void setTemperature(Double temperature) { this.temperature = temperature; }
        public Integer getMaxTokens() { return maxTokens; }
        public void setMaxTokens(Integer maxTokens) { this.maxTokens = maxTokens; }
        public Double getTopP() { return topP; }
        public void setTopP(Double topP) { this.topP = topP; }
        public Double getFrequencyPenalty() { return frequencyPenalty; }
        public void setFrequencyPenalty(Double frequencyPenalty) { this.frequencyPenalty = frequencyPenalty; }
        public Double getPresencePenalty() { return presencePenalty; }
        public void setPresencePenalty(Double presencePenalty) { this.presencePenalty = presencePenalty; }
    }

    public static class PromptConfig {
        @JsonProperty("system_prompt")
        private String systemPrompt;
        @JsonProperty("user_prompt_template")
        private String userPromptTemplate;
        private List<PromptVariable> variables;

        // Getters and Setters
        public String getSystemPrompt() { return systemPrompt; }
        public void setSystemPrompt(String systemPrompt) { this.systemPrompt = systemPrompt; }
        public String getUserPromptTemplate() { return userPromptTemplate; }
        public void setUserPromptTemplate(String userPromptTemplate) { this.userPromptTemplate = userPromptTemplate; }
        public List<PromptVariable> getVariables() { return variables; }
        public void setVariables(List<PromptVariable> variables) { this.variables = variables; }
    }

    public static class PromptVariable {
        private String name;
        private String type;
        private String description;
        private Object defaultValue;
        private boolean required;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public Object getDefaultValue() { return defaultValue; }
        public void setDefaultValue(Object defaultValue) { this.defaultValue = defaultValue; }
        public boolean isRequired() { return required; }
        public void setRequired(boolean required) { this.required = required; }
    }

    public static class ToolConfig {
        @JsonProperty("tool_id")
        private String toolId;
        @JsonProperty("tool_name")
        private String toolName;
        private boolean enabled;
        private Map<String, Object> settings;

        // Getters and Setters
        public String getToolId() { return toolId; }
        public void setToolId(String toolId) { this.toolId = toolId; }
        public String getToolName() { return toolName; }
        public void setToolName(String toolName) { this.toolName = toolName; }
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public Map<String, Object> getSettings() { return settings; }
        public void setSettings(Map<String, Object> settings) { this.settings = settings; }
    }

    public static class KnowledgeConfig {
        @JsonProperty("knowledge_id")
        private String knowledgeId;
        @JsonProperty("knowledge_name")
        private String knowledgeName;
        private boolean enabled;
        @JsonProperty("retrieval_config")
        private Map<String, Object> retrievalConfig;

        // Getters and Setters
        public String getKnowledgeId() { return knowledgeId; }
        public void setKnowledgeId(String knowledgeId) { this.knowledgeId = knowledgeId; }
        public String getKnowledgeName() { return knowledgeName; }
        public void setKnowledgeName(String knowledgeName) { this.knowledgeName = knowledgeName; }
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public Map<String, Object> getRetrievalConfig() { return retrievalConfig; }
        public void setRetrievalConfig(Map<String, Object> retrievalConfig) { this.retrievalConfig = retrievalConfig; }
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getIcon() { return icon; }
    public void setIcon(String icon) { this.icon = icon; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public ModelConfig getModelConfig() { return modelConfig; }
    public void setModelConfig(ModelConfig modelConfig) { this.modelConfig = modelConfig; }
    public PromptConfig getPromptConfig() { return promptConfig; }
    public void setPromptConfig(PromptConfig promptConfig) { this.promptConfig = promptConfig; }
    public List<ToolConfig> getToolConfigs() { return toolConfigs; }
    public void setToolConfigs(List<ToolConfig> toolConfigs) { this.toolConfigs = toolConfigs; }
    public List<KnowledgeConfig> getKnowledgeConfigs() { return knowledgeConfigs; }
    public void setKnowledgeConfigs(List<KnowledgeConfig> knowledgeConfigs) { this.knowledgeConfigs = knowledgeConfigs; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
}
