package com.xhcai.modules.rag.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件存储配置VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "文件存储配置VO")
public class FileStorageConfigVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 存储配置名称
     */
    @Schema(description = "存储配置名称", example = "默认MinIO存储")
    private String name;

    /**
     * 存储类型
     */
    @Schema(description = "存储类型", example = "minio")
    private String storageType;

    /**
     * 存储类型名称
     */
    @Schema(description = "存储类型名称", example = "MinIO")
    private String storageTypeName;

    /**
     * 主机地址
     */
    @Schema(description = "主机地址", example = "*************")
    private String host;

    /**
     * 端口号
     */
    @Schema(description = "端口号", example = "9000")
    private Integer port;

    /**
     * 访问密钥ID
     */
    @Schema(description = "访问密钥ID")
    private String accessKey;

    /**
     * 存储桶名称/根目录
     */
    @Schema(description = "存储桶名称/根目录", example = "xhcai-files")
    private String bucketName;

    /**
     * 区域
     */
    @Schema(description = "区域", example = "us-east-1")
    private String region;

    /**
     * 是否启用SSL
     */
    @Schema(description = "是否启用SSL", example = "Y")
    private String sslEnabled;

    /**
     * 自定义域名
     */
    @Schema(description = "自定义域名", example = "files.example.com")
    private String customDomain;

    /**
     * 连接配置JSON
     */
    @Schema(description = "连接配置JSON")
    private String connectionConfig;

    /**
     * 是否为默认存储
     */
    @Schema(description = "是否为默认存储", example = "Y")
    private String isDefault;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0")
    private String status;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 图标
     */
    @Schema(description = "图标")
    private String icon;

    /**
     * 图标颜色
     */
    @Schema(description = "图标颜色")
    private String iconColor;
}
