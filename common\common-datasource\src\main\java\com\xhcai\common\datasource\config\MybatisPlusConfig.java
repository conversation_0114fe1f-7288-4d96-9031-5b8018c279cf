package com.xhcai.common.datasource.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.xhcai.common.datasource.plugin.MultiTenantLineHandler;

/**
 * MyBatis Plus 配置 支持动态数据源的 MyBatis Plus 配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableTransactionManagement
public class MybatisPlusConfig {

    @Autowired
    private MultiTenantLineHandler multiTenantLineHandler;

    /**
     * MyBatis Plus 拦截器配置 添加分页插件和多租户插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 多租户插件（必须放在分页插件之前）
        TenantLineInnerInterceptor tenantInterceptor = new TenantLineInnerInterceptor();
        tenantInterceptor.setTenantLineHandler(multiTenantLineHandler);
        interceptor.addInnerInterceptor(tenantInterceptor);

        // 分页插件，指定数据库类型为 PostgreSQL
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.POSTGRE_SQL);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInterceptor.setMaxLimit(1000L);
        // 设置溢出总页数后是否进行处理（默认不处理）
        paginationInterceptor.setOverflow(false);
        // 单页分页条数限制（默认无限制）
        paginationInterceptor.setMaxLimit(1000L);
        interceptor.addInnerInterceptor(paginationInterceptor);

        return interceptor;
    }

}
