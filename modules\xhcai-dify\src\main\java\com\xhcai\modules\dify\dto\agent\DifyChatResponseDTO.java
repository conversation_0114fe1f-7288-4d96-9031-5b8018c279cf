package com.xhcai.modules.dify.dto.agent;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 智能体对话响应DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyChatResponseDTO {

    /**
     * 消息ID
     */
    @JsonProperty("message_id")
    private String messageId;

    /**
     * 会话ID
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 响应内容
     */
    private String answer;

    /**
     * 响应状态：success-成功, error-错误, processing-处理中
     */
    private String status;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /**
     * 使用情况统计
     */
    private Usage usage;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 引用的知识库文档
     */
    @JsonProperty("retrieval_documents")
    private List<RetrievalDocument> retrievalDocuments;

    /**
     * 使用的工具调用
     */
    @JsonProperty("tool_calls")
    private List<ToolCall> toolCalls;

    /**
     * 使用情况统计
     */
    public static class Usage {
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
        @JsonProperty("prompt_price")
        private String promptPrice;
        @JsonProperty("completion_price")
        private String completionPrice;
        @JsonProperty("total_price")
        private String totalPrice;
        private String currency;

        // Getters and Setters
        public Integer getPromptTokens() { return promptTokens; }
        public void setPromptTokens(Integer promptTokens) { this.promptTokens = promptTokens; }
        public Integer getCompletionTokens() { return completionTokens; }
        public void setCompletionTokens(Integer completionTokens) { this.completionTokens = completionTokens; }
        public Integer getTotalTokens() { return totalTokens; }
        public void setTotalTokens(Integer totalTokens) { this.totalTokens = totalTokens; }
        public String getPromptPrice() { return promptPrice; }
        public void setPromptPrice(String promptPrice) { this.promptPrice = promptPrice; }
        public String getCompletionPrice() { return completionPrice; }
        public void setCompletionPrice(String completionPrice) { this.completionPrice = completionPrice; }
        public String getTotalPrice() { return totalPrice; }
        public void setTotalPrice(String totalPrice) { this.totalPrice = totalPrice; }
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
    }

    /**
     * 检索文档
     */
    public static class RetrievalDocument {
        @JsonProperty("document_id")
        private String documentId;
        @JsonProperty("document_name")
        private String documentName;
        private String content;
        private Double score;
        @JsonProperty("knowledge_id")
        private String knowledgeId;
        @JsonProperty("knowledge_name")
        private String knowledgeName;

        // Getters and Setters
        public String getDocumentId() { return documentId; }
        public void setDocumentId(String documentId) { this.documentId = documentId; }
        public String getDocumentName() { return documentName; }
        public void setDocumentName(String documentName) { this.documentName = documentName; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public Double getScore() { return score; }
        public void setScore(Double score) { this.score = score; }
        public String getKnowledgeId() { return knowledgeId; }
        public void setKnowledgeId(String knowledgeId) { this.knowledgeId = knowledgeId; }
        public String getKnowledgeName() { return knowledgeName; }
        public void setKnowledgeName(String knowledgeName) { this.knowledgeName = knowledgeName; }
    }

    /**
     * 工具调用
     */
    public static class ToolCall {
        @JsonProperty("tool_name")
        private String toolName;
        @JsonProperty("tool_input")
        private Map<String, Object> toolInput;
        @JsonProperty("tool_output")
        private String toolOutput;
        private String status;
        private String error;

        // Getters and Setters
        public String getToolName() { return toolName; }
        public void setToolName(String toolName) { this.toolName = toolName; }
        public Map<String, Object> getToolInput() { return toolInput; }
        public void setToolInput(Map<String, Object> toolInput) { this.toolInput = toolInput; }
        public String getToolOutput() { return toolOutput; }
        public void setToolOutput(String toolOutput) { this.toolOutput = toolOutput; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }

    // Getters and Setters
    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Usage getUsage() {
        return usage;
    }

    public void setUsage(Usage usage) {
        this.usage = usage;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public List<RetrievalDocument> getRetrievalDocuments() {
        return retrievalDocuments;
    }

    public void setRetrievalDocuments(List<RetrievalDocument> retrievalDocuments) {
        this.retrievalDocuments = retrievalDocuments;
    }

    public List<ToolCall> getToolCalls() {
        return toolCalls;
    }

    public void setToolCalls(List<ToolCall> toolCalls) {
        this.toolCalls = toolCalls;
    }
}
