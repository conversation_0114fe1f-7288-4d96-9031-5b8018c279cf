<template>
  <!-- 智能体运行弹出层 -->
  <div
    class="agent-runner-window"
    v-show="visible && !isMinimized"
    :style="runnerWindowStyle"
    ref="runnerWindow"
  >
    <!-- 顶部信息栏（可拖动） -->
    <div
      class="runner-header"
      @mousedown="startDrag"
      @dblclick="toggleMaximize"
    >
      <div class="agent-info-bar">
        <div class="agent-avatar-runner">
          <i :class="agentInfo?.icon" v-if="agentInfo"></i>
        </div>
        <div class="agent-details-runner">
          <h3>{{ agentInfo?.name }}</h3>
          <div class="agent-meta-runner">
            <span class="agent-type-badge-runner">{{ agentInfo?.type }}</span>
            <span class="agent-unit-runner">{{ agentInfo?.unit }}</span>
            <span class="agent-creator-runner">设计者：{{ agentInfo?.creator }}</span>
          </div>
        </div>
      </div>
      <div class="runner-controls">
        <button class="runner-control-btn minimize-btn" title="最小化" @click="minimizeRunner">
          <i class="fas fa-minus"></i>
        </button>
        <button class="runner-control-btn maximize-btn" title="最大化/还原" @click="toggleMaximize">
          <i :class="isMaximized ? 'fas fa-window-restore' : 'fas fa-window-maximize'"></i>
        </button>
        <button class="runner-control-btn close-btn" title="关闭" @click="closeRunner">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- AIExplore 组件容器 -->
    <div class="runner-content-container">
      <AIExplore
        v-if="agentInfo"
        :key="agentInfo.id"
        :agent-info="agentInfo"
        :is-in-modal="true"
        class="runner-ai-explore"
      />
    </div>

    <!-- 调整大小的拖拽点 -->
    <div class="resize-handles">
      <div class="resize-handle resize-n" @mousedown="startResize('n')"></div>
      <div class="resize-handle resize-s" @mousedown="startResize('s')"></div>
      <div class="resize-handle resize-w" @mousedown="startResize('w')"></div>
      <div class="resize-handle resize-e" @mousedown="startResize('e')"></div>
      <div class="resize-handle resize-nw" @mousedown="startResize('nw')"></div>
      <div class="resize-handle resize-ne" @mousedown="startResize('ne')"></div>
      <div class="resize-handle resize-sw" @mousedown="startResize('sw')"></div>
      <div class="resize-handle resize-se" @mousedown="startResize('se')"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import AIExplore from '@/views/aiExplore/AIExplore.vue'

// 定义智能体类型
interface Agent {
  id: string
  name: string
  description: string
  icon: string
  unit: string
  creator: string
  createTime: string
  type: string
  tags: string[]
}

// Props
interface Props {
  visible: boolean
  agentInfo: Agent | null
  isMinimized?: boolean
  isMaximized?: boolean
  position?: { x: number; y: number }
  size?: { width: number; height: number }
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  agentInfo: null,
  isMinimized: false,
  isMaximized: true,
  position: () => ({ x: 100, y: 80 }),
  size: () => ({ width: 1200, height: 800 })
})

// Emits
const emit = defineEmits<{
  minimize: []
  maximize: [isMaximized: boolean]
  close: []
  'update:position': [position: { x: number; y: number }]
  'update:size': [size: { width: number; height: number }]
  'update:isMaximized': [isMaximized: boolean]
}>()

// 响应式数据
const runnerWindow = ref<HTMLElement | null>(null)
const isDragging = ref(false)
const isResizing = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const resizeDirection = ref('')
const originalWindowState = ref({ x: 0, y: 0, width: 0, height: 0 })

// 计算属性
const runnerWindowStyle = computed(() => {
  if (props.isMaximized) {
    return {
      position: 'fixed' as const,
      top: '60px',
      left: '0px',
      width: '100vw',
      height: 'calc(100vh - 60px)',
      zIndex: 10000
    }
  }

  return {
    position: 'fixed' as const,
    top: props.position.y + 'px',
    left: props.position.x + 'px',
    width: props.size.width + 'px',
    height: props.size.height + 'px',
    zIndex: 10000
  }
})

// 方法
const minimizeRunner = () => {
  emit('minimize')
}

const closeRunner = () => {
  emit('close')
}

const toggleMaximize = () => {
  if (props.isMaximized) {
    // 还原窗口
    emit('update:isMaximized', false)
    emit('update:position', { ...originalWindowState.value })
    emit('update:size', {
      width: originalWindowState.value.width,
      height: originalWindowState.value.height
    })
  } else {
    // 最大化窗口
    originalWindowState.value = {
      x: props.position.x,
      y: props.position.y,
      width: props.size.width,
      height: props.size.height
    }
    emit('update:isMaximized', true)
  }
}

// 拖动相关方法
const startDrag = (event: MouseEvent) => {
  if (props.isMaximized) return

  isDragging.value = true
  dragStart.value = {
    x: event.clientX - props.position.x,
    y: event.clientY - props.position.y
  }

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
  event.preventDefault()
}

const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  const newPosition = {
    x: Math.max(0, Math.min(window.innerWidth - 200, event.clientX - dragStart.value.x)),
    y: Math.max(60, Math.min(window.innerHeight - 100, event.clientY - dragStart.value.y))
  }
  emit('update:position', newPosition)
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 调整大小相关方法
const startResize = (direction: string) => {
  if (props.isMaximized) return

  isResizing.value = true
  resizeDirection.value = direction

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
}

const handleResize = (event: MouseEvent) => {
  if (!isResizing.value) return

  const minWidth = 400
  const minHeight = 300
  const direction = resizeDirection.value

  let newWidth = props.size.width
  let newHeight = props.size.height
  let newX = props.position.x
  let newY = props.position.y

  // 计算新的尺寸和位置
  if (direction.includes('e')) {
    newWidth = Math.max(minWidth, event.clientX - props.position.x)
  }
  if (direction.includes('w')) {
    const newLeft = Math.min(event.clientX, props.position.x + props.size.width - minWidth)
    newWidth = props.position.x + props.size.width - newLeft
    newX = newLeft
  }
  if (direction.includes('s')) {
    newHeight = Math.max(minHeight, event.clientY - props.position.y)
  }
  if (direction.includes('n')) {
    let newTop = Math.min(event.clientY, props.position.y + props.size.height - minHeight)
    newTop = Math.max(60, newTop) // 不能超过顶部菜单
    newHeight = props.position.y + props.size.height - newTop
    newY = newTop
  }

  // 确保窗口不超出屏幕边界
  newX = Math.max(0, Math.min(newX, window.innerWidth - newWidth))
  newY = Math.max(60, Math.min(newY, window.innerHeight - newHeight))

  // 应用新的尺寸和位置
  emit('update:size', { width: newWidth, height: newHeight })
  emit('update:position', { x: newX, y: newY })
}

const stopResize = () => {
  isResizing.value = false
  resizeDirection.value = ''
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}
</script>

<style scoped>
/* 智能体运行窗口样式 */
.agent-runner-window {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  min-width: 400px;
  min-height: 300px;
  user-select: none;
}

.runner-header {
  height: 50px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom: 1px solid #e2e8f0;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  flex-shrink: 0;
  cursor: move;
  position: relative;
}

.agent-info-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.agent-avatar-runner {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1a365d;
  font-size: 14px;
}

.agent-details-runner h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a365d;
  line-height: 1.2;
}

.agent-meta-runner {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.agent-type-badge-runner {
  background: linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%);
  color: #1a365d;
  padding: 1px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
}

.agent-unit-runner,
.agent-creator-runner {
  font-size: 10px;
  color: #64748b;
}

.runner-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.runner-control-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}


.minimize-btn,
.maximize-btn,
.close-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.minimize-btn {
  background: #ffc107;
  color: white;
}

.maximize-btn {
  background: #28a745;
  color: white;
}

.close-btn {
  background: #dc3545;
  color: white;
}

.minimize-btn:hover,
.maximize-btn:hover,
.close-btn:hover {
  transform: scale(1.1);
}

.runner-content-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f8fafc;
}

.runner-ai-explore {
  width: 100%;
  height: 100%;
  border: none;
  background: #ffffff;
}

/* 调整大小拖拽点 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  pointer-events: all;
  z-index: 10;
}

.resize-n {
  top: -3px;
  left: 10px;
  right: 10px;
  height: 6px;
  cursor: n-resize;
}

.resize-s {
  bottom: -3px;
  left: 10px;
  right: 10px;
  height: 6px;
  cursor: s-resize;
}

.resize-w {
  left: -3px;
  top: 10px;
  bottom: 10px;
  width: 6px;
  cursor: w-resize;
}

.resize-e {
  right: -3px;
  top: 10px;
  bottom: 10px;
  width: 6px;
  cursor: e-resize;
}

.resize-nw {
  top: -3px;
  left: -3px;
  width: 10px;
  height: 10px;
  cursor: nw-resize;
}

.resize-ne {
  top: -3px;
  right: -3px;
  width: 10px;
  height: 10px;
  cursor: ne-resize;
}

.resize-sw {
  bottom: -3px;
  left: -3px;
  width: 10px;
  height: 10px;
  cursor: sw-resize;
}

.resize-se {
  bottom: -3px;
  right: -3px;
  width: 10px;
  height: 10px;
  cursor: se-resize;
}

/* 运行窗口响应式设计 */
@media (max-width: 768px) {
  .agent-runner-window {
    border-radius: 8px;
    min-width: 300px;
    min-height: 250px;
  }

  .runner-header {
    height: 44px;
    padding: 0 12px;
    border-radius: 8px 8px 0 0;
  }

  .agent-avatar-runner {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .agent-details-runner h3 {
    font-size: 13px;
  }

  .agent-meta-runner {
    gap: 6px;
  }

  .agent-type-badge-runner,
  .agent-unit-runner,
  .agent-creator-runner {
    font-size: 9px;
  }

  .runner-control-btn {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
}
</style>
