<template>
  <BaseFileViewer :file="file" @download="handleDownload" @refresh="handleRefresh">
    <template #content>
      <div class="image-viewer">
        <div class="image-toolbar">
          <div class="toolbar-left">
            <button class="tool-btn" @click="zoomOut" :disabled="scale <= 0.1">
              <i class="fas fa-search-minus"></i>
            </button>
            <span class="zoom-info">{{ Math.round(scale * 100) }}%</span>
            <button class="tool-btn" @click="zoomIn" :disabled="scale >= 5">
              <i class="fas fa-search-plus"></i>
            </button>
            <button class="tool-btn" @click="resetZoom">
              <i class="fas fa-expand-arrows-alt"></i>
            </button>
            <button class="tool-btn" @click="fitToWindow">
              <i class="fas fa-compress-arrows-alt"></i>
              适应窗口
            </button>
          </div>
          <div class="toolbar-center">
            <div class="image-info" v-if="imageInfo">
              {{ imageInfo.width }} × {{ imageInfo.height }} px
              <span class="file-size">{{ formatFileSize(file?.size) }}</span>
            </div>
          </div>
          <div class="toolbar-right">
            <button class="tool-btn" @click="rotateLeft">
              <i class="fas fa-undo"></i>
            </button>
            <button class="tool-btn" @click="rotateRight">
              <i class="fas fa-redo"></i>
            </button>
            <button class="tool-btn" @click="toggleFullscreen">
              <i class="fas fa-expand"></i>
            </button>
            <button class="tool-btn" @click="handleDownload" title="下载文件">
              <i class="fas fa-download"></i>
            </button>
            <button class="tool-btn" @click="handleRefresh" title="刷新内容">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>
        
        <div class="image-content" ref="imageContainer" @wheel="handleWheel">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载图片...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="loadImage">重新加载</button>
          </div>
          
          <div v-else class="image-display" @mousedown="startDrag" @mousemove="drag" @mouseup="endDrag" @mouseleave="endDrag">
            <img 
              ref="imageElement"
              :src="imageSrc"
              :style="imageStyle"
              @load="onImageLoad"
              @error="onImageError"
              draggable="false"
              alt="预览图片"
            />
          </div>
        </div>
        
        <!-- 图片信息面板 -->
        <div class="image-info-panel" v-if="showInfo">
          <div class="info-header">
            <h4>图片信息</h4>
            <button class="close-info" @click="toggleInfo">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="info-content">
            <div class="info-item">
              <span class="info-label">文件名:</span>
              <span class="info-value">{{ file?.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">文件大小:</span>
              <span class="info-value">{{ formatFileSize(file?.size) }}</span>
            </div>
            <div class="info-item" v-if="imageInfo">
              <span class="info-label">尺寸:</span>
              <span class="info-value">{{ imageInfo.width }} × {{ imageInfo.height }} px</span>
            </div>
            <div class="info-item">
              <span class="info-label">格式:</span>
              <span class="info-value">{{ file?.type?.toUpperCase() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">缩放:</span>
              <span class="info-value">{{ Math.round(scale * 100) }}%</span>
            </div>
            <div class="info-item">
              <span class="info-label">旋转:</span>
              <span class="info-value">{{ rotation }}°</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseFileViewer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import BaseFileViewer from './BaseFileViewer.vue'
import { formatFileSize } from '@/utils/fileUtils'

// Props
interface Props {
  file: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  download: [file: any]
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const scale = ref(1)
const rotation = ref(0)
const position = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const showInfo = ref(false)
const imageInfo = ref<{ width: number; height: number } | null>(null)
const imageContainer = ref<HTMLElement | null>(null)
const imageElement = ref<HTMLImageElement | null>(null)

// 模拟图片数据
const imageSrc = ref('')

// 计算属性
const imageStyle = computed(() => {
  return {
    transform: `scale(${scale.value}) rotate(${rotation.value}deg) translate(${position.value.x}px, ${position.value.y}px)`,
    transformOrigin: 'center center',
    transition: isDragging.value ? 'none' : 'transform 0.2s ease',
    cursor: isDragging.value ? 'grabbing' : 'grab',
    maxWidth: 'none',
    maxHeight: 'none'
  }
})

// 方法
const handleDownload = (file: any) => {
  emit('download', file)
}

const handleRefresh = () => {
  loadImage()
}

const loadImage = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 模拟图片加载
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 生成模拟图片（使用 placeholder 服务）
    const width = 800
    const height = 600
    imageSrc.value = `https://picsum.photos/${width}/${height}?random=${Date.now()}`
    
    // 模拟图片信息
    imageInfo.value = { width, height }
    
  } catch (err) {
    error.value = '加载图片失败'
  } finally {
    loading.value = false
  }
}

const onImageLoad = () => {
  if (imageElement.value) {
    imageInfo.value = {
      width: imageElement.value.naturalWidth,
      height: imageElement.value.naturalHeight
    }
    fitToWindow()
  }
}

const onImageError = () => {
  error.value = '图片加载失败'
  loading.value = false
}

const zoomIn = () => {
  if (scale.value < 5) {
    scale.value = Math.min(5, scale.value * 1.2)
  }
}

const zoomOut = () => {
  if (scale.value > 0.1) {
    scale.value = Math.max(0.1, scale.value / 1.2)
  }
}

const resetZoom = () => {
  scale.value = 1
  position.value = { x: 0, y: 0 }
}

const fitToWindow = () => {
  if (!imageContainer.value || !imageElement.value || !imageInfo.value) return
  
  const containerRect = imageContainer.value.getBoundingClientRect()
  const containerWidth = containerRect.width - 40 // 留出边距
  const containerHeight = containerRect.height - 40
  
  const scaleX = containerWidth / imageInfo.value.width
  const scaleY = containerHeight / imageInfo.value.height
  
  scale.value = Math.min(scaleX, scaleY, 1) // 不超过原始大小
  position.value = { x: 0, y: 0 }
}

const rotateLeft = () => {
  rotation.value = (rotation.value - 90) % 360
}

const rotateRight = () => {
  rotation.value = (rotation.value + 90) % 360
}

const toggleInfo = () => {
  showInfo.value = !showInfo.value
}

const toggleFullscreen = () => {
  if (imageContainer.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      imageContainer.value.requestFullscreen()
    }
  }
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  
  if (event.deltaY < 0) {
    zoomIn()
  } else {
    zoomOut()
  }
}

const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  dragStart.value = {
    x: event.clientX - position.value.x,
    y: event.clientY - position.value.y
  }
}

const drag = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  position.value = {
    x: event.clientX - dragStart.value.x,
    y: event.clientY - dragStart.value.y
  }
}

const endDrag = () => {
  isDragging.value = false
}



// 生命周期
onMounted(() => {
  loadImage()
})
</script>

<style scoped>
.image-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.image-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 16px;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: white;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  font-size: 12px;
}

.tool-btn:hover:not(:disabled) {
  background: #f1f5f9;
  color: #334155;
}

.tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-info {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  min-width: 50px;
  text-align: center;
}

.image-info {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-size {
  color: #94a3b8;
}

.image-content {
  flex: 1;
  overflow: hidden;
  background: #f1f5f9;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.image-display {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-display img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  user-select: none;
}

.image-info-panel {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 250px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e8f0;
  z-index: 10;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 8px 8px 0 0;
}

.info-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.close-info {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.close-info:hover {
  background: #f1f5f9;
  color: #334155;
}

.info-content {
  padding: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #64748b;
  font-weight: 500;
}

.info-value {
  color: #1e293b;
  font-weight: 500;
  text-align: right;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
