package com.xhcai.modules.rag.enums;

public enum FileTypeEnum {

    // 文档类
    DOC("doc", "DOC", 1, "documents","📄","#dbeafe"),
    DOCX("docx", "DOCX", 2,"documents","📃","#dbeafe"),
    XLS("xls", "XLS", 3,"documents","📊","#dbeafe"),
    XLSX("xlsx", "XLSX", 4,"documents","📈","#dbeafe"),
    PDF("pdf", "PDF", 5, "documents","📕","#dbeafe"),
    TXT("txt", "TXT", 6, "documents","📝","#dbeafe"),
    MD("md", "Markdown", 7, "documents","✍️","#dbeafe"),
    RTF("rtf", "RTF", 8, "documents","📑","#dbeafe"),
    CSV("csv", "CSV", 10, "documents","📈","#dbeafe"),
    PPTX("pptx", "PPTX", 11, "documents","📊","#dbeafe"),
    PPT("ppt", "PPT", 12, "documents","📊","#dbeafe"),
    LOG("log", "LOG", 13, "documents","📜","#dbeafe"),
    TEX("tex", "TEX", 14, "documents","📚","#dbeafe"),

    // 图片类
    JPG("jpg", "JPG", 1, "images","🖼","#dbeafe"),
    JPEG("jpeg", "JPEG", 2,"images","🌅","#dbeafe"),
    PNG("png", "PNG", 3,"images","🖌","#dbeafe"),
    GIF("gif", "GIF", 4,"images","🎞","#dbeafe"),
    BMP("bmp", "BMP", 5, "images","🧩","#dbeafe"),
    TIFF("tiff", "TIFF", 6, "images","📰","#dbeafe"),
    WEBP("webp", "WEBP", 7, "images","🖼️","#dbeafe"),
    SVG("svg", "SVG", 8, "images","✒","#dbeafe"),

    // 音频类
    WAV("wav", "WAV", 1, "audios","🎵","#dbeafe"),
    MP3("mp3", "MP3", 2,"audios","🎶","#dbeafe"),
    WMA("wma", "WMA", 3,"audios","🎧","#dbeafe"),
    M4A("m4a", "M4A", 4,"audios","🎼","#dbeafe"),
    FLAC("flac", "FLAC", 5, "audios","🎻","#dbeafe"),
    OGG("ogg", "OGG", 6, "audios","🎤","#dbeafe"),
    MID("mid", "MID", 7, "audios","🎹","#dbeafe"),
    MIDI("midi", "MIDI", 8, "audios","🎹","#dbeafe"),
    PCM("pcm", "PCM", 9, "audios","🔊","#dbeafe"),

    // 视频类
    MP4("mp4", "MP4", 1, "videos","🎬","#dbeafe"),
    AVI("avi", "AVI", 2,"videos","🎞","#dbeafe"),
    MOV("mov", "MOV", 3,"videos","🎥","#dbeafe"),
    WMV("wmv", "WMV", 4,"videos","📽","#dbeafe"),
    FLV("flv", "FLV", 5, "videos","📡","#dbeafe"),
    MKV("mkv", "MKV", 6, "videos","🎥","#dbeafe"),
    MPEG("mpeg", "MPEG", 7, "videos","📼","#dbeafe"),
    MPG("mpg", "MPG", 8, "videos","📼","#dbeafe"),
    WEBM("webm", "WEBM", 9, "videos","🎦","#dbeafe"),
    M4V("m4v", "M4V", 10, "videos","🎦","#dbeafe"),
    TSV("tsv", "TSV", 11, "videos","🖥️","#dbeafe"),

    // 代码类
    JS("js", "JS", 1, "code", "⚡","#dbeafe"),
    TS("ts", "TS", 1, "code", "🔷","#dbeafe"),
    JSX("jsx", "JSX", 1, "code", "⚛️","#dbeafe"),
    TSX("tsx", "TSX", 1, "code", "⚛️","#dbeafe"),
    VUE("vue", "VUE", 1, "code", "🌿","#dbeafe"),
    HTML("html", "HTML", 1, "code", "🌐","#dbeafe"),
    CSS("css", "CSS", 1, "code", "🎨","#dbeafe"),
    SCSS("scss", "SCSS", 1, "code", "🎨","#dbeafe"),
    SASS("sass", "SASS", 1, "code", "🎨","#dbeafe"),
    LESS("less", "LESS", 1, "code", "🎨","#dbeafe"),
    JSON("json", "JSON", 1, "code", "🗂️","#dbeafe"),
    XML("xml", "XML", 1, "code", "⚙️","#dbeafe"),
    YAML("yaml", "YAML", 1, "code", "⚙️","#dbeafe"),
    YML("yml", "YML", 1, "code", "⚙️","#dbeafe"),
    PY("py", "PY", 1, "code", "🐍","#dbeafe"),
    JAVA("java", "JAVA", 1, "code", "☕","#dbeafe"),
    CPP("cpp", "CPP", 1, "code", "⚙️","#dbeafe"),
    C("c", "C", 1, "code", "💻","#dbeafe"),
    CS("cs", "CS", 1, "code", "💻","#dbeafe"),
    PHP("php", "PHP", 1, "code", "🐘","#dbeafe"),
    RB("rb", "RB", 1, "code", "💎","#dbeafe"),
    GO("go", "GO", 1, "code", "🐹","#dbeafe"),
    RS("rs", "RS", 1, "code", "🦀","#dbeafe"),
    SWIFT("swift", "SWIFT", 1, "code", "🕊️","#dbeafe"),
    KT("kt", "KT", 1, "code", "📱","#dbeafe"),
    SQL("sql", "SQL", 1, "code", "🗄️","#dbeafe"),
    INI("ini", "INI", 1, "code", "⚙️","#dbeafe"),
    CONF("conf", "CONF", 1, "code", "⚙️","#dbeafe"),
    CONFIG("config", "CONFIG", 1, "code", "⚙️","#dbeafe"),
    ENV("env", "ENV", 1, "code", "🌱","#dbeafe");

    private final String code;
    private final String description;
    private final String type;
    private final int order;
    private final String icon;
    private final String css;

    FileTypeEnum(String code, String description, int order, String type, String icon, String css) {
        this.code = code;
        this.description = description;
        this.icon = icon;
        this.css = css;
        this.type = type;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getIcon() {
        return icon;
    }

    public String getCss() {
        return css;
    }

    public String getType() {
        return type;
    }

    public int getOrder() {
        return order;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 文档状态枚举
     */
    public static FileTypeEnum fromCode(String code) {
        for (FileTypeEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的文档状态代码: " + code);
    }
}
