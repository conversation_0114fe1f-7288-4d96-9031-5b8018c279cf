<template>
  <div class="conversation-list-test p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">对话记录列表测试</h1>
      
      <!-- 测试控制区域 -->
      <div class="bg-white rounded-lg shadow p-4 mb-6">
        <h2 class="text-lg font-semibold mb-4">测试控制</h2>
        
        <div class="flex flex-col gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              智能体 AppId:
            </label>
            <input
              v-model="testAppId"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入智能体的AppId"
            />
          </div>
          
          <div class="flex gap-2">
            <button
              @click="fetchConversations"
              :disabled="loading || !testAppId"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {{ loading ? '获取中...' : '获取对话记录' }}
            </button>
            
            <button
              @click="clearResults"
              class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              清空结果
            </button>
          </div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <h3 class="text-red-800 font-semibold mb-2">错误信息:</h3>
        <p class="text-red-700">{{ error }}</p>
      </div>
      
      <!-- 结果显示区域 -->
      <div v-if="conversations.length > 0" class="bg-white rounded-lg shadow p-4">
        <h2 class="text-lg font-semibold mb-4">
          对话记录列表 (共 {{ conversations.length }} 条)
        </h2>
        
        <div class="space-y-3">
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <h3 class="font-medium text-gray-900 mb-1">
                  {{ conversation.name }}
                </h3>
                <p class="text-sm text-gray-600 mb-2">
                  ID: {{ conversation.id }}
                </p>
                <p class="text-sm text-gray-600 mb-2">
                  状态: 
                  <span :class="{
                    'text-green-600': conversation.status === 'normal',
                    'text-red-600': conversation.status === 'error',
                    'text-yellow-600': conversation.status === 'pending'
                  }">
                    {{ conversation.status }}
                  </span>
                </p>
                <p v-if="conversation.introduction" class="text-sm text-gray-600 mb-2">
                  简介: {{ conversation.introduction }}
                </p>
              </div>
              
              <div class="text-right text-sm text-gray-500">
                <p>创建: {{ formatTimestamp(conversation.createdAt) }}</p>
                <p>更新: {{ formatTimestamp(conversation.updatedAt) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading && !error" class="bg-gray-50 rounded-lg p-8 text-center">
        <p class="text-gray-600">暂无对话记录，请输入AppId并点击获取按钮</p>
      </div>
      
      <!-- 原始数据展示 -->
      <div v-if="rawData" class="mt-6 bg-gray-100 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-2">原始响应数据:</h3>
        <pre class="text-sm text-gray-700 overflow-auto">{{ JSON.stringify(rawData, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { exploreApi, type ConversationInfo } from '@/api/explore'

// 响应式数据
const testAppId = ref('3778735c-cc1a-41e3-988f-7a108e3eafb0') // 默认使用通用助手的AppId
const loading = ref(false)
const error = ref('')
const conversations = ref<ConversationInfo[]>([])
const rawData = ref<any>(null)

// 获取对话记录
const fetchConversations = async () => {
  if (!testAppId.value.trim()) {
    error.value = '请输入AppId'
    return
  }
  
  loading.value = true
  error.value = ''
  conversations.value = []
  rawData.value = null
  
  try {
    console.log('开始获取对话记录，AppId:', testAppId.value)
    const result = await exploreApi.getConversations(testAppId.value, {
      limit: 100,
      pinned: false
    })
    
    console.log('获取对话记录成功:', result)
    conversations.value = result
    rawData.value = result
    
  } catch (err: any) {
    console.error('获取对话记录失败:', err)
    error.value = err.message || '获取对话记录失败'
  } finally {
    loading.value = false
  }
}

// 清空结果
const clearResults = () => {
  conversations.value = []
  error.value = ''
  rawData.value = null
}

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '未知'
  
  const date = new Date(timestamp * 1000) // 假设是秒级时间戳
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.conversation-list-test {
  min-height: 100vh;
  background: linear-gradient(to bottom right, #f8fafc, #e2e8f0);
}
</style>
