<template>
  <div class="third-platform-agent-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">第三方智能体管理</h2>
        <p class="page-description">管理和配置第三方智能体的连接信息</p>
      </div>
      <div class="header-right">
        <button class="btn btn-outline" @click="refreshData" title="刷新数据">
          <i class="fas fa-refresh"></i>
          刷新
        </button>
        <button class="btn btn-primary" @click="openCreateModal">
          <i class="fas fa-plus"></i>
          添加智能体
        </button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="search-filter-section">
      <div class="search-box">
        <i class="fas fa-search search-icon"></i>
        <input
          type="text"
          placeholder="搜索智能体名称或描述..."
          v-model="searchQuery"
          class="search-input"
        >
      </div>
      <div class="filter-controls">
        <div class="status-filter-wrapper">
          <StatusSelector
            v-model="statusFilter"
            mode="dropdown"
            preset="agent"
            align="left"
            :config="{
              placeholder: '全部状态',
              size: 'default',
              clearable: true,
              showDescription: false
            }"
            @change="handleStatusFilterChange"
          />
        </div>
        <div class="unit-filter-wrapper">
          <DeptTreeSelector
            v-model="unitFilter"
            mode="dropdown"
            align="right"
            :config="{
              multiple: false,
              placeholder: '全部单位',
              size: 'default',
              clearable: true,
              filterable: true,
              checkStrictly: true,
              showCheckbox: false
            }"
            @change="handleUnitFilterChange"
          />
        </div>
      </div>
    </div>

    <!-- 智能体列表 -->
    <div class="agents-list">
      <div class="list-header">
        <div class="header-cell name">智能体名称</div>
        <div class="header-cell platform">平台类型</div>
        <div class="header-cell unit">所属单位</div>
        <div class="header-cell url">连接地址</div>
        <div class="header-cell access">访问范围</div>
        <div class="header-cell status">状态</div>
        <div class="header-cell creator">创建人</div>
        <div class="header-cell updater">更新人</div>
        <div class="header-cell created">创建时间</div>
        <div class="header-cell actions">操作</div>
      </div>

      <div class="list-body">
        <div
          v-for="agent in filteredAgents"
          :key="agent.id"
          class="agent-row"
          :class="{ 'inactive': agent.status === 0 }"
        >
          <div class="cell name">
            <div class="agent-info">
              <div class="agent-icon" :style="{ backgroundColor: agent.iconBg }">
                {{ agent.icon }}
              </div>
              <div class="agent-details">
                <div class="agent-name">{{ agent.name }}</div>
                <div class="agent-description">{{ agent.description }}</div>
              </div>
            </div>
          </div>
          <div class="cell platform">
            <div class="platform-info">
              <div class="platform-badge" v-if="agent.platformInfo">
                <span class="platform-icon" :style="{ backgroundColor: agent.platformInfo.listClass }">
                  {{ agent.platformInfo.cssClass || '🤖' }}
                </span>
                <span class="platform-name">{{ agent.platformInfo.dictLabel }}</span>
              </div>
              <span v-else class="platform-unknown">未知平台</span>
            </div>
          </div>
          <div class="cell unit">
            <span class="unit-badge">{{ getUnitName(agent.unitId) }}</span>
          </div>
          <div class="cell url">
            <div class="url-info">
              <span class="url-text">{{ agent.connectionUrl }}</span>
              <button class="copy-btn" @click="copyUrl(agent.connectionUrl)" title="复制地址">
                <i class="fas fa-copy"></i>
              </button>
            </div>
          </div>
          <div class="cell access">
            <div class="access-info">
              <span class="access-badge" :class="agent.accessScope">
                {{ getAccessScopeText(agent.accessScope) }}
              </span>
              <div class="access-details" v-if="agent.accessScope === 'partial_users' && agent.authorizedUsers?.length">
                <span class="detail-text">{{ agent.authorizedUsers.length }}个用户</span>
              </div>
              <div class="access-details" v-if="agent.accessScope === 'partial_units' && agent.authorizedUnits?.length">
                <span class="detail-text">{{ agent.authorizedUnits.length }}个单位</span>
              </div>
            </div>
          </div>
          <div class="cell status">
            <span class="status-badge" :class="agent.status === 1 ? 'active' : 'inactive'">
              {{ agent.status === 1 ? '启用' : '禁用' }}
            </span>
          </div>
          <div class="cell creator">
            <span class="creator-name">{{ agent.createByName || '未知' }}</span>
          </div>
          <div class="cell updater">
            <span class="updater-name">{{ agent.updateByName || '未知' }}</span>
          </div>
          <div class="cell created">{{ formatDate(agent.createTime) }}</div>
          <div class="cell actions">
            <button class="action-btn" @click="testConnection(agent)" title="测试连接">
              <i class="fas fa-plug"></i>
            </button>
            <button class="action-btn" @click="editAgent(agent)" title="编辑">
              <i class="fas fa-edit"></i>
            </button>
            <button
              class="action-btn"
              @click="toggleStatus(agent)"
              :title="agent.status === 1 ? '禁用' : '启用'"
            >
              <i class="fas" :class="agent.status === 1 ? 'fa-pause' : 'fa-play'"></i>
            </button>
            <button class="action-btn danger" @click="deleteAgent(agent)" title="删除">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredAgents.length === 0" class="empty-state">
          <div class="empty-icon">🤖</div>
          <p class="empty-text">暂无第三方智能体</p>
          <p class="empty-hint">点击"添加智能体"按钮创建第一个智能体</p>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <Pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="filteredAgents.length"
      :show-page-size-selector="true"
      :show-jumper="true"
      @change="handlePaginationChange"
    />

    <!-- 创建/编辑模态框 -->
    <el-dialog
      v-model="showModal"
      :title="editingAgent ? '编辑智能体' : '添加智能体'"
      width="600px"
      :before-close="closeModal"
    >
      <el-form
        :model="agentForm"
        label-width="120px"
        :rules="formRules"
        ref="agentFormRef"
      >
        <el-form-item label="智能体平台" prop="platformId">
          <el-select
            v-model="agentForm.platformId"
            placeholder="请选择智能体平台"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="platform in platformOptions"
              :key="platform.id"
              :label="platform.dictLabel"
              :value="platform.id"
            >
              <div class="flex items-center gap-2">
                <span :style="{ backgroundColor: platform.listClass }" class="w-6 h-6 rounded flex items-center justify-center text-sm">
                  {{ platform.cssClass }}
                </span>
                <span>{{ platform.dictLabel }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="智能体名称" prop="name">
          <el-input
            v-model="agentForm.name"
            placeholder="请输入智能体名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="描述信息">
          <el-input
            v-model="agentForm.description"
            type="textarea"
            placeholder="请输入智能体描述"
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="所属单位" prop="unitId">
          <el-select
            v-model="agentForm.unitId"
            placeholder="请选择单位"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="unit in units"
              :key="unit.id"
              :label="unit.name"
              :value="unit.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="连接地址" prop="connectionUrl">
          <el-input
            v-model="agentForm.connectionUrl"
            placeholder="https://example.com/api"
            type="url"
          />
        </el-form-item>

        <el-form-item label="API密钥">
          <el-input
            v-model="agentForm.apiKey"
            type="password"
            placeholder="请输入API密钥（可选）"
            show-password
          />
        </el-form-item>

        <el-form-item label="超时时间（秒）">
          <el-input-number
            v-model="agentForm.timeout"
            :min="1"
            :max="300"
            placeholder="30"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="访问范围" prop="accessScope">
          <el-select
            v-model="agentForm.accessScope"
            placeholder="请选择访问范围"
            style="width: 100%"
          >
            <el-option label="个人" value="personal" />
            <el-option label="公开" value="public" />
            <el-option label="部分人" value="partial_users" />
            <el-option label="部分单位" value="partial_units" />
          </el-select>
        </el-form-item>

        <el-form-item label="授权用户" v-if="agentForm.accessScope === 'partial_users'">
          <div class="user-selector-wrapper">
            <UserByDeptSelector
              v-model="agentForm.authorizedUsers"
              mode="dropdown"
              :config="{
                multiple: true,
                placeholder: '选择授权用户',
                size: 'default'
              }"
              :exclude-user-ids="[]"
              @change="handleAuthorizedUsersChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="授权单位" v-if="agentForm.accessScope === 'partial_units'">
          <div class="unit-selector-wrapper">
            <DeptTreeSelector
              v-model="agentForm.authorizedUnits"
              mode="dropdown"
              :config="{
                multiple: true,
                placeholder: '选择授权单位',
                size: 'default',
                checkStrictly: true,
                showCheckbox: true
              }"
              @change="handleAuthorizedUnitsChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch
            v-model="agentForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeModal">取消</el-button>
          <el-button
            type="primary"
            @click="saveAgent"
            :disabled="!canSave"
            :loading="loading"
          >
            {{ editingAgent ? '保存' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ThirdPlatformAPI } from '@/api/agents'
import { DictAPI } from '@/api/dict'
import { DeptTreeSelector, UserByDeptSelector } from '@/components/common/selectors'
import StatusSelector from '@/components/common/selectors/StatusSelector.vue'
import Pagination from '@/components/common/Pagination.vue'

// 响应式数据
const agents = ref<any[]>([])
const units = ref<any[]>([])
const platformOptions = ref<any[]>([])
const searchQuery = ref('')
const statusFilter = ref('')
const unitFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showModal = ref(false)
const editingAgent = ref<any>(null)
const loading = ref(false)
const agentFormRef = ref<any>(null)
const agentForm = ref({
  platformId: '',
  name: '',
  description: '',
  unitId: '',
  connectionUrl: '',
  apiKey: '',
  timeout: 30,
  accessScope: '',
  authorizedUsers: [] as string[],
  authorizedUnits: [] as string[],
  isActive: true
})

// 表单验证规则
const formRules = {
  platformId: [
    { required: true, message: '请选择智能体平台', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入智能体名称', trigger: 'blur' },
    { min: 1, max: 100, message: '智能体名称长度必须在1-100个字符之间', trigger: 'blur' }
  ],
  unitId: [
    { required: true, message: '请选择所属单位', trigger: 'change' }
  ],
  connectionUrl: [
    { required: true, message: '请输入连接地址', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  accessScope: [
    { required: true, message: '请选择访问范围', trigger: 'change' }
  ]
}

// 计算属性
const filteredAgents = computed(() => {
  let result = agents.value

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(agent =>
      agent.name.toLowerCase().includes(query) ||
      agent.description.toLowerCase().includes(query)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    const statusValue = statusFilter.value === 'active' ? 1 : 0
    result = result.filter(agent => agent.status === statusValue)
  }

  // 单位过滤
  if (unitFilter.value) {
    result = result.filter(agent => agent.unitId === unitFilter.value)
  }

  return result
})

const canSave = computed(() => {
  return agentForm.value.platformId &&
         agentForm.value.name.trim() &&
         agentForm.value.unitId &&
         agentForm.value.connectionUrl.trim() &&
         agentForm.value.accessScope
})

// 事件处理函数
const handleStatusFilterChange = (value: string | number | null) => {
  // 状态过滤器变化处理
  console.log('状态过滤器变化:', value)
  // StatusSelector 返回的值直接使用，因为预设的 agent 类型已经使用了 'active'/'inactive'
  statusFilter.value = value ? String(value) : ''
}

const handleUnitFilterChange = (value: string | string[]) => {
  // 单位过滤器变化处理
  console.log('单位过滤器变化:', value)
  unitFilter.value = Array.isArray(value) ? value[0] || '' : value || ''
}



const handleAuthorizedUsersChange = (value: string | string[]) => {
  agentForm.value.authorizedUsers = Array.isArray(value) ? value : (value ? [value] : [])
}

const handleAuthorizedUnitsChange = (value: string | string[]) => {
  agentForm.value.authorizedUnits = Array.isArray(value) ? value : (value ? [value] : [])
}

const handlePaginationChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
  // 可以在这里添加重新加载数据的逻辑
}

// 方法
const loadAgents = async () => {
  try {
    console.log('开始加载智能体列表...')
    const queryDTO = {
      current: currentPage.value,
      size: pageSize.value,
      name: searchQuery.value,
      unitId: unitFilter.value,
      status: statusFilter.value ? (statusFilter.value === 'active' ? 1 : 0) : undefined
    }
    console.log('查询参数:', queryDTO)

    const response = await ThirdPlatformAPI.getPlatformPage(queryDTO)
    console.log('智能体列表API响应:', response)

    if (response.success) {
      const agentList = response.data.records || []

      // 为每个智能体添加平台信息
      agents.value = agentList.map((agent: any) => {
        const platformInfo = platformOptions.value.find(p => p.id === agent.platformId)
        return {
          ...agent,
          platformInfo: platformInfo
        }
      })

      console.log('智能体列表加载成功:', agents.value.length, '条记录')
    } else {
      console.error('加载智能体列表失败:', response.message)
      agents.value = []
    }
  } catch (error) {
    console.error('加载智能体列表失败:', error)
    agents.value = []
  }
}

const loadUnits = async () => {
  try {
    console.log('开始加载部门列表...')
    const response = await ThirdPlatformAPI.getDeptList()
    console.log('部门列表API响应:', response)

    if (response.success && response.data) {
      units.value = response.data.map((dept: any) => ({
        id: dept.id,
        name: dept.deptName
      }))
      console.log('部门列表加载成功:', units.value)
    } else {
      console.error('加载部门列表失败:', response.message)
      units.value = []
    }
  } catch (error) {
    console.error('加载部门列表失败:', error)
    units.value = []
  }
}

const loadPlatformOptions = async () => {
  try {
    const response = await DictAPI.getDictDataByType('third_platform')

    if (response.success && response.data) {
      platformOptions.value = response.data
    } else {
      console.error('加载智能体平台选项失败:', response.message)
      platformOptions.value = []
    }
  } catch (error) {
    console.error('加载智能体平台选项失败:', error)
    platformOptions.value = []
  }
}

const getUnitName = (unitId: string) => {
  const unit = units.value.find(u => u.id === unitId)
  return unit ? unit.name : '未知单位'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    return new Date(dateStr).toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

const getAccessScopeText = (scope: string) => {
  const scopeMap: Record<string, string> = {
    'personal': '个人',
    'public': '公开',
    'partial_users': '部分人',
    'partial_units': '部分单位'
  }
  return scopeMap[scope] || '未知'
}

const openCreateModal = () => {
  editingAgent.value = null
  agentForm.value = {
    platformId: '',
    name: '',
    description: '',
    unitId: '',
    connectionUrl: '',
    apiKey: '',
    timeout: 30,
    accessScope: '',
    authorizedUsers: [],
    authorizedUnits: [],
    isActive: true
  }
  showModal.value = true
}

const editAgent = (agent: any) => {
  editingAgent.value = agent
  agentForm.value = {
    platformId: agent.platformId || '',
    name: agent.name,
    description: agent.description,
    unitId: agent.unitId,
    connectionUrl: agent.connectionUrl,
    apiKey: agent.apiKey || '',
    timeout: agent.timeout,
    accessScope: agent.accessScope || '',
    authorizedUsers: agent.authorizedUsers || [],
    authorizedUnits: agent.authorizedUnits || [],
    isActive: agent.status === 1
  }
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  editingAgent.value = null
}

const saveAgent = async () => {
  if (!canSave.value) return

  try {
    const agentData = {
      id: editingAgent.value?.id,
      platformId: agentForm.value.platformId,
      name: agentForm.value.name,
      description: agentForm.value.description,
      unitId: agentForm.value.unitId,
      connectionUrl: agentForm.value.connectionUrl,
      apiKey: agentForm.value.apiKey,
      timeout: agentForm.value.timeout,
      accessScope: agentForm.value.accessScope,
      authorizedUsers: JSON.stringify(agentForm.value.authorizedUsers),
      authorizedUnits: JSON.stringify(agentForm.value.authorizedUnits),
      status: agentForm.value.isActive ? 1 : 0,
      icon: '🤖',
      iconBg: '#6366f1'
    }

    let response
    if (editingAgent.value) {
      // 编辑智能体
      response = await ThirdPlatformAPI.updateAgent(agentData)
    } else {
      // 创建新智能体
      response = await ThirdPlatformAPI.addAgent(agentData)
    }

    if (response.success) {
      closeModal()
      ElMessage.success(editingAgent.value ? '智能体更新成功！' : '智能体创建成功！')
      // 重新加载列表
      await loadAgents()
    } else {
      ElMessage.error(response.message || '保存失败，请重试')
    }
  } catch (error) {
    console.error('保存智能体失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

const testConnection = async (agent: any) => {
  try {
    const response = await ThirdPlatformAPI.testConnection(agent.id)
    if (response.success) {
      ElMessage.success({
        message: `智能体 "${agent.name}" 连接测试成功！`,
        duration: 3000
      })
      // 重新加载列表以更新测试结果
      await loadAgents()
    } else {
      ElMessage.error({
        message: `智能体 "${agent.name}" 连接测试失败！${response.message ? '\n' + response.message : ''}`,
        duration: 4000
      })
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error(`智能体 "${agent.name}" 连接测试失败！`)
  }
}

const toggleStatus = async (agent: any) => {
  try {
    const newStatus = agent.status === 1 ? 0 : 1
    const response = await ThirdPlatformAPI.toggleStatus(agent.id, newStatus)

    if (response.success) {
      agent.status = newStatus
      ElMessage.success(`智能体已${newStatus === 1 ? '启用' : '禁用'}`)
    } else {
      ElMessage.error(response.message || '状态切换失败，请重试')
    }
  } catch (error) {
    console.error('状态切换失败:', error)
    ElMessage.error('状态切换失败，请重试')
  }
}

const deleteAgent = async (agent: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除智能体"${agent.name}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const response = await ThirdPlatformAPI.deleteAgent(agent.id)

    if (response.success) {
      agents.value = agents.value.filter(a => a.id !== agent.id)
      ElMessage.success('智能体删除成功！')
    } else {
      ElMessage.error(response.message || '删除失败，请重试')
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除，不显示错误信息
      return
    }
    console.error('删除失败:', error)
    ElMessage.error('删除失败，请重试')
  }
}

const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    ElMessage.success({
      message: '地址已复制到剪贴板',
      duration: 2000
    })
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = url
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success({
      message: '地址已复制到剪贴板',
      duration: 2000
    })
  }
}

const refreshData = async () => {
  try {
    await Promise.all([loadAgents(), loadUnits(),  loadPlatformOptions()])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage.error('数据刷新失败，请重试')
  }
}



// 监听搜索条件变化，自动重新加载数据
let searchTimeout: NodeJS.Timeout | null = null
watch([searchQuery, statusFilter, unitFilter], () => {
  console.log('搜索条件变化，重新加载数据')
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    loadAgents()
  }, 300)
})

// 生命周期
onMounted(async () => {
  try {
    // 并行加载所有数据
    await Promise.all([loadAgents(), loadUnits(), loadPlatformOptions()])
  } catch (error) {
    console.error('页面数据加载失败:', error)
    ElMessage.error('页面数据加载失败，请刷新重试')
  }
})
</script>

<style scoped>
.third-platform-agent-management {
  max-width: none;
  margin: 0;
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-outline {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-outline:hover {
  background: #f8fafc;
  color: #334155;
}

.search-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 过滤区域选择器统一样式 */
.filter-controls :deep(.dropdown-trigger) {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  transition: border-color 0.2s;
}

.filter-controls :deep(.dropdown-trigger:hover) {
  border-color: #c0c4cc;
}

.filter-controls :deep(.dropdown-trigger:focus-within) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.agents-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: grid;
  grid-template-columns: 2fr 140px 120px 180px 120px 80px 100px 100px 140px 120px;
  gap: 16px;
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
}

.list-body {
  min-height: 200px;
}

.agent-row {
  display: grid;
  grid-template-columns: 2fr 140px 120px 180px 120px 80px 100px 100px 140px 120px;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.agent-row:hover {
  background: #f9fafb;
}

.agent-row.inactive {
  opacity: 0.6;
}

.cell {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.agent-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.agent-details {
  min-width: 0;
  flex: 1;
}

.agent-name {
  font-weight: 500;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agent-description {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.platform-info {
  display: flex;
  align-items: center;
}

.platform-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.platform-icon {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  flex-shrink: 0;
}

.platform-name {
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

.platform-unknown {
  color: #9ca3af;
  font-size: 12px;
  font-style: italic;
}

.unit-badge {
  padding: 2px 8px;
  background: #f3f4f6;
  color: #374151;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.url-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.url-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: monospace;
  font-size: 12px;
  color: #6b7280;
}

.copy-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.copy-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.access-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.access-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
}

.access-badge.personal {
  background: #fef3c7;
  color: #d97706;
}

.access-badge.public {
  background: #dcfce7;
  color: #16a34a;
}

.access-badge.partial_users {
  background: #dbeafe;
  color: #2563eb;
}

.access-badge.partial_units {
  background: #f3e8ff;
  color: #9333ea;
}

.access-details {
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-text {
  font-size: 10px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 1px 4px;
  border-radius: 2px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.inactive {
  background: #fef2f2;
  color: #dc2626;
}

.creator-name,
.updater-name {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

.actions {
  gap: 8px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 12px;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #dc2626;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  margin: 0;
  opacity: 0.7;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-top: 24px;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f9fafb;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #6b7280;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #dc2626;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.form-checkbox {
  width: auto;
  margin: 0;
}

.checkbox-text {
  font-size: 14px;
  color: #374151;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

/* 新增组件样式 */
.status-filter-wrapper {
  min-width: 150px;
}

.unit-filter-wrapper {
  min-width: 200px;
}

.user-selector-wrapper,
.unit-selector-wrapper {
  width: 100%;
}

/* 下拉式选择器样式优化 */
.unit-filter-wrapper :deep(.dept-tree-selector.is-dropdown) {
  width: 100%;
}

.unit-selector-wrapper :deep(.dept-tree-selector.is-dropdown) {
  width: 100%;
}

.user-selector-wrapper :deep(.user-by-dept-selector.is-dropdown) {
  width: 100%;
}

/* 下拉面板样式 */
.unit-filter-wrapper :deep(.dept-dropdown-panel),
.unit-selector-wrapper :deep(.dept-dropdown-panel) {
  min-width: 300px;
  max-width: 500px;
}

.user-selector-wrapper :deep(.user-by-dept-dropdown-panel) {
  min-width: 400px;
  max-width: 600px;
  max-height: 500px;
}

.unit-filter-wrapper :deep(.dept-dropdown-panel .tree-container) {
  min-height: 200px;
  max-height: 300px;
}

.unit-selector-wrapper :deep(.dept-dropdown-panel .tree-container) {
  min-height: 250px;
  max-height: 350px;
}

/* 用户选择器下拉面板内容优化 */
.user-selector-wrapper :deep(.user-by-dept-dropdown-panel .dropdown-content) {
  padding: 8px;
}

.user-selector-wrapper :deep(.user-by-dept-dropdown-panel .user-by-dept-selector-content) {
  border: none;
  box-shadow: none;
}

/* 模态框中的下拉选择器优化 */
.modal-content .user-selector-wrapper :deep(.dropdown-trigger) {
  min-height: 36px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 32px 8px 12px;
  background: #fff;
  transition: border-color 0.2s;
}

.modal-content .user-selector-wrapper :deep(.dropdown-trigger:hover) {
  border-color: #c0c4cc;
}

.modal-content .user-selector-wrapper :deep(.dropdown-trigger:focus-within) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 选中状态显示优化 */
.modal-content .user-selector-wrapper :deep(.selected-count) {
  color: #409eff;
  font-weight: 500;
}

.modal-content .user-selector-wrapper :deep(.placeholder-text) {
  color: #c0c4cc;
}

/* 模态框中所有下拉选择器的统一样式 */
.modal-content .unit-selector-wrapper :deep(.dropdown-trigger),
.modal-content .user-selector-wrapper :deep(.dropdown-trigger) {
  min-height: 36px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 32px 8px 12px;
  background: #fff;
  transition: all 0.2s;
  font-size: 14px;
}

.modal-content .unit-selector-wrapper :deep(.dropdown-trigger:hover),
.modal-content .user-selector-wrapper :deep(.dropdown-trigger:hover) {
  border-color: #c0c4cc;
}

.modal-content .unit-selector-wrapper :deep(.dropdown-trigger:focus-within),
.modal-content .user-selector-wrapper :deep(.dropdown-trigger:focus-within) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 下拉箭头样式 */
.modal-content .unit-selector-wrapper :deep(.dropdown-arrow),
.modal-content .user-selector-wrapper :deep(.dropdown-arrow) {
  color: #c0c4cc;
  transition: transform 0.2s, color 0.2s;
}

.modal-content .unit-selector-wrapper :deep(.dropdown-trigger:hover .dropdown-arrow),
.modal-content .user-selector-wrapper :deep(.dropdown-trigger:hover .dropdown-arrow) {
  color: #909399;
}

/* 表单组样式优化 */
.form-group .unit-selector-wrapper,
.form-group .user-selector-wrapper {
  margin-top: 4px;
}

/* 确保下拉面板在模态框上方显示 */
.modal-content .unit-selector-wrapper :deep(.dropdown-panel),
.modal-content .user-selector-wrapper :deep(.dropdown-panel) {
  z-index: 3000; /* 高于模态框的z-index */
}

/* 表单中的平台图标样式 */
.platform-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 4px;
}
</style>
