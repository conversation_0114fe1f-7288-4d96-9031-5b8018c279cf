<template>
  <div class="dept-tree-selector-content">
    <!-- 选择器头部 -->
    <div v-if="showHeader" class="selector-header flex items-center justify-between mb-3">
      <div class="flex items-center gap-2">
        <span class="text-sm font-medium text-gray-700">
          {{ config.multiple ? '选择部门' : '选择部门' }}
        </span>
        <span v-if="hasSelection" class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
          已选择 {{ selectedCount }} 个
        </span>
      </div>
      <div class="flex items-center gap-2">
        <el-button 
          v-if="config.multiple" 
          @click="selectAll" 
          size="small" 
          type="primary" 
          plain
          :disabled="loading || !filteredOptions.length"
        >
          全选
        </el-button>
        <el-button 
          @click="clearSelection" 
          size="small" 
          :disabled="!hasSelection"
        >
          清空
        </el-button>
        <el-button 
          @click="expandAll" 
          size="small" 
          plain
          :disabled="loading"
        >
          展开
        </el-button>
        <el-button 
          @click="collapseAll" 
          size="small" 
          plain
          :disabled="loading"
        >
          折叠
        </el-button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div v-if="config.filterable" class="search-box mb-3">
      <el-input
        v-model="filterText"
        placeholder="搜索部门名称..."
        :prefix-icon="Search"
        clearable
        :size="config.size"
        :disabled="config.disabled || loading"
      />
    </div>

    <!-- 部门树 -->
    <div class="tree-container border border-gray-200 rounded-lg p-3 bg-white min-h-[300px] max-h-[400px] overflow-auto">
      <el-tree
        ref="treeRef"
        :data="filteredOptions"
        :props="treeProps"
        :show-checkbox="config.multiple"
        :check-strictly="config.checkStrictly"
        :expand-on-click-node="config.expandOnClickNode"
        :default-expand-all="config.defaultExpandAll"
        :filter-node-method="filterNodeMethod"
        :highlight-current="!config.multiple"
        :current-node-key="!config.multiple ? selectedValues : undefined"
        :checked-keys="config.multiple ? selectedValues : []"
        :expanded-keys="expandedKeys"
        node-key="value"
        class="dept-tree"
        @check="handleCheck"
        @node-click="handleNodeClick"
        @expand="handleExpand"
        @collapse="handleCollapse"
      >
        <template #default="{ node, data }">
          <div class="tree-node flex items-center justify-between w-full">
            <div class="node-content flex items-center">
              <span class="node-icon mr-2">
                <i v-if="data.children && data.children.length > 0" class="el-icon-folder text-yellow-500"></i>
                <i v-else class="el-icon-office-building text-blue-500"></i>
              </span>
              <span class="node-label text-sm" :class="{ 'text-gray-400': data.disabled }">
                {{ data.label }}
              </span>
              <span v-if="data.deptCode" class="dept-code text-xs text-gray-500 ml-2">
                ({{ data.deptCode }})
              </span>
            </div>
            <div class="node-extra flex items-center text-xs text-gray-500">
              <span v-if="data.userCount !== undefined" class="user-count mr-2">
                {{ data.userCount }}人
              </span>
              <span v-if="data.leaderName" class="leader-name">
                负责人: {{ data.leaderName }}
              </span>
            </div>
          </div>
        </template>
      </el-tree>

      <!-- 空状态 -->
      <div v-if="!loading && !filteredOptions.length" class="empty-state text-center py-8">
        <div class="text-gray-400 mb-2">
          <i class="el-icon-folder-opened text-4xl"></i>
        </div>
        <p class="text-gray-500 text-sm">
          {{ filterText ? '未找到匹配的部门' : '暂无部门数据' }}
        </p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state text-center py-8">
        <div class="loading-spinner">
          <i class="el-icon-loading animate-spin"></i>
        </div>
        <p class="text-gray-500 text-sm mt-2">加载中...</p>
      </div>
    </div>

    <!-- 已选择的部门标签 -->
    <div v-if="hasSelection && config.multiple && showSelectedTags" class="selected-tags mt-3">
      <div class="text-xs text-gray-600 mb-2">已选择的部门:</div>
      <div class="flex flex-wrap gap-1">
        <el-tag
          v-for="(label, index) in getSelectedLabels()"
          :key="index"
          :closable="!config.disabled"
          size="small"
          @close="removeSelection(getSelectedValues()[index])"
        >
          {{ label }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElTree, ElInput, ElButton, ElTag } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { DeptAPI } from '@/api/system'
import { useSelector } from './composables/useSelector'
import type { DeptSelectorOption, SelectorConfig } from '@/types/system'
import type { SysDeptVO } from '@/types/system'

interface Props {
  modelValue?: string | string[]
  config?: Partial<SelectorConfig>
  excludeDeptId?: string
  onlyEnabled?: boolean
  showHeader?: boolean
  showSelectedTags?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], options: DeptSelectorOption[]): void
  (e: 'select', value: string, option: DeptSelectorOption): void
  (e: 'remove', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  config: () => ({}),
  onlyEnabled: true,
  showHeader: false,
  showSelectedTags: true
})

const emit = defineEmits<Emits>()

// 树组件引用
const treeRef = ref<InstanceType<typeof ElTree>>()

// 树属性配置
const treeProps = {
  children: 'children',
  label: 'label',
  disabled: 'disabled'
}

// 使用选择器组合函数
const {
  loading,
  options: filteredOptions,
  selectedValues,
  filterText,
  expandedKeys,
  config,
  hasSelection,
  selectedCount,
  clearSelection,
  selectAll,
  expandAll,
  collapseAll,
  getSelectedLabels,
  init
} = useSelector({
  config: {
    multiple: false,
    checkStrictly: false,
    showCheckbox: true,
    expandOnClickNode: false,
    defaultExpandAll: false,
    ...props.config
  },
  loadData: async () => {
    const response = await DeptAPI.getDeptTree({
      status: props.onlyEnabled ? '0' : undefined
    })
    return response.data || []
  },
  transformData: (data: SysDeptVO[]) => {
    const transform = (depts: SysDeptVO[]): DeptSelectorOption[] => {
      return depts.map(dept => ({
        value: dept.id,
        label: dept.deptName,
        disabled: dept.status !== '0',
        deptCode: dept.deptCode,
        parentId: dept.parentId,
        orderNum: dept.orderNum,
        leaderId: dept.leaderId,
        leaderName: dept.leaderName,
        status: dept.status,
        userCount: dept.userCount,
        level: dept.level,
        children: dept.children && dept.children.length > 0 ? transform(dept.children) : undefined
      }))
    }
    return transform(data)
  },
  filterMethod: (value: string, data: DeptSelectorOption) => {
    return data.label.toLowerCase().includes(value.toLowerCase()) ||
           (data.deptCode && data.deptCode.toLowerCase().includes(value.toLowerCase()))
  }
})

// 计算属性
const getSelectedValues = () => {
  if (config.value.multiple && Array.isArray(selectedValues.value)) {
    return selectedValues.value
  } else if (selectedValues.value) {
    return [selectedValues.value as string]
  }
  return []
}

// 过滤节点方法
const filterNodeMethod = (value: string, data: DeptSelectorOption) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase()) ||
         (data.deptCode && data.deptCode.toLowerCase().includes(value.toLowerCase()))
}

// 事件处理
const handleCheck = (data: DeptSelectorOption, checkedInfo: any) => {
  if (!config.value.multiple) return
  
  const checkedKeys = checkedInfo.checkedKeys
  selectedValues.value = checkedKeys
  emit('update:modelValue', checkedKeys)
  
  const selectedOptions = getSelectedOptions(checkedKeys)
  emit('change', checkedKeys, selectedOptions)
}

const handleNodeClick = (data: DeptSelectorOption) => {
  if (config.value.multiple || data.disabled) return

  selectedValues.value = data.value
  emit('update:modelValue', data.value)

  // 使用getSelectedOptions方法获取完整的选项信息
  const selectedOptions = getSelectedOptions(data.value)
  emit('change', data.value, selectedOptions)
  emit('select', data.value, data)
}

const handleExpand = (data: DeptSelectorOption) => {
  if (!expandedKeys.value.includes(data.value)) {
    expandedKeys.value.push(data.value)
  }
}

const handleCollapse = (data: DeptSelectorOption) => {
  const index = expandedKeys.value.indexOf(data.value)
  if (index > -1) {
    expandedKeys.value.splice(index, 1)
  }
}

const removeSelection = (value: string) => {
  if (config.value.multiple && Array.isArray(selectedValues.value)) {
    const newValues = selectedValues.value.filter(v => v !== value)
    selectedValues.value = newValues
    emit('update:modelValue', newValues)

    const selectedOptions = getSelectedOptions(newValues)
    emit('change', newValues, selectedOptions)
    emit('remove', value)

    // 更新树的选中状态
    nextTick(() => {
      treeRef.value?.setCheckedKeys(newValues)
    })
  }
}

const getSelectedOptions = (values: string | string[]): DeptSelectorOption[] => {
  // 统一处理为数组
  const targetValues = Array.isArray(values) ? values : (values ? [values] : [])

  const findOptions = (options: DeptSelectorOption[], targetValues: string[]): DeptSelectorOption[] => {
    let result: DeptSelectorOption[] = []
    for (const option of options) {
      if (targetValues.includes(option.value)) {
        result.push(option)
      }
      if (option.children && option.children.length > 0) {
        result = result.concat(findOptions(option.children, targetValues))
      }
    }
    return result
  }
  return findOptions(filteredOptions.value, targetValues)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValues.value = newValue || (config.value.multiple ? [] : '')
  
  // 同步树的选中状态
  nextTick(() => {
    if (config.value.multiple && Array.isArray(newValue)) {
      treeRef.value?.setCheckedKeys(newValue)
    } else if (newValue) {
      treeRef.value?.setCurrentKey(newValue as string)
    }
  })
}, { immediate: true })

// 监听过滤文本变化
watch(filterText, (value) => {
  treeRef.value?.filter(value)
})

// 组件挂载
onMounted(async () => {
  await init()

  // 设置初始选中状态
  nextTick(() => {
    if (config.value.multiple && Array.isArray(props.modelValue)) {
      treeRef.value?.setCheckedKeys(props.modelValue)
      // 触发change事件同步选项信息
      if (props.modelValue.length > 0) {
        const selectedOptions = getSelectedOptions(props.modelValue)
        emit('change', props.modelValue, selectedOptions)
      }
    } else if (props.modelValue) {
      treeRef.value?.setCurrentKey(props.modelValue as string)
      // 触发change事件同步选项信息
      const selectedOptions = getSelectedOptions(props.modelValue)
      emit('change', props.modelValue, selectedOptions)
    }
  })
})

// 暴露方法
defineExpose({
  clearSelection,
  selectAll,
  expandAll,
  collapseAll,
  getSelectedValues,
  getSelectedLabels,
  refresh: init
})
</script>

<style scoped>
.dept-tree-selector-content {
  @apply w-full;
}

.tree-container {
  position: relative;
}

.dept-tree :deep(.el-tree-node__content) {
  height: auto;
  padding: 8px 0;
}

.tree-node {
  flex: 1;
  padding-right: 8px;
}

.node-content {
  flex: 1;
  min-width: 0;
}

.node-label {
  word-break: break-all;
}

.node-extra {
  flex-shrink: 0;
}

.selected-tags {
  max-height: 100px;
  overflow-y: auto;
}

.empty-state,
.loading-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.loading-spinner {
  font-size: 24px;
  color: #409eff;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
