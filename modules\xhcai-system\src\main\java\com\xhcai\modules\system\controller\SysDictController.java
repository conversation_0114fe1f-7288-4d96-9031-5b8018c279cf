package com.xhcai.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.system.dto.SysDictQueryDTO;
import com.xhcai.modules.system.entity.SysDict;
import com.xhcai.modules.system.service.ISysDictService;
import com.xhcai.modules.system.vo.SysDictVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 字典类型管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "字典类型管理", description = "字典类型管理相关接口")
@RestController
@RequestMapping("/api/system/dict")
public class SysDictController {

    @Autowired
    private ISysDictService dictService;

    /**
     * 分页查询字典类型列表
     */
    @Operation(summary = "分页查询字典类型", description = "分页查询字典类型列表")
    @GetMapping("/page")
    @RequiresPermissions("system:dict:list")
    public Result<PageResult<SysDictVO>> page(@Valid SysDictQueryDTO queryDTO) {
        PageResult<SysDictVO> pageResult = dictService.selectDictPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询字典类型列表
     */
    @Operation(summary = "查询字典类型列表", description = "查询字典类型列表")
    @GetMapping("/list")
    @RequiresPermissions("system:dict:list")
    public Result<List<SysDictVO>> list(@Valid SysDictQueryDTO queryDTO) {
        List<SysDictVO> dictList = dictService.selectDictList(queryDTO);
        return Result.success(dictList);
    }

    /**
     * 根据字典ID查询字典信息
     */
    @Operation(summary = "查询字典详情", description = "根据字典ID查询字典信息")
    @GetMapping("/{dictId}")
    @RequiresPermissions("system:dict:query")
    public Result<SysDictVO> getInfo(@Parameter(description = "字典ID") @PathVariable String dictId) {
        SysDictVO dictVO = dictService.selectDictById(dictId);
        return Result.success(dictVO);
    }

    /**
     * 创建字典类型
     */
    @Operation(summary = "创建字典类型", description = "创建新的字典类型")
    @PostMapping
    @RequiresPermissions("system:dict:add")
    public Result<Void> add(@Valid @RequestBody SysDict dict) {
        boolean success = dictService.insertDict(dict);
        return success ? Result.success() : Result.fail("创建字典类型失败");
    }

    /**
     * 更新字典类型
     */
    @Operation(summary = "更新字典类型", description = "更新字典类型信息")
    @PutMapping
    @RequiresPermissions("system:dict:edit")
    public Result<Void> edit(@Valid @RequestBody SysDict dict) {
        boolean success = dictService.updateDict(dict);
        return success ? Result.success() : Result.fail("更新字典类型失败");
    }

    /**
     * 删除字典类型
     */
    @Operation(summary = "删除字典类型", description = "删除字典类型")
    @DeleteMapping("/{dictIds}")
    @RequiresPermissions("system:dict:remove")
    public Result<Void> remove(@Parameter(description = "字典ID列表，多个用逗号分隔") @PathVariable List<String> dictIds) {
        boolean success = dictService.deleteDicts(dictIds);
        return success ? Result.success() : Result.fail("删除字典类型失败");
    }

    /**
     * 批量更新字典状态
     */
    @Operation(summary = "批量更新字典状态", description = "批量更新字典状态")
    @PutMapping("/status")
    @RequiresPermissions("system:dict:edit")
    public Result<Void> updateStatus(@RequestParam List<String> dictIds, @RequestParam String status) {
        boolean success = dictService.batchUpdateStatus(dictIds, status);
        return success ? Result.success() : Result.fail("更新字典状态失败");
    }

    /**
     * 获取所有字典类型
     */
    @Operation(summary = "获取所有字典类型", description = "获取所有字典类型列表")
    @GetMapping("/types")
    @RequiresPermissions("system:dict:list")
    public Result<List<String>> getAllDictTypes() {
        List<String> dictTypes = dictService.selectAllDictTypes();
        return Result.success(dictTypes);
    }

    /**
     * 刷新字典缓存
     */
    @Operation(summary = "刷新字典缓存", description = "刷新字典缓存")
    @PostMapping("/refresh")
    @RequiresPermissions("system:dict:edit")
    public Result<Void> refreshCache() {
        dictService.refreshCache();
        return Result.success();
    }

    /**
     * 导出字典数据
     */
    @Operation(summary = "导出字典数据", description = "导出字典数据")
    @PostMapping("/export")
    @RequiresPermissions("system:dict:export")
    public Result<List<SysDictVO>> export(@RequestBody SysDictQueryDTO queryDTO) {
        List<SysDictVO> dictList = dictService.exportDicts(queryDTO);
        return Result.success(dictList);
    }

    /**
     * 导入字典数据
     */
    @Operation(summary = "导入字典数据", description = "导入字典数据")
    @PostMapping("/import")
    @RequiresPermissions("system:dict:import")
    public Result<String> importData(@RequestBody List<SysDict> dictList) {
        String result = dictService.importDicts(dictList);
        return Result.success(result);
    }
}
