package com.xhcai.modules.system.dto;

import com.xhcai.common.api.dto.PageQueryDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

/**
 * 权限查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "权限查询DTO")
public class SysPermissionQueryDTO extends PageQueryDTO {

    /**
     * 权限编码
     */
    @Schema(description = "权限编码", example = "system:user:list")
    private String permissionCode;

    /**
     * 权限名称
     */
    @Schema(description = "权限名称", example = "用户查询")
    private String permissionName;

    /**
     * 权限类型
     */
    @Schema(description = "权限类型", example = "1", allowableValues = {"1", "2", "3"})
    @Pattern(regexp = "^[123]$", message = "权限类型值必须为1-3")
    private String permissionType;

    /**
     * 父权限ID
     */
    @Schema(description = "父权限ID", example = "0")
    private String parentId;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    /**
     * 是否可见
     */
    @Schema(description = "是否可见")
    private Boolean visible;

    /**
     * 数据权限SQL
     */
    @Schema(hidden = true)
    private String dataScope;

    // Getters and Setters
    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    @Override
    public String toString() {
        return "SysPermissionQueryDTO{"
                + "permissionCode='" + permissionCode + '\''
                + ", permissionName='" + permissionName + '\''
                + ", permissionType='" + permissionType + '\''
                + ", parentId=" + parentId
                + ", visible=" + visible
                + ", current=" + getCurrent()
                + ", size=" + getSize()
                + ", orderBy='" + getOrderBy() + '\''
                + ", orderDirection='" + getOrderDirection() + '\''
                + '}';
    }
}
