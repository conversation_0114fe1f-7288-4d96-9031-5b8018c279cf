package com.xhcai.modules.rag.dto;

import com.xhcai.common.api.dto.PageQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * AI模型查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "AI模型查询DTO")
public class AiModelQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 模型名称（模糊查询）
     */
    @Schema(description = "模型名称", example = "GPT")
    @Size(max = 100, message = "模型名称长度不能超过100个字符")
    private String name;

    /**
     * 模型标识（模糊查询）
     */
    @Schema(description = "模型标识", example = "gpt")
    @Size(max = 100, message = "模型标识长度不能超过100个字符")
    private String modelId;

    /**
     * 模型提供商
     */
    @Schema(description = "模型提供商", example = "OpenAI")
    @Size(max = 50, message = "模型提供商长度不能超过50个字符")
    private String provider;

    /**
     * 模型类型
     */
    @Schema(description = "模型类型", example = "对话")
    @Size(max = 50, message = "模型类型长度不能超过50个字符")
    private String type;

    /**
     * 状态：0-停用，1-启用
     */
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "开始时间格式必须为：yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", 
             message = "结束时间格式必须为：yyyy-MM-dd HH:mm:ss")
    private String endTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "AiModelQueryDTO{" +
                "name='" + name + '\'' +
                ", modelId='" + modelId + '\'' +
                ", provider='" + provider + '\'' +
                ", type='" + type + '\'' +
                ", status='" + status + '\'' +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", current=" + getCurrent() +
                ", size=" + getSize() +
                ", orderBy='" + getOrderBy() + '\'' +
                ", orderDirection='" + getOrderDirection() + '\'' +
                '}';
    }
}
