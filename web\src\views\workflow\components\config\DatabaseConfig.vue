<template>
  <div class="database-config">
    <div class="config-section">
      <h5 class="section-title">数据库连接</h5>
      
      <div class="form-group">
        <label>数据库类型</label>
        <select v-model="localConfig.dbType" class="form-select" @change="onUpdate">
          <option value="mysql">MySQL</option>
          <option value="postgresql">PostgreSQL</option>
          <option value="oracle">Oracle</option>
          <option value="dameng">达梦数据库</option>
          <option value="elasticsearch">Elasticsearch</option>
          <option value="redis">Redis</option>
        </select>
      </div>

      <div class="form-group">
        <label>主机地址</label>
        <input 
          v-model="localConfig.host" 
          type="text" 
          class="form-input"
          placeholder="localhost"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <label>端口</label>
        <input 
          v-model.number="localConfig.port" 
          type="number" 
          class="form-input"
          :placeholder="getDefaultPort(localConfig.dbType)"
          @input="onUpdate"
        />
      </div>

      <div class="form-group" v-if="localConfig.dbType !== 'redis'">
        <label>数据库名</label>
        <input 
          v-model="localConfig.database" 
          type="text" 
          class="form-input"
          placeholder="database_name"
          @input="onUpdate"
        />
      </div>

      <div class="form-group" v-if="localConfig.dbType === 'oracle'">
        <label>服务名</label>
        <input 
          v-model="localConfig.serviceName" 
          type="text" 
          class="form-input"
          placeholder="ORCL"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <label>用户名</label>
        <input 
          v-model="localConfig.username" 
          type="text" 
          class="form-input"
          placeholder="username"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <label>密码</label>
        <input 
          v-model="localConfig.password" 
          type="password" 
          class="form-input"
          placeholder="password"
          @input="onUpdate"
        />
      </div>
    </div>

    <div class="config-section" v-if="localConfig.dbType !== 'redis'">
      <h5 class="section-title">SQL配置</h5>
      
      <div class="form-group">
        <label>操作类型</label>
        <select v-model="localConfig.operation" class="form-select" @change="onUpdate">
          <option value="select">查询 (SELECT)</option>
          <option value="insert">插入 (INSERT)</option>
          <option value="update">更新 (UPDATE)</option>
          <option value="delete">删除 (DELETE)</option>
          <option value="custom">自定义</option>
        </select>
      </div>

      <div class="form-group">
        <label>SQL语句</label>
        <textarea 
          v-model="localConfig.sql" 
          class="form-textarea"
          rows="6"
          :placeholder="getSqlPlaceholder(localConfig.operation)"
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group">
        <div class="checkbox-group">
          <label class="checkbox-label">
            <input 
              v-model="localConfig.enableTransaction" 
              type="checkbox"
              @change="onUpdate"
            />
            <span class="checkmark"></span>
            启用事务
          </label>
        </div>
      </div>
    </div>

    <div class="config-section" v-if="localConfig.dbType === 'redis'">
      <h5 class="section-title">Redis配置</h5>
      
      <div class="form-group">
        <label>数据库索引</label>
        <input 
          v-model.number="localConfig.database" 
          type="number" 
          class="form-input"
          min="0"
          max="15"
          placeholder="0"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <label>操作类型</label>
        <select v-model="localConfig.operation" class="form-select" @change="onUpdate">
          <option value="get">获取 (GET)</option>
          <option value="set">设置 (SET)</option>
          <option value="del">删除 (DEL)</option>
          <option value="exists">检查存在 (EXISTS)</option>
          <option value="expire">设置过期 (EXPIRE)</option>
          <option value="custom">自定义</option>
        </select>
      </div>

      <div class="form-group">
        <label>键名</label>
        <input 
          v-model="localConfig.key" 
          type="text" 
          class="form-input"
          placeholder="key_name"
          @input="onUpdate"
        />
      </div>

      <div class="form-group" v-if="['set'].includes(localConfig.operation)">
        <label>值</label>
        <textarea 
          v-model="localConfig.value" 
          class="form-textarea"
          rows="3"
          placeholder="value"
          @input="onUpdate"
        ></textarea>
      </div>

      <div class="form-group" v-if="localConfig.operation === 'expire'">
        <label>过期时间（秒）</label>
        <input 
          v-model.number="localConfig.ttl" 
          type="number" 
          class="form-input"
          min="1"
          placeholder="3600"
          @input="onUpdate"
        />
      </div>
    </div>

    <div class="config-section">
      <h5 class="section-title">高级选项</h5>
      
      <div class="form-group">
        <label>连接超时（毫秒）</label>
        <input 
          v-model.number="localConfig.timeout" 
          type="number" 
          class="form-input"
          min="1000"
          placeholder="30000"
          @input="onUpdate"
        />
      </div>

      <div class="form-group">
        <div class="checkbox-group">
          <label class="checkbox-label">
            <input 
              v-model="localConfig.enableRetry" 
              type="checkbox"
              @change="onUpdate"
            />
            <span class="checkmark"></span>
            启用重试
          </label>
        </div>
      </div>

      <div class="form-group" v-if="localConfig.enableRetry">
        <label>重试次数</label>
        <input 
          v-model.number="localConfig.retryCount" 
          type="number" 
          class="form-input"
          min="1"
          max="10"
          placeholder="3"
          @input="onUpdate"
        />
      </div>
    </div>

    <div class="config-actions">
      <button class="btn btn-secondary btn-sm" @click="testConnection">
        <i class="fas fa-plug"></i>
        测试连接
      </button>
      
      <button class="btn btn-primary btn-sm" @click="saveTemplate">
        <i class="fas fa-save"></i>
        保存模板
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'

// Props
interface Props {
  config: Record<string, any>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [config: Record<string, any>]
}>()

// 响应式数据
const localConfig = reactive({
  dbType: 'mysql',
  host: 'localhost',
  port: 3306,
  database: '',
  serviceName: '',
  username: '',
  password: '',
  operation: 'select',
  sql: '',
  enableTransaction: false,
  key: '',
  value: '',
  ttl: 3600,
  timeout: 30000,
  enableRetry: true,
  retryCount: 3
})

// 方法
const getDefaultPort = (dbType: string) => {
  const portMap: Record<string, number> = {
    mysql: 3306,
    postgresql: 5432,
    oracle: 1521,
    dameng: 5236,
    elasticsearch: 9200,
    redis: 6379
  }
  return portMap[dbType]?.toString() || '3306'
}

const getSqlPlaceholder = (operation: string) => {
  const placeholders: Record<string, string> = {
    select: 'SELECT * FROM table_name WHERE condition',
    insert: 'INSERT INTO table_name (column1, column2) VALUES (?, ?)',
    update: 'UPDATE table_name SET column1 = ? WHERE condition',
    delete: 'DELETE FROM table_name WHERE condition',
    custom: '输入自定义SQL语句'
  }
  return placeholders[operation] || '输入SQL语句'
}

const onUpdate = () => {
  emit('update', { ...localConfig })
}

const testConnection = async () => {
  try {
    // TODO: 实现连接测试逻辑
    console.log('测试数据库连接:', localConfig)
    alert('连接测试功能待实现')
  } catch (error) {
    console.error('连接测试失败:', error)
    alert('连接测试失败')
  }
}

const saveTemplate = () => {
  // TODO: 实现保存模板逻辑
  console.log('保存配置模板:', localConfig)
  alert('保存模板功能待实现')
}

// 初始化配置
const initializeConfig = () => {
  Object.assign(localConfig, props.config)
  
  // 设置默认端口
  if (!localConfig.port) {
    localConfig.port = parseInt(getDefaultPort(localConfig.dbType))
  }
}

// 监听配置变化
watch(() => props.config, () => {
  initializeConfig()
}, { immediate: true })

// 监听数据库类型变化，自动更新端口
watch(() => localConfig.dbType, (newType) => {
  localConfig.port = parseInt(getDefaultPort(newType))
  onUpdate()
})

// 生命周期
onMounted(() => {
  initializeConfig()
})
</script>

<style scoped>
.database-config {
  padding: 16px;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: #3b82f6;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.config-actions {
  display: flex;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}
</style>
