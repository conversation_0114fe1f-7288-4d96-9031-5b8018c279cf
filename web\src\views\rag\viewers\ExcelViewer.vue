<template>
  <BaseFileViewer :file="file" @download="handleDownload" @refresh="handleRefresh">
    <template #content>
      <div class="excel-viewer">
        <div class="excel-toolbar">
          <div class="toolbar-left">
            <div class="sheet-tabs">
              <button 
                v-for="sheet in sheets" 
                :key="sheet.id"
                class="sheet-tab"
                :class="{ active: currentSheet === sheet.id }"
                @click="switchSheet(sheet.id)"
              >
                {{ sheet.name }}
              </button>
            </div>
          </div>
          <div class="toolbar-right">
            <button class="tool-btn" @click="zoomOut" :disabled="scale <= 0.5">
              <i class="fas fa-search-minus"></i>
            </button>
            <span class="zoom-info">{{ Math.round(scale * 100) }}%</span>
            <button class="tool-btn" @click="zoomIn" :disabled="scale >= 2">
              <i class="fas fa-search-plus"></i>
            </button>
            <button class="tool-btn" @click="resetZoom">
              <i class="fas fa-expand-arrows-alt"></i>
            </button>
            <button class="tool-btn" @click="toggleFullscreen">
              <i class="fas fa-expand"></i>
            </button>
            <button class="tool-btn" @click="handleDownload" title="下载文件">
              <i class="fas fa-download"></i>
            </button>
            <button class="tool-btn" @click="handleRefresh" title="刷新内容">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>
        
        <div class="excel-content" ref="excelContainer">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载 Excel 文件...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="loadExcel">重新加载</button>
          </div>
          
          <div v-else class="excel-display">
            <div class="excel-container" :style="{ transform: `scale(${scale})` }">
              <!-- 列标题 -->
              <div class="column-headers">
                <div class="row-header"></div>
                <div 
                  v-for="col in visibleColumns" 
                  :key="col"
                  class="column-header"
                >
                  {{ getColumnName(col) }}
                </div>
              </div>
              
              <!-- 数据行 -->
              <div class="excel-rows">
                <div
                  v-for="row in visibleRows"
                  :key="row"
                  class="excel-row"
                  :class="{
                    'segment-highlighted': isRowInSelectedSegment(row),
                    'segment-border': isRowInSelectedSegment(row)
                  }"
                >
                  <div class="row-header">
                    {{ row }}
                    <!-- 分段操作按钮 -->
                    <div v-if="isRowInSelectedSegment(row)" class="row-actions">
                      <button
                        @click="editSegmentRow(row)"
                        class="action-btn edit-btn"
                        title="编辑行"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        @click="deleteSegmentRow(row)"
                        class="action-btn delete-btn"
                        title="删除行"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div
                    v-for="col in visibleColumns"
                    :key="`${row}-${col}`"
                    class="excel-cell"
                    :class="{
                      selected: selectedCell.row === row && selectedCell.col === col,
                      header: row === 1,
                      editing: editingCell.row === row && editingCell.col === col
                    }"
                    @click="selectCell(row, col)"
                    @dblclick="startEditCell(row, col)"
                  >
                    <input
                      v-if="editingCell.row === row && editingCell.col === col"
                      v-model="editingValue"
                      @blur="saveCell"
                      @keydown.enter="saveCell"
                      @keydown.esc="cancelEdit"
                      class="cell-input"
                      ref="cellInput"
                    />
                    <span v-else>{{ getCellValue(row, col) }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 单元格信息栏 -->
            <div class="cell-info-bar" v-if="selectedCell.row && selectedCell.col">
              <div class="cell-address">
                {{ getColumnName(selectedCell.col) }}{{ selectedCell.row }}
              </div>
              <div class="cell-value">
                {{ getCellValue(selectedCell.row, selectedCell.col) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseFileViewer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import BaseFileViewer from './BaseFileViewer.vue'

// Props
interface Props {
  file: any
  segments?: any[]
  selectedSegmentId?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  download: [file: any]
  segmentHover: [segmentInfo: any]
  segmentLeave: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const scale = ref(1)
const currentSheet = ref('sheet1')
const selectedCell = ref({ row: 0, col: 0 })
const editingCell = ref({ row: 0, col: 0 })
const editingValue = ref('')
const excelContainer = ref<HTMLElement | null>(null)
const cellInput = ref<HTMLInputElement | null>(null)

// 模拟数据
const sheets = ref([
  { id: 'sheet1', name: '销售数据' },
  { id: 'sheet2', name: '产品信息' },
  { id: 'sheet3', name: '统计报表' }
])

const sampleData = ref<Record<string, any>>({
  sheet1: {
    1: { 1: '产品名称', 2: '销售额', 3: '数量', 4: '单价', 5: '日期' },
    2: { 1: 'iPhone 15', 2: '¥50,000', 3: '50', 4: '¥1,000', 5: '2024-01-15' },
    3: { 1: 'MacBook Pro', 2: '¥120,000', 3: '10', 4: '¥12,000', 5: '2024-01-16' },
    4: { 1: 'iPad Air', 2: '¥30,000', 3: '60', 4: '¥500', 5: '2024-01-17' },
    5: { 1: 'Apple Watch', 2: '¥25,000', 3: '100', 4: '¥250', 5: '2024-01-18' },
    6: { 1: 'AirPods Pro', 2: '¥15,000', 3: '75', 4: '¥200', 5: '2024-01-19' }
  },
  sheet2: {
    1: { 1: 'ID', 2: '产品名称', 3: '类别', 4: '库存', 5: '状态' },
    2: { 1: 'P001', 2: 'iPhone 15', 3: '手机', 4: '150', 5: '在售' },
    3: { 1: 'P002', 2: 'MacBook Pro', 3: '笔记本', 4: '25', 5: '在售' },
    4: { 1: 'P003', 2: 'iPad Air', 3: '平板', 4: '80', 5: '在售' },
    5: { 1: 'P004', 2: 'Apple Watch', 3: '智能手表', 4: '200', 5: '在售' }
  },
  sheet3: {
    1: { 1: '月份', 2: '总销售额', 3: '订单数', 4: '平均订单额' },
    2: { 1: '2024-01', 2: '¥240,000', 3: '295', 4: '¥814' },
    3: { 1: '2024-02', 2: '¥180,000', 3: '220', 4: '¥818' },
    4: { 1: '2024-03', 2: '¥320,000', 3: '380', 4: '¥842' }
  }
})

// 计算属性
const visibleRows = computed(() => {
  return Array.from({ length: 20 }, (_, i) => i + 1)
})

const visibleColumns = computed(() => {
  return Array.from({ length: 10 }, (_, i) => i + 1)
})

// 检查行是否在选中的分段中
const isRowInSelectedSegment = (row: number) => {
  if (!props.selectedSegmentId || !props.segments) return false

  const selectedSegment = props.segments.find(seg => seg.id === props.selectedSegmentId)
  if (!selectedSegment || !selectedSegment.metadata) return false

  const startRow = selectedSegment.metadata.startRow || 1
  const endRow = selectedSegment.metadata.endRow || startRow

  return row >= startRow && row <= endRow
}

// 方法
const handleDownload = (file: any) => {
  emit('download', file)
}

const handleRefresh = () => {
  loadExcel()
}

const loadExcel = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 模拟 Excel 文件加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
  } catch (err) {
    error.value = '加载 Excel 文件失败'
  } finally {
    loading.value = false
  }
}

const getColumnName = (col: number) => {
  let result = ''
  while (col > 0) {
    col--
    result = String.fromCharCode(65 + (col % 26)) + result
    col = Math.floor(col / 26)
  }
  return result
}

const getCellValue = (row: number, col: number) => {
  const sheetData = sampleData.value[currentSheet.value]
  return sheetData?.[row]?.[col] || ''
}

const selectCell = (row: number, col: number) => {
  selectedCell.value = { row, col }
}

// 单元格编辑相关方法
const startEditCell = (row: number, col: number) => {
  editingCell.value = { row, col }
  editingValue.value = getCellValue(row, col)
  nextTick(() => {
    if (cellInput.value) {
      cellInput.value.focus()
      cellInput.value.select()
    }
  })
}

const saveCell = () => {
  const { row, col } = editingCell.value
  if (row > 0 && col > 0) {
    // 更新数据
    if (!sampleData.value[currentSheet.value][row]) {
      sampleData.value[currentSheet.value][row] = {}
    }
    sampleData.value[currentSheet.value][row][col] = editingValue.value

    // 这里应该调用API保存到后端
    console.log('保存单元格:', row, col, editingValue.value)
  }
  cancelEdit()
}

const cancelEdit = () => {
  editingCell.value = { row: 0, col: 0 }
  editingValue.value = ''
}

// 分段行操作方法
const editSegmentRow = (row: number) => {
  // 编辑整行数据
  console.log('编辑行:', row)
  // 可以打开一个对话框来编辑整行数据
}

const deleteSegmentRow = (row: number) => {
  if (confirm(`确定要删除第 ${row} 行吗？`)) {
    // 删除行数据
    delete sampleData.value[currentSheet.value][row]

    // 重新排列后续行
    const maxRow = Math.max(...Object.keys(sampleData.value[currentSheet.value]).map(Number))
    for (let i = row + 1; i <= maxRow; i++) {
      if (sampleData.value[currentSheet.value][i]) {
        sampleData.value[currentSheet.value][i - 1] = sampleData.value[currentSheet.value][i]
        delete sampleData.value[currentSheet.value][i]
      }
    }

    console.log('删除行:', row)
  }
}

const switchSheet = (sheetId: string) => {
  currentSheet.value = sheetId
  selectedCell.value = { row: 0, col: 0 }
}

const zoomIn = () => {
  if (scale.value < 2) {
    scale.value = Math.min(2, scale.value + 0.25)
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(0.5, scale.value - 0.25)
  }
}

const resetZoom = () => {
  scale.value = 1
}

const toggleFullscreen = () => {
  if (excelContainer.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      excelContainer.value.requestFullscreen()
    }
  }
}

// 生命周期
onMounted(() => {
  loadExcel()
})
</script>

<style scoped>
.excel-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.excel-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  gap: 16px;
}

.toolbar-left {
  flex: 1;
}

.sheet-tabs {
  display: flex;
  gap: 4px;
}

.sheet-tab {
  padding: 6px 12px;
  border: none;
  border-radius: 4px 4px 0 0;
  background: #e2e8f0;
  color: #64748b;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.sheet-tab:hover {
  background: #cbd5e1;
  color: #334155;
}

.sheet-tab.active {
  background: white;
  color: #1e293b;
  border-bottom: 2px solid #3b82f6;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: white;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.tool-btn:hover:not(:disabled) {
  background: #f1f5f9;
  color: #334155;
}

.tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-info {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.excel-content {
  flex: 1;
  overflow: auto;
  background: white;
  position: relative;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.excel-display {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.excel-container {
  flex: 1;
  overflow: auto;
  transform-origin: top left;
  transition: transform 0.2s ease;
}

.column-headers {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 2;
  background: white;
  border-bottom: 2px solid #e2e8f0;
}

.row-header {
  width: 50px;
  min-width: 50px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
}

.column-header {
  width: 100px;
  min-width: 100px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
}

.excel-rows {
  flex: 1;
}

.excel-row {
  display: flex;
}

.excel-row:nth-child(even) {
  background: #fafbfc;
}

.excel-row.segment-highlighted {
  background: rgba(59, 130, 246, 0.05) !important;
}

.excel-row.segment-border {
  border: 2px dashed #3b82f6;
  border-radius: 4px;
  margin: 1px;
}

/* Excel编辑相关样式 */
.row-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-actions {
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.excel-row:hover .row-actions {
  opacity: 1;
}

.action-btn {
  padding: 2px 4px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s ease;
}

.edit-btn {
  background: #3b82f6;
  color: white;
}

.edit-btn:hover {
  background: #2563eb;
}

.delete-btn {
  background: #ef4444;
  color: white;
}

.delete-btn:hover {
  background: #dc2626;
}

.excel-cell.editing {
  padding: 0;
}

.cell-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 4px 8px;
  font-size: 12px;
  background: #fff3cd;
  border: 2px solid #ffc107;
}

.excel-cell {
  width: 100px;
  min-width: 100px;
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  border-right: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  color: #1e293b;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.excel-cell:hover {
  background: #f1f5f9;
}

.excel-cell.selected {
  background: #eff6ff;
  border: 2px solid #3b82f6;
  z-index: 1;
  position: relative;
}

.excel-cell.header {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.cell-info-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 16px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  font-size: 12px;
}

.cell-address {
  font-weight: 600;
  color: #374151;
  min-width: 60px;
}

.cell-value {
  color: #64748b;
  flex: 1;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}
</style>
