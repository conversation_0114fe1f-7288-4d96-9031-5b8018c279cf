'use client';

import { useState, useEffect } from 'react';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Trash2, 
  <PERSON>ting<PERSON>, 
  Eye, 
  Download,
  Search,
  Filter,
  MoreVertical,
  CheckSquare,
  Square as UncheckedSquare
} from 'lucide-react';
import { ElasticComponent, ComponentStatus, ComponentStatusLabels, ComponentStatusColors, ComponentType, ComponentTypeLabels } from '@/types/component';
import ComponentsAPI from '@/api/components';
import toast from 'react-hot-toast';

interface ComponentListProps {
  onComponentSelect?: (component: ElasticComponent) => void;
  onUploadClick?: () => void;
}

export default function ComponentList({ onComponentSelect, onUploadClick }: ComponentListProps) {
  const [components, setComponents] = useState<ElasticComponent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedComponents, setSelectedComponents] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ComponentStatus | 'ALL'>('ALL');
  const [typeFilter, setTypeFilter] = useState<ComponentType | 'ALL'>('ALL');
  const [actionLoading, setActionLoading] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadComponents();
  }, [statusFilter, typeFilter]);

  const loadComponents = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (statusFilter !== 'ALL') params.status = statusFilter;
      if (typeFilter !== 'ALL') params.type = typeFilter;

      const response = await ComponentsAPI.getComponents(params);
      if (response.success && response.data) {
        setComponents(response.data);
      } else {
        throw new Error(response.message || '获取组件列表失败');
      }
    } catch (error: any) {
      console.error('获取组件列表失败:', error);
      toast.error(error.message || '获取组件列表失败');
    } finally {
      setLoading(false);
    }
  };

  const filteredComponents = components.filter(component => {
    const matchesSearch = component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         component.type.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const handleComponentAction = async (componentId: string, action: 'start' | 'stop' | 'restart' | 'uninstall') => {
    setActionLoading(prev => new Set(prev).add(componentId));
    
    try {
      let response;
      switch (action) {
        case 'start':
          response = await ComponentsAPI.startComponent(componentId);
          break;
        case 'stop':
          response = await ComponentsAPI.stopComponent(componentId);
          break;
        case 'restart':
          response = await ComponentsAPI.restartComponent(componentId);
          break;
        case 'uninstall':
          response = await ComponentsAPI.uninstallComponent(componentId);
          break;
      }

      if (response.success) {
        toast.success(`组件${getActionLabel(action)}成功`);
        await loadComponents(); // 刷新列表
      } else {
        throw new Error(response.message || `${getActionLabel(action)}失败`);
      }
    } catch (error: any) {
      console.error(`组件${getActionLabel(action)}失败:`, error);
      toast.error(error.message || `${getActionLabel(action)}失败`);
    } finally {
      setActionLoading(prev => {
        const newSet = new Set(prev);
        newSet.delete(componentId);
        return newSet;
      });
    }
  };

  const handleBatchAction = async (action: 'start' | 'stop') => {
    if (selectedComponents.size === 0) {
      toast.error('请选择要操作的组件');
      return;
    }

    const componentIds = Array.from(selectedComponents);
    
    try {
      let response;
      if (action === 'start') {
        response = await ComponentsAPI.batchStartComponents(componentIds);
      } else {
        response = await ComponentsAPI.batchStopComponents(componentIds);
      }

      if (response.success && response.data) {
        const { successCount, totalCount } = response.data;
        toast.success(`批量${getActionLabel(action)}完成: ${successCount}/${totalCount} 成功`);
        setSelectedComponents(new Set());
        await loadComponents();
      } else {
        throw new Error(response.message || `批量${getActionLabel(action)}失败`);
      }
    } catch (error: any) {
      console.error(`批量${getActionLabel(action)}失败:`, error);
      toast.error(error.message || `批量${getActionLabel(action)}失败`);
    }
  };

  const getActionLabel = (action: string) => {
    const labels: Record<string, string> = {
      start: '启动',
      stop: '停止',
      restart: '重启',
      uninstall: '卸载'
    };
    return labels[action] || action;
  };

  const toggleComponentSelection = (componentId: string) => {
    setSelectedComponents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(componentId)) {
        newSet.delete(componentId);
      } else {
        newSet.add(componentId);
      }
      return newSet;
    });
  };

  const toggleSelectAll = () => {
    if (selectedComponents.size === filteredComponents.length) {
      setSelectedComponents(new Set());
    } else {
      setSelectedComponents(new Set(filteredComponents.map(c => c.id)));
    }
  };

  const getStatusIcon = (status: ComponentStatus) => {
    switch (status) {
      case ComponentStatus.RUNNING:
        return <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>;
      case ComponentStatus.STOPPED:
        return <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>;
      case ComponentStatus.ERROR:
        return <div className="w-2 h-2 bg-error-500 rounded-full"></div>;
      case ComponentStatus.INSTALLING:
        return <div className="w-2 h-2 bg-warning-500 rounded-full animate-pulse"></div>;
      default:
        return <div className="w-2 h-2 bg-secondary-300 rounded-full"></div>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">加载组件列表...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">组件管理</h2>
          <p className="text-gray-600">管理 Elastic Stack 组件的安装、配置和运行</p>
        </div>
        <button onClick={onUploadClick} className="btn-primary">
          <Download className="h-4 w-4 mr-2" />
          上传组件
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-soft p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索组件名称或类型..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 form-input"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as ComponentStatus | 'ALL')}
              className="form-input"
            >
              <option value="ALL">所有状态</option>
              {Object.entries(ComponentStatusLabels).map(([value, label]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>

          {/* Type Filter */}
          <div className="sm:w-48">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value as ComponentType | 'ALL')}
              className="form-input"
            >
              <option value="ALL">所有类型</option>
              {Object.entries(ComponentTypeLabels).map(([value, label]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Batch Actions */}
      {selectedComponents.size > 0 && (
        <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-primary-900">
              已选择 {selectedComponents.size} 个组件
            </span>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBatchAction('start')}
                className="btn-success btn-sm"
              >
                <Play className="h-3 w-3 mr-1" />
                批量启动
              </button>
              <button
                onClick={() => handleBatchAction('stop')}
                className="btn-secondary btn-sm"
              >
                <Square className="h-3 w-3 mr-1" />
                批量停止
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Component List */}
      <div className="bg-white rounded-lg shadow-soft overflow-hidden">
        {filteredComponents.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Settings className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无组件</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? '没有找到匹配的组件' : '还没有上传任何组件'}
            </p>
            {!searchTerm && (
              <button onClick={onUploadClick} className="btn-primary">
                上传第一个组件
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="table">
              <thead className="table-header">
                <tr>
                  <th className="table-header-cell w-12">
                    <button
                      onClick={toggleSelectAll}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {selectedComponents.size === filteredComponents.length ? (
                        <CheckSquare className="h-4 w-4" />
                      ) : (
                        <UncheckedSquare className="h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th className="table-header-cell">组件信息</th>
                  <th className="table-header-cell">状态</th>
                  <th className="table-header-cell">端口</th>
                  <th className="table-header-cell">更新时间</th>
                  <th className="table-header-cell w-32">操作</th>
                </tr>
              </thead>
              <tbody className="table-body">
                {filteredComponents.map((component) => (
                  <ComponentRow
                    key={component.id}
                    component={component}
                    selected={selectedComponents.has(component.id)}
                    onSelect={() => toggleComponentSelection(component.id)}
                    onAction={handleComponentAction}
                    onView={() => onComponentSelect?.(component)}
                    loading={actionLoading.has(component.id)}
                  />
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

// Component Row Component
interface ComponentRowProps {
  component: ElasticComponent;
  selected: boolean;
  onSelect: () => void;
  onAction: (componentId: string, action: 'start' | 'stop' | 'restart' | 'uninstall') => void;
  onView: () => void;
  loading: boolean;
}

function ComponentRow({ component, selected, onSelect, onAction, onView, loading }: ComponentRowProps) {
  const [showActions, setShowActions] = useState(false);

  const getStatusIcon = (status: ComponentStatus) => {
    switch (status) {
      case ComponentStatus.RUNNING:
        return <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>;
      case ComponentStatus.STOPPED:
        return <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>;
      case ComponentStatus.ERROR:
        return <div className="w-2 h-2 bg-error-500 rounded-full"></div>;
      case ComponentStatus.INSTALLING:
        return <div className="w-2 h-2 bg-warning-500 rounded-full animate-pulse"></div>;
      default:
        return <div className="w-2 h-2 bg-secondary-300 rounded-full"></div>;
    }
  };

  return (
    <tr className="table-row">
      <td className="table-cell">
        <button
          onClick={onSelect}
          className="text-gray-400 hover:text-gray-600"
        >
          {selected ? (
            <CheckSquare className="h-4 w-4 text-primary-600" />
          ) : (
            <UncheckedSquare className="h-4 w-4" />
          )}
        </button>
      </td>
      <td className="table-cell">
        <div>
          <div className="font-medium text-gray-900">{component.name}</div>
          <div className="text-sm text-gray-500">
            {ComponentTypeLabels[component.type as ComponentType]} v{component.version}
          </div>
        </div>
      </td>
      <td className="table-cell">
        <div className="flex items-center space-x-2">
          {getStatusIcon(component.status)}
          <span className={`status-badge ${ComponentStatusColors[component.status]}`}>
            {ComponentStatusLabels[component.status]}
          </span>
        </div>
      </td>
      <td className="table-cell">
        {component.port || '-'}
      </td>
      <td className="table-cell">
        {new Date(component.updateTime).toLocaleString()}
      </td>
      <td className="table-cell">
        <div className="flex items-center space-x-1">
          <button
            onClick={onView}
            className="p-1 text-gray-400 hover:text-gray-600"
            title="查看详情"
          >
            <Eye className="h-4 w-4" />
          </button>
          
          {component.status === ComponentStatus.STOPPED && (
            <button
              onClick={() => onAction(component.id, 'start')}
              disabled={loading}
              className="p-1 text-success-600 hover:text-success-700"
              title="启动"
            >
              <Play className="h-4 w-4" />
            </button>
          )}
          
          {component.status === ComponentStatus.RUNNING && (
            <button
              onClick={() => onAction(component.id, 'stop')}
              disabled={loading}
              className="p-1 text-warning-600 hover:text-warning-700"
              title="停止"
            >
              <Square className="h-4 w-4" />
            </button>
          )}
          
          <button
            onClick={() => onAction(component.id, 'restart')}
            disabled={loading || component.status === ComponentStatus.INSTALLING}
            className="p-1 text-primary-600 hover:text-primary-700"
            title="重启"
          >
            <RotateCcw className="h-4 w-4" />
          </button>
          
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <MoreVertical className="h-4 w-4" />
            </button>
            
            {showActions && (
              <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10">
                <button
                  onClick={() => {
                    onAction(component.id, 'uninstall');
                    setShowActions(false);
                  }}
                  className="w-full px-4 py-2 text-left text-sm text-error-600 hover:bg-error-50"
                >
                  <Trash2 className="h-4 w-4 inline mr-2" />
                  卸载
                </button>
              </div>
            )}
          </div>
        </div>
      </td>
    </tr>
  );
}
