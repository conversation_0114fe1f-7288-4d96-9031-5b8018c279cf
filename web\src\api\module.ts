/**
 * 模块管理相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'

// 模块信息接口 - 匹配后端ModuleInitDTO
export interface ModuleInfo {
  moduleId: string
  moduleName: string
  description: string
  version: string
  author: string
  order: number
  apiPrefix: string
  features: string[]
  status: string
  initialized: boolean
  progress: number
  message?: string
  errorMessage?: string
  initializerClass?: string
  lastInitTime?: string
  manualInit?: boolean
  dependencies?: string[]
}

// 模块初始化结果接口 - 匹配后端ModuleInitResult
export interface ModuleInitResult {
  moduleId: string
  moduleName: string
  status: string
  progress: number
  message?: string
  errorMessage?: string
  startTime: number
  endTime?: number
  duration?: number
  details?: Record<string, any>
}

// 模块初始化请求参数 - 匹配后端ModuleInitRequestDTO
export interface ModuleInitRequest {
  tenantId: string
  forceReinit?: boolean
  initType?: 'BASIC' | 'FULL'
  async?: boolean
}

/**
 * 模块管理API类
 */
export class ModuleAPI {
  /**
   * 获取所有模块列表
   */
  static async getAllModules(): Promise<ApiResponse<ModuleInfo[]>> {
    return apiClient.get<ModuleInfo[]>('/api/system/modules')
  }

  /**
   * 获取模块信息
   */
  static async getModuleInfo(moduleId: string): Promise<ApiResponse<ModuleInfo>> {
    return apiClient.get<ModuleInfo>(`/api/system/modules/${moduleId}`)
  }

  /**
   * 刷新模块列表
   */
  static async refreshModules(): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/system/modules/refresh')
  }

  /**
   * 获取模块状态
   */
  static async getModuleStatus(moduleId: string): Promise<ApiResponse<ModuleInfo>> {
    return apiClient.get<ModuleInfo>(`/api/${moduleId}/init/status`)
  }

  /**
   * 初始化模块
   */
  static async initializeModule(
    moduleId: string,
    forceReinit = false
  ): Promise<ApiResponse<ModuleInitResult>> {
    return apiClient.post<ModuleInitResult>(`/api/${moduleId}/init/execute`, null, {
      params: {
        forceReinit
      }
    })
  }

  /**
   * 获取系统模块信息
   */
  static async getSystemModuleInfo(): Promise<ApiResponse<ModuleInfo>> {
    return apiClient.get<ModuleInfo>('/api/system/init/info')
  }

  /**
   * 获取AI模块信息
   */
  static async getAiModuleInfo(): Promise<ApiResponse<ModuleInfo>> {
    return apiClient.get<ModuleInfo>('/api/ai/init/info')
  }

  /**
   * 获取Dify模块信息
   */
  static async getDifyModuleInfo(): Promise<ApiResponse<ModuleInfo>> {
    return apiClient.get<ModuleInfo>('/api/dify/init/info')
  }

  /**
   * 获取智能体模块信息
   */
  static async getAgentModuleInfo(): Promise<ApiResponse<ModuleInfo>> {
    return apiClient.get<ModuleInfo>('/api/agent/init/info')
  }

  /**
   * 获取RAG模块信息
   */
  static async getRagModuleInfo(): Promise<ApiResponse<ModuleInfo>> {
    return apiClient.get<ModuleInfo>('/api/rag/init/info')
  }
}

// 导出便捷函数
export const getAllModules = ModuleAPI.getAllModules
export const getModuleInfo = ModuleAPI.getModuleInfo
export const refreshModules = ModuleAPI.refreshModules
export const getModuleStatus = ModuleAPI.getModuleStatus
export const initializeModule = ModuleAPI.initializeModule
export const getSystemModuleInfo = ModuleAPI.getSystemModuleInfo
export const getAiModuleInfo = ModuleAPI.getAiModuleInfo
export const getDifyModuleInfo = ModuleAPI.getDifyModuleInfo
export const getAgentModuleInfo = ModuleAPI.getAgentModuleInfo
export const getRagModuleInfo = ModuleAPI.getRagModuleInfo

export default {
  ModuleAPI
}
