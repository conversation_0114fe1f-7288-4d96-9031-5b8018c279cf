/**
 * 登录日志管理API接口
 */

import { apiClient } from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'
import type { PageResult } from '@/types/system'

/**
 * 登录日志查询DTO
 */
export interface SysLoginLogQueryDTO {
  current?: number
  size?: number
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
  username?: string
  loginIp?: string
  loginLocation?: string
  status?: string
  browser?: string
  os?: string
  sessionId?: string
  beginTime?: string
  endTime?: string
}

/**
 * 登录日志VO
 */
export interface SysLoginLogVO {
  id: string
  userId?: string
  username: string
  loginIp: string
  loginLocation?: string
  browser?: string
  os?: string
  status: string
  msg?: string
  loginTime: string
  logoutTime?: string
  sessionId: string
  tenantId?: string
  createdAt?: string
}

/**
 * 登录统计信息
 */
export interface LoginStats {
  totalLogins: number
  successLogins: number
  failedLogins: number
  activeUsers: number
}

/**
 * 登录日志管理API
 */
export class LoginLogAPI {
  /**
   * 分页查询登录日志列表
   */
  static async getLoginLogPage(queryDTO: SysLoginLogQueryDTO): Promise<ApiResponse<PageResult<SysLoginLogVO>>> {
    return apiClient.get('/api/system/login-log/page', queryDTO)
  }

  /**
   * 根据ID查询登录日志详情
   */
  static async getLoginLogById(id: string): Promise<ApiResponse<SysLoginLogVO>> {
    return apiClient.get(`/api/system/login-log/${id}`)
  }

  /**
   * 删除登录日志
   */
  static async deleteLoginLog(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/login-log/${id}`)
  }

  /**
   * 批量删除登录日志
   */
  static async deleteLoginLogBatch(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete('/api/system/login-log/batch', ids)
  }

  /**
   * 清理过期登录日志
   */
  static async cleanExpiredLogs(days: number): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/system/login-log/clean/${days}`)
  }

  /**
   * 获取登录统计信息
   */
  static async getLoginStats(): Promise<ApiResponse<LoginStats>> {
    return apiClient.get('/api/system/login-log/stats')
  }
}
