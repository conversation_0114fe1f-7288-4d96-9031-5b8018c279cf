package com.xhcai.modules.rag.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.rag.common.FileCommon;
import com.xhcai.modules.rag.config.RagProperties;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.DocumentSegment;
import com.xhcai.modules.rag.enums.DocumentStatus;
import com.xhcai.modules.rag.service.IDocumentProcessingService;
import com.xhcai.modules.rag.mapper.DocumentMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.sax.BodyContentHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 文档处理服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@DS("master")
public class DocumentProcessingServiceImpl implements IDocumentProcessingService {
    @Autowired
    private RagProperties ragProperties;

    @Autowired
    private DocumentMapper documentMapper;

    private final Tika tika = new Tika();
    private final AutoDetectParser parser = new AutoDetectParser();

    // 文本清理正则表达式
    private static final Pattern MULTIPLE_SPACES = Pattern.compile("\\s+");
    private static final Pattern MULTIPLE_NEWLINES = Pattern.compile("\\n{3,}");
    private static final Pattern SPECIAL_CHARS = Pattern.compile("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]");

    @Override
    public String parseDocument(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            log.info("开始解析文档: fileName={}, size={}", file.getOriginalFilename(), file.getSize());

            // 使用Tika解析文档
            String content = tika.parseToString(inputStream);

            log.info("文档解析完成: fileName={}, contentLength={}",
                    file.getOriginalFilename(), content.length());

            return content;
        } catch (IOException | TikaException e) {
            log.error("文档解析失败: fileName={}, error={}", file.getOriginalFilename(), e.getMessage());
            throw new BusinessException("文档解析失败: " + e.getMessage());
        }
    }

    @Override
    public String parseDocument(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                throw new BusinessException("文件不存在: " + filePath);
            }

            log.info("开始解析文档: filePath={}", filePath);

            String content = tika.parseToString(path);

            log.info("文档解析完成: filePath={}, contentLength={}", filePath, content.length());

            return content;
        } catch (IOException | TikaException e) {
            log.error("文档解析失败: filePath={}, error={}", filePath, e.getMessage());
            throw new BusinessException("文档解析失败: " + e.getMessage());
        }
    }

    @Override
    public List<String> splitDocument(String content, int chunkSize, int chunkOverlap) {
        if (!StringUtils.hasText(content)) {
            return Collections.emptyList();
        }

        log.info("开始文档分段: contentLength={}, chunkSize={}, chunkOverlap={}",
                content.length(), chunkSize, chunkOverlap);

        List<String> chunks = new ArrayList<>();

        // 首先按段落分割
        String[] paragraphs = content.split("\\n\\s*\\n");

        StringBuilder currentChunk = new StringBuilder();

        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            if (paragraph.isEmpty()) {
                continue;
            }

            // 如果当前段落加上新段落超过了分段大小
            if (currentChunk.length() + paragraph.length() + 1 > chunkSize) {
                // 保存当前分段
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());

                    // 处理重叠
                    if (chunkOverlap > 0 && currentChunk.length() > chunkOverlap) {
                        String overlap = currentChunk.substring(currentChunk.length() - chunkOverlap);
                        currentChunk = new StringBuilder(overlap);
                    } else {
                        currentChunk = new StringBuilder();
                    }
                }

                // 如果单个段落就超过了分段大小，需要进一步分割
                if (paragraph.length() > chunkSize) {
                    List<String> subChunks = splitLongParagraph(paragraph, chunkSize, chunkOverlap);
                    chunks.addAll(subChunks);
                } else {
                    currentChunk.append(paragraph);
                }
            } else {
                // 添加到当前分段
                if (currentChunk.length() > 0) {
                    currentChunk.append(FileCommon.LINE_SEPARATOR);
                }
                currentChunk.append(paragraph);
            }
        }

        // 添加最后一个分段
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        log.info("文档分段完成: 总分段数={}", chunks.size());

        return chunks;
    }

    @Override
    public List<DocumentSegment> processDocument(Document document, MultipartFile file) {
        log.info("开始处理文档: documentId={}, fileName={}",
                document.getId(), file.getOriginalFilename());

        try {
            // 1. 解析文档内容
            String content = parseDocument(file);

            // 2. 清理内容
            String cleanedContent = cleanContent(content);

            // 3. 文档分段
            List<String> chunks = splitDocument(cleanedContent,
                    ragProperties.getDocument().getChunkSize(),
                    ragProperties.getDocument().getChunkOverlap());

            // 4. 创建文档分段实体
            List<DocumentSegment> segments = new ArrayList<>();
            for (int i = 0; i < chunks.size(); i++) {
                DocumentSegment segment = new DocumentSegment();
                segment.setDocumentId(document.getId());
                segment.setDatasetId(document.getDatasetId());
                segment.setPosition(i + 1);
                segment.setContent(chunks.get(i));
                segment.setWordCount(chunks.get(i).length());
                segment.setTokens(estimateTokenCount(chunks.get(i)));
                segment.setStatus("waiting");
                segment.setEnabled(true);
                segment.setUpdateTime(LocalDateTime.now());

                segments.add(segment);
            }

            log.info("文档处理完成: documentId={}, segmentCount={}",
                    document.getId(), segments.size());

            return segments;

        } catch (Exception e) {
            log.error("文档处理失败: documentId={}, error={}", document.getId(), e.getMessage());
            throw new BusinessException("文档处理失败: " + e.getMessage());
        }
    }

    @Override
    @Async("documentProcessingExecutor")
    public void processDocumentAsync(String documentId, DocumentStatus documentStatus) {
        log.info("开始异步处理文档: documentId={}", documentId);

        try {
            // 更新文档状态为处理中
            updateDocumentStatus(documentId, "processing");

            // TODO: 实现完整的异步处理流程
            // 1. 获取文档信息
            // 2. 解析文档内容
            // 3. 文档分段
            // 4. 向量化
            // 5. 存储到向量数据库
            // 6. 更新文档状态
            log.info("异步文档处理完成: documentId={}", documentId);

        } catch (Exception e) {
            log.error("异步文档处理失败: documentId={}, error={}", documentId, e.getMessage());
            updateDocumentError(documentId, documentStatus, e.getMessage());
        }
    }

    @Override
    public String cleanContent(String content) {
        if (!StringUtils.hasText(content)) {
            return "";
        }

        // 移除特殊控制字符
        content = SPECIAL_CHARS.matcher(content).replaceAll("");

        // 规范化空白字符
        content = MULTIPLE_SPACES.matcher(content).replaceAll(" ");

        // 规范化换行符
        content = MULTIPLE_NEWLINES.matcher(content).replaceAll(FileCommon.LINE_SEPARATOR);

        // 移除首尾空白
        content = content.trim();

        return content;
    }

    @Override
    public Object extractMetadata(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            Metadata metadata = new Metadata();
            BodyContentHandler handler = new BodyContentHandler(-1);
            ParseContext context = new ParseContext();

            parser.parse(inputStream, handler, metadata, context);

            Map<String, Object> metadataMap = new HashMap<>();
            for (String name : metadata.names()) {
                metadataMap.put(name, metadata.get(name));
            }

            return metadataMap;

        } catch (IOException | SAXException | TikaException e) {
            log.error("提取文档元数据失败: fileName={}, error={}", file.getOriginalFilename(), e.getMessage());
            return Collections.emptyMap();
        }
    }

    @Override
    public String detectLanguage(String content) {
        // 简单的语言检测逻辑，可以集成更专业的语言检测库
        if (!StringUtils.hasText(content)) {
            return "unknown";
        }

        // 检测中文字符
        long chineseCharCount = content.chars()
                .filter(ch -> Character.UnicodeScript.of(ch) == Character.UnicodeScript.HAN)
                .count();

        if (chineseCharCount > content.length() * 0.3) {
            return "zh";
        }

        return "en";
    }

    @Override
    public Object calculateStats(String content) {
        Map<String, Object> stats = new HashMap<>();

        if (!StringUtils.hasText(content)) {
            stats.put("charCount", 0);
            stats.put("wordCount", 0);
            stats.put("paragraphCount", 0);
            stats.put("lineCount", 0);
            return stats;
        }

        stats.put("charCount", content.length());
        stats.put("wordCount", content.split("\\s+").length);
        stats.put("paragraphCount", content.split("\\n\\s*\\n").length);
        stats.put("lineCount", content.split("\\n").length);

        return stats;
    }

    @Override
    public boolean isSupportedFormat(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String filename = file.getOriginalFilename();
        if (!StringUtils.hasText(filename)) {
            return false;
        }

        String extension = getFileExtension(filename).toLowerCase();
        return ragProperties.getDocument().getAllowedTypes().contains(extension);
    }

    @Override
    public Object getProcessingProgress(String documentId) {
        // TODO: 实现处理进度查询
        Document document = documentMapper.selectById(documentId);
        if (document == null) {
            return null;
        }

        Map<String, Object> progress = new HashMap<>();
        progress.put("documentId", documentId);
        progress.put("status", document.getDocumentStatus());
        progress.put("progressPercent", calculateProgressPercent(document.getDocumentStatus()));
        progress.put("wordCount", document.getWordCount());
        progress.put("tokens", document.getTokens());
        return progress;
    }

    @Override
    public boolean cancelProcessing(String documentId) {
        // TODO: 实现处理取消逻辑
        log.info("取消文档处理: documentId={}", documentId);
        return updateDocumentStatus(documentId, "cancelled");
    }

    /**
     * 分割长段落
     */
    private List<String> splitLongParagraph(String paragraph, int chunkSize, int chunkOverlap) {
        List<String> chunks = new ArrayList<>();

        for (int i = 0; i < paragraph.length(); i += chunkSize - chunkOverlap) {
            int end = Math.min(i + chunkSize, paragraph.length());
            chunks.add(paragraph.substring(i, end));

            if (end >= paragraph.length()) {
                break;
            }
        }

        return chunks;
    }

    /**
     * 估算token数量
     */
    private int estimateTokenCount(String content) {
        if (!StringUtils.hasText(content)) {
            return 0;
        }
        // 简单的token估算，中文大约1个字符=1个token，英文大约4个字符=1个token
        return (int) Math.ceil(content.length() * 0.75);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 更新文档状态
     */
    private boolean updateDocumentStatus(String documentId, String status) {
        Document document = new Document();
        document.setId(documentId);
        document.setDocumentStatus(status);
        document.setUpdateTime(LocalDateTime.now());

        if ("processing".equals(status)) {
            document.setUpdateTime(LocalDateTime.now());
        } else if ("completed".equals(status)) {
            document.setCompletedAt(LocalDateTime.now());
        } else if ("error".equals(status) || "cancelled".equals(status)) {
            document.setStoppedAt(LocalDateTime.now());
        }

        return documentMapper.updateById(document) > 0;
    }

    /**
     * 更新文档错误信息
     */
    private boolean updateDocumentError(String documentId, DocumentStatus documentStatus, String error) {
        Document document = new Document();
        document.setId(documentId);
        document.setDocumentStatus(documentStatus.getCode());
        document.setError(error);
        document.setStoppedAt(LocalDateTime.now());
        document.setUpdateTime(LocalDateTime.now());
        return documentMapper.updateById(document) > 0;
    }

    /**
     * 计算处理进度百分比
     */
    private int calculateProgressPercent(String status) {
        if (status == null) {
            return 0;
        }

        switch (status) {
            case "waiting":
                return 0;
            case "processing":
                return 50;
            case "completed":
                return 100;
            case "error":
            case "cancelled":
                return 0;
            default:
                return 0;
        }
    }
}
