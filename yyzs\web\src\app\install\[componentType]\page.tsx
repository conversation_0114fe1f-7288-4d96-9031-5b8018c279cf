'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { 
  ArrowLeft, 
  Upload, 
  Settings, 
  Play, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  FileText,
  Server
} from 'lucide-react';
import { ComponentType, ComponentTypeLabels, ComponentUploadRequest } from '@/types/component';
import ComponentsAPI from '@/api/components';
import FileUpload from '@/components/FileUpload';
import ComponentConfigEditor from '@/components/ComponentConfigEditor';
import InstallProgress from '@/components/InstallProgress';
import toast from 'react-hot-toast';

// 安装步骤
enum InstallStep {
  UPLOAD = 'upload',
  CONFIGURE = 'configure', 
  INSTALL = 'install',
  COMPLETE = 'complete'
}

const STEP_LABELS = {
  [InstallStep.UPLOAD]: '上传安装包',
  [InstallStep.CONFIGURE]: '配置参数',
  [InstallStep.INSTALL]: '安装组件',
  [InstallStep.COMPLETE]: '安装完成'
};

export default function ComponentInstallPage() {
  const router = useRouter();
  const params = useParams();
  const componentType = params.componentType as ComponentType;

  const [currentStep, setCurrentStep] = useState<InstallStep>(InstallStep.UPLOAD);
  const [loading, setLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [componentId, setComponentId] = useState<string>('');
  const [version, setVersion] = useState('');
  const [description, setDescription] = useState('');
  const [config, setConfig] = useState<Record<string, any>>({});
  const [installProgress, setInstallProgress] = useState(0);

  // 验证组件类型
  useEffect(() => {
    if (!componentType || !ComponentTypeLabels[componentType]) {
      router.push('/install');
      return;
    }
  }, [componentType, router]);

  // 文件上传处理
  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // 验证文件类型
    const allowedExtensions = ['.tar.gz', '.tgz', '.zip'];
    const fileName = file.name.toLowerCase();
    const isValidFile = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    if (!isValidFile) {
      toast.error('请上传 .tar.gz、.tgz 或 .zip 格式的文件');
      return;
    }

    // 验证文件大小 (最大500MB)
    const maxSize = 500 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('文件大小不能超过 500MB');
      return;
    }

    setUploadedFile(file);
    
    // 自动提取版本号（从文件名）
    const versionMatch = fileName.match(/(\d+\.\d+\.\d+)/);
    if (versionMatch) {
      setVersion(versionMatch[1]);
    }
  };

  // 上传安装包
  const handleUploadPackage = async () => {
    if (!uploadedFile || !version) {
      toast.error('请选择文件并输入版本号');
      return;
    }

    setLoading(true);
    try {
      const uploadRequest: ComponentUploadRequest = {
        file: uploadedFile,
        componentType,
        version,
        description
      };

      const response = await ComponentsAPI.uploadPackage(uploadRequest);
      if (response.success && response.data) {
        setComponentId(response.data.componentId);
        setCurrentStep(InstallStep.CONFIGURE);
        toast.success('安装包上传成功');
      } else {
        toast.error(response.message || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      toast.error('上传失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 安装组件
  const handleInstallComponent = async () => {
    if (!componentId) return;

    setLoading(true);
    setCurrentStep(InstallStep.INSTALL);

    try {
      // 模拟安装进度
      const progressInterval = setInterval(() => {
        setInstallProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 500);

      const response = await ComponentsAPI.installComponent(componentId, config);

      clearInterval(progressInterval);
      setInstallProgress(100);

      if (response.success) {
        setTimeout(() => {
          setCurrentStep(InstallStep.COMPLETE);
          toast.success('组件安装成功');
        }, 1000);
      } else {
        toast.error(response.message || '安装失败');
        setCurrentStep(InstallStep.CONFIGURE);
        setInstallProgress(0);
      }
    } catch (error) {
      console.error('安装失败:', error);
      toast.error('安装失败，请重试');
      setCurrentStep(InstallStep.CONFIGURE);
      setInstallProgress(0);
    } finally {
      setLoading(false);
    }
  };

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    const steps = Object.values(InstallStep);
    const currentIndex = steps.indexOf(currentStep);

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => (
          <div key={step} className="flex items-center">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
              index <= currentIndex 
                ? 'bg-primary-600 border-primary-600 text-white' 
                : 'border-gray-300 text-gray-400'
            }`}>
              {index < currentIndex ? (
                <CheckCircle className="h-5 w-5" />
              ) : (
                <span className="text-sm font-medium">{index + 1}</span>
              )}
            </div>
            <span className={`ml-2 text-sm font-medium ${
              index <= currentIndex ? 'text-gray-900' : 'text-gray-400'
            }`}>
              {STEP_LABELS[step]}
            </span>
            {index < steps.length - 1 && (
              <div className={`w-12 h-0.5 mx-4 ${
                index < currentIndex ? 'bg-primary-600' : 'bg-gray-300'
              }`} />
            )}
          </div>
        ))}
      </div>
    );
  };

  if (!componentType || !ComponentTypeLabels[componentType]) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="btn-outline mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </button>
              <div className="flex items-center">
                <Server className="h-8 w-8 text-primary-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    安装 {ComponentTypeLabels[componentType]}
                  </h1>
                  <p className="text-sm text-gray-500">按照步骤完成组件安装</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderStepIndicator()}

        <div className="card">
          <div className="card-body">
            {/* 上传步骤 */}
            {currentStep === InstallStep.UPLOAD && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  上传 {ComponentTypeLabels[componentType]} 安装包
                </h3>
                
                <div className="space-y-4">
                  {/* 文件上传区域 */}
                  <div>
                    <label className="form-label">安装包文件</label>
                    <FileUpload
                      onFileSelect={handleFileUpload}
                      className="mt-1"
                    />
                  </div>

                  {/* 版本号 */}
                  <div>
                    <label className="form-label">版本号 *</label>
                    <input
                      type="text"
                      value={version}
                      onChange={(e) => setVersion(e.target.value)}
                      placeholder="例如: 8.11.0"
                      className="form-input"
                      required
                    />
                  </div>

                  {/* 描述 */}
                  <div>
                    <label className="form-label">描述 (可选)</label>
                    <textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="组件描述信息..."
                      rows={3}
                      className="form-input"
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-6">
                  <button
                    onClick={handleUploadPackage}
                    disabled={!uploadedFile || !version || loading}
                    className="btn-primary"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        上传中...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        上传安装包
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* 配置步骤 */}
            {currentStep === InstallStep.CONFIGURE && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  配置 {ComponentTypeLabels[componentType]}
                </h3>
                
                <div className="space-y-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <AlertCircle className="h-5 w-5 text-blue-600 mr-2" />
                      <p className="text-sm text-blue-800">
                        安装包已上传成功，现在可以配置组件参数。如果使用默认配置，可以直接点击安装。
                      </p>
                    </div>
                  </div>

                  <ComponentConfigEditor
                    componentType={componentType}
                    config={config}
                    onChange={setConfig}
                  />
                </div>

                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => setCurrentStep(InstallStep.UPLOAD)}
                    className="btn-outline"
                  >
                    上一步
                  </button>
                  <button
                    onClick={handleInstallComponent}
                    disabled={loading}
                    className="btn-primary"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    开始安装
                  </button>
                </div>
              </div>
            )}

            {/* 安装步骤 */}
            {currentStep === InstallStep.INSTALL && (
              <InstallProgress
                componentName={ComponentTypeLabels[componentType]}
                progress={installProgress}
                onComplete={() => {
                  setCurrentStep(InstallStep.COMPLETE);
                  toast.success('组件安装成功');
                }}
                onError={(error) => {
                  toast.error(`安装失败: ${error}`);
                  setCurrentStep(InstallStep.CONFIGURE);
                }}
              />
            )}

            {/* 完成步骤 */}
            {currentStep === InstallStep.COMPLETE && (
              <div>
                <div className="text-center">
                  <CheckCircle className="h-16 w-16 text-success-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {ComponentTypeLabels[componentType]} 安装成功！
                  </h3>
                  <p className="text-gray-600 mb-6">
                    组件已成功安装并配置完成
                  </p>
                </div>

                <div className="flex justify-center space-x-4">
                  <button
                    onClick={() => router.push('/')}
                    className="btn-outline"
                  >
                    返回首页
                  </button>
                  <button
                    onClick={() => router.push('/install')}
                    className="btn-primary"
                  >
                    安装其他组件
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
