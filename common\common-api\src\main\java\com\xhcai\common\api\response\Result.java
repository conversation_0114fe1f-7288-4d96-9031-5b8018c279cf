package com.xhcai.common.api.response;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.xhcai.common.core.enums.ResultCode;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 统一响应结果
 *
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "统一响应结果")
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    @Schema(description = "响应码", example = "200")
    private Integer code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "操作成功")
    private String message;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private T data;

    /**
     * 响应时间
     */
    @Schema(description = "响应时间")
    private LocalDateTime timestamp;

    /**
     * 请求路径
     */
    @Schema(description = "请求路径")
    private String path;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    public Result() {
        this.timestamp = LocalDateTime.now();
    }

    public Result(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
        this.success = ResultCode.SUCCESS.getCode().equals(code);
    }

    public Result(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    public Result(ResultCode resultCode) {
        this(resultCode.getCode(), resultCode.getMessage());
    }

    public Result(ResultCode resultCode, T data) {
        this(resultCode.getCode(), resultCode.getMessage(), data);
    }

    /**
     * 成功响应
     *
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultCode.SUCCESS);
    }

    /**
     * 成功响应
     *
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResultCode.SUCCESS, data);
    }

    /**
     * 成功响应
     *
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message);
    }

    /**
     * 成功响应
     *
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 成功响应
     *
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> ok() {
        return new Result<>(ResultCode.SUCCESS);
    }

    /**
     * 成功响应
     *
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> ok(T data) {
        return new Result<>(ResultCode.SUCCESS, data);
    }

    /**
     * 成功响应
     *
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> ok(String message) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message);
    }

    /**
     * 成功响应
     *
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> ok(String message, T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 失败响应
     *
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> fail() {
        return new Result<>(ResultCode.FAIL);
    }

    /**
     * 失败响应
     *
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> fail(String message) {
        return new Result<>(ResultCode.FAIL.getCode(), message);
    }

    /**
     * 失败响应
     *
     * @param code 响应码
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> fail(Integer code, String message) {
        return new Result<>(code, message);
    }

    /**
     * 失败响应
     *
     * @param resultCode 响应码枚举
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> fail(ResultCode resultCode) {
        return new Result<>(resultCode);
    }

    /**
     * 失败响应
     *
     * @param resultCode 响应码枚举
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> fail(ResultCode resultCode, T data) {
        return new Result<>(resultCode, data);
    }

    /**
     * 失败响应
     *
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> error() {
        return new Result<>(ResultCode.FAIL);
    }

    /**
     * 失败响应
     *
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(ResultCode.FAIL.getCode(), message);
    }

    /**
     * 失败响应
     *
     * @param code 响应码
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message);
    }

    /**
     * 失败响应
     *
     * @param resultCode 响应码枚举
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return new Result<>(resultCode);
    }

    /**
     * 失败响应
     *
     * @param resultCode 响应码枚举
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> error(ResultCode resultCode, T data) {
        return new Result<>(resultCode, data);
    }


    /**
     * 根据条件返回成功或失败
     *
     * @param condition 条件
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> status(boolean condition) {
        return condition ? success() : fail();
    }

    /**
     * 根据条件返回成功或失败
     *
     * @param condition 条件
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应结果
     */
    public static <T> Result<T> status(boolean condition, T data) {
        return condition ? success(data) : fail();
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isFail() {
        return !isSuccess();
    }

    // Getters and Setters
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
        this.success = ResultCode.SUCCESS.getCode().equals(code);
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    @Override
    public String toString() {
        return "Result{"
                + "code=" + code
                + ", message='" + message + '\''
                + ", data=" + data
                + ", timestamp=" + timestamp
                + ", path='" + path + '\''
                + ", success=" + success
                + '}';
    }
}
