package com.xhcai.modules.agent.service;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.agent.dto.AgentWorkflowHistoryCreateDTO;
import com.xhcai.modules.agent.dto.AgentWorkflowHistoryQueryDTO;
import com.xhcai.modules.agent.entity.AgentWorkflow;
import com.xhcai.modules.agent.entity.AgentWorkflowHistory;
import com.xhcai.modules.agent.vo.AgentWorkflowHistoryVO;
import com.xhcai.modules.agent.vo.HistoryStatsVO;

/**
 * 智能体工作流历史记录服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IAgentWorkflowHistoryService extends IService<AgentWorkflowHistory> {

    /**
     * 分页查询工作流历史记录
     *
     * @param queryDTO 查询条件
     * @return 历史记录分页列表
     */
    IPage<AgentWorkflowHistoryVO> getHistoryPage(AgentWorkflowHistoryQueryDTO queryDTO);

    /**
     * 根据工作流ID查询历史记录列表
     *
     * @param workflowId 工作流ID
     * @return 历史记录列表
     */
    List<AgentWorkflowHistoryVO> getHistoryByWorkflowId(String workflowId);

    /**
     * 根据智能体ID查询历史记录列表
     *
     * @param agentId 智能体ID
     * @return 历史记录列表
     */
    List<AgentWorkflowHistoryVO> getHistoryByAgentId(String agentId);

    /**
     * 根据配置哈希值查询历史记录
     *
     * @param configHash 配置哈希值
     * @return 历史记录
     */
    AgentWorkflowHistoryVO getHistoryByConfigHash(String configHash);

    /**
     * 查询最近的历史记录
     *
     * @param workflowId 工作流ID
     * @param limit 限制数量
     * @return 最近的历史记录列表
     */
    List<AgentWorkflowHistoryVO> getRecentHistory(String workflowId, Integer limit);

    /**
     * 保存工作流历史记录
     *
     * @param createDTO 创建信息
     * @return 历史记录ID
     */
    String saveHistory(AgentWorkflowHistoryCreateDTO createDTO);

    /**
     * 从工作流实体保存历史记录
     *
     * @param workflow 工作流实体
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param changeSummary 变更摘要
     * @param isMajorChange 是否为重要变更
     * @return 历史记录ID，如果配置未变更则返回null
     */
    String saveHistoryFromWorkflow(AgentWorkflow workflow, String operationType,
            String operationDesc, String changeSummary, Boolean isMajorChange);

    /**
     * 计算工作流配置的哈希值
     *
     * @param workflow 工作流实体
     * @return 配置哈希值
     */
    String calculateConfigHash(AgentWorkflow workflow);

    /**
     * 计算配置内容的哈希值
     *
     * @param workflowConfig 工作流配置（已废弃，传null）
     * @param nodesData 节点数据
     * @param edgesData 边数据
     * @param globalVariables 全局变量配置
     * @return 配置哈希值
     */
    String calculateConfigHash(String workflowConfig, String nodesData,
            String edgesData, String globalVariables);

    /**
     * 检查配置是否已存在
     *
     * @param configHash 配置哈希值
     * @return 是否存在
     */
    boolean isConfigExists(String configHash);

    /**
     * 统计工作流历史记录数量
     *
     * @param workflowId 工作流ID
     * @return 历史记录数量
     */
    Long countHistoryByWorkflowId(String workflowId);

    /**
     * 统计智能体历史记录数量
     *
     * @param agentId 智能体ID
     * @return 历史记录数量
     */
    Long countHistoryByAgentId(String agentId);

    /**
     * 统计指定时间范围内的历史记录数量
     *
     * @param workflowId 工作流ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 历史记录数量
     */
    Long countHistoryByTimeRange(String workflowId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询重要变更的历史记录
     *
     * @param workflowId 工作流ID
     * @return 重要变更历史记录列表
     */
    List<AgentWorkflowHistoryVO> getMajorChanges(String workflowId);

    /**
     * 查询指定用户的操作历史
     *
     * @param workflowId 工作流ID
     * @param userId 用户ID
     * @return 用户操作历史列表
     */
    List<AgentWorkflowHistoryVO> getHistoryByUser(String workflowId, String userId);

    /**
     * 查询指定操作类型的历史记录
     *
     * @param workflowId 工作流ID
     * @param operationType 操作类型
     * @return 操作历史列表
     */
    List<AgentWorkflowHistoryVO> getHistoryByOperationType(String workflowId, String operationType);

    /**
     * 删除指定时间之前的历史记录
     *
     * @param beforeTime 时间点
     * @return 删除的记录数量
     */
    int deleteHistoryBeforeTime(LocalDateTime beforeTime);

    /**
     * 查询历史记录统计信息
     *
     * @param agentId 智能体ID
     * @return 统计信息
     */
    HistoryStatsVO getHistoryStats(String agentId);

    /**
     * 对比两个历史记录的差异
     *
     * @param fromHistoryId 源历史记录ID
     * @param toHistoryId 目标历史记录ID
     * @return 差异信息
     */
    HistoryCompareVO compareHistory(String fromHistoryId, String toHistoryId);

    /**
     * 生成变更摘要
     *
     * @param oldWorkflow 旧工作流配置
     * @param newWorkflow 新工作流配置
     * @return 变更摘要
     */
    String generateChangeSummary(AgentWorkflow oldWorkflow, AgentWorkflow newWorkflow);

    /**
     * 判断是否为重要变更
     *
     * @param oldWorkflow 旧工作流配置
     * @param newWorkflow 新工作流配置
     * @return 是否为重要变更
     */
    boolean isMajorChange(AgentWorkflow oldWorkflow, AgentWorkflow newWorkflow);

    /**
     * 历史记录对比VO
     */
    class HistoryCompareVO {

        private AgentWorkflowHistoryVO fromHistory;
        private AgentWorkflowHistoryVO toHistory;
        private List<String> differences;
        private String summary;

        // Getters and Setters
        public AgentWorkflowHistoryVO getFromHistory() {
            return fromHistory;
        }

        public void setFromHistory(AgentWorkflowHistoryVO fromHistory) {
            this.fromHistory = fromHistory;
        }

        public AgentWorkflowHistoryVO getToHistory() {
            return toHistory;
        }

        public void setToHistory(AgentWorkflowHistoryVO toHistory) {
            this.toHistory = toHistory;
        }

        public List<String> getDifferences() {
            return differences;
        }

        public void setDifferences(List<String> differences) {
            this.differences = differences;
        }

        public String getSummary() {
            return summary;
        }

        public void setSummary(String summary) {
            this.summary = summary;
        }
    }
}
