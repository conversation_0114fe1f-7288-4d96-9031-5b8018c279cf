<template>
  <div class="recent-activities">
    <div class="section-header">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">最近活动</h2>
      <p class="text-gray-600">查看您最近的平台使用活动记录</p>
    </div>

    <div class="space-y-6 mt-6">
      <!-- 活动筛选 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <label class="text-sm font-medium text-gray-600">时间范围：</label>
            <select v-model="timeFilter" @change="filterActivities" class="px-3 py-1 border border-gray-300 rounded text-sm">
              <option value="today">今天</option>
              <option value="week">最近一周</option>
              <option value="month">最近一月</option>
              <option value="all">全部</option>
            </select>
          </div>
          
          <div class="flex items-center gap-2">
            <label class="text-sm font-medium text-gray-600">活动类型：</label>
            <select v-model="typeFilter" @change="filterActivities" class="px-3 py-1 border border-gray-300 rounded text-sm">
              <option value="">全部类型</option>
              <option value="conversation">对话</option>
              <option value="agent">智能体</option>
              <option value="knowledge">知识库</option>
              <option value="login">登录</option>
              <option value="upload">上传</option>
            </select>
          </div>

          <div class="flex items-center gap-2">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索活动..."
              size="small"
              style="width: 200px"
              @input="filterActivities"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </div>

      <!-- 活动时间线 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-6 flex items-center gap-2">
          <el-icon class="text-blue-500"><Clock /></el-icon>
          活动时间线
        </h3>

        <div class="space-y-4">
          <div v-for="activity in filteredActivities" :key="activity.id" class="flex items-start gap-4 p-4 hover:bg-gray-50 rounded-lg transition-colors">
            <!-- 活动图标 -->
            <div class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-lg" :class="getActivityIconClass(activity.type)">
              {{ getActivityIcon(activity.type) }}
            </div>
            
            <!-- 活动内容 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-800 truncate">{{ activity.title }}</h4>
                <span class="text-xs text-gray-500 flex-shrink-0 ml-2">{{ formatTime(activity.time) }}</span>
              </div>
              <p class="text-sm text-gray-600 mt-1">{{ activity.description }}</p>
              
              <!-- 活动详情 -->
              <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                <span v-if="activity.ip">IP: {{ activity.ip }}</span>
                <span v-if="activity.device">设备: {{ activity.device }}</span>
                <span v-if="activity.location">位置: {{ activity.location }}</span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" :class="getStatusClass(activity.status)">
                  {{ activity.status }}
                </span>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredActivities.length === 0" class="text-center py-12">
            <el-icon class="text-4xl text-gray-400 mb-4"><DocumentRemove /></el-icon>
            <p class="text-gray-500">暂无符合条件的活动记录</p>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="filteredActivities.length > 0" class="text-center mt-6">
          <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm">
            加载更多
          </button>
        </div>
      </div>

      <!-- 活动统计 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white/90 backdrop-blur-xl rounded-xl shadow-lg border border-gray-200/50 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">今日活动</p>
              <p class="text-2xl font-bold text-blue-600">{{ todayActivities }}</p>
            </div>
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <el-icon class="text-blue-600"><Calendar /></el-icon>
            </div>
          </div>
        </div>

        <div class="bg-white/90 backdrop-blur-xl rounded-xl shadow-lg border border-gray-200/50 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">本周活动</p>
              <p class="text-2xl font-bold text-green-600">{{ weekActivities }}</p>
            </div>
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <el-icon class="text-green-600"><TrendCharts /></el-icon>
            </div>
          </div>
        </div>

        <div class="bg-white/90 backdrop-blur-xl rounded-xl shadow-lg border border-gray-200/50 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">最常用功能</p>
              <p class="text-lg font-bold text-purple-600">AI对话</p>
            </div>
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <el-icon class="text-purple-600"><ChatDotRound /></el-icon>
            </div>
          </div>
        </div>

        <div class="bg-white/90 backdrop-blur-xl rounded-xl shadow-lg border border-gray-200/50 p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">活跃时段</p>
              <p class="text-lg font-bold text-orange-600">14:00-16:00</p>
            </div>
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <el-icon class="text-orange-600"><Timer /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Clock, Search, DocumentRemove, Calendar, TrendCharts, ChatDotRound, Timer } from '@element-plus/icons-vue'

interface Activity {
  id: string
  type: 'conversation' | 'agent' | 'knowledge' | 'login' | 'upload'
  title: string
  description: string
  time: Date
  status: string
  ip?: string
  device?: string
  location?: string
}

// 筛选条件
const timeFilter = ref('week')
const typeFilter = ref('')
const searchKeyword = ref('')

// 活动数据
const activities = ref<Activity[]>([
  {
    id: '1',
    type: 'conversation',
    title: '开始了新的AI对话',
    description: '与智能助手讨论技术问题，获得了详细的解答',
    time: new Date(Date.now() - 1000 * 60 * 30),
    status: '成功',
    ip: '*************',
    device: 'Chrome 浏览器',
    location: '北京'
  },
  {
    id: '2',
    type: 'agent',
    title: '使用了代码生成助手',
    description: '生成了一个Vue组件的基础代码结构',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2),
    status: '成功',
    ip: '*************',
    device: 'Chrome 浏览器',
    location: '北京'
  },
  {
    id: '3',
    type: 'knowledge',
    title: '查询了技术文档知识库',
    description: '搜索了关于Vue 3组合式API的相关文档',
    time: new Date(Date.now() - 1000 * 60 * 60 * 4),
    status: '成功',
    ip: '*************',
    device: 'Chrome 浏览器',
    location: '北京'
  },
  {
    id: '4',
    type: 'upload',
    title: '上传了技术文档',
    description: '向知识库上传了新的技术规范文档',
    time: new Date(Date.now() - 1000 * 60 * 60 * 6),
    status: '失败',
    ip: '*************',
    device: 'Chrome 浏览器',
    location: '北京'
  },
  {
    id: '5',
    type: 'login',
    title: '登录系统',
    description: '通过用户名密码成功登录系统',
    time: new Date(Date.now() - 1000 * 60 * 60 * 24),
    status: '成功',
    ip: '*************',
    device: 'Chrome 浏览器',
    location: '北京'
  }
])

// 统计数据
const todayActivities = ref(3)
const weekActivities = ref(15)

// 筛选后的活动
const filteredActivities = computed(() => {
  let filtered = activities.value

  // 时间筛选
  const now = new Date()
  if (timeFilter.value === 'today') {
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    filtered = filtered.filter(activity => activity.time >= today)
  } else if (timeFilter.value === 'week') {
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    filtered = filtered.filter(activity => activity.time >= weekAgo)
  } else if (timeFilter.value === 'month') {
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    filtered = filtered.filter(activity => activity.time >= monthAgo)
  }

  // 类型筛选
  if (typeFilter.value) {
    filtered = filtered.filter(activity => activity.type === typeFilter.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(activity => 
      activity.title.toLowerCase().includes(keyword) ||
      activity.description.toLowerCase().includes(keyword)
    )
  }

  return filtered.sort((a, b) => b.time.getTime() - a.time.getTime())
})

const filterActivities = () => {
  // 触发计算属性重新计算
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'conversation': '💬',
    'agent': '🤖',
    'knowledge': '📖',
    'login': '🔐',
    'upload': '📄'
  }
  return iconMap[type] || '📝'
}

const getActivityIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    'conversation': 'bg-blue-100 text-blue-600',
    'agent': 'bg-green-100 text-green-600',
    'knowledge': 'bg-purple-100 text-purple-600',
    'login': 'bg-orange-100 text-orange-600',
    'upload': 'bg-yellow-100 text-yellow-600'
  }
  return classMap[type] || 'bg-gray-100 text-gray-600'
}

const getStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    '成功': 'bg-green-100 text-green-800',
    '失败': 'bg-red-100 text-red-800',
    '处理中': 'bg-yellow-100 text-yellow-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.section-header {
  margin-bottom: 1.5rem;
}
</style>
