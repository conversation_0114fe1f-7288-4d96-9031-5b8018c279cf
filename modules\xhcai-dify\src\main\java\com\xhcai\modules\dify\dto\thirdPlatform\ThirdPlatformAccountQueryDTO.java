package com.xhcai.modules.dify.dto.thirdPlatform;

import com.xhcai.common.api.dto.PageTimeRangeQueryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 用户第三方智能体账号查询DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "用户第三方智能体账号查询DTO")
public class ThirdPlatformAccountQueryDTO extends PageTimeRangeQueryDTO {

    /**
     * 第三方平台ID（字典值）
     */
    @Schema(description = "第三方平台ID", example = "dify")
    @Size(max = 50, message = "第三方平台ID长度不能超过50个字符")
    private String platformId;

    /**
     * 账号名称（模糊查询）
     */
    @Schema(description = "账号名称", example = "Dify")
    @Size(max = 100, message = "账号名称长度不能超过100个字符")
    private String accountName;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 最后连接测试结果：0-失败，1-成功
     */
    @Schema(description = "最后连接测试结果：0-失败，1-成功", example = "1")
    private Integer lastTestResult;

    // Getters and Setters
    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getLastTestResult() {
        return lastTestResult;
    }

    public void setLastTestResult(Integer lastTestResult) {
        this.lastTestResult = lastTestResult;
    }

    @Override
    public String toString() {
        return "ThirdPlatformAccountQueryDTO{" +
                "platformId='" + platformId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", status=" + status +
                ", lastTestResult=" + lastTestResult +
                ", " + super.toString() +
                '}';
    }
}
