package com.xhcai.modules.dify.dto.knowledge;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 知识库DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyKnowledgeBaseDTO {

    /**
     * 知识库ID
     */
    private String id;

    /**
     * 知识库名称
     */
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 100, message = "知识库名称长度不能超过100个字符")
    private String name;

    /**
     * 知识库描述
     */
    @Size(max = 500, message = "知识库描述长度不能超过500个字符")
    private String description;

    /**
     * 知识库图标
     */
    private String icon;

    /**
     * 知识库状态：active-活跃, inactive-非活跃
     */
    private String status;

    /**
     * 权限设置：private-私有, public-公开, team-团队
     */
    private String permission;

    /**
     * 嵌入模型配置
     */
    @JsonProperty("embedding_model")
    private EmbeddingModel embeddingModel;

    /**
     * 检索配置
     */
    @JsonProperty("retrieval_config")
    private RetrievalConfig retrievalConfig;

    /**
     * 文档数量
     */
    @JsonProperty("document_count")
    private Integer documentCount;

    /**
     * 字符数量
     */
    @JsonProperty("character_count")
    private Long characterCount;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    @JsonProperty("created_by")
    private String createdBy;

    /**
     * 扩展属性
     */
    private Map<String, Object> metadata;

    /**
     * 嵌入模型配置
     */
    public static class EmbeddingModel {
        private String provider;
        private String model;
        private Integer dimensions;
        @JsonProperty("max_tokens")
        private Integer maxTokens;

        // Getters and Setters
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public Integer getDimensions() { return dimensions; }
        public void setDimensions(Integer dimensions) { this.dimensions = dimensions; }
        public Integer getMaxTokens() { return maxTokens; }
        public void setMaxTokens(Integer maxTokens) { this.maxTokens = maxTokens; }
    }

    /**
     * 检索配置
     */
    public static class RetrievalConfig {
        @JsonProperty("search_method")
        private String searchMethod; // vector, keyword, hybrid

        @JsonProperty("top_k")
        private Integer topK;

        @JsonProperty("score_threshold")
        private Double scoreThreshold;

        @JsonProperty("rerank_enable")
        private Boolean rerankEnable;

        @JsonProperty("rerank_model")
        private String rerankModel;

        @JsonProperty("weights")
        private SearchWeights weights;

        public static class SearchWeights {
            @JsonProperty("vector_search")
            private Double vectorSearch;
            @JsonProperty("keyword_search")
            private Double keywordSearch;

            // Getters and Setters
            public Double getVectorSearch() { return vectorSearch; }
            public void setVectorSearch(Double vectorSearch) { this.vectorSearch = vectorSearch; }
            public Double getKeywordSearch() { return keywordSearch; }
            public void setKeywordSearch(Double keywordSearch) { this.keywordSearch = keywordSearch; }
        }

        // Getters and Setters
        public String getSearchMethod() { return searchMethod; }
        public void setSearchMethod(String searchMethod) { this.searchMethod = searchMethod; }
        public Integer getTopK() { return topK; }
        public void setTopK(Integer topK) { this.topK = topK; }
        public Double getScoreThreshold() { return scoreThreshold; }
        public void setScoreThreshold(Double scoreThreshold) { this.scoreThreshold = scoreThreshold; }
        public Boolean getRerankEnable() { return rerankEnable; }
        public void setRerankEnable(Boolean rerankEnable) { this.rerankEnable = rerankEnable; }
        public String getRerankModel() { return rerankModel; }
        public void setRerankModel(String rerankModel) { this.rerankModel = rerankModel; }
        public SearchWeights getWeights() { return weights; }
        public void setWeights(SearchWeights weights) { this.weights = weights; }
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getIcon() { return icon; }
    public void setIcon(String icon) { this.icon = icon; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public String getPermission() { return permission; }
    public void setPermission(String permission) { this.permission = permission; }
    public EmbeddingModel getEmbeddingModel() { return embeddingModel; }
    public void setEmbeddingModel(EmbeddingModel embeddingModel) { this.embeddingModel = embeddingModel; }
    public RetrievalConfig getRetrievalConfig() { return retrievalConfig; }
    public void setRetrievalConfig(RetrievalConfig retrievalConfig) { this.retrievalConfig = retrievalConfig; }
    public Integer getDocumentCount() { return documentCount; }
    public void setDocumentCount(Integer documentCount) { this.documentCount = documentCount; }
    public Long getCharacterCount() { return characterCount; }
    public void setCharacterCount(Long characterCount) { this.characterCount = characterCount; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
}
