package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.dify.dto.auth.DifyLoginResponseDTO;
import com.xhcai.modules.dify.service.IDifyMultiPlatformAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Dify 多平台认证管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@RestController
@RequestMapping("/api/dify/auth")
@Tag(name = "Dify多平台认证管理", description = "Dify多平台认证相关接口")
public class DifyMultiPlatformAuthController {

    private static final Logger log = LoggerFactory.getLogger(DifyMultiPlatformAuthController.class);

    @Autowired
    private IDifyMultiPlatformAuthService multiPlatformAuthService;

    @Operation(summary = "登录指定平台", description = "用户登录指定的Dify平台")
    @PostMapping("/login/{platformId}")
    @RequiresPermissions("dify:auth:login")
    public Mono<Result<DifyLoginResponseDTO>> login(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("用户请求登录平台: {}", platformId);
        
        return multiPlatformAuthService.currentUserLogin(platformId)
                .map(Result::success)
                .doOnNext(result -> log.info("用户登录平台{}成功", platformId))
                .doOnError(error -> log.error("用户登录平台{}失败", platformId, error))
                .onErrorResume(throwable -> {
                    String errorMessage = "登录平台" + platformId + "失败";
                    if (throwable.getMessage() != null) {
                        if (throwable.getMessage().contains("账号")) {
                            errorMessage = "平台" + platformId + "账号配置不存在或无效";
                        } else if (throwable.getMessage().contains("连接")) {
                            errorMessage = "无法连接到平台" + platformId + "服务器";
                        } else if (throwable.getMessage().contains("认证")) {
                            errorMessage = "平台" + platformId + "认证失败，请检查账号密码";
                        }
                    }
                    return Mono.just(Result.fail(errorMessage));
                });
    }

    @Operation(summary = "获取平台访问令牌", description = "获取用户在指定平台的有效访问令牌")
    @GetMapping("/token/{platformId}")
    @RequiresPermissions("dify:auth:token")
    public Mono<Result<String>> getAccessToken(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("用户请求获取平台{}的访问令牌", platformId);
        
        return multiPlatformAuthService.getCurrentUserValidAccessToken(platformId)
                .map(token -> Result.<String>success(token))
                .doOnNext(result -> log.debug("获取平台{}的访问令牌成功", platformId))
                .doOnError(error -> log.error("获取平台{}的访问令牌失败", platformId, error))
                .onErrorResume(throwable -> {
                    String errorMessage = "获取平台" + platformId + "访问令牌失败";
                    if (throwable.getMessage() != null) {
                        if (throwable.getMessage().contains("账号")) {
                            errorMessage = "平台" + platformId + "账号配置不存在或无效";
                        } else if (throwable.getMessage().contains("登录")) {
                            errorMessage = "需要先登录平台" + platformId;
                        }
                    }
                    return Mono.just(Result.<String>fail(errorMessage));
                });
    }

    @Operation(summary = "直接登录Dify平台", description = "直接调用Dify登录接口获取token，不存储到Redis")
    @PostMapping("/direct-login/{platformId}")
    @RequiresPermissions("dify:auth:login")
    public Mono<Result<DifyLoginResponseDTO>> directLogin(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("用户请求直接登录平台: {}", platformId);

        return multiPlatformAuthService.directLogin(platformId)
                .map(Result::success)
                .doOnNext(result -> log.info("用户直接登录平台{}成功", platformId))
                .doOnError(error -> log.error("用户直接登录平台{}失败", platformId, error))
                .onErrorResume(throwable -> {
                    String errorMessage = "直接登录平台" + platformId + "失败";
                    if (throwable.getMessage() != null) {
                        if (throwable.getMessage().contains("账号")) {
                            errorMessage = "平台" + platformId + "账号配置不存在或无效";
                        } else if (throwable.getMessage().contains("连接")) {
                            errorMessage = "无法连接到平台" + platformId + "服务器";
                        } else if (throwable.getMessage().contains("认证")) {
                            errorMessage = "平台" + platformId + "认证失败，请检查账号密码";
                        }
                    }
                    return Mono.just(Result.fail(errorMessage));
                });
    }

    @Operation(summary = "检查平台状态", description = "检查指定平台的配置和状态信息")
    @GetMapping("/platform-status/{platformId}")
    @RequiresPermissions("dify:auth:test")
    public Result<Map<String, Object>> checkPlatformStatus(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("检查平台{}的状态", platformId);

        try {
            Map<String, Object> result = multiPlatformAuthService.checkPlatformStatus(platformId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查平台{}状态失败", platformId, e);
            return Result.fail("检查平台状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "测试平台连接", description = "测试用户在指定平台的连接状态")
    @GetMapping("/test/{platformId}")
    @RequiresPermissions("dify:auth:test")
    public Mono<Result<String>> testConnection(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("用户请求测试平台{}的连接", platformId);
        
        return multiPlatformAuthService.testCurrentUserConnection(platformId)
                .map(result -> Result.<String>success(result))
                .doOnNext(result -> log.info("测试平台{}连接完成: {}", platformId, result.getData()))
                .doOnError(error -> log.error("测试平台{}连接失败", platformId, error))
                .onErrorResume(throwable -> {
                    String errorMessage = "测试平台" + platformId + "连接失败: " + throwable.getMessage();
                    return Mono.just(Result.<String>fail(errorMessage));
                });
    }

    @Operation(summary = "清除平台令牌缓存", description = "清除用户在指定平台的令牌缓存")
    @DeleteMapping("/token/{platformId}")
    @RequiresPermissions("dify:auth:clear")
    public Result<String> clearTokenCache(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("用户请求清除平台{}的令牌缓存", platformId);
        
        try {
            multiPlatformAuthService.clearCurrentUserTokenCache(platformId);
            log.info("清除平台{}的令牌缓存成功", platformId);
            return Result.success("清除平台" + platformId + "令牌缓存成功");
        } catch (Exception e) {
            log.error("清除平台{}的令牌缓存失败", platformId, e);
            return Result.fail("清除平台" + platformId + "令牌缓存失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查平台账号配置", description = "检查用户在指定平台是否有有效的账号配置")
    @GetMapping("/account/{platformId}")
    @RequiresPermissions("dify:auth:check")
    public Result<Boolean> checkAccount(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("用户请求检查平台{}的账号配置", platformId);
        
        try {
            boolean hasValidAccount = multiPlatformAuthService.currentUserHasValidAccount(platformId);
            log.debug("用户在平台{}的账号配置检查结果: {}", platformId, hasValidAccount);
            return Result.success(hasValidAccount);
        } catch (Exception e) {
            log.error("检查平台{}的账号配置失败", platformId, e);
            return Result.fail("检查平台" + platformId + "账号配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "刷新平台访问令牌", description = "刷新用户在指定平台的访问令牌")
    @PostMapping("/refresh/{platformId}")
    @RequiresPermissions("dify:auth:refresh")
    public Mono<Result<String>> refreshToken(
            @Parameter(description = "平台ID") @PathVariable String platformId) {
        log.info("用户请求刷新平台{}的访问令牌", platformId);
        
        return multiPlatformAuthService.handleCurrentUserUnauthorized(platformId)
                .map(token -> Result.<String>success(token))
                .doOnNext(result -> log.info("刷新平台{}的访问令牌成功", platformId))
                .doOnError(error -> log.error("刷新平台{}的访问令牌失败", platformId, error))
                .onErrorResume(throwable -> {
                    String errorMessage = "刷新平台" + platformId + "访问令牌失败";
                    if (throwable.getMessage() != null) {
                        if (throwable.getMessage().contains("账号")) {
                            errorMessage = "平台" + platformId + "账号配置不存在或无效";
                        } else if (throwable.getMessage().contains("刷新令牌")) {
                            errorMessage = "平台" + platformId + "刷新令牌无效，需要重新登录";
                        }
                    }
                    return Mono.just(Result.<String>fail(errorMessage));
                });
    }
}
