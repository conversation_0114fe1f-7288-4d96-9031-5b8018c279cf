'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Server, 
  Activity, 
  Database, 
  Monitor, 
  FileText, 
  Network, 
  Shield, 
  Search,
  Zap
} from 'lucide-react';
import { ComponentType, ComponentTypeLabels } from '@/types/component';

// 组件信息配置
const COMPONENT_INFO: Record<ComponentType, {
  icon: React.ComponentType<any>;
  description: string;
  category: string;
  features: string[];
  defaultPort?: number;
}> = {
  filebeat: {
    icon: FileText,
    description: '轻量级日志收集器，用于转发和集中日志数据',
    category: 'Beats',
    features: ['日志收集', '文件监控', '多输出支持', '轻量级'],
    defaultPort: 5044
  },
  heartbeat: {
    icon: Activity,
    description: '轻量级监控工具，用于检查服务的可用性',
    category: 'Beats',
    features: ['服务监控', '可用性检查', '响应时间监控', '告警通知'],
    defaultPort: 5066
  },
  metricbeat: {
    icon: Monitor,
    description: '轻量级指标收集器，用于收集系统和服务指标',
    category: 'Beats',
    features: ['系统指标', '服务监控', '性能分析', '实时数据'],
    defaultPort: 5067
  },
  packetbeat: {
    icon: Network,
    description: '网络数据包分析器，用于监控网络流量',
    category: 'Beats',
    features: ['网络监控', '流量分析', '协议解析', '安全监控'],
    defaultPort: 5068
  },
  winlogbeat: {
    icon: Shield,
    description: 'Windows事件日志收集器',
    category: 'Beats',
    features: ['Windows日志', '事件监控', '安全审计', '系统日志'],
    defaultPort: 5069
  },
  auditbeat: {
    icon: Shield,
    description: '审计数据收集器，用于监控文件完整性和用户活动',
    category: 'Beats',
    features: ['文件完整性', '用户活动', '安全审计', '合规监控'],
    defaultPort: 5070
  },
  logstash: {
    icon: Database,
    description: '数据处理管道，用于收集、解析和转换数据',
    category: 'Elastic Stack',
    features: ['数据处理', '日志解析', '数据转换', '多输入输出'],
    defaultPort: 5044
  },
  elasticsearch: {
    icon: Search,
    description: '分布式搜索和分析引擎',
    category: 'Elastic Stack',
    features: ['全文搜索', '实时分析', '分布式存储', 'RESTful API'],
    defaultPort: 9200
  },
  kafka: {
    icon: Zap,
    description: '分布式流处理平台',
    category: 'Message Queue',
    features: ['消息队列', '流处理', '高吞吐量', '持久化存储'],
    defaultPort: 9092
  }
};

export default function InstallPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 获取所有分类
  const categories = ['all', ...Array.from(new Set(Object.values(COMPONENT_INFO).map(info => info.category)))];

  // 过滤组件
  const filteredComponents = Object.entries(COMPONENT_INFO).filter(([type, info]) => {
    const matchesSearch = type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ComponentTypeLabels[type as ComponentType].toLowerCase().includes(searchTerm.toLowerCase()) ||
                         info.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || info.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleInstallComponent = (componentType: ComponentType) => {
    router.push(`/install/${componentType}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="btn-outline mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </button>
              <div className="flex items-center">
                <Server className="h-8 w-8 text-primary-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">组件安装</h1>
                  <p className="text-sm text-gray-500">选择要安装的 Elastic Stack 组件</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索和过滤 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索组件..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="form-input"
              >
                <option value="all">所有分类</option>
                {categories.slice(1).map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 组件网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredComponents.map(([type, info]) => {
            const IconComponent = info.icon;
            return (
              <div key={type} className="card hover:shadow-medium transition-shadow cursor-pointer">
                <div className="card-body">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 p-3 bg-primary-50 rounded-lg">
                        <IconComponent className="h-6 w-6 text-primary-600" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {ComponentTypeLabels[type as ComponentType]}
                        </h3>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary-100 text-secondary-800">
                          {info.category}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-4">
                    {info.description}
                  </p>
                  
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">主要功能:</h4>
                    <div className="flex flex-wrap gap-1">
                      {info.features.map((feature, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  {info.defaultPort && (
                    <div className="mb-4">
                      <span className="text-sm text-gray-500">
                        默认端口: <span className="font-medium text-gray-900">{info.defaultPort}</span>
                      </span>
                    </div>
                  )}
                  
                  <button
                    onClick={() => handleInstallComponent(type as ComponentType)}
                    className="btn-primary w-full"
                  >
                    安装 {ComponentTypeLabels[type as ComponentType]}
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {filteredComponents.length === 0 && (
          <div className="text-center py-12">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">没有找到匹配的组件</p>
          </div>
        )}
      </main>
    </div>
  );
}
