<template>
  <div class="parameter-config-test p-6">
    <h1 class="text-2xl font-bold mb-6">参数配置组件测试</h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 测试用例1：下拉选择框 -->
      <div class="test-case">
        <h2 class="text-lg font-semibold mb-4">测试用例1：下拉选择框</h2>
        <ParameterConfig
          :parameters="selectParameters"
          :values="selectValues"
          @update:values="handleSelectValuesUpdate"
        />
        <div class="mt-4 p-3 bg-gray-100 rounded">
          <strong>当前值：</strong>
          <pre>{{ JSON.stringify(selectValues, null, 2) }}</pre>
        </div>
      </div>

      <!-- 测试用例2：文本输入框 -->
      <div class="test-case">
        <h2 class="text-lg font-semibold mb-4">测试用例2：文本输入框</h2>
        <ParameterConfig
          :parameters="textParameters"
          :values="textValues"
          @update:values="handleTextValuesUpdate"
        />
        <div class="mt-4 p-3 bg-gray-100 rounded">
          <strong>当前值：</strong>
          <pre>{{ JSON.stringify(textValues, null, 2) }}</pre>
        </div>
      </div>

      <!-- 测试用例3：混合参数 -->
      <div class="test-case lg:col-span-2">
        <h2 class="text-lg font-semibold mb-4">测试用例3：混合参数</h2>
        <ParameterConfig
          :parameters="mixedParameters"
          :values="mixedValues"
          @update:values="handleMixedValuesUpdate"
        />
        <div class="mt-4 p-3 bg-gray-100 rounded">
          <strong>当前值：</strong>
          <pre>{{ JSON.stringify(mixedValues, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 模拟API响应 -->
    <div class="mt-8">
      <h2 class="text-lg font-semibold mb-4">模拟API响应数据</h2>
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div class="api-response">
          <h3 class="font-medium mb-2">下拉选择框配置</h3>
          <pre class="text-xs bg-gray-100 p-2 rounded">{{ JSON.stringify(selectParameters, null, 2) }}</pre>
        </div>
        <div class="api-response">
          <h3 class="font-medium mb-2">文本输入框配置</h3>
          <pre class="text-xs bg-gray-100 p-2 rounded">{{ JSON.stringify(textParameters, null, 2) }}</pre>
        </div>
        <div class="api-response">
          <h3 class="font-medium mb-2">混合参数配置</h3>
          <pre class="text-xs bg-gray-100 p-2 rounded">{{ JSON.stringify(mixedParameters, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ParameterConfig from '@/components/ParameterConfig.vue'

// 测试用例1：下拉选择框参数
const selectParameters = ref([
  {
    select: {
      variable: "model",
      label: "模型",
      type: "select",
      max_length: 48,
      required: true,
      options: [
        "deepseek-r1",
        "qwen3",
        "gpt-4"
      ]
    }
  }
])

const selectValues = ref({
  model: "deepseek-r1"
})

// 测试用例2：文本输入框参数
const textParameters = ref([
  {
    variable: "topic",
    label: "主题",
    type: "text-input",
    max_length: 48,
    required: true,
    options: []
  },
  {
    variable: "description",
    label: "描述",
    type: "text-input",
    max_length: 200,
    required: false,
    options: []
  }
])

const textValues = ref({
  topic: "",
  description: ""
})

// 测试用例3：混合参数
const mixedParameters = ref([
  {
    select: {
      variable: "model",
      label: "模型",
      type: "select",
      max_length: 48,
      required: true,
      options: [
        "deepseek-r1",
        "qwen3"
      ]
    }
  },
  {
    variable: "topic",
    label: "主题",
    type: "text-input",
    max_length: 48,
    required: true,
    options: []
  },
  {
    select: {
      variable: "language",
      label: "语言",
      type: "select",
      max_length: 20,
      required: false,
      options: [
        "中文",
        "English",
        "日本語"
      ]
    }
  }
])

const mixedValues = ref({
  model: "deepseek-r1",
  topic: "",
  language: "中文"
})

// 事件处理
const handleSelectValuesUpdate = (values: Record<string, any>) => {
  selectValues.value = values
  console.log('下拉选择框值更新:', values)
}

const handleTextValuesUpdate = (values: Record<string, any>) => {
  textValues.value = values
  console.log('文本输入框值更新:', values)
}

const handleMixedValuesUpdate = (values: Record<string, any>) => {
  mixedValues.value = values
  console.log('混合参数值更新:', values)
}
</script>

<style scoped>
.parameter-config-test {
  max-width: 1200px;
  margin: 0 auto;
}

.test-case {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: white;
}

.api-response {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  background: #f9fafb;
}

pre {
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
