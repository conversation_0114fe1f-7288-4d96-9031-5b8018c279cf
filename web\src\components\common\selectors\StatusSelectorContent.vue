<template>
  <div class="status-selector-content" :class="{ 'has-header': showHeader }">
    <!-- 标题头部 -->
    <div v-if="showHeader" class="selector-header">
      <h4 class="header-title">选择状态</h4>
      <p v-if="config.showDescription" class="header-description">
        请选择一个状态选项
      </p>
    </div>

    <!-- 状态选项列表 -->
    <div class="status-options" :class="{ 'is-grid': config.multiple }">
      <div
        v-for="option in options"
        :key="option.value"
        class="status-option"
        :class="{
          'is-selected': isSelected(option.value),
          'is-disabled': option.disabled,
          'is-multiple': config.multiple
        }"
        @click="handleOptionClick(option)"
      >
        <!-- 选择指示器 -->
        <div v-if="config.multiple" class="option-checkbox">
          <div class="checkbox-inner" :class="{ 'is-checked': isSelected(option.value) }">
            <svg v-if="isSelected(option.value)" class="check-icon" viewBox="0 0 1024 1024" width="12" height="12">
              <path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z" fill="currentColor"/>
            </svg>
          </div>
        </div>

        <!-- 状态徽章 -->
        <div class="status-badge" :style="getBadgeStyle(option)">
          <span class="badge-text">{{ option.label }}</span>
        </div>

        <!-- 描述信息 -->
        <div v-if="config.showDescription && option.description" class="option-description">
          {{ option.description }}
        </div>

        <!-- 单选指示器 -->
        <div v-if="!config.multiple" class="option-radio">
          <div class="radio-inner" :class="{ 'is-checked': isSelected(option.value) }">
            <div v-if="isSelected(option.value)" class="radio-dot"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="options.length === 0" class="empty-state">
      <div class="empty-icon">
        <svg viewBox="0 0 1024 1024" width="48" height="48">
          <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor" opacity="0.3"/>
          <path d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.4-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.8 41.3-19.8 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0 1 30.9-44.8c59-22.7 97.1-74.7 97.1-132.5 0-39.3-17.2-76-48.4-103.3z" fill="currentColor"/>
          <circle cx="512" cy="708" r="48" fill="currentColor"/>
        </svg>
      </div>
      <p class="empty-text">暂无状态选项</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface StatusOption {
  value: string | number
  label: string
  description?: string
  color?: string
  bgColor?: string
  disabled?: boolean
}

interface SelectorConfig {
  multiple?: boolean
  clearable?: boolean
  placeholder?: string
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  showDescription?: boolean
  showBadge?: boolean
}

interface Props {
  modelValue?: string | number | null | (string | number)[]
  config: SelectorConfig
  options: StatusOption[]
  showHeader?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string | number | null | (string | number)[]): void
  (e: 'change', value: string | number | null | (string | number)[], option: StatusOption | null): void
  (e: 'select', value: string | number, option: StatusOption): void
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: false
})

const emit = defineEmits<Emits>()

// 计算属性
const selectedValues = computed(() => {
  if (props.config.multiple) {
    return Array.isArray(props.modelValue) ? props.modelValue : []
  }
  return props.modelValue
})

// 方法
const isSelected = (value: string | number): boolean => {
  if (props.config.multiple) {
    const values = Array.isArray(props.modelValue) ? props.modelValue : []
    return values.includes(value)
  }
  return props.modelValue === value
}

const getBadgeStyle = (option: StatusOption) => {
  const style: any = {}
  
  if (option.color) {
    style.color = option.color
  }
  
  if (option.bgColor) {
    style.backgroundColor = option.bgColor
  }
  
  return style
}

const handleOptionClick = (option: StatusOption) => {
  if (option.disabled || props.config.disabled) return

  let newValue: string | number | null | (string | number)[]

  if (props.config.multiple) {
    const currentValues = Array.isArray(props.modelValue) ? [...props.modelValue] : []
    const index = currentValues.indexOf(option.value)
    
    if (index > -1) {
      // 取消选择
      currentValues.splice(index, 1)
    } else {
      // 添加选择
      currentValues.push(option.value)
    }
    
    newValue = currentValues
  } else {
    // 单选模式
    newValue = isSelected(option.value) ? null : option.value
  }

  emit('update:modelValue', newValue)
  emit('change', newValue, option)
  emit('select', option.value, option)
}
</script>

<style scoped>
.status-selector-content {
  width: 100%;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.status-selector-content.has-header {
  border: 1px solid #e4e7ed;
}

/* 头部样式 */
.selector-header {
  padding: 16px 16px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(to bottom, #fafbfc, #f8f9fa);
}

.header-title {
  margin: 0 0 4px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.header-description {
  margin: 0;
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

/* 选项列表 */
.status-options {
  padding: 12px;
  max-height: 320px;
  overflow-y: auto;
}

.status-options.is-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

/* 滚动条样式 */
.status-options::-webkit-scrollbar {
  width: 6px;
}

.status-options::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.status-options::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 3px;
  transition: background 0.2s;
}

.status-options::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* 状态选项 */
.status-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 5px;
  margin-bottom: 5px;
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
  position: relative;
}

.status-option:hover:not(.is-disabled) {
  background: #f8f9fa;
  border-color: #e4e7ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-option.is-selected {
  background: #f0f9ff;
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
}

.status-option.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f7fa;
}

.status-option.is-multiple {
  padding-left: 44px;
}

/* 复选框 */
.option-checkbox {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
}

.checkbox-inner {
  width: 16px;
  height: 16px;
  border: 2px solid #dcdfe6;
  border-radius: 3px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.checkbox-inner.is-checked {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.check-icon {
  display: block;
}

/* 单选框 */
.option-radio {
  margin-left: auto;
  flex-shrink: 0;
}

.radio-inner {
  width: 16px;
  height: 16px;
  border: 2px solid #dcdfe6;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.radio-inner.is-checked {
  border-color: #409eff;
}

.radio-dot {
  width: 8px;
  height: 8px;
  background: #409eff;
  border-radius: 50%;
}

/* 状态徽章 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: #f4f4f5;
  color: #909399;
  flex-shrink: 0;
}

.badge-text {
  line-height: 1;
}

/* 描述信息 */
.option-description {
  flex: 1;
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #c0c4cc;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

/* 响应式 */
@media (max-width: 768px) {
  .status-options.is-grid {
    grid-template-columns: 1fr;
  }
  
  .status-option {
    padding: 16px 12px;
  }
  
  .selector-header {
    padding: 12px;
  }
}
</style>
