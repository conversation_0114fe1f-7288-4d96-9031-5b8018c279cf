package com.xhcai.modules.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * 模块初始化请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "模块初始化请求")
public class ModuleInitRequestDTO {

    @Schema(description = "要初始化的模块ID列表")
    private List<String> moduleIds;

    @Schema(description = "是否初始化所有模块")
    private Boolean initAll = false;

    @Schema(description = "是否强制重新初始化")
    private Boolean forceReinit = false;

    @Schema(description = "租户ID")
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    @Schema(description = "初始化类型：BASIC-基础初始化，FULL-完整初始化")
    private String initType = "BASIC";

    @Schema(description = "是否异步执行")
    private Boolean async = true;

    public List<String> getModuleIds() {
        return moduleIds;
    }

    public void setModuleIds(List<String> moduleIds) {
        this.moduleIds = moduleIds;
    }

    public Boolean getInitAll() {
        return initAll;
    }

    public void setInitAll(Boolean initAll) {
        this.initAll = initAll;
    }

    public Boolean getForceReinit() {
        return forceReinit;
    }

    public void setForceReinit(Boolean forceReinit) {
        this.forceReinit = forceReinit;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getInitType() {
        return initType;
    }

    public void setInitType(String initType) {
        this.initType = initType;
    }

    public Boolean getAsync() {
        return async;
    }

    public void setAsync(Boolean async) {
        this.async = async;
    }
}
