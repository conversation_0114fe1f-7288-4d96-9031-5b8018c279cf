package com.xhcai.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * AI文件记录实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "ai_file_record")
@Schema(description = "AI文件记录")
@TableName("ai_file_record")
public class AiFileRecord extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 原始文件名
     */
    @Column(name = "original_filename", length = 255)
    @Schema(description = "原始文件名", example = "document.pdf")
    @NotBlank(message = "原始文件名不能为空")
    @Size(max = 255, message = "原始文件名长度不能超过255个字符")
    @TableField("original_filename")
    private String originalFilename;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    @Schema(description = "文件大小（字节）", example = "1024000")
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件扩展名
     */
    @Column(name = "file_extension", length = 10)
    @Schema(description = "文件扩展名", example = "pdf")
    @Size(max = 10, message = "文件扩展名长度不能超过10个字符")
    @TableField("file_extension")
    private String fileExtension;

    /**
     * MIME类型
     */
    @Column(name = "mime_type", length = 100)
    @Schema(description = "MIME类型", example = "application/pdf")
    @Size(max = 100, message = "MIME类型长度不能超过100个字符")
    @TableField("mime_type")
    private String mimeType;

    /**
     * Dify文件ID
     */
    @Column(name = "dify_file_id", length = 100)
    @Schema(description = "Dify文件ID", example = "4d76840c-ba74-43e7-a1e0-b18474338ec4")
    @NotBlank(message = "Dify文件ID不能为空")
    @Size(max = 100, message = "Dify文件ID长度不能超过100个字符")
    @TableField("dify_file_id")
    private String difyFileId;

    /**
     * MinIO存储桶名称
     */
    @Column(name = "minio_bucket", length = 100)
    @Schema(description = "MinIO存储桶名称", example = "ai-files")
    @Size(max = 100, message = "MinIO存储桶名称长度不能超过100个字符")
    @TableField("minio_bucket")
    private String minioBucket;

    /**
     * MinIO对象名称（文件路径）
     */
    @Column(name = "minio_object_name", length = 500)
    @Schema(description = "MinIO对象名称", example = "2024/01/15/1642234567890_document.pdf")
    @Size(max = 500, message = "MinIO对象名称长度不能超过500个字符")
    @TableField("minio_object_name")
    private String minioObjectName;

    /**
     * MinIO访问URL
     */
    @Column(name = "minio_url", length = 1000)
    @Schema(description = "MinIO访问URL")
    @Size(max = 1000, message = "MinIO访问URL长度不能超过1000个字符")
    @TableField("minio_url")
    private String minioUrl;

    /**
     * 文件状态
     */
    @Column(name = "status", length = 20)
    @Schema(description = "文件状态", example = "uploaded", allowableValues = {"uploading", "uploaded", "failed", "deleted"})
    @Size(max = 20, message = "文件状态长度不能超过20个字符")
    @TableField("status")
    private String status;

    /**
     * 上传时间
     */
    @Column(name = "upload_time")
    @Schema(description = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    /**
     * 上传用户ID
     */
    @Column(name = "upload_user_id", length = 36)
    @Schema(description = "上传用户ID", example = "user_001")
    @Size(max = 36, message = "上传用户ID长度不能超过36个字符")
    @TableField("upload_user_id")
    private String uploadUserId;

    /**
     * Dify预览URL
     */
    @Column(name = "dify_preview_url", length = 1000)
    @Schema(description = "Dify预览URL")
    @Size(max = 1000, message = "Dify预览URL长度不能超过1000个字符")
    @TableField("dify_preview_url")
    private String difyPreviewUrl;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    @Schema(description = "错误信息")
    @Size(max = 1000, message = "错误信息长度不能超过1000个字符")
    @TableField("error_message")
    private String errorMessage;

    // Getters and Setters
    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getDifyFileId() {
        return difyFileId;
    }

    public void setDifyFileId(String difyFileId) {
        this.difyFileId = difyFileId;
    }

    public String getMinioBucket() {
        return minioBucket;
    }

    public void setMinioBucket(String minioBucket) {
        this.minioBucket = minioBucket;
    }

    public String getMinioObjectName() {
        return minioObjectName;
    }

    public void setMinioObjectName(String minioObjectName) {
        this.minioObjectName = minioObjectName;
    }

    public String getMinioUrl() {
        return minioUrl;
    }

    public void setMinioUrl(String minioUrl) {
        this.minioUrl = minioUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getUploadUserId() {
        return uploadUserId;
    }

    public void setUploadUserId(String uploadUserId) {
        this.uploadUserId = uploadUserId;
    }

    public String getDifyPreviewUrl() {
        return difyPreviewUrl;
    }

    public void setDifyPreviewUrl(String difyPreviewUrl) {
        this.difyPreviewUrl = difyPreviewUrl;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
