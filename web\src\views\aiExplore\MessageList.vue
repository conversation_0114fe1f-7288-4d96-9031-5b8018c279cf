<template>
  <div class="message-list p-4 space-y-3"
       :style="{ marginBottom: '140px' }">
    <div class="messages-wrapper w-full max-w-full">
      <!-- 消息列表 -->
      <div
        v-for="message in messages"
        :key="message.id"
        class="message-item flex gap-4 w-full"
        :class="{ 'flex-row-reverse': message.role === 'user' }"
      >
        <!-- 头像 -->
        <div class="avatar flex-shrink-0">
          <div
            class="w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold transition-all duration-300"
            :class="[
              message.role === 'user' ? 'bg-gradient-to-r from-green-500 to-teal-500' : 'bg-gradient-to-r from-blue-500 to-purple-600',
              { 'animate-pulse scale-110': message.isTyping }
            ]"
          >
            {{ message.role === 'user' ? '👤' : '🤖' }}
          </div>
        </div>

        <!-- 消息内容 -->
        <div
          class="message-content max-w-[70%] p-4 rounded-2xl shadow-sm flex-shrink min-w-0"
          :class="message.role === 'user'
            ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-gray-800 border border-blue-100'
            : 'bg-white border border-gray-200'"
        >
          <!-- 节点状态显示 - 只在AI消息且有节点信息时显示 -->
          <div
            v-if="message.role === 'assistant' && message.currentNode && message.streaming"
            class="node-status mb-2"
          >
            <div class="flex items-center gap-1">
              <div
                class="w-1.5 h-1.5 rounded-full"
                :class="message.currentNode.isActive ? 'bg-gray-400 animate-pulse' : 'bg-gray-300'"
              ></div>
              <span class="text-xs text-gray-500">
                正在处理: {{ message.currentNode.title }}
              </span>
              <div
                v-if="message.currentNode.isActive"
                class="flex space-x-0.5"
              >
                <div class="w-0.5 h-0.5 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
                <div class="w-0.5 h-0.5 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
                <div class="w-0.5 h-0.5 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
              </div>
            </div>
          </div>

          <!-- 使用MessageContent组件 - 优化智能体流式输出性能 -->
          <MessageContent
            :content="message.content"
            :content-type="message.contentType || 'text'"
            :is-typing="message.isTyping"
            :streaming="message.streaming"
            :data="getOptimizedMessageData(message)"
            @link-click="handleLinkClick"
            @image-click="handleImageClick"
            @file-download="handleFileDownload"
          />

          <!-- 文件预览（保留原有样式） -->
          <div v-if="message.files && message.files.length > 0" class="files mt-3">
            <div
              v-for="file in message.files"
              :key="file.name"
              class="file-item flex items-center gap-2 p-2 bg-black/10 rounded-lg mb-2"
            >
              <span class="text-sm">📎</span>
              <span class="text-sm">{{ file.name }}</span>
            </div>
          </div>

          <!-- 智能体文件预览 -->
          <div v-if="message.agentFiles && message.agentFiles.length > 0" class="agent-files mt-3">
            <div
              v-for="file in message.agentFiles"
              :key="file.upload_file_id"
              class="agent-file-item flex items-center gap-2 p-2 bg-blue-50 rounded-lg mb-2 border border-blue-200"
            >
              <!-- 图片文件显示缩略图 -->
              <div v-if="file.type === 'image'" class="flex-shrink-0">
                <div class="w-12 h-12 rounded border border-blue-300 overflow-hidden bg-white">
                  <img
                    v-if="file.preview_url"
                    :src="file.preview_url"
                    :alt="file.name"
                    class="w-full h-full object-cover"
                    @error="handleMessageImageError"
                  />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <span class="text-lg">🖼️</span>
                  </div>
                </div>
              </div>

              <!-- 其他文件类型显示图标 -->
              <span v-else class="text-sm flex-shrink-0">
                <span v-if="file.type === 'document'">📄</span>
                <span v-else-if="file.type === 'audio'">🎵</span>
                <span v-else-if="file.type === 'video'">🎬</span>
                <span v-else>📎</span>
              </span>

              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-blue-700 truncate">{{ file.name }}</div>
                <div class="text-xs text-blue-500">
                  {{ file.type }} • {{ formatFileSize(file.size) }}
                  <span v-if="file.transfer_method === 'remote_url'" class="ml-1">• 远程文件</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 消息文件预览 (message_files) -->
          <div v-if="message.message_files && message.message_files.length > 0" class="message-files mt-3">
            <div
              v-for="file in message.message_files"
              :key="file.id"
              class="message-file-item relative group flex items-center gap-2 p-2 bg-blue-50 rounded-lg mb-2 border border-blue-200"
            >
              <!-- 图片文件显示缩略图 -->
              <div v-if="file.type === 'image' || isImageFileByMimeType(file.mime_type)" class="flex-shrink-0">
                <div class="w-12 h-12 rounded border border-blue-300 overflow-hidden bg-white">
                  <img
                    v-if="file.url"
                    :src="file.url"
                    :alt="file.filename || 'Image'"
                    class="w-full h-full object-cover"
                    @error="handleMessageImageError"
                  />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <span class="text-lg">🖼️</span>
                  </div>
                </div>
              </div>

              <!-- 其他文件类型显示图标 -->
              <span v-else class="text-sm flex-shrink-0">
                <span v-if="file.type === 'document' || isDocumentFileByMimeType(file.mime_type)">📄</span>
                <span v-else-if="file.type === 'audio' || isAudioFileByMimeType(file.mime_type)">🎵</span>
                <span v-else-if="file.type === 'video' || isVideoFileByMimeType(file.mime_type)">🎬</span>
                <span v-else>📎</span>
              </span>

              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-blue-700 truncate">
                  {{ file.filename || getFileNameFromUrl(file.url) }}
                </div>
                <div class="text-xs text-blue-500">
                  {{ file.type || getFileTypeFromMimeType(file.mime_type) }}
                  <span v-if="file.size" class="ml-1">• {{ formatFileSize(file.size) }}</span>
                  <span v-if="file.belongs_to" class="ml-1">• {{ file.belongs_to === 'user' ? '用户上传' : '系统' }}</span>
                  <span v-if="file.transfer_method === 'remote_url'" class="ml-1">• 远程文件</span>
                </div>
              </div>

              <!-- 文件下载按钮 - 鼠标悬浮时显示 -->
              <div class="download-button opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
                <button
                  @click="handleFileDownload(file)"
                  class="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-colors duration-200"
                  title="下载文件"
                >
                  <el-icon class="text-sm">
                    <Download />
                  </el-icon>
                </button>
              </div>
            </div>
          </div>

          <!-- 时间戳 -->
          <div
            class="timestamp text-xs mt-2 opacity-70"
            :class="message.role === 'user' ? 'text-gray-600' : 'text-gray-500'"
          >
            {{ formatTime(message.timestamp) }}
          </div>
        </div>
      </div>

      <!-- AI思考中的动态效果 - 只在思考阶段显示 -->
      <div v-if="isSending && !isStreaming" class="message-item flex gap-4 w-full animate-fade-in">
        <!-- AI头像 - 带动画 -->
        <div class="avatar flex-shrink-0">
          <div class="w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse scale-110 shadow-lg">
            🤖
          </div>
        </div>

        <!-- 思考中的消息气泡 -->
        <div class="message-content max-w-[70%] p-4 rounded-2xl shadow-sm flex-shrink min-w-0 bg-white border border-gray-200 animate-pulse-subtle">
          <div class="thinking-animation">
            <!-- 思考中的文字和动态点点点 -->
            <div class="flex items-center gap-2 mb-3">
              <span class="text-gray-600 text-sm font-medium">{{ thinkingText }}</span>
              <div class="flex gap-1">
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
              </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-section">
              <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  class="bg-gradient-to-r from-blue-500 via-purple-500 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out animate-shimmer"
                  :style="{ width: thinkingProgress + '%' }"
                ></div>
              </div>
              <div class="flex justify-between items-center mt-2">
                <div class="text-xs text-gray-500">{{ thinkingStage }}</div>
                <div class="text-xs text-blue-600 font-medium">{{ thinkingProgress }}%</div>
              </div>
            </div>

            <!-- 思考步骤指示器 -->
            <div class="thinking-steps flex items-center gap-2 mt-3">
              <div
                v-for="(step, index) in thinkingSteps"
                :key="index"
                class="step-indicator flex items-center gap-1"
              >
                <div
                  class="w-3 h-3 rounded-full transition-all duration-300"
                  :class="step.completed ? 'bg-green-500' : step.active ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'"
                ></div>
                <span
                  class="text-xs transition-colors duration-300"
                  :class="step.completed ? 'text-green-600' : step.active ? 'text-blue-600' : 'text-gray-400'"
                >
                  {{ step.name }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import MessageContent from '@/views/aiExplore/MessageContent.vue'
import { Download } from '@element-plus/icons-vue'

// 定义接口
interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  contentType?: 'text' | 'markdown' | 'html' | 'audio' | 'video' | 'file' | 'chart' | 'image' | 'flowchart'
  timestamp: Date
  files?: File[] | any[]
  images?: string[]
  isTyping?: boolean
  streaming?: boolean
  // 当前节点信息（用于显示流式处理的节点状态）
  currentNode?: {
    title: string
    nodeType: string
    isActive: boolean
  } | null
}

interface ThinkingStep {
  name: string
  active: boolean
  completed: boolean
}

// 定义props
const props = defineProps<{
  messages: Message[]
  isSending: boolean
  isStreaming: boolean
  thinkingText: string
  thinkingProgress: number
  thinkingStage: string
  thinkingSteps: ThinkingStep[]
  formatTime: (date: Date) => string
  getMessageData: (message: Message) => any
}>()

// 定义事件
const emit = defineEmits<{
  'link-click': [url: string]
  'image-click': [image: any, index: number]
  'file-download': [file: any]
}>()

// 处理链接点击
const handleLinkClick = (url: string) => {
  emit('link-click', url)
}

// 处理图片点击
const handleImageClick = (image: any, index: number) => {
  emit('image-click', image, index)
}

// 处理文件下载
const handleFileDownload = (file: any) => {
  console.log('下载文件:', file)

  // 如果文件有URL，直接下载
  if (file.url) {
    try {
      // 创建一个临时的a标签来触发下载
      const link = document.createElement('a')
      link.href = file.url
      link.download = file.filename || file.name || 'download'

      // 设置target为_blank以在新窗口打开，避免跨域问题
      link.target = '_blank'

      // 添加到DOM，点击，然后移除
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      console.log('文件下载已触发:', file.filename || file.name)
    } catch (error) {
      console.error('文件下载失败:', error)
      // 如果下载失败，在新窗口打开文件URL
      window.open(file.url, '_blank')
    }
  } else {
    console.warn('文件没有可用的下载URL:', file)
  }

  // 同时发出事件，保持向后兼容
  emit('file-download', file)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 处理消息中图片加载错误
const handleMessageImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  const parent = img.parentElement
  if (parent) {
    parent.innerHTML = '<span class="text-lg">🖼️</span>'
  }
}

// 从URL中提取文件名
const getFileNameFromUrl = (url: string): string => {
  if (!url) return '未知文件'

  try {
    // 移除查询参数
    const urlWithoutQuery = url.split('?')[0]
    // 提取文件名
    const fileName = urlWithoutQuery.split('/').pop() || '未知文件'
    return decodeURIComponent(fileName)
  } catch (error) {
    return '未知文件'
  }
}

// 从URL中判断文件类型
const getFileTypeFromUrl = (url: string): string => {
  if (!url) return 'unknown'

  const extension = url.split('.').pop()?.toLowerCase()

  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
    return 'image'
  } else if (['pdf', 'doc', 'docx', 'txt', 'md', 'html', 'xlsx', 'xls', 'ppt', 'pptx'].includes(extension || '')) {
    return 'document'
  } else if (['mp3', 'm4a', 'wav', 'amr', 'mpga'].includes(extension || '')) {
    return 'audio'
  } else if (['mp4', 'mov', 'mpeg', 'webm'].includes(extension || '')) {
    return 'video'
  }

  return 'document'
}

// 判断是否为图片文件
const isImageFile = (file: any): boolean => {
  if (file.type === 'image') return true
  return getFileTypeFromUrl(file.url) === 'image'
}

// 判断是否为文档文件
const isDocumentFile = (file: any): boolean => {
  if (file.type === 'document') return true
  return getFileTypeFromUrl(file.url) === 'document'
}

// 判断是否为音频文件
const isAudioFile = (file: any): boolean => {
  if (file.type === 'audio') return true
  return getFileTypeFromUrl(file.url) === 'audio'
}

// 判断是否为视频文件
const isVideoFile = (file: any): boolean => {
  if (file.type === 'video') return true
  return getFileTypeFromUrl(file.url) === 'video'
}

// 根据MIME类型判断文件类型
const getFileTypeFromMimeType = (mimeType: string): string => {
  if (!mimeType) return 'document'

  if (mimeType.startsWith('image/')) {
    return 'image'
  } else if (mimeType.startsWith('audio/')) {
    return 'audio'
  } else if (mimeType.startsWith('video/')) {
    return 'video'
  } else {
    return 'document'
  }
}

// 基于MIME类型判断是否为图片文件
const isImageFileByMimeType = (mimeType: string): boolean => {
  return mimeType && mimeType.startsWith('image/')
}

// 基于MIME类型判断是否为文档文件
const isDocumentFileByMimeType = (mimeType: string): boolean => {
  if (!mimeType) return true
  return mimeType.startsWith('text/') ||
         mimeType.includes('pdf') ||
         mimeType.includes('document') ||
         mimeType.includes('spreadsheet') ||
         mimeType.includes('presentation') ||
         mimeType.includes('msword') ||
         mimeType.includes('ms-excel') ||
         mimeType.includes('ms-powerpoint') ||
         mimeType.includes('officedocument')
}

// 基于MIME类型判断是否为音频文件
const isAudioFileByMimeType = (mimeType: string): boolean => {
  return mimeType && mimeType.startsWith('audio/')
}

// 基于MIME类型判断是否为视频文件
const isVideoFileByMimeType = (mimeType: string): boolean => {
  return mimeType && mimeType.startsWith('video/')
}

// 优化的消息数据获取函数 - 避免智能体流式输出时频繁调用复杂数据处理
const getOptimizedMessageData = (message: Message) => {
  // 对于智能体流式输出的消息，返回空数据避免不必要的处理
  // 智能体回复通常是纯文本或markdown，不包含图表、音频、视频等复杂内容
  if (message.streaming && message.role === 'assistant' &&
      (message.contentType === 'text' || message.contentType === 'markdown')) {
    return {
      images: undefined,
      audioContent: undefined,
      videoContent: undefined,
      files: undefined,
      chartData: undefined,
      chartType: undefined,
      flowchartData: undefined
    }
  }

  // 对于其他情况，使用原有的getMessageData函数
  return props.getMessageData(message)
}
</script>

<style scoped>
/* 思考动画相关样式 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.animate-shimmer {
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

/* 流式输出时的光标效果 */
.streaming-cursor::after {
  content: '|';
  animation: blink 1s infinite;
  color: #3b82f6;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
