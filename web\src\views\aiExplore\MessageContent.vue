<template>
  <div class="message-content">
    <!-- 主要内容渲染 -->
    <div v-if="content" class="main-content">
      <!-- Markdown渲染器 -->
      <MarkdownRenderer
        v-if="contentType === 'markdown'"
        :content="content"
        :is-typing="isTyping"
        :streaming="streaming"
        @link-click="handleLinkClick"
      />

      <!-- 纯文本渲染器 -->
      <PlainTextRenderer
        v-else-if="contentType === 'text'"
        :content="content"
        :is-typing="isTyping"
        :streaming="streaming"
        @link-click="handleLinkClick"
      />

      <!-- HTML内容渲染 -->
      <div v-else-if="contentType === 'html'" v-html="sanitizeHtml(content)" class="html-content"></div>

      <!-- 默认文本渲染 -->
      <PlainTextRenderer
        v-else
        :content="content"
        :is-typing="isTyping"
        :streaming="streaming"
        @link-click="handleLinkClick"
      />
    </div>

    <!-- 根据contentType渲染特定内容 -->
    <!-- 图片画廊 -->
    <ImageGallery
      v-if="contentType === 'image' && normalizedImages && normalizedImages.length > 0"
      :images="normalizedImages"
      @image-click="handleImageClick"
    />

    <!-- 音频播放器 -->
    <AudioPlayer
      v-if="contentType === 'audio' && data?.audioContent"
      :audio="data.audioContent"
    />

    <!-- 视频播放器 -->
    <VideoPlayer
      v-if="contentType === 'video' && data?.videoContent"
      :video="data.videoContent"
    />

    <!-- 调试信息 -->
    <div v-if="isDev && contentType === 'video'" class="debug-info">
      <p>Debug: contentType = {{ contentType }}, videoContent = {{ JSON.stringify(data?.videoContent) }}</p>
    </div>

    <!-- 文件渲染器 -->
    <FileRenderer
      v-if="contentType === 'file' && data?.files && data.files.length > 0"
      v-for="file in data.files"
      :key="file.url"
      :file="file"
      :content-type="getFileContentType(file.type)"
      @download="handleFileDownload"
    />

    <!-- 图表渲染器 -->
    <ChartRenderer
      v-if="contentType === 'chart' && data?.chartData && data?.chartType"
      :data="data.chartData"
      :type="data.chartType as 'line' | 'bar' | 'pie' | 'scatter'"
    />

    <!-- 流程图渲染器 -->
    <FlowchartRenderer
      v-if="contentType === 'flowchart' && data?.flowchartData"
      :data="data.flowchartData"
    />

    <!-- 混合内容：当contentType为text/markdown/html时，也可能包含其他类型的内容 -->
    <template v-if="['text', 'markdown', 'html'].includes(contentType)">
      <!-- 图片画廊 -->
      <ImageGallery
        v-if="normalizedImages && normalizedImages.length > 0"
        :images="normalizedImages"
        @image-click="handleImageClick"
      />

      <!-- 音频播放器 -->
      <AudioPlayer
        v-if="data?.audioContent"
        :audio="data.audioContent"
      />

      <!-- 视频播放器 -->
      <VideoPlayer
        v-if="data?.videoContent"
        :video="data.videoContent"
      />

      <!-- 文件渲染器 -->
      <FileRenderer
        v-if="data?.files && data.files.length > 0"
        v-for="file in data.files"
        :key="file.url"
        :file="file"
        :content-type="getFileContentType(file.type)"
        @download="handleFileDownload"
      />

      <!-- 图表渲染器 -->
      <ChartRenderer
        v-if="data?.chartData && data?.chartType"
        :data="data.chartData"
        :type="data.chartType as 'line' | 'bar' | 'pie' | 'scatter'"
      />

      <!-- 流程图渲染器 -->
      <FlowchartRenderer
        v-if="data?.flowchartData"
        :data="data.flowchartData"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, nextTick } from 'vue'
import DOMPurify from 'dompurify'

// 导入所有渲染器组件
import MarkdownRenderer from '@/views/aiExplore/renderers/MarkdownRenderer.vue'
import PlainTextRenderer from '@/views/aiExplore/renderers/PlainTextRenderer.vue'
import ImageGallery from '@/views/aiExplore/renderers/ImageGallery.vue'
import AudioPlayer from '@/views/aiExplore/renderers/AudioPlayer.vue'
import VideoPlayer from '@/views/aiExplore/renderers/VideoPlayer.vue'
import FileRenderer from '@/views/aiExplore/renderers/FileRenderer.vue'
import ChartRenderer from '@/views/aiExplore/renderers/ChartRenderer.vue'
import FlowchartRenderer from '@/views/aiExplore/renderers/FlowchartRenderer.vue'

// 导入工具类
import { StreamContentParser, mockStreamData } from '@/utils/streamContentParser'
import { RendererSampleGenerator, ContentMatcher } from '@/utils/rendererSamples'
import type { RendererType } from '@/types/renderer'

// Props定义
interface Props {
  content?: string
  contentType?: 'text' | 'markdown' | 'html' | 'audio' | 'video' | 'file' | 'chart' | 'image' | 'flowchart'
  isTyping?: boolean
  streaming?: boolean
  // 统一的数据对象，包含所有类型的数据
  data?: {
    images?: Array<string | { url: string; alt?: string; caption?: string }>
    audioContent?: {
      url: string
      title?: string
      duration?: string | number
    }
    videoContent?: {
      url: string
      title?: string
      poster?: string
    }
    files?: Array<{
      name: string
      url: string
      type: string
      size?: number
    }>
    chartData?: any
    chartType?: string
    flowchartData?: any
  }
}

const props = withDefaults(defineProps<Props>(), {
  contentType: 'text',
  isTyping: false,
  streaming: false
})

// 开发环境标识
const isDev = import.meta.env.DEV

// Emits定义
const emit = defineEmits<{
  'link-click': [url: string]
  'image-click': [image: any, index: number]
  'file-download': [file: any]
}>()

// 标准化图片数据
const normalizedImages = computed(() => {
  const images = props.data?.images
  if (!images || images.length === 0) return []

  return images.map((image: any) => {
    if (typeof image === 'string') {
      return {
        url: image,
        alt: '图片',
        caption: undefined
      }
    }
    return image
  })
})

// 获取文件内容类型
const getFileContentType = (mimeType: string): 'text' | 'pdf' | 'word' | 'excel' => {
  if (mimeType.includes('pdf')) return 'pdf'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'word'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'excel'
  return 'text'
}

// HTML内容清理
const sanitizeHtml = (html: string) => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'code', 'pre', 'a', 'img', 'table', 'thead',
      'tbody', 'tr', 'th', 'td', 'div', 'span', 'hr'
    ],
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'class', 'id', 'target', 'rel'
    ]
  })
}

// 处理图片点击
const handleImageClick = (image: any, index: number) => {
  emit('image-click', image, index)
}

// 处理链接点击
const handleLinkClick = (url: string) => {
  emit('link-click', url)
}

// 处理文件下载
const handleFileDownload = (file: any) => {
  emit('file-download', file)
}

// 消息接口定义
interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  contentType?: 'text' | 'markdown' | 'html' | 'audio' | 'video' | 'file' | 'chart' | 'image' | 'flowchart'
  timestamp: Date
  files?: File[] | any[]
  images?: string[]
  audioContent?: {
    url: string
    title?: string
    duration?: number
  }
  videoContent?: {
    url: string
    title?: string
    duration?: number
    poster?: string
  }
  chartData?: {
    type: string
    data: any
    options?: any
  }
  chartType?: 'bar' | 'line' | 'pie' | 'scatter'
  flowchartData?: {
    type: 'mermaid' | 'drawio' | 'custom'
    data: string | object
  }
  isTyping?: boolean
  streaming?: boolean
}

// 模拟AI回复函数
const simulateAIResponse = async (
  message: Message,
  selectedRenderer: RendererType = 'text',
  onProgress?: (progress: number) => void,
  onStageChange?: (stage: string) => void
) => {
  // 根据选中的渲染器类型获取对应的样例数据
  let selectedResponse: string

  if (selectedRenderer && selectedRenderer !== 'text') {
    // 如果选中了特定的渲染器，使用对应的样例数据
    const sample = RendererSampleGenerator.getSample(selectedRenderer)
    selectedResponse = sample.content
    // 移除渲染器样例数据选择的控制台输出
    // console.log(`使用${selectedRenderer}渲染器的样例数据`)
  } else {
    // 如果没有选中特定渲染器，随机选择回复内容
    const responseOptions = [
      mockStreamData.plainText,        // PlainTextRenderer
      mockStreamData.markdownText,     // MarkdownRenderer
      mockStreamData.chartText,        // ChartRenderer
      mockStreamData.imageText,        // ImageGallery
      mockStreamData.audioText,        // AudioPlayer
      mockStreamData.videoText,        // VideoPlayer
      mockStreamData.fileText,         // FileRenderer
      mockStreamData.flowchartText,    // FlowchartRenderer
      mockStreamData.htmlText,         // HTML内容
      mockStreamData.mixedContent      // 复合内容（多种渲染器）
    ]
    selectedResponse = responseOptions[Math.floor(Math.random() * responseOptions.length)]
  }

  // 开始智能流式输出
  await simulateIntelligentStream(message, selectedResponse, onProgress, onStageChange)
}

// 智能流式输出函数 - 按token速度输出（30token/s）
const simulateIntelligentStream = async (
  message: Message,
  fullContent: string,
  onProgress?: (progress: number) => void,
  onStageChange?: (stage: string) => void
) => {
  message.streaming = true
  message.content = ''

  // 使用解析器来识别特殊内容
  const parser = new StreamContentParser()

  // 将内容按token分组进行输出
  const tokens = tokenizeForStreaming(fullContent)

  // 30 tokens/s = 1000ms / 30 = 33.33ms per token
  const tokenDelay = 1000 / 30

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i]

    // 更新显示内容 - 使用响应式更新
    const newContent = message.content + token
    message.content = newContent

    // 报告进度
    const progress = Math.round((i / tokens.length) * 100)
    onProgress?.(progress)

    // 移除流式输出更新的控制台输出
    // if (import.meta.env.DEV && i % 10 === 0) {
    //   console.log('流式输出更新:', message.content.length, '字符, contentType:', message.contentType)
    // }

    try {
      // 使用解析器处理当前token
      const parsedData = parser.addChunk(token)

      // 检查是否识别到了特殊内容类型
      const hasSpecialContent = parsedData.contentType !== 'text' ||
                               parsedData.chartData ||
                               (parsedData.images && parsedData.images.length > 0) ||
                               parsedData.audioContent ||
                               parsedData.videoContent ||
                               (parsedData.files && parsedData.files.length > 0) ||
                               parsedData.flowchartData

      if (hasSpecialContent) {
        // 识别到特殊内容，更新消息数据
        const validContentTypes = ['text', 'markdown', 'html', 'audio', 'video', 'file', 'chart', 'image', 'flowchart']
        const newContentType = validContentTypes.includes(parsedData.contentType)
          ? parsedData.contentType
          : 'text'

        // 响应式更新contentType
        message.contentType = newContentType as any

        // 只有在检测到完整的特殊内容时才设置相应的属性
        if (parsedData.chartData && parsedData.isComplete) {
          message.chartData = parsedData.chartData
          message.chartType = parsedData.chartType as any
        }

        if (parsedData.images && parsedData.images.length > 0) {
          message.images = parsedData.images.map(img => img.url)
        }

        if (parsedData.audioContent) {
          message.audioContent = parsedData.audioContent
        }

        if (parsedData.videoContent) {
          message.videoContent = parsedData.videoContent
          // 移除视频内容检测的控制台输出
          // console.log('检测到视频内容:', parsedData.videoContent)
        }

        if (parsedData.files && parsedData.files.length > 0) {
          message.files = parsedData.files
        }

        if (parsedData.flowchartData) {
          message.flowchartData = parsedData.flowchartData
        }
      } else {
        // 未识别到特殊内容，保持为文本类型
        message.contentType = 'text'
      }
    } catch (error) {
      // 如果解析器出错，确保使用默认的文本类型
      console.warn('解析器处理token时出错:', error)
      message.contentType = 'text'
    }

    // 固定延迟，确保30token/s的速度
    await new Promise(resolve => setTimeout(resolve, tokenDelay))

    // 强制触发Vue的响应式更新
    await nextTick()
  }

  // 结束流式输出
  message.streaming = false
  parser.reset()
}

// 按token分词用于流式输出 - 模拟真实的GPT token分割
const tokenizeForStreaming = (content: string): string[] => {
  const tokens: string[] = []
  let currentToken = ''

  for (let i = 0; i < content.length; i++) {
    const char = content[i]

    // 根据字符类型决定token边界
    if (char === ' ' || char === '\n' || char === '\t') {
      // 空白字符：结束当前token，空白字符作为独立token
      if (currentToken) {
        tokens.push(currentToken)
        currentToken = ''
      }
      tokens.push(char)
    } else if (/[.,!?;:]/.test(char)) {
      // 标点符号：结束当前token，标点作为独立token
      if (currentToken) {
        tokens.push(currentToken)
        currentToken = ''
      }
      tokens.push(char)
    } else if (/[\u4e00-\u9fff]/.test(char)) {
      // 中文字符：每个字符作为独立token
      if (currentToken) {
        tokens.push(currentToken)
        currentToken = ''
      }
      tokens.push(char)
    } else {
      // 英文字母和数字：累积到当前token
      currentToken += char
    }
  }

  // 处理最后的token
  if (currentToken) {
    tokens.push(currentToken)
  }

  return tokens.filter(token => token.length > 0)
}

// 暴露simulateAIResponse函数供外部使用
defineExpose({
  simulateAIResponse
})
</script>

<style scoped>
.message-content {
  word-wrap: break-word;
  word-break: break-word;
}

.prose {
  color: inherit;
}

.prose h1, .prose h2, .prose h3 {
  margin-top: 0;
}

.prose p {
  margin-bottom: 0.75rem;
}

.prose p:last-child {
  margin-bottom: 0;
}

.markdown-content {
  line-height: 1.6;
}

.html-content {
  line-height: 1.6;
}

.images-content img {
  transition: transform 0.2s ease;
}

.images-content img:hover {
  transform: scale(1.02);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid.md\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid.grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
