<template>
  <div class="settings-page min-h-screen flex">
    <!-- 左侧固定区域 -->
    <div class="settings-sidebar w-80 bg-white shadow-lg border-r border-gray-200 flex flex-col">
      <!-- 页面标题 -->
      <div class="page-header p-6 border-b border-gray-200">
        <h1 class="text-2xl font-bold text-gray-900">系统设置</h1>
        <p class="text-sm text-gray-600 mt-2">用户管理、智能体发布、接口授权、平台公告等系统设置</p>
      </div>

      <!-- 设置导航 -->
      <div class="settings-nav flex-1 p-4">
        <div class="nav-tabs space-y-2">
          <button
            v-for="tab in settingsTabs"
            :key="tab.key"
            @click="navigateToTab(tab.key)"
            class="nav-tab w-full px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center text-left"
            :class="{
              'bg-blue-50 text-blue-600 border border-blue-200': activeTab === tab.key,
              'text-gray-700 hover:text-blue-600 hover:bg-blue-50': activeTab !== tab.key
            }"
          >
            <span class="mr-3 text-lg">{{ tab.icon }}</span>
            <span>{{ tab.name }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="settings-content flex-1 min-h-0 overflow-auto">
      <div class="p-6">
        <!-- 使用路由视图显示子组件 -->
        <div class="settings-section">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 当前激活的标签页，基于路由路径
const activeTab = computed(() => {
  const path = route.path
  if (path.includes('/settings/')) {
    return path.split('/settings/')[1]
  }
  return 'system'
})

// 导航到指定标签页
const navigateToTab = (tabKey: string) => {
  router.push(`/settings/${tabKey}`)
}

// 设置标签页
const settingsTabs = [
  { key: 'system', name: '系统设置', icon: '⚙️' },
  { key: 'users', name: '用户管理', icon: '👥' },
  { key: 'units', name: '单位管理', icon: '🏢' },
  { key: 'tenants', name: '租户管理', icon: '🏬' },
  { key: 'dict', name: '字典管理', icon: '📚' },
  { key: 'vector-database', name: '向量数据库管理', icon: '🗄️' },
  { key: 'file-storage', name: '文件存储管理', icon: '💾' },
  { key: 'knowledge-config', name: '知识库配置', icon: '🧠' },
  { key: 'knowledge-api', name: '知识库API密钥', icon: '🔑' },
  { key: 'agent-api', name: '智能体API密钥', icon: '🤖' },
  { key: 'model-config', name: '模型配置', icon: '🔧' },
  { key: 'third-platform-agents', name: '第三方智能体', icon: '🔗' },
  { key: 'rabbitmq', name: 'RabbitMQ管理', icon: '🐰' },
  { key: 'announcements', name: '公告发布', icon: '📢' },
  { key: 'help-docs', name: '帮助文档配置', icon: '📖' }
]
</script>

<style scoped>
.settings-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.settings-sidebar {
  position: fixed;
  left: 0;
  top: 70px; /* 顶部导航栏高度 */
  height: calc(100vh - 70px); /* 减去顶部导航栏高度 */
  z-index: 40; /* 低于顶部导航栏的z-index: 50 */
  overflow-y: auto;
}

.settings-content {
  margin-left: 320px; /* 左侧边栏宽度 + 间距 */
  height: calc(100vh - 70px); /* 减去顶部导航栏高度 */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
}

.nav-tab {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-tab:hover::before {
  left: 100%;
}

.nav-tab:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.nav-tab.bg-blue-50 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
}

.settings-section {
  animation: slideInRight 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .settings-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    top: 0;
  }

  .settings-content {
    margin-left: 0;
    margin-top: 0;
    height: auto;
    border-radius: 0;
  }

  .settings-page {
    flex-direction: column;
  }

  .nav-tabs {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .nav-tabs {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 1.5rem;
  }

  .nav-tab {
    padding: 0.75rem 1rem;
  }
}
</style>
