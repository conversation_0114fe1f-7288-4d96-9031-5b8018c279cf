package com.xhcai.modules.rag.dto;

import com.xhcai.modules.rag.entity.DocumentSegment;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 搜索结果DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "搜索结果")
public class SearchResult {

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "查询内容")
    private String query;

    @Schema(description = "搜索类型")
    private String searchType;

    @Schema(description = "搜索结果列表")
    private List<SearchResultItem> results;

    @Schema(description = "总结果数量")
    private Integer totalCount;

    @Schema(description = "搜索耗时（毫秒）")
    private Double searchTime;

    @Schema(description = "是否有更多结果")
    private Boolean hasMore;

    @Schema(description = "搜索时间")
    private LocalDateTime searchTime2;

    @Schema(description = "搜索参数")
    private Map<String, Object> searchParams;

    @Schema(description = "搜索统计信息")
    private Map<String, Object> stats;

    // 构造函数
    public SearchResult() {}

    public SearchResult(String datasetId, String query, String searchType) {
        this.datasetId = datasetId;
        this.query = query;
        this.searchType = searchType;
        this.searchTime2 = LocalDateTime.now();
    }

    // Getters and Setters
    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public List<SearchResultItem> getResults() {
        return results;
    }

    public void setResults(List<SearchResultItem> results) {
        this.results = results;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Double getSearchTime() {
        return searchTime;
    }

    public void setSearchTime(Double searchTime) {
        this.searchTime = searchTime;
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public LocalDateTime getSearchTime2() {
        return searchTime2;
    }

    public void setSearchTime2(LocalDateTime searchTime2) {
        this.searchTime2 = searchTime2;
    }

    public Map<String, Object> getSearchParams() {
        return searchParams;
    }

    public void setSearchParams(Map<String, Object> searchParams) {
        this.searchParams = searchParams;
    }

    public Map<String, Object> getStats() {
        return stats;
    }

    public void setStats(Map<String, Object> stats) {
        this.stats = stats;
    }

    /**
     * 搜索结果项
     */
    @Schema(description = "搜索结果项")
    public static class SearchResultItem {

        @Schema(description = "分段ID")
        private String segmentId;

        @Schema(description = "文档ID")
        private String documentId;

        @Schema(description = "文档名称")
        private String documentName;

        @Schema(description = "分段内容")
        private String content;

        @Schema(description = "高亮内容")
        private String highlightContent;

        @Schema(description = "相似度分数")
        private Double score;

        @Schema(description = "分段位置")
        private Integer position;

        @Schema(description = "字符数")
        private Integer wordCount;

        @Schema(description = "文档类型")
        private String docType;

        @Schema(description = "文档语言")
        private String docLanguage;

        @Schema(description = "元数据")
        private Map<String, Object> metadata;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        // 构造函数
        public SearchResultItem() {}

        public SearchResultItem(DocumentSegment segment, Double score) {
            this.segmentId = segment.getId();
            this.documentId = segment.getDocumentId();
            this.content = segment.getContent();
            this.score = score;
            this.position = segment.getPosition();
            this.wordCount = segment.getWordCount();
            this.createTime = segment.getCreateTime();
        }

        // Getters and Setters
        public String getSegmentId() {
            return segmentId;
        }

        public void setSegmentId(String segmentId) {
            this.segmentId = segmentId;
        }

        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public String getDocumentName() {
            return documentName;
        }

        public void setDocumentName(String documentName) {
            this.documentName = documentName;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getHighlightContent() {
            return highlightContent;
        }

        public void setHighlightContent(String highlightContent) {
            this.highlightContent = highlightContent;
        }

        public Double getScore() {
            return score;
        }

        public void setScore(Double score) {
            this.score = score;
        }

        public Integer getPosition() {
            return position;
        }

        public void setPosition(Integer position) {
            this.position = position;
        }

        public Integer getWordCount() {
            return wordCount;
        }

        public void setWordCount(Integer wordCount) {
            this.wordCount = wordCount;
        }

        public String getDocType() {
            return docType;
        }

        public void setDocType(String docType) {
            this.docType = docType;
        }

        public String getDocLanguage() {
            return docLanguage;
        }

        public void setDocLanguage(String docLanguage) {
            this.docLanguage = docLanguage;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }
    }
}
