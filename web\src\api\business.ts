/**
 * 业务项目相关API
 */
import { apiClient } from '@/utils/apiClient'
import type { ApiResponse, PageResult } from '@/types/api'
import type { SysUserVO } from '@/types/system'

// 项目团队成员类型
export interface ProjectTeamMember {
  id: string
  projectId: string
  userId: string
  role: string
  joinTime: string
  createTime: string
  updateTime: string
  // 关联的用户信息
  user: SysUserVO
}

// 业务项目类型
export interface BusinessProject {
  id: string
  name: string
  description?: string
  ownerId: string
  environment: 'production' | 'test' | 'development'
  status: 'active' | 'inactive' | 'maintenance'
  icon?: string
  iconColor?: string
  remark?: string
  deleted: number
  createBy?: string
  createTime: string
  updateBy?: string
  updateTime: string
  tenantId?: string

  // 关联信息
  owner?: SysUserVO
  teamMembers?: ProjectTeamMember[]

  // 统计信息
  agentCount?: number
  knowledgeCount?: number
  graphCount?: number
  agentRunningCount?: number
  knowledgeBuiltCount?: number
  graphRelationCount?: number
}

// 查询参数类型
export interface BusinessProjectQuery {
  name?: string
  description?: string
  environment?: 'production' | 'test' | 'development'
  status?: 'active' | 'inactive' | 'maintenance'
  ownerId?: string
  createTimeStart?: string
  createTimeEnd?: string
  current?: number
  size?: number
}

// 创建业务项目请求
export interface CreateBusinessProjectRequest {
  name: string
  description?: string
  ownerId: string
  environment: 'production' | 'test' | 'development'
  icon?: string
  iconColor?: string
  remark?: string
  teamMembers?: Array<{
    userId: string
    role: string
  }>
}

// 更新业务项目请求
export interface UpdateBusinessProjectRequest {
  id: string
  name?: string
  description?: string
  ownerId?: string
  environment?: 'production' | 'test' | 'development'
  status?: 'active' | 'inactive' | 'maintenance'
  icon?: string
  iconColor?: string
  remark?: string
}

// 项目团队成员操作请求
export interface ProjectTeamMemberRequest {
  projectId: string
  userId: string
  role: string
}

// API方法
export const businessProjectApi = {
  /**
   * 获取业务项目列表
   */
  getProjects: (params?: BusinessProjectQuery): Promise<ApiResponse<PageResult<BusinessProject>>> => {
    return apiClient.get('/api/agent/business-projects', { params })
  },

  /**
   * 获取业务项目详情
   */
  getProject: (id: string): Promise<ApiResponse<BusinessProject>> => {
    return apiClient.get(`/api/agent/business-projects/${id}`)
  },

  /**
   * 创建业务项目
   */
  createProject: (data: CreateBusinessProjectRequest): Promise<ApiResponse<BusinessProject>> => {
    return apiClient.post('/api/agent/business-projects', data)
  },

  /**
   * 更新业务项目
   */
  updateProject: (id: string, data: UpdateBusinessProjectRequest): Promise<ApiResponse<BusinessProject>> => {
    return apiClient.put(`/api/agent/business-projects/${id}`, data)
  },

  /**
   * 删除业务项目
   */
  deleteProject: (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/api/agent/business-projects/${id}`)
  },

  /**
   * 添加项目团队成员
   */
  addProjectTeamMember: (data: ProjectTeamMemberRequest): Promise<ApiResponse<ProjectTeamMember>> => {
    return apiClient.post('/api/agent/business-projects/team-members', data)
  },

  /**
   * 移除项目团队成员
   */
  removeProjectTeamMember: (projectId: string, userId: string): Promise<ApiResponse<void>> => {
    return apiClient.delete(`/api/agent/business-projects/${projectId}/team-members/${userId}`)
  },

  /**
   * 更新项目团队成员角色
   */
  updateProjectTeamMemberRole: (projectId: string, userId: string, role: string): Promise<ApiResponse<ProjectTeamMember>> => {
    return apiClient.put(`/api/agent/business-projects/${projectId}/team-members/${userId}`, { role })
  },

  /**
   * 批量更新项目团队
   */
  updateProjectTeam: (projectId: string, teamMembers: Array<{ userId: string; role: string }>): Promise<ApiResponse<BusinessProject>> => {
    return apiClient.put(`/api/agent/business-projects/${projectId}/team`, { teamMembers })
  },

  /**
   * 切换项目环境
   */
  switchProjectEnvironment: (id: string, environment: 'production' | 'test' | 'development'): Promise<ApiResponse<BusinessProject>> => {
    return apiClient.put(`/api/agent/business-projects/${id}/environment`, { environment })
  },

  /**
   * 获取项目统计信息
   */
  getProjectStats: (id: string): Promise<ApiResponse<{
    agentCount: number
    knowledgeCount: number
    graphCount: number
    agentRunningCount: number
    knowledgeBuiltCount: number
    graphRelationCount: number
  }>> => {
    return apiClient.get(`/api/agent/business-projects/${id}/stats`)
  },

  /**
   * 获取系统用户列表（用于团队成员选择）
   */
  getSystemUsers: (params?: {
    keyword?: string
    deptId?: string
    status?: number
    current?: number
    size?: number
  }): Promise<ApiResponse<PageResult<SysUserVO>>> => {
    return apiClient.get('/api/system/users', { params })
  }
}

export default businessProjectApi
