# 第三方智能体账号密码字段调整说明

## 概述

本次更新将第三方智能体用户的密码字段从 `api_key` 调整为 `pwd`，并采用与系统用户一致的 BCrypt 加密方式。

## 主要变更

### 1. 数据库变更

- **新增字段**: `pwd VARCHAR(100)` - 密码字段（BCrypt加密存储）
- **字段调整**: `api_key` 字段改为可空（保持向后兼容）
- **索引优化**: 为 `pwd` 字段添加索引以提高查询性能

### 2. 实体类变更

**ThirdPlatformAccount.java**
- 新增 `pwd` 字段
- `api_key` 字段改为可选

### 3. DTO变更

**ThirdPlatformAccountCreateDTO.java**
- 新增 `pwd` 字段（必填，6-50字符）
- `api_key` 字段改为可选

**ThirdPlatformAccountUpdateDTO.java**
- 新增 `pwd` 字段（可选，不传则不修改）
- `api_key` 字段改为可选

**新增 PasswordVerifyDTO.java**
- 用于密码验证的专用DTO

### 4. VO变更

**ThirdPlatformAccountVO.java**
- 新增 `hasPwd` 字段，表示是否设置了密码
- 不返回明文密码，保证安全性

### 5. 服务层变更

**ThirdPlatformAccountServiceImpl.java**
- 集成 BCryptPasswordEncoder 进行密码加密
- 创建账号时自动加密密码
- 更新账号时支持密码修改（可选）
- 新增密码验证方法

### 6. 控制器变更

**ThirdPlatformAccountController.java**
- 新增密码验证接口 `POST /{id}/verify-password`

### 7. 数据访问层变更

**ThirdPlatformAccountMapper.java**
- 查询结果中添加 `has_pwd` 字段
- 支持密码相关的数据查询

### 8. 前端接口变更

**ThirdPlatformAccount.ts**
- 更新所有相关的 TypeScript 接口定义
- 新增密码验证API方法

## 加密方式

采用与系统用户一致的 BCrypt 加密算法：
- **加密强度**: 默认强度（10轮）
- **安全性**: BCrypt 是业界标准的密码哈希算法
- **兼容性**: 与系统用户密码加密方式完全一致

## API接口变更

### 新增接口

```http
POST /api/third-platform/account/{id}/verify-password
Content-Type: application/json

{
  "password": "原始密码"
}
```

### 修改接口

**创建账号**
```http
POST /api/third-platform/account
Content-Type: application/json

{
  "platformId": "dify",
  "accountName": "我的账号",
  "pwd": "123456",          // 新增必填字段
  "apiKey": "app-xxx",      // 改为可选
  "remark": "备注",
  "status": 1
}
```

**更新账号**
```http
PUT /api/third-platform/account/{id}
Content-Type: application/json

{
  "accountName": "我的账号",
  "pwd": "新密码",          // 新增可选字段
  "apiKey": "app-xxx",      // 改为可选
  "remark": "备注",
  "status": 1
}
```

## 数据迁移

执行以下SQL脚本进行数据库结构更新：

```sql
-- 执行迁移脚本
source modules/xhcai-agent/src/main/resources/db/migration/add_pwd_field_to_third_platform_account.sql
```

## 向后兼容性

- 保留了 `api_key` 字段，确保现有数据不受影响
- 新创建的账号必须设置密码
- 现有账号可以通过更新接口设置密码

## 安全性提升

1. **密码加密**: 使用 BCrypt 算法，无法逆向解密
2. **密码验证**: 提供专用的密码验证接口
3. **数据脱敏**: 前端不返回密码明文，只返回是否设置密码的标识
4. **权限控制**: 所有密码相关操作都需要相应权限

## 注意事项

1. **密码长度**: 6-50个字符
2. **加密存储**: 数据库中存储的是加密后的密码哈希值
3. **密码验证**: 使用专用接口进行密码验证，不要在前端进行密码比较
4. **数据迁移**: 升级前请备份数据库
5. **测试验证**: 升级后请测试账号的创建、更新、验证功能

## 测试建议

1. 测试新账号创建（必须包含密码）
2. 测试现有账号更新（密码可选）
3. 测试密码验证功能
4. 测试前端界面的密码相关功能
5. 验证数据库字段和索引是否正确创建
