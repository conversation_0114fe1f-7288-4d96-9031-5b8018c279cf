package com.xhcai.modules.dify.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * Dify聊天请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Schema(description = "Dify聊天请求")
public class DifyChatRequestDTO {

    /**
     * 输入参数
     */
    @Schema(description = "输入参数")
    private Map<String, Object> inputs;

    /**
     * 用户查询内容
     */
    @Schema(description = "用户查询内容", example = "What are the specs of the iPhone 13 Pro Max?")
    @NotBlank(message = "查询内容不能为空")
    private String query;

    /**
     * 响应模式
     */
    @Schema(description = "响应模式", example = "streaming", allowableValues = {"blocking", "streaming"})
    @JsonProperty("response_mode")
    private String responseMode = "streaming";

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 用户标识
     */
    @Schema(description = "用户标识", example = "abc-123")
    @NotBlank(message = "用户标识不能为空")
    private String user;

    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    private List<DifyFileDTO> files;

    /**
     * 自动生成名称
     */
    @Schema(description = "自动生成名称")
    @JsonProperty("auto_generate_name")
    private Boolean autoGenerateName;

    public DifyChatRequestDTO() {
    }

    public DifyChatRequestDTO(String query, String user) {
        this.query = query;
        this.user = user;
    }

    public DifyChatRequestDTO(String query, String user, String conversationId) {
        this.query = query;
        this.user = user;
        this.conversationId = conversationId;
    }

    // Getters and Setters
    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getResponseMode() {
        return responseMode;
    }

    public void setResponseMode(String responseMode) {
        this.responseMode = responseMode;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public List<DifyFileDTO> getFiles() {
        return files;
    }

    public void setFiles(List<DifyFileDTO> files) {
        this.files = files;
    }

    public Boolean getAutoGenerateName() {
        return autoGenerateName;
    }

    public void setAutoGenerateName(Boolean autoGenerateName) {
        this.autoGenerateName = autoGenerateName;
    }

    @Override
    public String toString() {
        return "DifyChatRequestDTO{" +
                "inputs=" + inputs +
                ", query='" + query + '\'' +
                ", responseMode='" + responseMode + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", user='" + user + '\'' +
                ", files=" + files +
                ", autoGenerateName=" + autoGenerateName +
                '}';
    }
}
