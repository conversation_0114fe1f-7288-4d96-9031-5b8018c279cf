package com.xhcai.modules.dify.service;

import com.xhcai.modules.dify.dto.chat.DifyChatRequestDTO;
import com.xhcai.modules.dify.dto.chat.DifyChatResponseDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

/**
 * Dify聊天服务接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IDifyChatService {

    /**
     * 发送聊天消息（阻塞模式）
     *
     * @param requestDTO 聊天请求
     * @return 聊天响应
     */
    DifyChatResponseDTO chat(DifyChatRequestDTO requestDTO);

    /**
     * 发送聊天消息（流式模式）
     *
     * @param requestDTO 聊天请求
     * @return 流式响应
     */
    Flux<DifyChatResponseDTO> streamChat(DifyChatRequestDTO requestDTO);

    /**
     * 发送聊天消息（SSE流式模式）
     *
     * @param requestDTO 聊天请求
     * @return SSE发射器
     */
    SseEmitter streamChatSse(DifyChatRequestDTO requestDTO);

    /**
     * 发送聊天消息（SSE流式模式-调试版）
     *
     * @param requestDTO 聊天请求
     * @return SSE发射器
     */
    SseEmitter streamChatSseDebug(DifyChatRequestDTO requestDTO);

    /**
     * 获取会话历史
     *
     * @param conversationId 会话ID
     * @param user 用户标识
     * @return 会话历史
     */
    Object getConversationHistory(String conversationId, String user);

    /**
     * 删除会话
     *
     * @param conversationId 会话ID
     * @param user 用户标识
     * @return 是否成功
     */
    boolean deleteConversation(String conversationId, String user);

    /**
     * 重命名会话
     *
     * @param conversationId 会话ID
     * @param name 新名称
     * @param user 用户标识
     * @return 是否成功
     */
    boolean renameConversation(String conversationId, String name, String user);

    /**
     * 获取会话列表
     *
     * @param user 用户标识
     * @param page 页码
     * @param limit 每页数量
     * @return 会话列表
     */
    Object getConversations(String user, Integer page, Integer limit);

    /**
     * 停止生成
     *
     * @param taskId 任务ID
     * @param user 用户标识
     * @return 是否成功
     */
    boolean stopGeneration(String taskId, String user);

    /**
     * 获取应用参数
     *
     * @param user 用户标识
     * @return 应用参数
     */
    Object getAppParameters(String user);

    /**
     * 文件上传
     *
     * @param file 文件
     * @param user 用户标识
     * @return 文件信息
     */
    Object uploadFile(Object file, String user);

    /**
     * 音频转文字
     *
     * @param file 音频文件
     * @param user 用户标识
     * @return 转换结果
     */
    Object audioToText(Object file, String user);

    /**
     * 文字转音频
     *
     * @param text 文本内容
     * @param user 用户标识
     * @param streaming 是否流式
     * @return 音频数据
     */
    Object textToAudio(String text, String user, Boolean streaming);
}
