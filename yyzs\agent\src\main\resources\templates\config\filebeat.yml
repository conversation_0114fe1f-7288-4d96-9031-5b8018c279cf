# Filebeat Configuration Template
# ======================== Filebeat Configuration ============================

# ============================== Filebeat inputs =============================
filebeat.inputs:

# Each - is an input. Most options can be set at the input level, so
# you can use different inputs for various configurations.
# Below are the input specific configurations.

# filestream is an input for collecting log messages from files.
- type: filestream
  # Unique ID among all inputs, an ID is required.
  id: ${INPUT_ID:default-filestream}
  
  # Change to true to enable this input configuration.
  enabled: ${INPUT_ENABLED:true}
  
  # Paths that should be crawled and fetched. Glob based paths.
  paths:
    - ${LOG_PATHS:/var/log/*.log}
    - ${LOG_PATHS_2:/var/log/*/*.log}
    #- c:\programdata\elasticsearch\logs\*
  
  # Exclude lines. A list of regular expressions to match. It drops the lines that are
  # matching any regular expression from the list.
  # Line filtering happens after the parsers (see parsers) and before multiline (see multiline).
  #exclude_lines: ['^DBG']
  
  # Include lines. A list of regular expressions to match. It exports the lines that are
  # matching any regular expression from the list.
  # Line filtering happens after the parsers (see parsers) and before multiline (see multiline).
  #include_lines: ['^ERR', '^WARN']
  
  # Exclude files. A list of regular expressions to match. Filebeat drops the files that
  # are matching any regular expression from the list. By default, no files are dropped.
  #prospector.scanner.exclude_files: ['.gz$']
  
  # Optional additional fields. These fields can be freely picked
  # to add additional information to the crawled log files for filtering
  fields:
    logtype: ${LOG_TYPE:application}
    environment: ${ENVIRONMENT:production}
  fields_under_root: ${FIELDS_UNDER_ROOT:false}
  
  # Set to true to publish fields with null values in events.
  #keep_null: false

# ============================== Filebeat modules ==============================
filebeat.config.modules:
  # Glob pattern for configuration loading
  path: ${MODULES_PATH:${path.config}/modules.d/*.yml}
  
  # Set to true to enable config reloading
  reload.enabled: ${MODULES_RELOAD:false}
  
  # Period on which files under path should be checked for changes
  #reload.period: 10s

# ================================== General ===================================

# The name of the shipper that publishes the network data. It can be used to group
# all the transactions sent by a single shipper in the web interface.
name: ${SHIPPER_NAME:filebeat}

# The tags of the shipper are included in their own field with each
# transaction published.
tags: [${TAGS:"filebeat", "logs"}]

# Optional fields that you can specify to add additional information to the
# output.
fields:
  env: ${ENVIRONMENT:production}
  service: ${SERVICE_NAME:default}

# ================================= Dashboards =================================
# These settings control loading the sample dashboards to the Kibana index. Loading
# the dashboards is disabled by default and can be enabled either by setting the
# options here or by using the `setup` command.
setup.dashboards.enabled: ${DASHBOARDS_ENABLED:false}

# The URL from where to download the dashboards archive. By default this URL
# has a value which is computed based on the Beat name and version. For released
# versions, this URL points to the dashboard archive on the artifacts.elastic.co
# website.
#setup.dashboards.url:

# =================================== Kibana ===================================

# Starting with Beats version 6.0.0, the dashboards are loaded via the Kibana API.
# This requires a Kibana endpoint configuration.
setup.kibana:
  # Kibana Host
  # Scheme and port can be left out and will be set to the default (http and 5601)
  # In case you specify and additional path, the scheme is required: http://localhost:5601/path
  # IPv6 addresses should always be defined as: https://[2001:db8::1]:5601
  host: "${KIBANA_HOST:localhost:5601}"
  
  # Kibana Space ID
  # ID of the Kibana Space into which the dashboards should be loaded. By default,
  # the Default Space will be used.
  #space.id:

# =============================== Elastic Cloud ================================

# These settings simplify using Filebeat with the Elastic Cloud (https://cloud.elastic.co/).

# The cloud.id setting overwrites the `output.elasticsearch.hosts` and
# `setup.kibana.host` options.
# You can find the `cloud.id` in the Elastic Cloud web UI.
#cloud.id:

# The cloud.auth setting overwrites the `output.elasticsearch.username` and
# `output.elasticsearch.password` settings. The format is `<user>:<pass>`.
#cloud.auth:

# ================================== Outputs ===================================

# Configure what output to use when sending the data collected by the beat.

# ---------------------------- Elasticsearch Output ----------------------------
output.elasticsearch:
  # Array of hosts to connect to.
  hosts: [${ELASTICSEARCH_HOSTS:"localhost:9200"}]
  
  # Protocol - either `http` (default) or `https`.
  protocol: "${ELASTICSEARCH_PROTOCOL:http}"
  
  # Authentication credentials - either API key or username/password.
  #api_key: "id:api_key"
  username: "${ELASTICSEARCH_USERNAME:elastic}"
  password: "${ELASTICSEARCH_PASSWORD:changeme}"
  
  # Index name
  index: "${INDEX_NAME:filebeat-%{+yyyy.MM.dd}}"
  
  # Template settings
  template.name: "${TEMPLATE_NAME:filebeat}"
  template.pattern: "${TEMPLATE_PATTERN:filebeat-*}"

# ------------------------------ Logstash Output ----------------------------
#output.logstash:
  # The Logstash hosts
  #hosts: [${LOGSTASH_HOSTS:"localhost:5044"}]
  
  # Optional SSL. By default is off.
  # List of root certificates for HTTPS server verifications
  #ssl.certificate_authorities: ["/etc/pki/root/ca.pem"]
  
  # Certificate for SSL client authentication
  #ssl.certificate: "/etc/pki/client/cert.pem"
  
  # Client Certificate Key
  #ssl.key: "/etc/pki/client/cert.key"

# ================================= Processors =================================
processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_cloud_metadata: ~
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~

# ================================== Logging ===================================

# Sets log level. The default log level is info.
# Available log levels are: error, warning, info, debug
logging.level: ${LOG_LEVEL:info}

# At debug level, you can selectively enable logging only for some components.
# To enable all selectors use ["*"]. Examples of other selectors are "beat",
# "publisher", "service".
#logging.selectors: ["*"]

# ============================= X-Pack Monitoring ==============================
# Filebeat can export internal metrics to a central Elasticsearch monitoring
# cluster.  This requires xpack monitoring to be enabled in Elasticsearch.  The
# reporting is disabled by default.

# Set to true to enable the monitoring reporter.
monitoring.enabled: ${MONITORING_ENABLED:false}

# Sets the UUID of the Elasticsearch cluster under which monitoring data for this
# Filebeat instance will appear in the Stack Monitoring UI. If output.elasticsearch
# is enabled, the UUID is derived from the Elasticsearch cluster referenced by output.elasticsearch.
#monitoring.cluster_uuid:

# Uncomment to send the metrics to Elasticsearch. Most settings from the
# Elasticsearch output are accepted here as well.
# Note that the settings should point to your Elasticsearch *monitoring* cluster.
# Any setting that is not set is automatically inherited from the Elasticsearch
# output configuration, so if you have the Elasticsearch output configured such
# that it is pointing to your Elasticsearch monitoring cluster, you can simply
# uncomment the following line.
#monitoring.elasticsearch:

# ============================== Instrumentation ===============================

# Instrumentation support for the filebeat.
#instrumentation:
    # Set to true to enable instrumentation of filebeat.
    #enabled: false
    
    # Environment in which filebeat is running on (eg: staging, production, etc.)
    #environment: ""
    
    # APM Server hosts to report instrumentation results to.
    #hosts:
    #  - http://localhost:8200
    
    # API Key for the APM Server(s).
    # If api_key is set then secret_token will be ignored.
    #api_key:
    
    # Secret token for the APM Server(s).
    #secret_token:

# ================================= Migration ==================================

# This allows to enable 6.7 migration aliases
#migration.6_to_7.enabled: true

# Custom configuration
${CUSTOM_CONFIG:}
