<template>
  <div class="tenant-filters">
    <!-- 筛选区域 -->
    <div class="filters-section bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100 mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-800 flex items-center">
          <span class="mr-2">🔍</span>
          筛选条件
        </h4>
        <button @click="handleClearFilters" class="btn-secondary text-sm px-3 py-1">
          <span class="mr-1">🔄</span>
          清空筛选
        </button>
      </div>

      <el-form :model="filters" :inline="true" class="search-form">
        <el-form-item label="租户编码">
          <el-input
            v-model="filters.tenantCode"
            placeholder="输入租户编码..."
            clearable
            :prefix-icon="Key"
            size="small"
            style="width: 180px"
            @input="handleFilterChange"
          />
        </el-form-item>

        <el-form-item label="租户名称">
          <el-input
            v-model="filters.tenantName"
            placeholder="输入租户名称..."
            clearable
            :prefix-icon="OfficeBuilding"
            size="small"
            style="width: 180px"
            @input="handleFilterChange"
          />
        </el-form-item>

        <el-form-item label="联系人">
          <el-input
            v-model="filters.contactPerson"
            placeholder="输入联系人..."
            clearable
            :prefix-icon="User"
            size="small"
            style="width: 150px"
            @input="handleFilterChange"
          />
        </el-form-item>

        <el-form-item label="联系电话">
          <el-input
            v-model="filters.contactPhone"
            placeholder="输入联系电话..."
            clearable
            :prefix-icon="Phone"
            size="small"
            style="width: 150px"
            @input="handleFilterChange"
          />
        </el-form-item>

        <el-form-item label="租户状态">
          <el-select
            v-model="filters.status"
            placeholder="全部状态"
            clearable
            size="small"
            style="width: 120px"
            @change="handleFilterChange"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.dictValue"
              :value="status.dictValue"
              :label="status.dictLabel"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="包含过期">
          <el-switch
            v-model="filters.includeExpired"
            size="small"
            @change="handleFilterChange"
          />
        </el-form-item>
      </el-form>

      <!-- 搜索按钮 -->
      <div class="flex items-center gap-3 mt-4">
        <el-button 
          type="primary" 
          @click="handleSearch" 
          :loading="loading" 
          size="small" 
          :icon="Search"
        >
          {{ loading ? '搜索中...' : '搜索' }}
        </el-button>
        <el-button 
          @click="handleClearFilters" 
          size="small" 
          :icon="Refresh"
        >
          重置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import {
  Key,
  OfficeBuilding,
  User,
  Phone,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import type { SysTenantQueryDTO } from '@/types/system'
import { useTenantData } from './composables/useTenantData'

// 使用租户数据管理 composable
const {
  statusOptions,
  loading,
  searchTenants,
  clearFilters
} = useTenantData()

// 筛选条件
const filters = reactive<SysTenantQueryDTO>({
  tenantCode: '',
  tenantName: '',
  contactPerson: '',
  contactPhone: '',
  status: '',
  includeExpired: false,
  current: 1,
  size: 10
})

// 方法
const handleSearch = () => {
  searchTenants({ ...filters })
}

const handleClearFilters = () => {
  Object.assign(filters, {
    tenantCode: '',
    tenantName: '',
    contactPerson: '',
    contactPhone: '',
    status: '',
    includeExpired: false,
    current: 1,
    size: 10
  })
  clearFilters()
}

const handleFilterChange = () => {
  // 重置页码
  filters.current = 1
}

// 监听筛选条件变化，自动搜索
watch(filters, () => {
  handleSearch()
}, { deep: true })
</script>

<style scoped>
.tenant-filters {
  margin-bottom: 20px;
}

.filters-section {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
