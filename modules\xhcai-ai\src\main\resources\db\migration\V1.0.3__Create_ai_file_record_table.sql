-- AI文件记录表
CREATE TABLE IF NOT EXISTS ai_file_record (
    id VARCHAR(36) PRIMARY KEY,
    original_filename VARCHAR(255) NOT NULL,
    file_size BIGINT,
    file_extension VARCHAR(10),
    mime_type VARCHAR(100),
    dify_file_id VARCHAR(100) NOT NULL,
    minio_bucket VARCHAR(100),
    minio_object_name VARCHAR(500),
    minio_url VARCHAR(1000),
    status VARCHAR(20) DEFAULT 'uploading',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    upload_user_id VARCHAR(36),
    dify_preview_url VARCHAR(1000),
    error_message VARCHAR(1000),

    -- 基础字段
    tenant_id VARCHAR(36),
    create_by VARCHAR(36),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(36),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted SMALLINT DEFAULT 0,
    version INT DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE ai_file_record IS 'AI文件记录表';

-- 添加列注释
COMMENT ON COLUMN ai_file_record.id IS '主键ID';
COMMENT ON COLUMN ai_file_record.original_filename IS '原始文件名';
COMMENT ON COLUMN ai_file_record.file_size IS '文件大小（字节）';
COMMENT ON COLUMN ai_file_record.file_extension IS '文件扩展名';
COMMENT ON COLUMN ai_file_record.mime_type IS 'MIME类型';
COMMENT ON COLUMN ai_file_record.dify_file_id IS 'Dify文件ID';
COMMENT ON COLUMN ai_file_record.minio_bucket IS 'MinIO存储桶名称';
COMMENT ON COLUMN ai_file_record.minio_object_name IS 'MinIO对象名称（文件路径）';
COMMENT ON COLUMN ai_file_record.minio_url IS 'MinIO访问URL';
COMMENT ON COLUMN ai_file_record.status IS '文件状态：uploading-上传中，uploaded-已上传，failed-失败，deleted-已删除';
COMMENT ON COLUMN ai_file_record.upload_time IS '上传时间';
COMMENT ON COLUMN ai_file_record.upload_user_id IS '上传用户ID';
COMMENT ON COLUMN ai_file_record.dify_preview_url IS 'Dify预览URL';
COMMENT ON COLUMN ai_file_record.error_message IS '错误信息';
COMMENT ON COLUMN ai_file_record.tenant_id IS '租户ID';
COMMENT ON COLUMN ai_file_record.create_by IS '创建人';
COMMENT ON COLUMN ai_file_record.create_time IS '创建时间';
COMMENT ON COLUMN ai_file_record.update_by IS '更新人';
COMMENT ON COLUMN ai_file_record.update_time IS '更新时间';
COMMENT ON COLUMN ai_file_record.deleted IS '删除标记：0-未删除，1-已删除';
COMMENT ON COLUMN ai_file_record.version IS '版本号';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_ai_file_record_update_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_ai_file_record_update_time
    BEFORE UPDATE ON ai_file_record
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_file_record_update_time();

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_file_record_dify_file_id ON ai_file_record(dify_file_id);
CREATE INDEX IF NOT EXISTS idx_ai_file_record_upload_user_id ON ai_file_record(upload_user_id);
CREATE INDEX IF NOT EXISTS idx_ai_file_record_status ON ai_file_record(status);
CREATE INDEX IF NOT EXISTS idx_ai_file_record_upload_time ON ai_file_record(upload_time);
CREATE INDEX IF NOT EXISTS idx_ai_file_record_tenant_id ON ai_file_record(tenant_id);
CREATE INDEX IF NOT EXISTS idx_ai_file_record_deleted ON ai_file_record(deleted);
