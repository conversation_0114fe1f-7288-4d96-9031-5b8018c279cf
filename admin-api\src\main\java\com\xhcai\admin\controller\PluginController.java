package com.xhcai.admin.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.plugin.core.PluginInfo;
import com.xhcai.plugin.core.PluginType;
import com.xhcai.plugin.service.PluginServiceManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 插件管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/plugin")
@RequiredArgsConstructor
@Tag(name = "插件管理", description = "插件系统管理接口")
public class PluginController {

    private final PluginServiceManager pluginServiceManager;

    @GetMapping("/types")
    @Operation(summary = "获取插件类型列表")
    public Result<PluginType[]> getPluginTypes() {
        return Result.success(PluginType.values());
    }

    @GetMapping("/list/{type}")
    @Operation(summary = "获取指定类型的插件列表")
    public Result<List<PluginInfo>> getPluginList(
            @Parameter(description = "插件类型") @PathVariable String type) {
        try {
            PluginType pluginType = PluginType.fromCode(type);
            List<PluginInfo> plugins = pluginServiceManager.getHotSwapManager()
                    .getLoadedPlugins(pluginType);
            return Result.success(plugins);
        } catch (Exception e) {
            log.error("Failed to get plugin list for type: {}", type, e);
            return Result.fail("获取插件列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/load/{type}")
    @Operation(summary = "加载插件")
    public Result<String> loadPlugin(
            @Parameter(description = "插件类型") @PathVariable String type,
            @Parameter(description = "插件文件") @RequestParam("file") MultipartFile file) {
        try {
            PluginType pluginType = PluginType.fromCode(type);

            // 保存上传的插件文件
            String fileName = file.getOriginalFilename();
            Path pluginPath = Paths.get("plugins", type, fileName);
            file.transferTo(pluginPath.toFile());

            // 加载插件
            String pluginId = pluginServiceManager.getHotSwapManager()
                    .loadPlugin(pluginType, pluginPath);

            return Result.success("插件加载成功", pluginId);
        } catch (Exception e) {
            log.error("Failed to load plugin for type: {}", type, e);
            return Result.fail("加载插件失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/unload/{type}/{pluginId}")
    @Operation(summary = "卸载插件")
    public Result<Boolean> unloadPlugin(
            @Parameter(description = "插件类型") @PathVariable String type,
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        try {
            PluginType pluginType = PluginType.fromCode(type);
            boolean result = pluginServiceManager.getHotSwapManager()
                    .unloadPlugin(pluginType, pluginId);

            if (result) {
                return Result.success("插件卸载成功", true);
            } else {
                return Result.fail("插件卸载失败");
            }
        } catch (Exception e) {
            log.error("Failed to unload plugin: {} - {}", type, pluginId, e);
            return Result.fail("卸载插件失败: " + e.getMessage());
        }
    }

    @PostMapping("/reload/{type}/{pluginId}")
    @Operation(summary = "重新加载插件")
    public Result<Boolean> reloadPlugin(
            @Parameter(description = "插件类型") @PathVariable String type,
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        try {
            PluginType pluginType = PluginType.fromCode(type);
            boolean result = pluginServiceManager.getHotSwapManager()
                    .reloadPlugin(pluginType, pluginId);

            if (result) {
                return Result.success("插件重新加载成功", true);
            } else {
                return Result.fail("插件重新加载失败");
            }
        } catch (Exception e) {
            log.error("Failed to reload plugin: {} - {}", type, pluginId, e);
            return Result.fail("重新加载插件失败: " + e.getMessage());
        }
    }

    @PostMapping("/restart-context/{type}")
    @Operation(summary = "重启插件上下文")
    public Result<Boolean> restartPluginContext(
            @Parameter(description = "插件类型") @PathVariable String type) {
        try {
            PluginType pluginType = PluginType.fromCode(type);
            pluginServiceManager.restartPluginContext(pluginType);
            return Result.success("插件上下文重启成功", true);
        } catch (Exception e) {
            log.error("Failed to restart plugin context: {}", type, e);
            return Result.fail("重启插件上下文失败: " + e.getMessage());
        }
    }

    @GetMapping("/health/{type}/{pluginId}")
    @Operation(summary = "检查插件健康状态")
    public Result<Boolean> checkPluginHealth(
            @Parameter(description = "插件类型") @PathVariable String type,
            @Parameter(description = "插件ID") @PathVariable String pluginId) {
        try {
            PluginType pluginType = PluginType.fromCode(type);
            boolean healthy = pluginServiceManager.isPluginServiceHealthy(pluginType, pluginId);
            return Result.success(healthy);
        } catch (Exception e) {
            log.error("Failed to check plugin health: {} - {}", type, pluginId, e);
            return Result.fail("检查插件健康状态失败: " + e.getMessage());
        }
    }

    @GetMapping("/storage/services")
    @Operation(summary = "获取存储服务列表")
    public Result<List<Map<String, Object>>> getStorageServices() {
        try {
            var services = pluginServiceManager.getStorageServices();
            List<Map<String, Object>> serviceInfos = services.stream()
                    .map(service -> Map.<String, Object>of(
                    "name", service.getServiceName(),
                    "type", service.getServiceType(),
                    "healthy", service.isHealthy()
            ))
                    .toList();
            return Result.success(serviceInfos);
        } catch (Exception e) {
            log.error("Failed to get storage services", e);
            return Result.fail("获取存储服务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/model/services")
    @Operation(summary = "获取模型服务列表")
    public Result<List<Map<String, Object>>> getModelServices() {
        try {
            var services = pluginServiceManager.getModelServices();
            List<Map<String, Object>> serviceInfos = services.stream()
                    .map(service -> Map.<String, Object>of(
                    "name", service.getServiceName(),
                    "type", service.getServiceType(),
                    "healthy", service.isHealthy(),
                    "stats", service.getServiceStats()
            ))
                    .toList();
            return Result.success(serviceInfos);
        } catch (Exception e) {
            log.error("Failed to get model services", e);
            return Result.fail("获取模型服务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/model/supported-models")
    @Operation(summary = "获取支持的模型列表")
    public Result<List<Map<String, Object>>> getSupportedModels() {
        try {
            var services = pluginServiceManager.getModelServices();
            List<Map<String, Object>> allModels = services.stream()
                    .flatMap(service -> service.getSupportedModels().stream())
                    .map(model -> Map.<String, Object>of(
                    "modelName", model.getModelName(),
                    "displayName", model.getDisplayName(),
                    "description", model.getDescription(),
                    "type", model.getModelType(),
                    "capabilities", model.getCapabilities(),
                    "provider", model.getProvider(),
                    "available", model.getAvailable()
            ))
                    .toList();
            return Result.success(allModels);
        } catch (Exception e) {
            log.error("Failed to get supported models", e);
            return Result.fail("获取支持的模型列表失败: " + e.getMessage());
        }
    }
}
