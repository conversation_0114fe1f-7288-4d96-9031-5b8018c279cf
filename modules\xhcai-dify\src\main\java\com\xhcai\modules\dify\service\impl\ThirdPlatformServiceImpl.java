package com.xhcai.modules.dify.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.dify.mapper.ThirdPlatformMapper;
import com.xhcai.modules.dify.vo.ThirdPlatformVO;
import com.xhcai.modules.dify.dto.thirdPlatform.ThirdPlatformQueryDTO;
import com.xhcai.modules.dify.entity.ThirdPlatform;
import com.xhcai.modules.dify.service.IThirdPlatformService;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * 第三方智能体服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class ThirdPlatformServiceImpl extends ServiceImpl<ThirdPlatformMapper, ThirdPlatform>
        implements IThirdPlatformService {

    private static final Logger log = LoggerFactory.getLogger(ThirdPlatformServiceImpl.class);

    @Autowired
    private ThirdPlatformMapper agentMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public PageResult<ThirdPlatformVO> getPlatformPage(ThirdPlatformQueryDTO queryDTO) {
        Page<ThirdPlatformVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<ThirdPlatformVO> result = agentMapper.selectAgentPage(page, queryDTO);

        // 处理返回数据
        result.getRecords().forEach(this::processAgentVO);

        return PageResult.of(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
    }

    @Override
    public List<ThirdPlatformVO> getPlatformList(ThirdPlatformQueryDTO queryDTO) {
        String userId = SecurityUtils.getCurrentUserId();
        String tenantId = SecurityUtils.getCurrentTenantId();

        List<ThirdPlatformVO> result = agentMapper.selectAgentList(queryDTO, userId, tenantId);

        // 处理返回数据
        result.forEach(this::processAgentVO);

        return result;
    }

    @Override
    public ThirdPlatformVO getPlatformById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        ThirdPlatformVO agentVO = agentMapper.selectAgentById(id);
        if (agentVO == null) {
            throw new BusinessException("智能体不存在");
        }

        processAgentVO(agentVO);
        return agentVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPlatform(ThirdPlatform agent) {
        // 验证配置
        String validationResult = validatePlatformConfig(agent);
        if (StringUtils.isNotBlank(validationResult)) {
            throw new BusinessException(validationResult);
        }

        // 检查名称是否重复
        LambdaQueryWrapper<ThirdPlatform> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ThirdPlatform::getName, agent.getName());
        if (this.count(wrapper) > 0) {
            throw new BusinessException("智能体名称已存在");
        }

        // 设置默认值
        if (agent.getTimeout() == null) {
            agent.setTimeout(30);
        }
        if (StringUtils.isBlank(agent.getIcon())) {
            agent.setIcon("🤖");
        }
        if (StringUtils.isBlank(agent.getIconBg())) {
            agent.setIconBg("#6366f1");
        }
        if (agent.getStatus() == null) {
            agent.setStatus(1);
        }

        // 处理授权列表
        processAuthorizedData(agent);

        return this.save(agent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlatform(ThirdPlatform agent) {
        if (StringUtils.isBlank(agent.getId())) {
            throw new BusinessException("智能体ID不能为空");
        }

        // 验证智能体是否存在
        ThirdPlatform existingAgent = this.getById(agent.getId());
        if (existingAgent == null) {
            throw new BusinessException("智能体不存在");
        }

        // 验证配置
        String validationResult = validatePlatformConfig(agent);
        if (StringUtils.isNotBlank(validationResult)) {
            throw new BusinessException(validationResult);
        }

        // 检查名称是否重复（排除自己）
        LambdaQueryWrapper<ThirdPlatform> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ThirdPlatform::getName, agent.getName())
                .ne(ThirdPlatform::getId, agent.getId());
        if (this.count(wrapper) > 0) {
            throw new BusinessException("智能体名称已存在");
        }

        // 处理授权列表
        processAuthorizedData(agent);

        return this.updateById(agent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePlatform(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        ThirdPlatform agent = this.getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeletePlatforms(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("智能体ID列表不能为空");
        }

        return this.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleStatus(String id, Integer status) {
        if (StringUtils.isBlank(id)) {
            throw new BusinessException("智能体ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw new BusinessException("状态值无效");
        }

        ThirdPlatform agent = this.getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        agent.setStatus(status);
        return this.updateById(agent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchToggleStatus(List<String> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("智能体ID列表不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw new BusinessException("状态值无效");
        }

        String updateBy = SecurityUtils.getCurrentUsername();
        return agentMapper.batchUpdateStatus(ids, status, updateBy) > 0;
    }

    @Override
    public String testConnection(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BusinessException("智能体ID不能为空");
        }

        ThirdPlatform agent = this.getById(id);
        if (agent == null) {
            throw new BusinessException("智能体不存在");
        }

        String result = "连接测试成功";
        Integer testResult = 1;
        String testError = null;

        try {
            // 简单的连接测试
            URL url = new URL(agent.getConnectionUrl());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(agent.getTimeout() * 1000);
            connection.setReadTimeout(agent.getTimeout() * 1000);

            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 400) {
                result = "连接测试成功";
                testResult = 1;
            } else {
                result = "连接测试失败，HTTP状态码：" + responseCode;
                testResult = 0;
                testError = result;
            }

            connection.disconnect();
        } catch (Exception e) {
            result = "连接测试失败：" + e.getMessage();
            testResult = 0;
            testError = e.getMessage();
            log.error("智能体连接测试失败，ID：{}，错误：{}", id, e.getMessage(), e);
        }

        // 更新测试结果
        String updateBy = SecurityUtils.getCurrentUsername();
        agentMapper.updateTestResult(id, testResult, testError, updateBy);

        return result;
    }

    @Override
    public List<ThirdPlatformVO> getAccessiblePlatforms(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new BusinessException("用户ID不能为空");
        }

        SysUser user = userService.getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        List<ThirdPlatformVO> agents = agentMapper.selectAccessibleAgents(userId, user.getDeptId());
        agents.forEach(this::processAgentVO);

        return agents;
    }

    @Override
    public boolean checkUserAccess(String agentId, String userId) {
        if (StringUtils.isBlank(agentId) || StringUtils.isBlank(userId)) {
            return false;
        }

        ThirdPlatform agent = this.getById(agentId);
        if (agent == null || agent.getStatus() != 1) {
            return false;
        }

        SysUser user = userService.getById(userId);
        if (user == null) {
            return false;
        }

        // 检查访问权限
        switch (agent.getAccessScope()) {
            case "public":
                return true;
            case "personal":
                return agent.getCreateBy().equals(userId);
            case "partial_users":
                return isUserAuthorized(agent.getAuthorizedUsers(), userId);
            case "partial_units":
                return isUnitAuthorized(agent.getAuthorizedUnits(), user.getDeptId());
            default:
                return false;
        }
    }

    @Override
    public Map<String, Object> getPlatformStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总数统计
        statistics.put("total", this.count());
        statistics.put("active", this.count(new LambdaQueryWrapper<ThirdPlatform>().eq(ThirdPlatform::getStatus, 1)));
        statistics.put("inactive", this.count(new LambdaQueryWrapper<ThirdPlatform>().eq(ThirdPlatform::getStatus, 0)));

        // 访问范围统计
        statistics.put("accessScopeStats", getAccessScopeStatistics());

        return statistics;
    }

    @Override
    public Map<String, Long> getAccessScopeStatistics() {
        Map<String, Long> stats = new HashMap<>();

        stats.put("personal", this.count(new LambdaQueryWrapper<ThirdPlatform>().eq(ThirdPlatform::getAccessScope, "personal")));
        stats.put("public", this.count(new LambdaQueryWrapper<ThirdPlatform>().eq(ThirdPlatform::getAccessScope, "public")));
        stats.put("partial_users", this.count(new LambdaQueryWrapper<ThirdPlatform>().eq(ThirdPlatform::getAccessScope, "partial_users")));
        stats.put("partial_units", this.count(new LambdaQueryWrapper<ThirdPlatform>().eq(ThirdPlatform::getAccessScope, "partial_units")));

        return stats;
    }

    @Override
    public String validatePlatformConfig(ThirdPlatform agent) {
        if (agent == null) {
            return "智能体配置不能为空";
        }

        if (StringUtils.isBlank(agent.getName())) {
            return "智能体名称不能为空";
        }

        if (StringUtils.isBlank(agent.getUnitId())) {
            return "所属单位不能为空";
        }

        if (StringUtils.isBlank(agent.getConnectionUrl())) {
            return "连接地址不能为空";
        }

        if (StringUtils.isBlank(agent.getAccessScope())) {
            return "访问范围不能为空";
        }

        if (!Arrays.asList("personal", "public", "partial_users", "partial_units").contains(agent.getAccessScope())) {
            return "访问范围值无效";
        }

        if (agent.getTimeout() == null || agent.getTimeout() < 1 || agent.getTimeout() > 300) {
            return "超时时间必须在1-300秒之间";
        }

        // 验证URL格式
        try {
            new URL(agent.getConnectionUrl());
        } catch (Exception e) {
            return "连接地址格式无效";
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyPlatform(String sourceId, String newName) {
        if (StringUtils.isBlank(sourceId) || StringUtils.isBlank(newName)) {
            throw new BusinessException("源智能体ID和新名称不能为空");
        }

        ThirdPlatform sourceAgent = this.getById(sourceId);
        if (sourceAgent == null) {
            throw new BusinessException("源智能体不存在");
        }

        // 检查新名称是否重复
        LambdaQueryWrapper<ThirdPlatform> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ThirdPlatform::getName, newName);
        if (this.count(wrapper) > 0) {
            throw new BusinessException("智能体名称已存在");
        }

        // 复制智能体
        ThirdPlatform newAgent = new ThirdPlatform();
        newAgent.setName(newName);
        newAgent.setDescription(sourceAgent.getDescription());
        newAgent.setUnitId(sourceAgent.getUnitId());
        newAgent.setConnectionUrl(sourceAgent.getConnectionUrl());
        newAgent.setApiKey(sourceAgent.getApiKey());
        newAgent.setTimeout(sourceAgent.getTimeout());
        newAgent.setAccessScope(sourceAgent.getAccessScope());
        newAgent.setAuthorizedUsers(sourceAgent.getAuthorizedUsers());
        newAgent.setAuthorizedUnits(sourceAgent.getAuthorizedUnits());
        newAgent.setStatus(0); // 默认禁用
        newAgent.setIcon(sourceAgent.getIcon());
        newAgent.setIconBg(sourceAgent.getIconBg());

        return this.save(newAgent);
    }

    @Override
    public List<ThirdPlatformVO> exportPlatforms(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("智能体ID列表不能为空");
        }

        List<ThirdPlatformVO> agents = new ArrayList<>();
        for (String id : ids) {
            ThirdPlatformVO agent = this.getPlatformById(id);
            if (agent != null) {
                agents.add(agent);
            }
        }

        return agents;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importPlatforms(List<ThirdPlatform> agents) {
        if (agents == null || agents.isEmpty()) {
            throw new BusinessException("导入数据不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (ThirdPlatform agent : agents) {
            try {
                // 重置ID，作为新记录插入
                agent.setId(null);
                agent.setStatus(0); // 默认禁用

                // 检查名称是否重复，如果重复则添加后缀
                String originalName = agent.getName();
                int suffix = 1;
                while (this.count(new LambdaQueryWrapper<ThirdPlatform>().eq(ThirdPlatform::getName, agent.getName())) > 0) {
                    agent.setName(originalName + "_" + suffix);
                    suffix++;
                }

                if (this.addPlatform(agent)) {
                    successCount++;
                } else {
                    failCount++;
                    errorMessages.append("智能体 ").append(originalName).append(" 导入失败；");
                }
            } catch (Exception e) {
                failCount++;
                errorMessages.append("智能体 ").append(agent.getName()).append(" 导入失败：").append(e.getMessage()).append("；");
            }
        }

        return String.format("导入完成，成功：%d，失败：%d。%s", successCount, failCount, errorMessages.toString());
    }

    /**
     * 处理智能体VO数据
     */
    private void processAgentVO(ThirdPlatformVO agentVO) {
        // 处理状态文本
        agentVO.setStatusText(agentVO.getStatus() == 1 ? "启用" : "禁用");

        // 处理访问范围文本
        switch (agentVO.getAccessScope()) {
            case "personal":
                agentVO.setAccessScopeText("个人");
                break;
            case "public":
                agentVO.setAccessScopeText("公开");
                break;
            case "partial_users":
                agentVO.setAccessScopeText("部分人");
                break;
            case "partial_units":
                agentVO.setAccessScopeText("部分单位");
                break;
            default:
                agentVO.setAccessScopeText("未知");
        }

        // 处理测试结果文本
        if (agentVO.getLastTestResult() != null) {
            agentVO.setLastTestResultText(agentVO.getLastTestResult() == 1 ? "成功" : "失败");
        }

        // 脱敏API密钥
        if (StringUtils.isNotBlank(agentVO.getApiKey())) {
            String apiKey = agentVO.getApiKey();
            if (apiKey.length() > 8) {
                agentVO.setApiKey(apiKey.substring(0, 4) + "***" + apiKey.substring(apiKey.length() - 4));
            } else {
                agentVO.setApiKey("***");
            }
        }

        // 处理授权用户和单位列表
        try {
            if (StringUtils.isNotBlank(agentVO.getAuthorizedUsersJson())) {
                List<String> userIds = objectMapper.readValue(agentVO.getAuthorizedUsersJson(), new TypeReference<List<String>>() {
                });
                agentVO.setAuthorizedUsers(userIds);
            }
            if (StringUtils.isNotBlank(agentVO.getAuthorizedUnitsJson())) {
                List<String> unitIds = objectMapper.readValue(agentVO.getAuthorizedUnitsJson(), new TypeReference<List<String>>() {
                });
                agentVO.setAuthorizedUnits(unitIds);
            }
        } catch (Exception e) {
            log.warn("处理授权列表失败：{}", e.getMessage());
        }
    }

    /**
     * 处理授权数据
     */
    private void processAuthorizedData(ThirdPlatform agent) {
        // 这里可以根据需要处理授权用户和单位的JSON序列化
        // 前端传入的应该已经是JSON格式的字符串
        if (agent.getAuthorizedUsers() == null) {
            agent.setAuthorizedUsers("[]");
        }
        if (agent.getAuthorizedUnits() == null) {
            agent.setAuthorizedUnits("[]");
        }
    }

    /**
     * 检查用户是否被授权
     */
    private boolean isUserAuthorized(String authorizedUsersJson, String userId) {
        if (StringUtils.isBlank(authorizedUsersJson) || StringUtils.isBlank(userId)) {
            return false;
        }

        try {
            List<String> userIds = objectMapper.readValue(authorizedUsersJson, new TypeReference<List<String>>() {
            });
            return userIds.contains(userId);
        } catch (Exception e) {
            log.warn("解析授权用户列表失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查单位是否被授权
     */
    private boolean isUnitAuthorized(String authorizedUnitsJson, String unitId) {
        if (StringUtils.isBlank(authorizedUnitsJson) || StringUtils.isBlank(unitId)) {
            return false;
        }

        try {
            List<String> unitIds = objectMapper.readValue(authorizedUnitsJson, new TypeReference<List<String>>() {
            });
            return unitIds.contains(unitId);
        } catch (Exception e) {
            log.warn("解析授权单位列表失败：{}", e.getMessage());
            return false;
        }
    }
}
