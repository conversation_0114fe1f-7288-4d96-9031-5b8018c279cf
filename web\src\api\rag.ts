/**
 * RAG知识库管理相关API
 * 基于后端xhcai-rag模块的API接口
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'
import type { SysUserVO } from '@/types/system'
import { envConfig } from '@/config/env'
import type { Document, DocumentVO, DocumentQueryDTO, UploadedFile, FileCleanSegmentConfig, BatchSegmentationRequest, FileCleanSegmentConfigParams } from '@/types/rag'

// ==================== 数据集相关类型定义 ====================

/**
 * 数据集实体
 */
export interface Dataset {
  id: string
  name: string
  description?: string
  dataSourceType: string
  modelId?: string
  vectorizationConfig?: Record<string, any>
  tags?: string[]
  tenantId: string
  createdBy?: string
  updatedBy?: string
  createdAt: string
  updatedAt?: string
  remark?: string
  deleted?: boolean
}

/**
 * 数据集创建DTO
 */
export interface DatasetCreateDTO {
  name: string
  description?: string
  dataSourceType?: string
  modelId?: string
  vectorizationConfig: Record<string, any>
  vectorDatabaseId?: string
  fileStorageId?: string
  tags?: string[]
}

/**
 * 数据集更新DTO
 */
export interface UpdateDatasetRequest {
  name?: string
  description?: string
  icon?: string
  iconBg?: string
  isPublic?: boolean
  status?: string
  dataSourceType?: string
  modelId?: string
  vectorizationConfig: Record<string, any>
  tags?: string[]
}

/**
 * 知识库查询DTO
 */
export interface DatasetQueryDTO {
  current?: number
  size?: number
  name?: string
  /**
   * 数据源类型
   */
  dataSourceType?: string
  startTime?: string
  endTime?: string
  /**
   * 标签
   */
  tags?: string[]
  /**
   * 部门ID
   */
  deptId?: string
}

/**
 * 数据集VO
 */
export interface DatasetVO {
  id: string
  name: string
  description: string
  icon: string
  iconBg: string
  unit: string
  owner: string
  createTime: string
  updateTime: string
  docCount: number
  totalWordCount: number
  totalWordCountToK: string
  appCount: number
  tags: string[]
  isPublic?: boolean
  status?: string
  dataSourceType?: string
  createdBy?: string
  createdAt?: string
  updatedAt?: string
  documentCount?: number
  characterCount?: number
  // 新增用户和部门信息字段
  createBy?: string
  updateBy?: string
  createUser: SysUserVO
  updateUser: SysUserVO
}

// ==================== 文档分段相关类型定义 ====================

/**
 * 文档分段实体
 */
export interface DocumentSegment {
  id: string
  tenantId: string
  datasetId: string
  documentId: string
  position: number
  content: string
  wordCount: number
  tokens: number
  keywords?: string[]
  indexNodeId?: string
  indexNodeHash?: string
  hitCount?: number
  enabled: boolean
  disabledAt?: string
  disabledBy?: string
  status: string
  createdBy?: string
  createdAt: string
  indexingAt?: string
  completedAt?: string
  error?: string
  stoppedAt?: string
  answer?: string
  vector?: number[]
  vectorHash?: string
  embeddingModel?: string
  remark?: string
  deleted?: boolean
}

/**
 * 文档分段查询DTO
 */
export interface DocumentSegmentQueryDTO {
  current?: number
  size?: number
  datasetId?: string
  documentId?: string
  enabled?: boolean
  status?: string
  keyword?: string
  startTime?: string
  endTime?: string
}

// ==================== 检索相关类型定义 ====================
/**
 * 检索请求
 */
export interface SearchRequest {
  query: string
  datasetIds?: string[]
  vectorizationConfig?: Record<string, any>
  topK?: number
  scoreThreshold?: number
  rerankingModel?: Record<string, any>
}

/**
 * 检索结果项
 */
export interface SearchResultItem {
  id: string
  content: string
  score: number
  datasetId: string
  datasetName: string
  documentId: string
  documentName: string
  segmentId: string
  position: number
  wordCount: number
  tokens: number
  metadata?: Record<string, any>
}

/**
 * 检索结果
 */
export interface SearchResult {
  query: string
  results: SearchResultItem[]
  totalCount: number
  retrievalTime: number
}

// ==================== 分页结果类型 ====================

/**
 * 分页结果
 */
export interface PageResult<T> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

// ==================== API类定义 ====================

/**
 * RAG知识库管理API类
 */
export class RagAPI {
  // ==================== 数据集管理API ====================

  /**
   * 分页查询数据集列表
   */
  static async getDatasetPage(queryDTO: DatasetQueryDTO): Promise<ApiResponse<PageResult<DatasetVO>>> {
    return apiClient.get<PageResult<DatasetVO>>('/api/rag/datasets/page', queryDTO)
  }

  /**
   * 创建数据集
   */
  static async createDataset(createDTO: DatasetCreateDTO): Promise<ApiResponse<DatasetVO>> {
    return apiClient.post<DatasetVO>('/api/rag/datasets', createDTO)
  }

  /**
   * 更新数据集
   */
  static async updateDataset(id: string, updateDTO: UpdateDatasetRequest): Promise<ApiResponse<DatasetVO>> {
    return apiClient.put<DatasetVO>(`/api/rag/datasets/${id}`, updateDTO)
  }

  /**
   * 删除数据集
   */
  static async deleteDataset(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/datasets/${id}`)
  }

  /**
   * 获取数据集详情
   */
  static async getDatasetById(id: string): Promise<ApiResponse<Dataset>> {
    return apiClient.get<Dataset>(`/api/rag/datasets/${id}`)
  }

  // ==================== 文档管理API ====================

  /**
   * 分页查询文档列表
   */
  static async getDocumentPage(queryDTO: DocumentQueryDTO): Promise<ApiResponse<PageResult<UploadedFile>>> {
    return apiClient.get<PageResult<UploadedFile>>('/api/rag/documents/page', queryDTO)
  }

  /**
   * 上传文档
   */
  static async uploadDocument(file: File, datasetId: string, batchId: string): Promise<ApiResponse<DocumentVO>> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('datasetId', datasetId)
    formData.append('batchId', batchId)

    // 直接使用postFormData方法，避免重复创建FormData
    return apiClient.postFormData<DocumentVO>('/api/rag/documents/upload', formData)
  }

  /**
   * 批量上传文档（支持进度回调）
   */
  static async batchUploadDocuments(
    formData: FormData,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<DocumentVO[]>> {
    // 使用 apiClient 的统一规范，支持进度回调
    return apiClient.postFormData<any>('/api/rag/documents/batch-upload', formData, {
      onUploadProgress: onProgress,
      timeout: 300000 // 5分钟超时
    })
  }

  /**
   * 删除文档
   */
  static async deleteDocument(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/documents/${id}`)
  }

  /**
   * 获取文档详情
   */
  static async getDocumentById(id: string): Promise<ApiResponse<Document>> {
    return apiClient.get<Document>(`/api/rag/documents/${id}`)
  }

  // ==================== 文档分段管理API ====================

  /**
   * 分页查询文档分段列表
   */
  static async getDocumentSegmentPage(queryDTO: DocumentSegmentQueryDTO): Promise<ApiResponse<PageResult<DocumentSegment>>> {
    return apiClient.get<PageResult<DocumentSegment>>('/api/rag/segments/page', queryDTO)
  }

  // ==================== 检索功能API ====================

  /**
   * 向量检索
   */
  static async search(searchRequest: SearchRequest): Promise<ApiResponse<SearchResult>> {
    return apiClient.post<SearchResult>('/api/rag/retrieval/search', searchRequest)
  }

  /**
   * 智能问答
   */
  static async qa(query: string, datasetIds?: string[]): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/retrieval/qa', {
      query,
      datasetIds
    })
  }

  // ==================== 文档分段管理API ====================

  /**
   * 更新文档分段
   */
  static async updateDocumentSegment(id: string, updateData: Partial<DocumentSegment>): Promise<ApiResponse<DocumentSegment>> {
    return apiClient.put<DocumentSegment>(`/api/rag/segments/${id}`, updateData)
  }

  /**
   * 删除文档分段
   */
  static async deleteDocumentSegment(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/segments/${id}`)
  }

  /**
   * 批量删除文档分段
   */
  static async batchDeleteDocumentSegments(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/segments/batch-delete', { ids })
  }

  /**
   * 批量开始分段处理
   */
  static async batchStartSegmentation(request: BatchSegmentationRequest): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/document-segments/batch/start', request)
  }

  /**
   * 批量保存文档配置
   * 保存文档的分段配置和清洗配置到documents表
   */
  static async batchSaveDocumentConfigs(request: FileCleanSegmentConfigParams): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/documents/batch/save-configs', request)
  }

  /**
   * 获取文档状态SSE连接URL
   */
  static getDocumentStatusSSEUrl(): string {
    // 使用apiClient的baseURL来构建完整的SSE URL
    return `${envConfig.apiBaseUrl}/api/rag/document-status-sse/connect`
  }

  /**
   * 获取SSE连接统计信息
   */
  static async getSSEConnectionStats(): Promise<ApiResponse<Record<string, number>>> {
    return apiClient.get<Record<string, number>>('/api/rag/document-status-sse/stats')
  }

  /**
   * 关闭SSE连接
   */
  static async disconnectSSE(): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/document-status-sse/disconnect')
  }

  /**
   * 启用/禁用文档分段
   */
  static async updateSegmentEnabled(id: string, enabled: boolean): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`/api/rag/segments/${id}/enabled`, { enabled })
  }

  /**
   * 重新分段文档
   */
  static async resegmentDocument(documentId: string, chunkSize: number, chunkOverlap: number): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/documents/${documentId}/resegment`, {
      chunkSize,
      chunkOverlap
    })
  }

  // ==================== 文档分段任务API ====================

  /**
   * 获取分段任务列表
   */
  static async getSegmentationTasks(queryDTO: {
    current?: number
    size?: number
    status?: string
    datasetId?: string
  }): Promise<ApiResponse<PageResult<any>>> {
    return apiClient.get<PageResult<any>>('/api/rag/segmentation/tasks', queryDTO)
  }

  /**
   * 开始分段处理
   */
  static async startSegmentation(documentIds: string[], options?: {
    chunkSize?: number
    chunkOverlap?: number
  }): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/segmentation/start', {
      documentIds,
      ...options
    })
  }

  /**
   * 取消分段任务
   */
  static async cancelSegmentationTask(taskId: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/segmentation/tasks/${taskId}/cancel`)
  }

  /**
   * 重试分段任务
   */
  static async retrySegmentationTask(taskId: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/segmentation/tasks/${taskId}/retry`)
  }

  // ==================== 向量化处理API ====================

  /**
   * 获取向量化任务列表
   */
  static async getVectorizationTasks(queryDTO: {
    current?: number
    size?: number
    status?: string
    datasetId?: string
  }): Promise<ApiResponse<PageResult<any>>> {
    return apiClient.get<PageResult<any>>('/api/rag/vectorization/tasks', queryDTO)
  }

  /**
   * 开始向量化处理
   */
  static async startVectorization(request: {
    datasetId?: string
    documentIds?: string[]
    type: 'full' | 'incremental'
    concurrency?: number
    optimizeAfter?: boolean
  }): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/vectorization/start', request)
  }

  /**
   * 暂停向量化任务
   */
  static async pauseVectorizationTask(taskId: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/vectorization/tasks/${taskId}/pause`)
  }

  /**
   * 恢复向量化任务
   */
  static async resumeVectorizationTask(taskId: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/vectorization/tasks/${taskId}/resume`)
  }

  /**
   * 取消向量化任务
   */
  static async cancelVectorizationTask(taskId: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/vectorization/tasks/${taskId}/cancel`)
  }

  /**
   * 重试向量化任务
   */
  static async retryVectorizationTask(taskId: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/vectorization/tasks/${taskId}/retry`)
  }

  /**
   * 获取向量化统计信息
   */
  static async getVectorizationStats(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/vectorization/stats')
  }

  // ==================== 向量状态监控API ====================

  /**
   * 获取向量集合列表
   */
  static async getVectorCollections(): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>('/api/rag/vector/collections')
  }

  /**
   * 获取向量集合详情
   */
  static async getVectorCollectionDetail(name: string): Promise<ApiResponse<any>> {
    return apiClient.get<any>(`/api/rag/vector/collections/${name}`)
  }

  /**
   * 优化向量集合
   */
  static async optimizeVectorCollection(name: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/vector/collections/${name}/optimize`)
  }

  /**
   * 重建向量集合索引
   */
  static async rebuildVectorCollection(name: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/rag/vector/collections/${name}/rebuild`)
  }

  /**
   * 获取向量统计信息
   */
  static async getVectorStats(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/vector/stats')
  }

  /**
   * 获取性能指标
   */
  static async getPerformanceMetrics(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/vector/performance')
  }

  /**
   * 获取健康检查结果
   */
  static async getHealthChecks(): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>('/api/rag/vector/health')
  }

  /**
   * 优化全局索引
   */
  static async optimizeGlobalIndex(): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/vector/optimize')
  }

  // ==================== 文件存储管理API ====================

  /**
   * 获取存储配置列表
   */
  static async getStorageConfigs(): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>('/api/rag/storage/configs')
  }

  /**
   * 创建存储配置
   */
  static async createStorageConfig(config: any): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/storage/configs', config)
  }

  /**
   * 更新存储配置
   */
  static async updateStorageConfig(id: string, config: any): Promise<ApiResponse<any>> {
    return apiClient.put<any>(`/api/rag/storage/configs/${id}`, config)
  }

  /**
   * 删除存储配置
   */
  static async deleteStorageConfig(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/storage/configs/${id}`)
  }

  /**
   * 测试存储连接
   */
  static async testStorageConnection(config: any): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/storage/test', config)
  }

  /**
   * 获取存储统计信息
   */
  static async getStorageStats(configId?: string): Promise<ApiResponse<any>> {
    const params = configId ? { configId } : {}
    return apiClient.get<any>('/api/rag/storage/stats', params)
  }

  // ==================== 知识库配置管理API ====================

  /**
   * 获取知识库配置
   */
  static async getDatasetConfig(datasetId: string): Promise<ApiResponse<any>> {
    return apiClient.get<any>(`/api/rag/datasets/${datasetId}/config`)
  }

  /**
   * 更新知识库配置
   */
  static async updateDatasetConfig(datasetId: string, config: any): Promise<ApiResponse<any>> {
    return apiClient.put<any>(`/api/rag/datasets/${datasetId}/config`, config)
  }

  /**
   * 获取可用的嵌入模型列表
   */
  static async getAvailableEmbeddingModels(): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>('/api/rag/embedding/models')
  }

  /**
   * 测试嵌入模型连接
   */
  static async testEmbeddingConnection(config: any): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/api/rag/embedding/test', config)
  }

  // ==================== 嵌入模型管理API ====================

  /**
   * 获取嵌入模型列表
   */
  static async getEmbeddingModels(): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>('/api/rag/embedding/models')
  }

  /**
   * 获取嵌入模型详情
   */
  static async getEmbeddingModelDetail(modelId: string): Promise<ApiResponse<any>> {
    return apiClient.get<any>(`/api/rag/embedding/models/${modelId}`)
  }

  /**
   * 测试指定嵌入模型
   */
  static async testSpecificEmbeddingModel(modelId: string, text: string): Promise<ApiResponse<any>> {
    return apiClient.post<any>(`/api/rag/embedding/models/${modelId}/test`, { text })
  }

  // ==================== 文件管理API ====================

  /**
   * 获取文件列表
   */
  static async getFiles(queryDTO: {
    current?: number
    size?: number
    name?: string
    type?: string
    storageId?: string
  }): Promise<ApiResponse<PageResult<any>>> {
    return apiClient.get<PageResult<any>>('/api/rag/files', queryDTO)
  }

  /**
   * 删除文件
   */
  static async deleteFile(fileId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/files/${fileId}`)
  }

  /**
   * 批量删除文件
   */
  static async batchDeleteFiles(fileIds: string[]): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/files/batch-delete', { fileIds })
  }

  /**
   * 获取文件预览URL
   */
  static async getFilePreviewUrl(fileId: string): Promise<ApiResponse<string>> {
    return apiClient.get<string>(`/api/rag/files/${fileId}/preview-url`)
  }

  /**
   * 获取文件下载URL
   */
  static async getFileDownloadUrl(fileId: string): Promise<ApiResponse<string>> {
    return apiClient.get<string>(`/api/rag/files/${fileId}/download-url`)
  }

  /**
   * 获取文档分段数据
   */
  static async getDocumentSegments(documentId: string): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>(`/api/rag/documents/${documentId}/segments`)
  }

  /**
   * 获取文档预览URL（OnlyOffice）
   */
  static async getDocumentPreviewUrl(documentId: string): Promise<ApiResponse<string>> {
    return apiClient.get<string>(`/api/rag/documents/${documentId}/preview-url`)
  }

  /**
   * 删除文档
   */
  static async deleteDocument(documentId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/api/rag/documents/${documentId}`)
  }

  /**
   * 记录文件删除操作
   */
  static async recordFileDeleteOperation(data: {
    originalFilename: string
    fileSize: number
    fileExtension: string
    mimeType: string
    fileHash: string
    datasetId: string
    batchId: string
    documentId?: string
    minioUrl: string
    deleteReason: string
  }): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/api/rag/upload-files/record-delete', data)
  }

  /**
   * 获取OnlyOffice文档配置
   */
  static async getOnlyOfficeConfig(documentId: string, mode: string = 'view'): Promise<ApiResponse<any>> {
    return apiClient.get<any>(`/api/rag/onlyoffice/config/${documentId}?mode=${mode}`)
  }

  // ==================== 配置管理API ====================

  /**
   * 获取RAG模块配置信息
   */
  static async getRagConfig(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/config')
  }

  // ==================== 健康检查API ====================

  /**
   * 模块健康状态检查
   */
  static async healthCheck(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/health/check')
  }

  /**
   * 获取模块基本信息
   */
  static async getModuleInfo(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/api/rag/health/info')
  }
}

export default RagAPI
