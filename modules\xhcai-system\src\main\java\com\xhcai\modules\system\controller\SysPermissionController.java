package com.xhcai.modules.system.controller;

import java.util.List;

import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.vo.SysUserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.annotation.RequiresTenantAdmin;
import com.xhcai.modules.system.annotation.DataScope;
import com.xhcai.modules.system.dto.SysPermissionQueryDTO;
import com.xhcai.modules.system.entity.SysPermission;
import com.xhcai.modules.system.service.ISysPermissionService;
import com.xhcai.modules.system.vo.SysPermissionVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 权限管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "权限管理", description = "权限管理相关接口")
@RestController
@RequestMapping("/api/system/permission")
@RequiresTenantAdmin(message = "权限管理需要租户管理员权限")
public class SysPermissionController {

    @Autowired
    private ISysPermissionService permissionService;

    /**
     * 分页查询权限列表
     */
    @Operation(summary = "分页查询权限列表", description = "根据条件分页查询权限信息")
    @GetMapping("/page")
    @RequiresPermissions("system:permission:view")
    @DataScope(deptAlias = "d", userAlias = "u")
    public Result<PageResult<SysPermissionVO>> page(@Valid SysPermissionQueryDTO queryDTO) {
        PageResult<SysPermissionVO> pageResult = permissionService.selectPermissionPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询权限列表
     */
    @Operation(summary = "查询权限列表", description = "根据条件查询权限列表")
    @GetMapping("/list")
    @RequiresPermissions("system:permission:view")
    @DataScope(deptAlias = "d", userAlias = "u")
    public Result<List<SysPermissionVO>> list(@Valid SysPermissionQueryDTO queryDTO) {
        List<SysPermissionVO> permissionList = permissionService.selectPermissionList(queryDTO);
        return Result.success(permissionList);
    }

    /**
     * 查询权限树
     */
    @Operation(summary = "查询权限树", description = "查询权限树形结构")
    @GetMapping("/tree")
    @RequiresPermissions("system:permission:view")
    @DataScope(deptAlias = "d", userAlias = "u")
    public Result<List<SysPermissionVO>> tree(@Valid SysPermissionQueryDTO queryDTO) {
        List<SysPermissionVO> permissionTree = permissionService.selectPermissionTree(queryDTO);
        return Result.success(permissionTree);
    }

    /**
     * 根据ID查询权限详情
     */
    @Operation(summary = "查询权限详情", description = "根据权限ID查询详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("system:permission:view")
    public Result<SysPermissionVO> getById(
            @Parameter(description = "权限ID", required = true) @PathVariable String id) {
        SysPermissionVO permissionVO = permissionService.selectPermissionById(id);
        return Result.success(permissionVO);
    }

    /**
     * 创建权限
     */
    @Operation(summary = "创建权限", description = "创建新的权限")
    @PostMapping
    @RequiresPermissions("system:permission:add")
    public Result<Void> create(@Valid @RequestBody SysPermission permission) {
        boolean result = permissionService.insertPermission(permission);
        return result ? Result.success() : Result.fail("创建权限失败");
    }

    /**
     * 更新权限信息
     */
    @Operation(summary = "更新权限", description = "更新权限信息")
    @PutMapping
    @RequiresPermissions("system:permission:edit")
    public Result<Void> update(@Valid @RequestBody SysPermission permission) {
        boolean result = permissionService.updatePermission(permission);
        return result ? Result.success() : Result.fail("更新权限失败");
    }

    /**
     * 删除权限
     */
    @Operation(summary = "删除权限", description = "批量删除权限")
    @DeleteMapping
    @RequiresPermissions("system:permission:remove")
    public Result<Void> delete(@RequestBody List<String> permissionIds) {
        boolean result = permissionService.deletePermissions(permissionIds);
        return result ? Result.success() : Result.fail("删除权限失败");
    }

    /**
     * 启用权限
     */
    @Operation(summary = "启用权限", description = "启用指定权限")
    @PutMapping("/{id}/enable")
    @RequiresPermissions("system:permission:edit")
    public Result<Void> enable(
            @Parameter(description = "权限ID", required = true) @PathVariable String id) {
        boolean result = permissionService.enablePermission(id);
        return result ? Result.success() : Result.fail("启用权限失败");
    }

    /**
     * 停用权限
     */
    @Operation(summary = "停用权限", description = "停用指定权限")
    @PutMapping("/{id}/disable")
    @RequiresPermissions("system:permission:edit")
    public Result<Void> disable(
            @Parameter(description = "权限ID", required = true) @PathVariable String id) {
        boolean result = permissionService.disablePermission(id);
        return result ? Result.success() : Result.fail("停用权限失败");
    }

    /**
     * 查询权限选择树
     */
    @Operation(summary = "查询权限选择树", description = "查询权限选择树，排除指定权限及其子权限")
    @GetMapping("/select-tree")
    @RequiresPermissions("system:permission:view")
    public Result<List<SysPermissionVO>> selectTree(
            @Parameter(description = "排除的权限ID") @RequestParam(required = false) String excludePermissionId) {
        List<SysPermissionVO> permissionTree = permissionService.buildPermissionSelectTree(excludePermissionId);
        return Result.success(permissionTree);
    }

    /**
     * 获取所有可用权限
     */
    @Operation(summary = "获取可用权限", description = "获取所有可用权限列表，用于角色分配")
    @GetMapping("/available")
    @RequiresPermissions("system:permission:view")
    public Result<List<SysPermissionVO>> getAvailablePermissions() {
        List<SysPermissionVO> permissions = permissionService.selectAllAvailablePermissions();
        return Result.success(permissions);
    }

    /**
     * 根据权限类型查询权限
     */
    @Operation(summary = "根据类型查询权限", description = "根据权限类型查询权限列表")
    @GetMapping("/type/{permissionType}")
    @RequiresPermissions("system:permission:view")
    public Result<List<SysPermissionVO>> getPermissionsByType(
            @Parameter(description = "权限类型", required = true) @PathVariable String permissionType) {
        List<SysPermissionVO> permissions = permissionService.selectPermissionsByType(permissionType);
        return Result.success(permissions);
    }

    /**
     * 根据用户ID查询权限
     */
    @Operation(summary = "查询用户权限", description = "根据用户ID查询权限列表")
    @GetMapping("/user/{userId}")
    @RequiresPermissions("system:permission:view")
    public Result<List<SysPermissionVO>> getPermissionsByUserId(
            @Parameter(description = "用户ID", required = true) @PathVariable String userId) {
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setId(userId);
        sysUserVO.setTenantId(SecurityUtils.getCurrentTenantId());
        List<SysPermissionVO> permissions = permissionService.selectPermissionsByUserId(sysUserVO);
        return Result.success(permissions);
    }

    /**
     * 根据角色ID查询权限
     */
    @Operation(summary = "查询角色权限", description = "根据角色ID查询权限列表")
    @GetMapping("/role/{roleId}")
    @RequiresPermissions("system:permission:view")
    public Result<List<SysPermissionVO>> getPermissionsByRoleId(
            @Parameter(description = "角色ID", required = true) @PathVariable String roleId) {
        List<SysPermissionVO> permissions = permissionService.selectPermissionsByRoleId(roleId);
        return Result.success(permissions);
    }

    /**
     * 获取权限路径
     */
    @Operation(summary = "获取权限路径", description = "获取权限的完整路径")
    @GetMapping("/{id}/path")
    @RequiresPermissions("system:permission:view")
    public Result<String> getPermissionPath(
            @Parameter(description = "权限ID", required = true) @PathVariable String id) {
        String path = permissionService.getPermissionPath(id);
        return Result.success(path);
    }

    /**
     * 移动权限
     */
    @Operation(summary = "移动权限", description = "移动权限到新的父权限下")
    @PutMapping("/{id}/move")
    @RequiresPermissions("system:permission:edit")
    public Result<Void> movePermission(
            @Parameter(description = "权限ID", required = true) @PathVariable String id,
            @Parameter(description = "新父权限ID", required = true) @RequestParam String newParentId) {
        boolean result = permissionService.movePermission(id, newParentId);
        return result ? Result.success() : Result.fail("移动权限失败");
    }

    /**
     * 批量更新权限状态
     */
    @Operation(summary = "批量更新状态", description = "批量更新权限状态")
    @PutMapping("/batch-status")
    @RequiresPermissions("system:permission:edit")
    public Result<Void> batchUpdateStatus(
            @RequestBody List<String> permissionIds,
            @Parameter(description = "状态", required = true) @RequestParam String status) {
        boolean result = permissionService.batchUpdateStatus(permissionIds, status);
        return result ? Result.success() : Result.fail("批量更新状态失败");
    }

    /**
     * 同步权限排序
     */
    @Operation(summary = "同步权限排序", description = "同步权限排序号")
    @PutMapping("/sync-order")
    @RequiresPermissions("system:permission:edit")
    public Result<Void> syncOrder(@RequestBody List<SysPermission> permissions) {
        boolean result = permissionService.syncPermissionOrder(permissions);
        return result ? Result.success() : Result.fail("同步排序失败");
    }

    /**
     * 检查权限编码是否存在
     */
    @Operation(summary = "检查权限编码", description = "检查权限编码是否已存在")
    @GetMapping("/check-code")
    @RequiresPermissions("system:permission:view")
    public Result<Boolean> checkPermissionCode(
            @Parameter(description = "权限编码", required = true) @RequestParam("permissionCode") String permissionCode,
            @Parameter(description = "排除的权限ID") @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = permissionService.existsPermissionCode(permissionCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查权限名称是否存在
     */
    @Operation(summary = "检查权限名称", description = "检查同级权限名称是否已存在")
    @GetMapping("/check-name")
    @RequiresPermissions("system:permission:view")
    public Result<Boolean> checkPermissionName(
            @Parameter(description = "权限名称", required = true) @RequestParam("permissionName") String permissionName,
            @Parameter(description = "父权限ID", required = true) @RequestParam("parentId") String parentId,
            @Parameter(description = "排除的权限ID") @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = permissionService.existsPermissionName(permissionName, parentId, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查是否存在子权限
     */
    @Operation(summary = "检查子权限", description = "检查是否存在子权限")
    @GetMapping("/{id}/has-children")
    @RequiresPermissions("system:permission:view")
    public Result<Boolean> hasChildren(
            @Parameter(description = "权限ID", required = true) @PathVariable String id) {
        boolean hasChildren = permissionService.hasChildren(id);
        return Result.success(hasChildren);
    }
}
