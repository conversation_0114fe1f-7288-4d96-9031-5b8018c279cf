package com.xhcai.modules.rag.mapper;

import java.util.List;

import com.xhcai.modules.rag.vo.DatasetVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.rag.entity.Dataset;

/**
 * 知识库Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface DatasetMapper extends BaseMapper<Dataset> {

    /**
     * 根据租户ID分页查询知识库列表
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param name 知识库名称（模糊查询）
     * @param dataSourceType 数据源类型
     * @return 知识库分页列表
     */
    /**
     * 根据租户ID分页查询知识库列表（基础版本）
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param name 知识库名称（模糊查询）
     * @param dataSourceType 数据源类型
     * @return 知识库分页列表
     */
    @Select("<script>"
            + "SELECT d.*, "
            + "(select count(1) from documents dc where dc.dataset_id = d.id) as documentCount, "
            + "(select sum(word_count) from documents dc where dc.dataset_id = d.id) as totalWordCount "
            + "FROM datasets d "
            + "WHERE d.deleted = 0 "
            + "<if test='tenantId != null and tenantId != \"\"'>"
            + "  AND d.tenant_id = #{tenantId} "
            + "</if>"
            + "<if test='name != null and name != \"\"'>"
            + "  AND d.name LIKE CONCAT('%', #{name}, '%') "
            + "</if>"
            + "<if test='dataSourceType != null and dataSourceType != \"\"'>"
            + "  AND d.data_source_type = #{dataSourceType} "
            + "</if>"
            + "ORDER BY d.create_time DESC"
            + "</script>")
    IPage<DatasetVO> selectPageByTenant(Page<DatasetVO> page,
                                        @Param("tenantId") String tenantId,
                                        @Param("name") String name,
                                        @Param("dataSourceType") String dataSourceType);

    /**
     * 根据租户ID查询知识库列表
     *
     * @param tenantId 租户ID
     * @return 知识库列表
     */
    @Select("SELECT * FROM datasets WHERE tenant_id = #{tenantId} AND deleted = 0 ORDER BY create_time DESC")
    List<DatasetVO> selectByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据模型ID查询使用该模型的知识库数量
     *
     * @param modelId 模型ID
     * @return 使用该模型的知识库数量
     */
    @Select("SELECT COUNT(*) FROM datasets WHERE model_id = #{modelId} AND deleted = 0")
    Long countByModelId(@Param("modelId") String modelId);

    /**
     * 根据租户ID统计知识库数量
     *
     * @param tenantId 租户ID
     * @return 知识库数量
     */
    @Select("SELECT COUNT(*) FROM datasets WHERE tenant_id = #{tenantId} AND deleted = 0")
    Long countByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据数据源类型统计知识库数量
     *
     * @param tenantId 租户ID
     * @param dataSourceType 数据源类型
     * @return 知识库数量
     */
    @Select("SELECT COUNT(*) FROM datasets WHERE tenant_id = #{tenantId} AND data_source_type = #{dataSourceType} AND deleted = 0")
    Long countByDataSourceType(@Param("tenantId") String tenantId, @Param("dataSourceType") String dataSourceType);

    /**
     * 查询用户有权限的知识库列表
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @param name 知识库名称（模糊查询）
     * @return 知识库分页列表
     */
    @Select("<script>"
            + "SELECT DISTINCT d.* FROM datasets d "
            + "LEFT JOIN dataset_permission dp ON d.id = dp.dataset_id "
            + "WHERE d.deleted = 0 AND d.tenant_id = #{tenantId} "
            + "<if test='name != null and name != \"\"'>"
            + "  AND d.name LIKE CONCAT('%', #{name}, '%') "
            + "</if>"
            + "AND ("
            + "  d.create_by = #{userId} "
            + "  OR dp.object_type = 'account' AND dp.object_id = #{userId} "
            + "  OR dp.object_type = 'dept' AND dp.object_id = #{deptId} "
            + "  <if test='roleIds != null and roleIds.size() > 0'>"
            + "    OR (dp.object_type = 'rule' AND dp.object_id IN "
            + "    <foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>"
            + "      #{roleId}"
            + "    </foreach>)"
            + "  </if>"
            + ") "
            + "ORDER BY d.create_time DESC"
            + "</script>")
    IPage<Dataset> selectPageByUserPermission(Page<Dataset> page,
            @Param("tenantId") String tenantId,
            @Param("userId") String userId,
            @Param("deptId") String deptId,
            @Param("roleIds") List<String> roleIds,
            @Param("name") String name);

    /**
     * 检查用户是否有知识库权限
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @return 是否有权限
     */
    @Select("<script>"
            + "SELECT COUNT(*) > 0 FROM datasets d "
            + "LEFT JOIN dataset_permission dp ON d.id = dp.dataset_id "
            + "WHERE d.id = #{datasetId} AND d.deleted = 0 "
            + "AND ("
            + "  d.create_by = #{userId} "
            + "  OR dp.object_type = 'account' AND dp.object_id = #{userId} "
            + "  OR dp.object_type = 'dept' AND dp.object_id = #{deptId} "
            + "  <if test='roleIds != null and roleIds.size() > 0'>"
            + "    OR (dp.object_type = 'rule' AND dp.object_id IN "
            + "    <foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>"
            + "      #{roleId}"
            + "    </foreach>)"
            + "  </if>"
            + ")"
            + "</script>")
    boolean hasPermission(@Param("datasetId") String datasetId,
            @Param("userId") String userId,
            @Param("deptId") String deptId,
            @Param("roleIds") List<String> roleIds);
}
