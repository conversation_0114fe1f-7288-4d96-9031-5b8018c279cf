package com.xhcai.modules.agent.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 智能体模块配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class AgentModuleConfig {

    private static final Logger log = LoggerFactory.getLogger(AgentModuleConfig.class);

    /**
     * 智能体管理API分组配置
     */
    @Bean
    public GroupedOpenApi agentApi() {
        return GroupedOpenApi.builder()
                .group("agent")
                .displayName("智能体管理")
                .pathsToMatch("/api/agent/**")
                .build();
    }

    /**
     * 智能体聊天API分组配置
     */
    @Bean
    public GroupedOpenApi chatApi() {
        return GroupedOpenApi.builder()
                .group("chat")
                .displayName("智能体聊天")
                .pathsToMatch("/api/chat/**")
                .build();
    }
}
