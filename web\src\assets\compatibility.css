/* CSS兼容性处理文件 */
/* 为不支持现代CSS特性的浏览器提供fallback */

/* 1. CSS Grid 兼容性处理 */
.grid-fallback {
  /* Flexbox fallback for CSS Grid */
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* 支持CSS Grid的浏览器 */
@supports (display: grid) {
  .grid-fallback {
    display: grid;
  }
}

/* 2. Flexbox 兼容性处理 */
.flex-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.flex-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.flex-wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* 3. backdrop-filter 兼容性处理 */
.backdrop-blur-fallback {
  /* 不支持backdrop-filter的浏览器使用半透明背景 */
  background-color: rgba(255, 255, 255, 0.8);
}

/* 支持backdrop-filter的浏览器 */
@supports (backdrop-filter: blur(10px)) or (-webkit-backdrop-filter: blur(10px)) {
  .backdrop-blur-fallback {
    background-color: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
}

/* 4. CSS变量兼容性处理 */
/* 为不支持CSS变量的浏览器提供固定值 */
.css-vars-fallback {
  /* Fallback colors */
  color: #2c3e50;
  background-color: #667eea;
  border-color: rgba(0, 0, 0, 0.1);
}

/* 支持CSS变量的浏览器 */
@supports (color: var(--primary-color)) {
  .css-vars-fallback {
    color: var(--text-primary, #2c3e50);
    background-color: var(--primary-color, #667eea);
    border-color: var(--border-light, rgba(0, 0, 0, 0.1));
  }
}

/* 5. transform 兼容性处理 */
.transform-fallback {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}

.transform-fallback:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
}

/* 6. border-radius 兼容性处理 */
.rounded-fallback {
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

/* 7. box-shadow 兼容性处理 */
.shadow-fallback {
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 8. gradient 兼容性处理 */
.gradient-fallback {
  /* Fallback for old browsers */
  background: #667eea;
  /* Chrome 10-25, Safari 5.1-6 */
  background: -webkit-linear-gradient(135deg, #667eea, #764ba2);
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  background: linear-gradient(135deg, #667eea, #764ba2);
}

/* 9. object-fit 兼容性处理 */
.object-fit-fallback {
  /* Fallback for IE */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 支持object-fit的浏览器 */
@supports (object-fit: cover) {
  .object-fit-fallback {
    object-fit: cover;
  }
}

/* 10. sticky positioning 兼容性处理 */
.sticky-fallback {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

/* IE11不支持sticky，使用fixed作为fallback */
@media (forced-colors: active) {
  .sticky-fallback {
    position: fixed;
  }
}

/* 保持对旧浏览器的兼容性 */
@supports not (forced-colors: active) {
  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .sticky-fallback {
      position: fixed;
    }
  }
}

/* 11. clip-path 兼容性处理 */
.clip-path-fallback {
  /* Fallback for browsers that don't support clip-path */
  overflow: hidden;
}

@supports (clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%)) {
  .clip-path-fallback {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}

/* 12. 滚动条样式兼容性 */
.scrollbar-fallback {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #667eea rgba(0, 0, 0, 0.1);
}

/* Webkit browsers */
.scrollbar-fallback::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-fallback::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.scrollbar-fallback::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 4px;
}

/* 13. 文本截断兼容性 */
.text-ellipsis-fallback {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  /* Fallback for older browsers */
  width: 100%;
}

/* 多行文本截断 */
.text-ellipsis-multiline-fallback {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  /* Fallback for non-webkit browsers */
  max-height: 3em;
  line-height: 1.5em;
}

/* 14. 用户选择兼容性 */
.user-select-none-fallback {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 15. 指针事件兼容性 */
.pointer-events-none-fallback {
  pointer-events: none;
  /* IE fallback */
  cursor: default;
}

/* 16. 混合模式兼容性 */
.mix-blend-mode-fallback {
  /* Fallback for browsers that don't support mix-blend-mode */
  opacity: 0.8;
}

@supports (mix-blend-mode: multiply) {
  .mix-blend-mode-fallback {
    mix-blend-mode: multiply;
    opacity: 1;
  }
}
