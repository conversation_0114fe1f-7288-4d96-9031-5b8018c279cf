package com.xhcai.modules.dify.controller;


import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateRequestDTO;
import com.xhcai.modules.dify.dto.agent.DifyAgentCreateResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyAppsListResponseDTO;
import com.xhcai.modules.dify.service.IDifyAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 智能体控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Tag(name = "智能体管理", description = "Dify智能体服务对接")
@RestController
@RequestMapping("/api/dify/agents")
public class DifyAgentController {

    @Autowired
    private IDifyAgentService agentService;

    @Operation(summary = "创建指定平台智能体", description = "创建新的Dify智能体")
    @PostMapping
    @RequiresPermissions("dify:agent:create")
    public Mono<Result<DifyAgentCreateResponseDTO>> createAgent(@Valid @RequestBody DifyAgentCreateRequestDTO createRequest) {
        // 在Controller层获取用户信息，然后传递到响应式上下文中
        String userId = SecurityUtils.getCurrentUserId();
        if (userId == null || userId.trim().isEmpty()) {
            return Mono.just(Result.fail("用户未登录"));
        }

        // 如果请求中包含platformId，则调用指定平台的方法
        if (createRequest.getPlatformId() != null && !createRequest.getPlatformId().trim().isEmpty()) {
            return agentService.createThirdPartyAgent(createRequest)
                    .contextWrite(context -> context.put("userId", userId));
        } else {
            return Mono.just(Result.fail("需指定dify平台ID"));
        }
    }




    @Operation(summary = "获取指定平台的智能体列表", description = "获取指定第三方智能体平台的智能体列表")
    @GetMapping("/list/{platformId}")
    @RequiresPermissions("dify:agent:view")
    public Mono<Result<DifyAppsListResponseDTO>> getAgentsListByPlatform(
            @Parameter(description = "平台ID") @PathVariable String platformId,
            @Parameter(description = "页码，默认1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，默认30") @RequestParam(defaultValue = "30") Integer limit,
            @Parameter(description = "智能体名称过滤（可选）") @RequestParam(required = false) String name,
            @Parameter(description = "是否只获取我创建的智能体，默认true") @RequestParam(defaultValue = "true") Boolean isCreatedByMe) {

        // 在Controller层获取用户信息，然后传递到响应式上下文中
        String userId = SecurityUtils.getCurrentUserId();
        if (userId == null || userId.trim().isEmpty()) {
            return Mono.just(Result.fail("用户未登录"));
        }

        return agentService.getAgentsList(platformId, page, limit, name, isCreatedByMe)
                .contextWrite(context -> context.put("userId", userId));
    }
}
