package com.xhcai.modules.dify.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Dify文件信息DTO
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Schema(description = "Dify文件信息")
public class DifyFileDTO {

    /**
     * 文件类型
     */
    @Schema(description = "文件类型", example = "image", allowableValues = {"image", "document", "audio", "video"})
    private String type;

    /**
     * 传输方式
     */
    @Schema(description = "传输方式", example = "remote_url", allowableValues = {"remote_url", "local_file"})
    @JsonProperty("transfer_method")
    private String transferMethod;

    /**
     * 文件URL（当transfer_method为remote_url时使用）
     */
    @Schema(description = "文件URL")
    private String url;

    /**
     * 文件ID（当transfer_method为local_file时使用）
     */
    @Schema(description = "文件ID")
    @JsonProperty("upload_file_id")
    private String uploadFileId;

    public DifyFileDTO() {
    }

    public DifyFileDTO(String type, String transferMethod, String url) {
        this.type = type;
        this.transferMethod = transferMethod;
        this.url = url;
    }

    public DifyFileDTO(String type, String transferMethod, String url, String uploadFileId) {
        this.type = type;
        this.transferMethod = transferMethod;
        this.url = url;
        this.uploadFileId = uploadFileId;
    }

    // Getters and Setters
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTransferMethod() {
        return transferMethod;
    }

    public void setTransferMethod(String transferMethod) {
        this.transferMethod = transferMethod;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUploadFileId() {
        return uploadFileId;
    }

    public void setUploadFileId(String uploadFileId) {
        this.uploadFileId = uploadFileId;
    }

    @Override
    public String toString() {
        return "DifyFileDTO{" +
                "type='" + type + '\'' +
                ", transferMethod='" + transferMethod + '\'' +
                ", url='" + url + '\'' +
                ", uploadFileId='" + uploadFileId + '\'' +
                '}';
    }
}
