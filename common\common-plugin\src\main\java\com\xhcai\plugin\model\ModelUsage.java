package com.xhcai.plugin.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 模型使用情况统计
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelUsage {
    
    /**
     * 提示词 tokens 数量
     */
    private Integer promptTokens;
    
    /**
     * 完成 tokens 数量
     */
    private Integer completionTokens;
    
    /**
     * 总 tokens 数量
     */
    private Integer totalTokens;
    
    /**
     * 请求耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 成本（可选）
     */
    private Double cost;
}
