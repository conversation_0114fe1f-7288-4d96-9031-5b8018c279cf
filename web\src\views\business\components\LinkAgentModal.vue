<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="fas fa-link"></i>
          关联智能体
        </h3>
        <button class="modal-close" @click="handleClose">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <div class="project-info">
          <h4>{{ project?.name }}</h4>
          <p>选择要关联到此项目的智能体</p>
        </div>

        <!-- 搜索框 -->
        <div class="search-section">
          <div class="search-input-wrapper">
            <i class="fas fa-search search-icon"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索智能体名称或描述..."
              class="search-input"
            />
          </div>
        </div>

        <!-- 智能体列表 -->
        <div class="agents-section">
          <div v-if="loading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>

          <div v-else-if="filteredAgents.length === 0" class="empty-state">
            <i class="fas fa-robot"></i>
            <p>{{ searchQuery ? '未找到匹配的智能体' : '暂无可关联的智能体' }}</p>
          </div>

          <div v-else class="agents-list">
            <div
              v-for="agent in filteredAgents"
              :key="agent.id"
              class="agent-item"
              :class="{ selected: selectedAgents.includes(agent.id) }"
              @click="toggleAgent(agent.id)"
            >
              <div class="agent-checkbox">
                <i v-if="selectedAgents.includes(agent.id)" class="fas fa-check"></i>
              </div>
              <div class="agent-avatar" :style="{ background: agent.iconBackground || '#667eea' }">
                <i :class="agent.avatar || 'fas fa-robot'"></i>
              </div>
              <div class="agent-info">
                <div class="agent-name">{{ agent.name }}</div>
                <div class="agent-description">{{ agent.description || '暂无描述' }}</div>
                <div class="agent-meta">
                  <span class="agent-type">{{ getAgentTypeLabel(agent.type) }}</span>
                  <span class="agent-status" :class="`status-${agent.status}`">
                    {{ getAgentStatusLabel(agent.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 选择统计 -->
        <div v-if="selectedAgents.length > 0" class="selection-summary">
          <i class="fas fa-check-circle"></i>
          已选择 {{ selectedAgents.length }} 个智能体
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="handleClose">
          取消
        </button>
        <button 
          class="btn btn-primary" 
          @click="handleConfirm"
          :disabled="selectedAgents.length === 0 || linking"
        >
          <i v-if="linking" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-link"></i>
          {{ linking ? '关联中...' : `关联 ${selectedAgents.length} 个智能体` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { AgentsAPI, type AgentVO } from '@/api/agents'
import type { BusinessProject } from '@/api/business'

// Props
const props = defineProps<{
  visible: boolean
  project?: BusinessProject | null
}>()

// Emits
const emit = defineEmits<{
  close: []
  confirm: [agentIds: string[]]
}>()

// 响应式数据
const loading = ref(false)
const linking = ref(false)
const searchQuery = ref('')
const availableAgents = ref<AgentVO[]>([])
const selectedAgents = ref<string[]>([])

// 计算属性
const filteredAgents = computed(() => {
  if (!searchQuery.value.trim()) {
    return availableAgents.value
  }
  
  const query = searchQuery.value.toLowerCase()
  return availableAgents.value.filter(agent => 
    agent.name.toLowerCase().includes(query) ||
    (agent.description && agent.description.toLowerCase().includes(query))
  )
})

// 方法
const loadAvailableAgents = async () => {
  if (!props.project?.id) return
  
  try {
    loading.value = true
    
    // 获取所有智能体，过滤掉已关联到当前项目的
    const response = await AgentsAPI.getAgents({
      current: 1,
      size: 20, // 获取所有
      projectId: null // 只获取未关联项目的智能体
    })
    
    if (response.success && response.data) {
      availableAgents.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载可关联智能体失败:', error)
  } finally {
    loading.value = false
  }
}

const toggleAgent = (agentId: string) => {
  const index = selectedAgents.value.indexOf(agentId)
  if (index > -1) {
    selectedAgents.value.splice(index, 1)
  } else {
    selectedAgents.value.push(agentId)
  }
}

const getAgentTypeLabel = (type: string) => {
  const labels = {
    'advanced-chat': 'Chatflow',
    'workflow': '工作流',
    'chat': '聊天助手',
    'agent-chat': 'Agent',
    'completion': '文本生成'
  }
  return labels[type as keyof typeof labels] || type
}

const getAgentStatusLabel = (status: string) => {
  const labels = {
    'active': '活跃',
    'inactive': '停用',
    'draft': '草稿'
  }
  return labels[status as keyof typeof labels] || status
}

const handleConfirm = async () => {
  if (selectedAgents.value.length === 0) return
  
  try {
    linking.value = true
    emit('confirm', [...selectedAgents.value])
  } finally {
    linking.value = false
  }
}

const handleClose = () => {
  emit('close')
}

const handleOverlayClick = () => {
  handleClose()
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    selectedAgents.value = []
    searchQuery.value = ''
    loadAvailableAgents()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadAvailableAgents()
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  width: 100%;
  max-width: 700px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-light);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-light);
}

.project-info {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.project-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.project-info p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.search-section {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.agents-section {
  min-height: 300px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-muted);
}

.loading-state i,
.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.loading-state i {
  color: var(--primary-color);
}

.agents-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.agent-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.agent-item:hover {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.02);
}

.agent-item.selected {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.agent-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  flex-shrink: 0;
}

.agent-item.selected .agent-checkbox {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.agent-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.agent-info {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.agent-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  line-height: 1.4;
}

.agent-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.agent-type,
.agent-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.agent-type {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.agent-status.status-active {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.agent-status.status-inactive {
  background: rgba(149, 165, 166, 0.1);
  color: #95a5a6;
}

.agent-status.status-draft {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.selection-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(46, 204, 113, 0.1);
  border: 1px solid rgba(46, 204, 113, 0.2);
  border-radius: var(--border-radius);
  color: #2ecc71;
  font-size: 14px;
  font-weight: 500;
  margin-top: 16px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.btn-secondary {
  background: white;
  color: var(--text-primary);
  border: 2px solid var(--border-light);
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}
</style>
