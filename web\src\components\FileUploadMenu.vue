<template>
  <div class="file-upload-menu relative">
    <!-- 触发按钮 -->
    <button
      @click="toggleMenu"
      class="upload-trigger p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
      :class="{ 'text-blue-600 bg-blue-50': isOpen }"
      title="上传文件"
    >
      <el-icon class="text-lg">
        <Plus />
      </el-icon>
    </button>

    <!-- 上拉菜单 -->
    <div 
      v-if="isOpen"
      class="upload-menu absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-40"
    >
      <div class="py-2">
        <!-- 文件上传 -->
        <button
          @click="triggerFileUpload"
          class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
        >
          <el-icon class="text-blue-500"><Document /></el-icon>
          文件上传
        </button>
        
        <!-- 图片上传 -->
        <button
          @click="triggerImageUpload"
          class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
        >
          <el-icon class="text-green-500"><Picture /></el-icon>
          图片上传
        </button>
        
        <!-- 证件上传 -->
        <button
          @click="triggerIdUpload"
          class="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
        >
          <el-icon class="text-orange-500"><CreditCard /></el-icon>
          证件上传
        </button>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      class="hidden"
      @change="handleFileSelect"
      accept=".pdf,.doc,.docx,.txt,.xlsx,.xls,.ppt,.pptx"
    />
    
    <input
      ref="imageInput"
      type="file"
      multiple
      class="hidden"
      @change="handleImageSelect"
      accept="image/*"
    />
    
    <input
      ref="idInput"
      type="file"
      multiple
      class="hidden"
      @change="handleIdSelect"
      accept="image/*,.pdf"
    />

    <!-- 遮罩层 -->
    <div 
      v-if="isOpen"
      @click="closeMenu"
      class="fixed inset-0 z-40"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Plus, Document, Picture, CreditCard } from '@element-plus/icons-vue'
import { useChatInputStore } from '@/stores/chatInputStore'

// 使用全局聊天输入状态
const chatInputStore = useChatInputStore()

// 响应式数据
const isOpen = ref(false)
const fileInput = ref<HTMLInputElement>()
const imageInput = ref<HTMLInputElement>()
const idInput = ref<HTMLInputElement>()

// 方法
const toggleMenu = () => {
  isOpen.value = !isOpen.value
}

const closeMenu = () => {
  isOpen.value = false
}

const triggerFileUpload = () => {
  fileInput.value?.click()
  closeMenu()
}

const triggerImageUpload = () => {
  imageInput.value?.click()
  closeMenu()
}

const triggerIdUpload = () => {
  idInput.value?.click()
  closeMenu()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    chatInputStore.handleFileUpload(target.files, 'file')
    target.value = '' // 清空输入
  }
}

const handleImageSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    chatInputStore.handleFileUpload(target.files, 'image')
    target.value = '' // 清空输入
  }
}

const handleIdSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    chatInputStore.handleFileUpload(target.files, 'id')
    target.value = '' // 清空输入
  }
}

// 点击外部关闭
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.file-upload-menu')) {
    closeMenu()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.upload-menu {
  animation: slideUp 0.2s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.upload-menu button:first-child {
  border-radius: 0.5rem 0.5rem 0 0;
}

.upload-menu button:last-child {
  border-radius: 0 0 0.5rem 0.5rem;
}
</style>
