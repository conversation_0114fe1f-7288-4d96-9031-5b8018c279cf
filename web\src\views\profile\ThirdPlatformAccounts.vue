<template>
  <div class="third-platform-agent-accounts">
    <div class="section-header">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">第三方智能体账号管理</h2>
      <p class="text-gray-600">管理您在各个第三方智能体平台的登录账号信息</p>
    </div>

    <div class="space-y-6 mt-6">
      <!-- 添加账号按钮 -->
      <div class="flex justify-end">
        <button 
          @click="showAddAccount = true"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm font-medium flex items-center gap-2"
        >
          <el-icon><Plus /></el-icon>
          添加账号
        </button>
      </div>

      <!-- 账号列表 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div v-for="account in thirdPlatformAccounts" :key="account.id" class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
          <!-- 平台信息 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center gap-3">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center text-2xl" :style="{ backgroundColor: account.platformInfo?.iconBg || '#3b82f6' }">
                {{ account.platformInfo?.icon }}
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">{{ account.platformInfo?.name }}</h3>
                <p class="text-sm text-gray-600">{{ account.platformInfo?.description }}</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" :class="getStatusClass(account.status)">
                {{ account.statusText || getStatusText(account.status) }}
              </span>
              <el-dropdown @command="handleAccountAction">
                <button class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center">
                  <el-icon class="text-gray-500"><MoreFilled /></el-icon>
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit-${account.id}`">编辑</el-dropdown-item>
                    <el-dropdown-item :command="`test-${account.id}`">测试连接</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${account.id}`" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 账号信息 -->
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">登陆账号</span>
              <span class="text-sm font-medium text-gray-800">{{ account.accountName }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">添加时间</span>
              <span class="text-sm text-gray-600">{{ formatDate(account.createTime) }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">最后使用</span>
              <span class="text-sm text-gray-600">{{ formatDate(account.lastUsedTime) }}</span>
            </div>
          </div>

          <!-- 使用统计 -->
          <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-3 gap-4 text-center">
              <div>
                <p class="text-lg font-bold text-blue-600">{{ account.totalCalls }}</p>
                <p class="text-xs text-gray-600">总调用次数</p>
              </div>
              <div>
                <p class="text-lg font-bold text-green-600">{{ account.successRate }}%</p>
                <p class="text-xs text-gray-600">成功率</p>
              </div>
              <div>
                <p class="text-lg font-bold text-orange-600">{{ account.avgResponseTime }}ms</p>
                <p class="text-xs text-gray-600">平均响应时间</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="thirdPlatformAccounts.length === 0" class="col-span-full text-center py-12">
          <el-icon class="text-4xl text-gray-400 mb-4"><Connection /></el-icon>
          <p class="text-gray-500 mb-4">暂未添加任何第三方智能体账号</p>
          <button 
            @click="showAddAccount = true"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm"
          >
            添加第一个账号
          </button>
        </div>
      </div>

      <!-- 支持的平台 -->
      <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/50 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <el-icon class="text-blue-500"><Platform /></el-icon>
          支持的第三方平台
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div v-for="platform in supportedPlatforms" :key="platform.id" class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div class="w-8 h-8 rounded-lg flex items-center justify-center text-lg" :style="{ backgroundColor: platform.iconBg }">
              {{ platform.icon }}
            </div>
            <div>
              <p class="text-sm font-medium text-gray-800">{{ platform.name}}</p>
              <p class="text-xs text-gray-600">{{ platform.accessScopeText }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加账号弹窗 -->
    <el-dialog v-model="showAddAccount" title="添加第三方智能体账号" width="600px">
      <el-form :model="accountForm" label-width="120px">
        <el-form-item label="选择平台">
          <el-select v-model="accountForm.platformId" placeholder="请选择平台" style="width: 100%">
            <el-option
              v-for="platform in supportedPlatforms"
              :key="platform.id"
              :label="platform.name"
              :value="platform.id"
            >
              <div class="flex items-center gap-2">
                <span :style="{ backgroundColor: platform.iconBg }" class="w-6 h-6 rounded flex items-center justify-center text-sm">
                  {{ platform.icon }}
                </span>
                <span>{{ platform.name }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="登陆账号">
          <el-input v-model="accountForm.accountName" placeholder="请输入登陆账号" />
        </el-form-item>
        <el-form-item label="登陆密码">
          <el-input v-model="accountForm.pwd" type="password" show-password placeholder="请输入登陆密码" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="accountForm.remark" type="textarea" placeholder="请输入备注信息（可选）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddAccount = false">取消</el-button>
          <el-button type="primary" @click="addAccount">确认添加</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑账号弹窗 -->
    <el-dialog v-model="showEditAccount" title="编辑第三方智能体账号" width="600px">
      <el-form :model="editAccountForm" label-width="120px">
        <el-form-item label="平台">
          <el-input :value="getSelectedPlatform(editAccountForm.platformId)?.name" disabled />
        </el-form-item>
        <el-form-item label="登陆账号">
          <el-input v-model="editAccountForm.accountName" placeholder="请输入登陆账号" />
        </el-form-item>
        <el-form-item label="登陆密码">
          <el-input v-model="editAccountForm.pwd" type="password" show-password placeholder="请输入登陆密码（留空则不修改）" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editAccountForm.remark" type="textarea" placeholder="请输入备注信息（可选）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditAccount = false">取消</el-button>
          <el-button type="primary" @click="updateAccount">确认修改</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Plus, MoreFilled, Connection, Platform } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getThirdPlatformAccountPage,
  createThirdPlatformAccount,
  updateThirdPlatformAccount,
  deleteThirdPlatformAccount,
  testThirdPlatformAccountConnection,
  type ThirdPlatformAccountVO,
  type ThirdPlatformAccountCreateDTO,
  type ThirdPlatformAccountUpdateDTO
} from '@/api/agent/ThirdPlatformAccount'

import { ThirdPlatformAPI } from '@/api/agents'

// 第三方账号列表
const thirdPlatformAccounts = ref<ThirdPlatformAccountVO[]>([])

// 支持的平台
const supportedPlatforms = ref<any[]>([])

// 加载状态
const loading = ref(false)

// 弹窗状态
const showAddAccount = ref(false)
const showEditAccount = ref(false)

// 表单数据
const accountForm = reactive<ThirdPlatformAccountCreateDTO>({
  platformId: '',
  accountName: '',
  apiKey: '',
  pwd: '',
  remark: '',
  status: 1
})

const editAccountForm = reactive<ThirdPlatformAccountUpdateDTO & { id: string; platformId: string }>({
  id: '',
  platformId: '',
  accountName: '',
  apiKey: '',
  pwd: '',
  remark: '',
  status: 1
})



// 初始化数据
onMounted(async () => {
  // 先加载支持的平台，然后再加载账号列表
  await loadSupportedPlatforms()
  await loadAccounts()
})

// 加载账号列表
const loadAccounts = async () => {
  try {
    loading.value = true
    const response = await getThirdPlatformAccountPage({
      current: 1,
      size: 100
    })

    const accounts = (response as any).data?.records || []

    // 为每个账号填充平台信息
    thirdPlatformAccounts.value = accounts.map((account: any) => {
      const platform = supportedPlatforms.value.find(p => p.id === account.platformId)
      return {
        ...account,
        platformInfo: platform
      }
    })
  } catch (error) {
    console.error('加载账号列表失败:', error)
    ElMessage.error('加载账号列表失败')
  } finally {
    loading.value = false
  }
}

// 加载支持的平台
const loadSupportedPlatforms = async () => {
  try {
    // 使用新的第三方智能体列表接口
    const response = await ThirdPlatformAPI.getThirdPlatformList()

    if (response.success && response.data) {
      // 将第三方智能体转换为平台信息
      supportedPlatforms.value = response.data
    } else {
      supportedPlatforms.value = []
    }
  } catch (error) {
    console.error('加载支持平台失败:', error)
    supportedPlatforms.value = []
  }
}

// 方法
const getStatusClass = (status: number) => {
  return status === 1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: number) => {
  return status === 1 ? '正常' : '禁用'
}



const formatDate = (date: Date | string | undefined) => {
  if (!date) return '未知'

  const dateObj = typeof date === 'string' ? new Date(date) : date

  if (isNaN(dateObj.getTime())) return '无效日期'

  return dateObj.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getSelectedPlatform = (platformId: string) => {
  return supportedPlatforms.value.find(p => p.id === platformId)
}

const handleAccountAction = (command: string) => {
  const [action, accountId] = command.split('-')
  const account = thirdPlatformAccounts.value.find(acc => acc.id === accountId)

  if (!account) return

  switch (action) {
    case 'edit':
      editAccountForm.id = account.id
      editAccountForm.platformId = account.platformId
      editAccountForm.accountName = account.accountName
      editAccountForm.apiKey = account.apiKey || ''
      editAccountForm.pwd = '' // 密码字段留空，用户可以选择是否修改
      editAccountForm.remark = account.remark || ''
      editAccountForm.status = account.status
      showEditAccount.value = true
      break
    case 'test':
      testConnection(account)
      break
    case 'delete':
      deleteAccount(account)
      break
  }
}

const addAccount = async () => {
  if (!accountForm.platformId || !accountForm.accountName || !accountForm.pwd) {
    ElMessage.warning('请填写必要信息')
    return
  }

  try {
    loading.value = true
    const result = await createThirdPlatformAccount(accountForm)
    if (result.success) {
      ElMessage.success('账号添加成功')
      showAddAccount.value = false

      // 重置表单
      Object.assign(accountForm, {
        platformId: '',
        accountName: '',
        apiKey: '',
        pwd: '',
        remark: '',
        status: 1
      })

      // 重新加载列表
      await loadAccounts()
    } else {
      ElMessage.error(result.message || '账号添加失败')
    }
  } catch (error) {
    console.error('添加账号失败:', error)
    ElMessage.error('账号添加失败')
  } finally {
    loading.value = false
  }
}

const updateAccount = async () => {
  if (!editAccountForm.id) return

  try {
    loading.value = true
    const { id, ...updateData } = editAccountForm
    const result = await updateThirdPlatformAccount(id, updateData)
    if (result.success) {
      ElMessage.success('账号更新成功')
      showEditAccount.value = false
      // 重新加载列表
      await loadAccounts()
    } else {
      ElMessage.error(result.message || '账号更新失败')
    }
  } catch (error) {
    console.error('更新账号失败:', error)
    ElMessage.error('账号更新失败')
  } finally {
    loading.value = false
  }
}

const testConnection = async (account: ThirdPlatformAccountVO) => {
  try {
    ElMessage.info('正在测试连接...')
    const result = await testThirdPlatformAccountConnection(account.id)
    if (result.success) {
      ElMessage.success('连接测试成功')
      // 重新加载列表以更新测试结果
      await loadAccounts()
    } else {
      ElMessage.error(result.message || '连接测试失败')
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('连接测试失败')
  }
}

const deleteAccount = (account: ThirdPlatformAccountVO) => {
  ElMessageBox.confirm(
    `确定要删除账号"${account.accountName}"吗？此操作不可恢复。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const result = await deleteThirdPlatformAccount(account.id)
      if (result.success) {
        ElMessage.success('账号删除成功')
        // 重新加载列表
        await loadAccounts()
      } else {
        ElMessage.error(result.message || '账号删除失败')
      }
    } catch (error) {
      console.error('删除账号失败:', error)
      ElMessage.error('账号删除失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}
</script>

<style scoped>
.section-header {
  margin-bottom: 1.5rem;
}
</style>
