package com.xhcai.modules.system.service;

import com.xhcai.modules.system.dto.ModuleInitDTO;

import java.util.List;

/**
 * 模块发现服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IModuleDiscoveryService {

    /**
     * 获取所有可用的模块列表
     *
     * @return 模块列表
     */
    List<ModuleInitDTO> getAllModules();

    /**
     * 获取指定模块的信息
     *
     * @param moduleId 模块ID
     * @return 模块信息
     */
    ModuleInitDTO getModuleInfo(String moduleId);

    /**
     * 刷新模块列表
     */
    void refreshModules();
}
