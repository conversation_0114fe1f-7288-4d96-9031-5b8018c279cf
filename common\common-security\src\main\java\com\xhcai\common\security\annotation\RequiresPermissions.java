package com.xhcai.common.security.annotation;

import java.lang.annotation.*;

/**
 * 权限校验注解
 * 用于方法级别的权限控制
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPermissions {

    /**
     * 需要的权限标识
     * 支持多个权限，默认为AND关系
     */
    String[] value() default {};

    /**
     * 权限关系类型
     * AND: 需要拥有所有权限
     * OR: 需要拥有任意一个权限
     */
    Logical logical() default Logical.AND;

    /**
     * 权限校验失败时的提示信息
     */
    String message() default "权限不足";

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 且关系
         */
        AND,
        /**
         * 或关系
         */
        OR
    }
}
