/**
 * 插件管理相关API
 */

import apiClient from '@/utils/apiClient'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

// 插件基本信息
export interface Plugin {
  id: string
  name: string
  description: string
  version: string
  author: string
  category: string
  tags: string[]
  icon?: string
  status: 'active' | 'inactive' | 'pending' | 'rejected'
  isOfficial: boolean
  isInstalled: boolean
  downloadCount: number
  rating: number
  createdAt: string
  updatedAt: string
  config: PluginConfig
  manifest: PluginManifest
}

// 插件配置
export interface PluginConfig {
  permissions: string[]
  dependencies: string[]
  settings: Record<string, any>
  apiEndpoints?: string[]
  webhooks?: string[]
}

// 插件清单
export interface PluginManifest {
  name: string
  version: string
  description: string
  author: string
  homepage?: string
  repository?: string
  license: string
  keywords: string[]
  main: string
  scripts?: Record<string, string>
  dependencies?: Record<string, string>
}

// 插件查询参数
export interface PluginQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  tags?: string[]
  status?: 'active' | 'inactive' | 'pending' | 'rejected'
  isOfficial?: boolean
  isInstalled?: boolean
  sortBy?: 'name' | 'createdAt' | 'downloadCount' | 'rating'
  sortOrder?: 'asc' | 'desc'
}

// 插件安装请求
export interface InstallPluginRequest {
  pluginId: string
  version?: string
  config?: Record<string, any>
}

// 插件更新请求
export interface UpdatePluginRequest {
  name?: string
  description?: string
  category?: string
  tags?: string[]
  status?: 'active' | 'inactive'
  config?: Partial<PluginConfig>
}

// 插件执行请求
export interface ExecutePluginRequest {
  action: string
  parameters?: Record<string, any>
  context?: Record<string, any>
}

// 插件执行结果
export interface PluginExecutionResult {
  success: boolean
  result?: any
  error?: string
  logs?: string[]
  executionTime: number
}

/**
 * 插件管理API类
 */
export class PluginsAPI {
  /**
   * 获取插件列表
   */
  static async getPlugins(params?: PluginQueryParams): Promise<ApiResponse<PaginatedResponse<Plugin>>> {
    return apiClient.get<PaginatedResponse<Plugin>>('/api/plugins', params)
  }

  /**
   * 获取插件详情
   */
  static async getPluginById(id: string): Promise<ApiResponse<Plugin>> {
    return apiClient.get<Plugin>(`/api/plugins/${id}`)
  }

  /**
   * 安装插件
   */
  static async installPlugin(data: InstallPluginRequest): Promise<ApiResponse<Plugin>> {
    return apiClient.post<Plugin>('/api/plugins/install', data)
  }

  /**
   * 卸载插件
   */
  static async uninstallPlugin(id: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/plugins/${id}/uninstall`)
  }

  /**
   * 启用插件
   */
  static async enablePlugin(id: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/plugins/${id}/enable`)
  }

  /**
   * 禁用插件
   */
  static async disablePlugin(id: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/plugins/${id}/disable`)
  }

  /**
   * 更新插件
   */
  static async updatePlugin(id: string, data: UpdatePluginRequest): Promise<ApiResponse<Plugin>> {
    return apiClient.put<Plugin>(`/api/plugins/${id}`, data)
  }

  /**
   * 升级插件
   */
  static async upgradePlugin(id: string, version?: string): Promise<ApiResponse<Plugin>> {
    return apiClient.post<Plugin>(`/api/plugins/${id}/upgrade`, { version })
  }

  /**
   * 执行插件
   */
  static async executePlugin(id: string, data: ExecutePluginRequest): Promise<ApiResponse<PluginExecutionResult>> {
    return apiClient.post<PluginExecutionResult>(`/api/plugins/${id}/execute`, data)
  }

  /**
   * 获取插件配置
   */
  static async getPluginConfig(id: string): Promise<ApiResponse<PluginConfig>> {
    return apiClient.get<PluginConfig>(`/api/plugins/${id}/config`)
  }

  /**
   * 更新插件配置
   */
  static async updatePluginConfig(id: string, config: Partial<PluginConfig>): Promise<ApiResponse<PluginConfig>> {
    return apiClient.put<PluginConfig>(`/api/plugins/${id}/config`, config)
  }

  /**
   * 获取插件日志
   */
  static async getPluginLogs(id: string, params?: {
    page?: number
    pageSize?: number
    level?: 'debug' | 'info' | 'warn' | 'error'
    startTime?: string
    endTime?: string
  }): Promise<ApiResponse<PaginatedResponse<{
    id: string
    level: string
    message: string
    timestamp: string
    metadata?: Record<string, any>
  }>>> {
    return apiClient.get(`/api/plugins/${id}/logs`, params)
  }

  /**
   * 获取已安装插件列表
   */
  static async getInstalledPlugins(): Promise<ApiResponse<Plugin[]>> {
    return apiClient.get<Plugin[]>('/api/plugins/installed')
  }

  /**
   * 获取插件分类
   */
  static async getCategories(): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>('/api/plugins/categories')
  }

  /**
   * 搜索插件
   */
  static async searchPlugins(keyword: string, params?: {
    category?: string
    tags?: string[]
    limit?: number
  }): Promise<ApiResponse<Plugin[]>> {
    return apiClient.get<Plugin[]>('/api/plugins/search', { keyword, ...params })
  }

  /**
   * 获取插件统计信息
   */
  static async getPluginStats(id: string): Promise<ApiResponse<{
    downloadCount: number
    activeInstalls: number
    averageRating: number
    executionCount: number
    errorRate: number
  }>> {
    return apiClient.get(`/api/plugins/${id}/stats`)
  }

  /**
   * 评价插件
   */
  static async ratePlugin(id: string, rating: number, comment?: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/api/plugins/${id}/rate`, { rating, comment })
  }

  /**
   * 获取插件评价
   */
  static async getPluginRatings(id: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<PaginatedResponse<{
    id: string
    rating: number
    comment?: string
    userId: string
    userName: string
    createdAt: string
  }>>> {
    return apiClient.get(`/api/plugins/${id}/ratings`, params)
  }

  /**
   * 上传插件
   */
  static async uploadPlugin(file: File, metadata?: Record<string, any>): Promise<ApiResponse<Plugin>> {
    const formData = new FormData()
    formData.append('file', file)
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata))
    }

    // 直接使用postFormData方法，避免手动设置Content-Type
    return apiClient.postFormData<Plugin>('/api/plugins/upload', formData)
  }

  /**
   * 下载插件
   */
  static async downloadPlugin(id: string): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.get<{ downloadUrl: string }>(`/api/plugins/${id}/download`)
  }

  /**
   * 获取插件依赖
   */
  static async getPluginDependencies(id: string): Promise<ApiResponse<{
    dependencies: string[]
    conflicts: string[]
    recommendations: string[]
  }>> {
    return apiClient.get(`/api/plugins/${id}/dependencies`)
  }
}

export default PluginsAPI
