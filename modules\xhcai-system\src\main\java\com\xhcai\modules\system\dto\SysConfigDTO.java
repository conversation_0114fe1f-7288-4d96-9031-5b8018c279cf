package com.xhcai.modules.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 系统配置DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "系统配置DTO")
public class SysConfigDTO {

    @Schema(description = "配置ID")
    private String id;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "配置名称不能为空")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    private String configName;

    @Schema(description = "配置键名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "配置键名不能为空")
    @Size(max = 100, message = "配置键名长度不能超过100个字符")
    private String configKey;

    @Schema(description = "配置键值")
    @Size(max = 500, message = "配置键值长度不能超过500个字符")
    private String configValue;

    @Schema(description = "系统内置（Y是 N否）")
    @Size(max = 1, message = "系统内置标识长度不能超过1个字符")
    private String configType;

    @Schema(description = "配置分组")
    @Size(max = 50, message = "配置分组长度不能超过50个字符")
    private String configGroup;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @Schema(description = "状态（0正常 1停用）")
    @Size(max = 1, message = "状态长度不能超过1个字符")
    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getConfigGroup() {
        return configGroup;
    }

    public void setConfigGroup(String configGroup) {
        this.configGroup = configGroup;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
