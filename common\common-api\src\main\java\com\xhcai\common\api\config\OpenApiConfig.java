package com.xhcai.common.api.config;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI配置类 配置Swagger文档生成
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class OpenApiConfig {

    private static final Logger log = LoggerFactory.getLogger(OpenApiConfig.class);

    @Autowired
    private Environment environment;

    /**
     * 自定义OpenAPI信息
     */
    @Bean
    public OpenAPI customOpenAPI() {
        // 动态获取服务器信息
        List<Server> servers = buildDynamicServers();

        return new OpenAPI()
                .info(new Info()
                        .title("XHC AI Plus API")
                        .description("XHC AI Plus 平台接口文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("xhcai")
                                .email("<EMAIL>")
                                .url("https://github.com/xhcai"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(servers)
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("Bearer Authentication",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("JWT认证")));
    }

    /**
     * 动态构建服务器列表
     */
    private List<Server> buildDynamicServers() {
        List<Server> servers = new ArrayList<>();

        // 获取当前环境配置
        String activeProfile = environment.getProperty("spring.profiles.active", "default");
        String port = environment.getProperty("server.port", "8080");

        // 获取服务器IP地址
        String hostAddress = getServerAddress();

        // 构建当前环境的服务器地址
        String currentServerUrl = "http://" + hostAddress + ":" + port;
        String currentDescription = "当前环境 (" + activeProfile + ")";

        servers.add(new Server().url(currentServerUrl).description(currentDescription));

        // 根据环境添加其他常用服务器地址
        if ("dev".equals(activeProfile)) {
            // 开发环境额外添加localhost
            if (!hostAddress.equals("localhost") && !hostAddress.equals("127.0.0.1")) {
                servers.add(new Server().url("http://localhost:" + port).description("本地开发环境"));
            }
        } else if ("prod".equals(activeProfile)) {
            // 生产环境可以添加域名地址（如果有的话）
            String prodDomain = environment.getProperty("app.domain");
            if (prodDomain != null && !prodDomain.isEmpty()) {
                servers.add(new Server().url("https://" + prodDomain).description("生产域名"));
            }
        }

        log.info("OpenAPI服务器列表构建完成，当前服务器: {}", currentServerUrl);

        return servers;
    }

    /**
     * 获取服务器地址
     */
    private String getServerAddress() {
        // 首先尝试从配置中获取
        String serverAddress = environment.getProperty("server.address");
        if (serverAddress != null && !serverAddress.isEmpty() && !"0.0.0.0".equals(serverAddress)) {
            return serverAddress;
        }

        // 尝试获取本机IP地址
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            String hostAddress = localHost.getHostAddress();

            // 避免返回回环地址，除非是开发环境
            if (!hostAddress.startsWith("127.") || "dev".equals(environment.getProperty("spring.profiles.active"))) {
                return hostAddress;
            }
        } catch (UnknownHostException e) {
            log.warn("无法获取本机IP地址: {}", e.getMessage());
        }

        // 最后回退到localhost
        return "localhost";
    }

    /**
     * 通用接口API分组 通用工具类接口，不属于特定模块
     */
    @Bean
    public GroupedOpenApi commonApi() {
        return GroupedOpenApi.builder()
                .group("common")
                .displayName("通用接口")
                .pathsToMatch("/api/common/**")
                .build();
    }

    /**
     * 测试接口API分组 开发测试用接口
     */
    @Bean
    public GroupedOpenApi testApi() {
        return GroupedOpenApi.builder()
                .group("test")
                .displayName("测试接口")
                .pathsToMatch("/api/test/**", "/api/demo/**")
                .build();
    }

    /**
     * 所有接口API分组 包含所有API接口的汇总分组 注意：此分组优先级最低，避免覆盖其他分组
     */
    @Bean
    public GroupedOpenApi allApi() {
        return GroupedOpenApi.builder()
                .group("zzz-all") // 使用 zzz 前缀确保排序在最后
                .displayName("所有接口")
                .pathsToMatch("/api/**")
                // 移除pathsToExclude，显示所有接口
                .build();
    }
}
