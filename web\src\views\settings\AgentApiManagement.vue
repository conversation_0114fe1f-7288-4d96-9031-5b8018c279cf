<template>
  <div class="agent-api-management bg-white rounded-lg shadow-sm p-6">
    <div class="api-header flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-800">智能体API密钥列表</h3>
      <button @click="showAddApiKeyModal = true" class="btn-primary">
        <span class="mr-2">➕</span>
        分配密钥
      </button>
    </div>

    <div class="agent-api-table">
      <div class="table-header grid grid-cols-8 gap-2 p-4 bg-gray-50 rounded-t-lg font-medium text-gray-700 text-sm">
        <div>智能体名称</div>
        <div>设计者</div>
        <div>授权密钥</div>
        <div>授权时间</div>
        <div>使用单位</div>
        <div>使用者</div>
        <div>联系方式</div>
        <div>访问频率</div>
      </div>
      <div class="table-body">
        <div
          v-for="apiKey in paginatedAgentApiKeys"
          :key="apiKey.id"
          class="table-row grid grid-cols-8 gap-2 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-300 text-sm"
        >
          <div class="font-medium text-gray-800">{{ apiKey.agentName }}</div>
          <div class="text-gray-600">{{ apiKey.designer }}</div>
          <div class="text-gray-600">
            <span v-if="apiKey.showKey">{{ apiKey.apiKey }}</span>
            <span v-else>{{ apiKey.maskedKey }}</span>
            <button @click="toggleKeyVisibility(apiKey)" class="ml-2 text-blue-500 hover:text-blue-700">
              {{ apiKey.showKey ? '隐藏' : '显示' }}
            </button>
          </div>
          <div class="text-gray-600">{{ apiKey.authorizedAt }}</div>
          <div class="text-gray-600">{{ apiKey.useUnit }}</div>
          <div class="text-gray-600">{{ apiKey.userName }}</div>
          <div class="text-gray-600">{{ apiKey.contact }}</div>
          <div class="text-gray-600">{{ apiKey.frequency }}</div>
        </div>
      </div>
    </div>

    <!-- 智能体API密钥管理分页 -->
    <Pagination
      v-model:currentPage="agentApiPagination.currentPage"
      v-model:pageSize="agentApiPagination.pageSize"
      :total="agentApiKeys.length"
    />


    <!-- 添加智能体API密钥模态框 -->
    <div v-if="showAddApiKeyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">分配智能体API密钥</h3>
          <button @click="closeApiKeyModal" class="text-gray-400 hover:text-gray-600">
            <span class="text-xl">×</span>
          </button>
        </div>

        <form @submit.prevent="saveApiKey" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="form-label">智能体名称</label>
              <select v-model="currentApiKey.agentId" class="form-input" required>
                <option value="">请选择智能体</option>
                <option v-for="agent in agents" :key="agent.id" :value="agent.id">{{ agent.name }}</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="form-label">设计者</label>
              <input v-model="currentApiKey.designer" type="text" class="form-input" required />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="form-label">使用单位</label>
              <input v-model="currentApiKey.useUnit" type="text" class="form-input" required />
            </div>
            
            <div class="form-group">
              <label class="form-label">使用者</label>
              <input v-model="currentApiKey.userName" type="text" class="form-input" required />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="form-label">联系方式</label>
              <input v-model="currentApiKey.contact" type="text" class="form-input" required />
            </div>
            
            <div class="form-group">
              <label class="form-label">访问频率限制</label>
              <select v-model="currentApiKey.frequency" class="form-input">
                <option value="50次/天">50次/天</option>
                <option value="100次/天">100次/天</option>
                <option value="200次/天">200次/天</option>
                <option value="500次/天">500次/天</option>
                <option value="无限制">无限制</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">项目名称</label>
            <input v-model="currentApiKey.projectName" type="text" class="form-input" />
          </div>

          <div class="flex gap-3 pt-4">
            <button type="submit" class="btn-primary flex-1">生成并分配密钥</button>
            <button type="button" @click="closeApiKeyModal" class="btn-secondary flex-1">取消</button>
          </div>
        </form>
      </div>
    </div>




  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Pagination from '@/components/common/Pagination.vue'



// 智能体API密钥数据
const agentApiKeys = ref([
  {
    id: 1,
    agentName: '智能客服助手',
    designer: '张三',
    apiKey: 'ag_1234567890abcdef',
    maskedKey: 'ag_****cdef',
    showKey: false,
    authorizedAt: '2024-01-18',
    useUnit: '客服部',
    userName: '李四',
    projectName: '客服系统',
    contact: '13700137001',
    frequency: '200次/天'
  },
  {
    id: 2,
    agentName: '代码审查助手',
    designer: '王五',
    apiKey: 'ag_abcdef1234567890',
    maskedKey: 'ag_****7890',
    showKey: false,
    authorizedAt: '2024-01-22',
    useUnit: '技术部',
    userName: '赵六',
    projectName: '代码质量管控',
    contact: '13600136002',
    frequency: '80次/天'
  }
])

// 智能体列表
const agents = ref([
  { id: 1, name: '智能客服助手' },
  { id: 2, name: '代码审查助手' },
  { id: 3, name: '文档生成助手' },
  { id: 4, name: '数据分析助手' }
])



// 分页数据
const agentApiPagination = ref({ currentPage: 1, pageSize: 10 })

// 分页计算属性
const paginatedAgentApiKeys = computed(() => {
  const start = (agentApiPagination.value.currentPage - 1) * agentApiPagination.value.pageSize
  const end = start + agentApiPagination.value.pageSize
  return agentApiKeys.value.slice(start, end)
})

// 模态框状态
const showAddApiKeyModal = ref(false)

// 当前数据
const currentApiKey = ref({
  agentId: '',
  designer: '',
  useUnit: '',
  userName: '',
  contact: '',
  frequency: '100次/天',
  projectName: ''
})



// API密钥管理方法
const toggleKeyVisibility = (apiKey: any) => {
  apiKey.showKey = !apiKey.showKey
}

const closeApiKeyModal = () => {
  showAddApiKeyModal.value = false
  currentApiKey.value = {
    agentId: '',
    designer: '',
    useUnit: '',
    userName: '',
    contact: '',
    frequency: '100次/天',
    projectName: ''
  }
}

const generateApiKey = () => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = 'ag_'
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

const saveApiKey = () => {
  const agent = agents.value.find(a => a.id === Number(currentApiKey.value.agentId))
  
  if (!agent) {
    alert('请选择智能体')
    return
  }

  const apiKey = generateApiKey()
  const newApiKeyRecord = {
    id: Date.now(),
    agentName: agent.name,
    designer: currentApiKey.value.designer,
    apiKey: apiKey,
    maskedKey: `ag_****${apiKey.slice(-4)}`,
    showKey: false,
    authorizedAt: new Date().toISOString().split('T')[0],
    useUnit: currentApiKey.value.useUnit,
    userName: currentApiKey.value.userName,
    projectName: currentApiKey.value.projectName,
    contact: currentApiKey.value.contact,
    frequency: currentApiKey.value.frequency
  }

  agentApiKeys.value.push(newApiKeyRecord)
  closeApiKeyModal()
  alert('API密钥生成并分配成功')
}


</script>

<style scoped>
/* 继承Settings.vue的样式 */
@import url('./settings-common.css');
</style>
