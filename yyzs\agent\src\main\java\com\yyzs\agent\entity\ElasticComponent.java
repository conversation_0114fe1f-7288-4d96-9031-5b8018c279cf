package com.yyzs.agent.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * Elastic Stack组件实体
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "elastic_component")
@TableName("elastic_component")
public class ElasticComponent extends BaseEntity {

    /**
     * 组件名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 组件类型（filebeat, heartbeat, metricbeat, packetbeat, winlogbeat, auditbeat, logstash, elasticsearch, kafka）
     */
    @Column(name = "type", nullable = false, length = 50)
    private String type;

    /**
     * 组件版本
     */
    @Column(name = "version", nullable = false, length = 50)
    private String version;

    /**
     * 组件状态（INSTALLING, INSTALLED, RUNNING, STOPPED, ERROR, UNINSTALLED）
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ComponentStatus status = ComponentStatus.UNINSTALLED;

    /**
     * 安装路径
     */
    @Column(name = "install_path", length = 500)
    private String installPath;

    /**
     * 配置文件路径
     */
    @Column(name = "config_path", length = 500)
    private String configPath;

    /**
     * 日志文件路径
     */
    @Column(name = "log_path", length = 500)
    private String logPath;

    /**
     * 进程ID
     */
    @Column(name = "process_id")
    private Long processId;

    /**
     * 监听端口
     */
    @Column(name = "port")
    private Integer port;

    /**
     * 主机地址
     */
    @Column(name = "host", length = 100)
    private String host = "localhost";

    /**
     * 组件描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 安装包文件名
     */
    @Column(name = "package_file_name", length = 200)
    private String packageFileName;

    /**
     * 安装包文件路径
     */
    @Column(name = "package_file_path", length = 500)
    private String packageFilePath;

    /**
     * 安装包文件大小（字节）
     */
    @Column(name = "package_file_size")
    private Long packageFileSize;

    /**
     * 安装时间
     */
    @Column(name = "install_time")
    private LocalDateTime installTime;

    /**
     * 最后启动时间
     */
    @Column(name = "last_start_time")
    private LocalDateTime lastStartTime;

    /**
     * 最后停止时间
     */
    @Column(name = "last_stop_time")
    private LocalDateTime lastStopTime;

    /**
     * 自动启动标志
     */
    @Column(name = "auto_start", nullable = false)
    private Boolean autoStart = false;

    /**
     * 启动命令
     */
    @Column(name = "start_command", length = 1000)
    private String startCommand;

    /**
     * 停止命令
     */
    @Column(name = "stop_command", length = 1000)
    private String stopCommand;

    /**
     * 重启命令
     */
    @Column(name = "restart_command", length = 1000)
    private String restartCommand;

    /**
     * 状态检查命令
     */
    @Column(name = "status_command", length = 1000)
    private String statusCommand;

    /**
     * 组件状态枚举
     */
    public enum ComponentStatus {
        INSTALLING("安装中"),
        INSTALLED("已安装"),
        RUNNING("运行中"),
        STOPPED("已停止"),
        ERROR("错误"),
        UNINSTALLED("未安装");

        private final String description;

        ComponentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
