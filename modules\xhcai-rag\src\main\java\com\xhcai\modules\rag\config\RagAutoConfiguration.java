package com.xhcai.modules.rag.config;

import com.xhcai.common.core.config.YamlPropertySourceFactory;
import com.xhcai.plugin.service.PluginServiceManager;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;

import jakarta.annotation.PostConstruct;

/**
 * RAG模块自动配置类
 * 负责RAG模块的组件扫描、实体扫描、Mapper扫描和配置文件加载
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@AutoConfiguration
@ComponentScan(basePackages = {
    "com.xhcai.modules.rag"
})
@EntityScan(basePackages = "com.xhcai.modules.rag.entity")
@MapperScan(basePackages = "com.xhcai.modules.rag.mapper")
@ConfigurationPropertiesScan(basePackages = "com.xhcai.modules.rag.config")
@PropertySource(value = "classpath:application-xhcai-rag.yml", factory = YamlPropertySourceFactory.class)
public class RagAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(RagAutoConfiguration.class);

    @Autowired(required = false)
    private PluginServiceManager pluginServiceManager;

    public RagAutoConfiguration() {
        log.info("=== RAG模块自动配置已启用 ===");
        log.info("组件扫描包: com.xhcai.modules.rag.*");
        log.info("实体扫描包: com.xhcai.modules.rag.entity");
        log.info("Mapper扫描包: com.xhcai.modules.rag.mapper");
        log.info("配置文件: application-xhcai-rag.yml");
    }

    @PostConstruct
    public void checkPluginServices() {
        log.info("=== 检查RAG模块插件服务状态 ===");

        if (pluginServiceManager == null) {
            log.warn("PluginServiceManager 未注入，存储功能可能不可用");
            return;
        }

        try {
            var storageServices = pluginServiceManager.getStorageServices();
            log.info("可用存储服务数量: {}", storageServices.size());

            if (storageServices.isEmpty()) {
                log.warn("没有可用的存储服务，文档上传功能将受限");
            } else {
                storageServices.forEach(service -> {
                    log.info("存储服务: {} ({}), 健康状态: {}",
                            service.getServiceName(),
                            service.getServiceType(),
                            service.isHealthy());
                });
            }
        } catch (Exception e) {
            log.error("检查插件服务状态时发生错误", e);
        }
    }
}
