'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Bell, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Plus,
  Edit,
  Trash2,
  Eye,
  Filter,
  Search,
  Calendar,
  Clock,
  TrendingUp,
  Mail,
  MessageSquare,
  Webhook
} from 'lucide-react';
import toast from 'react-hot-toast';

// 告警级别
enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 告警状态
enum AlertStatus {
  ACTIVE = 'active',
  RESOLVED = 'resolved',
  ACKNOWLEDGED = 'acknowledged'
}

// 告警规则接口
interface AlertRule {
  id: string;
  name: string;
  description: string;
  type: 'metric' | 'log' | 'component';
  condition: string;
  threshold: number;
  level: AlertLevel;
  enabled: boolean;
  notificationChannels: string[];
  createTime: string;
  updateTime: string;
}

// 告警记录接口
interface AlertRecord {
  id: string;
  ruleId: string;
  ruleName: string;
  level: AlertLevel;
  status: AlertStatus;
  message: string;
  details: string;
  triggerTime: string;
  resolveTime?: string;
  acknowledgeTime?: string;
  acknowledgeBy?: string;
}

// 通知渠道接口
interface NotificationChannel {
  id: string;
  name: string;
  type: 'email' | 'webhook' | 'dingtalk' | 'wechat';
  config: Record<string, any>;
  enabled: boolean;
}

// 告警标签页
enum AlertTab {
  RULES = 'rules',
  HISTORY = 'history',
  CHANNELS = 'channels',
  STATISTICS = 'statistics'
}

const TAB_LABELS = {
  [AlertTab.RULES]: '告警规则',
  [AlertTab.HISTORY]: '告警历史',
  [AlertTab.CHANNELS]: '通知渠道',
  [AlertTab.STATISTICS]: '告警统计'
};

const ALERT_LEVEL_LABELS = {
  [AlertLevel.INFO]: '信息',
  [AlertLevel.WARNING]: '警告',
  [AlertLevel.ERROR]: '错误',
  [AlertLevel.CRITICAL]: '严重'
};

const ALERT_LEVEL_COLORS = {
  [AlertLevel.INFO]: 'text-blue-600 bg-blue-50',
  [AlertLevel.WARNING]: 'text-yellow-600 bg-yellow-50',
  [AlertLevel.ERROR]: 'text-red-600 bg-red-50',
  [AlertLevel.CRITICAL]: 'text-purple-600 bg-purple-50'
};

const ALERT_STATUS_LABELS = {
  [AlertStatus.ACTIVE]: '活跃',
  [AlertStatus.RESOLVED]: '已解决',
  [AlertStatus.ACKNOWLEDGED]: '已确认'
};

const ALERT_STATUS_COLORS = {
  [AlertStatus.ACTIVE]: 'text-red-600 bg-red-50',
  [AlertStatus.RESOLVED]: 'text-green-600 bg-green-50',
  [AlertStatus.ACKNOWLEDGED]: 'text-yellow-600 bg-yellow-50'
};

export default function AlertsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<AlertTab>(AlertTab.RULES);
  const [loading, setLoading] = useState(false);
  
  // 告警规则相关状态
  const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
  const [showRuleModal, setShowRuleModal] = useState(false);
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null);
  
  // 告警历史相关状态
  const [alertHistory, setAlertHistory] = useState<AlertRecord[]>([]);
  const [historyFilter, setHistoryFilter] = useState({
    level: '',
    status: '',
    dateRange: '',
    search: ''
  });
  
  // 通知渠道相关状态
  const [notificationChannels, setNotificationChannels] = useState<NotificationChannel[]>([]);
  const [showChannelModal, setShowChannelModal] = useState(false);
  const [editingChannel, setEditingChannel] = useState<NotificationChannel | null>(null);

  useEffect(() => {
    loadAlertsData();
  }, [activeTab]);

  const loadAlertsData = async () => {
    setLoading(true);
    try {
      switch (activeTab) {
        case AlertTab.RULES:
          await loadAlertRules();
          break;
        case AlertTab.HISTORY:
          await loadAlertHistory();
          break;
        case AlertTab.CHANNELS:
          await loadNotificationChannels();
          break;
        case AlertTab.STATISTICS:
          // TODO: 加载告警统计数据
          break;
      }
    } catch (error) {
      console.error('加载告警数据失败:', error);
      toast.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAlertRules = async () => {
    // TODO: 调用API加载告警规则
    setAlertRules([
      {
        id: '1',
        name: 'CPU使用率过高',
        description: '当CPU使用率超过80%时触发告警',
        type: 'metric',
        condition: 'cpu_usage > 80',
        threshold: 80,
        level: AlertLevel.WARNING,
        enabled: true,
        notificationChannels: ['email-1'],
        createTime: '2024-01-01T00:00:00Z',
        updateTime: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: '组件服务异常',
        description: '当组件状态为ERROR时触发告警',
        type: 'component',
        condition: 'component_status == ERROR',
        threshold: 0,
        level: AlertLevel.CRITICAL,
        enabled: true,
        notificationChannels: ['email-1', 'webhook-1'],
        createTime: '2024-01-01T00:00:00Z',
        updateTime: '2024-01-01T00:00:00Z'
      }
    ]);
  };

  const loadAlertHistory = async () => {
    // TODO: 调用API加载告警历史
    setAlertHistory([
      {
        id: '1',
        ruleId: '1',
        ruleName: 'CPU使用率过高',
        level: AlertLevel.WARNING,
        status: AlertStatus.RESOLVED,
        message: 'CPU使用率达到85%',
        details: '主机: localhost, 当前值: 85%',
        triggerTime: '2024-01-01T10:00:00Z',
        resolveTime: '2024-01-01T10:30:00Z'
      },
      {
        id: '2',
        ruleId: '2',
        ruleName: '组件服务异常',
        level: AlertLevel.CRITICAL,
        status: AlertStatus.ACTIVE,
        message: 'Elasticsearch组件状态异常',
        details: '组件ID: es-001, 状态: ERROR',
        triggerTime: '2024-01-01T11:00:00Z'
      }
    ]);
  };

  const loadNotificationChannels = async () => {
    // TODO: 调用API加载通知渠道
    setNotificationChannels([
      {
        id: 'email-1',
        name: '邮件通知',
        type: 'email',
        config: {
          smtp_host: 'smtp.example.com',
          smtp_port: 587,
          username: '<EMAIL>',
          recipients: ['<EMAIL>']
        },
        enabled: true
      },
      {
        id: 'webhook-1',
        name: 'Webhook通知',
        type: 'webhook',
        config: {
          url: 'https://hooks.example.com/alerts',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        },
        enabled: true
      }
    ]);
  };

  // 切换告警规则状态
  const toggleRuleStatus = async (ruleId: string, enabled: boolean) => {
    try {
      // TODO: 调用API切换规则状态
      setAlertRules(prev => 
        prev.map(rule => 
          rule.id === ruleId ? { ...rule, enabled } : rule
        )
      );
      toast.success(enabled ? '规则已启用' : '规则已禁用');
    } catch (error) {
      console.error('切换规则状态失败:', error);
      toast.error('操作失败');
    }
  };

  // 删除告警规则
  const deleteRule = async (ruleId: string) => {
    if (!confirm('确定要删除这个告警规则吗？')) return;
    
    try {
      // TODO: 调用API删除规则
      setAlertRules(prev => prev.filter(rule => rule.id !== ruleId));
      toast.success('规则已删除');
    } catch (error) {
      console.error('删除规则失败:', error);
      toast.error('删除失败');
    }
  };

  // 确认告警
  const acknowledgeAlert = async (alertId: string) => {
    try {
      // TODO: 调用API确认告警
      setAlertHistory(prev => 
        prev.map(alert => 
          alert.id === alertId 
            ? { ...alert, status: AlertStatus.ACKNOWLEDGED, acknowledgeTime: new Date().toISOString() }
            : alert
        )
      );
      toast.success('告警已确认');
    } catch (error) {
      console.error('确认告警失败:', error);
      toast.error('操作失败');
    }
  };

  // 解决告警
  const resolveAlert = async (alertId: string) => {
    try {
      // TODO: 调用API解决告警
      setAlertHistory(prev => 
        prev.map(alert => 
          alert.id === alertId 
            ? { ...alert, status: AlertStatus.RESOLVED, resolveTime: new Date().toISOString() }
            : alert
        )
      );
      toast.success('告警已解决');
    } catch (error) {
      console.error('解决告警失败:', error);
      toast.error('操作失败');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="btn-outline mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </button>
              <div className="flex items-center">
                <Bell className="h-8 w-8 text-primary-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">告警管理</h1>
                  <p className="text-sm text-gray-500">配置告警规则和查看告警历史</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 侧边栏导航 */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {Object.entries(TAB_LABELS).map(([tab, label]) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as AlertTab)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  {label}
                </button>
              ))}
            </nav>
          </div>

          {/* 主内容区域 */}
          <div className="flex-1">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {TAB_LABELS[activeTab]}
                </h3>
              </div>
              <div className="card-body">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="loading-spinner w-8 h-8 mx-auto mb-4"></div>
                    <p className="text-gray-600">加载中...</p>
                  </div>
                ) : (
                  <>
                    {/* 告警规则管理 */}
                    {activeTab === AlertTab.RULES && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">配置和管理告警规则</p>
                          <button
                            onClick={() => setShowRuleModal(true)}
                            className="btn-primary"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            新建规则
                          </button>
                        </div>

                        <div className="space-y-4">
                          {alertRules.map((rule) => (
                            <div key={rule.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-3">
                                    <h4 className="font-medium text-gray-900">{rule.name}</h4>
                                    <span className={`px-2 py-1 rounded text-xs ${ALERT_LEVEL_COLORS[rule.level]}`}>
                                      {ALERT_LEVEL_LABELS[rule.level]}
                                    </span>
                                    <span className={`px-2 py-1 rounded text-xs ${
                                      rule.enabled ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                                    }`}>
                                      {rule.enabled ? '启用' : '禁用'}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-600 mt-1">{rule.description}</p>
                                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span>类型: {rule.type}</span>
                                    <span>条件: {rule.condition}</span>
                                    <span>通知: {rule.notificationChannels.length}个渠道</span>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => toggleRuleStatus(rule.id, !rule.enabled)}
                                    className={`btn-sm ${rule.enabled ? 'btn-warning' : 'btn-success'}`}
                                    title={rule.enabled ? '禁用规则' : '启用规则'}
                                  >
                                    {rule.enabled ? '禁用' : '启用'}
                                  </button>
                                  <button
                                    onClick={() => {
                                      setEditingRule(rule);
                                      setShowRuleModal(true);
                                    }}
                                    className="btn-sm btn-secondary"
                                    title="编辑规则"
                                  >
                                    <Edit className="h-3 w-3" />
                                  </button>
                                  <button
                                    onClick={() => deleteRule(rule.id)}
                                    className="btn-sm btn-error"
                                    title="删除规则"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 告警历史 */}
                    {activeTab === AlertTab.HISTORY && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">查看和管理告警历史记录</p>
                          <div className="flex items-center space-x-2">
                            <div className="relative">
                              <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                              <input
                                type="text"
                                placeholder="搜索告警..."
                                value={historyFilter.search}
                                onChange={(e) => setHistoryFilter(prev => ({...prev, search: e.target.value}))}
                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm"
                              />
                            </div>
                            <select
                              value={historyFilter.level}
                              onChange={(e) => setHistoryFilter(prev => ({...prev, level: e.target.value}))}
                              className="form-select text-sm"
                            >
                              <option value="">所有级别</option>
                              {Object.entries(ALERT_LEVEL_LABELS).map(([level, label]) => (
                                <option key={level} value={level}>{label}</option>
                              ))}
                            </select>
                            <select
                              value={historyFilter.status}
                              onChange={(e) => setHistoryFilter(prev => ({...prev, status: e.target.value}))}
                              className="form-select text-sm"
                            >
                              <option value="">所有状态</option>
                              {Object.entries(ALERT_STATUS_LABELS).map(([status, label]) => (
                                <option key={status} value={status}>{label}</option>
                              ))}
                            </select>
                          </div>
                        </div>

                        <div className="space-y-4">
                          {alertHistory.map((alert) => (
                            <div key={alert.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-3">
                                    <h4 className="font-medium text-gray-900">{alert.ruleName}</h4>
                                    <span className={`px-2 py-1 rounded text-xs ${ALERT_LEVEL_COLORS[alert.level]}`}>
                                      {ALERT_LEVEL_LABELS[alert.level]}
                                    </span>
                                    <span className={`px-2 py-1 rounded text-xs ${ALERT_STATUS_COLORS[alert.status]}`}>
                                      {ALERT_STATUS_LABELS[alert.status]}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-900 mt-1">{alert.message}</p>
                                  <p className="text-xs text-gray-500 mt-1">{alert.details}</p>
                                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span>触发时间: {new Date(alert.triggerTime).toLocaleString()}</span>
                                    {alert.resolveTime && (
                                      <span>解决时间: {new Date(alert.resolveTime).toLocaleString()}</span>
                                    )}
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {alert.status === AlertStatus.ACTIVE && (
                                    <>
                                      <button
                                        onClick={() => acknowledgeAlert(alert.id)}
                                        className="btn-sm btn-warning"
                                        title="确认告警"
                                      >
                                        确认
                                      </button>
                                      <button
                                        onClick={() => resolveAlert(alert.id)}
                                        className="btn-sm btn-success"
                                        title="解决告警"
                                      >
                                        解决
                                      </button>
                                    </>
                                  )}
                                  <button
                                    className="btn-sm btn-secondary"
                                    title="查看详情"
                                  >
                                    <Eye className="h-3 w-3" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 通知渠道管理 */}
                    {activeTab === AlertTab.CHANNELS && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">配置告警通知渠道</p>
                          <button
                            onClick={() => setShowChannelModal(true)}
                            className="btn-primary"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            添加渠道
                          </button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {notificationChannels.map((channel) => (
                            <div key={channel.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-2">
                                  {channel.type === 'email' && <Mail className="h-5 w-5 text-blue-600" />}
                                  {channel.type === 'webhook' && <Webhook className="h-5 w-5 text-green-600" />}
                                  {channel.type === 'dingtalk' && <MessageSquare className="h-5 w-5 text-orange-600" />}
                                  {channel.type === 'wechat' && <MessageSquare className="h-5 w-5 text-green-600" />}
                                  <h4 className="font-medium text-gray-900">{channel.name}</h4>
                                </div>
                                <span className={`px-2 py-1 rounded text-xs ${
                                  channel.enabled ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                                }`}>
                                  {channel.enabled ? '启用' : '禁用'}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 mb-3">类型: {channel.type}</p>
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => {
                                    setEditingChannel(channel);
                                    setShowChannelModal(true);
                                  }}
                                  className="btn-sm btn-secondary"
                                  title="编辑渠道"
                                >
                                  <Edit className="h-3 w-3" />
                                </button>
                                <button
                                  className="btn-sm btn-error"
                                  title="删除渠道"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 告警统计 */}
                    {activeTab === AlertTab.STATISTICS && (
                      <div>
                        <div className="flex justify-between items-center mb-6">
                          <p className="text-gray-600">查看告警统计信息和趋势</p>
                        </div>

                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                          <div className="flex items-center">
                            <TrendingUp className="h-5 w-5 text-yellow-600 mr-2" />
                            <p className="text-sm text-yellow-800">
                              告警统计功能正在开发中，敬请期待。
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
